import React, { FC, useEffect, useState } from 'react';
import { Modal, Button, Input, Tabs, Table, Checkbox, message } from 'antd';
import { MenuOutlined } from '@ant-design/icons';
import './index.less';
import { SortableContainer, SortableElement, SortableHandle, arrayMove } from 'react-sortable-hoc';
import rmanApis from '@/service/rman';

const enum_: any = {
	highStorages: "高质量",
	streamStorages: "流媒体",
	keyframeStorages: "关键帧"
};
interface SpaceManageModalProps {
	modalVisible: boolean;
	modalClose: () => void;
	spaceManageOk: (data: any) => void;
	title: string;
	resourceGroup: any
}
const { TabPane } = Tabs;
const SpaceManageModal: FC<SpaceManageModalProps> = ({
	modalVisible,
	modalClose,
	spaceManageOk,
	title,
	resourceGroup
}) => {

	const [activeTabs, setActiveTabs] = useState<string>('highStorages');
	const [temp, setTemp] = useState<any>({});
	const [list, setList] = useState<any>([]);
	const [stroageloading, setStroageloading] = useState<boolean>(false);
	const [storageConfig, setStorageConfig] = useState<any>([]); // 空间配置

	useEffect(() => {
		if (resourceGroup?.contentId_ && modalVisible) {
			rmanApis.getGroupStroage({ folderId: resourceGroup.contentId_ }).then((res: any) => {
				if (res?.success) {
					setStorageConfig(res?.data)
				}
			})
		}
	}, [resourceGroup, modalVisible])

	useEffect(() => {
		if (storageConfig) {
			//默认选中高质量
			setList(storageConfig.highStorages?.map((item: any) => ({
				...item,
				space: (item.unitStorageSize || item.space) > 0 ? (item.unitStorageSize || item.space) / 1024 / 1024 / 1024 : 0,
				order: item.unitOrder,
				over: item.assignOver,
				expansion: false
			})));
			setTemp({
				highStorages: storageConfig.highStorages?.map((item: any) => ({
					...item,
					space: (item.unitStorageSize || item.space) > 0 ? (item.unitStorageSize || item.space) / 1024 / 1024 / 1024 : 0,
					order: item.unitOrder,
					over: item.unitStorageSize === -1,
					expansion: false
				})),
				streamStorages: storageConfig.streamStorages?.map((item: any) => ({
					...item,
					space: (item.unitStorageSize || item.space) > 0 ? (item.unitStorageSize || item.space) / 1024 / 1024 / 1024 : 0,
					order: item.unitOrder,
					over: item.unitStorageSize === -1 || item.assignOver && item.unitStorageSize === 0, //流媒体跟关键帧默认值给最高存储
					expansion: false
				})),
				keyframeStorages: storageConfig.keyframeStorages?.map((item: any) => ({
					...item,
					space: (item.unitStorageSize || item.space) > 0 ? (item.unitStorageSize || item.space) / 1024 / 1024 / 1024 : 0,
					order: item.unitOrder,
					over: item.unitStorageSize === -1 || item.assignOver && item.unitStorageSize === 0,
					expansion: false
				}))
			});
		}
	}, [storageConfig]);

	const sizeChange = (e: any, code: any) => {
		const value = e.target.value;
		if (isNaN(Number(value)) || Number(value) < 0) {
			message.info('请填写非负数值');
			setList(JSON.parse(JSON.stringify(temp[activeTabs]))); //清空无效输入
			return;
		}
		const change = temp[activeTabs].map((item: any) => {
			if (item.storageCode === code) {
				// unitUsedSize代表已分配的，value代表当前输入的， storageSize代表总共大小
				if (item.storageSize !== -1 && item.storageSize <= Number(value * 1024 * 1024 * 1024 + item.unitUsedSize)) {
					message.warning('群组空间不足不能分配存储空间');
					return item;
				}
				return {
					...item,
					space: Number(value)
				};
			} else {
				return item;
			}
		});
		setTemp((pre: any) => ({
			...pre,
			[activeTabs]: change
		}));
		setList(change); //更新列表
	};

	const handleChange = (values: any, code: any, type: string) => {
		const value = values[0] || false;
		const change = temp[activeTabs].map((item: any) => {
			if (item.storageCode === code) {
				if (type == 'limit') { // 不限
					return {
						...item,
						over: value
					};
				} else if (type == 'expansion') { // 扩容
					return {
						...item,
						expansion: value
					};
				}
			} else {
				return item;
			}
		});
		setTemp((pre: any) => ({
			...pre,
			[activeTabs]: change
		}));
		setList(change); //更新列表
	};

	const SortableItem = SortableElement((props: any) => <tr {...props} />);
	const SortableBody = SortableContainer((props: any) => <tbody {...props} />);
	const DragHandle = SortableHandle(() => <MenuOutlined title={"拖动排序"} style={{ cursor: 'grab', color: '#999' }} />);
	const onSortEnd = ({ oldIndex, newIndex }: any) => {
		if (oldIndex !== newIndex) {
			const newData = arrayMove(list.slice(), oldIndex, newIndex).filter(
				(el: any) => !!el
			);
			console.log('Sorted items: ', newData);
			setList(newData);
			setTemp((pre: any) => ({
				...pre,
				[activeTabs]: newData.map((item: any, index: number) => ({
					...item,
					order: index + 1
				}))
			}));
		}
	};

	const DraggableContainer = (props: any) =>
		<SortableBody
			useDragHandle
			disableAutoscroll
			helperClass="row-dragging"
			onSortEnd={onSortEnd}
			{...props} />;


	const DraggableBodyRow: React.FC<any> = ({ className, style, ...restProps }) => {
		const index = list?.findIndex((x: any) => x.storageCode === restProps['data-row-key']);
		return <SortableItem key={index} index={index || 0} {...restProps} />;
	};

	const columns: any = [
		{
			title: '',
			width: '5%',
			align: 'center',
			dataIndex: 'sort',
			className: 'drag-visible',
			render: () => <DragHandle />
		},
		{
			title: "优先级",
			width: '5%',
			align: 'center',
			ellipsis: true,
			render: (value: any, record: any, index: number) => <span title={"拖拽存储使用优先级"}>{index + 1}</span>
		},
		{
			title: "存储名",
			width: '52%',
			align: 'center',
			dataIndex: 'storageName',
			key: 'storageName',
			ellipsis: true,
			render: (value: any, record: any, index: number) => {
				return <div className='bucket_div'>
					<label className='label' title={`${record.storageName}${record.storageType ? '(' + record.storageType + ')' : ''}：`}>{`${record.storageName}${record.storageType ? '(' + record.storageType + ')' : ''}：`}</label>
					<Input
						disabled={record.disabled || record.over || record.space === -1}
						onBlur={(e: any) => sizeChange(e, record.storageCode)}
						// onChange={(e: any) => sizeChange(e, record.storageCode)}
						defaultValue={record.space > 0 ? record.space : undefined} />

					<span>&nbsp;G</span>
					<Checkbox.Group defaultValue={[record.over ? 'limit' : '']}
						options={[{ label: '不限', value: 'limit', disabled: record.disabled || !record.assignOver }]}
						onChange={(e) => handleChange(e, record.storageCode, 'limit')} />
					<Checkbox.Group defaultValue={[record.expansion ? 'expansion' : '']}
						options={[{ label: '扩容', value: 'expansion'}]}
						onChange={(e) => handleChange(e, record.storageCode, 'expansion')} />
				</div>
			}
		},
		{
			title: "分配信息",
			width: '38%',
			align: 'right',
			dataIndex: 'storageSize',
			key: 'storageSize',
			ellipsis: true,
			render: (value: any, record: any) => {
				return <div className='storage_div'>
					{/* assignOver为true代表无限 */}
					<span>{`已分配：${record.unitUsedSize < 0 ? '0B' : range(record.unitUsedSize)} / 共：${record.assignOver ? 
						'不限' : range(record.storageSize)}`}</span>
				</div>

			}
		}];
		
		const range = (size: number) => {
			if (size < 0) {
				return 0;
			} else {
				if (size > 1024 * 1024 * 1024) {
					return (size / 1024 / 1024 / 1024).toFixed(2) + 'GB';	
				} else if (size > 1024 * 1024) {
					return (size / 1024 / 1024).toFixed(2) + 'MB';	
				} else if (size > 1024) {
					return (size / 1024).toFixed(2) + 'KB';
				} else {
					return size.toFixed(2) + 'B';
				}
			}
		}

	const confirm = () => {
		let h = 0, k = 0, s = 0;
		const params: any = {
			folderId: resourceGroup.contentId_,
			folderName: resourceGroup.name_,
			highStorages: temp.highStorages.map((item: any) => {
				h += item.over ? 1 : item.space;
				return {
					disabled: item.disabled,
					space: item.over ? -1 : item.space * 1024 * 1024 * 1024, //约定超分就传-1 其余转化成G
					order: item.order,
					storageName: item.storageName,
					storageCode: item.storageCode,
					expansion: item.expansion == 'expansion' ? true : false, 
				};
			}),
			keyframeStorages: temp.keyframeStorages.map((item: any) => {
				k += item.over ? 1 : item.space;
				return {
					disabled: item.disabled,
					storageName: item.storageName,
					storageCode: item.storageCode,
					space: item.over ? -1 : item.space * 1024 * 1024 * 1024, //约定超分就传-1 其余转化成G
					order: item.order,
					expansion: item.expansion == 'expansion' ? true : false,
				};
			}),
			streamStorages: temp.streamStorages.map((item: any) => {
				s += item.over ? 1 : item.space;
				return {
					disabled: item.disabled,
					storageName: item.storageName,
					storageCode: item.storageCode,
					space: item.over ? -1 : item.space * 1024 * 1024 * 1024, //约定超分就传-1 其余转化成G
					order: item.order,
					expansion: item.expansion == 'expansion' ? true : false,
				};
			})
		};
		console.log(temp, 'temp', params, 'param', h, k, s);
		if (h === 0 || k === 0 || s === 0) {
			message.warning(`${h === 0 ? "高质量" : ''} ${k === 0 ? "关键帧" : ''} ${s === 0 ? "流媒体" : ''}${'存储须至少填一个'}`);
		} else {
			setStroageloading(true)
			rmanApis.updateStroage(params).then((res: any) => {
				if (res?.success) {
					modalClose();
					message.success('分配成功');
					// spaceManageOk({});
					setStroageloading(false)
				}
			}).catch((err: any) => {
				setStroageloading(false)
			})
		}
	};

	return <Modal
		title={title}
		open={modalVisible}
		width={750}
		className={'spaceManageModal'}
		destroyOnClose={true}
		footer={[
			<Button onClick={modalClose} key='0'>取消</Button>,
			<Button onClick={confirm} type='primary' key='1' loading={stroageloading}>确定</Button>
		]}
		onCancel={modalClose}>
		<div className='right'>
			<Tabs
				activeKey={activeTabs}
				tabPosition={'top'}
				onChange={(activeTabs) => {
					setActiveTabs(activeTabs);
					setList(temp[activeTabs]);
				}}>
				{
					Object.keys(temp)?.map((item: any) => {
						return <TabPane
							key={item}
							tab={<div className='tab-title'> <span>{enum_[item]}</span> </div>}>
							<Table
								rowKey="storageCode"
								columns={columns}
								showHeader={false}
								dataSource={list}
								pagination={false}
								components={{
									body: {
										wrapper: DraggableContainer,
										row: DraggableBodyRow
									}
								}} />
						</TabPane>;
					})}
			</Tabs>
		</div>
	</Modal>;
};

export default SpaceManageModal;