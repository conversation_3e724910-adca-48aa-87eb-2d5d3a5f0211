.download_modal {
  .smartTag,.analysis,.translate,.sensitiveFaceTag,.sensitiveWordTag,.summaryTag, .screenshotTag{
    label {
      margin-right: 10px;
    }
  }
}
.language-modal-content{
  display: flex;
  .left-item, .right-item{
    margin-bottom: 5px;
  }
  .right{
    margin-left: 20px;
    display: flex;
    &>span{
      margin-top: 5px;
    }
  }
}
.intelligentanalysismodal {
  box-shadow: 0px 2px 12px 0px rgba(0,0,0,0.06);
  border-radius: 4px;
  .ant-modal-header {
    height: 53px;
  }
  .ant-modal-body{
    .singleDisplay{
      >.head{
        width: 224px;
        height: 22px;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 600;
        color: #4A4F64;
        line-height: 22px;
        margin: 0 auto;
      }
      .body{
        display: flex;
        flex-wrap: wrap;
        margin:30px 0 0 0;
        >div{
          width: 155px;
          height: 60px;
          margin: 0 0 20px 20px;
          border-radius: 6px;
          position: relative;
          cursor: pointer;
          overflow: hidden;
          transition: all .3s;
          .item{
            display: flex;
            flex-direction: row;
            align-items: center;
            font-size: 16px;
            width: 100%;
            height: 100%;
            .tag{
              width: 4px;
              height: 16px;
              margin-left: 10px;
              background: #FFFFFF;
              border-radius: 2px;
            }
            .title{
              color: #FFFFFF;
              margin-left: 6px;
              white-space: nowrap;
            }
          }
          .mask{
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            display: none;
          }
          &:hover{
            transform: scale(1.05);
          }
          &.selected{
            .select{
              position: absolute;
              display: block;   
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
            }
            .mask{
              display: block;
              background: rgba(0,0,0,0.46);
            }
          }
          .select{
            display: none;
          }
          &.disabled{
            cursor: no-drop;
            .mask{
              display: block;
              background: rgba(0,0,0,0.2);
            }
          }
          &.voice{
            background: url('/rman/static/images/smart/voice.png') no-repeat 100% 100%;
          }
          &.translate{
            background: url('/rman/static/images/smart/translate.png') no-repeat 100% 100%;
          }
          &.tag{
            background: url('/rman/static/images/smart/tag.png') no-repeat 100% 100%;
          }
          &.knowledge{
            background: url('/rman/static/images/smart/knowledge.png') no-repeat 100% 100%;
          }
          &.person{
            background: url('/rman/static/images/smart/face.png') no-repeat 100% 100%;
          }
          &.sensitiveword{
            background: url('/rman/static/images/smart/word.png') no-repeat 100% 100%;
          }
          &.abstract{
            background: url('/rman/static/images/smart/abstract.png') no-repeat 100% 100%;
          }
          &.screenshot{
            background: url('/rman/static/images/smart/summary.png') no-repeat 100% 100%;
          }
          &.disassemble{
            background: url('/rman/static/images/smart/summary.png') no-repeat 100% 100%;
          }
          &.videoCheck{
            background: url('/rman/static/images/smart/videoCheck.png') no-repeat 100% 100%;
          }
          &.peopleNum{
            background: url('/rman/static/images/smart/people.png') no-repeat 100% 100%;
          }
          &.object{
            background: url('/rman/static/images/smart/object.png') no-repeat 100% 100%;
          }
          &.belcanto{
            background: url('/rman/static/images/smart/belcanto.png') no-repeat 100% 100%;
          }
          &.face{
            background: url('/rman/static/images/smart/face.png') no-repeat 100% 100%;
          }
          &.voiceKnowledge{
            background: url('/rman/static/images/smart/knowledge.png') no-repeat 100% 100%;
          }
          &.content{
            background: url('/rman/static/images/smart/contentCheck.png') no-repeat 100% 100%;
          }
          
        }
      }
    }
    .ant-table-container{
      .ant-table-thead{
        .ant-table-cell{
          padding: 10px 8px;
        }
      }
      .ant-table-tbody{
        tr td{
          border: 0;
          .time{
            white-space: nowrap;
          }
        }
      }
    }
  }
  .ant-modal-footer{
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: center;
    border: 0;
    .intelligentanalysismodal-title {
      color: #fff;
  
      span {
        color: #E9D7D7;
        font-size: 12px;
        margin-left: 15px;
      }
    }
    .intelligentanalysismodal-footer {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
  
      span {
        color: #8f8a8a;
        font-size: 12px;
        margin-left: 15px;
      }
    }
    .footer-btn{
      display: flex;
      align-items: flex-end;
    }
  }
}
@media screen and(max-width:768px){
  .intelligentanalysismodal{
    .ant-modal-body{
      padding: 0;
      .ant-table-thead{
        tr th{
          >div{
            display: flex;
            flex-direction: column;
            align-items: center;
            white-space: nowrap;
            >label{
              margin: 0 !important;
            }
          }
        }
      }
    }
    .ant-modal-footer{
      flex-direction:column !important;
      .intelligentanalysismodal-footer{
        text-align: left;
      }
      .footer-btn{
        justify-content:flex-end;
      }
    }
  }
}
.mobileModal_{
  top:400px !important;
}