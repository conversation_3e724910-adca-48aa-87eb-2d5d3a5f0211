import React from "react";
import { FC, useEffect, useState, useRef } from 'react';
import Cropper from "react-cropper";
import "cropperjs/dist/cropper.css";
import { Modal } from "antd";

interface CropperModalProps {
  image: string;
  visible: boolean;
  cropperOk: (newImage: string) => void;
  cropperCancel: () => void;
}

const CropperModal: FC<CropperModalProps> = ({ image, visible, cropperOk, cropperCancel }) => {
  const cropperRef = useRef<HTMLImageElement>(null);
  const [newImage, setNewImage] = useState<string>("");
  const onCrop = () => {
    const imageElement: any = cropperRef.current;
    const cropper: any = imageElement.cropper;
    setNewImage(cropper?.getCroppedCanvas()?.toDataURL("image/jpg") ?? '');
  };
  return (
    <Modal title="图片裁剪" visible={visible} onCancel={cropperCancel} onOk={() => cropperOk(newImage)}>
      <Cropper
      src={image}
      style={{ height: 300, width: "100%" }}
      aspectRatio={16 / 9}
      zoomable={true}
      rotatable={true}
      guides={false}
      crop={onCrop}
      ref={cropperRef} />
      
    </Modal>);

};

export default CropperModal;