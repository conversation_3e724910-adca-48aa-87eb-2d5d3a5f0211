* {
  box-sizing: border-box;
}
#root,
body,
html {
  height: 100%;
}
body .ant-pagination-item, body .ant-pagination-options-quick-jumper input{
  border-radius: 5px;
}
body .ant-input, body .ant-input-affix-wrapper, body .ant-btn:not(.ant-btn-round, .ant-btn-circle), body .ant-select:not(.ant-select-customize-input) .ant-select-selector{
  border-radius: 6px;
}
.contentlist_container {
  height: 100%;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  position: relative;

  .content_bottom {
    height: 100%;
    display: flex;
    background: #F7F9FA;
    .resourcephoto{
      width: 143px;
      height: 40px;
      margin: 0px auto;
      border-radius: 25px;
    }
    .directory_tree {
      width: 260px;
      border: 1px solid #DEDEDE;
      border-top: 0;
      // margin-right: 20px;
      background: white;
      padding-top: 16px;
      flex-shrink: 0;
      box-shadow: 0px 0px 4px 0px rgba(177, 177, 177, 0.5);
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding-bottom: 10px;
      .personalStorage{
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        >div{
         width: 90%;
         >div.ant-progress{
          .ant-progress-inner{
            .ant-progress-bg{
              background-color: var(--primary-color);
            }
          }
         }
         >div:last-child{
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          font-size: 12px;
          >div{
            >span:last-child{
              opacity: .5;
            }
          }
          .applyContainer{
            .audit{
              color: #FFA630;
            }
            .pass{
              color: #549CFF;
            }
            .fail{
              color: #FD7676;
            }
          }
         }
        }
      }
    }

    .content {
      .inner{
        padding: 20px 20px 0 20px;
        .goback{
          height: 22px;
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 600;
          color: #525252;
          line-height: 22px;
          cursor: pointer;
        }
        .shareinfo{
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #525252;
          padding: 10px 15px 0 15px;
          >span{
            margin-right: 10px;
          }
        }
      }
      .ant-tabs{
        .ant-tabs-nav{
          .ant-tabs-nav-list{
            .ant-tabs-tab{
              .ant-badge{
                .ant-badge-multiple-words{
                  padding: 0 3px;
                }
                .ant-badge-count{
                  right: -10px;
                }
              }
            }
            .ant-tabs-tab-active{
              .ant-badge{
                >span{
                  color: var(--primary-color);
                }
              }
            }
          }
        }
      }
      .ant-tabs{
        .ant-tabs-nav-list{
          .ant-tabs-tab:first-child::after{
            width: 10px;
            height: 10px;
            background-color: red;
          }
        }
      }
      flex: 1;
      // display: flex;
      // flex-direction: column;
      // height: 100%;
      background: white;
      overflow: hidden;
      width: 1px;
      // padding: 15px;
      // margin: 15px;
      // box-shadow: 0px 0px 4px 0px rgba(177, 177, 177, 0.5);

      // border: 1px solid #000000;
      .auto {
        //overflow: auto;
        // background: #F7F9FA;
        display: flex;
        flex-direction: column;
        height: 100%;
      }
      .auto_shared {
        //overflow: auto;
        display: flex;
        flex-direction: column;
        height: calc(100% - 48px);
        padding-top: 12px;
      }

      .top_search {
        // height: 114px;
        // margin-bottom: 20px;
        margin: 20px 20px 0 20px;
        padding-bottom: 20px;
        border-bottom:1px solid #dedede;
        // height: 141px;
        display: flex;
        flex-direction: column;
        align-items: center;
        transition: all .5s;

        .drop_down {
          width: 100%;
          height: 100%;
          transition: all .5s;
          background: #FFFFFF;
          display: flex;
          // height: 90px;
          justify-content: space-between;
          // border-bottom: 1px solid #D3D3D3;
          // box-shadow: 0px 0px 4px 0px rgb(177 177 177 / 50%);
          overflow: hidden;
          position: relative;
        }
      }

      .top_search_down {
        height: 164px;

        .drop_down {
          height: 140px;
        }
      }
      .shareMyself{
        padding: 0 15px;
        .ant-tabs-nav{
          margin: 0;
        }
      }
      .auto_shared,.auto_shared_inner{
        .content_box {
          padding: 0px 15px 20px 15px !important;
        }
      }

      .content_box {
        // height: 78%;
        
        background: #FFFFFF;
        padding: 20px 15px 20px 15px;
        box-shadow: 0px 2px 5px 0px rgba(0,0,0,0.0700);
        ::-webkit-scrollbar{
          width: 5px;
        }
        ::-webkit-scrollbar-track {/*滚动条里面轨道*/
          --webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
          border-radius: 10px;
          background: #EDEDED;
        }
        ::-webkit-scrollbar-thumb {/*滚动条里面小方块*/
          border-radius: 10px;
          //  --webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
          // background: #535353;
          background: var(--primary-color);
        }
        width: 100%;
        flex: 1;
        display: flex;
        flex-direction: column;

        // height: calc(~"100vh - 250px");
        // flex: 1;

        .box_top {
          // margin-top: -26px;
          height: 30px;
          // border-bottom: 1px solid rgba(211, 211, 211, 1);
          display: flex;
          align-items: center;
          justify-content: space-between;
          // padding: 0 15px;
          flex-shrink: 0;

          .top_left {
            display: flex;
            align-items: center;
            .ant-btn-primary{
              border-radius: 16px;
            }
            .ant-btn-link{
              padding: 0 10px;
              color: #525252;
              &:hover{
                color:var(--primary-color)
              }
              &[disabled]{
                color: rgba(0, 0, 0, 0.25)
              }
            }
            .ant-btn-link:not(:last-child)::after {
              content: "";
              display: inline-block;
              width: 1px;
              height: 16px;
              background: #D8D8D8;
              right: 0;
              top: 4px;
              position: absolute;
            }
            .myshareDom{
              display: flex;
              >div{
                padding: 0 10px;
                position: relative;
                cursor: pointer;
                &.disabled{
                  cursor: no-drop;
                  color: rgba(0, 0, 0, 0.25);
                }
                >span{
                  margin-right: 8px;
                }
                &:not(:last-child)::after{
                  content: "";
                  display: inline-block;
                  width: 1px;
                  height: 16px;
                  background: #D8D8D8;
                  right: 0;
                  top: 4px;
                  position: absolute;
                }
              }
              .ant-btn{
                height: 25px;
                display: flex;
                justify-content: center;
                align-items: center;
              }
            }
            .myshareInnerDom{
              display: flex;
              padding: 5px 20px 0 13px;
              .ant-btn{
                width: 36px;
                height: 25px;
                display: flex;
                justify-content: center;
                align-items: center;
              }
            }
          }
          .top_right {
            display: flex;
            align-items: center;
            font-size: 16px;
            height: 48px;

            .ant-select-selection-item {
              display: flex;
              align-items: center;
            }

            .icon {
              font-size: 15px;
              margin-left: 4px;
              color: var(--primary-color);
            }

            .mode_switch {
              margin-left: 15px;
              cursor: pointer;
              transition: all .2s ease 0s;
              color: #919596;

              .active {
                color: var(--primary-color);
              }
            }
          }
        }

        .box_bottom {
          flex-shrink: 0;
          flex: 1;
          >.opera_div{
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px 10px 10px;
            ._left{
              display: flex;
              flex-direction: row;
              justify-content: space-between;
              align-items: center;
              .ant-breadcrumb {
                a{
                  color:var(--primary-color);
                }
                ol > li:last-child a{
                  color:rgba(0, 0, 0, 0.85);
                  cursor: default;
                }
                span:last-child a{
                  color:rgba(0, 0, 0, 0.85);
                  cursor: default;
                }

                .ant-breadcrumb-separator{
                  margin:0 4px !important;
                }
              }
            }
            
            ._right{
              display: flex;
              flex-direction: row;
              justify-content: space-between;
              align-items: center;
              .mode_switch {
                margin-left: 15px;
                cursor: pointer;
                transition: all .2s ease 0s;
                color: #919596;
  
                .active {
                  color: var(--primary-color);
                }
              }
            }
          }
          // height: calc(~"100vh - 250px");

          .contentitem_box {
            width: 100%;
            // height: calc(~"100vh - 340px");
            // overflow-y: auto;
            // padding-bottom: 26px;

            .ant-empty {
              margin: 130px 8px
            }
          }
          .contentitem_box_empty{
            height: calc(100vh - 345px);
          }
          .contentitem_box_nosearch {
            width: 100%;
            // height: calc(~"100vh - 190px");
            .ant-empty {
              margin: 130px 8px
            }
          }
          .contentitem_shared_nosearch {
            // margin-top: 5px;
            width: 100%;
            // height: calc(~"100vh - 282px");
            .ant-empty {
              margin: 130px 8px
            }
          }
          .contentitem_box_video{
            margin-top: 5px;
            width: 100%;
          }

          .ant-checkbox-group, .ant-radio-group {
            display: flex;
            flex-wrap: wrap;
            align-content: flex-start;
            font-size: 13px;
          }
          .height1{
            // height: calc(100vh - 314px);
            // height: calc(100vh - 351px);
            height: calc(100vh - 280px);
            overflow-y: auto;
          }
          .height1_empty{
            overflow-y: auto;
          }
          .height2 {
            display: inline-block;
            // height: calc(100vh - 366px);
            // height: calc(100vh - 403px);
            height: calc(100vh - 328px);
            overflow-y: auto;
          }
          .height3_list {
            display: inline-block;
            // height: calc(100vh - 230px);
            height: calc(100vh - 267px);
            overflow-y: auto;
          }
          .height3_shared_list {
            display: inline-block;
            // height: calc(100vh - 332px);
            height: calc(100vh - 369px);
            overflow-y: auto;
          }
          .height3 {
            // height: calc(100vh - 180px);
            height: calc(100vh - 217px);
            overflow-y: auto;
          }
          .height3_shared {
            height: calc(100vh - 284px);
            overflow-y: auto;
          }
        }
        //展开时
        .box_bottom_expand{
          .height1{
            // height: calc(100vh - 430px);
            height: calc(100vh - 380px);
          }
          .height2 {
            display: inline-block;
            height: calc(100vh - 482px);
            overflow-y: auto;
          }
          .height3_list {
            height: calc(100vh - 346px);
          }
          .height3_shared_list {
            height: calc(100vh - 446px);
          }
          .height3 {
            height: calc(100vh - 296px);
          }
        }
        //非共享目录下
        .box_bottom_other{
          .height1{
            height: calc(100vh - 330px);
          }
          .height2 {
            display: inline-block;
            height: calc(100vh - 374px);
            overflow-y: auto;
          }
          .height3_list {
            height: calc(100vh - 246px);
          }
          .height3_shared_list {
            height: calc(100vh - 303px);
          }
          .height3 {
            height: calc(100vh - 206px);
          }
        }
        .video_height {
          height: calc(100vh - 300px);
          overflow-y: auto;
        }
        .myVideo_box_bottom {
          display: flex;
          flex-direction: column;
          overflow: hidden;
          .contentitem_box_video {
            flex: 1;
            overflow: hidden;
            .height2 {
              height: calc(100% - 54px);
            }
          }
        }
        .hasTop_box_bottom {
          .video_height {
            height: calc(100vh - 329px);
          }
        }

        .myVideo_box_bottom {
          display: flex;
          flex-direction: column;
          overflow: hidden;
          .contentitem_box_video {
            flex: 1;
            overflow: hidden;
            .height2 {
              height: calc(100% - 54px);
            }
          }
        }
        .pagination {
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          // padding-top: 15px;
        }
      }
      .myvideo_box {
        overflow: hidden;
      }

      .myvideo_box {
        overflow: hidden;
      }

      .recyclebin_box {
        height: calc(~"100vh - 100px");
        display: flex;
        flex-direction: column;

        //.box_top {
        //  flex-shrink: 0;
        //}

        .box_bottom {
          // overflow: auto;
          // height: calc(100vh - 288px);
          // overflow-y: auto;
        }


        //.box_bottom {
        //  height: calc(~"100vh - 160px");
        //
        //  .contentitem_box {
        //    height: calc(~"100vh - 220px");
        //  }
        //}
      }
    }
  }
}

.applyStorageModal{
  .ant-modal-body{
   >div:first-child{
    display: flex;
    align-items: center;
    >label{
      width: 120px;
      text-align: right;
    }
    .ant-input{
      width: 60%;
    }
   } 
   >div.applyReason{
    margin-top: 20px;
    display: flex;
    >label{
      width: 120px;
      text-align: right;
    }
    .ant-input{
      width: 60%;
    }
   }
   .result{
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin-top: 20px;
    .audit{
      color: #FFA630;
    }
    .pass{
      color: #549CFF;
    }
    .fail{
      color: #FD7676;
    }
   } 
   .failResult{
    display: flex;
    align-items: flex-start;
    margin-top:20px;
    color: #FD7676;
    >label{
      width: 120px;
      text-align: right;
    }
    .fail{
      flex: 1 1;
    }
   }
  }
  .ant-modal-footer{
    display: flex;
    justify-content: center;
  }
}

//h5 移动端
@media screen and (max-width:768px){
  body{
    width: 100%;
    height: 100%;
    min-width: unset !important;
    #root{
      .content_view{
        width: 100%;
        height: 100%;
        .basic_height{
          height: 100%;
          .uf-header-wrapper{
            padding: 0 15px !important;
          }
          .basic_content{
            flex: none;
            .contentlist_container{
              .content_bottom{
                .directory_tree{
                  display: flex;
                  width: 100%;
                  &.none{
                    display: none;
                  }
                }
                .content{
                  width: 100%;
                  &.none{
                    display: none;
                  }
                  &.mobile_content{
                    .top_search {
                      padding: 0 0 10px 0;
                      margin: 10px 2% 0 2%;
                    }
                    .content_box {
                      padding: 0px 5px 10px 10px !important;
                      height: calc(100% - 53px);
                     .box_bottom {
                        height: calc(100% - 25px);
                        .opera_div{
                          padding: 10px 0px 10px 0px !important;
                          ._left{
                            flex: 1 1;
                            justify-content:flex-start !important;
                            .ant-checkbox-wrapper{
                              white-space: nowrap;
                              .ant-checkbox + span{
                                padding: 0 5px !important;
                              }
                            }
                            >div{
                              margin-left: 0 !important;
                              .ant-btn-primary{
                                padding: 4px 8px !important;
                                .anticon + span{
                                  margin-left: 4px;
                                }
                              }
                            }
                            .mobile_btns_myshare{
                              display: flex;
                              >.ant-btn{
                                padding: 0 10px;
                                margin-right: 10px;
                                &.disabled{
                                  color: rgba(0, 0, 0, 0.25)
                                }
                              }
                            }
                            .shareinfo{
                              display: flex;
                              flex-direction: column;
                              flex: 1 1;
                              align-items: center;
                              justify-content: center;
                            }
                            .links_box{
                              // display: flex;
                              // align-items: center;
                              // justify-content:space-evenly;
                              // flex: 1 1;
                              margin-left: 10px !important;
                              .ant-btn{
                                height: 32px !important;
                                // margin-left: 10px;
                                &.disabled{
                                  color: rgba(0, 0, 0, 0.25)
                                }
                              }
                            }
                          }
                          ._right{
                            >div{
                              .ant-select{
                                width: auto !important;
                                .ant-select-selector{
                                  padding: 0 8px;
                                }
                              }
                            }
                            .mode_switch{
                              display: none !important;
                            }
                          }
                        }
                        .mobile_left{
                          display: flex;
                          >span:first-child{
                            color: var(--primary-color);
                            transform: rotate(-90deg);
                          }
                          .ant-breadcrumb {
                            width: 100%;
                            a{
                              color:var(--primary-color);
                            }
                            ol {
                              display: flex;
                              flex-direction: row;
                              flex-wrap: nowrap;
                              .root_{
                                a{
                                  color:var(--primary-color) !important;
                                }
                              }
                              > li{
                                white-space: nowrap;
                                &:last-child{
                                  cursor: default;
                                  text-overflow: ellipsis;
                                  overflow: hidden;
                                  white-space: nowrap;
                                  a{
                                    color:rgba(0, 0, 0, 0.85);
                                  }
                                }
                              }
                            }
                            span:last-child a{
                              color:rgba(0, 0, 0, 0.85);
                              cursor: default;
                            }
            
                            .ant-breadcrumb-separator{
                              margin:0 4px !important;
                            }
                          }
                        }
                        .contentitem_box_video,.contentitem_box_nosearch{
                          height: calc(100% - 90px);
                          .height{
                            height: 100% !important;
                          }
                        }
                        .contentitem_box{
                          height: calc(100% - 78px);
                          .height1{
                            height: 100% !important;
                          }
                          .content_item{
                            .imgbox{
                              div:first-child{  
                                height: 100px !important;
                              }
                            }
                            .content_title{
                              margin-top: 7px !important;
                            }
                          }
                        }
                        .contentitem_shared_nosearch{
                          height: calc(100% - 78px);
                          .height3_shared{
                            height: 100% !important;
                            overflow-y: auto;
                          }
                          .content_item{
                            .imgbox{
                              div:first-child{  
                                height: 100px !important;
                              }
                            }
                            .content_title{
                              margin-top: 7px !important;
                            }
                          }
                        }
                      } 
                      .myCollection{
                        .links_box{
                          flex:none !important;
                          .ant-btn{
                            margin-left: 10px !important;
                          }
                        }
                      }
                    }
                    .auto_shared_inner{
                      height: 100%;
                      .content_box {
                        height: 100%;
                        .box_bottom{
                          height: calc(100% - 25px);
                          .contentitem_shared_nosearch{
                            height: calc(100% - 86px);
                          }
                        }
                      }
                    }
                    .recyclebin_box{
                      .opera_div{
                        .links_box{
                          flex: none !important;
                          .ant-btn{
                            margin-right: 10px !important;
                          }
                        }
                      }
                      .box_bottom{
                        height: calc(100% - 38px);
                        .contentitem_box_nosearch{
                          height: calc(100% - 78px);
                          .height3{
                            height: 100%;
                          }
                        }
                      }
                    } 
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}