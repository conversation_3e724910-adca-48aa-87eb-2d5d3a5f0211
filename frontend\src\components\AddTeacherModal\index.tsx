import React, {
  useMemo,
  useState,
  useEffect,
  KeyboardEvent,
  FocusEvent,
  useRef,
} from 'react';
import {
  Modal,
  Input,
  Button,
  Tree,
  message,
  Form,
  Table,
  Select,
  Checkbox,
  Tabs
} from 'antd';
import { useIntl, useSelector, useDispatch, useHistory } from 'umi';
import { TeamOutlined } from '@ant-design/icons';
import './index.less';
import {
  childOrganization,
  codeOrganization,
  getAllUserByOrg,
  rootOrganization,
} from '@/service/choicepeople';
const { DirectoryTree } = Tree;
const { Option } = Select;
interface CreateModalProps {
  modalVisible: boolean;
  modalClose: () => void;
  chooseedTeacher: any;
  callback?: (lists:any) => void; // 刷新
}
interface ITreeItemProps {
  code: string;
  title: string;
  key: string;
  children?: Array<ITreeItemProps>;
  icon?: React.ReactNode;
  parentName?: string;
  parentId?: number;
  parentCode?: string;
}
const AddTeacherModal: React.FC<CreateModalProps> = (props) => {
  const history: any = useHistory();
  const [editform] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const { modalClose, modalVisible, callback, chooseedTeacher } = props;
  const [rootKeys, setRootKeys] = useState<Array<string>>([]);
  const [treeData, setTreeData] = useState<Array<ITreeItemProps>>([]);
  const [selectCode, setSelectCode] = useState<string>('');
  const [current, setCurrent] = useState<number>(1);
  const [keyword, setKeyword] = useState<string>('');
  const [account, setAccount] = useState<string>('');
  const [total, setTotal] = useState<number>(0);
  const [pageSize, setPageSize] = useState<number>(30);
  const [selectedRowKeys, setSelectedRowKeys] = useState<Array<any>>([]);
  const [selectedRows, setSelectedRows] = useState<Array<any>>([]);
  const [userList, setUserList] = useState<Array<any>>([]);
  const listRef = useRef<any>([])
  const [searchform] = Form.useForm();
  const [tabKey, setTabKey] = useState<string>('1');
  const options = [
    { label: '修改基本信息', value: 'modifyBasicInfo' },
    { label: '修改章节', value: 'chapterRevision' },
    { label: '修改课程公告', value: 'modifyCourseNotice' },
    { label: '学生管理', value: 'studentManagement' },
    { label: '教学团队管理', value: 'teachingTeamManage' },
    { label: '修改学习设置', value: 'modifySettings' },
  ];
  useEffect(() => {
    if (modalVisible) {
      console.log('chooseedTeacher',chooseedTeacher)
      getRoot();
      listRef.current = chooseedTeacher; 
      setSelectedRowKeys(
        chooseedTeacher.map((n: any) => {
          return n.user_code;
        }),
      );
    }
  }, [modalVisible]);
  useEffect(() => {
    if (selectCode) {
      fetchAllUser();
    }
  }, [current,pageSize, keyword, selectCode, account, tabKey]);
  const modalOk = () => {
    console.log('selectedRows',selectedRows);
    if(callback){
      callback(selectedRows)
    }
    modalClose()
  };
  const getRoot = () => {
    codeOrganization('r_teacher').then((res: any) => {
      console.log(res);
      if (res && res.errorCode === 'success') {
        const rootData = res.extendMessage.map((item: any) => {
          return {
            title: item.organizationName,
            key: item.id,
            code: item.organizationCode,
          };
        });
        setTreeData(rootData);
        setSelectCode(rootData[0].code);
        if (rootData.length) {
          onLoadChild(rootData[0], true);
        }
      }
    });
  };
  const onLoadChild = (node: any, isRoot: boolean = false): Promise<any> => {
    const { key, children, code, title } = node;
    return new Promise(async (resolve) => {
      if (children) {
        resolve(null);
        return;
      }
      function updateTreeData(
        list: ITreeItemProps[],
        key: React.Key,
        children: ITreeItemProps[],
      ): ITreeItemProps[] {
        return list.map((node) => {
          if (node.key === key) {
            return {
              ...node,
              children,
            };
          } else if (node.children) {
            return {
              ...node,
              children: updateTreeData(node.children, key, children),
            };
          }
          return node;
        });
      }

      const res: any = await childOrganization(code);
      if (res && res.errorCode === 'success') {
        setTreeData((origin) =>
          updateTreeData(
            origin,
            key,
            res.extendMessage.map((item: any) => {
              return {
                title: item.organizationName,
                key: item.id,
                code: item.organizationCode,
                parentName: title,
                parentId: key,
                parentCode: code,
              };
            }),
          ),
        );
        if (isRoot) {
          setRootKeys([key]);
        }
        resolve(null);
      }
    });
  };
  // 目录树选择
  const onSelect = (selectedKeys: any, e: any) => {
    setSelectCode(e.node.code);
  };
  const searchRole = () => {
    setKeyword(searchform.getFieldsValue().keyword);
    setAccount(searchform.getFieldsValue().account);
    setCurrent(1);
    // setCurrent(1);
  };
  const columns: any = [
    {
      title: '姓名',
      dataIndex: 'nick_name',
    },
    {
      title: '性别',
      dataIndex: 'sexshow',
      width:'7%'
    },
    {
      title: '工号/学号',
      dataIndex: 'accountshow',
    },
    {
      title: '学院',
      dataIndex: 'orgname',
    },
  ];
  const pagination = {
    total,
    current,
    showTotal: (total:any) => `共 ${total} 条`,
    showSizeChanger: true,
    showQuickJumper: true,
    onChange: (page: number,size:any) =>{
      setPageSize(size)
    },
   
    defaultPageSize: pageSize,
  };
  const rowSelection = {
    type: 'checkbox',
    onChange: (newSelectedRowKeys: Array<any>, selectedRows: any) => {
      let temp:any = [];
      const a:any = {}
      console.log(newSelectedRowKeys);
      console.log(selectedRows);
      // 处理antd 只保留keys的缺陷 只能自己单独存；
      temp = listRef.current.concat(selectedRows).filter(Boolean).filter((item:any) => {
        if (!a[item.user_code]) {
          a[item.user_code] = true
          return newSelectedRowKeys.includes(item.user_code)
        } else {
          return false
        }
      })
      listRef.current = temp;
      console.log('listRef.current',listRef.current)
      
      setSelectedRowKeys(newSelectedRowKeys);
      // setSelectedRows(selectedRows)
      setSelectedRows(listRef.current) 
    },
    preserveSelectedRowKeys: true,
    selectedRowKeys,
    getCheckboxProps: (record: any) => ({
      disabled: record.disabled, // Column configuration not to be checked
    }),
  };
  // 切换页码
  const handleTableChnage = (params: any) => {
    setCurrent(params.current);
  };
  // 获取列表
  const fetchAllUser = async () => {
    setLoading(true);
    let param: any = {
      organizationCode: selectCode,
      keyword,
      page: current,
      size: pageSize,
      include_roles: true,
      fill_detail: false,
      roleCode: tabKey == '1' ? "r_teacher": "r_student",
      isIncludeSubUser: true
    };
    if (account) {
      param = {
        organizationCode: selectCode,
        extend: {
          account,
        },
        keyword,
        page: current,
        size: pageSize,
        roleCode: tabKey == '1' ? "r_teacher": "r_student",
        include_roles: true,
        fill_detail:false
      };
    }
    const res = await getAllUserByOrg(param);
    if (res && res.errorCode === 'success') {
      let data = res.extendMessage.results;
      data.map((item: any) => {
        item.sexshow = item.extend.sex;
        item.accountshow = item.extend.account;
        item.orgname = item.extend.school+(item.extend.college?'/'+item.extend.college:'');
        item.jurisdiction = 1;
        item.rolelist = item.roles;
        item.disabled = selectedRowKeys.includes(item.user_code);
        return item;
      });
      console.log('data',data)
      setUserList(data);
      setTotal(res.extendMessage.recordTotal);
    }
    setLoading(false);
  };

  const items = useMemo(() => 
    [
      {
        key: "1",
        label: "教师",
        children: null
      },
      {
        key: "2",
        label: "学生",
        children: null
      },
    ],  [modalVisible])

  return (
    <>
      <Modal
        destroyOnClose
        title="添加人员"
        visible={modalVisible}
        closable={false}
        width={1200}
        footer={[
          <Button key="back" onClick={modalClose}>
            取消
          </Button>,
          <Button 
            key="submit" 
            type="primary" 
            disabled={selectedRowKeys.length===0}
            onClick={modalOk}>
            确定
          </Button>,
        ]}
      >
         <Tabs onChange={(value) => setTabKey(value)} activeKey={tabKey} items={items} />
        <div className="add-student">
          <div className="tree">
            {!!rootKeys.length && (
              <Tree
                icon={<TeamOutlined />}
                defaultExpandedKeys={rootKeys}
                defaultSelectedKeys={[rootKeys[0]]}
                loadData={onLoadChild}
                treeData={treeData}
                onSelect={onSelect}
              />
            )}
          </div>
          <div className="content">
            <div className="search_student">
              <Form layout={'inline'} form={searchform}>
                {/* <Form.Item label="学号" name="account">
                  <Input autoComplete="off" placeholder="请输入学号" />
                </Form.Item> */}
                <Form.Item label="姓名" name="keyword">
                  <Input autoComplete="off" placeholder="请输入姓名" onPressEnter={searchRole} />
                </Form.Item>
                <Form.Item>
                  <Button type="primary" onClick={searchRole}>
                    检索
                  </Button>
                </Form.Item>
              </Form>
            </div>
            <Table
              loading={loading}
              dataSource={userList}
              columns={columns}
              rowKey="user_code"
              size="small"
              rowSelection={rowSelection as any}
              pagination={{
                ...pagination,
              }}
              scroll={{ y: 350 }}
              onChange={handleTableChnage}
            />
          </div>
        </div>
      </Modal>
    </>
  );
};

export default AddTeacherModal;
