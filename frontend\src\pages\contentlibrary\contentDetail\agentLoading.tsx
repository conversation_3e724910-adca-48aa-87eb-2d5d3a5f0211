import React, { useState, useEffect } from 'react';
import './agentLoading.less';

const AgentLoading = ({ progress }: { progress: number }) => {

    // useEffect(() => {
    //     // 模拟加载进度
    //     const interval = setInterval(() => {
    //         setProgress((prev) => {
    //             if (prev >= 100) {
    //                 clearInterval(interval);
    //                 return 100;
    //             }
    //             return prev + 1;
    //         });
    //     }, 50); // 每50ms增加1%
    //     return () => clearInterval(interval);
    // }, []);

    return (
        <div className="circle-loader-container">
            <div className="circle-loader">
                <div
                    className="circle-progress"
                    style={{
                        background: `conic-gradient(
              var(--primary-color) ${progress * 3.6}deg,
              var(--el-color-primary-light-8) 0deg
            )`,
                    }}
                ></div>
                <div className="inner-circle">
                    <span className="percentage"
                    >{progress}%</span>
                </div>
            </div>
            <div className='text-loading'>向量分析中 请稍后
                <span className="dots">
                    <span>.</span>
                    <span>.</span>
                    <span>.</span>
                    <span>.</span>
                    <span>.</span>
                    <span>.</span>
                </span>
            </div>
        </div>
    );
};

export default AgentLoading;
