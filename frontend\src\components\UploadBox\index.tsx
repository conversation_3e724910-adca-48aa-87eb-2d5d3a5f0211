import React, {
  FC,
  useState,
  useEffect,
  ChangeEvent,
  useRef,
  useMemo,
} from 'react';
import { Tag, Upload } from 'antd';
import { Modal, Button, message, TreeSelect, Table, Tabs } from 'antd';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { PlusOutlined, CloseOutlined, ForwardFilled, BackwardOutlined,InboxOutlined,PlusCircleOutlined,DeleteOutlined,PlusCircleFilled} from '@ant-design/icons';
import { FileList } from './type';
import './index.less';
import { useIntl, useSelector, IUpload, useDispatch, useHistory } from 'umi';
import { copyObject, asyncLoadScript, debounce, getUnique,computeFileInfoMD5 } from '@/utils';
import uploader from '@/components/upload/core/uploader';
import uploadTypes from '@/types/uploadTypes';
import BasicMetadata, {
  IBasicMetadataRef,
} from '@/components/basicMetadata/basicMetadata';
import ResourceModal from '@/components/ResourceModal';
import ResourcePreviewModal from '@/components/ResourcePreviewModal';
import { IFormItem } from '@/types/entityTypes';
import uploadApis from '@/service/uploadApis';
import WebUploader from './WebUploader';
import { IconFont } from '../iconFont/iconFont';
import ctyun from '@/otherStorage/ctyun';
import aliyun from '@/otherStorage/aliyun';
// import QRCode from 'qrcode.react';

import hwyun from '@/otherStorage/hwyun';
import axios from 'axios'
const { TabPane } = Tabs;

interface CreateModalProps {
  targetFolder: string;
  folderName: string;
  breadCrumb?: any;
}
const { Dragger } = Upload;
import rmanApis from '@/service/rman';
import { IPermission } from '@/models/permission';
import globalParams, { ModuleCfg } from '@/permission/globalParams';
import { use } from 'echarts';
import ClassificationModal from '../ClassificationMoal';
import entityApis from '@/service/entityApis';
import contentListApis from '@/service/contentListApis';

const UploadBox: React.FC<CreateModalProps> = (props, ref) => {
  const { breadCrumb } = props;
  const {
    showModal,
    tasks,
    taskPanls,
    fields,
    orginTasks,
    uploadformat,
    uploadformatEnum
  } = useSelector<{ upload: IUpload }, IUpload>(({ upload }) => {
    return upload;
  });
  const  {mobileFlag}  = useSelector<{ config: any }, any>(
    ({config})=>config
 );
 const { modules,permissions, rmanGlobalParameter } = useSelector<
  { permission: any },
  IPermission
  >(({ permission }) => permission);
  let history: any = useHistory();
  let showVideoResouce = history.location?.query?.showVideoResouce || ''

  const dispatch = useDispatch();
  const onCancel = () => {
    setEditNumber(0);
    dispatch({
      type: 'upload/changeTasks',
      payload: {
        value: [],
      },
    });
    dispatch({
      type: 'upload/changeModal',
      payload: {
        value: false,
      },
    });
    props.refresh()

  };
  const [editNumber, setEditNumber] = useState<number>(0); //正在编辑的素材
  const metadataRef = useRef<IBasicMetadataRef | null>(null);
  const [webUploader, setWebUpolader] = useState<any>(null);
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [batchSettingVisible, setBatchSettingVisible] = useState<number>(0); //0 关闭 1 设置水印 2 批量设置
  const [showPanl, setShowPanl] = useState<boolean>(false);
  const [treeData, setTreeData] = useState<any>([]);
  const [selectedKey, setSelectedKey] = useState<any>('');
  const [selectedDetail, setSelectedDetail] = useState<any>(null);
  const currentSelected = useRef<any>([]);
  const [currentType, setCurrentType] = useState<any>('');
  const reftype = useRef<any>('');
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [uploadmisson, setUploadMisson] = useState<boolean>(true);
  const [uploadTip, setUploadTip] = useState<string>('');
  const [storageConfig, setStorageConfig] = useState<any>([]);
  const initSet = {
    head: {
      keyframe_: '',
      contentId_: '',
    },
    tail: {
      keyframe_: '',
      contentId_: '',
    },
    watermark: {
      keyframe_: '',
      contentId_: '',
      position: 'lt',
    },
  };
  const [batchSet, setBatchSet] = useState<any>(initSet);
  const fileLists = useRef<any>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  const [entityPreview, setEntityPreview] = useState<any>(null);
  const [entityModalVisible, setEntityModalVisible] = useState<boolean>(false);
  const [ClassificationModallVisible, setClassificationModallVisible] = useState<boolean>(false);
  const [showplaceholder, setShowplaceholder] = useState<string>('');
  const [ResourceList, setresourceList] = useState<React.Key[]>([]);
  const [SavedValue, setSavedValue] = useState<string>('');

  const [columns, setColumns] = useState<any>([{
    title: '文件名',
    dataIndex: 'fileName',
    width: '40%',
  }]);

  const typeMapping: { [propsName: string]: string } = {
    picture: 'biz_sobey_picture',
    video: 'biz_sobey_video',
    audio: 'biz_sobey_audio',
    document: 'biz_sobey_document',
    other: 'biz_sobey_other',
  };
  useEffect(() => {
    fetchTree();
    getUploadFormat();
    initcolumns();
  }, [JSON.stringify(rmanGlobalParameter)]);
  useEffect(() => {
    console.log('tasks', tasks);
    fileLists.current = tasks;
    setSelectedRowKeys(tasks.map((item: any) => item.fileMd5))
    setSelectedRows(tasks)
    const selected:any = tasks.filter((item:any)=>item.selected);
    const param:any = selected.map((item_:any)=>({
      ...item_,
      fileName:item_.file.name,
      fileLength:item_.file.size,
      fileType:item_.entityType,
      poolType:window.localStorage.getItem('upform_platform')==='Lark'?'ROLE':'',
      pathType:props.targetFolder.includes('global_sobey_defaultclass/public/群组资源')?2:
        props.targetFolder.includes('global_sobey_defaultclass/private')?1:0
    }));

    // 群组上传
    const paramGroup = selected.map((item_:any)=>({
      folderId: breadCrumb[breadCrumb.length - 1]?.folderId,
      fileName: item_.file.name,
      fileLength: item_.file.size,
      fileType: item_.entityType,
    }));

    if(tasks.length){
      console.log('param', breadCrumb);
      // 判断是否是群组上传
      if (breadCrumb[0]?.path.includes('群组资源')) {
        uploadApis.storageGroupConfig(paramGroup).then((res: any) => {
          if(res?.success) {
            setUploadTip('')
            setStorageConfig(res.data);
          }
          else{
            setUploadTip(res?.error?.title)
          }
        }) 
      } else  {
        uploadApis.storageConfig(param).then((res: any) => {
          if(res?.success) {
            setStorageConfig(res.data);
            setUploadTip('')
          }
          else{
            setUploadTip(res.error?.title)
          }
        })
      }
    }
    // initcolumns();
  }, [tasks]);

  // 资源库上传的默认路径需要改成，在那个目录栏、文件夹上传时就默认哪个目录栏、文件夹
  useEffect(() => {
    if (props.targetFolder) {
      setShowplaceholder(getDirTeeStr(props.targetFolder));
      setSelectedKey(props.targetFolder);
    }
  }, [props.targetFolder]);


  useEffect(() => {
    gettree();
}, []);

const gettree = () => {
    contentListApis.getTreetheme().then(res => {
        if (res && res.extendMessage) {
            console.log(res.extendMessage, 'extendMessage');
            const formattedData = res.extendMessage.map(item => ({
                title: item.categoryName,
                key: item.categoryCode,
                children: item.children ? item.children.map((child: { categoryName: any; categoryCode: any; }) => ({
                    title: child.categoryName,
                    key: child.categoryCode,
                })) : [],
            }));
            // setTestTree(formattedData);
        } else {
            console.error('获取树主题失败，res未定义或extendMessage未定义');
        }
    }).catch(error => {
        console.error('请求失败:', error);
    });
}

  const getAllFields = () => {
    const promise: Promise<any>[] = [];
    Object.keys(typeMapping).forEach(key => {
      uploadApis.getAllFieldsByType(typeMapping[key]).then(res => {
        if (res && res.data && res.success) {
          res.data.forEach(item => {
            if (!item.value) {
              item.value = item.isArray ? [] : '';
            }
          });
          const value: any = {};
          value[key] = res.data;
          console.log(value, 'valuevaluevalue');

        }
      });
    });
  };

   // 路径转译
   const getDirTeeStr = (dirTree?: string): string => {
    if(!dirTree){
      return ''
    }
    if (dirTree.includes('global_sobey_defaultclass/public')) {
      return dirTree.replace('global_sobey_defaultclass/public', '').replace('/','');
    } else if (dirTree.includes('global_sobey_defaultclass/private')) {
      let newdir = dirTree.replace(
        'global_sobey_defaultclass/private',
        '个人资源',
      );
      if (newdir.includes('/')) {
        let alldir = newdir.split('/');
        if (alldir.length >= 2) {
          newdir = newdir.replace('/' + alldir[1], '');
        }
      }
      return newdir;
    } else {
      return dirTree;
    }
  };

  // 初始化表格的字段
  const initcolumns = () => {
    // columns.length = 0; // 清空之前的列
    // 判断是否有权限
    if(rmanGlobalParameter.includes(globalParams.titles_trailer_display)){
      columns.push({
        title: '片头',
        dataIndex: 'head',
        width: '20%',
        render: (value: any, row: any) => {
          if(row.entityType === 'video'){
            if (value.keyframe_) {
              return (
                <div className="keyframe">
                  <img src={value.keyframe_} />
                  <Button
                    type="link"
                    icon={<DeleteOutlined />}
                    onClick={() => deleteHeadTail(row, 'head')}
                  />
                </div>
              );
            } else {
              return <Button onClick={() => addHeadTail(row, 'head')}>添加</Button>;
            }
          }else{
            return '-'
          }
        },
      })
      columns.push({
        title: '片尾',
        dataIndex: 'tail',
        width: '20%',
        render: (value: any, row: any) => {
          if(row.entityType === 'video'){
            if (value.keyframe_) {
              return (
                <div className="keyframe">
                  <img src={value.keyframe_} />
                  <Button
                    type="link"
                    icon={<DeleteOutlined />}
                    onClick={() => deleteHeadTail(row, 'tail')}
                  />
                </div>
              );
            } else {
              return <Button onClick={() => addHeadTail(row, 'tail')}>添加</Button>;
            }
          }else{
            return '-'
          }
        },
      })
    }

    if(rmanGlobalParameter.includes(globalParams.watermark_display)){
      columns.push({
        title: '水印/LOGO',
        dataIndex: 'watermark',
        width: mobileFlag?'90px':'20%',
        // 当前列不显示
        render: (value: any, row: any) => {
          if(row.entityType === 'video'){
            if (value.keyframe_) {
              return (
                <div className="keyframe">
                  <img src={value.keyframe_} />
                  <Button
                    type="link"
                    icon={<DeleteOutlined />}
                    onClick={() => deleteHeadTail(row, 'watermark')}
                  />
                </div>
              );
            } else {
              return <Button onClick={() => addHeadTail(row, 'watermark')}>添加</Button>;
            }
          }else{
            return '-'
          }
        },
      })
    }
    columns.push({
      title: '分类',
      dataIndex: 'selectedItems',
      width: '20%',
      render: (value: any, row: any) => {
        console.log(value, row, '11111');
        if (value && value.length > 0) {
          return (
            <div>
              {value.map((resource: any, index: number) => (
                <Tag color="blue">{resource.theme_names || '无名称'}</Tag> // 添加默认值以确保显示
              ))}
            </div>
          );
        } else {
          return Array.isArray(row?.metadata?.field) && row?.metadata?.field.some((item: { fieldName: string; }) => item.fieldName == "theme_codes") ? (
            <Button onClick={() => { setClassificationModallVisible(true); setSavedValue(row.fileName); }}>添加</Button>
          ) : null;
        }
      },
    })
  }
  // 删除文件
  const deletefile = () => {
    // 被删除的对象
    const removeArr: number[] = [];
    // 被删除的key
    const removekey: string[] = [];

    const newTasks = tasks.filter((item, index) => {
      if (selectedRowKeys.includes(item.fileMd5)) {
        removeArr.push(index);
        removekey.push(item.fileMd5);
        return false;
      }else{        
        return true;
      }
    });
   
    
    dispatch({
      type: 'upload/changeTasks',
      payload: {
        value: newTasks,
      },
    });
    const tempList:any = [];
    for (let i = orginTasks.length - 1; i >= 0; i--) {
      if (!removeArr.includes(i)) {
        tempList.unshift(orginTasks[i]);
      }
    }
    if(newTasks.length){
      dispatch({
        type: 'upload/setOrginTasks',
        payload: {
          value: tempList,
        },
      });
      // 把删除掉的去处
      let newselectedRowKeys = selectedRowKeys.filter((item:any,index:number)=>!removekey.includes(item));
      let newSelectedRows = selectedRows.filter((item:any,index:number)=>!removekey.includes(item.fileMd5));
      setSelectedRowKeys(newselectedRowKeys);
      setSelectedRows(newSelectedRows);
    }else{
      dispatch({
        type: 'upload/setOrginTasks',
        payload: {
          value: [],
        },
      });
      setSelectedRowKeys([]);
      setSelectedRows([]);
    }
  };

  const checkAllStatus = useMemo(() => {
    return {
      checkAll: tasks.every(item => item.selected) && tasks.length > 0,
      indeterminate:
        tasks.some(item => item.selected) &&
        !tasks.every(item => item.selected),
    };
  }, [tasks]);

  // 全选
  const onCheckAllChange = (e: CheckboxChangeEvent) => {
    const newTasks = copyObject(tasks);
    newTasks.forEach(item => {
      item.selected = e.target.checked;
    });
    dispatch({
      type: 'upload/changeTasks',
      payload: {
        value: newTasks,
      },
    });
  };
  // 单选
  const onChange = (index: number, e: CheckboxChangeEvent) => {
    console.log(tasks)
    const newTasks = copyObject(tasks);
    newTasks[index].selected = e.target.checked;
    dispatch({
      type: 'upload/changeTasks',
      payload: {
        value: newTasks,
      },
    });
  };
  // 更改编辑对象
  const changeEdit = (index: number) => {
    if (metadataRef && metadataRef.current) {
      setEditNumber(index);
    }
  };
  useEffect(() => {
    asyncLoadScript('/rman/libs/webuploader/webuploader.js');
  }, []);

  const uploadProgress = (file: any, progress: number) => {
    const p = progress >= 1 ? 0.999 : progress;
    dispatch({
      type: 'upload/updateTaskPanls',
      payload: {
        value: { guid: file.source.guid, progress: p },
      },
    });
  };
  const uploadComplete = (file: any, newWebUploader: any) => {
    uploadApis.filemerge(file.source.guid, file.name,file.source.fileGuid).then(res => {
      if (res?.success) {
        pollMergeStatus(file, newWebUploader);
      } else {
        uploadError(file);
      }
    });
  };
  /**
   * react中异步回调中的state,是创建的那次渲染中看到的,不是最新的state
   * 这个hook使用useRef保存那个state,确保获取最新的state
   * @param state
   * @returns
   */
    const stateRef = useRef<any>()
    useEffect(() => {
      stateRef.current = taskPanls
    }, [taskPanls])
  /**
   * 轮询合并状态
   * @param file
   * @param newWebUploader
   */
  const pollMergeStatus = async (file: any, newWebUploader: any) => {
    const res = await uploadApis.fetchMergeStatus(file.source.guid);
    if (res?.data?.state === 1 && res.data.finalFilePath) {
      const temp: any = {
        video_start: file.source?.extraParam?.head?.contentId_,
        video_end: file.source?.extraParam?.tail?.contentId_,
      };
      if (file.source.extraParam.watermark.keyframe_) {
        temp.watermark = {
          contentid: file.source.extraParam.watermark.contentId_,
          logo_path: file.source.extraParam.watermark.keyframe_,
          start_x: file.source.extraParam.watermark.position[0] == 'l' ? 0 : 1,
          start_y: file.source.extraParam.watermark.position[1] == 't' ? 0 : 1,
        };
      }
      
      uploadApis[showVideoResouce === 'true' ? 'uploadRecordingImport' : 'uploadImport'](
          selectedKey,
          // file.source.folderPath as string,
          res.data.finalFilePath,
          file.source.metadata as IFormItem[],
          file.size,
          temp,
        )
        .then(dd => {
          if (dd && dd.data && dd.success) {
            dispatch({
              type: 'upload/updateTaskPanls',
              payload: {
                value: { guid: file.source.guid, progress: 1 },
              },
            });

            // 从队列中删除
            newWebUploader.removeFile(file, true);

            // 删除task
            uploadApis.deleteMergeTask(file.source.guid);

            // 弹窗提示
            message.success(
              file.name +
              intl.formatMessage({
                id: 'upload-success',
                defaultMessage: '上传成功',
              }),
            );
            if(rmanGlobalParameter.includes(globalParams.audit_import) && (props.targetFolder.includes('global_sobey_defaultclass/public/群组资源') || props.targetFolder.includes('global_sobey_defaultclass/public/共享资源')) ){
              setTimeout(() => {
                message.info('资源等待入库审核');
                sessionStorage.setItem('id', 'myVerify');
              }, 1000);
            }
          } else {
            uploadError(file);
          }
        });
    } else if (res?.data?.state === 0) {
      // 手动移除掉的任务 停止轮询
      const realTaskPanls = stateRef.current;
      console.log('当前任务列表',realTaskPanls,file)
      if(realTaskPanls.some((item:any)=>item.guid === file.source.guid)){
        setTimeout(() => {
          pollMergeStatus(file, newWebUploader);
        }, 500);
      }
    } else if (res?.data?.state === -1 && res?.data.errorMsg) {
      message.error(res?.data.errorMsg);
      uploadError(file);
    } else {
      uploadError(file);
    }
  };

  const uploadError = (file: any) => {
    dispatch({
      type: 'upload/errorTask',
      payload: {
        value: file.source.guid,
      },
    });
    message.error(
      file.name +
      intl.formatMessage({
        id: 'upload-error',
        defaultMessage: '上传失败',
      }),
    );
  };
  useEffect(() => {
    if (metadataRef && metadataRef.current) {
      metadataRef.current.resetFields();
    }
  }, [editNumber]);
  //上传完成回调
  const completeUpload = (data:any)=>{
    const temp: any = {
      video_start: data.source?.extraParam?.head?.contentId_ ,
      video_end: data.source?.extraParam?.tail?.contentId_,
    };
    if (data.source?.extraParam?.watermark.keyframe_) {
      temp.watermark = {
        contentid: data.source.extraParam.watermark.contentId_,
        logo_path: data.source.extraParam.watermark.keyframe_,
        start_x: data.source.extraParam.watermark.position[0] == 'l' ? 0 : 1,
        start_y: data.source.extraParam.watermark.position[1] == 't' ? 0 : 1,
      };
    }
    uploadApis
    .uploadImport(
      selectedKey,
      // data.file.folderPath as string,
      `${data.usehttp? 'http': 'https'}://${data.bucket}.${data.endpoint}/${data.key}`,
      data.file.metadata as IFormItem[],
      data.file.size as number,
      temp,
    ).then(dd => {
      if (dd && dd.data && dd.success) {
        dispatch({
          type: 'upload/updateTaskPanls',
          payload: {
            value: { guid: data.file.ruid, progress: 1 },
          },
        })
        // 弹窗提示
        message.success(
          data.file.name +
          intl.formatMessage({
            id: 'upload-success',
            defaultMessage: '上传成功',
          }),
        );
      } else {
        message.error('上传失败')
      }
    });
  }
  //不分片上传
  const commonUpload = async(params:any)=>{
    if(params.product==='ctyun'){
      ctyun.init(params);//先初始化
      const res:any = await ctyun.putObject({
        Bucket:params.bucket,
        Body:params.file.source,
        Key:params.key
      });
      console.log('无上传',res);
      if(res){
        completeUpload(params);
      }
    }else if(params.product==='aliyun'){
      aliyun.init(params);
      const result =await aliyun.putObject(params);
      if(result.url){
        console.log('aliyun',result.url)
        completeUpload(params);
      }
    }else if(params.product==='amazon'){}

  }
  const getQueryVariable = (url, variable) =>{
    var query = url.split('?');
    if(query.length>1){
        var vars = query[1].split("&");
        for (var i=0;i<vars.length;i++) {
            var pair = vars[i].split("=");
            if(pair[0] == variable){return pair[1];}
        }
    }
    return false;
  }
  //阿里云分片上传
  const amazonUpload= async (data:any, lists)=>{
    const current_:any = stateRef.current.filter((item:any)=>data.file.ruid === item.guid);
    console.log('阿里云分片上传---'+new Date(),current_[0]);
    if(current_.length===0) return;
    uploadApis.getSinedUrl({
      bucket: data.bucket,  
      objectPath: data.key, 
      requestOper: 2,   
      queryParams: {},  
      product: data.product
    }).then(async res =>  {
      const {signUrl} = res?.data
      const reopt = {
        method : 'POST',
        url : signUrl,
        withCredentials: false, 
        maxRedirects : 0,
        responseType : 'text',
      } as any
      const uploadId = getQueryVariable(signUrl, 'uploadId');
      
      if(uploadId){
        let partETags = [] as any
            for (let index = 0; index < lists.length; index++) {
              await uploadApis.getSinedUrl({
                bucket: data.bucket,  
                objectPath: data.key, 
                requestOper: 3,   
                queryParams: {
                  partNumber: index+1,
                  uploadId
                },
                product: data.product
                }).then(async (res)  => {
                const {signUrl} = res?.data
                await axios.request({...reopt, method: 'PUT', data: lists[index].chunk, url : signUrl}).then( (res) => {
                  partETags.push({
                    partNumber: index+1,
                    eTag: res.headers.etag
                  })
              })
            })
            const progress = (index+1)/lists.length;
            const p = progress >= 1 ? 0.999 : progress;
            dispatch({
              type: 'upload/updateTaskPanls',
              payload: {
                value: { guid: data.file.ruid, progress: p },
              },
            });
            //把参数异步传过去  方便后续取消操作
            dispatch({
              type: 'upload/updateTaskPanls_',
              payload: {
                value: { guid: data.file.ruid, param:{
                  bucket:data.bucket,
                  key:data.key,
                  uploadId:data.uploadId,
                  endpoint:data.endpoint,
                  usehttp: data.usehttp,
                  historyLists:lists
                } },
              },
            });
            }
            await uploadApis.getSinedUrl({
              bucket: data.bucket,
              objectPath: data.key,
              requestOper:  4,
              partETags,
              queryParams: {
                uploadId
              },
              product: data.product
              }).then(async (res)  => {
                completeUpload(data);
              })
      }
      else{
        axios.request(reopt).then(async (response) => {
          if(response.status < 300){                     
            console.log(response.data);
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(response.data, "text/xml");
            const uploadIdNode = xmlDoc.getElementsByTagName('UploadId')[0];
            const uploadId = uploadIdNode.textContent;
            var content = "<CompleteMultipartUpload>";
            for (let index = 0; index < lists.length; index++) {
             await uploadApis.getSinedUrl({
                "bucket": data.bucket,  
                "objectPath": data.key, 
                "requestOper": 3,   
                "queryParams": {
                  partNumber: index+1,
                  uploadId
                },
                "product": data.product
                }).then(async (res)  => {
                const {signUrl, actualSignedRequestHeaders} = res?.data
                 await axios.request({...reopt, method: 'PUT', data: lists[index].chunk, headers : actualSignedRequestHeaders || {}, url : signUrl}).then( (res) => {
                  console.log(response, 'response')
                  content +=  `<Part><PartNumber>${index+1}</PartNumber><ETag>${res.headers.etag}</ETag></Part>`
              })
            })
            const progress = (index+1)/lists.length;
            const p = progress >= 1 ? 0.999 : progress;
            dispatch({
              type: 'upload/updateTaskPanls',
              payload: {
                value: { guid: data.file.ruid, progress: p },
              },
            });
            //把参数异步传过去  方便后续取消操作
            dispatch({
              type: 'upload/updateTaskPanls_',
              payload: {
                value: { guid: data.file.ruid, param:{
                  bucket:data.bucket,
                  key:data.key,
                  uploadId:data.uploadId,
                  endpoint:data.endpoint,
                  usehttp: data.usehttp,
                  historyLists:lists
                } },
              },
            });
            }
            await uploadApis.getSinedUrl({
              "bucket": data.bucket,
              "objectPath": data.key,
              "requestOper":  4, 
              "queryParams": {
                uploadId
              },
              "product": data.product
              }).then(async (res)  => {
                const {signUrl, actualSignedRequestHeaders} = res?.data
                axios.request({...reopt, data: content+= '</CompleteMultipartUpload>', headers : actualSignedRequestHeaders || {}, url : signUrl}).then( (res) => {
                  completeUpload(data);
                })
              })
          }
        })
      }
    })

       
    
  }
  //阿里云分片上传
  const aliyunUpload= async (data:any)=>{
    const current_:any = stateRef.current.filter((item:any)=>data.file.ruid === item.guid);
    console.log('阿里云分片上传---'+new Date(),current_[0]);
    if(current_.length===0) return;
    //如果存在uploadId 则是之前已经初始化过
    if(current_[0].uploader.uploadId){
      //重传
      try{
        const result = await current_[0].uploader.client.multipartUpload(data.key,data.file.source,{
          checkpoint:current_[0].uploader.uploadId,
          progress: (p:any, cpt:any, res:any) => {
            // 获取上传进度。
            console.log('aliyun重传进度',p);
            dispatch({
              type: 'upload/updateTaskPanls',
              payload: {
                value: { guid: data.file.ruid, progress: p },
              },
            });
          },
        })
        console.log('aliyun重传',result);
        if(result.res?.status===200){
          completeUpload(data);
        }
      }catch(e){
        console.log(e)
      }
    }else{
      const aclient = aliyun.init(data);
      // 分片上传。
      const options = {
        // 获取分片上传进度、断点和返回值。
        progress: (progress: any, cpt: any, res: any) => {
          console.log('获取分片上传进度、断点和返回值',progress,cpt,res);
          //abortCheckpoint = cpt
          const p = progress >= 1 ? 0.999 : progress;
          //把参数异步传过去  方便后续取消操作
          dispatch({
            type: 'upload/updateTaskPanls_',
            payload: {
              value: { guid: data.file.ruid, param:{
                bucket:data.bucket,
                key:data.key,
                client:aclient,
                uploadId:cpt,
                endpoint:data.endpoint,
                usehttp: data.usehttp
              } },
            },
          });
          dispatch({
            type: 'upload/updateTaskPanls',
            payload: {
              value: { guid: data.file.ruid, progress: p },
            },
          });
        },
        // 设置并发上传的分片数量。
        parallel: 4,
        // 设置分片大小。默认值为1 MB，最小值为100 KB。
        partSize: 5 * 1024 * 1024,
      };
      const res = await aclient.multipartUpload(data.key, data.file.source, {
        ...options,
      });
      console.log('multipartUpload',res)
      if(res.res?.status===200){
        completeUpload(data);
      }
    }
  }
  //天翼云分片上传
  const ctyunUpload = async (data:any,lists:any)=>{
    const current_:any = stateRef.current.filter((item:any)=>data.file.ruid === item.guid);
    console.log('当前执行的分片任务---'+new Date(),current_[0]);
    if(current_.length===0) return;
    //如果存在uploadId 则是之前已经初始化过
    if(current_[0].uploader.uploadId){
      data = {...data,
        bucket: current_[0].uploader.bucket,
        key: current_[0].uploader.key,
        endpoint:current_[0].uploader.endpoint,
        usehttp: current_[0].uploader.usehttp,
        uploadId: current_[0].uploader.uploadId
      };
    }else{
      ctyun.init(data);//先初始化
      //初始化分片容器
      const ress:any =await ctyun.createMultipartUpload({
        Bucket:data.bucket,
        Key:data.key
      });
      if(!ress) return;
      console.log('分片上传',data,ress,lists);
      data = {...data,
        uploadId:ress.UploadId,
      };
      //定时更新sts token 计算到期时间还剩余多少
      const time:number = (new Date(data.expiration)).getTime()-(new Date()).getTime()-5*60*1000; //单位毫秒 提前5分钟续时
      setTimeout(()=>{
        //会话过期处理
        console.log('定时更新sts ',time/1000/60)
        ctyun.getStsToken('ctyun');
      },time);
    }
    console.log('ctyunUpload _ client',data);
    let MultipartUpload:any = {
      Parts: []
    };
    let fin_:boolean=false;//中断标识
    for(let i=0;i<lists.length;i++){
      //还要判断是否被暂停了
      const current:any = stateRef.current.filter((item:any)=>data.file.ruid === item.guid);
      console.log('当前任务循环队列---'+new Date(),current[0]);
      if(current.length===0){ //不在列表中 已经被移除了
        console.log('不在列表中 已经被移除了')
        fin_=true;
        break;
      }
      if(current[0]?.pause){
        fin_=true;
        break
      }
      //仅上传剩余的分片
      const index_ = current[0].progress * lists.length;
      if(index_>i){
        continue
      }
      const res:any = await ctyun.uploadPart({
        Bucket:data.bucket,
        Key:data.key,
        PartNumber:i+1,
        UploadId:data.uploadId,
        Body:lists[i].chunk
      });
      if(res.ETag){
        const progress = (i+1)/lists.length;
        const p = progress >= 1 ? 0.999 : progress;
        dispatch({
          type: 'upload/updateTaskPanls',
          payload: {
            value: { guid: data.file.ruid, progress: p },
          },
        });
        MultipartUpload.Parts.push({
          PartNumber: i+1,
          ETag: res.ETag,
        });
        //把参数异步传过去  方便后续取消操作
        dispatch({
          type: 'upload/updateTaskPanls_',
          payload: {
            value: { guid: data.file.ruid, param:{
              bucket:data.bucket,
              key:data.key,
              uploadId:data.uploadId,
              endpoint:data.endpoint,
              usehttp: data.usehttp,
              historyLists:lists
            } },
          },
        });
        continue
      }
    }
    if(fin_) return;
    console.log('文件全部上传完毕,发起合并请求');
    const params = {
      Bucket:data.bucket,
      Key:data.key,
      UploadId:data.uploadId,
      MultipartUpload:{
        Parts:MultipartUpload.Parts.sort((a:any,b:any)=>a.PartNumber-b.PartNumber)
      }
    }
    console.log('合并请求params',params)
    //发起合并请求
    const finish:any = await ctyun.completeMultipartUpload(params);
    console.log('finish',finish)
    if(finish?.Location){
      completeUpload(data);
    }else{
      message.error('分片合成失败，请重试')
    }
  }
  //其他存储
  const otherPlatform = async (params:any,lists?:any)=>{
    console.log('otherPlatform',params);
    if(params.product==='ctyun'){
      if(lists){
        ctyunUpload(params,lists);
      }else{
        //不分片
        commonUpload(params);
      }
    }else if(params.product==='aliyun'){
      if(lists){
        aliyunUpload(params);
      }else{
        //不分片
        commonUpload(params);
      }
    }else if(params.product==='amazon' || params.product === 'huawei'){
      if(lists){
        amazonUpload(params, lists);
      }else{
        //不分片
        uploadApis.getSinedUrl({
          "bucket": params.bucket,  
          "objectPath": params.key, 
          "requestOper": 1,   
          "queryParams": {},  
          "product": params.product
        }).then(res => {
          // for (let index = 0; index < lists.length; index++) {
            const {signUrl, actualSignedRequestHeaders} = res?.data
            const reopt = {
              method : 'PUT',
              url : signUrl,
              withCredentials: false, 
              headers : actualSignedRequestHeaders || {},
              maxRedirects : 0,
              responseType : 'text',
              data : params.file.source,
          };
            axios(reopt).then(function (response) {
              if(response.status < 300){                     
                completeUpload(params);
            }          
          })
        })
      }
    }
  }
  // 表单数据修改
  const upload =  async () => {
    // if (!webUploader) {
    //   return;
    // }
    let err: number[] = [];
    selectedRows.forEach((item, index) => {
      if (item.selected && item.metadata.field) {
        if (item.metadata.field.some(o => o.isMustInput && !o.value)) {
          err.push(index);
        }
      }
    });
    if (err.length > 0) {
      message.error(
        intl.formatMessage({
          id: 'no-verify',
          defaultMessage: '您还有数据没有通过验证',
        }),
      );
      return setEditNumber(err[0]);
    }
    const newTaskPanls:any = copyObject(taskPanls);
    let tempArr:any = [];
    let showFlag = false;
    //获取每一个上传对象的存储方式 start
    const selected:any = selectedRows.filter((item:any)=>item.selected);

    console.log('selected' ,storageConfig,selected);
    //获取每一个上传对象的存储方式 end
    for(let index=0;index<selected.length;index++){
      const item:any = selected[index];
      const storage_:any = storageConfig[index];
      //判断存储方式
      if(!storage_.access_type) continue;
      console.log(storage_.access_type,'storage_.access_type');
      
      if(storage_.access_type==='NAS'){
        showFlag=true;
        const file = new (window as any).WebUploader.Lib.File(
          (window as any).WebUploader.Base.guid(),
          // orginTasks[index],
          item.file.originFileObj || item.file //兼容拖拽有originFileObj的bug
          // item.file.originFileObj?.size || item.file //兼容拖拽有originFileObj的bug
        );
        file.guid = (window as any).WebUploader.Base.guid();
        file.metadata = item.metadata.field;
        // file.folderId = props.targetFolder;
        file.folderPath = props.targetFolder;
        file.fileGuid = storage_.path;//针对同名文件增加不同标识
        file.extraParam = {
          head: item.head,
          tail: item.tail,
          watermark: item.watermark,
        }
        const updatedFields = item.metadata.field.map((field: { fieldName: string; }) => {
          const theme_codes1 = item.selectedItems?.map((selectedItem: { theme_codes: any; }) => selectedItem.theme_codes);
          if (field.fieldName == 'theme_codes') {
            return {
              ...field,
              value: theme_codes1
            };
          }
          return field;
        });
        console.log(updatedFields,'updatedFields');
        
        file.metadata = updatedFields;
        // file.selectedItems = item.selectedItems;
        // @ts-ignore
        const webUploader = new WebUploader({
          uploadError,
          uploadComplete,
          uploadProgress,
        });
        webUploader.addFiles(file);

        const files = webUploader.getFiles();

        files.forEach((item: any) => {
          if (
            newTaskPanls.filter(i => i.guid === item.source.guid).length === 0
          ) {
            newTaskPanls.push({
              uploader: webUploader,
              name: item.name,
              size: item.size,
              folderName: props.folderName,
              status: 0,
              progress: 0,
              index,
              guid: item.source.guid,
              uploading: false,
              pause: false,
              selectedItems: item.selectedItems
            });
          }else{
            // message.info(item.name+'文件已在列表中');
          }
        });
      }else if(storage_.access_type==='OSS'){
        //为了模拟传统数据结构 方便后续操作
        showFlag=true;//当存在有效上传文件时才打开
        const file = new (window as any).WebUploader.Lib.File(
          (window as any).WebUploader.Base.guid(),
          orginTasks[index],
        );
        file.guid = (window as any).WebUploader.Base.guid();
        const updatedFields = item.metadata.field.map((field: { fieldName: string; }) => {
          const theme_codes1 = item.selectedItems?.map((selectedItem: { theme_codes: any; }) => selectedItem.theme_codes);
          if (field.fieldName == 'theme_codes') {
            return {
              ...field,
              value: theme_codes1
            };
          }
          return field;
        });
        file.metadata = updatedFields;
        // file.metadata = item.metadata.field;
        file.folderPath = props.targetFolder;
        file.extraParam = {
          head: item.head,
          tail: item.tail,
          watermark: item.watermark,
        }
        tempArr.push(file);
        if(!tempArr.some((item:any)=>newTaskPanls.some((item_:any)=>item.source.guid === item_.guid))){
          //先判断是否需要分片上传 大于5M才分片
          const chunkSize:number = 5*1024*1024;
          newTaskPanls.push({
            uploader: {
              ...file,
              upload:()=>file.size>chunkSize ?otherPlatform({ ...item,...storage_,file},ctyun.splitFile(file.source))
              :otherPlatform({ ...item,...storage_,file})
            },
            storage:storage_.access_type,
            product:storage_.product,
            name: file.name,
            size: file.size,
            selectedItems: file.selectedItems,
            folderName: props.folderName,
            status: 0,
            progress: 0,
            index,
            guid: file.ruid,
            uploading: false,
            pause: false,
          });
        }else{
          // message.info(file.name+'文件已在列表中');
        }
      }else{

      }
    }
    console.log('newTaskPanls',newTaskPanls)
    dispatch({
      type: 'upload/setTaskPanls',
      payload: {
        value: newTaskPanls,
      },
    });
    dispatch({
      type: 'upload/changeShowPanl',
      payload: {
        value: showFlag,
      },
    });

    onCancel();
  };

  const intl = useIntl();
  const uploadDefault = ()=>{
    console.log('阻止upload的默认上传')
    return true
  }

  //判断上传文件类型
  const fileType = (type: string) => {
    const str = type.split('/');
    let type_ = str[str.length - 1];
    uploadformatEnum?.forEach((item: any) => {
      if (item.allowedFormat.includes(type_)) {
        type_ = item.type;
      }
    });
    return type_;
  };

  //拖拽参数初始化
  const fileprops = {
    name: 'file',
    multiple: true,
    fileList:[],
    showUploadList:false,
    onChange(info:any) {
      //保证只触发一次，避免重复调用问题产生脏数据；
      if(info.file.uid === info.fileList[info.fileList.length-1].uid){
        const newTasks = copyObject(tasks);
        let dragList = uploader.tf.getTasksByFiles(info.fileList); //对于手动拖拽需要处理数据字段
        dragList.forEach((item:any) => {
          item.metadata.field = copyObject(fields[item.entityType]);
          item.metadata.field?.forEach((o:any) => {
            if (o.fieldName.indexOf('name') > -1) {
              o.value = item.metadata.name;
            }
            if (o.fieldName.indexOf('createUser_') > -1) {
              o.value = (window as any).login_useInfo.userCode
            }
          });
          item.selected = true;
          item.fileMd5 = uploader.tf.calcFileMd5(item.file.originFileObj); //计算文件md5
          item.head = {
            keyframe_: '',
            contentId_: '',
          }
          item.tail = {
            keyframe_: '',
            contentId_: '',
          }
          item.watermark= {
            keyframe_: '',
            contentId_: '',
            position: 'lt',
          }
        });
        // 注释这里是因为一个bug  每次添加文件 会提示文件已经在列表中
        const temp_:any=newTasks.concat(dragList);
        const newArr_= getUnique(temp_,'fileMd5');
        if(newArr_.repeat.length>0){
          message.info(newArr_.repeat.map((item:any)=>item.file.name).join(',')+'文件已在列表中');
        }
        console.log('新添加的数据',newArr_)
        dispatch({
          type: 'upload/changeTasks',
          payload: {
            value: newArr_.temp,
          },
        });
        const temp =newArr_.temp.map((item:any)=>item.file.originFileObj as File);
        // let ccopyData:any= temp; //orginTasks.concat(temp);
        // let set = new Set(ccopyData);
        // let newArr = Array.from(set);
        console.log('新添加原始数据',temp)
        dispatch({
          type: 'upload/setOrginTasks',
          payload: {
            value: temp,
          },
        });
      }
    },
    onDrop(e:any) {
      console.log('Dropped files', e.dataTransfer.files);
    },
  };
  // 添加
  const addTask = () => {
    uploader.tf.openFileSelector(
      (ntasks: uploadTypes.ITask[]) => {
        const newTasks = copyObject(tasks);
        if (fields && Object.keys(fields).length > 0) {
          ntasks.forEach(item => {
            item.metadata.field = copyObject(fields[item.entityType]);
            item.metadata.field?.forEach(o => {
              if (o.fieldName.indexOf('name') > -1) {
                o.value = item.metadata.name;
              }
            });
            item.selected = true;
            item.fileMd5 = uploader.tf.calcFileMd5(item.file); //计算文件md5
          });
        }
        const temp_:any=newTasks.concat(ntasks);
        const newArr_= getUnique(temp_,'fileMd5');
        if(newArr_.repeat.length>0){
          let set = new Set(newArr_.repeat.map((item:any)=>item.file.name));
          let msgarr = Array.from(set);
          message.info(msgarr.join(',')+'文件已在列表中');
          // message.info(newArr_.repeat.map((item:any)=>item.file.name).join(',')+'文件已在列表中');
        }
        dispatch({
          type: 'upload/changeTasks',
          payload: {
            value: newArr_.temp
          },
        });
        // 使用push的话 会在本地运行报错 object is not extensible 采用concat
        // ntasks.forEach(item => {
        //   // orginTasks.push(item.file as File);
        // });
        const temp =ntasks.map((item:any)=>item.file as File);
        let ccopyData:any=orginTasks.concat(temp);
        console.log(ccopyData)
        dispatch({
          type: 'upload/setOrginTasks',
          payload: {
            // value: orginTasks,
            value: ccopyData,
          },
        });
      },
      false,
      uploadformat,
    );
  };
  const onValueChange = (items: IFormItem[]) => {
    const newTasks:any = copyObject(tasks);
    newTasks[editNumber].metadata.field = items;
    dispatch({
      type: 'upload/changeTasks',
      payload: {
        value: newTasks,
      },
    });
  };
  const changeShow = () => {
    setShowPanl(!showPanl)
  }

   /**
   * 获取上传格式
   */
   const getUploadFormat = () => {
    rmanApis.getuploadFormat().then((res) => {
      if (res && res.data && res.success) {
        let temp: any = '';
        res.data.map((item: any) => {
          temp = temp + item.allowedFormat + ',';
        });
        dispatch({
          type: 'upload/setUploadformatOrignal',
          payload: {
            value: res.data,
          },
        });
        dispatch({
          type: 'upload/changeUploadformat',
          payload: {
            value: temp,
          },
        });
      }
    });
  };

  const fetchTree = () => {
    rmanApis.gettreebylevel(2).then((res: any) => {
      if (res && res.data && res.success) {
        let data = res.data;
        let newData: any = [];
        data.map((item: any) => {
          if (item.name === '公共资源') {
            item.children?.forEach((item: any) => {
              newData.push({ ...item, layer: 1 });
            });
          } else {
            newData.push(item);
          }
        });
        newData = newData.filter(Boolean); //过滤空对象
        // if(!permission){
        //   removeTreeListItem(newData, '录播资源');
        // }
        // setPrivatePath(newData.filter((item:any)=>item.name == '个人资源')?.[0].path || newData[0].path);//默认选中个人资源
        // 过滤录播资源 默认不显示
        newData = newData.filter((item: any) => item.name != '录播资源');
        const rootData = newData.map((item: any) => {
          return {
            key: item.path,
            value: item.path,
            title: item.name,
            id: item.id,
            disabled: (item.name === '群组资源') ? true : false,
          };
        });
        setSelectedKey(
          newData.filter((item: any) => item.name == '个人资源')?.[0].path || newData[0].path,
        ); //默认选中个人资源
        setTreeData(rootData);
      }
    });
  };

  //动态加载子节点
  const onLoadChild = (node: any, isRoot: boolean = false) => {
    const { key, children, code, title } = node;
    return new Promise(async (resolve) => {
      if (key === 'add') {
        resolve(null);
        return;
      }
      if (children) {
        resolve(null);
        return;
      }
      function updateTreeData(list: any, key: React.Key, children: any): any {
        return list.map((node: any) => {
          if (node.key === key) {
            return {
              ...node,
              children,
            };
          } else if (node.children) {
            return {
              ...node,
              children: updateTreeData(node.children, key, children),
            };
          }
          return node;
        });
      }
      const res: any = await rmanApis.loadChild(key, false);
      if (res && res.data && res.success) {
        let treeList = res.data;
        treeList = treeList.filter((item: any) => {
          return item.name !== '录播资源';
        });
        setTreeData((origin: any) =>
          updateTreeData(
            origin,
            key,
            treeList.map((item: any) => {
              return {
                key: item.path,
                title: item.name,
                id: item.contentId,
                value: item.path,
              };
            }),
          ),
        );
        resolve(null);
      }
    });
  };
  //片头片尾
  const addHeadTail = (row: any, str: string) => {
    if(mobileFlag) {
      message.info('暂不支持手机端，请前往电脑端操作');
      return
    }
    currentSelected.current = row.fileMd5;
    setCurrentType(str);
    reftype.current = str;
    if (str == 'watermark') {
      setBatchSettingVisible(1);
    } else {
      setModalVisible(true);
    }
  };
  //添加批量片头片尾
  const addHeadTailBatch = (str: string) => {
    setCurrentType(str);
    reftype.current = str;
    setModalVisible(true);
  };
  //删除片头片尾水印
  const deleteHeadTail = (row: any, str: string) => {
    setBatchSet(initSet);
    let newtask = fileLists.current.map((item: any) => {
      if (row.fileMd5 == item.fileMd5) {
        let temp: any = {
          keyframe_: '',
          contentId_: '',
        };
        if (str == 'watermark') {
          temp.position = 'lt';
        }
        return {
          ...item,
          [str]: temp,
        };
      } else {
        return item;
      }
    });
    dispatch({
      type: 'upload/changeTasks',
      payload: {
        value: newtask,
      },
    });
  };

  const rowSelections = {
    onChange: (newSelectedRowKeys: Array<any>, newSelectedRows: any) => {
      console.log('zzh', newSelectedRowKeys, newSelectedRows);
      setSelectedRowKeys(newSelectedRowKeys);
      setSelectedRows(newSelectedRows);
    },
    // preserveSelectedRowKeys: true,
    selectedRowKeys,
  };

  const onSelect = (key:any, node: any) => {
    setSelectedDetail({
      key: node.key,
      title: node.title,
      id: node.id,
    });
    setSelectedKey(key);
    setShowplaceholder(getDirTeeStr(key));
    uploadApis.uploadRermissions({folderPath: key}).then(res => {
      if(res?.success) {
        setUploadMisson(res.data)
      }
    })
  };

  const resourceModalConfirm = async (resource: any[]) => {
    console.log('resource', resource);
    const item = resource[0];
    setModalVisible(false);
    let temp: any = {
      keyframe_: item.keyframe_,
      contentId_: item.contentId_,
    };
    if (currentType == 'watermark') {
      temp.position = 'lt';
    }
    if (batchSettingVisible > 0) {
      //批量
      setBatchSet({
        ...batchSet,
        [currentType]: temp,
      });
    } else {
      let newtask = tasks.map((item: any) => {
        if (currentSelected.current == item.fileMd5) {
          return {
            ...item,
            [currentType]: temp,
          };
        } else {
          return item;
        }
      })
      dispatch({
        type: 'upload/changeTasks',
        payload: {
          value: newtask,
        },
      });
    }
  };

  const batchSetPotion = (e: MouseEvent, str: string) => {
    e.stopPropagation();
    setBatchSet({
      ...batchSet,
      watermark: {
        ...batchSet.watermark,
        position: str,
      },
    });
  };

  const onConfirm = () => {
    console.log(batchSet);
    //单个设置水印
    if (batchSettingVisible == 1) {
      let newtask = tasks.map((item: any) => {
        if (currentSelected.current == item.fileMd5) {
          return {
            ...item,
            watermark: batchSet.watermark,
          };
        } else {
          return item;
        }
      })
      dispatch({
        type: 'upload/changeTasks',
        payload: {
          value: newtask,
        },
      });
    } else {
      const selected = copyObject(tasks);
      const newdata = selected.map((item: any) => {
        if (selectedRowKeys.includes(item.fileMd5) && item.entityType == 'video') {

          if(batchSet.head.contentId_){
            item.head = batchSet.head;
          }

          if(batchSet.tail.contentId_){
            item.tail = batchSet.tail;
          }

          if(batchSet.watermark.contentId_){
            item.watermark = batchSet.watermark;
          }
          return item;
        } else {
          return item;
        }
      });
      dispatch({
        type: 'upload/changeTasks',
        payload: {
          value: newdata,
        },
      });
    }
    setBatchSet(initSet);
    setBatchSettingVisible(0);
  };



  const ClassificationModalConfirm = async (resource: unknown[]) => {
    console.log('resource', resource);
    setresourceList(resource);
    setClassificationModallVisible(false);
  };

  return (
    <Modal
      destroyOnClose
      maskClosable={false}
      visible={showModal}
      footer={null}
      onCancel={() => onCancel()}
      className={`uploadbox${mobileFlag?' mobilebox':''}`}
      width={showPanl ? 1299 : 926}
    >
      <Tabs >
        <TabPane tab='Web上传' key='web' className='shareMyself'>
        <div className="upload_top">
            <div className="uploaded_file">
              <div className="top">
                <div className="left">
                  <Upload {...fileprops} customRequest={uploadDefault}>
                    <Button type="primary" icon={<PlusCircleOutlined />}>
                      添加
                    </Button>
                  </Upload>
                  <Button disabled={selectedRows.length == 0} icon={<DeleteOutlined />} onClick={deletefile}>
                    删除
                  </Button>
                  {
                    (rmanGlobalParameter.includes(globalParams.titles_trailer_display) || rmanGlobalParameter.includes(globalParams.watermark_display)) && rmanGlobalParameter.includes(globalParams.batch_process_display) &&
                    <Button
                      disabled={(()=>{
                        if(selectedRows.length == 0 || selectedRows.some((item:any)=>item.entityType !== 'video')){
                          return true
                        }else{
                          return false
                        }
                      })()}
                      icon={<IconFont type="iconSettingsbeifen" />}
                      onClick={()=>{
                        if(mobileFlag) {
                          message.info('暂不支持手机端，请前往电脑端操作');
                          return
                        }
                        setBatchSettingVisible(2)
                      }}
                    >
                      批量加工
                    </Button>
                  }

                </div>
                <div className="right">
                  <span>上传至</span>
                  <TreeSelect
                    // placeholder={showplaceholder}
                    treeData={treeData}
                    value={showplaceholder}
                    onSelect={onSelect}
                    loadData={onLoadChild}
                  />
                </div>
            </div>
            <div className="center">
              <Table
                dataSource={tasks}
                columns={columns}
                rowKey="fileMd5"
                size="small"
                rowSelection={rowSelections}
                scroll={{ y: 'calc(100vh - 500px' }}
                pagination={false}
                onRow={(record,index:any) => {
                  return {
                    onClick: event => {
                      setEditNumber(index);
                    }
                  }
                }}
              />
            </div>
            <div className='dragUpload'>
              <Dragger
                key={Math.random()} //加这个的原因是为了清除图片缓存
                {...fileprops} accept={uploadformat}
                customRequest={uploadDefault}>
                {/* <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p> */}
                <p className="ant-upload-text">
                  可拖拽文件至此区域
                </p>
              </Dragger>
            </div>
              {/* <div className="file_top">
                <div className="box-header-title">
                  {intl.formatMessage({
                    id: 'uploaded-file',
                    defaultMessage: '待上传文件',
                  })}
                </div>
                <div className="icon_box">
                  <Button
                    onClick={addTask}
                    type='primary'
                  >
                    <PlusOutlined />
                    <span>添加</span>
                  </Button>
                  <Button
                    onClick={deletefile}
                  >
                    <IconFont type="iconshanchu-heise-copy" />
                    <span>删除</span>
                  </Button>
                </div>
              </div> */}
              {/* <div className="file_bottom">
                <div className="select_all">
                  <Checkbox
                    indeterminate={checkAllStatus.indeterminate}
                    onChange={onCheckAllChange}
                    checked={checkAllStatus.checkAll}
                  >
                    {intl.formatMessage({
                      id: 'select-all',
                      defaultMessage: '全部选择',
                    })}
                  </Checkbox>
                </div>
                <div className="select_box">
                  {tasks.map((item, index) => {
                    return (
                      <div className="select_item" key={index}>
                        {item.progress !== 0 && (
                          <div
                            className={
                              'progress' +
                              (item.progress === 1 ? ' progress-success' : '')
                            }
                            style={{ width: item.progress * 100 + '%' }}
                          />
                        )}
                        <Checkbox
                          checked={item.selected}
                          value={index}
                          onChange={e => onChange(index, e)}
                        />
                        <div
                          onClick={() => changeEdit(index)}
                          className={`change_edit ${index === editNumber ? 'no_edit' : ''
                            }`}
                        >
                          <span>
                            {index + 1}.{item.metadata.name} ({item.entityType})
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div> */}


            </div>
            <div className={showPanl?'switch switch_expand':'switch'} onClick={changeShow}>
              {!showPanl ?
                <img src='/rman/static/images/expand.png' title='展开' className='expand'/>
                : <img src='/rman/static/images/shrink.png' title='收起' className='shrink'/>}
            </div>
            {showPanl && <div className='splitLine'></div>}
            <div className={showPanl ? 'basic_data' : 'basic_data_hidden'}>
              <div className="file_top">
                <div className="box-header-title">
                  {intl.formatMessage({
                    id: 'basic-data',
                    defaultMessage: '基本元数据',
                  })}
                </div>
              </div>
              <div className="file_bottom">
                {tasks.length > 0 && (
                  <BasicMetadata
                    ref={metadataRef}
                    items={tasks[editNumber].metadata.field || []}
                    onValueChange={onValueChange}
                    edit={true}
                  />
                )}
              </div>
            </div>
          </div>
          <div className="upload_bottom">
            <Button
              type="primary"
              onClick={debounce(upload, 200)}
              disabled={ selectedRowKeys.length === 0|| !uploadmisson || !!uploadTip}
            >
              {intl.formatMessage({
                id: 'upload',
                defaultMessage: '上传',
              })}
            </Button>
            <div>{uploadTip}</div>
          </div>
        </TabPane>
        {/* <TabPane tab='移动端上传' key='mobile'>
          <div className='qrcode'>
            <QRCode id='QRdom' size={200} includeMargin={true} value={`${window.location.origin}/mobile/#/pages/mine/index?upload=true`}></QRCode>
            <div>手机扫码上传</div>
          </div>
        </TabPane>  */}
      </Tabs>
      
      <ResourceModal
        treeData={treeData}
        visible={modalVisible}
        onConfirm={resourceModalConfirm}
        onCancel={() => setModalVisible(false)}
        onShowDetail={(id, detail) => {
          setEntityPreview({
            id: id,
            name: detail.name,
            type: detail.type,
          });
          setEntityModalVisible(true);
        }}
        fileType={(reftype.current == 'watermark' ? ['biz_sobey_picture'] : ['biz_sobey_video'])}
        multi={false}
      />
      { entityModalVisible?
        <ResourcePreviewModal
          modalVisible={entityModalVisible}
          modalClose={() => setEntityModalVisible(false)}
          resource={entityPreview}
        />:null
      }
      <ClassificationModal
        SavedValue={SavedValue}
        visible={ClassificationModallVisible}
        onConfirm={ClassificationModalConfirm}
        onCancel={() => setClassificationModallVisible(false)}
      />
      <Modal
        title={batchSettingVisible == 1 ? '添加水印' : '批量加工'}
        open={batchSettingVisible == 0 ? false : true}
        onCancel={() => setBatchSettingVisible(0)}
        className="batchSettingModal"
        footer={[<Button onClick={onConfirm}>确认</Button>]}
      >
        <div className="body">
          {batchSettingVisible == 2 && rmanGlobalParameter.includes(globalParams.titles_trailer_display) && (
            <>
              <div>
                <label>片头：</label>
                <div className="add" onClick={() => addHeadTailBatch('head')}>
                  {batchSet.head.keyframe_ ? (
                    <div className="done">
                      <img src={batchSet.head.keyframe_} />
                      <Button
                        type="link"
                        onClick={(e) => {
                          e.stopPropagation();
                          setBatchSet({
                            ...batchSet,
                            head: {
                              keyframe_: '',
                              contentId_: '',
                            },
                          });
                        }}
                        icon={<DeleteOutlined />}
                      />
                    </div>
                  ) : (
                    <>
                      <PlusCircleFilled />
                      <span>添加</span>
                    </>
                  )}
                </div>
              </div>
              <div>
                <label>片尾：</label>
                <div className="add" onClick={() => addHeadTailBatch('tail')}>
                  {batchSet.tail.keyframe_ ? (
                    <div className="done">
                      <img src={batchSet.tail.keyframe_} />
                      <Button
                        type="link"
                        onClick={(e) => {
                          e.stopPropagation();
                          setBatchSet({
                            ...batchSet,
                            tail: {
                              keyframe_: '',
                              contentId_: '',
                            },
                          });
                        }}
                        icon={<DeleteOutlined />}
                      />
                    </div>
                  ) : (
                    <>
                      <PlusCircleFilled />
                      <span>添加</span>
                    </>
                  )}
                </div>
              </div>
            </>
          )}
          {
            rmanGlobalParameter.includes(globalParams.watermark_display) &&
            <div>
              <label>水印/LOGO：</label>
              <div className="watermark" onClick={() => addHeadTailBatch('watermark')}>
                {batchSet.watermark.keyframe_ ? (
                  <div className="done">
                    <img src={batchSet.watermark.keyframe_} />
                    <Button
                      type="link"
                      onClick={(e) => {
                        e.stopPropagation();
                        setBatchSet({
                          ...batchSet,
                          watermark: {
                            keyframe_: '',
                            contentId_: '',
                            position: 'lt',
                          },
                        });
                      }}
                      icon={<DeleteOutlined />}
                    />
                  </div>
                ) : (
                  <div className="add">
                    <PlusCircleFilled />
                    <span>添加</span>
                  </div>
                )}
                <div
                  onClick={(e: any) => batchSetPotion(e, 'lt')}
                  className={`leftTop dot${batchSet.watermark.position == 'lt' ? ' active' : ''}`}
                ></div>
                <div
                  onClick={(e: any) => batchSetPotion(e, 'rt')}
                  className={`rightTop dot${batchSet.watermark.position == 'rt' ? ' active' : ''}`}
                ></div>
                <div
                  onClick={(e: any) => batchSetPotion(e, 'lb')}
                  className={`leftBottom dot${batchSet.watermark.position == 'lb' ? ' active' : ''}`}
                ></div>
                <div
                  onClick={(e: any) => batchSetPotion(e, 'rb')}
                  className={`rightBottom dot${
                    batchSet.watermark.position == 'rb' ? ' active' : ''
                  }`}
                ></div>
                <span className="tips">点击小方块选择水印位置</span>
              </div>
            </div>
          }
        </div>
      </Modal>
    </Modal>
  );
};

export default UploadBox;