﻿var mamUpload;
if (typeof define === "function" && define.amd)
{
    mamUpload = require('mam-upload');
}
else
{
    mamUpload = window.mamUpload;
}

var uploadModalmaterialPackageCtrl = ["$scope", "$uibModalInstance", "$stateParams", "$state", "$http", "opts",
    function ($scope, $uibModalInstance, $stateParams, $state, $http, opts) {

    var transfer = mamUpload.uploader.transfers[opts.uploadParams.transferType];
    var targetFolder = opts.uploadParams.targetFolder;
    var validateFun;
    var type = _.find(nxt.config.entityTypes, { code: 'materialpackage' });
    $scope.model = {};
    $scope.sortableOptions = {};
    $scope.titleName = (type && type.name) ? type.name : '素材包'

    $scope.addFiles = function () {
        transfer.openFileSelector(function (files) {
            handleFiles(files);
        });
    }

    $scope.removeAll = function () {
        $scope.model.files = []
    }

    $scope.ok = function () {
        var validateResult = validateFun();
        if (validateResult.success) {
            $scope.model.metadata.name = _.find($scope.model.metadata.field, { fieldName: 'name_' }).value;
            //创建素材包
            $http.post('~/materialpackage',{entityData:$scope.model.metadata.field,targetFolder}).then(function (res) {
                if(res && res.data && res.data.contentId){
                    $scope.model.relationContentId = res.data.contentId;
                    mam.message.success($scope.titleName+'创建成功！');
                    $uibModalInstance.close($scope.model);
                }else{
                    mam.message.error($scope.titleName+'创建失败！');
                }
            });
        } else {
            mam.message.error('元数据信息未通过验证，请修改后再试。');
        }
    }

    $scope.close = function () {
        $uibModalInstance.close(null);
    }

    $scope.getValidateFun = function (fun) {
        validateFun = fun;
    }

    function checkExist(file) {
        var isHas;
        if (opts.uploadParams.transferType == mamUpload.uploader.transferType.web) {
            isHas = _.find($scope.model.files, function (item) {
                return file.file.name === item.file.name &&
                    file.file.size === item.file.size &&
                    file.file.lastModified === item.file.lastModified;
            });
        } else { //vtube
            isHas = _.find($scope.model.files, function (item) {
                return file.file.FilePath === item.file.FilePath;
            });
        }
        if (isHas != null) {
            mam.message.error('文件 ' + file.fileName + ' 已存在');
            return true;
        }
        return false;
    }

    function handleFiles(files) {
        var ext = [];
        var fileName = [];
        var flg = false;
        _.forEach(files, function (file, index) {
            if (checkExist(file)) {
                return;
            }
            if (_.indexOf(type.extensions, file.metadata.ext.toLowerCase()) > -1) { //检查是否为允许的格式
                var data = {
                    fileName: file.fileName,
                    file: file.file
                }
                $scope.model.files.push(data);
            } else {
                flg = true;
                var temp = _.filter(ext, function (item) { return item == file.metadata.ext });
                if (!temp || temp.length == 0) {
                    ext.push(file.metadata.ext);
                }
                fileName.push(file.file.name);
            }
        });
        if (flg) {
            var a = ext.join("、");
            var b = fileName.join("、");
            mam.prompt('对不起，'+$scope.titleName+'不支持 ' + a + ' 格式的文件，' + b + ' 添加失败')
        }
        $scope.$applyAsync();
    }

        function autoFillUploadFieldUserInfoSetting(fields){
            if (nxt.config.autoFillUploadFieldUserInfoSetting)//根据配置自动填写元数据
            {
                var autoSetting = nxt.config.autoFillUploadFieldUserInfoSetting;
                _.forEach(autoSetting, function(autoField){
                    var field = _.find(fields, function (o) {
                        return o.fieldName === autoField.fieldName && o.fieldPath === autoField.fieldPath;
                    });
                    if (field != null) {
                        field.value = autoField.value.replace(/\$\{(.*?)\}/g, function (outer, content) {
                            var func = new Function(undefined, 'return ' + content);
                            return func.apply(window, []);
                        }) || '';
                    }
                })
            }
        }

        function getCookie(cname){
            var name = cname + "=";
            var ca = document.cookie.split(';');
            for(var i=0; i<ca.length; i++) 
            {
                var c = ca[i].trim();
                if (c.indexOf(name)==0) return c.substring(name.length,c.length);
            }
            return "";
        }

    function getMetadata(callback) {
        var data = {
            source: opts.uploadParams.module
        }
        let apiVersion = getCookie('apiVersion');
            var url = '/upload/get-all-fields-by-source'
            if (apiVersion && apiVersion === 'mamcore2.3'){
                url = '/scntm/v1/old/upload/get-all-fields-by-source'
            }
        $http.post('~' + url,data).then(function (res) {
            $scope.model.metadata.field = res.data[type.code];
            var metadata = res.data[type.code];
            if (type.code === 'video' && !(metadata instanceof Array))
            {
                metadata = metadata.data;
            }
            $scope.model.metadata.field = metadata;

            autoFillUploadFieldUserInfoSetting($scope.model.metadata.field);

            callback();
        });
    }

    function init() {
        if (type == null) {
            mam.message.error('系统没有配置素材包类型，无法进行素材包上传。');
            $uibModalInstance.close(null);
        } else {
            $scope.model = {
                entityType: type.code,
                files: [],
                metadata: {},
                status: 'added',
                progress: 0
            }
            getMetadata(function () {
                handleFiles(opts.files);
            });
        }
    }

    init();

}];

angular.module('mam-upload').registerController('uploadModalmaterialPackageCtrl', uploadModalmaterialPackageCtrl);
