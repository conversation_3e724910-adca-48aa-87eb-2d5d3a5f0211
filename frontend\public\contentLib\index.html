<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <title>内容库演示</title>
</head>

<body>
  <div id="container"></div>

  <script src="./contentLibrary.min.js"></script>


  <script type="text/javascript">
    document.addEventListener("DOMContentLoaded", function () {
      var contentLibrary = new ContentLibrary("container", {
        ipPrefix: "http://**************",
        isMultiple: true,
        // onlyType: "video",x
        primaryColor: "#eb766c",
        onSelect: function (data) {
          console.info(data);
        },
      });
      contentLibrary.open();
      // contentLibrary.setToken("3cd4ff8c-e135-4097-b019-27e7197a6271");
    });
    window.less = {
      async: false,
      env: 'production'
    };
  </script>
  <link rel="stylesheet/less" href="./theme.less">
  <script src="./less.min.js"></script>
</body>

</html>