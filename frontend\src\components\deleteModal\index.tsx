import React, { useState, useEffect } from 'react';
import {
  useIntl
} from 'umi';
import { Modal, Progress, Button, Spin, message, Table } from 'antd';
import deleteApis from '@/service/deleteApis';
import './index.less';

interface CreateModalProps {
  modalVisible: boolean;
  modalClose: () => void;
  refresh: (page?: number) => void; // 刷新
  deletelist: any;
  recycleBin?: boolean;
  clickedBottom?: string;
}

const DeleteModal: React.FC<CreateModalProps> = props => {
  const {
    modalClose,
    modalVisible,
    refresh,
    deletelist,
    recycleBin,
    clickedBottom,
  } = props;
  const [sameType, setSameType] = useState<boolean>(false);
  const [beginDelete, setBeginDelete] = useState<boolean>(false);
  const [processId, setProcessId] = useState<string>('');
  const [process, setProcess] = useState<number>(0);
  const intl = useIntl();
  useEffect(() => {
    if (modalVisible === true) {
      if (recycleBin) {
        setSameType(true);
        return;
      }
      let i = deletelist.some((item: any) => {
        return item.type_ === 'folder';
      });
      let k = deletelist.every((item: any) => {
        return item.type_ === 'folder';
      });
      let j = deletelist.some((item: any) => {
        return item.usestatus === 'true';
      });
      if (!k && i) {
        message.error(intl.formatMessage({ id: '不能同时删除文件和文件夹' }));
        setSameType(false);
        modalClose();
      }else if(j){
        // message.warning(intl.formatMessage({ id: '该资源已被课程使用，删除后课程中不可查看，是否确认删除？' }));
        setSameType(true);
      }
       else {
        setSameType(true);
      }
    }
  }, [modalVisible]);
  useEffect(() => {
    if (processId) {
      let data = '删除';
      data = recycleBin
        ? clickedBottom === 'empty'
          ? '清空'
          : clickedBottom === 'reduction'
          ? '还原'
          : '删除'
        : '删除';
      let flag = true;
      let detint = setInterval(() => {
        if(flag){ 
          flag = false //加个锁 防止因为异步导致触发多次弹框
          deleteApis.deleteResult(processId).then(resdata => {
            if (resdata && resdata.success && resdata.data) {
              flag = true
              setProcess(
                (100 * resdata.data.success_count) / resdata.data.total_count,
              );
              if (resdata.data.status === 'FINISH') {
                clearInterval(detint);
                resdata.data.failed_count === 0
                  ? message.success(`${data}${intl.formatMessage({ id: '成功' })}`)
                  : message.error(`${data}${intl.formatMessage({ id: '失败' })}`);
                setTimeout(()=>{closeOk();},1000) //为了能给进度条显示 加个延迟
                //删除完毕后 将数据同步给learn
                if(resdata.data.failed_count === 0){  
                  if(recycleBin){
                    if(clickedBottom === 'reduction'){ //还原
                      let reductionVideo:any = [];
                      // console.log('reductionVideo',deletelist);
                      deletelist.forEach((i: any) => {
                        if(i.type_ === "biz_sobey_video"){
                          reductionVideo.push(i.contentId_);
                        }
                      });
                      if(reductionVideo){
                        deleteApis.syncReductionData(reductionVideo)
                      }
                    }
                  }else{ //删除
                    let deleteVideo:any = [];
                    // console.log('deletelist',deletelist);
                    deletelist.forEach((i: any) => {
                      if(i.type_ === "biz_sobey_video"){
                        deleteVideo.push(i.contentId_);
                      }
                    });
                    if(deleteVideo){
                      deleteApis.syncDeleteData(deleteVideo)
                    }
                  }
                }
              }
            } else {
              clearInterval(detint);
              message.error(`${data}${intl.formatMessage({ id: '失败' })}`)
              closeOk();
            }
          });
        }
      }, 100);
    }
  }, [processId]);
  const handleCancel = () => {
    setSameType(false);
    modalClose();
    setBeginDelete(false);
    setProcess(0);
  };
  const closeOk = () => {
    handleCancel();
    refresh(1);
  };

  // 确认删除
  const deleteitem = () => {
    //删除
    let deleteId: any = [];
    deletelist.forEach((i: any) => {
      deleteId.push(i.contentId_);
    });
    setBeginDelete(true);
    if (recycleBin) {
      if (clickedBottom === 'reduction') {
        deleteApis
          .reductionResource({
            contentIds: deleteId,
          })
          .then((res: any) => {
            if (res && res.success && res.data) {
              setProcessId(res.data.process_id);
            } else {
              message.error(intl.formatMessage({ id: '还原失败' }))
              closeOk();
            }
            refresh();
          });
      } else {
        if (clickedBottom === 'empty') {
          deleteId = [];
        }
        deleteApis.deleteResourceRecycle([...deleteId]).then((res: any) => {
          if (res && res.success && res.data) {
            setProcessId(res.data.data.process_id);
          } else {
            message.error(intl.formatMessage({ id: '删除失败' }))
            closeOk();
          }
        });
      }
    } else {
      // 获取删除的资源是否是public
      let isPublic = deletelist[0]?.privilege_?.includes('public') ?? false;
      deleteApis.deleteResource([...deleteId], isPublic, true).then((res: any) => {
        if (res && res.success && res.data) {
          setProcessId(res.data.process_id);
          deleteId.forEach(item => {
            refresh(1, {
              name: '删除',
              contentId_: item
            })
          })
        } else {
          message.error('删除失败');
          message.error(intl.formatMessage({ id: '删除失败' }))
          closeOk();
        }
      });
    }
  };



  let bott = null;
  let content = null;
  if (!beginDelete) {
    if (recycleBin) {
      content =
        clickedBottom === 'empty' ? (
          <div>{intl.formatMessage({ id: '确认清空整个回收站文件' })}</div>
        ) : clickedBottom === 'reduction' ? (
          <div>{intl.formatMessage({ id: '确认还原选中的文件' })}</div>
        ) : (
          <div>{intl.formatMessage({ id: '文件删除后将无法恢复，您确认要彻底删除所选文件吗' })}</div>
        );
    } else {
      content =
        deletelist[0] && deletelist[0].type_ === 'folder' ? (
          <div>{intl.formatMessage({ id: '是否删除文件夹及文件夹内的素材到回收站' })}</div>
        ) : (
          <><div>{intl.formatMessage({ id: '是否删除素材到回收站' })}</div>
          {deletelist.some((item: any) => {return item.usestatus === 'true'}) && <div>该资源已被课程使用，删除后课程中不可查看，是否确认删除？</div>}</>
        );
    }
    bott = [
      <Button key="back" onClick={handleCancel}>
        {intl.formatMessage({ id: '取消' })}
      </Button>,
      <Button key="submit" type="primary" onClick={deleteitem}>
        {intl.formatMessage({ id: '确定' })}
      </Button>,
    ];
  } else {
    content = (
      <div className="delete-process">
        <div>
          <div>
            {intl.formatMessage({ id: '正在' })}{clickedBottom === 'reduction' ? `${intl.formatMessage({ id: '还原' })}` : `${intl.formatMessage({ id: '删除' })}`}
            {`${intl.formatMessage({ id: '文件，请耐心等待' })}...`}
          </div>
          <Progress
            percent={process}
            style={{ width: 300 }}
            strokeColor={{
              '0%': '#FF806F',
              '100%': '#FF8750',
            }}
          />
        </div>
      </div>
    );
    // content = clickedBottom === 'reduction' ? <Spin tip="还原中" /> : <Spin tip="删除中" />
  }
  return (
    <Modal
      destroyOnClose
      title={
        recycleBin
          ? clickedBottom === 'empty'
            ? intl.formatMessage({ id: '清空' })
            : clickedBottom === 'reduction'
            ? intl.formatMessage({ id: '还原' })
            : intl.formatMessage({ id: '删除' })
          : intl.formatMessage({ id: '删除' })
      }
      visible={modalVisible && sameType}
      closable={false}
      footer={bott}
      // width={700}
    >
      <div className="delete-modal">{content}</div>
    </Modal>
  );
};

export default DeleteModal;
