import loginApis from '@/service/loginApis';
import loginTypes from '@/types/loginTypes';

export interface IPermission {
  permissions: string[];
  modules: string[];
  rmanGlobalParameter: string[];
  parameterConfig: IObj<'true' | 'false'>;
  my_myVerify_display: boolean;
  classifiedByschedule: boolean
  publish_review_display: boolean;
}
interface IObj<T> {
  [propsName: string]: T;
}

const _rmanCode = 'rman';
const initPlatform = async () => {
  const res: any = await loginApis.fetchPlatform();
  if (res && res.errorCode === 'success') {
    window.localStorage.setItem('upform_platform', res.extendMessage);
  }
};
export default {
  namespace: 'permission',
  subscriptions: {
    setup({ dispatch, history }: any) {
      return history.listen(({ pathname }: any) => {
        initPlatform();
        if (pathname === '/mapv3' || pathname.indexOf('basic') > -1 || pathname.indexOf('task/taskprogress') > -1) {
          dispatch({
            type: 'fetchPermissions',
          });
          dispatch({
            type: 'fetchGlobalParameter',
          });
          dispatch({
            type: 'fetchGlobalParameterConfigs'
          })
        }
      });
    },
  },
  state: {
    permissions: [],
    rmanGlobalParameter: [],
    modules: [],
    parameterConfig: {},
    njtcPlayAddress: [],
    rmanGlobalText:[],
    classifiedByschedule:[],
    my_myVerify_display: true,
    publish_review_display: true
  },
  effects: {
    *fetchPermissions(_: any, { call, put }: any) {
      const {
        errorCode,
        extendMessage,
      }: API.OsResponse<loginTypes.IPermission[]> = yield call(
        loginApis.fetchUserPermissionsV2,
      );
      if (errorCode === 'success') {
        // const rmanPermission = extendMessage.find(
        //   (per: loginTypes.IPermission) => per.code === _rmanCode,
        // )?.moduleFeatures;
        // if (rmanPermission) {
        //   const permissions = Object.keys(rmanPermission)
        //     .map(key => rmanPermission[key])
        //     .flat(2);
        //   yield put({
        //     type: 'updateState',
        //     payload: {
        //       permissions,
        //     },
        //   });
        // }
        if (typeof extendMessage.moduleFeatures === 'object') {
          const permissions = Object.keys(extendMessage.moduleFeatures)
            .map(key => extendMessage.moduleFeatures[key])
            .flat(2);
          const modules = extendMessage.modules
          yield put({
            type: 'updateState',
            payload: {
              permissions,
              modules
            },
          });
        }
      }
    },
    *fetchGlobalParameter(_: any, { call, put }: any) {
      const {
        errorCode,
        extendMessage,
      }: API.OsResponse<loginTypes.IGlobalParam[]> = yield call(
        loginApis.fetchRmanGlobalParam,
      );
      if (errorCode === 'success') {
        if (Array.isArray(extendMessage)) {
          const rmanGlobalParameter = extendMessage.filter(item => item.value === "true").map(item => item.code);
          const rmanGlobalText = extendMessage.filter(item => item.code === "resource_detail_menu").map(item => JSON.parse(item.value));
          const njtcPlayAddress = extendMessage.filter(item => item.code === "njtc_play_address").map(item => item.value);
          // true 按课表分类  false 不按课表分类  不设置 按课表分类
          const classifiedByschedule = !extendMessage.filter(item => item.code === "my_video_classified_schedule").map(item => item.value).includes('false');
          console.log(classifiedByschedule, 'myVideoClassified')
          const my_myVerify_display = !extendMessage.filter(item => item.code === "my_myVerify_display").map(item => item.value).includes('false');
          const publish_review_display = !extendMessage.filter(item => item.code === "publish_review_display").map(item => item.value).includes('false');
          yield put({
            type: 'updateState',
            payload: {
              rmanGlobalText,
              rmanGlobalParameter,
              njtcPlayAddress,
              classifiedByschedule,
              my_myVerify_display,
              publish_review_display
            },
          });

        }
      }
    },
    *fetchGlobalParameterConfigs(_: any, { call, put }: any) {
      const { errorCode, extendMessage} = yield call(loginApis.fetchParameterConfigs);
      if (errorCode === 'success') {
        if (Array.isArray(extendMessage)) {
          const parameterConfig: IObj<'true' | 'false'> = {};
          extendMessage.forEach((r: any) => {
            parameterConfig[r.code] = r.value;
          })
          yield put({
            type: 'updateState',
            payload: {
              parameterConfig
            },
          });
        }
      }
    }
  },
  reducers: {
    updateState(state: IPermission, { payload }: any) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
