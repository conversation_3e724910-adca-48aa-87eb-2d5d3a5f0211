import React, { FC, useEffect } from 'react';
import { asyncLoadScript } from '@/utils';
import jQuery from 'jquery';
import { Player } from '@/components';
import { H5PlayerProps } from '@/components/h5player';
import './audio.less';
import { useSelector } from 'umi';
import { IPermission } from '@/models/permission';
import globalParams from '@/permission/globalParams';

const defaultFeatures: any = [
  'playpause',
  'current',
  'progress',
  'duration',
  'tracks',
  'volume',
  'fullscreen',
  'contextmenu',
  'speed',
  'backframe',
  'prevframe',
  'skipback',
  'skipforward',
  // 'trimin',
  // 'trimout',
  // 'cleantrim',
  'seeking',
  // 'volumnTrack',
  // 'getKeyframe',
  // 'setSection',
  // 'exportSection',
  // 'saveSection',
  // 'togglekeypoint',
  // 'selectScreen',
  'tostart',
  'toend',
  'audioVisualizer',
];
const mobileFeatures = [
  'playpause',
  'current',
  'progress',
  'duration',
  // 'volume',
  'fullscreen',
  'contextmenu',
  // 'speed',
  'backframe',
  'prevframe',
  'skipback',
  'skipforward',
  // 'trimin',
  // 'trimout',
  // 'cleantrim',
  // 'seeking',
  'tostart',
  'toend',
  // 'audioVisualizer',
]
const defaultConfig = {
  playType: 'audio',
  frameRate: 25,
  pluginPath: '/rman/libs/mam-h5player/dist/',
  features: defaultFeatures,
  disableRangeLock: true,
  // poster: '/rman/libs/mam-h5player/dist/content/musicbg.jpg',
  /**
   * set 标记
   * @param videoElement
   */
  setTrimin: function (videoElement: any) {
    videoElement.stop();
  },
};

let player: any;
const Audio: FC<H5PlayerProps> = ({
  src,
  config = {},
  onSuccess,
  setSection,
  modifySection,
  resetSection,
  onPlayChange,
  onError,
  isEditPermission = false,
  linkWatermark
}) => {
  const { mobileFlag } = useSelector<{ config: any }, any>(
    ({ config }) => config
  );
  const { rmanGlobalParameter } = useSelector<{ permission: any }, IPermission>(({ permission }) => permission);
  const userInfo = (window as any).login_useInfo
  const init = () => {
    if (!player) {
      player = ($('#h5Audio') as any).h5mediaplayer({
        ...defaultConfig,
        // features: mobileFlag?mobileFeatures:defaultFeatures,
        features: mobileFlag ? mobileFeatures :
          isEditPermission ? [...defaultFeatures, 'trimin', 'trimout', 'setSection'] : defaultFeatures,
        success: (media: any, node: any, player: Player) => {
          if (onSuccess && typeof onSuccess === 'function') {
            onSuccess(player);
          }
        },
        error: (e: any) => {
          if (onError && typeof onError === 'function') {
            onError(e);
          }
        },
        ...config,
        setSection,
        modifySection,
        resetSection,
      });
      (window as any).document
        .getElementById('h5Audio')
        .addEventListener('accurateTimeUpdate', function () {
          onPlayChange && onPlayChange(player.getCurrentTime() * 10000000);
        });
    }
  };
  useEffect(() => {
    (window as any).$ = (window as any).jQuery = jQuery;
    (window as any).nxt = {};
    asyncLoadScript('/rman/libs/mam-base/dist/mam-base.js');
    // asyncLoadScript(
    //   '/rman/libs/mam-timecode-convert/dist/mam-timecode-convert.js',
    // );
    asyncLoadScript('/rman/libs/mam-h5player/dist/mam-h5player.js', () => {
      init();
    });
  }, []);

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
      }}
      className='audio_container'
    >
      <audio
        id="h5Audio"
        style={{ width: '100%', height: '100%' }}
        preload="metadata"
        crossOrigin="anonymous"
        src={src}
      />
      {/* 水印 */}
      {
        rmanGlobalParameter.includes(globalParams.watermark_display) && linkWatermark &&
        <div className='watermarkShow'>
          {Array(20).fill(null).map((_, i) => (
            <div key={i}>{linkWatermark}</div>
          ))}
        </div>
      }
    </div>
  );
};
export default Audio;
