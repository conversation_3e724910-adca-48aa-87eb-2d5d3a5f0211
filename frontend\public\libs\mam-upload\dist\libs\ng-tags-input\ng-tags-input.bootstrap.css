tags-input {
  box-shadow: none;
  border: none;
  padding: 0;
  min-height: 34px;
}
tags-input .host {
  margin: 0;
}
tags-input .tags {
  -moz-appearance: none;
  -webkit-appearance: none;
  border: 1px solid #ccc;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -webkit-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
  -moz-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}
tags-input .tags .tag-item {
  color: #fff;
  background: #428bca;
  border: 1px solid #357ebd;
  border-radius: 4px;
}
tags-input .tags .tag-item.selected {
  color: #fff;
  background: #d9534f;
  border: 1px solid #d43f3a;
}
tags-input .tags .tag-item .remove-button:hover {
  text-decoration: none;
}
tags-input .tags.focused {
  border: 1px solid #66afe9;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
}
tags-input .autocomplete {
  border-radius: 4px;
}
tags-input .autocomplete .suggestion-item.selected {
  color: #262626;
  background-color: #f5f5f5;
}
tags-input .autocomplete .suggestion-item.selected em {
  color: #262626;
  background-color: #f5f5f5;
}
tags-input .autocomplete .suggestion-item em {
  color: #000;
  background-color: #fff;
}
tags-input.ng-invalid .tags {
  border-color: #843534;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
}
tags-input[disabled] .tags {
  background-color: #eee;
}
tags-input[disabled] .tags .tag-item {
  background: #337ab7;
  opacity: 0.65;
}
tags-input[disabled] .tags .input {
  background-color: #eee;
}

.input-group tags-input {
  padding: 0;
  display: table-cell;
}
.input-group tags-input:not(:first-child) .tags {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.input-group tags-input:not(:last-child) .tags {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group-lg tags-input:first-child .tags {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}
.input-group-lg tags-input:last-child .tags {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.input-group-sm tags-input:first-child .tags {
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}
.input-group-sm tags-input:last-child .tags {
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}

tags-input.ti-input-lg, .input-group-lg tags-input {
  min-height: 46px;
}
tags-input.ti-input-lg .tags, .input-group-lg tags-input .tags {
  border-radius: 6px;
}
tags-input.ti-input-lg .tags .tag-item, .input-group-lg tags-input .tags .tag-item {
  height: 38px;
  line-height: 37px;
  font-size: 18px;
  border-radius: 6px;
}
tags-input.ti-input-lg .tags .tag-item .remove-button, .input-group-lg tags-input .tags .tag-item .remove-button {
  font-size: 20px;
}
tags-input.ti-input-lg .tags .input, .input-group-lg tags-input .tags .input {
  height: 38px;
  font-size: 18px;
}
tags-input.ti-input-sm, .input-group-sm tags-input {
  min-height: 30px;
}
tags-input.ti-input-sm .tags, .input-group-sm tags-input .tags {
  border-radius: 3px;
}
tags-input.ti-input-sm .tags .tag-item, .input-group-sm tags-input .tags .tag-item {
  height: 22px;
  line-height: 21px;
  font-size: 12px;
  border-radius: 3px;
}
tags-input.ti-input-sm .tags .tag-item .remove-button, .input-group-sm tags-input .tags .tag-item .remove-button {
  font-size: 16px;
}
tags-input.ti-input-sm .tags .input, .input-group-sm tags-input .tags .input {
  height: 22px;
  font-size: 12px;
}

.has-feedback tags-input .tags {
  padding-right: 30px;
}

.has-success tags-input .tags {
  border-color: #3c763d;
}
.has-success tags-input .tags.focused {
  border-color: #2b542c;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168;
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168;
}

.has-error tags-input .tags {
  border-color: #a94442;
}
.has-error tags-input .tags.focused {
  border-color: #843534;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
}

.has-warning tags-input .tags {
  border-color: #8a6d3b;
}
.has-warning tags-input .tags.focused {
  border-color: #66512c;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b;
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b;
}

/*# sourceMappingURL=ng-tags-input.bootstrap.css.map */
