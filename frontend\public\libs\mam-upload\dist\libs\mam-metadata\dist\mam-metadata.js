/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// identity function for calling harmony imports with the correct context
/******/ 	__webpack_require__.i = function(value) { return value; };
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, {
/******/ 				configurable: false,
/******/ 				enumerable: true,
/******/ 				get: getter
/******/ 			});
/******/ 		}
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "/dist/";
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = 52);
/******/ })
/************************************************************************/
/******/ ([
/* 0 */
/***/ (function(module, exports) {

/*
	MIT License http://www.opensource.org/licenses/mit-license.php
	Author Tobias Koppers @sokra
*/
// css base code, injected by the css-loader
module.exports = function(useSourceMap) {
	var list = [];

	// return the list of modules as css string
	list.toString = function toString() {
		return this.map(function (item) {
			var content = cssWithMappingToString(item, useSourceMap);
			if(item[2]) {
				return "@media " + item[2] + "{" + content + "}";
			} else {
				return content;
			}
		}).join("");
	};

	// import a list of modules into the list
	list.i = function(modules, mediaQuery) {
		if(typeof modules === "string")
			modules = [[null, modules, ""]];
		var alreadyImportedModules = {};
		for(var i = 0; i < this.length; i++) {
			var id = this[i][0];
			if(typeof id === "number")
				alreadyImportedModules[id] = true;
		}
		for(i = 0; i < modules.length; i++) {
			var item = modules[i];
			// skip already imported module
			// this implementation is not 100% perfect for weird media query combinations
			//  when a module is imported multiple times with different media queries.
			//  I hope this will never occur (Hey this way we have smaller bundles)
			if(typeof item[0] !== "number" || !alreadyImportedModules[item[0]]) {
				if(mediaQuery && !item[2]) {
					item[2] = mediaQuery;
				} else if(mediaQuery) {
					item[2] = "(" + item[2] + ") and (" + mediaQuery + ")";
				}
				list.push(item);
			}
		}
	};
	return list;
};

function cssWithMappingToString(item, useSourceMap) {
	var content = item[1] || '';
	var cssMapping = item[3];
	if (!cssMapping) {
		return content;
	}

	if (useSourceMap && typeof btoa === 'function') {
		var sourceMapping = toComment(cssMapping);
		var sourceURLs = cssMapping.sources.map(function (source) {
			return '/*# sourceURL=' + cssMapping.sourceRoot + source + ' */'
		});

		return [content].concat(sourceURLs).concat([sourceMapping]).join('\n');
	}

	return [content].join('\n');
}

// Adapted from convert-source-map (MIT)
function toComment(sourceMap) {
	// eslint-disable-next-line no-undef
	var base64 = btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap))));
	var data = 'sourceMappingURL=data:application/json;charset=utf-8;base64,' + base64;

	return '/*# ' + data + ' */';
}


/***/ }),
/* 1 */
/***/ (function(module, exports, __webpack_require__) {

/*
	MIT License http://www.opensource.org/licenses/mit-license.php
	Author Tobias Koppers @sokra
*/

var stylesInDom = {};

var	memoize = function (fn) {
	var memo;

	return function () {
		if (typeof memo === "undefined") memo = fn.apply(this, arguments);
		return memo;
	};
};

var isOldIE = memoize(function () {
	// Test for IE <= 9 as proposed by Browserhacks
	// @see http://browserhacks.com/#hack-e71d8692f65334173fee715c222cb805
	// Tests for existence of standard globals is to allow style-loader
	// to operate correctly into non-standard environments
	// @see https://github.com/webpack-contrib/style-loader/issues/177
	return window && document && document.all && !window.atob;
});

var getElement = (function (fn) {
	var memo = {};

	return function(selector) {
		if (typeof memo[selector] === "undefined") {
			memo[selector] = fn.call(this, selector);
		}

		return memo[selector]
	};
})(function (target) {
	return document.querySelector(target)
});

var singleton = null;
var	singletonCounter = 0;
var	stylesInsertedAtTop = [];

var	fixUrls = __webpack_require__(49);

module.exports = function(list, options) {
	if (typeof DEBUG !== "undefined" && DEBUG) {
		if (typeof document !== "object") throw new Error("The style-loader cannot be used in a non-browser environment");
	}

	options = options || {};

	options.attrs = typeof options.attrs === "object" ? options.attrs : {};

	// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>
	// tags it will allow on a page
	if (!options.singleton) options.singleton = isOldIE();

	// By default, add <style> tags to the <head> element
	if (!options.insertInto) options.insertInto = "head";

	// By default, add <style> tags to the bottom of the target
	if (!options.insertAt) options.insertAt = "bottom";

	var styles = listToStyles(list, options);

	addStylesToDom(styles, options);

	return function update (newList) {
		var mayRemove = [];

		for (var i = 0; i < styles.length; i++) {
			var item = styles[i];
			var domStyle = stylesInDom[item.id];

			domStyle.refs--;
			mayRemove.push(domStyle);
		}

		if(newList) {
			var newStyles = listToStyles(newList, options);
			addStylesToDom(newStyles, options);
		}

		for (var i = 0; i < mayRemove.length; i++) {
			var domStyle = mayRemove[i];

			if(domStyle.refs === 0) {
				for (var j = 0; j < domStyle.parts.length; j++) domStyle.parts[j]();

				delete stylesInDom[domStyle.id];
			}
		}
	};
};

function addStylesToDom (styles, options) {
	for (var i = 0; i < styles.length; i++) {
		var item = styles[i];
		var domStyle = stylesInDom[item.id];

		if(domStyle) {
			domStyle.refs++;

			for(var j = 0; j < domStyle.parts.length; j++) {
				domStyle.parts[j](item.parts[j]);
			}

			for(; j < item.parts.length; j++) {
				domStyle.parts.push(addStyle(item.parts[j], options));
			}
		} else {
			var parts = [];

			for(var j = 0; j < item.parts.length; j++) {
				parts.push(addStyle(item.parts[j], options));
			}

			stylesInDom[item.id] = {id: item.id, refs: 1, parts: parts};
		}
	}
}

function listToStyles (list, options) {
	var styles = [];
	var newStyles = {};

	for (var i = 0; i < list.length; i++) {
		var item = list[i];
		var id = options.base ? item[0] + options.base : item[0];
		var css = item[1];
		var media = item[2];
		var sourceMap = item[3];
		var part = {css: css, media: media, sourceMap: sourceMap};

		if(!newStyles[id]) styles.push(newStyles[id] = {id: id, parts: [part]});
		else newStyles[id].parts.push(part);
	}

	return styles;
}

function insertStyleElement (options, style) {
	var target = getElement(options.insertInto)

	if (!target) {
		throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");
	}

	var lastStyleElementInsertedAtTop = stylesInsertedAtTop[stylesInsertedAtTop.length - 1];

	if (options.insertAt === "top") {
		if (!lastStyleElementInsertedAtTop) {
			target.insertBefore(style, target.firstChild);
		} else if (lastStyleElementInsertedAtTop.nextSibling) {
			target.insertBefore(style, lastStyleElementInsertedAtTop.nextSibling);
		} else {
			target.appendChild(style);
		}
		stylesInsertedAtTop.push(style);
	} else if (options.insertAt === "bottom") {
		target.appendChild(style);
	} else {
		throw new Error("Invalid value for parameter 'insertAt'. Must be 'top' or 'bottom'.");
	}
}

function removeStyleElement (style) {
	if (style.parentNode === null) return false;
	style.parentNode.removeChild(style);

	var idx = stylesInsertedAtTop.indexOf(style);
	if(idx >= 0) {
		stylesInsertedAtTop.splice(idx, 1);
	}
}

function createStyleElement (options) {
	var style = document.createElement("style");

	options.attrs.type = "text/css";

	addAttrs(style, options.attrs);
	insertStyleElement(options, style);

	return style;
}

function createLinkElement (options) {
	var link = document.createElement("link");

	options.attrs.type = "text/css";
	options.attrs.rel = "stylesheet";

	addAttrs(link, options.attrs);
	insertStyleElement(options, link);

	return link;
}

function addAttrs (el, attrs) {
	Object.keys(attrs).forEach(function (key) {
		el.setAttribute(key, attrs[key]);
	});
}

function addStyle (obj, options) {
	var style, update, remove, result;

	// If a transform function was defined, run it on the css
	if (options.transform && obj.css) {
	    result = options.transform(obj.css);

	    if (result) {
	    	// If transform returns a value, use that instead of the original css.
	    	// This allows running runtime transformations on the css.
	    	obj.css = result;
	    } else {
	    	// If the transform function returns a falsy value, don't add this css.
	    	// This allows conditional loading of css
	    	return function() {
	    		// noop
	    	};
	    }
	}

	if (options.singleton) {
		var styleIndex = singletonCounter++;

		style = singleton || (singleton = createStyleElement(options));

		update = applyToSingletonTag.bind(null, style, styleIndex, false);
		remove = applyToSingletonTag.bind(null, style, styleIndex, true);

	} else if (
		obj.sourceMap &&
		typeof URL === "function" &&
		typeof URL.createObjectURL === "function" &&
		typeof URL.revokeObjectURL === "function" &&
		typeof Blob === "function" &&
		typeof btoa === "function"
	) {
		style = createLinkElement(options);
		update = updateLink.bind(null, style, options);
		remove = function () {
			removeStyleElement(style);

			if(style.href) URL.revokeObjectURL(style.href);
		};
	} else {
		style = createStyleElement(options);
		update = applyToTag.bind(null, style);
		remove = function () {
			removeStyleElement(style);
		};
	}

	update(obj);

	return function updateStyle (newObj) {
		if (newObj) {
			if (
				newObj.css === obj.css &&
				newObj.media === obj.media &&
				newObj.sourceMap === obj.sourceMap
			) {
				return;
			}

			update(obj = newObj);
		} else {
			remove();
		}
	};
}

var replaceText = (function () {
	var textStore = [];

	return function (index, replacement) {
		textStore[index] = replacement;

		return textStore.filter(Boolean).join('\n');
	};
})();

function applyToSingletonTag (style, index, remove, obj) {
	var css = remove ? "" : obj.css;

	if (style.styleSheet) {
		style.styleSheet.cssText = replaceText(index, css);
	} else {
		var cssNode = document.createTextNode(css);
		var childNodes = style.childNodes;

		if (childNodes[index]) style.removeChild(childNodes[index]);

		if (childNodes.length) {
			style.insertBefore(cssNode, childNodes[index]);
		} else {
			style.appendChild(cssNode);
		}
	}
}

function applyToTag (style, obj) {
	var css = obj.css;
	var media = obj.media;

	if(media) {
		style.setAttribute("media", media)
	}

	if(style.styleSheet) {
		style.styleSheet.cssText = css;
	} else {
		while(style.firstChild) {
			style.removeChild(style.firstChild);
		}

		style.appendChild(document.createTextNode(css));
	}
}

function updateLink (link, options, obj) {
	var css = obj.css;
	var sourceMap = obj.sourceMap;

	/*
		If convertToAbsoluteUrls isn't defined, but sourcemaps are enabled
		and there is no publicPath defined then lets turn convertToAbsoluteUrls
		on by default.  Otherwise default to the convertToAbsoluteUrls option
		directly
	*/
	var autoFixUrls = options.convertToAbsoluteUrls === undefined && sourceMap;

	if (options.convertToAbsoluteUrls || autoFixUrls) {
		css = fixUrls(css);
	}

	if (sourceMap) {
		// http://stackoverflow.com/a/26603875
		css += "\n/*# sourceMappingURL=data:application/json;base64," + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + " */";
	}

	var blob = new Blob([css], { type: "text/css" });

	var oldSrc = link.href;

	link.href = URL.createObjectURL(blob);

	if(oldSrc) URL.revokeObjectURL(oldSrc);
}


/***/ }),
/* 2 */
/***/ (function(module, exports, __webpack_require__) {

angular.module('mam-metadata')
    .directive('mam2MfcBool', ['mam2MetadataService', function (mam2MetadataService) {
        return {
            restrict: 'E',
            template: __webpack_require__(25),
            replace: true,
            scope: {
                item: '=',
                type: '@',
            },
            link: function (scope, element, attr, ctrl) {
                // done
            }
        };
    }]);

/***/ }),
/* 3 */
/***/ (function(module, exports, __webpack_require__) {

angular.module('mam-metadata')
    .directive('mam2MfcDate', ['mam2MetadataService', function (mam2MetadataService) {
        return {
            restrict: 'E',
            template: __webpack_require__(26),
            replace: true,
            scope: {
                item: '=',
                type: '@',
            },
            link: function (scope, element, attr, ctrl) {
                var $e = $(element);


                function validata() {
                    mam2MetadataService.validate(scope.item);
                }

                function init() {
                    var ctrlData;
                    try {
                        ctrlData = JSON.parse(scope.item.controlData);
                    } catch (error) {
                        ctrlData = null;
                    }

                    var opts = {
                        showSecond: true,
                        format: 'Y-m-d',
                        timepicker: false,
                        onClose: function () {
                            if ($e.val() != '') {
                                scope.item.value = $e.val();
                                validata();
                                scope.$applyAsync();
                            }
                        }
                    };

                    if (ctrlData != null) {
                        switch (ctrlData.type) {
                            case "onlypass":
                                opts.maxDate = '0'; //只能选今天以前-仅过去时间
                                break;
                            case "onlyfuture":
                                opts.minDate = '0';//只能选今天以后-仅未来时间
                                break;
                        }
                    }

                    if (!scope.item.isReadOnly) {
                        $e.datetimepicker(opts);
                    }
                }

                scope.$watch('type', function () {
                    if (scope.type == 'edit') {
                        init();
                        element.find('input[type=text]').on('blur', function () {
                            scope.$apply(function () {
                                validata();
                            });
                        });
                    } else {

                    }
                })

            }
        };
    }]);

/***/ }),
/* 4 */
/***/ (function(module, exports, __webpack_require__) {

angular.module('mam-metadata')
    .directive('mam2MfcDatetime', ['mam2MetadataService', function (mam2MetadataService) {
        return {
            restrict: 'E',
            template: __webpack_require__(27),
            replace: true,
            scope: {
                item: '=',
                type: '@',
            },
            link: function (scope, element, attr, ctrl) {
                var $e = $(element);


                function validata() {
                    mam2MetadataService.validate(scope.item);
                }

                function init() {
                    var ctrlData;
                    try {
                        ctrlData = JSON.parse(scope.item.controlData);
                    } catch (error) {
                        ctrlData = null;
                    }

                    var opts = {
                        showSecond: true,
                        formatTime: 'H:m:s',
                        format: 'Y-m-d H:m:s',
                        onClose: function () {
                            if ($e.val() != '') {
                                scope.item.value = $e.val();
                                validata();
                                scope.$applyAsync();
                            }
                        }
                    };

                    if (ctrlData != null) {
                        switch (ctrlData.type) {
                            case "onlypass":
                                opts.maxDate = '0'; //只能选今天以前-仅过去时间
                                break;
                            case "onlyfuture":
                                opts.minDate = '0';//只能选今天以后-仅未来时间
                                break;
                        }
                    }

                    if (!scope.item.isReadOnly) {
                        $e.datetimepicker(opts);
                    }
                }

                scope.$watch('type', function () {
                    if (scope.type == 'edit') {
                        init();
                        element.find('input[type=text]').on('blur', function () {
                            scope.$apply(function () {
                                validata();
                            });
                        });
                    } else {

                    }
                })

            }
        };
    }]);

/***/ }),
/* 5 */
/***/ (function(module, exports, __webpack_require__) {

angular.module('mam-metadata')
    .directive('mam2MfcFrameToTimecode', function () {
        return {
            restrict: 'E',
            template: __webpack_require__(28),
            replace: true,
            scope: {
                item: '=',
                type: '@',
                entity: '<'
            },
            link: function (scope, element, attr, ctrl) {
                scope.model = scope.item.value;
                if (scope.model == null || scope.model == '') {
                    scope.model = 0;
                }
                scope.model = timecodeconvert.frame2Tc(scope.model, scope.entity.frameRate);
            }
        };
    });

/***/ }),
/* 6 */
/***/ (function(module, exports, __webpack_require__) {

angular.module('mam-metadata')
    .directive('mam2MfcNanosecondToTimecode', function () {
        return {
            restrict: 'E',
            template: __webpack_require__(29),
            replace: true,
            scope: {
                item: '=',
                type: '@',
                entity: '<'
            },
            link: function (scope, element, attr, ctrl) {
                scope.model = scope.item.value;

                if (scope.model == null || scope.model == '') {
                    scope.model = 0;
                }
                if (scope.entity.type == 'audio') {
                    scope.model = timecodeconvert.SecondToTimeString_audio(parseInt(scope.model) / 10000000);
                } else {
                    scope.model = timecodeconvert.frame2Tc(timecodeconvert.second2Frame((parseInt(scope.model) / 10000000), scope.frameRate), scope.frameRate);
                }
            }
        };
    });

/***/ }),
/* 7 */
/***/ (function(module, exports, __webpack_require__) {

angular.module('mam-metadata')
    .directive('mam2MfcNumber', ['mam2MetadataService', function (mam2MetadataService) {
        return {
            restrict: 'E',
            template: __webpack_require__(30),
            replace: true,
            scope: {
                item: '=',
                type: '@'
            },
            link: function (scope, element, attr, ctrl) {
                var $e = $(element);

                scope.$watch('type', function () {
                    if (scope.type != 'browse') {
                        $e.find('input').on('blur', function () {
                            mam2MetadataService.validate(scope.item);
                            scope.$applyAsync();
                        });
                    }
                });

            }
        };
    }]);

/***/ }),
/* 8 */
/***/ (function(module, exports, __webpack_require__) {

angular.module('mam-metadata')
    .directive('mam2MfcSelect', ['mam2MetadataService', function (mam2MetadataService) {
        return {
            restrict: 'E',
            template: __webpack_require__(31),
            replace: true,
            scope: {
                item: '=',
                type: '@',
                onChange: '&?'
            },
            link: function (scope, element, attr, ctrl) {
                var lastSelect = null; //上次选择的项，只有单选时才有效果
                scope.items = [];
                scope.model;


                function valueToModel() {
                    var keys = scope.item.value;
                    if (keys == null || keys == '') {
                        keys = '[]';
                    }
                    try {
                        keys = JSON.parse(keys);
                    } catch (e) {
                        keys = [keys];
                    }
                    var objs = _.map(keys, function (item) {
                        return { key: item, value: scope.items[item] }
                    });
                    if (scope.item.isMultiSelect) {
                        scope.model = objs;
                    } else {
                        scope.model = objs.length == 0 ? {} : objs[0];
                    }
                }

                function modelToValue() {
                    var val;
                    if (scope.item.isMultiSelect) {
                        val = _.map(scope.model, 'key');
                    } else {
                        val = [scope.model.key];
                    }
                    scope.item.value = JSON.stringify(val);
                }

                scope.onSelect = function (item, model) {
                    scope.model = model;
                    modelToValue();
                    mam2MetadataService.validate(scope.item);
                    if (!scope.item.isMultiSelect) {
                        if (lastSelect.key != scope.model.key) {
                            var old = lastSelect.key || '';
                            scope.onChange({ value: scope.model.key, oldValue: old, item: scope.item });
                            lastSelect = scope.model;
                        }
                    }
                }

                scope.onRemove = scope.onSelect;

                function init() {
                    if (_.isString(scope.item.controlData) && scope.item.controlData.length > 0) {
                        scope.items = JSON.parse(scope.item.controlData);
                    }
                    valueToModel();
                    if (!scope.item.isMultiSelect) {
                        lastSelect = scope.model;
                    }
                }

                init();
            }
        };
    }]);

/***/ }),
/* 9 */
/***/ (function(module, exports, __webpack_require__) {

angular.module('mam-metadata')
    .directive('mam2MfcSize', [function () {
        return {
            restrict: 'E',
            template: __webpack_require__(32),
            replace: true,
            scope: {
                item: '=',
                type: '@'
            },
            link: function (scope, element, attr, ctrl) {

            }
        };
    }]);

/***/ }),
/* 10 */
/***/ (function(module, exports, __webpack_require__) {

var selectorjs = __webpack_require__(50);
var selectorhtml = __webpack_require__(33);
angular.module('mam-metadata')
    .directive('mam2MfcTable', ['$timeout', 'mam2MetadataService', '$uibModal', '$interval', function($timeout, mam2MetadataService, $uibModal, $interval) {
        return {
            restrict: 'E',
            template: __webpack_require__(34),
            replace: true,
            scope: {
                item: '=',
                type: '@',
                onChange: '&?'
            },
            link: function(scope, element, attr, ctrl) {
                var tableItemWidth = 200;

                //设置表格宽度
                function setTableWidth() {
                    if (scope.type == 'browse') {
                        element.find(".mam-metadata-table").width(_.filter(scope.configDataJson, function(item) {
                            if (item.isShow === undefined || item.isShow === true) {
                                return true;
                            }
                            return false;
                        }).length * tableItemWidth);
                    } else if (scope.type == 'edit') {
                        element.find(".mam-metadata-table").width(scope.field.length * tableItemWidth);
                    }
                }

                function init() {
                    if (!scope.type)
                        scope.type = 'browse';
                    scope.selectIndex = -1;
                    scope.configData = JSON.parse(scope.item.controlData);
                    scope.configDataJson = angular.copy(scope.configData);

                    try {
                        scope.model = angular.fromJson(scope.item.value);
                        if (!scope.model)
                            scope.model = [];
                    } catch (e) {
                        scope.model = [];
                    }
                    getConfig();

                    if (scope.configDataJson) {
                        $timeout(function() {
                            setExtraRows();
                        });
                    }

                    scope.edit();
                    setTableWidth();
                }

                //浏览模式下的最后空白行
                function setExtraRows() {
                    scope.extraRows = [];
                    if (scope.model.length < 3) {
                        for (var i = scope.model.length + 1; i <= 3; i++) {
                            scope.extraRows.push(i);
                        }
                    } else {
                        scope.extraRows.push(1);
                    }
                }

                //编辑模式下的最后空白行
                function setEditExtraRows() {
                    scope.editExtraRows = [];
                    if (scope.fieldData.length < 3) {
                        for (var i = scope.fieldData.length + 1; i <= 3; i++) {
                            scope.editExtraRows.push(i);
                        }
                    } else {
                        scope.editExtraRows.push(1);
                    }
                }

                //修改控件保存值
                function setNewValue() {
                    if (scope.model.length > 0) {
                        var model = angular.copy(scope.model);
                        _.forEach(model, function(item) {
                            if (item.hasOwnProperty("$$hashKey"));
                            delete item.$$hashKey;
                        });
                        scope.item.value = JSON.stringify(model);
                    } else
                        scope.item.value = "[]";
                }

                //将选中项设置到弹出框并弹出(已废弃的方法，仅供参考)
                function open(selectIndex) {
                    $uibModal.open({
                        template: selectorhtml,
                        controller: "mamMetadataTableSelectorCtrl",
                        windowClass: "mam-metadata-table-selector",
                        resolve: { params: function() { return { field: angular.copy(scope.configData), data: angular.copy(scope.model) } } }
                    }).result.then(function(e) {
                        if (e) {
                            scope.model = e;
                            setExtraRows();
                            setNewValue();
                        }
                        setTableWidth();
                    });
                }

                //编辑模式下，自动增加新行
                scope.addBlankRow = function() {
                    var row = [];
                    scope.lastIndex++;
                    _.forEach(scope.field, function(f) {
                        var cf = angular.copy(f);
                        cf.value = "";
                        cf.index = scope.lastIndex;
                        Object.defineProperty(cf, "value", {
                            get: function() {
                                return this._value;
                            },
                            set: function(newValue) {
                                this._value = newValue;
                                if (newValue && cf.index === scope.lastIndex) {
                                    scope.addBlankRow();
                                }
                            }
                        });
                        row.push(cf);
                    });
                    scope.fieldData.push(row);
                    setEditExtraRows();
                };

                //切换到编辑模式
                scope.edit = function() {
                    var params = {
                        field: scope.configData,
                        data: scope.model
                    }
                    scope.field = params.field;
                    scope.fieldData = _.map(params.data, function(row) {
                        return _.map(params.field, function(f) {
                            var ret = angular.copy(f);
                            ret.value = row[f.fieldName];
                            return ret;
                        });
                    });
                    scope.lastIndex = scope.fieldData.length - 1;
                    scope.addBlankRow();
                }

                //空行判断
                var isEmptyRow = function(fd) {
                    var isEmpty = true;
                    _.forEach(fd, function(f) {
                        if (f.value) {
                            isEmpty = false;
                        }
                    });
                    return isEmpty;
                };

                //设置新的model值
                var setNewModel = function() {
                    var datas = [];
                    _.forEach(scope.fieldData, function(fd) {
                        if (!isEmptyRow(fd)) {
                            var ret = {};
                            _.forEach(fd, function(f) {
                                ret[f.fieldName] = f.value;
                            });
                            datas.push(ret);
                        }
                    });
                    scope.model = datas;
                }

                //删除项
                scope.reduce = function(item, index) {
                    mam.confirm("确定删除此项吗？").then(function(res) {
                        $timeout(function() {
                            scope.selectIndex = index;
                            if (scope.selectIndex < 0) return;
                            scope.fieldData.splice(scope.selectIndex, 1);
                            scope.selectIndex = -1;
                            setNewModel();
                            setNewValue();
                            setEditExtraRows();
                        })
                    }, function(res) {})
                };

                //拖拽句柄
                scope.sortableOptions = {
                    update: function(e, ui) {

                    },
                    stop: function(e, ui) {
                        setNewValue();
                    }
                };

                //分类字段获取地址
                function getPath(code, datalist) {
                    var path = "";
                    _.forEach(datalist, function(item) {
                        var iscontinue = false;
                        if (code.indexOf(item.categoryCode) > -1) {
                            path += item.categoryName + "/";
                            if (item.categoryCode !== code) {
                                path += getPath(code, item.children);
                            } else {
                                iscontinue = true;
                            }
                        };
                        if (iscontinue) {
                            return path;
                        }
                    });
                    return path;
                }

                //下拉框获取名称
                scope.getName = function(value, obj) {
                    if (value != null && value.indexOf("[") > -1) {
                        value = JSON.parse(value);
                        var res = "";
                        _.forEach(value, function(item) {
                            res = res += obj[item];
                        });
                        return res;
                    }
                }

                //分类获取路径
                scope.getPath = function(code, datas) {
                    if (code == null || code == "" || code == "[]") {
                        return;
                    }
                    var valuelist = code.split(",");
                    var showValue = [];
                    _.forEach(valuelist, function(item, i) {
                        var path = getPath(item, datas);
                        if (path !== "")
                            path = path.substring(0, path.length - 1);
                        showValue[i] = path;
                    });
                    var newValue = [];
                    _.forEach(showValue, function(item, i) {
                        if (item !== "") {
                            newValue[newValue.length] = item;
                        }
                    });
                    return newValue.join();
                }

                scope.getBool = function(value) {
                    var res = (value == true || value == 'True' || value == 'true') ? '是' : '否';
                    return res;
                }

                //切换类型时重新初始化
                scope.$watch('type', function(newValue, oldValue) {
                    if (newValue) {
                        init();
                    }
                })

                //保存值
                $interval(function() {
                    setNewModel();
                    setNewValue();
                }, 1000);

                //页面关闭时清楚定时器
                window.onunload = function(event) {
                    $interval.cancel(interval);
                };

                function getConfig() {
                    _.forEach(scope.configDataJson, function(item) {
                        if (item.data != null && typeof(item.data) == 'string' && item.data != "") {
                            item.data = JSON.parse(item.data);
                        }
                        if (item.controlData != null && typeof(item.controlData) == 'string' && item.controlData != "") {
                            item.controlData = JSON.parse(item.controlData);
                        }
                    });
                }

                init();
            }
        };
    }]);

/***/ }),
/* 11 */
/***/ (function(module, exports, __webpack_require__) {

angular.module('mam-metadata')
    .directive('mam2MfcTag', ['mam2MetadataService', function (mam2MetadataService) {
        return {
            restrict: 'E',
            template: __webpack_require__(35),
            replace: true,
            scope: {
                item: '=',
                type: '@'
            },
            link: function (scope, element, attr, ctrl) {
                scope.tags = [];
                scope.options = {};


                function valueToTags() {
                    if (scope.item.value != null && scope.item.value.length > 0) {
                        scope.tags = _.map(scope.item.value.split(','), function (o) { return { text: o } });
                    } else {
                        scope.tags = [];
                    }
                }

                function tagsToValue() {
                    scope.item.value = _.map(scope.tags, 'text').join(',');
                }

                function validateTag(tag) {
                    if (scope.options.tagMinLen != null && scope.options.tagMinLen > 0 && tag.text.length < scope.options.tagMinLen) {
                        scope.item.error = 'custom:单个标签的最小长度为' + scope.options.tagMinLen;
                        return false;
                    }
                    if (scope.options.tagMaxLen != null && scope.options.tagMaxLen > 0 && tag.text.length > scope.options.tagMaxLen) {
                        scope.item.error = 'custom:单个标签的最大长度为' + scope.options.tagMaxLen;
                        return false;
                    }
                    if (_.find(scope.tags, { text: tag.text }) != null) {
                        scope.item.error = 'custom:已存在该标签';
                        return false;
                    }
                    return true;
                }

                scope.adding = function (tag) {
                    if (!validateTag(tag)) {
                        return false;
                    }
                }
                scope.added = function (tag) {
                    tagsToValue();
                    mam2MetadataService.validate(scope.item);
                }

                scope.remove = function (tag) {
                    tagsToValue();
                    mam2MetadataService.validate(scope.item);
                }

                scope.invalid = function (tag) {
                    validateTag(tag);
                }

                function init() {
                    valueToTags();

                    if (_.isString(scope.item.controlData) && scope.item.controlData.length > 0) {
                        scope.options = JSON.parse(scope.item.controlData);
                    }
                    
                    var $e = $(element);
                    $(document).ready(function () {
                        if (scope.type != 'browse') {
                            $e.find('input[type=text]').on('blur', function () {
                                mam2MetadataService.validate(scope.item);
                                scope.$applyAsync();
                            });
                        }
                    })
                }

                scope.$watch('item.value', function (newValue) {
                    if (newValue != undefined) {
                        valueToTags();
                    }
                });

                init();
            }
        };
    }]);

/***/ }),
/* 12 */
/***/ (function(module, exports, __webpack_require__) {

angular.module('mam-metadata')
    .directive('mam2MfcText', ['mam2MetadataService', function (mam2MetadataService) {
        return {
            restrict: 'E',
            template: __webpack_require__(36),
            replace: true,
            scope: {
                item: '=',
                type: '@'
            },
            link: function (scope, element, attr, ctrl) {
                var $e = $(element);

                scope.$watch('type', function () {
                    if (scope.type != 'browse') {
                        $e.find('input[type=text]').on('blur', function () {
                            mam2MetadataService.validate(scope.item);
                            scope.$applyAsync();
                        });
                    }
                })

            }
        };
    }]);

/***/ }),
/* 13 */
/***/ (function(module, exports, __webpack_require__) {

angular.module('mam-metadata')
    .directive('mam2MfcTextarea', ['mam2MetadataService', '$sce', function (mam2MetadataService, $sce) {
        return {
            restrict: 'E',
            template: __webpack_require__(37),
            replace: true,
            scope: {
                item: '=',
                type: '@'
            },
            link: function (scope, element, attr, ctrl) {
                var $e = $(element);

                scope.trustAsHtml = function (text) {
                    if (_.isString(text)) {
                        text = text.replace(new RegExp('\r\n', 'g'), '<br>');
                        text = text.replace(new RegExp('\n', 'g'), '<br>');
                        text = text.replace(new RegExp('\r', 'g'), '<br>');
                        return $sce.trustAsHtml(text);
                    }
                    return text;
                }

                scope.$watch('type', function () {
                    if (scope.type != 'browse') {
                        $e.find('textarea').on('blur', function () {
                            mam2MetadataService.validate(scope.item);
                            scope.$applyAsync();
                        });
                    } else {

                    }
                });
            }
        };
    }]);

/***/ }),
/* 14 */
/***/ (function(module, exports, __webpack_require__) {

angular.module('mam-metadata')
    .directive('mam2MfcTimearea', ['$timeout', 'mam2MetadataService', function ($timeout, mam2MetadataService) {
        return {
            restrict: 'E',
            template: __webpack_require__(38),
            replace: true,
            scope: {
                item: '=',
                type: '@',
            },
            link: function (scope, element, attr, ctrl) {
                var $e = $(element);
                scope.model = {
                    startModel: "",
                    endModel: ""
                };
                if (scope.item.value != null && scope.item.value.length > 0) {
                    var arr = scope.item.value.split(",");
                    scope.model.startModel = arr[0];
                    scope.model.endModel = arr[1];
                }

                function validata() {
                    mam2MetadataService.validate(scope.item);
                }

                function init() {
                    var ctrlData;
                    try {
                        ctrlData = JSON.parse(scope.item.controlData);
                    } catch (error) {
                        ctrlData = null;
                    }

                    var startopts = {
                        showSecond: true,
                        format: 'Y-m-d',
                        timepicker: false,
                        onClose: function () {
                            $timeout(function () {
                                setValue();
                            });
                        }
                    };

                    var endopts = angular.copy(startopts);

                    if (ctrlData != null) {
                        switch (ctrlData.type) {
                            case "onlypass":
                                startopts.maxDate = '0';
                                endopts.maxDate = '0'; //只能选今天以前-仅过去时间
                                break;
                            case "onlyfuture":
                                startopts.minDate = '0';//只能选今天以后-仅未来时间
                                endopts.minDate = '0';
                                break;
                        }
                    }

                    if (!scope.item.isReadOnly) {
                        $e.find(".start-time>input").datetimepicker(startopts);
                        $e.find(".end-time>input").datetimepicker(endopts);
                    }
                }

                function setValue() {
                    scope.item.value = scope.model.startModel + "," + scope.model.endModel;
                    validata();
                }

                scope.getErrorInfo = function (item, errorCode) {
                    return mam2MetadataService.getErrorInfo(item, errorCode);
                };

                scope.$watch('type', function () {
                    if (scope.type == 'edit') {
                        init();
                        element.find('input[type=text]').each(function (index) {
                            $(this).on('blur', function () {
                                scope.$apply(function () {
                                    validata();
                                });
                            });
                        });
                    } else {

                    }
                });
            }
        };
    }]);

/***/ }),
/* 15 */
/***/ (function(module, exports, __webpack_require__) {

var selectorjs = __webpack_require__(51);
var selectorhtml = __webpack_require__(39);

angular.module('mam-metadata')
    .directive('mam2MfcTree', ['mam2MetadataService', '$uibModal', function (mam2MetadataService, $uibModal) {
        return {
            restrict: 'E',
            template: __webpack_require__(40),
            replace: true,
            scope: {
                item: '=',
                type: '@'
            },
            link: function (scope, element, attr, ctrl) {
                var $e = $(element);
                var tree = [];
                scope.model = [];


                function findNode(code, nodes) {
                    for (var i = 0; i < nodes.length; i++) {
                        nodes[i].path = nodes[i].categoryName;
                        if (nodes[i].categoryCode == code) {
                            return nodes[i];
                        }
                        if (_.isArray(nodes[i].children) && nodes[i].children.length > 0) {
                            var res = findNode(code, nodes[i].children);
                            if (res != null) {
                                res.path = nodes[i].categoryName + '/' + res.path;
                                return res;
                            }
                        }
                    }
                }

                function valueToModel() {
                    if (scope.item.value != null && scope.item.value.length > 0) {
                        if (tree.length > 0) {
                            scope.model = [];
                            _.forEach(scope.item.value.split(','), function (code) {
                                var node = findNode(code, tree);
                                if (node != null) {
                                    scope.model.push(angular.copy(node));
                                }
                            });
                        } else {
                            scope.model = [];
                        }
                    } else {
                        scope.model = [];
                    }
                }

                function getTree() {
                    var data = scope.item.controlData;
                    if (data == null || data.length == 0) {
                        tree = [];
                    } else {
                        try {
                            tree = JSON.parse(data);
                        } catch (e) {
                            tree = [];
                        }
                    }
                }

                scope.getErrorInfo = function (item, errorCode) {
                    return mam2MetadataService.getErrorInfo(item, errorCode);
                };


                scope.open = function () {
                    $uibModal.open({
                        template: selectorhtml,
                        controller: "mamMetadataTreeSelectorCtrl",
                        windowClass: "mam-metadata-tree-selector",
                        resolve: { params: function () { return { field: scope.item } } }
                    }).result.then(function (e) {
                        if (_.isString(e)) {
                            scope.item.value = e;
                            valueToModel();
                        }
                    });
                }

                function init() {
                    getTree();
                    valueToModel();
                }

                init();
            }
        };
    }]);

/***/ }),
/* 16 */
/***/ (function(module, exports) {

// angular.module('mam-metadate')
//     .filter('mam2MfcClass', function () {
//         return function(){
            
//         }
//     });

/***/ }),
/* 17 */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less__ = __webpack_require__(46);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__style_less__);


angular.module('mam-metadata')
    .directive('mam2MetadataForm', ['mam2MetadataService', function (mam2MetadataService) {
        return {
            restrict: 'E',
            template: __webpack_require__(41),
            replace: true,
            transclude: {
                'mmf-right': '?mmfRight'
            },
            scope: {
                items: '=',         //绑定的数据，这个数据应该是统一的格式
                type: '@',          //类型，browse:浏览，edit:编辑，optional-edit:选择性编辑
                entity: '<?',       //关联的素材
                twoway: '<?',       //对items启用双向绑定，如果不传，就是单向
                getFunc: '&',       //获取表单的相关方法
                className: '@?',     //默认class为mam-metadata-form，如果不是用默认class，那么就要自己写该组件的所有样式
                onProgramformChange: '&?' //节目类型改变时
            },
            compile: function (element, attr) {
                //动态添加必须的数据（当然也是可以直接在界面上写的）
                var controls = element.find('.mmf-content').children();
                _.forEach(controls, function (item) {
                    var tagName = item.tagName.toLowerCase();
                    if (tagName.indexOf('mam2-mfc-') == 0) {
                        var $e = $(item);
                        $e.attr('item', 'item');
                        $e.attr('type', '{{type}}');
                        $e.attr('class', tagName + ' mmf-status-{{type}}');
                    }
                });

                return function (scope, element, attr, ctrl) {
                    scope.className = scope.className || 'mam-metadata-form';

                    var oldItems;
                    scope.models;

                    scope.onSelectChange = function (value, oldValue, item) {
                        if (item.fieldName == 'programform') {
                            scope.onProgramformChange({ value: value, oldValue: oldValue });
                        }
                    }

                    function validateForm() {
                        var items = scope.models;
                        if (scope.type == 'optional-edit') {
                            items = _.filter(items, { selected: true });
                        }
                        //过滤不显示的字段
                        items = _.filter(items, function(item){
                            return item.isShow === undefined || item.isShow === true;
                        });

                        var result = mam2MetadataService.validate(items);
                        result.newItems = items;
                        result.oldItems = oldItems;
                        return result;
                    }

                    function clearErros() {
                        _.forEach(scope.models, function (item) {
                            item.error = null;
                        })
                    }
                    function reset() {
                        scope.models = angular.copy(oldItems);
                    }

                    function execFunc(name) {
                        if (name == 'clearErros') {
                            return clearErros();
                        }
                        if (name == 'reset') {
                            return reset();
                        }
                        return validateForm();
                    }


                    function init() {
                        oldItems = angular.copy(scope.items);

                        if (scope.twoway) {
                            scope.models = scope.items;
                        } else {
                            scope.models = angular.copy(scope.items);
                        }

                        if (_.isFunction(scope.getFunc)) {
                            scope.getFunc({ func: execFunc })
                        }
                        if (scope.entity == null) {
                            scope.entity = {};
                        }
                        if (scope.entity.type == null) {
                            scope.entity.type = 'video';
                        }
                        if (scope.entity.frameRate == null) {
                            scope.entity.frameRate = 25;
                        }
                    }

                    scope.changeSelect = function (item) {
                        if (!item.selected) {
                            item.error = null;
                        }
                    }

                    scope.getErrorInfo = function (item, errorCode) {
                        return mam2MetadataService.getErrorInfo(item, errorCode);
                    };

                    scope.$watch('items', function () {
                        init();
                    });

                    scope.getCtrlByType = function (type) {
                        var dict = {
                            '1': 'datetime',
                            '2': 'date',
                            '3': '',
                            '4': 'number',
                            '5': 'text',
                            '6': 'textarea',
                            '7': 'bool',
                            '8': 'select',
                            '9': 'frame-to-timecode',
                            '10': 'size',
                            '11': 'nanosecond-to-timecode',
                            '12': 'tag',
                            '13': '',
                            '14': 'tree',
                            '15': 'table',
                            '16': 'timearea',
                        }
                        return (!type || !dict[type]) ? '' : dict[type];
                    }
                }
            }
        };
    }]);

/***/ }),
/* 18 */
/***/ (function(module, exports, __webpack_require__) {

var fieldSelectorjs = __webpack_require__(53);
var fieldSelectorhtml = __webpack_require__(42);

angular.module('mam-metadata').service('mam2MetadataService',
    ["$rootScope", "$http", "$q", "$uibModal", "mamValidationService", function ($rootScope, $http, $q, $uibModal, mamValidationService) {
        var self = this;
        this.openFieldSelector = function (selectedItem, qTreeUrl) {
            var defer = $q.defer();
            $uibModal.open({
                template: fieldSelectorhtml,
                controller: "mamFieldSelectorController",
                windowClass: "mam-field-selector",
                backdrop: "static",
                resolve: {
                    selectedItem: function () { return selectedItem || []; },
                    qTreeUrl: function () { return qTreeUrl; }
                }
            }).result.then(function (e) {
                defer.resolve(e);
            });
            return defer.promise;
        };

        var validateDate = function (item, ctrlData, value) {
            if (item.isMustInput) {
                if (value == null || value === "") {
                    return 'must';
                }
            }
            if (!mamValidationService.dateValidate(value)) {
                return 'date';
            }
            if (ctrlData.type != null && ctrlData.type != "no") {
                var nowDate = (new Date()).format("yyyy-MM-dd");
                var inputDate = new Date(value).format("yyyy-MM-dd");
                if (ctrlData.type == "onlypass" && Date.parse(inputDate) > Date.parse(nowDate))
                    return 'onlypass';
                if (ctrlData.type == "onlyfuture" && Date.parse(inputDate) < Date.parse(nowDate))
                    return 'onlyfuture';
            }
            return;
        };

        var validator = {
            //日期时间
            '1': function (item) {
                var ctrlData = { type: "no" };

                if (item.controlData != undefined) {
                    if (_.isString(item.controlData)) {
                        ctrlData = JSON.parse(item.controlData);
                    } else {
                        ctrlData = item.controlData;//如果是对象则不用转换了
                    }
                }
                return validateDate(item, ctrlData, item.value);
            },
            //日期
            '2': function (item) {
                var ctrlData = { type: "no" };

                if (item.controlData != undefined) {
                    if (_.isString(item.controlData)) {
                        ctrlData = JSON.parse(item.controlData);
                    } else {
                        ctrlData = item.controlData;//如果是对象则不用转换了
                    }
                }
                return validateDate(item, ctrlData, item.value);
            },

            //数字
            '4': function (item) {
                if (!item.isMustInput && (item.value == null || item.value == '')) {
                    return;
                }
                if (item.isMustInput && (item.value == null || item.value == '')) {
                    return 'must';
                }
                if (isNaN(item.value)) {
                    return 'nubmer';
                }
                var num = parseFloat(item.value);

                if (item.minLength !== 0 && num < item.minLength) {
                    return 'nubmerMin';
                }
                if (item.maxLength !== 0 && num > item.maxLength) {
                    return 'nubmerMax';
                }
            },
            //单行文本
            '5': function (item) {
                if (!item.isMustInput && item.value == null) {
                    return;
                }
                var length = !item.value ? 0 : item.value.length;
                if (item.isMustInput && length === 0) {
                    return 'must';
                }
                if (item.minLength !== 0 && length < item.minLength) {
                    return 'lengthMin';
                }
                if (item.maxLength !== 0 && length > item.maxLength) {
                    return 'lengthMax';
                }
            },
            // 多行
            '6': function (item) {
                if (!item.isMustInput && item.value == null) {
                    return;
                }
                var length = !item.value ? 0 : item.value.length;
                if (item.isMustInput && length === 0) {
                    return 'must';
                }
                if (item.minLength !== 0 && length < item.minLength) {
                    return 'lengthMin'
                }
                if (item.maxLength !== 0 && length > item.maxLength) {
                    return 'lengthMax';
                }
                return;
            },

            //下拉框
            '8': function (item) {
                if (!item.isMustInput) {
                    return;
                }
                var keys = item.value;
                if (keys == null || keys == '') {
                    keys = '[]';
                }
                try {
                    keys = angular.fromJson(keys);
                } catch (e) {
                    keys = [keys];
                }
                var selectData = {};
                try {
                    selectData = angular.fromJson(item.controlData);
                } catch (e) {
                    console.error(e);
                }
                _.remove(keys, function (o) { return selectData[o] == undefined });
                if (keys.length == 0) {
                    return 'must';
                }
            },

            // 标签
            '12': function (item) {
                if (!item.isMustInput && item.value == null) {
                    return;
                }
                if (item.isMustInput && (item.value == null || item.value.length == 0)) {
                    return 'must';
                }
                var length = item.value.replace(/,/g, '').length;
                if (item.minLength !== 0 && length < item.minLength) {
                    return 'lengthMin'
                }
                if (item.maxLength !== 0 && length > item.maxLength) {
                    return 'lengthMax';
                }
            },

            //分类
            '14': function (item) {
                if (item.isMustInput && (item.value == null || item.value === "" || item.value === "[]" || item.value.length === 0)) {
                    return 'must';
                }
            },

            //复杂类型
            '15': function (item) {
                if (item.isMustInput && (item.value == null || item.value === "" || item.value === "[]" || item.value.length === 0)) {
                    return 'must';
                }
            },

            //日期范围
            '16': function (item) {
                if (!item.isMustInput && !item.value) {
                    return;
                }
                if (!item.value) {
                    return "mustStartAndEndTime";
                }
                var timeArr = item.value.split(",");
                var startTime = timeArr[0];
                var endTime = timeArr[1];

                var ctrlData = { type: "no" };

                if (item.controlData != undefined) {
                    if (_.isString(item.controlData)) {
                        ctrlData = JSON.parse(item.controlData);
                    } else {
                        ctrlData = item.controlData;//如果是对象则不用转换了
                    }
                }

                var errors = [];
                var startError = validateDate(item, ctrlData, startTime);
                var endError = validateDate(item, ctrlData, endTime);
                if (!startError && !endError && new Date(startTime) - new Date(endTime) > 0) {
                    errors.push("startTimeBiggerThanEnd");
                    errors.push("startTimeBiggerThanEnd");
                }
                else {
                    errors.push(startError);
                    errors.push(endError);
                }

                return !errors[0] && !errors[1] ? null : errors;
            }
        }

        this.validate = function (items) {
            var res = {
                success: true,
                errors: []
            };
            if (!_.isArray(items)) {
                items = [items];
            }

            _.forEach(items, function (item) {
                var func = validator[item.controlType];
                if (func != null) {
                    var error = func(item);
                    if (item.isShow === false || error == null) {//隐藏的项默认不验证
                        item.error = null;
                        item.errors = null;
                    } else {
                        if (_.isArray(error)) {
                            item.errors = error;
                        }
                        else {
                            item.error = error;
                        }
                        res.errors.push(item);
                    }
                }
            });
            res.success = res.errors.length == 0;
            return res;
        };

        this.getErrorInfo = function (item, errorCode) {
            var code = errorCode || item.error;
            if (code == 'must') {
                return '该字段为必填！';
            }
            if (code == 'mustStartAndEndTime') {
                return '请填写开始日期和结束日期';
            }
            if (code == 'format') {
                return '格式错误'
            }
            if (code == 'lengthMin') {
                return '长度不能小于' + item.minLength
            }
            if (code == 'lengthMax') {
                return '长度不能大于' + item.maxLength;
            }
            if (code == 'nubmer') {
                return '必须为数字';
            }
            if (code == 'nubmerMin') {
                return '不能小于' + item.minLength;
            }
            if (code == 'nubmerMax') {
                return '不能大于' + item.maxLength;
            }
            if (code == 'onlypass') {
                return '日期不能大于今天';
            }
            if (code == 'onlyfuture') {
                return '日期不能小于今天';
            }
            if (code == 'date') {
                return '日期格式不正确';
            }
            if (code == 'startTimeBiggerThanEnd') {
                return '开始时间不能大于结束时间';
            }
            if (code.indexOf('custom') == 0) {
                return code.split(':')[1];
            }
            return '未知错误' + code;
        };
    }]
);

/***/ }),
/* 19 */
/***/ (function(module, exports, __webpack_require__) {

__webpack_require__(48);

angular.module('mam-metadata')
    .directive('mamMetadataSelector', ["$rootScope","$http", "$q", function ($rootScope, $http, $q) {
        return {
            restrict: 'EA',
            template: __webpack_require__(43),
            replace: true,
            scope: {
                qTreeUrl : "=",
                selectedItem : "="
            },
            link: function (scope, element, attr, ctrl) {
                scope.qTreeUrl = scope.qTreeUrl || "~/business/metadata/resource/fields?typeName={0}";

                var dataType = { //元数据控件类型对应数字
                    'date': [16],
                    'long': [4, 9, 11, 10],
                    'string': [5, 6, 12, 14, 17, 18],
                    'boolean': [7],
                    'enum': [8],
                    'object': [15]
                };
                scope.config = {
                    checkParent : true,
                    typeName : "model_sobey_object_entity",
                    dataType : dataType
                };

                var init = function(){
                    //获取所有一级数据
                    _.forEach(scope.selectedItem, function(item) {
                        if (!item.hasOwnProperty("fieldPath")) {
                            item.fieldPath = "";
                            item.showNamePath = "";
                        }
                    });
                    getFolderTree({ "refResourceTypeName": scope.config.typeName });
                };

                function delSelectChildren(item) {
                    _.forEach(item.children, function(nav, i) {
                        var index = _.findIndex(scope.selectedItem, { fieldName: nav.fieldName, dataType: nav.dataType, fieldPath: nav.fieldPath });
                        if (index > -1) {
                            nav.selected = false;
                            scope.selectedItem.splice(index, 1);
                        }
                    });
                    item.expand = false;
                }

                function getControlType(item){
                    var type = scope.config.dataType[item.dataType][0];
                    if (item.fieldName === "createUser_" || item.fieldName === "creator"
                        || item.fieldName === "modifier" || item.fieldName === "member"
                        || item.fieldName === "deletor" || item.fieldName === "journallist")//用户
                    {
                        type = scope.config.dataType[item.dataType][4];
                    }
                    else if (item.fieldName === "department"
                        || item.fieldName === "program_channel_department")//部门
                    {
                        type = scope.config.dataType[item.dataType][5];
                    }
                    return type;
                }

                function setObjectControlData(children) {
                    var array = [];
                    _.forEach(children, function(item, i) {
                        var obj = {
                            controlType: getControlType(item),
                            isReadOnly: false,
                            "hiveMaxLength": item.maxLen,
                            "hiveMinLength": item.minLen,
                            "hiveMustInput": item.mustInput === 1 ? true : false,
                            order: i,
                            metadataType: scope.config.type,
                            alias: item.alias,
                            showName: item.alias,
                            fixItemId: item.fixItemId,
                            dataType: item.dataType,
                            isEnable: true,
                            fieldName: item.fieldName
                        };
                        array.push(obj);
                    });
                    // obj.controlData = JSON.stringify(array);
                    return JSON.stringify(array);
                }

                //配置字段选中
                scope.selectItem = function(item) {
                    if (!item.selected) {
                        var index = -1;
                        if (item.fieldPath !== item.fieldName)
                            index = _.findIndex(scope.selectedItem, { fieldName: item.fieldName, dataType: item.dataType, fieldPath: item.fieldPath });
                        else {
                            _.forEach(scope.selectedItem, function(obj, i) {
                                if (obj.fieldName === item.fieldName && obj.dataType === item.dataType && obj.fieldPath === item.fieldPath) {
                                    index = i;
                                    return false;
                                }
                            });
                        }
                        scope.selectedItem.splice(index, 1);
                    } else {
                        var obj = {
                            controlType: getControlType(item),
                            fieldPath: item.fieldPath ? item.fieldPath : '',
                            showNamePath : item.showNamePath ? item.showNamePath : '',
                            isReadOnly: false,
                            "hiveMaxLength": item.maxLen,
                            "hiveMinLength": item.minLen,
                            "hiveMustInput": item.mustInput === 1 ? true : false,
                            isArray: item.isArray === 1 ? true : false,
                            order: scope.selectedItem.length,
                            metadataType: scope.config.type,
                            alias: item.alias,
                            showName: item.alias,
                            fixItemId: item.fixItemId,
                            dataType: item.dataType,
                            fieldName: item.fieldName
                        }
                        if (scope.config.typeName === "model_sobey_object_entity" && scope.config.type !== "tag")
                            obj.isUploadNeed = false;
                        if (item.dataType === 'object') {
                            obj.refResourceField = item.refResourceTypeName;
                            obj.isMultiSelect = item.isArray === 1 ? true : false;
                            if (item.hasOwnProperty("children") && item.children.length > 0) {
                                delSelectChildren(item);
                                obj.controlData = setObjectControlData(item.children);
                                scope.selectedItem.push(obj);
                            } else {
                                ajaxDelSelectChildren(item, obj);
                            }
                        } else {
                            scope.selectedItem.push(obj);
                        }

                    }

                }

                function ajaxDelSelectChildren(parent, obj) {
                    var url = scope.qTreeUrl.replace(/\{0\}/g, parent.refResourceTypeName);

                    $http.get(url).then(function(res) {
                        var data = res.data;
                        _.forEach(data, function(item, i) {
                            if (parent.fieldPath)
                                item.fieldPath = parent.fieldPath + "," + item.fieldName;
                            else
                                item.fieldPath = parent.fieldName + "," + item.fieldName;
                            if (parent.showNamePath)
                            {
                                item.showNamePath = parent.showNamePath + "," + (item.alias || item.showName);
                            }
                            else
                            {
                                item.showNamePath = (parent.alias || parent.showName) + "," + (item.alias || item.showName);
                            }
                            var index = _.findIndex(scope.selectedItem, { fieldName: item.fieldName, fieldPath: item.fieldPath });
                            if (index > -1) {
                                scope.selectedItem.splice(index, 1);
                            }
                        });

                        obj.controlData = setObjectControlData(data);
                        scope.selectedItem.push(obj);
                    });
                }

                function setDefalutPath(array) {
                    _.forEach(array, function(item) {
                        if (!item.hasOwnProperty("fieldPath"))
                            item.fieldPath = "";
                        if (!item.hasOwnProperty("showNamePath"))
                            item.showNamePath = "";
                    });
                    return array;
                }
                function judeSameDate(getArray, selectArray) {
                    selectArray = _.filter(selectArray, { fieldPath: "" });
                    _.forEach(selectArray, function(item) {
                        var index = _.findIndex(getArray, { fieldName: item.fieldName });
                        if (index > -1) {
                            getArray[index].selected = true;
                        }
                    });
                    return getArray;
                }

                scope.getChildren = function(item) {
                    if (item.expand) {
                        item.expand = false;
                    } else {
                        if (!item.hasOwnProperty('children'))
                            getFolderTree(item);
                        else
                            item.expand = true;
                    }

                };

                //获取节点数据
                var getFolderTree = function(parent) {
                    var url = "";

                    url = scope.qTreeUrl.replace(/\{0\}/g, parent.refResourceTypeName);
                    $http.get(url).then(function(res) {
                        var data = res.data;
                        if (!parent.hasOwnProperty("dataType")) {
                            data = setDefalutPath(res.data);
                            data = judeSameDate(data, scope.selectedItem);
                            scope.folders = data;
                        } else {
                            _.forEach(data, function(item) {
                                if (parent.fieldPath)
                                    item.fieldPath = parent.fieldPath + "," + item.fieldName;
                                else
                                    item.fieldPath = parent.fieldName + "," + item.fieldName;
                                if (parent.showNamePath)
                                {
                                    item.showNamePath = parent.showNamePath + "," + (item.alias || item.showName);
                                }
                                else
                                {
                                    item.showNamePath = (parent.alias || parent.showName) + "," + (item.alias || item.showName);
                                }

                                var index = _.findIndex(scope.selectedItem, { fieldName: item.fieldName, fieldPath: item.fieldPath });
                                if (index > -1) {
                                    item.selected = true;
                                }
                            });
                            //获取当前节点下已保存的节点
                            //  data = judeSameDate(res.data, scope.selectedItem);
                            parent.children = data;
                            parent.expand = true;
                        }
                    });
                };

                var checkItemByKeyword = function(item){
                    if (!scope.keyword
                        || (item.alias && item.alias.indexOf(scope.keyword) > -1)
                        || (item.showName && item.showName.indexOf(scope.keyword) > -1))
                    {
                        return true;
                    }
                    return false;
                };
                var showItemsByKeyword = function(parent){
                    var show = false;//当前层级是否全部都不显示
                    _.forEach(parent, function(item){
                        show = false;
                        if (item.children)
                        {
                            show = showItemsByKeyword(item.children);
                        }
                        if (show || checkItemByKeyword(item))//如果有子节点匹配，则不能去掉
                        {
                            item.show = true;
                            show = true;
                        }
                        else
                        {
                            item.show = false;
                        }
                    });
                    return show;
                };
                scope.onKeywordChanged = function(){
                    showItemsByKeyword(scope.folders);
                };

                init();
            }
        };
    }]);

/***/ }),
/* 20 */
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(0)(false);
// imports


// module
exports.push([module.i, ".mam-metadata-table-selector {\n  z-index: 999;\n}\n.mam-metadata-table-selector .modal-dialog {\n  width: 800px;\n}\n.mam-metadata-table-selector .modal-body {\n  padding-bottom: 3px;\n  height: 500px;\n}\n.mam-metadata-table-selector .mam-metadata-form .mam-mfc-table .mam-metadata-tr .mam-metadata-td {\n  width: 200px;\n}\n.mam-metadata-table-selector .mam-metadata-form .mam-mfc-table .mam-metadata-tr .mam-metadata-td div {\n  width: 100%;\n}\n", ""]);

// exports


/***/ }),
/* 21 */
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(0)(false);
// imports


// module
exports.push([module.i, ".mam-metadata-tree-selector .modal-body {\n  padding-bottom: 3px;\n  height: 560px;\n  overflow-y: auto;\n}\n.mam-metadata-tree-selector ul {\n  list-style: none;\n  margin-left: 20px;\n}\n.mam-metadata-tree-selector .tree-node {\n  display: flex;\n  align-items: center;\n  margin-bottom: 12px;\n}\n.mam-metadata-tree-selector .tree-node .icon-expand {\n  width: 22px;\n  text-align: center;\n}\n.mam-metadata-tree-selector .tree-node .mam-checkbox {\n  margin: 0 6px;\n}\n.mam-metadata-tree-selector .no-children .icon-expand {\n  visibility: hidden;\n}\n", ""]);

// exports


/***/ }),
/* 22 */
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(0)(false);
// imports


// module
exports.push([module.i, ".mam-metadata-form {\n  flex: 1;\n}\n.mam-metadata-form .mmf-group {\n  display: flex;\n  margin-bottom: 15px;\n}\n.mam-metadata-form .mmf-group .mmf-head {\n  min-width: 130px;\n}\n.mam-metadata-form .mmf-group .mmf-head label {\n  padding-top: 6px;\n}\n.mam-metadata-form .mmf-group .mmf-head sup {\n  color: #e30000;\n}\n.mam-metadata-form .mmf-group .mmf-content {\n  width: 1px;\n  flex: 1 0 auto;\n  line-height: 27px;\n}\n.mam-metadata-form .mmf-group .mmf-content.disabled {\n  pointer-events: none;\n}\n.mam-metadata-form .mmf-group .mmf-content .mmf-error {\n  position: relative;\n}\n.mam-metadata-form .mmf-group .mmf-content .mmf-error-text {\n  position: absolute;\n  background: #e30000;\n  padding: 4px 10px;\n  border-radius: 5px;\n  color: #fff;\n  white-space: nowrap;\n  margin-top: 8px;\n  z-index: 8;\n}\n.mam-metadata-form .mmf-group .mmf-content .mmf-error-text:before {\n  position: absolute;\n  z-index: 8;\n  content: ' ';\n  top: -6px;\n  width: 15px;\n  height: 8px;\n  border-left: 8px solid transparent;\n  border-right: 8px solid transparent;\n  border-bottom: 8px solid #e30000;\n}\n.mam-metadata-form .mam2-mfc-text.mmf-status-browse {\n  word-break: break-all;\n}\n.mam-metadata-form .mam-mfc-select.mmf-status-browse div {\n  display: flex;\n  flex-wrap: wrap;\n}\n.mam-metadata-form .mam-mfc-select.mmf-status-browse span {\n  background: #337ab7;\n  color: #fff;\n  margin: 5px;\n  padding: 4px;\n  border-radius: 4px;\n}\n.mam-metadata-form .mam-mfc-tag.mmf-status-browse .browse-box,\n.mam-metadata-form .mam2-mfc-tag.mmf-status-browse .browse-box {\n  display: flex;\n  flex-wrap: wrap;\n}\n.mam-metadata-form .mam-mfc-tag.mmf-status-browse span,\n.mam-metadata-form .mam2-mfc-tag.mmf-status-browse span {\n  background: #337ab7;\n  color: #fff;\n  margin: 0 5px 5px 0;\n  padding: 4px;\n  border-radius: 4px;\n}\n.mam-metadata-form .mam-mfc-tree {\n  display: flex;\n}\n.mam-metadata-form .mam-mfc-tree .items {\n  flex: 1 0 auto;\n  padding: 6px 12px;\n  font-size: 14px;\n  color: #555;\n  background-color: #fff;\n  background-image: none;\n  border: 1px solid #ccc;\n  border-radius: 4px;\n  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);\n}\n.mam-metadata-form .mam-mfc-tree .operate {\n  min-width: 60px;\n  margin-left: 10px;\n}\n.mam-metadata-form .mam-mfc-timearea .time-area {\n  display: flex;\n}\n.mam-metadata-form .mam-mfc-timearea .time-area .start-time {\n  flex: 1;\n}\n.mam-metadata-form .mam-mfc-timearea .time-area .end-time {\n  flex: 1;\n}\n.mam-metadata-form .mam-mfc-timearea .time-area-browse {\n  display: flex;\n}\n.mam-metadata-form .mam-mfc-timearea .time-area-browse .time-divide {\n  width: 30px;\n}\n.mam-metadata-form .mam-mfc-timearea .time-divide {\n  width: 80px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.mam-metadata-form .mam-mfc-table {\n  flex: 1;\n}\n.mam-metadata-form .mam-mfc-table .mam-matadata-op {\n  padding: 6px 0;\n}\n.mam-metadata-form .mam-mfc-table .mam-matadata-op .isactive {\n  color: white;\n  background: #337ab7;\n}\n.mam-metadata-form .mam-mfc-table .mam-matadata-op .confirm-tip {\n  display: inline-block;\n  margin-left: 10px;\n  vertical-align: bottom;\n  line-height: 16px;\n}\n.mam-metadata-form .mam-mfc-table .mam-matadata-op .confirm-tip .label {\n  padding: .1em .3em;\n}\n.mam-metadata-form .mam-mfc-table .mam-matadata-op .confirm-tip .label-danger {\n  cursor: pointer;\n}\n.mam-metadata-form .mam-mfc-table .mam-matadata-op .confirm-tip .label-default {\n  background-color: white;\n  color: black;\n  border: 1px solid #ccc;\n}\n.mam-metadata-form .mam-mfc-table .mam-metadata-content {\n  overflow-x: auto;\n}\n.mam-metadata-form .mam-mfc-table .mam-metadata-content .mam-metadata-table {\n  width: auto;\n}\n.mam-metadata-form .mam-mfc-table .mam-metadata-content .mam-metadata-table .flex-item.select-item {\n  background: #337ab7;\n}\n.mam-metadata-form .mam-mfc-table .mam-metadata-content .mam-metadata-table .flex-item.select-item .mam-metadata-table-item {\n  color: #fff;\n}\n.mam-metadata-form .mam-mfc-table .mam-metadata-content .mam-metadata-table .mam-metadata-table-item {\n  width: 200px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.mam-metadata-form .mam-mfc-table .mam-metadata-content .mam-metadata-table .mam-metadata-table-item .mam-metadata-table-item-com {\n  width: 100%;\n  padding: 0 5px;\n}\n.mam-metadata-form .mam-mfc-table .mam-metadata-content .mam-metadata-table .mam-metadata-table-operate {\n  width: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: none;\n}\n.mam-metadata-form .mam-mfc-table .mam-metadata-content .mam-metadata-table .mam-metadata-table-operate i {\n  font-size: 12px;\n  cursor: pointer;\n  color: gray;\n  padding: 2px 2px 2px 10px;\n}\n.mam-metadata-table-selector .modal-dialog .modal-content {\n  width: 1000px;\n  height: 800px;\n}\n.mam-metadata-table-selector .modal-dialog .modal-content .modal-body {\n  height: 682px;\n}\n", ""]);

// exports


/***/ }),
/* 23 */
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(0)(false);
// imports


// module
exports.push([module.i, ".mam-field-selector .modal-content {\n  width: 600px !important;\n  height: auto !important;\n}\n.mam-field-selector .modal-content .modal-body {\n  height: 500px;\n  overflow-y: auto;\n}\n", ""]);

// exports


/***/ }),
/* 24 */
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(0)(false);
// imports


// module
exports.push([module.i, ".mam-search-label {\n  height: 34px;\n  line-height: 34px;\n  margin-bottom: 10px;\n}\n.mam-search-input {\n  margin-bottom: 10px;\n}\n", ""]);

// exports


/***/ }),
/* 25 */
/***/ (function(module, exports) {

module.exports = "<div>\r\n    \r\n    <label class=\"mam-checkbox\">\r\n        <input type=\"checkbox\" mam-checkbox ng-model=\"item.value\" ng-disabled=\"item.isReadOnly || type=='browse'\">\r\n    </label>\r\n\r\n</div>";

/***/ }),
/* 26 */
/***/ (function(module, exports) {

module.exports = "<div>\r\n\r\n    <div ng-if=\"type=='browse'\">{{item.value}}</div>\r\n    \r\n    <input ng-if=\"type!='browse'\" type=\"text\" class=\"form-control\" ng-model=\"item.value\" ng-readonly=\"item.isReadOnly\" />\r\n\r\n</div>";

/***/ }),
/* 27 */
/***/ (function(module, exports) {

module.exports = "<div>\r\n\r\n    <div ng-if=\"type=='browse'\">{{item.value}}</div>\r\n    \r\n    <input ng-if=\"type!='browse'\" type=\"text\" class=\"form-control\" ng-model=\"item.value\" ng-readonly=\"item.isReadOnly\" />\r\n\r\n</div>";

/***/ }),
/* 28 */
/***/ (function(module, exports) {

module.exports = "<div>\r\n\r\n    <div ng-if=\"type=='browse'\">{{model}}</div>\r\n    \r\n    <input ng-if=\"type!='browse'\" type=\"text\" class=\"form-control\" value=\"{{model}}\" ng-readonly=\"item.isReadOnly\" />\r\n\r\n</div>";

/***/ }),
/* 29 */
/***/ (function(module, exports) {

module.exports = "<div>\r\n\r\n    <div ng-if=\"type=='browse'\">{{model}}</div>\r\n    \r\n    <input ng-if=\"type!='browse'\" type=\"text\" class=\"form-control\" value=\"{{model}}\" ng-readonly=\"item.isReadOnly\"/>\r\n\r\n</div>";

/***/ }),
/* 30 */
/***/ (function(module, exports) {

module.exports = "<div>\r\n\r\n    <div ng-if=\"type=='browse'\">{{item.value}}</div>\r\n\r\n    <input ng-if=\"type!='browse'\" type=\"text\" class=\"form-control\" ng-model=\"item.value\" ng-readonly=\"item.isReadOnly\"></input>\r\n\r\n</div>";

/***/ }),
/* 31 */
/***/ (function(module, exports) {

module.exports = "<div>\r\n\r\n    <div ng-if=\"type=='browse'\">\r\n        <div ng-if=\"item.isMultiSelect\">\r\n            <span ng-repeat=\"n in model\">{{n.value}}</span>\r\n        </div>\r\n        <div ng-if=\"!item.isMultiSelect\">\r\n            <span ng-if=\"model!=null && model.value!=null\">{{model.value}}</span>\r\n        </div>\r\n    </div>\r\n\r\n    <div ng-if=\"type!='browse' && !item.isMultiSelect\">\r\n        <ui-select ng-model=\"model\" theme=\"bootstrap\" sortable=\"true\" ng-disabled=\"item.isReadOnly\" on-select=\"onSelect($item, $select.selected)\"\r\n            on-remove=\"onRemove($item, $select.selected)\" append-to-body=\"true\">\r\n            <ui-select-match placeholder=\"\">{{$select.selected.value}}</ui-select-match>\r\n            <ui-select-choices repeat=\"(key,value) in items | filter:$select.search\">\r\n                {{value.value}}\r\n            </ui-select-choices>\r\n        </ui-select>\r\n    </div>\r\n\r\n    <div ng-if=\"type!='browse' && item.isMultiSelect\">\r\n        <ui-select ng-model=\"model\" multiple theme=\"bootstrap\" sortable=\"true\" ng-disabled=\"item.isReadOnly\" on-select=\"onSelect($item, $select.selected)\"\r\n            on-remove=\"onRemove($item, $select.selected)\" append-to-body=\"true\">\r\n            <ui-select-match placeholder=\"\">{{$item.value}}</ui-select-match>\r\n            <ui-select-choices repeat=\"(key,value) in items | filter:$select.search\">\r\n                {{value.value}}\r\n            </ui-select-choices>\r\n        </ui-select>\r\n    </div>\r\n\r\n</div>";

/***/ }),
/* 32 */
/***/ (function(module, exports) {

module.exports = "<div>\r\n\r\n    <div ng-if=\"type=='browse'\">{{ item.value | formatSize }}</div>\r\n\r\n    <input ng-if=\"type!='browse'\" type=\"text\" class=\"form-control\" value=\"{{item.value | formatSize}}\" ng-readonly=\"item.isReadOnly\"></input>\r\n\r\n</div>";

/***/ }),
/* 33 */
/***/ (function(module, exports) {

module.exports = "<div class=\"modal-header\">\r\n    <button type=\"button\" class=\"close\" ng-click=\"close()\">\r\n        <i class=\"fa fa-times\"></i>\r\n    </button>\r\n    <h4 class=\"modal-title\" ng-hide=\"!title\">{{title}}</h4>\r\n</div>\r\n<div class=\"modal-body object-con\">\r\n    <div class=\"mam-metadata-form\">\r\n        <div class=\"mam-mfc-table\">\r\n            <div class=\"mam-metadata-content\">\r\n                <div class=\"mam-flex-table mam-metadata-table\">\r\n                    <div class=\"flex-head\">\r\n                        <div class=\"mam-metadata-table-item\" ng-repeat=\"item in field\">\r\n                            <div>{{ !item.alias ? item.fieldName : item.alias}}</div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"flex-body\">\r\n                        <div class=\"flex-item\" ng-if=\"fieldData.length>0\" ng-repeat=\"fd in fieldData track by $index\">\r\n                            <div class=\"mam-metadata-table-item\" ng-repeat=\"item in fd\" ng-switch=\"item.controlType\">\r\n                                <!-- 1: 日期+时间 -->\r\n                                <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"1\">\r\n                                    <mam2-mfc-datetime item=\"item\" type=\"edit\"></mam2-mfc-datetime>\r\n                                </div>\r\n\r\n                                <!-- 2: 日期 -->\r\n                                <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"2\">\r\n                                    <mam2-mfc-date item=\"item\" type=\"edit\"></mam2-mfc-date>\r\n                                </div>\r\n\r\n                                <!-- 4: 数字 -->\r\n                                <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"4\">\r\n                                    <mam2-mfc-number item=\"item\" type=\"edit\"></mam2-mfc-number>\r\n                                </div>\r\n\r\n                                <!-- 5: 单行文本 -->\r\n                                <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"5\">\r\n                                    <mam2-mfc-text item=\"item\" type=\"edit\"></mam2-mfc-text>\r\n                                </div>\r\n\r\n                                <!-- 6: 多行文本 -->\r\n                                <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"6\">\r\n                                    <mam2-mfc-textarea item=\"item\" type=\"edit\"></mam2-mfc-textarea>\r\n                                </div>\r\n\r\n                                <!-- 7: 单选按钮 -->\r\n                                <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"7\">\r\n                                    <mam2-mfc-bool item=\"item\" type=\"edit\"></mam2-mfc-bool>\r\n                                </div>\r\n\r\n                                <!-- 8: 下拉列表框 -->\r\n                                <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"8\">\r\n                                    <mam2-mfc-select item=\"item\" type=\"edit\"></mam2-mfc-select>\r\n                                </div>\r\n\r\n                                <!-- 9: 帧显示时码 -->\r\n                                <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"9\">\r\n                                    <mam2-mfc-frame-to-timecode item=\"item\" type=\"edit\" entity=\"entity\"></mam2-mfc-frame-to-timecode>\r\n                                </div>\r\n\r\n                                <!-- 10: 容量显示 -->\r\n                                <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"10\">\r\n                                    <mam2-mfc-size item=\"item\" type=\"edit\"></mam2-mfc-size>\r\n                                </div>\r\n\r\n                                <!-- 11: 百纳秒显示时码 -->\r\n                                <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"11\">\r\n                                    <mam2-mfc-nanosecond-to-timecode item=\"item\" type=\"edit\" entity=\"entity\"></mam2-mfc-nanosecond-to-timecode>\r\n                                </div>\r\n\r\n                                <!-- 12: 标签输入框 -->\r\n                                <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"12\">\r\n                                    <mam2-mfc-tag item=\"item\" type=\"edit\"></mam2-mfc-tag>\r\n                                </div>\r\n\r\n                                <!-- 14: 树形数据 -->\r\n                                <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"14\">\r\n                                    <mam2-mfc-tree item=\"item\" type=\"edit\"></mam2-mfc-tree>\r\n                                </div>\r\n\r\n                                <!-- 16: 开始-结束时间 -->\r\n                                <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"16\">\r\n                                    <mam2-mfc-timearea item=\"item\" type=\"edit\"></mam2-mfc-timearea>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n\r\n<div class=\"modal-footer\">\r\n    <button class=\"btn btn-primary\" ng-click=\"ok()\">确定</button>\r\n    <button class=\"btn btn-default\" ng-click=\"close()\">取消</button>\r\n</div>";

/***/ }),
/* 34 */
/***/ (function(module, exports) {

module.exports = "<div class=\"mam-mfc-table\">\r\n    <div class=\"mam-metadata-content\">\r\n        <div class=\"mam-flex-table mam-metadata-table\" ng-if=\"type == 'browse'\">\r\n            <div class=\"flex-head\">\r\n                <div class=\"mam-metadata-table-item\" ng-repeat=\"item in configData\" ng-if=\"item.isShow === undefined || item.isShow === true\">\r\n                    <div>{{ !item.alias ? item.fieldName : item.alias}}</div>\r\n                </div>\r\n            </div>\r\n            <div class=\"flex-body\">\r\n                <div class=\"flex-item\" ng-repeat=\"modelItem in model track by $index\" mam-resize-table>\r\n                    <div class=\"mam-metadata-table-item\" ng-repeat=\"control in configDataJson\" ng-switch=\"control.controlType\" ng-if=\"control.isShow === undefined || control.isShow === true\">\r\n                        <span ng-switch-when=\"8\" title=\"{{getName(modelItem[control.fieldName],control.controlData)}}\">{{getName(modelItem[control.fieldName],control.controlData)}}</span>\r\n                        <span ng-switch-when=\"14\" title=\"{{getPath(modelItem[control.fieldName],control.controlData)}}\">{{getPath(modelItem[control.fieldName],control.controlData)}}</span>\r\n                        <span ng-switch-when=\"7\" title=\"{{getBool(modelItem[control.fieldName])}}\">{{getBool(modelItem[control.fieldName])}}</span>\r\n                        <span ng-switch-default title=\"{{modelItem[control.fieldName]}}\">{{modelItem[control.fieldName]}}</span>\r\n                    </div>\r\n                </div>\r\n                <div class=\"flex-item\" ng-repeat=\"extraRow in extraRows track by $index\" mam-resize-table>\r\n                    <div class=\"mam-metadata-table-item\" ng-repeat=\"control in configDataJson\" ng-switch=\"control.controlType\" ng-if=\"control.isShow === undefined || control.isShow === true\">\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"mam-flex-table mam-metadata-table\" ng-if=\"type == 'edit' && !item.isReadOnly\">\r\n            <div class=\"flex-head\">\r\n                <div class=\"mam-metadata-table-operate\"></div>\r\n                <div class=\"mam-metadata-table-item\" ng-repeat=\"item in field\">\r\n                    <div>{{ !item.alias ? item.fieldName : item.alias}}</div>\r\n                </div>\r\n            </div>\r\n            <div class=\"flex-body\">\r\n                <div class=\"flex-item\" ng-if=\"fieldData.length>0\" ng-repeat=\"fd in fieldData track by $index\" mam-resize-table>\r\n                    <div class=\"mam-metadata-table-operate\">\r\n                        <i class=\"fa fa-times\" ng-show=\"fieldData.length-1 !== $index\" ng-click=\"reduce(fd,$index)\" title=\"删除\"></i>\r\n                    </div>\r\n                    <div class=\"mam-metadata-table-item\" ng-repeat=\"item in fd\" ng-switch=\"item.controlType\">\r\n                        <!-- 1: 日期+时间 -->\r\n                        <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"1\">\r\n                            <mam2-mfc-datetime item=\"item\" type=\"edit\"></mam2-mfc-datetime>\r\n                        </div>\r\n\r\n                        <!-- 2: 日期 -->\r\n                        <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"2\">\r\n                            <mam2-mfc-date item=\"item\" type=\"edit\"></mam2-mfc-date>\r\n                        </div>\r\n\r\n                        <!-- 4: 数字 -->\r\n                        <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"4\">\r\n                            <mam2-mfc-number item=\"item\" type=\"edit\"></mam2-mfc-number>\r\n                        </div>\r\n\r\n                        <!-- 5: 单行文本 -->\r\n                        <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"5\">\r\n                            <mam2-mfc-text item=\"item\" type=\"edit\"></mam2-mfc-text>\r\n                        </div>\r\n\r\n                        <!-- 6: 多行文本 -->\r\n                        <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"6\">\r\n                            <mam2-mfc-textarea item=\"item\" type=\"edit\"></mam2-mfc-textarea>\r\n                        </div>\r\n\r\n                        <!-- 7: 单选按钮 -->\r\n                        <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"7\">\r\n                            <mam2-mfc-bool item=\"item\" type=\"edit\"></mam2-mfc-bool>\r\n                        </div>\r\n\r\n                        <!-- 8: 下拉列表框 -->\r\n                        <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"8\">\r\n                            <mam2-mfc-select item=\"item\" type=\"edit\"></mam2-mfc-select>\r\n                        </div>\r\n\r\n                        <!-- 9: 帧显示时码 -->\r\n                        <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"9\">\r\n                            <mam2-mfc-frame-to-timecode item=\"item\" type=\"edit\" entity=\"entity\"></mam2-mfc-frame-to-timecode>\r\n                        </div>\r\n\r\n                        <!-- 10: 容量显示 -->\r\n                        <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"10\">\r\n                            <mam2-mfc-size item=\"item\" type=\"edit\"></mam2-mfc-size>\r\n                        </div>\r\n\r\n                        <!-- 11: 百纳秒显示时码 -->\r\n                        <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"11\">\r\n                            <mam2-mfc-nanosecond-to-timecode item=\"item\" type=\"edit\" entity=\"entity\"></mam2-mfc-nanosecond-to-timecode>\r\n                        </div>\r\n\r\n                        <!-- 12: 标签输入框 -->\r\n                        <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"12\">\r\n                            <mam2-mfc-tag item=\"item\" type=\"edit\"></mam2-mfc-tag>\r\n                        </div>\r\n\r\n                        <!-- 14: 树形数据 -->\r\n                        <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"14\">\r\n                            <mam2-mfc-tree item=\"item\" type=\"edit\"></mam2-mfc-tree>\r\n                        </div>\r\n\r\n                        <!-- 16: 开始-结束时间 -->\r\n                        <div class=\"mam-metadata-table-item-com\" ng-switch-when=\"16\">\r\n                            <mam2-mfc-timearea item=\"item\" type=\"edit\"></mam2-mfc-timearea>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"flex-item\" ng-repeat=\"editExtraRow in editExtraRows track by $index\">\r\n                    <div class=\"mam-metadata-table-operate\">\r\n\r\n                    </div>\r\n                    <div class=\"mam-metadata-table-item\" ng-repeat=\"control in configDataJson\">\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>";

/***/ }),
/* 35 */
/***/ (function(module, exports) {

module.exports = "<div>\r\n\r\n    <div class=\"browse-box\" ng-if=\"type=='browse'\">\r\n        <span ng-if=\"item.value!=null && item.value.length>0\" ng-repeat=\"i in item.value.split(',')\">{{i}}</span>\r\n    </div>\r\n\r\n    <tags-input ng-if=\"type!='browse'\" ng-model=\"tags\" min-length=\"0\" ng-disabled=\"item.isReadOnly\" placeholder=\"添加标签\" \r\n        on-tag-adding=\"adding($tag)\" on-tag-added=\"added($tag)\" on-tag-removed=\"remove($tag)\" class=\"tags-input\" on-invalid-tag=\"invalid($tag)\"></tags-input>\r\n</div>";

/***/ }),
/* 36 */
/***/ (function(module, exports) {

module.exports = "<div>\r\n\r\n    <div ng-if=\"type=='browse'\">{{item.value}}</div>\r\n\r\n    <input ng-if=\"type!='browse'\" type=\"text\" class=\"form-control\" ng-model=\"item.value\" ng-readonly=\"item.isReadOnly\" />\r\n\r\n</div>";

/***/ }),
/* 37 */
/***/ (function(module, exports) {

module.exports = "<div>\r\n\r\n    <div ng-if=\"type=='browse'\" ng-bind-html=\"trustAsHtml(item.value)\"></div>\r\n\r\n    <textarea ng-if=\"type!='browse'\" class=\"form-control\" ng-model=\"item.value\" ng-readonly=\"item.isReadOnly\"></textarea>\r\n\r\n</div>";

/***/ }),
/* 38 */
/***/ (function(module, exports) {

module.exports = "<div class=\"mam-mfc-timearea\">\r\n\r\n    <div ng-if=\"type=='browse'\" class=\"time-area-browse\">{{model.startModel}}<span class=\"time-divide\">-</span>{{model.endModel}}</div>\r\n\r\n    <div ng-if=\"type!='browse'\" class=\"time-area\">\r\n        <div class=\"start-time\">\r\n            <input id=\"\" type=\"text\" class=\"start-time form-control\" ng-model='model.startModel' placeholder=\"开始时间\" ng-readonly=\"item.isReadOnly\"/>\r\n            <div class=\"mmf-error\" ng-if=\"(type=='edit' || (type=='optional-edit' && item.selected)) && item.errors[0]!==undefined\">\r\n                <div class=\"mmf-error-text\">{{ getErrorInfo(item, item.errors[0]) }}</div>\r\n            </div>\r\n        </div>\r\n        <span class=\"time-divide\">-</span>\r\n        <div class=\"end-time\">\r\n            <input type=\"text\" class=\"end-time form-control\" ng-model='model.endModel' placeholder=\"结束时间\" ng-readonly=\"item.isReadOnly\"/>\r\n            <div class=\"mmf-error\" ng-if=\"(type=='edit' || (type=='optional-edit' && item.selected)) && item.errors[1]!==undefined\">\r\n                <div class=\"mmf-error-text\">{{ getErrorInfo(item, item.errors[1]) }}</div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n</div>";

/***/ }),
/* 39 */
/***/ (function(module, exports) {

module.exports = "<div class=\"modal-header\">\r\n    <button type=\"button\" class=\"close\" ng-click=\"close()\"><i class=\"fa fa-times\"></i></button>\r\n    <h4 class=\"modal-title\">分类选择</h4>\r\n</div>\r\n\r\n<div class=\"modal-body\">\r\n    <script type=\"text/ng-template\" id=\"mam-metadata-tree-selector-items\">\r\n        <div class=\"tree-node\" ng-class=\"{'no-children':item.children==null||item.children.length==0}\">\r\n            <i class=\"icon-expand fa\" ng-click=\"item.expand=!item.expand\" ng-class=\"item.expand?'fa-minus-square':'fa-plus-square'\"></i>\r\n            <label class=\"mam-checkbox\">\r\n                <input type=\"checkbox\" ng-model=\"item.selected\" mam-checkbox ng-click=\"selectItem(item)\" />\r\n                <span>{{item.categoryName}}</span>\r\n            </label>\r\n        </div>\r\n\r\n        <ul ng-if=\"item.children && item.expand\">\r\n            <li ng-repeat=\"item in item.children\" ng-include=\"'mam-metadata-tree-selector-items'\"></li>\r\n        </ul>\r\n    </script>\r\n    <ul class=\"mam-category-tree\">\r\n        <li ng-repeat=\"item in tree.children\" ng-include=\"'mam-metadata-tree-selector-items'\"></li>\r\n    </ul>\r\n</div>\r\n\r\n<div class=\"modal-footer\">\r\n    <button class=\"btn btn-primary\" ng-click=\"ok()\">确定</button>\r\n    <button class=\"btn btn-default\" ng-click=\"close()\">取消</button>\r\n</div>";

/***/ }),
/* 40 */
/***/ (function(module, exports) {

module.exports = "<div>\r\n\r\n    <div class=\"items\">\r\n        <div class=\"item\" ng-repeat=\"item in model\">{{item.path}}</div>\r\n    </div>\r\n    <div class=\"operate\">\r\n        <button class=\"btn btn-default\" ng-click=\"open()\" ng-if=\"type=='edit'\">选择</button>\r\n    </div>\r\n\r\n</div>";

/***/ }),
/* 41 */
/***/ (function(module, exports) {

module.exports = "<div class=\"{{ className }}\">\r\n    <!-- mmf 是  mam-metadata-form 的缩写-->\r\n    <div class=\"mmf-group mmf-group-{{item.fieldName}} mmf-control-{{getCtrlByType(item.controlType)}}\" ng-repeat=\"item in models\"\r\n        ng-if=\"item.isShow === undefined || item.isShow\">\r\n        <div class=\"mmf-head\">\r\n            <label>\r\n                {{item.alias}}\r\n                <sup ng-if=\"type!='browse' && item.isMustInput\">*</sup>\r\n            </label>\r\n            <label class=\"mam-checkbox\" ng-show=\"type=='optional-edit' && !item.isReadOnly\" ng-click=\"changeSelect(item)\">\r\n                <input type=\"checkbox\" ng-model=\"item.selected\" mam-checkbox />\r\n            </label>\r\n        </div>\r\n\r\n        <div class=\"mmf-content\" ng-switch=\"item.controlType\" ng-class=\"{'disabled':type=='optional-edit' && !item.selected}\">\r\n            <!-- mfc 是 metadata-form-control 的缩写 -->\r\n\r\n            <!-- 1: 日期+时间 -->\r\n            <mam2-mfc-datetime ng-switch-when=\"1\"></mam2-mfc-datetime>\r\n\r\n            <!-- 2: 日期 -->\r\n            <mam2-mfc-date ng-switch-when=\"2\"></mam2-mfc-date>\r\n\r\n            <!-- 3：时间（后期未开放） -->\r\n\r\n            <!-- 4: 数字 -->\r\n            <mam2-mfc-number ng-switch-when=\"4\"></mam2-mfc-number>\r\n\r\n            <!-- 5: 单行文本 -->\r\n            <mam2-mfc-text ng-switch-when=\"5\"></mam2-mfc-text>\r\n\r\n            <!-- 6: 多行文本 -->\r\n            <mam2-mfc-textarea ng-switch-when=\"6\"></mam2-mfc-textarea>\r\n\r\n            <!-- 7: 单选按钮 -->\r\n            <mam2-mfc-bool ng-switch-when=\"7\"></mam2-mfc-bool>\r\n\r\n            <!-- 8: 下拉列表框 -->\r\n            <mam2-mfc-select ng-switch-when=\"8\" on-change=\"onSelectChange(value,oldValue,item)\"></mam2-mfc-select>\r\n\r\n            <!-- 9: 帧显示时码 -->\r\n            <mam2-mfc-frame-to-timecode ng-switch-when=\"9\" entity=\"entity\"></mam2-mfc-frame-to-timecode>\r\n\r\n            <!-- 10: 容量显示 -->\r\n            <mam2-mfc-size ng-switch-when=\"10\"></mam2-mfc-size>\r\n\r\n            <!-- 11: 百纳秒显示时码 -->\r\n            <mam2-mfc-nanosecond-to-timecode ng-switch-when=\"11\" entity=\"entity\"></mam2-mfc-nanosecond-to-timecode>\r\n\r\n            <!-- 12: 标签输入框 -->\r\n            <mam2-mfc-tag ng-switch-when=\"12\"></mam2-mfc-tag>\r\n\r\n            <!-- 14: 树形数据 -->\r\n            <mam2-mfc-tree ng-switch-when=\"14\"></mam2-mfc-tree>\r\n\r\n            <!-- 15: 表格 -->\r\n            <mam2-mfc-table ng-switch-when=\"15\"></mam2-mfc-table>\r\n\r\n            <!-- 16: 开始-结束时间 -->\r\n            <mam2-mfc-timearea ng-switch-when=\"16\"></mam2-mfc-timearea>\r\n\r\n            <div ng-switch-default>\r\n                <mam2-mfc-text></mam2-mfc-text>\r\n            </div>\r\n\r\n            <div class=\"mmf-error\" ng-if=\"(type=='edit' || (type=='optional-edit' && item.selected)) && item.error!=null\">\r\n\r\n                <div class=\"mmf-error-text\">{{ getErrorInfo(item) }}</div>\r\n\r\n            </div>\r\n\r\n        </div>\r\n\r\n        <div class=\"mmf-right\" ng-transclude=\"mmf-right\" item=\"item\"></div>\r\n    </div>\r\n\r\n</div>";

/***/ }),
/* 42 */
/***/ (function(module, exports) {

module.exports = "<div class=\"mam-field-selector-modal\">\r\n    <div class=\"modal-header\">\r\n        <button type=\"button\" class=\"close\" ng-click=\"close()\"><i class=\"fa fa-times\"></i></button>\r\n        <h4 class=\"modal-title\" ng-bind=\"title\"></h4>\r\n    </div>\r\n    <div class=\"modal-body\">\r\n        <mam-metadata-selector selected-item=\"selectedItem\" q-tree-url=\"qTreeUrl\"></mam-metadata-selector>\r\n    </div>\r\n    <div class=\"modal-footer\">\r\n        <button class=\"btn btn-primary\" ng-click=\"ok()\">确定</button>\r\n        <button class=\"btn btn-default\" ng-click=\"close()\">取消</button>\r\n    </div>\r\n</div>";

/***/ }),
/* 43 */
/***/ (function(module, exports) {

module.exports = "<div>\r\n    <div class=\"form-group\">\r\n        <div class=\"col-lg-2 mam-search-label\">\r\n            <label>搜索：</label>\r\n        </div>\r\n        <div class=\"col-lg-10 mam-search-input\">\r\n            <input class=\"form-control\" type=\"text\" ng-model=\"keyword\" ng-change=\"onKeywordChanged()\"/>\r\n        </div>\r\n    </div>\r\n    <script type=\"text/ng-template\" id=\"tree\">\r\n        <div ng-if=\"!item.editMode\" class=\"nav\" ng-class=\"item.selected?'checked':'unchecked'\" title=\"{{item.description}}\">\r\n            <label class=\"mam-checkbox\" ng-if=\"item.dataType!='object'\">\r\n                <input type=\"checkbox\" ng-model=\"item.selected\" mam-checkbox ng-click=\"selectItem(item)\" />\r\n            </label>\r\n            <label class=\"mam-checkbox\" ng-if=\"config.checkParent == 'true' && item.dataType=='object'\">\r\n                <input type=\"checkbox\" ng-model=\"item.selected\" mam-checkbox ng-click=\"selectItem(item)\" />\r\n            </label>\r\n\r\n            <i class=\"fa \" ng-click=\"getChildren(item)\" ng-if=\"item.dataType=='object'&& !item.selected\" ng-class=\"item.expand?'fa-minus-square':'fa-plus-square'\"></i>\r\n            <a ng-click=\"setModel(item)\">{{item.alias}}</a>\r\n        </div>\r\n\r\n        <ul ng-if=\"item.children\" ng-show=\"item.expand\">\r\n            <li ng-repeat=\"item in item.children\" ng-include=\"'tree'\" ng-if=\"item.show===undefined || item.show\"></li>\r\n        </ul>\r\n    </script>\r\n    <ul class=\"mam-tree\">\r\n        <li ng-repeat=\"item in folders\" ng-include=\"'tree'\" ng-if=\"item.show===undefined || item.show\"></li>\r\n    </ul>\r\n\r\n    <!--<div class=\"mam-flex-table\">-->\r\n        <!--<div class=\"flex-head\">-->\r\n            <!--<div class=\"check-box\">-->\r\n                <!--<label class=\"mam-checkbox\">-->\r\n                    <!--<input type=\"checkbox\" ng-model=\"selecedAll\" mam-checkbox mam-all-checkbox collection=\"model\" property=\"isSelected\" />-->\r\n                <!--</label>-->\r\n            <!--</div>-->\r\n            <!--<div class=\"item\">-->\r\n                <!--名称-->\r\n            <!--</div>-->\r\n        <!--</div>-->\r\n        <!--<div class=\"flex-body\" ui-sortable=\"sortableOptions\">-->\r\n            <!--<div class=\"flex-item\" ng-repeat=\"item in folders\" ng-include=\"'table-tree'\">-->\r\n                <!--<div class=\"check-box\">-->\r\n                    <!--<label class=\"mam-checkbox\">-->\r\n                        <!--<input type=\"checkbox\" ng-model=\"iyuiutyi\" mam-checkbox property=\"isSelected\" />-->\r\n                    <!--</label>-->\r\n                <!--</div>-->\r\n                <!--<div class=\"item\">-->\r\n                    <!--新闻节目2-->\r\n                <!--</div>-->\r\n            <!--</div>-->\r\n        <!--</div>-->\r\n    <!--</div>-->\r\n</div>";

/***/ }),
/* 44 */
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(20);
if(typeof content === 'string') content = [[module.i, content, '']];
// Prepare cssTransformation
var transform;

var options = {}
options.transform = transform
// add the styles to the DOM
var update = __webpack_require__(1)(content, options);
if(content.locals) module.exports = content.locals;
// Hot Module Replacement
if(false) {
	// When the styles change, update the <style> tags
	if(!content.locals) {
		module.hot.accept("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/less-loader/dist/cjs.js!./selector.less", function() {
			var newContent = require("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/less-loader/dist/cjs.js!./selector.less");
			if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
			update(newContent);
		});
	}
	// When the module is disposed, remove the <style> tags
	module.hot.dispose(function() { update(); });
}

/***/ }),
/* 45 */
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(21);
if(typeof content === 'string') content = [[module.i, content, '']];
// Prepare cssTransformation
var transform;

var options = {}
options.transform = transform
// add the styles to the DOM
var update = __webpack_require__(1)(content, options);
if(content.locals) module.exports = content.locals;
// Hot Module Replacement
if(false) {
	// When the styles change, update the <style> tags
	if(!content.locals) {
		module.hot.accept("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/less-loader/dist/cjs.js!./selector.less", function() {
			var newContent = require("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/less-loader/dist/cjs.js!./selector.less");
			if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
			update(newContent);
		});
	}
	// When the module is disposed, remove the <style> tags
	module.hot.dispose(function() { update(); });
}

/***/ }),
/* 46 */
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(22);
if(typeof content === 'string') content = [[module.i, content, '']];
// Prepare cssTransformation
var transform;

var options = {}
options.transform = transform
// add the styles to the DOM
var update = __webpack_require__(1)(content, options);
if(content.locals) module.exports = content.locals;
// Hot Module Replacement
if(false) {
	// When the styles change, update the <style> tags
	if(!content.locals) {
		module.hot.accept("!!../../node_modules/css-loader/index.js!../../node_modules/less-loader/dist/cjs.js!./style.less", function() {
			var newContent = require("!!../../node_modules/css-loader/index.js!../../node_modules/less-loader/dist/cjs.js!./style.less");
			if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
			update(newContent);
		});
	}
	// When the module is disposed, remove the <style> tags
	module.hot.dispose(function() { update(); });
}

/***/ }),
/* 47 */
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(23);
if(typeof content === 'string') content = [[module.i, content, '']];
// Prepare cssTransformation
var transform;

var options = {}
options.transform = transform
// add the styles to the DOM
var update = __webpack_require__(1)(content, options);
if(content.locals) module.exports = content.locals;
// Hot Module Replacement
if(false) {
	// When the styles change, update the <style> tags
	if(!content.locals) {
		module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less", function() {
			var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less");
			if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
			update(newContent);
		});
	}
	// When the module is disposed, remove the <style> tags
	module.hot.dispose(function() { update(); });
}

/***/ }),
/* 48 */
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(24);
if(typeof content === 'string') content = [[module.i, content, '']];
// Prepare cssTransformation
var transform;

var options = {}
options.transform = transform
// add the styles to the DOM
var update = __webpack_require__(1)(content, options);
if(content.locals) module.exports = content.locals;
// Hot Module Replacement
if(false) {
	// When the styles change, update the <style> tags
	if(!content.locals) {
		module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less", function() {
			var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less");
			if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
			update(newContent);
		});
	}
	// When the module is disposed, remove the <style> tags
	module.hot.dispose(function() { update(); });
}

/***/ }),
/* 49 */
/***/ (function(module, exports) {


/**
 * When source maps are enabled, `style-loader` uses a link element with a data-uri to
 * embed the css on the page. This breaks all relative urls because now they are relative to a
 * bundle instead of the current page.
 *
 * One solution is to only use full urls, but that may be impossible.
 *
 * Instead, this function "fixes" the relative urls to be absolute according to the current page location.
 *
 * A rudimentary test suite is located at `test/fixUrls.js` and can be run via the `npm test` command.
 *
 */

module.exports = function (css) {
  // get current location
  var location = typeof window !== "undefined" && window.location;

  if (!location) {
    throw new Error("fixUrls requires window.location");
  }

	// blank or null?
	if (!css || typeof css !== "string") {
	  return css;
  }

  var baseUrl = location.protocol + "//" + location.host;
  var currentDir = baseUrl + location.pathname.replace(/\/[^\/]*$/, "/");

	// convert each url(...)
	/*
	This regular expression is just a way to recursively match brackets within
	a string.

	 /url\s*\(  = Match on the word "url" with any whitespace after it and then a parens
	   (  = Start a capturing group
	     (?:  = Start a non-capturing group
	         [^)(]  = Match anything that isn't a parentheses
	         |  = OR
	         \(  = Match a start parentheses
	             (?:  = Start another non-capturing groups
	                 [^)(]+  = Match anything that isn't a parentheses
	                 |  = OR
	                 \(  = Match a start parentheses
	                     [^)(]*  = Match anything that isn't a parentheses
	                 \)  = Match a end parentheses
	             )  = End Group
              *\) = Match anything and then a close parens
          )  = Close non-capturing group
          *  = Match anything
       )  = Close capturing group
	 \)  = Match a close parens

	 /gi  = Get all matches, not the first.  Be case insensitive.
	 */
	var fixedCss = css.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi, function(fullMatch, origUrl) {
		// strip quotes (if they exist)
		var unquotedOrigUrl = origUrl
			.trim()
			.replace(/^"(.*)"$/, function(o, $1){ return $1; })
			.replace(/^'(.*)'$/, function(o, $1){ return $1; });

		// already a full url? no change
		if (/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/)/i.test(unquotedOrigUrl)) {
		  return fullMatch;
		}

		// convert the url to a full url
		var newUrl;

		if (unquotedOrigUrl.indexOf("//") === 0) {
		  	//TODO: should we add protocol?
			newUrl = unquotedOrigUrl;
		} else if (unquotedOrigUrl.indexOf("/") === 0) {
			// path should be relative to the base url
			newUrl = baseUrl + unquotedOrigUrl; // already starts with '/'
		} else {
			// path should be relative to current directory
			newUrl = currentDir + unquotedOrigUrl.replace(/^\.\//, ""); // Strip leading './'
		}

		// send back the fixed url(...)
		return "url(" + JSON.stringify(newUrl) + ")";
	});

	// send back the fixed css
	return fixedCss;
};


/***/ }),
/* 50 */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__selector_less__ = __webpack_require__(44);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__selector_less___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__selector_less__);



var mamMetadataTableSelectorCtrl = function ($rootScope, $scope, $q, $timeout, $parse, $uibModalInstance, $state, $http, params) {
    $scope.field = params.field;
    $scope.data = params.data;
    var tableItemWidth = 200;

    function init() {
        $scope.fieldData = _.map($scope.data, function(row){
            return _.map($scope.field, function(f){
                var ret = angular.copy(f);
                ret.value = row[f.fieldName];
                return ret;
            });
        });
        $scope.lastIndex = $scope.fieldData.length - 1;
        $scope.addBlankRow();

        if ($scope.field)
        {
            $timeout(function(){
                $(".mam-metadata-table").width($scope.field.length * tableItemWidth);
            });
        }
    }

    $scope.addBlankRow = function(){
        var row = [];
        $scope.lastIndex++;
        _.forEach($scope.field, function(f){
            var cf = angular.copy(f);
            cf.value = "";
            cf.index = $scope.lastIndex;
            Object.defineProperty(cf, "value", {
                get: function() {
                    return this._value;
                },
                set: function(newValue) {
                    this._value = newValue;
                    if (newValue && cf.index === $scope.lastIndex)
                    {
                        $scope.addBlankRow();
                    }
                }
            });
            row.push(cf);
        });
        $scope.fieldData.push(row);
    };

    var isEmptyRow = function(fd){
        var isEmpty = true;
        _.forEach(fd, function(f){
            if (f.value)
            {
                isEmpty = false;
            }
        });
        return isEmpty;
    };

    $scope.ok = function () {
        var datas = [];
        _.forEach($scope.fieldData, function(fd){
            if (!isEmptyRow(fd))
            {
                var ret = {};
                _.forEach(fd, function(f){
                    ret[f.fieldName] = f.value;
                });
                datas.push(ret);
            }
        });
        $uibModalInstance.close(datas);
    }

    $scope.close = function () {
        $uibModalInstance.close(false);
    }

    init();
};

mamMetadataTableSelectorCtrl.$inject = ['$rootScope', '$scope', '$q', '$timeout', '$parse', '$uibModalInstance', '$state', '$http', 'params'];
angular.module('mam-metadata').controller('mamMetadataTableSelectorCtrl', mamMetadataTableSelectorCtrl);

/***/ }),
/* 51 */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__selector_less__ = __webpack_require__(45);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__selector_less___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__selector_less__);



var mamMetadataTreeSelectorCtrl = function ($rootScope, $scope, $q, $timeout, $parse, $uibModalInstance, $state, $http, params) {
    var field = params.field;
    var result = [];
    $scope.tree = {};
    var treeDict = {};

    function init() {
        var data = field.controlData;

        function initSelected() {
            if (field.value != undefined && field.value.length > 0) {
                var array = field.value.split(',');
                _.forEach(array, function (v) {
                    if (!field.isMultiSelect && result.length > 0)
                        return;
                    if (treeDict[v] != undefined) {
                        treeDict[v].selected = true;
                        $scope.selectItem(treeDict[v]);
                    }
                });
            }
        }

        if (data == null || data.length == 0) {
            $http.get('~/business/tree/category/' + field.refResourceField).then(function (res) {
                $scope.tree = { children: res.data };
                toDict($scope.tree.children);
                initSelected();
            });
        } else {
            data = JSON.parse(data);
            $scope.tree = { children: data };
            toDict($scope.tree.children);
            initSelected();
        }
    }

    function toDict(tree) {
        _.forEach(tree, function (item) {
            treeDict[item.categoryCode] = item;
            if (item.children != null && item.children.length > 0) {
                toDict(item.children);
            }
        });
    }

    function selectParent(code, value) {
        if (code && treeDict[code] != null) {
            if (!value && _.some(treeDict[code].children, 'selected'))
                return;
            treeDict[code].selected = value;
            if (value && !treeDict[code].expand) {
                treeDict[code].expand = true;
            }
            selectParent(treeDict[code].categoryParent, value);
        }
    }

    function selectChildren(tree, value) {
        _.forEach(tree, function (item) {
            item.selected = value;
            if (item.children != null && item.children.length > 0) {
                selectChildren(item.children, value);
            }
        });
    }

    $scope.selectItem = function (item) {
        if (!item.selected) {
            selectChildren(item.children, item.selected);
        } else {
            if (field.isMultiSelect) {
            }
            else {//单选时取消其他选中
                _.forEach(result, function (o) {
                    selectChildren(treeDict[o].children, false);
                    selectParent(o, false);
                });
                result = [item.categoryCode];
            }
        }
        selectParent(item.categoryParent, item.selected);
    }

    init();

    $scope.ok = function () {
        if (result.length === 0)
        {
            var hasRecord;
            for (var key in treeDict)
            {
                if (treeDict[key].selected)
                {
                    hasRecord = false;
                    _.forEach(result, function(ret, index){
                        if (treeDict[key].categoryCode.indexOf(ret) > -1)
                        {
                            result[index] = treeDict[key].categoryCode;
                            hasRecord = true;
                        }
                    });
                    if (!hasRecord)
                    {
                        result.push(treeDict[key].categoryCode);
                    }
                }
            }
        }

        $uibModalInstance.close(result.join());
    }

    $scope.close = function () {
        $uibModalInstance.close(false);
    }
};

mamMetadataTreeSelectorCtrl.$inject = ['$rootScope', '$scope', '$q', '$timeout', '$parse', '$uibModalInstance', '$state', '$http', 'params'];

angular.module('mam-metadata').controller('mamMetadataTreeSelectorCtrl', mamMetadataTreeSelectorCtrl);

/***/ }),
/* 52 */
/***/ (function(module, exports, __webpack_require__) {

if (!window.mam) {
    window.mam = {};
}
window.mam.metadata = {};

angular.module('mam-metadata', ['mam-ng', 'ui.bootstrap']);


__webpack_require__(2);
__webpack_require__(3);
__webpack_require__(4);
__webpack_require__(14);
__webpack_require__(5);
__webpack_require__(6);
__webpack_require__(7);
__webpack_require__(8);
__webpack_require__(9);
__webpack_require__(11);
__webpack_require__(12);
__webpack_require__(13);
__webpack_require__(15);
__webpack_require__(10);


__webpack_require__(16);
__webpack_require__(18);

__webpack_require__(17);

__webpack_require__(19);

/***/ }),
/* 53 */
/***/ (function(module, exports, __webpack_require__) {

__webpack_require__(47);

var mamFieldSelectorController = function ($rootScope, $scope, $q, $timeout, $parse, $uibModalInstance, $state, $http, selectedItem, qTreeUrl) {
    $scope.title = "选择字段";
    $scope.selectedItem = selectedItem;
    $scope.qTreeUrl = qTreeUrl;

    $scope.ok = function(){
        $uibModalInstance.close($scope.selectedItem);
    };

    $scope.close = function(){
        $uibModalInstance.close();
    };
};

mamFieldSelectorController.$inject = ['$rootScope', '$scope', '$q', '$timeout', '$parse', '$uibModalInstance', '$state', '$http','selectedItem','qTreeUrl'];
angular.module('mam-metadata').controller("mamFieldSelectorController", mamFieldSelectorController);

/***/ })
/******/ ]);