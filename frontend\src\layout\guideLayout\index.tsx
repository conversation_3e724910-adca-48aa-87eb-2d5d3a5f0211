/*
 * @Author: 李武林
 * @Date: 2022-05-30 18:29:32
 * @LastEditors: 李武林
 * @LastEditTime: 2022-06-08 10:30:38
 * @FilePath: \frontend\src\layout\guideLayout\index.tsx
 * @Description:
 *
 * Copyright (c) 2022 by 李武林/索贝数码, All Rights Reserved.
 */
import { Button, Drawer, message, Checkbox } from 'antd';
import React, { useEffect, useState, FC, useRef } from 'react';
import { useHistory, useIntl, useLocation, useSelector } from 'umi';
import './index.less';
import { getguidedirectory, getSeoConfig } from '@/service/help';
import finger from '@/images/icons/finger.png';
import { IConfig } from '@/models/config';

const App: FC = ({ children }) => {
    let history: any = useHistory();
    let hideHead = history.location?.query?.hideHead || false;
    // 是否显示引导图标
    const [showGuide, setShowGuide] = useState(true);
    // 用户引导弹窗是否打开
    const [visible, setVisible] = useState(false);
    // 当前路由信息
    const location: any = useLocation();
    let target = location?.query?.target || ''
    // 当前页面信息
    const [currentPage, setCurrentPage] = useState<any>(null);
    const params = window.location.href.split('/')[window.location.href.split('/')?.length - 1] || ''
    const shareFlag_ = params?.split('_')[1];
    // 当前页面的所有指引
    const [currentPageGuide, setCurrentPageGuide] = useState([]);
    // 所有的图片
    const [allGuide, setAllGuide] = useState<any>([]);
    // 是否打开图片
    const [openImage, setOpenImage] = useState(false);
    // 当前预览到第几张图片
    const [currentImage, setCurrentImage] = useState(0);
    // 当前是否在拖拽
    let isDrag = useRef(false);
    // 当前拖拽的位置
    let [dragPosition, setDragPosition] = useState<any>(null);
    // 是否隐藏所有的指引
    const [noshow, setNoshow] = useState<any>(false);

    const { parameterConfig } = useSelector<{ permission: any }, IPermission>(
        ({ permission }) => permission,
    );
    const intl = useIntl();

    const urlSon = window.location.href.split('/')[window.location.href.split('/')?.length - 2]
    const url = window.location.href.split('/')[window.location.href.split('/')?.length - 3]
    const standardUrl = url + '/' + urlSon
    const  configs:any  = useSelector<{ config: any }, IConfig>( ({config})=>config);
    let notShowHeader = standardUrl === 'basic/rmanDetail' && configs.mobileFlag && parameterConfig.target_customer as string =='npu'


    useEffect(() => {
      //自定义国际化
      if (parameterConfig.custom_i18n) {
        let arr = parameterConfig.custom_i18n.split('$$');
        arr.map((str: string) => {
          let words = str.split('|');
          if (intl.locale == 'zh-CN') {
            intl.messages[words[0]] = words[1];
          } else if (intl.locale == 'en-US') {
            intl.messages[words[0]] = words[2];
          }
        });
      }
    }, [parameterConfig]);
    
    useEffect(() => {
        // 判断是否为移动端
        if (/(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent)) {
            setShowGuide(false);
        } else {
            let noshow: any = localStorage.getItem('noshow') || null;
            if (noshow !== 'true') {
                let localStordata: any = localStorage.getItem('nohintpage') || null;
                const nohintpage: any = JSON.parse(localStordata) || [];
                let pathname = location.pathname;
                if (pathname.indexOf('/basic/rmanDetail') != -1) {
                    pathname = '/basic/rmanDetail';
                }
                //分享的详情单页面不需要指引
                if (pathname.indexOf('/basic/shareDetail') != -1) {
                    setVisible(false);
                    setShowGuide(false);
                    return
                }
                // 如果没有打开过，则打开引导弹窗
                let isfind = nohintpage.findIndex((item: any) => item === pathname);
                // 如果设置了不再显示 或者已经打开过
                if (isfind !== -1) {
                    setVisible(false);
                    setShowGuide(true);
                } else {
                    getpagecodehelp(location.pathname);
                }
            } else {
                setNoshow(true);
            }
        }
    }, [location])

    useEffect(() => {
        setSeoinfo();
    }, []);

    // 获取seo配置
    const setSeoinfo = () => {
        getSeoConfig().then((res: any) => {
            if (res?.errorCode === 'success') {
                if (res.extendMessage.websiteStatistics && res.extendMessage.websiteStatistics != '') {
                    let container: any = document.createElement('div');
                    container.innerHTML = res.extendMessage.websiteStatistics;
                    if (container.children.length) {
                        // 添加到head中
                        container.children.forEach((element: any) => {
                            addscript(element);
                        });
                    }
                }
            } else {
                console.error('查询seo配置失败');
            }
        });
    };

    // 动态创建标签并且添加到head中
    const addscript = (dom: any) => {
        if (dom.src && dom.src !== '') {
            let script = document.createElement('script');
            script.type = 'text/JavaScript';
            script.src = dom.src;
            document.head.appendChild(script);
        } else {
            let script = document.createElement('script');
            script.type = 'text/JavaScript';
            script.innerHTML = dom.innerHTML;
            document.head.appendChild(script);
        }
    };

    // 关闭提示
    const onhelpClose = () => {
        setVisible(false);
        let pathname = location.pathname;
        if (pathname.indexOf('/basic/rmanDetail') != -1) {
            pathname = '/basic/rmanDetail';
        }
        let localStordata: any = localStorage.getItem('nohintpage') || null;
        const nohintpage: any = JSON.parse(localStordata) || [];
        let newdata: any = [...new Set([...nohintpage, pathname])];
        localStorage.setItem('nohintpage', JSON.stringify(newdata));
    }

    // 根据当前页面的路由获取是否有指引
    const getpagecodehelp = (pathname: string) => {
        if (pathname.indexOf('/basic/rmanDetail') != -1) {
            pathname = '/basic/rmanDetail';
        }
        getguidedirectory(pathname).then((res: any) => {
            if (res?.errorCode === 'success') {
                if (res.extendMessage.length) {
                    // 如果有指引，则打开引导弹窗
                    setVisible(true);
                    // 显示当前的图标
                    setShowGuide(true);
                    // 设置当前页面信息
                    setCurrentPage(res.extendMessage[0].newhandGuidePage);
                    // 设置当前页面的所有指引
                    setCurrentPageGuide(res.extendMessage);

                } else {
                    // 如果没有指引
                    setVisible(false);
                    // 隐藏当前的图标
                    setShowGuide(false);
                }

            } else {
                console.log('查询指引失败')
                // message.error('查询指引失败');
            }
        })
    }

    // 动态渲染dom
    const getdoms = (dom: any) => {

        if (dom.isGuide) {
            return (
                <div className='zhiyin' key={dom.id}>
                    <div className='item_view'>
                        <img src={finger} /><span onClick={() => { setAllGuide(dom.items); setOpenImage(true) }}>{dom.name}</span>
                    </div>
                </div>
            )
        } else {
            return (
                <div className="mulu" key={dom.id}>
                    <span className='name_span'>{dom.name}</span>
                    <div className='item_view'>
                        {
                            dom.childrens.map((item: any, index: number) => (
                                <div key={item.id} style={{ display: 'flex', alignItems: 'center' }}>
                                    <img src={finger} /><span onClick={() => { setAllGuide(item.items); setOpenImage(true) }}>{item.name}</span>
                                </div>
                            ))
                        }
                    </div>
                </div>
            )
        }
    }

    const arrowMove = (e: any) => {
        // 元素大小
        let elW = e.currentTarget.offsetWidth;
        let elH = e.currentTarget.offsetHeight;
        // 元素位置
        let elL = e.currentTarget.offsetLeft;
        let elT = e.currentTarget.offsetTop;
        // 鼠标位置
        let x = e.clientX;
        let y = e.clientY;
        // 窗口大小
        let w = window.innerWidth;
        let h = window.innerHeight;
        // 鼠标到元素左边距离
        let moveX = x - elL;
        let moveY = y - elT;
        let el = e.currentTarget;
        document.onmousemove = function (e) {
            isDrag.current = true;
            let right = w - (e.clientX - moveX) - elW;
            let bottom = h - (e.clientY - moveY) - elH;
            // 限制右边和底部的位置不能超出窗口的可视范围
            if (right < 0) {
                right = 0;
            } else if (right > w - elW) {
                right = w - elW;
            }
            if (bottom < 0) {
                bottom = 0;
            } else if (bottom > h - elH) {
                bottom = h - elH;
            }
            el.style.right = right + 'px';
            el.style.bottom = bottom + 'px';
        };
        document.onmouseup = function (e) {
            document.onmousemove = null;
            document.onmouseup = null;

            // 左右自动吸附
            let right = w - (e.clientX - moveX) - elW;
            const distance = w / 2; // 定义距离窗口边缘的距离
            if (right < distance) {
                right = 0;
                el.style.right = right + 'px';
            } else if ((w - right - elW) < distance) {
                right = w - elW;
                el.style.right = right + 'px';
            }

            localStorage.setItem('helpIconPosition', JSON.stringify({
                right: right + 'px',
                bottom: el.style.bottom
            }));

            setTimeout(() => {
                isDrag.current = false;
            }, 300);
        };
    };

    useEffect(() => {
        let helpIconPosition = localStorage.getItem('helpIconPosition');
        if (helpIconPosition) {
            setDragPosition(JSON.parse(helpIconPosition));
            console.log(dragPosition);
        }
    }, []);

    return (
        <div className={`content_view ${parameterConfig.target_customer === 'npu' && target !== 'custom' && "npu-app-container"} ${notShowHeader ? 'hide-header' : ''}`}>
            {showGuide && target !== 'custom' && shareFlag_ !== 'share' ? <div className="help_btn" style={dragPosition ? dragPosition : undefined}
                onMouseDownCapture={arrowMove}
                onClick={() => {
                    // 判断是否按下了鼠标
                    if (isDrag.current == false) {
                        setVisible(true);
                        getpagecodehelp(location.pathname);
                    }
                }}>
                <div className="help_icon">?</div> <div>帮助</div>
            </div> : false}
            {children}
            <Drawer title={currentPage?.name} placement="right" width={350} onClose={onhelpClose} visible={visible} className="helpDrawer_view">
                <div className='desc_view'>
                    <span className='desc_span'>{currentPage?.desc}</span>
                </div>
                <div className='content_box'>
                    {
                        currentPageGuide.map((item: any, index: number) => {
                            return getdoms(item);
                        })
                    }
                </div>
                <div className='noshow'>
                    <Checkbox checked={noshow} onChange={(e: any) => {
                        setNoshow(e.target.checked);
                        localStorage.setItem('noshow', e.target.checked);
                    }}>不再弹出帮助提示</Checkbox>
                </div>
            </Drawer>
            {
                openImage ?
                    <div className='helpperview'>
                        <div className='content_perview'>
                            <img className='imgdom' onClick={() => {
                                if (currentImage < allGuide.length - 1) {
                                    setCurrentImage(currentImage + 1);
                                } else {
                                    setOpenImage(false);
                                    setCurrentImage(0);
                                }
                            }} src={allGuide.length ? allGuide[currentImage]?.imageUrl : ''} />
                            <div className='options_view'>
                                <Button size='large' onClick={() => {
                                    setOpenImage(false);
                                    setCurrentImage(0);
                                }}>跳过{currentImage + 1}/{allGuide.length}</Button>
                                <Button type="primary" size='large' onClick={() => {
                                    if (currentImage > 0) {
                                        setCurrentImage(currentImage - 1);
                                    }
                                }}>上一步</Button>



                                <Button type="primary" size='large' onClick={() => {
                                    if (currentImage < allGuide.length - 1) {
                                        setCurrentImage(currentImage + 1);
                                    } else {
                                        setOpenImage(false);
                                        setCurrentImage(0);
                                    }
                                }}>{currentImage < allGuide.length - 1 ? '下一步' : '完成指引'}</Button>
                            </div>
                        </div>
                    </div>
                    : null
            }
        </div>
    );
};

export default App;
