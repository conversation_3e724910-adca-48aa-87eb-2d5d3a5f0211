import React, { FC, useEffect, useRef, useState } from 'react';
import { asyncLoadScript, convertVttToJson } from '@/utils';
import jQuery from 'jquery';
import contentListApis from '@/service/contentListApis';
import rmanApis from '@/service/rman';
import globalParams from '@/permission/globalParams';
import { IPermission } from '@/models/permission';
import { useSelector, history, useParams } from 'umi';
import './index.less'

export interface Player {
  setTrack: (trackId: any, keyboard: any) => void;
  addKeyPoint: (points: any) => void;
  addButton: any;
  selectedTrack: any;
  tracks: any; //字幕音轨
  captionsButton: any;//字幕按钮
  play: () => void;
  stop: () => void;
  /**
   * 设置播放器的当前时间
   * @param opt 位置
   */
  setCurrentTime: (opt: number) => void;
  /**
   * 获取播放器的当前时间
   * @param opt 位置
   */
  getCurrentTime: () => void;
  /***
   * 设置片段的入点
   * @param frame 帧
   */
  setTrimin: (frame: number) => void;
  /**
   * 设置片段的出点
   * @param frame 帧
   */
  setTrimout: (frame: number) => void;
  /**
   * 获取片段的入点
   */
  getTrimin: () => void;
  /**
   * 获取片段的出点
   */
  getTrimout: () => void;
  /**
   * 清除片段
   */
  cleantrim: () => void;
  /**
   * 获取关键帧
   */
  getCurrentKeyframe: () => string;
  /**
   * 隐藏修改按钮
   */
  hideModifyBtn: (state?:boolean) => void;
  /**
   * 显示修改按钮
   */
  showModifyBtn: () => void;
}

export interface H5PlayerProps {
  src: string;
  id?: string,
  contrast?: number,
  brightness?: number,
  saturate?:number,
  blur?:number,
  grayscale?:number,
  invert?:number,
  opacity?:number,
  sepia?:number,
  hueRotate?:number,
  controlBox?: boolean;
  config?: any;
  frameRate?: number;
  errorCode?: number;
  shareFlag?: boolean;
  translateFlag?: boolean;
  zhFlag?: boolean;
  enFlag?: boolean;
  onSuccess?: (player: Player) => void;
  onError?: (e: any) => void;
  setSection?: (
    media: any,
    img: string,
    trimin: number,
    trimout: number,
  ) => void;
  getKeyframe?: (
    media: any,
    currentTime: any,
    img: any,
  ) => void;
  modifySection?: (
    media: any,
    img: string,
    trimin: number,
    trimout: number,
  ) => void;
  resetSection?: (callback: any) => void;
  onPlayChange?: (point: number) => void;
  isEditPermission?: boolean;
  contendId?: any
  linkWatermark?: string
}

export let defaultFeatures = [
  'playpause',
  'current',
  'progress',
  'duration',
  'tc',
  'tracks',
  'volume',
  'fullscreen',
  'contextmenu',
  'speed',
  'backframe',
  'prevframe',
  'skipback',
  'skipforward',
  'trimin',
  'trimout',
  'cleantrim',
  'seeking',
  //  'toggleKeyPoint',//视频进度条标记
  // 'getKeyframe',
  // 'setSection', //片段导出
  // 'exportSection',
  // 'saveSection',
  // 'togglekeypoint',
  // 'setCatalog',
  'tostart',
  'toend',
  // 'selectScreen',
  // 'ocr',
  // 'getgifdetails',
  // 'selectFile',
  // 'eightTrackPro',
  'volumnTrack',
  'audioVisualizer',
];

const defaultConfig = {
  playType: 'video',
  frameRate: 25,
  pluginPath: '/rman/libs/mam-h5player/dist/',
  features: defaultFeatures,
  disableRangeLock: true,
  /**
   * set 标记
   * @param videoElement
   */
  setTrimin: function (videoElement: any) {
    videoElement.stop();
  },
};
const H5Player: FC<H5PlayerProps> = ({
  src,
  id,
  config = {},
  frameRate,
  onSuccess,
  setSection,
  shareFlag,
  translateFlag,
  onPlayChange,
  modifySection,
  getKeyframe,
  resetSection,
  voice,
  substitles,
  onError,
  isEditPermission = false,
  contendId,
  contrast,
  brightness,
  controlBox,
  saturate,
  blur,
  grayscale,
  invert,
  opacity,
  sepia,
  hueRotate,
  linkWatermark
}) => {
  let player: any;
  const mobileFeatures = [
    'playpause',
    'current',
    'progress',
    'duration',
    'tc',
    'tracks',
    'volume',
    'fullscreen',
    'contextmenu',
    'speed',
    'skipback',
    'skipforward',
    'trimin',
    'trimout',
    'cleantrim',
    'seeking',
    //  'toggleKeyPoint',//视频进度条标记
    // 'getKeyframe',
    // 'setSection', //片段导出
    // 'exportSection',
    // 'saveSection',
    // 'togglekeypoint',
    // 'setCatalog',
    // 'selectScreen',
    // 'ocr',
    // 'getgifdetails',
    // 'selectFile',
    // 'eightTrackPro',
    'volumnTrack',
    'audioVisualizer',
  ];

 let target = history.location?.query?.target || ''
 const params = useParams<{ contentId: string }>();
 const shareFlag_ = params?.contentId?.split('_')[1];
  const { modules,permissions, rmanGlobalParameter } = useSelector<
  { permission: any },
    IPermission
  >(({ permission }) => permission);
  const  {mobileFlag}  = useSelector<{ config: any }, any>(
    ({config})=>config
 );
 const flag = useRef<boolean>(false);
 const [subTitles, setSubtitles] = useState<any>([]);
 const [playerInitFlag, setPlayerInitFlag] = useState<boolean>(false);
 const [watermark, setWatermark] = useState<any>({});
 const [isFullscreen, setIsFullScreen] = useState<boolean>(false);
 const direction = ['lt', 'lb', 'rt', 'rb'] as any
 const userInfo = (window as any).login_useInfo
  const init = async () => {
    if (!player) {
      if(isEditPermission && !location.hash.includes('hidebtn')){
        !defaultFeatures.includes('setSection') && defaultFeatures.push('setSection');
        !mobileFeatures.includes('setSection') && mobileFeatures.push('setSection');
        if(window.localStorage.getItem('upform_platform')==='standard' && rmanGlobalParameter.includes(globalParams.courseware_screenshot_display)){
          !defaultFeatures.includes('getKeyframe') && defaultFeatures.push('getKeyframe');
          !mobileFeatures.includes('getKeyframe') && mobileFeatures.push('getKeyframe');
        }
      }
      if(target === 'chd'){
        defaultFeatures = ['playpause', 'current', 'progress', 'contextmenu', 'duration', 'volume', 'fullscreen']
      }
      if(shareFlag_ == 'share' ){
        defaultFeatures = ['playpause', 'backframe', 'prevframe','tracks', 'contextmenu', 'seeking', 'speed', 'skipback', 'skipforward','volumnTrack','current', 'progress', 'contextmenu', 'duration', 'volume', 'fullscreen']
      }
      player = ($(`#${id}`) as any)?.h5mediaplayer({
        ...defaultConfig,
        frameRate: frameRate,
        features: mobileFlag ? mobileFeatures: defaultFeatures,
        success: (media: any, node: any, player: Player) => {
          if (onSuccess && typeof onSuccess === 'function') {
            onSuccess(player);
            setPlayerInitFlag(true)
          }
        },
        error: (e: any) => {
          if (onError && typeof onError === 'function') {
            onError(e);
          }
        },
        ...config,
        setSection,
        getKeyframe,
        modifySection,
        resetSection,
        // markerColor:'#ff0000',
        // markers:['1','2','3'],
        // markerWidth:1,
        // markerCallback:(media:any, time:any)=>{
        //   console.log(time)
        // }
      });
      (window as any).document
        .getElementById(id)
        .addEventListener('accurateTimeUpdate', function () {
          onPlayChange && onPlayChange(player.getCurrentTime() * 10000000);
        });
      window.onresize = function (e) {
        if (!checkFull()) {
          //要执行的动作
          player.exitFullScreen()
        }
      }
    }
  };
  const loadScripts = async () => {
    await asyncLoadScript('/rman/libs/mam-base/dist/mam-base.js');
    await asyncLoadScript('/rman/libs/mam-h5player/dist/mam-h5player.js');
    return true;
  };
  /**
   * esc退出全屏bug（底部按钮消失问题）
   */
  //判断浏览器是否全屏（需要考虑兼容问题）
  const checkFull = () => {
    const doc = (document as any)
    let isFull = doc.mozFullScreen ||
      doc.fullScreen ||
      //谷歌浏览器及Webkit内核浏览器
      doc.webkitIsFullScreen ||
      doc.webkitRequestFullScreen ||
      doc.mozRequestFullScreen ||
      doc.msFullscreenEnabled
    if (isFull === undefined) {
      isFull = false
    }
    setIsFullScreen(isFull)
    // console.log(isFull, 'isFull')
    return isFull;
  }
  useEffect(() => {
    if(playerInitFlag && !flag.current){
    let video = document.querySelector('.mejs__mediaelement') as HTMLElement
    let startX: number, startY: number, endX, endY;
    let isSelecting = false;
    let selectionBox = '' as any
    video.onmousedown = (e) => {
      console.log(controlBox)
      if(controlBox){
        flag.current = true
        document.getElementById(id as string).style.filter = ''
        e.stopPropagation()
        if (!selectionBox) {
          selectionBox = document.createElement('div')
          selectionBox.className = 'selection_box';
          selectionBox.style.backdropFilter = `saturate(${saturate}%) contrast(${contrast}%) brightness(${brightness}%) blur(${blur}px) grayscale(${grayscale}%) invert(${invert}%) opacity(${opacity}%) sepia(${sepia}%) hue-rotate(${hueRotate}deg)`
          video.appendChild(selectionBox);
        }
        const rect = video.getBoundingClientRect();
        startX = e.clientX - rect.left;
        startY = e.clientY - rect.top;
        selectionBox.style.left = `${startX}px`;
        selectionBox.style.top = `${startY}px`;
        selectionBox.style.width = '0px';
        selectionBox.style.height = '0px';
        isSelecting = true;
        video.onmousemove=(e) => {
          e.stopPropagation()
            if (isSelecting) {
                const rect = video.getBoundingClientRect();
                endX = e.clientX - rect.left;
                endY = e.clientY - rect.top;
                const width = Math.abs(endX - startX);
                const height = Math.abs(endY - startY);
                const left = Math.min(startX, endX);
                const top = Math.min(startY, endY);
                selectionBox.style.left = `${left}px`;
                selectionBox.style.top = `${top}px`;
                selectionBox.style.width = `${width}px`;
                selectionBox.style.height = `${height}px`;
                selectionBox.style.border = '1px solid red'
            }
            video.onmouseup= (e) => {
              e.stopPropagation()
              video.onmousemove = null
              isSelecting = false;
            }
            
        }
      }
    }
    const contextmenu = (event: any) => {
      event.preventDefault(); // 阻止默认的右键菜单显示
        event.stopPropagation(); // 阻止事件冒泡
        selectionBox.parentNode && video.removeChild(selectionBox)
        selectionBox = ''
        document.getElementById(id as string).style.filter =  `saturate(${saturate}%) contrast(${contrast}%) brightness(${brightness}%) blur(${blur}px) grayscale(${grayscale}%) invert(${invert}%) opacity(${opacity}%) sepia(${sepia}%) hue-rotate(${hueRotate}deg)`
    }
    if(controlBox){
      video.addEventListener('contextmenu', contextmenu , false);
    }
    return () => {
      video.removeEventListener('click', contextmenu);
    };
  }
  }, [playerInitFlag, controlBox, saturate,contrast,brightness, blur, grayscale, invert, opacity, sepia, hueRotate])
  useEffect(() => {
    (window as any).$ = (window as any).jQuery = jQuery;
    (window as any).nxt = {};

    loadScripts().finally(() => {
      init();
    });
    
    // 篇头
    contentListApis.getSettingWatermark().then((res)=>{
      setWatermark(res?.extendMessage?.[0] || {})
    })
    
  }, []);
  useEffect(() => {
    if(voice?.length>0){
      const video = document.getElementById(id) as any
      let currentSubtitles = [] as any;      
      video.addEventListener('timeupdate', () => {
          currentSubtitles = [];
            voice.forEach(i => {
              if (video.currentTime * 10000000 >= i._in && video.currentTime * 10000000 <  i._out ) {
                Object.keys(i.textLanguage).forEach(j =>{
                  currentSubtitles.push({ lang: j.split('_')[1], text: i.textLanguage[j] });
                })
              }
            })
          setSubtitles(currentSubtitles)
      });
    } else {
      setSubtitles([])
    }
  }, [voice])
  useEffect(() => {
    if(!isFullscreen){
      dragTrack()
    }
  }, [isFullscreen])
  const dragTrack = ()=>{
    //拖拽：在div上按下鼠标，移动鼠标，移动过程中让div跟随鼠标移动
    const trackDiv:any = document.querySelector('.subtitles');
    trackDiv && trackDiv.addEventListener('mousedown',function(e:any){
      e.stopPropagation()
      const x1 = e.clientX -trackDiv.offsetLeft,y1=e.clientY -trackDiv.offsetTop;
      document.onmousemove=(e_:any)=>{
        e.stopPropagation()
        trackDiv.classList.add('mejs__captions-text_drag');
        const video: any = document.getElementById('h5Player');
        let realX = video.clientWidth,realY=video.clientHeight;
        const maxX = realX - trackDiv.offsetWidth;//X轴可移动最大距离
        const maxY = realY - trackDiv.offsetHeight;//Y轴可移动最大距离
        const x2 = e_.clientX,y2=e_.clientY;
        const moveX=Math.min(maxX, Math.max(0,x2-x1));
        const  moveY=Math.min(maxY, Math.max(0,y2-y1));
        trackDiv.style.left=moveX +'px';
        trackDiv.style.top=moveY +'px';
        trackDiv.style.transform = `translateX(0)`;
        window.onmouseup = ()=> {
          document.onmousemove = null;
          window.onmouseup = null;
        }
      }
    })
  }
  useEffect(() => {
    let selectionBox = document.querySelector('.selection_box') as HTMLElement
    let video = document.getElementById(id as string) as HTMLElement
    if(selectionBox){
      video.style.filter = ''
      selectionBox.style.backdropFilter = `saturate(${saturate}%) contrast(${contrast}%) brightness(${brightness}%) blur(${blur}px) grayscale(${grayscale}%) invert(${invert}%) opacity(${opacity}%) sepia(${sepia}%) hue-rotate(${hueRotate}deg)`
    }
    else{
      video.style.filter =  `saturate(${saturate}%) contrast(${contrast}%) brightness(${brightness}%) blur(${blur}px) grayscale(${grayscale}%) invert(${invert}%) opacity(${opacity}%) sepia(${sepia}%) hue-rotate(${hueRotate}deg)`
    }
  },[saturate,contrast,brightness, blur, grayscale, invert, opacity, sepia, hueRotate])


  return (
    <div
      className='video_container'
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {id === 'h5Player' && <img  src={require('@/images/contentlibrary/select_box.png')} className='selection_box'/>}
      <video
        id={id}
        style={{ width: '100%', height: '100%'}}
        preload="metadata"
        crossOrigin="anonymous"
        src={src}
      >
        {/* {isFullscreen && <track src={`/api/search/webvtt?contentId=${contendId}&voice=true`} kind="subtitles" srcLang="zh" label="简体中文" default />} */}
        {isFullscreen && <track src={`/rman/v1/search/webvtt?contentId=${contendId}&voice=true${shareFlag?'&isSysAuth=true':''}`} kind="subtitles" srcLang="zh" label="简体中文" default />}
        {
          translateFlag && isFullscreen&&
          // <track src={`/api/search/webvtt?contentId=${contendId}&voice=true&lang=en${shareFlag?'&isSysAuth=true':''}`} kind="subtitles" srcLang="en" label="英文" default />
          <track src={`/rman/v1/search/webvtt?contentId=${contendId}&voice=true&lang=en${shareFlag?'&isSysAuth=true':''}`} kind="subtitles" srcLang="en" label="英文" default />
        }
      </video>
      {/* 篇头 */}
      {watermark.watermarkPictureUrl && watermark.watermarkModule.includes(2) &&
      <div className={`${direction[watermark.watermarkLocation]} watermark`}>
      <img src={watermark.watermarkPictureUrl} alt="" />
      </div>}
      {/* 水印 */}
      {
        rmanGlobalParameter.includes(globalParams.watermark_display) && linkWatermark && 
        <div className='watermarkShow'>
          {Array(20).fill(null).map((_, i) => (
            <div key={i}>{linkWatermark}</div>
          ))}
        </div>
      }
      {/* 字幕 */}
        {!isFullscreen &&<div id="subtitles" className="subtitles">
        { subTitles.map((item:any) => (
          substitles.includes(item.lang) && <div key={item.lang} className={`subtitle_${item.lang}`}>{item.text}</div>
        ))}
      </div>}
    </div>
  );
};
export default H5Player;
