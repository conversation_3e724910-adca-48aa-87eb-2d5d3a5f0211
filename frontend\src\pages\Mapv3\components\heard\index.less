.map_heard_view{
    position: absolute;
    width: calc(100% - 12px);
    left: 6px;
    height: 88px;
    background-image: url('/rman/static/images/coursemap/v3/bg1.png');
    background-size: 100% 100%;
    top: 6px;
    display: flex;
    align-items: center;
    z-index: 1;

    .left{
        width: 75%;
        height: 100%;
        display: flex;
        align-items: center;

        .title{
            position: relative;
            width: 21.5%;
            height: 61px;
            background-image: url('/rman/static/images/coursemap/v3/bg2.png');
            background-size: 100% 100%;
            margin-left: 10px;
            display: flex;
            align-items: center;
            
            .icon1{
                width: 22px;
                height: 22px;
                margin-left: 5%;
            }

            .name{
                font-size: 24px;
                font-family: AppleSystemUIFont;
                color: #000;
                margin-left: 15px;
            }

            .icon2{
                position: absolute;
                width: 40px;
                right: 7%;
            }
        }

        .search_view{
            width: 24%;
            height: 31px;
            margin-left: 40px;
            margin-top: -23px;
            display: flex;
            align-items: center;

            .left_select{
                position: relative;
                width: 35%;
                height: 100%;

                .icon3{
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    top: 0;
                    left: 0;
                }

                .ant-select-selector{
                    text-align: center;
                    color: #FFFFFF;
                }

                .ant-select-arrow{
                    color:#fff;
                }

            }

            .right_view{
                position: relative;
                width: 65%;
                height: 100%;
                margin-left: -14px;
                display: flex;
                align-items: center;
                justify-content: flex-end;
                background-image: url('/rman/static/images/coursemap/v3/bg3.png');
                background-size: 100% 100%;

                input{
                    position: absolute;
                    left: 0;
                    width: 80%;
                    height: 100%;
                    // 去除默认样式
                    border: none;
                    outline: none;
                    background-color: transparent;
                    padding-left: 15px;
                    color: #FFFFFF;
                }

                .icon3{
                    width: 16px;
                    height: 16px;
                    margin-right: 15px;
                    cursor: pointer;
                }
            }
        }

        .select1{
            position: relative;
            width: 145px;
            height: 31px;
            display: flex;
            margin-left: 20px;
            align-items: center;
            margin-top: -23px;

            .icon3{
                position: absolute;
                width: 100%;
                height: 100%;
                top: 0;
                left: 0;
            }

            .ant-select-selector{
                text-align: center;
                color: #FFFFFF;
            }

            .ant-select-arrow{
                color:#fff;
            }

        }
    }

    .right{
        width: 25%;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-top: -23px;
        padding-right: 20px;
    }
}