﻿<div class="modal-header">
    <button type="button" class="close" ng-click="close()"><i class="fa fa-times"></i></button>
    <h4 class="modal-title">{{l('upload.upload','上传')}}</h4>
</div>
<div class="modal-body">

    <div class="task-list-box">

        <div class="box-header">
            <div class="box-header-title">{{l('upload.fileToUpload','待上传文件')}}</div>
            <div class="box-header-btns">
                <button class="add" ng-click="addFiles()" title="{{l('upload.addFile','添加文件')}}">
                    <i class="fa fa-plus"></i><span>{{l('upload.addFile','添加文件')}}</span>
                </button>
                <button class="edit" ng-disabled="isDisabled('edit')" ng-click="editMetadatas()" title="{{l('upload.editSelectedFile','编辑元数据')}}">
                    <i class="fa fa-edit"></i><span>{{l('upload.editSelectedFile','编辑元数据')}}</span>
                </button>
                <button class="delete" ng-disabled="isDisabled('del')" ng-click="delete()" title="{{l('upload.deleteSelectedTip','删除选中文件')}}">
                    <i class="fa fa-times"></i><span>{{l('upload.delete','删除')}}</span>
                </button>
                <!--<button class="upload" ng-click="ok()" title="上传全部文件">
                    <i class="fa fa-upload"></i><span>上传</span>
                </button>-->
            </div>
        </div>

        <div class="box-toolbar">
            <div class="box-toolbar-container">
                <label class="mam-checkbox">
                    <input type="checkbox" mam-checkbox mam-all-checkbox ng-model="allSelected" collection="filterTask()"
                        property="selected" />
                    <span>{{l('upload.allSelected','全部选择')}}</span>
                </label>
                <div class="entity-type-filter">
                    <label class="mam-checkbox" ng-repeat="item in entityAllTypes">
                        <input type="checkbox" mam-checkbox ng-model="item.selected" ng-change="queryEntitys()" />
                        <span uib-tooltip="{{item.name}}">{{item.code | entityTypeShortName}}</span>
                    </label>
                </div>
            </div>
        </div>

        <div class="list-list" flex-scrollable>
            <div class="item" ng-repeat="item in filterTask()" ng-class="{'active':current==item,'validate-error':item.metadata.isPass===false}">
                <label class="mam-checkbox">
                    <input type="checkbox" ng-model="item.selected" ng-disabled="editing" mam-checkbox />
                </label>
                <div class="no">
                    {{$index + 1}}.
                </div>
                <div class="type" ng-class="(item.entityType | entityTypeShortName)+'-color'">{{item.entityType |
                    entityTypeShortName}}</div>
                <div class="title">
                    <a ng-if="$index==0" ng-click="changeCurrent(item)" title="{{item.metadata.name}}">{{item.metadata.name|cutString:40}}</a>
                    <a ng-if="$index!=0" ng-click="changeCurrent(item)" title="{{item.metadata.name}}">{{item.metadata.name|cutString:40}}</a>
                </div>
                <div class="op-btns">
                    <button class="btn-icon" title="{{l('upload.delete','删除')}}" ng-click="delete(item)"><i class="fa fa-times"></i></button>
                </div>
            </div>
        </div>

        <div class="mask-layer" ng-show="editing"></div>
    </div>
    <div class="metadata-box">
        <div class="box-header">
            <div class="box-header-title">{{l('upload.baseMetadata','基本元数据')}}</div>
        </div>
        <div class="box-content">
            <div class="metadata-form-wrap" flex-scrollable>
                <mam2-metadata-form ng-if="current != undefined" items="current.metadata.field" type="edit" twoway="true" mmf-right-filter="mmfRightFilter"
                    get-func="getValidateFun(func)" on-programform-change="onProgramformChange(value, oldValue)">
                    <mmf-right ng-if="editing && config.uploadBatchEnable" style="display:inline-flex;align-items:center;height: 30px;margin-left: 6px;">
                        <label style="margin: 0;display: inline-flex;align-items: center;height: 100%;">
                            <input type="checkbox" style="margin:0;" ng-model="rightBatchChange.openOrder" ng-change="rightBatchChange.openOrderChange(rightBatchChange.openOrder,$event)">
                            <span>批量修改</span>
                        </label>
                        <label ng-if="rightBatchChange.openOrder" style="margin: 0;display: inline-flex;align-items: center;height: 100%;">
                            <input type="checkbox" style="margin:0;" ng-model="rightBatchChange.isOrder" ng-change="rightBatchChange.isOrderChange(rightBatchChange.isOrder,$event)">
                            <span>自动编号</span>
                        </label>
                        <input style="width:68px;text-align: center;margin-left: 6px;" ng-if="rightBatchChange.isOrder && rightBatchChange.openOrder"
                            class="form-control" type="text" maxlength="5" ng-value="rightBatchChange.orderNumber" ng-keyup="rightBatchChange.fliterOrderNumber($event)">
                    </mmf-right>
                </mam2-metadata-form>
            </div>
        </div>
        <div class="box-footer">
            <button class="btn btn-primary" ng-click="saveTaskMetadata(item)" ng-show="editing">
                <i class="icon iconfont icon-tianjia"></i><span>{{l('com.save','保存')}}</span>
            </button>
            <button class="btn btn-default" ng-click="exitBulkEdit()" ng-show="editing">
                <i class="icon iconfont icon-icon13"></i>
                <span>{{l('upload.exitBulkEdit','退出批量编辑')}}</span>
            </button>
        </div>
    </div>

</div>

<div class="modal-footer">
    <button class="btn btn-primary" ng-click="ok()" title="{{l('upload.uploadAllFiles','上传全部文件')}}" ng-disabled="!tasks || tasks.length==0">{{l('upload.upload','上传')}}</button>
</div>