import { IReducers } from '@/types/modelsTypes';
import { IConfig } from '@/types/configTypes';

export default {
  namespace: 'uploadConfig',
  state: {
    entityTypes: [
      {
        code: 'video',
        enableCatalogue: true,
        enableExport: true,
        enableSearchTab: true,
        extensions: ['.mp4', '.mov', '.avi', '.wmv'],
        isOther: false,
        keyframe: '/rman/static/images/video.png',
        name: '视频',
        namespace: 'biz_sobey_video',
        shortCode: 'V',
      },
      {
        code: 'picture',
        enableCatalogue: true,
        enableExport: true,
        enableSearchTab: true,
        extensions: ['.jpg', '.png', '.gif', '.jpeg'],
        isOther: false,
        keyframe: '/rman/static/images/picture.png',
        name: '图片',
        namespace: 'biz_sobey_picture',
        shortCode: 'P',
      },
      {
        code: 'audio',
        enableCatalogue: true,
        enableExport: true,
        enableSearchTab: true,
        extensions: ['.mp3', '.wav', '.wma'],
        isOther: false,
        keyframe: '/rman/static/images/audio.png',
        name: '音频',
        namespace: 'biz_sobey_audio',
        shortCode: 'A',
      },
      {
        code: 'document',
        enableCatalogue: true,
        enableExport: true,
        enableSearchTab: true,
        extensions: ['.pptx', '.doc', '.pdf', '.excel'],
        isOther: false,
        keyframe: '/rman/static/images/document.png',
        name: '文档',
        namespace: 'biz_sobey_document',
        shortCode: 'D',
      },
      {
        code: 'other',
        enableCatalogue: true,
        enableExport: true,
        enableSearchTab: true,
        extensions: ['.zip,.rar,.7z,.tar,.tar,.gz'],
        isOther: true,
        keyframe: '/rman/static/images/other.png',
        name: '其他',
        namespace: 'biz_sobey_other',
        shortCode: 'O',
      }
    ],
    loginUrl: '#/login',
    isHandleHttpPath: true,
    server: '/ff',
    webUploadThreads: 1,
    webUploadMd5Enable: true,
    ossUpCallbackUrl: '',
    vtubeInfo: {
      address: '',
      importType: 1,
      password: '',
      path: '/',
      port: 21,
      usedConfig: true,
      userName: '',
    },
    vtubeDownloadPath: '/bucket-k/resource/Sobey_vRocket_v2.0_Setup.exe',
  },
  reducers: {
    changeShowLoading: (
      state: IConfig,
      { payload: data }: IReducers<IConfig>,
    ) => {
      return data.value;
    },
    updateEntityTypes: (state: IConfig, { payload }: any) => {
      return {
        ...state,
        entityTypes: payload,
      };
    },
  },
};
