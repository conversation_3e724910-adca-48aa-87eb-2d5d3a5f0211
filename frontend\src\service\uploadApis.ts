import http from '@/http/http';
import { IFormItem } from '@/types/entityTypes';

namespace uploadApis {
  export const getALLFields = (source: string = 'webupload') => {
    return http(`/old/upload/get-all-fields-by-source?source=${source}`);
  };
  export const getuploadFormat = (type?: string) => {
    return http(`/upload/format`, {
      method: 'GET',
      params: {
        type,
      },
    });
  };
  export const initiateFaceImage = (filePath?: string) => {
    return http(`/rman/v1/intelligent/initiate/face-image`, {
      method: 'GET',
      params: {
        filePath,
      },
    });
  };
  export const getFaceImage = (taskGuid?: string) => {
    return http(`/rman/v1/intelligent/face-image`, {
      method: 'GET',
      params: {
        taskGuid,
      },
    });
  };
  export const uploadImport = (
    folderPath: string,
    filePath: string,
    uploadMetas: IFormItem[],
    fileLength?: number,
    extraParam?:any[]
  ) => {
    return http<boolean>('/upload/import', {
      method: 'POST',
      data: {
        folderPath,
        filePath,
        uploadMetas,
        fileLength,
        extraParam
      },
    });
  };
  export const uploadRecordingImport = (
    folderPath: string,
    filePath: string,
    uploadMetas: IFormItem[],
    fileLength?: number,
    extraParam?:any[]
  ) => {
    return http<boolean>('/upload/recording/video/import', {
      method: 'POST',
      data: {
        folderPath,
        filePath,
        uploadMetas,
        fileLength,
        extraParam
      },
    });
  };
  
  export const filemerge = (guid: string, fileName: string,fileGuid:string) => {
    return http<string>('/upload/filemerge', {
      method: 'POST',
      data: {
        guid,
        fileName,
        fileGuid
      },
    });
  };
  export const filesave = (data: any) => {
    return http<string>('/upload/filesave', {
      method: 'POST',
      headers: {
        "Content-Type": "multipart/form-data", 
      },
      data
    });
  };

  /**
   * 查询合并进度
   * @param fileGuid
   */
  export const fetchMergeStatus = (fileGuid: string) =>
    http<{
      state: number;
      errorMsg: string;
      finalFilePath: string;
    }>(`/upload/get/composite/task/details/${fileGuid}`);
  /**
   * 删除合成任务
   * @param fileGuid
   */
  export const deleteMergeTask = (fileGuid: string) =>
    http(`/upload/delete/composite/task/${fileGuid}`, {
      method: 'DELETE',
    });
  /**
   * 取消文件任务
   * @param guid
   */
  export const cancelFileTask = (guid: string) => {
    return http(`/upload/delete/fragment/file`, {
      method: 'POST',
      params: {
        guid,
      },
    });
  };
  /**
   * 上传判定（防止个人空间不足）
   * @param params
   */
  export const beforeUpload = (params: any) => {
    return http(`/rman/v1/upload/path`, {
      method: 'GET',
      params
    });
  };
  /**
   * OOS配置
   * @param data
   */
  export const storageConfig =async (data: any) => {
    return http(`/upload/v4/path`, {
      method: 'POST',
      data
    });
  };
  export const storageGroupConfig =async (data: any) => {
    return http(`/rman/v1/group/storage/path`, {
      method: 'POST',
      data
    });
  };
  /**
   * 获取新的token；
   * @param params
   */
  export const getStsToken = (param?:string) => {
    return http(`/rman/v1/upload/oss/security/token?product=${param}`, {
      method: 'GET',
    });
  };

  export const uploadRermissions = (params?: any) => {
    return http(`/rman/v1/folder/upload/rermissions`, {
      method: 'GET',
      params
    });
  };
  
  export const getSinedUrl = (data: any) => {
    return http(`/upload/oss/security/signedurl`, {
      method: 'POST',
      data
    });
  };

  export const sendChunk = (url: string, data: any) => {
    return http(url, {
      method: 'PUT',
      data
    });
  };

  export const getAllFieldsByType = (type: string) => {
    return http<IFormItem[]>(
      `/metadata/config/fields/upload?EntityType=${type}&Type=basic&ResourceType=model_sobey_object_entity`,
    );
  };
  
  export const getReplacementVideo = (data: any) => {
    return http(
      `/rman/v1/upload/recorded/video/import`,
      {
        method: 'POST',
        data
      }
    );
  };
}

export default uploadApis;
