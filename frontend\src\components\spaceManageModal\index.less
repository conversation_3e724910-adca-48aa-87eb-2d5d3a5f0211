.spaceManageModal{
  .ant-modal-content{
    .ant-modal-body{
      padding: 10px 30px;
      display: flex;
      .left{
        min-width: 210px;
        white-space: nowrap;
        height: 350px;
        >div{
          margin-bottom: 30px;
          &:nth-child(5){
            display: flex;
            span{
              flex: 1;
              width: 0;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
        
      }
      padding: 10px 24px;
      >.split{
        width: 1px;
        flex: none;
        margin: 0 10px;
        border-left:1px dashed #00000050;
       }
      .right_refuse{
        flex: 1 1;
        display: flex;
        >span{
          white-space: nowrap;
        }
        textarea{
          height: 200px;
          resize: none;
        }
      }
      .right{
        flex: 1 1;
        .current{
          display: flex;
          justify-content: space-between;
          .tips{
            color:red;
          }
        }
        .ant-tabs{
          .ant-tabs-nav-list{
            width: 100%;
            justify-content: space-between;
           .ant-tabs-tab{
            padding: 5px;
            .ant-tabs-tab-btn{
              >.tab-title{
                display: flex;
                // flex-direction: column;
                align-items: center;
              }
            }
           } 
          }
          .ant-tabs-content-holder{
            .ant-table-wrapper{
              .ant-table{
                .ant-table-content{
                 .ant-table-tbody{
                  tr{
                    >td{
                      padding: 12px 0;
                    }
                  }
                 } 
                }
              }
            }
            .bucket_div{
              display: flex;
              align-items: center;
              >.label{
                display: inline-block;
                width: 200px;
                overflow: hidden;
                text-overflow: ellipsis;
                text-align: right;
              }
              .ant-checkbox-wrapper{
                padding-left: 8px;
              }
              .ant-input{
                width: 90px;
              }
            }
          }
        }
        .refuseDiv{
          width: 50%;
          height: 200px;
          margin: 0 auto;
          >div{
            margin-top: 20px;
          }
          .ant-input-textarea{
            height: 100%;
            .ant-input-affix-wrapper{
              height: 100%;
              textarea{
                resize: none;
              }
            }
          }
        }
      }
      
    }
  }
}
body>.row-dragging{
  z-index: 9999999;
  font-size: 14px;
  display: flex;
  align-items: center;
  .bucket_div{
    display: flex;
    align-items: center;
    >.label{
      display: inline-block;
      width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      text-align: right;
    }
    .ant-checkbox-wrapper{
      padding-left: 8px;
    }
    .ant-input{
      width: 90px;
    }
  }
}