import React, { <PERSON> } from 'react';
import { IBaseEntityTypes } from '@/types/entityTypes';

interface IPDFProps extends IBaseEntityTypes {}

const PDF: FC<IPDFProps> = ({ src, onError }) => {
  return (
    <div className="entity-pdf">
      <iframe
        src={`/pdfview/?file=${src}`}
        onError={onError}
        width={'100%'}
        height={'100%'}
      />
    </div>
  );
};
export default PDF;
