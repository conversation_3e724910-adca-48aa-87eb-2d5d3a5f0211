import React, { useEffect, useRef, useState } from 'react';
import G6 from '@antv/g6';
import { Button, Spin, Empty } from 'antd';
import { ZoomInOutlined, ZoomOutOutlined, DownloadOutlined } from '@ant-design/icons';
import './index.less';

interface MindMapProps {
    data: any;
}

const { Util } = G6;
const MindMap: React.FC<MindMapProps> = (props) => {
    const { data } = props
    const containerRef = useRef<HTMLDivElement>(null);
    const graphRef = useRef<any>(null);
    const [spinning, setSpinning] = useState(false);

    // 放大
    const handleZoomIn = () => {
        if (graphRef.current) {
            const currentZoom = graphRef.current.getZoom();
            graphRef.current.zoomTo(currentZoom * 1.2);
        }
    };

    // 缩小
    const handleZoomOut = () => {
        if (graphRef.current) {
            const currentZoom = graphRef.current.getZoom();
            graphRef.current.zoomTo(currentZoom * 0.8);
        }
    };
    // 获取完整导图尺寸
    const getFullGraphSize = () => {
        if (!graphRef.current) return { width: 0, height: 0 };
        const nodes = graphRef.current.getNodes();
        let minX = Infinity;
        let maxX = -Infinity;
        let minY = Infinity;
        let maxY = -Infinity;

        nodes.forEach((node: any) => {
            const bbox = node.getBBox();
            minX = Math.min(minX, bbox.minX);
            maxX = Math.max(maxX, bbox.maxX);
            minY = Math.min(minY, bbox.minY);
            maxY = Math.max(maxY, bbox.maxY);
        });

        return {
            width: maxX - minX + 1000, // 添加边距
            height: maxY - minY + 1000
        };
    };

    // 下载完整导图
    const handleDownload = () => {
        if (graphRef.current) {
            setSpinning(true);

            // 保存当前视口状态
            const currentZoom = graphRef.current.getZoom();
            const currentMatrix = graphRef.current.get('group').getMatrix();

            // 获取完整图形尺寸
            const { width, height } = getFullGraphSize();

            // 临时调整画布大小
            const canvas = graphRef.current.get('canvas');
            const originalWidth = graphRef.current.get('width');
            const originalHeight = graphRef.current.get('height');

            graphRef.current.changeSize(width, height);

            // 监听布局完成事件
            graphRef.current.on('afterlayout', () => {
                graphRef.current.off('afterlayout'); // 移除监听
                graphRef.current.fitView();

                // 重试机制，确保图形完全渲染
                const MAX_RETRIES = 5;
                let retries = 0;

                const tryDraw = () => {
                    // 创建临时canvas并设置白色背景
                    const tempCanvas = document.createElement('canvas');
                    tempCanvas.width = width;
                    tempCanvas.height = height;
                    const ctx: any = tempCanvas.getContext('2d');

                    // 填充白色背景
                    ctx.fillStyle = '#ffffff';
                    ctx.fillRect(0, 0, width, height);

                    // 将原始canvas内容绘制到临时canvas
                    ctx.drawImage(canvas.get('el'), 0, 0);

                    // 检查绘制的内容是否完整
                    const imageData = ctx.getImageData(0, 0, width, height);
                    const data = imageData.data;
                    let hasContent = false;
                    for (let i = 0; i < data.length; i += 4) {
                        if (data[i + 3] > 0) {
                            hasContent = true;
                            break;
                        }
                    }

                    if (hasContent || retries >= MAX_RETRIES) {
                        const dataUrl = tempCanvas.toDataURL('image/png');
                        const link = document.createElement('a');
                        link.download = (props?.data?.children[0]?.label || 'mindmap') + '.png';
                        link.href = dataUrl;

                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);

                        // 恢复原始状态
                        graphRef.current.changeSize(originalWidth, originalHeight);
                        graphRef.current.zoomTo(currentZoom);
                        graphRef.current.get('group').setMatrix(currentMatrix);
                        graphRef.current.fitView();

                        setSpinning(false);
                    } else {
                        retries++;
                        requestAnimationFrame(tryDraw);
                    }
                };

                requestAnimationFrame(tryDraw);
            });

            graphRef.current.layout(); // 触发布局
        }
    };

    // 根节点
    G6.registerNode(
        'dice-mind-map-root', {
        jsx: (cfg: any) => {
            const width = Util.getTextSize(cfg.label, 16)[0] + 24;
            const stroke = cfg.style.stroke || '#096dd9';
            const fill = cfg.style.fill;

            return ` <group>
                <rect draggable="true" keyshape>
                    <text style={{ fontSize: 20, marginLeft: 12, marginTop: 12}}>${cfg.label}</text>
                    <text style={{ marginLeft: ${width - 16}, marginTop: -20, stroke: '#66ccff', fill: '#000', cursor: 'pointer', opacity: ${cfg.hover ? 0.75 : 0
                } }} action="add">+</text>
                </rect>
                </group>
            `;
        },
        getAnchorPoints() {
            return [
                [0, 0.5],
                [1, 0.5],
            ];
        },
    },
        'single-node',
    );

    // 子节点
    G6.registerNode(
        'dice-mind-map-sub', {
        jsx: (cfg: any) => {
            const width = Util.getTextSize(cfg.label, 14)[0] + 24;
            const color = cfg.color || cfg.style.stroke;
            return `
        <group>
          <rect draggable="true" style={{width: ${width + 24}, height: 22}} keyshape>
            <text draggable="true" style={{ fontSize: 18, marginLeft: 12, marginTop: 6}}>${cfg.label
                }</text>
            <text style={{ marginLeft: ${width - 8
                }, marginTop: -10, stroke: ${color}, fill: '#000', cursor: 'pointer', opacity: ${cfg.hover ? 0.75 : 0
                }, next: 'inline' }} action="add">+</text>
            <text style={{ marginLeft: ${width - 4
                }, marginTop: -10, stroke: ${color}, fill: '#000', cursor: 'pointer', opacity: ${cfg.hover ? 0.75 : 0
                }, next: 'inline' }} action="delete">-</text>
          </rect>
          <rect style={{ fill: ${color}, width: ${width + 24}, height: 2, x: 0, y: 22 }} />
          
        </group>
      `;
        },
        getAnchorPoints() {
            return [
                [0, 0.965],
                [1, 0.965],
            ];
        },
    },
        'single-node',
    );

    // 叶子节点
    G6.registerNode(
        'dice-mind-map-leaf', {
        jsx: (cfg: any) => {
            const width = Util.getTextSize(cfg.label, 12)[0] + 24;
            const color = cfg.color || cfg.style.stroke;

            return `
        <group>
          <rect draggable="true" style={{width: ${width + 20}, height: 26, fill: 'transparent' }}>
            <text style={{ fontSize: 14, marginLeft: 12, marginTop: 6 }}>${cfg.label}</text>
                <text style={{ marginLeft: ${width - 8
                }, marginTop: -10, stroke: ${color}, fill: '#000', cursor: 'pointer', opacity: ${cfg.hover ? 0.75 : 0
                }, next: 'inline' }} action="add">+</text>
                <text style={{ marginLeft: ${width - 4
                }, marginTop: -10, stroke: ${color}, fill: '#000', cursor: 'pointer', opacity: ${cfg.hover ? 0.75 : 0
                }, next: 'inline' }} action="delete">-</text>
          </rect>
          <rect style={{ fill: ${color}, width: ${width + 24}, height: 2, x: 0, y: 32 }} />
          
        </group>
      `;
        },
        getAnchorPoints() {
            return [
                [0, 0.965],
                [1, 0.965],
            ];
        },
    },
        'single-node',
    );

    // 点击事件
    // G6.registerBehavior('dice-mindmap', {
    //     getEvents() {
    //         return {
    //             'node:click': 'clickNode',
    //             'node:dblclick': 'editNode',
    //             'node:mouseenter': 'hoverNode',
    //             'node:mouseleave': 'hoverNodeOut',
    //         };
    //     },
    //     clickNode(evt: any) {
    //         const model = evt.item.get('model');
    //         const name = evt.target.get('action');
    //         switch (name) {
    //             case 'add':
    //                 const newId =
    //                     model.id +
    //                     '-' +
    //                     (((model.children || []).reduce((a: any, b: any) => {
    //                         const num = Number(b.id.split('-').pop());
    //                         return a < num ? num : a;
    //                     }, 0) || 0) +
    //                         1);
    //                 evt.currentTarget.updateItem(evt.item, {
    //                     children: (model.children || []).concat([{
    //                         id: newId,
    //                         direction: newId.charCodeAt(newId.length - 1) % 2 === 0 ? 'right' : 'left',
    //                         label: 'New',
    //                         type: 'dice-mind-map-leaf',
    //                         color: model.color || colorArr[Math.floor(Math.random() * colorArr.length)],
    //                     },]),
    //                 });
    //                 evt.currentTarget.layout(false);
    //                 break;
    //             case 'delete':
    //                 const parent = evt.item.get('parent');
    //                 evt.currentTarget.updateItem(parent, {
    //                     children: (parent.get('model').children || []).filter((e: any) => e.id !== model.id),
    //                 });
    //                 evt.currentTarget.layout(false);
    //                 break;
    //             case 'edit':
    //                 break;
    //             default:
    //                 return;
    //         }
    //     },
    //     editNode(evt: any) {
    //         const item = evt.item;
    //         const model = item.get('model');
    //         const {
    //             x,
    //             y
    //         } = item.calculateBBox();
    //         const graph = evt.currentTarget;
    //         const realPosition = evt.currentTarget.getClientByPoint(x, y);
    //         const el = document.createElement('div');
    //         const fontSizeMap = {
    //             'dice-mind-map-root': 24,
    //             'dice-mind-map-sub': 18,
    //             'dice-mind-map-leaf': 16,
    //         };
    //         el.style.fontSize = fontSizeMap[model.type as keyof typeof fontSizeMap] + 'px';
    //         el.style.position = 'fixed';
    //         el.style.top = realPosition.y + 'px';
    //         el.style.left = realPosition.x + 'px';
    //         el.style.paddingLeft = '12px';
    //         el.style.transformOrigin = 'top left';
    //         el.style.transform = `scale(${evt.currentTarget.getZoom()})`;
    //         const input = document.createElement('input');
    //         input.style.border = 'none';
    //         input.value = model.label;
    //         input.style.width = Util.getTextSize(model.label, fontSizeMap[model.type as keyof typeof fontSizeMap])[0] + 'px';
    //         input.className = 'dice-input';
    //         el.className = 'dice-input';
    //         el.appendChild(input);
    //         document.body.appendChild(el);
    //         const destroyEl = () => {
    //             document.body.removeChild(el);
    //         };
    //         const clickEvt = (event: any) => {
    //             if (!(event.target && event.target.className && event.target.className.includes('dice-input'))) {
    //                 window.removeEventListener('mousedown', clickEvt);
    //                 window.removeEventListener('scroll', clickEvt);
    //                 graph.updateItem(item, {
    //                     label: input.value,
    //                 });
    //                 graph.layout(false);
    //                 graph.off('wheelZoom', clickEvt);
    //                 destroyEl();
    //             }
    //         };
    //         graph.on('wheelZoom', clickEvt);
    //         window.addEventListener('mousedown', clickEvt);
    //         window.addEventListener('scroll', clickEvt);
    //         input.addEventListener('keyup', (event) => {
    //             if (event.key === 'Enter') {
    //                 clickEvt({
    //                     target: {},
    //                 });
    //             }
    //         });
    //     },
    //     hoverNode(evt: any) {
    //         evt.currentTarget.updateItem(evt.item, {
    //             hover: true,
    //         });
    //     },
    //     hoverNodeOut(evt: any) {
    //         evt.currentTarget.updateItem(evt.item, {
    //             hover: false,
    //         });
    //     },
    // });

    // 滚动事件
    G6.registerBehavior('scroll-canvas', {
        getEvents: function getEvents() {
            return {
                wheel: 'onWheel',
            };
        },

        onWheel: function onWheel(ev: any) {
            const graph: any = this.graph;
            if (!graph) {
                return;
            }
            if (ev.ctrlKey) {
                const canvas = graph.get('canvas');
                const point = canvas.getPointByClient(ev.clientX, ev.clientY);
                let ratio = graph.getZoom();
                if (ev.wheelDelta > 0) {
                    ratio += ratio * 0.05;
                } else {
                    ratio *= ratio * 0.05;
                }
                graph.zoomTo(ratio, {
                    x: point.x,
                    y: point.y,
                });
            } else {
                const x = ev.deltaX || ev.movementX;
                const y = ev.deltaY || ev.movementY || (-ev.wheelDelta * 125) / 3;
                graph.translate(-x, -y);
            }
            ev.preventDefault();
        },
    });

    const dataTransform = (data: any) => {
        const changeData = (d: any, level = 0, color?: any) => {
            const data = {
                ...d,
            };
            switch (level) {
                case 0:
                    data.type = 'dice-mind-map-root';
                    break;
                case 1:
                    data.type = 'dice-mind-map-sub';
                    // 根据节点类型设置不同颜色
                    data.color = d.label === '知识要点' ? '#F6A353' : '#F94740';
                    // 强制所有子节点方向朝右
                    data.direction = 'right'; 
                    break;
                default:
                    data.type = 'dice-mind-map-leaf';
                    // 子节点继承父节点的颜色
                    data.color = color;
                    // 强制所有子节点方向朝右
                    data.direction = 'right'; 
                    break;
            }

            data.hover = false;

            if (d.children) {
                data.children = d.children.map((child: any) => changeData(child, level + 1, data.color));
            }
            return data;
        };
        return changeData(data);
    };

    useEffect(() => {
        if (!containerRef.current || !data) return;

        const container = containerRef.current;
        const width = container.scrollWidth;
        const height = (container.scrollHeight || 500) - 20;
        const tree = new G6.TreeGraph({
            container: container,
            width,
            height,
            fitView: false, // 关闭自动适配视图
            minZoom: 0.3,
            maxZoom: 5,
            layout: {
                type: 'mindmap',
                direction: 'H',
                getHeight: () => {
                    return 16;
                },
                getWidth: (node: any) => {
                    return node.level === 0 ?
                        Util.getTextSize(node.label, 16)[0] + 12 :
                        Util.getTextSize(node.label, 12)[0];
                },
                getVGap: () => {
                    return 10;
                },
                getHGap: () => {
                    return 60;
                },
                getSide: (node: any) => {
                    return node.data.direction;
                },
            },
            defaultEdge: {
                type: 'cubic-horizontal',
                style: {
                    lineWidth: 2,
                },
            },
            modes: {
                default: ['drag-canvas', 'zoom-canvas', 'dice-mindmap'],
            },
        });
        graphRef.current = tree; // 保存图实例的引用
        tree.data(dataTransform(data));
        tree.render();

        // 尽可能缩小页面
        tree.zoomTo(tree.get('minZoom')); 
        tree.fitView();

        return () => {
            tree.destroy();
        };
    }, [data]);

    useEffect(() => {
        if (!containerRef.current || !data) return;
        const container = containerRef.current;
        const height = (container.scrollHeight || 500) - 20;
        const resizeObserver = new ResizeObserver(entries => {
            for (let entry of entries) {
                if (graphRef.current) {
                    const newWidth = entry.contentRect.width;
                    graphRef.current.changeSize(newWidth, height);
                    graphRef.current.fitView();
                }
            }
        });

        resizeObserver.observe(container);
        return () => {
            resizeObserver.disconnect();
        };
    }, [data])

    return <div className='mindmap-container'>
        <Spin spinning={spinning} tip='加载中...'>
            {data && Object.keys(data).length > 0 ? <>
                <div className='mindmap-control' >
                    <Button title='下载' icon={<DownloadOutlined />} onClick={handleDownload}>
                    </Button>
                    <Button title='放大' icon={<ZoomInOutlined />} onClick={handleZoomIn}>
                    </Button>
                    <Button title='缩小' icon={<ZoomOutOutlined />} onClick={handleZoomOut}>
                    </Button>
                </div>
                <div id="container" ref={containerRef} />
            </> : <Empty />}
        </Spin>
    </div>;
};

export default MindMap;


