import http from '../http/http';
import { IEntityRes, IEntityMetadata, IFormItem } from '@/types/entityTypes';

namespace entityApis {
  export const getEntity = (
    contentId: string,
    link = false,
    shareFlag_ = false,
  ) => {
    let url = `/entity/base/${contentId}`;
    if (shareFlag_) {
      url = `/entity/base/${contentId}&isSysAuth=true`;
    } else {
      // if (
      //   navigator.userAgent.match(/Mobi/i) ||
      //   navigator.userAgent.match(/Android/i) ||
      //   navigator.userAgent.match(/iPhone/i) ||
      //   window.innerWidth < 768
      // ){
      //   url = `/entity/base/${contentId}`;
      // } else{
      //   url = `/entity/base/${contentId}`;
      // }
      url = `/entity/base/${contentId}`;
    }
    return http<IEntityRes>(
      `${link}` ? url :  (contentId.includes('isSysAuth') ? url : `/entity/base/${contentId}?isSysAuth=true`),
      {
        method: 'GET',
      },
    );
  };
  export const checksharepublish = (
    contentId: string,
    link = false,
    shareFlag_ = false,
  ) => {
    let url = '';
    if (shareFlag_) {
      url = `/entity/base/checksharepublish/${contentId}`;
    } else {
      if (
        navigator.userAgent.match(/Mobi/i) ||
        navigator.userAgent.match(/Android/i) ||
        navigator.userAgent.match(/iPhone/i) ||
        window.innerWidth < 768
      ) {
        url = `/entity/base/checksharepublish/${contentId}?isSysAuth=true`;
      } else {
        url = `/entity/base/checksharepublish/${contentId}`;
      }
    }

    return http<IEntityRes>(
      `${link}` ? url : `/entity/base/checksharepublish/${contentId}?isSysAuth=true`,
      {
        method: 'GET',
      },
    );
  };
  export const getEntityMetadata = (contentId: string) => {
    return http<IFormItem[]>(`/entity/entitydata/${contentId}?isSysAuth=true`, {
      method: 'GET',
    });
  };

  export const setEntityMetadata = (
    contentId: string,
    data: { oldValue: IFormItem[]; newValue: IFormItem[] },
    isPublic?: boolean,
  ) => {
    return http<boolean>(`/entity/entitydata/${contentId}`, {
      method: 'PATCH',
      params: {
        isPublic: isPublic,
      },
      data,
    });
  };
  export const getAllFieldsByType = (type: string) => {
    return http<IFormItem[]>(
      `/metadata/config/fields/upload?EntityType=${type}&Type=basic&ResourceType=model_sobey_object_entity`,
    );
  };

  export const getExif = (contentId: string, flag?: string) => {
    return http<IFormItem[]>(
      `/entity/picture/${contentId}/exif${flag}?isSysAuth=true`,
    );
  };
  export const saveCourse = (data: any) =>
    http('/learn/v1/course/create', {
      method: 'POST',
      data,
    });
  export const getDetail = (data: any) =>
    http('/rman/v1/search/entity', {
      method: 'POST',
      data,
    });
  export const getCoverList = () =>
    http('/learn/v1/course/cover', { method: 'GET' });

  //视频播放记忆
  export const getVideoHistory = (params: any) => {
    return http('/learn/v1/video/history', {
      method: 'GET',
      params,
    });
  };

  // 获取资源问答进度等信息
  export const setResoureProcess = (params: any) => {
    return http('/terminator/api/v1/application/chat/resource_precheck', {
      method: 'POST',
      data: params,
    });
  };

  // 获取资源问答进度等信息
  export const getResoureProcess = (params: any) => {
    return http('/terminator/api/v1/application/chat/resource_precheck', {
      method: 'GET',
      params,
    });
  };

  // 获取frameID
  export const reqQaList = (params: any) => {
    return http('/terminator/api/v1/application/chat', {
      method: 'GET',
      params,
    });
  };

  //保存
  export const setVideoHistory = (data: any) => {
    return http('/learn/v1/video/history', {
      method: 'POST',
      data,
    });
  };
  //更新
  export const updateVideoHistory = (params: any) => {
    //     var xhr = new XMLHttpRequest();
    //     xhr.open('GET',`/learn/v1/video/history/update?id=${params.id}&watchTime=${params.watchTime}`)
    //     xhr.send();
    //     xhr.onreadystatechange = function(){
    //       return xhr
    // 　　};
    return http('/learn/v1/video/history/update', {
      method: 'GET',
      params,
    });
  };
  //新增课件（截图）
  export const setKeyframe = (data: any) => {
    return http('/rman/v1/entity/sendkeyframe', {
      method: 'POST',
      data: {
        ...data,
        isScreenshot: true,
      },
    });
  };
  //获取课件（截图）
  export const getKeyframes = (params: any, flag?: any) => {
    return http(`/rman/v1/entity/file/screenshot${flag}`, {
      method: 'GET',
      params
    });
  };
  //删除课件（截图）
  export const deleteKeyframes = (data: any) => {
    return http(`/rman/v1/entity/delete/frame`, {
      method: 'POST',
      data,
    });
  };
  //更新封面
  export const updateKeyframes = (contentId: string, data: any) => {
    return http(`/rman/v1/entity/keyframe/update?contentId=${contentId}`, {
      method: 'POST',
      data,
    });
  };
  //素材点赞
  export const resourceLike = (contentId: string) => {
    return http(`/rman/v1/entity/like?contentId=${contentId}`, {
      method: 'POST',
    });
  };
  //素材取消点赞
  export const resourceCancleLike = (contentId: string) => {
    return http(`/rman/v1/entity/cancel/like?contentId=${contentId}`, {
      method: 'POST',
    });
  };
  //素材操作行为统计：like（点赞）、 collect（收藏）、download（下载）、share（分享）、copy（复制）、micr（微课）、knowledge（知识点绑定）
  export const resourceOptionsCount = (
    contentId: any,
    behavioralType: string,
  ) => {
    const temp = Array.isArray(contentId) ? contentId : [contentId];
    return http(`/rman/v1/behavioral/add/batch`, {
      method: 'POST',
      params: {
        behavioralType,
      },
      data: temp,
    });
  };
  //添加jove剪辑日志
  export const joveoneLog = (contentId: any, resourceName: string) => {
    return http(`/rman/v1/operation/log/jove`, {
      method: 'POST',
      params: {
        contentId,
        resourceName,
      },
    });
  };
  //素材智能小结查询
  export const getIntelligentSummary = (contentId: string) => {
    return http(`/learn/resource/analysis/get/${contentId}`, {
      method: 'GET',
    });
  };
  //素材智能小结新增
  export const addIntelligentSummary = (data: any) => {
    return http(`/learn/resource/analysis/add`, {
      method: 'POST',
      data,
    });
  };
  //素材智能小结删除
  export const deleteIntelligentSummary = (contentId: string) => {
    return http(`/learn/resource/analysis/delete/${contentId}`, {
      method: 'GET',
    });
  };

  //素材操作日志
  export const resourcelogs = (params: any) => {
    return http(`/rman/v1/operation/log/resource`, {
      method: 'GET',
      params,
    });
  };

  // 获取操作日志类型
  export const getlogtype = () => {
    return http(`/rman/v1/operation/log/type`, {
      method: 'GET',
    });
  };

  // 获取素材审核日志
  export const getAuditLog = (params: any) => {
    return http(`/unifiedplatform/v1/audit/log`, {
      method: 'GET',
      params,
    });
  };
  // 下载日志
  export const downloadlogs = (params: any) => {
    return http(`/rman/v1/operation/log/download`, {
      method: 'POST',
      params,
    });
  };
  export const getIntelligentObject = (params: any) => {
    return http(`/rman/v1/intelligent/object/group`, {
      method: 'GET',
      params
    });
  }
  export const getIntelligentPeople = (params: any) => {
    return http(`/rman/v1/intelligent/people/identified`, {
      method: 'GET',
      params
    });
  }
  export const getIntelligentFace = (params: any) => {
    return http(`/rman/v1/intelligent/face`, {
      method: 'GET',
      params
    });
  }
  export const querySensitiveWord = (detectionText: string) => {
    return http(`/sensitiveword/match`, {
      method: 'GET',
      params: {detectionText}
    });
  }
}

export default entityApis;
