// 默认的节点data
export const defaultNodeData: any = {
  isCollapsed: true, //是否展开
  isedit: false,//是否编辑
  isyinandian: false,//是否是重难点
  explanation: '',//详解
  bindresource: [],//绑定的资源
  isDiscriminate: false,// 是否有对比辨析
  quoteKnowledge: [],//引用的知识点
  bindlink: [],//绑定的外部链接
  referenceMaterials: [],//参考资料
  homework: [] //作业
}

export const MapConfig = {
  marjor: {
    fontSize: 20,
    size: [108, 108],
  }, //专业
  course: {
    fontSize: 18,
    size: [71, 71],
  },//课程
  fenlei: {
    fontSize: 16,
    size: [45, 45],
  },//分类节点
  knowledge: {
    fontSize: 14,
    size: [25, 25],
  },//知识点
}

export function createguid() {
  function S4() {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }
  return (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
}

// 构造节点数据
export const createNodeData = (name: string, data: any) => {
  let rootid = createguid();
  let nodes: any = [{
    id: rootid,
    size: [50, 50],
    label: 'hello', // String，节点标签
    shape: "react-shape",
    width: 50,   // Number，可选，节点大小的 width 值
    height: 50,
    component: "react-compont",
    zIndex: 3,
    data: {
      isroot: true,//是不是根节点
      label: name,
      type: 1, // 1子节点 2知识节点 3课程节点 4专业节点
      layoutname: 1, //默认布局
      ...defaultNodeData,
    }
  }];
  let edges: any = [];
  let mapcourseobj: any = {};  //这里构造一字典 方便查询到课程地图后 找到节点id
  data.forEach((item: any) => {
    let targetid = createguid();
    mapcourseobj[item.courseId] = targetid;
    nodes.push({
      id: targetid,
      size: [25, 25],
      width: 25,
      height: 25,
      shape: "react-shape",
      component: "react-compont",
      zIndex: 3,
      data: {
        isroot: false,//是不是根节点
        label: item.name_,
        contentId: item.contentId_,
        linkDistance: item.score_ > 100 ?item.score_ - 100 : item.score_ ,
        type: 2, // 1子节点 2知识节点 3课程节点 4专业节点
        layoutname: 1, //默认布局
        ...defaultNodeData,
      }
    });//课程节点
    edges.push({
      data: { visible: true, type: 1, isnew: false },
      source: rootid,
      target: targetid,
      type: 1,
      attrs: {
        line: {
          stroke: '#722ed1',
        },
      }
    },);//专业节点和课程节点的关系
  });
  return {
    nodes: nodes,
    edges: edges,
  }
}