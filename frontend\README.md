# 前端开发规范
## 语法
1. 命名：小写驼峰命名法，使用变量用途英文命名
2. 类组件/函数组件：大写驼峰命名法
3. class类名单词之间使用'_'
4. 使用 ;
5. 使用===
6. 推荐使用es6语法，例如对象解构、let、const等，具体参照：<https://es6.ruanyifeng.com/#README>
7. H5标签闭合或者<input />，react组件类似
8. 样式书写顺序：结构属性在表现属性之前，如下  
	##### 结构性属性：
    * display
	* position, left, top, right
	* overflow, float, clear
	* margin, padding
	##### 表现性属性：
	* background, border
	* font, color
9. 注释（复杂业务处多加注释）
```javascript
/** 
* 函数说明
* @关键字 
*/
	
// 单行注释：关键代码、变量注解
```
	
## 项目
1. 使用react技术栈，推荐使用hooks
2. 推荐使用Typescript
3. 使用antd组件库，组件功能强大，按需加载，社区活跃，中文文档
4. 使用less预编译，方便和antd对接
5. 推荐使用umi脚手架，可以提高开发效率
6. 组件功能单一化，拆分复杂业务组件为多个小组件，便于维护
7. 推荐使用 husky+lint-staged+prettier 优化代码格式，pre-commit检查（umi脚手架内置）
8. 推荐统一使用EsModule，避免CommonJs、AMD等混用

## 推荐阅读：
 <https://github.com/lin-123/javascript>  
  
 <https://guide.aotu.io/docs/index.html>
