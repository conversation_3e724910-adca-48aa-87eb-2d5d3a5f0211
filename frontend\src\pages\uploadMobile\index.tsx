import { Button, message, TreeSelect, Checkbox } from 'antd';
import React, { FC, useState, useEffect, useRef } from "react";
import uploadApi from '@/service/uploadApis';
import entityApis from '@/service/entityApis';
import { useIntl, useSelector, IUpload, useDispatch } from 'umi';
import _ from 'lodash';
import { useHistory } from 'umi';
import jQuery from 'jquery';
import ctyun from '@/otherStorage/ctyun';
import WebUploader from './WebUploader';
import { asyncLoadScript, copyObject, getUnique } from '@/utils';
import { IFormItem } from '@/types/entityTypes';
import './index.less'
import {
    IconFont,
} from '@/components';
import axios from 'axios'
const CheckboxGroup = Checkbox.Group;
const uploadMobile: React.FC<{}> = () => {
    const typeMapping: { [propsName: string]: string } = {
        picture: 'biz_sobey_picture',
        video: 'biz_sobey_video',
        audio: 'biz_sobey_audio',
        document: 'biz_sobey_document',
        other: 'biz_sobey_other',
    };
    let history: any = useHistory();
    const [metadata, setMetadata] = useState<any>({})
    const [uploadPath, setUploadPath] = useState<any>([]);
    const dispatch = useDispatch();
    const intl = useIntl();
    const [files, setFiles] = useState<any>([]);
    const [loading, setLoading] = useState<boolean>(false)
    const [uploadModal, setUploadModal] = useState<boolean>(false)
    const [showplaceholder, setShowplaceholder] = useState<string>('');
    const [selectedKey, setSelectedKey] = useState<any>('');
    const [checkedList, setCheckedList] = useState<any>('');
    let folderPath = history.location?.query?.folderPath || '';
    let myphoto = history.location?.query?.myphoto || false;
    let name = history.location?.query?.name || '';
    const ref = useRef<any>(null)
    const {
        tasks,
        taskPanls,
        orginTasks
    } = useSelector<{ upload: IUpload }, IUpload>(({ upload }) => {
        return upload;
    });
    const getAllFields = () => {
        const promise: Promise<any>[] = [];
        Object.keys(typeMapping).forEach(key => {
            entityApis.getAllFieldsByType(typeMapping[key]).then(res => {
                if (res && res.data) {
                    res.data.forEach(item => {
                        if (!item.value) {
                            item.value = item.isArray ? [] : '';
                        }
                    });
                    const value: any = {};
                    value[typeMapping[key]] = res.data;
                    setMetadata((pre: any) => Object.assign(pre, value))
                }
            });
        });
    };
    const stateRef = useRef<any>()
    useEffect(() => {
        stateRef.current = taskPanls
    }, [taskPanls])
    useEffect(() => {
        (window as any).$ = (window as any).jQuery = jQuery;
        asyncLoadScript('/rman/libs/webuploader/webuploader.js');
        getAllFields();
        setShowplaceholder(getDirTeeStr1(folderPath));
        setSelectedKey(folderPath);
    }, []);
    const chooseFile = () => {
        const newTasks = copyObject(tasks);
        setUploadPath([])
        setCheckedList([])
        ref.current.click()
        ref.current.onchange = (event: { target: { files: any[]; }; }) => {
            let files = event.target.files;
            setFiles(files)
            const fileMap = _.map(files, (item) => ({
                fileLength: item.size,
                fileName: item.name,
                fileType: item.type.includes("image")
                    ? "picture"
                    : item.type === "video"
                        ? "video"
                        : "other",
                pathType: 1,
                poolType: ""
            }));
            const temp_: any = newTasks.concat(files);
            dispatch({
                type: 'upload/changeTasks',
                payload: {
                    value: temp_,
                },
            });
            dispatch({
                type: 'upload/setOrginTasks',
                payload: {
                    value: temp_,
                },
            });
            setUploadPath((pre) => _.concat(pre, fileMap));
            setCheckedList((pre) => _.concat(pre, fileMap));
            setUploadModal(true)
        }
    }
    const uploadFile = async () => {
        if (checkedList.length === 0) {
            message.warning('请选择文件')
            return
        }
        setLoading(true)
        // if (!webUploader) {
        //   return;
        // }
        const newTaskPanls: any = copyObject(taskPanls);
        let tempArr: any = [];
        let showFlag = false;
        //获取每一个上传对象的存储方式 start
        const param = _.map(files, (item) => ({
            fileLength: item.size,
            fileName: item.name,
            fileType: item.type.includes("image")
                ? "picture"
                : item.type === "video"
                    ? "video"
                    : "other",
            poolType: window.localStorage.getItem('upform_platform') === 'Lark' ? 'ROLE' : '',
            // isPrivate:props.targetFolder.includes('global_sobey_defaultclass/private') //v3 暂时弃用
            pathType: selectedKey.includes('global_sobey_defaultclass/public/群组资源') ? 2 :
                selectedKey.includes('global_sobey_defaultclass/private') ? 1 : 0
        }));
        const storageConfig: any = await uploadApi.storageConfig(param);
        if (!storageConfig?.success) {
            setLoading(false)
            return
        };
        //获取每一个上传对象的存储方式 end
        for (let index = 0; index < files.length; index++) {
            const item: any = files[index];
            console.log(item, 'item')
            item.uid = guid()
            const storage_: any = storageConfig.data[index];
            console.log(metadata, 'metadata')
            console.log(checkedList[index].fileType, 'checkedList[index].fileType')
            const metadataTypes = metadata[typeMapping[checkedList[index].fileType]];
            const uploadMetas = _.map(metadataTypes, (item) => ({
                ...item,
                value:
                    item.fieldName === "name" || item.fieldName === "name_"
                        ? checkedList[index].fileName.substring(0, checkedList[index].fileName.lastIndexOf("."))
                        : item.value,
            }));
            //判断存储方式
            if (!storage_.access_type) continue;
            if (storage_.access_type === 'NAS') {
                showFlag = true;
                const file = new (window as any).WebUploader.Lib.File(
                    (window as any).WebUploader.Base.guid(),
                    // orginTasks[index],
                    item //兼容拖拽有originFileObj的bug
                    // item.file.originFileObj?.size || item.file //兼容拖拽有originFileObj的bug
                );
                file.guid = (window as any).WebUploader.Base.guid();
                file.metadata = uploadMetas;
                file.folderPath = selectedKey;
                file.fileGuid = storage_.path;//针对同名文件增加不同标识
                console.log('NAS', file)
                // @ts-ignore
                const webUploader = new WebUploader({
                    uploadError,
                    uploadComplete,
                });
                webUploader.addFiles(file);
                const files = webUploader.getFiles();
                files.forEach((item: any) => {
                    if (
                        newTaskPanls.filter(i => i.guid === item.source.guid).length === 0
                    ) {
                        newTaskPanls.push({
                            uploader: webUploader,
                            name: item.name,
                            size: item.size,
                            status: 0,
                            progress: 0,
                            index,
                            guid: item.source.guid,
                            uploading: false,
                            pause: false,
                        });
                    }
                });
                dispatch({
                    type: 'upload/setTaskPanls',
                    payload: {
                        value: newTaskPanls,
                    },
                });
            } else if (storage_.access_type === 'OSS') {
                //为了模拟传统数据结构 方便后续操作
                showFlag = true;//当存在有效上传文件时才打开
                const file = new (window as any).WebUploader.Lib.File(
                    (window as any).WebUploader.Base.guid(),
                    orginTasks[index],
                );
                file.guid = (window as any).WebUploader.Base.guid();
                file.metadata = uploadMetas;
                file.folderPath = selectedKey;
                file.name = item.name
                file.extraParam = {
                    head: item.head,
                    tail: item.tail,
                    watermark: item.watermark,
                }
                tempArr.push(file);
                
                if (!tempArr.some((item: any) => newTaskPanls.some((item_: any) => item.source.guid === item_.guid))) {
                    //先判断是否需要分片上传 大于5M才分片
                    const chunkSize: number = 5 * 1024 * 1024;
                    newTaskPanls.push({
                        uploader: {
                            ...file,
                            upload: () => file.size > chunkSize ? otherPlatform({ ...item, ...storage_, file }, ctyun.splitFile(file.source)): otherPlatform({ ...item, ...storage_, file })
                        },
                        storage: storage_.access_type,
                        product: storage_.product,
                        name: file.name,
                        size: file.size,
                        status: 0,
                        progress: 0,
                        index,
                        guid: file.ruid,
                        uploading: false,
                        pause: false,
                    });
                } 
            }
            dispatch({
                type: 'upload/setTaskPanls',
                payload: {
                value: newTaskPanls,
                },
            });
        }
    };
    const uploadError = (file: any) => {
        message.error(
            file.name +
            intl.formatMessage({
                id: 'upload-error',
                defaultMessage: '上传失败',
            }),
        );
        setLoading(false)
    };
    const uploadComplete = (file: any, newWebUploader: any) => {
        uploadApi.filemerge(file.source.guid, file.name, file.source.fileGuid).then(res => {
            if (res?.success) {
                pollMergeStatus(file, newWebUploader);
            } else {
                uploadError(file);
            }
        });
    };
    //其他存储
    const otherPlatform = async (params: any, lists?: any) => {
        console.log('otherPlatform', params);
        if (params.product === 'ctyun') {
            if (lists) {
                // ctyunUpload(params,lists);
            } else {
                //不分片
                // commonUpload(params);
            }
        } else if (params.product === 'aliyun') {
            if (lists) {
                // aliyunUpload(params);
            } else {
                //不分片
                // commonUpload(params);
            }
        } else if (params.product === 'amazon') {
            if (lists) {
                amazonUpload(params, lists);
            } else {
                //不分片
                uploadApi.getSinedUrl({
                    "bucket": params.bucket,
                    "objectPath": params.key,
                    "requestOper": 1,
                    "queryParams": {},
                    "product": "amazon"
                }).then(res => {
                    // for (let index = 0; index < lists.length; index++) {
                    const { signUrl, actualSignedRequestHeaders } = res?.data
                    const reopt = {
                        method: 'PUT',
                        url: signUrl,
                        withCredentials: false,
                        headers: actualSignedRequestHeaders || {},
                        maxRedirects: 0,
                        responseType: 'text',
                        data: params.file.source[0]
                    };
                    axios(reopt).then(function (response) {
                        if (response.status < 300) {
                            completeUpload(params);
                        }
                    })
                })
            }
        }
    }
    //华为云分片上传
    const amazonUpload = async (data: any, lists) => {
        const current_: any = stateRef.current.filter((item: any) => data.file.ruid === item.guid);
        console.log('阿里云分片上传---' + new Date(), current_[0]);
        if (current_.length === 0) return;
        uploadApi.getSinedUrl({
            "bucket": data.bucket,
            "objectPath": data.key,
            "requestOper": 2,
            "queryParams": {},
            "product": "amazon"
        }).then(res => {
            const { signUrl, actualSignedRequestHeaders } = res?.data
            const reopt = {
                method: 'POST',
                url: signUrl,
                withCredentials: false,
                headers: actualSignedRequestHeaders || {},
                maxRedirects: 0,
                responseType: 'text',
            };
            axios.request(reopt).then(async (response) => {
                if (response.status < 300) {
                    console.log(response.data);
                    const parser = new DOMParser();
                    const xmlDoc = parser.parseFromString(response.data, "text/xml");
                    const uploadIdNode = xmlDoc.getElementsByTagName('UploadId')[0];
                    const uploadId = uploadIdNode.textContent;
                    // completeUpload(data);
                    var content = "<CompleteMultipartUpload>";
                    for (let index = 0; index < lists.length; index++) {
                        await uploadApi.getSinedUrl({
                            "bucket": data.bucket,
                            "objectPath": data.key,
                            "requestOper": 3,
                            "queryParams": {
                                partNumber: index + 1,
                                uploadId
                            },
                            "product": "amazon"
                        }).then(async (res) => {
                            const { signUrl, actualSignedRequestHeaders } = res?.data
                            await axios.request({ ...reopt, method: 'PUT', data: lists[index].chunk, headers: actualSignedRequestHeaders || {}, url: signUrl }).then((res) => {
                                console.log(response, 'response')
                                content += `<Part><PartNumber>${index + 1}</PartNumber><ETag>${res.headers.etag}</ETag></Part>`
                            })
                        })
                        const progress = (index + 1) / lists.length;
                        const p = progress >= 1 ? 0.999 : progress;
                        dispatch({
                            type: 'upload/updateTaskPanls',
                            payload: {
                                value: { guid: data.file.ruid, progress: p },
                            },
                        });
                        //把参数异步传过去  方便后续取消操作
                        dispatch({
                            type: 'upload/updateTaskPanls_',
                            payload: {
                                value: {
                                    guid: data.file.ruid, param: {
                                        bucket: data.bucket,
                                        key: data.key,
                                        uploadId: data.uploadId,
                                        endpoint: data.endpoint,
                                        historyLists: lists
                                    }
                                },
                            },
                        });
                    }
                    await uploadApi.getSinedUrl({
                        "bucket": data.bucket,
                        "objectPath": data.key,
                        "requestOper": 4,
                        "queryParams": {
                            uploadId
                        },
                        "product": "amazon"
                    }).then(async (res) => {
                        const { signUrl, actualSignedRequestHeaders } = res?.data
                        axios.request({ ...reopt, data: content += '</CompleteMultipartUpload>', headers: actualSignedRequestHeaders || {}, url: signUrl }).then((res) => {
                            completeUpload(data);
                        })
                    })
                }
            })
        })
    }
    //上传完成回调
    const completeUpload = (data: any) => {
        const temp: any = {
            video_start: data.source?.extraParam?.head?.contentId_,
            video_end: data.source?.extraParam?.tail?.contentId_,
        };
        if (data.source?.extraParam?.watermark.keyframe_) {
            temp.watermark = {
                contentid: data.source.extraParam.watermark.contentId_,
                logo_path: data.source.extraParam.watermark.keyframe_,
                start_x: data.source.extraParam.watermark.position[0] == 'l' ? 0 : 1,
                start_y: data.source.extraParam.watermark.position[1] == 't' ? 0 : 1,
            };
        }
        uploadApi
            .uploadImport(
                selectedKey,
                // data.file.folderPath as string,
                `http://${data.bucket}.${data.endpoint}/${data.key}`,
                data.file.metadata as IFormItem[],
                data.file.size as number,
                temp,
            ).then(dd => {
                if (dd && dd.data && dd.success) {
                    dispatch({
                        type: 'upload/updateTaskPanls',
                        payload: {
                            value: { guid: data.file.ruid, progress: 1 },
                        },
                    })
                    // 弹窗提示
                    setLoading(false)
                    setUploadPath([])
                    setCheckedList([])
                    setUploadModal(false)
                    message.success(
                        data.file.name +
                        intl.formatMessage({
                            id: 'upload-success',
                            defaultMessage: '上传成功',
                        }),
                    );
                } else {
                    message.error('上传失败')
                }
            });
    }
    /**
 * 轮询合并状态
 * @param file
 * @param newWebUploader
 */
    const pollMergeStatus = async (file: any, newWebUploader: any) => {

        const res = await uploadApi.fetchMergeStatus(file.source.guid);
        if (res?.data?.state === 1 && res.data.finalFilePath) {
            uploadApi
                .uploadImport(
                    selectedKey,
                    // file.source.folderPath as string,
                    res.data.finalFilePath,
                    file.source.metadata as IFormItem[],
                    file.size,
                )
                .then(dd => {
                    if (dd && dd.data && dd.success) {
                        dispatch({
                            type: 'upload/updateTaskPanls',
                            payload: {
                                value: { guid: file.source.guid, progress: 1 },
                            },
                        });

                        // 从队列中删除
                        newWebUploader.removeFile(file, true);

                        // 删除task
                        // 弹窗提示
                        setLoading(false)
                        setUploadPath([])
                        setCheckedList([])
                        message.success(
                            file.name +
                            intl.formatMessage({
                                id: 'upload-success',
                                defaultMessage: '上传成功',
                            }),
                        );
                        setUploadModal(false)
                    } else {
                        uploadError(file);
                    }
                });
        } else if (res?.data?.state === 0) {
            // 手动移除掉的任务 停止轮询
            const realTaskPanls = stateRef.current;
            if (realTaskPanls.some((item: any) => item.guid === file.source.guid)) {
                setTimeout(() => {
                    pollMergeStatus(file, newWebUploader);
                }, 500);
            }
        } else if (res?.data?.state === -1 && res?.data.errorMsg) {
            message.error(res?.data.errorMsg);
            uploadError(file);
        } else {
            uploadError(file);
        }
    };
    // 路径转译
    const getDirTeeStr1 = (dirTree?: string): string => {
        if (!dirTree) {
            return ''
        }
        if (dirTree.includes('global_sobey_defaultclass/public')) {
            return dirTree.replace('global_sobey_defaultclass/public', '').replace('/', '');
        } else if (dirTree.includes('global_sobey_defaultclass/private')) {
            let newdir = dirTree.replace(
                'global_sobey_defaultclass/private',
                '个人资源',
            );
            if (newdir.includes('/')) {
                let alldir = newdir.split('/');
                if (alldir.length >= 2) {
                    newdir = newdir.replace('/' + alldir[1], '');
                }
            }
            return newdir;
        } else {
            return dirTree;
        }
    };
    const onChange = (check) => {
        setCheckedList(check)
        let arr: any[] = []
        files.forEach(item => {
            check.forEach(element => {
                if (item.name === element.fileName) {
                    arr.push(item)
                }
            })
        })
        setFiles(arr)
    }
    const icons = {
        'png': 'icona-tupian1',
        'jpg': 'icona-tupian1',
        'jpeg': 'icona-tupian1',
        'gif': 'icona-tupian1',
        'ppt': 'iconPPT',
        'pptx': 'iconPPT',
        'mp3': 'iconpreview_icon_audio',
        'pdf': 'iconPDF1',
        'mp4': 'icona-shipin11',
        'doc': 'iconWORD',
        'docx': 'iconWORD',
        'xls': 'iconEXCEL',
        'xlsx': 'iconEXCEL',
    } as any
    return (
        <div className='uploadMobile'>
            <input ref={ref} type="file" accept={myphoto === 'true' ? 'image/*, video/*' : '*'} style={{ display: 'none' }} multiple />
            <div className="popup-bottom">
                <Button onClick={chooseFile}>选择文件</Button>
            </div>
            {uploadModal && <div className="popup-bottom1">
                <p>上传</p>
                <span className='close' onClick={() => setUploadModal(false)}>X</span>
                <div className="uploadList">
                    <CheckboxGroup
                        value={checkedList}
                        onChange={onChange}
                        style={{ width: '100%' }}
                    >
                        {uploadPath.map((item: any) => (
                            <div className="list-item" key={item.fieldName}>
                                <IconFont type={icons[(item.fileName.substring(item.fileName.lastIndexOf(".") + 1)).toLowerCase()] ?
                                    icons[(item.fileName.substring(item.fileName.lastIndexOf(".") + 1)).toLowerCase()] : 'iconpreview_icon_image'} />
                                <div className='name'>{item.fileName}</div>
                                <Checkbox value={item} />
                            </div>
                        ))}
                    </CheckboxGroup>
                </div>
                <div className="treeSelect">
                    <div className='upload-box'>
                        <span>上传到：</span>
                        <span>{showplaceholder}</span>
                    </div>
                    <Button type='primary' onClick={uploadFile} loading={loading}>上传</Button>
                </div>
            </div>}

        </div >
    )
}

export default uploadMobile;
function guid() {
    function S1() {
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    }
    return (S1() + S1() + "-" + S1() + "-" + S1() + "-" + S1() + "-" + S1() + S1() + S1());
}