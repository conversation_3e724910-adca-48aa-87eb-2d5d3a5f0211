import React, { useState, useEffect, FC, useRef } from "react";
import './index.less'
import Header from "./components/heard";
import MapX6 from "./components/mapX6";
import entityApis from '@/service/entityApis';
import { getType, changesize, searchKeywords, updateScrollPosition } from '@/utils';
import { useLocation, useSelector, useDispatch, useHistory, useIntl } from 'umi';
import globalParams, { ModuleCfg } from '@/permission/globalParams';
import { createNodeData, MapConfig } from './components/util';
import contentListApis from '@/service/contentListApis';
import { CloseOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import MergeSettingModal from '@/components/mergeSettingModal/mergeSettingModal';
import EchartsGroup from '@/components/EchartsGroup';
import BasicMetadata from '@/components/basicMetadata/basicMetadata';
import PointsItem from './components/pointsItem';
import { ExifConfig, IEntity, IExifInfo, IFormItem } from '@/types/entityTypes';
import SmartService from '@/service/smartService';
import { Drawer, Button, message, Menu, Form, Timeline, Pagination, Empty, Checkbox, Input, Select } from 'antd';
import { IPermission } from '@/models/permission';
import Entity from '@/components/entity/entity';

import {
    H5Player,
    Player,
    DownloadModal,
    IconFont,
    IntelligentAnalysisModal,
    ShareModal,
} from '@/components';
// import './components/index.less';
const { Option } = Select;
import _ from 'lodash';
// 课程详情
import CourseInfo from './components/CourseInfo'
const Mapv3: FC<any> = () => {
    const history: any = useHistory();
    const [knowKey, setKnowKey] = useState<string>('')
    const [mapinfo, setMapInfo] = useState<any>(null);
    const [graph, setGraph] = useState<any>(null);
    const [startSpan, setStartSpan] = React.useState(0);
    const [currentFrame, setCurrentFrame] = useState<number>(0);
    const [initFlag, setInitFlag] = useState<boolean>(false);
    const [endSpan, setEndSpan] = React.useState(0);
    const [videoKeyframe, setVideoKeyframe] = useState('');
    const [settingModalVisible, setsmVisible] = useState(false);
    const [isVision, setIsVision] = useState(false);
    // 搜索组件的ref 里面暴露了方法出来
    const searchref = useRef<any>();
    const [track, setTrack] = useState<boolean>(false);
    // 0是关闭状态  1显示知识节点弹窗  2显示对比辨析弹窗  3关联节点   4知识节点对比辨析  5绑定管理  6搜素   7 excel word 导入  8 对比辨析详情页面  9课程大钢  10课程地图按课件生成  11 地图保存记录 12 知识点达成度 13选择添加的课程  14课程详情  
    // 15是导入xmind
    const [visible, setVisible] = useState<number>(0);
    const { modules, permissions, rmanGlobalParameter } = useSelector<
        { permission: any },
        IPermission
    >(({ permission }) => permission);
    const player = useRef<Player>();
    const win = window as any;
    const [dirTree, setDirTree] = useState<string>('');
    const metadataRef = useRef<any>();
    const [isTransCode, setIsTransCode] = useState<boolean>(true);
    const [sensitiveWords, setSensitiveWords] = useState<any>([]);
    const [currentLanguage, setCurrentLanguage] = useState<any>('cn');//当前选中语言
    const [entity, setEntity] = useState<IEntity>();
    // 片段
    const [sequencemeta, setSequencemeta] = useState<SmartTypes.SequenceMeta[]>(
        [],
    );
    const [canEdit, setCanEdit] = useState<boolean>(false)
    const [translateFlag, setTranslateFlag] = useState<boolan>(false);//有无翻译标识
    const [frameRate, setFrameRate] = useState<number>(25);
    const [currentCourseware, setCurrentCourseware] = useState<any>('');//当前选中课件
    const [coursewareTotal, setCoursewareTotal] = useState<number>(0);//
    const [contentId, setContentId] = useState<string>('');
    const { mobileFlag } = useSelector<{ config: any }, any>(
        ({ config }) => config
    );
    const [coursewarePage, setCoursewarePage] = useState<number>(1);//
    const [fontSize, setFontSize] = useState<number>(15)
    const [coursewarePageSize, setCoursewarePageSize] = useState<number>(10);//
    const currentTop = useRef<any>(0);
    const [keyframeFlag, setKeyFrameFlag] = useState<boolean>(false);
    const [voice, setVoice] = useState<SmartTypes.Lyrics[]>([]);
    // 查询类型 0是全部  1是子节点  2是知识节点
    const [querytype, setQuerytype] = useState<string>('0');
    //  查询节点的类型  疑难点  对比辨析节点  
    const [metadata, setMetadata] = useState<IFormItem[]>([]);
    const [selecttype, setSelecttype] = useState<string>('0');
    const [playFlag, setPlayFlag] = useState<boolean>(false);
    let hidecatalogue = history.location?.query?.hidecatalogue || '0';
    // 搜索框的参数
    const [contentDetail, setContentDetail] = useState<any>({});
    //全选start
    const [checkedList, setCheckedList] = useState<any[]>([]);
    const [checkedKeys, setCheckedKeys] = useState<any[]>([]);
    const [indeterminate, setIndeterminate] = useState(false);
    const [searchIndex, setSearchIndex] = useState<number[]>([]);
    const [checkAll, setCheckAll] = useState(false);
    const [searchVoiceWord, setSearchVoiceWord] = useState<string>('');
    const [searchCurrent, setSearchCurrent] = useState<number>(0);
    const [courseware, setCourseware] = useState<any[]>([]);//课件
    const [errorCode, setErrorCode] = useState<number>(0);
    const [inputtext, setInputtext] = useState<string>('');
    const [keywords, setKeywords] = useState<any>([]);
    // 详情的参数
    const [drawerdata, setDrawerdata] = useState<any>(null);
    // 当前选择的课程id
    const [selectcourseid, setSelectcourseid] = useState<any>(null);
    const [selectmapid, setSelectmapid] = useState<any>(null);
    // 获取url参数
    const [activeTabs, setActiveTabs] = useState<string>('1');
    const { query }: any = useLocation();
    const [form0] = Form.useForm();
    const [form1] = Form.useForm();
    const [form2] = Form.useForm();
    const [form3] = Form.useForm();
    const intl = useIntl();
    const isFileSize = (fieldName: string) =>
        ['fileSize', 'fileSize_'].includes(fieldName);

    useEffect(() => {
        contentListApis.initialization().then(res => {
            console.log(res, 'res')
            let nodes: any[] = []
            // generateNodeData()
            // res.data.results.sort((a, b) => +a.level - +b.level)
            let edges: any = []
            generateNodeData(res.data, nodes, edges)
            let newNodes = nodes.map((item: any) => {
                if (item.data.type === 1) {
                    if (item.data.isroot) {
                        return {
                            ...item,
                            size: [71, 71],
                            width: 71,
                            height: 71
                        }
                    }
                    return {
                        ...item,
                        size: [50, 50],
                        width: 50,
                        height: 50
                    }
                }
                else if (item.data.type === 2) {
                    return {
                        ...item,
                        size: [25, 25],
                        width: 25,
                        height: 25
                    }
                }
            })
            // let mapdata = createNodeData('fdasfsaf', nodes);
            setMapInfo({ nodes: newNodes, edges });
        })
    }, [])
    useEffect(() => {
        
    }, [])
    useEffect(() => {
        if (contentId) {
            getMetaData()
            getQuerySequencemeta()
            getQueryKeywords()
            getCourseware()
            getQueryVoice()
        }
    }, [contentId])
    const generateNodeData = (data: any[], nodes: any[] = [], edges: any[] = [], parentId?: any) => {
        data.map((item: any, i: number) => {
          let id = createguid()
          nodes.push({
            "id": id,
            "nodeId": id,
            "data": {
                "isCollapsed": true,
                "isDiscriminate": false,
                "isedit": false,
                "isroot": item.level === 1|| item.level === 0,
                "label": item.themeName,
                "type": 1,
            },
            "zIndex": 3,
            "shape": "react-shape",
            "component": "react-compont",
            "type": +item.level,
            "size": [
                71 - +item.level * 5,
                71 - +item.level * 5
            ],
            "width": 71 - +item.level * 5 ,
            "height": 71 - +item.level * 5
        })
        item.associatedResource.forEach(ele => {
            let id1 = createguid()
            nodes.push({
                "id": id1,
                "nodeId": id1,
                "data": {
                    "isCollapsed": true,
                    "isDiscriminate": false,
                    "isedit": false,
                    "isroot": ele.level === 1|| ele.level === 0,
                    "label": ele.name_,
                    contentId: ele.contentId_,
                    "type": 2,
                },
                "zIndex": 3,
                "shape": "react-shape",
                "component": "react-compont",
                size: [50, 50],
                "width": 71 - +item.level * 5,
                "height": 71 - +item.level * 5
            })
            edges.push({
                "source": id,
                "target": id1,
                "type": item.level,
                "data": {
                    "visible": true,
                    "type": item.level,
                    "isnew": false,
                    "newadd": false
                },
                "attrs": {
                    "line": {
                        "stroke": "#8A8B99",
                        "strokeWidth": 1,
                        "targetMarker": {
                            "args": {
                                "size": 8
                            },
                            "name": "classic"
                        }
                    }
                }
            })
        })

          if (parentId) {
            edges.push({
              "source": parentId,
              "target": id,
              "type": 2, //1-包含，2-等价，3-后续
              "data": {
                "visible": true,
                "type": 2,
                "isnew": false,
                "newadd": false
              },
              "attrs": {
                "line": {
                  "stroke": "#8A8B99",
                  "strokeWidth": 1,
                  "targetMarker": {
                    "args": {
                      "size": 8
                    },
                    "name": "classic"
                  }
                }
              }
            })
          }
          if (item.children) {
            generateNodeData(item.children, nodes, edges, id)
          }
        })
      }
    // 获取关键词
    const searchVoive = (
        e: KeyboardEvent<HTMLInputElement> | FocusEvent<HTMLInputElement> | any,
    ) => {
        e.preventDefault();
        let name = e.target.value;
        let Index: number[] = [];
        voice.forEach((item, index) => {
            if (item.text?.indexOf(name) > -1) {
                Index.push(index);
            }
        });
        setSearchCurrent(0);
        setSearchIndex(Index);
        setSearchVoiceWord(name);
    };
    const isInViewport = (ele: { getBoundingClientRect: () => { top: any; right: any; bottom: any; left: any; }; }) => {
        let { top, right, bottom, left } = ele.getBoundingClientRect()
        let w = window.innerWidth
        let h = window.innerHeight
        if (bottom < 0 || top > h) {
            // y 轴方向
            return false
        }
        if (right < 0 || left > w) {
            // x 轴方向
            return false
        }
        return true
    }
    useEffect(() => {
        contentId && getCourseware()
    }, [coursewarePage, coursewarePageSize])
    const getCourseware = async () => {
        const res = await entityApis.getKeyframes({
            contentId,
            page: coursewarePage,
            size: coursewarePageSize
        }, '');
        const temp = res?.data?.data.sort((x: any, y: any) => {
            return x.keyFrameNo - y.keyFrameNo
        })
        setCoursewareTotal(res?.data?.recordTotal)
        setCourseware(temp?.map((item: any, index: number) => {
            return {
                ...item,
                hover: false
            }
        }));
    };
    const handlePlayChange = (point: number) => {
        setCurrentFrame(point);
    };
    const getQueryKeywords = () => {
        SmartService.queryKeywordNew(contentId).then((res: any) => {
            if (res?.success) {
                let result_: any = [], temp_ = res.data;
                temp_?.sort((a: any, b: any) => b.value - a.value).map((item: any, index: number) => {
                    let result, average = 1 / (res.data.length - 1)//平均梯度;
                    if (index == 0) {
                        result = 1  //以最大的为单位1
                    } else {
                        const scale = item.value / temp_[index - 1].value; //与上一个值的比例
                        result = (Math.max(scale, (1 - average))) * result_[index - 1].value; //统一阶梯比例在0.9 - 1 之间
                    }
                    result_.push({
                        name: item.name,
                        value: result
                    })
                });
                console.log(result_)
                setKeywords(result_)
            } else {
                setKeywords([])
            }
        });
    };
    const createguid = () => {
        function S4() {
            return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
        }
        return (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
    }
    const changeCoursewarePage = (page: number, size: any) => {
        setCoursewarePage(page);
    };
    //获取敏感词
    //词云关键词点击事件
    const setCurrentTime = (point: number) => {
        player.current?.setCurrentTime(win.TimeCodeConvert.l100Ns2Second(point));
    };
    const wordCloudclick = {
        'click': (params: any) => {
            console.log(params.data);
            // jumpVideoTime(params.data._in);
        },
    }
    const sortFuc = (a: SmartTypes.SequenceMeta, b: SmartTypes.SequenceMeta) =>
        a.inpoint - b.inpoint;
    const getQuerySequencemeta = (times?: number) => {
        SmartService.querySequencemetaNew(contentId).then(res => {
            const result = res as any;
            if (result?.success && result.data?.length > 0) {
                if (times === 1) {
                    const metadata = result.data;
                    const curData = metadata[metadata.length - 1]
                    setKnowKey(curData.guid_);
                    //   setPlayerSec(curData.inpoint, curData.outpoint, curData);
                    setActiveTabs('4');
                    const domObj: any = document.querySelector('.sequence_list')?.parentElement;
                    if (domObj?.scrollTo) {
                        setTimeout(() => {
                            const curDomObj: any = document.querySelector('.sequence_item_wrap.active');
                            domObj.scrollTo({ top: curDomObj.offsetTop });
                        }, 100);
                    }
                }
                const arr = result.data.sort(sortFuc);
                //新增语音翻译功能标识
                if (keyframeFlag) {
                    if (arr.every((item: any) => item.keyframepath)) { //如果每个知识点都有封面帧 循环结束
                        // clearInterval(timer);
                        setKeyFrameFlag(false)
                    }
                }
                const temp = JSON.parse(window.localStorage.getItem('searchKeyWord') || '{}');
                if (temp?.word) {
                    arr?.map((item: any) => {
                        searchKeywords(item.title, 'knowledege');
                    })
                }
                setSequencemeta(arr);
                // 判断是否有inpoint
                // if(history.location?.query.inpoint){
                //   setActiveTabs('4');
                // }
            } else {
                setSequencemeta([]);
            }
        });
    };
    const languageChange = (e: any) => {
        setCurrentLanguage(e);
    }
    const makedata = (nodesVos: any, relationVos: []) => {
        let newnodes = nodesVos.map((item: any) => {
            let data = JSON.parse(item.valueMap);
            let obj = {
                ...item,
                id: item.nodeId,
                data: {
                    ...data.data,
                    mapId: item.mapId || null
                },
                zIndex: 3,
                shape: 'react-shape',
                component: 'react-compont',
                type: data.data.type,
            }
            if (data.data.type == 4) {
                obj.size = [MapConfig.marjor.size[0], MapConfig.marjor.size[1]];
                obj.width = MapConfig.marjor.size[0];
                obj.height = MapConfig.marjor.size[1];
            } else if (data.data.type == 3) {
                obj.size = [MapConfig.course.size[0], MapConfig.course.size[1]];
                obj.width = MapConfig.course.size[0];
                obj.height = MapConfig.course.size[1];
            } else if (data.data.type == 1) {
                if (data.data.isroot) {
                    obj.size = [MapConfig.course.size[0], MapConfig.course.size[1]];
                    obj.width = MapConfig.course.size[0];
                    obj.height = MapConfig.course.size[1];
                } else {
                    obj.size = [MapConfig.fenlei.size[0], MapConfig.fenlei.size[1]];
                    obj.width = MapConfig.fenlei.size[0];
                    obj.height = MapConfig.fenlei.size[1];
                }
            } else if (data.data.type == 2) {
                obj.size = [MapConfig.knowledge.size[0], MapConfig.knowledge.size[1]];
                obj.width = MapConfig.knowledge.size[0];
                obj.height = MapConfig.knowledge.size[1];
            } else {
                console.log('未知类型');
            }
            return obj
        })
        let newedges = relationVos.map((item: any) => {
            const data = JSON.parse(item.data)
            //每个边的初始化数据
            const obj = {
                ...item,
                data: {
                    ...data,
                    newadd: data.isnew || false
                }
            }
            if (data.type == 1) { //包含
                obj.attrs = {
                    line: {
                        stroke: "#8A8B99", // 指定 path 元素的填充色
                        strokeWidth: 1,
                        targetMarker: {
                            args: { size: 8 },
                            name: 'classic',
                        },
                    },
                }
            } else if (data.type == 2) { //等价
                obj.attrs = {
                    line: {
                        stroke: "#8A8B99", // 指定 path 元素的填充色
                        strokeWidth: 1,
                        targetMarker: null,
                        strokeDasharray: 5
                    },
                }
            } else if (data.type == 3) { //后续
                obj.attrs = {
                    line: {
                        stroke: "#8A8B99", // 指定 path 元素的填充色
                        strokeWidth: 1,
                        // targetMarker: null
                        // #333333
                        strokeDasharray: 5,
                        targetMarker: {
                            args: { size: 8 },
                            name: 'classic',
                        },
                    },
                }
            }
            return obj
        })

        return {
            nodes: newnodes,
            edges: newedges,
        }
    }
    const coursewareDelete = async (e: any, item: any) => {
        e.stopPropagation();
        // alert(item.fileGUID)
        const res: any = await entityApis.deleteKeyframes([
            {
                contentId,
                keyFrameNos: [item.keyFrameNo]
            }
        ]);
        if (res.success) {
            message.success(intl.formatMessage({ id: '删除成功' }));
            setTimeout(() => getCourseware(), 1000);
        } else {
            message.error(intl.formatMessage({ id: '删除失败' }));
        }
    };
    const getMetaData = () => {
        entityApis.getEntityMetadata(contentId).then(res => {
            if (res && res.data && res.success) {
                // getEntityData();//当资源存在才继续调用
                if (Array.isArray(res.data)) {
                    res.data.forEach(item => {
                        if (item.alias === '标签') {
                            item.alias = '关键词'
                        }
                        if (!item.value) {
                            item.value = '';
                        }
                        if (!isNaN(item.value as any) && isFileSize(item.fieldName)) {
                            item.value = changesize(item.value);
                            item.isReadOnly = true;
                        }
                    });
                    let arr = res.data.filter(item => item.fieldName !== "createUser_")
                    arr.sort((a, b) => a.order - b.order)
                    console.log(arr, 'fsaf')
                    setMetadata(arr);
                }
            } else {
                //401没权限 404资源已删除
                if (['401', '404'].includes(res?.error?.code || '')) {
                    setErrorCode(Number(res?.error?.code))
                } else {
                    //   getEntityData();
                }
            }
        });
    };
    // 更新节点的对比辨析字段
    const updatanodecompare = (compare: any) => {
        const allnodes = graph.getNodes();
        // 先把所有的都改成false
        allnodes.forEach((element: any) => {
            let data = element.getData();
            element.updateData({
                ...data,
                isDiscriminate: false
            })
        });
        // 再更新节点
        compare.forEach((id: any) => {
            const node = graph.getCellById(id);
            if (node) {
                let data = node.getData();
                node.updateData({
                    ...data,
                    isDiscriminate: true
                })
            }
        });
    }

    // 更新节点的data
    const updatanode = (id: string, data: any) => {
        const node = graph.getCellById(id);
        // console.log('更新节点的data', node.getData());
        let nodedata = node.getData();
        let newdata = {
            ...nodedata,
            ...data
        }
        node.updateData(newdata);
    }
    const isBasicFormat = (src: string) => {
        return (
            src?.indexOf('.html') > -1 ||
            src?.indexOf('.htm') > -1 ||
            src?.indexOf('.jpg') > -1 ||
            src?.indexOf('.mp4') > -1 ||
            src?.indexOf('.mp3') > -1 ||
            src?.indexOf('.pdf') > -1
        );
    };
    const handleSuccess = (play: Player) => {
        // console.log('permissions', permissions)
        player.current = play;
        if (!(play as any).isVideo) {  //如果不是视频就中断
            return
        }
        setInitFlag(true)//判断插件已加载完毕
        // getPlayerStart();
    };
    const onSelection = () => {
        const selection = window.getSelection();
        if (!selection?.isCollapsed) {
            const baseNode = parseInt(
                selection?.anchorNode?.parentElement?.parentElement?.dataset.id || '',
            );
            const focusNode = parseInt(
                selection?.focusNode?.parentElement?.parentElement?.dataset.id || '',
            );
            if (baseNode - focusNode > 0) {
                setStartSpan(focusNode);
                setEndSpan(baseNode);
                // doPop(
                //     selection?.focusNode?.parentElement?.parentElement?.offsetTop || 0,
                //     selection?.focusNode?.parentElement?.parentElement?.offsetLeft || 0,
                // );
            } else {
                setStartSpan(baseNode);
                setEndSpan(focusNode);
                // doPop(
                //     selection?.anchorNode?.parentElement?.parentElement?.offsetTop || 0,
                //     selection?.anchorNode?.parentElement?.parentElement?.offsetLeft || 0,
                // );
            }
        } else {
        }
    };
    const searchVoice = (tit: any) => {
        const temp = searchKeywords(tit, 'voice');
        if (!searchVoiceWord && temp.index > -1) {
            return <span>
                {temp.beforeStr}
                <span className="key-search-value">{temp.word}</span>
                {temp.afterStr}
            </span>
        }
        if (searchVoiceWord) { //在检索时 需要置空
            window.localStorage.setItem('searchKeyWord', '{}');
        }
        const index = tit?.indexOf(searchVoiceWord);
        const beforeStr = tit?.substr(0, index);
        const afterStr = tit?.substr(index + searchVoiceWord.length);
        const title =
            index > -1 ? (
                <span>
                    {beforeStr}
                    <span className="voice-search-value">{searchVoiceWord}</span>
                    {afterStr}
                </span>
            ) : (
                <span>{tit}</span>
            );
        return title;
        // setVoiceTitle(title)
    };
    //获取网络带宽
    const getNetworkSpeed = () => {
        // 判断浏览器是否支持navigator.connection属性
        if ((navigator as any)?.connection) {
            // 获取当前网络连接信息
            var connection = (navigator as any)?.connection;
            // 如果浏览器支持downlink属性，则输出当前网络的下行带宽
            if (connection.downlink) {
                return connection.downlink
            } else {
                return
            }
        } else {
            return 0
        }
    }
    const getValue = (value: string) => {
        let res: string[] = [];
        try {
            const arr = Array.isArray(value) ? value : JSON.parse(value);
            if (Array.isArray(arr)) {
                res = arr;
            } else {
                res = [arr.toString()];
            }
        } catch (e) {
            console.error(e);
        }
        return res;
    }
    const getQueryVoice = () => {
        SmartService.queryVoice(contentId, '').then(res => {
            if (res?.data && res.data.data.length > 0) {
                setTrack(true);
                const list = res.data.data[0].metadata.sort((a, b) => a._in - b._in);
                const temp = JSON.parse(window.localStorage.getItem('searchKeyWord') || '{}');
                if (temp?.word) {
                    list?.map((item: any) => {
                        searchKeywords(item.text, 'voice');
                    })
                }
                setVoice(list);
                //新增语音翻译功能标识
                const flag = res.data.data[0].metadata[0]?.text_en ? true : false;
                setTranslateFlag(flag);
            } else {
                setTrack(false)
                setVoice([]);
            }
        });
    };
    useEffect(() => {
        if (!playFlag) {
            return
        }
        if (activeTabs === '3') { //语音信息
            if (isVision) {
                const domObj: any = document.getElementsByClassName('lyric_item active')[0];
                let timelineObj: any = document.getElementById('timeline');
                // console.log(domObj?.parentElement?.parentElement?.offsetTop)
                if (domObj) {
                    updateScrollPosition({
                        dom: timelineObj,
                        dis: domObj.parentElement?.parentElement?.offsetTop - 123
                    })
                }
            } else {
                const domObj_: any = document.getElementsByClassName('currentPlay')[0];

                let timelineObj_: any = document.getElementsByClassName('paragraph-content')[0];
                // console.log(domObj?.parentElement?.parentElement?.offsetTop)
                if (domObj_) {
                    // console.log(domObj_.getBoundingClientRect().top);
                    updateScrollPosition({
                        dom: timelineObj_,
                        dis: domObj_.offsetTop - 123
                    })
                }
            }

        } else if (activeTabs === '4') {
            const domObj: any = document.getElementsByClassName('knowledgeCheckGroup')[0];
            const curDomObj: any = document.getElementsByClassName('sequence_item_wrap active')[0];
            if (curDomObj && domObj && currentTop.current !== curDomObj.offsetTop) { //必须把top值保存起来 不然无法滚动
                if (!isInViewport(curDomObj)) {
                    currentTop.current = curDomObj.offsetTop;
                    updateScrollPosition({
                        dom: domObj,
                        dis: curDomObj.offsetTop - 42
                    })
                }
            }
        } else {

        }

    }, [currentFrame, playFlag, sequencemeta, isVision]);
    const onCheckAllChange = (e: any) => {
        setCheckedList(e.target.checked ? sequencemeta.map((e: any) => e) : []);
        setCheckedKeys(
            e.target.checked ? sequencemeta.map((item: any) => item.guid_) : [],
        );
        setIndeterminate(false);
        setCheckAll(e.target.checked);
    };
    const setPlayerSec = (inpoint: number, outpoint: number, data: any, canModify: boolean = true) => {
        setKnowKey(data.guid_)
        // sectionData = { ...data };
        const _inpoint = win.TimeCodeConvert.l100Ns2Second(inpoint),
            _outpoint = win.TimeCodeConvert.l100Ns2Second(outpoint);
        // console.log(inpoint, outpoint);
        // console.log(_inpoint, _outpoint);
        player.current?.hideModifyBtn(true);
        player.current?.setTrimin(_inpoint);
        player.current?.setTrimout(_outpoint);
        player.current?.setCurrentTime(_inpoint);
        if (canModify) {
            player.current?.showModifyBtn();
        }
        player.current?.play();
        setPlayFlag(true);
    };
    const coursewareClick = (item: any) => {
        setCurrentCourseware(item.fileGUID);
        const time = win.TimeCodeConvert.frame2Second(item.keyFrameNo, frameRate);//帧转秒
        player.current?.setCurrentTime(time); //时间是秒
    };
    const nodeClick = (node) => {
        if (node.data.type == 2) {
            let contentId = node.data.contentId
            setContentId(contentId)
            entityApis.getEntity(contentId).then((res: any) => {
                let dataType = res.data.type;
                setContentDetail(res.data)
                setDirTree(res.data.path);
                setCanEdit(res.data.extensions?.canEdit);
                let path = _.find(res.data.fileGroups, (f: any) => {
                    return f.typeCode === (dataType === 'biz_sobey_document' ? 'sourcefile' : 'previewfile');
                });
                if (!path) {
                    path = _.find(res.data.fileGroups, (f: any) => {
                        return f.typeCode === 'sourcefile';
                    });
                    if (
                        dataType === 'biz_sobey_picture' ||
                        dataType === 'biz_sobey_audio' ||
                        dataType === 'biz_sobey_document' ||
                        dataType === 'biz_sobey_video'
                    ) {
                        path?.fileItems[0] &&
                            setIsTransCode(isBasicFormat(path?.fileItems[0].displayPath));
                    }
                }
                //自动适应清晰度
                let autoPathObj = path ? path.fileItems[0] : {}
                if (path && path.fileItems && path.fileItems.length) {
                    let speed = getNetworkSpeed()
                    if (speed < 1) {
                        autoPathObj = path.fileItems.find((p: any) => p.bitRate <= 512 * 1000) || path.fileItems[0]
                    } else if (speed < 3) {
                        autoPathObj = path.fileItems.find((p: any) => p.bitRate <= 1024 * 1000) || path.fileItems[0]
                    } else if (speed < 5) {
                        autoPathObj = path.fileItems.find((p: any) => p.bitRate <= 2048 * 1000) || path.fileItems[0]
                    } else {
                        autoPathObj = path.fileItems[path.fileItems.length - 1]
                    }
                }
                let autoPath = autoPathObj.displayPath
                setEntity({
                    path: autoPath,
                    keyframes:
                        res.data.keyframe && res.data.keyframe.filePath
                            ? res.data.keyframe.filePath
                            : '',
                    type: getType(res.data.type),
                });
                setVisible(1);
            })
        }
    }
    return (
        <div className="mapv3_view">
            <Header mapdata={mapinfo} graph={graph} querytype={querytype} inputtext={inputtext}
                setInputtext={(e: any) => setInputtext(e.target.value)}
                setSelecttype={setSelecttype}
                typeonselect={(LabeledValue: any) => {
                    setQuerytype(LabeledValue);
                    setVisible(6);
                    setInputtext('');
                    searchref.current.querynode();
                }}
                search={(e: any) => {
                    setVisible(6);
                    setQuerytype('0');
                    searchref.current.querynode();
                }}
            >
            </Header>
            <div className="map_content_view">
                <MapX6 mapdata={mapinfo}
                    nodeClick={nodeClick}
                    // nodeClick={(node: any) => {
                    //     const data = node.getData();
                    //      if (data.type == 2) {
                    //         setDrawerdata(node);
                    //         setVisible(1);
                    //     }
                    // if (data.type == 2) {
                    //     setDrawerdata(node);
                    //     setSelectmapid(data.mapId);
                    //     setVisible(1);
                    // } else if (data.type == 3) {
                    //     setSelectcourseid(data.course.courseId);
                    //     setVisible(14);
                    // } else {
                    //     console.log('其他类型的节点不处理')
                    // }

                    // }}
                    initover={(e: any) => {
                        setGraph(e);
                        // let rootnode = e.getRootNodes();
                        // if(rootnode.length){
                        //     // 隐藏节点
                        //     rootnode[0].setVisible(false);
                        //     // 获取相邻边
                        //     let neiedge = graph.getConnectedEdges(rootnode[0]);
                        //     // 隐藏边
                        //     neiedge.forEach((item:any)=>{
                        //         item.setVisible(false);
                        //     }) 
                        // }
                    }}
                ></MapX6>
                <Drawer
                    placement="right"
                    mask={false}
                    title={
                        <div style={{ position: 'relative', width: '100%', height: '100%' }}>
                            <img style={{ width: '100%', height: 'auto' }} src={require('../../images/coursemap/v3/title_bg.png')}></img>
                            <img onClick={() => setVisible(0)} style={{ position: 'absolute', right: '20px', width: '15px', top: '22px' }} src={require('../../images/coursemap/v3/close.png')} alt="" />
                        </div>
                    }
                    closable={false}
                    onClose={() => {
                        setVisible(0);
                        setDrawerdata(null);
                    }}
                    visible={visible == 1}
                    getContainer={false}
                    style={{ position: 'absolute' }}
                    width="500px"
                    className="node_detail_view"
                >
                    {/* {entity && <video src={entity.path} controls style={{ width: '100%', height: '300px', objectFit: 'cover' }}></video>} */}
                    {entity && isTransCode ? (
                        <div style={{ height: '280px', overflow: 'hidden' }} className='entity_view_wrapper'>
                            <Entity
                                id={'h5Player1'}
                                type={entity.type}
                                src={entity.path}
                                frameRate={frameRate}
                                keyframes={entity.keyframes}
                                errorCode={errorCode}
                                contendId={contentId}
                                onSuccess={handleSuccess}
                                translateFlag={translateFlag}
                                key={translateFlag as any}
                                // getKeyframe={handleKeyframe}
                                onPlayChange={handlePlayChange}
                            // modifySection={handleModifySection}
                            // resetSection={handleResetSection}
                            />
                        </div>
                    ) : (
                        <div className='entity_view_wrapper '>
                            <div className="entity-error">
                                {/* <img src={require('../../../.images/contentlibrary/zmz.png')} /> */}
                            </div>
                        </div>
                    )}
                    <Menu
                        selectedKeys={[activeTabs]}
                        mode="horizontal"
                        className={'entity_' + entity?.type}
                        overflowedIndicator={<span>{intl.formatMessage({ id: '更多' })}</span>}
                        onClick={item => {
                            setActiveTabs(item.key);
                            if (item.key == '4') {
                                // setPointKey();
                            }
                        }}
                    >
                        <Menu.Item key="1">
                            <div>{intl.formatMessage({ id: '基本信息' })}</div>
                        </Menu.Item>

                        {rmanGlobalParameter.includes(globalParams.speech_analysis_display) &&
                            (entity?.type === 'video' || entity?.type === 'audio') &&
                            !(hidecatalogue === '1') && <Menu.Item key="3">
                                <div>
                                    <span>{intl.formatMessage({ id: '语音文本' })}</span>
                                    {voice.length === 0 && <span className="msgCountNum">
                                        ({intl.formatMessage({ id: '无' })})
                                    </span>}
                                </div>
                            </Menu.Item>}

                        {(entity?.type === 'video' || entity?.type === 'audio') &&
                            !(hidecatalogue === '1') && <Menu.Item key='4'>
                                <div>
                                    <span>{intl.formatMessage({ id: '知识点' })}</span>
                                    <span className="msgCountNum">
                                        ({sequencemeta.length})
                                    </span>
                                </div>
                            </Menu.Item>}
                        {(entity?.type === 'video' || entity?.type === 'audio' || entity?.type === 'document') &&
                            !(hidecatalogue === '1') && rmanGlobalParameter.includes(globalParams.video_keywords_display) &&
                            <Menu.Item key='5'>
                                <div>
                                    <div>
                                        <span>{intl.formatMessage({ id: '词云' })}</span>
                                        {keywords.length === 0 && <span className="msgCountNum">
                                            ({intl.formatMessage({ id: `${intl.formatMessage({ id: '无' })}` })})
                                        </span>}
                                    </div>
                                </div>
                            </Menu.Item>}
                        {(entity?.type === 'video') && rmanGlobalParameter.includes(globalParams.courseware_screenshot_display) &&
                            <Menu.Item key='7'>
                                <div >
                                    <span>{intl.formatMessage({ id: '课件截图' })}</span>
                                </div>
                            </Menu.Item>}
                    </Menu>
                    {activeTabs === '1' && <div className='entity_info_item'>
                        <BasicMetadata
                            ref={metadataRef}
                            items={metadata.slice(0, 6)}
                            frameRate={frameRate}
                            voiceList={voice}
                            // jumpVideoTime={jumpVideoTime}
                            path={dirTree}
                            detail={contentDetail}
                        />
                    </div>}
                    {activeTabs === '3' && rmanGlobalParameter.includes(globalParams.speech_analysis_display) &&
                        (entity?.type === 'video' || entity?.type === 'audio') &&
                        !(hidecatalogue === '1') && (
                            <div className="voice_div">
                                {voice.length > 0 && (
                                    <>
                                        {(
                                            <div className="paragraph-wrapper">
                                                <div className='tips'>
                                                    <ExclamationCircleOutlined />
                                                    {intl.formatMessage({ id: '此文本为智能语音转写结果，可能存有少量误差，仅供参考。如需修改，请切换至时码模式' })}
                                                </div>

                                                {rmanGlobalParameter.includes(globalParams.voicetext_check_display) && <div className='right_'>
                                                    <Button
                                                        type='link'
                                                        icon={<IconFont type='iconzitifangda' />}
                                                        title={intl.formatMessage({ id: '字号变大' })}
                                                        disabled={fontSize >= 32}
                                                        onClick={(e: any) => {
                                                            e.stopPropagation();
                                                            e.preventDefault();
                                                            setFontSize((pre: number) => pre += 1)
                                                        }}
                                                    />
                                                    <Button
                                                        type='link'
                                                        icon={<IconFont type='iconzitisuoxiao' />}
                                                        title={intl.formatMessage({ id: '字号变小' })}
                                                        disabled={fontSize <= 12}
                                                        onClick={(e: any) => {
                                                            e.stopPropagation();
                                                            e.preventDefault();
                                                            setFontSize((pre: any) => pre -= 1)
                                                        }}
                                                    />
                                                </div>}
                                                <div
                                                    onClick={e => {
                                                        e.stopPropagation();
                                                    }}
                                                    onMouseUp={onSelection}
                                                    style={{ fontSize: fontSize + 'px' }}
                                                    onMouseLeave={onSelection}
                                                    className="paragraph-content"
                                                >
                                                    {voice.map((item, index) => {
                                                        if ((index + 1) % 20 == 0) {
                                                            return (
                                                                <span
                                                                    key={index}
                                                                    data-id={index}
                                                                    onClick={() => {
                                                                        setCurrentTime(item._in);
                                                                    }}
                                                                    className={(() => {
                                                                        let temp: any = `mao_${index}`;
                                                                        const flag = sequencemeta.some(
                                                                            s =>
                                                                                s.inpoint <= item._in &&
                                                                                s.outpoint >= item._out,
                                                                        )
                                                                        if (flag) {
                                                                            temp += ' selected';
                                                                        }
                                                                        if (currentFrame >= item._in && currentFrame < item._out) {
                                                                            temp += ' currentPlay';
                                                                        }
                                                                        return temp;
                                                                    })()
                                                                    }
                                                                >
                                                                    {searchVoice(currentLanguage === 'cn' ? item.text : item.text_en)}
                                                                    <br />
                                                                </span>
                                                            );
                                                        }
                                                        return (
                                                            <span
                                                                key={index}
                                                                data-id={index}
                                                                onClick={() => {
                                                                    setCurrentTime(item._in);
                                                                }}
                                                                className={(() => {
                                                                    let temp: any = `mao_${index}`;
                                                                    const flag = sequencemeta.some(
                                                                        s =>
                                                                            s.inpoint <= item._in &&
                                                                            s.outpoint >= item._out,
                                                                    )
                                                                    if (flag) {
                                                                        temp += ' selected';
                                                                    }
                                                                    if (currentFrame >= item._in && currentFrame < item._out) {
                                                                        temp += ' currentPlay';
                                                                    }
                                                                    return temp;
                                                                })()
                                                                }
                                                            >
                                                                {searchVoice(currentLanguage === 'cn' ? item.text : item.text_en)}
                                                            </span>
                                                        );
                                                    })}
                                                </div>
                                            </div>
                                        )}
                                    </>
                                )}
                            </div>
                        )}
                    {activeTabs === "4" && (entity?.type === 'video' || entity?.type === 'audio') &&
                        !(hidecatalogue === '1') && (
                            <div className='entity_info_item'>
                                {sequencemeta.length > 0 ? (
                                    <div className="sequence_list">
                                        {sequencemeta.map((item, index) => (
                                            <div className={`sequence_item_wrap
                                            ${index === sequencemeta.length - 1 ? currentFrame >= item.inpoint && currentFrame < item.outpoint ? 'active' : '' :
                                                    currentFrame >= item.inpoint && currentFrame < sequencemeta[index + 1]?.inpoint ? 'active' : ''}`
                                            } key={item.guid_}>
                                                <div style={{ width: '100%' }}>
                                                    <PointsItem
                                                        type={entity?.type || ''}
                                                        key={item.guid_}
                                                        detail={item}
                                                        disabled={!canEdit}
                                                        setPlayerSec={() => {
                                                            setPlayerSec(item.inpoint, item.outpoint, item, canEdit)
                                                        }
                                                        }
                                                        getQuerySequencemeta={getQuerySequencemeta}
                                                        downVisible={false}
                                                        keyframeLoading={keyframeFlag && !item.keyframepath}
                                                    // sendMicro={() => createMicroHandle(item)}
                                                    />
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <Empty description={intl.formatMessage({ id: '暂无结果' })} />
                                )}
                            </div>
                        )}
                    {activeTabs === '7' && (entity?.type === 'video') && (
                        <div className='entity_info_item'>
                            {courseware?.length > 0 ? (
                                <div className="courseware_list">
                                    <Timeline mode='left'>
                                        {courseware.map((item: any, index: number) => {
                                            return (
                                                <Timeline.Item
                                                    label={win.timecodeconvert.frame2Tc(item.keyFrameNo, frameRate, false)}
                                                    className={`courseware_item_wrap ${currentCourseware === item.fileGUID ? 'active' : ''} ${courseware.length - 1 === index ? 'lastItem' : ''}`}
                                                >
                                                    <div onClick={() => coursewareClick(item)}>
                                                        <img src={item.filePath} ></img>
                                                        <div className='number'>
                                                            {index + 1} &nbsp;
                                                            {item.fromFileGUID === 'screenshot' ? `${intl.formatMessage({ id: '手动生成' })}` : `${intl.formatMessage({ id: '智能识别' })}`}
                                                        </div>
                                                        {
                                                            item.iconKeyframe == 1 &&
                                                            <div className='currentCover'>
                                                                {intl.formatMessage({ id: '当前素材封面' })}
                                                            </div>
                                                        }
                                                        <div className='deleteBtnDiv'>
                                                            <CloseOutlined
                                                                onClick={(e: any) => coursewareDelete(e, item)}
                                                                className={`deleteBtn`}
                                                            />
                                                        </div>
                                                        {/* {
                                                            !item.iconKeyframe && <div className='setCoverBtn' onClick={(e) => { e.preventDefault(); e.stopPropagation(); updateCover(item) }}>
                                                                设为素材封面
                                                            </div>
                                                        } */}
                                                    </div>
                                                </Timeline.Item>
                                            );
                                        })}
                                    </Timeline>
                                    <div className="pagination">
                                        <Pagination
                                            current={coursewarePage}
                                            total={coursewareTotal}
                                            pageSize={coursewarePageSize}
                                            size="small"
                                            showTotal={total => intl.formatMessage({
                                                id: "共条"
                                              },{total})}
                                            showSizeChanger={false}
                                            onChange={changeCoursewarePage}
                                        />
                                    </div>
                                </div>
                            ) : (
                                <>
                                    <Empty description={intl.formatMessage({ id: '暂无结果' })} />
                                    {/* <div className='item_' onClick={() => reply('PptScreen')}>
                                            <img src={require('@/images/contentlibrary/zhineng.png')} />
                                            <span>发起智能分析</span>
                                        </div> */}
                                </>
                            )}
                        </div>
                    )}
                    {activeTabs === '5' && (entity?.type === 'video' || entity?.type === 'audio' || entity?.type === 'document') &&
                        !(hidecatalogue === '1') && (
                            <div className='entity_info_item'>
                                <div
                                    style={{ height: '100%' }}
                                >
                                    {
                                        keywords.length > 0 ?
                                            <EchartsGroup flag='wordcloud' clickCallback={wordCloudclick} dataList={keywords} />
                                            : <Empty description={intl.formatMessage({ id: '暂无结果' })} />
                                    }
                                </div>
                            </div>
                        )}

                    {/* <Rdrawer graph={graph} x6node={drawerdata} visible={visible} onback={() => setDrawerdata(null)} 
                        updatanodecompare={updatanodecompare} 
                        centerednode={(e: any) => {

                        }} 
                        updatanode={updatanode} 
                        perviewtype={3} 
                        mapid={selectmapid} 
                        courseid={null} 
                        coursename={null} 
                        isedit={false} 
                        setVisible={(e: number) => setVisible(e)}>
                    </Rdrawer> */}
                </Drawer>
            </div>
            {/* <MergeSettingModal
                videoKeyframe={videoKeyframe}
                visible={settingModalVisible}
                onCancel={() => setsmVisible(false)}
                onOk={isComposite ? compositeVideo : finishProduct}
                showTitle={isComposite}
                checkSubTile={voice.length > 0}
            /> */}
        </div>
    )
}

export default Mapv3;