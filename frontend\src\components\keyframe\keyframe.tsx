import React, { FC, useState, useEffect } from 'react'
import { getType } from '@/utils'
import { useSelector} from 'umi';
interface IKeyframeProps {
    src: string // 图片的路径
    root: HTMLDivElement | null // 父级节点 必须设置overflow
    defaultSrc?: string // 加载失败后显示的图片
    className?: string
    type:string
    custom_keyframe?:boolean //自定义素材的标识
    viewFlag?:boolean //针对图例模式默认路径图片缩放
    damaged?:boolean //针对因为网络原因损坏的文件
    isDelete?:boolean //针对我的收藏素材被删除的flag
    resourceGroup?:boolean //群主资源的标识
}

const Keyframe: FC<IKeyframeProps> = ({ src, root,damaged, className,type,viewFlag,custom_keyframe,isDelete,resourceGroup }) => {
    const [img, setImg] = useState<HTMLImageElement | null>()
    useEffect(()=>{
        if(resourceGroup && getType(type) === 'folder' && !custom_keyframe){
            setNewSrc('/rman/static/images/groupFolder.png')
        }else{
            setNewSrc(src)
        }
    },[src])
    const [newSrc,setNewSrc] = useState<string>(src)
    const [visable, setVisable] = useState<boolean>(false)
    const { mobileFlag } = useSelector<{ config: any }, any>(
        ({ config }) => config,
      );
    useEffect(() => {
        setVisable(false)
        if (root && ('IntersectionObserver' in window)) {
            const options = {
                root,
                // threshold: [0, 0.5, 1]
                rootMargin:'0px 0px 26px 0px'
            }
            const io = new IntersectionObserver(entries => {
                entries.forEach(item => {
                    if (item.isIntersecting) {
                        setVisable(true)
                        io.unobserve(item.target)
                    }
                })
            }, options)
            if (img) {
                io.observe(img)
            }
        } else {
            setVisable(true)
        }
    }, [img,newSrc])
    useEffect(()=>{
        if(damaged){
            setNewSrc('/rman/static/images/damage.png')
        }
    },[damaged])
    const imgError = () => {
        //采用统一的有损样式
        setNewSrc('/rman/static/images/damage.png');
        // switch (getType(type)) {
        //     case 'video':
        //         setNewSrc('/rman/static/images/video3.png')
        //         break;
        //     case 'audio':
        //         setNewSrc('/rman/static/images/audio3.png')
        //         break;
        //     case 'document':
        //         setNewSrc('/rman/static/images/documnet3.png')
        //         break;
        //     case 'picture':
        //         setNewSrc('/rman/static/images/picture3.png')
        //         break;
        //     case 'folder':
        //         setNewSrc('/rman/static/images/folder2.png')
        //         break
        //     default:
        //         setNewSrc('/rman/static/images/other.png')
        //         break
        // }
    }

    return (
        <img
            className={
                isDelete?
                ('isDeleteFlag keyframe' + (type === 'folder' ? ' folder' : ''))
                :('keyframe' + (type === 'folder' ? ' folder' : ''))
            }
            onError={imgError} 
            style={{ maxWidth:mobileFlag?'': viewFlag?'50%':'100%', maxHeight: viewFlag?'50%':'100%' }} 
            src={visable ? newSrc : undefined} ref={imgRef => setImg(imgRef)} />
    )
}

export default Keyframe