
.result_search_public{
  // height: 140px !important;
  height: 112px !important;
  transition: all 0.5s;
}

.result_search {
  display: flex;
  flex-direction: column;
  // height: 100px;
  border-radius: 4px;
  // background: url('/rman/static/images/newStyle/searchBg2.png') no-repeat center -5px;
  // background-color: var(--primary-color);
  // background-color: var(--active-bg-color);
  // height: 100px;
  // position: relative;
  flex: 1;
  // &::after{
  //   content: "";
  //   // background: url('/rman/static/images/newStyle/searchBg2.png') no-repeat center -5px;
  //   background-image: url('/rman/static/images/newStyle/bg_left.png'),url('/rman/static/images/newStyle/bg_right.png');
  //   background-repeat: no-repeat, no-repeat;
  //   background-position: left top, right top;
  //   position: absolute;
  //   width: 100%;
  //   border-radius: 4px;
  //   height: 100%;
  //   z-index: 0;
  // }
  .result_search_header{
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 1;
    .top_{
      display: flex;
      flex-direction: row;
      justify-content: center;
      // padding-top: 28px;
      padding-top: 20px;
      .search_box{
        width: 45.6%;
        position: relative;
        .ant-input{
          // height: 50px;
          height: 40px;
          border-radius: 25px;
          font-size: 16px;
          padding-left: 30px;
        }
        .clean{
          cursor: pointer;
          position: absolute;
          right: 106px;
          top: 14px;
          opacity: .5;
        }
        >div{
          cursor: pointer;
          position: absolute;
          right: 4px;
          top: 4px;
          display: flex;
          font-size: 16px;
          flex-direction: row;
          align-items: center;
          color: #FFFFFF;
          width: 94px;
          // height: 42px;
          height: 32px;
          background: var(--primary-color);
          border-radius: 23px;
          >.anticon-search{
            font-size: 20px;
            margin: 0 8px 0 17px;
          }
        }
      }
    }
    .center_{
      width: 100%;
      height: 20px;
      display: flex;
      justify-content: center;
      margin: 12px 0 20px 0;
      >span{
        cursor: pointer;
        margin-left: 24px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #9D9D9D;
      }
      >span.active_{
        color: var(--primary-color);
        font-weight: 600;
      }
    }
    .bottom_{
      display: flex;
      justify-content: space-evenly;
      >.ant-select{
        width: 13%;
        >.ant-select-selector{
          border-radius: 4px;
          height: 36px;
          padding: 2px 11px;
        }
      }
      .ant-picker{
        border-radius: 4px;
      }
      .ant-select.wide{
        width: 16%;
      }
      .resetBtn{
        cursor: pointer;
        width: 40px;
        height: 36px;
        background: #FFFFFF;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: var(--primary-color);
        font-size: 18px;
      }
    }
    .expandDiv{
      position: absolute;
      transform: translate(-50%, 0%);
      cursor: pointer;
      bottom: 0;
      left: 50%;
      width: 40px;
      height: 14px;
      background: var(--primary-color);
      border-radius: 4px 4px 0px 0px;
      opacity: 0.9;
      margin: 0 auto;
      >span{
        color: #FFFFFF;
        display: block;
      }
    }
  }
  .myVideo{
    height: 80px;
    .ant-input-affix-wrapper{
      border-radius: 6px !important;
    }
    .keyword-width {
      width: 100% !important;
    }
    .ant-form{
      display: flex;
      flex-direction:column;
      justify-content: space-between;
      flex-wrap:nowrap;
    }
  }
  .chdMyvideo{
    height: auto;
    .ant-form{
      display: flex;
      flex-direction:column;
      justify-content: space-between;
      flex-wrap:nowrap;
    }
  }
  .chdMyvideo{
    height: auto;
    .ant-form{
      display: flex;
      flex-direction:column;
      justify-content: space-between;
      flex-wrap:nowrap;
    }
  }
  .none{
    display: none;
  }
  .input_width {
    width: 385px;
    margin-right: 20px;
  }

  .searchbox {
    width: 300px;

  }

  .result_bottom {
    margin-left: 20px;
    width: 70px;
    height: 32px;
    color: rgba(255, 255, 255, 1);
  }

  .textcenter {

    .ant-picker-input {
      input {
        text-align: center;
      }
    }
  }
  .ant-input-group{
    .ant-select-selector{
      border-top-left-radius:6px !important;
      border-bottom-left-radius:6px !important;
      border-top-right-radius:0px !important;
      border-bottom-right-radius:0px !important;
    }
    .ant-input-group-addon>.ant-btn{
      border-top-right-radius:6px !important;
      border-bottom-right-radius:6px !important;
    }
    .ant-input-affix-wrapper{
      border-top-right-radius:6px !important;
      border-bottom-right-radius:6px !important;
    }
  }
  .ant-select-selector,.ant-picker{
    border-radius: 6px !important;
  }
  .type-width {
    width: 30% !important;
  }
  .chd-keyword-width {
    width: 100% !important;
  }
  .keyword-width {
    width: 70% !important;
    .anticon {
        position: relative;
        cursor: pointer;
        color: var(--primary-color);
        padding: 0;
        @keyframes wave {
          0% {
            transform: translate(-50%, -50%) scale(.8);
          }

          0% {
            transform: translate(-50%, -50%) scale(0.5);
          }

          100% {
            transform: translate(-50%, -50%) scale(.8);
          }
        }
        &.active {

          &::before,
          &::after {
            content: '';
            position: absolute;
            z-index: 0;
            background-color: var(--active-bg-color);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%) scale(.8);
            animation: wave 1s linear infinite;
            transform-origin: center center;
          }

          &::after {
            background-color: var(--active-bg-color);
            animation: wave 1s linear 0.5s infinite;
          }
        }
      
        .iconfont {
          margin-right: 0 !important;
        }
      }
  }

  .align-form-item {
    & > .ant-form-item-label {
      width: 25%;

    }

    & > .ant-form-item-control {
      width: 75%;
    }
  }
  .ant-select-selection-overflow{
    flex-wrap:nowrap;
    overflow: hidden;
  }
  .result_search_content {
    flex: 1;
    z-index: 1;
    padding: 15px 15px 0 15px;
    .teacher-item {
      .ant-form-item-label {
        width: 30%;
      }
    }
    
    .ant-row{
      .ant-col-6{
        flex: 0 0 24%;
        max-width: 24%;
      }
      .ant-col-5{
        flex: 0 0 19%;
        max-width: 19%;
      }
    }
  }

  .result_search_buttom {
    display: flex;
    align-items: center;
    // bottom: 13px;
    // right: 10px;

    button {
      border-radius: 6px;
    }
    .ant-btn-link{
      color: #525252;
      &:hover{
        color:var(--primary-color)
      }
      &[disabled]{
        color: rgba(0, 0, 0, 0.25)
      }
    }
    .photo{
      font-size: 20px;
      color:var(--primary-color);
      margin-left: 10px;
    }
  }
}
.result_search_expand{
  height: 170px !important;
}

.file_upload {
  height: 28px;
  display: flex;
  align-items: center;
  font-size: 16px;
  color: rgba(121, 121, 121, 1);
  position: relative;
  cursor: pointer;

  .hide_open_file {
    position: absolute;
    opacity: 0;
    width: 80px;
    cursor: pointer;
  }

  img {
    height: 16px;
    margin-right: 6px;
  }
}


//移动端
.search_mobile{
  // margin: 10px 2%;
  width: 100%;
  .ant-btn {
    padding: 3px 10px;
  }
 .catalogue{
  .ant-form{
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: center;
    .ant-input-group.ant-input-group-compact{
      display: flex;
      width: 90%;
      .ant-select{
        .ant-select-selector{
          border-top-left-radius:6px !important;
          border-bottom-left-radius:6px !important;
          border-top-right-radius:0px !important;
          border-bottom-right-radius:0px !important;
        }
      }
      .ant-input-group-wrapper{
        border-radius: 0;
        .ant-input-affix-wrapper{
          border-radius: 0;
        }
      }
    }
    >.ant-btn{
      margin-left: 3%;
    }
  }
 } 
 .ant-modal-mask{
  transition: all .5s;
 }
 .moreSearch{
    &.none{
      top:-100%;
    }
    position: fixed;
    top: 0;
    left: 0;
    transition: top .5s;
    background: white;
    z-index: 1000;
    width: 100%;
    // height: 60%;
    padding: 5px 5%;
    .head{
      margin-top: 20px;
      text-align:center;
      >span:first-child{
        font-size: 18px;
      }
      .anticon{
        position: absolute;
        right: 7%;
        line-height:30px;
      }
    }
    .public_group{
      margin-top: 20px;
      >.item{
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 20px;
        >div{
          width: 100%;
        }
      }
      .ant-picker-time-panel{
        display: none;
      }
    }
    .btns{
      display: flex;
      flex-direction: row;
      justify-content: space-evenly;
      flex-wrap: nowrap;
      margin-bottom: 10px;
    }
  }
}