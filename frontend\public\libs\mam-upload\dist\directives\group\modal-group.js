﻿var mamUpload;
if (typeof define === "function" && define.amd)
{
    mamUpload = require('mam-upload');
}
else
{
    mamUpload = window.mamUpload;
}

var uploadModalgroupCtrl = ["$scope", "$uibModalInstance", "$stateParams", "$state", "$http", "opts",
    function($scope, $uibModalInstance, $stateParams, $state, $http, opts) {

    var transfer = mamUpload.uploader.transfers[opts.uploadParams.transferType];
    var validateFun;
    var type = _.find(nxt.config.entityTypes, { code: 'video' });
    $scope.model = {};
    $scope.video = null;
    $scope.audios = [];
    var metadatas = [];

    $scope.addFiles = function() {
        transfer.openFileSelector(function(files) {
            handleFiles(files);
        });
    }

    $scope.getValidateFun = function(func) {
        validateFun = func;
    }

    $scope.ok = function() {
        var validateResult = validateFun();
        if (validateResult.success) {
            $scope.model.metadata.name = _.find($scope.model.metadata.field, { fieldName: 'name_' }).value;
            if ($scope.video != null)
                $scope.model.files.unshift($scope.video);
            _.forEach($scope.audios, function(item) {
                $scope.model.files.push(item);
            });
            $uibModalInstance.close($scope.model);
        } else {
            mam.message.error('元数据信息未通过验证，请修改后再试。');
        }
        //var isAllPass = validateFun();
        //if (!isAllPass.ispass) return false;
        //$scope.metadata.field = isAllPass.entityInfo;
        //var result = {
        //    metadata: $scope.metadata,
        //    files: []
        //};
        //if ($scope.video != null)
        //    result.files.unshift($scope.video);
        //if ($scope.audio.length > 0) {
        //    for (var i = 0; i < $scope.audio.length; i++) {
        //        result.files.push($scope.audio[i]);
        //    }
        //}

        //$uibModalInstance.close(result);
    }

    $scope.close = function() {
        $uibModalInstance.close(null);
    }

    $scope.deleteVideo = function() {
        $scope.video = null;
    }

        $scope.onProgramformChange = function (value, oldValue) {
            var newMetadata = metadatas[$scope.model.entityType][value];
            var programformField = _.find(newMetadata, { fieldName: 'programform' });
            if (programformField != null) {
                programformField.value = '["' + value + '"]';
            }
            _.find(newMetadata, { fieldName: 'name_' }).value = _.find($scope.model.metadata.field, { fieldName: 'name_' }).value;
            _.find(newMetadata, { fieldName: 'createUser_' }).value = _.find($scope.model.metadata.field, { fieldName: 'createUser_' }).value;
            _.find(newMetadata, { fieldName: 'createDate_' }).value = _.find($scope.model.metadata.field, { fieldName: 'createDate_' }).value;
            $scope.model.metadata.field = newMetadata;
            autoFillUploadFieldUserInfoSetting($scope.model.metadata.field);
        }

        function autoFillUploadFieldUserInfoSetting(fields){
            if (nxt.config.autoFillUploadFieldUserInfoSetting)//根据配置自动填写元数据
            {
                var autoSetting = nxt.config.autoFillUploadFieldUserInfoSetting;
                _.forEach(autoSetting, function(autoField){
                    var field = _.find(fields, function (o) {
                        return o.fieldName === autoField.fieldName && o.fieldPath === autoField.fieldPath;
                    });
                    if (field != null) {
                        field.value = autoField.value.replace(/\$\{(.*?)\}/g, function (outer, content) {
                            var func = new Function(undefined, 'return ' + content);
                            return func.apply(window, []);
                        }) || '';
                    }
                })
            }
        }

        function getCookie(cname){
            var name = cname + "=";
            var ca = document.cookie.split(';');
            for(var i=0; i<ca.length; i++) 
            {
                var c = ca[i].trim();
                if (c.indexOf(name)==0) return c.substring(name.length,c.length);
            }
            return "";
        }

    function getMetadata(callback) {
        var data = {
            source: opts.uploadParams.module
        }
        let apiVersion = getCookie('apiVersion');
            var url = '/upload/get-all-fields-by-source'
            if (apiVersion && apiVersion === 'mamcore2.3'){
                url = '/scntm/v1/old/upload/get-all-fields-by-source'
            }
        $http.post("~" + url, data).then(function(res) {
            metadatas = res.data;
            var metadata = metadatas[type.code];
            if (type.code === 'video' && !(metadata instanceof Array))
            {
                metadata = metadata.data;
            }
            $scope.model.metadata.field = metadata;

            autoFillUploadFieldUserInfoSetting($scope.model.metadata.field);

            callback();
        });
    }

    function handleFiles(files) {
        _.forEach(files, function(file) {
            var ext = file.metadata.ext.toLowerCase();
            if (ext == ".avi") {
                $scope.video = file;
                if ($scope.model.metadata.name == null || $scope.model.metadata.name == "") {
                    $scope.model.metadata.name = file.metadata.name;
                    _.find($scope.model.metadata.field, { fieldName: 'name_' }).value = file.metadata.name;
                }
            }
            if (ext == ".wav") {
                $scope.audios.push(file);
            }
        });
        $scope.$applyAsync();
    }

    function init() {
        $scope.model = {
            entityType: type.code,
            files: [],
            metadata: {},
            status: 'added',
            progress: 0
        }
        getMetadata(function() {
            handleFiles(opts.files);
        });
    }

    init();

}];

angular.module('mam-upload').registerController("uploadModalgroupCtrl", uploadModalgroupCtrl);