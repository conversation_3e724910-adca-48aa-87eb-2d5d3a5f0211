import React, { FC } from 'react';
import {
  CheckCircleFilled,
  CloseOutlined,
  SyncOutlined,
  CloseCircleFilled,
  PlayCircleOutlined,
  PauseCircleOutlined,
} from '@ant-design/icons';
import uploadTypes from '@/types/uploadTypes';
import { changesize } from '@/utils';
import { useDispatch } from 'umi';
import { Space, Popconfirm } from 'antd';
import uploadApis from '@/service/uploadApis';
import OOSDK from '@/otherStorage/ctyun';

interface ITaskProps {
  className?: string;
  task: uploadTypes.IUploadTask;
}

const Task: FC<ITaskProps> = props => {
  const dispatch = useDispatch();
  // const [stop, setStop] = useState(false);
  const removeTask = () => {
    if (props.task.progress !== 1) {
      if(props.task.storage){ //其他存储单独处理
        if(props.task.uploader.uploadId){ //仅对分片终止
          console.log('removeTask',props.task);
          if(props.task.product==='ctyun'){
            OOSDK.abortMultipartUpload({
              Bucket:props.task.uploader.Bucket,
              Key:props.task.uploader.Key,
              UploadId:props.task.uploader.uploadId,
            })
          }else if(props.task.product==='aliyun'){
            props.task.uploader.client.cancel();
          }else{

          }
          
        }
      }else{
        // 暂停任务
        props.task.uploader.stop(props.task.guid);
        // todo 后台取消接口
        uploadApis.cancelFileTask(props.task.guid).then(res => {
          if (res?.success) {
            console.log('cancel file success');
          }
        });
      }
    }
    dispatch({
      type: 'upload/removeTask',
      payload: {
        value: props.task.guid,
      },
    });
  };

  const handleStopTask = () => {
    // setStop(true);
    // props.task.uploader.stop(true);
    dispatch({
      type: 'upload/updateTaskPause',
      payload: {
        value: {
          pause: true,
          guid: props.task.guid,
        },
      },
    });
  };

  const handleStartTask = () => {
    // setStop(false);
    // props.task.uploader.upload();
    dispatch({
      type: 'upload/updateTaskPause',
      payload: {
        value: {
          pause: false,
          guid: props.task.guid,
        },
      },
    });
  };

  return (
    <div className={`task task_hearder ${props.className || ''}`}>
      <div>
        {props.task.progress !== 1 ? (
          <SyncOutlined spin={!props.task.pause} />
        ) : props.task.status === -1 ? (
          <CloseCircleFilled />
        ) : (
          <CheckCircleFilled />
        )}
      </div>
      <div title={props.task.name}>{props.task.name}</div>
      <div title={changesize(props.task.size)}>
        {changesize(props.task.size)}
      </div>
      <div title={props.task.folderName}>{props.task.folderName}</div>
      <div>
        {Number(
          (props.task.progress * 100).toString().match(/^\d+(?:\.\d{0,2})?/),
        ) + '%'}
      </div>
      <Space className="opt-btn">
        {props.task.progress !== 1 &&
          (props.task.pause ? (
            <PlayCircleOutlined onClick={handleStartTask} />
          ) : (
            <PauseCircleOutlined onClick={handleStopTask} />
          ))}
        <CloseOutlined onClick={removeTask} />
      </Space>
      <div
        className={`progress_bar${
          props.task.progress === 1
            ? props.task.status === -1
              ? ' progress_bar_error'
              : ' progress_bar_success'
            : ''
        }`}
        style={{ width: props.task.progress * 100 + '%' }}
      />
    </div>
  );
};

export default Task;
