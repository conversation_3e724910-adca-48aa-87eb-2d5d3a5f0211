!function(e){function t(n){if(r[n])return r[n].exports;var i=r[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,t),i.l=!0,i.exports}var r={};t.m=e,t.c=r,t.d=function(e,r,n){t.o(e,r)||Object.defineProperty(e,r,{configurable:!1,enumerable:!0,get:n})},t.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(r,"a",r),r},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="/dist/",t(t.s=28)}([function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n={asyncLoadedScripts:{},asyncLoadedScriptsCallbackQueue:{},getScriptDomFromUrl:function(e){var t;return/.+\.js$/.test(e)?(t=document.createElement("SCRIPT"),t.setAttribute("type","text/javascript"),t.setAttribute("src",e)):/.+\.css$/.test(e)&&(t=document.createElement("link"),t.href=e,t.type="text/css",t.rel="stylesheet"),t},asyncLoadScript:function(e,t){var r=n;if(void 0!=r.asyncLoadedScripts[e])return void(t&&"function"==typeof t&&(0==r.asyncLoadedScripts[e]?(r.asyncLoadedScriptsCallbackQueue[e]||(r.asyncLoadedScriptsCallbackQueue[e]=[]),r.asyncLoadedScriptsCallbackQueue[e].push(t)):t.apply(r,[])));r.asyncLoadedScripts[e]=0;var i=r.getScriptDomFromUrl(e);i.readyState?i.onreadystatechange=function(){if(("loaded"==i.readyState||"complete"==i.readyState)&&(i.onreadystatechange=null,r.asyncLoadedScripts[e]=1,t&&"function"==typeof t&&t.apply(r,[]),r.asyncLoadedScriptsCallbackQueue[e])){for(var n=0,o=r.asyncLoadedScriptsCallbackQueue[e].length;n<o;n++)r.asyncLoadedScriptsCallbackQueue[e][n].apply(r,[]);r.asyncLoadedScriptsCallbackQueue[e]=void 0}}:i.onload=function(){if(r.asyncLoadedScripts[e]=1,t&&"function"==typeof t&&t.apply(r,[]),r.asyncLoadedScriptsCallbackQueue[e]){for(var n=0,i=r.asyncLoadedScriptsCallbackQueue[e].length;n<i;n++)r.asyncLoadedScriptsCallbackQueue[e][n].apply(r,[]);r.asyncLoadedScriptsCallbackQueue[e]=void 0}},document.getElementsByTagName("head")[0].appendChild(i)},getFileNameFromUrl:function(e){return e.substring(e.lastIndexOf("/")+1,e.length)},isIncludeScript:function(e){for(var t=/js$/i.test(e),r=document.getElementsByTagName(t?"script":"link"),n=0;n<r.length;n++)if(-1!=r[n][t?"src":"href"].indexOf(e))return!0;return!1},loadScripts:function(e){if(e instanceof Array){for(var t=[],r=0;r<e.length;r++)t.push(new Promise(function(t,i){n.isIncludeScript(n.getFileNameFromUrl(e[r]))?t():n.asyncLoadScript(e[r],function(){t()})}));return Promise.all(t)}return new Promise(function(e,t){e()})},getExtensions:function(e){if(!e)return"";for(var t=e.length;0!=t;t--)if("."==e[t])return e.substring(t,e.length);return""},getExtension:function(e){var t=n.getExtensions(e);return t.length>0?t.substring(1,t.length):""},getFileName:function(e){for(var t=e.length,r=e.length;0!=r;r--)if("."!=e[r]||t!=e.length){if("\\"==e[r])return e.substring(r+1,t)}else t=r;return e.substring(0,t)},getFullFileName:function(e){for(var t=e.length;0!=t;t--)if("\\"==e[t])return e.substring(t+1,e.length);return e},getTypeByExt:function(e,t){e=e.toLowerCase(),0!==e.indexOf(".")&&(e="."+e);var r=_.get(window,"nxt.config.entityTypes",[]);t&&0===r.length&&(r=_.get(t,"entityTypes",[]));for(var n=0;n<r.length;n++)if(-1!=r[n].extensions.indexOf(e))return r[n];return _.find(r,{code:"other"})},formatSize:function(e,t,r){var n;for(r=r||["B","KB","MB","GB","TB"];(n=r.shift())&&e>1024;)e/=1024;return("B"===n?e:e.toFixed(t||2))+" "+n},prompt:function(e){mam&&mam.prompt&&mam.prompt(e)},msgOk:function(e){mam&&mam.message&&mam.message.ok&&mam.message.ok(e)}};t.default=n},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n={};t.default=n},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(0),i={getGetParamStr:function(e,t){var r="";if(void 0!=e&&!$.isEmptyObject(e)){for(var n in e)r+=t?"/"+e[n]:n+"="+e[n]+"&";t||(r="?"+r.substring(0,r.length-1))}return r},get:function(e,t){var r=$.Deferred(),o=e;return t||(t={}),o=e+i.getGetParamStr(t),$.ajax({type:"get",xhrFields:{withCredentials:!(e.indexOf("token=")>-1)},url:o}).then(function(e){e.success?r.resolve(e):(console.error("response",e),n.default.prompt(l("system."+e.data.code,e.data.title)),r.reject(e))},function(e){r.reject(e)}),r},post:function(e,t,r){var i=$.Deferred();t||(t={});var o={type:"post",data:t,contentType:"application/json",processData:!1,xhrFields:{withCredentials:!(e.indexOf("token=")>-1)},url:e};return r&&void 0!==r.contentType&&(o.contentType=r.contentType),r&&void 0!==r.processData&&(o.processData=r.processData),"application/json"===o.contentType&&(o.data=JSON.stringify(t)),$.ajax(o).then(function(e){e.success?i.resolve(e):(console.error("response",e),n.default.prompt(l("system."+e.error.code,e.error.title)),i.reject(e))},function(e){i.reject(e)}),i}};t.default=i},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){var n=r(0),i=(r(1).default,r(2).default),o=r(9),s=function(t){function r(){return window.nxt&&window.nxt.config?window.nxt.config:t.configInst?t.configInst:void 0}function s(){if(U.getFilesByStatus("progress","deleteing").length>0)return l("upload.closePageTip","当前页面存在未完成的上传任务，关闭页面会导致上传失败，你确定要关闭吗？")}function a(e){return _.isUndefined(e.lastModifiedDate)?e.lastModified:e.lastModifiedDate.getTime()}function u(e){var t=_.find(r().entityTypes,{code:e.entityType});return null==t&&(t=_.find(r().entityTyps,{isOther:!0})),null==t?"":t.keyframe.replace("~","")}function f(){_.forEach(P,function(e){"added"!==e.status&&"success"!==e.status&&(e.status="error")}),$(window).off("beforeunload",s),n.default.prompt(l("upload.unauthorized","你未登录或已超时，请重新登录。")),location.href=r().loginUrl+"?login_backUrl="+location.href}function c(){null==S&&(E=_.uniqueId("mam-web-transfer-"),S=$('<input id="'+E+'" type="file" multiple style="display:none"/>'),$("body").append(S))}function p(){null!=S&&(S.unbind("change"),S.remove(),S=null)}function h(e,t){$(U).trigger(e,t)}function d(e,t){if(6==r().storageType)return y(e,t);if(4==r().storageType){var n={Bucket:e.bucket,Key:t.location.replace(/\\/g,"/")},i={accessKeyId:e.keyId,secretAccessKey:e.secretKey,endpoint:e.serviceURL,sslEnabled:!e.useHttp,s3ForcePathStyle:!0,signatureVersion:e.version};if(AWS.config.update(i),e&&e.region&&(AWS.config.region=e.region),A=new AWS.S3({apiVersion:"2006-03-01"}),!(t.fileSize<=5242880)){var o=A.createMultipartUpload(n).promise();t.createUploadPromise=o;var s=$.Deferred();return t.createUploadPromise.then(function(e){t.uploadId=e.UploadId,s.resolve()}).catch(function(e){s.reject(),console.info(e)}),s.promise()}}}function g(e){var t={accessKeyId:e.keyId,secretAccessKey:e.secretKey,endpoint:e.serviceURL,sslEnabled:!e.useHttp,s3ForcePathStyle:!0,signatureVersion:"v"+e.version,apiVersion:"2006-03-01"};e.region&&(AWS.config.region=e.region),A=new OOS.S3(t)}function y(e,t){if(6==r().storageType&&(g(e),!(t.fileSize<=5242880))){var n={Bucket:e.bucket,Key:t.location.replace(/\\/g,"/")},i=A.createMultipartUpload(n).promise();t.createUploadPromise=i;var o=$.Deferred();return t.createUploadPromise.then(function(e){t.uploadId=e.UploadId,o.resolve()}).catch(function(e){o.reject(),console.info(e)}),o.promise()}}function v(e){h("task-init-before",e);var o=[],s=r().server+"/upload/multipart/init";t.loginToken&&(s+="?token="+t.loginToken),i.post(s,e).then(function(t){t=t.data;for(var i=0;i<t.files.length;i++){var s=e.files[i].file;e.files[i]=t.files[i],e.files[i].file=s,e.files[i].status="prepared",e.files[i].fileSizeString=n.default.formatSize(t.files[i].fileSize),e.files[i].progress=0,e.sizeTotal+=t.files[i].fileSize,e.chunkTotal+=t.files[i].chunkTotal;var a=d(t.ossClientInfo,e.files[i]);a&&o.push(a)}e.taskId=t.taskId,e.entityType=t.entityType,e.fileTotal=t.fileTotal,e.targetFolder=t.targetFolder,e.targetFolderName=t.targetFolderName,e.targetType=t.targetType,e.keyframe=t.keyframe,e.status="prepared",e.inited=!0,e.sizeTotalString=n.default.formatSize(e.sizeTotal),e.isJsUpload=t.isJsUpload,e.ossClientInfo=t.ossClientInfo,h("task-init-success",e),(4==r().storageType||6==r().storageType)&&e.isJsUpload&&o.length>0?$.when.apply($,o).done(function(){U.prepareUpload()}):U.prepareUpload()},function(t){if(h("task-init-error",[e,t]),401===t.status)return void f(P);e.status="error",_.forEach(e.files,function(e){e.status="error"}),n.default.prompt(l("upload.","上传失败：${text}",{text:t.data.desc||t.data.title})),U.prepareUpload()})}function m(e,t,n){r().webUploadMd5Enable?(null==e.file.fileReader&&(e.file.fileReader=new FileReader),e.file.fileReader.onload=function(e){var t=new o;t.appendBinary(e.target.result);var r=t.end();t.destroy(),n(r)},e.file.fileReader.onerror=function(t){T(e)},e.file.fileReader.readAsBinaryString(t)):n("")}function k(e,t){null!=t.startTime&&(_.isNumber(t.surplusTime)||(t.surplusTime=0),t.surplusTime=(new Date-t.startTime)*(t.chunkTotal-t.chunkIndex),_.isNumber(e.surplusTime)||(e.surplusTime=0),e.surplusTime=(new Date-t.startTime)*(e.chunkTotal-e.chunkFinished)),t.startTime=new Date}function T(e){e.file.hasOwnProperty("errorCount")||(e.file.errorCount=0),e.file.errorCount<4?(e.file.errorCount++,B[e.file.fileId]=setTimeout(function(){U.upload(e)},3e3)):(e.task.status=e.file.status="error",h("task-upload-error",e.task),U.prepareUpload())}function w(e,t){if(e==t)return 100;var r=e/t*100;return-1==r.toString().indexOf(".")?r:r.toFixed(2)}function I(e){return w(e.chunkIndex,e.chunkTotal)}function b(e){for(var t=0,r=0;r<e.files.length;r++)t+=e.files[r].chunkIndex;return e.chunkFinished=t,w(e.chunkFinished,e.chunkTotal)}var S,E,A,U=this,P=[],B={};this.on=function(e,t){$(U).on(e,t)},this.off=function(e,t){$(U).off(e,t)},this.openFileSelector=function(e,r){p(),c(),!0===r?S.removeAttr("multiple"):S.attr("multiple","multiple"),S.on("change",function(){if(""!==S.val()){var i=S[0].files;!0===r&&(i=i[0]);var o=[];_.forEach(i,function(e){var r="."+n.default.getExtension(e.name);o.push({entityType:n.default.getTypeByExt(r,t.configInst).code,fileName:e.name,metadata:{name:n.default.getFileName(e.name),ext:r},status:"added",progress:0,file:e})}),e(o),c()}}),S.click(function(e){e.stopPropagation()}),S.trigger("click")},this.createTask=function(e,t){var r=[];switch(t.taskType){case 1:_.isArray(e)||(e=[e]),_.forEach(e,function(e){e.files&&e.files.length>0?(e.files=_.map(e.files,function(e){return{file:e.file,fileName:e.file.name,fileSize:e.file.size,fileLastModifiedDate:a(e.file),type:e.type}}),e.files.unshift({file:e.file,fileName:e.file.name,fileSize:e.file.size,fileLastModifiedDate:a(e.file),type:void 0!==e.type?e.type:"0"})):e.files=[{file:e.file,fileName:e.file.name,fileSize:e.file.size,fileLastModifiedDate:a(e.file),type:void 0!==e.type?e.type:"0"}],delete e.file,r.push(e)});break;case 2:case 3:_.forEach(e.files,function(e){e.fileName=e.file.name,e.fileSize=e.file.size,e.fileLastModifiedDate=a(e.file)}),r.push(e)}_.forEach(r,function(e){e.taskType=t.taskType,e.transferType=t.transferType,e.targetFolder=t.targetFolder,e.relationContentType=t.relationContentType,e.relationContentId=t.relationContentId,U.addTask(e)})},this.addTask=function(e){e.status="init",e.progress=e.sizeTotal=e.chunkFinished=e.chunkTotal=0,e.keyframe=u(e),P.push(e),v(e)},this.prepareUpload=function(){var e=U.getFilesByStatus("progress"),t=r().webUploadThreads-e.length;if(!(t<=0)){var n=U.getFilesByStatus("prepared");if(t>n.length&&(t=n.length),0!==t)for(var i=0;i<t;i++)n[i].task.status=n[i].file.status="progress",U.upload(n[i])}},this.uploadByBackend=function(e){var n=e.file,o=e.task;if(n.chunkIndex<n.chunkTotal){k(o,n);var s=n.chunkIndex*n.chunkSize,a=Math.min(n.fileSize,s+n.chunkSize),u=new FormData,l=n.file.slice(s,a);if(null==l||0==l.size)return e.task.status=e.file.status="error",h("task-upload-error",e.task),e.file.file=null,void U.prepareUpload();m(e,l,function(s){if(u.append("fileData",l),u.append("taskId",n.taskId),u.append("fileId",n.fileId),u.append("chunkIndex",n.chunkIndex),u.append("md5",s),"deleteing"===o.status)return void U.clearTask(o);var a=r().server+"/upload/multipart";t.loginToken&&(a+="?token="+t.loginToken),i.post(a,u,{contentType:!1,processData:!1}).then(function(t){if("deleteing"===o.status)return void U.clearTask(o);t=t.data,n=_.find(o.files,{fileId:t.fileId}),n.chunkIndex=t.chunkIndex,3===t.taskStatus?(n.progress=o.progress=100,n.surplusTime=o.surplusTime=null,delete n.fileReader,n.status=o.status="success",h("task-upload-success",o),U.prepareUpload()):(o.progress=b(o),n.progress=I(n),n.status=o.status="progress",h("task-upload-progress",o),n.errorCount=0,U.upload(e))},function(t){if(401===t.status)return void f();T(e)})})}else n.hasOwnProperty("errorCount")&&(delete B[n.fileId],delete n.errorCount),delete n.fileReader,n.surplusTime=null,n.progress=100,n.status="success",o.progress=b(o),h("task-upload-success",o),U.prepareUpload()};var R=function(e){var n=new FormData;n.append("taskId",e.taskId),n.append("fileId",e.fileId),n.append("chunkIndex",e.chunkIndex),n.append("partInfo",JSON.stringify(e.partInfo)),n.append("uploadId",e.uploadId),n.append("checkPoint",e.checkPoint);var o=r().server+"/upload/multipart";return t.loginToken&&(o+="?token="+t.loginToken),i.post(o,n,{contentType:!1,processData:!1})};this.uploadByWeb=function(t){var r=t.file,n=t.task,i={region:n.ossClientInfo.region,accessKeyId:n.ossClientInfo.accessKeyId,accessKeySecret:n.ossClientInfo.accessKeySecret,bucket:n.ossClientInfo.bucketName};n.ossClientInfo.endpoint&&(i.endpoint=n.ossClientInfo.endpoint);var o=new this.OSS.Wrapper(i);if(r.fileSize<=102400)t.fileReader=new FileReader,t.fileReader.onload=function(t){o.put(r.location,new e(this.result)).then(function(e){R(r).then(function(e){r.progress=n.progress=100,r.surplusTime=n.surplusTime=null,delete r.fileReader,r.status=n.status="success",h("task-upload-success",n),U.prepareUpload()})},function(e){console.error(e)})},t.fileReader.readAsArrayBuffer(r.file);else{var s={partSize:r.chunkSize,progress:function(e,i,o){return function(e){"deleteing"===n.status?U.clearTask(n):(r.partInfo=i.doneParts[i.doneParts.length-1],r.uploadId=i.uploadId,r.checkPoint=JSON.stringify(i,function(e,t){if("file"!==e)return t}),R(r).then(function(t){if("deleteing"===n.status)return void U.clearTask(n);t=t.data,r.chunkIndex=t.chunkIndex,3===t.taskStatus?(r.progress=n.progress=100,r.surplusTime=n.surplusTime=null,delete r.fileReader,r.status=n.status="success",h("task-upload-success",n),U.prepareUpload(),e()):(n.progress=b(n),r.progress=I(r),r.status=n.status="progress",h("task-upload-progress",n),r.errorCount=0,e())},function(e){if(401===e.status)return void f();T(t)}))}}};r.checkPoint&&(s.checkpoint=JSON.parse(r.checkPoint),s.checkpoint.file=r.file),o.multipartUpload(r.location,r.file,s).then(function(e){console.log("upload success",e)},function(e){console.log(e)})}},this.uploadByS3=function(e){var n=e.file,o=e.task;if(n.chunkIndex<n.chunkTotal){var s=n.chunkIndex*n.chunkSize,a=Math.min(n.fileSize,s+n.chunkSize),u=new FormData,l=n.file.slice(s,a);if(null==l||0==l.size)return e.task.status=e.file.status="error",h("task-upload-error",e.task),e.file.file=null,void U.prepareUpload();var c=function(n,o){if(u.append("taskId",o.taskId),u.append("fileId",o.fileId),u.append("chunkIndex",o.chunkIndex),u.append("partInfo",o.partInfo),u.append("uploadId",o.uploadId),"deleteing"===n.status)return void U.clearTask(n);var s=r().server+"/upload/multipart";t.loginToken&&(s+="?token="+t.loginToken),i.post(s,u,{contentType:!1,processData:!1}).then(function(t){if("deleteing"===n.status)return void U.clearTask(n);t=t.data,o=_.find(n.files,{fileId:t.fileId}),o.chunkIndex=t.chunkIndex,3===t.taskStatus?(o.progress=n.progress=100,o.surplusTime=n.surplusTime=null,delete o.fileReader,o.status=n.status="success",h("task-upload-success",n),U.prepareUpload()):(n.progress=b(n),o.progress=I(o),o.status=n.status="progress",h("task-upload-progress",n),o.errorCount=0,U.upload(e))},function(t){if(401===t.status)return void f();T(e)})};if(n.fileSize<=5242880)A.putObject({Body:n.file,Bucket:o.ossClientInfo.bucket,Key:n.location.replace(/\\/g,"/")},function(t,r){t?(console.info(t,t.stack),T(e)):(r.ETag&&(n.partInfo=r.ETag),c(o,n),console.info(r))});else{var p={Body:l,Bucket:o.ossClientInfo.bucket,Key:n.location.replace(/\\/g,"/"),PartNumber:n.chunkIndex+1,UploadId:n.uploadId};A.uploadPart(p,function(t,r){t?(console.info(t,t.stack),T(e)):(r.ETag&&(n.partInfo=r.ETag.replace(/"/g,"")),c(o,n),console.info(r))})}}else n.hasOwnProperty("errorCount")&&(delete B[n.fileId],delete n.errorCount),delete n.fileReader,n.surplusTime=null,n.progress=100,n.status="success",o.progress=b(o),h("task-upload-success",o),U.prepareUpload()},this.uploadByOos=function(e){var n=e.file,o=e.task;if(n.chunkIndex<n.chunkTotal){var s=n.chunkIndex*n.chunkSize,a=Math.min(n.fileSize,s+n.chunkSize),u=new FormData,l=n.file.slice(s,a);if(null==l||0==l.size)return e.task.status=e.file.status="error",h("task-upload-error",e.task),e.file.file=null,void U.prepareUpload();var c=function(n,o){if(u.append("taskId",o.taskId),u.append("fileId",o.fileId),u.append("chunkIndex",o.chunkIndex),u.append("partInfo",o.partInfo),u.append("uploadId",o.uploadId),"deleteing"===n.status)return void U.clearTask(n);var s=r().server+"/upload/multipart";t.loginToken&&(s+="?token="+t.loginToken),i.post(s,u,{contentType:!1,processData:!1}).then(function(t){if("deleteing"===n.status)return void U.clearTask(n);t=t.data,o=_.find(n.files,{fileId:t.fileId}),o.chunkIndex=t.chunkIndex,3===t.taskStatus?(o.progress=n.progress=100,o.surplusTime=n.surplusTime=null,delete o.fileReader,o.status=n.status="success",h("task-upload-success",n),U.prepareUpload()):(n.progress=b(n),o.progress=I(o),o.status=n.status="progress",h("task-upload-progress",n),o.errorCount=0,U.upload(e))},function(t){if(401===t.status)return void f();T(e)})};if(A||g(o.ossClientInfo),n.fileSize<=5242880)A.putObject({Body:n.file,Bucket:o.ossClientInfo.bucket,Key:n.location.replace(/\\/g,"/")},function(t,r){t?(console.info(t,t.stack),T(e)):(r.ETag&&(n.partInfo=r.ETag),c(o,n),console.info(r))});else{var p={Body:l,Bucket:o.ossClientInfo.bucket,Key:n.location.replace(/\\/g,"/"),PartNumber:n.chunkIndex+1,UploadId:n.uploadId};A.uploadPart(p,function(t,r){t?(console.info(t,t.stack),T(e)):(r.ETag&&(n.partInfo=r.ETag.replace(/"/g,"")),c(o,n),console.info(r))})}}else n.hasOwnProperty("errorCount")&&(delete B[n.fileId],delete n.errorCount),delete n.fileReader,n.surplusTime=null,n.progress=100,n.status="success",o.progress=b(o),h("task-upload-success",o),U.prepareUpload()},this.upload=function(e){e.task.isJsUpload?4==r().storageType?this.uploadByS3(e):6==r().storageType?this.uploadByOos(e):this.uploadByWeb(e):this.uploadByBackend(e)},this.continueUpload=function(e,t,r){null==t.file?U.openFileSelector(function(i){var o=U.calcFileMd5(i[0].file);if(t.fileMd5!==o)return n.default.prompt(l("upload.fileDiffer","选择的文件不一致，请重新上传")),void(r&&r.apply(window,[l("upload.fileDiffer","选择的文件不一致，请重新上传")]));t.file=i[0].file,e.status=t.status="prepared",U.prepareUpload()},!1):e.inited?(e.status=t.status="prepared",U.prepareUpload()):v(e)},this.getUnfinishedTask=function(e,o,s){var a=$.Deferred();null==e&&(e=""),_.isNumber(o)||(o=0),_.isNumber(o)||(o=1);var u=r().server+"/upload/get-unfinished-task?relationId="+e+"&relationContentType="+o+"&targetType="+s;return t.loginToken&&(u+="&token="+t.loginToken),i.get(u).then(function(e){var t=[];_.forEach(e.data,function(e){e.status="error",e.inited=!0,e.sizeTotal=e.chunkFinished=e.chunkTotal=0,_.forEach(e.files,function(t){t.fileSizeString=n.default.formatSize(t.fileSize),t.progress=I(t),t.status=100===t.progress?"success":"error",e.sizeTotal+=t.fileSize,e.chunkTotal+=t.chunkTotal}),e.progress=b(e),e.sizeTotalString=n.default.formatSize(e.sizeTotal),P.push(e),t.push(e)}),a.resolve(t)},function(e){a.reject(e)}),a},this.canDeleteTask=function(e){return null!=e&&("init"!=e.status&&("deleteing"!=e.status&&("progress"!=e.status||e.chunkFinished!=e.chunkTotal-1)))},this.removeTask=function(e){_.remove(P,function(t){return t.taskId===e.taskId})},this.deleteTask=function(e){if(this.canDeleteTask(e))if(!0===e.inited)switch(_.forEach(e.files,function(e){null!=B[e.fileId]&&(timeout.cancel(B[e.fileId]),delete B[e.fileId])}),e.status){case"progress":return void(e.status="deleteing");case"prepared":case"error":return void U.clearTask(e);default:U.removeTask(e),h("task-delete-success",e)}else U.removeTask(e),h("task-delete-success",e)},this.clearTask=function(e){var n={};if(e.files&&e.files.length>0){_.forEach(e.files,function(e){n[e.fileId]=e.uploadId});var o=r().server+"/upload/delete-task";t.loginToken&&(o+="?token="+t.loginToken),i.post(o,{taskId:e.taskId,fileAndTag:n}).then(function(t){U.removeTask(e),h("task-delete-success",e),U.prepareUpload()},function(t){h("task-delete-error",[e,t])})}},this.calcFileMd5=function(e){return o.hash(e.name+e.size+a(e))},this.getFilesByStatus=function(){for(var e=[].slice.call(arguments,0),t=[],r=0;r<P.length;r++)for(var n=0;n<P[r].files.length;n++)for(var i=0;i<e.length;i++)if(P[r].files[n].status===e[i]){t.push({task:P[r],file:P[r].files[n]});break}return t},function(){$(window).on("beforeunload",s)}()};t.default=s}.call(t,r(4).Buffer)},function(e,t,r){"use strict";(function(e){function n(){return o.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function i(e,t){if(n()<t)throw new RangeError("Invalid typed array length");return o.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t),e.__proto__=o.prototype):(null===e&&(e=new o(t)),e.length=t),e}function o(e,t,r){if(!(o.TYPED_ARRAY_SUPPORT||this instanceof o))return new o(e,t,r);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return f(this,e)}return s(this,e,t,r)}function s(e,t,r,n){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?p(e,t,r,n):"string"==typeof t?l(e,t,r):h(e,t)}function a(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function u(e,t,r,n){return a(t),t<=0?i(e,t):void 0!==r?"string"==typeof n?i(e,t).fill(r,n):i(e,t).fill(r):i(e,t)}function f(e,t){if(a(t),e=i(e,t<0?0:0|d(t)),!o.TYPED_ARRAY_SUPPORT)for(var r=0;r<t;++r)e[r]=0;return e}function l(e,t,r){if("string"==typeof r&&""!==r||(r="utf8"),!o.isEncoding(r))throw new TypeError('"encoding" must be a valid string encoding');var n=0|y(t,r);e=i(e,n);var s=e.write(t,r);return s!==n&&(e=e.slice(0,s)),e}function c(e,t){var r=t.length<0?0:0|d(t.length);e=i(e,r);for(var n=0;n<r;n+=1)e[n]=255&t[n];return e}function p(e,t,r,n){if(t.byteLength,r<0||t.byteLength<r)throw new RangeError("'offset' is out of bounds");if(t.byteLength<r+(n||0))throw new RangeError("'length' is out of bounds");return t=void 0===r&&void 0===n?new Uint8Array(t):void 0===n?new Uint8Array(t,r):new Uint8Array(t,r,n),o.TYPED_ARRAY_SUPPORT?(e=t,e.__proto__=o.prototype):e=c(e,t),e}function h(e,t){if(o.isBuffer(t)){var r=0|d(t.length);return e=i(e,r),0===e.length?e:(t.copy(e,0,0,r),e)}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||q(t.length)?i(e,0):c(e,t);if("Buffer"===t.type&&X(t.data))return c(e,t.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function d(e){if(e>=n())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+n().toString(16)+" bytes");return 0|e}function g(e){return+e!=e&&(e=0),o.alloc(+e)}function y(e,t){if(o.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var r=e.length;if(0===r)return 0;for(var n=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return K(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return V(e).length;default:if(n)return K(e).length;t=(""+t).toLowerCase(),n=!0}}function v(e,t,r){var n=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if(r>>>=0,t>>>=0,r<=t)return"";for(e||(e="utf8");;)switch(e){case"hex":return x(this,t,r);case"utf8":case"utf-8":return U(this,t,r);case"ascii":return B(this,t,r);case"latin1":case"binary":return R(this,t,r);case"base64":return A(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return C(this,t,r);default:if(n)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),n=!0}}function m(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function k(e,t,r,n,i){if(0===e.length)return-1;if("string"==typeof r?(n=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),r=+r,isNaN(r)&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return-1;r=e.length-1}else if(r<0){if(!i)return-1;r=0}if("string"==typeof t&&(t=o.from(t,n)),o.isBuffer(t))return 0===t.length?-1:T(e,t,r,n,i);if("number"==typeof t)return t&=255,o.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):T(e,[t],r,n,i);throw new TypeError("val must be string, number or Buffer")}function T(e,t,r,n,i){function o(e,t){return 1===s?e[t]:e.readUInt16BE(t*s)}var s=1,a=e.length,u=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return-1;s=2,a/=2,u/=2,r/=2}var f;if(i){var l=-1;for(f=r;f<a;f++)if(o(e,f)===o(t,-1===l?0:f-l)){if(-1===l&&(l=f),f-l+1===u)return l*s}else-1!==l&&(f-=f-l),l=-1}else for(r+u>a&&(r=a-u),f=r;f>=0;f--){for(var c=!0,p=0;p<u;p++)if(o(e,f+p)!==o(t,p)){c=!1;break}if(c)return f}return-1}function w(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=t.length;if(o%2!=0)throw new TypeError("Invalid hex string");n>o/2&&(n=o/2);for(var s=0;s<n;++s){var a=parseInt(t.substr(2*s,2),16);if(isNaN(a))return s;e[r+s]=a}return s}function I(e,t,r,n){return W(K(t,e.length-r),e,r,n)}function b(e,t,r,n){return W(J(t),e,r,n)}function _(e,t,r,n){return b(e,t,r,n)}function S(e,t,r,n){return W(V(t),e,r,n)}function E(e,t,r,n){return W(Q(t,e.length-r),e,r,n)}function A(e,t,r){return 0===t&&r===e.length?G.fromByteArray(e):G.fromByteArray(e.slice(t,r))}function U(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var o=e[i],s=null,a=o>239?4:o>223?3:o>191?2:1;if(i+a<=r){var u,f,l,c;switch(a){case 1:o<128&&(s=o);break;case 2:u=e[i+1],128==(192&u)&&(c=(31&o)<<6|63&u)>127&&(s=c);break;case 3:u=e[i+1],f=e[i+2],128==(192&u)&&128==(192&f)&&(c=(15&o)<<12|(63&u)<<6|63&f)>2047&&(c<55296||c>57343)&&(s=c);break;case 4:u=e[i+1],f=e[i+2],l=e[i+3],128==(192&u)&&128==(192&f)&&128==(192&l)&&(c=(15&o)<<18|(63&u)<<12|(63&f)<<6|63&l)>65535&&c<1114112&&(s=c)}}null===s?(s=65533,a=1):s>65535&&(s-=65536,n.push(s>>>10&1023|55296),s=56320|1023&s),n.push(s),i+=a}return P(n)}function P(e){var t=e.length;if(t<=Z)return String.fromCharCode.apply(String,e);for(var r="",n=0;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=Z));return r}function B(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}function R(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}function x(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=t;o<r;++o)i+=$(e[o]);return i}function C(e,t,r){for(var n=e.slice(t,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}function F(e,t,r){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function O(e,t,r,n,i,s){if(!o.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<s)throw new RangeError('"value" argument is out of bounds');if(r+n>e.length)throw new RangeError("Index out of range")}function N(e,t,r,n){t<0&&(t=65535+t+1);for(var i=0,o=Math.min(e.length-r,2);i<o;++i)e[r+i]=(t&255<<8*(n?i:1-i))>>>8*(n?i:1-i)}function D(e,t,r,n){t<0&&(t=4294967295+t+1);for(var i=0,o=Math.min(e.length-r,4);i<o;++i)e[r+i]=t>>>8*(n?i:3-i)&255}function L(e,t,r,n,i,o){if(r+n>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function z(e,t,r,n,i){return i||L(e,t,r,4,3.4028234663852886e38,-3.4028234663852886e38),H.write(e,t,r,n,23,4),r+4}function M(e,t,r,n,i){return i||L(e,t,r,8,1.7976931348623157e308,-1.7976931348623157e308),H.write(e,t,r,n,52,8),r+8}function Y(e){if(e=j(e).replace(ee,""),e.length<2)return"";for(;e.length%4!=0;)e+="=";return e}function j(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}function $(e){return e<16?"0"+e.toString(16):e.toString(16)}function K(e,t){t=t||1/0;for(var r,n=e.length,i=null,o=[],s=0;s<n;++s){if((r=e.charCodeAt(s))>55295&&r<57344){if(!i){if(r>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(s+1===n){(t-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&o.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;o.push(r)}else if(r<2048){if((t-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function J(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}function Q(e,t){for(var r,n,i,o=[],s=0;s<e.length&&!((t-=2)<0);++s)r=e.charCodeAt(s),n=r>>8,i=r%256,o.push(i),o.push(n);return o}function V(e){return G.toByteArray(Y(e))}function W(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length||i>=e.length);++i)t[i+r]=e[i];return i}function q(e){return e!==e}/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <<EMAIL>> <http://feross.org>
 * @license  MIT
 */
var G=r(6),H=r(7),X=r(8);t.Buffer=o,t.SlowBuffer=g,t.INSPECT_MAX_BYTES=50,o.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}(),t.kMaxLength=n(),o.poolSize=8192,o._augment=function(e){return e.__proto__=o.prototype,e},o.from=function(e,t,r){return s(null,e,t,r)},o.TYPED_ARRAY_SUPPORT&&(o.prototype.__proto__=Uint8Array.prototype,o.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&o[Symbol.species]===o&&Object.defineProperty(o,Symbol.species,{value:null,configurable:!0})),o.alloc=function(e,t,r){return u(null,e,t,r)},o.allocUnsafe=function(e){return f(null,e)},o.allocUnsafeSlow=function(e){return f(null,e)},o.isBuffer=function(e){return!(null==e||!e._isBuffer)},o.compare=function(e,t){if(!o.isBuffer(e)||!o.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var r=e.length,n=t.length,i=0,s=Math.min(r,n);i<s;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:n<r?1:0},o.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},o.concat=function(e,t){if(!X(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return o.alloc(0);var r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;var n=o.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var s=e[r];if(!o.isBuffer(s))throw new TypeError('"list" argument must be an Array of Buffers');s.copy(n,i),i+=s.length}return n},o.byteLength=y,o.prototype._isBuffer=!0,o.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)m(this,t,t+1);return this},o.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)m(this,t,t+3),m(this,t+1,t+2);return this},o.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)m(this,t,t+7),m(this,t+1,t+6),m(this,t+2,t+5),m(this,t+3,t+4);return this},o.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?U(this,0,e):v.apply(this,arguments)},o.prototype.equals=function(e){if(!o.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===o.compare(this,e)},o.prototype.inspect=function(){var e="",r=t.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,r).match(/.{2}/g).join(" "),this.length>r&&(e+=" ... ")),"<Buffer "+e+">"},o.prototype.compare=function(e,t,r,n,i){if(!o.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return-1;if(t>=r)return 1;if(t>>>=0,r>>>=0,n>>>=0,i>>>=0,this===e)return 0;for(var s=i-n,a=r-t,u=Math.min(s,a),f=this.slice(n,i),l=e.slice(t,r),c=0;c<u;++c)if(f[c]!==l[c]){s=f[c],a=l[c];break}return s<a?-1:a<s?1:0},o.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},o.prototype.indexOf=function(e,t,r){return k(this,e,t,r,!0)},o.prototype.lastIndexOf=function(e,t,r){return k(this,e,t,r,!1)},o.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(r)?(r|=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var i=this.length-t;if((void 0===r||r>i)&&(r=i),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var o=!1;;)switch(n){case"hex":return w(this,e,t,r);case"utf8":case"utf-8":return I(this,e,t,r);case"ascii":return b(this,e,t,r);case"latin1":case"binary":return _(this,e,t,r);case"base64":return S(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return E(this,e,t,r);default:if(o)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),o=!0}},o.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var Z=4096;o.prototype.slice=function(e,t){var r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);var n;if(o.TYPED_ARRAY_SUPPORT)n=this.subarray(e,t),n.__proto__=o.prototype;else{var i=t-e;n=new o(i,void 0);for(var s=0;s<i;++s)n[s]=this[s+e]}return n},o.prototype.readUIntLE=function(e,t,r){e|=0,t|=0,r||F(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n},o.prototype.readUIntBE=function(e,t,r){e|=0,t|=0,r||F(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},o.prototype.readUInt8=function(e,t){return t||F(e,1,this.length),this[e]},o.prototype.readUInt16LE=function(e,t){return t||F(e,2,this.length),this[e]|this[e+1]<<8},o.prototype.readUInt16BE=function(e,t){return t||F(e,2,this.length),this[e]<<8|this[e+1]},o.prototype.readUInt32LE=function(e,t){return t||F(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},o.prototype.readUInt32BE=function(e,t){return t||F(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},o.prototype.readIntLE=function(e,t,r){e|=0,t|=0,r||F(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return i*=128,n>=i&&(n-=Math.pow(2,8*t)),n},o.prototype.readIntBE=function(e,t,r){e|=0,t|=0,r||F(e,t,this.length);for(var n=t,i=1,o=this[e+--n];n>0&&(i*=256);)o+=this[e+--n]*i;return i*=128,o>=i&&(o-=Math.pow(2,8*t)),o},o.prototype.readInt8=function(e,t){return t||F(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},o.prototype.readInt16LE=function(e,t){t||F(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},o.prototype.readInt16BE=function(e,t){t||F(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},o.prototype.readInt32LE=function(e,t){return t||F(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},o.prototype.readInt32BE=function(e,t){return t||F(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},o.prototype.readFloatLE=function(e,t){return t||F(e,4,this.length),H.read(this,e,!0,23,4)},o.prototype.readFloatBE=function(e,t){return t||F(e,4,this.length),H.read(this,e,!1,23,4)},o.prototype.readDoubleLE=function(e,t){return t||F(e,8,this.length),H.read(this,e,!0,52,8)},o.prototype.readDoubleBE=function(e,t){return t||F(e,8,this.length),H.read(this,e,!1,52,8)},o.prototype.writeUIntLE=function(e,t,r,n){if(e=+e,t|=0,r|=0,!n){O(this,e,t,r,Math.pow(2,8*r)-1,0)}var i=1,o=0;for(this[t]=255&e;++o<r&&(i*=256);)this[t+o]=e/i&255;return t+r},o.prototype.writeUIntBE=function(e,t,r,n){if(e=+e,t|=0,r|=0,!n){O(this,e,t,r,Math.pow(2,8*r)-1,0)}var i=r-1,o=1;for(this[t+i]=255&e;--i>=0&&(o*=256);)this[t+i]=e/o&255;return t+r},o.prototype.writeUInt8=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,1,255,0),o.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},o.prototype.writeUInt16LE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,2,65535,0),o.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):N(this,e,t,!0),t+2},o.prototype.writeUInt16BE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,2,65535,0),o.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):N(this,e,t,!1),t+2},o.prototype.writeUInt32LE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,4,4294967295,0),o.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):D(this,e,t,!0),t+4},o.prototype.writeUInt32BE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,4,4294967295,0),o.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):D(this,e,t,!1),t+4},o.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);O(this,e,t,r,i-1,-i)}var o=0,s=1,a=0;for(this[t]=255&e;++o<r&&(s*=256);)e<0&&0===a&&0!==this[t+o-1]&&(a=1),this[t+o]=(e/s>>0)-a&255;return t+r},o.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);O(this,e,t,r,i-1,-i)}var o=r-1,s=1,a=0;for(this[t+o]=255&e;--o>=0&&(s*=256);)e<0&&0===a&&0!==this[t+o+1]&&(a=1),this[t+o]=(e/s>>0)-a&255;return t+r},o.prototype.writeInt8=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,1,127,-128),o.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},o.prototype.writeInt16LE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,2,32767,-32768),o.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):N(this,e,t,!0),t+2},o.prototype.writeInt16BE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,2,32767,-32768),o.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):N(this,e,t,!1),t+2},o.prototype.writeInt32LE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,4,2147483647,-2147483648),o.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):D(this,e,t,!0),t+4},o.prototype.writeInt32BE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),o.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):D(this,e,t,!1),t+4},o.prototype.writeFloatLE=function(e,t,r){return z(this,e,t,!0,r)},o.prototype.writeFloatBE=function(e,t,r){return z(this,e,t,!1,r)},o.prototype.writeDoubleLE=function(e,t,r){return M(this,e,t,!0,r)},o.prototype.writeDoubleBE=function(e,t,r){return M(this,e,t,!1,r)},o.prototype.copy=function(e,t,r,n){if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i,s=n-r;if(this===e&&r<t&&t<n)for(i=s-1;i>=0;--i)e[i+t]=this[i+r];else if(s<1e3||!o.TYPED_ARRAY_SUPPORT)for(i=0;i<s;++i)e[i+t]=this[i+r];else Uint8Array.prototype.set.call(e,this.subarray(r,r+s),t);return s},o.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),1===e.length){var i=e.charCodeAt(0);i<256&&(e=i)}if(void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!o.isEncoding(n))throw new TypeError("Unknown encoding: "+n)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw new RangeError("Out of range index");if(r<=t)return this;t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0);var s;if("number"==typeof e)for(s=t;s<r;++s)this[s]=e;else{var a=o.isBuffer(e)?e:K(new o(e,n).toString()),u=a.length;for(s=0;s<r-t;++s)this[s+t]=a[s%u]}return this};var ee=/[^+\/0-9A-Za-z-_]/g}).call(t,r(5))},function(e,t){var r;r=function(){return this}();try{r=r||Function("return this")()||(0,eval)("this")}catch(e){"object"==typeof window&&(r=window)}e.exports=r},function(e,t,r){"use strict";function n(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");return"="===e[t-2]?2:"="===e[t-1]?1:0}function i(e){return 3*e.length/4-n(e)}function o(e){var t,r,i,o,s,a=e.length;o=n(e),s=new c(3*a/4-o),r=o>0?a-4:a;var u=0;for(t=0;t<r;t+=4)i=l[e.charCodeAt(t)]<<18|l[e.charCodeAt(t+1)]<<12|l[e.charCodeAt(t+2)]<<6|l[e.charCodeAt(t+3)],s[u++]=i>>16&255,s[u++]=i>>8&255,s[u++]=255&i;return 2===o?(i=l[e.charCodeAt(t)]<<2|l[e.charCodeAt(t+1)]>>4,s[u++]=255&i):1===o&&(i=l[e.charCodeAt(t)]<<10|l[e.charCodeAt(t+1)]<<4|l[e.charCodeAt(t+2)]>>2,s[u++]=i>>8&255,s[u++]=255&i),s}function s(e){return f[e>>18&63]+f[e>>12&63]+f[e>>6&63]+f[63&e]}function a(e,t,r){for(var n,i=[],o=t;o<r;o+=3)n=(e[o]<<16&16711680)+(e[o+1]<<8&65280)+(255&e[o+2]),i.push(s(n));return i.join("")}function u(e){for(var t,r=e.length,n=r%3,i="",o=[],s=0,u=r-n;s<u;s+=16383)o.push(a(e,s,s+16383>u?u:s+16383));return 1===n?(t=e[r-1],i+=f[t>>2],i+=f[t<<4&63],i+="=="):2===n&&(t=(e[r-2]<<8)+e[r-1],i+=f[t>>10],i+=f[t>>4&63],i+=f[t<<2&63],i+="="),o.push(i),o.join("")}t.byteLength=i,t.toByteArray=o,t.fromByteArray=u;for(var f=[],l=[],c="undefined"!=typeof Uint8Array?Uint8Array:Array,p="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",h=0,d=p.length;h<d;++h)f[h]=p[h],l[p.charCodeAt(h)]=h;l["-".charCodeAt(0)]=62,l["_".charCodeAt(0)]=63},function(e,t){t.read=function(e,t,r,n,i){var o,s,a=8*i-n-1,u=(1<<a)-1,f=u>>1,l=-7,c=r?i-1:0,p=r?-1:1,h=e[t+c];for(c+=p,o=h&(1<<-l)-1,h>>=-l,l+=a;l>0;o=256*o+e[t+c],c+=p,l-=8);for(s=o&(1<<-l)-1,o>>=-l,l+=n;l>0;s=256*s+e[t+c],c+=p,l-=8);if(0===o)o=1-f;else{if(o===u)return s?NaN:1/0*(h?-1:1);s+=Math.pow(2,n),o-=f}return(h?-1:1)*s*Math.pow(2,o-n)},t.write=function(e,t,r,n,i,o){var s,a,u,f=8*o-i-1,l=(1<<f)-1,c=l>>1,p=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,h=n?0:o-1,d=n?1:-1,g=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,s=l):(s=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-s))<1&&(s--,u*=2),t+=s+c>=1?p/u:p*Math.pow(2,1-c),t*u>=2&&(s++,u/=2),s+c>=l?(a=0,s=l):s+c>=1?(a=(t*u-1)*Math.pow(2,i),s+=c):(a=t*Math.pow(2,c-1)*Math.pow(2,i),s=0));i>=8;e[r+h]=255&a,h+=d,a/=256,i-=8);for(s=s<<i|a,f+=i;f>0;e[r+h]=255&s,h+=d,s/=256,f-=8);e[r+h-d]|=128*g}},function(e,t){var r={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==r.call(e)}},function(e,t,r){!function(t){e.exports=t()}(function(e){"use strict";var t=function(e,t){return e+t&4294967295},r=function(e,r,n,i,o,s){return r=t(t(r,e),t(i,s)),t(r<<o|r>>>32-o,n)},n=function(e,t,n,i,o,s,a){return r(t&n|~t&i,e,t,o,s,a)},i=function(e,t,n,i,o,s,a){return r(t&i|n&~i,e,t,o,s,a)},o=function(e,t,n,i,o,s,a){return r(t^n^i,e,t,o,s,a)},s=function(e,t,n,i,o,s,a){return r(n^(t|~i),e,t,o,s,a)},a=function(e,r){var a=e[0],u=e[1],f=e[2],l=e[3];a=n(a,u,f,l,r[0],7,-680876936),l=n(l,a,u,f,r[1],12,-389564586),f=n(f,l,a,u,r[2],17,606105819),u=n(u,f,l,a,r[3],22,-1044525330),a=n(a,u,f,l,r[4],7,-176418897),l=n(l,a,u,f,r[5],12,1200080426),f=n(f,l,a,u,r[6],17,-1473231341),u=n(u,f,l,a,r[7],22,-45705983),a=n(a,u,f,l,r[8],7,1770035416),l=n(l,a,u,f,r[9],12,-1958414417),f=n(f,l,a,u,r[10],17,-42063),u=n(u,f,l,a,r[11],22,-1990404162),a=n(a,u,f,l,r[12],7,1804603682),l=n(l,a,u,f,r[13],12,-40341101),f=n(f,l,a,u,r[14],17,-1502002290),u=n(u,f,l,a,r[15],22,1236535329),a=i(a,u,f,l,r[1],5,-165796510),l=i(l,a,u,f,r[6],9,-1069501632),f=i(f,l,a,u,r[11],14,643717713),u=i(u,f,l,a,r[0],20,-373897302),a=i(a,u,f,l,r[5],5,-701558691),l=i(l,a,u,f,r[10],9,38016083),f=i(f,l,a,u,r[15],14,-660478335),u=i(u,f,l,a,r[4],20,-405537848),a=i(a,u,f,l,r[9],5,568446438),l=i(l,a,u,f,r[14],9,-1019803690),f=i(f,l,a,u,r[3],14,-187363961),u=i(u,f,l,a,r[8],20,1163531501),a=i(a,u,f,l,r[13],5,-1444681467),l=i(l,a,u,f,r[2],9,-51403784),f=i(f,l,a,u,r[7],14,1735328473),u=i(u,f,l,a,r[12],20,-1926607734),a=o(a,u,f,l,r[5],4,-378558),l=o(l,a,u,f,r[8],11,-2022574463),f=o(f,l,a,u,r[11],16,1839030562),u=o(u,f,l,a,r[14],23,-35309556),a=o(a,u,f,l,r[1],4,-1530992060),l=o(l,a,u,f,r[4],11,1272893353),f=o(f,l,a,u,r[7],16,-155497632),u=o(u,f,l,a,r[10],23,-1094730640),a=o(a,u,f,l,r[13],4,681279174),l=o(l,a,u,f,r[0],11,-358537222),f=o(f,l,a,u,r[3],16,-722521979),u=o(u,f,l,a,r[6],23,76029189),a=o(a,u,f,l,r[9],4,-640364487),l=o(l,a,u,f,r[12],11,-421815835),f=o(f,l,a,u,r[15],16,530742520),u=o(u,f,l,a,r[2],23,-995338651),a=s(a,u,f,l,r[0],6,-198630844),l=s(l,a,u,f,r[7],10,1126891415),f=s(f,l,a,u,r[14],15,-1416354905),u=s(u,f,l,a,r[5],21,-57434055),a=s(a,u,f,l,r[12],6,1700485571),l=s(l,a,u,f,r[3],10,-1894986606),f=s(f,l,a,u,r[10],15,-1051523),u=s(u,f,l,a,r[1],21,-2054922799),a=s(a,u,f,l,r[8],6,1873313359),l=s(l,a,u,f,r[15],10,-30611744),f=s(f,l,a,u,r[6],15,-1560198380),u=s(u,f,l,a,r[13],21,1309151649),a=s(a,u,f,l,r[4],6,-145523070),l=s(l,a,u,f,r[11],10,-1120210379),f=s(f,l,a,u,r[2],15,718787259),u=s(u,f,l,a,r[9],21,-343485551),e[0]=t(a,e[0]),e[1]=t(u,e[1]),e[2]=t(f,e[2]),e[3]=t(l,e[3])},u=function(e){var t,r=[];for(t=0;t<64;t+=4)r[t>>2]=e.charCodeAt(t)+(e.charCodeAt(t+1)<<8)+(e.charCodeAt(t+2)<<16)+(e.charCodeAt(t+3)<<24);return r},f=function(e){var t,r=[];for(t=0;t<64;t+=4)r[t>>2]=e[t]+(e[t+1]<<8)+(e[t+2]<<16)+(e[t+3]<<24);return r},l=function(e){var t,r,n,i,o,s,f=e.length,l=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=f;t+=64)a(l,u(e.substring(t-64,t)));for(e=e.substring(t-64),r=e.length,n=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],t=0;t<r;t+=1)n[t>>2]|=e.charCodeAt(t)<<(t%4<<3);if(n[t>>2]|=128<<(t%4<<3),t>55)for(a(l,n),t=0;t<16;t+=1)n[t]=0;return i=8*f,i=i.toString(16).match(/(.*?)(.{0,8})$/),o=parseInt(i[2],16),s=parseInt(i[1],16)||0,n[14]=o,n[15]=s,a(l,n),l},c=function(e){var t,r,n,i,o,s,u=e.length,l=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=u;t+=64)a(l,f(e.subarray(t-64,t)));for(e=t-64<u?e.subarray(t-64):new Uint8Array(0),r=e.length,n=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],t=0;t<r;t+=1)n[t>>2]|=e[t]<<(t%4<<3);if(n[t>>2]|=128<<(t%4<<3),t>55)for(a(l,n),t=0;t<16;t+=1)n[t]=0;return i=8*u,i=i.toString(16).match(/(.*?)(.{0,8})$/),o=parseInt(i[2],16),s=parseInt(i[1],16)||0,n[14]=o,n[15]=s,a(l,n),l},p=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"],h=function(e){var t,r="";for(t=0;t<4;t+=1)r+=p[e>>8*t+4&15]+p[e>>8*t&15];return r},d=function(e){var t;for(t=0;t<e.length;t+=1)e[t]=h(e[t]);return e.join("")},g=function(){this.reset()};return"5d41402abc4b2a76b9719d911017c592"!==function(e){return d(l(e))}("hello")&&(t=function(e,t){var r=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(r>>16)<<16|65535&r}),g.prototype.append=function(e){return/[\u0080-\uFFFF]/.test(e)&&(e=unescape(encodeURIComponent(e))),this.appendBinary(e),this},g.prototype.appendBinary=function(e){this._buff+=e,this._length+=e.length;var t,r=this._buff.length;for(t=64;t<=r;t+=64)a(this._state,u(this._buff.substring(t-64,t)));return this._buff=this._buff.substr(t-64),this},g.prototype.end=function(e){var t,r,n=this._buff,i=n.length,o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<i;t+=1)o[t>>2]|=n.charCodeAt(t)<<(t%4<<3);return this._finish(o,i),r=e?this._state:d(this._state),this.reset(),r},g.prototype._finish=function(e,t){var r,n,i,o=t;if(e[o>>2]|=128<<(o%4<<3),o>55)for(a(this._state,e),o=0;o<16;o+=1)e[o]=0;r=8*this._length,r=r.toString(16).match(/(.*?)(.{0,8})$/),n=parseInt(r[2],16),i=parseInt(r[1],16)||0,e[14]=n,e[15]=i,a(this._state,e)},g.prototype.reset=function(){return this._buff="",this._length=0,this._state=[1732584193,-271733879,-1732584194,271733878],this},g.prototype.destroy=function(){delete this._state,delete this._buff,delete this._length},g.hash=function(e,t){/[\u0080-\uFFFF]/.test(e)&&(e=unescape(encodeURIComponent(e)));var r=l(e);return t?r:d(r)},g.hashBinary=function(e,t){var r=l(e);return t?r:d(r)},g.ArrayBuffer=function(){this.reset()},g.ArrayBuffer.prototype.append=function(e){var t,r=this._concatArrayBuffer(this._buff,e),n=r.length;for(this._length+=e.byteLength,t=64;t<=n;t+=64)a(this._state,f(r.subarray(t-64,t)));return this._buff=t-64<n?r.subarray(t-64):new Uint8Array(0),this},g.ArrayBuffer.prototype.end=function(e){var t,r,n=this._buff,i=n.length,o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<i;t+=1)o[t>>2]|=n[t]<<(t%4<<3);return this._finish(o,i),r=e?this._state:d(this._state),this.reset(),r},g.ArrayBuffer.prototype._finish=g.prototype._finish,g.ArrayBuffer.prototype.reset=function(){return this._buff=new Uint8Array(0),this._length=0,this._state=[1732584193,-271733879,-1732584194,271733878],this},g.ArrayBuffer.prototype.destroy=g.prototype.destroy,g.ArrayBuffer.prototype._concatArrayBuffer=function(e,t){var r=e.length,n=new Uint8Array(r+t.byteLength);return n.set(e),n.set(new Uint8Array(t),r),n},g.ArrayBuffer.hash=function(e,t){var r=c(new Uint8Array(e));return t?r:d(r)},g})},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(0),i=(r(1).default,r(2).default),o=function(e){function t(){return window.nxt&&window.nxt.config?window.nxt.config:e.configInst?e.configInst:void 0}function r(){return"http://"+location.host+"/upload/complete/?user_token="}function o(e){var n={TaskName:e.metadata.name,TaskGuid:e.taskId,TransType:"Upload",TransFile:[],UserInfo:{UserId:nxt.user.current.id.toString(),UserName:nxt.user.current.nickName||nxt.user.current.loginName,UserCode:nxt.user.current.userCode},ExtendeAttr:[]};return 1==t().vtubeInfo.importType&&(n.CallBackUrl=r()),t().vtubeInfo.usedConfig&&(n.ServerInfo={HostName:t().vtubeInfo.address,Port:parseInt(t().vtubeInfo.port),Scheme:"FTP",UserName:t().vtubeInfo.userName,Password:t().vtubeInfo.password,PathRoot:""}),2==t().storageType&&(n.ServerInfo={HostName:"",Port:0,Scheme:"ALIS3",UserName:"",Password:"",PathRoot:""}),n.ExtendeAttr=_.map(e.metadata.field,function(e){var t={ItemCode:e.fieldName,ItemName:e.alias};if(8==e.controlType)if(null!=e.value&&""!=e.value&&"[]"!=e.value)try{t.Value=JSON.parse(e.value)[0]}catch(e){t.Value=""}else t.Value="";else t.Value=e.value||"";return t}),n.ExtendeAttr.push({ItemCode:"tree",ItemName:"目录树",Value:e.tree}),_.remove(n.ExtendeAttr,{ItemCode:"cliptype"}),n.ExtendeAttr.push({ItemCode:"cliptype",ItemName:"素材类型",Value:e.entityType}),n}var s=this,a="http://127.0.0.1:8084/";this.openFileSelector=function(r){$.ajax({url:a+"request/getuploadfiles?user_token=&filetype=all",success:function(t){var i=_.isString(t)?JSON.parse(t):t;if(0!==i.FileCount){var o=[];_.forEach(i.FileList,function(t){var r="."+n.default.getExtension(t.FilePath);o.push({entityType:n.default.getTypeByExt(r,e.configInst).code,fileName:n.default.getFullFileName(t.FilePath),metadata:{name:n.default.getFileName(t.FilePath),ext:r},status:"added",progress:0,file:t})}),r(o)}},error:function(e){console.error(e);var r=t().vtubeDownloadPath||t().server+"/assets/Sobey_vRocket_v2.0_Setup.exe";n.default.prompt(l("upload.clientInstallTip",'<p>打开客户端失败</p><p>请确定已经正常开启客户端或重启客户端再重试。</p><p>如没有安装，请点击下面地址。</p><p><a href="${path}"><i class="icon iconfont icon-iconfontxiazai" style="margin-right:5px"></i>点击客户端下载</a></p>',{path:r}))}})},this.createTask=function(e,t){var r=[];switch(t.taskType){case 1:_.isArray(e)||(e=[e]),_.forEach(e,function(e){e.file?(e.files=[{file:e.file,fileName:e.fileName,fileSize:e.file.FileSize}],delete e.file,r.push(e)):e.files&&e.files.length>0&&(e.files=_.map(e.files,function(e){return{file:e.file,fileName:e.fileName,fileSize:e.fileSize}}),r.push(e))});break;case 2:case 3:r.push(e)}_.forEach(r,function(e){e.taskType=t.taskType,e.transferType=t.transferType,e.targetFolder=t.targetFolder,e.relationContentType=t.relationContentType,e.relationContentId=t.relationContentId,s.addTask(e)})},this.addTask=function(r){var s=t().server+"/upload/multipart/init";e.loginToken&&(s+="?token="+e.loginToken),i.post(s,r).then(function(e){function i(e){return _.isString(e.location)&&-1==e.location.indexOf("oss:")?t().vtubeInfo.path+e.location.replace(/\\/g,"/"):e.location}e=e.data;var s=o(e);switch(s.ExtendData=e.userCode,r.taskType){case 1:s.TransFile.push({SourceFile:r.files[0].file.FilePath,DestFile:i(e.files[0])});break;case 2:case 3:_.forEach(e.files,function(e){s.TransFile.push({SourceFile:e.fileName,DestFile:i(e)})})}$.ajax({url:a+"request/addtask?user_token=",type:"POST",contentType:"application/json",data:JSON.stringify(s),success:function(e){"string"==typeof e&&(e=JSON.parse(e)),1==e.Result?n.default.msgOk(l("upload.addTaskOk","添加任务成功")):n.default.prompt(l("upload.addTaskError","添加任务失败：")+e.Msg),console.info(e)},error:function(e){console.info(e),n.default.prompt(l("upload.addTaskError","添加任务失败"))}})},function(e){n.default.prompt(l("upload.uploadError","上传失败：")+e.data.desc)})}};t.default=o},,,,,,,,,,,,,,,,,,function(e,t,r){var n=r(3),i=r(10),o={init:function(e){o.web=new n.default(e),o.vtube=new i.default(e)}};window.mamUpload=o}]);
//# sourceMappingURL=mam-upload-pure.min.js.map