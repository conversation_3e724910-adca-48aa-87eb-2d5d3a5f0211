import React, { FC, useEffect, useState } from 'react';
import moment from "moment";
import loginApis from "@/service/loginApis";
import { Empty,Modal,message ,Button} from "antd";
import "./index.less";
import {  useIntl } from 'umi';
import contentListApis from '@/service/contentListApis';

interface textProps {
  clipList:Array<any>;
  textclipVisible:boolean;
  onClose:()=> void;
  onCreate:()=> void;
}
const TextclipModal: FC<textProps> = props => {
  const {
    clipList,
    textclipVisible,
    onClose,
    onCreate
  } = props;
  const [list,setList] =useState<any>([])
  const intl = useIntl();
  useEffect(()=>{
    // initList(contentId);
    if(clipList.length>0){
      sort();
    }
  },[clipList])
  const goTextClip =(taskId:string) => {
    window.location.href =`${window.location.origin}/textclip/#/clip/textClipDetail/${taskId}`; 
  }
  // 对时间进行排序显示
  const sort =() => {
    const temp:any =  clipList.sort((a, b) => {
      return Date.parse(b.split('_')[1].replace(/-/g, "/")) - Date.parse(a.split('_')[1].replace(/-/g, "/"))
    });
    setList(temp)
  }
  return (
    <Modal 
      className='textclipModal'
      title={intl.formatMessage({ id: '剪辑记录' })}
      visible={textclipVisible}
      onCancel={onClose}
      width={400}
      footer={[
        <Button
          type='primary'
          onClick={onCreate}
        >
          {intl.formatMessage({ id: '新建剪辑任务' })}
        </Button>
      ]}
    >
      <div className='title'>{intl.formatMessage({ id: '历史剪辑任务' })}：</div>
      {list?.map((item:any,index:number)=>{
        return <div className='list_item' key={index}>
          <span className='time'>
            {
              item.split('_')[1]
            }
          </span>
          <a onClick={()=>goTextClip(item.split('_')[0])}>{intl.formatMessage({ id: '进入此任务' })}</a>
        </div>
      })}
      <span className='tips'>{intl.formatMessage({ id: '如需新建剪辑任务，可点击下方按钮' })}</span>
    </Modal>
  )
};

export default TextclipModal;