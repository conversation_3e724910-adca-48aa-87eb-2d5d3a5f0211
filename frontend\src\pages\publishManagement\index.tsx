import React, { FC, useState, useEffect, useRef } from 'react';
import {
  Form,
  Input,
  Tooltip,
  message,
  Tabs,
  Row,
  Col,
  Empty,
  Pagination,
  Select,
  Checkbox,
} from 'antd';
import './index.less';
import globalParams from '@/permission/globalParams';
import { history, useDispatch, useSelector, useParams, Idownlist, useIntl } from 'umi';
import contentListApis from '@/service/contentListApis';
import { IPermission } from '@/models/permission';
import {
  MenuItem,
} from '../contentlibrary/contentList/type';
import { ContentItem, DownloadModal, IconFont, ListTop } from '@/components';

import {
  RedoOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons';
import perCfg from '@/permission/config';
interface resourceVerifyProps {
  setCatalogueVisible: (bool) => void
}
const PublishManagement: FC<resourceVerifyProps> = props => {
  // const [activeTabs, setActiveTabs] = useState<string>('verifyToMe');
  const sortby = useRef<boolean>(true);
  const { permissions, rmanGlobalParameter } = useSelector<
    { permission: any },
    IPermission
  >(({ permission }) => permission);
  const [form] = Form.useForm();
  const activeTabs = useRef<string>(permissions.includes(perCfg.share_review) ? 'verifyToMe' : 'myVerify');

  const verifyType = useRef<string>('publish');

  const [current, setCurrent] = useState<number>(1); //当前页码
  const [pageSize, setPageSize] = useState<number>(30); //每页条数
  const [modeSwitch, setModeSwitch] = useState<boolean>(
    localStorage.getItem('view-mode') !== '0',
  ); // 视图切换
  const [innerItem, setInnerItem] = useState<any>({});
  const [collation, setCollation] = useState<string>('applyDateTime,0'); //排序
  const [innerFlag, setInnerFlag] = useState<boolean>(['inventoryVerify', 'publishVerify'].includes(verifyType.current)); //点进了审核任务详情
  const [status, setStatus] = useState<string>(''); //点进了审核任务详情
  const currentTask = useRef<any>(null);
  const [totalPage, setTotalPage] = useState<number>(0); //素材总数
  const [innerTotalPage, setInnerTotalPage] = useState<number>(0); //内部素材总数
  // 驳回审核弹窗
  const [rejectisModalOpen, setRejectisModalOpen] = useState<boolean>(false);
  // 驳回理由
  const [rejectisReason, setRejectisReason] = useState<string>('');
  const options: Array<MenuItem> = [
    { label: '全部', value: undefined },
    { label: '音频', value: 'biz_sobey_audio' },
    { label: '视频', value: 'biz_sobey_video' },
    { label: '图片', value: 'biz_sobey_picture' },
    { label: '文档', value: 'biz_sobey_document' },
    { label: '文件夹', value: 'folder' },
    { label: '其他', value: 'biz_sobey_other' },
  ];
  const columns = [
    'directory',
    'publishUser',
    'classificationNames', // 发布专题
    'publishTime'
  ]
  const columns2 = [
    'directory',
    'publishUser',
    'classificationNames', // 发布专题
    'publishTime',
    'auditUserName',
    'auditDateTime'
  ]
  const [indeterminate, setIndeterminate] = useState<boolean>(false);
  const [checkAll, setCheckAll] = useState<boolean>(false);
  const [checkedList, setCheckedList] = useState<Array<any>>([]); //选中的列表
  const [allList, setAllList] = useState<Array<any>>([]); //外面总的的列表
  const [innerList, setInnerList] = useState<Array<any>>([]); //内部总的的列表
  const CheckboxGroup = Checkbox.Group;
  const [copyShow, setCopyShow] = useState<boolean>(false);
  const [moreSelectDrawerOpen, setMoreSelectDrawerOpen] = useState(false)
  const dispatch = useDispatch();
  const intl = useIntl();
  useEffect(() => {
    formChange();
  }, [current, pageSize, verifyType.current, activeTabs.current]);
  const { Option } = Select;
  // 页码切换
  const changepage = (page: number, size: any) => {
    setCurrent(page);
    setPageSize(size || 0);
  };
  const detail = (item: any, type: number) => {
    //已经在内部 则直接页面跳转
    if (innerFlag) {
      let flag = activeTabs.current == 'verifyToMe'
      let src = `#/basic/rmanDetail/${item.resourceId}?isaudit=true&${verifyType.current === 'publishVerify' ? 'isPublish=true' : ''}&${flag ? 'showBtn=true&' : ''}instanceId=${verifyType.current === 'publishVerify' ? item.id : item.instanceId}&processInstanceId=${verifyType.current === 'publishVerify' ? item.flowInfo?.processInstance?.id : ''}&flowId=${verifyType.current === 'publishVerify' ? item.flowInfo?.id : ''}&page=${current}&size=${pageSize}`
      // let hideBanner = history.location?.query?.hidebanner || false; // 是否显示导航栏
      // if (hideBanner) {
      //   src = src + `&hidebanner=${hideBanner}`;
      // }
      window.open(src);
      
    } else {
      setInnerFlag(true);
      currentTask.current = item;
      // taskList();
      reset();
    }
  };
  // 全选
  const onCheckAllChange = (e: any) => {
    setCheckedList(e.target.checked ? (allList) : []);
    // setSelectRows(e.target.checked ? allList : []);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  };
  // 单选
  const onChange = (check: Array<any>) => {
    setCheckedList(check);
    // setSelectRows(check);
    setIndeterminate(!!check.length && check.length < (allList).length);
    setCheckAll(check.length === (allList).length);
  };
  useEffect(() => {
    // 判断是否包含文件夹
    let i = checkedList.some((item: any) => {
      return item.type_ === 'folder';
    });
    // 判断是否全为视频资源
    let k = checkedList.every((item: any) => {
      return (
        item.type_ === 'biz_sobey_video' || item.type_ === 'biz_sobey_audio'
      );
    });
    // 判断是否全为视频、文档、图片 供绑定知识点使用
    let l = checkedList.every((item: any) => {
      return (
        item.type_ === 'biz_sobey_video' ||
        item.type_ === 'biz_sobey_picture' ||
        item.type_ === 'biz_sobey_audio' ||
        item.type_ === 'biz_sobey_document'
      );
    });
    setCopyShow(i);
  }, [checkedList]);

  const sort: Array<MenuItem> = [
    {
      label: 'applyDateTime,1',
      value: intl.formatMessage({ id: '发布时间' }),
      icon: <IconFont type="iconchangjiantou-xia" className="icon" />,
    },
    {
      label: 'applyDateTime,0',
      value: intl.formatMessage({ id: '发布时间' }),
      icon: <IconFont type="iconchangjiantou-shang" className="icon" />,
    },
    {
      label: 'resourcename,1',
      value: '资源名称',
      icon: <IconFont type="iconchangjiantou-xia" className="icon" />,
    },
    {
      label: 'resourcename,0',
      value: '资源名称',
      icon: <IconFont type="iconchangjiantou-shang" className="icon" />,
    },
  ];
  const removeSort = [
    {
      label: 'auditdatetime,1',
      value: intl.formatMessage({ id: '下架时间' }),
      icon: <IconFont type="iconchangjiantou-xia" className="icon" />,
    },
    {
      label: 'auditdatetime,0',
      value: intl.formatMessage({ id: '下架时间' }),
      icon: <IconFont type="iconchangjiantou-shang" className="icon" />,
    },
    ...sort
  ]
  // 排序切换
  const handleChange = (value: string) => {
    setCollation(value);
    formChange();
    // setCurrent(1);
  };
  const formChange = () => {
    const formData = form.getFieldsValue();
    //在内部调用接口都是一致的
    // let flage = false;
    // setInnerFlag((pre:any) => {
    //   flage = pre;
    //   return pre;
    // 获取最新的 innerFlag
    const params = {
      keyword: formData.search.keyword,
      page: current,
      resourceType: formData.search.format,
      size: pageSize,
      state: verifyType.current === 'publish' ? 5 : 6,
      isDesc: sortby.current,
    };
    contentListApis.getPublistList({[collation.split(',')[0]]: +collation.split(',')[1]}, params).then((res: any) => {
      if (res?.data.errorCode === 'success') {
        setAllList(res.data.extendMessage.data);
        setCheckedList([]);
        setIndeterminate(false);
        setCheckAll(false);
        setTotalPage(res.data.extendMessage.recordTotal
        );
      } else {
        // message.error('')
      }
    });
  };
  const batchOperate = (list, operateType) => {
    contentListApis.upperlowerframes(list.map((item: any) => item.id), {operateType }).then(res => {
      if (res.data.errorCode == 'success') {
        message.success(intl.formatMessage({ id: `${operateType === 0 ? '发布' : '下架'}成功`}));
        formChange()
      }
    }
    )
  }
  const reset = () => {
    form.resetFields();
    setCheckAll(false);
    setCheckedList([]);
    setCurrent(1);
    setPageSize(30);
  };
  let btnlist: any = [];
  if(verifyType.current === 'publish'){
    btnlist.push({
      title: intl.formatMessage({ id: '下架' }),
      disabled: checkedList.length === 0,
      func: () => batchOperate(checkedList, 1),
      icon: <CloseCircleOutlined />,
      className: 'reject_btn',
    });
  }
  if(verifyType.current === 'remove'){
    btnlist.push({
      title: intl.formatMessage({ id: '发布' }),
      func: () => batchOperate(checkedList, 0),
      disabled: checkedList.length === 0,
      icon: <IconFont type="iconparticipation" />,
      className: 'reject_btn',
    });
  }
  btnlist.push({
    title: intl.formatMessage({ id: '刷新' }),
    func: () => formChange(),
    icon: <RedoOutlined />,
    className: 'refresh_btn',
  });
  return (
    <div className='publish_management'>
      <Tabs
        onChange={e => {
          verifyType.current = e;
          reset();
        }}
      >
        <Tabs.TabPane key={'publish'} tab={intl.formatMessage({ id: '已发布' })}>
        </Tabs.TabPane>
        <Tabs.TabPane key={'remove'} tab={intl.formatMessage({ id: '已下架' })} >
        </Tabs.TabPane>
      </Tabs>
      <div className="body">
        <div className="searchBox">
          <Form form={form} initialValues={{
            search: {
              format: '',
            },
          }} name="verify-form">
            <Row style={{ width: '100%' }} className='pc_show'>
              <Col span={6}>
                <Form.Item name={'resourceName'}>
                  <Input.Group compact>
                    {<Form.Item name={['search', 'format']} noStyle>
                      <Select className="type-width" onChange={formChange}>
                        {options.map((item, index) => (
                          <Option value={item.value || ''} key={'type' + index}>
                            {item.label}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>}
                    <Form.Item name={['search', 'keyword']} noStyle>
                      <Input
                        placeholder={intl.formatMessage({ id: '输入关键词' })}
                        allowClear
                        className='keyword-width'
                        autoComplete={'off'}
                        onPressEnter={formChange}
                      // onSearch={oldFormChange}
                      />
                    </Form.Item>
                  </Input.Group>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
        <div className="operatBox">
          <div className="left_">
              <Checkbox
                indeterminate={indeterminate}
                onChange={onCheckAllChange}
                checked={checkAll}
              >
                {intl.formatMessage({ id: '全部' })}
              </Checkbox>
            {btnlist.map((item: any, index: number) => (
              <div
                key={index}
                className={(item.disabled ? 'disabled' : '') + ' ' + item.className}
                onClick={() => {
                  if (!item.disabled) {
                    item.func();
                  }
                }}
              >
                {item.icon}
                <span>{item.title}</span>
              </div>
            ))}
          </div>
          <div className="right_">
            <Select
              defaultValue="applyDateTime,1"
              style={{ width: 120 }}
              onChange={handleChange}
            >
              {(verifyType.current ==='publish' ? sort :removeSort).map((item, index) => (
                <Option value={item.label} key={index}>
                  {item.value}
                  {item.icon}
                </Option>
              ))}
            </Select>

            {
              rmanGlobalParameter.includes(globalParams.is_show_legendmodel) && <div
                onClick={() => setModeSwitch(true)}
                className="mode_switch"
              >
                <Tooltip title={intl.formatMessage({ id: '图例模式' })}>
                  <IconFont
                    type="iconhebingxingzhuangfuzhi2"
                    className={modeSwitch ? 'active' : ''}
                  />
                </Tooltip>
              </div>
            }
            <div
              onClick={() => setModeSwitch(false)}
              className="mode_switch"
            >
              <Tooltip title={intl.formatMessage({ id: '列表模式' })}>
                <IconFont
                  type="iconliebiao"
                  className={modeSwitch ? '' : 'active'}
                />
              </Tooltip>
            </div>
          </div>
        </div>
        <div className="list">
          {!modeSwitch && <ListTop columns={verifyType.current=== 'publish' ? columns : columns2} />}
          {allList.length == 0 ? (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          ) : (
            <CheckboxGroup
              value={checkedList}
              onChange={onChange}
              style={{ width: '100%' }}
            >
              {(allList).map((item: any) => (
                <ContentItem
                  key={(item.resourceId || item.instanceId) + item.createTime}
                  columns={verifyType.current=== 'publish' ? columns : columns2}
                  modal={modeSwitch}
                  detail={item}
                  recycleBin={true}
                  searchVoiceWord=""
                  downEnable={0}
                  batchOperate={batchOperate}
                  publishManagement={verifyType.current}
                  setInnerItem={setInnerItem}
                  goDetail={(type: number) => detail(item, type)}
                  shareEnble={false}
                  resourceGroup={false}
                />
              ))}
            </CheckboxGroup>
          )}
        </div>
        <div className="pagination">
          <Pagination
            current={current}
            pageSize={pageSize}
            total={totalPage}
            size="small"
            showQuickJumper
            onChange={changepage}
            showTotal={total => intl.formatMessage({
              id: "共条"
            }, { total })}
            showSizeChanger={true}
            pageSizeOptions={['30', '40', '50', '100']}
          />
        </div>
      </div>
    </div>
  );
};

export default PublishManagement;