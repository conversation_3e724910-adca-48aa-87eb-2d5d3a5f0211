import searchTypes from '@/types/searchTypes'
import { IReducers } from '@/types/modelsTypes'

export interface Ijurisdiction {
    Secondarydirectory: boolean,
    searchIndex: string[]
}

export default {
    namespace: 'jurisdiction',
    state: {
        Secondarydirectory: true,
        searchIndex: []
    },
    reducers: {
        changeSecondarydirectory: (state: Ijurisdiction, { payload: data }: IReducers<boolean>) => {
            return {
                ...state,
                Secondarydirectory: data.value
            }
        },
        changeSearchIndex: (state: Ijurisdiction, { payload: data }: IReducers<string[]>) => {
            // debugger
            return {
                ...state,
                searchIndex: [...state.searchIndex, data.value]
            }
        }
    }
}