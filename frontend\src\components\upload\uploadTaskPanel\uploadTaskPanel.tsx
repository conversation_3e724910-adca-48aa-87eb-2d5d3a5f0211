import React, { useEffect } from 'react'
import uploader from '../core/uploader';
import uploadTypes from '@/types/uploadTypes';

const UploadTaskPanel = () => {
    useEffect(()=>{
        initEvents()
    },[])
    const initEvents = () => {
        uploader.setTaskInitSuccess(taskInitSuccessCallback);
        uploader.setTaskUploadProgress(taskUploadProgressCallback);
        uploader.setTaskDeleteSuccess(taskDeleteSuccessCallback);
        uploader.setTaskUploadSuccess(taskUploadSuccessCallback);
    }
    const taskInitSuccessCallback = (task: uploadTypes.IUnfinishedTaskRes) => {

    }

    const taskUploadProgressCallback = (task: uploadTypes.IUnfinishedTaskRes) => {
        console.log(task)
    }

    const taskDeleteSuccessCallback = (task: uploadTypes.IUnfinishedTaskRes) => {

    }

    const taskUploadSuccessCallback = (task: uploadTypes.IUnfinishedTaskRes) => {

    }
    return (
        <ul>
            <li></li>
        </ul>
    )
}

export default UploadTaskPanel