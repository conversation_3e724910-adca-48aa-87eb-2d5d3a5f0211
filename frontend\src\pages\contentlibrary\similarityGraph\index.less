.similarity-graph {
    display: flex;
    height: 100%;

    .left {
        width: 650px;
        height: 100%;
        background-color: #202124;
        color: #fff;
        position: relative;
        padding: 40px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;

        .top {
            width: 100%;
            display: flex;
            justify-content: space-between;

            .upload_btn {
                color: #fff;

                .iconfont {
                    margin-top: -4px;
                    margin-right: 6px;
                }
            }

            .back_btn {
                cursor: pointer;
            }

            .ant-btn-text {
                color: #fff;
            }
        }

        .picture-box {
            flex: 1;

            .picture {
                position: absolute;
                width: auto;
                height: auto;
                max-width: 100%;
                max-height: 100%;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                overflow: hidden;
                padding: 2px;
                box-sizing: border-box;

                img {
                    display: block;
                    width: 100%;
                    height: auto;
                    max-height: 100%;
                    border-radius: 8px;
                    position: relative;
                    z-index: 1;
                    background-color: #000;
                }

                .circle {
                    width: 16px;
                    height: 16px;
                    border: 2px solid #fff;
                    border-radius: 99px;
                    position: absolute;

                    &.circle_1 {
                        left: 0px;
                        top: 0px;
                        border-right: unset;
                        border-bottom: unset;
                    }

                    &.circle_2 {
                        left: 0px;
                        bottom: 0px;
                        border-top: unset;
                        border-right: unset;
                    }

                    &.circle_3 {
                        right: 0px;
                        top: 0px;
                        border-left: unset;
                        border-bottom: unset;
                    }

                    &.circle_4 {
                        right: 0px;
                        bottom: 0px;
                        border-top: unset;
                        border-left: unset;
                    }
                }
            }
        }
    }

    .right {
        flex: 1;
        height: 100%;
        padding: 20px 15px 20px 15px;
        box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.07);

        .contentitem_box {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            height: calc(100vh - 115px);
            overflow-y: auto;

            .ant-empty {
                width: 100%;
                margin-top: 200px;
            }
        }

        .pagination {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .content_item {
            margin-right: 30px;
        }
    }
}