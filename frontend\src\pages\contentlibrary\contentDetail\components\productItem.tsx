import React, {
  FC,
  useEffect,
  useRef,
  useState,
  KeyboardEvent,
  FocusEvent,
} from 'react';

import { Link } from 'umi';
import { DownloadModal, IconFont } from '@/components';
import { Input, Button, message, Modal,Checkbox } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import _ from 'lodash';
import '../index.less';
import SmartService from '@/service/smartService';
import { useParams, useSelector,useHistory, useIntl } from 'umi';
import { IPermission } from '@/models/permission';
import perCfg from '@/permission/config';
import contentListApis from '@/service/contentListApis';
import deleteApis from '@/service/deleteApis';
import { number } from 'yargs';
import { HexToRgb } from '@/utils';
import BindKnowledgeMap from '@/components/bindKnowledgeMap';
import globalParams from '@/permission/globalParams';
import { getSensitiveWord } from '@/utils';

interface IData {
  detail: any;
  handleSecDel: any;
  getQuerySequencemeta: any;
  downVisible: any;
}

const PointsItem: FC<IData> = props => {
  let history: any = useHistory();
  const params = useParams<{ contentId: string }>();
  const intl = useIntl();
  const { detail, handleSecDel, getQuerySequencemeta, downVisible } = props;
  const hidecatalogue =
    JSON.stringify(history.location.query) === '{}'
      ? '0'
      : history.location.query.hidecatalogue;
  const win = window as any;
  const sequencemetaName = useRef<any>();
  const [sequencemetaEdit, setSequencemetaEdit] = useState<boolean>(true);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const { permissions,rmanGlobalParameter } = useSelector<{ permission: any }, IPermission>(
    ({ permission }) => permission,
  );
  const [name, setName] = useState('');
  const [downloadModalVisible, setDownloadModalVisible] = useState(false);
  const [
    bindKnowledgeModalVisible,
    setBindKnowledgeModalVisible,
  ] = useState<boolean>(false);
  //全选start
  const [checkedList, setCheckedList] = useState<any[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<any[]>([]);
  const [indeterminate, setIndeterminate] = useState(false);
  const [checkAll, setCheckAll] = useState(false);
  /**
   * 转换时间成HH:mm:ss
   * @param mss
   */
  const formatDate2HHmmss = (item: any): string => {
    if(!item.duration){
      return `00:00:00:00`
    }
    let result:number = Number((Math.floor(item.duration * 100)/1000000000));
    let h = Math.floor(result / 3600) < 10 ? '0' + Math.floor(result / 3600) : Math.floor(result / 3600);
    let m = Math.floor((result / 60 % 60)) < 10 ? '0' + Math.floor((result / 60 % 60)) : Math.floor((result / 60 % 60));
    let s = Math.floor((result % 60)) < 10 ? '0' + Math.floor((result % 60)) : Math.floor((result % 60));
    let ends = result - Number(parseInt(result+''));
    let ms  = parseInt(Math.round(ends * item.framerate)+'')
    return `${h}:${m}:${s}:${ms>9?ms:'0'+ms}`
  };



  const downloadVideo = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
    setDownloadModalVisible(true);
  };
  //重新合成
  const reStartMixup = async (e: any) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('需要重新合成的对象',detail)
    const res = await contentListApis.getEntityByContentId(detail.contentId_);
    const result = res?.data.entityData.flowParameter;
    console.log(res,result);
    if(result?.beginId){ //独立合成
      if (result.colorImprove) {
        result.colorImprove = HexToRgb(result.colorImprove).toString();
      }
      let data = result.models?.map((e: any) => {
        const { saveName, inPoint, outPoint } = e;
        return { saveName: saveName, inPoint: inPoint, outPoint: outPoint };
      });
      const res = await SmartService.createProduct(
        result.contentid,
        data,
        result,
      );
      if (res?.success) {
        message.success(intl.formatMessage({ id: '提交合成成功' }));
        getQuerySequencemeta();
      }else{
        message.error(intl.formatMessage({ id: '提交合成失败' }));
      }
    }else{ //拼接合成
      if (result?.colorImprove) {
        result.colorImprove = HexToRgb(result.colorImprove).toString();
      }
      let res;
      if (result?.beginId || result?.endId) {
        const data = result.models.map(({ inpoint, outpoint, contentId}: any) => ({
          inPoint: inpoint,
          outPoint: outpoint,
          contentId: contentId,
        }));
        if (result.beginId) {
          data.unshift({
            contentId: result.beginId,
            inPoint: -1,
            outPoint: -1,
          });
          delete result.beginId;
        }
        if (result.endId) {
          data.push({
            contentId: result.endId,
            inPoint: -1,
            outPoint: -1,
          });
          delete result.endId;
        }
        res = await SmartService.mergeProduct2(data, result);
      } else {
        const data = result?.models?.map(({ inPoint, outPoint }: any) => ({
          inPoint: inPoint,
          outPoint: outPoint,
        }));
        res = await SmartService.mergeProduct(params.contentId, data, result);
      }
      if (res?.success) {
        message.success(intl.formatMessage({ id: '提交合成成功' }));
        getQuerySequencemeta();
      }else{
        message.error(intl.formatMessage({ id: '提交合成失败' }));
      }
    }
  };

  useEffect(() => {
    if (!sequencemetaEdit) {
      sequencemetaName.current?.focus();
    }
  }, [sequencemetaEdit]);

  const sequencemetaChangeName = (
    e: KeyboardEvent<HTMLInputElement> | FocusEvent<HTMLInputElement> | any,
    resourceId: string,
  ) => {
    e.preventDefault();
    let name = e.target.value;
    if (name === '') {
      message.error(intl.formatMessage({ id: '标题不能为空' }));

      return;
    }
    getSensitiveWord(name, intl.formatMessage({ id: '文件名' }), async ()=> {
      const res = await contentListApis.renameentity({
        resourceId,
        newName: name,
        oldName: detail.name,
      });
      if (res?.success) {
        message.success(intl.formatMessage({ id: '编辑成功' }));

        getQuerySequencemeta();
        setSequencemetaEdit(true);
      } else {
        message.error(intl.formatMessage({ id: '编辑失败' }));
      }
   })
  };

  const handleDeleteRes = () => {
    deleteApis.deleteResource([detail.contentId_]).then((res: any) => {
      if (res && res.success) {
        message.success('删除成功');
        message.success(intl.formatMessage({ id: '删除成功' }));
        setTimeout(() => {
          getQuerySequencemeta();
        }, 400);
      } else {
        message.error(intl.formatMessage({ id: '删除失败' }));
      }
    });
  };

  // 跳转到详情
  const todetail = (item:any) => {
    const layout = history.location.query?.layout || '0';
    if (layout === '0') {
      // 跳转路径为window.parent的路径加上childurl参数
      let src = window.parent.location.pathname + window.parent.location.hash.split('?')[0];      
      if(src.includes('/teaching/rman')){
        src = window.parent.location.origin + window.parent.location.pathname + `#/teaching/contentDetail?contentid=${item.contentId_}`
      }else if(src.includes('/teaching/contentDetail')){
        src = window.parent.location.origin + window.parent.location.pathname + `#/teaching/contentDetail?contentid=${item.contentId_}`
      }else if(src.includes('/resource')){
         src = window.parent.location.origin + window.parent.location.pathname +  `#/resource/contentDetail?contentid=${item.contentId_}`
      }else if(src.includes('/basic/contentDetail')){
        src = window.parent.location.origin + window.parent.location.pathname +  `#/basic/contentDetail/${item.contentId_}`
      } else if (src.includes("/basic/rmanDetail")) {
        const dev = process.env.NODE_ENV === 'development' ? "" : "/rman"
        src = `${window.location.origin}${dev}/#/basic/rmanDetail/${item.contentId_}`;
      }
      window.open(src);
    }else if(layout === '1'){
      let childurl = encodeURIComponent(`/rman/#/basic/contentDetail/${item.contentId_}?layout=${layout}&hidecatalogue=${hidecatalogue?hidecatalogue:0}`);
      let src = window.parent.location.origin + window.parent.location.pathname +'#/resource?childurl='+ childurl;
      window.open(src);
    }else{
      console.log('其他情况');
    }
      // if(history.location.query.layout){
      //   path = src +`?contentid=${item.contentId_}&asr_status=${item.asr_status}&layout=${history.location.query.layout}`
      // }
      
    
  };
  //百纳秒转时分秒
  const timeFormat=(item:any)=>{
    if(!item.duration){
      return `0s`
    }
    const result = Math.floor((Number(item.duration))/10000000); //百纳秒
    let h = Math.floor(result / 3600);
    let m = Math.floor((result / 60 % 60));
    let s = Math.floor((result % 60));
    let res = '';
    if(h!=0) res += `${h}h`;
    if(m!=0) res += `${m}min`;
    res += `${s}s`;
    return res;
  }
  return (
    // <Link to={`/basic/contentDetail/${detail.contentId_}`} target="_blank">
    <div>
        <div className="sequence_item" onClick={()=>todetail(detail)}>
          <div className={(detail.status && detail.status !=='2')?'backgroundDom sequence_item_content':'sequence_item_content'}>
            <div className='imgContainer'>
              <img src={detail.keyframe_ || '/rman/static/images/video.png'} className={(detail.status && detail.status !=='2')?'opacityDom':''}/>
            </div>
            {detail.duration &&
              <span className='timeSpan'>
                <span>
                  {timeFormat(detail || 0)}
                </span>
              </span>
            }
            {
              (detail.status && detail.status !=='2')&& //没合成完成
              <div>
                {
                  detail.status === '-1' ?
                  <div  className='mixup_container_failed'>
                    <img src='/rman/static/images/mixfailed.png'/>
                    <span>{intl.formatMessage({ id: '合成失败' })}</span>
                  </div> 
                  :
                  <div  className='mixup_container_doing'>
                    <img src='/rman/static/images/mixing.png'/>
                    <span>{intl.formatMessage({ id: '合成中' })}</span>
                  </div>
                }
              </div>
            }
          </div>
          <div className="sequence_content">
            {sequencemetaEdit ? (
              <div className="sequence_title" title={detail.name_}>{detail.name_}</div>
            ) : (
              <Input
                value={name}
                onChange={e => setName(e.target.value)}
                ref={sequencemetaName}
                onPressEnter={e => sequencemetaChangeName(e, detail.contentId_)}
                onBlur={e => sequencemetaChangeName(e, detail.contentId_)}
              />
            )}
             {
              (!detail.status || detail.status ==='2') ?
                <div className="del_icon_product">
                  {rmanGlobalParameter.includes(globalParams.knowledge_map_display)&&
                    hidecatalogue === '0' && 
                    <Button
                      type="link"
                      title={intl.formatMessage({ id: '知识点绑定' })}
                      size="small"
                      onClick={ (e)=> {
                        e.preventDefault();
                        e.stopPropagation();
                        setBindKnowledgeModalVisible(true)
                      }}
                      icon={<IconFont type="iconditu" />}
                    >
                    </Button>
                  }
                  <Button
                    size="small"
                    type="link"
                    onClick={downloadVideo}
                    icon={<DownloadOutlined />}
                  />
                  {permissions.includes(perCfg.resource_edit) && sequencemetaEdit && (
                    <Button
                      size="small"
                      type="link"
                      icon={<IconFont type="iconbianji17" />}
                      onClick={e => {
                        e.preventDefault();
                        e.stopPropagation();
                        setSequencemetaEdit(false);
                        setName(detail.name);
                      }}
                    />
                  )}
                  {permissions.includes(perCfg.resource_delete) && (
                    <Button
                      size="small"
                      type="link"
                      onClick={e => {
                        e.preventDefault();
                        e.stopPropagation();
                        setModalVisible(true);
                      }}
                      icon={<IconFont type="iconshanchu4" />}
                    />
                  )}
              </div>
              :
              (
                (detail.status && detail.status ==='-1')?
                <div className="del_icon">
                  <Button
                    size="middle"
                    className='reStartMixup'
                    type="link"
                    onClick={reStartMixup}
                  >{intl.formatMessage({ id: '重新合成' })}</Button>
                </div>:''
              )
            }
          </div>
        </div>
        {/* 这个下载弹框不能嵌入内部，不然点击事件冒泡会让人疯掉的 */}
        {detail.contentId_ && (
            <DownloadModal
              modalVisible={downloadModalVisible}
              modalClose={() => setDownloadModalVisible(false)}
              downloadlist={[{ contentId_: detail.contentId_ }]}
            />
          )}
        <Modal
          visible={modalVisible}
          title={intl.formatMessage({ id: '删除' })}
          onOk={() => {
            handleDeleteRes();
            setModalVisible(false);
          }}
          onCancel={() => setModalVisible(false)}
        >
          {intl.formatMessage({ id: '是否删除素材到回收站' })}
        </Modal>
        <BindKnowledgeMap
            modalVisible={bindKnowledgeModalVisible}
            modalClose={() => setBindKnowledgeModalVisible(false)}
            analysislist={detail}
          />   
    </div>
    // </Link>
  );
};
export default PointsItem;
