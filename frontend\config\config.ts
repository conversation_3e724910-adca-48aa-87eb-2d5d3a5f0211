import { defineConfig } from 'umi';
import routes from './config.routes';
import proxy from './config.proxy';
// const CompressionPlugin = require("compression-webpack-plugin");
// const productionGzipExtensions = /\.(js|css|json|txt|html|ico|svg)(\?.*)?$/i;

export default defineConfig({
  nodeModulesTransform: {
    // node_modules 编译模式
    type: 'all',
    exclude: [],
  },
  publicPath: '/rman/',
  history: {
    // 路由模式
    type: 'hash',
  },
  antd: {
    dark: false,
    compact: false,
  },
  metas: [
    {
      httpEquiv: 'Cache-Control',
      content: 'no-cache',
    },
    {
      httpEquiv: 'Pragma',
      content: 'no-cache',
    },
    {
      httpEquiv: 'Expires',
      content: '0',
    },
  ],
  routes: routes, // 路由文件
  locale: {
    // 国际化配置
    default: 'zh-CN',
    antd: true,
    title: true,
    baseNavigator: false, // 默认true会去localstorage读umi_locale
  },
  dva: {
    // dva配置
    immer: true,
    hmr: false,
  },
  // mfsu: {},
  // webpack5: {},
  // mfsu:{
  //   production: { output: '.mfsu-production' }
  // },
  proxy: proxy,
  // hash: true, // 打包文件生成hash
  // publicPath: './', // 打包路径
  // forkTSChecker: {}, // ts语法检查
  // devtool: "eval", // 生成map文件
  // theme: defaultTheme,
  dynamicImport: {
    loading: '@/components/loading/loading',
  },
  // favicon: '/rman/static/favicon.ico',
  // links: [
  //   // 样式库
  //   {
  //     rel: 'stylesheet',
  //     type: 'text/css',
  //     href: '//at.alicdn.com/t/font_2019002_uq6va50rnq.css',
  //   },
  // ],
  scripts: [
    {
      type: 'text/javascript',
      src: '/rman/libs/mam-timecode-convert/dist/mam-timecode-convert.js',
    },
  ],
  targets: {
    ie: 10,
  },
  define: {
    SAFE_MODE: false,
  },
  // chunks: process.env.NODE_ENV === 'production' ? ['vendors', 'umi'] : false,
  // chainWebpack(config: any) {
  //   if (process.env.NODE_ENV === 'production') {
  //     config.merge({
  //       optimization: {
  //         minimize: true,
  //         splitChunks: {
  //           chunks: 'async',
  //           minSize: 30000,
  //           minChunks: 2,
  //           automaticNameDelimiter: '.',
  //           cacheGroups: {
  //             vendor: {
  //               name: 'vendors',
  //               test: /^.*node_modules[\\/](?!lodash|react-virtualized|antd).*$/,
  //               chunks: 'all',
  //               priority: 10,
  //             },
  //             antd: {
  //               name: 'antd',
  //               test: /[\\/]node_modules[\\/]antd[\\/]/,
  //               chunks: 'all',
  //               priority: 9,
  //             },
  //             lodash: {
  //               name: 'lodash',
  //               test: /[\\/]node_modules[\\/]lodash[\\/]/,
  //               chunks: 'all',
  //               priority: -2,
  //             },
  //           },
  //         },
  //       },
  //     });
  //     //过滤掉momnet的那些不使用的国际化文件
  //     config
  //       .plugin('replace')
  //       ?.use(require('webpack')?.ContextReplacementPlugin)
  //       ?.tap(() => {
  //         return [/moment[/\\]locale$/, /zh-cn/];
  //       });
  //   } else {
  //     // 为开发环境修改配置...
  //   }
  // },
  // 由于服务器启用了gzip 前端不用做了
  // chainWebpack(memo){
  //   memo.plugin('CompressionPlugin').use(new CompressionPlugin({
  //     // filename: "[path].gz[query]",
  //     algorithm: "gzip",
  //     test: productionGzipExtensions,
  //     // 只处理大于xx字节 的文件，默认：0
  //     threshold: 10240,
  //     // 示例：一个1024b大小的文件，压缩后大小为768b，minRatio : 0.75
  //     minRatio: 0.6, // 默认: 0.8
  //     // 是否删除源文件，默认: false
  //     deleteOriginalAssets: false
  //   }));
  // }

  chunks:['vendors', 'umi'],
  chainWebpack: function (config, { webpack }) {
    config.merge({
      optimization: {
        // runtimeChunk: true,
        splitChunks: {
          chunks: 'all',
          minSize: 20000,
          minChunks: 2,
          automaticNameDelimiter: '.',
          cacheGroups: {
            vendors: {
              test: /[\\/]node_modules[\\/]/,
              name(module:any) {
                const reg = /(echarts|@antv|zrender|lodash|jquery|xgplayer-flv|crypto-js|@ant-design|antd|regl|cropperjs|rc-picker|rc-table|rc-tree|rc-tabs|rc-select|react-dom|header|core|matrix|p_Mapv3|vendors)/;
                if (reg.test(module.identifier())) {
                  const [chunkName] = reg.exec(module.identifier()) || [];
                  return `npm.${chunkName}`;
                };
                return 'vendors';
              },
              priority: 10,
            }            
          },
        },
      }
    });
  },
});
