import React, {
    FC,
    useState,
    useRef,
    useEffect,
    RefObject,
    InputHTMLAttributes,
    ChangeEvent,
  } from 'react';
  import {
    Form,
    message,
    Button,
    Checkbox,
    Empty,
    Tooltip,
    Pagination,
    Select,
    Breadcrumb,
  } from 'antd';
  import './index.less';
  import { CheckboxChangeEvent } from 'antd/lib/checkbox';
  import contentListApis from '@/service/contentListApis';
  import searchTreeApis from '@/service/searchTreeApis';
  import {
    formatMessage,
    useIntl,
    useSelector,
    useDispatch,
    Idownlist,
  } from 'umi';
  import {
    MenuItem,
    Idetail,
    IrequestsearchData,
    MaterialList,
    BreadCrumb,
    ISearchlist,
  } from './type';
  import searchTypes from '@/types/searchTypes';
  import {
    SearchTree,
    ContentItem,
    SearchBox,
    ListTop,
    UploadBox,
    UploadTask,
    LinkBox,
    Loading,
    UploadButton,
    IconFont,
    NewFolder,
    DownloadModal,
  } from '../../../components/index';
  import { IPermission } from '@/models/permission';
  import perCfg from '@/permission/config';
  
  const { Option } = Select;
  const CheckboxGroup = Checkbox.Group;
  
  const sort: Array<MenuItem> = [
    {
      label: 'relevant',
      value: '相关度',
    },
    {
      label: 'timedown',
      value: '上传时间',
      icon: <IconFont type="iconchangjiantou-xia" className="icon" />,
    },
    {
      label: 'timeup',
      value: '上传时间',
      icon: <IconFont type="iconchangjiantou-shang" className="icon" />,
    },
    {
      label: 'namedown',
      value: '名称',
      icon: <IconFont type="iconchangjiantou-xia" className="icon" />,
    },
    {
      label: 'nameup',
      value: '名称',
      icon: <IconFont type="iconchangjiantou-shang" className="icon" />,
    },
  ];
  const ContentList: FC<{}> = () => {
    const intl = useIntl();
    const [form] = Form.useForm();
    const dispatch = useDispatch();
    const [recycleBin, setRecycleBin] = useState<boolean>(false); // 回收站
  
    const [modeSwitch, setModeSwitch] = useState<boolean>(
      localStorage.getItem('view-mode') === '1',
    ); // 视图切换
    const [indeterminate, setIndeterminate] = useState<boolean>(false);
    const [checkAll, setCheckAll] = useState<boolean>(false);
    const [checkedList, setCheckedList] = useState<Array<any>>([]); //选中的列表
    const [allList, setAllList] = useState<Array<MaterialList>>([]); //总的的列表
  
    const [singleOrAll, setSingleOrAll] = useState<boolean>(false);
  
    const [searchData, setSearchData] = useState<IrequestsearchData>({
      starttime: [], //开始时间
      endtime: [], //结束时间
      teacher: [], //老师
      college: [], //学院
      keyword: [], //关键词
      major: [], //专业
      type: [], //素材类型
      susagestatus: '', //素材类型
      sintelligence: '', //智能
    });
  
    const [collation, setCollation] = useState<string>('timedown'); //排序
    const [totalPage, setTotalPage] = useState<number>(0); //素材总数
    const [current, setCurrent] = useState<number>(1); //当前页码
    const [pageSize, setPageSize] = useState<number>(30); //每页条数
  
    const [folderId, setFolderId] = useState<string>(''); //文件夹ID
    const [getFolder, setGetFolder] = useState<Idetail | null>(null); //新建文件夹
    const [renameShow, setRenameShow] = useState<boolean>(false); // 重命名
    const [testTree, setTestTree] = useState<searchTypes.IFolder | any>([]);
    const [breadCrumb, setBreadCrumb] = useState<Array<BreadCrumb>>([
      { name: '', folderId: '' },
    ]);
    const [downloadModalVisible, setDownloadModalVisible] = useState<boolean>(
      false,
    );
    const downlist = useSelector<{ download: any }, Idownlist>(({ download }) => {
      return download.downlist;
    });
    const { permissions } = useSelector<{ permission: any }, IPermission>(
      ({ permission }) => permission,
    );
    useEffect(() => {
      if (folderId !== '') {
        recycleBin
          ? searchRecycleBin()
          : singleOrAll
          ? searchAllList()
          : searchFolderList();
      } else {
        searchpublic();
      }
    }, [searchData, collation, current, folderId]);
    useEffect(() => {
      localStorage.setItem('view-mode', modeSwitch ? '1' : '0');
    }, [modeSwitch]);
    //目录树
    const searchpublic = async () => {
      await searchTreeApis.tree('public').then(res => {
        if (res && res.data && res.success) {
          if (folderId === '') {
            setFolderId(res.data[0].code);
          }
          setTestTree(res.data);
        }
      });
    };
    // 公共素材检索
    const searchFolderList = async () => {
      let data: ISearchlist = processingData();
      await contentListApis.searchfolder(data).then(res => {
        if (res && res.data && res.success) {
          setTotalPage(res.data.recordTotal);
          if (res.data.pageTotal < 0 && current !== 1) {
            setCurrent(current - 1);
            return;
          }
          setAllList(res.data.data);
          setCheckedList([]);
          setIndeterminate(false);
          setCheckAll(false);
          setBreadCrumb(res.data.breadcrumbs);
          dispatch({
            type: 'jurisdiction/changeSecondarydirectory',
            payload: {
              value:
                (res.data.breadcrumbs.length === 1 &&
                  res.data.breadcrumbs[0].name === '公共资源') ||
                (res.data.breadcrumbs[1] &&
                  res.data.breadcrumbs[1].name === '录播资源'),
            },
          });
        } else {
          setAllList([]);
        }
      });
    };
    // 全部素材检索
    const searchAllList = async () => {
      let data: ISearchlist = processingData();
      await contentListApis.searchall(data).then(res => {
        if (res && res.data && res.success) {
          setTotalPage(res.data.recordTotal);
          if (res.data.pageTotal < 0 && current !== 1) {
            setCurrent(current - 1);
            return;
          }
          setAllList(res.data.data);
          setCheckedList([]);
          setIndeterminate(false);
          setCheckAll(false);
          setBreadCrumb(res.data.breadcrumbs);
          dispatch({
            type: 'jurisdiction/changeSecondarydirectory',
            payload: {
              value:
                (res.data.breadcrumbs.length === 1 &&
                  res.data.breadcrumbs[0].name === '公共资源') ||
                (res.data.breadcrumbs[1] &&
                  res.data.breadcrumbs[1].name === '录播资源'),
            },
          });
        } else {
          setAllList([]);
        }
      });
    };
    const searchRecycleBin = async () => {
      contentListApis
        .getrecyclebinlist({
          pageIndex: current,
          pageSize: pageSize,
        })
        .then(res => {
          if (res && res.data && res.success) {
            setTotalPage(res.data.recordTotal);
            if (res.data.pageTotal < 0 && current !== 1) {
              setCurrent(current - 1);
              return;
            }
            setAllList(res.data.data);
            setCheckedList([]);
            setIndeterminate(false);
            setCheckAll(false);
          } else {
            setAllList([]);
          }
        });
    };
    // 数据处理
    const processingData = () => {
      let list: ISearchlist = {
        folderId: folderId,
        sortFields: [
          {
            field: collation.indexOf('time') != -1 ? 'createDate_' : 'name_',
            isDesc: collation.indexOf('down') != -1 ? true : false,
          },
        ],
        pageIndex: current,
        pageSize: pageSize,
      };
      // let sotrList = []
      // if (collation !== 'relevant') {
      //   sotrList = [
      //     {
      //       field: collation.indexOf('time') != -1 ? 'createDate_' : 'name_',
      //       isDesc: collation.indexOf('down') != -1 ? true : false,
      //     },
      //   ]
      // }
      collation === 'relevant' && (list.sortFields = []);
      searchData.keyword.length !== 0 && (list.keyword = searchData.keyword);
      let condition = [];
      searchData.starttime.length !== 0 &&
        condition.push(
          {
            field: 'createDate_',
            searchRelation: 6,
            value: searchData.endtime,
          },
          {
            field: 'createDate_',
            searchRelation: 4,
            value: searchData.starttime,
          },
        );
      searchData.type.length !== 0 &&
        condition.push({
          field: 'type_',
          searchRelation: 0,
          value: searchData.type,
        });
      searchData.teacher.length !== 0 &&
        condition.push({
          field: 'teacher',
          searchRelation: 0,
          value: searchData.teacher,
        });
      searchData.college.length !== 0 &&
        condition.push({
          field: 'college',
          searchRelation: 0,
          value: searchData.college,
        });
      searchData.major.length !== 0 &&
        condition.push({
          field: 'major',
          searchRelation: 0,
          value: searchData.major,
        });
      searchData.susagestatus &&
        condition.push({
          field: 'release_status',
          searchRelation: Number(searchData.susagestatus),
          value: ['yes'],
        });
      searchData.sintelligence &&
        condition.push({
          field: 'intelliState',
          searchRelation: 0,
          value: [searchData.sintelligence],
        });
      list.conditions = condition;
      return list;
    };
    // 跳转到详情
    const detail = (item: MaterialList) => {
      if (!renameShow && !recycleBin) {
        if (item.type_ === 'folder') {
          setFolderId(item.contentId_);
          reset();
        } else {
          window.open('#/basic/contentDetail/' + item.contentId_);
        }
      }
    };
    // 排序切换
    const handleChange = (value: string) => {
      setCollation(value);
      setCurrent(1);
    };
    // 格式切换
    const optionsChange = (value: string) => {
      resourceSearch();
    };
    const resourceSearch = () => {
      const {
        search,
        data,
        college,
        major,
        teacher,
        usagestatus,
        intelligence,
      } = form.getFieldsValue();
      setSingleOrAll(
        !!(
          !!(search ? search.keyword || search.format : search) ||
          data ||
          college ||
          !!(major ? major.length : major) ||
          teacher ||
          usagestatus ||
          intelligence
        ),
      );
      setSearchData({
        starttime: data ? [data[0].format('YYYY-MM-DD') + ' 00:00:00'] : [],
        endtime: data ? [data[1].format('YYYY-MM-DD') + ' 23:59:59'] : [],
        teacher: teacher ? [teacher] : [],
        college: college ? [college] : [],
        keyword: search ? (search.keyword ? [search.keyword] : []) : [],
        major: major ? major : [],
        type: search ? (search.format ? [search.format] : []) : [],
        susagestatus: usagestatus,
        sintelligence: intelligence,
      });
      setCurrent(1);
    };
    // 页码切换
    const changepage = (page: number) => {
      if (page === 0) {
        setCurrent(1);
      } else {
        setCurrent(page);
      }
    };
    // 全选
    const onCheckAllChange = (e: CheckboxChangeEvent) => {
      setCheckedList(e.target.checked ? allList : []);
      setIndeterminate(false);
      setCheckAll(e.target.checked);
    };
    // 单选
    const onChange = (check: Array<any>) => {
      setCheckedList(check);
      setIndeterminate(!!check.length && check.length < allList.length);
      setCheckAll(check.length === allList.length);
    };
    // 刷新
    const refresh = () => {
      setGetFolder(null);
      return Promise.all([
        recycleBin
          ? searchRecycleBin()
          : singleOrAll
          ? searchAllList()
          : searchFolderList(),
        searchpublic(),
      ]);
    };
    // 新建文件夹
    const newFolder = () => {
      setGetFolder({
        type_: 'folder',
        name_: '新建文件夹',
        fatherTreeId: folderId,
        keyframe: '/rman/static/images/folder.png',
      });
    };
    // 目录树选择
    const selsctTree = async (id: string) => {
      setFolderId(id);
      setRecycleBin(id === 'recyclebin');
      reset();
    };
    // 重置
    const reset = () => {
      form.resetFields();
      resourceSearch();
    };
    // 导航条跳转
    const goBreadcrumb = (item: BreadCrumb) => {
      if (item.folderId !== folderId) {
        setFolderId(item.folderId);
        reset();
      }
    };
    return (
      <div className="contentlist_container">
        <div className="content_bottom">
          <div className="directory_tree">
            <SearchTree
              onSelect={selsctTree}
              basictree={testTree}
              id={folderId}
            />
          </div>
          <div className="content">
            {!recycleBin && (
              <div className="top_search">
                <SearchBox
                  optionsChange={optionsChange}
                  resourceSearch={resourceSearch}
                  form={form}
                  reset={reset}
                />
                {permissions.includes(perCfg.resource_edit) && (
                  <UploadButton targetFolder={folderId} />
                )}
              </div>
            )}
            <div className={`content_box ${recycleBin ? 'recyclebin_box' : ''}`}>
              <div className="box_top">
                <div className="top_left">
                  <Checkbox
                    indeterminate={indeterminate}
                    onChange={onCheckAllChange}
                    checked={checkAll}
                  >
                    {intl.formatMessage({
                      id: 'all',
                      defaultMessage: '全部',
                    })}
                  </Checkbox>
                  <LinkBox
                    newFolder={newFolder}
                    refresh={refresh}
                    LinkBoxList={checkedList}
                    downloadBoxOpen={() => setDownloadModalVisible(true)}
                    recycleBin={recycleBin}
                    copyAddMove={
                      breadCrumb.length === 1 && breadCrumb[0].name === '公共资源'
                    }
                    datalength={allList.length}
                  />
                </div>
                <div className="top_right">
                  <div>
                    {!recycleBin && (
                      <Select
                        defaultValue="timedown"
                        style={{ width: 120 }}
                        onChange={handleChange}
                      >
                        {sort.map((item, index) => (
                          <Option value={item.label} key={index}>
                            {item.value}
                            {item.icon}
                          </Option>
                        ))}
                      </Select>
                    )}
                  </div>
                  <div
                    onClick={() => setModeSwitch(true)}
                    className="mode_switch"
                  >
                    <Tooltip title="图例模式">
                      <IconFont
                        type="iconhebingxingzhuangfuzhi2"
                        className={modeSwitch ? 'active' : ''}
                      />
                    </Tooltip>
                  </div>
                  <div
                    onClick={() => setModeSwitch(false)}
                    className="mode_switch"
                  >
                    <Tooltip title="列表模式">
                      <IconFont
                        type="iconliebiao"
                        className={modeSwitch ? '' : 'active'}
                      />
                    </Tooltip>
                  </div>
                </div>
              </div>
              <div className="box_bottom">
                {!recycleBin && (
                  <Breadcrumb>
                    {breadCrumb.map((item, index) => (
                      <Breadcrumb.Item key={index}>
                        <a onClick={() => goBreadcrumb(item)}>{item.name}</a>
                      </Breadcrumb.Item>
                    ))}
                  </Breadcrumb>
                )}
                <div
                  className="contentitem_box"
                  style={!modeSwitch ? { minWidth: 1000 } : {}}
                >
                  {modeSwitch ? '' : <ListTop />}
                  {allList.length === 0 && getFolder === null ? (
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  ) : (
                    ''
                  )}
                  <CheckboxGroup
                    value={checkedList}
                    onChange={onChange}
                    className={`${modeSwitch ? 'height1' : 'height2'} height`}
                    style={{ width: '100%' }}
                  >
                    {getFolder === null ? (
                      ''
                    ) : (
                      <NewFolder
                        modal={modeSwitch}
                        detail={getFolder}
                        refresh={refresh}
                        setallrename={item => setRenameShow(item)}
                        privateorpublic={breadCrumb[0].name === '公共资源'}
                      />
                    )}
                    {allList.map((item, index) => (
                      <ContentItem
                        key={item.contentId_}
                        modal={modeSwitch}
                        detail={item}
                        goDetail={() => detail(item)}
                        refresh={refresh}
                        setallrename={item => setRenameShow(item)}
                        downloadBoxOpen={() => setDownloadModalVisible(true)}
                        recycleBin={recycleBin}
                      />
                    ))}
                  </CheckboxGroup>
                </div>
                <div className="pagination">
                  <Pagination
                    current={current}
                    showSizeChanger={false}
                    defaultPageSize={pageSize}
                    total={totalPage}
                    size="small"
                    showQuickJumper
                    onChange={changepage}
                    showTotal={total => `共 ${total} 个`}
                    // showTotal={total => intl.formatMessage({
                    //   id: "page-total",
                    //   defaultMessage: `共 ${total} 页`
                    // })}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <UploadTask refresh={refresh} />
        {permissions.includes(perCfg.resource_edit) && (
          <UploadBox
            // onCancel={() => setUploadBoxVisible(false)}
            // uploadFiles={uploadFile}
            targetFolder={folderId}
            folderName={breadCrumb[breadCrumb.length - 1].name}
          />
        )}
        <DownloadModal
          modalVisible={downloadModalVisible}
          modalClose={() => setDownloadModalVisible(false)}
          downloadlist={downlist}
        />
        <Loading />
      </div>
    );
  };
  export default ContentList;
  