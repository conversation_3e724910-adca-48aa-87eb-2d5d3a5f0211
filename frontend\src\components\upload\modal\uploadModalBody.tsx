import React, { FC, useMemo, useState } from 'react'
import uploadTypes from '@/types/uploadTypes'
import { Checkbox } from 'antd'

interface IUploadModalBodyProps {
    tasks: uploadTypes.ITask[]
}

const UploadModalBody: FC<IUploadModalBodyProps> = (props) => {
    console.log(props.tasks)
    const columns = [
        {
            title: '名称',
            dataIndex: 'name',
            render:(text:string,record:uploadTypes.ITask) => {
                return record.metadata.name
            }
        }
    ]
    return (
        <div className="upload-modal-body">
            <div className="upload-files">
                <ul>
                    {
                        props.tasks.map((item,index)=>{
                            return (
                                <li key={index}>
                                    <Checkbox></Checkbox>
                                    <span>{item.metadata.name}</span>
                                </li>
                            )
                        })
                    }
                </ul>
            </div>
            <div className="upload-metadatas">

            </div>
        </div>
    )
}

export default UploadModalBody