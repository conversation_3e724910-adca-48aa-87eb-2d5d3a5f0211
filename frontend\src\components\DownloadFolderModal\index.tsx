import React, { useState, useEffect } from 'react';
import { Modal, message, Progress } from 'antd';
import './index.less'
import progressApis from '@/service/progress';
import rmanApis from '@/service/rman';
interface interfaceFolder {
    modalVisible: boolean;
    folderObj: any;
    setModalVisible: () => void;
}

const DownloadFolderModal = (props: interfaceFolder) => {
    const { modalVisible, setModalVisible, folderObj } = props;
    const [downloadProgress, setDownloadProgress] = useState<any>(0); // 下载
    const [compressionProgress, setCompressionProgress] = useState<any>(0); // 压缩
    const [loadingProgress, setLoadingProgress] = useState<any>(0); // 加载

    useEffect(() => {
        if (folderObj.taskId && modalVisible) {
            let pollInterval: any;

            const startDownload = async () => {
                try {
                    // 第一次调用进度接口
                    let firstResponse: any
                    await progressApis.folderDownloadProgess({ taskId: folderObj.taskId }).then((res: any) => {
                        if (res?.success) {
                            firstResponse = res?.data
                        } else {
                            throw new Error(res?.error);
                        }
                    })
                    // 第一次调用就成功，直接调用压缩包下载接口
                    if (firstResponse?.isCompleted) {
                        downloadZip(firstResponse?.destinationZipFile, firstResponse?.zipFileLength);
                        // closeModal();
                        setDownloadProgress(100)
                        setCompressionProgress(100)
                    } else {
                        // 开始轮询
                        pollInterval = setInterval(async () => {
                            await progressApis.folderDownloadProgess({ taskId: folderObj.taskId }).then((res: any) => {
                                if (res?.success) {
                                    const { filesDownloaded, totalFiles, isCompleted, destinationZipFile, step, zipFileLength } = res?.data;
                                    if (step == 'download') {
                                        setDownloadProgress((filesDownloaded / totalFiles * 100).toFixed(0))
                                    } else if (step == 'compression') {
                                        setDownloadProgress(100)
                                        setCompressionProgress((filesDownloaded / totalFiles * 100).toFixed(0))
                                    }
                                    if (isCompleted) {
                                        clearInterval(pollInterval);
                                        downloadZip(destinationZipFile, zipFileLength);
                                        // closeModal();
                                    }
                                } else {
                                    throw new Error(res?.error);
                                }
                            })
                        }, 2000); // 每几秒轮询一次
                    }
                } catch (error: any) {
                    pollInterval && clearInterval(pollInterval);
                    closeModal();
                }
            };

            startDownload();

            return () => {
                clearInterval(pollInterval);
            };
        }
    }, [folderObj, modalVisible]);

    const closeModal = () => {
        setModalVisible();
        setDownloadProgress(0);
        setCompressionProgress(0);
    }

    // const downloadZip = (urlString: string) => {
    //     progressApis.folderDownloadZip(JSON.stringify(urlString)).then((res: any) => {
    //         if (!(res?.success == false)) {
    //             const blob = new Blob([res], { type: 'application/zip' });
    //             const url = window.URL.createObjectURL(blob);

    //             // 创建一个隐藏的 <a> 元素用于下载
    //             const a = document.createElement("a");
    //             a.style.display = "none";
    //             a.href = url;

    //             // 设置下载文件的文件名
    //             a.download = folderObj.fileName; // 使用文件夹名称作为下载文件名

    //             // 将 <a> 元素添加到 DOM 并触发点击
    //             document.body.appendChild(a);
    //             a.click();

    //             // 释放 URL 对象并移除 <a> 元素
    //             window.URL.revokeObjectURL(url);
    //             document.body.removeChild(a);
    //             downloadDelete(urlString); // 触发删除操作
    //         }
    //     })
    // }

    const downloadZip = async (newUrl: string, size: number) => {
        const url = newUrl.replace('/mstorage', '');
        setLoadingProgress(0); // 重置进度
        try {
            const response = await fetch(url);
            const contentLength: any = response.headers.get('content-length') || size || 0;
            if (!contentLength) {
                // 模拟假的进度条
                let fakeProgress = 0;
                const fakeInterval = setInterval(() => {
                    const randomIncrement = Math.floor(Math.random() * 4) + 2;
                    fakeProgress += randomIncrement;
                    setLoadingProgress(fakeProgress);
                    if (fakeProgress >= 90) {
                        clearInterval(fakeInterval);
                    }
                }, 5000);
            }
            const total = parseInt(contentLength, 10);
            let loaded = 0;
            const newResponse = new Response(
                new ReadableStream({
                    start(controller) {
                        const reader = response.body!.getReader();

                        function read() {
                            reader.read().then(({ done, value }) => {
                                if (done) {
                                    controller.close();
                                    return;
                                }
                                loaded += value!.byteLength;
                                if (contentLength) {
                                    const progress = Math.round((loaded / total) * 100);
                                    setLoadingProgress(progress);
                                }
                                controller.enqueue(value);
                                read();
                            }).catch(error => {
                                controller.error(error);
                            });
                        }

                        read();
                    },
                })
            );

            const blob = await new Blob([await newResponse.blob()], { type: 'application/zip' });
            const objectUrl = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = objectUrl;
            a.download = folderObj.fileName;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(objectUrl);
            document.body.removeChild(a);
            setLoadingProgress(100);
            closeModal();
            downloadDelete(newUrl); // 触发删除操作
        } catch (error) {
            console.error('下载出错:', error);
        }
    };


    // 下载成功后删除打包流destinationZipFile
    const downloadDelete = (url: string) => {
        rmanApis.folderDownloadDelete(JSON.stringify(url)).then((res: any) => {
            if (!res.success) {
                message.error(res.error);
            }
        });
    }

    return (
        <Modal
            className='download-folder-modal'
            title={null}
            open={modalVisible}
            footer={null}
            closable={false}
        >
            <div>
                <span>下载：</span>
                <Progress percent={downloadProgress} />
            </div>
            <div>
                <span>压缩：</span>
                <Progress percent={compressionProgress} />
            </div>
            <div>
                <span>保存：</span>
                <Progress percent={loadingProgress} />
            </div>
        </Modal>
    );
};

export default DownloadFolderModal;