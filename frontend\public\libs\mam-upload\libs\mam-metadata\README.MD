# mam metadata

元数据组件，用于元数据的展示，编辑表单，数据验证等。

## 开发说明
- `mam2-metadata-form` 中的2 ，是因为老项目中的代码已经有相同的名称，而且还在使用，所以这边暂时改名为 mam2-metadata-form，如果后续其他地方全替换成该组件了，可以考虑改回mam-metadata-form。并且删掉原来项目中的相关代码
- 此组件是兼容元数据`无分组`和`有分组`的两个数据结构的

## 开发备忘
- BUG，字段选择器，检索功能只能检索第一级的，子级的无法检索。界面需要细化完善
- 复杂类型 中存在定时器，感觉不需要，有时间需要重构一下。 胡耀 2018-07-03 16:52:25

[TOC]

## 依赖
> [jQuery](http://jquery.com/)

> [lodash](https://lodash.com/)

> [angularjs](https://angularjs.org/)

> [angular-ui-bootstrap](https://angular-ui.github.io/bootstrap/)

> [mam-base](http://172.16.128.150/front-end/mam-base)

> [mam-ng](http://172.16.128.150/front-end/mam-ng)

> [requirejs](http://requirejs.org/)
