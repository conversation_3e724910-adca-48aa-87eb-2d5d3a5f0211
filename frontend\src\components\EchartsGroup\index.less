.circleChart_statistisc {
  position: relative;
  display: flex;
  // flex-direction: column;
  width: 100%;
  height: 100%;
  justify-content: center;
  .circleChart_title3 {
    position: absolute;
    display: flex;
    top: 28%;
    right: 12%;
    bottom: 0;
    margin: auto;
    font-size: 14px;
    &.homeother {
      top: 22%;
      right: 11%;
    }
    .name {
      margin-top: 7px;
    }
    .number {
      span {
        font-size: 22px;
        color: #E4635D;
      }
    }
    span {
      font-size: 14px;
      color: #333;
    }
  }
  .circleChart_title1, .circleChart_title2 {
    width: 100px;
    height: 100px;
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    .number {
      span {
        font-size: 22px;
        color: #E4635D;
      }
    }
    span {
      font-weight: bold;
      font-size: 14px;
      color: #333;
    }
  }
}
.circle-charts {
  width: 100%;
  height: 70%;
}
.circle-charts2 {
  width: 100%;
  height: 100%;
}
// .class_collect{
//   position: absolute;
//   left: 40px;
//   div{
//     background: url(../../pages/learn/收藏情况.svg);
//     width: 16px;
//     height: 17px;
//     position: relative;
//     top: 16px;
//     left: -21px;
//   }
// }
.class_add{
  // margin-top: 0;
  // margin-left: 0;
  // font-size: 16px;
  // // width: 150px;
  position: absolute;
  left: 40px;
  // div{
  //   background: url(../../pages/learn/加入课程情况.svg)
  //   100% no-repeat;
  //   width: 16px;
  //   height: 16px;
  //   position: relative;
  //   top: 16px;
  //   left: -21px;
  //   // color: #fff;
  // }
 
}
.class_adds{
  position: relative !important;
  width: 150px !important;
  height: 30px !important;
  font-size: 16px;
  
  // div{
  //   background: url(../../pages/learn/加入课程情况.svg)
  //   100% no-repeat;
  //   width: 16px;
  //   height: 16px;
  //   position: relative;
  //   top: 16px;
  //   left: -21px;
  //   // color: #fff;
  // }
}
// 台式
@media screen and (max-width: 2000px) {
  .mycirclecharts {
    width: 780px;
  }
  .mycirclecharts2 {
    width: 510px;
  }
}
@media screen and (max-width: 1780px) {
  .mycirclecharts {
    width: 440px;
  }
  .mycirclecharts2 {
    width: 360px;
  }
}
// 14寸
@media screen and (max-width: 1400px) {
  .mycirclecharts {
    width: 500px;
  }
  .mycirclecharts2 {
    width: 350px;
  }
  .circleChart_title1 {
    font-size: 14px;
  }
  .circleChart_title2 {
    font-size: 14px;
  }
  .circleChart_title3 {
    font-size: 14px;
  }
}
@media screen and (max-width: 1200px) {
  .mycirclecharts {
    width: 400px;
  }
  .mycirclecharts2 {
    width: 360px;
  }
}
@media screen and (max-width: 1100px) {
  .mycirclecharts {
    width: 600px;
  }
  .mycirclecharts2 {
    width: 360px;
  }
}
