import React, { FC, useEffect, useRef, useState } from 'react';
import { useSelector } from 'umi';
import { IConfig } from '@/models/config';
interface props {
  userInfo: any
}
const LeftMenu: FC<props> = ({ userInfo }) => {
  const commonMenu = useRef<any>();
  const configs: any = useSelector<{ config: any }, IConfig>(
    ({ config }) => config
  );
  const getMenus = (header: string) => {
    commonMenu.current = new CommonMenu("menu-header-box", {
      headerUrl: header,
      headerHeight: "calc(100vh - 52px)"
    });
    commonMenu.current.open(); // 开启挂载
  };
  const getMenusFromMicroMajor = async (header: string) => {
    commonMenu.current = new CommonMenu("menu-header-box", {
      headerUrl: header,
      headerHeight: "calc(100vh - 52px)",
      defaultMenus: [
        {
          disable: false,
          icon: "iconxueshengguanli",
          id: "f2b4051305a34654b15a4f82ddfeb509",
          isH5Show: false,
          isInternal: true,
          isSystem: false,
          link: "/learn/workbench/#/micromajor/course?micromajor=true",
          name: "微专业",
          key: "micromajor",
          selectedIcon: ""
        },
        {
          disable: false,
          icon: "iconmenu_rman",
          id: "eea0d9d02bac479fbdb47920be52b834",
          isH5Show: true,
          isInternal: true,
          isSystem: true,
          link: "/rman/#/basic/rmanCenterList?micromajor=true",
          name: "资源",
          key: "basic",
          selectedIcon: "icona-wodeziyuanxuanzhong"
        },
        {
          disable: false,
          icon: "iconmenu_exam",
          id: "e822672b64c74ee2845221329811bd86",
          isH5Show: true,
          isInternal: true,
          isSystem: true,
          link: "/exam/#/exam/topicManage?micromajor=true",
          name: "试题",
          key: "exam",
          selectedIcon: "icona-wodeshitixuanzhong"
        },
        {
          disable: false,
          icon: "iconmenu_coursemap",
          id: "96d3efa365b448faaea4559a4403d8ac",
          isH5Show: true,
          isInternal: false,
          isSystem: true,
          link: "/learn/workbench/#/coursemap/minemap?micromajor=true",
          name: "知识地图",
          key: "coursemap",
          selectedIcon: ""
        },
      ],
      otherMenus: [
        {
          icon: "iconmenu_message",
          text: "消息通知",
          url: "/unifiedplatform/#/personal/message?micromajor=true",
          key: "message?micromajor=true",
          activeIcon: "iconmessage_selected",
        },
      ]
    });
    commonMenu.current.open(); // 开启挂载
  }

  useEffect(() => {
    const hash = window.location.hash
    if (hash.includes('micromajor=true')) {
      getMenusFromMicroMajor(require('@/images/login/default-avatar.png'));
    } else {
      getMenus(require('@/images/login/default-avatar.png'));
    }
  }, []);

  useEffect(() => {
    if (configs.mobileFlag) {
      commonMenu.current?.changeExpand(configs.leftMenuExpand);
    }
  }, [configs.leftMenuExpand]);
  useEffect(() => {
    if (userInfo?.avatar) {
      commonMenu.current?.setHeader(userInfo.avatar)
    }
  }, [userInfo])
  return <div> 
    <div id="menu-header-box"></div> 
  </div>
};
export default LeftMenu
