import uploadTypes from '@/types/uploadTypes'
import { asyncLoadScript } from '@/utils';

import { message, Modal } from "antd";
import _ from 'lodash'
import $ from 'jquery'
import { getDvaApp } from 'umi';

const { _store } = getDvaApp()
class Uploader {
    private params!: uploadTypes.IUploadOptions;
    public tf: any;
    private taskInitSuccess!: (task: uploadTypes.ITask) => void;
    private taskInitBefore!: (task: uploadTypes.ITask) => void;
    private taskUploadProgress!: (task: uploadTypes.ITask) => void;
    private taskDeleteSuccess!: (task: uploadTypes.ITask) => void;
    private taskUploadSuccess!: (task: uploadTypes.ITask) => void;

    constructor() {
        (window as any).$ = (window as any).jQuery = $
        document.cookie = 'apiVersion=mamcore2.3'
        // asyncLoadScript('/rman/libs/mam-upload/dist/libs/oss/aliyun-oss-sdk.min.js'); //屏蔽掉老版本
        asyncLoadScript('/rman/libs/mam-upload/dist/libs/oss-s3/aws-sdk.min.js');
        asyncLoadScript('/rman/libs/mam-upload/dist/libs/oos/oos-sdk.min.js');
        asyncLoadScript('/rman/libs/mam-upload/dist/libs/plupload/plupload.full.min.js');
        asyncLoadScript('/rman/libs/mam-upload/dist/libs/ksyun/ks3jssdk.min.js');
        asyncLoadScript('/rman/libs/mam-upload/dist/mam-upload-pure.js');
    }

    public setTaskInitSuccess(fun: (task: uploadTypes.IUnfinishedTaskRes) => void) {
        (this.taskInitSuccess as any) = fun;
    }
    public setTaskInitBefore(fun: (task: uploadTypes.IUnfinishedTaskRes) => void) {
        (this.taskInitBefore as any) = fun;
    }
    public setTaskUploadProgress(fun: (task: uploadTypes.IUnfinishedTaskRes) => void) {
        (this.taskUploadProgress as any) = fun;
    }
    public setTaskDeleteSuccess(fun: (task: uploadTypes.IUnfinishedTaskRes) => void) {
        (this.taskDeleteSuccess as any) = fun;
    }
    public setTaskUploadSuccess(fun: (task: uploadTypes.IUnfinishedTaskRes) => void) {
        (this.taskUploadSuccess as any) = fun;
    }

    public init() {
        if (!(window as any).mamUpload.web || !(window as any).mamUpload.vtube) {
            (window as any).mamUpload.init({
                configInst: _store.getState().uploadConfig,
                // loginToken:_store.getState().uploadConfig.loginToken
            });
            this.initEvents();
        }
    }

    public selectFile(options: uploadTypes.IUploadOptions) {
        this.init();
        this.params = $.extend({}, {
            module: uploadTypes.UploadModules.YUNPAN,
            taskType: uploadTypes.TaskTypes.GENERAL,
            transferType: uploadTypes.TransferTypes.WEB,
            targetFolder: '',
            relationContentType: uploadTypes.RelationContentType.NONE,
            relationContentId: ''
        }, options);
        if (this.params.transferType === uploadTypes.TransferTypes.WEB) {
            this.tf = (window as any).mamUpload.web;
        }
        else {
            this.tf = (window as any).mamUpload.vtube;
        }
        if (this.params.transferType === uploadTypes.TransferTypes.WEB) {
            // this.tf.openFileSelector((tasks: uploadTypes.ITask[]) => {
            //     if (this.params.writeMetadataBefore && this.params.writeMetadataBefore(this.getTasksByType(tasks), this.params) === false) {
            //         return;
            //     }
                //去掉必须先选择文件才能打开弹窗的步骤
                if (options.openModal) {
                    options.openModal(this.getTasksByType([]), this.params);
                }
            // }, false, options.uploadformat);
        } else {
            // 客户端操作，因此要先验证客户端是否存在
            this.tf.openFileSelector((tasks: uploadTypes.ITask[]) => {
                if (this.params.writeMetadataBefore && this.params.writeMetadataBefore(this.getTasksByType(tasks), this.params) === false) {
                    return;
                }
                if (options.openModal) {
                    options.openModal(this.getTasksByType(tasks), this.params);
                }
            }, () => {
                let vtubeDownloadPath: string = (window as any).nxt.config.vtubeDownloadPath;
                const modal = Modal.confirm(
                    {
                        title: '客户端异常',
                        content: '请确定已经正常开启客户端或重启客户端再重试。',
                        okText: '下载客户端',
                        onOk: () => {
                            message.info('正在下载客户端')
                            setTimeout(() => {
                                window.open(vtubeDownloadPath)
                            }, 1000);
                        }
                    }
                );
            });
        }

    }

    public createTask(tasks: uploadTypes.ITask | uploadTypes.ITask[]) {
        this.tf.createTask(tasks, this.params);
    }

    public get getFransfer() {
        return this.tf;
    }

    public checkExist(files: uploadTypes.IFile[], file: uploadTypes.IFile) {
        let isHas;
        if (this.params.transferType === uploadTypes.TransferTypes.WEB) {
            isHas = _.find(files, (item: uploadTypes.IFile) => {
                return item !== file && (item.file as File).name === (file.file as File).name &&
                    (item.file as File).size === (file.file as File).size &&
                    (item.file as File).lastModified === (file.file as File).lastModified;
            });
        } else { //vtube
            isHas = _.find(files, (item: uploadTypes.IFile) => {
                return item !== file
                    && (file.file as uploadTypes.IVtubeFile).FilePath === (item.file as uploadTypes.IVtubeFile).FilePath;
            });
        }
        if (isHas != null) {
            message.error('文件 ' + file.fileName + ' 已存在');
            return true;
        }
        return false;
    }

    public getTasksByType(tasks: uploadTypes.ITask[]): uploadTypes.ITask[] {
        if (this.params.taskType === uploadTypes.TaskTypes.GENERAL) {
            return tasks;
        }
        else if (this.params.taskType === uploadTypes.TaskTypes.PICTUREPACKAGEUPLOAD
            || this.params.taskType === uploadTypes.TaskTypes.GROUPUPLOAD) {
            return [(window as any).mamUpload.web.getGroupTask(tasks, this.params.taskType === uploadTypes.TaskTypes.PICTUREPACKAGEUPLOAD ?
                'picture' : 'video', this.params.targetFolder, this.params.taskType, this.params.transferType)];
        }
        return [];
    }

    private initEvents() {
        (window as any).mamUpload.web.off('task-init-success');
        (window as any).mamUpload.web.on('task-init-success', (e: any, task: uploadTypes.IUnfinishedTaskRes) => {
            if (this.taskInitSuccess) {
                this.taskInitSuccess(task);
            }
        });
        (window as any).mamUpload.web.on('task-init-error', (...arg: any) => {
            // message.error(arg[2].error.title)
            message.error('创建任务失败')
        });

        (window as any).mamUpload.web.off('task-init-before');
        (window as any).mamUpload.web.on('task-init-before', (e: any, task: uploadTypes.IUnfinishedTaskRes) => {
            if (this.taskInitBefore) {
                this.taskInitBefore(task);
            }
        });
        (window as any).mamUpload.web.off('task-upload-progress');
        (window as any).mamUpload.web.on('task-upload-progress', (e: any, task: uploadTypes.IUnfinishedTaskRes) => {
            if (this.taskUploadProgress) {
                this.taskUploadProgress(task);
            }
        });

        (window as any).mamUpload.web.off('task-delete-success');
        (window as any).mamUpload.web.on('task-delete-success', (e: any, task: uploadTypes.IUnfinishedTaskRes) => {
            if (this.taskDeleteSuccess) {
                this.taskDeleteSuccess(task);
            }
        });

        (window as any).mamUpload.web.off('task-upload-success');
        (window as any).mamUpload.web.on('task-upload-success', (e: any, task: uploadTypes.IUnfinishedTaskRes) => {
            if (this.taskUploadSuccess) {
                this.taskUploadSuccess(task);
            }
        });
    }
}

const uploader = new Uploader()

export default uploader