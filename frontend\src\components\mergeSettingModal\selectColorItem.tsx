import React, { FC, useEffect, useRef, useState } from 'react';
import { Button, Tooltip, Modal, Space } from 'antd';
import { IFormItem } from './selectMaterialItem';
import { SettingOutlined } from '@ant-design/icons';
import './style.less';
import {  useIntl } from 'umi';

const INIT_COLOR = '#69c0ff';
const SelectColorItem: FC<IFormItem & { videoKeyframe: string }> = ({
  value,
  onChange,
  videoKeyframe,
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [visible, setVisible] = useState(false);
  const [color, setColor] = useState(INIT_COLOR);
  const intl = useIntl();
  const [selected, setSelected] = useState(false);
  const handleSetting = () => {
    setVisible(true);
    inputRef.current?.click();
  };

  useEffect(() => {
    if (value) {
      setColor(value);
      setSelected(true);
    } else {
      setColor(INIT_COLOR);
      setSelected(false);
    }
  }, [value]);

  return (
    <>
      {selected ? (
        <Space className="color-show-wrapper" align="end">
          <div className="color-show-box">
            <Tooltip title={color}>
              <div style={{ backgroundColor: color }} />
            </Tooltip>
          </div>
          <a className={'close-btn'} onClick={() => setSelected(false)}>
            {intl.formatMessage({ id: '删除' })}
          </a>
        </Space>
      ) : (
        <Button onClick={handleSetting} size="small">
          <SettingOutlined />
          {intl.formatMessage({ id: '设置' })}
        </Button>
      )}
      <Modal
        visible={visible}
        onCancel={() => setVisible(false)}
        closable={false}
        footer={<Button onClick={() => setVisible(false)}>关闭</Button>}
        width={'70%'}
      >
        <div className="color-modal">
          <div className="preview-wrapper">
            <img src={videoKeyframe} alt="" />
          </div>
          <div className="color-input-wrapper">
            <Tooltip title={intl.formatMessage({ id: '取色器' })}>
              <input
                type="color"
                ref={inputRef}
                value={color}
                onChange={e => {
                  setColor(e.target.value);
                  setSelected(true);
                  onChange && onChange(e.target.value);
                }}
              />
            </Tooltip>
            <span>{intl.formatMessage({ id: '*点击左边取色器在图片中取色' })}</span>
          </div>
        </div>
      </Modal>
    </>
  );
};
export default SelectColorItem;
