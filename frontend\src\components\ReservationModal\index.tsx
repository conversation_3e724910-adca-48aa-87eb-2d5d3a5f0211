import React, { useState, useEffect, useRef } from 'react';
import { Modal, Button, Table, Form, DatePicker, Tabs, Popconfirm } from 'antd';
import contentListApis from '@/service/contentListApis';
import './index.less';
import { IPermission } from '@/models/permission';
import { useSelector, useIntl } from 'umi';
import CourseDetailModal from './components/courseDetailModal';
import rmanApis from '@/service/rman';
import HeaderForm from './components/headerForm'
import moment from 'moment';
interface CreateModalProps {
  modalVisible: boolean;
  modalClose: () => void;
  analysislist: any;
  className_?: string;
  pubOrPri: boolean;
  refresh?: (tag?: boolean, voice?: boolean, point?: boolean) => any;
}

const ReservationModal: React.FC<CreateModalProps> = props => {
  const { modalClose, modalVisible, className_, refresh } = props;
  const [treeData, setTreeData] = useState<any>([]);
  const [showplaceholder, setShowplaceholder] = useState<string>('');

  const [dataSource, setDataSource] = useState<any>([])
  const [current, setCurrent] = useState<number>(1) //当前页码
  const [totalPage, setTotalPage] = useState<number>(0) //素材总数
  const [semesterStartSate, setSemesterStartSate] = useState<any>('')
  const [dataSource1, setDataSource1] = useState<any>([])
  const [current1, setCurrent1] = useState<number>(1) //当前页码
  const [totalPage1, setTotalPage1] = useState<number>(0) //素材总数
  // 权限
  const [courseDetailModalVisible, setCourseDetailModalVisible] = useState<boolean>(false)
  const [errorModalVisible, setErrorModalVisible] = useState<boolean>(false)
  const [errorMessage, setErrorMessage] = useState<string>('')
  const tab1 = useRef<any>('')
  const tab2 = useRef<any>('')
  const [courseDetail, setCourseDetail] = useState<any>({})
  const [searchData1, setSearchData1] = useState<any>({})
  const [searchData2, setSearchData2] = useState<any>({})
  const [tabKey, setTabKey] = useState<string>('1')
  const [userInfo, setUserInfo] = useState<any>({})
  const [userroles, setUserroles] = useState<any>([]); //用户角色
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<
    React.Key[]>([]);
  useEffect(() => {
    fetchTree();
  }, [])
  useEffect(() => {
    const userInfo = (window as any).login_useInfo
    if(userInfo?.roles && userInfo?.roles.length ){
      setUserInfo(userInfo)
      setUserroles(userInfo.roles.map((item: any) => item.roleCode));
    }
  }, [(window as any).login_useInfo])
  const closemodal = () => {
    modalClose();
  };
  const columns = [
    {
      title: '课程名',
      dataIndex: 'course_name',
      key: 'course_name',
      align: 'center',
      width: '200px',
      ellipsis: true
    },
    {
      title: '教室',
      dataIndex: 'area',
      key: 'area',
      align: 'center',
      width: '300px',
    },
    {
      title: '课序号',
      dataIndex: 'serial_number',
      align: 'center',
      key: 'serial_number',
    },
    {
      title: '教师',
      dataIndex: 'teacher',
      key: 'teacher',
      align: 'center',
      width: '100px',
      ellipsis: true,
      render: (text: number) => {
        return <div>{text?.map(item => (
          <div>{item.name}</div>
        ))}</div>
      }
    },
    {
      title: '上课日期',
      dataIndex: 'date',
      key: 'date',
      align: 'center',
      width: '230px',
      ellipsis: true,
      render: (text: number, record) => {
        return <span>{record.teaching_week}  周{['一', '二', '三', '四', '五', '六', '日'][record.week_day - 1]}  第{record.section}节</span>
      }
    },
    {
      title: '操作',
      align: 'center',
      dataIndex: 'action',
      key: 'action',
      render: (text: number, record: any) => (
        <div style={{ cursor: 'pointer' }} onClick={() => {
          setCourseDetail(record)
          console.log(record, 'record');
          
          setCourseDetailModalVisible(true)
        }}>预约</div>
      )
    }
  ]
  const status = {
    0: '待入库',
    1: '取消预约',
    2: '已入库',
    3: '拉取资源失败', 
    4: '资源定时清除', 
    5: '拉取资源中'
  } as any
  const columns1 = [
    {
      title: '课程名',
      dataIndex: 'courseName',
      key: 'courseName',
      align: 'center',
      width: '115px',
      ellipsis: true,
    },
    {
      title: '课序号',
      dataIndex: 'courseNo',
      align: 'center',
      key: 'courseNo',
    },
    {
      title: '上课日期',
      dataIndex: 'classTime',
      align: 'center',
      key: 'classTime',
    },
    {
      title: '创建日期',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      width: '115px',
      ellipsis: true,
      render: (text: any) => {
        return <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>
      }
    },
    {
      title: '教室',
      dataIndex: 'academicBuilding',
      key: 'academicBuilding',
      align: 'center',
      width: '230px',
      ellipsis: true,
      render: (text: any, record: any) => {
        return <span>{`${record.campus}-${record.academicBuilding}-${record.classroomNo}`}</span>
      }
    },
    {
      title: '教师',
      dataIndex: 'teacherName',
      key: 'teacherName',
      align: 'center',
      width: '80px',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      width: '100px',
      ellipsis: true,
      render: (text: number) => {
        return <div>{status[text]}</div>
      }
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      render: (text: number, record: any) => (
        <div style={{ cursor: 'pointer' }}>
          {[0, 1, 3].includes(record.status) && <Popconfirm
            title="确定删除吗？"
            onConfirm={() => {
              contentListApis.reservationBatchDelete([record.id]).then(res => {
                reservationQuery(searchData2)
              })
            }}
            okText="确定"
            cancelText="取消"
          >
            <span style={{ marginRight: '10px' }}>删除</span>
          </Popconfirm>}
          {
            record.status === 0 && <Popconfirm
              title="确定取消预约吗？"
              onConfirm={() => {
                contentListApis.reservationBatchCancel([record.id]).then(res => {
                  reservationQuery(searchData2)
                })
              }}
              okText="确定"
              cancelText="取消"
            >
              取消预约
            </Popconfirm>
          }
          {
            record.errorMessage &&<span onClick={() => {
              setErrorMessage(record.errorMessage)
              setErrorModalVisible(true)
            }}>查看错误原因</span>
          }
        </div>
      )
    }
  ]
  if(!userroles.includes('r_resource_manager') && !userroles.includes('r_sys_manager') && !userroles.includes('admin_S1')){
    columns.splice(3, 1)
    columns1.splice(5, 1)
  }
  const getReservationList = (form: any) => {
    console.log(form, 'form');
    
    contentListApis.getReservationList({
      page: current,
      size: 10,
      key_word: form.courseName,
      teacher: !userroles.includes('r_resource_manager') && !userroles.includes('r_sys_manager') && !userroles.includes('admin_S1') ? userInfo.userCode : form.teacher,
      area_id: form.classroom
    }).then(res => {
      if (res.error_msg === 'Success') {
        setDataSource(res.extend_message.results)
        setSemesterStartSate(res.extend_message.semester_start_date)
        setTotalPage(res.extend_message.count)
      }
    })
  }
  const reservationQuery = (form) => {
    contentListApis.reservationQuery({
      page: current1,
      size: 10,
      startTime: form?.date?.[0].format('YYYY-MM-DD'),
      endTime: form?.date?.[1].format('YYYY-MM-DD'),
      courseName: form?.courseName,
      academicBuilding: form?.building,
      campus: form?.campus,
      classroomNo: form?.classroom,
      teacher: form?.teacher,
    }).then(res => {
      if (res?.status === 200) {
        setDataSource1(res.data.results)
        setTotalPage1(res.data.total)
      }
    })
  }
  // 目录树查询

  // 路径转译
  const getDirTeeStr = (dirTree?: string): string => {
    if (!dirTree) {
      return ''
    }
    if (dirTree.includes('global_sobey_defaultclass/public')) {
      return dirTree.replace('global_sobey_defaultclass/public', '').replace('/', '');
    } else if (dirTree.includes('global_sobey_defaultclass/private')) {
      let newdir = dirTree.replace(
        'global_sobey_defaultclass/private',
        '个人资源',
      );
      if (newdir.includes('/')) {
        let alldir = newdir.split('/');
        if (alldir.length >= 2) {
          newdir = newdir.replace('/' + alldir[1], '');
        }
      }
      return newdir;
    } else {
      return dirTree;
    }
  };
  const onSelect = (key: any, node: any) => {
    setSelectedKey(key);
    setShowplaceholder(getDirTeeStr(key));
  };
  const fetchTree = () => {
    rmanApis.gettreebylevel(2).then((res: any) => {
      if (res && res.data && res.success) {
        let data = res.data;
        let newData: any = [];
        data.map((item: any) => {
          if (item.name === '公共资源') {
            item.children?.forEach((item: any) => {
              newData.push({ ...item, layer: 1 });
            });
          } else {
            newData.push(item);
          }
        });
        newData = newData.filter(Boolean); //过滤空对象
        // if(!permission){
        //   removeTreeListItem(newData, '录播资源');
        // }
        // setPrivatePath(newData.filter((item:any)=>item.name == '个人资源')?.[0].path || newData[0].path);//默认选中个人资源
        // 过滤录播资源 默认不显示
        newData = newData.filter((item: any) => item.name != '录播资源');
        const rootData = newData.map((item: any) => {
          return {
            key: item.path,
            value: item.path,
            title: item.name,
            id: item.id,
            disabled: (item.name === '群组资源') ? true : false,
          };
        });
        setTreeData(rootData);
      }
    });
  };
  //动态加载子节点
  const onLoadChild = (node: any, isRoot: boolean = false) => {
    const { key, children, code, title } = node;
    return new Promise(async (resolve) => {
      if (key === 'add') {
        resolve(null);
        return;
      }
      if (children) {
        resolve(null);
        return;
      }
      function updateTreeData(list: any, key: React.Key, children: any): any {
        return list.map((node: any) => {
          if (node.key === key) {
            return {
              ...node,
              children,
            };
          } else if (node.children) {
            return {
              ...node,
              children: updateTreeData(node.children, key, children),
            };
          }
          return node;
        });
      }
      const res: any = await rmanApis.loadChild(key, false);
      if (res && res.data && res.success) {
        let treeList = res.data;
        treeList = treeList.filter((item: any) => {
          return item.name !== '录播资源';
        });
        setTreeData((origin: any) =>
          updateTreeData(
            origin,
            key,
            treeList.map((item: any) => {
              return {
                key: item.path,
                title: item.name,
                id: item.contentId,
                value: item.path,
              };
            }),
          ),
        );
        resolve(null);
      }
    });
  };

  const onSelectChange = (
    newSelectedRowKeys: any,
    selectedRows: any,
  ) => {
    setSelectedRowKeys(newSelectedRowKeys)
    setSelectedRows(selectedRows)
  };
  useEffect(() => {
    userInfo.userCode && getReservationList(searchData1)
  }, [current, searchData1, userInfo])
  useEffect(() => {
    reservationQuery(searchData2)
  }, [current1, searchData2])
  return (
    <Modal
      className='reservationModal'
      destroyOnClose={true}
      title=''
      open={modalVisible}
      onCancel={closemodal}
      footer={null}
      width={1200}
    >
      <Tabs onTabClick={(key) => {
        if (key === 'item-2') {
          setTabKey(key)
          reservationQuery(searchData2)
        }
      }}>
        <Tabs.TabPane tab="预约入库" key="item-1">
          <HeaderForm tabKey={tabKey} userroles={userroles} setSearchData={(data) => {
            setSearchData1(data)
            setCurrent(1)
          }} />
          {/* <div className="top">
            <div className="left">
              <Button onClick={reserva}>
                预约入库
              </Button>
            </div>
            <div className="right">
              <span>{intl.formatMessage({ id: '入库至' })}</span>
              <TreeSelect
                // placeholder={showplaceholder}
                treeData={treeData}
                value={showplaceholder}
                onSelect={onSelect}
                loadData={onLoadChild}
              />
            </div>
          </div> */}
          <Table
            dataSource={dataSource}
            columns={columns}
            pagination={{
              current,
              showSizeChanger: false,
              defaultPageSize: 10,
              total: totalPage,
              onChange: (page) => setCurrent(page),
            }}
          />
        </Tabs.TabPane>
        <Tabs.TabPane tab="我的预约" key="item-2">
          <HeaderForm tabKey={tabKey} userroles={userroles} setSearchData={(data) => {
            setSearchData2(data)
            setCurrent1(1)
          }} />
          <div className="top">
            <Popconfirm
              title="确定删除吗？"
              onConfirm={() => {
                contentListApis.reservationBatchDelete(selectedRowKeys).then(res => {
                  reservationQuery(searchData2)
                })
              }}
              okText="确定"
              cancelText="取消"
            >
              <Button disabled={selectedRowKeys.length === 0}>
                删除
              </Button>
            </Popconfirm>
            <Popconfirm
              title="确定取消预约吗？"
              onConfirm={() => {
                contentListApis.reservationBatchCancel(selectedRowKeys).then(res => {
                  reservationQuery(searchData2)
                })
              }}
              okText="确定"
              cancelText="取消"
            >
              <Button disabled={selectedRowKeys.length === 0 || selectedRows.some(item => item.status !== 0)}  style={{ marginLeft: '10px' }}>
                取消预约
              </Button>
            </Popconfirm>
          </div>
          <Table
            rowSelection={{
              selectedRowKeys,
              onChange: onSelectChange,
            }}
            dataSource={dataSource1}
            rowKey={(record) => record.id}
            columns={columns1}
            pagination={{
              current: current1,
              showSizeChanger: false,
              defaultPageSize: 10,
              total: totalPage1,
              onChange: (page) => setCurrent1(page),
            }}
          />
        </Tabs.TabPane>
      </Tabs>
      {/* <div className="singleDisplay">
          <div className="head"></div>
          <div className="body">
          </div>
        </div> */}

      {<CourseDetailModal
        courseDetail={courseDetail}
        semesterStartSate={semesterStartSate}
        modalVisible={courseDetailModalVisible}
        modalClose={() => setCourseDetailModalVisible(false)}
      />}
      <Modal
        title='错误原因'
        visible={errorModalVisible}
        footer={null}
        onCancel={() => setErrorModalVisible(false)}>
        <div>{errorMessage}</div>
      </Modal>
    </Modal>
  );
};

export default ReservationModal;
