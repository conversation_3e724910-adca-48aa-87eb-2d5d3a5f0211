.batchDownload{
  .ant-modal-body{
    .ant-table-container{
      border: 0 !important;
      table{
        border: 0 !important;
      }
      .ant-table-thead{
        .ant-table-cell{
          padding: 10px 8px;
          border: 0 !important;
        }
      }
      .ant-table-tbody{
        tr td{
          border: 0 !important;
        }
      }
    }
  }
  .download_modal {
    .downloadbox {
      display: flex;
  
      .ant-checkbox-wrapper {
        display: flex;
      }
  
      .downloadname {
        flex: 1;
        padding-left: 10px;
        padding-right: 15px;
        overflow: auto;
      }
  
      .downloadbottom {
        display: flex;
        align-items: center;
        line-height: 22px;
        // float: right;
      }
    }
    .checkbox_span{
      margin-left: 10px;
    }
  }
  .selectCounts{
    display: flex;
    justify-content: center;
    margin-top: 20px;
    height: 20px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #4A4F64;
    line-height: 20px;
  }
  .download-dis {
    display: none;
  }
  .ant-modal-footer{
    border: 0 !important;
    display: flex;
    justify-content: center;
  }
}

.downLoadBottomModal{
  top:400px !important;
  .checkbox_span{
    margin-left: 2px;
  }
  .ant-table-thead{
    tr th{
      >div{
        white-space: nowrap;
      }
    }
  }
}
