import React, { FC, useEffect, useState } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Button,
  Pagination,
  Checkbox,
  Space,
  message,
} from 'antd';
import { SearchTree } from '@/components';
import searchTreeApis from '@/service/searchTreeApis';
import searchTypes from '@/types/searchTypes';
import { options } from '@/components/search';
import contentListApis from '@/service/contentListApis';
import { MaterialList } from '@/pages/contentlibrary/contentList/type';
import ContentItem from '@/components/ContentItem';
import {
  useIntl
} from 'umi';
import './materialModal.less';
interface IMaterialModal {
  visible: boolean;
  onCancel: () => void;
  onOk: (item: MaterialList) => void;
}

const PAGE_SIZE = 9;

const MaterialModal: FC<IMaterialModal> = ({ visible, onCancel, onOk }) => {
  const [folderId, setFolderId] = useState<any>('');
  const [folderPath, setFolderPath] = useState<any>('');
  const intl = useIntl();
  const [treeData, setTreeData] = useState<searchTypes.IFolder | any>([]);
  const [folderList, setFolderList] = useState<MaterialList[]>([]);
  const [current, setCurrent] = useState(1);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<any>({
    type: '',
  });
  const [checkedList, setCheckedList] = useState<any[]>([]);
  const [form] = Form.useForm();

  const handleSelect = (id: string,path:string) => {
    setFolderId(id);
    setFolderPath(path);
  };

  const fetchTreeData = () => {
    searchTreeApis.tree().then(res => {
      if (res && res.data && res.success) {
        let newData: any = [];
        res.data.map((item:any)=>{
          if(item.name ==='公共资源'){
            item.children?.forEach((item: any) => {
              newData.push({ ...item, layer: 1 })
            })
          }else{
            newData.push(item);
          }
        })
        newData = newData.filter(Boolean);//过滤空对象
        console.log('newData', newData);
        if (folderId === '') {
          setFolderId(newData[0].id);
          setFolderPath(newData[0].path);
        }
        setTreeData(newData);
      }
    });
  };

  const searchFolderList = () => {
    const conditions = [];
    if (query.type) {
      conditions.push({
        field: 'type_',
        searchRelation: 0,
        value: [query.type],
      });
    }
    if (query.date) {
      conditions.push(
        {
          field: 'createDate_',
          searchRelation: 4,
          value: [query.date[0].format('YYYY-MM-DD') + ' 00:00:00'],
        },
        {
          field: 'createDate_',
          searchRelation: 6,
          value: [query.date[1].format('YYYY-MM-DD') + ' 23:59:59'],
        },
      );
    }

    const params = {
      folderId: folderId,
      folderPath:folderPath,
      sortFields: query.keyword ? []: [
        {
          field: 'createDate_',
          isDesc: true,
        },
      ],
      pageIndex: current,
      pageSize: PAGE_SIZE,
      keyword: query.keyword ? [query.keyword] : undefined,
      conditions,
    };
    if (params.keyword || params.conditions.length > 0) {
      contentListApis.searchall(params).then(res => {
        if (res?.data && res.success) {
          setTotal(res.data.recordTotal);
          setFolderList(res.data.data);
        }
      });
    } else {
      contentListApis.searchfolder(params).then(res => {
        if (res?.data && res.success) {
          setTotal(res.data.recordTotal);
          setFolderList(res.data.data);
        }
      });
    }
  };

  useEffect(() => {
    if (folderId !== '') {
      searchFolderList();
    } else {
      fetchTreeData();
    }
  }, [folderId, query, current]);

  const handleOk = () => {
    if (
      checkedList.length !== 1 ||
      checkedList[0].type_ !== 'biz_sobey_video'
    ) {
      message.error(intl.formatMessage({ id: '请选择一个视频素材' }));
      return;
    }
    onOk(checkedList[0]);
  };

  return (
    <Modal
      visible={visible}
      width={1100}
      onCancel={onCancel}
      footer={
        <Space>
          <Button onClick={() => setCheckedList([])}>{intl.formatMessage({ id: '清空选中' })}</Button>
          <Button type="primary" onClick={handleOk}>
            {intl.formatMessage({ id: '确定' })}
          </Button>
        </Space>
      }
    >
      <div className="material-modal-content">
        <div className="directory">
          <SearchTree
            onSelect={handleSelect}
            basictree={treeData}
            id={folderId}
            recycle={true}
          />
        </div>
        <div className="content">
          <div className="search-wrapper">
            <Form
              name="searchForm"
              form={form}
              onFinish={values =>
                setQuery({
                  ...values,
                })
              }
              initialValues={query}
              layout="inline"
            >
              <Form.Item>
                <Input.Group compact>
                  <Form.Item noStyle name="type">
                    <Select className="type-item">
                      {options.map((item, index) => (
                        <Select.Option
                          value={item.value || ''}
                          key={'type' + index}
                        >
                          {item.label}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                  <Form.Item name="keyword" noStyle>
                    <Input
                      placeholder={intl.formatMessage({ id: '请输入搜索内容' })}
                      allowClear
                      autoComplete={'off'}
                      className="keyword-item"
                      onPressEnter={() => form.submit()}
                    />
                  </Form.Item>
                </Input.Group>
              </Form.Item>
              <Form.Item
                name="date"
                labelCol={{
                  span: 4,
                  offset: 0,
                }}
              >
                <DatePicker.RangePicker />
              </Form.Item>
              <Form.Item>
                <Button htmlType="submit" type="primary">
                  {intl.formatMessage({ id: '搜索' })}
                </Button>
              </Form.Item>
            </Form>
          </div>
          <Checkbox.Group
            className="folder-list"
            value={checkedList}
            onChange={checkedValue => setCheckedList(checkedValue)}
          >
            {folderList.map(item => (
              <ContentItem
                key={item.contentId_}
                columns={[]}
                modal={true}
                detail={item}
                recycleBin={false}
                hideBtn={true}
                goDetail={() =>{
                  if(item.type_ === 'folder'){
                    setFolderId(item.contentId_);
                    setFolderPath(item.tree_[0]);
                  }
                }
              }
              />
            ))}
          </Checkbox.Group>
          <Pagination
            className="pagination-wrapper"
            current={current}
            pageSize={PAGE_SIZE}
            total={total}
            size="small"
            showQuickJumper
            onChange={page => setCurrent(page)}
          />
        </div>
      </div>
    </Modal>
  );
};
export default MaterialModal;
