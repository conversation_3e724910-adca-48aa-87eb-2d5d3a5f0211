﻿var mamUpload;
if (typeof define === "function" && define.amd) {
    mamUpload = require('mam-upload');
} else {
    mamUpload = window.mamUpload;
}

var uploadModalgeneralCtrl = ["$scope", "$uibModalInstance", "$stateParams", "$state", "$http", "opts", "mam2MetadataService",
    function ($scope, $uibModalInstance, $stateParams, $state, $http, opts, mam2MetadataService) {
        /*
            可添加附加信息extraField:[{fileName:"",value:""}]
        */
        var transfer = mamUpload.uploader.transfers[opts.uploadParams.transferType];

        //界面展示用
        $scope.tasks = [];
        $scope.limitFiles = [];
        //按类型获取的元数据缓存
        var metadatas = {};
        //素材类型
        $scope.entityAllTypes = [];

        //是否正在批量编辑
        $scope.editing = false;

        $scope.current = {};
        $scope.currentMetadataBak = [];
        $scope.nameField = {};

        $scope.ok = function () {
            var isAllPass = true;
            _.forEach($scope.tasks, function (item, i) {
                var res = mam2MetadataService.validate(item.metadata.field);
                //var res = item.metadata.validate();
                if (!res.success) {
                    isAllPass = false;
                    item.metadata.isPass = false;
                    if (item.metadata.validate != null)
                        item.metadata.validate();
                } else {
                    var n = _.find(item.metadata.field, function (o) {
                        return o.fieldName === "name_";
                    });
                    if (n != null) {
                        $scope.tasks[i].metadata.name = n.value;
                    }
                }
            });
            if (isAllPass) {
                $uibModalInstance.close(_.filter($scope.tasks));
            } else {
                mam.message.error('元数据验证未通过，请检查修改后再试。');
            }

        }

        $scope.close = function () {
            $uibModalInstance.close([]);
        }

        //类型过滤
        var entityType = {
            add: function (type) {
                if (_.find($scope.entityAllTypes, {
                    code: type
                }) == null) {
                    var item = _.find(nxt.config.entityTypes, {
                        code: type
                    });
                    item.selected = true;
                    $scope.entityAllTypes.push(angular.copy(item));
                }
            },
            del: function (type) {
                _.remove($scope.entityAllTypes, function (o) {
                    return o.code === type
                });
            },
            sync: function () {
                var temp = angular.copy($scope.entityAllTypes);
                _.forEach(temp, function (item) {
                    if (_.find($scope.tasks, {
                        entityType: item.code
                    }) == null)
                        entityType.del(item.code);
                });
            }
        };

        //删除待上传的素材
        $scope.delete = function (item) {
            var isChangeCurrent = false;
            if (item != undefined) {
                if (item == $scope.current) {
                    isChangeCurrent = true;
                }
                $scope.tasks.remove(item);
                if (_.find($scope.tasks, {
                    entityType: item.entityType
                }) == undefined) {
                    entityType.del(item.entityType);

                }
            } else {
                var items = $scope.getSelectedItems();
                if (items != undefined && items.length > 0) {
                    _.forEach(items, function (o) {
                        if (o == $scope.current)
                            isChangeCurrent = true;
                        $scope.tasks.remove(o);
                    });
                }
                entityType.sync();
            }
            if (isChangeCurrent) {
                if ($scope.tasks.length > 0)
                    $scope.current = $scope.tasks[0];
                else
                    $scope.changeCurrent();
            }
        }

        $scope.resetMetadata = function () {
            if ($scope.editing) {
                $scope.editMetadatas();
            } else {
                $scope.current.metadata = angular.copy($scope.currentMetadataBak);
            }
        }

        function clearSelectedItem() {
            _.forEach($scope.filterTask(), function (item) {
                item.selected = false;
            });
        }

        $scope.exitBulkEdit = function () {
            $scope.changeCurrent($scope.getSelectedItems()[0]);
            clearSelectedItem();
            $scope.editing = false;
        }
        var validataData;
        //获取验证
        $scope.getValidateFun = function (func) {
            validataData = func;
        }
        //
        function validate() {
            var res = validataData();
            if (!res.success) {
                var r = mam2MetadataService.validate($scope.current.metadata.field);
                if (!r.result) {
                    $scope.current.metadata.isReady = false;
                    $scope.current.metadata.isPass = false;
                }
            }
            //$scope.current.metadata.field = res.entityInfo;
            //var n = _.find($scope.current.metadata.field, function (o) { return o.fieldName === "name_"; });
            //if (n != null && n.value != $scope.current.metadata.name)
            //    $scope.current.metadata.name = n.value;
            if (res.success) {
                $scope.current.metadata.isPass = true;
                $scope.current.metadata.isReady = true;
                $scope.currentMetadataBak = angular.copy($scope.current.metadata);
            }
            //mam.message.ok('保存成功');
        }


        $scope.$watch("current.metadata.field", function (newValue, oldValue, $scope) {
            if (newValue != null && newValue !== oldValue) {
                if ($scope.current.metadata != null) {
                    $scope.nameField = _.find($scope.current.metadata.field, function (o) {
                        return o.fieldName === "name_";
                    });
                }
            }
        });
        $scope.$watch("nameField.value", function (newValue, oldValue, $scope) {
            if (newValue != null && newValue !== oldValue) {
                $scope.current.metadata.name = newValue;
                if (!$scope.current.metadata.isPass) {
                    validate();
                }
            }
        });

        $scope.changeCurrent = function (item) {
            if (item == undefined) {
                if ($scope.tasks != undefined && $scope.tasks.length > 0) {
                    item = $scope.tasks[0];
                } else {
                    item = null;
                }
            }
            validate();
            $scope.current = item;
            if (item != null) {
                $scope.currentMetadataBak = angular.copy($scope.current.metadata);
                if (item.metadata.isPass === false && item.metadata.validate != null) {
                    //setTimeout(function() {
                    //    $scope.current.metadata.validate();
                    //}, 1000);
                }
            }
        }

        $scope.saveTaskMetadata = function () {
            if ($scope.editing) {
                validate();
                if (!$scope.current.metadata.isPass) return;
                var items = $scope.getSelectedItems();
                _.forEach(items, function (o, index) {
                    var n = _.find(o.metadata.field, function (i) {
                        return i.fieldName === "name_";
                    });
                    o.metadata.field = angular.copy($scope.current.metadata.field);
                    var name = _.find(o.metadata.field, function (i) {
                        return i.fieldName === "name_";
                    });

                    var newName = '';
                    for (let i = 0; i < $scope.current.metadata.field.length; i++) {
                        if ($scope.current.metadata.field[i].fieldName === 'name_') {
                            newName = $scope.current.metadata.field[i].value
                        }
                    }
                    if (name != null) {
                        if (nxt.config.uploadBatchEnable && $scope.rightBatchChange.openOrder) {
                            if ($scope.rightBatchChange.isOrder) {
                                name.value = newName + "_" + (Array(5).join(0) + (index + Number($scope.rightBatchChange.orderNumber))).slice(-5);
                                name.isReadOnly = n.isReadOnly;
                            } else {
                                if (n.value.lastIndexOf('_') > -1) {
                                    var nu = n.value.substring(n.value.lastIndexOf('_'), n.value.length)
                                    name.value = newName + nu
                                } else {
                                    name.value = n.value
                                }
                                name.isReadOnly = n.isReadOnly;
                            }
                        } else {
                            name.value = n.value;
                            name.isReadOnly = n.isReadOnly;
                        }
                    }
                    o.metadata.name = name.value
                    //isReady 已编目
                });
                $scope.exitBulkEdit();
            } else {
                validate();
            }
        }

        $scope.isDisabled = function (type) {
            var items = $scope.getSelectedItems();
            switch (type) {
                case "edit":
                    return items == undefined || items.length === 0 || $scope.editing;
                //|| _.find(items, function (o) { return o.entityType != items[0].entityType }) != undefined;
                case "del":
                    return items == undefined || items.length === 0;
                default:
                    return true;
            }
        }

        $scope.editMetadatas = function () {
            if ($scope.isDisabled("edit")) return;
            if (_.find($scope.getSelectedItems(), function (o) {
                return o.entityType != $scope.getSelectedItems()[0].entityType
            }) != undefined) {
                mam.message.error('不同的素材类型不能进行批量编辑');
                return;
            }

            if ($scope.getSelectedItems().length === 1) {
                $scope.changeCurrent($scope.getSelectedItems()[0]);
                return;
            }

            $scope.editing = true;
            var first = $scope.getSelectedItems()[0];
            var bulkMeta = {
                editing: true,
                metadata: {
                    field: []
                }
            };
            var defaultMetadata = angular.copy(metadatas[first.entityType]);
            bulkMeta.metadata.field = angular.copy(first.metadata.field);
            var n = _.find(bulkMeta.metadata.field, function (o) {
                return o.fieldName === "name_";
            });

            if (n != null) {
                if (nxt.config.uploadBatchEnable && $scope.rightBatchChange.openOrder) {
                    n.value = first.metadata.name
                    n.isReadOnly = false;
                } else {
                    n.value = nxt.config.uploadBatchEnable ? first.metadata.name : "标题不能进行批量编辑，请逐个编辑";
                    n.isReadOnly = true;
                }
            }

            _.forEach(bulkMeta.metadata.field, function (item) {
                _.forEach($scope.getSelectedItems(), function (j) {
                    var temp = _.find(j.metadata.field, function (k) {
                        return k.fieldName === item.fieldName && k.value != item.value;
                    });
                    if (temp != null && temp.fieldName !== "name_") {
                        var d = _.find(defaultMetadata, {
                            fieldName: item.fieldName
                        });
                        if (d != null && d.value != null)
                            item.value = d.value;
                        else
                            delete item.value;
                    }
                });
            });

            $scope.changeCurrent(bulkMeta);
        }

        $scope.filterTask = function () {
            var types = _.filter($scope.entityAllTypes, {
                selected: true
            });
            return _.filter($scope.tasks, function (task) {
                return _.find(types, {
                    code: task.entityType
                }) != null;
            });
        }

        function filterExt(ext) {
            if (nxt.config.uploadConfigExtensionLimit.type === 3) {
                if (ext === _.find(nxt.config.uploadConfigExtensionLimit.extensions, function (o) {
                    return o === ext
                }))
                    return false;
            } else if (nxt.config.uploadConfigExtensionLimit.type === 2) {
                if (ext !== _.find(nxt.config.uploadConfigExtensionLimit.extensions, function (o) {
                    return o === ext
                }))
                    return false;
            }
            return true;
        };

        var fileInfo = {
            size: "filesize",
            ext: "fileext"
        }

        $scope.onProgramformChange = function (value, oldValue) {
            var newMetadata = metadatas[$scope.current.entityType][value];
            var programformField = _.find(newMetadata, {
                fieldName: 'programform'
            });
            if (programformField != null) {
                programformField.value = '["' + value + '"]';
            }
            _.find(newMetadata, {
                fieldName: 'name_'
            }).value = _.find($scope.current.metadata.field, {
                fieldName: 'name_'
            }).value;
            _.find(newMetadata, {
                fieldName: 'createUser_'
            }).value = _.find($scope.current.metadata.field, {
                fieldName: 'createUser_'
            }).value;
            _.find(newMetadata, {
                fieldName: 'createDate_'
            }).value = _.find($scope.current.metadata.field, {
                fieldName: 'createDate_'
            }).value;
            $scope.current.metadata.field = newMetadata;
            autoFillUploadFieldUserInfoSetting($scope.current.metadata.field);
        }

        function autoFillUploadFieldUserInfoSetting(fields) {
            if (nxt.config.autoFillUploadFieldUserInfoSetting)//根据配置自动填写元数据
            {
                var autoSetting = nxt.config.autoFillUploadFieldUserInfoSetting;
                _.forEach(autoSetting, function (autoField) {
                    var field = _.find(fields, function (o) {
                        return o.fieldName === autoField.fieldName && o.fieldPath === autoField.fieldPath;
                    });
                    if (field != null) {
                        field.value = autoField.value.replace(/\$\{(.*?)\}/g, function (outer, content) {
                            var func = new Function(undefined, 'return ' + content);
                            return func.apply(window, []);
                        }) || '';
                    }
                })
            }
        }

        //绑定素材的元数据
        function assembleMetadata(item) {
            var metadata = metadatas[item.entityType];
            if (item.entityType === 'video' && !(metadata instanceof Array)) {
                metadata = metadata.data;
            }
            item.metadata.field = angular.copy(metadata);

            autoFillUploadFieldUserInfoSetting(item.metadata.field);

            var now = new Date();
            var specialFields = { //特殊字段
                'name_': item.metadata.name,
                'filesize': !item.file ? item.files[0].fileSize : item.file['size'],
                'fileext': !item.file ? ('.' + mam.utils.getExtension(item.files[0].fileName)) : item.file['ext'],
                'createUser_': nxt.user.current.nickName,
                'createDate_': now.format('yyyy-MM-dd hh:mm:ss'),
            }
            for (var sf in specialFields) {
                var field = _.find(item.metadata.field, function (o) {
                    return o.fieldName === sf;
                });
                if (field != null && !field.value) {
                    field.value = specialFields[sf];
                }
            }

            if (opts.extraField != undefined && opts.extraField.length > 0) { //继承附加的field
                _.forEach(opts.extraField, function (e) {
                    var temp = _.find(item.metadata.field, function (o) {
                        return o.fieldName === e.fieldName;
                    });
                    if (temp != null && e.value != null)
                        temp.value = e.value;
                });
            }
        }

        //请求各种类型对应的元数据
        function getMetadataField(types) {
            let apiVersion = getCookie('apiVersion');
            var url = '/upload/get-all-fields-by-source'
            if (apiVersion && apiVersion === 'mamcore2.3'){
                url = '/scntm/v1/old/upload/get-all-fields-by-source'
            }
            $http.post("~" + url, {
                source: opts.uploadParams.module
            }).then(function (res) {
                for (var t in res.data) {
                    metadatas[t] = res.data[t]; //为类型赋值元数据
                }
                _.forEach($scope.tasks, function (item) {
                    if (item.isNewAdd)
                        assembleMetadata(item);
                    delete item.isNewAdd;
                });
            });
        };

        function appendFiles(files) {
            var types = [];
            _.forEach(files, function (item, i) {
                if (!filterExt(item.metadata.ext)) {
                    $scope.limitFiles.push(item.metadata.ext);
                } else {
                    //CCTV 默认选中
                    item.selected = true;
                    $scope.tasks.push(item);
                    entityType.add(item.entityType);
                    if (!metadatas.hasOwnProperty(item.entityType) || metadatas[item.entityType].length === 0) {
                        item.isNewAdd = true;
                        if (!_.includes(types, item.entityType)) {
                            types.push(item.entityType);
                        }
                    } else {
                        assembleMetadata(item);
                    }
                    if (i === 0) {
                        $scope.current = $scope.tasks[$scope.tasks.length - 1];
                        $scope.currentMetadataBak = angular.copy($scope.current.metadata);
                    }
                }
                $scope.$applyAsync();
            });
            if ($scope.limitFiles.length > 0) {
                let limitTip = $scope.limitFiles.join() + ' 类型文件已被限制上传！';
                const limitConfig = nxt.config.uploadConfigExtensionLimit;
                const limitExt = (limitConfig.extensions && limitConfig.extensions.length) ?
                    limitConfig.extensions.join() :
                    $scope.limitFiles.join();
                if (limitConfig.type === 3) {
                    // 黑名单
                    limitTip = `目前不允许上传${limitExt}格式文件`.l('upload.notAllowExt', { limitExt: limitExt });
                } else if (limitConfig.type === 2) {
                    // 白名单
                    limitTip = `目前只允许上传${limitExt}格式文件`.l('upload.onlyAllowExt', { limitExt: limitExt });
                }
                mam.message.error(limitTip);
            }
            if (types.length !== 0) {
                getMetadataField(types);
            }
        }

        $scope.addFiles = function () {
            transfer.openFileSelector(function (files) {
                appendFiles(files);
            });
        }

        $scope.getSelectedItems = function () {
            return _.filter($scope.filterTask(), {
                selected: true
            });
        }

        function getCookie(cname){
            var name = cname + "=";
            var ca = document.cookie.split(';');
            for(var i=0; i<ca.length; i++) 
            {
                var c = ca[i].trim();
                if (c.indexOf(name)==0) return c.substring(name.length,c.length);
            }
            return "";
        }

        function RightBatchChange() {
            this.openOrder = false;
            this.openOrderChange = (openOrder, event) => {
                this.openOrder = openOrder
                $scope.editing = false
                $scope.editMetadatas()
            }
            this.isOrder = true;
            this.isOrderChange = (isOrder, event) => {
                this.isOrder = isOrder
            }
            this.orderNumber = '1';
            this.fliterOrderNumber = (event) => {
                this.orderNumber = event.target.value
                if (event.target.value == '' || Number(this.orderNumber) + $scope.getSelectedItems().filter((item) => item.selected).length > 100000 || this.orderNumber.replace(/\b(0+)/gi, "") == '') {
                    this.orderNumber = '1'
                }
                this.orderNumber = this.orderNumber.replace(/\b(0+)/gi, "").replace(/[^\d]/g, '')
                $(event.target).val(this.orderNumber);
            }
        }

        $scope.rightBatchChange = new RightBatchChange();
        $scope.mmfRightFilter = ['name_']

        function init() {
            appendFiles(opts.files);
        }

        init();
    }
];
angular.module('mam-upload').registerController("uploadModalgeneralCtrl", uploadModalgeneralCtrl);