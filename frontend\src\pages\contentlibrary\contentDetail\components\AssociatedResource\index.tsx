import React, { FC, useState, useEffect, useRef } from "react";
import './index.less';
import entityApis from '@/service/entityApis';
import MapX6 from "../mapX6";
import { IEntity } from '@/types/entityTypes';
import Entity from '@/components/entity/entity';
import { createNodeData } from '../mapX6/util';
import _ from 'lodash';
import { useIntl } from 'umi';
import {
  IconFont
} from '@/components';
import { Drawer, Input, Select, Timeline, message, Button } from 'antd';
import { getType } from '@/utils';
const AssociatedResource: FC<any> = ({ contentId, title, shareFlag_, shareLink, associatedResource,associateResource }) => {
  const [entity, setEntity] = useState<IEntity>();
  const [isTransCode, setIsTransCode] = useState<boolean>(true);
  const intl = useIntl();

  const [mapInfo, setMapInfo] = useState<any>(null);
  const [current, setCurrent] = useState<any>(['all']);
  const [videoName, setVideoName] = useState<string>('');
  const btnText = [{key: 'all', text: intl.formatMessage({ id: '全部' })}, 
                  {key: 'entityData.copyFrom_', text: intl.formatMessage({ id: '来源素材' })},
                  {key: 'metadatas.model_sobey_keywords.worrds', text: intl.formatMessage({ id: '关键词' })}, 
                  {key: 'metadatas.model_sobey_cata_sequencemeta.title', text: intl.formatMessage({ id: '知识点' })}, 
                  {key: 'metadatas.model_sobey_smart_face_.person_name', text: intl.formatMessage({ id: '人物' })}, 
                  {key: 'metadatas.model_sobey_sensitive_word.sensitive_word', text: intl.formatMessage({ id: '敏感词' })}]
  useEffect(() => {
    associatedResource.length && setMapInfo(createNodeData(title, associatedResource))
  }, [JSON.stringify(associatedResource)])
  const chooseCurrent = (item: string) => {
    if(item === 'all'){
      setCurrent(['all'])
      associateResource({contentId, highlightFields: ''})
    }
    else{
      let copyCurrent = [...current]
      if(copyCurrent.includes('all')){
        copyCurrent = [...current].filter(item_ => item_ !== 'all')
      }
      if(copyCurrent.includes(item)){
          let index = copyCurrent.findIndex(item_ => item_ ===item)
          copyCurrent.splice(index, 1)
      }
      else{
          copyCurrent.push(item)
      }
      associateResource({contentId, highlightFields: copyCurrent.join(',')})
      setCurrent(copyCurrent)

    }
}
  const isBasicFormat = (src: string) => {
    return (
      src?.indexOf('.html') > -1 ||
      src?.indexOf('.htm') > -1 ||
      src?.indexOf('.jpg') > -1 ||
      src?.indexOf('.mp4') > -1 ||
      src?.indexOf('.mp3') > -1 ||
      src?.indexOf('.pdf') > -1
    );
  };
  //获取网络带宽
  const getNetworkSpeed = () => {
    // 判断浏览器是否支持navigator.connection属性
    if ((navigator as any)?.connection) {
      // 获取当前网络连接信息
      var connection = (navigator as any)?.connection;
      // 如果浏览器支持downlink属性，则输出当前网络的下行带宽
      if (connection.downlink) {
        return connection.downlink
      } else {
        return
      }
    } else {
      return 0
    }
  }
  const nodeClick = (node) => {
    if(node.data.type === 1) return
    let contentId = node.data.contentId;
    setVideoName(node.data.label)
    let temp = contentId;
    if (shareFlag_ === 'share') {
      temp = contentId + '?isSysAuth=true&link=' + shareLink
    }
    entityApis.getEntity(temp, true, shareFlag_ === 'share').then((res: any) => {

      let dataType = res.data.type;
      let path = _.find(res.data.fileGroups, (f: any) => {
        return f.typeCode === (dataType === 'biz_sobey_document' ? 'sourcefile' : 'previewfile');
      });
      if (!path) {
        path = _.find(res.data.fileGroups, (f: any) => {
          return f.typeCode === 'sourcefile';
        });
        if (
          dataType === 'biz_sobey_picture' ||
          dataType === 'biz_sobey_audio' ||
          dataType === 'biz_sobey_document' ||
          dataType === 'biz_sobey_video'
        ) {
          path?.fileItems[0] &&
            setIsTransCode(isBasicFormat(path?.fileItems[0].displayPath));
        }
      }
      //自动适应清晰度
      let autoPathObj = path ? path.fileItems[0] : {}
      if (path && path.fileItems && path.fileItems.length) {
        let speed = getNetworkSpeed()
        if (speed < 1) {
          autoPathObj = path.fileItems.find((p: any) => p.bitRate <= 512 * 1000) || path.fileItems[0]
        } else if (speed < 3) {
          autoPathObj = path.fileItems.find((p: any) => p.bitRate <= 1024 * 1000) || path.fileItems[0]
        } else if (speed < 5) {
          autoPathObj = path.fileItems.find((p: any) => p.bitRate <= 2048 * 1000) || path.fileItems[0]
        } else {
          autoPathObj = path.fileItems[path.fileItems.length - 1]
        }
      }
      let autoPath = autoPathObj.displayPath
      setEntity({
        path: autoPath,
        keyframes:
          res.data.keyframe && res.data.keyframe.filePath
            ? res.data.keyframe.filePath
            : '',
        type: getType(res.data.type),
      });
    })

  }
  const shownode = (graph, node: any) => {
    // 设置当前边的筛选模式
    // setshowmodel(0);
    // 获取相邻节点
    let neinode = graph.getNeighbors(node);
    if (neinode.length) {
        neinode.forEach((item: any) => {
            item.attr('foreignObject/opacity', 1);
        })
        node.attr('foreignObject/opacity', 1);
    }
    // 获取相邻边
    let neiedge = graph.getConnectedEdges(node);
    graph.getEdges().forEach((item: any) => {
        // 设置节点透明
        item.attr('line/stroke', '#333333');
    })
    if (neiedge.length) {
        neiedge.forEach((item: any) => {
            item.attr('line/stroke', '#8A8B99');
        })
        node.attr('line/stroke', '#8A8B99');
    }
  }
  return (
    <div className="associatedResource">
      {intl.formatMessage({ id: '关联点' })}
      <>
        {btnText.map(item =>(
          <Button className={`${current.includes(item.key)?'active':''}`} onClick={()=> chooseCurrent(item.key)}>{item.text}</Button>
        ))}
      </>
      <div className="mapX6">
        <MapX6 mapdata={mapInfo} nodeClick={nodeClick} initover={(graph) => {
          graph.getNodes().forEach((item: any) => {
              // 设置节点透明
              item.attr('foreignObject/opacity', 0.2);
          })
          graph.getNodes().forEach(item => {
              if(item.data.highlight){
                  shownode(graph, item)
              }
          })
        }} current={current}></MapX6>
      </div>
      {entity&&<div className='video'>
          <div className="video-header">
            <div className="left" title={videoName}>{videoName}</div>
            <IconFont type="iconguanbi2" onClick={()=> setEntity('')} />
          </div>
          <video src={entity.path} controls style={{width: '100%', height: '300px', objectFit:'cover'}}></video>
          </div>}
    </div>
  )
}

export default AssociatedResource
