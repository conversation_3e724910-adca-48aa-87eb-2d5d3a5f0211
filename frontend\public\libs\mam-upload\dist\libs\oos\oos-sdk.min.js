_xamzrequire=function(){function e(t,r,i){function n(o,s){if(!r[o]){if(!t[o]){var u="function"==typeof require&&require;if(!s&&u)return u(o,!0);if(a)return a(o,!0);var c=new Error("Cannot find module '"+o+"'");throw c.code="MODULE_NOT_FOUND",c}var l=r[o]={exports:{}};t[o][0].call(l.exports,function(e){var r=t[o][1][e];return n(r||e)},l,l.exports,e,t,r,i)}return r[o].exports}for(var a="function"==typeof require&&require,o=0;o<i.length;o++)n(i[o]);return n}return e}()({38:[function(e,t,r){var i={util:e("./util")};var n={};n.toString();t.exports=i;i.util.update(i,{VERSION:"2.296.0",Signers:{},Protocol:{Json:e("./protocol/json"),Query:e("./protocol/query"),Rest:e("./protocol/rest"),RestJson:e("./protocol/rest_json"),RestXml:e("./protocol/rest_xml")},XML:{Builder:e("./xml/builder"),Parser:null},JSON:{Builder:e("./json/builder"),Parser:e("./json/parser")},Model:{Api:e("./model/api"),Operation:e("./model/operation"),Shape:e("./model/shape"),Paginator:e("./model/paginator"),ResourceWaiter:e("./model/resource_waiter")},apiLoader:e("./api_loader")});e("./service");e("./config");e("./http");e("./sequential_executor");e("./event_listeners");e("./request");e("./response");e("./resource_waiter");e("./signers/request_signer");e("./param_validator");i.events=new i.SequentialExecutor},{"./api_loader":27,"./config":37,"./event_listeners":58,"./http":59,"./json/builder":61,"./json/parser":62,"./model/api":63,"./model/operation":65,"./model/paginator":66,"./model/resource_waiter":67,"./model/shape":68,"./param_validator":69,"./protocol/json":71,"./protocol/query":72,"./protocol/rest":73,"./protocol/rest_json":74,"./protocol/rest_xml":75,"./request":80,"./resource_waiter":81,"./response":82,"./sequential_executor":84,"./service":85,"./signers/request_signer":104,"./util":112,"./xml/builder":114}],114:[function(e,t,r){var i=e("../util");var n=e("./xml-node").XmlNode;var a=e("./xml-text").XmlText;function o(){}o.prototype.toXML=function(e,t,r,i){var a=new n(r);p(a,t,true);s(a,e,t);return a.children.length>0||i?a.toString():""};function s(e,t,r){switch(r.type){case"structure":return u(e,t,r);case"map":return c(e,t,r);case"list":return l(e,t,r);default:return f(e,t,r)}}function u(e,t,r){i.arrayEach(r.memberNames,function(i){var a=r.members[i];if(a.location!=="body")return;var o=t[i];var u=a.name;if(o!==undefined&&o!==null){if(a.isXmlAttribute){e.addAttribute(u,o)}else if(a.flattened){s(e,o,a)}else{var c=new n(u);e.addChildNode(c);p(c,a);s(c,o,a)}}})}function c(e,t,r){var a=r.key.name||"key";var o=r.value.name||"value";i.each(t,function(t,i){var u=new n(r.flattened?r.name:"entry");e.addChildNode(u);var c=new n(a);var l=new n(o);u.addChildNode(c);u.addChildNode(l);s(c,t,r.key);s(l,i,r.value)})}function l(e,t,r){if(r.flattened){i.arrayEach(t,function(t){var i=r.member.name||r.name;var a=new n(i);e.addChildNode(a);s(a,t,r.member)})}else{i.arrayEach(t,function(t){var i=r.member.name||"member";var a=new n(i);e.addChildNode(a);s(a,t,r.member)})}}function f(e,t,r){e.addChildNode(new a(r.toWireFormat(t)))}function p(e,t,r){var i,n="xmlns";if(t.xmlNamespaceUri){i=t.xmlNamespaceUri;if(t.xmlNamespacePrefix)n+=":"+t.xmlNamespacePrefix}else if(r&&t.api.xmlNamespaceUri){i=t.api.xmlNamespaceUri}if(i)e.addAttribute(n,i)}t.exports=o},{"../util":112,"./xml-node":117,"./xml-text":118}],118:[function(e,t,r){var i=e("./escape-element").escapeElement;function n(e){this.value=e}n.prototype.toString=function(){return i(""+this.value)};t.exports={XmlText:n}},{"./escape-element":116}],116:[function(e,t,r){function i(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}t.exports={escapeElement:i}},{}],117:[function(e,t,r){var i=e("./escape-attribute").escapeAttribute;function n(e,t){if(t===void 0){t=[]}this.name=e;this.children=t;this.attributes={}}n.prototype.addAttribute=function(e,t){this.attributes[e]=t;return this};n.prototype.addChildNode=function(e){this.children.push(e);return this};n.prototype.removeAttribute=function(e){delete this.attributes[e];return this};n.prototype.toString=function(){var e=Boolean(this.children.length);var t="<"+this.name;var r=this.attributes;for(var n=0,a=Object.keys(r);n<a.length;n++){var o=a[n];var s=r[o];if(typeof s!=="undefined"&&s!==null){t+=" "+o+'="'+i(""+s)+'"'}}return t+=!e?"/>":">"+this.children.map(function(e){return e.toString()}).join("")+"</"+this.name+">"};t.exports={XmlNode:n}},{"./escape-attribute":115}],115:[function(e,t,r){function i(e){return e.replace(/&/g,"&amp;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;")}t.exports={escapeAttribute:i}},{}],104:[function(e,t,r){var i=e("../core");var n=i.util.inherit;i.Signers.RequestSigner=n({constructor:function e(t){this.request=t},setServiceClientId:function e(t){this.serviceClientId=t},getServiceClientId:function e(){return this.serviceClientId}});i.Signers.RequestSigner.getVersion=function e(t){switch(t){case"v2":return i.Signers.V2;case"v3":return i.Signers.V3;case"v4":return i.Signers.V4;case"s3":return i.Signers.S3;case"v3https":return i.Signers.V3Https}throw new Error("Unknown signing version "+t)};e("./v2");e("./v3");e("./v3https");e("./v4");e("./s3");e("./presign")},{"../core":38,"./presign":103,"./s3":105,"./v2":106,"./v3":107,"./v3https":108,"./v4":109}],109:[function(e,t,r){var i=e("../core");var n=e("./v4_credentials");var a=i.util.inherit;var o="presigned-expires";i.Signers.V4=a(i.Signers.RequestSigner,{constructor:function e(t,r,n){i.Signers.RequestSigner.call(this,t);this.serviceName=r;n=n||{};this.signatureCache=typeof n.signatureCache==="boolean"?n.signatureCache:true;this.operation=n.operation},algorithm:"AWS4-HMAC-SHA256",addAuthorization:function e(t,r){var n=i.util.date.iso8601(r).replace(/[:\-]|\.\d{3}/g,"");if(this.isPresigned()){this.updateForPresigned(t,n)}else{this.addHeaders(t,n)}this.request.headers["Authorization"]=this.authorization(t,n)},addHeaders:function e(t,r){this.request.headers["X-Amz-Date"]=r;if(t.sessionToken){this.request.headers["x-amz-security-token"]=t.sessionToken}},updateForPresigned:function e(t,r){var n=this.credentialString(r);var a={"X-Amz-Date":r,"X-Amz-Algorithm":this.algorithm,"X-Amz-Credential":t.accessKeyId+"/"+n,"X-Amz-Expires":this.request.headers[o],"X-Amz-SignedHeaders":this.signedHeaders()};if(t.sessionToken){a["X-Amz-Security-Token"]=t.sessionToken}if(this.request.headers["Content-Type"]){a["Content-Type"]=this.request.headers["Content-Type"]}if(this.request.headers["Content-MD5"]){a["Content-MD5"]=this.request.headers["Content-MD5"]}if(this.request.headers["Cache-Control"]){a["Cache-Control"]=this.request.headers["Cache-Control"]}i.util.each.call(this,this.request.headers,function(e,t){if(e===o)return;if(this.isSignableHeader(e)){var r=e.toLowerCase();if(r.indexOf("x-amz-meta-")===0){a[r]=t}else if(r.indexOf("x-amz-")===0){a[e]=t}}});var s=this.request.path.indexOf("?")>=0?"&":"?";this.request.path+=s+i.util.queryParamsToString(a)},authorization:function e(t,r){var i=[];var n=this.credentialString(r);i.push(this.algorithm+" Credential="+t.accessKeyId+"/"+n);i.push("SignedHeaders="+this.signedHeaders());i.push("Signature="+this.signature(t,r));return i.join(", ")},signature:function e(t,r){var a=n.getSigningKey(t,r.substr(0,8),this.request.region,this.serviceName,this.signatureCache);return i.util.crypto.hmac(a,this.stringToSign(r),"hex")},stringToSign:function e(t){var r=[];r.push("AWS4-HMAC-SHA256");r.push(t);r.push(this.credentialString(t));r.push(this.hexEncodedHash(this.canonicalString()));return r.join("\n")},canonicalString:function e(){var t=[],r=this.request.pathname();if(this.serviceName!=="s3")r=i.util.uriEscapePath(r);t.push(this.request.method);t.push(r);t.push(this.request.search());t.push(this.canonicalHeaders()+"\n");t.push(this.signedHeaders());t.push(this.hexEncodedBodyHash());return t.join("\n")},canonicalHeaders:function e(){var t=[];i.util.each.call(this,this.request.headers,function(e,r){t.push([e,r])});t.sort(function(e,t){return e[0].toLowerCase()<t[0].toLowerCase()?-1:1});var r=[];i.util.arrayEach.call(this,t,function(e){var t=e[0].toLowerCase();if(this.isSignableHeader(t)){var n=e[1];if(typeof n==="undefined"||n===null||typeof n.toString!=="function"){throw i.util.error(new Error("Header "+t+" contains invalid value"),{code:"InvalidHeader"})}r.push(t+":"+this.canonicalHeaderValues(n.toString()))}});return r.join("\n")},canonicalHeaderValues:function e(t){return t.replace(/\s+/g," ").replace(/^\s+|\s+$/g,"")},signedHeaders:function e(){var t=[];i.util.each.call(this,this.request.headers,function(e){e=e.toLowerCase();if(this.isSignableHeader(e))t.push(e)});return t.sort().join(";")},credentialString:function e(t){return n.createScope(t.substr(0,8),this.request.region,this.serviceName)},hexEncodedHash:function e(t){return i.util.crypto.sha256(t,"hex")},hexEncodedBodyHash:function e(){var t=this.request;if(this.isPresigned()&&this.serviceName==="s3"&&!t.body){return"UNSIGNED-PAYLOAD"}else if(t.headers["X-Amz-Content-Sha256"]){return t.headers["X-Amz-Content-Sha256"]}else{return this.hexEncodedHash(this.request.body||"")}},unsignableHeaders:["authorization","content-type","content-length","user-agent",o,"expect","x-amzn-trace-id"],isSignableHeader:function e(t){if(t.toLowerCase().indexOf("x-amz-")===0)return true;return this.unsignableHeaders.indexOf(t)<0},isPresigned:function e(){return this.request.headers[o]?true:false}});t.exports=i.Signers.V4},{"../core":38,"./v4_credentials":110}],110:[function(e,t,r){var i=e("../core");var n={};var a=[];var o=50;var s="aws4_request";t.exports={createScope:function e(t,r,i){return[t.substr(0,8),r,i,s].join("/")},getSigningKey:function e(t,r,u,c,l){var f=i.util.crypto.hmac(t.secretAccessKey,t.accessKeyId,"base64");var p=[f,r,u,c].join("_");l=l!==false;if(l&&p in n){return n[p]}var h=i.util.crypto.hmac("AWS4"+t.secretAccessKey,r,"buffer");var d=i.util.crypto.hmac(h,u,"buffer");var m=i.util.crypto.hmac(d,c,"buffer");var y=i.util.crypto.hmac(m,s,"buffer");if(l){n[p]=y;a.push(p);if(a.length>o){delete n[a.shift()]}}return y},emptyCache:function e(){n={};a=[]}}},{"../core":38}],108:[function(e,t,r){var i=e("../core");var n=i.util.inherit;e("./v3");i.Signers.V3Https=n(i.Signers.V3,{authorization:function e(t){return"AWS3-HTTPS "+"AWSAccessKeyId="+t.accessKeyId+","+"Algorithm=HmacSHA256,"+"Signature="+this.signature(t)},stringToSign:function e(){return this.request.headers["X-Amz-Date"]}});t.exports=i.Signers.V3Https},{"../core":38,"./v3":107}],107:[function(e,t,r){var i=e("../core");var n=i.util.inherit;i.Signers.V3=n(i.Signers.RequestSigner,{addAuthorization:function e(t,r){var n=i.util.date.rfc822(r);this.request.headers["X-Amz-Date"]=n;if(t.sessionToken){this.request.headers["x-amz-security-token"]=t.sessionToken}this.request.headers["X-Amzn-Authorization"]=this.authorization(t,n)},authorization:function e(t){return"AWS3 "+"AWSAccessKeyId="+t.accessKeyId+","+"Algorithm=HmacSHA256,"+"SignedHeaders="+this.signedHeaders()+","+"Signature="+this.signature(t)},signedHeaders:function e(){var t=[];i.util.arrayEach(this.headersToSign(),function e(r){t.push(r.toLowerCase())});return t.sort().join(";")},canonicalHeaders:function e(){var t=this.request.headers;var r=[];i.util.arrayEach(this.headersToSign(),function e(i){r.push(i.toLowerCase().trim()+":"+String(t[i]).trim())});return r.sort().join("\n")+"\n"},headersToSign:function e(){var t=[];i.util.each(this.request.headers,function e(r){if(r==="Host"||r==="Content-Encoding"||r.match(/^X-Amz/i)){t.push(r)}});return t},signature:function e(t){return i.util.crypto.hmac(t.secretAccessKey,this.stringToSign(),"base64")},stringToSign:function e(){var t=[];t.push(this.request.method);t.push("/");t.push("");t.push(this.canonicalHeaders());t.push(this.request.body);return i.util.crypto.sha256(t.join("\n"))}});t.exports=i.Signers.V3},{"../core":38}],106:[function(e,t,r){var i=e("../core");var n=i.util.inherit;i.Signers.V2=n(i.Signers.RequestSigner,{addAuthorization:function e(t,r){if(!r)r=i.util.date.getDate();var n=this.request;n.params.Timestamp=i.util.date.iso8601(r);n.params.SignatureVersion="2";n.params.SignatureMethod="HmacSHA256";n.params.AWSAccessKeyId=t.accessKeyId;if(t.sessionToken){n.params.SecurityToken=t.sessionToken}delete n.params.Signature;n.params.Signature=this.signature(t);n.body=i.util.queryParamsToString(n.params);n.headers["Content-Length"]=n.body.length},signature:function e(t){return i.util.crypto.hmac(t.secretAccessKey,this.stringToSign(),"base64")},stringToSign:function e(){var t=[];t.push(this.request.method);t.push(this.request.endpoint.host.toLowerCase());t.push(this.request.pathname());t.push(i.util.queryParamsToString(this.request.params));return t.join("\n")}});t.exports=i.Signers.V2},{"../core":38}],105:[function(e,t,r){var i=e("../core");var n=i.util.inherit;i.Signers.S3=n(i.Signers.RequestSigner,{subResources:{acl:1,accelerate:1,analytics:1,cors:1,lifecycle:1,delete:1,inventory:1,location:1,logging:1,metrics:1,notification:1,partNumber:1,policy:1,requestPayment:1,replication:1,restore:1,tagging:1,torrent:1,uploadId:1,uploads:1,versionId:1,versioning:1,versions:1,website:1},responseHeaders:{"response-content-type":1,"response-content-language":1,"response-expires":1,"response-cache-control":1,"response-content-disposition":1,"response-content-encoding":1},addAuthorization:function e(t,r){if(!this.request.headers["presigned-expires"]){this.request.headers["X-Amz-Date"]=i.util.date.rfc822(r)}if(t.sessionToken){this.request.headers["x-amz-security-token"]=t.sessionToken}var n=this.sign(t.secretAccessKey,this.stringToSign());var a="AWS "+t.accessKeyId+":"+n;this.request.headers["Authorization"]=a},stringToSign:function e(){var t=this.request;var r=[];r.push(t.method);r.push(t.headers["Content-MD5"]||"");r.push(t.headers["Content-Type"]||"");r.push(t.headers["presigned-expires"]||"");var i=this.canonicalizedAmzHeaders();if(i)r.push(i);r.push(this.canonicalizedResource());return r.join("\n")},canonicalizedAmzHeaders:function e(){var t=[];i.util.each(this.request.headers,function(e){if(e.match(/^x-amz-/i))t.push(e)});t.sort(function(e,t){return e.toLowerCase()<t.toLowerCase()?-1:1});var r=[];i.util.arrayEach.call(this,t,function(e){r.push(e.toLowerCase()+":"+String(this.request.headers[e]))});return r.join("\n")},canonicalizedResource:function e(){var t=this.request;var r=t.path.split("?");var n=r[0];var a=r[1];var o="";if(t.virtualHostedBucket)o+="/"+t.virtualHostedBucket;o+=n;if(a){var s=[];i.util.arrayEach.call(this,a.split("&"),function(e){var t=e.split("=")[0];var r=e.split("=")[1];if(this.subResources[t]||this.responseHeaders[t]){var i={name:t};if(r!==undefined){if(this.subResources[t]){i.value=r}else{i.value=decodeURIComponent(r)}}s.push(i)}});s.sort(function(e,t){return e.name<t.name?-1:1});if(s.length){a=[];i.util.arrayEach(s,function(e){if(e.value===undefined){a.push(e.name)}else{a.push(e.name+"="+e.value)}});o+="?"+a.join("&")}}return o},sign:function e(t,r){return i.util.crypto.hmac(t,r,"base64","sha1")}});t.exports=i.Signers.S3},{"../core":38}],103:[function(e,t,r){var i=e("../core");var n=i.util.inherit;var a="presigned-expires";function o(e){var t=e.httpRequest.headers[a];var r=e.service.getSignerClass(e);delete e.httpRequest.headers["User-Agent"];delete e.httpRequest.headers["X-Amz-User-Agent"];if(r===i.Signers.V4){if(t>604800){var n="Presigning does not support expiry time greater "+"than a week with SigV4 signing.";throw i.util.error(new Error,{code:"InvalidExpiryTime",message:n,retryable:false})}e.httpRequest.headers[a]=t}else if(r===i.Signers.S3){var o=e.service?e.service.getSkewCorrectedDate():i.util.date.getDate();e.httpRequest.headers[a]=parseInt(i.util.date.unixTimestamp(o)+t,10).toString()}else{throw i.util.error(new Error,{message:"Presigning only supports S3 or SigV4 signing.",code:"UnsupportedSigner",retryable:false})}}function s(e){var t=e.httpRequest.endpoint;var r=i.util.urlParse(e.httpRequest.path);var n={};if(r.search){n=i.util.queryStringParse(r.search.substr(1))}var o=e.httpRequest.headers["Authorization"].split(" ");if(o[0]==="OOS"){o=o[1].split(":");n["AWSAccessKeyId"]=o[0];n["Signature"]=o[1];i.util.each(e.httpRequest.headers,function(e,t){if(e===a)e="Expires";if(e.indexOf("x-amz-meta-")===0){delete n[e];e=e.toLowerCase()}n[e]=t});delete e.httpRequest.headers[a];delete n["Authorization"];delete n["Host"]}else if(o[0]==="AWS4-HMAC-SHA256"){o.shift();var s=o.join(" ");var u=s.match(/Signature=(.*?)(?:,|\s|\r?\n|$)/)[1];n["X-Amz-Signature"]=u;delete n["Expires"]}t.pathname=r.pathname;t.search=i.util.queryParamsToString(n)}i.Signers.Presign=n({sign:function e(t,r,n){t.httpRequest.headers[a]=r||3600;t.on("build",o);t.on("sign",s);t.removeListener("afterBuild",i.EventListeners.Core.SET_CONTENT_LENGTH);t.removeListener("afterBuild",i.EventListeners.Core.COMPUTE_SHA256);t.emit("beforePresign",[t]);if(n){t.build(function(){if(this.response.error)n(this.response.error);else{n(null,i.util.urlFormat(t.httpRequest.endpoint))}})}else{t.build();if(t.response.error)throw t.response.error;return i.util.urlFormat(t.httpRequest.endpoint)}}});t.exports=i.Signers.Presign},{"../core":38}],85:[function(e,t,r){var i=e("./core");var n=e("./model/api");var a=e("./region_config");var o=i.util.inherit;var s=0;i.Service=o({constructor:function e(t){if(!this.loadServiceClass){throw i.util.error(new Error,"Service must be constructed with `new' operator")}var r=this.loadServiceClass(t||{});if(r){var n=i.util.copy(t);var a=new r(t);Object.defineProperty(a,"_originalConfig",{get:function(){return n},enumerable:false,configurable:true});a._clientId=++s;return a}this.initialize(t)},initialize:function e(t){var r=i.config[this.serviceIdentifier];this.config=new i.Config(i.config);if(r)this.config.update(r,true);if(t)this.config.update(t,true);this.validateService();if(!this.config.endpoint)a(this);this.config.endpoint=this.endpointFromTemplate(this.config.endpoint);this.setEndpoint(this.config.endpoint)},validateService:function e(){},loadServiceClass:function e(t){var r=t;if(!i.util.isEmpty(this.api)){return null}else if(r.apiConfig){return i.Service.defineServiceApi(this.constructor,r.apiConfig)}else if(!this.constructor.services){return null}else{r=new i.Config(i.config);r.update(t,true);var n=r.apiVersions[this.constructor.serviceIdentifier];n=n||r.apiVersion;return this.getLatestServiceClass(n)}},getLatestServiceClass:function e(t){t=this.getLatestServiceVersion(t);if(this.constructor.services[t]===null){i.Service.defineServiceApi(this.constructor,t)}return this.constructor.services[t]},getLatestServiceVersion:function e(t){if(!this.constructor.services||this.constructor.services.length===0){throw new Error("No services defined on "+this.constructor.serviceIdentifier)}if(!t){t="latest"}else if(i.util.isType(t,Date)){t=i.util.date.iso8601(t).split("T")[0]}if(Object.hasOwnProperty(this.constructor.services,t)){return t}var r=Object.keys(this.constructor.services).sort();var n=null;for(var a=r.length-1;a>=0;a--){if(r[a][r[a].length-1]!=="*"){n=r[a]}if(r[a].substr(0,10)<=t){return n}}throw new Error("Could not find "+this.constructor.serviceIdentifier+" API to satisfy version constraint `"+t+"'")},api:{},defaultRetryCount:3,customizeRequests:function e(t){if(!t){this.customRequestHandler=null}else if(typeof t==="function"){this.customRequestHandler=t}else{throw new Error("Invalid callback type '"+typeof t+"' provided in customizeRequests")}},makeRequest:function e(t,r,n){if(typeof r==="function"){n=r;r=null}r=r||{};if(this.config.params){var a=this.api.operations[t];if(a){r=i.util.copy(r);i.util.each(this.config.params,function(e,t){if(a.input.members[e]){if(r[e]===undefined||r[e]===null){r[e]=t}}})}}var o=new i.Request(this,t,r);this.addAllRequestListeners(o);if(n)o.send(n);return o},makeUnauthenticatedRequest:function e(t,r,i){if(typeof r==="function"){i=r;r={}}var n=this.makeRequest(t,r).toUnauthenticated();return i?n.send(i):n},waitFor:function e(t,r,n){var a=new i.ResourceWaiter(this,t);return a.wait(r,n)},addAllRequestListeners:function e(t){var r=[i.events,i.EventListeners.Core,this.serviceInterface(),i.EventListeners.CorePost];for(var n=0;n<r.length;n++){if(r[n])t.addListeners(r[n])}if(!this.config.paramValidation){t.removeListener("validate",i.EventListeners.Core.VALIDATE_PARAMETERS)}if(this.config.logger){t.addListeners(i.EventListeners.Logger)}this.setupRequestListeners(t);if(typeof this.constructor.prototype.customRequestHandler==="function"){this.constructor.prototype.customRequestHandler(t)}if(Object.prototype.hasOwnProperty.call(this,"customRequestHandler")&&typeof this.customRequestHandler==="function"){this.customRequestHandler(t)}},setupRequestListeners:function e(){},getSignerClass:function e(t){var r;var n=null;var a="";if(t){var o=t.service.api.operations||{};n=o[t.operation]||null;a=n?n.authtype:""}if(this.config.signatureVersion){r=this.config.signatureVersion}else if(a==="v4"||a==="v4-unsigned-body"){r="v4"}else{r=this.api.signatureVersion}return i.Signers.RequestSigner.getVersion(r)},serviceInterface:function e(){switch(this.api.protocol){case"ec2":return i.EventListeners.Query;case"query":return i.EventListeners.Query;case"json":return i.EventListeners.Json;case"rest-json":return i.EventListeners.RestJson;case"rest-xml":return i.EventListeners.RestXml}if(this.api.protocol){throw new Error("Invalid service `protocol' "+this.api.protocol+" in API config")}},successfulResponse:function e(t){return t.httpResponse.statusCode<300},numRetries:function e(){if(this.config.maxRetries!==undefined){return this.config.maxRetries}else{return this.defaultRetryCount}},retryDelays:function e(t){return i.util.calculateRetryDelay(t,this.config.retryDelayOptions)},retryableError:function e(t){if(this.timeoutError(t))return true;if(this.networkingError(t))return true;if(this.expiredCredentialsError(t))return true;if(this.throttledError(t))return true;if(t.statusCode>=500)return true;return false},networkingError:function e(t){return t.code==="NetworkingError"},timeoutError:function e(t){return t.code==="TimeoutError"},expiredCredentialsError:function e(t){return t.code==="ExpiredTokenException"},clockSkewError:function e(t){switch(t.code){case"RequestTimeTooSkewed":case"RequestExpired":case"InvalidSignatureException":case"SignatureDoesNotMatch":case"AuthFailure":case"RequestInTheFuture":return true;default:return false}},getSkewCorrectedDate:function e(){return new Date(Date.now()+this.config.systemClockOffset)},applyClockOffset:function e(t){if(t){this.config.systemClockOffset=t-Date.now()}},isClockSkewed:function e(t){if(t){return Math.abs(this.getSkewCorrectedDate().getTime()-t)>=3e4}},throttledError:function e(t){switch(t.code){case"ProvisionedThroughputExceededException":case"Throttling":case"ThrottlingException":case"RequestLimitExceeded":case"RequestThrottled":return true;default:return false}},endpointFromTemplate:function e(t){if(typeof t!=="string")return t;var r=t;r=r.replace(/\{service\}/g,this.api.endpointPrefix);r=r.replace(/\{region\}/g,this.config.region);r=r.replace(/\{scheme\}/g,this.config.sslEnabled?"https":"http");return r},setEndpoint:function e(t){this.endpoint=new i.Endpoint(t,this.config)},paginationConfig:function e(t,r){var n=this.api.operations[t].paginator;if(!n){if(r){var a=new Error;throw i.util.error(a,"No pagination configuration for "+t)}return null}return n}});i.util.update(i.Service,{defineMethods:function e(t){i.util.each(t.prototype.api.operations,function e(r){if(t.prototype[r])return;var i=t.prototype.api.operations[r];if(i.authtype==="none"){t.prototype[r]=function(e,t){return this.makeUnauthenticatedRequest(r,e,t)}}else{t.prototype[r]=function(e,t){return this.makeRequest(r,e,t)}}})},defineService:function e(t,r,n){i.Service._serviceMap[t]=true;if(!Array.isArray(r)){n=r;r=[]}var a=o(i.Service,n||{});if(typeof t==="string"){i.Service.addVersions(a,r);var s=a.serviceIdentifier||t;a.serviceIdentifier=s}else{a.prototype.api=t;i.Service.defineMethods(a)}return a},addVersions:function e(t,r){if(!Array.isArray(r))r=[r];t.services=t.services||{};for(var i=0;i<r.length;i++){if(t.services[r[i]]===undefined){t.services[r[i]]=null}}t.apiVersions=Object.keys(t.services).sort()},defineServiceApi:function e(t,r,a){var s=o(t,{serviceIdentifier:t.serviceIdentifier});function u(e){if(e.isApi){s.prototype.api=e}else{s.prototype.api=new n(e)}}if(typeof r==="string"){if(a){u(a)}else{try{u(i.apiLoader(t.serviceIdentifier,r))}catch(e){throw i.util.error(e,{message:"Could not find API configuration "+t.serviceIdentifier+"-"+r})}}if(!Object.prototype.hasOwnProperty.call(t.services,r)){t.apiVersions=t.apiVersions.concat(r).sort()}t.services[r]=s}else{u(r)}i.Service.defineMethods(s);return s},hasService:function(e){return Object.prototype.hasOwnProperty.call(i.Service._serviceMap,e)},_serviceMap:{}});t.exports=i.Service},{"./core":38,"./model/api":63,"./region_config":78}],78:[function(e,t,r){var i=e("./util");var n=e("./region_config_data.json");function a(e){if(!e)return null;var t=e.split("-");if(t.length<3)return null;return t.slice(0,t.length-2).join("-")+"-*"}function o(e){var t=e.config.region;var r=a(t);var i=e.api.endpointPrefix;return[[t,i],[r,i],[t,"*"],[r,"*"],["*",i],["*","*"]].map(function(e){return e[0]&&e[1]?e.join("/"):null})}function s(e,t){i.each(t,function(t,r){if(t==="globalEndpoint")return;if(e.config[t]===undefined||e.config[t]===null){e.config[t]=r}})}function u(e){var t=o(e);for(var r=0;r<t.length;r++){var a=t[r];if(!a)continue;if(Object.prototype.hasOwnProperty.call(n.rules,a)){var u=n.rules[a];if(typeof u==="string"){u=n.patterns[u]}if(e.config.useDualstack&&i.isDualstackAvailable(e)){u=i.copy(u);u.endpoint="{service}.dualstack.{region}.amazonaws.com"}e.isGlobalEndpoint=!!u.globalEndpoint;if(!u.signatureVersion)u.signatureVersion="v4";s(e,u);return}}}t.exports=u},{"./region_config_data.json":79,"./util":112}],79:[function(e,t,r){t.exports={rules:{"*/*":{endpoint:"{service}.{region}.amazonaws.com"},"cn-*/*":{endpoint:"{service}.{region}.amazonaws.com.cn"},"*/budgets":"globalSSL","*/cloudfront":"globalSSL","*/iam":"globalSSL","*/sts":"globalSSL","*/importexport":{endpoint:"{service}.amazonaws.com",signatureVersion:"v2",globalEndpoint:true},"*/route53":{endpoint:"https://{service}.amazonaws.com",signatureVersion:"v3https",globalEndpoint:true},"*/waf":"globalSSL","us-gov-*/iam":"globalGovCloud","us-gov-*/sts":{endpoint:"{service}.{region}.amazonaws.com"},"us-gov-west-1/s3":"s3signature","us-west-1/s3":"s3signature","us-west-2/s3":"s3signature","eu-west-1/s3":"s3signature","ap-southeast-1/s3":"s3signature","ap-southeast-2/s3":"s3signature","ap-northeast-1/s3":"s3signature","sa-east-1/s3":"s3signature","us-east-1/s3":{endpoint:"{service}.amazonaws.com",signatureVersion:"s3"},"us-east-1/sdb":{endpoint:"{service}.amazonaws.com",signatureVersion:"v2"},"*/sdb":{endpoint:"{service}.{region}.amazonaws.com",signatureVersion:"v2"}},patterns:{globalSSL:{endpoint:"https://{service}.amazonaws.com",globalEndpoint:true},globalGovCloud:{endpoint:"{service}.us-gov.amazonaws.com"},s3signature:{endpoint:"{service}.{region}.amazonaws.com",signatureVersion:"s3"}}}},{}],82:[function(e,t,r){var i=e("./core");var n=i.util.inherit;var a=e("jmespath");i.Response=n({constructor:function e(t){this.request=t;this.data=null;this.error=null;this.retryCount=0;this.redirectCount=0;this.httpResponse=new i.HttpResponse;if(t){this.maxRetries=t.service.numRetries();this.maxRedirects=t.service.config.maxRedirects}},nextPage:function e(t){var r;var n=this.request.service;var a=this.request.operation;try{r=n.paginationConfig(a,true)}catch(e){this.error=e}if(!this.hasNextPage()){if(t)t(this.error,null);else if(this.error)throw this.error;return null}var o=i.util.copy(this.request.params);if(!this.nextPageTokens){return t?t(null,null):null}else{var s=r.inputToken;if(typeof s==="string")s=[s];for(var u=0;u<s.length;u++){o[s[u]]=this.nextPageTokens[u]}return n.makeRequest(this.request.operation,o,t)}},hasNextPage:function e(){this.cacheNextPageTokens();if(this.nextPageTokens)return true;if(this.nextPageTokens===undefined)return undefined;else return false},cacheNextPageTokens:function e(){if(Object.prototype.hasOwnProperty.call(this,"nextPageTokens"))return this.nextPageTokens;this.nextPageTokens=undefined;var t=this.request.service.paginationConfig(this.request.operation);if(!t)return this.nextPageTokens;this.nextPageTokens=null;if(t.moreResults){if(!a.search(this.data,t.moreResults)){return this.nextPageTokens}}var r=t.outputToken;if(typeof r==="string")r=[r];i.util.arrayEach.call(this,r,function(e){var t=a.search(this.data,e);if(t){this.nextPageTokens=this.nextPageTokens||[];this.nextPageTokens.push(t)}});return this.nextPageTokens}})},{"./core":38,jmespath:8}],81:[function(e,t,r){var i=e("./core");var n=i.util.inherit;var a=e("jmespath");function o(e){var t=e.request._waiter;var r=t.config.acceptors;var i=false;var n="retry";r.forEach(function(r){if(!i){var a=t.matchers[r.matcher];if(a&&a(e,r.expected,r.argument)){i=true;n=r.state}}});if(!i&&e.error)n="failure";if(n==="success"){t.setSuccess(e)}else{t.setError(e,n==="retry")}}i.ResourceWaiter=n({constructor:function e(t,r){this.service=t;this.state=r;this.loadWaiterConfig(this.state)},service:null,state:null,config:null,matchers:{path:function(e,t,r){try{var i=a.search(e.data,r)}catch(e){return false}return a.strictDeepEqual(i,t)},pathAll:function(e,t,r){try{var i=a.search(e.data,r)}catch(e){return false}if(!Array.isArray(i))i=[i];var n=i.length;if(!n)return false;for(var o=0;o<n;o++){if(!a.strictDeepEqual(i[o],t)){return false}}return true},pathAny:function(e,t,r){try{var i=a.search(e.data,r)}catch(e){return false}if(!Array.isArray(i))i=[i];var n=i.length;for(var o=0;o<n;o++){if(a.strictDeepEqual(i[o],t)){return true}}return false},status:function(e,t){var r=e.httpResponse.statusCode;return typeof r==="number"&&r===t},error:function(e,t){if(typeof t==="string"&&e.error){return t===e.error.code}return t===!!e.error}},listeners:(new i.SequentialExecutor).addNamedListeners(function(e){e("RETRY_CHECK","retry",function(e){var t=e.request._waiter;if(e.error&&e.error.code==="ResourceNotReady"){e.error.retryDelay=(t.config.delay||0)*1e3}});e("CHECK_OUTPUT","extractData",o);e("CHECK_ERROR","extractError",o)}),wait:function e(t,r){if(typeof t==="function"){r=t;t=undefined}if(t&&t.$waiter){t=i.util.copy(t);if(typeof t.$waiter.delay==="number"){this.config.delay=t.$waiter.delay}if(typeof t.$waiter.maxAttempts==="number"){this.config.maxAttempts=t.$waiter.maxAttempts}delete t.$waiter}var n=this.service.makeRequest(this.config.operation,t);n._waiter=this;n.response.maxRetries=this.config.maxAttempts;n.addListeners(this.listeners);if(r)n.send(r);return n},setSuccess:function e(t){t.error=null;t.data=t.data||{};t.request.removeAllListeners("extractData")},setError:function e(t,r){t.data=null;t.error=i.util.error(t.error||new Error,{code:"ResourceNotReady",message:"Resource is not in the state "+this.state,retryable:r})},loadWaiterConfig:function e(t){if(!this.service.api.waiters[t]){throw new i.util.error(new Error,{code:"StateNotFoundError",message:"State "+t+" not found."})}this.config=i.util.copy(this.service.api.waiters[t])}})},{"./core":38,jmespath:8}],80:[function(e,t,r){(function(t){var r=e("./core");var i=e("./state_machine");var n=r.util.inherit;var a=r.util.domain;var o=e("jmespath");var s={success:1,error:1,complete:1};function u(e){return Object.prototype.hasOwnProperty.call(s,e._asm.currentState)}var c=new i;c.setupStates=function(){var e=function(e,t){var r=this;r._haltHandlersOnError=false;r.emit(r._asm.currentState,function(e){if(e){if(u(r)){if(a&&r.domain instanceof a.Domain){e.domainEmitter=r;e.domain=r.domain;e.domainThrown=false;r.domain.emit("error",e)}else{throw e}}else{r.response.error=e;t(e)}}else{t(r.response.error)}})};this.addState("validate","build","error",e);this.addState("build","afterBuild","restart",e);this.addState("afterBuild","sign","restart",e);this.addState("sign","send","retry",e);this.addState("retry","afterRetry","afterRetry",e);this.addState("afterRetry","sign","error",e);this.addState("send","validateResponse","retry",e);this.addState("validateResponse","extractData","extractError",e);this.addState("extractError","extractData","retry",e);this.addState("extractData","success","retry",e);this.addState("restart","build","error",e);this.addState("success","complete","complete",e);this.addState("error","complete","complete",e);this.addState("complete",null,null,e)};c.setupStates();r.Request=n({constructor:function e(t,n,o){var s=t.endpoint;var u=t.config.region;var l=t.config.customUserAgent;if(t.isGlobalEndpoint)u="us-east-1";this.domain=a&&a.active;this.service=t;this.operation=n;this.params=o||{};this.httpRequest=new r.HttpRequest(s,u);this.httpRequest.appendToUserAgent(l);this.startTime=t.getSkewCorrectedDate();this.response=new r.Response(this);this._asm=new i(c.states,"validate");this._haltHandlersOnError=false;r.SequentialExecutor.call(this);this.emit=this.emitEvent},send:function e(t){if(t){this.httpRequest.appendToUserAgent("callback");this.on("complete",function(e){t.call(e,e.error,e.data)})}this.runTo();return this.response},build:function e(t){return this.runTo("send",t)},runTo:function e(t,r){this._asm.runTo(t,r,this);return this},abort:function e(){this.removeAllListeners("validateResponse");this.removeAllListeners("extractError");this.on("validateResponse",function e(t){t.error=r.util.error(new Error("Request aborted by user"),{code:"RequestAbortedError",retryable:false})});if(this.httpRequest.stream&&!this.httpRequest.stream.didCallback){this.httpRequest.stream.abort();if(this.httpRequest._abortCallback){this.httpRequest._abortCallback()}else{this.removeAllListeners("send")}}return this},eachPage:function e(t){t=r.util.fn.makeAsync(t,3);function i(e){t.call(e,e.error,e.data,function(n){if(n===false)return;if(e.hasNextPage()){e.nextPage().on("complete",i).send()}else{t.call(e,null,null,r.util.fn.noop)}})}this.on("complete",i).send()},eachItem:function e(t){var i=this;function n(e,n){if(e)return t(e,null);if(n===null)return t(null,null);var a=i.service.paginationConfig(i.operation);var s=a.resultKey;if(Array.isArray(s))s=s[0];var u=o.search(n,s);var c=true;r.util.arrayEach(u,function(e){c=t(null,e);if(c===false){return r.util.abort}});return c}this.eachPage(n)},isPageable:function e(){return this.service.paginationConfig(this.operation)?true:false},createReadStream:function e(){var i=r.util.stream;var n=this;var a=null;if(r.HttpClient.streamsApiVersion===2){a=new i.PassThrough;t.nextTick(function(){n.send()})}else{a=new i.Stream;a.readable=true;a.sent=false;a.on("newListener",function(e){if(!a.sent&&e==="data"){a.sent=true;t.nextTick(function(){n.send()})}})}this.on("error",function(e){a.emit("error",e)});this.on("httpHeaders",function e(t,o,s){if(t<300){n.removeListener("httpData",r.EventListeners.Core.HTTP_DATA);n.removeListener("httpError",r.EventListeners.Core.HTTP_ERROR);n.on("httpError",function e(t){s.error=t;s.error.retryable=false});var u=false;var c;if(n.httpRequest.method!=="HEAD"){c=parseInt(o["content-length"],10)}if(c!==undefined&&!isNaN(c)&&c>=0){u=true;var l=0}var f=function e(){if(u&&l!==c){a.emit("error",r.util.error(new Error("Stream content length mismatch. Received "+l+" of "+c+" bytes."),{code:"StreamContentLengthMismatch"}))}else if(r.HttpClient.streamsApiVersion===2){a.end()}else{a.emit("end")}};var p=s.httpResponse.createUnbufferedStream();if(r.HttpClient.streamsApiVersion===2){if(u){var h=new i.PassThrough;h._write=function(e){if(e&&e.length){l+=e.length}return i.PassThrough.prototype._write.apply(this,arguments)};h.on("end",f);a.on("error",function(e){u=false;p.unpipe(h);h.emit("end");h.end()});p.pipe(h).pipe(a,{end:false})}else{p.pipe(a)}}else{if(u){p.on("data",function(e){if(e&&e.length){l+=e.length}})}p.on("data",function(e){a.emit("data",e)});p.on("end",f)}p.on("error",function(e){u=false;a.emit("error",e)})}});return a},emitEvent:function e(t,i,n){if(typeof i==="function"){n=i;i=null}if(!n)n=function(){};if(!i)i=this.eventParameters(t,this.response);var a=r.SequentialExecutor.prototype.emit;a.call(this,t,i,function(e){if(e)this.response.error=e;n.call(this,e)})},eventParameters:function e(t){switch(t){case"restart":case"validate":case"sign":case"build":case"afterValidate":case"afterBuild":return[this];case"error":return[this.response.error,this.response];default:return[this.response]}},presign:function e(t,i){if(!i&&typeof t==="function"){i=t;t=null}return(new r.Signers.Presign).sign(this.toGet(),t,i)},isPresigned:function e(){return Object.prototype.hasOwnProperty.call(this.httpRequest.headers,"presigned-expires")},toUnauthenticated:function e(){this.removeListener("validate",r.EventListeners.Core.VALIDATE_CREDENTIALS);this.removeListener("sign",r.EventListeners.Core.SIGN);return this},toGet:function e(){if(this.service.api.protocol==="query"||this.service.api.protocol==="ec2"){this.removeListener("build",this.buildAsGet);this.addListener("build",this.buildAsGet)}return this},buildAsGet:function e(t){t.httpRequest.method="GET";t.httpRequest.path=t.service.endpoint.path+"?"+t.httpRequest.body;t.httpRequest.body="";delete t.httpRequest.headers["Content-Length"];delete t.httpRequest.headers["Content-Type"]},haltHandlersOnError:function e(){this._haltHandlersOnError=true}});r.Request.addPromisesToClass=function e(t){this.prototype.promise=function e(){var r=this;this.httpRequest.appendToUserAgent("promise");return new t(function(e,t){r.on("complete",function(r){if(r.error){t(r.error)}else{e(Object.defineProperty(r.data||{},"$response",{value:r}))}});r.runTo()})}};r.Request.deletePromisesFromClass=function e(){delete this.prototype.promise};r.util.addPromises(r.Request);r.util.mixin(r.Request,r.SequentialExecutor)}).call(this,e("_process"))},{"./core":38,"./state_machine":111,_process:9,jmespath:8}],111:[function(e,t,r){function i(e,t){this.currentState=t||null;this.states=e||{}}i.prototype.runTo=function e(t,r,i,n){if(typeof t==="function"){n=i;i=r;r=t;t=null}var a=this;var o=a.states[a.currentState];o.fn.call(i||a,n,function(e){if(e){if(o.fail)a.currentState=o.fail;else return r?r.call(i,e):null}else{if(o.accept)a.currentState=o.accept;else return r?r.call(i):null}if(a.currentState===t){return r?r.call(i,e):null}a.runTo(t,r,i,e)})};i.prototype.addState=function e(t,r,i,n){if(typeof r==="function"){n=r;r=null;i=null}else if(typeof i==="function"){n=i;i=null}if(!this.currentState)this.currentState=t;this.states[t]={accept:r,fail:i,fn:n};return this};t.exports=i},{}],69:[function(e,t,r){var i=e("./core");i.ParamValidator=i.util.inherit({constructor:function e(t){if(t===true||t===undefined){t={min:true}}this.validation=t},validate:function e(t,r,n){this.errors=[];this.validateMember(t,r||{},n||"params");if(this.errors.length>1){var a=this.errors.join("\n* ");a="There were "+this.errors.length+" validation errors:\n* "+a;throw i.util.error(new Error(a),{code:"MultipleValidationErrors",errors:this.errors})}else if(this.errors.length===1){throw this.errors[0]}else{return true}},fail:function e(t,r){this.errors.push(i.util.error(new Error(r),{code:t}))},validateStructure:function e(t,r,i){this.validateType(r,i,["object"],"structure");var n;for(var a=0;t.required&&a<t.required.length;a++){n=t.required[a];var o=r[n];if(o===undefined||o===null){this.fail("MissingRequiredParameter","Missing required key '"+n+"' in "+i)}}for(n in r){if(!Object.prototype.hasOwnProperty.call(r,n))continue;var s=r[n],u=t.members[n];if(u!==undefined){var c=[i,n].join(".");this.validateMember(u,s,c)}else{this.fail("UnexpectedParameter","Unexpected key '"+n+"' found in "+i)}}return true},validateMember:function e(t,r,i){switch(t.type){case"structure":return this.validateStructure(t,r,i);case"list":return this.validateList(t,r,i);case"map":return this.validateMap(t,r,i);default:return this.validateScalar(t,r,i)}},validateList:function e(t,r,i){if(this.validateType(r,i,[Array])){this.validateRange(t,r.length,i,"list member count");for(var n=0;n<r.length;n++){this.validateMember(t.member,r[n],i+"["+n+"]")}}},validateMap:function e(t,r,i){if(this.validateType(r,i,["object"],"map")){var n=0;for(var a in r){if(!Object.prototype.hasOwnProperty.call(r,a))continue;this.validateMember(t.key,a,i+"[key='"+a+"']");this.validateMember(t.value,r[a],i+"['"+a+"']");n++}this.validateRange(t,n,i,"map member count")}},validateScalar:function e(t,r,i){switch(t.type){case null:case undefined:case"string":return this.validateString(t,r,i);case"base64":case"binary":return this.validatePayload(r,i);case"integer":case"float":return this.validateNumber(t,r,i);case"boolean":return this.validateType(r,i,["boolean"]);case"timestamp":return this.validateType(r,i,[Date,/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?Z$/,"number"],"Date object, ISO-8601 string, or a UNIX timestamp");default:return this.fail("UnkownType","Unhandled type "+t.type+" for "+i)}},validateString:function e(t,r,i){var n=["string"];if(t.isJsonValue){n=n.concat(["number","object","boolean"])}if(r!==null&&this.validateType(r,i,n)){this.validateEnum(t,r,i);this.validateRange(t,r.length,i,"string length");this.validatePattern(t,r,i)}},validatePattern:function e(t,r,i){if(this.validation["pattern"]&&t["pattern"]!==undefined){if(!new RegExp(t["pattern"]).test(r)){this.fail("PatternMatchError",'Provided value "'+r+'" '+"does not match regex pattern /"+t["pattern"]+"/ for "+i)}}},validateRange:function e(t,r,i,n){if(this.validation["min"]){if(t["min"]!==undefined&&r<t["min"]){this.fail("MinRangeError","Expected "+n+" >= "+t["min"]+", but found "+r+" for "+i)}}if(this.validation["max"]){if(t["max"]!==undefined&&r>t["max"]){this.fail("MaxRangeError","Expected "+n+" <= "+t["max"]+", but found "+r+" for "+i)}}},validateEnum:function e(t,r,i){if(this.validation["enum"]&&t["enum"]!==undefined){if(t["enum"].indexOf(r)===-1){this.fail("EnumError","Found string value of "+r+", but "+"expected "+t["enum"].join("|")+" for "+i)}}},validateType:function e(t,r,n,a){if(t===null||t===undefined)return false;var o=false;for(var s=0;s<n.length;s++){if(typeof n[s]==="string"){if(typeof t===n[s])return true}else if(n[s]instanceof RegExp){if((t||"").toString().match(n[s]))return true}else{if(t instanceof n[s])return true;if(i.util.isType(t,n[s]))return true;if(!a&&!o)n=n.slice();n[s]=i.util.typeName(n[s])}o=true}var u=a;if(!u){u=n.join(", ").replace(/,([^,]+)$/,", or$1")}var c=u.match(/^[aeiou]/i)?"n":"";this.fail("InvalidParameterType","Expected "+r+" to be a"+c+" "+u);return false},validateNumber:function e(t,r,i){if(r===null||r===undefined)return;if(typeof r==="string"){var n=parseFloat(r);if(n.toString()===r)r=n}if(this.validateType(r,i,["number"])){this.validateRange(t,r,i,"numeric value")}},validatePayload:function e(t,r){if(t===null||t===undefined)return;if(typeof t==="string")return;if(t&&typeof t.byteLength==="number")return;if(i.util.isNode()){var n=i.util.stream.Stream;if(i.util.Buffer.isBuffer(t)||t instanceof n)return}var a=["Buffer","Stream","File","Blob","ArrayBuffer","DataView"];if(t){for(var o=0;o<a.length;o++){if(i.util.isType(t,a[o]))return;if(i.util.typeName(t.constructor)===a[o])return}}this.fail("InvalidParameterType","Expected "+r+" to be a "+"string, Buffer, Stream, Blob, or typed array object")}})},{"./core":38}],63:[function(e,t,r){var i=e("./collection");var n=e("./operation");var a=e("./shape");var o=e("./paginator");var s=e("./resource_waiter");var u=e("../util");var c=u.property;var l=u.memoizedProperty;function f(e,t){e=e||{};t=t||{};t.api=this;e.metadata=e.metadata||{};c(this,"isApi",true,false);c(this,"apiVersion",e.metadata.apiVersion);c(this,"endpointPrefix",e.metadata.endpointPrefix);c(this,"signingName",e.metadata.signingName);c(this,"globalEndpoint",e.metadata.globalEndpoint);c(this,"signatureVersion",e.metadata.signatureVersion);c(this,"jsonVersion",e.metadata.jsonVersion);c(this,"targetPrefix",e.metadata.targetPrefix);c(this,"protocol",e.metadata.protocol);c(this,"timestampFormat",e.metadata.timestampFormat);c(this,"xmlNamespaceUri",e.metadata.xmlNamespace);c(this,"abbreviation",e.metadata.serviceAbbreviation);c(this,"fullName",e.metadata.serviceFullName);l(this,"className",function(){var t=e.metadata.serviceAbbreviation||e.metadata.serviceFullName;if(!t)return null;t=t.replace(/^Amazon|OOS\s*|\(.*|\s+|\W+/g,"");if(t==="ElasticLoadBalancing")t="ELB";return t});c(this,"operations",new i(e.operations,t,function(e,r){return new n(e,r,t)},u.string.lowerFirst));c(this,"shapes",new i(e.shapes,t,function(e,r){return a.create(r,t)}));c(this,"paginators",new i(e.paginators,t,function(e,r){return new o(e,r,t)}));c(this,"waiters",new i(e.waiters,t,function(e,r){return new s(e,r,t)},u.string.lowerFirst));if(t.documentation){c(this,"documentation",e.documentation);c(this,"documentationUrl",e.documentationUrl)}}t.exports=f},{"../util":112,"./collection":64,"./operation":65,"./paginator":66,"./resource_waiter":67,"./shape":68}],67:[function(e,t,r){var i=e("../util");var n=i.property;function a(e,t,r){r=r||{};n(this,"name",e);n(this,"api",r.api,false);if(t.operation){n(this,"operation",i.string.lowerFirst(t.operation))}var a=this;var o=["type","description","delay","maxAttempts","acceptors"];o.forEach(function(e){var r=t[e];if(r){n(a,e,r)}})}t.exports=a},{"../util":112}],66:[function(e,t,r){var i=e("../util").property;function n(e,t){i(this,"inputToken",t.input_token);i(this,"limitKey",t.limit_key);i(this,"moreResults",t.more_results);i(this,"outputToken",t.output_token);i(this,"resultKey",t.result_key)}t.exports=n},{"../util":112}],65:[function(e,t,r){var i=e("./shape");var n=e("../util");var a=n.property;var o=n.memoizedProperty;function s(e,t,r){var n=this;r=r||{};a(this,"name",t.name||e);a(this,"api",r.api,false);t.http=t.http||{};a(this,"httpMethod",t.http.method||"POST");a(this,"httpPath",t.http.requestUri||"/");a(this,"authtype",t.authtype||"");o(this,"input",function(){if(!t.input){return new i.create({type:"structure"},r)}return i.create(t.input,r)});o(this,"output",function(){if(!t.output){return new i.create({type:"structure"},r)}return i.create(t.output,r)});o(this,"errors",function(){var e=[];if(!t.errors)return null;for(var n=0;n<t.errors.length;n++){e.push(i.create(t.errors[n],r))}return e});o(this,"paginator",function(){return r.api.paginators[e]});if(r.documentation){a(this,"documentation",t.documentation);a(this,"documentationUrl",t.documentationUrl)}o(this,"idempotentMembers",function(){var e=[];var t=n.input;var r=t.members;if(!t.members){return e}for(var i in r){if(!r.hasOwnProperty(i)){continue}if(r[i].isIdempotent===true){e.push(i)}}return e});o(this,"hasEventOutput",function(){var e=n.output;return u(e)})}function u(e){var t=e.members;var r=e.payload;if(!e.members){return false}if(r){var i=t[r];return i.isEventStream}for(var n in t){if(!t.hasOwnProperty(n)){if(t[n].isEventStream===true){return true}}}return false}t.exports=s},{"../util":112,"./shape":68}],59:[function(e,t,r){var i=e("./core");var n=i.util.inherit;i.Endpoint=n({constructor:function e(t,r){i.util.hideProperties(this,["slashes","auth","hash","search","query"]);if(typeof t==="undefined"||t===null){throw new Error("Invalid endpoint: "+t)}else if(typeof t!=="string"){return i.util.copy(t)}if(!t.match(/^http/)){var n=r&&r.sslEnabled!==undefined?r.sslEnabled:i.config.sslEnabled;t=(n?"https":"http")+"://"+t}i.util.update(this,i.util.urlParse(t));if(this.port){this.port=parseInt(this.port,10)}else{this.port=this.protocol==="https:"?443:80}}});i.HttpRequest=n({constructor:function e(t,r){t=new i.Endpoint(t);this.method="POST";this.path=t.path||"/";this.headers={};this.body="";this.endpoint=t;this.region=r;this._userAgent="";this.setUserAgent()},setUserAgent:function e(){this._userAgent=this.headers[this.getUserAgentHeaderName()]=i.util.userAgent()},getUserAgentHeaderName:function e(){var t=i.util.isBrowser()?"X-Amz-":"";return t+"User-Agent"},appendToUserAgent:function e(t){if(typeof t==="string"&&t){this._userAgent+=" "+t}this.headers[this.getUserAgentHeaderName()]=this._userAgent},getUserAgent:function e(){return this._userAgent},pathname:function e(){return this.path.split("?",1)[0]},search:function e(){var t=this.path.split("?",2)[1];if(t){t=i.util.queryStringParse(t);return i.util.queryParamsToString(t)}return""}});i.HttpResponse=n({constructor:function e(){this.statusCode=undefined;this.headers={};this.body=undefined;this.streaming=false;this.stream=null},createUnbufferedStream:function e(){this.streaming=true;return this.stream}});i.HttpClient=n({});i.HttpClient.getInstance=function e(){if(this.singleton===undefined){this.singleton=new this}return this.singleton}},{"./core":38}],58:[function(e,t,r){var i=e("./core");var n=e("./sequential_executor");i.EventListeners={Core:{}};function a(e){if(!e.service.api.operations){return""}var t=e.service.api.operations[e.operation];return t?t.authtype:""}i.EventListeners={Core:(new n).addNamedListeners(function(e,t){t("VALIDATE_CREDENTIALS","validate",function e(t,r){if(!t.service.api.signatureVersion)return r();t.service.config.getCredentials(function(e){if(e){t.response.error=i.util.error(e,{code:"CredentialsError",message:"Missing credentials in config"})}r()})});e("VALIDATE_REGION","validate",function e(t){if(!t.service.config.region&&!t.service.isGlobalEndpoint){t.response.error=i.util.error(new Error,{code:"ConfigError",message:"Missing region in config"})}});e("BUILD_IDEMPOTENCY_TOKENS","validate",function e(t){if(!t.service.api.operations){return}var r=t.service.api.operations[t.operation];if(!r){return}var n=r.idempotentMembers;if(!n.length){return}var a=i.util.copy(t.params);for(var o=0,s=n.length;o<s;o++){if(!a[n[o]]){a[n[o]]=i.util.uuid.v4()}}t.params=a});e("VALIDATE_PARAMETERS","validate",function e(t){if(!t.service.api.operations){return}var r=t.service.api.operations[t.operation].input;var n=t.service.config.paramValidation;new i.ParamValidator(n).validate(r,t.params)});t("COMPUTE_SHA256","afterBuild",function e(t,r){t.haltHandlersOnError();if(!t.service.api.operations){return}var n=t.service.api.operations[t.operation];var a=n?n.authtype:"";if(!t.service.api.signatureVersion&&!a)return r();if(t.service.getSignerClass(t)===i.Signers.V4){var o=t.httpRequest.body||"";if(a.indexOf("unsigned-body")>=0){t.httpRequest.headers["X-Amz-Content-Sha256"]="UNSIGNED-PAYLOAD";return r()}i.util.computeSha256(o,function(e,i){if(e){r(e)}else{t.httpRequest.headers["X-Amz-Content-Sha256"]=i;r()}})}else{r()}});e("SET_CONTENT_LENGTH","afterBuild",function e(t){var r=a(t);if(t.httpRequest.headers["Content-Length"]===undefined){try{var n=i.util.string.byteLength(t.httpRequest.body);t.httpRequest.headers["Content-Length"]=n}catch(e){if(r.indexOf("unsigned-body")===-1){throw e}else{return}}}});e("SET_HTTP_HOST","afterBuild",function e(t){t.httpRequest.headers["Host"]=t.httpRequest.endpoint.host});e("RESTART","restart",function e(){var t=this.response.error;if(!t||!t.retryable)return;this.httpRequest=new i.HttpRequest(this.service.endpoint,this.service.region);if(this.response.retryCount<this.service.config.maxRetries){this.response.retryCount++}else{this.response.error=null}});t("SIGN","sign",function e(t,r){var i=t.service;var n=t.service.api.operations||{};var a=n[t.operation];var o=a?a.authtype:"";if(!i.api.signatureVersion&&!o)return r();i.config.getCredentials(function(e,n){if(e){t.response.error=e;return r()}try{var o=i.getSkewCorrectedDate();var s=i.getSignerClass(t);var u=new s(t.httpRequest,i.api.signingName||i.api.endpointPrefix,{signatureCache:i.config.signatureCache,operation:a});u.setServiceClientId(i._clientId);delete t.httpRequest.headers["Authorization"];delete t.httpRequest.headers["Date"];delete t.httpRequest.headers["X-Amz-Date"];u.addAuthorization(n,o);t.signedAt=o}catch(e){t.response.error=e}r()})});e("VALIDATE_RESPONSE","validateResponse",function e(t){if(this.service.successfulResponse(t,this)){t.data={};t.error=null}else{t.data=null;t.error=i.util.error(new Error,{code:"UnknownError",message:"An unknown error occurred."})}});t("SEND","send",function e(t,r){t.httpResponse._abortCallback=r;t.error=null;t.data=null;function n(e){t.httpResponse.stream=e;var n=t.request.httpRequest.stream;var a=t.request.service;var o=a.api;var s=t.request.operation;var u=o.operations[s]||{};e.on("headers",function n(o,s,c){t.request.emit("httpHeaders",[o,s,t,c]);if(!t.httpResponse.streaming){if(i.HttpClient.streamsApiVersion===2){if(u.hasEventOutput&&a.successfulResponse(t)){t.request.emit("httpDone");r();return}e.on("readable",function r(){var i=e.read();if(i!==null){t.request.emit("httpData",[i,t])}})}else{e.on("data",function e(r){t.request.emit("httpData",[r,t])})}}});e.on("end",function e(){if(!n||!n.didCallback){if(i.HttpClient.streamsApiVersion===2&&(u.hasEventOutput&&a.successfulResponse(t))){return}t.request.emit("httpDone");r()}})}function a(e){e.on("sendProgress",function e(r){t.request.emit("httpUploadProgress",[r,t])});e.on("receiveProgress",function e(r){t.request.emit("httpDownloadProgress",[r,t])})}function o(e){if(e.code!=="RequestAbortedError"){var n=e.code==="TimeoutError"?e.code:"NetworkingError";e=i.util.error(e,{code:n,region:t.request.httpRequest.region,hostname:t.request.httpRequest.endpoint.hostname,retryable:true})}t.error=e;t.request.emit("httpError",[t.error,t],function(){r()})}function s(){var e=i.HttpClient.getInstance();var r=t.request.service.config.httpOptions||{};try{var s=e.handleRequest(t.request.httpRequest,r,n,o);a(s)}catch(e){o(e)}}var u=(t.request.service.getSkewCorrectedDate()-this.signedAt)/1e3;if(u>=60*10){this.emit("sign",[this],function(e){if(e)r(e);else s()})}else{s()}});e("HTTP_HEADERS","httpHeaders",function e(t,r,n,a){n.httpResponse.statusCode=t;n.httpResponse.statusMessage=a;n.httpResponse.headers=r;n.httpResponse.body=new i.util.Buffer("");n.httpResponse.buffers=[];n.httpResponse.numBytes=0;var o=r.date||r.Date;var s=n.request.service;if(o){var u=Date.parse(o);if(s.config.correctClockSkew&&s.isClockSkewed(u)){s.applyClockOffset(u)}}});e("HTTP_DATA","httpData",function e(t,r){if(t){if(i.util.isNode()){r.httpResponse.numBytes+=t.length;var n=r.httpResponse.headers["content-length"];var a={loaded:r.httpResponse.numBytes,total:n};r.request.emit("httpDownloadProgress",[a,r])}r.httpResponse.buffers.push(new i.util.Buffer(t))}});e("HTTP_DONE","httpDone",function e(t){if(t.httpResponse.buffers&&t.httpResponse.buffers.length>0){var r=i.util.buffer.concat(t.httpResponse.buffers);t.httpResponse.body=r}delete t.httpResponse.numBytes;delete t.httpResponse.buffers});e("FINALIZE_ERROR","retry",function e(t){if(t.httpResponse.statusCode){t.error.statusCode=t.httpResponse.statusCode;if(t.error.retryable===undefined){t.error.retryable=this.service.retryableError(t.error,this)}}});e("INVALIDATE_CREDENTIALS","retry",function e(t){if(!t.error)return;switch(t.error.code){case"RequestExpired":case"ExpiredTokenException":case"ExpiredToken":t.error.retryable=true;t.request.service.config.credentials.expired=true}});e("EXPIRED_SIGNATURE","retry",function e(t){var r=t.error;if(!r)return;if(typeof r.code==="string"&&typeof r.message==="string"){if(r.code.match(/Signature/)&&r.message.match(/expired/)){t.error.retryable=true}}});e("CLOCK_SKEWED","retry",function e(t){if(!t.error)return;if(this.service.clockSkewError(t.error)&&this.service.config.correctClockSkew){t.error.retryable=true}});e("REDIRECT","retry",function e(t){if(t.error&&t.error.statusCode>=300&&t.error.statusCode<400&&t.httpResponse.headers["location"]){this.httpRequest.endpoint=new i.Endpoint(t.httpResponse.headers["location"]);this.httpRequest.headers["Host"]=this.httpRequest.endpoint.host;t.error.redirect=true;t.error.retryable=true}});e("RETRY_CHECK","retry",function e(t){if(t.error){if(t.error.redirect&&t.redirectCount<t.maxRedirects){t.error.retryDelay=0}else if(t.retryCount<t.maxRetries){t.error.retryDelay=this.service.retryDelays(t.retryCount)||0}}});t("RESET_RETRY_STATE","afterRetry",function e(t,r){var i,n=false;if(t.error){i=t.error.retryDelay||0;if(t.error.retryable&&t.retryCount<t.maxRetries){t.retryCount++;n=true}else if(t.error.redirect&&t.redirectCount<t.maxRedirects){t.redirectCount++;n=true}}if(n){t.error=null;setTimeout(r,i)}else{r()}})}),CorePost:(new n).addNamedListeners(function(e){e("EXTRACT_REQUEST_ID","extractData",i.util.extractRequestId);e("EXTRACT_REQUEST_ID","extractError",i.util.extractRequestId);e("ENOTFOUND_ERROR","httpError",function e(t){if(t.code==="NetworkingError"&&t.errno==="ENOTFOUND"){var r="Inaccessible host: `"+t.hostname+"'. This service may not be available in the `"+t.region+"' region.";this.response.error=i.util.error(new Error(r),{code:"UnknownEndpoint",region:t.region,hostname:t.hostname,retryable:true,originalError:t})}})}),Logger:(new n).addNamedListeners(function(t){t("LOG_REQUEST","complete",function t(r){var n=r.request;var a=n.service.config.logger;if(!a)return;function o(e,t){if(!t){return t}switch(e.type){case"structure":var r={};i.util.each(t,function(t,i){if(Object.prototype.hasOwnProperty.call(e.members,t)){r[t]=o(e.members[t],i)}else{r[t]=i}});return r;case"list":var n=[];i.util.arrayEach(t,function(t,r){n.push(o(e.member,t))});return n;case"map":var a={};i.util.each(t,function(t,r){a[t]=o(e.value,r)});return a;default:if(e.isSensitive){return"***SensitiveInformation***"}else{return t}}}function s(){var t=r.request.service.getSkewCorrectedDate().getTime();var s=(t-n.startTime.getTime())/1e3;var u=a.isTTY?true:false;var c=r.httpResponse.statusCode;var l=n.params;if(n.service.api.operations&&n.service.api.operations[n.operation]&&n.service.api.operations[n.operation].input){var f=n.service.api.operations[n.operation].input;l=o(f,n.params)}var p=e("util").inspect(l,true,null);var h="";if(u)h+="[33m";h+="[OOS "+n.service.serviceIdentifier+" "+c;h+=" "+s.toString()+"s "+r.retryCount+" retries]";if(u)h+="[0;1m";h+=" "+i.util.string.lowerFirst(n.operation);h+="("+p+")";if(u)h+="[0m";return h}var u=s();if(typeof a.log==="function"){a.log(u)}else if(typeof a.write==="function"){a.write(u+"\n")}})}),Json:(new n).addNamedListeners(function(t){var r=e("./protocol/json");t("BUILD","build",r.buildRequest);t("EXTRACT_DATA","extractData",r.extractData);t("EXTRACT_ERROR","extractError",r.extractError)}),Rest:(new n).addNamedListeners(function(t){var r=e("./protocol/rest");t("BUILD","build",r.buildRequest);t("EXTRACT_DATA","extractData",r.extractData);t("EXTRACT_ERROR","extractError",r.extractError)}),RestJson:(new n).addNamedListeners(function(t){var r=e("./protocol/rest_json");t("BUILD","build",r.buildRequest);t("EXTRACT_DATA","extractData",r.extractData);t("EXTRACT_ERROR","extractError",r.extractError)}),RestXml:(new n).addNamedListeners(function(t){var r=e("./protocol/rest_xml");t("BUILD","build",r.buildRequest);t("EXTRACT_DATA","extractData",r.extractData);t("EXTRACT_ERROR","extractError",r.extractError)}),Query:(new n).addNamedListeners(function(t){var r=e("./protocol/query");t("BUILD","build",r.buildRequest);t("EXTRACT_DATA","extractData",r.extractData);t("EXTRACT_ERROR","extractError",r.extractError)})}},{"./core":38,"./protocol/json":71,"./protocol/query":72,"./protocol/rest":73,"./protocol/rest_json":74,"./protocol/rest_xml":75,"./sequential_executor":84,util:20}],84:[function(e,t,r){var i=e("./core");i.SequentialExecutor=i.util.inherit({constructor:function e(){this._events={}},listeners:function e(t){return this._events[t]?this._events[t].slice(0):[]},on:function e(t,r){if(this._events[t]){this._events[t].push(r)}else{this._events[t]=[r]}return this},onAsync:function e(t,r){r._isAsync=true;return this.on(t,r)},removeListener:function e(t,r){var i=this._events[t];if(i){var n=i.length;var a=-1;for(var o=0;o<n;++o){if(i[o]===r){a=o}}if(a>-1){i.splice(a,1)}}return this},removeAllListeners:function e(t){if(t){delete this._events[t]}else{this._events={}}return this},emit:function e(t,r,i){if(!i)i=function(){};var n=this.listeners(t);var a=n.length;this.callListeners(n,r,i);return a>0},callListeners:function e(t,r,n,a){var o=this;var s=a||null;function u(e){if(e){s=i.util.error(s||new Error,e);if(o._haltHandlersOnError){return n.call(o,s)}}o.callListeners(t,r,n,s)}while(t.length>0){var c=t.shift();if(c._isAsync){c.apply(o,r.concat([u]));return}else{try{c.apply(o,r)}catch(e){s=i.util.error(s||new Error,e)}if(s&&o._haltHandlersOnError){n.call(o,s);return}}}n.call(o,s)},addListeners:function e(t){var r=this;if(t._events)t=t._events;i.util.each(t,function(e,t){if(typeof t==="function")t=[t];i.util.arrayEach(t,function(t){r.on(e,t)})});return r},addNamedListener:function e(t,r,i){this[t]=i;this.addListener(r,i);return this},addNamedAsyncListener:function e(t,r,i){i._isAsync=true;return this.addNamedListener(t,r,i)},addNamedListeners:function e(t){var r=this;t(function(){r.addNamedListener.apply(r,arguments)},function(){r.addNamedAsyncListener.apply(r,arguments)});return this}});i.SequentialExecutor.prototype.addListener=i.SequentialExecutor.prototype.on;t.exports=i.SequentialExecutor},{"./core":38}],75:[function(e,t,r){var i=e("../core");var n=e("../util");var a=e("./rest");function o(e){var t=e.service.api.operations[e.operation].input;var r=new i.XML.Builder;var a=e.params;var o=t.payload;if(o){var s=t.members[o];a=a[o];if(a===undefined)return;if(s.type==="structure"){var u=s.name;e.httpRequest.body=r.toXML(a,s,u,true)}else{e.httpRequest.body=a}}else{e.httpRequest.body=r.toXML(a,t,t.name||t.shape||n.string.upperFirst(e.operation)+"Request")}}function s(e){a.buildRequest(e);if(["GET","HEAD"].indexOf(e.httpRequest.method)<0){o(e)}}function u(e){a.extractError(e);var t;try{t=(new i.XML.Parser).parse(e.httpResponse.body.toString())}catch(r){t={Code:e.httpResponse.statusCode,Message:e.httpResponse.statusMessage}}if(t.Errors)t=t.Errors;if(t.Error)t=t.Error;if(t.Code){e.error=n.error(new Error,{code:t.Code,message:t.Message})}else{e.error=n.error(new Error,{code:e.httpResponse.statusCode,message:null})}}function c(e){a.extractData(e);var t;var r=e.request;var o=e.httpResponse.body;var s=r.service.api.operations[r.operation];var u=s.output;var c=s.hasEventOutput;var l=u.payload;if(l){var f=u.members[l];if(f.isEventStream){t=new i.XML.Parser;e.data[l]=n.createEventStream(i.HttpClient.streamsApiVersion===2?e.httpResponse.stream:e.httpResponse.body,t,f)}else if(f.type==="structure"){t=new i.XML.Parser;e.data[l]=t.parse(o.toString(),f)}else if(f.type==="binary"||f.isStreaming){e.data[l]=o}else{e.data[l]=f.toType(o)}}else if(o.length>0){t=new i.XML.Parser;var p=t.parse(o.toString(),u);n.update(e.data,p)}}t.exports={buildRequest:s,extractError:u,extractData:c}},{"../core":38,"../util":112,"./rest":73}],74:[function(e,t,r){var i=e("../util");var n=e("./rest");var a=e("./json");var o=e("../json/builder");var s=e("../json/parser");function u(e){var t=new o;var r=e.service.api.operations[e.operation].input;if(r.payload){var i={};var n=r.members[r.payload];i=e.params[r.payload];if(i===undefined)return;if(n.type==="structure"){e.httpRequest.body=t.build(i,n);c(e)}else{e.httpRequest.body=i;if(n.type==="binary"||n.isStreaming){c(e,true)}}}else{e.httpRequest.body=t.build(e.params,r);c(e)}}function c(e,t){var r=e.service.api.operations[e.operation];var i=r.input;if(!e.httpRequest.headers["Content-Type"]){var n=t?"binary/octet-stream":"application/json";e.httpRequest.headers["Content-Type"]=n}}function l(e){n.buildRequest(e);if(["GET","HEAD","DELETE"].indexOf(e.httpRequest.method)<0){u(e)}}function f(e){a.extractError(e)}function p(e){n.extractData(e);var t=e.request;var r=t.service.api.operations[t.operation];var o=t.service.api.operations[t.operation].output||{};var u;var c=r.hasEventOutput;if(o.payload){var l=o.members[o.payload];var f=e.httpResponse.body;if(l.isEventStream){u=new s;e.data[payload]=i.createEventStream(OOS.HttpClient.streamsApiVersion===2?e.httpResponse.stream:f,u,l)}else if(l.type==="structure"||l.type==="list"){var u=new s;e.data[o.payload]=u.parse(f,l)}else if(l.type==="binary"||l.isStreaming){e.data[o.payload]=f}else{e.data[o.payload]=l.toType(f)}}else{var p=e.data;a.extractData(e);e.data=i.merge(p,e.data)}}t.exports={buildRequest:l,extractError:f,extractData:p}},{"../json/builder":61,"../json/parser":62,"../util":112,"./json":71,"./rest":73}],73:[function(e,t,r){var i=e("../util");function n(e){e.httpRequest.method=e.service.api.operations[e.operation].httpMethod}function a(e,t,r,n){var a=[e,t].join("/");a=a.replace(/\/+/g,"/");var o={},s=false;i.each(r.members,function(e,t){var r=n[e];if(r===null||r===undefined)return;if(t.location==="uri"){var u=new RegExp("\\{"+t.name+"(\\+)?\\}");a=a.replace(u,function(e,t){var n=t?i.uriEscapePath:i.uriEscape;return n(String(r))})}else if(t.location==="querystring"){s=true;if(t.type==="list"){o[t.name]=r.map(function(e){return i.uriEscape(t.member.toWireFormat(e).toString())})}else if(t.type==="map"){i.each(r,function(e,t){if(Array.isArray(t)){o[e]=t.map(function(e){return i.uriEscape(String(e))})}else{o[e]=i.uriEscape(String(t))}})}else{o[t.name]=i.uriEscape(t.toWireFormat(r).toString())}}});if(s){a+=a.indexOf("?")>=0?"&":"?";var u=[];i.arrayEach(Object.keys(o).sort(),function(e){if(!Array.isArray(o[e])){o[e]=[o[e]]}for(var t=0;t<o[e].length;t++){u.push(i.uriEscape(String(e))+"="+o[e][t])}});a+=u.join("&")}return a}function o(e){var t=e.service.api.operations[e.operation];var r=t.input;var i=a(e.httpRequest.endpoint.path,t.httpPath,r,e.params);e.httpRequest.path=i}function s(e){var t=e.service.api.operations[e.operation];i.each(t.input.members,function(t,r){var n=e.params[t];if(n===null||n===undefined)return;if(r.location==="headers"&&r.type==="map"){i.each(n,function(t,i){e.httpRequest.headers[r.name+t]=i})}else if(r.location==="header"){n=r.toWireFormat(n).toString();if(r.isJsonValue){n=i.base64.encode(n)}e.httpRequest.headers[r.name]=n}})}function u(e){n(e);o(e);s(e)}function c(){}function l(e){var t=e.request;var r={};var n=e.httpResponse;var a=t.service.api.operations[t.operation];var o=a.output;var s={};i.each(n.headers,function(e,t){s[e.toLowerCase()]=t});i.each(o.members,function(e,t){var a=(t.name||e).toLowerCase();if(t.location==="headers"&&t.type==="map"){r[e]={};var o=t.isLocationName?t.name:"";var u=new RegExp("^"+o+"(.+)","i");i.each(n.headers,function(t,i){var n=t.match(u);if(n!==null){r[e][n[1]]=i}})}else if(t.location==="header"){if(s[a]!==undefined){var c=t.isJsonValue?i.base64.decode(s[a]):s[a];r[e]=t.toType(c)}}else if(t.location==="statusCode"){r[e]=parseInt(n.statusCode,10)}});e.data=r}t.exports={buildRequest:u,extractError:c,extractData:l,generateURI:a}},{"../util":112}],72:[function(e,t,r){var i=e("../core");var n=e("../util");var a=e("../query/query_param_serializer");var o=e("../model/shape");function s(e){var t=e.service.api.operations[e.operation];var r=e.httpRequest;r.headers["Content-Type"]="application/x-www-form-urlencoded; charset=utf-8";r.params={Version:e.service.api.apiVersion,Action:t.name};var i=new a;i.serialize(e.params,t.input,function(e,t){r.params[e]=t});r.body=n.queryParamsToString(r.params)}function u(e){var t,r=e.httpResponse.body.toString();if(r.match("<UnknownOperationException")){t={Code:"UnknownOperation",Message:"Unknown operation "+e.request.operation}}else{try{t=(new i.XML.Parser).parse(r)}catch(r){t={Code:e.httpResponse.statusCode,Message:e.httpResponse.statusMessage}}}if(t.requestId&&!e.requestId)e.requestId=t.requestId;if(t.Errors)t=t.Errors;if(t.Error)t=t.Error;if(t.Code){e.error=n.error(new Error,{code:t.Code,message:t.Message})}else{e.error=n.error(new Error,{code:e.httpResponse.statusCode,message:null})}}function c(e){var t=e.request;var r=t.service.api.operations[t.operation];var a=r.output||{};var s=a;if(s.resultWrapper){var u=o.create({type:"structure"});u.members[s.resultWrapper]=a;u.memberNames=[s.resultWrapper];n.property(a,"name",a.resultWrapper);a=u}var c=new i.XML.Parser;if(a&&a.members&&!a.members._XAMZRequestId){var l=o.create({type:"string"},{api:{protocol:"query"}},"requestId");a.members._XAMZRequestId=l}var f=c.parse(e.httpResponse.body.toString(),a);e.requestId=f._XAMZRequestId||f.requestId;if(f._XAMZRequestId)delete f._XAMZRequestId;if(s.resultWrapper){if(f[s.resultWrapper]){n.update(f,f[s.resultWrapper]);delete f[s.resultWrapper]}}e.data=f}t.exports={buildRequest:s,extractError:u,extractData:c}},{"../core":38,"../model/shape":68,"../query/query_param_serializer":76,"../util":112}],76:[function(e,t,r){var i=e("../util");function n(){}n.prototype.serialize=function(e,t,r){o("",e,t,r)};function a(e){if(e.isQueryName||e.api.protocol!=="ec2"){return e.name}else{return e.name[0].toUpperCase()+e.name.substr(1)}}function o(e,t,r,n){i.each(r.members,function(r,i){var o=t[r];if(o===null||o===undefined)return;var s=a(i);s=e?e+"."+s:s;c(s,o,i,n)})}function s(e,t,r,n){var a=1;i.each(t,function(t,i){var o=r.flattened?".":".entry.";var s=o+a+++".";var u=s+(r.key.name||"key");var l=s+(r.value.name||"value");c(e+u,t,r.key,n);c(e+l,i,r.value,n)})}function u(e,t,r,n){var o=r.member||{};if(t.length===0){n.call(this,e,null);return}i.arrayEach(t,function(t,i){var s="."+(i+1);if(r.api.protocol==="ec2"){s=s+""}else if(r.flattened){if(o.name){var u=e.split(".");u.pop();u.push(a(o));e=u.join(".")}}else{s="."+(o.name?o.name:"member")+s}c(e+s,t,o,n)})}function c(e,t,r,i){if(t===null||t===undefined)return;if(r.type==="structure"){o(e,t,r,i)}else if(r.type==="list"){u(e,t,r,i)}else if(r.type==="map"){s(e,t,r,i)}else{i(e,r.toWireFormat(t).toString())}}t.exports=n},{"../util":112}],68:[function(e,t,r){var i=e("./collection");var n=e("../util");function a(e,t,r){if(r!==null&&r!==undefined){n.property.apply(this,arguments)}}function o(e,t){if(!e.constructor.prototype[t]){n.memoizedProperty.apply(this,arguments)}}function s(e,t,r){t=t||{};a(this,"shape",e.shape);a(this,"api",t.api,false);a(this,"type",e.type);a(this,"enum",e.enum);a(this,"min",e.min);a(this,"max",e.max);a(this,"pattern",e.pattern);a(this,"location",e.location||this.location||"body");a(this,"name",this.name||e.xmlName||e.queryName||e.locationName||r);a(this,"isStreaming",e.streaming||this.isStreaming||false);a(this,"isComposite",e.isComposite||false);a(this,"isShape",true,false);a(this,"isQueryName",Boolean(e.queryName),false);a(this,"isLocationName",Boolean(e.locationName),false);a(this,"isIdempotent",e.idempotencyToken===true);a(this,"isJsonValue",e.jsonvalue===true);a(this,"isSensitive",e.sensitive===true||e.prototype&&e.prototype.sensitive===true);a(this,"isEventStream",Boolean(e.eventstream),false);a(this,"isEvent",Boolean(e.event),false);a(this,"isEventPayload",Boolean(e.eventpayload),false);a(this,"isEventHeader",Boolean(e.eventheader),false);a(this,"isTimestampFormatSet",Boolean(e.timestampFormat)||e.prototype&&e.prototype.isTimestampFormatSet===true,false);if(t.documentation){a(this,"documentation",e.documentation);a(this,"documentationUrl",e.documentationUrl)}if(e.xmlAttribute){a(this,"isXmlAttribute",e.xmlAttribute||false)}a(this,"defaultValue",null);this.toWireFormat=function(e){if(e===null||e===undefined)return"";return e};this.toType=function(e){return e}}s.normalizedTypes={character:"string",double:"float",long:"integer",short:"integer",biginteger:"integer",bigdecimal:"float",blob:"binary"};s.types={structure:c,list:l,map:f,boolean:g,timestamp:p,float:d,integer:m,string:h,base64:v,binary:y};s.resolve=function e(t,r){if(t.shape){var i=r.api.shapes[t.shape];if(!i){throw new Error("Cannot find shape reference: "+t.shape)}return i}else{return null}};s.create=function e(t,r,i){if(t.isShape)return t;var n=s.resolve(t,r);if(n){var a=Object.keys(t);if(!r.documentation){a=a.filter(function(e){return!e.match(/documentation/)})}var o=function(){n.constructor.call(this,t,r,i)};o.prototype=n;return new o}else{if(!t.type){if(t.members)t.type="structure";else if(t.member)t.type="list";else if(t.key)t.type="map";else t.type="string"}var u=t.type;if(s.normalizedTypes[t.type]){t.type=s.normalizedTypes[t.type]}if(s.types[t.type]){return new s.types[t.type](t,r,i)}else{throw new Error("Unrecognized shape type: "+u)}}};function u(e){s.apply(this,arguments);a(this,"isComposite",true);if(e.flattened){a(this,"flattened",e.flattened||false)}}function c(e,t){var r=this;var n=null,c=!this.isShape;u.apply(this,arguments);if(c){a(this,"defaultValue",function(){return{}});a(this,"members",{});a(this,"memberNames",[]);a(this,"required",[]);a(this,"isRequired",function(){return false})}if(e.members){a(this,"members",new i(e.members,t,function(e,r){return s.create(r,t,e)}));o(this,"memberNames",function(){return e.xmlOrder||Object.keys(e.members)});if(e.event){o(this,"eventPayloadMemberName",function(){var e=r.members;var t=r.memberNames;for(var i=0,n=t.length;i<n;i++){if(e[t[i]].isEventPayload){return t[i]}}});o(this,"eventHeaderMemberNames",function(){var e=r.members;var t=r.memberNames;var i=[];for(var n=0,a=t.length;n<a;n++){if(e[t[n]].isEventHeader){i.push(t[n])}}return i})}}if(e.required){a(this,"required",e.required);a(this,"isRequired",function(t){if(!n){n={};for(var r=0;r<e.required.length;r++){n[e.required[r]]=true}}return n[t]},false,true)}a(this,"resultWrapper",e.resultWrapper||null);if(e.payload){a(this,"payload",e.payload)}if(typeof e.xmlNamespace==="string"){a(this,"xmlNamespaceUri",e.xmlNamespace)}else if(typeof e.xmlNamespace==="object"){a(this,"xmlNamespacePrefix",e.xmlNamespace.prefix);a(this,"xmlNamespaceUri",e.xmlNamespace.uri)}}function l(e,t){var r=this,i=!this.isShape;u.apply(this,arguments);if(i){a(this,"defaultValue",function(){return[]})}if(e.member){o(this,"member",function(){return s.create(e.member,t)})}if(this.flattened){var n=this.name;o(this,"name",function(){return r.member.name||n})}}function f(e,t){var r=!this.isShape;u.apply(this,arguments);if(r){a(this,"defaultValue",function(){return{}});a(this,"key",s.create({type:"string"},t));a(this,"value",s.create({type:"string"},t))}if(e.key){o(this,"key",function(){return s.create(e.key,t)})}if(e.value){o(this,"value",function(){return s.create(e.value,t)})}}function p(e){var t=this;s.apply(this,arguments);if(e.timestampFormat){a(this,"timestampFormat",e.timestampFormat)}else if(t.isTimestampFormatSet&&this.timestampFormat){a(this,"timestampFormat",this.timestampFormat)}else if(this.location==="header"){a(this,"timestampFormat","rfc822")}else if(this.location==="querystring"){a(this,"timestampFormat","iso8601")}else if(this.api){switch(this.api.protocol){case"json":case"rest-json":a(this,"timestampFormat","unixTimestamp");break;case"rest-xml":case"query":case"ec2":a(this,"timestampFormat","iso8601");break}}this.toType=function(e){if(e===null||e===undefined)return null;if(typeof e.toUTCString==="function")return e;return typeof e==="string"||typeof e==="number"?n.date.parseTimestamp(e):null};this.toWireFormat=function(e){return n.date.format(e,t.timestampFormat)}}function h(){s.apply(this,arguments);var e=["rest-xml","query","ec2"];this.toType=function(t){t=this.api&&e.indexOf(this.api.protocol)>-1?t||"":t;if(this.isJsonValue){return JSON.parse(t)}return t&&typeof t.toString==="function"?t.toString():t};this.toWireFormat=function(e){return this.isJsonValue?JSON.stringify(e):e}}function d(){s.apply(this,arguments);this.toType=function(e){if(e===null||e===undefined)return null;return parseFloat(e)};this.toWireFormat=this.toType}function m(){s.apply(this,arguments);this.toType=function(e){if(e===null||e===undefined)return null;return parseInt(e,10)};this.toWireFormat=this.toType}function y(){s.apply(this,arguments);this.toType=n.base64.decode;this.toWireFormat=n.base64.encode}function v(){y.apply(this,arguments)}function g(){s.apply(this,arguments);this.toType=function(e){if(typeof e==="boolean")return e;if(e===null||e===undefined)return null;return e==="true"}}s.shapes={StructureShape:c,ListShape:l,MapShape:f,StringShape:h,BooleanShape:g,Base64Shape:v};t.exports=s},{"../util":112,"./collection":64}],64:[function(e,t,r){var i=e("../util").memoizedProperty;function n(e,t,r,n){i(this,n(e),function(){return r(e,t)})}function a(e,t,r,i){i=i||String;var a=this;for(var o in e){if(Object.prototype.hasOwnProperty.call(e,o)){n.call(a,o,e[o],r,i)}}}t.exports=a},{"../util":112}],71:[function(e,t,r){var i=e("../util");var n=e("../json/builder");var a=e("../json/parser");function o(e){var t=e.httpRequest;var r=e.service.api;var i=r.targetPrefix+"."+r.operations[e.operation].name;var a=r.jsonVersion||"1.0";var o=r.operations[e.operation].input;var s=new n;if(a===1)a="1.0";t.body=s.build(e.params||{},o);t.headers["Content-Type"]="application/x-amz-json-"+a;t.headers["X-Amz-Target"]=i}function s(e){var t={};var r=e.httpResponse;t.code=r.headers["x-amzn-errortype"]||"UnknownError";if(typeof t.code==="string"){t.code=t.code.split(":")[0]}if(r.body.length>0){try{var n=JSON.parse(r.body.toString());if(n.__type||n.code){t.code=(n.__type||n.code).split("#").pop()}if(t.code==="RequestEntityTooLarge"){t.message="Request body must be less than 1 MB"}else{t.message=n.message||n.Message||null}}catch(n){t.statusCode=r.statusCode;t.message=r.statusMessage}}else{t.statusCode=r.statusCode;t.message=r.statusCode.toString()}e.error=i.error(new Error,t)}function u(e){var t=e.httpResponse.body.toString()||"{}";if(e.request.service.config.convertResponseTypes===false){e.data=JSON.parse(t)}else{var r=e.request.service.api.operations[e.request.operation];var i=r.output||{};var n=new a;e.data=n.parse(t,i)}}t.exports={buildRequest:o,extractError:s,extractData:u}},{"../json/builder":61,"../json/parser":62,"../util":112}],62:[function(e,t,r){var i=e("../util");function n(){}n.prototype.parse=function(e,t){return a(JSON.parse(e),t)};function a(e,t){if(!t||e===undefined)return undefined;switch(t.type){case"structure":return o(e,t);case"map":return u(e,t);case"list":return s(e,t);default:return c(e,t)}}function o(e,t){if(e==null)return undefined;var r={};var n=t.members;i.each(n,function(t,i){var n=i.isLocationName?i.name:t;if(Object.prototype.hasOwnProperty.call(e,n)){var o=e[n];var s=a(o,i);if(s!==undefined)r[t]=s}});return r}function s(e,t){if(e==null)return undefined;var r=[];i.arrayEach(e,function(e){var i=a(e,t.member);if(i===undefined)r.push(null);else r.push(i)});return r}function u(e,t){if(e==null)return undefined;var r={};i.each(e,function(e,i){var n=a(i,t.value);if(n===undefined)r[e]=null;else r[e]=n});return r}function c(e,t){return t.toType(e)}t.exports=n},{"../util":112}],61:[function(e,t,r){var i=e("../util");function n(){}n.prototype.build=function(e,t){return JSON.stringify(a(e,t))};function a(e,t){if(!t||e===undefined||e===null)return undefined;switch(t.type){case"structure":return o(e,t);case"map":return u(e,t);case"list":return s(e,t);default:return c(e,t)}}function o(e,t){var r={};i.each(e,function(e,i){var n=t.members[e];if(n){if(n.location!=="body")return;var o=n.isLocationName?n.name:e;var s=a(i,n);if(s!==undefined)r[o]=s}});return r}function s(e,t){var r=[];i.arrayEach(e,function(e){var i=a(e,t.member);if(i!==undefined)r.push(i)});return r}function u(e,t){var r={};i.each(e,function(e,i){var n=a(i,t.value);if(n!==undefined)r[e]=n});return r}function c(e,t){return t.toWireFormat(e)}t.exports=n},{"../util":112}],112:[function(e,t,r){(function(r,i){var n;var a={environment:"nodejs",engine:function e(){if(a.isBrowser()&&typeof navigator!=="undefined"){return navigator.userAgent}else{var e=r.platform+"/"+r.version;if(r.env.AWS_EXECUTION_ENV){e+=" exec-env/"+r.env.AWS_EXECUTION_ENV}return e}},userAgent:function t(){var r=a.environment;var i="aws-sdk-"+r+"/"+e("./core").VERSION;if(r==="nodejs")i+=" "+a.engine();return i},isBrowser:function e(){return r&&r.browser},isNode:function e(){return!a.isBrowser()},uriEscape:function e(t){var r=encodeURIComponent(t);r=r.replace(/[^A-Za-z0-9_.~\-%]+/g,escape);r=r.replace(/[*]/g,function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()});return r},uriEscapePath:function e(t){var r=[];a.arrayEach(t.split("/"),function(e){r.push(a.uriEscape(e))});return r.join("/")},urlParse:function e(t){return a.url.parse(t)},urlFormat:function e(t){return a.url.format(t)},queryStringParse:function e(t){return a.querystring.parse(t)},queryParamsToString:function e(t){var r=[];var i=a.uriEscape;var n=Object.keys(t).sort();a.arrayEach(n,function(e){var n=t[e];var o=i(e);var s=o+"=";if(Array.isArray(n)){var u=[];a.arrayEach(n,function(e){u.push(i(e))});s=o+"="+u.sort().join("&"+o+"=")}else if(n!==undefined&&n!==null){s=o+"="+i(n)}r.push(s)});return r.join("&")},readFileSync:function t(r){if(a.isBrowser())return null;return e("fs").readFileSync(r,"utf-8")},base64:{encode:function e(t){if(typeof t==="number"){throw a.error(new Error("Cannot base64 encode number "+t))}if(t===null||typeof t==="undefined"){return t}var r=typeof a.Buffer.from==="function"&&a.Buffer.from!==Uint8Array.from?a.Buffer.from(t):new a.Buffer(t);return r.toString("base64")},decode:function e(t){if(typeof t==="number"){throw a.error(new Error("Cannot base64 decode number "+t))}if(t===null||typeof t==="undefined"){return t}return typeof a.Buffer.from==="function"&&a.Buffer.from!==Uint8Array.from?a.Buffer.from(t,"base64"):new a.Buffer(t,"base64")}},buffer:{toStream:function e(t){if(!a.Buffer.isBuffer(t))t=new a.Buffer(t);var r=new a.stream.Readable;var i=0;r._read=function(e){if(i>=t.length)return r.push(null);var n=i+e;if(n>t.length)n=t.length;r.push(t.slice(i,n));i=n};return r},concat:function(e){var t=0,r=0,i=null,n;for(n=0;n<e.length;n++){t+=e[n].length}i=new a.Buffer(t);for(n=0;n<e.length;n++){e[n].copy(i,r);r+=e[n].length}return i}},string:{byteLength:function t(r){if(r===null||r===undefined)return 0;if(typeof r==="string")r=new a.Buffer(r);if(typeof r.byteLength==="number"){return r.byteLength}else if(typeof r.length==="number"){return r.length}else if(typeof r.size==="number"){return r.size}else if(typeof r.path==="string"){return e("fs").lstatSync(r.path).size}else{throw a.error(new Error("Cannot determine length of "+r),{object:r})}},upperFirst:function e(t){return t[0].toUpperCase()+t.substr(1)},lowerFirst:function e(t){return t[0].toLowerCase()+t.substr(1)}},ini:{parse:function e(t){var r,i={};a.arrayEach(t.split(/\r?\n/),function(e){e=e.split(/(^|\s)[;#]/)[0];var t=e.match(/^\s*\[([^\[\]]+)\]\s*$/);if(t){r=t[1]}else if(r){var n=e.match(/^\s*(.+?)\s*=\s*(.+?)\s*$/);if(n){i[r]=i[r]||{};i[r][n[1]]=n[2]}}});return i}},fn:{noop:function(){},makeAsync:function e(t,r){if(r&&r<=t.length){return t}return function(){var e=Array.prototype.slice.call(arguments,0);var r=e.pop();var i=t.apply(null,e);r(i)}}},date:{getDate:function t(){if(!n)n=e("./core");if(n.config.systemClockOffset){return new Date((new Date).getTime()+n.config.systemClockOffset)}else{return new Date}},iso8601:function e(t){if(t===undefined){t=a.date.getDate()}return t.toISOString().replace(/\.\d{3}Z$/,"Z")},rfc822:function e(t){if(t===undefined){t=a.date.getDate()}return t.toUTCString()},unixTimestamp:function e(t){if(t===undefined){t=a.date.getDate()}return t.getTime()/1e3},from:function e(t){if(typeof t==="number"){return new Date(t*1e3)}else{return new Date(t)}},format:function e(t,r){if(!r)r="iso8601";return a.date[r](a.date.from(t))},parseTimestamp:function e(t){if(typeof t==="number"){return new Date(t*1e3)}else if(t.match(/^\d+$/)){return new Date(t*1e3)}else if(t.match(/^\d{4}/)){return new Date(t)}else if(t.match(/^\w{3},/)){return new Date(t)}else{throw a.error(new Error("unhandled timestamp format: "+t),{code:"TimestampParserError"})}}},crypto:{crc32Table:[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918e3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117],crc32:function e(t){var r=a.crypto.crc32Table;var i=0^-1;if(typeof t==="string"){t=new a.Buffer(t)}for(var n=0;n<t.length;n++){var o=t.readUInt8(n);i=i>>>8^r[(i^o)&255]}return(i^-1)>>>0},hmac:function e(t,r,i,n){if(!i)i="binary";if(i==="buffer"){i=undefined}if(!n)n="sha256";if(typeof r==="string")r=new a.Buffer(r);return a.crypto.lib.createHmac(n,t).update(r).digest(i)},md5:function e(t,r,i){return a.crypto.hash("md5",t,r,i)},sha256:function e(t,r,i){return a.crypto.hash("sha256",t,r,i)},hash:function(e,t,r,i){var n=a.crypto.createHash(e);if(!r){r="binary"}if(r==="buffer"){r=undefined}if(typeof t==="string")t=new a.Buffer(t);var o=a.arraySliceFn(t);var s=a.Buffer.isBuffer(t);if(a.isBrowser()&&typeof ArrayBuffer!=="undefined"&&t&&t.buffer instanceof ArrayBuffer)s=true;if(i&&typeof t==="object"&&typeof t.on==="function"&&!s){t.on("data",function(e){n.update(e)});t.on("error",function(e){i(e)});t.on("end",function(){i(null,n.digest(r))})}else if(i&&o&&!s&&typeof FileReader!=="undefined"){var u=0,c=1024*512;var l=new FileReader;l.onerror=function(){i(new Error("Failed to read data."))};l.onload=function(){var e=new a.Buffer(new Uint8Array(l.result));n.update(e);u+=e.length;l._continueReading()};l._continueReading=function(){if(u>=t.size){i(null,n.digest(r));return}var e=u+c;if(e>t.size)e=t.size;l.readAsArrayBuffer(o.call(t,u,e))};l._continueReading()}else{if(a.isBrowser()&&typeof t==="object"&&!s){t=new a.Buffer(new Uint8Array(t))}var f=n.update(t).digest(r);if(i)i(null,f);return f}},toHex:function e(t){var r=[];for(var i=0;i<t.length;i++){r.push(("0"+t.charCodeAt(i).toString(16)).substr(-2,2))}return r.join("")},createHash:function e(t){return a.crypto.lib.createHash(t)}},abort:{},each:function e(t,r){for(var i in t){if(Object.prototype.hasOwnProperty.call(t,i)){var n=r.call(this,i,t[i]);if(n===a.abort)break}}},arrayEach:function e(t,r){for(var i in t){if(Object.prototype.hasOwnProperty.call(t,i)){var n=r.call(this,t[i],parseInt(i,10));if(n===a.abort)break}}},update:function e(t,r){a.each(r,function e(r,i){t[r]=i});return t},merge:function e(t,r){return a.update(a.copy(t),r)},copy:function e(t){if(t===null||t===undefined)return t;var r={};for(var i in t){r[i]=t[i]}return r},isEmpty:function e(t){for(var r in t){if(Object.prototype.hasOwnProperty.call(t,r)){return false}}return true},arraySliceFn:function e(t){var r=t.slice||t.webkitSlice||t.mozSlice;return typeof r==="function"?r:null},isType:function e(t,r){if(typeof r==="function")r=a.typeName(r);return Object.prototype.toString.call(t)==="[object "+r+"]"},typeName:function e(t){if(Object.prototype.hasOwnProperty.call(t,"name"))return t.name;var r=t.toString();var i=r.match(/^\s*function (.+)\(/);return i?i[1]:r},error:function e(t,r){var i=null;if(typeof t.message==="string"&&t.message!==""){if(typeof r==="string"||r&&r.message){i=a.copy(t);i.message=t.message}}t.message=t.message||null;if(typeof r==="string"){t.message=r}else if(typeof r==="object"&&r!==null){a.update(t,r);if(r.message)t.message=r.message;if(r.code||r.name)t.code=r.code||r.name;if(r.stack)t.stack=r.stack}if(typeof Object.defineProperty==="function"){Object.defineProperty(t,"name",{writable:true,enumerable:false});Object.defineProperty(t,"message",{enumerable:true})}t.name=r&&r.name||t.name||t.code||"Error";t.time=new Date;if(i)t.originalError=i;return t},inherit:function e(t,r){var i=null;if(r===undefined){r=t;t=Object;i={}}else{var n=function e(){};n.prototype=t.prototype;i=new n}if(r.constructor===Object){r.constructor=function(){if(t!==Object){return t.apply(this,arguments)}}}r.constructor.prototype=i;a.update(r.constructor.prototype,r);r.constructor.__super__=t;return r.constructor},mixin:function e(){var t=arguments[0];for(var r=1;r<arguments.length;r++){for(var i in arguments[r].prototype){var n=arguments[r].prototype[i];if(i!=="constructor"){t.prototype[i]=n}}}return t},hideProperties:function e(t,r){if(typeof Object.defineProperty!=="function")return;a.arrayEach(r,function(e){Object.defineProperty(t,e,{enumerable:false,writable:true,configurable:true})})},property:function e(t,r,i,n,a){var o={configurable:true,enumerable:n!==undefined?n:true};if(typeof i==="function"&&!a){o.get=i}else{o.value=i;o.writable=true}Object.defineProperty(t,r,o)},memoizedProperty:function e(t,r,i,n){var o=null;a.property(t,r,function(){if(o===null){o=i()}return o},n)},hoistPayloadMember:function e(t){var r=t.request;var i=r.operation;var n=r.service.api.operations[i];var o=n.output;if(o.payload&&!n.hasEventOutput){var s=o.members[o.payload];var u=t.data[o.payload];if(s.type==="structure"){a.each(u,function(e,r){a.property(t.data,e,r,false)})}}},computeSha256:function t(r,i){if(a.isNode()){var n=a.stream.Stream;var o=e("fs");if(r instanceof n){if(typeof r.path==="string"){var s={};if(typeof r.start==="number"){s.start=r.start}if(typeof r.end==="number"){s.end=r.end}r=o.createReadStream(r.path,s)}else{return i(new Error("Non-file stream objects are "+"not supported with SigV4"))}}}a.crypto.sha256(r,"hex",function(e,t){if(e)i(e);else i(null,t)})},isClockSkewed:function e(t){if(t){a.property(n.config,"isClockSkewed",Math.abs((new Date).getTime()-t)>=3e5,false);return n.config.isClockSkewed}},applyClockOffset:function e(t){if(t)n.config.systemClockOffset=t-(new Date).getTime()},extractRequestId:function e(t){var r=t.httpResponse.headers["x-amz-request-id"]||t.httpResponse.headers["x-amzn-requestid"];if(!r&&t.data&&t.data.ResponseMetadata){r=t.data.ResponseMetadata.RequestId}if(r){t.requestId=r}if(t.error){t.error.requestId=r}},addPromises:function e(t,r){var i=false;if(r===undefined&&n&&n.config){r=n.config.getPromisesDependency()}if(r===undefined&&typeof Promise!=="undefined"){r=Promise}if(typeof r!=="function")i=true;if(!Array.isArray(t))t=[t];for(var a=0;a<t.length;a++){var o=t[a];if(i){if(o.deletePromisesFromClass){o.deletePromisesFromClass()}}else if(o.addPromisesToClass){o.addPromisesToClass(r)}}},promisifyMethod:function e(t,r){return function e(){var i=this;return new r(function(e,r){i[t](function(t,i){if(t){r(t)}else{e(i)}})})}},isDualstackAvailable:function t(r){if(!r)return false;var i=e("../apis/metadata.json");if(typeof r!=="string")r=r.serviceIdentifier;if(typeof r!=="string"||!i.hasOwnProperty(r))return false;return!!i[r].dualstackAvailable},calculateRetryDelay:function e(t,r){if(!r)r={};var i=r.customBackoff||null;if(typeof i==="function"){return i(t)}var n=typeof r.base==="number"?r.base:100;var a=Math.random()*(Math.pow(2,t)*n);return a},handleRequestWithRetries:function e(t,r,i){if(!r)r={};var o=n.HttpClient.getInstance();var s=r.httpOptions||{};var u=0;var c=function(e){var t=r.maxRetries||0;if(e&&e.code==="TimeoutError")e.retryable=true;if(e&&e.retryable&&u<t){u++;var n=a.calculateRetryDelay(u,r.retryDelayOptions);setTimeout(l,n+(e.retryAfter||0))}else{i(e)}};var l=function(){var e="";o.handleRequest(t,s,function(t){t.on("data",function(t){e+=t.toString()});t.on("end",function(){var r=t.statusCode;if(r<300){i(null,e)}else{var n=parseInt(t.headers["retry-after"],10)*1e3||0;var o=a.error(new Error,{retryable:r>=500||r===429});if(n&&o.retryable)o.retryAfter=n;c(o)}})},c)};n.util.defer(l)},uuid:{v4:function t(){return e("uuid").v4()}},convertPayloadToString:function e(t){var r=t.request;var i=r.operation;var n=r.service.api.operations[i].output||{};if(n.payload&&t.data[n.payload]){t.data[n.payload]=t.data[n.payload].toString()}},defer:function e(t){if(typeof r==="object"&&typeof r.nextTick==="function"){r.nextTick(t)}else if(typeof i==="function"){i(t)}else{setTimeout(t,0)}},defaultProfile:"default",configOptInEnv:"AWS_SDK_LOAD_CONFIG",sharedCredentialsFileEnv:"AWS_SHARED_CREDENTIALS_FILE",sharedConfigFileEnv:"AWS_CONFIG_FILE",imdsDisabledEnv:"AWS_EC2_METADATA_DISABLED"};t.exports=a}).call(this,e("_process"),e("timers").setImmediate)},{"../apis/metadata.json":26,"./core":38,_process:9,fs:2,timers:17,uuid:21}],37:[function(e,t,r){var i=e("./core");e("./credentials");e("./credentials/credential_provider_chain");var n;i.Config=i.util.inherit({constructor:function e(t){if(t===undefined)t={};t=this.extractCredentials(t);i.util.each.call(this,this.keys,function(e,r){this.set(e,t[e],r)})},getCredentials:function e(t){var r=this;function n(e){t(e,e?null:r.credentials)}function a(e,t){return new i.util.error(t||new Error,{code:"CredentialsError",message:e,name:"CredentialsError"})}function o(){r.credentials.get(function(e){if(e){var t="Could not load credentials from "+r.credentials.constructor.name;e=a(t,e)}n(e)})}function s(){var e=null;if(!r.credentials.accessKeyId||!r.credentials.secretAccessKey){e=a("Missing credentials")}n(e)}if(r.credentials){if(typeof r.credentials.get==="function"){o()}else{s()}}else if(r.credentialProvider){r.credentialProvider.resolve(function(e,t){if(e){e=a("Could not load credentials from any providers",e)}r.credentials=t;n(e)})}else{n(a("No credentials to load"))}},update:function e(t,r){r=r||false;t=this.extractCredentials(t);i.util.each.call(this,t,function(e,t){if(r||Object.prototype.hasOwnProperty.call(this.keys,e)||i.Service.hasService(e)){this.set(e,t)}})},loadFromPath:function e(t){this.clear();var r=JSON.parse(i.util.readFileSync(t));var n=new i.FileSystemCredentials(t);var a=new i.CredentialProviderChain;a.providers.unshift(n);a.resolve(function(e,t){if(e)throw e;else r.credentials=t});this.constructor(r);return this},clear:function e(){i.util.each.call(this,this.keys,function(e){delete this[e]});this.set("credentials",undefined);this.set("credentialProvider",undefined)},set:function e(t,r,n){if(r===undefined){if(n===undefined){n=this.keys[t]}if(typeof n==="function"){this[t]=n.call(this)}else{this[t]=n}}else if(t==="httpOptions"&&this[t]){this[t]=i.util.merge(this[t],r)}else{this[t]=r}},keys:{credentials:null,credentialProvider:null,region:null,logger:null,apiVersions:{},apiVersion:null,endpoint:undefined,httpOptions:{timeout:12e4},maxRetries:undefined,maxRedirects:10,paramValidation:true,sslEnabled:true,s3ForcePathStyle:false,s3BucketEndpoint:false,s3DisableBodySigning:true,computeChecksums:true,convertResponseTypes:true,correctClockSkew:false,customUserAgent:null,dynamoDbCrc32:true,systemClockOffset:0,signatureVersion:null,signatureCache:true,retryDelayOptions:{},useAccelerateEndpoint:false},extractCredentials:function e(t){if(t.accessKeyId&&t.secretAccessKey){t=i.util.copy(t);t.credentials=new i.Credentials(t)}return t},setPromisesDependency:function e(t){n=t;if(t===null&&typeof Promise==="function"){n=Promise}var r=[i.Request,i.Credentials,i.CredentialProviderChain];if(i.S3&&i.S3.ManagedUpload)r.push(i.S3.ManagedUpload);i.util.addPromises(r,n)},getPromisesDependency:function e(){return n}});i.config=new i.Config},{"./core":38,"./credentials":39,"./credentials/credential_provider_chain":41}],41:[function(e,t,r){var i=e("../core");i.CredentialProviderChain=i.util.inherit(i.Credentials,{constructor:function e(t){if(t){this.providers=t}else{this.providers=i.CredentialProviderChain.defaultProviders.slice(0)}},resolve:function e(t){if(this.providers.length===0){t(new Error("No providers"));return this}var r=0;var i=this.providers.slice(0);function n(e,a){if(!e&&a||r===i.length){t(e,a);return}var o=i[r++];if(typeof o==="function"){a=o.call()}else{a=o}if(a.get){a.get(function(e){n(e,e?null:a)})}else{n(null,a)}}n();return this}});i.CredentialProviderChain.defaultProviders=[];i.CredentialProviderChain.addPromisesToClass=function e(t){this.prototype.resolvePromise=i.util.promisifyMethod("resolve",t)};i.CredentialProviderChain.deletePromisesFromClass=function e(){delete this.prototype.resolvePromise};i.util.addPromises(i.CredentialProviderChain)},{"../core":38}],39:[function(e,t,r){var i=e("./core");i.Credentials=i.util.inherit({constructor:function e(){i.util.hideProperties(this,["secretAccessKey"]);this.expired=false;this.expireTime=null;if(arguments.length===1&&typeof arguments[0]==="object"){var t=arguments[0].credentials||arguments[0];this.accessKeyId=t.accessKeyId;this.secretAccessKey=t.secretAccessKey;this.sessionToken=t.sessionToken}else{this.accessKeyId=arguments[0];this.secretAccessKey=arguments[1];this.sessionToken=arguments[2]}},expiryWindow:15,needsRefresh:function e(){var t=i.util.date.getDate().getTime();var r=new Date(t+this.expiryWindow*1e3);if(this.expireTime&&r>this.expireTime){return true}else{return this.expired||!this.accessKeyId||!this.secretAccessKey}},get:function e(t){var r=this;if(this.needsRefresh()){this.refresh(function(e){if(!e)r.expired=false;if(t)t(e)})}else if(t){t()}},refresh:function e(t){this.expired=false;t()}});i.Credentials.addPromisesToClass=function e(t){this.prototype.getPromise=i.util.promisifyMethod("get",t);this.prototype.refreshPromise=i.util.promisifyMethod("refresh",t)};i.Credentials.deletePromisesFromClass=function e(){delete this.prototype.getPromise;delete this.prototype.refreshPromise};i.util.addPromises(i.Credentials)},{"./core":38}],27:[function(e,t,r){function i(e,t){if(!i.services.hasOwnProperty(e)){throw new Error("InvalidService: Failed to load api for "+e)}return i.services[e][t]}i.services={};t.exports=i},{}],26:[function(e,t,r){t.exports={acm:{name:"ACM",cors:true},apigateway:{name:"APIGateway",cors:true},applicationautoscaling:{prefix:"application-autoscaling",name:"ApplicationAutoScaling",cors:true},appstream:{name:"AppStream"},autoscaling:{name:"AutoScaling",cors:true},batch:{name:"Batch"},budgets:{name:"Budgets"},clouddirectory:{name:"CloudDirectory",versions:["2016-05-10*"]},cloudformation:{name:"CloudFormation",cors:true},cloudfront:{name:"CloudFront",versions:["2013-05-12*","2013-11-11*","2014-05-31*","2014-10-21*","2014-11-06*","2015-04-17*","2015-07-27*","2015-09-17*","2016-01-13*","2016-01-28*","2016-08-01*","2016-08-20*","2016-09-07*","2016-09-29*","2016-11-25*","2017-03-25*","2017-10-30*"],cors:true},cloudhsm:{name:"CloudHSM",cors:true},cloudsearch:{name:"CloudSearch"},cloudsearchdomain:{name:"CloudSearchDomain"},cloudtrail:{name:"CloudTrail",cors:true},cloudwatch:{prefix:"monitoring",name:"CloudWatch",cors:true},cloudwatchevents:{prefix:"events",name:"CloudWatchEvents",versions:["2014-02-03*"],cors:true},cloudwatchlogs:{prefix:"logs",name:"CloudWatchLogs",cors:true},codebuild:{name:"CodeBuild",cors:true},codecommit:{name:"CodeCommit",cors:true},codedeploy:{name:"CodeDeploy",cors:true},codepipeline:{name:"CodePipeline",cors:true},cognitoidentity:{prefix:"cognito-identity",name:"CognitoIdentity",cors:true},cognitoidentityserviceprovider:{prefix:"cognito-idp",name:"CognitoIdentityServiceProvider",cors:true},cognitosync:{prefix:"cognito-sync",name:"CognitoSync",cors:true},configservice:{prefix:"config",name:"ConfigService",cors:true},cur:{name:"CUR",cors:true},datapipeline:{name:"DataPipeline"},devicefarm:{name:"DeviceFarm",cors:true},directconnect:{name:"DirectConnect",cors:true},directoryservice:{prefix:"ds",name:"DirectoryService"},discovery:{name:"Discovery"},dms:{name:"DMS"},dynamodb:{name:"DynamoDB",cors:true},dynamodbstreams:{prefix:"streams.dynamodb",name:"DynamoDBStreams",cors:true},ec2:{name:"EC2",versions:["2013-06-15*","2013-10-15*","2014-02-01*","2014-05-01*","2014-06-15*","2014-09-01*","2014-10-01*","2015-03-01*","2015-04-15*","2015-10-01*","2016-04-01*","2016-09-15*"],cors:true},ecr:{name:"ECR",cors:true},ecs:{name:"ECS",cors:true},efs:{prefix:"elasticfilesystem",name:"EFS",cors:true},elasticache:{name:"ElastiCache",versions:["2012-11-15*","2014-03-24*","2014-07-15*","2014-09-30*"],cors:true},elasticbeanstalk:{name:"ElasticBeanstalk",cors:true},elb:{prefix:"elasticloadbalancing",name:"ELB",cors:true},elbv2:{prefix:"elasticloadbalancingv2",name:"ELBv2",cors:true},emr:{prefix:"elasticmapreduce",name:"EMR",cors:true},es:{name:"ES"},elastictranscoder:{name:"ElasticTranscoder",cors:true},firehose:{name:"Firehose",cors:true},gamelift:{name:"GameLift",cors:true},glacier:{name:"Glacier"},health:{name:"Health"},iam:{name:"IAM"},importexport:{name:"ImportExport"},inspector:{name:"Inspector",versions:["2015-08-18*"],cors:true},iot:{name:"Iot",cors:true},iotdata:{prefix:"iot-data",name:"IotData",cors:true},kinesis:{name:"Kinesis",cors:true},kinesisanalytics:{name:"KinesisAnalytics"},kms:{name:"KMS",cors:true},lambda:{name:"Lambda",cors:true},lexruntime:{prefix:"runtime.lex",name:"LexRuntime",cors:true},lightsail:{name:"Lightsail"},machinelearning:{name:"MachineLearning",cors:true},marketplacecommerceanalytics:{name:"MarketplaceCommerceAnalytics",cors:true},marketplacemetering:{prefix:"meteringmarketplace",name:"MarketplaceMetering"},mturk:{prefix:"mturk-requester",name:"MTurk",cors:true},mobileanalytics:{name:"MobileAnalytics",cors:true},opsworks:{name:"OpsWorks",cors:true},opsworkscm:{name:"OpsWorksCM"},organizations:{name:"Organizations"},pinpoint:{name:"Pinpoint"},polly:{name:"Polly",cors:true},rds:{name:"RDS",versions:["2014-09-01*"],cors:true},redshift:{name:"Redshift",cors:true},rekognition:{name:"Rekognition",cors:true},resourcegroupstaggingapi:{name:"ResourceGroupsTaggingAPI"},route53:{name:"Route53",cors:true},route53domains:{name:"Route53Domains",cors:true},s3:{name:"S3",dualstackAvailable:true,cors:true},servicecatalog:{name:"ServiceCatalog",cors:true},ses:{prefix:"email",name:"SES",cors:true},shield:{name:"Shield"},simpledb:{prefix:"sdb",name:"SimpleDB"},sms:{name:"SMS"},snowball:{name:"Snowball"},sns:{name:"SNS",cors:true},sqs:{name:"SQS",cors:true},ssm:{name:"SSM",cors:true},storagegateway:{name:"StorageGateway",cors:true},stepfunctions:{prefix:"states",name:"StepFunctions"},sts:{name:"STS",cors:true},support:{name:"Support"},swf:{name:"SWF"},xray:{name:"XRay"},waf:{name:"WAF",cors:true},wafregional:{prefix:"waf-regional",name:"WAFRegional"},workdocs:{name:"WorkDocs",cors:true},workspaces:{name:"WorkSpaces"},codestar:{name:"CodeStar"},lexmodelbuildingservice:{prefix:"lex-models",name:"LexModelBuildingService",cors:true},marketplaceentitlementservice:{prefix:"entitlement.marketplace",name:"MarketplaceEntitlementService"},athena:{name:"Athena"},greengrass:{name:"Greengrass"},dax:{name:"DAX"},migrationhub:{prefix:"AWSMigrationHub",name:"MigrationHub"},cloudhsmv2:{name:"CloudHSMV2"},glue:{name:"Glue"},mobile:{name:"Mobile"},pricing:{name:"Pricing"},costexplorer:{prefix:"ce",name:"CostExplorer"},mediaconvert:{name:"MediaConvert"},medialive:{name:"MediaLive"},mediapackage:{name:"MediaPackage"},mediastore:{name:"MediaStore"},mediastoredata:{prefix:"mediastore-data",name:"MediaStoreData"},appsync:{name:"AppSync"},guardduty:{name:"GuardDuty"},mq:{name:"MQ"},comprehend:{name:"Comprehend"},iotjobsdataplane:{prefix:"iot-jobs-data",name:"IoTJobsDataPlane"},kinesisvideoarchivedmedia:{prefix:"kinesis-video-archived-media",name:"KinesisVideoArchivedMedia",cors:true},kinesisvideomedia:{prefix:"kinesis-video-media",name:"KinesisVideoMedia"},kinesisvideo:{name:"KinesisVideo",cors:true},sagemakerruntime:{prefix:"runtime.sagemaker",name:"SageMakerRuntime"},sagemaker:{name:"SageMaker"},translate:{name:"Translate",cors:true},resourcegroups:{prefix:"resource-groups",name:"ResourceGroups"},alexaforbusiness:{name:"AlexaForBusiness"},cloud9:{name:"Cloud9"},serverlessapplicationrepository:{prefix:"serverlessrepo",name:"ServerlessApplicationRepository"},servicediscovery:{name:"ServiceDiscovery"},workmail:{name:"WorkMail"},autoscalingplans:{prefix:"autoscaling-plans",name:"AutoScalingPlans"},transcribeservice:{prefix:"transcribe",name:"TranscribeService"},connect:{name:"Connect"},acmpca:{prefix:"acm-pca",name:"ACMPCA"},fms:{name:"FMS"},secretsmanager:{name:"SecretsManager",cors:true},iotanalytics:{name:"IoTAnalytics"},iot1clickdevicesservice:{prefix:"iot1click-devices",name:"IoT1ClickDevicesService"},iot1clickprojects:{prefix:"iot1click-projects",name:"IoT1ClickProjects"},pi:{name:"PI"},neptune:{name:"Neptune"},mediatailor:{name:"MediaTailor"},eks:{name:"EKS"},macie:{name:"Macie"},dlm:{name:"DLM"}}},{}],21:[function(e,t,r){var i=e("./v1");var n=e("./v4");var a=n;a.v1=i;a.v4=n;t.exports=a},{"./v1":24,"./v4":25}],25:[function(e,t,r){var i=e("./lib/rng");var n=e("./lib/bytesToUuid");function a(e,t,r){var a=t&&r||0;if(typeof e=="string"){t=e=="binary"?new Array(16):null;e=null}e=e||{};var o=e.random||(e.rng||i)();o[6]=o[6]&15|64;o[8]=o[8]&63|128;if(t){for(var s=0;s<16;++s){t[a+s]=o[s]}}return t||n(o)}t.exports=a},{"./lib/bytesToUuid":22,"./lib/rng":23}],24:[function(e,t,r){var i=e("./lib/rng");var n=e("./lib/bytesToUuid");var a=i();var o=[a[0]|1,a[1],a[2],a[3],a[4],a[5]];var s=(a[6]<<8|a[7])&16383;var u=0,c=0;function l(e,t,r){var i=t&&r||0;var a=t||[];e=e||{};var l=e.clockseq!==undefined?e.clockseq:s;var f=e.msecs!==undefined?e.msecs:(new Date).getTime();var p=e.nsecs!==undefined?e.nsecs:c+1;var h=f-u+(p-c)/1e4;if(h<0&&e.clockseq===undefined){l=l+1&16383}if((h<0||f>u)&&e.nsecs===undefined){p=0}if(p>=1e4){throw new Error("uuid.v1(): Can't create more than 10M uuids/sec")}u=f;c=p;s=l;f+=122192928e5;var d=((f&268435455)*1e4+p)%4294967296;a[i++]=d>>>24&255;a[i++]=d>>>16&255;a[i++]=d>>>8&255;a[i++]=d&255;var m=f/4294967296*1e4&268435455;a[i++]=m>>>8&255;a[i++]=m&255;a[i++]=m>>>24&15|16;a[i++]=m>>>16&255;a[i++]=l>>>8|128;a[i++]=l&255;var y=e.node||o;for(var v=0;v<6;++v){a[i+v]=y[v]}return t?t:n(a)}t.exports=l},{"./lib/bytesToUuid":22,"./lib/rng":23}],23:[function(e,t,r){(function(e){var r;var i=e.crypto||e.msCrypto;if(i&&i.getRandomValues){var n=new Uint8Array(16);r=function e(){i.getRandomValues(n);return n}}if(!r){var a=new Array(16);r=function(){for(var e=0,t;e<16;e++){if((e&3)===0)t=Math.random()*4294967296;a[e]=t>>>((e&3)<<3)&255}return a}}t.exports=r}).call(this,typeof global!=="undefined"?global:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})},{}],22:[function(e,t,r){var i=[];for(var n=0;n<256;++n){i[n]=(n+256).toString(16).substr(1)}function a(e,t){var r=t||0;var n=i;return n[e[r++]]+n[e[r++]]+n[e[r++]]+n[e[r++]]+"-"+n[e[r++]]+n[e[r++]]+"-"+n[e[r++]]+n[e[r++]]+"-"+n[e[r++]]+n[e[r++]]+"-"+n[e[r++]]+n[e[r++]]+n[e[r++]]+n[e[r++]]+n[e[r++]]+n[e[r++]]}t.exports=a},{}],20:[function(e,t,r){(function(t,i){var n=/%[sdj%]/g;r.format=function(e){if(!C(e)){var t=[];for(var r=0;r<arguments.length;r++){t.push(s(arguments[r]))}return t.join(" ")}var r=1;var i=arguments;var a=i.length;var o=String(e).replace(n,function(e){if(e==="%%")return"%";if(r>=a)return e;switch(e){case"%s":return String(i[r++]);case"%d":return Number(i[r++]);case"%j":try{return JSON.stringify(i[r++])}catch(e){return"[Circular]"}default:return e}});for(var u=i[r];r<a;u=i[++r]){if(b(u)||!N(u)){o+=" "+u}else{o+=" "+s(u)}}return o};r.deprecate=function(e,n){if(w(i.process)){return function(){return r.deprecate(e,n).apply(this,arguments)}}if(t.noDeprecation===true){return e}var a=false;function o(){if(!a){if(t.throwDeprecation){throw new Error(n)}else if(t.traceDeprecation){console.trace(n)}else{console.error(n)}a=true}return e.apply(this,arguments)}return o};var a={};var o;r.debuglog=function(e){if(w(o))o=t.env.NODE_DEBUG||"";e=e.toUpperCase();if(!a[e]){if(new RegExp("\\b"+e+"\\b","i").test(o)){var i=t.pid;a[e]=function(){var t=r.format.apply(r,arguments);console.error("%s %d: %s",e,i,t)}}else{a[e]=function(){}}}return a[e]};function s(e,t){var i={seen:[],stylize:c};if(arguments.length>=3)i.depth=arguments[2];if(arguments.length>=4)i.colors=arguments[3];if(g(t)){i.showHidden=t}else if(t){r._extend(i,t)}if(w(i.showHidden))i.showHidden=false;if(w(i.depth))i.depth=2;if(w(i.colors))i.colors=false;if(w(i.customInspect))i.customInspect=true;if(i.colors)i.stylize=u;return f(i,e,i.depth)}r.inspect=s;s.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]};s.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"};function u(e,t){var r=s.styles[t];if(r){return"["+s.colors[r][0]+"m"+e+"["+s.colors[r][1]+"m"}else{return e}}function c(e,t){return e}function l(e){var t={};e.forEach(function(e,r){t[e]=true});return t}function f(e,t,i){if(e.customInspect&&t&&_(t.inspect)&&t.inspect!==r.inspect&&!(t.constructor&&t.constructor.prototype===t)){var n=t.inspect(i,e);if(!C(n)){n=f(e,n,i)}return n}var a=p(e,t);if(a){return a}var o=Object.keys(t);var s=l(o);if(e.showHidden){o=Object.getOwnPropertyNames(t)}if(q(t)&&(o.indexOf("message")>=0||o.indexOf("description")>=0)){return h(t)}if(o.length===0){if(_(t)){var u=t.name?": "+t.name:"";return e.stylize("[Function"+u+"]","special")}if(x(t)){return e.stylize(RegExp.prototype.toString.call(t),"regexp")}if(R(t)){return e.stylize(Date.prototype.toString.call(t),"date")}if(q(t)){return h(t)}}var c="",g=false,b=["{","}"];if(v(t)){g=true;b=["[","]"]}if(_(t)){var S=t.name?": "+t.name:"";c=" [Function"+S+"]"}if(x(t)){c=" "+RegExp.prototype.toString.call(t)}if(R(t)){c=" "+Date.prototype.toUTCString.call(t)}if(q(t)){c=" "+h(t)}if(o.length===0&&(!g||t.length==0)){return b[0]+c+b[1]}if(i<0){if(x(t)){return e.stylize(RegExp.prototype.toString.call(t),"regexp")}else{return e.stylize("[Object]","special")}}e.seen.push(t);var k;if(g){k=d(e,t,i,s,o)}else{k=o.map(function(r){return m(e,t,i,s,r,g)})}e.seen.pop();return y(k,c,b)}function p(e,t){if(w(t))return e.stylize("undefined","undefined");if(C(t)){var r="'"+JSON.stringify(t).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return e.stylize(r,"string")}if(k(t))return e.stylize(""+t,"number");if(g(t))return e.stylize(""+t,"boolean");if(b(t))return e.stylize("null","null")}function h(e){return"["+Error.prototype.toString.call(e)+"]"}function d(e,t,r,i,n){var a=[];for(var o=0,s=t.length;o<s;++o){if(L(t,String(o))){a.push(m(e,t,r,i,String(o),true))}else{a.push("")}}n.forEach(function(n){if(!n.match(/^\d+$/)){a.push(m(e,t,r,i,n,true))}});return a}function m(e,t,r,i,n,a){var o,s,u;u=Object.getOwnPropertyDescriptor(t,n)||{value:t[n]};if(u.get){if(u.set){s=e.stylize("[Getter/Setter]","special")}else{s=e.stylize("[Getter]","special")}}else{if(u.set){s=e.stylize("[Setter]","special")}}if(!L(i,n)){o="["+n+"]"}if(!s){if(e.seen.indexOf(u.value)<0){if(b(r)){s=f(e,u.value,null)}else{s=f(e,u.value,r-1)}if(s.indexOf("\n")>-1){if(a){s=s.split("\n").map(function(e){return"  "+e}).join("\n").substr(2)}else{s="\n"+s.split("\n").map(function(e){return"   "+e}).join("\n")}}}else{s=e.stylize("[Circular]","special")}}if(w(o)){if(a&&n.match(/^\d+$/)){return s}o=JSON.stringify(""+n);if(o.match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)){o=o.substr(1,o.length-2);o=e.stylize(o,"name")}else{o=o.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'");o=e.stylize(o,"string")}}return o+": "+s}function y(e,t,r){var i=0;var n=e.reduce(function(e,t){i++;if(t.indexOf("\n")>=0)i++;return e+t.replace(/\u001b\[\d\d?m/g,"").length+1},0);if(n>60){return r[0]+(t===""?"":t+"\n ")+" "+e.join(",\n  ")+" "+r[1]}return r[0]+t+" "+e.join(", ")+" "+r[1]}function v(e){return Array.isArray(e)}r.isArray=v;function g(e){return typeof e==="boolean"}r.isBoolean=g;function b(e){return e===null}r.isNull=b;function S(e){return e==null}r.isNullOrUndefined=S;function k(e){return typeof e==="number"}r.isNumber=k;function C(e){return typeof e==="string"}r.isString=C;function E(e){return typeof e==="symbol"}r.isSymbol=E;function w(e){return e===void 0}r.isUndefined=w;function x(e){return N(e)&&T(e)==="[object RegExp]"}r.isRegExp=x;function N(e){return typeof e==="object"&&e!==null}r.isObject=N;function R(e){return N(e)&&T(e)==="[object Date]"}r.isDate=R;function q(e){return N(e)&&(T(e)==="[object Error]"||e instanceof Error)}r.isError=q;function _(e){return typeof e==="function"}r.isFunction=_;function B(e){return e===null||typeof e==="boolean"||typeof e==="number"||typeof e==="string"||typeof e==="symbol"||typeof e==="undefined"}r.isPrimitive=B;r.isBuffer=e("./support/isBuffer");function T(e){return Object.prototype.toString.call(e)}function A(e){return e<10?"0"+e.toString(10):e.toString(10)}var P=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function I(){var e=new Date;var t=[A(e.getHours()),A(e.getMinutes()),A(e.getSeconds())].join(":");return[e.getDate(),P[e.getMonth()],t].join(" ")}r.log=function(){console.log("%s - %s",I(),r.format.apply(r,arguments))};r.inherits=e("inherits");r._extend=function(e,t){if(!t||!N(t))return e;var r=Object.keys(t);var i=r.length;while(i--){e[r[i]]=t[r[i]]}return e};function L(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}).call(this,e("_process"),typeof global!=="undefined"?global:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})},{"./support/isBuffer":19,_process:9,inherits:6}],19:[function(e,t,r){t.exports=function e(t){return t&&typeof t==="object"&&typeof t.copy==="function"&&typeof t.fill==="function"&&typeof t.readUInt8==="function"}},{}],17:[function(e,t,r){(function(t,i){var n=e("process/browser.js").nextTick;var a=Function.prototype.apply;var o=Array.prototype.slice;var s={};var u=0;r.setTimeout=function(){return new c(a.call(setTimeout,window,arguments),clearTimeout)};r.setInterval=function(){return new c(a.call(setInterval,window,arguments),clearInterval)};r.clearTimeout=r.clearInterval=function(e){e.close()};function c(e,t){this._id=e;this._clearFn=t}c.prototype.unref=c.prototype.ref=function(){};c.prototype.close=function(){this._clearFn.call(window,this._id)};r.enroll=function(e,t){clearTimeout(e._idleTimeoutId);e._idleTimeout=t};r.unenroll=function(e){clearTimeout(e._idleTimeoutId);e._idleTimeout=-1};r._unrefActive=r.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;if(t>=0){e._idleTimeoutId=setTimeout(function t(){if(e._onTimeout)e._onTimeout()},t)}};r.setImmediate=typeof t==="function"?t:function(e){var t=u++;var i=arguments.length<2?false:o.call(arguments,1);s[t]=true;n(function n(){if(s[t]){if(i){e.apply(null,i)}else{e.call(null)}r.clearImmediate(t)}});return t};r.clearImmediate=typeof i==="function"?i:function(e){delete s[e]}}).call(this,e("timers").setImmediate,e("timers").clearImmediate)},{"process/browser.js":9,timers:17}],9:[function(e,t,r){var i=t.exports={};var n;var a;function o(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}(function(){try{if(typeof setTimeout==="function"){n=setTimeout}else{n=o}}catch(e){n=o}try{if(typeof clearTimeout==="function"){a=clearTimeout}else{a=s}}catch(e){a=s}})();function u(e){if(n===setTimeout){return setTimeout(e,0)}if((n===o||!n)&&setTimeout){n=setTimeout;return setTimeout(e,0)}try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}function c(e){if(a===clearTimeout){return clearTimeout(e)}if((a===s||!a)&&clearTimeout){a=clearTimeout;return clearTimeout(e)}try{return a(e)}catch(t){try{return a.call(null,e)}catch(t){return a.call(this,e)}}}var l=[];var f=false;var p;var h=-1;function d(){if(!f||!p){return}f=false;if(p.length){l=p.concat(l)}else{h=-1}if(l.length){m()}}function m(){if(f){return}var e=u(d);f=true;var t=l.length;while(t){p=l;l=[];while(++h<t){if(p){p[h].run()}}h=-1;t=l.length}p=null;f=false;c(e)}i.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1){for(var r=1;r<arguments.length;r++){t[r-1]=arguments[r]}}l.push(new y(e,t));if(l.length===1&&!f){u(m)}};function y(e,t){this.fun=e;this.array=t}y.prototype.run=function(){this.fun.apply(null,this.array)};i.title="browser";i.browser=true;i.env={};i.argv=[];i.version="";i.versions={};function v(){}i.on=v;i.addListener=v;i.once=v;i.off=v;i.removeListener=v;i.removeAllListeners=v;i.emit=v;i.prependListener=v;i.prependOnceListener=v;i.listeners=function(e){return[]};i.binding=function(e){throw new Error("process.binding is not supported")};i.cwd=function(){return"/"};i.chdir=function(e){throw new Error("process.chdir is not supported")};i.umask=function(){return 0}},{}],8:[function(e,t,r){(function(e){"use strict";function t(e){if(e!==null){return Object.prototype.toString.call(e)==="[object Array]"}else{return false}}function r(e){if(e!==null){return Object.prototype.toString.call(e)==="[object Object]"}else{return false}}function i(e,n){if(e===n){return true}var a=Object.prototype.toString.call(e);if(a!==Object.prototype.toString.call(n)){return false}if(t(e)===true){if(e.length!==n.length){return false}for(var o=0;o<e.length;o++){if(i(e[o],n[o])===false){return false}}return true}if(r(e)===true){var s={};for(var u in e){if(hasOwnProperty.call(e,u)){if(i(e[u],n[u])===false){return false}s[u]=true}}for(var c in n){if(hasOwnProperty.call(n,c)){if(s[c]!==true){return false}}}return true}return false}function n(e){if(e===""||e===false||e===null){return true}else if(t(e)&&e.length===0){return true}else if(r(e)){for(var i in e){if(e.hasOwnProperty(i)){return false}}return true}else{return false}}function a(e){var t=Object.keys(e);var r=[];for(var i=0;i<t.length;i++){r.push(e[t[i]])}return r}function o(e,t){var r={};for(var i in e){r[i]=e[i]}for(var n in t){r[n]=t[n]}return r}var s;if(typeof String.prototype.trimLeft==="function"){s=function(e){return e.trimLeft()}}else{s=function(e){return e.match(/^\s*(.*)/)[1]}}var u=0;var c=1;var l=2;var f=3;var p=4;var h=5;var d=6;var m=7;var y=8;var v=9;var g="EOF";var b="UnquotedIdentifier";var S="QuotedIdentifier";var k="Rbracket";var C="Rparen";var E="Comma";var w="Colon";var x="Rbrace";var N="Number";var R="Current";var q="Expref";var _="Pipe";var B="Or";var T="And";var A="EQ";var P="GT";var I="LT";var L="GTE";var M="LTE";var U="NE";var D="Flatten";var O="Star";var z="Filter";var j="Dot";var K="Not";var F="Lbrace";var H="Lbracket";var V="Lparen";var G="Literal";var W={".":j,"*":O,",":E,":":w,"{":F,"}":x,"]":k,"(":V,")":C,"@":R};var X={"<":true,">":true,"=":true,"!":true};var Y={" ":true,"\t":true,"\n":true};function J(e){return e>="a"&&e<="z"||e>="A"&&e<="Z"||e==="_"}function Q(e){return e>="0"&&e<="9"||e==="-"}function $(e){return e>="a"&&e<="z"||e>="A"&&e<="Z"||e>="0"&&e<="9"||e==="_"}function Z(){}Z.prototype={tokenize:function(e){var t=[];this._current=0;var r;var i;var n;while(this._current<e.length){if(J(e[this._current])){r=this._current;i=this._consumeUnquotedIdentifier(e);t.push({type:b,value:i,start:r})}else if(W[e[this._current]]!==undefined){t.push({type:W[e[this._current]],value:e[this._current],start:this._current});this._current++}else if(Q(e[this._current])){n=this._consumeNumber(e);t.push(n)}else if(e[this._current]==="["){n=this._consumeLBracket(e);t.push(n)}else if(e[this._current]==='"'){r=this._current;i=this._consumeQuotedIdentifier(e);t.push({type:S,value:i,start:r})}else if(e[this._current]==="'"){r=this._current;i=this._consumeRawStringLiteral(e);t.push({type:G,value:i,start:r})}else if(e[this._current]==="`"){r=this._current;var a=this._consumeLiteral(e);t.push({type:G,value:a,start:r})}else if(X[e[this._current]]!==undefined){t.push(this._consumeOperator(e))}else if(Y[e[this._current]]!==undefined){this._current++}else if(e[this._current]==="&"){r=this._current;this._current++;if(e[this._current]==="&"){this._current++;t.push({type:T,value:"&&",start:r})}else{t.push({type:q,value:"&",start:r})}}else if(e[this._current]==="|"){r=this._current;this._current++;if(e[this._current]==="|"){this._current++;t.push({type:B,value:"||",start:r})}else{t.push({type:_,value:"|",start:r})}}else{var o=new Error("Unknown character:"+e[this._current]);o.name="LexerError";throw o}}return t},_consumeUnquotedIdentifier:function(e){var t=this._current;this._current++;while(this._current<e.length&&$(e[this._current])){this._current++}return e.slice(t,this._current)},_consumeQuotedIdentifier:function(e){var t=this._current;this._current++;var r=e.length;while(e[this._current]!=='"'&&this._current<r){var i=this._current;if(e[i]==="\\"&&(e[i+1]==="\\"||e[i+1]==='"')){i+=2}else{i++}this._current=i}this._current++;return JSON.parse(e.slice(t,this._current))},_consumeRawStringLiteral:function(e){var t=this._current;this._current++;var r=e.length;while(e[this._current]!=="'"&&this._current<r){var i=this._current;if(e[i]==="\\"&&(e[i+1]==="\\"||e[i+1]==="'")){i+=2}else{i++}this._current=i}this._current++;var n=e.slice(t+1,this._current-1);return n.replace("\\'","'")},_consumeNumber:function(e){var t=this._current;this._current++;var r=e.length;while(Q(e[this._current])&&this._current<r){this._current++}var i=parseInt(e.slice(t,this._current));return{type:N,value:i,start:t}},_consumeLBracket:function(e){var t=this._current;this._current++;if(e[this._current]==="?"){this._current++;return{type:z,value:"[?",start:t}}else if(e[this._current]==="]"){this._current++;return{type:D,value:"[]",start:t}}else{return{type:H,value:"[",start:t}}},_consumeOperator:function(e){var t=this._current;var r=e[t];this._current++;if(r==="!"){if(e[this._current]==="="){this._current++;return{type:U,value:"!=",start:t}}else{return{type:K,value:"!",start:t}}}else if(r==="<"){if(e[this._current]==="="){this._current++;return{type:M,value:"<=",start:t}}else{return{type:I,value:"<",start:t}}}else if(r===">"){if(e[this._current]==="="){this._current++;return{type:L,value:">=",start:t}}else{return{type:P,value:">",start:t}}}else if(r==="="){if(e[this._current]==="="){this._current++;return{type:A,value:"==",start:t}}}},_consumeLiteral:function(e){this._current++;var t=this._current;var r=e.length;var i;while(e[this._current]!=="`"&&this._current<r){var n=this._current;if(e[n]==="\\"&&(e[n+1]==="\\"||e[n+1]==="`")){n+=2}else{n++}this._current=n}var a=s(e.slice(t,this._current));a=a.replace("\\`","`");if(this._looksLikeJSON(a)){i=JSON.parse(a)}else{i=JSON.parse('"'+a+'"')}this._current++;return i},_looksLikeJSON:function(e){var t='[{"';var r=["true","false","null"];var i="-0123456789";if(e===""){return false}else if(t.indexOf(e[0])>=0){return true}else if(r.indexOf(e)>=0){return true}else if(i.indexOf(e[0])>=0){try{JSON.parse(e);return true}catch(e){return false}}else{return false}}};var ee={};ee[g]=0;ee[b]=0;ee[S]=0;ee[k]=0;ee[C]=0;ee[E]=0;ee[x]=0;ee[N]=0;ee[R]=0;ee[q]=0;ee[_]=1;ee[B]=2;ee[T]=3;ee[A]=5;ee[P]=5;ee[I]=5;ee[L]=5;ee[M]=5;ee[U]=5;ee[D]=9;ee[O]=20;ee[z]=21;ee[j]=40;ee[K]=45;ee[F]=50;ee[H]=55;ee[V]=60;function te(){}te.prototype={parse:function(e){this._loadTokens(e);this.index=0;var t=this.expression(0);if(this._lookahead(0)!==g){var r=this._lookaheadToken(0);var i=new Error("Unexpected token type: "+r.type+", value: "+r.value);i.name="ParserError";throw i}return t},_loadTokens:function(e){var t=new Z;var r=t.tokenize(e);r.push({type:g,value:"",start:e.length});this.tokens=r},expression:function(e){var t=this._lookaheadToken(0);this._advance();var r=this.nud(t);var i=this._lookahead(0);while(e<ee[i]){this._advance();r=this.led(i,r);i=this._lookahead(0)}return r},_lookahead:function(e){return this.tokens[this.index+e].type},_lookaheadToken:function(e){return this.tokens[this.index+e]},_advance:function(){this.index++},nud:function(e){var t;var r;var i;switch(e.type){case G:return{type:"Literal",value:e.value};case b:return{type:"Field",name:e.value};case S:var n={type:"Field",name:e.value};if(this._lookahead(0)===V){throw new Error("Quoted identifier not allowed for function names.")}else{return n}break;case K:r=this.expression(ee.Not);return{type:"NotExpression",children:[r]};case O:t={type:"Identity"};r=null;if(this._lookahead(0)===k){r={type:"Identity"}}else{r=this._parseProjectionRHS(ee.Star)}return{type:"ValueProjection",children:[t,r]};case z:return this.led(e.type,{type:"Identity"});case F:return this._parseMultiselectHash();case D:t={type:D,children:[{type:"Identity"}]};r=this._parseProjectionRHS(ee.Flatten);return{type:"Projection",children:[t,r]};case H:if(this._lookahead(0)===N||this._lookahead(0)===w){r=this._parseIndexExpression();return this._projectIfSlice({type:"Identity"},r)}else if(this._lookahead(0)===O&&this._lookahead(1)===k){this._advance();this._advance();r=this._parseProjectionRHS(ee.Star);return{type:"Projection",children:[{type:"Identity"},r]}}else{return this._parseMultiselectList()}break;case R:return{type:R};case q:i=this.expression(ee.Expref);return{type:"ExpressionReference",children:[i]};case V:var a=[];while(this._lookahead(0)!==C){if(this._lookahead(0)===R){i={type:R};this._advance()}else{i=this.expression(0)}a.push(i)}this._match(C);return a[0];default:this._errorToken(e)}},led:function(e,t){var r;switch(e){case j:var i=ee.Dot;if(this._lookahead(0)!==O){r=this._parseDotRHS(i);return{type:"Subexpression",children:[t,r]}}else{this._advance();r=this._parseProjectionRHS(i);return{type:"ValueProjection",children:[t,r]}}break;case _:r=this.expression(ee.Pipe);return{type:_,children:[t,r]};case B:r=this.expression(ee.Or);return{type:"OrExpression",children:[t,r]};case T:r=this.expression(ee.And);return{type:"AndExpression",children:[t,r]};case V:var n=t.name;var a=[];var o,s;while(this._lookahead(0)!==C){if(this._lookahead(0)===R){o={type:R};this._advance()}else{o=this.expression(0)}if(this._lookahead(0)===E){this._match(E)}a.push(o)}this._match(C);s={type:"Function",name:n,children:a};return s;case z:var u=this.expression(0);this._match(k);if(this._lookahead(0)===D){r={type:"Identity"}}else{r=this._parseProjectionRHS(ee.Filter)}return{type:"FilterProjection",children:[t,r,u]};case D:var c={type:D,children:[t]};var l=this._parseProjectionRHS(ee.Flatten);return{type:"Projection",children:[c,l]};case A:case U:case P:case L:case I:case M:return this._parseComparator(t,e);case H:var f=this._lookaheadToken(0);if(f.type===N||f.type===w){r=this._parseIndexExpression();return this._projectIfSlice(t,r)}else{this._match(O);this._match(k);r=this._parseProjectionRHS(ee.Star);return{type:"Projection",children:[t,r]}}break;default:this._errorToken(this._lookaheadToken(0))}},_match:function(e){if(this._lookahead(0)===e){this._advance()}else{var t=this._lookaheadToken(0);var r=new Error("Expected "+e+", got: "+t.type);r.name="ParserError";throw r}},_errorToken:function(e){var t=new Error("Invalid token ("+e.type+'): "'+e.value+'"');t.name="ParserError";throw t},_parseIndexExpression:function(){if(this._lookahead(0)===w||this._lookahead(1)===w){return this._parseSliceExpression()}else{var e={type:"Index",value:this._lookaheadToken(0).value};this._advance();this._match(k);return e}},_projectIfSlice:function(e,t){var r={type:"IndexExpression",children:[e,t]};if(t.type==="Slice"){return{type:"Projection",children:[r,this._parseProjectionRHS(ee.Star)]}}else{return r}},_parseSliceExpression:function(){var e=[null,null,null];var t=0;var r=this._lookahead(0);while(r!==k&&t<3){if(r===w){t++;this._advance()}else if(r===N){e[t]=this._lookaheadToken(0).value;this._advance()}else{var i=this._lookahead(0);var n=new Error("Syntax error, unexpected token: "+i.value+"("+i.type+")");n.name="Parsererror";throw n}r=this._lookahead(0)}this._match(k);return{type:"Slice",children:e}},_parseComparator:function(e,t){var r=this.expression(ee[t]);return{type:"Comparator",name:t,children:[e,r]}},_parseDotRHS:function(e){var t=this._lookahead(0);var r=[b,S,O];if(r.indexOf(t)>=0){return this.expression(e)}else if(t===H){this._match(H);return this._parseMultiselectList()}else if(t===F){this._match(F);return this._parseMultiselectHash()}},_parseProjectionRHS:function(e){var t;if(ee[this._lookahead(0)]<10){t={type:"Identity"}}else if(this._lookahead(0)===H){t=this.expression(e)}else if(this._lookahead(0)===z){t=this.expression(e)}else if(this._lookahead(0)===j){this._match(j);t=this._parseDotRHS(e)}else{var r=this._lookaheadToken(0);var i=new Error("Sytanx error, unexpected token: "+r.value+"("+r.type+")");i.name="ParserError";throw i}return t},_parseMultiselectList:function(){var e=[];while(this._lookahead(0)!==k){var t=this.expression(0);e.push(t);if(this._lookahead(0)===E){this._match(E);if(this._lookahead(0)===k){throw new Error("Unexpected token Rbracket")}}}this._match(k);return{type:"MultiSelectList",children:e}},_parseMultiselectHash:function(){var e=[];var t=[b,S];var r,i,n,a;for(;;){r=this._lookaheadToken(0);if(t.indexOf(r.type)<0){throw new Error("Expecting an identifier token, got: "+r.type)}i=r.value;this._advance();this._match(w);n=this.expression(0);a={type:"KeyValuePair",name:i,value:n};e.push(a);if(this._lookahead(0)===E){this._match(E)}else if(this._lookahead(0)===x){this._match(x);break}}return{type:"MultiSelectHash",children:e}}};function re(e){this.runtime=e}re.prototype={search:function(e,t){return this.visit(e,t)},visit:function(e,o){var s,u,c,l,f,p,h,d,m,y;switch(e.type){case"Field":if(o===null){return null}else if(r(o)){p=o[e.name];if(p===undefined){return null}else{return p}}else{return null}break;case"Subexpression":c=this.visit(e.children[0],o);for(y=1;y<e.children.length;y++){c=this.visit(e.children[1],c);if(c===null){return null}}return c;case"IndexExpression":h=this.visit(e.children[0],o);d=this.visit(e.children[1],h);return d;case"Index":if(!t(o)){return null}var v=e.value;if(v<0){v=o.length+v}c=o[v];if(c===undefined){c=null}return c;case"Slice":if(!t(o)){return null}var g=e.children.slice(0);var b=this.computeSliceParams(o.length,g);var S=b[0];var k=b[1];var C=b[2];c=[];if(C>0){for(y=S;y<k;y+=C){c.push(o[y])}}else{for(y=S;y>k;y+=C){c.push(o[y])}}return c;case"Projection":var E=this.visit(e.children[0],o);if(!t(E)){return null}m=[];for(y=0;y<E.length;y++){u=this.visit(e.children[1],E[y]);if(u!==null){m.push(u)}}return m;case"ValueProjection":E=this.visit(e.children[0],o);if(!r(E)){return null}m=[];var w=a(E);for(y=0;y<w.length;y++){u=this.visit(e.children[1],w[y]);if(u!==null){m.push(u)}}return m;case"FilterProjection":E=this.visit(e.children[0],o);if(!t(E)){return null}var x=[];var N=[];for(y=0;y<E.length;y++){s=this.visit(e.children[2],E[y]);if(!n(s)){x.push(E[y])}}for(var B=0;B<x.length;B++){u=this.visit(e.children[1],x[B]);if(u!==null){N.push(u)}}return N;case"Comparator":l=this.visit(e.children[0],o);f=this.visit(e.children[1],o);switch(e.name){case A:c=i(l,f);break;case U:c=!i(l,f);break;case P:c=l>f;break;case L:c=l>=f;break;case I:c=l<f;break;case M:c=l<=f;break;default:throw new Error("Unknown comparator: "+e.name)}return c;case D:var T=this.visit(e.children[0],o);if(!t(T)){return null}var O=[];for(y=0;y<T.length;y++){u=T[y];if(t(u)){O.push.apply(O,u)}else{O.push(u)}}return O;case"Identity":return o;case"MultiSelectList":if(o===null){return null}m=[];for(y=0;y<e.children.length;y++){m.push(this.visit(e.children[y],o))}return m;case"MultiSelectHash":if(o===null){return null}m={};var z;for(y=0;y<e.children.length;y++){z=e.children[y];m[z.name]=this.visit(z.value,o)}return m;case"OrExpression":s=this.visit(e.children[0],o);if(n(s)){s=this.visit(e.children[1],o)}return s;case"AndExpression":l=this.visit(e.children[0],o);if(n(l)===true){return l}return this.visit(e.children[1],o);case"NotExpression":l=this.visit(e.children[0],o);return n(l);case"Literal":return e.value;case _:h=this.visit(e.children[0],o);return this.visit(e.children[1],h);case R:return o;case"Function":var j=[];for(y=0;y<e.children.length;y++){j.push(this.visit(e.children[y],o))}return this.runtime.callFunction(e.name,j);case"ExpressionReference":var K=e.children[0];K.jmespathType=q;return K;default:throw new Error("Unknown node type: "+e.type)}},computeSliceParams:function(e,t){var r=t[0];var i=t[1];var n=t[2];var a=[null,null,null];if(n===null){n=1}else if(n===0){var o=new Error("Invalid slice, step cannot be 0");o.name="RuntimeError";throw o}var s=n<0?true:false;if(r===null){r=s?e-1:0}else{r=this.capSliceRange(e,r,n)}if(i===null){i=s?-1:e}else{i=this.capSliceRange(e,i,n)}a[0]=r;a[1]=i;a[2]=n;return a},capSliceRange:function(e,t,r){if(t<0){t+=e;if(t<0){t=r<0?-1:0}}else if(t>=e){t=r<0?e-1:e}return t}};function ie(e){this._interpreter=e;this.functionTable={abs:{_func:this._functionAbs,_signature:[{types:[u]}]},avg:{_func:this._functionAvg,_signature:[{types:[y]}]},ceil:{_func:this._functionCeil,_signature:[{types:[u]}]},contains:{_func:this._functionContains,_signature:[{types:[l,f]},{types:[c]}]},ends_with:{_func:this._functionEndsWith,_signature:[{types:[l]},{types:[l]}]},floor:{_func:this._functionFloor,_signature:[{types:[u]}]},length:{_func:this._functionLength,_signature:[{types:[l,f,p]}]},map:{_func:this._functionMap,_signature:[{types:[d]},{types:[f]}]},max:{_func:this._functionMax,_signature:[{types:[y,v]}]},merge:{_func:this._functionMerge,_signature:[{types:[p],variadic:true}]},max_by:{_func:this._functionMaxBy,_signature:[{types:[f]},{types:[d]}]},sum:{_func:this._functionSum,_signature:[{types:[y]}]},starts_with:{_func:this._functionStartsWith,_signature:[{types:[l]},{types:[l]}]},min:{_func:this._functionMin,_signature:[{types:[y,v]}]},min_by:{_func:this._functionMinBy,_signature:[{types:[f]},{types:[d]}]},type:{_func:this._functionType,_signature:[{types:[c]}]},keys:{_func:this._functionKeys,_signature:[{types:[p]}]},values:{_func:this._functionValues,_signature:[{types:[p]}]},sort:{_func:this._functionSort,_signature:[{types:[v,y]}]},sort_by:{_func:this._functionSortBy,_signature:[{types:[f]},{types:[d]}]},join:{_func:this._functionJoin,_signature:[{types:[l]},{types:[v]}]},reverse:{_func:this._functionReverse,_signature:[{types:[l,f]}]},to_array:{_func:this._functionToArray,_signature:[{types:[c]}]},to_string:{_func:this._functionToString,_signature:[{types:[c]}]},to_number:{_func:this._functionToNumber,_signature:[{types:[c]}]},not_null:{_func:this._functionNotNull,_signature:[{types:[c],variadic:true}]}}}ie.prototype={callFunction:function(e,t){var r=this.functionTable[e];if(r===undefined){throw new Error("Unknown function: "+e+"()")}this._validateArgs(e,t,r._signature);return r._func.call(this,t)},_validateArgs:function(e,t,r){var i;if(r[r.length-1].variadic){if(t.length<r.length){i=r.length===1?" argument":" arguments";throw new Error("ArgumentError: "+e+"() "+"takes at least"+r.length+i+" but received "+t.length)}}else if(t.length!==r.length){i=r.length===1?" argument":" arguments";throw new Error("ArgumentError: "+e+"() "+"takes "+r.length+i+" but received "+t.length)}var n;var a;var o;for(var s=0;s<r.length;s++){o=false;n=r[s].types;a=this._getTypeName(t[s]);for(var u=0;u<n.length;u++){if(this._typeMatches(a,n[u],t[s])){o=true;break}}if(!o){throw new Error("TypeError: "+e+"() "+"expected argument "+(s+1)+" to be type "+n+" but received type "+a+" instead.")}}},_typeMatches:function(e,t,r){if(t===c){return true}if(t===v||t===y||t===f){if(t===f){return e===f}else if(e===f){var i;if(t===y){i=u}else if(t===v){i=l}for(var n=0;n<r.length;n++){if(!this._typeMatches(this._getTypeName(r[n]),i,r[n])){return false}}return true}}else{return e===t}},_getTypeName:function(e){switch(Object.prototype.toString.call(e)){case"[object String]":return l;case"[object Number]":return u;case"[object Array]":return f;case"[object Boolean]":return h;case"[object Null]":return m;case"[object Object]":if(e.jmespathType===q){return d}else{return p}}},_functionStartsWith:function(e){return e[0].lastIndexOf(e[1])===0},_functionEndsWith:function(e){var t=e[0];var r=e[1];return t.indexOf(r,t.length-r.length)!==-1},_functionReverse:function(e){var t=this._getTypeName(e[0]);if(t===l){var r=e[0];var i="";for(var n=r.length-1;n>=0;n--){i+=r[n]}return i}else{var a=e[0].slice(0);a.reverse();return a}},_functionAbs:function(e){return Math.abs(e[0])},_functionCeil:function(e){return Math.ceil(e[0])},_functionAvg:function(e){var t=0;var r=e[0];for(var i=0;i<r.length;i++){t+=r[i]}return t/r.length},_functionContains:function(e){return e[0].indexOf(e[1])>=0},_functionFloor:function(e){return Math.floor(e[0])},_functionLength:function(e){if(!r(e[0])){return e[0].length}else{return Object.keys(e[0]).length}},_functionMap:function(e){var t=[];var r=this._interpreter;var i=e[0];var n=e[1];for(var a=0;a<n.length;a++){t.push(r.visit(i,n[a]))}return t},_functionMerge:function(e){var t={};for(var r=0;r<e.length;r++){var i=e[r];for(var n in i){t[n]=i[n]}}return t},_functionMax:function(e){if(e[0].length>0){var t=this._getTypeName(e[0][0]);if(t===u){return Math.max.apply(Math,e[0])}else{var r=e[0];var i=r[0];for(var n=1;n<r.length;n++){if(i.localeCompare(r[n])<0){i=r[n]}}return i}}else{return null}},_functionMin:function(e){if(e[0].length>0){var t=this._getTypeName(e[0][0]);if(t===u){return Math.min.apply(Math,e[0])}else{var r=e[0];var i=r[0];for(var n=1;n<r.length;n++){if(r[n].localeCompare(i)<0){i=r[n]}}return i}}else{return null}},_functionSum:function(e){var t=0;var r=e[0];for(var i=0;i<r.length;i++){t+=r[i]}return t},_functionType:function(e){switch(this._getTypeName(e[0])){case u:return"number";case l:return"string";case f:return"array";case p:return"object";case h:return"boolean";case d:return"expref";case m:return"null"}},_functionKeys:function(e){return Object.keys(e[0])},_functionValues:function(e){var t=e[0];var r=Object.keys(t);var i=[];for(var n=0;n<r.length;n++){i.push(t[r[n]])}return i},_functionJoin:function(e){var t=e[0];var r=e[1];return r.join(t)},_functionToArray:function(e){if(this._getTypeName(e[0])===f){return e[0]}else{return[e[0]]}},_functionToString:function(e){if(this._getTypeName(e[0])===l){return e[0]}else{return JSON.stringify(e[0])}},_functionToNumber:function(e){var t=this._getTypeName(e[0]);var r;if(t===u){return e[0]}else if(t===l){r=+e[0];if(!isNaN(r)){return r}}return null},_functionNotNull:function(e){for(var t=0;t<e.length;t++){if(this._getTypeName(e[t])!==m){return e[t]}}return null},_functionSort:function(e){var t=e[0].slice(0);t.sort();return t},_functionSortBy:function(e){var t=e[0].slice(0);if(t.length===0){return t}var r=this._interpreter;var i=e[1];var n=this._getTypeName(r.visit(i,t[0]));if([u,l].indexOf(n)<0){throw new Error("TypeError")}var a=this;var o=[];for(var s=0;s<t.length;s++){o.push([s,t[s]])}o.sort(function(e,t){var o=r.visit(i,e[1]);var s=r.visit(i,t[1]);if(a._getTypeName(o)!==n){throw new Error("TypeError: expected "+n+", received "+a._getTypeName(o))}else if(a._getTypeName(s)!==n){throw new Error("TypeError: expected "+n+", received "+a._getTypeName(s))}if(o>s){return 1}else if(o<s){return-1}else{return e[0]-t[0]}});for(var c=0;c<o.length;c++){t[c]=o[c][1]}return t},_functionMaxBy:function(e){var t=e[1];var r=e[0];var i=this.createKeyFunction(t,[u,l]);var n=-Infinity;var a;var o;for(var s=0;s<r.length;s++){o=i(r[s]);if(o>n){n=o;a=r[s]}}return a},_functionMinBy:function(e){var t=e[1];var r=e[0];var i=this.createKeyFunction(t,[u,l]);var n=Infinity;var a;var o;for(var s=0;s<r.length;s++){o=i(r[s]);if(o<n){n=o;a=r[s]}}return a},createKeyFunction:function(e,t){var r=this;var i=this._interpreter;var n=function(n){var a=i.visit(e,n);if(t.indexOf(r._getTypeName(a))<0){var o="TypeError: expected one of "+t+", received "+r._getTypeName(a);throw new Error(o)}return a};return n}};function ne(e){var t=new te;var r=t.parse(e);return r}function ae(e){var t=new Z;return t.tokenize(e)}function oe(e,t){var r=new te;var i=new ie;var n=new re(i);i._interpreter=n;var a=r.parse(t);return n.search(a,e)}e.tokenize=ae;e.compile=ne;e.search=oe;e.strictDeepEqual=i})(typeof r==="undefined"?this.jmespath={}:r)},{}],6:[function(e,t,r){if(typeof Object.create==="function"){t.exports=function e(t,r){t.super_=r;t.prototype=Object.create(r.prototype,{constructor:{value:t,enumerable:false,writable:true,configurable:true}})}}else{t.exports=function e(t,r){t.super_=r;var i=function(){};i.prototype=r.prototype;t.prototype=new i;t.prototype.constructor=t}}},{}],2:[function(e,t,r){},{}]},{},[]);_xamzrequire=function e(t,r,i){function n(o,s){if(!r[o]){if(!t[o]){var u=typeof _xamzrequire=="function"&&_xamzrequire;if(!s&&u)return u(o,!0);if(a)return a(o,!0);var c=new Error("Cannot find module '"+o+"'");throw c.code="MODULE_NOT_FOUND",c}var l=r[o]={exports:{}};t[o][0].call(l.exports,function(e){var r=t[o][1][e];return n(r?r:e)},l,l.exports,e,t,r,i)}return r[o].exports}var a=typeof _xamzrequire=="function"&&_xamzrequire;for(var o=0;o<i.length;o++)n(i[o]);return n}({28:[function(e,t,r){e("./browser_loader");var i=e("./core");if(typeof window!=="undefined")window.OOS=i;if(typeof t!=="undefined"){t.exports=i}if(typeof self!=="undefined")self.OOS=i},{"./browser_loader":35,"./core":38}],35:[function(e,t,r){(function(r){var i=e("./util");i.crypto.lib=e("./browserCryptoLib");i.Buffer=e("buffer/").Buffer;i.url=e("url/");i.querystring=e("querystring/");i.environment="js";i.createEventStream=e("./event-stream/buffered-create-event-stream").createEventStream;var n=e("./core");t.exports=n;e("./credentials");e("./credentials/credential_provider_chain");e("./credentials/temporary_credentials");e("./credentials/web_identity_credentials");e("./credentials/cognito_identity_credentials");e("./credentials/saml_credentials");n.XML.Parser=e("./xml/browser_parser");e("./http/xhr");if(typeof r==="undefined"){r={browser:true}}}).call(this,e("_process"))},{"./browserCryptoLib":29,"./core":38,"./credentials":39,"./credentials/cognito_identity_credentials":40,"./credentials/credential_provider_chain":41,"./credentials/saml_credentials":42,"./credentials/temporary_credentials":43,"./credentials/web_identity_credentials":44,"./event-stream/buffered-create-event-stream":51,"./http/xhr":60,"./util":112,"./xml/browser_parser":113,_process:9,"buffer/":3,"querystring/":16,"url/":18}],113:[function(e,t,r){var i=e("../util");var n=e("../model/shape");function a(){}a.prototype.parse=function(e,t){if(e.replace(/^\s+/,"")==="")return{};var r,n;try{if(window.DOMParser){try{var a=new DOMParser;r=a.parseFromString(e,"text/xml")}catch(e){throw i.error(new Error("Parse error in document"),{originalError:e,code:"XMLParserError",retryable:true})}if(r.documentElement===null){throw i.error(new Error("Cannot parse empty document."),{code:"XMLParserError",retryable:true})}var u=r.getElementsByTagName("parsererror")[0];if(u&&(u.parentNode===r||u.parentNode.nodeName==="body"||u.parentNode.parentNode===r||u.parentNode.parentNode.nodeName==="body")){var c=u.getElementsByTagName("div")[0]||u;throw i.error(new Error(c.textContent||"Parser error in document"),{code:"XMLParserError",retryable:true})}}else if(window.ActiveXObject){r=new window.ActiveXObject("Microsoft.XMLDOM");r.async=false;if(!r.loadXML(e)){throw i.error(new Error("Parse error in document"),{code:"XMLParserError",retryable:true})}}else{throw new Error("Cannot load XML parser")}}catch(e){n=e}if(r&&r.documentElement&&!n){var l=s(r.documentElement,t);var f=o(r.documentElement,"ResponseMetadata");if(f){l.ResponseMetadata=s(f,{})}return l}else if(n){throw i.error(n||new Error,{code:"XMLParserError",retryable:true})}else{return{}}};function o(e,t){var r=e.getElementsByTagName(t);for(var i=0,n=r.length;i<n;i++){if(r[i].parentNode===e){return r[i]}}}function s(e,t){if(!t)t={};switch(t.type){case"structure":return u(e,t);case"map":return c(e,t);case"list":return l(e,t);case undefined:case null:return p(e);default:return f(e,t)}}function u(e,t){var r={};if(e===null)return r;i.each(t.members,function(t,i){if(i.isXmlAttribute){if(Object.prototype.hasOwnProperty.call(e.attributes,i.name)){var n=e.attributes[i.name].value;r[t]=s({textContent:n},i)}}else{var a=i.flattened?e:o(e,i.name);if(a){r[t]=s(a,i)}else if(!i.flattened&&i.type==="list"){r[t]=i.defaultValue}}});return r}function c(e,t){var r={};var i=t.key.name||"key";var n=t.value.name||"value";var a=t.flattened?t.name:"entry";var u=e.firstElementChild;while(u){if(u.nodeName===a){var c=o(u,i).textContent;var l=o(u,n);r[c]=s(l,t.value)}u=u.nextElementSibling}return r}function l(e,t){var r=[];var i=t.flattened?t.name:t.member.name||"member";var n=e.firstElementChild;while(n){if(n.nodeName===i){r.push(s(n,t.member))}n=n.nextElementSibling}return r}function f(e,t){if(e.getAttribute){var r=e.getAttribute("encoding");if(r==="base64"){t=new n.create({type:r})}}var i=e.textContent;if(i==="")i=null;if(typeof t.toType==="function"){return t.toType(i)}else{return i}}function p(e){if(e===undefined||e===null)return"";if(!e.firstElementChild){if(e.parentNode.parentNode===null)return{};if(e.childNodes.length===0)return"";else return e.textContent}var t={type:"structure",members:{}};var r=e.firstElementChild;while(r){var i=r.nodeName;if(Object.prototype.hasOwnProperty.call(t.members,i)){t.members[i].type="list"}else{t.members[i]={name:i}}r=r.nextElementSibling}return u(e,t)}t.exports=a},{"../model/shape":68,"../util":112}],60:[function(e,t,r){var i=e("../core");var n=e("events").EventEmitter;e("../http");i.XHRClient=i.util.inherit({handleRequest:function e(t,r,a,o){var s=this;var u=t.endpoint;var c=new n;var l=u.protocol+"//"+u.hostname;if(u.port!==80&&u.port!==443){l+=":"+u.port}l+=t.path;var f=new XMLHttpRequest,p=false;t.stream=f;f.addEventListener("readystatechange",function(){try{if(f.status===0)return}catch(e){return}if(this.readyState>=this.HEADERS_RECEIVED&&!p){c.statusCode=f.status;c.headers=s.parseHeaders(f.getAllResponseHeaders());c.emit("headers",c.statusCode,c.headers,f.statusText);p=true}if(this.readyState===this.DONE){s.finishRequest(f,c)}},false);f.upload.addEventListener("progress",function(e){c.emit("sendProgress",e)});f.addEventListener("progress",function(e){c.emit("receiveProgress",e)},false);f.addEventListener("timeout",function(){o(i.util.error(new Error("Timeout"),{code:"TimeoutError"}))},false);f.addEventListener("error",function(){o(i.util.error(new Error("Network Failure"),{code:"NetworkingError"}))},false);f.addEventListener("abort",function(){o(i.util.error(new Error("Request aborted"),{code:"RequestAbortedError"}))},false);a(c);f.open(t.method,l,r.xhrAsync!==false);i.util.each(t.headers,function(e,t){if(e!=="Content-Length"&&e!=="User-Agent"&&e!=="Host"){f.setRequestHeader(e,t)}});if(r.timeout&&r.xhrAsync!==false){f.timeout=r.timeout}if(r.xhrWithCredentials){f.withCredentials=true}try{f.responseType="arraybuffer"}catch(e){}try{if(t.body){f.send(t.body)}else{f.send()}}catch(e){if(t.body&&typeof t.body.buffer==="object"){f.send(t.body.buffer)}else{throw e}}return c},parseHeaders:function e(t){var r={};i.util.arrayEach(t.split(/\r?\n/),function(e){var t=e.split(":",1)[0];var i=e.substring(t.length+2);if(t.length>0)r[t.toLowerCase()]=i});return r},finishRequest:function e(t,r){var n;if(t.responseType==="arraybuffer"&&t.response){var a=t.response;n=new i.util.Buffer(a.byteLength);var o=new Uint8Array(a);for(var s=0;s<n.length;++s){n[s]=o[s]}}try{if(!n&&typeof t.responseText==="string"){n=new i.util.Buffer(t.responseText)}}catch(e){}if(n)r.emit("data",n);r.emit("end")}});i.HttpClient.prototype=i.XHRClient.prototype;i.HttpClient.streamsApiVersion=1},{"../core":38,"../http":59,events:4}],51:[function(e,t,r){var i=e("../event-stream/event-message-chunker").eventMessageChunker;var n=e("./parse-event").parseEvent;function a(e,t,r){var a=i(e);var o=[];for(var s=0;s<a.length;s++){o.push(n(t,a[s],r))}return o}t.exports={createEventStream:a}},{"../event-stream/event-message-chunker":52,"./parse-event":54}],54:[function(e,t,r){var i=e("./parse-message").parseMessage;function n(e,t,r){var n=i(t);var o=n.headers[":message-type"];if(o){if(o.value==="error"){throw a(n)}else if(o.value!=="event"){return}}var s=n.headers[":event-type"];var u=r.members[s.value];if(!u){return}var c={};var l=u.eventPayloadMemberName;if(l){var f=u.members[l];if(f.type==="binary"){c[l]=n.body}else{c[l]=e.parse(n.body.toString(),f)}}var p=u.eventHeaderMemberNames;for(var h=0;h<p.length;h++){var d=p[h];if(n.headers[d]){c[d]=u.members[d].toType(n.headers[d].value)}}var m={};m[s.value]=c;return m}function a(e){var t=e.headers[":error-code"];var r=e.headers[":error-message"];var i=new Error(r.value||r);i.code=i.name=t.value||t;return i}t.exports={parseEvent:n}},{"./parse-message":55}],55:[function(e,t,r){var i=e("./int64").Int64;var n=e("./split-message").splitMessage;var a="boolean";var o="byte";var s="short";var u="integer";var c="long";var l="binary";var f="string";var p="timestamp";var h="uuid";function d(e){var t={};var r=0;while(r<e.length){var n=e.readUInt8(r++);var d=e.slice(r,r+n).toString();r+=n;switch(e.readUInt8(r++)){case 0:t[d]={type:a,value:true};break;case 1:t[d]={type:a,value:false};break;case 2:t[d]={type:o,value:e.readInt8(r++)};break;case 3:t[d]={type:s,value:e.readInt16BE(r)};r+=2;break;case 4:t[d]={type:u,value:e.readInt32BE(r)};r+=4;break;case 5:t[d]={type:c,value:new i(e.slice(r,r+8))};r+=8;break;case 6:var m=e.readUInt16BE(r);r+=2;t[d]={type:l,value:e.slice(r,r+m)};r+=m;break;case 7:var y=e.readUInt16BE(r);r+=2;t[d]={type:f,value:e.slice(r,r+y).toString()};r+=y;break;case 8:t[d]={type:p,value:new Date(new i(e.slice(r,r+8)).valueOf())};r+=8;break;case 9:var v=e.slice(r,r+16).toString("hex");r+=16;t[d]={type:h,value:v.substr(0,8)+"-"+v.substr(8,4)+"-"+v.substr(12,4)+"-"+v.substr(16,4)+"-"+v.substr(20)};break;default:throw new Error("Unrecognized header type tag")}}return t}function m(e){var t=n(e);return{headers:d(t.headers),body:t.body}}t.exports={parseMessage:m}},{"./int64":53,"./split-message":56}],56:[function(e,t,r){var i=e("../core").util;var n=e("./to-buffer").toBuffer;var a=4;var o=a*2;var s=4;var u=o+s*2;function c(e){if(!i.Buffer.isBuffer(e))e=n(e);if(e.length<u){throw new Error("Provided message too short to accommodate event stream message overhead")}if(e.length!==e.readUInt32BE(0)){throw new Error("Reported message length does not match received message length")}var t=e.readUInt32BE(o);if(t!==i.crypto.crc32(e.slice(0,o))){throw new Error("The prelude checksum specified in the message ("+t+") does not match the calculated CRC32 checksum.")}var r=e.readUInt32BE(e.length-s);if(r!==i.crypto.crc32(e.slice(0,e.length-s))){throw new Error("The message checksum did not match the expected value of "+r)}var c=o+s;var l=c+e.readUInt32BE(a);return{headers:e.slice(c,l),body:e.slice(l,e.length-s)}}t.exports={splitMessage:c}},{"../core":38,"./to-buffer":57}],53:[function(e,t,r){var i=e("../core").util;var n=e("./to-buffer").toBuffer;function a(e){if(e.length!==8){throw new Error("Int64 buffers must be exactly 8 bytes")}if(!i.Buffer.isBuffer(e))e=n(e);this.bytes=e}a.fromNumber=function(e){if(e>0x8000000000000000||e<-0x8000000000000000){throw new Error(e+" is too large (or, if negative, too small) to represent as an Int64")}var t=new Uint8Array(8);for(var r=7,i=Math.abs(Math.round(e));r>-1&&i>0;r--,i/=256){t[r]=i}if(e<0){o(t)}return new a(t)};a.prototype.valueOf=function(){var e=this.bytes.slice(0);var t=e[0]&128;if(t){o(e)}return parseInt(e.toString("hex"),16)*(t?-1:1)};a.prototype.toString=function(){return String(this.valueOf())};function o(e){for(var t=0;t<8;t++){e[t]^=255}for(var t=7;t>-1;t--){e[t]++;if(e[t]!==0){break}}}t.exports={Int64:a}},{"../core":38,"./to-buffer":57}],57:[function(e,t,r){var i=e("../core").util.Buffer;function n(e,t){return typeof i.from==="function"&&i.from!==Uint8Array.from?i.from(e,t):new i(e,t)}t.exports={toBuffer:n}},{"../core":38}],52:[function(e,t,r){function i(e){var t=[];var r=0;while(r<e.length){var i=e.readInt32BE(r);var n=e.slice(r,i+r);r+=i;t.push(n)}return t}t.exports={eventMessageChunker:i}},{}],44:[function(e,t,r){var i=e("../core");i.WebIdentityCredentials=i.util.inherit(i.Credentials,{constructor:function e(t,r){i.Credentials.call(this);this.expired=true;this.params=t;this.params.RoleSessionName=this.params.RoleSessionName||"web-identity";this.data=null;this._clientConfig=i.util.copy(r||{})},refresh:function e(t){var r=this;r.createClients();if(!t)t=function(e){if(e)throw e};r.service.assumeRoleWithWebIdentity(function(e,i){r.data=null;if(!e){r.data=i;r.service.credentialsFrom(i,r)}t(e)})},createClients:function(){if(!this.service){var e=i.util.merge({},this._clientConfig);e.params=this.params;this.service=new i.STS(e)}}})},{"../core":38}],43:[function(e,t,r){var i=e("../core");i.TemporaryCredentials=i.util.inherit(i.Credentials,{constructor:function e(t,r){i.Credentials.call(this);this.loadMasterCredentials(r);this.expired=true;this.params=t||{};if(this.params.RoleArn){this.params.RoleSessionName=this.params.RoleSessionName||"temporary-credentials"}},refresh:function e(t){var r=this;r.createClients();if(!t)t=function(e){if(e)throw e};r.masterCredentials.get(function(){r.service.config.credentials=r.masterCredentials;var e=r.params.RoleArn?r.service.assumeRole:r.service.getSessionToken;e.call(r.service,function(e,i){if(!e){r.service.credentialsFrom(i,r)}t(e)})})},loadMasterCredentials:function e(t){this.masterCredentials=t||i.config.credentials;while(this.masterCredentials.masterCredentials){this.masterCredentials=this.masterCredentials.masterCredentials}if(typeof this.masterCredentials.get!=="function"){this.masterCredentials=new i.Credentials(this.masterCredentials)}},createClients:function(){this.service=this.service||new i.STS({params:this.params})}})},{"../core":38}],42:[function(e,t,r){var i=e("../core");i.SAMLCredentials=i.util.inherit(i.Credentials,{constructor:function e(t){i.Credentials.call(this);this.expired=true;this.params=t},refresh:function e(t){var r=this;r.createClients();if(!t)t=function(e){if(e)throw e};r.service.assumeRoleWithSAML(function(e,i){if(!e){r.service.credentialsFrom(i,r)}t(e)})},createClients:function(){this.service=this.service||new i.STS({params:this.params})}})},{"../core":38}],40:[function(e,t,r){var i=e("../core");i.CognitoIdentityCredentials=i.util.inherit(i.Credentials,{localStorageKey:{id:"aws.cognito.identity-id.",providers:"aws.cognito.identity-providers."},constructor:function e(t,r){i.Credentials.call(this);this.expired=true;this.params=t;this.data=null;this._identityId=null;this._clientConfig=i.util.copy(r||{});this.loadCachedId();var n=this;Object.defineProperty(this,"identityId",{get:function(){n.loadCachedId();return n._identityId||n.params.IdentityId},set:function(e){n._identityId=e}})},refresh:function e(t){var r=this;r.createClients();r.data=null;r._identityId=null;r.getId(function(e){if(!e){if(!r.params.RoleArn){r.getCredentialsForIdentity(t)}else{r.getCredentialsFromSTS(t)}}else{r.clearIdOnNotAuthorized(e);t(e)}})},clearCachedId:function e(){this._identityId=null;delete this.params.IdentityId;var t=this.params.IdentityPoolId;var r=this.params.LoginId||"";delete this.storage[this.localStorageKey.id+t+r];delete this.storage[this.localStorageKey.providers+t+r]},clearIdOnNotAuthorized:function e(t){var r=this;if(t.code=="NotAuthorizedException"){r.clearCachedId()}},getId:function e(t){var r=this;if(typeof r.params.IdentityId==="string"){return t(null,r.params.IdentityId)}r.cognito.getId(function(e,i){if(!e&&i.IdentityId){r.params.IdentityId=i.IdentityId;t(null,i.IdentityId)}else{t(e)}})},loadCredentials:function e(t,r){if(!t||!r)return;r.expired=false;r.accessKeyId=t.Credentials.AccessKeyId;r.secretAccessKey=t.Credentials.SecretKey;r.sessionToken=t.Credentials.SessionToken;r.expireTime=t.Credentials.Expiration},getCredentialsForIdentity:function e(t){var r=this;r.cognito.getCredentialsForIdentity(function(e,i){if(!e){r.cacheId(i);r.data=i;r.loadCredentials(r.data,r)}else{r.clearIdOnNotAuthorized(e)}t(e)})},getCredentialsFromSTS:function e(t){var r=this;r.cognito.getOpenIdToken(function(e,i){if(!e){r.cacheId(i);r.params.WebIdentityToken=i.Token;r.webIdentityCredentials.refresh(function(e){if(!e){r.data=r.webIdentityCredentials.data;r.sts.credentialsFrom(r.data,r)}t(e)})}else{r.clearIdOnNotAuthorized(e);t(e)}})},loadCachedId:function e(){var t=this;if(i.util.isBrowser()&&!t.params.IdentityId){var r=t.getStorage("id");if(r&&t.params.Logins){var n=Object.keys(t.params.Logins);var a=(t.getStorage("providers")||"").split(",");var o=a.filter(function(e){return n.indexOf(e)!==-1});if(o.length!==0){t.params.IdentityId=r}}else if(r){t.params.IdentityId=r}}},createClients:function(){var e=this._clientConfig;this.webIdentityCredentials=this.webIdentityCredentials||new i.WebIdentityCredentials(this.params,e);if(!this.cognito){var t=i.util.merge({},e);t.params=this.params;this.cognito=new i.CognitoIdentity(t)}this.sts=this.sts||new i.STS(e)},cacheId:function e(t){this._identityId=t.IdentityId;this.params.IdentityId=this._identityId;if(i.util.isBrowser()){this.setStorage("id",t.IdentityId);if(this.params.Logins){this.setStorage("providers",Object.keys(this.params.Logins).join(","))}}},getStorage:function e(t){return this.storage[this.localStorageKey[t]+this.params.IdentityPoolId+(this.params.LoginId||"")]},setStorage:function e(t,r){try{this.storage[this.localStorageKey[t]+this.params.IdentityPoolId+(this.params.LoginId||"")]=r}catch(e){}},storage:function(){try{var e=i.util.isBrowser()&&window.localStorage!==null&&typeof window.localStorage==="object"?window.localStorage:{};e["aws.test-storage"]="foobar";delete e["aws.test-storage"];return e}catch(e){return{}}}()})},{"../core":38}],29:[function(e,t,r){var i=e("./browserHmac");var n=e("./browserMd5");var a=e("./browserSha1");var o=e("./browserSha256");t.exports=r={createHash:function e(t){t=t.toLowerCase();if(t==="md5"){return new n}else if(t==="sha256"){return new o}else if(t==="sha1"){return new a}throw new Error("Hash algorithm "+t+" is not supported in the browser SDK")},createHmac:function e(t,r){t=t.toLowerCase();if(t==="md5"){return new i(n,r)}else if(t==="sha256"){return new i(o,r)}else if(t==="sha1"){return new i(a,r)}throw new Error("HMAC algorithm "+t+" is not supported in the browser SDK")},createSign:function(){throw new Error("createSign is not implemented in the browser")}}},{"./browserHmac":31,"./browserMd5":32,"./browserSha1":33,"./browserSha256":34}],34:[function(e,t,r){var i=e("buffer/").Buffer;var n=e("./browserHashUtils");var a=64;var o=32;var s=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]);var u=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var c=Math.pow(2,53)-1;function l(){this.state=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];this.temp=new Int32Array(64);this.buffer=new Uint8Array(64);this.bufferLength=0;this.bytesHashed=0;this.finished=false}t.exports=r=l;l.BLOCK_SIZE=a;l.prototype.update=function(e){if(this.finished){throw new Error("Attempted to update an already finished hash.")}if(n.isEmptyData(e)){return this}e=n.convertToBuffer(e);var t=0;var r=e.byteLength;this.bytesHashed+=r;if(this.bytesHashed*8>c){throw new Error("Cannot hash more than 2^53 - 1 bits")}while(r>0){this.buffer[this.bufferLength++]=e[t++];r--;if(this.bufferLength===a){this.hashBuffer();this.bufferLength=0}}return this};l.prototype.digest=function(e){if(!this.finished){var t=this.bytesHashed*8;var r=new DataView(this.buffer.buffer,this.buffer.byteOffset,this.buffer.byteLength);var n=this.bufferLength;r.setUint8(this.bufferLength++,128);if(n%a>=a-8){for(var s=this.bufferLength;s<a;s++){r.setUint8(s,0)}this.hashBuffer();this.bufferLength=0}for(var s=this.bufferLength;s<a-8;s++){r.setUint8(s,0)}r.setUint32(a-8,Math.floor(t/4294967296),true);r.setUint32(a-4,t);this.hashBuffer();this.finished=true}var u=new i(o);for(var s=0;s<8;s++){u[s*4]=this.state[s]>>>24&255;u[s*4+1]=this.state[s]>>>16&255;u[s*4+2]=this.state[s]>>>8&255;u[s*4+3]=this.state[s]>>>0&255}return e?u.toString(e):u};l.prototype.hashBuffer=function(){var e=this,t=e.buffer,r=e.state;var i=r[0],n=r[1],o=r[2],u=r[3],c=r[4],l=r[5],f=r[6],p=r[7];for(var h=0;h<a;h++){if(h<16){this.temp[h]=(t[h*4]&255)<<24|(t[h*4+1]&255)<<16|(t[h*4+2]&255)<<8|t[h*4+3]&255}else{var d=this.temp[h-2];var m=(d>>>17|d<<15)^(d>>>19|d<<13)^d>>>10;d=this.temp[h-15];var y=(d>>>7|d<<25)^(d>>>18|d<<14)^d>>>3;this.temp[h]=(m+this.temp[h-7]|0)+(y+this.temp[h-16]|0)}var v=(((c>>>6|c<<26)^(c>>>11|c<<21)^(c>>>25|c<<7))+(c&l^~c&f)|0)+(p+(s[h]+this.temp[h]|0)|0)|0;var g=((i>>>2|i<<30)^(i>>>13|i<<19)^(i>>>22|i<<10))+(i&n^i&o^n&o)|0;p=f;f=l;l=c;c=u+v|0;u=o;o=n;n=i;i=v+g|0}r[0]+=i;r[1]+=n;r[2]+=o;r[3]+=u;r[4]+=c;r[5]+=l;r[6]+=f;r[7]+=p}},{"./browserHashUtils":30,"buffer/":3}],33:[function(e,t,r){var i=e("buffer/").Buffer;var n=e("./browserHashUtils");var a=64;var o=20;var s=new Uint32Array([1518500249,1859775393,2400959708|0,3395469782|0]);var u=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var c=Math.pow(2,53)-1;function l(){this.h0=1732584193;this.h1=4023233417;this.h2=2562383102;this.h3=271733878;this.h4=3285377520;this.block=new Uint32Array(80);this.offset=0;this.shift=24;this.totalLength=0}t.exports=r=l;l.BLOCK_SIZE=a;l.prototype.update=function(e){if(this.finished){throw new Error("Attempted to update an already finished hash.")}if(n.isEmptyData(e)){return this}e=n.convertToBuffer(e);var t=e.length;this.totalLength+=t*8;for(var r=0;r<t;r++){this.write(e[r])}return this};l.prototype.write=function e(t){this.block[this.offset]|=(t&255)<<this.shift;if(this.shift){this.shift-=8}else{this.offset++;this.shift=24}if(this.offset===16)this.processBlock()};l.prototype.digest=function(e){this.write(128);if(this.offset>14||this.offset===14&&this.shift<24){this.processBlock()}this.offset=14;this.shift=24;this.write(0);this.write(0);this.write(this.totalLength>0xffffffffff?this.totalLength/1099511627776:0);this.write(this.totalLength>4294967295?this.totalLength/4294967296:0);for(var t=24;t>=0;t-=8){this.write(this.totalLength>>t)}var r=new i(o);var n=new DataView(r.buffer);n.setUint32(0,this.h0,false);n.setUint32(4,this.h1,false);n.setUint32(8,this.h2,false);n.setUint32(12,this.h3,false);n.setUint32(16,this.h4,false);return e?r.toString(e):r};l.prototype.processBlock=function e(){for(var t=16;t<80;t++){var r=this.block[t-3]^this.block[t-8]^this.block[t-14]^this.block[t-16];this.block[t]=r<<1|r>>>31}var i=this.h0;var n=this.h1;var a=this.h2;var o=this.h3;var s=this.h4;var u,c;for(t=0;t<80;t++){if(t<20){u=o^n&(a^o);c=1518500249}else if(t<40){u=n^a^o;c=1859775393}else if(t<60){u=n&a|o&(n|a);c=2400959708}else{u=n^a^o;c=3395469782}var l=(i<<5|i>>>27)+u+s+c+(this.block[t]|0);s=o;o=a;a=n<<30|n>>>2;n=i;i=l}this.h0=this.h0+i|0;this.h1=this.h1+n|0;this.h2=this.h2+a|0;this.h3=this.h3+o|0;this.h4=this.h4+s|0;this.offset=0;for(t=0;t<16;t++){this.block[t]=0}}},{"./browserHashUtils":30,"buffer/":3}],32:[function(e,t,r){var i=e("./browserHashUtils");var n=e("buffer/").Buffer;var a=64;var o=16;var s=[1732584193,4023233417,2562383102,271733878];function u(){this.state=[1732584193,4023233417,2562383102,271733878];this.buffer=new DataView(new ArrayBuffer(a));this.bufferLength=0;this.bytesHashed=0;this.finished=false}t.exports=r=u;u.BLOCK_SIZE=a;u.prototype.update=function(e){if(i.isEmptyData(e)){return this}else if(this.finished){throw new Error("Attempted to update an already finished hash.")}var t=i.convertToBuffer(e);var r=0;var n=t.byteLength;this.bytesHashed+=n;while(n>0){this.buffer.setUint8(this.bufferLength++,t[r++]);n--;if(this.bufferLength===a){this.hashBuffer();this.bufferLength=0}}return this};u.prototype.digest=function(e){if(!this.finished){var t=this,r=t.buffer,i=t.bufferLength,s=t.bytesHashed;var u=s*8;r.setUint8(this.bufferLength++,128);if(i%a>=a-8){for(var c=this.bufferLength;c<a;c++){r.setUint8(c,0)}this.hashBuffer();this.bufferLength=0}for(var c=this.bufferLength;c<a-8;c++){r.setUint8(c,0)}r.setUint32(a-8,u>>>0,true);r.setUint32(a-4,Math.floor(u/4294967296),true);this.hashBuffer();this.finished=true}var l=new DataView(new ArrayBuffer(o));for(var c=0;c<4;c++){l.setUint32(c*4,this.state[c],true)}var f=new n(l.buffer,l.byteOffset,l.byteLength);return e?f.toString(e):f};u.prototype.hashBuffer=function(){var e=this,t=e.buffer,r=e.state;var i=r[0],n=r[1],a=r[2],o=r[3];i=l(i,n,a,o,t.getUint32(0,true),7,3614090360);o=l(o,i,n,a,t.getUint32(4,true),12,3905402710);a=l(a,o,i,n,t.getUint32(8,true),17,606105819);n=l(n,a,o,i,t.getUint32(12,true),22,3250441966);i=l(i,n,a,o,t.getUint32(16,true),7,4118548399);o=l(o,i,n,a,t.getUint32(20,true),12,1200080426);a=l(a,o,i,n,t.getUint32(24,true),17,2821735955);n=l(n,a,o,i,t.getUint32(28,true),22,4249261313);i=l(i,n,a,o,t.getUint32(32,true),7,1770035416);o=l(o,i,n,a,t.getUint32(36,true),12,2336552879);a=l(a,o,i,n,t.getUint32(40,true),17,4294925233);n=l(n,a,o,i,t.getUint32(44,true),22,2304563134);i=l(i,n,a,o,t.getUint32(48,true),7,1804603682);o=l(o,i,n,a,t.getUint32(52,true),12,4254626195);a=l(a,o,i,n,t.getUint32(56,true),17,2792965006);n=l(n,a,o,i,t.getUint32(60,true),22,1236535329);i=f(i,n,a,o,t.getUint32(4,true),5,4129170786);o=f(o,i,n,a,t.getUint32(24,true),9,3225465664);a=f(a,o,i,n,t.getUint32(44,true),14,643717713);n=f(n,a,o,i,t.getUint32(0,true),20,3921069994);i=f(i,n,a,o,t.getUint32(20,true),5,3593408605);o=f(o,i,n,a,t.getUint32(40,true),9,38016083);a=f(a,o,i,n,t.getUint32(60,true),14,3634488961);n=f(n,a,o,i,t.getUint32(16,true),20,3889429448);i=f(i,n,a,o,t.getUint32(36,true),5,568446438);o=f(o,i,n,a,t.getUint32(56,true),9,3275163606);a=f(a,o,i,n,t.getUint32(12,true),14,4107603335);n=f(n,a,o,i,t.getUint32(32,true),20,1163531501);i=f(i,n,a,o,t.getUint32(52,true),5,2850285829);o=f(o,i,n,a,t.getUint32(8,true),9,4243563512);a=f(a,o,i,n,t.getUint32(28,true),14,1735328473);n=f(n,a,o,i,t.getUint32(48,true),20,2368359562);i=p(i,n,a,o,t.getUint32(20,true),4,4294588738);o=p(o,i,n,a,t.getUint32(32,true),11,2272392833);a=p(a,o,i,n,t.getUint32(44,true),16,1839030562);n=p(n,a,o,i,t.getUint32(56,true),23,4259657740);i=p(i,n,a,o,t.getUint32(4,true),4,2763975236);o=p(o,i,n,a,t.getUint32(16,true),11,1272893353);a=p(a,o,i,n,t.getUint32(28,true),16,4139469664);n=p(n,a,o,i,t.getUint32(40,true),23,3200236656);i=p(i,n,a,o,t.getUint32(52,true),4,681279174);o=p(o,i,n,a,t.getUint32(0,true),11,3936430074);a=p(a,o,i,n,t.getUint32(12,true),16,3572445317);n=p(n,a,o,i,t.getUint32(24,true),23,76029189);i=p(i,n,a,o,t.getUint32(36,true),4,3654602809);o=p(o,i,n,a,t.getUint32(48,true),11,3873151461);a=p(a,o,i,n,t.getUint32(60,true),16,530742520);n=p(n,a,o,i,t.getUint32(8,true),23,3299628645);i=h(i,n,a,o,t.getUint32(0,true),6,4096336452);o=h(o,i,n,a,t.getUint32(28,true),10,1126891415);a=h(a,o,i,n,t.getUint32(56,true),15,2878612391);n=h(n,a,o,i,t.getUint32(20,true),21,4237533241);i=h(i,n,a,o,t.getUint32(48,true),6,1700485571);o=h(o,i,n,a,t.getUint32(12,true),10,2399980690);a=h(a,o,i,n,t.getUint32(40,true),15,4293915773);n=h(n,a,o,i,t.getUint32(4,true),21,2240044497);i=h(i,n,a,o,t.getUint32(32,true),6,1873313359);o=h(o,i,n,a,t.getUint32(60,true),10,4264355552);a=h(a,o,i,n,t.getUint32(24,true),15,2734768916);n=h(n,a,o,i,t.getUint32(52,true),21,1309151649);i=h(i,n,a,o,t.getUint32(16,true),6,4149444226);o=h(o,i,n,a,t.getUint32(44,true),10,3174756917);a=h(a,o,i,n,t.getUint32(8,true),15,718787259);n=h(n,a,o,i,t.getUint32(36,true),21,3951481745);r[0]=i+r[0]&4294967295;r[1]=n+r[1]&4294967295;r[2]=a+r[2]&4294967295;r[3]=o+r[3]&4294967295};function c(e,t,r,i,n,a){t=(t+e&4294967295)+(i+a&4294967295)&4294967295;return(t<<n|t>>>32-n)+r&4294967295}function l(e,t,r,i,n,a,o){return c(t&r|~t&i,e,t,n,a,o)}function f(e,t,r,i,n,a,o){return c(t&i|r&~i,e,t,n,a,o)}function p(e,t,r,i,n,a,o){return c(t^r^i,e,t,n,a,o)}function h(e,t,r,i,n,a,o){return c(r^(t|~i),e,t,n,a,o)}},{"./browserHashUtils":30,"buffer/":3}],31:[function(e,t,r){var i=e("./browserHashUtils");function n(e,t){this.hash=new e;this.outer=new e;var r=a(e,t);var i=new Uint8Array(e.BLOCK_SIZE);i.set(r);for(var n=0;n<e.BLOCK_SIZE;n++){r[n]^=54;i[n]^=92}this.hash.update(r);this.outer.update(i);for(var n=0;n<r.byteLength;n++){r[n]=0}}t.exports=r=n;n.prototype.update=function(e){if(i.isEmptyData(e)||this.error){return this}try{this.hash.update(i.convertToBuffer(e))}catch(e){this.error=e}return this};n.prototype.digest=function(e){if(!this.outer.finished){this.outer.update(this.hash.digest())}return this.outer.digest(e)};function a(e,t){var r=i.convertToBuffer(t);if(r.byteLength>e.BLOCK_SIZE){var n=new e;n.update(r);r=n.digest()}var a=new Uint8Array(e.BLOCK_SIZE);a.set(r);return a}},{"./browserHashUtils":30}],30:[function(e,t,r){var i=e("buffer/").Buffer;if(typeof ArrayBuffer!=="undefined"&&typeof ArrayBuffer.isView==="undefined"){ArrayBuffer.isView=function(e){return n.indexOf(Object.prototype.toString.call(e))>-1}}var n=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]","[object DataView]"];function a(e){if(typeof e==="string"){return e.length===0}return e.byteLength===0}function o(e){if(typeof e==="string"){e=new i(e,"utf8")}if(ArrayBuffer.isView(e)){return new Uint8Array(e.buffer,e.byteOffset,e.byteLength/Uint8Array.BYTES_PER_ELEMENT)}return new Uint8Array(e)}t.exports=r={isEmptyData:a,convertToBuffer:o}},{"buffer/":3}],18:[function(e,t,r){var i=e("punycode");r.parse=b;r.resolve=k;r.resolveObject=C;r.format=S;r.Url=n;function n(){this.protocol=null;this.slashes=null;this.auth=null;this.host=null;this.port=null;this.hostname=null;this.hash=null;this.search=null;this.query=null;this.pathname=null;this.path=null;this.href=null}var a=/^([a-z0-9.+-]+:)/i,o=/:[0-9]*$/,s=["<",">",'"',"`"," ","\r","\n","\t"],u=["{","}","|","\\","^","`"].concat(s),c=["'"].concat(u),l=["%","/","?",";","#"].concat(c),f=["/","?","#"],p=255,h=/^[a-z0-9A-Z_-]{0,63}$/,d=/^([a-z0-9A-Z_-]{0,63})(.*)$/,m={javascript:true,"javascript:":true},y={javascript:true,"javascript:":true},v={http:true,https:true,ftp:true,gopher:true,file:true,"http:":true,"https:":true,"ftp:":true,"gopher:":true,"file:":true},g=e("querystring");function b(e,t,r){if(e&&w(e)&&e instanceof n)return e;var i=new n;i.parse(e,t,r);return i}n.prototype.parse=function(e,t,r){if(!E(e)){throw new TypeError("Parameter 'url' must be a string, not "+typeof e)}var n=e;n=n.trim();var o=a.exec(n);if(o){o=o[0];var s=o.toLowerCase();this.protocol=s;n=n.substr(o.length)}if(r||o||n.match(/^\/\/[^@\/]+@[^@\/]+/)){var u=n.substr(0,2)==="//";if(u&&!(o&&y[o])){n=n.substr(2);this.slashes=true}}if(!y[o]&&(u||o&&!v[o])){var b=-1;for(var S=0;S<f.length;S++){var k=n.indexOf(f[S]);if(k!==-1&&(b===-1||k<b))b=k}var C,w;if(b===-1){w=n.lastIndexOf("@")}else{w=n.lastIndexOf("@",b)}if(w!==-1){C=n.slice(0,w);n=n.slice(w+1);this.auth=decodeURIComponent(C)}b=-1;for(var S=0;S<l.length;S++){var k=n.indexOf(l[S]);if(k!==-1&&(b===-1||k<b))b=k}if(b===-1)b=n.length;this.host=n.slice(0,b);n=n.slice(b);this.parseHost();this.hostname=this.hostname||"";var x=this.hostname[0]==="["&&this.hostname[this.hostname.length-1]==="]";if(!x){var N=this.hostname.split(/\./);for(var S=0,R=N.length;S<R;S++){var q=N[S];if(!q)continue;if(!q.match(h)){var _="";for(var B=0,T=q.length;B<T;B++){if(q.charCodeAt(B)>127){_+="x"}else{_+=q[B]}}if(!_.match(h)){var A=N.slice(0,S);var P=N.slice(S+1);var I=q.match(d);if(I){A.push(I[1]);P.unshift(I[2])}if(P.length){n="/"+P.join(".")+n}this.hostname=A.join(".");break}}}}if(this.hostname.length>p){this.hostname=""}else{this.hostname=this.hostname.toLowerCase()}if(!x){var L=this.hostname.split(".");var M=[];for(var S=0;S<L.length;++S){var U=L[S];M.push(U.match(/[^A-Za-z0-9_-]/)?"xn--"+i.encode(U):U)}this.hostname=M.join(".")}var D=this.port?":"+this.port:"";var O=this.hostname||"";this.host=O+D;this.href+=this.host;if(x){this.hostname=this.hostname.substr(1,this.hostname.length-2);if(n[0]!=="/"){n="/"+n}}}if(!m[s]){for(var S=0,R=c.length;S<R;S++){var z=c[S];var j=encodeURIComponent(z);if(j===z){j=escape(z)}n=n.split(z).join(j)}}var K=n.indexOf("#");if(K!==-1){this.hash=n.substr(K);n=n.slice(0,K)}var F=n.indexOf("?");if(F!==-1){this.search=n.substr(F);this.query=n.substr(F+1);if(t){this.query=g.parse(this.query)}n=n.slice(0,F)}else if(t){this.search="";this.query={}}if(n)this.pathname=n;if(v[s]&&this.hostname&&!this.pathname){this.pathname="/"}if(this.pathname||this.search){var D=this.pathname||"";var U=this.search||"";this.path=D+U}this.href=this.format();return this};function S(e){if(E(e))e=b(e);if(!(e instanceof n))return n.prototype.format.call(e);return e.format()}n.prototype.format=function(){var e=this.auth||"";if(e){e=encodeURIComponent(e);e=e.replace(/%3A/i,":");e+="@"}var t=this.protocol||"",r=this.pathname||"",i=this.hash||"",n=false,a="";if(this.host){n=e+this.host}else if(this.hostname){n=e+(this.hostname.indexOf(":")===-1?this.hostname:"["+this.hostname+"]");if(this.port){n+=":"+this.port}}if(this.query&&w(this.query)&&Object.keys(this.query).length){a=g.stringify(this.query)}var o=this.search||a&&"?"+a||"";if(t&&t.substr(-1)!==":")t+=":";if(this.slashes||(!t||v[t])&&n!==false){n="//"+(n||"");if(r&&r.charAt(0)!=="/")r="/"+r}else if(!n){n=""}if(i&&i.charAt(0)!=="#")i="#"+i;if(o&&o.charAt(0)!=="?")o="?"+o;r=r.replace(/[?#]/g,function(e){return encodeURIComponent(e)});o=o.replace("#","%23");return t+n+r+o+i};function k(e,t){return b(e,false,true).resolve(t)}n.prototype.resolve=function(e){return this.resolveObject(b(e,false,true)).format()};function C(e,t){if(!e)return t;return b(e,false,true).resolveObject(t)}n.prototype.resolveObject=function(e){if(E(e)){var t=new n;t.parse(e,false,true);e=t}var r=new n;Object.keys(this).forEach(function(e){r[e]=this[e]},this);r.hash=e.hash;if(e.href===""){r.href=r.format();return r}if(e.slashes&&!e.protocol){Object.keys(e).forEach(function(t){if(t!=="protocol")r[t]=e[t]});if(v[r.protocol]&&r.hostname&&!r.pathname){r.path=r.pathname="/"}r.href=r.format();return r}if(e.protocol&&e.protocol!==r.protocol){if(!v[e.protocol]){Object.keys(e).forEach(function(t){r[t]=e[t]});r.href=r.format();return r}r.protocol=e.protocol;if(!e.host&&!y[e.protocol]){var i=(e.pathname||"").split("/");while(i.length&&!(e.host=i.shift()));if(!e.host)e.host="";if(!e.hostname)e.hostname="";if(i[0]!=="")i.unshift("");if(i.length<2)i.unshift("");r.pathname=i.join("/")}else{r.pathname=e.pathname}r.search=e.search;r.query=e.query;r.host=e.host||"";r.auth=e.auth;r.hostname=e.hostname||e.host;r.port=e.port;if(r.pathname||r.search){var a=r.pathname||"";var o=r.search||"";r.path=a+o}r.slashes=r.slashes||e.slashes;r.href=r.format();return r}var s=r.pathname&&r.pathname.charAt(0)==="/",u=e.host||e.pathname&&e.pathname.charAt(0)==="/",c=u||s||r.host&&e.pathname,l=c,f=r.pathname&&r.pathname.split("/")||[],i=e.pathname&&e.pathname.split("/")||[],p=r.protocol&&!v[r.protocol];if(p){r.hostname="";r.port=null;if(r.host){if(f[0]==="")f[0]=r.host;else f.unshift(r.host)}r.host="";if(e.protocol){e.hostname=null;e.port=null;if(e.host){if(i[0]==="")i[0]=e.host;else i.unshift(e.host)}e.host=null}c=c&&(i[0]===""||f[0]==="")}if(u){r.host=e.host||e.host===""?e.host:r.host;r.hostname=e.hostname||e.hostname===""?e.hostname:r.hostname;r.search=e.search;r.query=e.query;f=i}else if(i.length){if(!f)f=[];f.pop();f=f.concat(i);r.search=e.search;r.query=e.query}else if(!N(e.search)){if(p){r.hostname=r.host=f.shift();var h=r.host&&r.host.indexOf("@")>0?r.host.split("@"):false;if(h){r.auth=h.shift();r.host=r.hostname=h.shift()}}r.search=e.search;r.query=e.query;if(!x(r.pathname)||!x(r.search)){r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")}r.href=r.format();return r}if(!f.length){r.pathname=null;if(r.search){r.path="/"+r.search}else{r.path=null}r.href=r.format();return r}var d=f.slice(-1)[0];var m=(r.host||e.host)&&(d==="."||d==="..")||d==="";var g=0;for(var b=f.length;b>=0;b--){d=f[b];if(d=="."){f.splice(b,1)}else if(d===".."){f.splice(b,1);g++}else if(g){f.splice(b,1);g--}}if(!c&&!l){for(;g--;g){f.unshift("..")}}if(c&&f[0]!==""&&(!f[0]||f[0].charAt(0)!=="/")){f.unshift("")}if(m&&f.join("/").substr(-1)!=="/"){f.push("")}var S=f[0]===""||f[0]&&f[0].charAt(0)==="/";if(p){r.hostname=r.host=S?"":f.length?f.shift():"";var h=r.host&&r.host.indexOf("@")>0?r.host.split("@"):false;if(h){r.auth=h.shift();r.host=r.hostname=h.shift()}}c=c||r.host&&f.length;if(c&&!S){f.unshift("")}if(!f.length){r.pathname=null;r.path=null}else{r.pathname=f.join("/")}if(!x(r.pathname)||!x(r.search)){r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")}r.auth=e.auth||r.auth;r.slashes=r.slashes||e.slashes;r.href=r.format();return r};n.prototype.parseHost=function(){var e=this.host;var t=o.exec(e);if(t){t=t[0];if(t!==":"){this.port=t.substr(1)}e=e.substr(0,e.length-t.length)}if(e)this.hostname=e};function E(e){return typeof e==="string"}function w(e){return typeof e==="object"&&e!==null}function x(e){return e===null}function N(e){return e==null}},{punycode:10,querystring:13}],16:[function(e,t,r){arguments[4][13][0].apply(r,arguments)},{"./decode":14,"./encode":15,dup:13}],15:[function(e,t,r){"use strict";var i=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}};t.exports=function(e,t,r,n){t=t||"&";r=r||"=";if(e===null){e=undefined}if(typeof e==="object"){return Object.keys(e).map(function(n){var a=encodeURIComponent(i(n))+r;if(Array.isArray(e[n])){return e[n].map(function(e){return a+encodeURIComponent(i(e))}).join(t)}else{return a+encodeURIComponent(i(e[n]))}}).join(t)}if(!n)return"";return encodeURIComponent(i(n))+r+encodeURIComponent(i(e))}},{}],14:[function(e,t,r){"use strict";function i(e,t){return Object.prototype.hasOwnProperty.call(e,t)}t.exports=function(e,t,r,n){t=t||"&";r=r||"=";var a={};if(typeof e!=="string"||e.length===0){return a}var o=/\+/g;e=e.split(t);var s=1e3;if(n&&typeof n.maxKeys==="number"){s=n.maxKeys}var u=e.length;if(s>0&&u>s){u=s}for(var c=0;c<u;++c){var l=e[c].replace(o,"%20"),f=l.indexOf(r),p,h,d,m;if(f>=0){p=l.substr(0,f);h=l.substr(f+1)}else{p=l;h=""}d=decodeURIComponent(p);m=decodeURIComponent(h);if(!i(a,d)){a[d]=m}else if(Array.isArray(a[d])){a[d].push(m)}else{a[d]=[a[d],m]}}return a}},{}],13:[function(e,t,r){"use strict";r.decode=r.parse=e("./decode");r.encode=r.stringify=e("./encode")},{"./decode":11,"./encode":12}],12:[function(e,t,r){"use strict";var i=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}};t.exports=function(e,t,r,s){t=t||"&";r=r||"=";if(e===null){e=undefined}if(typeof e==="object"){return a(o(e),function(o){var s=encodeURIComponent(i(o))+r;if(n(e[o])){return a(e[o],function(e){return s+encodeURIComponent(i(e))}).join(t)}else{return s+encodeURIComponent(i(e[o]))}}).join(t)}if(!s)return"";return encodeURIComponent(i(s))+r+encodeURIComponent(i(e))};var n=Array.isArray||function(e){return Object.prototype.toString.call(e)==="[object Array]"};function a(e,t){if(e.map)return e.map(t);var r=[];for(var i=0;i<e.length;i++){r.push(t(e[i],i))}return r}var o=Object.keys||function(e){var t=[];for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t.push(r)}return t}},{}],11:[function(e,t,r){"use strict";function i(e,t){return Object.prototype.hasOwnProperty.call(e,t)}t.exports=function(e,t,r,a){t=t||"&";r=r||"=";var o={};if(typeof e!=="string"||e.length===0){return o}var s=/\+/g;e=e.split(t);var u=1e3;if(a&&typeof a.maxKeys==="number"){u=a.maxKeys}var c=e.length;if(u>0&&c>u){c=u}for(var l=0;l<c;++l){var f=e[l].replace(s,"%20"),p=f.indexOf(r),h,d,m,y;if(p>=0){h=f.substr(0,p);d=f.substr(p+1)}else{h=f;d=""}m=decodeURIComponent(h);y=decodeURIComponent(d);if(!i(o,m)){o[m]=y}else if(n(o[m])){o[m].push(y)}else{o[m]=[o[m],y]}}return o};var n=Array.isArray||function(e){return Object.prototype.toString.call(e)==="[object Array]"}},{}],10:[function(e,t,r){(function(e){(function(i){var n=typeof r=="object"&&r&&!r.nodeType&&r;var a=typeof t=="object"&&t&&!t.nodeType&&t;var o=typeof e=="object"&&e;if(o.global===o||o.window===o||o.self===o){i=o}var s,u=2147483647,c=36,l=1,f=26,p=38,h=700,d=72,m=128,y="-",v=/^xn--/,g=/[^\x20-\x7E]/,b=/[\x2E\u3002\uFF0E\uFF61]/g,S={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},k=c-l,C=Math.floor,E=String.fromCharCode,w;function x(e){throw RangeError(S[e])}function N(e,t){var r=e.length;var i=[];while(r--){i[r]=t(e[r])}return i}function R(e,t){var r=e.split("@");var i="";if(r.length>1){i=r[0]+"@";e=r[1]}e=e.replace(b,".");var n=e.split(".");var a=N(n,t).join(".");return i+a}function q(e){var t=[],r=0,i=e.length,n,a;while(r<i){n=e.charCodeAt(r++);if(n>=55296&&n<=56319&&r<i){a=e.charCodeAt(r++);if((a&64512)==56320){t.push(((n&1023)<<10)+(a&1023)+65536)}else{t.push(n);r--}}else{t.push(n)}}return t}function _(e){return N(e,function(e){var t="";if(e>65535){e-=65536;t+=E(e>>>10&1023|55296);e=56320|e&1023}t+=E(e);return t}).join("")}function B(e){if(e-48<10){return e-22}if(e-65<26){return e-65}if(e-97<26){return e-97}return c}function T(e,t){return e+22+75*(e<26)-((t!=0)<<5)}function A(e,t,r){var i=0;e=r?C(e/h):e>>1;e+=C(e/t);for(;e>k*f>>1;i+=c){e=C(e/k)}return C(i+(k+1)*e/(e+p))}function P(e){var t=[],r=e.length,i,n=0,a=m,o=d,s,p,h,v,g,b,S,k,E;s=e.lastIndexOf(y);if(s<0){s=0}for(p=0;p<s;++p){if(e.charCodeAt(p)>=128){x("not-basic")}t.push(e.charCodeAt(p))}for(h=s>0?s+1:0;h<r;){for(v=n,g=1,b=c;;b+=c){if(h>=r){x("invalid-input")}S=B(e.charCodeAt(h++));if(S>=c||S>C((u-n)/g)){x("overflow")}n+=S*g;k=b<=o?l:b>=o+f?f:b-o;if(S<k){break}E=c-k;if(g>C(u/E)){x("overflow")}g*=E}i=t.length+1;o=A(n-v,i,v==0);if(C(n/i)>u-a){x("overflow")}a+=C(n/i);n%=i;t.splice(n++,0,a)}return _(t)}function I(e){var t,r,i,n,a,o,s,p,h,v,g,b=[],S,k,w,N;e=q(e);S=e.length;t=m;r=0;a=d;for(o=0;o<S;++o){g=e[o];if(g<128){b.push(E(g))}}i=n=b.length;if(n){b.push(y)}while(i<S){for(s=u,o=0;o<S;++o){g=e[o];if(g>=t&&g<s){s=g}}k=i+1;if(s-t>C((u-r)/k)){x("overflow")}r+=(s-t)*k;t=s;for(o=0;o<S;++o){g=e[o];if(g<t&&++r>u){x("overflow")}if(g==t){for(p=r,h=c;;h+=c){v=h<=a?l:h>=a+f?f:h-a;if(p<v){break}N=p-v;w=c-v;b.push(E(T(v+N%w,0)));p=C(N/w)}b.push(E(T(p,0)));a=A(r,k,i==n);r=0;++i}}++r;++t}return b.join("")}function L(e){return R(e,function(e){return v.test(e)?P(e.slice(4).toLowerCase()):e})}function M(e){return R(e,function(e){return g.test(e)?"xn--"+I(e):e})}s={version:"1.3.2",ucs2:{decode:q,encode:_},decode:P,encode:I,toASCII:M,toUnicode:L};if(typeof define=="function"&&typeof define.amd=="object"&&define.amd){define("punycode",function(){return s})}else if(n&&a){if(t.exports==n){a.exports=s}else{for(w in s){s.hasOwnProperty(w)&&(n[w]=s[w])}}}else{i.punycode=s}})(this)}).call(this,typeof global!=="undefined"?global:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})},{}],4:[function(e,t,r){function i(){this._events=this._events||{};this._maxListeners=this._maxListeners||undefined}t.exports=i;i.EventEmitter=i;i.prototype._events=undefined;i.prototype._maxListeners=undefined;i.defaultMaxListeners=10;i.prototype.setMaxListeners=function(e){if(!a(e)||e<0||isNaN(e))throw TypeError("n must be a positive number");this._maxListeners=e;return this};i.prototype.emit=function(e){var t,r,i,a,u,c;if(!this._events)this._events={};if(e==="error"){if(!this._events.error||o(this._events.error)&&!this._events.error.length){t=arguments[1];if(t instanceof Error){throw t}else{var l=new Error('Uncaught, unspecified "error" event. ('+t+")");l.context=t;throw l}}}r=this._events[e];if(s(r))return false;if(n(r)){switch(arguments.length){case 1:r.call(this);break;case 2:r.call(this,arguments[1]);break;case 3:r.call(this,arguments[1],arguments[2]);break;default:a=Array.prototype.slice.call(arguments,1);r.apply(this,a)}}else if(o(r)){a=Array.prototype.slice.call(arguments,1);c=r.slice();i=c.length;for(u=0;u<i;u++)c[u].apply(this,a)}return true};i.prototype.addListener=function(e,t){var r;if(!n(t))throw TypeError("listener must be a function");if(!this._events)this._events={};if(this._events.newListener)this.emit("newListener",e,n(t.listener)?t.listener:t);if(!this._events[e])this._events[e]=t;else if(o(this._events[e]))this._events[e].push(t);else this._events[e]=[this._events[e],t];if(o(this._events[e])&&!this._events[e].warned){if(!s(this._maxListeners)){r=this._maxListeners}else{r=i.defaultMaxListeners}if(r&&r>0&&this._events[e].length>r){this._events[e].warned=true;console.error("(node) warning: possible EventEmitter memory "+"leak detected. %d listeners added. "+"Use emitter.setMaxListeners() to increase limit.",this._events[e].length);if(typeof console.trace==="function"){console.trace()}}}return this};i.prototype.on=i.prototype.addListener;i.prototype.once=function(e,t){if(!n(t))throw TypeError("listener must be a function");var r=false;function i(){this.removeListener(e,i);if(!r){r=true;t.apply(this,arguments)}}i.listener=t;this.on(e,i);return this};i.prototype.removeListener=function(e,t){var r,i,a,s;if(!n(t))throw TypeError("listener must be a function");if(!this._events||!this._events[e])return this;r=this._events[e];a=r.length;i=-1;if(r===t||n(r.listener)&&r.listener===t){delete this._events[e];if(this._events.removeListener)this.emit("removeListener",e,t)}else if(o(r)){for(s=a;s-- >0;){if(r[s]===t||r[s].listener&&r[s].listener===t){i=s;break}}if(i<0)return this;if(r.length===1){r.length=0;delete this._events[e]}else{r.splice(i,1)}if(this._events.removeListener)this.emit("removeListener",e,t)}return this};i.prototype.removeAllListeners=function(e){var t,r;if(!this._events)return this;if(!this._events.removeListener){if(arguments.length===0)this._events={};else if(this._events[e])delete this._events[e];return this}if(arguments.length===0){for(t in this._events){if(t==="removeListener")continue;this.removeAllListeners(t)}this.removeAllListeners("removeListener");this._events={};return this}r=this._events[e];if(n(r)){this.removeListener(e,r)}else if(r){while(r.length)this.removeListener(e,r[r.length-1])}delete this._events[e];return this};i.prototype.listeners=function(e){var t;if(!this._events||!this._events[e])t=[];else if(n(this._events[e]))t=[this._events[e]];else t=this._events[e].slice();return t};i.prototype.listenerCount=function(e){if(this._events){var t=this._events[e];if(n(t))return 1;else if(t)return t.length}return 0};i.listenerCount=function(e,t){return e.listenerCount(t)};function n(e){return typeof e==="function"}function a(e){return typeof e==="number"}function o(e){return typeof e==="object"&&e!==null}function s(e){return e===void 0}},{}],3:[function(e,t,r){(function(t){"use strict";var i=e("base64-js");var n=e("ieee754");var a=e("isarray");r.Buffer=c;r.SlowBuffer=b;r.INSPECT_MAX_BYTES=50;c.TYPED_ARRAY_SUPPORT=t.TYPED_ARRAY_SUPPORT!==undefined?t.TYPED_ARRAY_SUPPORT:o();r.kMaxLength=s();function o(){try{var e=new Uint8Array(1);e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}};return e.foo()===42&&typeof e.subarray==="function"&&e.subarray(1,1).byteLength===0}catch(e){return false}}function s(){return c.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function u(e,t){if(s()<t){throw new RangeError("Invalid typed array length")}if(c.TYPED_ARRAY_SUPPORT){e=new Uint8Array(t);e.__proto__=c.prototype}else{if(e===null){e=new c(t)}e.length=t}return e}function c(e,t,r){if(!c.TYPED_ARRAY_SUPPORT&&!(this instanceof c)){return new c(e,t,r)}if(typeof e==="number"){if(typeof t==="string"){throw new Error("If encoding is specified then the first argument must be a string")}return h(this,e)}return l(this,e,t,r)}c.poolSize=8192;c._augment=function(e){e.__proto__=c.prototype;return e};function l(e,t,r,i){if(typeof t==="number"){throw new TypeError('"value" argument must not be a number')}if(typeof ArrayBuffer!=="undefined"&&t instanceof ArrayBuffer){return y(e,t,r,i)}if(typeof t==="string"){return d(e,t,r)}return v(e,t)}c.from=function(e,t,r){return l(null,e,t,r)};if(c.TYPED_ARRAY_SUPPORT){c.prototype.__proto__=Uint8Array.prototype;c.__proto__=Uint8Array;if(typeof Symbol!=="undefined"&&Symbol.species&&c[Symbol.species]===c){Object.defineProperty(c,Symbol.species,{value:null,configurable:true})}}function f(e){if(typeof e!=="number"){throw new TypeError('"size" argument must be a number')}else if(e<0){throw new RangeError('"size" argument must not be negative')}}function p(e,t,r,i){f(t);if(t<=0){return u(e,t)}if(r!==undefined){return typeof i==="string"?u(e,t).fill(r,i):u(e,t).fill(r)}return u(e,t)}c.alloc=function(e,t,r){return p(null,e,t,r)};function h(e,t){f(t);e=u(e,t<0?0:g(t)|0);if(!c.TYPED_ARRAY_SUPPORT){for(var r=0;r<t;++r){e[r]=0}}return e}c.allocUnsafe=function(e){return h(null,e)};c.allocUnsafeSlow=function(e){return h(null,e)};function d(e,t,r){if(typeof r!=="string"||r===""){r="utf8"}if(!c.isEncoding(r)){throw new TypeError('"encoding" must be a valid string encoding')}var i=S(t,r)|0;e=u(e,i);var n=e.write(t,r);if(n!==i){e=e.slice(0,n)}return e}function m(e,t){var r=t.length<0?0:g(t.length)|0;e=u(e,r);for(var i=0;i<r;i+=1){e[i]=t[i]&255}return e}function y(e,t,r,i){t.byteLength;if(r<0||t.byteLength<r){throw new RangeError("'offset' is out of bounds")}if(t.byteLength<r+(i||0)){throw new RangeError("'length' is out of bounds")}if(r===undefined&&i===undefined){t=new Uint8Array(t)}else if(i===undefined){t=new Uint8Array(t,r)}else{t=new Uint8Array(t,r,i)}if(c.TYPED_ARRAY_SUPPORT){e=t;e.__proto__=c.prototype}else{e=m(e,t)}return e}function v(e,t){if(c.isBuffer(t)){var r=g(t.length)|0;e=u(e,r);if(e.length===0){return e}t.copy(e,0,0,r);return e}if(t){if(typeof ArrayBuffer!=="undefined"&&t.buffer instanceof ArrayBuffer||"length"in t){if(typeof t.length!=="number"||te(t.length)){return u(e,0)}return m(e,t)}if(t.type==="Buffer"&&a(t.data)){return m(e,t.data)}}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function g(e){if(e>=s()){throw new RangeError("Attempt to allocate Buffer larger than maximum "+"size: 0x"+s().toString(16)+" bytes")}return e|0}function b(e){if(+e!=e){e=0}return c.alloc(+e)}c.isBuffer=function e(t){return!!(t!=null&&t._isBuffer)};c.compare=function e(t,r){if(!c.isBuffer(t)||!c.isBuffer(r)){throw new TypeError("Arguments must be Buffers")}if(t===r)return 0;var i=t.length;var n=r.length;for(var a=0,o=Math.min(i,n);a<o;++a){if(t[a]!==r[a]){i=t[a];n=r[a];break}}if(i<n)return-1;if(n<i)return 1;return 0};c.isEncoding=function e(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return true;default:return false}};c.concat=function e(t,r){if(!a(t)){throw new TypeError('"list" argument must be an Array of Buffers')}if(t.length===0){return c.alloc(0)}var i;if(r===undefined){r=0;for(i=0;i<t.length;++i){r+=t[i].length}}var n=c.allocUnsafe(r);var o=0;for(i=0;i<t.length;++i){var s=t[i];if(!c.isBuffer(s)){throw new TypeError('"list" argument must be an Array of Buffers')}s.copy(n,o);o+=s.length}return n};function S(e,t){if(c.isBuffer(e)){return e.length}if(typeof ArrayBuffer!=="undefined"&&typeof ArrayBuffer.isView==="function"&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer)){return e.byteLength}if(typeof e!=="string"){e=""+e}var r=e.length;if(r===0)return 0;var i=false;for(;;){switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case undefined:return J(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return r*2;case"hex":return r>>>1;case"base64":return Z(e).length;default:if(i)return J(e).length;t=(""+t).toLowerCase();i=true}}}c.byteLength=S;function k(e,t,r){var i=false;if(t===undefined||t<0){t=0}if(t>this.length){return""}if(r===undefined||r>this.length){r=this.length}if(r<=0){return""}r>>>=0;t>>>=0;if(r<=t){return""}if(!e)e="utf8";while(true){switch(e){case"hex":return U(this,t,r);case"utf8":case"utf-8":return A(this,t,r);case"ascii":return L(this,t,r);case"latin1":case"binary":return M(this,t,r);case"base64":return T(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return D(this,t,r);default:if(i)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase();i=true}}}c.prototype._isBuffer=true;function C(e,t,r){var i=e[t];e[t]=e[r];e[r]=i}c.prototype.swap16=function e(){var t=this.length;if(t%2!==0){throw new RangeError("Buffer size must be a multiple of 16-bits")}for(var r=0;r<t;r+=2){C(this,r,r+1)}return this};c.prototype.swap32=function e(){var t=this.length;if(t%4!==0){throw new RangeError("Buffer size must be a multiple of 32-bits")}for(var r=0;r<t;r+=4){C(this,r,r+3);C(this,r+1,r+2)}return this};c.prototype.swap64=function e(){var t=this.length;if(t%8!==0){throw new RangeError("Buffer size must be a multiple of 64-bits")}for(var r=0;r<t;r+=8){C(this,r,r+7);C(this,r+1,r+6);C(this,r+2,r+5);C(this,r+3,r+4)}return this};c.prototype.toString=function e(){var t=this.length|0;if(t===0)return"";if(arguments.length===0)return A(this,0,t);return k.apply(this,arguments)};c.prototype.equals=function e(t){if(!c.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(this===t)return true;return c.compare(this,t)===0};c.prototype.inspect=function e(){var t="";var i=r.INSPECT_MAX_BYTES;if(this.length>0){t=this.toString("hex",0,i).match(/.{2}/g).join(" ");if(this.length>i)t+=" ... "}return"<Buffer "+t+">"};c.prototype.compare=function e(t,r,i,n,a){if(!c.isBuffer(t)){throw new TypeError("Argument must be a Buffer")}if(r===undefined){r=0}if(i===undefined){i=t?t.length:0}if(n===undefined){n=0}if(a===undefined){a=this.length}if(r<0||i>t.length||n<0||a>this.length){throw new RangeError("out of range index")}if(n>=a&&r>=i){return 0}if(n>=a){return-1}if(r>=i){return 1}r>>>=0;i>>>=0;n>>>=0;a>>>=0;if(this===t)return 0;var o=a-n;var s=i-r;var u=Math.min(o,s);var l=this.slice(n,a);var f=t.slice(r,i);for(var p=0;p<u;++p){if(l[p]!==f[p]){o=l[p];s=f[p];break}}if(o<s)return-1;if(s<o)return 1;return 0};function E(e,t,r,i,n){if(e.length===0)return-1;if(typeof r==="string"){i=r;r=0}else if(r>2147483647){r=2147483647}else if(r<-2147483648){r=-2147483648}r=+r;if(isNaN(r)){r=n?0:e.length-1}if(r<0)r=e.length+r;if(r>=e.length){if(n)return-1;else r=e.length-1}else if(r<0){if(n)r=0;else return-1}if(typeof t==="string"){t=c.from(t,i)}if(c.isBuffer(t)){if(t.length===0){return-1}return w(e,t,r,i,n)}else if(typeof t==="number"){t=t&255;if(c.TYPED_ARRAY_SUPPORT&&typeof Uint8Array.prototype.indexOf==="function"){if(n){return Uint8Array.prototype.indexOf.call(e,t,r)}else{return Uint8Array.prototype.lastIndexOf.call(e,t,r)}}return w(e,[t],r,i,n)}throw new TypeError("val must be string, number or Buffer")}function w(e,t,r,i,n){var a=1;var o=e.length;var s=t.length;if(i!==undefined){i=String(i).toLowerCase();if(i==="ucs2"||i==="ucs-2"||i==="utf16le"||i==="utf-16le"){if(e.length<2||t.length<2){return-1}a=2;o/=2;s/=2;r/=2}}function u(e,t){if(a===1){return e[t]}else{return e.readUInt16BE(t*a)}}var c;if(n){var l=-1;for(c=r;c<o;c++){if(u(e,c)===u(t,l===-1?0:c-l)){if(l===-1)l=c;if(c-l+1===s)return l*a}else{if(l!==-1)c-=c-l;l=-1}}}else{if(r+s>o)r=o-s;for(c=r;c>=0;c--){var f=true;for(var p=0;p<s;p++){if(u(e,c+p)!==u(t,p)){f=false;break}}if(f)return c}}return-1}c.prototype.includes=function e(t,r,i){return this.indexOf(t,r,i)!==-1};c.prototype.indexOf=function e(t,r,i){return E(this,t,r,i,true)};c.prototype.lastIndexOf=function e(t,r,i){return E(this,t,r,i,false)};function x(e,t,r,i){r=Number(r)||0;var n=e.length-r;if(!i){i=n}else{i=Number(i);if(i>n){i=n}}var a=t.length;if(a%2!==0)throw new TypeError("Invalid hex string");if(i>a/2){i=a/2}for(var o=0;o<i;++o){var s=parseInt(t.substr(o*2,2),16);if(isNaN(s))return o;e[r+o]=s}return o}function N(e,t,r,i){return ee(J(t,e.length-r),e,r,i)}function R(e,t,r,i){return ee(Q(t),e,r,i)}function q(e,t,r,i){return R(e,t,r,i)}function _(e,t,r,i){return ee(Z(t),e,r,i)}function B(e,t,r,i){return ee($(t,e.length-r),e,r,i)}c.prototype.write=function e(t,r,i,n){if(r===undefined){n="utf8";i=this.length;r=0}else if(i===undefined&&typeof r==="string"){n=r;i=this.length;r=0}else if(isFinite(r)){r=r|0;if(isFinite(i)){i=i|0;if(n===undefined)n="utf8"}else{n=i;i=undefined}}else{throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported")}var a=this.length-r;if(i===undefined||i>a)i=a;if(t.length>0&&(i<0||r<0)||r>this.length){throw new RangeError("Attempt to write outside buffer bounds")}if(!n)n="utf8";var o=false;for(;;){switch(n){case"hex":return x(this,t,r,i);case"utf8":case"utf-8":return N(this,t,r,i);case"ascii":return R(this,t,r,i);case"latin1":case"binary":return q(this,t,r,i);case"base64":return _(this,t,r,i);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return B(this,t,r,i);default:if(o)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase();o=true}}};c.prototype.toJSON=function e(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function T(e,t,r){if(t===0&&r===e.length){return i.fromByteArray(e)}else{return i.fromByteArray(e.slice(t,r))}}function A(e,t,r){r=Math.min(e.length,r);var i=[];var n=t;while(n<r){var a=e[n];var o=null;var s=a>239?4:a>223?3:a>191?2:1;if(n+s<=r){var u,c,l,f;switch(s){case 1:if(a<128){o=a}break;case 2:u=e[n+1];if((u&192)===128){f=(a&31)<<6|u&63;if(f>127){o=f}}break;case 3:u=e[n+1];c=e[n+2];if((u&192)===128&&(c&192)===128){f=(a&15)<<12|(u&63)<<6|c&63;if(f>2047&&(f<55296||f>57343)){o=f}}break;case 4:u=e[n+1];c=e[n+2];l=e[n+3];if((u&192)===128&&(c&192)===128&&(l&192)===128){f=(a&15)<<18|(u&63)<<12|(c&63)<<6|l&63;if(f>65535&&f<1114112){o=f}}}}if(o===null){o=65533;s=1}else if(o>65535){o-=65536;i.push(o>>>10&1023|55296);o=56320|o&1023}i.push(o);n+=s}return I(i)}var P=4096;function I(e){var t=e.length;if(t<=P){return String.fromCharCode.apply(String,e)}var r="";var i=0;while(i<t){r+=String.fromCharCode.apply(String,e.slice(i,i+=P))}return r}function L(e,t,r){var i="";r=Math.min(e.length,r);for(var n=t;n<r;++n){i+=String.fromCharCode(e[n]&127)}return i}function M(e,t,r){var i="";r=Math.min(e.length,r);for(var n=t;n<r;++n){i+=String.fromCharCode(e[n])}return i}function U(e,t,r){var i=e.length;if(!t||t<0)t=0;if(!r||r<0||r>i)r=i;var n="";for(var a=t;a<r;++a){n+=Y(e[a])}return n}function D(e,t,r){var i=e.slice(t,r);var n="";for(var a=0;a<i.length;a+=2){n+=String.fromCharCode(i[a]+i[a+1]*256)}return n}c.prototype.slice=function e(t,r){var i=this.length;t=~~t;r=r===undefined?i:~~r;if(t<0){t+=i;if(t<0)t=0}else if(t>i){t=i}if(r<0){r+=i;if(r<0)r=0}else if(r>i){r=i}if(r<t)r=t;var n;if(c.TYPED_ARRAY_SUPPORT){n=this.subarray(t,r);n.__proto__=c.prototype}else{var a=r-t;n=new c(a,undefined);for(var o=0;o<a;++o){n[o]=this[o+t]}}return n};function O(e,t,r){if(e%1!==0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}c.prototype.readUIntLE=function e(t,r,i){t=t|0;r=r|0;if(!i)O(t,r,this.length);var n=this[t];var a=1;var o=0;while(++o<r&&(a*=256)){n+=this[t+o]*a}return n};c.prototype.readUIntBE=function e(t,r,i){t=t|0;r=r|0;if(!i){O(t,r,this.length)}var n=this[t+--r];var a=1;while(r>0&&(a*=256)){n+=this[t+--r]*a}return n};c.prototype.readUInt8=function e(t,r){if(!r)O(t,1,this.length);return this[t]};c.prototype.readUInt16LE=function e(t,r){if(!r)O(t,2,this.length);return this[t]|this[t+1]<<8};c.prototype.readUInt16BE=function e(t,r){if(!r)O(t,2,this.length);return this[t]<<8|this[t+1]};c.prototype.readUInt32LE=function e(t,r){if(!r)O(t,4,this.length);return(this[t]|this[t+1]<<8|this[t+2]<<16)+this[t+3]*16777216};c.prototype.readUInt32BE=function e(t,r){if(!r)O(t,4,this.length);return this[t]*16777216+(this[t+1]<<16|this[t+2]<<8|this[t+3])};c.prototype.readIntLE=function e(t,r,i){t=t|0;r=r|0;if(!i)O(t,r,this.length);var n=this[t];var a=1;var o=0;while(++o<r&&(a*=256)){n+=this[t+o]*a}a*=128;if(n>=a)n-=Math.pow(2,8*r);return n};c.prototype.readIntBE=function e(t,r,i){t=t|0;r=r|0;if(!i)O(t,r,this.length);var n=r;var a=1;var o=this[t+--n];while(n>0&&(a*=256)){o+=this[t+--n]*a}a*=128;if(o>=a)o-=Math.pow(2,8*r);return o};c.prototype.readInt8=function e(t,r){if(!r)O(t,1,this.length);if(!(this[t]&128))return this[t];return(255-this[t]+1)*-1};c.prototype.readInt16LE=function e(t,r){if(!r)O(t,2,this.length);var i=this[t]|this[t+1]<<8;return i&32768?i|4294901760:i};c.prototype.readInt16BE=function e(t,r){if(!r)O(t,2,this.length);var i=this[t+1]|this[t]<<8;return i&32768?i|4294901760:i};c.prototype.readInt32LE=function e(t,r){if(!r)O(t,4,this.length);return this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24};c.prototype.readInt32BE=function e(t,r){if(!r)O(t,4,this.length);return this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]};c.prototype.readFloatLE=function e(t,r){if(!r)O(t,4,this.length);return n.read(this,t,true,23,4)};c.prototype.readFloatBE=function e(t,r){if(!r)O(t,4,this.length);return n.read(this,t,false,23,4)};c.prototype.readDoubleLE=function e(t,r){if(!r)O(t,8,this.length);return n.read(this,t,true,52,8)};c.prototype.readDoubleBE=function e(t,r){if(!r)O(t,8,this.length);return n.read(this,t,false,52,8)};function z(e,t,r,i,n,a){if(!c.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>n||t<a)throw new RangeError('"value" argument is out of bounds');if(r+i>e.length)throw new RangeError("Index out of range")}c.prototype.writeUIntLE=function e(t,r,i,n){t=+t;r=r|0;i=i|0;if(!n){var a=Math.pow(2,8*i)-1;z(this,t,r,i,a,0)}var o=1;var s=0;this[r]=t&255;while(++s<i&&(o*=256)){this[r+s]=t/o&255}return r+i};c.prototype.writeUIntBE=function e(t,r,i,n){t=+t;r=r|0;i=i|0;if(!n){var a=Math.pow(2,8*i)-1;z(this,t,r,i,a,0)}var o=i-1;var s=1;this[r+o]=t&255;while(--o>=0&&(s*=256)){this[r+o]=t/s&255}return r+i};c.prototype.writeUInt8=function e(t,r,i){t=+t;r=r|0;if(!i)z(this,t,r,1,255,0);if(!c.TYPED_ARRAY_SUPPORT)t=Math.floor(t);this[r]=t&255;return r+1};function j(e,t,r,i){if(t<0)t=65535+t+1;for(var n=0,a=Math.min(e.length-r,2);n<a;++n){e[r+n]=(t&255<<8*(i?n:1-n))>>>(i?n:1-n)*8}}c.prototype.writeUInt16LE=function e(t,r,i){t=+t;r=r|0;if(!i)z(this,t,r,2,65535,0);if(c.TYPED_ARRAY_SUPPORT){this[r]=t&255;this[r+1]=t>>>8}else{j(this,t,r,true)}return r+2};c.prototype.writeUInt16BE=function e(t,r,i){t=+t;r=r|0;if(!i)z(this,t,r,2,65535,0);if(c.TYPED_ARRAY_SUPPORT){this[r]=t>>>8;this[r+1]=t&255}else{j(this,t,r,false)}return r+2};function K(e,t,r,i){if(t<0)t=4294967295+t+1;for(var n=0,a=Math.min(e.length-r,4);n<a;++n){e[r+n]=t>>>(i?n:3-n)*8&255}}c.prototype.writeUInt32LE=function e(t,r,i){t=+t;r=r|0;if(!i)z(this,t,r,4,4294967295,0);if(c.TYPED_ARRAY_SUPPORT){this[r+3]=t>>>24;this[r+2]=t>>>16;this[r+1]=t>>>8;this[r]=t&255}else{K(this,t,r,true)}return r+4};c.prototype.writeUInt32BE=function e(t,r,i){t=+t;r=r|0;if(!i)z(this,t,r,4,4294967295,0);if(c.TYPED_ARRAY_SUPPORT){this[r]=t>>>24;this[r+1]=t>>>16;this[r+2]=t>>>8;this[r+3]=t&255}else{K(this,t,r,false)}return r+4};c.prototype.writeIntLE=function e(t,r,i,n){t=+t;r=r|0;if(!n){var a=Math.pow(2,8*i-1);z(this,t,r,i,a-1,-a)}var o=0;var s=1;var u=0;this[r]=t&255;while(++o<i&&(s*=256)){if(t<0&&u===0&&this[r+o-1]!==0){u=1}this[r+o]=(t/s>>0)-u&255}return r+i};c.prototype.writeIntBE=function e(t,r,i,n){t=+t;r=r|0;if(!n){var a=Math.pow(2,8*i-1);z(this,t,r,i,a-1,-a)}var o=i-1;var s=1;var u=0;this[r+o]=t&255;while(--o>=0&&(s*=256)){if(t<0&&u===0&&this[r+o+1]!==0){u=1}this[r+o]=(t/s>>0)-u&255}return r+i};c.prototype.writeInt8=function e(t,r,i){t=+t;r=r|0;if(!i)z(this,t,r,1,127,-128);if(!c.TYPED_ARRAY_SUPPORT)t=Math.floor(t);if(t<0)t=255+t+1;this[r]=t&255;return r+1};c.prototype.writeInt16LE=function e(t,r,i){t=+t;r=r|0;if(!i)z(this,t,r,2,32767,-32768);if(c.TYPED_ARRAY_SUPPORT){this[r]=t&255;this[r+1]=t>>>8}else{j(this,t,r,true)}return r+2};c.prototype.writeInt16BE=function e(t,r,i){t=+t;r=r|0;if(!i)z(this,t,r,2,32767,-32768);if(c.TYPED_ARRAY_SUPPORT){this[r]=t>>>8;this[r+1]=t&255}else{j(this,t,r,false)}return r+2};c.prototype.writeInt32LE=function e(t,r,i){t=+t;r=r|0;if(!i)z(this,t,r,4,2147483647,-2147483648);if(c.TYPED_ARRAY_SUPPORT){this[r]=t&255;this[r+1]=t>>>8;this[r+2]=t>>>16;this[r+3]=t>>>24}else{K(this,t,r,true)}return r+4};c.prototype.writeInt32BE=function e(t,r,i){t=+t;r=r|0;if(!i)z(this,t,r,4,2147483647,-2147483648);if(t<0)t=4294967295+t+1;if(c.TYPED_ARRAY_SUPPORT){this[r]=t>>>24;this[r+1]=t>>>16;this[r+2]=t>>>8;this[r+3]=t&255}else{K(this,t,r,false)}return r+4};function F(e,t,r,i,n,a){if(r+i>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function H(e,t,r,i,a){if(!a){F(e,t,r,4,3.4028234663852886e38,-3.4028234663852886e38)}n.write(e,t,r,i,23,4);return r+4}c.prototype.writeFloatLE=function e(t,r,i){return H(this,t,r,true,i)};c.prototype.writeFloatBE=function e(t,r,i){return H(this,t,r,false,i)};function V(e,t,r,i,a){if(!a){F(e,t,r,8,1.7976931348623157e308,-1.7976931348623157e308)}n.write(e,t,r,i,52,8);return r+8}c.prototype.writeDoubleLE=function e(t,r,i){return V(this,t,r,true,i)};c.prototype.writeDoubleBE=function e(t,r,i){return V(this,t,r,false,i)};c.prototype.copy=function e(t,r,i,n){if(!i)i=0;if(!n&&n!==0)n=this.length;if(r>=t.length)r=t.length;if(!r)r=0;if(n>0&&n<i)n=i;if(n===i)return 0;if(t.length===0||this.length===0)return 0;if(r<0){throw new RangeError("targetStart out of bounds")}if(i<0||i>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");if(n>this.length)n=this.length;if(t.length-r<n-i){n=t.length-r+i}var a=n-i;var o;if(this===t&&i<r&&r<n){for(o=a-1;o>=0;--o){t[o+r]=this[o+i]}}else if(a<1e3||!c.TYPED_ARRAY_SUPPORT){for(o=0;o<a;++o){t[o+r]=this[o+i]}}else{Uint8Array.prototype.set.call(t,this.subarray(i,i+a),r)}return a};c.prototype.fill=function e(t,r,i,n){if(typeof t==="string"){if(typeof r==="string"){n=r;r=0;i=this.length}else if(typeof i==="string"){n=i;i=this.length}if(t.length===1){var a=t.charCodeAt(0);if(a<256){t=a}}if(n!==undefined&&typeof n!=="string"){throw new TypeError("encoding must be a string")}if(typeof n==="string"&&!c.isEncoding(n)){throw new TypeError("Unknown encoding: "+n)}}else if(typeof t==="number"){t=t&255}if(r<0||this.length<r||this.length<i){throw new RangeError("Out of range index")}if(i<=r){return this}r=r>>>0;i=i===undefined?this.length:i>>>0;if(!t)t=0;var o;if(typeof t==="number"){for(o=r;o<i;++o){this[o]=t}}else{var s=c.isBuffer(t)?t:J(new c(t,n).toString());var u=s.length;for(o=0;o<i-r;++o){this[o+r]=s[o%u]}}return this};var G=/[^+\/0-9A-Za-z-_]/g;function W(e){e=X(e).replace(G,"");if(e.length<2)return"";while(e.length%4!==0){e=e+"="}return e}function X(e){if(e.trim)return e.trim();return e.replace(/^\s+|\s+$/g,"")}function Y(e){if(e<16)return"0"+e.toString(16);return e.toString(16)}function J(e,t){t=t||Infinity;var r;var i=e.length;var n=null;var a=[];for(var o=0;o<i;++o){r=e.charCodeAt(o);if(r>55295&&r<57344){if(!n){if(r>56319){if((t-=3)>-1)a.push(239,191,189);continue}else if(o+1===i){if((t-=3)>-1)a.push(239,191,189);continue}n=r;continue}if(r<56320){if((t-=3)>-1)a.push(239,191,189);n=r;continue}r=(n-55296<<10|r-56320)+65536}else if(n){if((t-=3)>-1)a.push(239,191,189)}n=null;if(r<128){if((t-=1)<0)break;a.push(r)}else if(r<2048){if((t-=2)<0)break;a.push(r>>6|192,r&63|128)}else if(r<65536){if((t-=3)<0)break;a.push(r>>12|224,r>>6&63|128,r&63|128)}else if(r<1114112){if((t-=4)<0)break;a.push(r>>18|240,r>>12&63|128,r>>6&63|128,r&63|128)}else{throw new Error("Invalid code point")}}return a}function Q(e){var t=[];for(var r=0;r<e.length;++r){t.push(e.charCodeAt(r)&255)}return t}function $(e,t){var r,i,n;var a=[];for(var o=0;o<e.length;++o){if((t-=2)<0)break;r=e.charCodeAt(o);i=r>>8;n=r%256;a.push(n);a.push(i)}return a}function Z(e){return i.toByteArray(W(e))}function ee(e,t,r,i){for(var n=0;n<i;++n){if(n+r>=t.length||n>=e.length)break;t[n+r]=e[n]}return n}function te(e){return e!==e}}).call(this,typeof global!=="undefined"?global:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})},{"base64-js":1,ieee754:5,isarray:7}],7:[function(e,t,r){var i={}.toString;t.exports=Array.isArray||function(e){return i.call(e)=="[object Array]"}},{}],5:[function(e,t,r){r.read=function(e,t,r,i,n){var a,o;var s=n*8-i-1;var u=(1<<s)-1;var c=u>>1;var l=-7;var f=r?n-1:0;var p=r?-1:1;var h=e[t+f];f+=p;a=h&(1<<-l)-1;h>>=-l;l+=s;for(;l>0;a=a*256+e[t+f],f+=p,l-=8){}o=a&(1<<-l)-1;a>>=-l;l+=i;for(;l>0;o=o*256+e[t+f],f+=p,l-=8){}if(a===0){a=1-c}else if(a===u){return o?NaN:(h?-1:1)*Infinity}else{o=o+Math.pow(2,i);a=a-c}return(h?-1:1)*o*Math.pow(2,a-i)};r.write=function(e,t,r,i,n,a){var o,s,u;var c=a*8-n-1;var l=(1<<c)-1;var f=l>>1;var p=n===23?Math.pow(2,-24)-Math.pow(2,-77):0;var h=i?0:a-1;var d=i?1:-1;var m=t<0||t===0&&1/t<0?1:0;t=Math.abs(t);if(isNaN(t)||t===Infinity){s=isNaN(t)?1:0;o=l}else{o=Math.floor(Math.log(t)/Math.LN2);if(t*(u=Math.pow(2,-o))<1){o--;u*=2}if(o+f>=1){t+=p/u}else{t+=p*Math.pow(2,1-f)}if(t*u>=2){o++;u/=2}if(o+f>=l){s=0;o=l}else if(o+f>=1){s=(t*u-1)*Math.pow(2,n);o=o+f}else{s=t*Math.pow(2,f-1)*Math.pow(2,n);o=0}}for(;n>=8;e[r+h]=s&255,h+=d,s/=256,n-=8){}o=o<<n|s;c+=n;for(;c>0;e[r+h]=o&255,h+=d,o/=256,c-=8){}e[r+h-d]|=m*128}},{}],1:[function(e,t,r){"use strict";r.byteLength=l;r.toByteArray=p;r.fromByteArray=m;var i=[];var n=[];var a=typeof Uint8Array!=="undefined"?Uint8Array:Array;var o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(var s=0,u=o.length;s<u;++s){i[s]=o[s];n[o.charCodeAt(s)]=s}n["-".charCodeAt(0)]=62;n["_".charCodeAt(0)]=63;function c(e){var t=e.length;if(t%4>0){throw new Error("Invalid string. Length must be a multiple of 4")}var r=e.indexOf("=");if(r===-1)r=t;var i=r===t?0:4-r%4;return[r,i]}function l(e){var t=c(e);var r=t[0];var i=t[1];return(r+i)*3/4-i}function f(e,t,r){return(t+r)*3/4-r}function p(e){var t;var r=c(e);var i=r[0];var o=r[1];var s=new a(f(e,i,o));var u=0;var l=o>0?i-4:i;for(var p=0;p<l;p+=4){t=n[e.charCodeAt(p)]<<18|n[e.charCodeAt(p+1)]<<12|n[e.charCodeAt(p+2)]<<6|n[e.charCodeAt(p+3)];s[u++]=t>>16&255;s[u++]=t>>8&255;s[u++]=t&255}if(o===2){t=n[e.charCodeAt(p)]<<2|n[e.charCodeAt(p+1)]>>4;s[u++]=t&255}if(o===1){t=n[e.charCodeAt(p)]<<10|n[e.charCodeAt(p+1)]<<4|n[e.charCodeAt(p+2)]>>2;s[u++]=t>>8&255;s[u++]=t&255}return s}function h(e){return i[e>>18&63]+i[e>>12&63]+i[e>>6&63]+i[e&63]}function d(e,t,r){var i;var n=[];for(var a=t;a<r;a+=3){i=(e[a]<<16&16711680)+(e[a+1]<<8&65280)+(e[a+2]&255);n.push(h(i))}return n.join("")}function m(e){var t;var r=e.length;var n=r%3;var a=[];var o=16383;for(var s=0,u=r-n;s<u;s+=o){a.push(d(e,s,s+o>u?u:s+o))}if(n===1){t=e[r-1];a.push(i[t>>2]+i[t<<4&63]+"==")}else if(n===2){t=(e[r-2]<<8)+e[r-1];a.push(i[t>>10]+i[t>>4&63]+i[t<<2&63]+"=")}return a.join("")}},{}]},{},[28]);OOS.apiLoader.services["s3"]={};OOS.S3=OOS.Service.defineService("s3",["2006-03-01"]);_xamzrequire=function e(t,r,i){function n(o,s){if(!r[o]){if(!t[o]){var u=typeof _xamzrequire=="function"&&_xamzrequire;if(!s&&u)return u(o,!0);if(a)return a(o,!0);var c=new Error("Cannot find module '"+o+"'");throw c.code="MODULE_NOT_FOUND",c}var l=r[o]={exports:{}};t[o][0].call(l.exports,function(e){var r=t[o][1][e];return n(r?r:e)},l,l.exports,e,t,r,i)}return r[o].exports}var a=typeof _xamzrequire=="function"&&_xamzrequire;for(var o=0;o<i.length;o++)n(i[o]);return n}({99:[function(e,t,r){var i=e("../core");var n=e("../signers/v4_credentials");e("../s3/managed_upload");var a={completeMultipartUpload:true,copyObject:true,uploadPartCopy:true};var o=["AuthorizationHeaderMalformed","BadRequest","PermanentRedirect",301];i.util.update(i.S3.prototype,{getSignatureVersion:function e(t){var r=this.api.signatureVersion;var i=this._originalConfig?this._originalConfig.signatureVersion:null;var n=this.config.signatureVersion;var a=t?t.isPresigned():false;if(i){i=i==="v2"?"s3":i;return i}if(a!==true){r="v4"}else if(n){r=n}return r},getSignerClass:function e(t){var r=this.getSignatureVersion(t);return i.Signers.RequestSigner.getVersion(r)},validateService:function e(){var t;var r=[];if(!this.config.region)this.config.region="us-east-1";if(!this.config.endpoint&&this.config.s3BucketEndpoint){r.push("An endpoint must be provided when configuring "+"`s3BucketEndpoint` to true.")}if(r.length===1){t=r[0]}else if(r.length>1){t="Multiple configuration errors:\n"+r.join("\n")}if(t){throw i.util.error(new Error,{name:"InvalidEndpoint",message:t})}},shouldDisableBodySigning:function e(t){var r=this.getSignerClass();if(this.config.s3DisableBodySigning===true&&r===i.Signers.V4&&t.httpRequest.endpoint.protocol==="https:"){return true}return false},setupRequestListeners:function e(t){t.addListener("validate",this.validateScheme);t.addListener("validate",this.validateBucketEndpoint);t.addListener("validate",this.correctBucketRegionFromCache);t.addListener("validate",this.validateBucketName);t.addListener("build",this.addContentType);t.addListener("build",this.populateURI);t.addListener("build",this.computeContentMd5);t.addListener("build",this.computeSseCustomerKeyMd5);t.addListener("afterBuild",this.addExpect100Continue);t.removeListener("validate",i.EventListeners.Core.VALIDATE_REGION);t.addListener("extractError",this.extractError);t.onAsync("extractError",this.requestBucketRegion);t.addListener("extractData",this.extractData);t.addListener("extractData",i.util.hoistPayloadMember);t.addListener("beforePresign",this.prepareSignedUrl);if(i.util.isBrowser()){t.onAsync("retry",this.reqRegionForNetworkingError)}if(this.shouldDisableBodySigning(t)){t.removeListener("afterBuild",i.EventListeners.Core.COMPUTE_SHA256);t.addListener("afterBuild",this.disableBodySigning)}},validateScheme:function(e){var t=e.params,r=e.httpRequest.endpoint.protocol,n=t.SSECustomerKey||t.CopySourceSSECustomerKey;if(n&&r!=="https:"){var a="Cannot send SSE keys over HTTP. Set 'sslEnabled'"+"to 'true' in your configuration";throw i.util.error(new Error,{code:"ConfigError",message:a})}},validateBucketEndpoint:function(e){if(!e.params.Bucket&&e.service.config.s3BucketEndpoint){var t="Cannot send requests to root API with `s3BucketEndpoint` set.";throw i.util.error(new Error,{code:"ConfigError",message:t})}},validateBucketName:function e(t){var r=t.service;var n=r.getSignatureVersion(t);if(n!=="v4"){return}var a=t.params&&t.params.Bucket;var o=t.params&&t.params.Key;var s=a&&a.indexOf("/");if(a&&s>=0){if(typeof o==="string"){t.params=i.util.copy(t.params);var u=a.substr(s+1)||"";t.params.Key=u+"/"+o;t.params.Bucket=a.substr(0,s)}else{var c="Bucket names cannot contain forward slashes. Bucket: "+a;throw i.util.error(new Error,{code:"InvalidBucket",message:c})}}},isValidAccelerateOperation:function e(t){var r=["createBucket","deleteBucket","listBuckets"];return r.indexOf(t)===-1},populateURI:function e(t){var r=t.httpRequest;var i=t.params.Bucket;var n=t.service;var a=r.endpoint;if(i){if(!n.pathStyleBucketName(i)){if(n.config.useAccelerateEndpoint&&n.isValidAccelerateOperation(t.operation)){if(n.config.useDualstack){a.hostname=i+".s3-accelerate.dualstack.amazonaws.com"}else{a.hostname=i+".s3-accelerate.amazonaws.com"}}else if(!n.config.s3BucketEndpoint){a.hostname=i+"."+a.hostname}var o=a.port;if(o!==80&&o!==443){a.host=a.hostname+":"+a.port}else{a.host=a.hostname}r.virtualHostedBucket=i;n.removeVirtualHostedBucketFromPath(t)}}},removeVirtualHostedBucketFromPath:function e(t){var r=t.httpRequest;var i=r.virtualHostedBucket;if(i&&r.path){r.path=r.path.replace(new RegExp("/"+i),"");if(r.path[0]!=="/"){r.path="/"+r.path}}},addExpect100Continue:function e(t){var r=t.httpRequest.headers["Content-Length"];if(i.util.isNode()&&r>=1024*1024){t.httpRequest.headers["Expect"]="100-continue"}},addContentType:function e(t){var r=t.httpRequest;if(r.method==="GET"||r.method==="HEAD"){delete r.headers["Content-Type"];return}if(!r.headers["Content-Type"]){r.headers["Content-Type"]="application/octet-stream"}var n=r.headers["Content-Type"];if(i.util.isBrowser()){if(typeof r.body==="string"&&!n.match(/;\s*charset=/)){var a="; charset=UTF-8";r.headers["Content-Type"]+=a}else{var o=function(e,t,r){return t+r.toUpperCase()};r.headers["Content-Type"]=n.replace(/(;\s*charset=)(.+)$/,o)}}},computableChecksumOperations:{putBucketCors:true,putBucketLifecycle:true,putBucketLifecycleConfiguration:true,putBucketTagging:true,deleteObjects:true,putBucketReplication:true},willComputeChecksums:function e(t){if(this.computableChecksumOperations[t.operation])return true;if(!this.config.computeChecksums)return false;if(!i.util.Buffer.isBuffer(t.httpRequest.body)&&typeof t.httpRequest.body!=="string"){return false}var r=t.service.api.operations[t.operation].input.members;if(t.service.shouldDisableBodySigning(t)&&!Object.prototype.hasOwnProperty.call(t.httpRequest.headers,"presigned-expires")){if(r.ContentMD5&&!t.params.ContentMD5){return true}}if(t.service.getSignerClass(t)===i.Signers.V4){if(r.ContentMD5&&!r.ContentMD5.required)return false}if(r.ContentMD5&&!t.params.ContentMD5)return true},computeContentMd5:function e(t){if(t.service.willComputeChecksums(t)){var r=i.util.crypto.md5(t.httpRequest.body,"base64");t.httpRequest.headers["Content-MD5"]=r}},computeSseCustomerKeyMd5:function e(t){var r={SSECustomerKey:"x-amz-server-side-encryption-customer-key-MD5",CopySourceSSECustomerKey:"x-amz-copy-source-server-side-encryption-customer-key-MD5"};i.util.each(r,function(e,r){if(t.params[e]){var n=i.util.crypto.md5(t.params[e],"base64");t.httpRequest.headers[r]=n}})},pathStyleBucketName:function e(t){if(this.config.s3ForcePathStyle)return true;if(this.config.s3BucketEndpoint)return false;if(this.dnsCompatibleBucketName(t)){return this.config.sslEnabled&&t.match(/\./)?true:false}else{return true}},dnsCompatibleBucketName:function e(t){var r=t;var i=new RegExp(/^[a-z0-9][a-z0-9\.\-]{1,61}[a-z0-9]$/);var n=new RegExp(/(\d+\.){3}\d+/);var a=new RegExp(/\.\./);return r.match(i)&&!r.match(n)&&!r.match(a)?true:false},successfulResponse:function e(t){var r=t.request;var i=t.httpResponse;if(a[r.operation]&&i.body.toString().match("<Error>")){return false}else{return i.statusCode<300}},retryableError:function e(t,r){if(a[r.operation]&&t.statusCode===200){return true}else if(r._requestRegionForBucket&&r.service.bucketRegionCache[r._requestRegionForBucket]){return false}else if(t&&t.code==="RequestTimeout"){return true}else if(t&&o.indexOf(t.code)!=-1&&t.region&&t.region!=r.httpRequest.region){r.httpRequest.region=t.region;if(t.statusCode===301){r.service.updateReqBucketRegion(r)}return true}else{var n=i.Service.prototype.retryableError;return n.call(this,t,r)}},updateReqBucketRegion:function e(t,r){var n=t.httpRequest;if(typeof r==="string"&&r.length){n.region=r}if(!n.endpoint.host.match(/s3(?!-accelerate).*\.amazonaws\.com$/)){return}var a=t.service;var o=a.config;var s=o.s3BucketEndpoint;if(s){delete o.s3BucketEndpoint}var u=i.util.copy(o);delete u.endpoint;u.region=n.region;n.endpoint=new i.S3(u).endpoint;a.populateURI(t);o.s3BucketEndpoint=s;n.headers.Host=n.endpoint.host;if(t._asm.currentState==="validate"){t.removeListener("build",a.populateURI);t.addListener("build",a.removeVirtualHostedBucketFromPath)}},extractData:function e(t){var r=t.request;if(r.operation==="getBucketLocation"){var i=t.httpResponse.body.toString().match(/>(.+)<\/Location/);delete t.data["_"];if(i){t.data.LocationConstraint=i[1]}else{t.data.LocationConstraint=""}}var n=r.params.Bucket||null;if(r.operation==="deleteBucket"&&typeof n==="string"&&!t.error){r.service.clearBucketRegionCache(n)}else{var a=t.httpResponse.headers||{};var o=a["x-amz-bucket-region"]||null;if(!o&&r.operation==="createBucket"&&!t.error){var s=r.params.CreateBucketConfiguration;if(!s){o="us-east-1"}else if(s.LocationConstraint==="EU"){o="eu-west-1"}else{o=s.LocationConstraint}}if(o){if(n&&o!==r.service.bucketRegionCache[n]){r.service.bucketRegionCache[n]=o}}}r.service.extractRequestIds(t)},extractError:function e(t){var r={304:"NotModified",403:"Forbidden",400:"BadRequest",404:"NotFound"};var n=t.request;var a=t.httpResponse.statusCode;var o=t.httpResponse.body||"";var s=t.httpResponse.headers||{};var u=s["x-amz-bucket-region"]||null;var c=n.params.Bucket||null;var l=n.service.bucketRegionCache;if(u&&c&&u!==l[c]){l[c]=u}var f;if(r[a]&&o.length===0){if(c&&!u){f=l[c]||null;if(f!==n.httpRequest.region){u=f}}t.error=i.util.error(new Error,{code:r[a],message:null,region:u})}else{var p=(new i.XML.Parser).parse(o.toString());if(p.Region&&!u){u=p.Region;if(c&&u!==l[c]){l[c]=u}}else if(c&&!u&&!p.Region){f=l[c]||null;if(f!==n.httpRequest.region){u=f}}t.error=i.util.error(new Error,{code:p.Code||a,message:p.Message||null,region:u})}n.service.extractRequestIds(t)},requestBucketRegion:function e(t,r){var n=t.error;var a=t.request;var s=a.params.Bucket||null;if(!n||!s||n.region||a.operation==="listObjects"||i.util.isNode()&&a.operation==="headBucket"||n.statusCode===400&&a.operation!=="headObject"||o.indexOf(n.code)===-1){return r()}var u=i.util.isNode()?"headBucket":"listObjects";var c={Bucket:s};if(u==="listObjects")c.MaxKeys=0;var l=a.service[u](c);l._requestRegionForBucket=s;l.send(function(){var e=a.service.bucketRegionCache[s]||null;n.region=e;r()})},reqRegionForNetworkingError:function e(t,r){if(!i.util.isBrowser()){return r()}var n=t.error;var a=t.request;var o=a.params.Bucket;if(!n||n.code!=="NetworkingError"||!o||a.httpRequest.region==="us-east-1"){return r()}var s=a.service;var u=s.bucketRegionCache;var c=u[o]||null;if(c&&c!==a.httpRequest.region){s.updateReqBucketRegion(a,c);r()}else if(!s.dnsCompatibleBucketName(o)){s.updateReqBucketRegion(a,"us-east-1");if(u[o]!=="us-east-1"){u[o]="us-east-1"}r()}else if(a.httpRequest.virtualHostedBucket){var l=s.listObjects({Bucket:o,MaxKeys:0});s.updateReqBucketRegion(l,"us-east-1");l._requestRegionForBucket=o;l.send(function(){var e=s.bucketRegionCache[o]||null;if(e&&e!==a.httpRequest.region){s.updateReqBucketRegion(a,e)}r()})}else{r()}},bucketRegionCache:{},clearBucketRegionCache:function(e){var t=this.bucketRegionCache;if(!e){e=Object.keys(t)}else if(typeof e==="string"){e=[e]}for(var r=0;r<e.length;r++){delete t[e[r]]}return t},correctBucketRegionFromCache:function e(t){var r=t.params.Bucket||null;if(r){var i=t.service;var n=t.httpRequest.region;var a=i.bucketRegionCache[r];if(a&&a!==n){i.updateReqBucketRegion(t,a)}}},extractRequestIds:function e(t){var r=t.httpResponse.headers?t.httpResponse.headers["x-amz-id-2"]:null;var i=t.httpResponse.headers?t.httpResponse.headers["x-amz-cf-id"]:null;t.extendedRequestId=r;t.cfId=i;if(t.error){t.error.requestId=t.requestId||null;t.error.extendedRequestId=r;t.error.cfId=i}},getSignedUrl:function e(t,r,n){r=i.util.copy(r||{});var a=r.Expires||900;delete r.Expires;var o=this.makeRequest(t,r);if(n){i.util.defer(function(){o.presign(a,n)})}else{return o.presign(a,n)}},createPresignedPost:function e(t,r){if(typeof t==="function"&&r===undefined){r=t;t=null}t=i.util.copy(t||{});var n=this.config.params||{};var a=t.Bucket||n.Bucket,o=this,s=this.config,u=i.util.copy(this.endpoint);if(!s.s3BucketEndpoint){u.pathname="/"+a}function c(){return{url:i.util.urlFormat(u),fields:o.preparePostFields(s.credentials,s.region,a,t.Fields,t.Conditions,t.Expires)}}if(r){s.getCredentials(function(e){if(e){r(e)}r(null,c())})}else{return c()}},preparePostFields:function e(t,r,a,o,s,u){var c=this.getSkewCorrectedDate();if(!t||!r||!a){throw new Error("Unable to create a POST object policy without a bucket,"+" region, and credentials")}o=i.util.copy(o||{});s=(s||[]).slice(0);u=u||3600;var l=i.util.date.iso8601(c).replace(/[:\-]|\.\d{3}/g,"");var f=l.substr(0,8);var p=n.createScope(f,r,"s3");var h=t.accessKeyId+"/"+p;o["bucket"]=a;o["X-Amz-Algorithm"]="AWS4-HMAC-SHA256";o["X-Amz-Credential"]=h;o["X-Amz-Date"]=l;if(t.sessionToken){o["X-Amz-Security-Token"]=t.sessionToken}for(var d in o){if(o.hasOwnProperty(d)){var m={};m[d]=o[d];s.push(m)}}o.Policy=this.preparePostPolicy(new Date(c.valueOf()+u*1e3),s);o["X-Amz-Signature"]=i.util.crypto.hmac(n.getSigningKey(t,f,r,"s3",true),o.Policy,"hex");return o},preparePostPolicy:function e(t,r){return i.util.base64.encode(JSON.stringify({expiration:i.util.date.iso8601(t),conditions:r}))},prepareSignedUrl:function e(t){t.addListener("validate",t.service.noPresignedContentLength);t.removeListener("build",t.service.addContentType);if(!t.params.Body){t.removeListener("build",t.service.computeContentMd5)}else{t.addListener("afterBuild",i.EventListeners.Core.COMPUTE_SHA256)}},disableBodySigning:function e(t){var r=t.httpRequest.headers;if(!Object.prototype.hasOwnProperty.call(r,"presigned-expires")){r["X-Amz-Content-Sha256"]="UNSIGNED-PAYLOAD"}},noPresignedContentLength:function e(t){if(t.params.ContentLength!==undefined){throw i.util.error(new Error,{code:"UnexpectedParameter",message:"ContentLength is not supported in pre-signed URLs."})}},createBucket:function e(t,r){if(typeof t==="function"||!t){r=r||t;t={}}var i=this.endpoint.hostname;if(i!==this.api.globalEndpoint&&!t.CreateBucketConfiguration){t.CreateBucketConfiguration={LocationConstraint:this.config.region}}return this.makeRequest("createBucket",t,r)},upload:function e(t,r,n){if(typeof r==="function"&&n===undefined){n=r;r=null}r=r||{};r=i.util.merge(r||{},{service:this,params:t});var a=new i.S3.ManagedUpload(r);if(typeof n==="function")a.send(n);return a}})},{"../core":38,"../s3/managed_upload":83,"../signers/v4_credentials":110}],83:[function(e,t,r){var i=e("../core");var n=i.util.string.byteLength;var a=i.util.Buffer;i.S3.ManagedUpload=i.util.inherit({constructor:function e(t){var r=this;i.SequentialExecutor.call(r);r.body=null;r.sliceFn=null;r.callback=null;r.parts={};r.completeInfo=[];r.fillQueue=function(){r.callback(new Error("Unsupported body payload "+typeof r.body))};r.configure(t)},configure:function e(t){t=t||{};this.partSize=this.minPartSize;if(t.queueSize)this.queueSize=t.queueSize;if(t.partSize)this.partSize=t.partSize;if(t.leavePartsOnError)this.leavePartsOnError=true;if(t.tags){if(!Array.isArray(t.tags)){throw new Error("Tags must be specified as an array; "+typeof t.tags+" provided.")}this.tags=t.tags}if(this.partSize<this.minPartSize){throw new Error("partSize must be greater than "+this.minPartSize)}this.service=t.service;this.bindServiceObject(t.params);this.validateBody();this.adjustTotalBytes()},leavePartsOnError:false,queueSize:4,partSize:null,minPartSize:1024*1024*5,maxTotalParts:1e4,send:function(e){var t=this;t.failed=false;t.callback=e||function(e){if(e)throw e};var r=true;if(t.sliceFn){t.fillQueue=t.fillBuffer}else if(i.util.isNode()){var n=i.util.stream.Stream;if(t.body instanceof n){r=false;t.fillQueue=t.fillStream;t.partBuffers=[];t.body.on("error",function(e){t.cleanup(e)}).on("readable",function(){t.fillQueue()}).on("end",function(){t.isDoneChunking=true;t.numParts=t.totalPartNumbers;t.fillQueue.call(t);if(t.isDoneChunking&&t.totalPartNumbers>=1&&t.doneParts===t.numParts){t.finishMultiPart()}})}}if(r)t.fillQueue.call(t)},abort:function(){this.cleanup(i.util.error(new Error("Request aborted by user"),{code:"RequestAbortedError",retryable:false}))},validateBody:function e(){var t=this;t.body=t.service.config.params.Body;if(typeof t.body==="string"){t.body=new i.util.Buffer(t.body)}else if(!t.body){throw new Error("params.Body is required")}t.sliceFn=i.util.arraySliceFn(t.body)},bindServiceObject:function e(t){t=t||{};var r=this;if(!r.service){r.service=new i.S3({params:t})}else{var n=r.service;var a=i.util.copy(n.config);a.signatureVersion=n.getSignatureVersion();r.service=new n.constructor.__super__(a);r.service.config.params=i.util.merge(r.service.config.params||{},t)}},adjustTotalBytes:function e(){var t=this;try{t.totalBytes=n(t.body)}catch(e){}if(t.totalBytes){var r=Math.ceil(t.totalBytes/t.maxTotalParts);if(r>t.partSize)t.partSize=r}else{t.totalBytes=undefined}},isDoneChunking:false,partPos:0,totalChunkedBytes:0,totalUploadedBytes:0,totalBytes:undefined,numParts:0,totalPartNumbers:0,activeParts:0,doneParts:0,parts:null,completeInfo:null,failed:false,multipartReq:null,partBuffers:null,partBufferLength:0,fillBuffer:function e(){var t=this;var r=n(t.body);if(r===0){t.isDoneChunking=true;t.numParts=1;t.nextChunk(t.body);return}while(t.activeParts<t.queueSize&&t.partPos<r){var i=Math.min(t.partPos+t.partSize,r);var a=t.sliceFn.call(t.body,t.partPos,i);t.partPos+=t.partSize;if(n(a)<t.partSize||t.partPos===r){t.isDoneChunking=true;t.numParts=t.totalPartNumbers+1}t.nextChunk(a)}},fillStream:function e(){var t=this;if(t.activeParts>=t.queueSize)return;var r=t.body.read(t.partSize-t.partBufferLength)||t.body.read();if(r){t.partBuffers.push(r);t.partBufferLength+=r.length;t.totalChunkedBytes+=r.length}if(t.partBufferLength>=t.partSize){var i=t.partBuffers.length===1?t.partBuffers[0]:a.concat(t.partBuffers);t.partBuffers=[];t.partBufferLength=0;if(i.length>t.partSize){var n=i.slice(t.partSize);t.partBuffers.push(n);t.partBufferLength+=n.length;i=i.slice(0,t.partSize)}t.nextChunk(i)}if(t.isDoneChunking&&!t.isDoneSending){i=t.partBuffers.length===1?t.partBuffers[0]:a.concat(t.partBuffers);t.partBuffers=[];t.partBufferLength=0;t.totalBytes=t.totalChunkedBytes;t.isDoneSending=true;if(t.numParts===0||i.length>0){t.numParts++;t.nextChunk(i)}}t.body.read(0)},nextChunk:function e(t){var r=this;if(r.failed)return null;var n=++r.totalPartNumbers;if(r.isDoneChunking&&n===1){var a={Body:t};if(this.tags){a.Tagging=this.getTaggingHeader()}var o=r.service.putObject(a);o._managedUpload=r;o.on("httpUploadProgress",r.progress).send(r.finishSinglePart);return null}else if(r.service.config.params.ContentMD5){var s=i.util.error(new Error("The Content-MD5 you specified is invalid for multi-part uploads."),{code:"InvalidDigest",retryable:false});r.cleanup(s);return null}if(r.completeInfo[n]&&r.completeInfo[n].ETag!==null){return null}r.activeParts++;if(!r.service.config.params.UploadId){if(!r.multipartReq){r.multipartReq=r.service.createMultipartUpload();r.multipartReq.on("success",function(e){r.service.config.params.UploadId=e.data.UploadId;r.multipartReq=null});r.queueChunks(t,n);r.multipartReq.on("error",function(e){r.cleanup(e)});r.multipartReq.send()}else{r.queueChunks(t,n)}}else{r.uploadPart(t,n)}},getTaggingHeader:function e(){var t=[];for(var r=0;r<this.tags.length;r++){t.push(i.util.uriEscape(this.tags[r].Key)+"="+i.util.uriEscape(this.tags[r].Value))}return t.join("&")},uploadPart:function e(t,r){var n=this;var a={Body:t,ContentLength:i.util.string.byteLength(t),PartNumber:r};var o={ETag:null,PartNumber:r};n.completeInfo[r]=o;var s=n.service.uploadPart(a);n.parts[r]=s;s._lastUploadedBytes=0;s._managedUpload=n;s.on("httpUploadProgress",n.progress);s.send(function(e,t){delete n.parts[a.PartNumber];n.activeParts--;if(!e&&(!t||!t.ETag)){var r="No access to ETag property on response.";if(i.util.isBrowser()){r+=" Check CORS configuration to expose ETag header."}e=i.util.error(new Error(r),{code:"ETagMissing",retryable:false})}if(e)return n.cleanup(e);o.ETag=t.ETag;n.doneParts++;if(n.isDoneChunking&&n.doneParts===n.numParts){n.finishMultiPart()}else{n.fillQueue.call(n)}})},queueChunks:function e(t,r){var i=this;i.multipartReq.on("success",function(){i.uploadPart(t,r)})},cleanup:function e(t){var r=this;if(r.failed)return;if(typeof r.body.removeAllListeners==="function"&&typeof r.body.resume==="function"){r.body.removeAllListeners("readable");r.body.removeAllListeners("end");r.body.resume()}if(r.multipartReq){r.multipartReq.removeAllListeners("success");r.multipartReq.removeAllListeners("error");r.multipartReq.removeAllListeners("complete");delete r.multipartReq}if(r.service.config.params.UploadId&&!r.leavePartsOnError){r.service.abortMultipartUpload().send()}else if(r.leavePartsOnError){r.isDoneChunking=false}i.util.each(r.parts,function(e,t){t.removeAllListeners("complete");t.abort()});r.activeParts=0;r.partPos=0;r.numParts=0;r.totalPartNumbers=0;r.parts={};r.failed=true;r.callback(t)},finishMultiPart:function e(){var t=this;var r={MultipartUpload:{Parts:t.completeInfo.slice(1)}};t.service.completeMultipartUpload(r,function(e,r){if(e){return t.cleanup(e)}if(r&&typeof r.Location==="string"){r.Location=r.Location.replace(/%2F/g,"/")}if(Array.isArray(t.tags)){t.service.putObjectTagging({Tagging:{TagSet:t.tags}},function(e,i){if(e){t.callback(e)}else{t.callback(e,r)}})}else{t.callback(e,r)}})},finishSinglePart:function e(t,r){var i=this.request._managedUpload;var n=this.request.httpRequest;var a=n.endpoint;if(t)return i.callback(t);r.Location=[a.protocol,"//",a.host,n.path].join("");r.key=this.request.params.Key;r.Key=this.request.params.Key;r.Bucket=this.request.params.Bucket;i.callback(t,r)},progress:function e(t){var r=this._managedUpload;if(this.operation==="putObject"){t.part=1;t.key=this.params.Key}else{r.totalUploadedBytes+=t.loaded-this._lastUploadedBytes;this._lastUploadedBytes=t.loaded;t={loaded:r.totalUploadedBytes,total:r.totalBytes,part:this.params.PartNumber,key:this.params.Key}}r.emit("httpUploadProgress",[t])}});i.util.mixin(i.S3.ManagedUpload,i.SequentialExecutor);i.S3.ManagedUpload.addPromisesToClass=function e(t){this.prototype.promise=i.util.promisifyMethod("send",t)};i.S3.ManagedUpload.deletePromisesFromClass=function e(){delete this.prototype.promise};i.util.addPromises(i.S3.ManagedUpload);t.exports=i.S3.ManagedUpload},{"../core":38}]},{},[99]);OOS.apiLoader.services["s3"]["2006-03-01"]={version:"2.0",metadata:{apiVersion:"2006-03-01",checksumFormat:"md5",endpointPrefix:"s3",globalEndpoint:"s3.amazonaws.com",protocol:"rest-xml",serviceAbbreviation:"Amazon S3",serviceFullName:"Amazon Simple Storage Service",serviceId:"S3",signatureVersion:"s3",timestampFormat:"rfc822",uid:"s3-2006-03-01"},operations:{AbortMultipartUpload:{http:{method:"DELETE",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","Key","UploadId"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},UploadId:{location:"querystring",locationName:"uploadId"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}}},CompleteMultipartUpload:{http:{requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","Key","UploadId"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},MultipartUpload:{locationName:"CompleteMultipartUpload",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",members:{Parts:{locationName:"Part",type:"list",member:{type:"structure",members:{ETag:{},PartNumber:{type:"integer"}}},flattened:true}}},UploadId:{location:"querystring",locationName:"uploadId"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}},payload:"MultipartUpload"},output:{type:"structure",members:{Location:{},Bucket:{},Key:{},Expiration:{location:"header",locationName:"x-amz-expiration"},ETag:{},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},VersionId:{location:"header",locationName:"x-amz-version-id"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}}},CopyObject:{http:{method:"PUT",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","CopySource","Key"],members:{ACL:{location:"header",locationName:"x-amz-acl"},Bucket:{location:"uri",locationName:"Bucket"},CacheControl:{location:"header",locationName:"Cache-Control"},ContentDisposition:{location:"header",locationName:"Content-Disposition"},ContentEncoding:{location:"header",locationName:"Content-Encoding"},ContentLanguage:{location:"header",locationName:"Content-Language"},ContentType:{location:"header",locationName:"Content-Type"},CopySource:{location:"header",locationName:"x-amz-copy-source"},CopySourceIfMatch:{location:"header",locationName:"x-amz-copy-source-if-match"},CopySourceIfModifiedSince:{location:"header",locationName:"x-amz-copy-source-if-modified-since",type:"timestamp"},CopySourceIfNoneMatch:{location:"header",locationName:"x-amz-copy-source-if-none-match"},CopySourceIfUnmodifiedSince:{location:"header",locationName:"x-amz-copy-source-if-unmodified-since",type:"timestamp"},Expires:{location:"header",locationName:"Expires",type:"timestamp"},GrantFullControl:{location:"header",locationName:"x-amz-grant-full-control"},GrantRead:{location:"header",locationName:"x-amz-grant-read"},GrantReadACP:{location:"header",locationName:"x-amz-grant-read-acp"},GrantWriteACP:{location:"header",locationName:"x-amz-grant-write-acp"},Key:{location:"uri",locationName:"Key"},Metadata:{shape:"S11",location:"headers",locationName:"x-amz-meta-"},MetadataDirective:{location:"header",locationName:"x-amz-metadata-directive"},TaggingDirective:{location:"header",locationName:"x-amz-tagging-directive"},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},StorageClass:{location:"header",locationName:"x-amz-storage-class"},WebsiteRedirectLocation:{location:"header",locationName:"x-amz-website-redirect-location"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKey:{shape:"S19",location:"header",locationName:"x-amz-server-side-encryption-customer-key"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},CopySourceSSECustomerAlgorithm:{location:"header",locationName:"x-amz-copy-source-server-side-encryption-customer-algorithm"},CopySourceSSECustomerKey:{shape:"S1c",location:"header",locationName:"x-amz-copy-source-server-side-encryption-customer-key"},CopySourceSSECustomerKeyMD5:{location:"header",locationName:"x-amz-copy-source-server-side-encryption-customer-key-MD5"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"},Tagging:{location:"header",locationName:"x-amz-tagging"}}},output:{type:"structure",members:{CopyObjectResult:{type:"structure",members:{ETag:{},LastModified:{type:"timestamp"}}},Expiration:{location:"header",locationName:"x-amz-expiration"},CopySourceVersionId:{location:"header",locationName:"x-amz-copy-source-version-id"},VersionId:{location:"header",locationName:"x-amz-version-id"},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}},payload:"CopyObjectResult"},alias:"PutObjectCopy"},CreateBucket:{http:{method:"PUT",requestUri:"/{Bucket}"},input:{type:"structure",required:["Bucket"],members:{ACL:{location:"header",locationName:"x-amz-acl"},Bucket:{location:"uri",locationName:"Bucket"},CreateBucketConfiguration:{locationName:"CreateBucketConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",members:{LocationConstraint:{}}},GrantFullControl:{location:"header",locationName:"x-amz-grant-full-control"},GrantRead:{location:"header",locationName:"x-amz-grant-read"},GrantReadACP:{location:"header",locationName:"x-amz-grant-read-acp"},GrantWrite:{location:"header",locationName:"x-amz-grant-write"},GrantWriteACP:{location:"header",locationName:"x-amz-grant-write-acp"}},payload:"CreateBucketConfiguration"},output:{type:"structure",members:{Location:{location:"header",locationName:"Location"}}},alias:"PutBucket"},CreateMultipartUpload:{http:{requestUri:"/{Bucket}/{Key+}?uploads"},input:{type:"structure",required:["Bucket","Key"],members:{ACL:{location:"header",locationName:"x-amz-acl"},Bucket:{location:"uri",locationName:"Bucket"},CacheControl:{location:"header",locationName:"Cache-Control"},ContentDisposition:{location:"header",locationName:"Content-Disposition"},ContentEncoding:{location:"header",locationName:"Content-Encoding"},ContentLanguage:{location:"header",locationName:"Content-Language"},ContentType:{location:"header",locationName:"Content-Type"},Expires:{location:"header",locationName:"Expires",type:"timestamp"},GrantFullControl:{location:"header",locationName:"x-amz-grant-full-control"},GrantRead:{location:"header",locationName:"x-amz-grant-read"},GrantReadACP:{location:"header",locationName:"x-amz-grant-read-acp"},GrantWriteACP:{location:"header",locationName:"x-amz-grant-write-acp"},Key:{location:"uri",locationName:"Key"},Metadata:{shape:"S11",location:"headers",locationName:"x-amz-meta-"},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},StorageClass:{location:"header",locationName:"x-amz-storage-class"},WebsiteRedirectLocation:{location:"header",locationName:"x-amz-website-redirect-location"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKey:{shape:"S19",location:"header",locationName:"x-amz-server-side-encryption-customer-key"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"},Tagging:{location:"header",locationName:"x-amz-tagging"}}},output:{type:"structure",members:{AbortDate:{location:"header",locationName:"x-amz-abort-date",type:"timestamp"},AbortRuleId:{location:"header",locationName:"x-amz-abort-rule-id"},Bucket:{locationName:"Bucket"},Key:{},UploadId:{},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}},alias:"InitiateMultipartUpload"},DeleteBucket:{http:{method:"DELETE",requestUri:"/{Bucket}"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},DeleteBucketTrigger:{http:{method:"DELETE",requestUri:"/{Bucket}?trigger={triggerName}"},input:{type:"structure",required:["Bucket","triggerName"],members:{Bucket:{location:"uri",locationName:"Bucket"},triggerName:{location:"uri",locationName:"triggerName"}}}},DeleteBucketAnalyticsConfiguration:{http:{method:"DELETE",requestUri:"/{Bucket}?analytics"},input:{type:"structure",required:["Bucket","Id"],members:{Bucket:{location:"uri",locationName:"Bucket"},Id:{location:"querystring",locationName:"id"}}}},DeleteBucketCors:{http:{method:"DELETE",requestUri:"/{Bucket}?cors"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},DeleteBucketEncryption:{http:{method:"DELETE",requestUri:"/{Bucket}?encryption"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},DeleteBucketInventoryConfiguration:{http:{method:"DELETE",requestUri:"/{Bucket}?inventory"},input:{type:"structure",required:["Bucket","Id"],members:{Bucket:{location:"uri",locationName:"Bucket"},Id:{location:"querystring",locationName:"id"}}}},DeleteBucketLifecycle:{http:{method:"DELETE",requestUri:"/{Bucket}?lifecycle"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},DeleteBucketMetricsConfiguration:{http:{method:"DELETE",requestUri:"/{Bucket}?metrics"},input:{type:"structure",required:["Bucket","Id"],members:{Bucket:{location:"uri",locationName:"Bucket"},Id:{location:"querystring",locationName:"id"}}}},DeleteBucketPolicy:{http:{method:"DELETE",requestUri:"/{Bucket}?policy"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},DeleteBucketReplication:{http:{method:"DELETE",requestUri:"/{Bucket}?replication"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},DeleteBucketTagging:{http:{method:"DELETE",requestUri:"/{Bucket}?tagging"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},DeleteBucketWebsite:{http:{method:"DELETE",requestUri:"/{Bucket}?website"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},DeleteObject:{http:{method:"DELETE",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","Key"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},MFA:{location:"header",locationName:"x-amz-mfa"},VersionId:{location:"querystring",locationName:"versionId"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{DeleteMarker:{location:"header",locationName:"x-amz-delete-marker",type:"boolean"},VersionId:{location:"header",locationName:"x-amz-version-id"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}}},DeleteObjectTagging:{http:{method:"DELETE",requestUri:"/{Bucket}/{Key+}?tagging"},input:{type:"structure",required:["Bucket","Key"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},VersionId:{location:"querystring",locationName:"versionId"}}},output:{type:"structure",members:{VersionId:{location:"header",locationName:"x-amz-version-id"}}}},DeleteObjects:{http:{requestUri:"/{Bucket}?delete"},input:{type:"structure",required:["Bucket","Delete"],members:{Bucket:{location:"uri",locationName:"Bucket"},Delete:{locationName:"Delete",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",required:["Objects"],members:{Objects:{locationName:"Object",type:"list",member:{type:"structure",required:["Key"],members:{Key:{},VersionId:{}}},flattened:true},Quiet:{type:"boolean"}}},MFA:{location:"header",locationName:"x-amz-mfa"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}},payload:"Delete"},output:{type:"structure",members:{Deleted:{type:"list",member:{type:"structure",members:{Key:{},VersionId:{},DeleteMarker:{type:"boolean"},DeleteMarkerVersionId:{}}},flattened:true},RequestCharged:{location:"header",locationName:"x-amz-request-charged"},Errors:{locationName:"Error",type:"list",member:{type:"structure",members:{Key:{},VersionId:{},Code:{},Message:{}}},flattened:true}}},alias:"DeleteMultipleObjects"},GetBucketAccelerateConfiguration:{http:{method:"GET",requestUri:"/{Bucket}?accelerate"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{Status:{}}}},GetBucketAcl:{http:{method:"GET",requestUri:"/{Bucket}?acl"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{Owner:{shape:"S2v"},Grants:{shape:"S2y",locationName:"AccessControlList"}}}},GetBucketAnalyticsConfiguration:{http:{method:"GET",requestUri:"/{Bucket}?analytics"},input:{type:"structure",required:["Bucket","Id"],members:{Bucket:{location:"uri",locationName:"Bucket"},Id:{location:"querystring",locationName:"id"}}},output:{type:"structure",members:{AnalyticsConfiguration:{shape:"S37"}},payload:"AnalyticsConfiguration"}},GetBucketCors:{http:{method:"GET",requestUri:"/{Bucket}?cors"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{CORSRules:{shape:"S3n",locationName:"CORSRule"}}}},GetBucketEncryption:{http:{method:"GET",requestUri:"/{Bucket}?encryption"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{ServerSideEncryptionConfiguration:{shape:"S40"}},payload:"ServerSideEncryptionConfiguration"}},GetBucketInventoryConfiguration:{http:{method:"GET",requestUri:"/{Bucket}?inventory"},input:{type:"structure",required:["Bucket","Id"],members:{Bucket:{location:"uri",locationName:"Bucket"},Id:{location:"querystring",locationName:"id"}}},output:{type:"structure",members:{InventoryConfiguration:{shape:"S46"}},payload:"InventoryConfiguration"}},GetBucketLifecycle:{http:{method:"GET",requestUri:"/{Bucket}?lifecycle"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{Rules:{shape:"S4m",locationName:"Rule"}}},deprecated:true},GetBucketLifecycleConfiguration:{http:{method:"GET",requestUri:"/{Bucket}?lifecycle"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{Rules:{shape:"S51",locationName:"Rule"}}}},GetBucketLocation:{http:{method:"GET",requestUri:"/{Bucket}?location"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{LocationConstraint:{}}}},GetBucketLogging:{http:{method:"GET",requestUri:"/{Bucket}?logging"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{LoggingEnabled:{shape:"S5b"}}}},GetBucketMetricsConfiguration:{http:{method:"GET",requestUri:"/{Bucket}?metrics"},input:{type:"structure",required:["Bucket","Id"],members:{Bucket:{location:"uri",locationName:"Bucket"},Id:{location:"querystring",locationName:"id"}}},output:{type:"structure",members:{MetricsConfiguration:{shape:"S5j"}},payload:"MetricsConfiguration"}},GetBucketNotification:{http:{method:"GET",requestUri:"/{Bucket}?notification"},input:{shape:"S5m"},output:{shape:"S5n"},deprecated:true},GetBucketNotificationConfiguration:{http:{method:"GET",requestUri:"/{Bucket}?notification"},input:{shape:"S5m"},output:{shape:"S5y"}},GetBucketPolicy:{http:{method:"GET",requestUri:"/{Bucket}?policy"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{Policy:{}},payload:"Policy"}},GetBucketReplication:{http:{method:"GET",requestUri:"/{Bucket}?replication"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{ReplicationConfiguration:{shape:"S6h"}},payload:"ReplicationConfiguration"}},GetBucketRequestPayment:{http:{method:"GET",requestUri:"/{Bucket}?requestPayment"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{Payer:{}}}},GetBucketTagging:{http:{method:"GET",requestUri:"/{Bucket}?tagging"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",required:["TagSet"],members:{TagSet:{shape:"S3d"}}}},GetBucketVersioning:{http:{method:"GET",requestUri:"/{Bucket}?versioning"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{Status:{},MFADelete:{locationName:"MfaDelete"}}}},GetBucketWebsite:{http:{method:"GET",requestUri:"/{Bucket}?website"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{RedirectAllRequestsTo:{shape:"S75"},IndexDocument:{shape:"S78"},ErrorDocument:{shape:"S7a"},RoutingRules:{shape:"S7b"}}}},GetBucketTrigger:{http:{method:"GET",requestUri:"/{Bucket}?trigger"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{Trigger:{shape:"S5o1b"}}}},GetObject:{http:{method:"GET",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","Key"],members:{Bucket:{location:"uri",locationName:"Bucket"},IfMatch:{location:"header",locationName:"If-Match"},IfModifiedSince:{location:"header",locationName:"If-Modified-Since",type:"timestamp"},IfNoneMatch:{location:"header",locationName:"If-None-Match"},IfUnmodifiedSince:{location:"header",locationName:"If-Unmodified-Since",type:"timestamp"},Key:{location:"uri",locationName:"Key"},Range:{location:"header",locationName:"Range"},ResponseCacheControl:{location:"querystring",locationName:"response-cache-control"},ResponseContentDisposition:{location:"querystring",locationName:"response-content-disposition"},ResponseContentEncoding:{location:"querystring",locationName:"response-content-encoding"},ResponseContentLanguage:{location:"querystring",locationName:"response-content-language"},ResponseContentType:{location:"querystring",locationName:"response-content-type"},ResponseExpires:{location:"querystring",locationName:"response-expires",type:"timestamp"},VersionId:{location:"querystring",locationName:"versionId"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKey:{shape:"S19",location:"header",locationName:"x-amz-server-side-encryption-customer-key"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"},PartNumber:{location:"querystring",locationName:"partNumber",type:"integer"}}},output:{type:"structure",members:{Body:{streaming:true,type:"blob"},DeleteMarker:{location:"header",locationName:"x-amz-delete-marker",type:"boolean"},AcceptRanges:{location:"header",locationName:"accept-ranges"},Expiration:{location:"header",locationName:"x-amz-expiration"},Restore:{location:"header",locationName:"x-amz-restore"},LastModified:{location:"header",locationName:"Last-Modified",type:"timestamp"},ContentLength:{location:"header",locationName:"Content-Length",type:"long"},ETag:{location:"header",locationName:"ETag"},MissingMeta:{location:"header",locationName:"x-amz-missing-meta",type:"integer"},VersionId:{location:"header",locationName:"x-amz-version-id"},CacheControl:{location:"header",locationName:"Cache-Control"},ContentDisposition:{location:"header",locationName:"Content-Disposition"},ContentEncoding:{location:"header",locationName:"Content-Encoding"},ContentLanguage:{location:"header",locationName:"Content-Language"},ContentRange:{location:"header",locationName:"Content-Range"},ContentType:{location:"header",locationName:"Content-Type"},Expires:{location:"header",locationName:"Expires",type:"timestamp"},WebsiteRedirectLocation:{location:"header",locationName:"x-amz-website-redirect-location"},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},Metadata:{shape:"S11",location:"headers",locationName:"x-amz-meta-"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},StorageClass:{location:"header",locationName:"x-amz-storage-class"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"},ReplicationStatus:{location:"header",locationName:"x-amz-replication-status"},PartsCount:{location:"header",locationName:"x-amz-mp-parts-count",type:"integer"},TagCount:{location:"header",locationName:"x-amz-tagging-count",type:"integer"}},payload:"Body"}},GetObjectAcl:{http:{method:"GET",requestUri:"/{Bucket}/{Key+}?acl"},input:{type:"structure",required:["Bucket","Key"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},VersionId:{location:"querystring",locationName:"versionId"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{Owner:{shape:"S2v"},Grants:{shape:"S2y",locationName:"AccessControlList"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}}},GetObjectTagging:{http:{method:"GET",requestUri:"/{Bucket}/{Key+}?tagging"},input:{type:"structure",required:["Bucket","Key"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},VersionId:{location:"querystring",locationName:"versionId"}}},output:{type:"structure",required:["TagSet"],members:{VersionId:{location:"header",locationName:"x-amz-version-id"},TagSet:{shape:"S3d"}}}},GetObjectTorrent:{http:{method:"GET",requestUri:"/{Bucket}/{Key+}?torrent"},input:{type:"structure",required:["Bucket","Key"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{Body:{streaming:true,type:"blob"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}},payload:"Body"}},HeadBucket:{http:{method:"HEAD",requestUri:"/{Bucket}"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},HeadObject:{http:{method:"HEAD",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","Key"],members:{Bucket:{location:"uri",locationName:"Bucket"},IfMatch:{location:"header",locationName:"If-Match"},IfModifiedSince:{location:"header",locationName:"If-Modified-Since",type:"timestamp"},IfNoneMatch:{location:"header",locationName:"If-None-Match"},IfUnmodifiedSince:{location:"header",locationName:"If-Unmodified-Since",type:"timestamp"},Key:{location:"uri",locationName:"Key"},Range:{location:"header",locationName:"Range"},VersionId:{location:"querystring",locationName:"versionId"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKey:{shape:"S19",location:"header",locationName:"x-amz-server-side-encryption-customer-key"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"},PartNumber:{location:"querystring",locationName:"partNumber",type:"integer"}}},output:{type:"structure",members:{DeleteMarker:{location:"header",locationName:"x-amz-delete-marker",type:"boolean"},AcceptRanges:{location:"header",locationName:"accept-ranges"},Expiration:{location:"header",locationName:"x-amz-expiration"},Restore:{location:"header",locationName:"x-amz-restore"},LastModified:{location:"header",locationName:"Last-Modified",type:"timestamp"},ContentLength:{location:"header",locationName:"Content-Length",type:"long"},ETag:{location:"header",locationName:"ETag"},MissingMeta:{location:"header",locationName:"x-amz-missing-meta",type:"integer"},VersionId:{location:"header",locationName:"x-amz-version-id"},CacheControl:{location:"header",locationName:"Cache-Control"},ContentDisposition:{location:"header",locationName:"Content-Disposition"},ContentEncoding:{location:"header",locationName:"Content-Encoding"},ContentLanguage:{location:"header",locationName:"Content-Language"},ContentType:{location:"header",locationName:"Content-Type"},Expires:{location:"header",locationName:"Expires",type:"timestamp"},WebsiteRedirectLocation:{location:"header",locationName:"x-amz-website-redirect-location"},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},Metadata:{shape:"S11",location:"headers",locationName:"x-amz-meta-"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},StorageClass:{location:"header",locationName:"x-amz-storage-class"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"},ReplicationStatus:{location:"header",locationName:"x-amz-replication-status"},PartsCount:{location:"header",locationName:"x-amz-mp-parts-count",type:"integer"}}}},ListBucketAnalyticsConfigurations:{http:{method:"GET",requestUri:"/{Bucket}?analytics"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContinuationToken:{location:"querystring",locationName:"continuation-token"}}},output:{type:"structure",members:{IsTruncated:{type:"boolean"},ContinuationToken:{},NextContinuationToken:{},AnalyticsConfigurationList:{locationName:"AnalyticsConfiguration",type:"list",member:{shape:"S37"},flattened:true}}}},ListBucketInventoryConfigurations:{http:{method:"GET",requestUri:"/{Bucket}?inventory"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContinuationToken:{location:"querystring",locationName:"continuation-token"}}},output:{type:"structure",members:{ContinuationToken:{},InventoryConfigurationList:{locationName:"InventoryConfiguration",type:"list",member:{shape:"S46"},flattened:true},IsTruncated:{type:"boolean"},NextContinuationToken:{}}}},ListBucketMetricsConfigurations:{http:{method:"GET",requestUri:"/{Bucket}?metrics"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContinuationToken:{location:"querystring",locationName:"continuation-token"}}},output:{type:"structure",members:{IsTruncated:{type:"boolean"},ContinuationToken:{},NextContinuationToken:{},MetricsConfigurationList:{locationName:"MetricsConfiguration",type:"list",member:{shape:"S5j"},flattened:true}}}},ListBuckets:{http:{method:"GET"},output:{type:"structure",members:{Buckets:{type:"list",member:{locationName:"Bucket",type:"structure",members:{Name:{},CreationDate:{type:"timestamp"}}}},Owner:{shape:"S2v"}}},alias:"GetService"},ListMultipartUploads:{http:{method:"GET",requestUri:"/{Bucket}?uploads"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"},Delimiter:{location:"querystring",locationName:"delimiter"},EncodingType:{location:"querystring",locationName:"encoding-type"},KeyMarker:{location:"querystring",locationName:"key-marker"},MaxUploads:{location:"querystring",locationName:"max-uploads",type:"integer"},Prefix:{location:"querystring",locationName:"prefix"},UploadIdMarker:{location:"querystring",locationName:"upload-id-marker"}}},output:{type:"structure",members:{Bucket:{},KeyMarker:{},UploadIdMarker:{},NextKeyMarker:{},Prefix:{},Delimiter:{},NextUploadIdMarker:{},MaxUploads:{type:"integer"},IsTruncated:{type:"boolean"},Uploads:{locationName:"Upload",type:"list",member:{type:"structure",members:{UploadId:{},Key:{},Initiated:{type:"timestamp"},StorageClass:{},Owner:{shape:"S2v"},Initiator:{shape:"S97"}}},flattened:true},CommonPrefixes:{shape:"S98"},EncodingType:{}}}},ListObjectVersions:{http:{method:"GET",requestUri:"/{Bucket}?versions"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"},Delimiter:{location:"querystring",locationName:"delimiter"},EncodingType:{location:"querystring",locationName:"encoding-type"},KeyMarker:{location:"querystring",locationName:"key-marker"},MaxKeys:{location:"querystring",locationName:"max-keys",type:"integer"},Prefix:{location:"querystring",locationName:"prefix"},VersionIdMarker:{location:"querystring",locationName:"version-id-marker"}}},output:{type:"structure",members:{IsTruncated:{type:"boolean"},KeyMarker:{},VersionIdMarker:{},NextKeyMarker:{},NextVersionIdMarker:{},Versions:{locationName:"Version",type:"list",member:{type:"structure",members:{ETag:{},Size:{type:"integer"},StorageClass:{},Key:{},VersionId:{},IsLatest:{type:"boolean"},LastModified:{type:"timestamp"},Owner:{shape:"S2v"}}},flattened:true},DeleteMarkers:{locationName:"DeleteMarker",type:"list",member:{type:"structure",members:{Owner:{shape:"S2v"},Key:{},VersionId:{},IsLatest:{type:"boolean"},LastModified:{type:"timestamp"}}},flattened:true},Name:{},Prefix:{},Delimiter:{},MaxKeys:{type:"integer"},CommonPrefixes:{shape:"S98"},EncodingType:{}}},alias:"GetBucketObjectVersions"},ListObjects:{http:{method:"GET",requestUri:"/{Bucket}"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"},Delimiter:{location:"querystring",locationName:"delimiter"},EncodingType:{location:"querystring",locationName:"encoding-type"},Marker:{location:"querystring",locationName:"marker"},MaxKeys:{location:"querystring",locationName:"max-keys",type:"integer"},Prefix:{location:"querystring",locationName:"prefix"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{IsTruncated:{type:"boolean"},Marker:{},NextMarker:{},Contents:{shape:"S9q"},Name:{},Prefix:{},Delimiter:{},MaxKeys:{type:"integer"},CommonPrefixes:{shape:"S98"},EncodingType:{}}},alias:"GetBucket"},ListObjectsV2:{http:{method:"GET",requestUri:"/{Bucket}?list-type=2"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"},Delimiter:{location:"querystring",locationName:"delimiter"},EncodingType:{location:"querystring",locationName:"encoding-type"},MaxKeys:{location:"querystring",locationName:"max-keys",type:"integer"},Prefix:{location:"querystring",locationName:"prefix"},ContinuationToken:{location:"querystring",locationName:"continuation-token"},FetchOwner:{location:"querystring",locationName:"fetch-owner",type:"boolean"},StartAfter:{location:"querystring",locationName:"start-after"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{IsTruncated:{type:"boolean"},Contents:{shape:"S9q"},Name:{},Prefix:{},Delimiter:{},MaxKeys:{type:"integer"},CommonPrefixes:{shape:"S98"},EncodingType:{},KeyCount:{type:"integer"},ContinuationToken:{},NextContinuationToken:{},StartAfter:{}}}},ListParts:{http:{method:"GET",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","Key","UploadId"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},MaxParts:{location:"querystring",locationName:"max-parts",type:"integer"},PartNumberMarker:{location:"querystring",locationName:"part-number-marker",type:"integer"},UploadId:{location:"querystring",locationName:"uploadId"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{AbortDate:{location:"header",locationName:"x-amz-abort-date",type:"timestamp"},AbortRuleId:{location:"header",locationName:"x-amz-abort-rule-id"},Bucket:{},Key:{},UploadId:{},PartNumberMarker:{type:"integer"},NextPartNumberMarker:{type:"integer"},MaxParts:{type:"integer"},IsTruncated:{type:"boolean"},Parts:{locationName:"Part",type:"list",member:{type:"structure",members:{PartNumber:{type:"integer"},LastModified:{type:"timestamp"},ETag:{},Size:{type:"integer"}}},flattened:true},Initiator:{shape:"S97"},Owner:{shape:"S2v"},StorageClass:{},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}}},PutBucketAccelerateConfiguration:{http:{method:"PUT",requestUri:"/{Bucket}?accelerate"},input:{type:"structure",required:["Bucket","AccelerateConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},AccelerateConfiguration:{locationName:"AccelerateConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",members:{Status:{}}}},payload:"AccelerateConfiguration"}},PutBucketAcl:{http:{method:"PUT",requestUri:"/{Bucket}?acl"},input:{type:"structure",required:["Bucket"],members:{ACL:{location:"header",locationName:"x-amz-acl"},AccessControlPolicy:{shape:"Sa8",locationName:"AccessControlPolicy",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"}},Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},GrantFullControl:{location:"header",locationName:"x-amz-grant-full-control"},GrantRead:{location:"header",locationName:"x-amz-grant-read"},GrantReadACP:{location:"header",locationName:"x-amz-grant-read-acp"},GrantWrite:{location:"header",locationName:"x-amz-grant-write"},GrantWriteACP:{location:"header",locationName:"x-amz-grant-write-acp"}},payload:"AccessControlPolicy"}},PutBucketAnalyticsConfiguration:{http:{method:"PUT",requestUri:"/{Bucket}?analytics"},input:{type:"structure",required:["Bucket","Id","AnalyticsConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},Id:{location:"querystring",locationName:"id"},AnalyticsConfiguration:{shape:"S37",locationName:"AnalyticsConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"}}},payload:"AnalyticsConfiguration"}},PutBucketCors:{http:{method:"PUT",requestUri:"/{Bucket}?cors"},input:{type:"structure",required:["Bucket","CORSConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},CORSConfiguration:{locationName:"CORSConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",required:["CORSRules"],members:{CORSRules:{shape:"S3n",locationName:"CORSRule"}}},ContentMD5:{location:"header",locationName:"Content-MD5"}},payload:"CORSConfiguration"}},PutBucketEncryption:{http:{method:"PUT",requestUri:"/{Bucket}?encryption"},input:{type:"structure",required:["Bucket","ServerSideEncryptionConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},ServerSideEncryptionConfiguration:{shape:"S40",locationName:"ServerSideEncryptionConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"}}},payload:"ServerSideEncryptionConfiguration"}},PutBucketInventoryConfiguration:{http:{method:"PUT",requestUri:"/{Bucket}?inventory"},input:{type:"structure",required:["Bucket","Id","InventoryConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},Id:{location:"querystring",locationName:"id"},InventoryConfiguration:{shape:"S46",locationName:"InventoryConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"}}},payload:"InventoryConfiguration"}},PutBucketLifecycle:{http:{method:"PUT",requestUri:"/{Bucket}?lifecycle"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},LifecycleConfiguration:{locationName:"LifecycleConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",required:["Rules"],members:{Rules:{shape:"S4m",locationName:"Rule"}}}},payload:"LifecycleConfiguration"},deprecated:true},PutBucketLifecycleConfiguration:{http:{method:"PUT",requestUri:"/{Bucket}?lifecycle"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"},LifecycleConfiguration:{locationName:"LifecycleConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",required:["Rules"],members:{Rules:{shape:"S51",locationName:"Rule"}}}},payload:"LifecycleConfiguration"}},PutBucketLogging:{http:{method:"PUT",requestUri:"/{Bucket}?logging"},input:{type:"structure",required:["Bucket","BucketLoggingStatus"],members:{Bucket:{location:"uri",locationName:"Bucket"},BucketLoggingStatus:{locationName:"BucketLoggingStatus",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",members:{LoggingEnabled:{shape:"S5b"}}},ContentMD5:{location:"header",locationName:"Content-MD5"}},payload:"BucketLoggingStatus"}},PutBucketMetricsConfiguration:{http:{method:"PUT",requestUri:"/{Bucket}?metrics"},input:{type:"structure",required:["Bucket","Id","MetricsConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},Id:{location:"querystring",locationName:"id"},MetricsConfiguration:{shape:"S5j",locationName:"MetricsConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"}}},payload:"MetricsConfiguration"}},PutBucketNotification:{http:{method:"PUT",requestUri:"/{Bucket}?notification"},input:{type:"structure",required:["Bucket","NotificationConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},NotificationConfiguration:{shape:"S5n",locationName:"NotificationConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"}}},payload:"NotificationConfiguration"},deprecated:true},PutBucketNotificationConfiguration:{http:{method:"PUT",requestUri:"/{Bucket}?notification"},input:{type:"structure",required:["Bucket","NotificationConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},NotificationConfiguration:{shape:"S5y",locationName:"NotificationConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"}}},payload:"NotificationConfiguration"}},PutBucketPolicy:{http:{method:"PUT",requestUri:"/{Bucket}?policy"},input:{type:"structure",required:["Bucket","Policy"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},ConfirmRemoveSelfBucketAccess:{location:"header",locationName:"x-amz-confirm-remove-self-bucket-access",type:"boolean"},Policy:{}},payload:"Policy"}},PutBucketReplication:{http:{method:"PUT",requestUri:"/{Bucket}?replication"},input:{type:"structure",required:["Bucket","ReplicationConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},ReplicationConfiguration:{shape:"S6h",locationName:"ReplicationConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"}}},payload:"ReplicationConfiguration"}},PutBucketRequestPayment:{http:{method:"PUT",requestUri:"/{Bucket}?requestPayment"},input:{type:"structure",required:["Bucket","RequestPaymentConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},RequestPaymentConfiguration:{locationName:"RequestPaymentConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",required:["Payer"],members:{Payer:{}}}},payload:"RequestPaymentConfiguration"}},PutBucketTagging:{http:{method:"PUT",requestUri:"/{Bucket}?tagging"},input:{type:"structure",required:["Bucket","Tagging"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},Tagging:{shape:"Sau",locationName:"Tagging",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"}}},payload:"Tagging"}},PutBucketVersioning:{http:{method:"PUT",requestUri:"/{Bucket}?versioning"},input:{type:"structure",required:["Bucket","VersioningConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},MFA:{location:"header",locationName:"x-amz-mfa"},VersioningConfiguration:{locationName:"VersioningConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",members:{MFADelete:{locationName:"MfaDelete"},Status:{}}}},payload:"VersioningConfiguration"}},PutBucketWebsite:{http:{method:"PUT",requestUri:"/{Bucket}?website"},input:{type:"structure",required:["Bucket","WebsiteConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},WebsiteConfiguration:{locationName:"WebsiteConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",members:{ErrorDocument:{shape:"S7a"},IndexDocument:{shape:"S78"},RedirectAllRequestsTo:{shape:"S75"},RoutingRules:{shape:"S7b"}}}},payload:"WebsiteConfiguration"}},PutBucketTrigger:{http:{method:"PUT",requestUri:"/{Bucket}?trigger"},input:{type:"structure",required:["Bucket","TriggerConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},TriggerConfiguration:{locationName:"TriggerConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",members:{Trigger:{shape:"S5o1"}}}},payload:"TriggerConfiguration"}},PutObject:{http:{method:"PUT",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","Key"],members:{ACL:{location:"header",locationName:"x-amz-acl"},Body:{streaming:true,type:"blob"},Bucket:{location:"uri",locationName:"Bucket"},CacheControl:{location:"header",locationName:"Cache-Control"},ContentDisposition:{location:"header",locationName:"Content-Disposition"},ContentEncoding:{location:"header",locationName:"Content-Encoding"},ContentLanguage:{location:"header",locationName:"Content-Language"},ContentLength:{location:"header",locationName:"Content-Length",type:"long"},ContentMD5:{location:"header",locationName:"Content-MD5"},ContentType:{location:"header",locationName:"Content-Type"},Expires:{location:"header",locationName:"Expires",type:"timestamp"},GrantFullControl:{location:"header",locationName:"x-amz-grant-full-control"},GrantRead:{location:"header",locationName:"x-amz-grant-read"},GrantReadACP:{location:"header",locationName:"x-amz-grant-read-acp"},GrantWriteACP:{location:"header",locationName:"x-amz-grant-write-acp"},Key:{location:"uri",locationName:"Key"},Metadata:{shape:"S11",location:"headers",locationName:"x-amz-meta-"},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},StorageClass:{location:"header",locationName:"x-amz-storage-class"},WebsiteRedirectLocation:{location:"header",locationName:"x-amz-website-redirect-location"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKey:{shape:"S19",location:"header",locationName:"x-amz-server-side-encryption-customer-key"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"},Tagging:{location:"header",locationName:"x-amz-tagging"},Trigger:{location:"header",locationName:"x-ctyun-trigger"}},payload:"Body"},output:{type:"structure",members:{Expiration:{location:"header",locationName:"x-amz-expiration"},ETag:{location:"header",locationName:"ETag"},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},VersionId:{location:"header",locationName:"x-amz-version-id"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}}},PutObjectAcl:{http:{method:"PUT",requestUri:"/{Bucket}/{Key+}?acl"},input:{type:"structure",required:["Bucket","Key"],members:{ACL:{location:"header",locationName:"x-amz-acl"},AccessControlPolicy:{shape:"Sa8",locationName:"AccessControlPolicy",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"}},Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},GrantFullControl:{location:"header",locationName:"x-amz-grant-full-control"},GrantRead:{location:"header",locationName:"x-amz-grant-read"},GrantReadACP:{location:"header",locationName:"x-amz-grant-read-acp"},GrantWrite:{location:"header",locationName:"x-amz-grant-write"},GrantWriteACP:{location:"header",locationName:"x-amz-grant-write-acp"},Key:{location:"uri",locationName:"Key"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"},VersionId:{location:"querystring",locationName:"versionId"}},payload:"AccessControlPolicy"},output:{type:"structure",members:{RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}}},PutObjectTagging:{http:{method:"PUT",requestUri:"/{Bucket}/{Key+}?tagging"},input:{type:"structure",required:["Bucket","Key","Tagging"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},VersionId:{location:"querystring",locationName:"versionId"},ContentMD5:{location:"header",locationName:"Content-MD5"},Tagging:{shape:"Sau",locationName:"Tagging",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"}}},payload:"Tagging"},output:{type:"structure",members:{VersionId:{location:"header",locationName:"x-amz-version-id"}}}},RestoreObject:{http:{requestUri:"/{Bucket}/{Key+}?restore"},input:{type:"structure",required:["Bucket","Key"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},VersionId:{location:"querystring",locationName:"versionId"},RestoreRequest:{locationName:"RestoreRequest",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",members:{Days:{type:"integer"},GlacierJobParameters:{type:"structure",required:["Tier"],members:{Tier:{}}},Type:{},Tier:{},Description:{},SelectParameters:{type:"structure",required:["InputSerialization","ExpressionType","Expression","OutputSerialization"],members:{InputSerialization:{shape:"Sbd"},ExpressionType:{},Expression:{},OutputSerialization:{shape:"Sbr"}}},OutputLocation:{type:"structure",members:{S3:{type:"structure",required:["BucketName","Prefix"],members:{BucketName:{},Prefix:{},Encryption:{type:"structure",required:["EncryptionType"],members:{EncryptionType:{},KMSKeyId:{shape:"Sj"},KMSContext:{}}},CannedACL:{},AccessControlList:{shape:"S2y"},Tagging:{shape:"Sau"},UserMetadata:{type:"list",member:{locationName:"MetadataEntry",type:"structure",members:{Name:{},Value:{}}}},StorageClass:{}}}}}}},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}},payload:"RestoreRequest"},output:{type:"structure",members:{RequestCharged:{location:"header",locationName:"x-amz-request-charged"},RestoreOutputPath:{location:"header",locationName:"x-amz-restore-output-path"}}},alias:"PostObjectRestore"},SelectObjectContent:{http:{requestUri:"/{Bucket}/{Key+}?select&select-type=2"},input:{locationName:"SelectObjectContentRequest",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",required:["Bucket","Key","Expression","ExpressionType","InputSerialization","OutputSerialization"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKey:{shape:"S19",location:"header",locationName:"x-amz-server-side-encryption-customer-key"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},Expression:{},ExpressionType:{},RequestProgress:{type:"structure",members:{Enabled:{type:"boolean"}}},InputSerialization:{shape:"Sbd"},OutputSerialization:{shape:"Sbr"}}},output:{type:"structure",members:{Payload:{type:"structure",members:{Records:{type:"structure",members:{Payload:{eventpayload:true,type:"blob"}},event:true},Stats:{type:"structure",members:{Details:{eventpayload:true,type:"structure",members:{BytesScanned:{type:"long"},BytesProcessed:{type:"long"},BytesReturned:{type:"long"}}}},event:true},Progress:{type:"structure",members:{Details:{eventpayload:true,type:"structure",members:{BytesScanned:{type:"long"},BytesProcessed:{type:"long"},BytesReturned:{type:"long"}}}},event:true},Cont:{type:"structure",members:{},event:true},End:{type:"structure",members:{},event:true}},eventstream:true}},payload:"Payload"}},UploadPart:{http:{method:"PUT",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","Key","PartNumber","UploadId"],members:{Body:{streaming:true,type:"blob"},Bucket:{location:"uri",locationName:"Bucket"},ContentLength:{location:"header",locationName:"Content-Length",type:"long"},ContentMD5:{location:"header",locationName:"Content-MD5"},Key:{location:"uri",locationName:"Key"},PartNumber:{location:"querystring",locationName:"partNumber",type:"integer"},UploadId:{location:"querystring",locationName:"uploadId"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKey:{shape:"S19",location:"header",locationName:"x-amz-server-side-encryption-customer-key"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}},payload:"Body"},output:{type:"structure",members:{ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},ETag:{location:"header",locationName:"ETag"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}}},UploadPartCopy:{http:{method:"PUT",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","CopySource","Key","PartNumber","UploadId"],members:{Bucket:{location:"uri",locationName:"Bucket"},CopySource:{location:"header",locationName:"x-amz-copy-source"},CopySourceIfMatch:{location:"header",locationName:"x-amz-copy-source-if-match"},CopySourceIfModifiedSince:{location:"header",locationName:"x-amz-copy-source-if-modified-since",type:"timestamp"},CopySourceIfNoneMatch:{location:"header",locationName:"x-amz-copy-source-if-none-match"},CopySourceIfUnmodifiedSince:{location:"header",locationName:"x-amz-copy-source-if-unmodified-since",type:"timestamp"},CopySourceRange:{location:"header",locationName:"x-amz-copy-source-range"},Key:{location:"uri",locationName:"Key"},PartNumber:{location:"querystring",locationName:"partNumber",type:"integer"},UploadId:{location:"querystring",locationName:"uploadId"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKey:{shape:"S19",location:"header",locationName:"x-amz-server-side-encryption-customer-key"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},CopySourceSSECustomerAlgorithm:{location:"header",locationName:"x-amz-copy-source-server-side-encryption-customer-algorithm"},CopySourceSSECustomerKey:{shape:"S1c",location:"header",locationName:"x-amz-copy-source-server-side-encryption-customer-key"},CopySourceSSECustomerKeyMD5:{location:"header",locationName:"x-amz-copy-source-server-side-encryption-customer-key-MD5"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{CopySourceVersionId:{location:"header",locationName:"x-amz-copy-source-version-id"},CopyPartResult:{type:"structure",members:{ETag:{},LastModified:{type:"timestamp"}}},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}},payload:"CopyPartResult"}}},shapes:{Sj:{type:"string",sensitive:true},S11:{type:"map",key:{},value:{}},S19:{type:"blob",sensitive:true},S1c:{type:"blob",sensitive:true},S2v:{type:"structure",members:{DisplayName:{},ID:{}}},S2y:{type:"list",member:{locationName:"Grant",type:"structure",members:{Grantee:{shape:"S30"},Permission:{}}}},S30:{type:"structure",required:["Type"],members:{DisplayName:{},EmailAddress:{},ID:{},Type:{locationName:"xsi:type",xmlAttribute:true},URI:{}},xmlNamespace:{prefix:"xsi",uri:"http://www.w3.org/2001/XMLSchema-instance"}},S37:{type:"structure",required:["Id","StorageClassAnalysis"],members:{Id:{},Filter:{type:"structure",members:{Prefix:{},Tag:{shape:"S3a"},And:{type:"structure",members:{Prefix:{},Tags:{shape:"S3d",flattened:true,locationName:"Tag"}}}}},StorageClassAnalysis:{type:"structure",members:{DataExport:{type:"structure",required:["OutputSchemaVersion","Destination"],members:{OutputSchemaVersion:{},Destination:{type:"structure",required:["S3BucketDestination"],members:{S3BucketDestination:{type:"structure",required:["Format","Bucket"],members:{Format:{},BucketAccountId:{},Bucket:{},Prefix:{}}}}}}}}}}},S3a:{type:"structure",required:["Key","Value"],members:{Key:{},Value:{}}},S3d:{type:"list",member:{shape:"S3a",locationName:"Tag"}},S3n:{type:"list",member:{type:"structure",required:["AllowedMethods","AllowedOrigins"],members:{AllowedHeaders:{locationName:"AllowedHeader",type:"list",member:{},flattened:true},AllowedMethods:{locationName:"AllowedMethod",type:"list",member:{},flattened:true},AllowedOrigins:{locationName:"AllowedOrigin",type:"list",member:{},flattened:true},ExposeHeaders:{locationName:"ExposeHeader",type:"list",member:{},flattened:true},MaxAgeSeconds:{type:"integer"}}},flattened:true},S40:{type:"structure",required:["Rules"],members:{Rules:{locationName:"Rule",type:"list",member:{type:"structure",members:{ApplyServerSideEncryptionByDefault:{type:"structure",required:["SSEAlgorithm"],members:{SSEAlgorithm:{},KMSMasterKeyID:{shape:"Sj"}}}}},flattened:true}}},S46:{type:"structure",required:["Destination","IsEnabled","Id","IncludedObjectVersions","Schedule"],members:{Destination:{type:"structure",required:["S3BucketDestination"],members:{S3BucketDestination:{type:"structure",required:["Bucket","Format"],members:{AccountId:{},Bucket:{},Format:{},Prefix:{},Encryption:{type:"structure",members:{SSES3:{locationName:"SSE-S3",type:"structure",members:{}},SSEKMS:{locationName:"SSE-KMS",type:"structure",required:["KeyId"],members:{KeyId:{shape:"Sj"}}}}}}}}},IsEnabled:{type:"boolean"},Filter:{type:"structure",required:["Prefix"],members:{Prefix:{}}},Id:{},IncludedObjectVersions:{},OptionalFields:{type:"list",member:{locationName:"Field"}},Schedule:{type:"structure",required:["Frequency"],members:{Frequency:{}}}}},S4m:{type:"list",member:{type:"structure",required:["Prefix","Status"],members:{Expiration:{shape:"S4o"},ID:{},Prefix:{},Status:{},Transition:{shape:"S4t"},NoncurrentVersionTransition:{shape:"S4v"},NoncurrentVersionExpiration:{shape:"S4w"},AbortIncompleteMultipartUpload:{shape:"S4x"}}},flattened:true},S4o:{type:"structure",members:{Date:{shape:"S4p"},Days:{type:"integer"},ExpiredObjectDeleteMarker:{type:"boolean"}}},S4p:{type:"timestamp",timestampFormat:"iso8601"},S4t:{type:"structure",members:{Date:{shape:"S4p"},Days:{type:"integer"},StorageClass:{}}},S4v:{type:"structure",members:{NoncurrentDays:{type:"integer"},StorageClass:{}}},S4w:{type:"structure",members:{NoncurrentDays:{type:"integer"}}},S4x:{type:"structure",members:{DaysAfterInitiation:{type:"integer"}}},S51:{type:"list",member:{type:"structure",required:["Status"],members:{Expiration:{shape:"S4o"},ID:{},Prefix:{deprecated:true},Filter:{type:"structure",members:{Prefix:{},Tag:{shape:"S3a"},And:{type:"structure",members:{Prefix:{},Tags:{shape:"S3d",flattened:true,locationName:"Tag"}}}}},Status:{},Transitions:{locationName:"Transition",type:"list",member:{shape:"S4t"},flattened:true},NoncurrentVersionTransitions:{locationName:"NoncurrentVersionTransition",type:"list",member:{shape:"S4v"},flattened:true},NoncurrentVersionExpiration:{shape:"S4w"},AbortIncompleteMultipartUpload:{shape:"S4x"}}},flattened:true},S5o1:{type:"structure",required:["TriggerName","IsDefault"],members:{TriggerName:{},IsDefault:{type:"boolean"},RemoteSite:{shape:"S5o1a"}}},S5o1a:{type:"structure",required:["RemontEndPoint","RemoteBucketName","RemoteAK","RemoteSK"],members:{RemontEndPoint:{},ReplicaMode:{},RemoteBucketName:{},RemoteAK:{},RemoteSK:{}}},S5o1b:{type:"list",member:{type:"structure",members:{TriggerName:{},IsDefault:{type:"boolean"},RemoteSite:{shape:"S5o1a"}}},flattened:true},S5b:{type:"structure",required:["TargetBucket","TargetPrefix"],members:{TargetBucket:{},TargetGrants:{type:"list",member:{locationName:"Grant",type:"structure",members:{Grantee:{shape:"S30"},Permission:{}}}},TargetPrefix:{}}},S5j:{type:"structure",required:["Id"],members:{Id:{},Filter:{type:"structure",members:{Prefix:{},Tag:{shape:"S3a"},And:{type:"structure",members:{Prefix:{},Tags:{shape:"S3d",flattened:true,locationName:"Tag"}}}}}}},S5m:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},S5n:{type:"structure",members:{TopicConfiguration:{type:"structure",members:{Id:{},Events:{shape:"S5q",locationName:"Event"},Event:{deprecated:true},Topic:{}}},QueueConfiguration:{type:"structure",members:{Id:{},Event:{deprecated:true},Events:{shape:"S5q",locationName:"Event"},Queue:{}}},CloudFunctionConfiguration:{type:"structure",members:{Id:{},Event:{deprecated:true},Events:{shape:"S5q",locationName:"Event"},CloudFunction:{},InvocationRole:{}}}}},S5q:{type:"list",member:{},flattened:true},S5y:{type:"structure",members:{TopicConfigurations:{locationName:"TopicConfiguration",type:"list",member:{type:"structure",required:["TopicArn","Events"],members:{Id:{},TopicArn:{locationName:"Topic"},Events:{shape:"S5q",locationName:"Event"},Filter:{shape:"S61"}}},flattened:true},QueueConfigurations:{locationName:"QueueConfiguration",type:"list",member:{type:"structure",required:["QueueArn","Events"],members:{Id:{},QueueArn:{locationName:"Queue"},Events:{shape:"S5q",locationName:"Event"},Filter:{shape:"S61"}}},flattened:true},LambdaFunctionConfigurations:{locationName:"CloudFunctionConfiguration",type:"list",member:{type:"structure",required:["LambdaFunctionArn","Events"],members:{Id:{},LambdaFunctionArn:{locationName:"CloudFunction"},Events:{shape:"S5q",locationName:"Event"},Filter:{shape:"S61"}}},flattened:true}}},S61:{type:"structure",members:{Key:{locationName:"S3Key",type:"structure",members:{FilterRules:{locationName:"FilterRule",type:"list",member:{type:"structure",members:{Name:{},Value:{}}},flattened:true}}}}},S6h:{type:"structure",required:["Role","Rules"],members:{Role:{},Rules:{locationName:"Rule",type:"list",member:{type:"structure",required:["Prefix","Status","Destination"],members:{ID:{},Prefix:{},Status:{},SourceSelectionCriteria:{type:"structure",members:{SseKmsEncryptedObjects:{type:"structure",required:["Status"],members:{Status:{}}}}},Destination:{type:"structure",required:["Bucket"],members:{Bucket:{},Account:{},StorageClass:{},AccessControlTranslation:{type:"structure",required:["Owner"],members:{Owner:{}}},EncryptionConfiguration:{type:"structure",members:{ReplicaKmsKeyID:{}}}}}}},flattened:true}}},S75:{type:"structure",required:["HostName"],members:{HostName:{},Protocol:{}}},S78:{type:"structure",required:["Suffix"],members:{Suffix:{}}},S7a:{type:"structure",required:["Key"],members:{Key:{}}},S7b:{type:"list",member:{locationName:"RoutingRule",type:"structure",required:["Redirect"],members:{Condition:{type:"structure",members:{HttpErrorCodeReturnedEquals:{},KeyPrefixEquals:{}}},Redirect:{type:"structure",members:{HostName:{},HttpRedirectCode:{},Protocol:{},ReplaceKeyPrefixWith:{},ReplaceKeyWith:{}}}}}},S97:{type:"structure",members:{ID:{},DisplayName:{}}},S98:{type:"list",member:{type:"structure",members:{Prefix:{}}},flattened:true},S9q:{type:"list",member:{type:"structure",members:{Key:{},LastModified:{type:"timestamp"},ETag:{},Size:{type:"integer"},StorageClass:{},Owner:{shape:"S2v"}}},flattened:true},Sa8:{type:"structure",members:{Grants:{shape:"S2y",locationName:"AccessControlList"},Owner:{shape:"S2v"}}},Sau:{type:"structure",required:["TagSet"],members:{TagSet:{shape:"S3d"}}},Sbd:{type:"structure",members:{CSV:{type:"structure",members:{FileHeaderInfo:{},Comments:{},QuoteEscapeCharacter:{},RecordDelimiter:{},FieldDelimiter:{},QuoteCharacter:{},AllowQuotedRecordDelimiter:{type:"boolean"}}},CompressionType:{},JSON:{type:"structure",members:{Type:{}}}}},Sbr:{type:"structure",members:{CSV:{type:"structure",members:{QuoteFields:{},QuoteEscapeCharacter:{},RecordDelimiter:{},FieldDelimiter:{},QuoteCharacter:{}}},JSON:{type:"structure",members:{RecordDelimiter:{}}}}}},paginators:{ListBuckets:{result_key:"Buckets"},ListMultipartUploads:{input_token:["KeyMarker","UploadIdMarker"],limit_key:"MaxUploads",more_results:"IsTruncated",output_token:["NextKeyMarker","NextUploadIdMarker"],result_key:["Uploads","CommonPrefixes"]},ListObjectVersions:{input_token:["KeyMarker","VersionIdMarker"],limit_key:"MaxKeys",more_results:"IsTruncated",output_token:["NextKeyMarker","NextVersionIdMarker"],result_key:["Versions","DeleteMarkers","CommonPrefixes"]},ListObjects:{input_token:"Marker",limit_key:"MaxKeys",more_results:"IsTruncated",output_token:"NextMarker || Contents[-1].Key",result_key:["Contents","CommonPrefixes"]},ListObjectsV2:{input_token:"ContinuationToken",limit_key:"MaxKeys",output_token:"NextContinuationToken",result_key:["Contents","CommonPrefixes"]},ListParts:{input_token:"PartNumberMarker",limit_key:"MaxParts",more_results:"IsTruncated",output_token:"NextPartNumberMarker",result_key:"Parts"}},waiters:{BucketExists:{delay:5,operation:"HeadBucket",maxAttempts:20,acceptors:[{expected:200,matcher:"status",state:"success"},{expected:301,matcher:"status",state:"success"},{expected:403,matcher:"status",state:"success"},{expected:404,matcher:"status",state:"retry"}]},BucketNotExists:{delay:5,operation:"HeadBucket",maxAttempts:20,acceptors:[{expected:404,matcher:"status",state:"success"}]},ObjectExists:{delay:5,operation:"HeadObject",maxAttempts:20,acceptors:[{expected:200,matcher:"status",state:"success"},{expected:404,matcher:"status",state:"retry"}]},ObjectNotExists:{delay:5,operation:"HeadObject",maxAttempts:20,acceptors:[{expected:404,matcher:"status",state:"success"}]}}};OOS.apiLoader.services["sts"]={};OOS.STS=OOS.Service.defineService("sts",["2011-06-15"]);_xamzrequire=function e(t,r,i){function n(o,s){if(!r[o]){if(!t[o]){var u=typeof _xamzrequire=="function"&&_xamzrequire;if(!s&&u)return u(o,!0);if(a)return a(o,!0);var c=new Error("Cannot find module '"+o+"'");throw c.code="MODULE_NOT_FOUND",c}var l=r[o]={exports:{}};t[o][0].call(l.exports,function(e){var r=t[o][1][e];return n(r?r:e)},l,l.exports,e,t,r,i)}return r[o].exports}var a=typeof _xamzrequire=="function"&&_xamzrequire;for(var o=0;o<i.length;o++)n(i[o]);return n}({101:[function(e,t,r){var i=e("../core");i.util.update(i.STS.prototype,{credentialsFrom:function e(t,r){if(!t)return null;if(!r)r=new i.TemporaryCredentials;r.expired=false;r.accessKeyId=t.Credentials.AccessKeyId;r.secretAccessKey=t.Credentials.SecretAccessKey;r.sessionToken=t.Credentials.SessionToken;r.expireTime=t.Credentials.Expiration;return r},assumeRoleWithWebIdentity:function e(t,r){return this.makeUnauthenticatedRequest("assumeRoleWithWebIdentity",t,r)},assumeRoleWithSAML:function e(t,r){return this.makeUnauthenticatedRequest("assumeRoleWithSAML",t,r)}})},{"../core":38}]},{},[101]);OOS.apiLoader.services["sts"]["2011-06-15"]={version:"2.0",metadata:{apiVersion:"2011-06-15",endpointPrefix:"sts",globalEndpoint:"sts.amazonaws.com",protocol:"query",serviceAbbreviation:"OOS STS",serviceFullName:"OOS Security Token Service",serviceId:"STS",signatureVersion:"v4",uid:"sts-2011-06-15",xmlNamespace:"https://sts.amazonaws.com/doc/2011-06-15/"},operations:{AssumeRole:{input:{type:"structure",required:["RoleArn","RoleSessionName"],members:{RoleArn:{},RoleSessionName:{},Policy:{},DurationSeconds:{type:"integer"},ExternalId:{},SerialNumber:{},TokenCode:{}}},output:{resultWrapper:"AssumeRoleResult",type:"structure",members:{Credentials:{shape:"Sa"},AssumedRoleUser:{shape:"Sf"},PackedPolicySize:{type:"integer"}}}},AssumeRoleWithSAML:{input:{type:"structure",required:["RoleArn","PrincipalArn","SAMLAssertion"],members:{RoleArn:{},PrincipalArn:{},SAMLAssertion:{},Policy:{},DurationSeconds:{type:"integer"}}},output:{resultWrapper:"AssumeRoleWithSAMLResult",type:"structure",members:{Credentials:{shape:"Sa"},AssumedRoleUser:{shape:"Sf"},PackedPolicySize:{type:"integer"},Subject:{},SubjectType:{},Issuer:{},Audience:{},NameQualifier:{}}}},AssumeRoleWithWebIdentity:{input:{type:"structure",required:["RoleArn","RoleSessionName","WebIdentityToken"],members:{RoleArn:{},RoleSessionName:{},WebIdentityToken:{},ProviderId:{},Policy:{},DurationSeconds:{type:"integer"}}},output:{resultWrapper:"AssumeRoleWithWebIdentityResult",type:"structure",members:{Credentials:{shape:"Sa"},SubjectFromWebIdentityToken:{},AssumedRoleUser:{shape:"Sf"},PackedPolicySize:{type:"integer"},Provider:{},Audience:{}}}},DecodeAuthorizationMessage:{input:{type:"structure",required:["EncodedMessage"],members:{EncodedMessage:{}}},output:{resultWrapper:"DecodeAuthorizationMessageResult",type:"structure",members:{DecodedMessage:{}}}},GetCallerIdentity:{input:{type:"structure",members:{}},output:{resultWrapper:"GetCallerIdentityResult",type:"structure",members:{UserId:{},Account:{},Arn:{}}}},GetFederationToken:{input:{type:"structure",required:["Name"],members:{Name:{},Policy:{},DurationSeconds:{type:"integer"}}},output:{resultWrapper:"GetFederationTokenResult",type:"structure",members:{Credentials:{shape:"Sa"},FederatedUser:{type:"structure",required:["FederatedUserId","Arn"],members:{FederatedUserId:{},Arn:{}}},PackedPolicySize:{type:"integer"}}}},GetSessionToken:{input:{type:"structure",members:{DurationSeconds:{type:"integer"},SerialNumber:{},TokenCode:{}}},output:{resultWrapper:"GetSessionTokenResult",type:"structure",members:{Credentials:{shape:"Sa"}}}}},shapes:{Sa:{type:"structure",required:["AccessKeyId","SecretAccessKey","SessionToken","Expiration"],members:{AccessKeyId:{},SecretAccessKey:{},SessionToken:{},Expiration:{type:"timestamp"}}},Sf:{type:"structure",required:["AssumedRoleId","Arn"],members:{AssumedRoleId:{},Arn:{}}}},paginators:{}};