import React ,{FC,useEffect,useState,useRef} from "react";
import LeftMenu from '@/layout/leftMenu';
import './index.less';
import { Button, Form, Input ,Col, Row ,Timeline,Checkbox,Table,Select,DatePicker,message,Modal,Drawer} from 'antd';
import {
  CloseCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import progressApis from '@/service/progress';
const { RangePicker } = DatePicker;
import TeacherItem from '@/components/formItemBox/teacherItem';
import { useSelector } from "umi";

const taskprogress:FC<any> = ({}) => {
  const [form] = Form.useForm();
  const [formLayout, setFormLayout] = useState<any>('inline');
  const [dataSource, setDataSource] = useState<any>([]);
  const [configdata, setConfigdata] = useState<any>({
    '任务模块':[],
    '任务进度':[],
    '任务状态':[],
    '任务类型':[],
  });
  // const [taskCreator, setTaskCreator] = useState<any>(null);
  const taskCreator = useRef<any>(null);
  // 分页
  const [pageinfo, setPageinfo] = useState<any>({
    page:1,
    pageSize:10,
    total:0
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [progressdetail,setProgressdetail] = useState<any>([]);
  const [progressdetailvisible,setProgressdetailvisible] = useState<boolean>(false);
  const { parameterConfig } = useSelector<{ permission: any }, IPermission>(
    ({ permission }) => permission,
  );
  useEffect(() => {
    progressApis.getconfig().then((res:any)=>{
      if(res.success){
        setConfigdata(res.data);
        getdata({
          page:pageinfo.page,
          size:pageinfo.pageSize,
        });
      }
    })
  }, []);

  const getdata = (values:any) => {
    progressApis.searchprogress({
      taskName:values.taskName,
      taskModule:values.taskModule,
      taskType:values.taskType,
      taskCreator:taskCreator.current ? taskCreator.current : undefined,
      startTime:values.taskTime ? values.taskTime[0].format('YYYY-MM-DD HH:mm:ss') : undefined,
      endTime:values.taskTime ? values.taskTime[1].format('YYYY-MM-DD HH:mm:ss') : undefined,
      taskProgress:values.taskProgress,
      taskStatus:values.taskStatus,
      page:values.page,
      size:values.size,
    }).then((res:any)=>{
      if(res.success){
        setDataSource(res.data.data);
        setPageinfo({
          page:res.data.pageIndex,
          pageSize:res.data.pageSize,
          total:res.data.recordTotal
        })
      }
    })
  }

  const columns:any = [
    {
      title: '任务名称',
      dataIndex: 'taskName',
      key: 'taskName',
      width: '300px',
      ellipsis: true,
      align:'center',
      render : (text:any, record: { taskId: string; }) =>(
        <span style={{color: 'var(--primary-color)', cursor: 'pointer'}} onClick={()=>{getprogressdetail(record.taskId)}}>{text}</span>
      )
    },
    {
      title: '任务模块',
      dataIndex: 'taskModule',
      key: 'taskModule',
      align:'center',
    },
    {
      title: '任务类型',
      dataIndex: 'taskType',
      key: 'taskType',
      align:'center',
    },
    {
      title: '发起人',
      dataIndex: 'taskCreator',
      key: 'taskCreator',
      align:'center',
    },
    {
      title: '当前进度',
      dataIndex: 'taskProgress',
      key: 'taskProgress',
      align:'center',
    },
    {
      title: '任务状态',
      dataIndex: 'taskStatus',
      key: 'taskStatus',
      align:'center',
    },

    {
      title: '任务开始时间',
      dataIndex: 'taskStartTime',
      key: 'taskStartTime',
      align:'center',
      width: '180px'
    },
    {
      title: '任务完成时间',
      dataIndex: 'taskEndTime',
      key: 'taskEndTime',
      align:'center',
      width: '180px'
    },
    {
      title: '任务创建时间',
      dataIndex: 'taskCreatetTime',
      key: 'taskCreatetTime',
      align:'center',
      width: '180px'
    },
    {
      title: '操作',
      dataIndex: 'id',
      key: 'id',
      width: '180px',
      align:'center',
      render : (text:any,record:any) => (
        <div className="table_btn_box">
          <Button type="link" size="small" onClick={()=>{getprogressdetail(record.taskId)}}>查看</Button>
          <Button type="link" size="small" disabled={record.taskStatus === '已结束' || record.taskStatus === '进行中'} onClick={()=>{confirmredo(record.taskId)}}>重做</Button>
          <Button type="link" size="small" danger onClick={()=>confirmcancel(record.taskId)}>取消</Button>
        </div>
      )

    },
  ]

  // 选择框发生变化
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    console.log('selectedRowKeys changed: ', newSelectedRowKeys);
    setSelectedRowKeys(newSelectedRowKeys);
  };

  // 选择框数据
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  // 确认重做
  const confirmredo = (taskId?:string) => {
    Modal.confirm({
      title: '提示',
      content: '是否确认重做？',
      okText: '确认',
      cancelText: '取消',
      onOk:()=>{
        taskredo(taskId);
      }
    });
  }

  // 重做任务
  const taskredo = (taskId?:string) => {
    if(taskId){
      progressApis.taskredo({
        taskId
      }).then((res:any)=>{
        if(res.success){
          message.success('重做成功！');
        }else{
          message.error('重做失败！');
        }
        getdata({
          ...form.getFieldsValue(),
          page:pageinfo.page,
          size:pageinfo.pageSize,
        });
      })
    }else{
      progressApis.taskbulkredo(selectedRowKeys).then((res:any)=>{
        if(res.success){
          message.success('重做成功！');
        }else{
          message.error('重做失败！');
        }
        setSelectedRowKeys([]);
        getdata({
          ...form.getFieldsValue(),
          page:pageinfo.page,
          size:pageinfo.pageSize,
        });
      })
    }
  }

  // 取消任务
  const confirmcancel = (taskId?:string) => {
    Modal.confirm({
      title: '提示',
      content: '是否取消任务？',
      okText: '确认',
      cancelText: '取消',
      onOk:()=>{
        taskcancel(taskId);
      }
    });
  }

  // 重做任务
  const taskcancel = (taskId?:string) => {
    if(taskId){
      progressApis.taskcancel({
        taskId
      }).then((res:any)=>{
        if(res.success){
          message.success('取消任务成功！');
        }else{
          message.error('取消任务失败！');
        }
        getdata({
          ...form.getFieldsValue(),
          page:pageinfo.page,
          size:pageinfo.pageSize,
        });
      })
    }else{
      progressApis.taskbulkcancel(selectedRowKeys).then((res:any)=>{
        if(res.success){
          message.success('取消任务成功！');
        }else{
          message.error('取消任务失败！');
        }
        setSelectedRowKeys([]);
        getdata({
          ...form.getFieldsValue(),
          page:pageinfo.page,
          size:pageinfo.pageSize,
        });
      })
    }
  }

  // 查询流程详情
  const getprogressdetail = (taskId:string) => {
    progressApis.getprogressinfo(taskId).then((res:any)=>{
      if(res.success){
        setProgressdetail(res.data);
        setProgressdetailvisible(true);
      }else{
        setProgressdetail([]);
        message.error('查询流程详情失败！');
      }
    })
  }



  return (
    <div className="taskprogress_container">
      <div className="taskprogress_bottom">
        {window.localStorage.getItem('upform_platform') === 'standard' && parameterConfig.target_customer as string !=='npu' && <LeftMenu userInfo={(window as any).login_useInfo} />}
        <div className="search_box">
          <div className="search_input">
            <Form
              layout={formLayout}
              form={form}
              initialValues={{ layout: formLayout }}
              onValuesChange={(changedValues, allValues) => {}}
              onFinish={(values)=>{
                getdata({
                  ...values,
                  page:pageinfo.page,
                  size:pageinfo.pageSize,
                });
              }}
            >
              <Row style={{width:'100%',marginTop:'20px'}}>
                <Col span={3}>
                  <Form.Item name="taskName" >
                    <Input placeholder="请输入任务名称" />
                  </Form.Item>
                </Col>
                {/* <Col span={6}>
                  <Form.Item name="taskModule" >
                    <Select allowClear placeholder="请输入任务模块" options={
                      configdata['任务模块'].map((item:any)=>{
                        return {label:item,value:item}
                      })
                    }>
                    </Select>
                  </Form.Item>
                </Col> */}
                <Col span={3}>
                  <Form.Item name="taskType" >
                    <Select allowClear placeholder="请输入任务类型" options={
                      configdata['任务类型'].map((item:any)=>{
                        return {label:item,value:item}
                      })
                    }>
                    </Select>
                  </Form.Item>
                </Col>
                {/* <Col span={6}>
                    <TeacherItem
                      multiple={false}
                      required={false}
                      message={'任务发起人'}
                      label={''}
                      name={'taskCreator'}
                      key={'taskCreator'}
                      className={'basicTeacherItem'}
                      onChange={(value:any)=>{
                        if(value){
                          taskCreator.current = value.children.props.children[0];
                        }else{
                          taskCreator.current = null;
                        }
                      }}
                    />
                </Col> */}
                <Col span={3}>
                  <Form.Item name="taskTime" >
                    <RangePicker  showTime />
                  </Form.Item>
                </Col>
                <Col span={3}>
                  <Form.Item name="taskProgress" >
                    <Select allowClear placeholder="请输入任务进度" options={
                      configdata['任务进度'].map((item:any)=>{
                        return {label:item,value:item}
                      })
                    }>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={3}>
                  <Form.Item name="taskStatus" >
                    <Select allowClear placeholder="请输入任务状态" options={
                        configdata['任务状态'].map((item:any)=>{
                          return {label:item,value:item}
                        })
                      }>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={3}>
                  <Form.Item>
                    <Button type="primary" htmlType="submit">查询</Button>
                    <Button style={{marginLeft:'10px'}} onClick={()=>{
                       taskCreator.current = null;
                       form.resetFields();
                       getdata({
                        page:1,
                        size:pageinfo.pageSize,
                      });
                    }}>清空</Button>
                  </Form.Item>
                </Col>
              </Row>
              </Form>
          </div>
          <Row style={{width:'100%',marginTop:'20px'}}>
            <Col span={24}>
              {/* <Checkbox onChange={(e)=>{
                if(e.target.checked){
                  setSelectedRowKeys(dataSource.map((item:any)=>item.id));
                }else{
                  setSelectedRowKeys([]);
                }
              }}
              indeterminate={ selectedRowKeys.length > 0 && selectedRowKeys.length < dataSource.length }
              checked={selectedRowKeys.length === dataSource.length && dataSource.length !== 0}
              >全选</Checkbox> */}
              <Button disabled={!selectedRowKeys.length} type="primary" icon={<CloseCircleOutlined />} htmlType="submit" onClick={()=>confirmcancel()}>取消</Button>
              <Button disabled={!selectedRowKeys.length} type="primary" icon={<ReloadOutlined />} htmlType="submit" style={{marginLeft:'20px'}} onClick={()=>confirmredo()}>重做</Button>
            </Col>
          </Row>
          <div className="table_box">
            <Table loading={loading}
              pagination={{
                position:['bottomCenter'],
                total:pageinfo.total,
                current:pageinfo.current,
                showTotal: (total:any) => `共 ${total} 条`,
                showSizeChanger: true,
                showQuickJumper: true,
                size:'small',
                onChange: (page: number,size:any) =>{
                  setPageinfo({
                    ...pageinfo,
                    page:page,
                    pageSize:size
                  })
                  getdata({
                    ...form.getFieldsValue(),
                    page:page,
                    size:size,
                  });
                  setSelectedRowKeys([])
                },
                defaultPageSize: pageinfo.pageSize,
              }}  rowKey={record => record.id} rowSelection={rowSelection} style={{width:'100%',height:'100%'}} dataSource={dataSource} columns={columns} />
          </div>
        </div>
      </div>
      <Drawer title="任务进度" placement="right" getContainer={false} onClose={()=>setProgressdetailvisible(false)} open={progressdetailvisible}>
        <Timeline style={{marginTop:20}}>
              {
                progressdetail.map((item:any,index:number)=>{
                  return (
                    <Timeline.Item key={index}>
                      <p className="task_create_time">开始时间：{item.nodeStartTime}</p>
                      <p className="task_create_time">结束时间：{item.nodeEndTime}</p>
                      <p>{item.taskNode}</p>
                    </Timeline.Item>
                  )
                })
              }
            </Timeline>
      </Drawer>
    </div>
  )
}

export default taskprogress;
