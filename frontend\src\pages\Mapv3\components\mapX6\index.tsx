import React, { useState, useEffect, FC, useRef } from "react";
import ReactDOM from 'react-dom';
import './index.less'
import { <PERSON>raph, <PERSON>up, Model, DataUri } from '@antv/x6';
// 引入react插件
import '@antv/x6-react-shape'
import {
    But<PERSON>,
    Tooltip
  } from 'antd';
// 引入布局插件
import { GridLayout, RandomLayout, GForceLayout, ForceLayout, Force2Layout, CircularLayout, DagreLayout, DagreCompoundLayout, RadialLayout, ConcentricLayout, MDSLayout, FruchtermanLayout, FruchtermanGPULayout, GForceGPULayout, ComboForceLayout, ComboCombinedLayout, ForceAtlas2Layout, ERLayout } from '@antv/layout'
import {
    PlusOutlined,
    MinusOutlined,
  } from '@ant-design/icons';
const MapX6: FC<any> = ({ mapdata, initover, nodeClick }) => {
    const container = useRef<any>(null);
    const graph = useRef<any>(null);  //全局方法
    let contnum: number = 1;  //1到5之间  轮流取值
    let colormodel: any = {}; //颜色字典表
    // 当前显示模式
    const [showmodel, setshowmodel] = useState<number>(0);  //0 全部 1 包含  2 等价  3 后续
    const zoomdom = (code: string) => {
        if (code == 'enlarge') {
          const zoom = parseFloat(graph.current.zoom().toFixed(2));
          graph.current.zoomTo(zoom + 0.2);
        } else if (code == 'narrow') {
          const zoom = parseFloat(graph.current.zoom().toFixed(2));
          graph.current.zoomTo(zoom - 0.2);
        }
      }
    useEffect(() => {
        if (mapdata) {
            let towedges = mapdata.nodes.filter(item => item.data.isroot);
            // 获取第二层的节点
            // let towedges = mapdata.edges.filter((item: any) => item.source == mapdata.nodes.find(item => item.data.isroot).id);
            towedges.forEach((item: any) => {
                // 只有5个图标来随机 1到5之间
                if (contnum == 6) {
                    contnum = 1;
                }
                // 这里为了方便子集 取用父级的颜色 存了一个字典表
                colormodel[item.id] = contnum;
                contnum++
            })
            console.log(mapdata);

            initmap();
        }

        return () => {
            contnum = 1;
            colormodel = {};
        }
    }, [mapdata])

    const registerstyle = () => {
        try {
            // 注册返回 React 组件的函数
            Graph.registerReactComponent('react-compont', (node) => {
                // 获取前序节点
                let prenode = graph.current.getPredecessors(node, { distance: 1 });
                // 当前随机出来的图标
                let iconnum = 1;
                const data = node.getData();
                // let config:any = null;
                // if(data.type == 4){
                //     config = MapConfig.marjor;
                // }else if (data.type == 3){
                //     config = MapConfig.course;
                // }else if (data.type == 2){
                //     config = MapConfig.fenlei;
                // }else if (data.type == 1){
                //     config = MapConfig.knowledge;
                // }
                // 二级后的节点取用上级的颜色
                
                if (data.isroot || prenode.length > 0) {
                    if (colormodel[node.id]) {
                        iconnum = colormodel[node.id];
                        // 获取所有的子节点 给所有子节点加上标识
                        let nextnode = graph.current.getSuccessors(node, { deep: true });
                        nextnode.forEach((item: any) => {
                            colormodel[item.id] = iconnum;
                        })
                    }
                }

                const imgdom = () => {
                    if (Number(data.type) == 1 && data.isroot == true) {
                        return (
                            <img style={{ width: '100%', height: 'auto' }} src={require(`@/images/coursemap/v3/nodes/course${iconnum}.png`)}></img>
                        )
                    } else {
                        return (
                            <>
                                {data.type == 4 && <img style={{ width: '100%', height: 'auto' }} src={require(`@/images/coursemap/marjor${iconnum}.png`)}></img>}
                                {data.type == 3 && <img style={{ width: '100%', height: 'auto' }} src={require(`@/images/coursemap/course${iconnum}.png`)}></img>}
                                {data.type == 2 && <img style={{ width: '100%', height: 'auto' }} src={require(`@/images/coursemap/knowledge${iconnum}.png`)}></img>}
                                {data.type == 1 && <img style={{ width: '100%', height: 'auto' }} src={require(`@/images/coursemap/fenlei${iconnum}.png`)}></img>}
                            </>
                        )
                    }

                }

                return (
                    <div style={{ width: '100%', height: '100%', borderRadius: '50%', position: 'relative', display: 'flex', justifyContent: 'center', cursor: 'pointer' }} >
                        {/* className="marjor_node_img" */}
                        <div style={{ width: '100%', height: '100%' }}>
                            {
                                imgdom()
                            }
                        </div>
                        <div style={{
                            position: 'absolute',
                            width: '180px',
                            height: 'auto',
                            top: '100%',
                            textAlign: 'center',
                            whiteSpace: 'pre-wrap',
                            color: ' #000',
                            cursor: 'pointer',
                        }} className="name_view">
                            <span>{data.label}</span>
                        </div>
                    </div>
                )
            })
        } catch (error) {
            // console.log('注册组件报错！',error);
        }
    }

    const initmap = () => {
        const containerdom: any = ReactDOM.findDOMNode(container.current);
        const width = containerdom.scrollWidth;
        const height = containerdom.scrollHeight || 1000;
        if (graph.current) {
            try {
                graph.current.dispose();
                Graph.unregisterReactComponent('react-compont');
            } catch (error) {
                console.log(error);
            }
        }
        // x6 注册react 组件
        registerstyle();
        try {
            // 实例初始化
            graph.current = new Graph({
                container: containerdom,
                autoResize: true,
                width,
                height,
                panning: {
                    enabled: true,
                },
                // 滚轮放大缩小
                mousewheel: {
                    enabled: true,
                    modifiers: [], //配置快捷键 触发
                    minScale: 0.2,
                },
            });
            const gridLayout = new Force2Layout({
                type: 'gForce',
                preventOverlap: true,      // 防止节点重叠     
                factor: 1,                 //  斥力的一个系数  这个比较重要
                // maxIteration: 10000,        // 布局迭代次数
                workerEnabled: true,
                // linkDistance: 100,
                // maxSpeed:100,
                // clustering: true,
                // nodeClusterBy: 'type',
                // clusterNodeStrength: 100,         
                // tick: () => {
                //     console.log('ticking');
                //     graph.current.fromJSON(model);
                // },
                onLayoutEnd: () => {
                    console.log('force layout done');
                    graph.current.fromJSON(mapdata);
                    initover(graph.current);
                    graph.current.centerContent();

                }
            })
            gridLayout.layout(mapdata);
            // 节点点击事件
            graph.current.zoom(-0.3)
            graph.current.on('node:click', ({ e, x, y, node, view }: any) => {
                if (nodeClick) {
                    nodeClick(node)
                }
                shownode(node);
            })
            graph.current.on('edge:click', ({ e, x, y, edge, view }: any) => {
                showlinetonode(edge);
            })
            //点击画布空白区域 
            graph.current.on('blank:click', ({ e, x, y, node, view }: any) => {
                resetstyle();
            })
        } catch (error) {
            console.log(error);
        }
    }

    // 点击连线　展示２个节点的关系
    const showlinetonode = (edge: any) => {
        graph.current.getNodes().forEach((item: any) => {
            // 设置节点透明
            item.attr('foreignObject/opacity', 0.2);
        })
        graph.current.getEdges().forEach((item: any) => {
            // 设置节点透明
            item.attr('line/stroke', '#333333');
        })
        // 获取相邻节点
        let neinode = graph.current.getNeighbors(edge);
        neinode.forEach((item: any) => {
            item.attr('foreignObject/opacity', 1);
        })
        edge.attr('line/stroke', '#8A8B99');
    }

    // 筛选边
    const showedge = (type: number) => {
        resetstyle();
        graph.current.getEdges().forEach((item: any) => {
            const data = item.getData();
            // 选择全部
            if (type == 0) {
                item.attr('line/stroke', '#8A8B99');
                item.attr('line/strokeWidth', 1);
            } else {
                //1包含 2等价 3后续
                if (type == data.type) {
                    item.attr('line/stroke', '#fff');
                    // 加粗
                    item.attr('line/strokeWidth', 2);
                } else {
                    item.attr('line/stroke', '#333333');
                    item.attr('line/strokeWidth', 1);
                }
            }
        })
        setshowmodel(type);
    }

    // 重置所有节点和连线的样式
    const resetstyle = () => {
        graph.current.getNodes().forEach((item: any) => {
            // 设置节点透明
            item.attr('foreignObject/opacity', 1);
        })
        graph.current.getEdges().forEach((item: any) => {
            // 设置节点透明
            item.attr('line/stroke', '#8A8B99');
        })
    }

    // 选中节点处理事件
    const shownode = (node: any) => {
        // 设置当前边的筛选模式
        setshowmodel(0);
        // 获取相邻节点
        let neinode = graph.current.getNeighbors(node);
        graph.current.getNodes().forEach((item: any) => {
            // 设置节点透明
            item.attr('foreignObject/opacity', 0.2);
        })
        if (neinode.length) {
            neinode.forEach((item: any) => {
                item.attr('foreignObject/opacity', 1);
            })
            node.attr('foreignObject/opacity', 1);
        }
        // 获取相邻边
        let neiedge = graph.current.getConnectedEdges(node);
        graph.current.getEdges().forEach((item: any) => {
            // 设置节点透明
            item.attr('line/stroke', '#333333');
        })
        if (neiedge.length) {
            neiedge.forEach((item: any) => {
                item.attr('line/stroke', '#8A8B99');
            })
            node.attr('line/stroke', '#8A8B99');
        }
    }

    return (
        <div className="mapv3_x6_view">
            <div className="map_canvas" ref={container}></div>
            <div className="mapfunction">
                <Tooltip title="放大" placement="right">
                    <Button style={{backgroundColor:'#2D3847',border:'0'}} icon={<PlusOutlined style={{color:'#B3B3B3'}} />} onClick={() => zoomdom('enlarge')} />
                </Tooltip>
                <Tooltip title="缩小" placement="right">
                    <Button style={{backgroundColor:'#2D3847',border:'0',marginTop: '20px'}} icon={<MinusOutlined style={{color:'#B3B3B3'}} />} title="缩小" onClick={() => zoomdom('narrow')} />
                </Tooltip>
            </div>
        </div>
    )
}

export default MapX6;