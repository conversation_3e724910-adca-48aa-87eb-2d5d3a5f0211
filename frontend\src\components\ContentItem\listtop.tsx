import React, { useState } from 'react';
import './index.less';
import { formatMessage, IPermission, useIntl, useSelector } from 'umi';
import globalParams from '@/permission/globalParams';

interface ContentData {
  columns: string[];
  verify?: boolean; //我的审核
  myVideo?: boolean; //我的审核
  classifiedByschedule: boolean // 是否按课表
}
const ListTop: React.FC<ContentData> = ({ columns, verify, myVideo, children, classifiedByschedule }) => {
  console.log(columns, 'columns')
  const intl = useIntl();
  // 权限
  const { rmanGlobalParameter } = useSelector<{ permission: any }, IPermission>(
    ({ permission }) => permission,
  );
  const configs: any = useSelector<{ config: any }>(({ config }) => config);

  return (
    <div className="pc_show" style={{ width: '100%' }}>
      <div className="list_content list_contenttop">
        <div className="item checkbox"></div>
        {verify ? (
          <>
            {columns.includes('taskName') && (
              <div
                className={
                  columns.length > 7 ? 'overFlowStyle item' : 'contenttitle item'
                }
              >
                审核任务
              </div>
            )}
            {columns.includes('resourceName') && (
              <div className={columns.length > 7 ? 'overFlowStyle item' : 'contenttitle item'}>
                名称
              </div>
            )}
            {columns.includes('sensitive') && (
              <div className="item width1">是否包含敏感内容</div>
            )}
            {columns.includes('instanceType') && (
              <div className="item width1">审核类型</div>
            )}
            {columns.includes('remarks') && (
              <div className="item width1">发布专题</div>
            )}
             {columns.includes('auditUserName') && (
              <div className="item width1">审核人</div>
            )}
            {columns.includes('auditDateTime') && (
              <div className="time item width1">审核时间</div>
            )}
            {columns.includes('auditState') && (
              <div className="item width1">审核状态</div>
            )}
            {columns.includes('createUserName') && (
              <div className="item width1">发起人</div>
            )}
            {columns.includes('applyUserName') && (
              <div className="item width1">发布人</div>
            )}
            {columns.includes('auditCreator') && (
              <div className="item width1">审核人</div>
            )}
            {columns.includes('filesize') &&<div className='size item width1'>大小</div>}
            {(columns.includes('createdDate')) || columns.includes('createTime') && (
              <div className="c_time item width1">发起时间</div>
            )}
            {columns.includes('createTime') && (
              <div className="a_time item width1">发起时间</div>
            )}
            {columns.includes('auditTime') && (
              <div className="a_time item width1">审核时间</div>
            )}
            {columns.includes('progress') && (
              <div className="item width1">审核进度</div>
            )}
          </>
        ) : myVideo ? (
            classifiedByschedule ?  <>
              {columns.includes('course_name') && <div className='contenttitle item'>标题</div>}
              {columns.includes('resource') && <div className='resource item width1'>资源数</div>}
              {columns.includes('teacher_name') && <div className='teacher_name item width1' style={{width: columns.includes('week')?'':'20%'}}>{columns.includes('week')?'上传人':'教师'}</div>}
              {columns.includes('week') && <div className='week item width1'>周次</div>}
              {columns.includes('week_day') && <div className='week_day item width1'>星期节次</div>}
              {columns.includes('seat') && <div className='item width1' style={{width: '150px'}}>机位</div>}
              {columns.includes('duration') && <div className='duration item width1'>时长</div>}
              {columns.includes('filesize') &&<div className='size item width1'>大小</div>}
              {columns.includes('createDate_') &&<div className='time item width1'>上传时间</div>}
              {columns.includes('area_name') &&<div className="area_name item width1">教室</div>}
              {columns.includes('semester') &&<div className="semester item width1">学期</div>}
            </>  : <>
              {columns.includes('name') &&<div className={columns.length > 7 ? 'overFlowStyle item' : 'contenttitle item'}>素材名</div>}
              {columns.includes('fileext') &&<div className="extension item width1">扩展名</div>}
              {columns.includes('filesize') &&<div className="size item width1">大小</div>}
              {columns.includes('importuser') &&<div className="people item width1">上传人</div>}
              {columns.includes('createDate') &&<div className="time width1">上传时间</div>}
              {columns.includes('source') &&<div className="people item width1">来源</div>}
              {/* {columns.includes('intelliState') &&<div className="semester item width1">是否有知识点</div>} */}
              {columns.includes('duration') &&<div className="duration item width1">播放时长</div>}
              {/* {columns.includes('usestatus') &&<div className="semester item width1">使用状态</div>} */}
              {columns.includes('semester') &&<div className="semester item width1">学期</div>}
              {columns.includes('seat') &&<div className="seat item width1">机位</div>}
            </>
          )
        : <>
            <div
              className={
                columns.length > 7 ? 'overFlowStyle item' : 'contenttitle item'
              }
            >
              {intl.formatMessage({
                id: 'material_name',
                defaultMessage: '素材名',
              })}
            </div>
            {columns.includes('userName') && (
              <div className="type item width1">分享人</div>
            )}
            {columns.includes('shareUserName') && (
              <div className="type item width1">分享给</div>
            )}
            {columns.includes('readPeople') && (
              <div className="type item width1">查看次数</div>
            )}
            {columns.includes('validity') && (
              <div className="type item width1">有效期</div>
            )}
            {columns.includes('extension') && (
              <div className="extension item width1">
                {intl.formatMessage({
                  id: 'extension',
                  defaultMessage: '扩展名',
                })}
              </div>
            )}
            {columns.includes('cataloging_status') && (
              <div className="size item width1">
                {intl.formatMessage({
                  id: 'cataloging_status',
                  defaultMessage: '编目状态',
                })}
              </div>
            )}
            {columns.includes('size') && (
              <div className="size item width1">
                {intl.formatMessage({
                  id: 'size',
                  defaultMessage: '大小',
                })}
              </div>
            )}
            {columns.includes('directory') && (
              <div className="people item width1">
                {intl.formatMessage({
                  id: 'directory',
                  defaultMessage: '所在目录',
                })}
              </div>
            )}
            {columns.includes('teacher') && (
              <div className="people item width1">
                {intl.formatMessage({
                  id: 'teacher',
                  defaultMessage: '授课教师',
                })}
              </div>
            )}
            {columns.includes('deleteUser_') && (
              <div className="people item width1">删除人</div>
            )}
            {columns.includes('deleteTime_') && (
              <div className="time item width1">删除时间</div>
            )}
            {columns.includes('source') && (
              <div className="people item width1">
                {intl.formatMessage({
                  id: 'device_source',
                  defaultMessage: '来源',
                })}
              </div>
            )}
            {columns.includes('person') && (
              <div className="people item width1">
                {intl.formatMessage({
                  id: 'warehousing_person',
                  defaultMessage: '上传人',
                })}
              </div>
            )}
            {columns.includes('semester') && (
              <div className="time item">学期</div>
            )}
            {columns.includes('time') && (
              <div className="time item">
                {intl.formatMessage({
                  id: 'storage_time',
                  defaultMessage: '上传时间',
                })}
              </div>
            )}
            {columns.includes('smarttag') &&
              rmanGlobalParameter.includes(globalParams.smart_tag_display) && (
                <div className="type item width1">智能标签</div>
              )}
            {columns.includes('voice') &&
              rmanGlobalParameter.includes(
                globalParams.speech_analysis_display,
              ) && <div className="type item width1">语音分析</div>}
            {columns.includes('intelliState') &&
              rmanGlobalParameter.includes(
                globalParams.knowledge_analysis_display,
              ) && <div className="type item width1">是否有知识点</div>}
            {columns.includes('duration') && (
              <div className="type item width1">播放时长</div>
            )}
            {columns.includes('status') && (
              <div className="type item width1">使用状态</div>
            )}
            {columns.includes('deleteUserName_') && (
              <div className="deleteUserName_ item width1">删除用户</div>
            )}
            {columns.includes('shared_state') && (
              <div className="type item width1">共享状态</div>
            )}
            {columns.includes('type') && (
              <div className="type item width1">
                {intl.formatMessage({
                  id: 'type',
                  defaultMessage: '类型',
                })}
              </div>
            )}
            {columns.includes('shareTime') && (
              <div className="time item width1">分享时间</div>
            )}
            {columns.includes('publishUser') && (
              <div className="publishUser item width1">发布人</div>
            )}
             {columns.includes('classificationNames') && (
              <div className="item width1">发布专题</div>
            )}
            {columns.includes('publishTime') && (
              <div className="time item width1">发布时间</div>
            )}
            {columns.includes('auditUserName') && (
              <div className="auditUserName item width1">下架人</div>
            )}
            {columns.includes('auditDateTime') && (
              <div className="time item width1">下架时间</div>
            )}
          </>
        }
        {children}
      </div>
    </div>
  );
};

export default ListTop;
