import React, {
  useState,
  useEffect,
  KeyboardEvent,
  FocusEvent,
  useRef,
} from 'react';
import {
  Modal,
  Input,
  Button,
  Tree,
  message,
  Form,
  Spin,
  Progress,
} from 'antd';
import {
  useIntl,
  useSelector,
  useHistory,
  IPermission,
} from 'umi';
import copyandmoveApis from '@/service/copyandmoveApis';
import contentListApis from '@/service/contentListApis';
import deleteApis from '@/service/deleteApis';
import copyandmoveTypes from '@/types/copyandmoveTypes';
import searchTypes from '@/types/searchTypes';
import './index.less';
import perCfg from '@/permission/config';
import globalParams from '@/permission/globalParams';

const { DirectoryTree } = Tree;

interface CreateModalProps {
  modalVisible: boolean;
  isShare?: boolean;
  className_?: string;
  modalClose: () => void;
  refresh?: () => void; // 刷新
  callback?: (data: any) => void; // 计数回显
  // copyAndMovelist: searchTypes.ISearchData[];
  copyAndMovelist: any[];
  operatType: number; //0 复制 1 移动 2 共享
  pubOrPri: boolean;
  // currentPath?: any;
}

interface ITree {
  title: string;
  key: string;
  id: string;
  children: ITree[];
}

const _disableIds = ['global_sobey_defaultclass/public'];
const CopyAndMoveModal: React.FC<CreateModalProps> = props => {
  const intl = useIntl();
  const {
    modalClose,
    modalVisible,
    isShare,
    className_,
    refresh,
    callback,
    copyAndMovelist,
    operatType,
    pubOrPri,
    // currentPath
  } = props;
  const enum_: any = [intl.formatMessage({ id: '复制' }), intl.formatMessage({ id: '移动' }), intl.formatMessage({ id: '共享' })];
  const { mobileFlag } = useSelector<{ config: any }, any>(
    ({ config }) => config,
  );
  let history: any = useHistory();
  let showVideoResouce = history.location?.query?.showVideoResouce || ''
  const [expandedKeys, setExpandedKeys] = useState<React.ReactText[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<React.ReactText[]>([]);
  const [selectedDetail, setSelectedDetail] = useState<
    copyandmoveTypes.Imovedetail
  >();
  const [inputVisible, setInputVisible] = useState(false);
  const [treeData, setTreeData] = useState<ITree[]>([]);
  const [onNew, setOnNew] = useState<boolean>(false);
  const changenameRef = useRef<Input | null>(null);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  // const [upLoadClicktag, setUpLoadClicktag] = useState(true);
  const { permissions, rmanGlobalParameter } = useSelector<
    { permission: any },
    IPermission
  >(({ permission }) => permission);
  let upLoadClicktag = true;
  const [permission, setPermission] = useState<boolean>(false);
  const [processVisible, setProcessVisible] = useState<boolean>(false);
  const [process, setProcess] = useState<any>({});
  // 获取当前用户信息
  useEffect(() => {
    if ((window as any).login_useInfo) {
      // console.log('myVideo',props.myVideo)
      // 判断当前用户是否有删除的权限
      const userInfo = (window as any).login_useInfo;
      if (userInfo) {
        const roless = userInfo.roles?.map((item: any) => item.roleCode);
        if (
          (roless?.includes('admin_S1') ||
          roless?.includes('r_sys_manager') ||
          roless?.includes('r_resource_manager')) || showVideoResouce === 'true'
        ) {
          setPermission(true);
        } else {
          setPermission(false);
        }
      }
    }
  }, [(window as any).login_useInfo]);
  useEffect(() => {
    if (modalVisible) {
      form.resetFields();
      setExpandedKeys([]);
      setSelectedKeys([]);
      setSelectedDetail(undefined);
      setOnNew(false);
      searchpublic();
      console.log(permissions, 'zzzzz');
    }
  }, [modalVisible]);
  useEffect(() => {
  }, [isShare]);
  const searchpublic = async () => {
    setLoading(true);
    copyandmoveApis.movetree(2).then((res: any) => {
      setLoading(false);
      upLoadClicktag = true;
      if (res && res.data && res.success) {
        let data = res.data;
        if (!permission) {
          // console.log('copy',copyAndMovelist)
          removeTreeListItem(data, '录播资源');
        } else {
          console.log('move', copyAndMovelist);
        }
        let newData: any = [];
        data.map((item: any) => {
          if (item.name === '公共资源') {
            item.children?.forEach((item: any) => {
              newData.push({ ...item, layer: 1 });
            });
          } else {
            newData.push(item);
          }
        });
        newData = newData.filter(Boolean); //过滤空对象
        const rootData = newData.map((item: any) => {
          return {
            key: item.path,
            title: item.name,
            id: item.id,
            // disabled: item.name==='群组资源'?true:false
          };
        });
        if (
          !rmanGlobalParameter.includes(globalParams.share_folder_display) ||
          !permissions.includes(perCfg.share_folder_permission)
        ) {
          rootData.forEach((item: any, index: number) => {
            if (item.title === '共享资源') {
              rootData.splice(index, 1);
            }
          });
        }
        if (
          !rmanGlobalParameter.includes(globalParams.folder_group_display) ||
          !permissions.includes(perCfg.folder_group_permission)
        ) {
          rootData.forEach((item: any, index: number) => {
            if (item.title === '群组资源') {
              rootData.splice(index, 1);
            }
          });
        }
        //共享只能到共享资源目录和群组资源目录
        if (operatType == 2) {
          let shareTree: any = [];
          rootData.forEach((item: any, index: number) => {
            if (['共享资源', '群组资源'].includes(item.title)) {
              shareTree.push(item);
            }
          });
          setTreeData(shareTree);
        } else {
          if (
            !rmanGlobalParameter.includes(
              globalParams.vedio_resource_display,
            ) ||
            !permissions.includes(perCfg.vedio_resource_permission)
          ) {
            rootData.forEach((item: any, index: number) => {
              if (item.title === '录播资源') {
                rootData.splice(index, 1);
              }
            });
          }
          console.log('newData', rootData);
          setTreeData(rootData);
        }
      }
    });
  };
  const onLoadChild = (node: any, isRoot: boolean = false) => {
    const { key, children, code, title } = node;
    return new Promise(async resolve => {
      if (key === 'add') {
        resolve(null);
        return;
      }
      if (children) {
        resolve(null);
        return;
      }
      function updateTreeData(list: any, key: React.Key, children: any): any {
        return list.map((node: any) => {
          if (node.key === key) {
            return {
              ...node,
              children,
            };
          } else if (node.children) {
            return {
              ...node,
              children: updateTreeData(node.children, key, children),
            };
          }
          return node;
        });
      }
      const res: any = await copyandmoveApis.movetreeChildern(key, !permission);
      if (res && res.data && res.success) {
        let treeList = res.data;
        treeList = treeList.filter((item: any) => {
          return item.name !== '录播资源';
        });
        setTreeData(origin =>
          updateTreeData(
            origin,
            key,
            treeList.map((item: any) => {
              return {
                key: item.path,
                title: item.name,
                id: item.contentId,
              };
            }),
          ),
        );
        resolve(null);
      }
    });
  };
  const removeTreeListItem = (treeList: any, name: string) => {
    // 根据id属性从数组（树结构）中移除元素
    if (!treeList || !treeList.length) {
      return;
    }
    for (let i = 0; i < treeList.length; i++) {
      if (treeList[i].name === name) {
        treeList.splice(i, 1);
        break;
      }
      removeTreeListItem(treeList[i].children, name);
    }
  };
  const onSelect = (keys: React.ReactText[], { node }: any) => {
    setSelectedDetail({
      key: node.key,
      title: node.title,
      id: node.id,
    });
    setSelectedKeys(keys);
    if (_disableIds.includes(node.id) && !inputVisible) {
      setOnNew(true);
    } else if (!inputVisible) {
      setOnNew(false);
    }
  };
  const onExpand = (keys: React.ReactText[]) => {
    setExpandedKeys(keys);
  };
  // --------新建文件夹
  const newfolder = () => {
    if (!selectedDetail) {
      message.info(intl.formatMessage({ id: '请选择文件夹' }));
      return;
    }
    setExpandedKeys(origin =>
      origin.includes(selectedDetail.key)
        ? origin
        : [...origin, selectedDetail.key],
    );
    let list = forTree(treeData);
    setTreeData(list);
    setInputVisible(true);
    setOnNew(true);
  };
  useEffect(() => {
    if (inputVisible) {
      setTimeout(() => {
        changenameRef.current?.focus();
      }, 200);
    }
  }, [treeData]);
  const changeName = async (
    e: KeyboardEvent<HTMLInputElement> | FocusEvent<HTMLInputElement> | any,
  ) => {
    if (upLoadClicktag) {
      upLoadClicktag = false;
      if (!selectedDetail) {
        message.info(intl.formatMessage({ id: '请选择文件夹' }));
        upLoadClicktag = true;
        return;
      }
      e.preventDefault();
      let name = e.target.value;
      if (name === '') {
        message.error(intl.formatMessage({ id: '文件或文件夹姓名不能为空' }));
        
        upLoadClicktag = true;
        return;
      }
      let re = /^[^#\x22]*$/;
      if (!re.test(name)) {
        message.error(intl.formatMessage({ id: '文件夹姓名中不能包含' }));
        upLoadClicktag = true;
        return;
      }
      if (selectedDetail.key.indexOf('public') > -1) {
        await contentListApis[selectedDetail.key.indexOf('共享资源')> -1? 'newsharefolder':'newpublicfolder']({
            users: [],
            name: name,
            parentId: selectedDetail.id,
            privilege: 'public',
          })
          .then(async res => {
            if (res && res.success && res.data) {
              message.success(intl.formatMessage({ id: '新建文件夹成功' }));
              setSelectedKeys([res.data.contentId]);
              setSelectedDetail({
                key: res.data.contentId,
                title: res.data.name,
                id: res.data.path,
              });
            } else {
              message.error(intl.formatMessage({ id: '新建文件夹失败' }));
            }
            setOnNew(false);
          });
      } else {
        await contentListApis
          .newprivatefolder({
            users: [],
            name: name,
            parentId: selectedDetail.id,
            privilege: 'private',
          })
          .then(async res => {
            if (res && res.success && res.data) {
              message.success(intl.formatMessage({ id: '新建文件夹成功' }));
              setSelectedKeys([res.data.contentId]);
              setSelectedDetail({
                key: res.data.path,
                title: res.data.name,
                id: res.data.contentId,
              });
            } else {
              message.error(intl.formatMessage({ id: '新建文件夹失败' }));
            }
            setOnNew(false);
          });
      }
      onLoadChild(selectedDetail);
    }
  };
  const forTree = (tree: any) => {
    return tree.map((item: ITree) => {
      if (item.key === selectedKeys[0]) {
        let data = [];
        if (item.children) {
          data = [
            ...item.children,
            {
              title: (
                <Input
                  defaultValue={intl.formatMessage({ id: '新建文件夹' })}
                  onPressEnter={changeName}
                  onBlur={changeName}
                  ref={changenameRef}
                  autoComplete={'off'}
                />
              ),
              key: 'add',
              id: 'new',
            },
          ];
        } else {
          data = [
            {
              title: (
                <Input
                  defaultValue={intl.formatMessage({ id: '新建文件夹' })}
                  onPressEnter={changeName}
                  onBlur={changeName}
                  ref={changenameRef}
                  autoComplete={'off'}
                />
              ),
              key: 'add',
              id: 'new',
            },
          ];
        }
        return {
          ...item,
          children: data,
        };
      } else if (item.children) {
        return {
          ...item,
          children: forTree(item.children),
        };
      }
      return item;
    });
  };
  // --------新建文件夹end
  const modalOk = () => {
    if (!selectedDetail) {
      message.info(intl.formatMessage({ id: '请选择文件夹' }));
      return;
    }
    if (selectedDetail.title === '公共资源') {
      message.info(intl.formatMessage({ id: '不能文件或文件夹到根目录'},{type: enum_[operatType]  }));
      return;
    }
    if (operatType == 0) {
      // 复制
      let datd: any = {
        contentId: copyAndMovelist[0].contentId_,
        copyEntity: {
          tree: [selectedDetail.key],
          type: copyAndMovelist[0].type_,
        },
      };
      if (form.getFieldsValue()[copyAndMovelist[0].contentId_]) {
        datd.name = form.getFieldsValue()[copyAndMovelist[0].contentId_];
      }
      copyandmoveApis.entityCopy(datd, isShare).then(async res => {
        if (res && res.success) {
          message.success(intl.formatMessage({ id: '复制成功' }));
          refresh?.();
          callback?.(res.data);
          modalClose();
        } else {
          message.error(intl.formatMessage({ id: '复制失败' }));
          refresh?.();
          modalClose();
        }
      });
    } else if (operatType == 1) {
      // 移动
      let idlist: Array<string> = [];
      copyAndMovelist.forEach((item: searchTypes.ISearchData) => {
        idlist.push(item.contentId_);
      });

      copyandmoveApis
        .entityMove({
          checkPermission: false,
          paramType: 'RESOURCEID',
          source: idlist,
          target: selectedDetail.id,
          tryMerge: true,
        })
        .then(async res => {
          if (res && res.success && res.data ) {
            if (!res.data.process_id) {
              if (res?.data?.failed_list[0]?.msg) {
                message.error(res.data.failed_list[0]?.msg)
              }
              return 
            }
            setProcessVisible(true);
            let detint = setInterval(() => {
              deleteApis.deleteResult(res.data.process_id).then(resdata => {
                if (resdata && resdata.success && resdata.data) {
                  setProcess(resdata.data);
                  if (resdata.data.status === 'FINISH') {
                    clearInterval(detint);
                    resdata.data.failed_count === 0
                      ? message.success(intl.formatMessage({ id: '移动成功' }))
                      : message.error(intl.formatMessage({ id: '移动失败' }));
                    refresh?.();
                    modalClose();
                  }
                } else {
                  clearInterval(detint);
                  message.error(intl.formatMessage({ id: '移动失败' }))
                  refresh?.();
                  modalClose();
                }
              });
            }, 500);
          } else {
            message.error(intl.formatMessage({ id: '移动失败' }))
            refresh?.();
            modalClose();
          }
        });
    } else {
      //共享
      let shareResourceDetails: any = [];
      copyAndMovelist.forEach((item: searchTypes.ISearchData) => {
        shareResourceDetails.push({
          resourceId: item.contentId_,
          resourceName: item.name_,
          keyframe: item.keyframe_,
        });
      });
      console.log(selectedDetail, shareResourceDetails);
      copyandmoveApis
        .entityShare({
          shareResourceDetails,
          tree: [selectedDetail.key],
        })
        .then((res: any) => {
          if (res && res.success && res.data) {
            message.success(intl.formatMessage({ id: '共享请求已发起' }))
            modalClose();
          } else {
            message.error(intl.formatMessage({ id: '共享发起失败' }))
          }
        });
    }
  };
  return (
    <>
      <Modal
        destroyOnClose
        title={enum_[operatType] + '到'}
        className={className_ || ''}
        open={modalVisible}
        closable={false}
        width={400}
        footer={[
          <Button key="submit" type="primary" onClick={modalOk}>
            {intl.formatMessage({ id: '确定' })}
          </Button>,
          <Button key="back" onClick={modalClose}>
            {intl.formatMessage({ id: '取消' })}
          </Button>,
        ]}
      >
        <div className="copy-move-modal">
          <Form name="basic" form={form}>
            {(operatType == 0 || operatType == 2) && (
              <Button
                key="new"
                type="primary"
                onClick={newfolder}
                className="newfolderbtn"
                disabled={
                  onNew || !permissions.includes(perCfg.resource_create)
                }
              >
                {intl.formatMessage({ id: '新建文件夹' })}
              </Button>
            )}
            {operatType == 0 &&
              //批量复制后台暂时不支持
              copyAndMovelist.map(
                (item: searchTypes.ISearchData, index: number) => (
                  <Form.Item
                    className="rename"
                    key={item?.contentId_}
                    name={item?.contentId_}
                    // label={index + 1}
                    label={intl.formatMessage({ id: '重命名' })}
                  >
                    <Input />
                  </Form.Item>
                ),
              )}
          </Form>
          <div className="movetreebox">
            {loading ? (
              <Spin style={{ marginTop: 230 }} />
            ) : (
              <DirectoryTree
                expandedKeys={expandedKeys}
                selectedKeys={selectedKeys}
                onSelect={onSelect}
                onExpand={onExpand}
                loadData={onLoadChild as any}
                treeData={treeData}
              />
            )}
          </div>
        </div>
      </Modal>
      <Modal
        title={intl.formatMessage({ id: '移动进度' })}
        open={processVisible}
        className="moveProcessModal"
        onCancel={() => {
          setProcessVisible(false);
          refresh?.();
          modalClose();
        }}
        footer={[]}
      >
        <Progress
          percent={Number(
            ((process.success_count * 100) / process.total_count).toFixed(1) ||
              0,
          )}
        />
        <div className="result">
          <span>{intl.formatMessage({ id: '总个数' })}：{process.total_count || 0}</span>
          <span>{intl.formatMessage({ id: '成功数' })}：{process.success_count || 0}</span>
          <span>{intl.formatMessage({ id: '失败数' })}：{process.failed_count || 0}</span>
        </div>
      </Modal>
    </>
  );
};

export default CopyAndMoveModal;
