import http from '../http/http';
import loginTypes from '@/types/loginTypes';

namespace loginApis {
  export const login = (data: loginTypes.ILoginReq) => {
    return http<boolean>(
      `/account/login?loginName=${data.loginName}&password=${data.password}`,
    );
  };
  export const userinfo = () => {
    return http<boolean>(`/account/login-userinfo/S1`);
  };
  export const loginout = () => {
    return http<boolean>(`/account/signout`);
  };

  export const fetchUserInfo = () => http(`/unifiedplatform/v1/user/current`);
  
  /**
   * 获取用户权限
   */
  export const fetchUserPermissions = () =>
    http(`/unifiedplatform/v1/user/roleapp`);
  /**
   * 获取用户权限
   */
  export const fetchUserPermissionsV2 = () =>
    http(`/unifiedplatform/v1/user/rolemodule`);
  /**
   * 获取 资源管理 模块的全局参数
   */
  export const fetchRmanGlobalParam = () =>
    http(`/unifiedplatform/v1/app/app/module/parameter/list?moduleCode=zyzx`);
  /**
   * 获取系统设置
   */
  /**
   * 获取全局参数配置
   */
   export const fetchParameterConfigs = () => {
      return http(`/unifiedplatform/v1/app/app/module/parameter/list`, {
        method: 'post',
        data: ['mooc_kcgl', 'spoc_kcgl', 'wdkc', 'microcourse_kcgl', 'classreview_kcgl', 'LearningPortal', 'LearningPortalCourse'],
      });
    };
  export const fetchSysSetting = () =>
    http<API.OsResponse<loginTypes.ISetting>>(
      `/unifiedplatform/v1/setting/no/authorization`,
    );
  /**
   * 获取安全策略配置
   */
  export const fetchSafeConfig = async () =>{
    // 安全配置放在这初始化 避免多次调用
    const res = await fetch('safeMethodConfig.json');
    const result = await res.json();
    return result;
  }
  export const fetchHeaderList = () =>
    http(`/unifiedplatform/v1/navigationsettings/user/navigation?type=2`)
  /**
   * 获取平台名字【系统】
   *
   */
  export const fetchPlatform = () =>
    http(`/unifiedplatform/v1/app/projectname`)
  
  export function reqMessageList(data: any) {
    return http(`/unifiedplatform/v1/message/notify/self/list`,{
      method: 'GET',
      params: data
    });
  }
  
  export function reqUnreadMessageCount() {
    return http(`/unifiedplatform/v1/message/notify/unread/count`,{
      method: 'GET',
    });
  }
  
  export function updateMessageRead(data: any) {
    return http(`/unifiedplatform/v1/message/read`,{
      method: 'GET',
      params: data
    });
  }
  /**
   * 获取平台对公域名
   */
  export const fetchPlatformDomain = () => {
    return http(`/unifiedplatform/v1/app/app/module/parameter/list`, {
      method: 'GET',
      params:{
        moduleCode:'workbench'
      },
    });
  };
}

export default loginApis;
