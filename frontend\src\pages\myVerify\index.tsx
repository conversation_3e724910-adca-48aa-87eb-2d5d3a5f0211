import React, { FC, useState, useEffect, useRef } from 'react';
import {
  Form,
  Input,
  Button,
  Tooltip,
  Modal,
  message,
  Tabs,
  Row,
  Col,
  Breadcrumb,
  Empty,
  Pagination,
  Select,
  Checkbox,
  Drawer,
  Segmented,
  Radio 
} from 'antd';
import { Store } from 'antd/lib/form/interface';
import { RouteComponentProps } from 'react-router-dom';
import './index.less';
import { history, useDispatch, useSelector, useParams, Idownlist, useIntl } from 'umi';
import loginApis from '@/service/loginApis';
import Loading from '@/components/loading/loading';
import { Base64 } from 'js-base64';
import Header from '@/components/header';
import contentListApis from '@/service/contentListApis';
import { IPermission } from '@/models/permission';
import { getSensitiveWord } from '@/utils';
import globalParams from '@/permission/globalParams';
import {
  BreadCrumb,
  ISearchlist,
  MaterialList,
  MenuItem,
} from '../contentlibrary/contentList/type';
import { ContentItem, DownloadModal, IconFont, ListTop } from '@/components';


import ColumnSelect, { allColumns } from '@/components/ColumnSelect';
import { timeTransfer } from '@/utils';
const { TextArea } = Input;
import {
  CheckCircleOutlined,
  RedoOutlined,
  SearchOutlined,
  UndoOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons';
import AuditService from '@/service/audit';
import perCfg from '@/permission/config';
interface resourceVerifyProps { 
  setCatalogueVisible: (bool: any)=> void
}
const ResourceVerify: FC<resourceVerifyProps> = props => {
  // const [activeTabs, setActiveTabs] = useState<string>('verifyToMe');
  const sortby = useRef<boolean>(true);
  const { permissions, rmanGlobalParameter, publish_review_display } = useSelector<
  { permission: any },
  IPermission
  >(({ permission }) => permission);
  const { mobileFlag } = useSelector<{ config: any }, any>(
    ({ config }) => config
  )
  const [form] = Form.useForm();
  const activeTabs = useRef<string>(permissions.includes(perCfg.audit_to_me) ?'verifyToMe' : 'myVerify');
  
  // const verifyType = useRef<string>((rmanGlobalParameter.includes(globalParams.audit_import) && permissions.includes(perCfg.import_review)) ? 'inventoryVerify' : (rmanGlobalParameter.includes(globalParams.audit_shared) && permissions.includes(perCfg.share_review))? 'shareVerify': 'publishVerify');
  const verifyType = useRef<string>('inventoryVerify');
  const [queryOut, setQueryOut] = useState<any>({
    instanceName: '',
    // instanceType:''
    status: '',
    page: 1,
    size: 10,
  }); //外部分页参数
  const [queryIn, setQueryIn] = useState<any>({
    instanceId: '',
    resourceName: '',
    status: '',
    page: 1,
    size: 10,
  }); //内部分页参数
  const [current, setCurrent] = useState<number>(1); //当前页码
  const [pageSize, setPageSize] = useState<number>(30); //每页条数
  const [innerItem, setInnerItem] = useState<any>({}); 
  const [collation, setCollation] = useState<string>('createDate_,down'); //排序
  const [innerFlag, setInnerFlag] = useState<boolean>(['inventoryVerify','publishVerify', 'share_review'].includes(verifyType.current)); //点进了审核任务详情
  const [status, setStatus] = useState<string>(''); //点进了审核任务详情
  const currentTask = useRef<any>(null);
  const [totalPage, setTotalPage] = useState<number>(0); //素材总数
  const [innerTotalPage, setInnerTotalPage] = useState<number>(0); //内部素材总数
  // 驳回审核弹窗
  const [rejectisModalOpen, setRejectisModalOpen] = useState<boolean>(false);
  // 驳回理由
  const [rejectisReason, setRejectisReason] = useState<string>('');
  const [operateType, setOperateType] = useState<any>(null)
  const [currentList, setCurrentList] = useState<any>([])
  const [itemFalg, setItemFalg] = useState<boolean>(false)
  const column_1 = {
    out: [
      'taskName',
      'instanceType',
      'createUserName',
      'createdDate',
      'auditTime',
      'progress',
    ],
    in: verifyType.current === 'inventoryVerify'? ['sensitive', 'resourceName', 'instanceType', 'createUserName', 'createTime',  'progress']: ['resourceName', 'auditState', 'auditCreator', 'auditTime']
  };
  const column_2 = {
    out: [
      'taskName',
      'instanceType',
      'createUserName',
      'createdDate',
      'progress',
    ],
    in: verifyType.current === 'inventoryVerify'? ['sensitive', 'resourceName', 'instanceType', 'createUserName' , 'createTime',  'progress']: [
      'resourceName',
      'instanceType',
      'createUserName',
      'createdDate',
      'auditState',
      'auditCreator',
      'auditTime',
    ]
  };
  const column_3 = {
    verifyToMe:  [
      'resourceName',
      'auditState',
      'filesize',
      'applyUserName',
      'createdDate'
    ],
    myVerify:  [
      'resourceName',
      'auditState',
      'auditCreator',
      'auditTime'
    ]
  };
  const column_4 = {
    verifyToMe:  [
      'resourceName',
      'remarks', // 发布专题
      'auditState', // 状态
      'applyUserName', // 发布人
      'filesize', // 大小
      'createdDate' // 发起时间
    ],
    myVerify:  [
      'resourceName',
      'remarks', // 发布专题
      'auditUserName', // 审核人
      'auditDateTime', // 审核时间
      'auditState', // 状态
    ]
  };
  const column_5 = {
    verifyToMe:  [
      'resourceName',
      'auditState', // 状态
      'applyUserName', // 发布人
      'filesize', // 大小
      'createdDate' // 发起时间
    ],
    myVerify:  [
      'resourceName',
      'auditUserName', // 审核人
      'auditDateTime', // 审核时间
      'auditState', // 状态
    ]
  };
  const [indeterminate, setIndeterminate] = useState<boolean>(false);
  const [checkAll, setCheckAll] = useState<boolean>(false);
  const [checkedList, setCheckedList] = useState<Array<any>>([]); //选中的列表
  const [allList, setAllList] = useState<Array<any>>([]); //外面总的的列表
  const [innerList, setInnerList] = useState<Array<any>>([]); //内部总的的列表
  const CheckboxGroup = Checkbox.Group;
  const [copyShow, setCopyShow] = useState<boolean>(false);
  const [moreSelectDrawerOpen, setMoreSelectDrawerOpen] = useState(false)
  const [segment, setSegment] = useState<string | number>('资源发布审核')
  const dispatch = useDispatch();
  const intl = useIntl();
  useEffect(() => {
    formChange();
  }, [current, pageSize, innerFlag, verifyType.current, activeTabs.current, segment ]);
  const { Option } = Select;
  // 页码切换
  const changepage = (page: number, size: any) => {
    setCurrent(page);
    setPageSize(size || 0);
  };
  const detail = (item: any, type: number) => {
    //已经在内部 则直接页面跳转
    if (innerFlag) {
      let flag = activeTabs.current == 'verifyToMe'
      const instanceId = verifyType.current === 'publishVerify'? item.id: (verifyType.current === 'shareVerify' ? item.id : item.instanceId)
      const processInstanceId = verifyType.current === 'publishVerify'?item.flowInfo?.processInstance?.id: ''
      const flowId =( verifyType.current === 'publishVerify' ||  verifyType.current === 'shareVerify') ? item.flowInfo?.id : ''
      window.open(`#/basic/rmanDetail/${item.resourceId}?isaudit=true&myVerify=${verifyType.current === 'shareVerify'}& ${verifyType.current === 'publishVerify'?'isPublish=true': ''}&${flag?'showBtn=true&' : ''}instanceId=${instanceId}&processInstanceId=${processInstanceId}&flowId=${flowId}&page=${current}&size=${pageSize}`);
    } else {
      setInnerFlag(true);
      currentTask.current = item;
      reset();
    }
  };
  //查询任务内部列表
  const taskList = () => {
    // if (!currentTask.current?.instanceId) return;
    const formData = form.getFieldsValue();
    if(verifyType.current === 'publishVerify'){
      let param: any = {
        page: current,
        size: pageSize,
        isDesc: sortby.current,
      };
      if (formData.resourceName) {
        param.name = formData.resourceName;
      }
      if(activeTabs.current === 'myVerify'){
        param.status = formData.status;
      }
        contentListApis[activeTabs.current === 'myVerify'?'myAuditPublishResource':'auditTomePublishResource'](param).then((res: any) => {
          if (res?.errorCode === 'success') {
            setInnerList(res.extendMessage.data);
            setCheckedList([]);
            setIndeterminate(false);
            setCheckAll(false);
            setInnerTotalPage(res.extendMessage.recordTotal);
            }
          });
    } else if (verifyType.current === 'inventoryVerify') {
      let param: any = {
        page: current,
        size: pageSize,
        isDesc: sortby.current,
        modelKey: 'resource_receipt'
      };
      if (formData.resourceName) {
        param.name = formData.resourceName;
      }
      // if(activeTabs.current === 'myVerify'){
        param.status = formData.status;
      // }
      contentListApis[activeTabs.current === 'myVerify' ? 'storeOwnerInitiate':'storeOwnerAudit'](param).then((res: any) => {
        if (res?.errorCode === 'success') {
          setInnerList(res?.extendMessage?.results);
          setCheckedList([]);
          setIndeterminate(false);
          setCheckAll(false);
          setInnerTotalPage(res.extendMessage.recordTotal);
        } else {
          message.error(res?.errorMsg)
        }
      });
    } else {
      let param: any = {
        page: current,
        size: pageSize,
        isDesc: sortby.current,
        instanceType: verifyType.current === 'inventoryVerify'? '入库': '共享'
      };
      // if(verifyType.current === 'shareVerify'){
      //   param.instanceId = currentTask.current?.instanceId
      // }
      if (formData.resourceName) {
        param.resourceName = formData.resourceName;
      }
      if (formData.status) {
        param.status = formData.status;
      }
      if(verifyType.current === 'inventoryVerify' && activeTabs.current === 'myVerify'){
        contentListApis.myAuditResource(param).then((res: any) => {
          if (res?.errorCode === 'success') {
            setInnerList(res.extendMessage.results);
            setCheckedList([]);
            setIndeterminate(false);
            setCheckAll(false);
            setInnerTotalPage(res.extendMessage.recordTotal);
          }
        });
      } else if (verifyType.current === 'shareVerify'){
        delete param.instanceType;
        param.modelKey = segment == '资源发布审核' ? 'resource_publish_teaching_resource_library' : 'resource_publish_topic'
        param.name = formData.resourceName;
        const api = activeTabs.current === 'verifyToMe' ? 'ownerAudit' : 'ownerInitiate'
        contentListApis[api](param).then((res: any) => {
          if (res?.success) {
            const data = res.data.data.map((item: any) => {
              let auditStateName = ''
              switch (item.auditState) {
                case 0:
                  auditStateName = '待审核'
                  break;
                case 1:
                  auditStateName = '审核中'
                  break;
                case 2:
                  auditStateName = '已通过'
                  break;
                case 3:
                  auditStateName = '已驳回'
                  break;
                case 4:
                  auditStateName = '撤销'
                  break;
                case 5:
                  auditStateName = '已发布'
                  break;
                case 6:
                  auditStateName = '已下架'
                  break;
                default:
                  break;
              }
              return {
                ...item,
                auditStateName
              }
            })
            setInnerList(data);
            setCheckedList([]);
            setIndeterminate(false);
            setCheckAll(false);
            setInnerTotalPage(res.data.length);
          }
        });
      } else{
        contentListApis.auditResourceList(param).then((res: any) => {
          if (res.errorCode == 'success') {
            setInnerList(res.extendMessage.results);
            setInnerTotalPage(res.extendMessage.recordTotal);
          } else {
            setInnerList([]);
          }
          setCheckedList([]);
          setCheckAll(false);
          setIndeterminate(false);
       });
      }
    }
  };
  // 全选
  const onCheckAllChange = (e: any) => {
    setCheckedList(e.target.checked ? (innerFlag ? innerList : allList) : []);
    // setSelectRows(e.target.checked ? allList : []);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  };
  // 单选
  const onChange = (check: Array<any>) => {
    setCheckedList(check);
    // setSelectRows(check);
    setIndeterminate(!!check.length && check.length < (innerFlag ? innerList : allList).length);
    setCheckAll(check.length === (innerFlag ? innerList : allList).length);
  };
  useEffect(() => {
    // 判断是否包含文件夹
    let i = checkedList.some((item: any) => {
      return item.type_ === 'folder';
    });
    // 判断是否全为视频资源
    let k = checkedList.every((item: any) => {
      return (
        item.type_ === 'biz_sobey_video' || item.type_ === 'biz_sobey_audio'
      );
    });
    // 判断是否全为视频、文档、图片 供绑定知识点使用
    let l = checkedList.every((item: any) => {
      return (
        item.type_ === 'biz_sobey_video' ||
        item.type_ === 'biz_sobey_picture' ||
        item.type_ === 'biz_sobey_audio' ||
        item.type_ === 'biz_sobey_document'
      );
    });
    setCopyShow(i);
  }, [checkedList]);

  const sort: Array<MenuItem> = [
    {
      label: 'createDate_,down',
      value: intl.formatMessage({ id: '发起时间' }),
      icon: <IconFont type="iconchangjiantou-xia" className="icon" />,
    },
    {
      label: 'createDate_,up',
      value: intl.formatMessage({ id: '发起时间' }),
      icon: <IconFont type="iconchangjiantou-shang" className="icon" />,
    },
    // {
    //   label: 'name_,down',
    //   value: '名称',
    //   icon: <IconFont type="iconchangjiantou-xia" className="icon" />,
    // },
    // {
    //   label: 'name_,up',
    //   value: '名称',
    //   icon: <IconFont type="iconchangjiantou-shang" className="icon" />,
    // },
  ];
  // 排序切换
  const handleChange = (value: string) => {
    setCollation(value);
    if (value == 'createDate_,up') {
      sortby.current = false;
    } else {
      sortby.current = true;
    }
    formChange();
    // setCurrent(1);
  };
  const formChange = () => {
    const formData = form.getFieldsValue();
    //在内部调用接口都是一致的
    // let flage = false;
    // setInnerFlag((pre:any) => {
    //   flage = pre;
    //   return pre;
    // 获取最新的 innerFlag
    if (innerFlag) {
      taskList();
    } else {
      const data = {
        ...formData,
        page: current,
        size: pageSize,
        isDesc: sortby.current,
        instanceType: verifyType.current === 'inventoryVerify'? '审核': '共享'
      };
      if (activeTabs.current == 'verifyToMe') {
        contentListApis.auditToMe(data).then((res: any) => {
          if (res?.errorCode === 'success') {
            setAllList(res.extendMessage.results);
            setCheckedList([]);
            setIndeterminate(false);
            setCheckAll(false);
            setTotalPage(res.extendMessage.recordTotal);
          } else {
            // message.error('')
          }
        });
      } else {
        contentListApis.myAudit(data).then((res: any) => {
          if (res?.errorCode === 'success') {
            setAllList(res.extendMessage.results);
            setCheckedList([]);
            setIndeterminate(false);
            setCheckAll(false);
            setTotalPage(res.extendMessage.recordTotal);
          } else {
            // message.error('')
          }
        });
      }
    }
  };
  const batchPass = (flag: boolean, list: any, type?:number) => {
    if(verifyType.current === 'publishVerify'){
      let obj = {} as any
      list.forEach((item:any) => {
        obj[item.id] = item.flowInfo.id
      })
      if(flag){
        contentListApis.batchAuditPbulishResource(0, obj).then((res: any) => {
          if (res.errorCode == 'success') {
            message.success(intl.formatMessage({ id: '审核通过' }));
            taskList();
          }
        })
      }
      else{
        setRejectisModalOpen(true);
      }
    }  else if(verifyType.current === 'shareVerify') {
      if (flag) {
        let processNodes = list?.map((ele:any) => {
          return {
            id: ele.id,
            nodeId: ele?.flowInfo?.id || ''
          }
        })
        let params = {
          processNodes,
          "operateType": 0,
          "auditComment": ''
        }
        contentListApis.flowProcess(params).then((res: any) => {
          if (res.success) {
            message.success(intl.formatMessage({ id: `成功` }));
            setRejectisReason('');
            taskList();
          } 
        });
      } else {
        setOperateType(type)
        setRejectisModalOpen(true);
        setCurrentList(list)
      }
    } 
    else{
      if (flag) {
        if (verifyType.current === 'inventoryVerify') {
          let processFlowInfos = list?.map((item: any) => {
            return {
              instanceId: item.instanceId,
              resourceId: item.resourceId,
              flowNodeId: item.flowInfo.id
            }
          })
          let params = {
            status: "已审核",
            auditComment: "",
            processFlowInfos: processFlowInfos
          }
          contentListApis.bulkProcess(params).then((res: any) => {
            if (res.errorCode == 'success') {
              message.success(intl.formatMessage({ id: '审核通过' }));
              taskList();
            } else {
              message.error(res.errorMsg)
            }
          });
        } else {
          contentListApis.batchAuditResource('已审核', {flowNodeIds: list.map(item => item?.flowInfo?.id), resourceIds: list.map(item => item.resourceId), instanceIds: list.map(item => item.instanceId) })
          .then((res: any) => {
            if (res.errorCode == 'success') {
              message.success(intl.formatMessage({ id: '审核通过' }));
              taskList();
            }
          });
        }
      } else { // 驳回
        setRejectisModalOpen(true);
      }
    }
  };
  const revoke = (item?: any) => {
    contentListApis.revokeResource({
      instanceId: item ? item.instanceId : checkedList[0].instanceId,
      resourceId: item ? item.resourceId : checkedList[0].resourceId,
    }).then((res: any) => {
      if (res.errorCode == 'success') {
        message.success(intl.formatMessage({ id: '撤回成功' }));
        taskList();
      }
    })
  };
  const batchrevoke = (item?: any) => {
    contentListApis.batchRevokeResource({flowNodeIds: checkedList.map(item => item?.flowInfo?.id), resourceIds: checkedList.map(item => item.resourceId), instanceIds: checkedList.map(item => item.instanceId)}).then((res: any) => {
      if (res.errorCode == 'success') {
        message.success(intl.formatMessage({ id: '撤回成功' }));
        taskList();
      }
    })
  };
  const batchPublish =(list: any) => {
    if(verifyType.current === 'shareVerify') {
      let processNodes = list?.map((ele:any) => {
        return {
          id: ele.id,
          nodeId: ele?.flowInfo?.id || ''
        }
      })
      let params = {
        processNodes,
        "operateType": 3,
      }
      contentListApis.flowProcess(params).then((res: any) => {
        if (res.success) {
          message.success(intl.formatMessage({ id: '发布成功' }));
          taskList();
        }
      });
    } else {
      contentListApis.batchProcessPbulishResource(2, list.map((item: any) => item.id)).then((res: any) => {
        if (res.errorCode == 'success') {
          message.success(intl.formatMessage({ id: '发布成功' }));
          taskList();
        }
        }
      )
    }
  }

  const revert = (type:number) => {
      setRejectisModalOpen(true);
      setOperateType(type)
  }

  const batchRemove =(list: any) => {
    if(verifyType.current === 'shareVerify') {
      let processNodes = list?.map((ele:any) => {
        return {
          id: ele.id,
          nodeId: ele?.flowInfo?.id || ''
        }
      })
      let params = {
        processNodes,
        "operateType": 4,
      }
      contentListApis.flowProcess(params).then((res: any) => {
        if (res.success) {
          message.success(intl.formatMessage({ id: '下架成功' }));
          taskList();
        }
      });
    } else {
      contentListApis.batchProcessPbulishResource(3, list.map((item: any) => item.id)).then((res: any) => {
        if (res.errorCode == 'success') {
          message.success(intl.formatMessage({ id: '下架成功' }));
          taskList();
          }
        }
      )
    }
  }
  const reset = () => {
    form.resetFields();
    setCheckAll(false);
    setCheckedList([]);
    setCurrent(1);
    setPageSize(30);
  };
  let btnlist: any = [];
  if (verifyType.current == 'inventoryVerify') {
    if (activeTabs.current == 'verifyToMe') {
      btnlist.push({
        title: intl.formatMessage({ id: '通过' }),
        func: () => batchPass(true, checkedList),
        disabled:
          checkedList.length == 0 ||
          checkedList.some((item: any) => item.auditState == intl.formatMessage({ id: '已审核' }) || item.auditState == intl.formatMessage({ id: '已驳回' })|| item.auditState == intl.formatMessage({ id: '已撤销' })),
        icon: <CheckCircleOutlined />,
        className: 'pass_btn',
      });
      btnlist.push({
        title: intl.formatMessage({ id: '驳回' }),
        func: () => batchPass(false, checkedList),
        disabled:
          checkedList.length == 0 ||
          checkedList.some((item: any) => item.auditState == intl.formatMessage({ id: '已审核' }) || item.auditState == intl.formatMessage({ id: '已驳回' })|| item.auditState == intl.formatMessage({ id: '已撤销' })),
        icon: <CloseCircleOutlined />,
        className: 'reject_btn',
      });
    } else {
      btnlist.push({
        title: intl.formatMessage({ id: '撤回' }),
        func: () => batchrevoke(),
        disabled:
          checkedList.length == 0 ||
          checkedList.some((item: any) => item.auditState == intl.formatMessage({ id: '已审核' }) || item.auditState == intl.formatMessage({ id: '已驳回' })|| item.auditState == intl.formatMessage({ id: '已撤销' })),
        icon: <IconFont type="iconrecall" />,
        className: 'withdraw_btn',
      });
    }
  }

  if(verifyType.current === 'publishVerify' && activeTabs.current == 'verifyToMe'){ // 视频发布审核
    btnlist.push({
      title: intl.formatMessage({ id: '通过' }),
      func: () => batchPass(true, checkedList),
      disabled:
        checkedList.length == 0 ||
        checkedList.some((item: any) => item.auditStateName !== '待审核' ),
      icon: <CheckCircleOutlined />,
      className: 'pass_btn',
    });
    btnlist.push({
      title: intl.formatMessage({ id: '驳回' }),
      func: () => batchPass(false, checkedList),
      disabled:
        checkedList.length == 0 ||
        checkedList.some((item: any) => item.auditStateName !== '待审核' ),
      icon: <CloseCircleOutlined />,
      className: 'reject_btn',
    });
    btnlist.push({
      title: intl.formatMessage({ id: '发布' }),
      func: () => batchPublish(checkedList),
      disabled:
        checkedList.length == 0 ||
        checkedList.some((item: any) => item.auditStateName !== '已驳回' && item.auditStateName !== '已下架' ),
      icon: <CheckCircleOutlined />,
      className: 'pass_btn',
    });
    btnlist.push({
      title: intl.formatMessage({ id: '下架' }),
      func: () => batchRemove(checkedList),
      disabled:
        checkedList.length == 0 ||
        checkedList.some((item: any) => item.auditStateName !== '已通过' && item.auditStateName !== '已发布' ),
      icon: <CloseCircleOutlined />,
      className: 'reject_btn',
    });
  }

  if((verifyType.current === 'shareVerify')){
    if (activeTabs.current == 'verifyToMe') { // 待我审核
      btnlist.push({
        title: intl.formatMessage({ id: '通过' }),
        func: () => batchPass(true, checkedList, 0),
        disabled:
          checkedList.length == 0 ||
          checkedList.some((item: any) => (item.auditStateName !== '审核中' && item.auditStateName !== '待审核') ),
        icon: <CheckCircleOutlined />,
        className: 'pass_btn',
      });
      btnlist.push({
        title: intl.formatMessage({ id: '驳回' }),
        func: () => batchPass(false, checkedList, 1),
        disabled:
          checkedList.length == 0 ||
          checkedList.some((item: any) => (item.auditStateName !== '审核中' && item.auditStateName !== '待审核') ),
        icon: <CloseCircleOutlined />,
        className: 'reject_btn',
      });
      btnlist.push({
        title: intl.formatMessage({ id: '下架' }),
        func: () => batchRemove(checkedList),
        disabled:
          checkedList.length == 0 ||
          checkedList.some((item: any) => item.auditStateName !== '已通过' ),
        icon: <CloseCircleOutlined />,
        className: 'reject_btn',
      });
    } else { // 我发起的审核
      btnlist.push({
        title: intl.formatMessage({ id: '撤销' }),
        func: () => revert(2),
        disabled:
          checkedList.length == 0 ||
          checkedList.some((item: any) => (item.auditStateName !== '待审核' && item.auditStateName !== '审核中') ),
        icon: <CheckCircleOutlined />,
        className: 'pass_btn',
      });
    }
    
  }
  
  btnlist.push({
      title: intl.formatMessage({ id: '刷新' }),
      func: () => formChange(),
      icon: <RedoOutlined />,
      className: 'refresh_btn',
    });

  const goback = () => {
    reset()
    setInnerFlag(false);
    currentTask.current = null;
  };

  // 驳回
  const reject = (list) => {
    if(verifyType.current === 'shareVerify') {
      let type = ''
      switch (operateType) {
        case 0:
          type = '审核'
          break;
        case 1:
          type = '驳回'
          break;
        case 2:
          type = '撤销'
          break;
        default:
          type = '驳回'
          break;
      }
      if (rejectisReason == '') {
        message.error(intl.formatMessage({ id: `请输入${type}理由` }));
        return;
      }
      let processNodes = (itemFalg ? currentList :  list)?.map((ele:any) => {
        return {
          id: ele.id,
          nodeId: ele?.flowInfo?.id || ''
        }
      })
      let params = {
        processNodes,
        "operateType": operateType,
        "auditComment": rejectisReason
      }
      contentListApis.flowProcess(params).then((res: any) => {
        if (res.success) {
          message.success(intl.formatMessage({ id: `${type}成功` }));
          setRejectisModalOpen(false);
          setRejectisReason('');
          taskList();
        } 
      });
    } else {
      if (rejectisReason == '') {
        message.error(intl.formatMessage({ id: '请输入驳回理由' }));
        return;
      }
      getSensitiveWord(rejectisReason, intl.formatMessage({ id: '驳回理由' }), () => {
        if(verifyType.current === 'inventoryVerify'){
          let processFlowInfos = list?.map((item: any) => {
            return {
              instanceId: item.instanceId,
              resourceId: item.resourceId,
              flowNodeId: item.flowInfo.id
            }
          })
          let params = {
            status: "已驳回",
            auditComment: rejectisReason,
            processFlowInfos: processFlowInfos
          }
          contentListApis.bulkProcess(params).then((res: any) => {
            if (res.errorCode == 'success') {
              message.success(intl.formatMessage({ id: '驳回成功' }));
              setRejectisModalOpen(false);
              setRejectisReason('');
              taskList();
            } else {
              message.error(res.errorMsg)
            }
          });
  
          // contentListApis.batchAuditResource('已驳回', {flowNodeIds: list.map(item => item?.flowInfo?.id), resourceIds: list.map(item => item.resourceId), instanceIds: list.map(item => item.instanceId) }).then((res: any) => {
          //   if (res.errorCode == 'success') {
          //     message.success(intl.formatMessage({ id: '驳回成功' }));
          //     setRejectisModalOpen(false);
          //     setRejectisReason('');
          //     taskList();
          //   }
          // });
        }  else{
          let obj = {} as any
          (list.length?list:[innerItem]).forEach((item:any) => {
            obj[item.id] = item.flowInfo.id
          })
          contentListApis.batchAuditPbulishResource(1, obj, rejectisReason).then((res: any) => {
            if (res.errorCode == 'success') {
              message.success(intl.formatMessage({ id: '驳回成功' }));
              setRejectisModalOpen(false);
              setRejectisReason('');
              taskList();
            }
          })
        }
      });
    }
   
  };


  useEffect(() => {
    if (sessionStorage.publishResource) {
      verifyType.current = 'shareVerify'
      activeTabs.current = 'myVerify'
    }
  }, [sessionStorage.publishResource])

  useEffect(()=>{
    if (!(rmanGlobalParameter.includes(globalParams.audit_import) && permissions.includes(perCfg.import_review)) ) {
      verifyType.current = 'shareVerify'
    } else if (!publish_review_display) {
      verifyType.current = 'publishVerify'
    }
  },[])


  return (
    <div className={'verify-container ' + (innerFlag ? 'verify-container-inner' : '')}>
      <Tabs
        activeKey={verifyType.current}
        onChange={e => {
          setInnerFlag(true);
          verifyType.current = e;
          reset();
        }}
        activeKey={verifyType.current}
        type="card"
        >
        {
        rmanGlobalParameter.includes(globalParams.audit_import) && permissions.includes(perCfg.import_review) &&
        <Tabs.TabPane key={'inventoryVerify'} tab={intl.formatMessage({ id: '入库审核' })}>
        <Tabs
          activeKey={activeTabs.current}
          onChange={e => {
            activeTabs.current = e;
            reset();
          }}
          className='verify-tabs'
        >
          {permissions.includes(perCfg.audit_to_me) &&<Tabs.TabPane key={'verifyToMe'} tab={intl.formatMessage({ id: '待我审核' })} />}
          <Tabs.TabPane key={'myVerify'} tab={intl.formatMessage({ id: '我发起的审核' })} />
        </Tabs>
        </Tabs.TabPane>}
        {/* 默认显示, 不配置或者配置成true都显示 */}
        {publish_review_display && <Tabs.TabPane key={'shareVerify'} tab={intl.formatMessage({ id: '发布审核' })} >
        <Tabs
          activeKey={activeTabs.current}
          onChange={e => {
            activeTabs.current = e;
            reset();
          }}
          className='verify-tabs'
        >
          {permissions.includes(perCfg.audit_to_me) &&<Tabs.TabPane key={'verifyToMe'} tab={intl.formatMessage({ id: '待我审核' })} />}
          <Tabs.TabPane key={'myVerify'} tab={intl.formatMessage({ id: '我发起的审核' })} />
        </Tabs>
        </Tabs.TabPane>}
        {permissions.includes(perCfg.video_publish) && <Tabs.TabPane key={'publishVerify'} tab={intl.formatMessage({ id: '短视频发布审核' })} >
        <Tabs
          activeKey={activeTabs.current}
          onChange={e => {
            activeTabs.current = e;
            reset();
          }}
          className='verify-tabs'
        >
          {permissions.includes(perCfg.audit_to_me) &&<Tabs.TabPane key={'verifyToMe'} tab={intl.formatMessage({ id: '待我审核' })} />}
          <Tabs.TabPane key={'myVerify'} tab={intl.formatMessage({ id: '我发起的审核' })} />
        </Tabs>
        </Tabs.TabPane>}
      </Tabs>
      <div className="body">
        <div className="searchBox">
          <Form form={form} name="verify-form">
            <Row style={{ width: '100%' }} className='pc_show'>
              <Col span={5} hidden={!innerFlag}>
                <Form.Item name={'resourceName'} noStyle>
                  <Input
                    placeholder={intl.formatMessage({ id: '输入资源关键词' })}
                    allowClear
                    autoComplete={'off'}
                    onPressEnter={formChange}
                  />
                </Form.Item>
              </Col>
              <Col span={5} hidden={innerFlag}>
                <Form.Item name={'instanceName'} noStyle>
                  <Input
                    placeholder={intl.formatMessage({ id: '输入审核任务关键词' })}
                    allowClear
                    autoComplete={'off'}
                    onPressEnter={formChange}
                  />
                </Form.Item>
              </Col>
              {verifyType.current === 'inventoryVerify'?
              <Col span={4}>
                <Form.Item name={'status'} noStyle>
                  <Select placeholder={intl.formatMessage({ id: '审核进度' })} onChange={formChange}>
                        <Option value='待审核'>{intl.formatMessage({ id: '待审核' })}</Option>
                        <Option value='已审核'>{intl.formatMessage({ id: '已审核' })}</Option>
                        {innerFlag &&<Option value='已驳回'>{intl.formatMessage({ id: '已驳回' })}</Option>}
                  </Select>
                </Form.Item>
              </Col>: activeTabs.current === 'myVerify'? <Col span={4}>
                <Form.Item name={'status'} noStyle>
                  <Select placeholder={intl.formatMessage({ id: '审核状态' })} onChange={formChange}>
                        <Option value='待审核'>{intl.formatMessage({ id: '待审核' })}</Option>
                        <Option value='已通过'>{intl.formatMessage({ id: '已通过' })}</Option>
                        <Option value='已驳回'>{intl.formatMessage({ id: '已驳回' })}</Option>
                        <Option value='已发布'>{intl.formatMessage({ id: '已发布' })}</Option>
                        <Option value='已下架'>{intl.formatMessage({ id: '已下架' })}</Option>
                  </Select>
                </Form.Item>
              </Col> : ''}
              <Col span={4} className='pc_show'>
                <div className="result_search_buttom" >
                  <Button onClick={() => {reset();formChange()}} type="link">
                    {intl.formatMessage({ id: '清空' })}
                    <UndoOutlined />
                  </Button>
                  <Button onClick={formChange} type="primary">
                    {intl.formatMessage({ id: '搜索' })}
                    <IconFont type="iconsousuo2" />
                  </Button>
                </div>
              </Col>
            </Row>
            <div className='mobile_show'>
              <Row style={{ width: '100%' }}>
                {innerFlag && <Col span={1}><span className="back_btn" onClick={goback}></span></Col>}
                <Col span={innerFlag? 19: 21} hidden={innerFlag} style={{ display: 'flex', alignItems: 'center' }} className='search_box'>
                  <Form.Item name={'instanceName'} noStyle>
                    <Input
                      placeholder={intl.formatMessage({ id: '输入审核任务关键词' })}
                      allowClear
                      autoComplete={'off'}
                      onPressEnter={formChange}
                    />
                  </Form.Item>
                </Col>
                <Col span={innerFlag? 19: 21} hidden={!innerFlag} style={{ display: 'flex', alignItems: 'center' }} className='search_box'>
                  <IconFont style={{ fontSize: '20px' }} type="iconsousuo3" />
                  <Form.Item name={'resourceName'} noStyle>
                    <Input
                      placeholder={intl.formatMessage({ id: '输入资源关键词' })}
                      autoComplete={'off'}
                      onPressEnter={formChange}
                    />
                  </Form.Item>
                </Col>
                <Col span={1}>
                  <IconFont style={{ fontSize: '20px' }} onClick={() => setMoreSelectDrawerOpen(true)} type="iconshaixuan" />
                  <Drawer title={intl.formatMessage({ id: '更多筛选' })} className='more_select_drawer' open={moreSelectDrawerOpen} onClose={() => setMoreSelectDrawerOpen(false)} placement="top">
                    <Form.Item name={'status'} noStyle>
                      <Select placeholder={innerFlag?intl.formatMessage({ id: '审核状态' }):intl.formatMessage({ id: '审核进度' })} onChange={formChange}>
                        <Option value='待审核'>{intl.formatMessage({ id: '待审核' })}</Option>
                        <Option value='已审核'>{intl.formatMessage({ id: '已审核' })}</Option>
                        {innerFlag &&<Option value='已驳回'>{intl.formatMessage({ id: '已驳回' })}</Option>}
                      </Select>
                    </Form.Item>
                    <div className='footer'>
                      <Button type="primary" ghost onClick={() => { reset();formChange() }}>{intl.formatMessage({ id: '重置' })}</Button>
                      <Button type="primary" onClick={() => { setMoreSelectDrawerOpen(false) }}>{intl.formatMessage({ id: '确认' })}</Button>
                    </div>
                  </Drawer>
                </Col>
              </Row>
            </div>
          </Form>
          {verifyType.current =='shareVerify' && <Segmented value={segment} onChange={setSegment} options={['资源发布审核', '资源发布专题审核']} />}
        </div>
        {mobileFlag && <Breadcrumb>
          <Breadcrumb.Item key={-1} className='root_'>
            <a onClick={() => props.setCatalogueVisible(true)}>{intl.formatMessage({ id: '返回目录' })}</a>
          </Breadcrumb.Item>
        </Breadcrumb>}
        <div className="operatBox">
          <div className="left_">
            {innerFlag && (
              <Checkbox
                indeterminate={indeterminate}
                onChange={onCheckAllChange}
                checked={checkAll}
              >
                {intl.formatMessage({ id: '全部' })}
              </Checkbox>
            )}
            {btnlist.map((item: any, index: number) => (
              <div
                key={index}
                className={(item.disabled ? 'disabled' : '') + ' ' + item.className}
                onClick={() => {
                  if (!item.disabled) {
                    item.func();
                  }
                }}
              >
                {item.icon}
                <span>{item.title}</span>
              </div>
            ))}
          </div>
          <div className="right_">
            <Select
              defaultValue="createDate_,down"
              style={{ width: 120 }}
              onChange={handleChange}
            >
              {sort.map((item, index) => (
                <Option value={item.label} key={index}>
                  {item.value}
                  {item.icon}
                </Option>
              ))}
            </Select>
          </div>
        </div>
        <div className="list">
        <ListTop
            verify={true}
            columns={
              verifyType.current == 'publishVerify' ? (activeTabs.current == 'verifyToMe' ? column_3.verifyToMe : column_3.myVerify) : 
              (verifyType.current == 'shareVerify' ? ( activeTabs.current == 'verifyToMe' ? (segment == '资源发布审核' ? column_5.verifyToMe : column_4.verifyToMe) : 
              (segment == '资源发布审核' ? column_5.verifyToMe : column_4.verifyToMe)) :
              (activeTabs.current == 'verifyToMe' ? (innerFlag ? column_1.in : column_1.out) : (innerFlag ? column_2.in : column_1.out)))
            }
          />
          {(innerFlag ? innerList : allList).length == 0 ? (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          ) : (
            <CheckboxGroup
              value={checkedList}
              onChange={onChange}
              style={{ width: '100%' }}
            >
              {(innerFlag ? innerList : allList).map((item: any) => (
                <ContentItem
                  key={(item.resourceId || item.instanceId)+ item.createTime}
                  columns={
                    verifyType.current == 'publishVerify' ? (activeTabs.current == 'verifyToMe' ? column_3.verifyToMe : column_3.myVerify) : 
                    (verifyType.current == 'shareVerify' ? ( activeTabs.current == 'verifyToMe' ? (segment == '资源发布审核' ? column_5.verifyToMe : column_4.verifyToMe) : 
                    (segment == '资源发布审核' ? column_5.verifyToMe : column_4.verifyToMe)) :
                    (activeTabs.current == 'verifyToMe' ? (innerFlag ? column_1.in : column_1.out) : (innerFlag ? column_2.in : column_1.out)))
                  }
                  modal={false}
                  detail={item}
                  recycleBin={false}
                  searchVoiceWord=""
                  downEnable={0}
                  setInnerItem={setInnerItem}
                  goDetail={(type: number) => detail(item, type)}
                  shareEnble={false}
                  resourceGroup={false}
                  revoke={revoke}
                  pass={(flag: boolean, list: any, type?:number)=>{batchPass(flag, list, type); setItemFalg(true)}}
                  publish={batchPublish}
                  remove={batchRemove}
                  myVerify={
                    (verifyType.current === 'publishVerify' || verifyType.current == 'shareVerify') ? (activeTabs.current == 'verifyToMe' ? 'verifyToMe': 'myVerify') :
                    (activeTabs.current == 'verifyToMe' ? innerFlag ? '0-1' : '0-0' : innerFlag ? '1-1' : '1-0')
                  }
                  mineVerify={verifyType.current == 'shareVerify'}
                />
              ))}
            </CheckboxGroup>
          )}
        </div>
        <div className="pagination">
          <Pagination
            current={current}
            pageSize={pageSize}
            total={innerFlag?innerTotalPage:totalPage}
            size="small"
            showQuickJumper
            onChange={changepage}
            showTotal={total => intl.formatMessage({
              id: "共条"
            },{total})}
            showSizeChanger={true}
            pageSizeOptions={['30', '40', '50', '100']}
          />
        </div>
      </div>
      <Modal
        title={intl.formatMessage({ id: `${operateType == 0 ? '通过' : (operateType == 2 ? '撤销' : '驳回')}理由` })}
        footer={[
          <Button
            type="primary"
            key={1}
            onClick={() => reject(checkedList)}
          >
            {intl.formatMessage({ id: '确认' })}
          </Button>
        ]}
        open={rejectisModalOpen}
        getContainer={false}
        onOk={() => { }}
        onCancel={() => {
          setRejectisModalOpen(false);
          setRejectisReason('')
          setItemFalg(false)
        }}
      >
        {/* <span className='tip_span'>请输入驳回理由,最多30字：</span> */}
        <TextArea
          rows={6}
          value={rejectisReason}
          placeholder={intl.formatMessage({ id: `请输入${operateType == 0 ? '通过' : (operateType == 2 ? '撤销' : '驳回')}理由最多30字：` })}
          maxLength={30}
          onChange={e => setRejectisReason(e.target.value)}
        />
      </Modal>
    </div>
  );
};

export default ResourceVerify;