import http from '../http/http';
import searchTypes from '@/types/searchTypes';
const prefix = '/unifiedplatform/v1/storage';
namespace contentListApis {
  export const searchfolder = (data: any) => {
    //个人公共素材检索
    return http<searchTypes.ISearchRes>('/search/folder', {
      method: 'POST',
      data
    });
  };
  export const searchfolder_share = (data: any, link: string) => {
    //个人公共素材检索 针对分享没有权限问题
    return http<searchTypes.ISearchRes>('/share/folder/resources', {
      method: 'POST',
      params: {
        link,
      },
      data,
    });
  };
  export const searchall = (data: any) => {
    //搜索
    return http<searchTypes.ISearchRes>('/search/all', {
      method: 'POST',
      data,
    });
  };
  export const newpublicfolder = (data: searchTypes.Nfolder) => {
    //新建公共文件夹
    return http<searchTypes.INfolder>('/group/folder', {
      method: 'POST',
      data: data,
      isSleep: true,
    });
  };
  //群组资源
  export const newgroupfolder = (data: any) => {
    //新建群组文件夹
    return http<any>('/group/folder', {
      method: 'POST',
      data: data,
    });
  };
  export const newsharefolder = (data: any) => {
    //新建群组文件夹
    return http<any>('/folder/public', {
      method: 'POST',
      data: data,
    });
  };
  export const getgroupusers = (data: any) => {
    //初始化群组人员
    return http<any>('/group/select/user', {
      method: 'POST',
      data: data,
    });
  };
  export const addgroupusers = (data: any) => {
    //添加群组人员
    return http<any>('/group/add/user', {
      method: 'POST',
      data: data,
    });
  };
  export const deletegroupusers = (data: any) => {
    //删除群组人员
    return http<any>('/group/delete', {
      method: 'POST',
      data: data,
    });
  };
  export const newprivatefolder = (data: searchTypes.Nfolder) => {
    //新建个人文件夹
    return http<searchTypes.INfolder>('/folder/private', {
      method: 'POST',
      data: data,
      isSleep: true,
    });
  };
  export const renamefolder = (data: searchTypes.RNfolder) => {
    //文件夹重命名
    return http<boolean>('/folder/resource/rename', {
      method: 'POST',
      data: data,
      isSleep: true,
    });
  };
  export const renameentity = (
    data: searchTypes.RNfolder,
    isPublic?: boolean,
  ) => {
    //文件重命名
    return http<boolean>('/entity/entitydata/rename', {
      method: 'PATCH',
      data: data,
      params: {
        isPublic: isPublic,
      },
      isSleep: true,
    });
  };
  export const downloadentity = (data: Array<string>) => {
    //文件下载
    return http<searchTypes.IDownLoad[]>(`/entity/download/fileinfo`, {
      method: 'POST',
      data: data,
    });
  };
  export const getfields = () => {
    //获取元数据
    return http<searchTypes.IEntityType[]>(
      `/unifiedplatform/v1/base/data/database/get/show/base/data`,
      {
        method: 'GET',
      },
    );
  };
  export const getrecyclebinlist = (data: any) => {
    //文件下载
    return http<any>(`/recycle/resource/all`, {
      method: 'POST',
      data: data,
    });
  };
  export const getmyvideolist = (data: any) => {
    //我的录播
    return http<any>(`/search/my/video`, {
      method: 'POST',
      data: data,
    });
  };
  //院系录播
  export const getdepartmentvideolist = (data: any) => {
    return http(`/search/department/video`, {
        method: 'POST',
        data: data
    })
  }
  export const getmycollectionlist = (data: any) => {
    //查询我的收藏
    return http<any>(`/metadata/resource/search/collection`, {
      method: 'POST',
      data: data,
    });
  };
  export const addmycollectionlists = (data: any) => {
    //批量添加我的收藏
    return http<any>(`/metadata/resource/collections`, {
      method: 'POST',
      data: data,
    });
  };
  export const addmycollectionlist = (data: any) => {
    //单个添加我的收藏
    return http<any>(`/metadata/resource/collection?contentId=${data}`, {
      method: 'POST',
    });
  };
  export const deletemycollectionlist = (data: any) => {
    //取消我的收藏
    return http<any>(`/metadata/resource/delete/collection`, {
      method: 'POST',
      data: data,
    });
  };
  /**
   * 分页查询教师学院信息
   *
   * @param {string} data
   * 【query】
   * keyWord，pageIndex，pageSize
   * sourceTpye--查询类型 0：教师；1：学院；2：专业
   * 【options】
   * ifLoading：该请求不显示loading
   * @return {*}
   */
  export const getPagingData = (data: string) => {
    //获取分页元数据
    return http(`/unifiedplatform/v1/base/data/database/source/data?${data}`, {
      method: 'GET',
      ifLoading: false,
    });
  };
  export const getPagingDataYunqer = (data: string) => {
    //获取分页元数据
    return http(
      `/unifiedplatform/v1/base/data/database/source/data/new?${data}`,
      {
        method: 'GET',
        ifLoading: false,
      },
    );
  };
  //查询当前实例对应的基础信息
  export const getEntityByContentId = (data: string) => {
    //获取分页元数据
    return http(`/rman/v1/entity/${data}`, {
      method: 'GET',
      ifLoading: true,
    });
  };
  //资源分享
  export const shareResource = (data: any) => {
    return http(`/rman/v1/share/resource`, {
      method: 'POST',
      data,
    });
  };
  //资源分享_链接（新）
  export const shareResourceNew = (data: any) => {
    return http(`/rman/v1/share/create/share/link`, {
      method: 'POST',
      data,
    });
  };
  //资源分享_系统用户（新）
  export const shareUserResourceNew = (data: any) => {
    return http(`/rman/v1/share/create/user/share/link`, {
      method: 'POST',
      data,
    });
  };
  //分享详情查询（新）
  export const fetchShareDetail = (link: string, password: string) => {
    return http(`/rman/v1/share/link/info`, {
      method: 'GET',
      params: {
        link,
        password,
      },
    });
  };
  //链接分享详情查询（新）
  export const fetchShareLists = (params: any) => {
    return http(`/rman/v1/share/share/resources`, {
      method: 'GET',
      params,
    });
  };
  //取消分享
  export const cancelShared = (data: any) => {
    return http(`/rman/v1/share/delete/my/shared`, {
      method: 'POST',
      data,
    });
  };
  //移除
  export const removeShared = (data: any) => {
    return http(`/rman/v1/share/delete/share/with/me`, {
      method: 'POST',
      data,
    });
  };
  //查询自己分享的
  export const searchMyShare = (params: any) => {
    // return http(`/rman/v1/share/search/owner`, {
    return http(`/rman/v1/share/my/shared`, {
      method: 'GET',
      params,
    });
  };
  //查询未读分享数量
  export const searchUnreadNums = () => {
    // return http(`/rman/v1/share/unread/message/to/me`, {
    return http(`/rman/v1/share/count`, {
      method: 'GET',
    });
  };
  //标记已读_系统用户
  export const markRead = (params: any, temp: string = '') => {
    return http(`/rman/v1/share/read/with/me?link=${params}&userCode=${temp}`, {
      method: 'POST',
    });
  };
  //标记已读_链接分享
  export const markReadLink = (params: any, temp: string = '') => {
    return http(`/rman/v1/share/read/with/me?link=${params}${temp}`, {
      method: 'POST',
    });
  };
  //查询分享给自己的
  export const shareMyself = (params: any) => {
    // return http(`/rman/v1/share/search`, {
    return http(`/rman/v1/share/share/with/me`, {
      method: 'GET',
      params,
    });
  };
  /**
   * 申请空间扩容
   *
   */
  export const applyStorage = (data: any) =>
    http(`${prefix}/space/apply`, {
      method: 'POST',
      data,
    });
  /**
   * 获取用户空间
   *
   */
  export const fetchUserStorage = () =>
    http(`${prefix}/space/user/info`, {
      method: 'GET',
    });
  /**
   * 获取用户最新一条空间申请
   *
   */
  export const applyStorageLatest = () =>
    http(`${prefix}/space/apply/latest`, {
      method: 'POST',
    });
  /**
   * 标记用户最新一条空间已读
   *
   */
  export const applyStorageLatestRead = () =>
    http(`${prefix}/space/apply/latest/read`, {
      method: 'POST',
    });
  /**
   * 取消申请
   *
   */
  export const applyStorageCancle = () =>
    http(`${prefix}/space/apply/cancle`, {
      method: 'GET',
    });
  //获取学期数据
  export const getSemesterData = () => {
    return http(`/unifiedplatform/v1/base/data/database/get/semester/by/name`, {
      method: 'GET',
      ifLoading: false,
    });
  };
  //获取（录播）视频来源；
  export const getVideoSource = () => {
    return http(`/rman/v1/search/source`, {
      method: 'GET',
      ifLoading: false,
    });
  };
  //查询当前素材的任务列表
  export const getTextclipLists = (params: any) => {
    return http(`/textclip/IntelligentTask/task/id`, {
      method: 'GET',
      params,
    });
  };
  //创建语音剪辑任务
  export const createTextClip = (data: any) => {
    return http(`/textclip/IntelligentTask/add`, {
      method: 'POST',
      data,
    });
  };
  export const auditTomePublishResource = (data: any) => {
    return http(`/rman/v1/shortvideopublishaudit/owner/examine/instance/resource`, {
      method: 'POST',
      params: data,
    });
  };
  export const myAuditPublishResource = (data: any) => {
    return http(`/rman/v1/shortvideopublishaudit/owner/instance/resouce`, {
      method: 'POST',
      params: data,
    });
  };
  //待我审核
  export const auditToMe = (data: any) => {
    return http(`/unifiedplatform/v1/audit/owner/examine/instance`, {
      method: 'POST',
      params: data,
    });
  };
  //我发起的审核
  export const myAudit = (data: any) => {
    return http(`/unifiedplatform/v1/audit/owner/instance`, {
      method: 'POST',
      params: data,
    });
  };

  export const myAuditResource = (data: any) => {
    return http(`/unifiedplatform/v1/audit/owner/instance/resource`, {
      method: 'POST',
      params: data,
    });
  };

  // 入库审核 待我审核
  export const storeOwnerAudit = (data: any) => {
    return http(`/unifiedplatform/v1/audit/owner/audit`, {
      method: 'GET',
      params: data,
    });
  };
  // 入库审核 我发起的审核
  export const storeOwnerInitiate = (data: any) => {
    return http(`/unifiedplatform/v1/audit/owner/initiate`, {
      method: 'GET',
      params: data,
    });
  };

  // 入库审核 审核通过/驳回 
  export const bulkProcess = (data: any) => {
    return http(`/unifiedplatform/v1/audit/bulk/process`, {
      method: 'POST',
      data: data,
    });
  };

  // 待我审核
  export const ownerAudit = (data: any) => {
    return http(`/rman/v1/resourcepublishaudit/owner/audit`, {
      method: 'GET',
      params: data,
    });
  };
  // 我发起的审核
  export const ownerInitiate = (data: any) => {
    return http(`/rman/v1/resourcepublishaudit/owner/initiate`, {
      method: 'GET',
      params: data,
    });
  };
  //查询流程审核的实例资源
  export const auditResourceList = (params: any) => {
    return http(`/unifiedplatform/v1/audit/instance/resource`, {
      method: 'GET',
      params,
    });
  };
  //批量审核
  export const batchAuditResource = (status: "已审核" | "已驳回", data: any, auditComment?: string, instanceId?: string) => {
    return http(`/unifiedplatform/v1/audit/bulk/process`, {
      method: 'POST',
      params: { instanceId, status, auditComment },
      data
    });
  };
  //批量审核
  export const flowProcess = (data: any) => {
    return http(`/rman/v1/resourcepublishaudit/flow/process`, {
      method: 'POST',
      data
    });
  };
  //撤回审核
  export const revokeResource = (params: any) => {
    return http(`/unifiedplatform/v1/audit/revoke`, {
      method: 'POST',
      params,
    });
  };
  //批量撤回审核
  export const batchRevokeResource = (data: any) => {
    return http(`/unifiedplatform/v1/audit/bulk/revoke`, {
      method: 'POST',
      data
    });
  };
  export const batchAuditPbulishResource = (status: number, data: any, auditComment?: string) => {
    return http(`/rman/v1/shortvideopublishaudit/bulk/process/flow`, {
      method: 'POST',
      params: { operateType: status, auditComment },
      data
    });
  };
  export const getAuditinfoById = (params: any) => {
    return http(`/rman/v1/shortvideopublishaudit/getauditinfobyid`, {
      method: 'POST',
      params,
    });
  };
  export const getFlowInfo = (params: any) => {
    return http(`/rman/v1/resourcepublishaudit/flow/info`, {
      method: 'GET',
      params,
    });
  };
  
  export const batchProcessPbulishResource = (status: number, data: any, auditComment?: string) => {
    return http(`/rman/v1/shortvideopublishaudit/bulk/process`, {
      method: 'POST',
      params: { operateType: status, auditComment },
      data
    });
  };
  export const getCurrentSemester = () => {
    return http(`/unifiedplatform/v1/base/data/database/get/semester/current`, {
      method: 'get'
    });
  };
  export const getSettingWatermark = () => {
    return http(`/unifiedplatform/v1/setting/watermark`, {
      method: 'get'
    });
  };
  //评论
  export const addComment = (data: any) => {
    return http(`/rman/v1/comment/add`, {
      method: 'POST',
      data,
    });
  };
  // 查询评论
  export const getComment = (data: any) => {
    return http(`/rman/v1/comment/page`, {
      method: 'POST',
      data,
    });
  };
  // 删除评论
  export const deleteComment = (data: any) => {
    return http(`/rman/v1/comment/delete`, {
      method: 'DELETE',
      data,
    });
  };
  // 查询评论总数
  export const getCommentTotal = (data: any) => {
    return http(`/rman/v1/comment/count`, {
      method: 'GET',
      params: data,
    });
  };
  // 查询互动试题版本
  export const getQuestionVersion = (data: any) => {
    return http(`/question/version`, {
      method: 'GET',
      params: data,
    });
  };
  // 添加互动试题
  export const addQuesion = (data: any) => {
    return http(`/rman/v1/question/adds`, {
      method: 'POST',
      data,
    });
  };
  // 删除试题
  export const deleteQuestion = (data: any) => {
    return http(`/rman/v1/question/version`, {
      method: 'DELETE',
      data,
    });
  };
  // 删除互动试题，通过试题唯一标识
  export const deleteQuestionBySingle = (data: any) => {
    return http(`/rman/v1/question/single`, {
      method: 'DELETE',
      data,
    });
  };
  // 查询单个互动试题
  export const getQuestionDetails = (data: any) => {
    return http(`/rman/v1/question/details`, {
      method: 'GET',
      params: data,
    });
  };
  // 添加互动试题
  export const addQuesionVersion = (data: any) => {
    return http(`/rman/v1/question/version`, {
      method: 'POST',
      data,
    });
  };
   // 编辑互动试题
   export const editQuesionVersion = (params: any) => {
    return http(`/rman/v1/question/version`, {
      method: 'PATCH',
      params,
    });
  };
   // 删除互动试题
   export const deleteQuesionVersion = (params: any) => {
    return http(`/rman/v1/question/only/version`, {
      method: 'DELETE',
      params,
    });
  };
  // 查询版本互动试题
  export const getListByVersion = (params: any) => {
    return http(`/rman/v1/question/version/bank`, {
      method: 'GET',
      params,
    });
  };
  // 根据id批量查询
  export const getQuestionsByIds = (data: any) => {
    return http(`/exam-api/examination/getQuestionsByIds`, {
      method: 'POST',
      data,
    });
  };
  // 替换试题
  export const replaceQuestion = (data: any) => {
    return http(`/rman/v1/question/replace`, {
      method: 'POST',
      data,
    });
  };

  export const getQuestionPoint = (params: any) => {
    return http(`/rman/v1/question/in/point`, {
      method: 'GET',
      params,
    });
  };
  export const procesSsaveas = (data: any) => {
    return http(`/rman/v1/entity/video/process/saveas`, {
      method: 'POST',
      data,
    });
  };
  export const getRepairInfo = (params: any) => {
    return http(`/rman/v1/review/video/info`, {
      method: 'GET',
      params,
    });
  };
  export const repairInfo = (params: { contentId: string; }, data: any) => {
    return http(`/review/video/recovery`, {
      method: 'POST',
      data,
      params
    });
  };
  export const getContentids = (params: any, data: any) => {
    return http(`/rman/v1/search/contentids`, {
      method: 'POST',
      data,
      params
    });
  };
  export const getKnowledgePoints = (params: any) => {
    return http(`/rman/v1/intelligent/split/knowledge/points`, {
      method: 'GET',
      params
    });
  };

  export const getKnowledgeMap = (params: any) => {
    return http(`/rman/v1/intelligent/disassembly/knowledge/map`, {
      method: 'GET',
      params
    });
  };
  export const importGif= (data: any, params: any) => {
    return http(`/rman/v1/upload/import/gif`, {
      method: 'POST',
      data,
      params
    });
  };
  export const qualityCheck = (params: any) => {
    return http(`/entity/quality/check/${params.contentId}`, {
      method: 'POST'
    });
  };
  export const qualityCheckMeta = (contentId: string) => {
    return http(`/rman/v1/entity/quality/checkmeta/${contentId}`, {
      method: 'POST'
    });
  };
  export const qualityCheckMetaUpdate = (contentId: any, data: any) => {
    return http(`/rman/v1/entity/quality/checkmeta/${contentId}/update`, {
      method: 'POST',
      data
    });
  };
  export const qualityCheckMetaSaveas= (contentId: any, data: any) => {
    return http(`/rman/v1/entity/quality/checkmeta/${contentId}/saveas`, {
      method: 'POST',
      data
    });
  };
  export const associatedResource= (params: any) => {
    return http(`/rman/v1/search/associated/resource`, {
      params,
      method: 'GET',
    });
  };
  export const addTheme= (data: any) => {
    return http(`/rman/v1/entity/add/theme`, {
      data,
      method: 'POST',
    });
  };
  export const initialization= () => {
    return http(`/rman/v1/search/theme/initialization/tree`, {
      method: 'GET',
    });
  };
  export const getpictureSecurity = (params: any) => {
    return http(`/rman/v1/intelligent/image/content/security`, {
      method: 'GET',
      params
    });
  }
  export const getdocumentSecurity = (params: any) => {
    return http(`/rman/v1/intelligent/document/content/security`, {
      method: 'GET',
      params
    });
  }
  export const getaudioSecurity = (params: any) => {
    return http(`/rman/v1/intelligent/audio/content/security`, {
      method: 'GET',
      params
    });
  }
  export const getvideoSecurity = (params: any) => {
    return http(`/rman/v1/intelligent/video/content/security`, {
      method: 'GET',
      params
    });
  }
  export const getResourceOcr = (params: any) => {
    return http(`/rman/v1/intelligent/resource/ocr`, {
      method: 'GET',
      params
    });
  }
  export const resourceOcr = (data: any) => {
    return http(`/rman/v1/intelligent/resource/ocr`, {
      method: 'POST',
      data
    });
  }
  export const deleteResourceOcr = (params: any) => {
    return http(`/rman/v1/intelligent/delete/resource/ocr`, {
      method: 'POST',
      params
    });
  }
  export const getFileInfo = (data: any) => {
    return http(`/rman/v1/entity/download/fileinfo`, {
      method: 'POST',
      data
    });
  }
  export const getMyVideo = (params: any) => {
    return http(`/rman/v1/search/my/video`, {
      method: 'get',
      params
    });
  }
  export const getMyVideoNotByschedule = (data: any) => {
    return http(`/rman/v1/search/my/video`, {
      method: 'post',
      data
    });
  }
  export const getMyVideoDetails = (params: any) => {
    return http(`/rman/v1/search/my/video/details`, {
      method: 'get',
      params
    });
  }
  //目录树
  export const searchtree = (params?: any) => {
    return http('/ipingestman/area_tree', {
      method: 'GET',
      params,
    })
  }
  export const getReservationList = (params: any) => {
    return http('/ipingestman/schedule/course_loop/reservation/list', {
      method: 'GET',
      params,
    })
  }
  export const reservationAdd = (data: any) => {
    return http('/learn/resource/reservation/storage/add/or/update', {
      method: 'POST',
      data,
    })
  }
  export const reservationQuery = (params: any) => {
    return http('/learn/resource/reservation/storage/query/page', {
      method: 'GET',
      params,
    })
  }
  export const reservationBatchDelete = (data: any) => {
    return http('/learn/resource/reservation/storage/batch/delete', {
      method: 'POST',
      data,
    })
  }
  export const reservationBatchCancel = (data: any) => {
    return http('/learn/resource/reservation/storage/batch/cancel', {
      method: 'POST',
      data,
    })
  }
 
  export const getAssociationCount = (params: any) => {
    return http(`/rman/v1/association/count`, {
      method: 'get',
      params
    });
  }
  export const getAssociationPermissions = (params: any) => {
    return http(`/rman/v1/association/permissions`, {
      method: 'get',
      params
    });
  }
  export const getAssociationResource = (params: any) => {
    return http(`/rman/v1/association/resource`, {
      method: 'get',
      params
    });
  }
  export const getAssociationData = (params: any) => {
    return http(`/rman/v1/association/page`, {
      method: 'get',
      params
    });
  }
  export const updateAssociationStatus= (params: any) => {
    return http(`/rman/v1/association/status`, {
      method: 'post',
      params
    });
  }
  export const addAssociationInsert= (params: any) => {
    return http(`/rman/v1/association/insert`, {
      method: 'post',
      params
    });
  }
  export const updateScreenshotTime= (params: any) => {
    return http(`/rman/v1/entity/update/screenshot/time`, {
      method: 'post',
      params
    });
  }
    /**
   * 获取流程实例日志
   */
  export const getProcessInstanceLogs = (processInstanceId: string, headers?: any) => {
    return http('/flowbpm/bpm/task/list-by-process-instance-id', {
      method: 'GET',
      params: { processInstanceId },
    })
  }
    //学科新树形数据构造
  /**
   * 获取树形学科数据
   *
   */
  export  const getTree = (params: any) => {
    return http(`/unifiedplatform/v1/base/data/database/get/tree`, {
      method: 'get',
      params
    })
  }

  export  const getTreetheme = () => {
    return http(`/unifiedplatform/v1/base/data/database/get/tree?category_type=theme`, {
      method: 'get',
    })
  }
  export const addresourcepublish= (data: any) => {
    return http(`/rman/v1/resourcepublishaudit/addresourcepublish`, {
      method: 'post',
      data
    });
  }
  export const publishResource = (data: any) => {
    return http(`/rman/v1/resourcepublishaudit/publish/resource`, {
      method: 'post',
      data
    });
  }
  export const upperlowerframes= (data: any, params: any) => {
    // return http(`/rman/v1/resourcepublishaudit/upperlowerframes`, {
    return http(`/rman/v1/resourcepublishaudit/upperlowerresources`, {
      method: 'post',
      data,
      params
    });
  }
  export const getPublistList= (data: any, params: any) => {
    return http(`/rman/v1/resourcepublishaudit/getlist`, {
      method: 'post',
      data,
      params
    });
  }
  
  
  export const getZhSubtitles= (contendId: any) => {
    return http(`/rman/v1/search/webvtt?contentId=${contendId}&voice=true`, {
      method: 'get',
    });
  }
  export const getSubtitles= (contendId: any, lang: string) => {
    return http(`/rman/v1/search/webvtt?contentId=${contendId}&voice=true&lang=${lang}`, {
      method: 'get',
    });
  }
  
  export const getAllLanguageConfig = () => {
    return http(`/unifiedplatform/v1/translate/getAllConfig?JustEnable=true`, {
      method: 'GET',
    })
  }
  export const filetochaoxing= (data: any) => {
    return http(`/rman/v1/entity/watermark/filetochaoxing`, {
      method: 'post',
      data
    });
  }

  // 查询所有专题
  export const getAllConfig = (params: any) => {
    return http(`/learn/v1/thematic/configure/data/get/config?type=${params.type}`, {
      method: 'get',
    });
  }

  // 查询所有专题
  export const createCourseConfig = (data: any) => {
    return http(`/learn/v1/configure/data/batch/create/course/config`, {
      method: 'post',
      data
    });
  }
  export const resourceTopic = (data: any) => {
    return http(`/rman/v1/resourcepublishaudit/publish/resource/topic`, {
      method: 'post',
      data
    });
  }
  // 查询所有专题
  export const getConfigChildren = (code: any) => {
    return http(`learn/v1/teaching/course/get/course/config?courseclassification_code=${code}`, {
      method: 'get',
    });
  }
  // /rman/v1/upload/save/image
  export const createCourseimage = (data: any) => {
    return http(`/rman/v1/upload/save/image`, {
      method: 'post',
      data
    });
  }

}

export default contentListApis;