import React, { useEffect, FC } from 'react';
import { Button, message,Tooltip } from 'antd';
import uploader from '../core/uploader';
import uploadTypes from '@/types/uploadTypes';
import { useSelector, useDispatch, IUpload, useIntl, Ijurisdiction, useHistory } from 'umi';

import { IConfig } from '@/types/configTypes';
import entityApis from '@/service/entityApis';
import uploadApis from '@/service/uploadApis';

import { copyObject } from '@/utils';
import { IconFont } from '@/components/iconFont/iconFont';

interface IUpoladButtonProps {
  targetFolder?: string;
  disabled?:boolean
}

const formatMapping: { [propsName: string]: number } = {
  video: 0,
  audio: 1,
  picture: 2,
  document: 3,
  other: 4,
};
const typeMapping: { [propsName: string]: string } = {
  picture: 'biz_sobey_picture',
  video: 'biz_sobey_video',
  audio: 'biz_sobey_audio',
  document: 'biz_sobey_document',
  other:'biz_sobey_other',
};

const UploadButton: FC<IUpoladButtonProps> = props => {
  const configInst = useSelector<{ uploadConfig: IConfig }, IConfig>(
    ({ uploadConfig }) => {
      return uploadConfig;
    },
  );
  const { fields, uploadformat, taskPanls } = useSelector<{ upload: IUpload }, IUpload>(
    ({ upload }) => {
      return upload;
    },
  );
  const Secondarydirectory = useSelector<
    { jurisdiction: Ijurisdiction },
    boolean
  >(({ jurisdiction }) => {
    return jurisdiction.Secondarydirectory;
  });
  const intl = useIntl();
  const { location }: any = useHistory();
  let showVideoResouce = location?.query?.showVideoResouce || ''
  const dispatch = useDispatch();
  const openModal = (
    tasks: uploadTypes.ITask[],
    options?: uploadTypes.IUploadOptions,
  ) => {
    if (Object.keys(fields).length > 0) {
      tasks.forEach(item => {
        item.metadata.field = copyObject(fields[item.entityType]);
        item.metadata.field?.forEach(o => {
          if (o.fieldName.indexOf('name') > -1) {
            o.value = item.metadata.name;
          }
        });
        item.selected = true;
      });
    }
    dispatch({
      type: 'upload/changeTasks',
      payload: {
        value: tasks,
      },
    });
    dispatch({
      type: 'upload/setOrginTasks',
      payload: {
        value: tasks.map(item => item.file),
      },
    });

    dispatch({
      type: 'upload/changeModal',
      payload: {
        value: true,
      },
    });
  };
  const getAllFields = () => {
    const promise: Promise<any>[] = [];
    Object.keys(typeMapping).forEach(key => {
      // promise.push(entityApis.getAllFieldsByType(typeMapping[key])); //解决重复调用问题 还没搞清楚为啥原来要这么写 暂时注释
      entityApis.getAllFieldsByType(typeMapping[key]).then(res => {
        if (res && res.data && res.success) {
          res.data.forEach(item => {
            if (!item.value) {
              item.value = item.isArray ? [] : '';
            }
          });
          const value: any = {};
          value[key] = res.data;

          dispatch({
            type: 'upload/changeFields',
            payload: {
              value,
            },
          });
        }
      });
    });
  };
  /**
   * 获取上传格式
   */
  const getUploadFormat = () => {
    uploadApis.getuploadFormat().then(res => {
      if (res && res.data && res.success) {
        let temp:any = '';
        res.data.map((item:any)=>{
          temp = temp + item.allowedFormat+','
        })
        // let str = res.data.join(',');
        dispatch({
          type: 'upload/changeUploadformat',
          payload: {
            value: temp,
          },
        });
        // set entity type
        const entityTypes = configInst.entityTypes.map(item =>
          res.data[formatMapping[item.code]]
            ? {
                ...item,
                extensions: res.data[formatMapping[item.code]].allowedFormat.split(','),
              }
            : item,
        );
        dispatch({
          type: 'uploadConfig/updateEntityTypes',
          payload: entityTypes,
        });
      }
    });
  };
  useEffect(() => {
    getUploadFormat();
    getAllFields();
  }, []);
  useEffect(() => {
    if (location.query.isAdd == 'true') {
      if (fields && Object.keys(fields).length > 0) {
        upload(uploadTypes.TaskTypes.GENERAL)
      }
    }
  }, [fields])
  const upload = (type: number) => {
    (window as any).nxt = {
      config: configInst,
    };
    if (!fields || Object.keys(fields).length === 0) {
      return message.error('没有获取到元数据');
    }
    // console.log(uploadformat)
    // openModal([])
    uploader.selectFile({
      configInst,
      taskType: type,
      openModal,
      targetFolder: props.targetFolder || 'doha',
      transferType: uploadTypes.TransferTypes.WEB,
      uploadformat,
    });
  };
  useEffect(() => {
    console.log(taskPanls.length > 0 && taskPanls.every(item => item.progress === 1), 'taskPanls.length > 0 && taskPanls.every(item => item.progress === 1)')
  }, [taskPanls])
  // 判断权限来展示
  return (
    <div style={{marginLeft: '10px'}}>
      {
        props.disabled?
        <Tooltip trigger="hover" placement="left" defaultVisible={false} title="该目录暂无权限，请至个人资源上传">
          <Button
            onClick={() => upload(uploadTypes.TaskTypes.GENERAL)}
            type="primary"
            // disabled={Secondarydirectory}
            disabled={true}
          >
            <IconFont type="iconshangchuan2"></IconFont>
            {intl.formatMessage({
              id: '上传'
            })}
          </Button>  
        </Tooltip>
        :<Button
          onClick={() => upload(uploadTypes.TaskTypes.GENERAL)}
          type="primary"
          disabled={ (['global_sobey_defaultclass/public/群组资源', 'global_sobey_defaultclass/public/录播资源'].includes(props.targetFolder as string) && showVideoResouce !=='true') || !(taskPanls.length > 0 ? taskPanls.every(item => item.progress === 1) : true) }

        >
          <IconFont type="iconshangchuan2"></IconFont>
          {intl.formatMessage({
              id: '上传'
            })}
        </Button>
      }</div>
  );
};

export default UploadButton;
