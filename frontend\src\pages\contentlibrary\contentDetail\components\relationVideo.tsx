import React, {
  FC,
  useEffect,
  useRef,
  useState,
  KeyboardEvent,
  FocusEvent,
} from 'react';

import { Input, Button, message, Modal,Checkbox } from 'antd';
import _ from 'lodash';
import '../index.less';
import SmartService from '@/service/smartService';
import { useParams, useSelector,useHistory } from 'umi';
import contentListApis from '@/service/contentListApis';
import { HexToRgb } from '@/utils';

interface IData {
  detail: any;
  getQuerySequencemeta: any;
}

const RelationVideo: FC<IData> = props => {
  let history: any = useHistory();
  const params = useParams<{ contentId: string }>();
  const { detail, getQuerySequencemeta} = props;
  const hidecatalogue =
    JSON.stringify(history.location.query) === '{}'
      ? '0'
      : history.location.query.hidecatalogue;
  /**
   * 转换时间成HH:mm:ss
   * @param mss
   */
  const formatDate2HHmmss = (item: any): string => {
    if(!item.duration){
      return `00:00:00:00`
    }
    let result:number = Number((Math.floor(item.duration * 100)/1000000000));
    let h = Math.floor(result / 3600) < 10 ? '0' + Math.floor(result / 3600) : Math.floor(result / 3600);
    let m = Math.floor((result / 60 % 60)) < 10 ? '0' + Math.floor((result / 60 % 60)) : Math.floor((result / 60 % 60));
    let s = Math.floor((result % 60)) < 10 ? '0' + Math.floor((result % 60)) : Math.floor((result % 60));
    let ends = result - Number(parseInt(result+''));
    let ms  = parseInt(Math.round(ends * item.framerate)+'')
    return `${h}:${m}:${s}:${ms>9?ms:'0'+ms}`
  };
  //重新合成
  const reStartMixup = async (e: any) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('需要重新合成的对象',detail)
    const res = await contentListApis.getEntityByContentId(detail.contentId_);
    const result = res?.data.entityData.flowParameter;
    console.log(res,result);
    if(result?.beginId){ //独立合成
      if (result.colorImprove) {
        result.colorImprove = HexToRgb(result.colorImprove).toString();
      }
      let data = result.models?.map((e: any) => {
        const { saveName, inPoint, outPoint } = e;
        return { saveName: saveName, inPoint: inPoint, outPoint: outPoint };
      });
      const res = await SmartService.createProduct(
        result.contentid,
        data,
        result,
      );
      if (res?.success) {
        message.success('提交合成成功');
        getQuerySequencemeta();
      }else{
        message.success('提交合成失败');
      }
    }else{ //拼接合成
      if (result?.colorImprove) {
        result.colorImprove = HexToRgb(result.colorImprove).toString();
      }
      let res;
      if (result?.beginId || result?.endId) {
        const data = result.models.map(({ inpoint, outpoint, contentId}: any) => ({
          inPoint: inpoint,
          outPoint: outpoint,
          contentId: contentId,
        }));
        if (result.beginId) {
          data.unshift({
            contentId: result.beginId,
            inPoint: -1,
            outPoint: -1,
          });
          delete result.beginId;
        }
        if (result.endId) {
          data.push({
            contentId: result.endId,
            inPoint: -1,
            outPoint: -1,
          });
          delete result.endId;
        }
        res = await SmartService.mergeProduct2(data, result);
      } else {
        const data = result?.models?.map(({ inPoint, outPoint }: any) => ({
          inPoint: inPoint,
          outPoint: outPoint,
        }));
        res = await SmartService.mergeProduct(params.contentId, data, result);
      }
      if (res?.success) {
        message.success('提交合成成功');
        getQuerySequencemeta();
      }else{
        message.success('提交合成失败');
      }
    }
  };

  // 跳转到详情
  const todetail = (item:any) => {
    const layout = history.location.query?.layout || '0';
    if (layout === '0') {
      // 跳转路径为window.parent的路径加上childurl参数
      let src = window.parent.location.pathname + window.parent.location.hash.split('?')[0];      
      if(src.includes('/teaching/rman')){
        src = window.parent.location.origin + window.parent.location.pathname + `#/teaching/contentDetail?contentid=${item.contentId_}`
      }else if(src.includes('/teaching/contentDetail')){
        src = window.parent.location.origin + window.parent.location.pathname + `#/teaching/contentDetail?contentid=${item.contentId_}`
      }else if(src.includes('/resource')){
         src = window.parent.location.origin + window.parent.location.pathname +  `#/resource/contentDetail?contentid=${item.contentId_}`
      }else if(src.includes('/basic/contentDetail')){
        src = window.parent.location.origin + window.parent.location.pathname +  `#/basic/contentDetail/${item.contentId_}`
      } else if (src.includes("/basic/rmanDetail")) {
        const dev = process.env.NODE_ENV === 'development' ? "" : "/rman"
        src = `${window.location.origin}${dev}/#/basic/rmanDetail/${item.contentId_}`;
        // let hideBanner = history.location?.query?.hidebanner || false; // 是否显示导航栏
        // if (hideBanner) {
        //   src = src + `&hidebanner=${hideBanner}`;
        // }
      }
      window.open(src);
    }else if(layout === '1'){
      let childurl = encodeURIComponent(`/rman/#/basic/contentDetail/${item.contentId_}?layout=${layout}&hidecatalogue=${hidecatalogue?hidecatalogue:0}`);
      let src = window.parent.location.origin + window.parent.location.pathname +'#/resource?childurl='+ childurl;
      window.open(src);
    }else{
      console.log('其他情况');
    }
      // if(history.location.query.layout){
      //   path = src +`?contentid=${item.contentId_}&asr_status=${item.asr_status}&layout=${history.location.query.layout}`
      // }
      
    
  };
  //百纳秒转时分秒
  const timeFormat=(item:any)=>{
    if(!item.duration){
      return `0s`
    }
    const result = Math.floor((Number(item.duration))/10000000); //百纳秒
    let h = Math.floor(result / 3600);
    let m = Math.floor((result / 60 % 60));
    let s = Math.floor((result % 60));
    let res = '';
    if(h!=0) res += `${h}h`;
    if(m!=0) res += `${m}min`;
    res += `${s}s`;
    return res;
  }
  return (
    <div className="sequence_item" onClick={()=>todetail(detail)}>
      <div className={'sequence_item_content'}>
        <div className={(detail.status && detail.status !=='2')?'backgroundDom imgContainer':'imgContainer'}>
          <img src={detail.keyframe_ || '/rman/static/images/video.png'} 
            className={`${(detail.status && detail.status !=='2')?'opacityDom':''} ${(!detail.keyframe_ || detail.keyframe_.includes('/rman/static/images'))?'imgScale':''}`}
            />
        </div>
        <span className='name_' title={detail.name_}>{detail.name_}</span>
        {/* {detail.duration &&
          <span className='timeSpan'>
            <span>
              {timeFormat(detail || 0)}
            </span>
          </span>
        } */}
        {
          (detail.status && detail.status !=='2')&& //没合成完成
          <>
            {
              detail.status === '-1' ?
              <div  className='mixup_container_failed'>
                <img src='/rman/static/images/mixfailed.png'/>
                <span>合成失败</span>
                <a onClick={reStartMixup}>点击重新合成</a>
              </div> 
              :
              <div  className='mixup_container_doing'>
                <img src='/rman/static/images/mixing.png'/>
                <span>合成中</span>
              </div>
            }
          </>
        }
      </div>
    </div>
  );
};
export default RelationVideo;
