.login-container {
  height: 100vh;
  position: relative;

  .login-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .login-main {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .login-top {
    height: 10px;
    background-color: #c1c3c3;

    > div {
      width: 65%;
      height: 100%;
      background-color: var(--second-color);
    }
  }

  .login-bottom {
    width: 400px;
    background-color: rgba(255, 255, 255, .14);
    border: 1px solid #ececec;
    padding: 50px 30px;
    box-shadow: 0 0 26px 0 rgba(0, 0, 0, 0.41);
    z-index: 10;

    > .logo-title-wrapper {
      text-align: center;
      margin-bottom: 36px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      img {
        height: 36px;
        margin-bottom: 12px;
      }

      h1 {
        font-size: 20px;
        color: #515151;
      }
    }
  }
}

.login-logo {
  position: absolute;
  left: 86px;
  top: 72px;
  width: 285px;
}

.login-container .login-bottoms {
  border-radius: 5px;
  background-color: var(--second-color);
  color: #fff;

  &:hover {
    background-color: var(--second-color);
    color: #fff;
  }

  &:active {
    background: var(--second-color);
    border-color: var(--second-color);
    color: #fff;
  }
}


.login-input {
  &:hover {

  }

  &:focus {
    box-shadow: 0px 3px 10px 4px rgba(186, 42, 23, 0.2);
  }
}
