
import React, { FC, useState, useEffect, useRef, useMemo } from 'react';
import ReactEcharts from 'echarts-for-react';
// import 'echarts-liquidfill';
import 'echarts-wordcloud';
import './index.less'
import { byteTransfer } from '@/utils';
import {  useIntl } from 'umi';

interface IProps {
  flag: string;
  dataList?: Array<DataItem>;
  storage?: any;
  college?: any;
  result?: any;
  total?: any;
  style?: string;
  scaleFlag?: any;
  special?: any;
  clickCallback?: any;
  zoomData?: boolean;
}
interface DataItem {
  name: any,
  value: any,
  itemStyle?:any
}
const EchartsGroup: FC<IProps> = ({ dataList, flag, storage, total, special,college,result,clickCallback,scaleFlag,zoomData }) => {
  const [option, setOption] = useState<any>()
  const echartRef = useRef<any>()
  const intl = useIntl();
  useEffect(() => {
    // console.log('storage',storage)
    setOption(getOption())
  }, [dataList,result])
  useEffect(() => {
    window.addEventListener('resize', () => {

    })
  }, [])
  const getOption = () => {
    let option;
    
    if (flag === 'pie') { //饼状图
      option = {
        tooltip: {
          trigger: 'item',
          // formatter: "{b}: {c} ({d}%)"
          formatter: function(data:any,x:any,y:any){
            if(special){
              const temp = JSON.parse(data.data.name);
              const percent = `<span color=${temp.itemStyle.color}>${temp.percent}</span>`; 
              // return '{name|' + temp.name + '}' +' '+'{value|' + temp.value + '}'+' '+'{percent|' + temp.percent + '}'
              return temp.name +' '+ temp.value + ' '+ temp.percent
            }else{
              return data.name +': '+ data.value + ' ('+ data.percent+'%)'
            }
          },
        },
        color: ["#27D9C8", "#D8D8D8"],
        legend: {
          top: '10%',
          // right: special?'10':'0%',
          left:special?'52%':'70%',
          // right:special?'0%':'',
          // left:'right',
          type:'scroll',
          orient: 'vertical',
          icon: 'circle',
          // textStyle:function(data:any){
          //   debugger
          // },
          formatter: function(data:any,x:any,y:any){
            if(special){
              const temp = JSON.parse(data);
              const name = temp.name.split(',')[0];
              // return '{name|' + temp.name + '}' +' '+'{value|' + temp.value + '}'+' '+'{percent|' + temp.percent + '}'
              return name.length>12?(name.substring(0,12)+'...'):name +' '+ temp.value + ' '+ temp.percent
            }else{
              return data
            }
          },
          rich: {
            name: {
              color:'red',
            }, 
            value: {
              color:'blue',
            },
            percent: {
              color:'yellow'
            },
          }
        },
        title: {
          text: total?.value, //圆环中心
          left: (total?.value?.length) < 3 ? '32%': (total?.value?.length) < 4 ? "30%" : (total?.value?.length) < 5 ? "29%":(total?.value?.length) < 6? "28%": "25%",
          // left:'35%',
          top: "50%",
          textStyle: {
            color: "#72bbff",
            fontSize: scaleFlag?12:20,
            align: "center"
          }
        },
        graphic: {
          type: "text",
          left: "32%",
          top: "40%",
          style: {
            text: total?.name,
            textAlign: "center",
            fill: "#33333380",
            fontSize: scaleFlag?12:14,
            fontWeight: 700
          }
        },
        series: [
          {
            // name: '运动情况',
            type: 'pie',
            radius: ['40%', '70%'],
            center: [special?'30%':'40%', '50%'],
            avoidLabelOverlap: false,
            label: {
              normal: {
                show: true,
                position: 'inner',
                textStyle: {
                  fontWeight: 200,
                  fontSize: 12,  //文字的字体大小
                  color: '#fff'
                },
                // formatter: '{d}%',/*饼状图内显示百分比*/
                formatter: function(data:any){
                  return data.percent.toFixed(0)+'%';
                },
              },
            },
            data: dataList
          }
        ]
      };
    } else if (flag === 'line') { //折线图
      option = {
        // title: {
        //   text: 'Stacked Line'
        // },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: result?.y?.map((item:any)=>item.name),
          textStyle: {
            fontSize: scaleFlag? 10 : 14
          },
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '3%',
          top:'10%',
          containLabel: true
        },
        toolbox: {
          show: false,
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: result?.x,
          axisLabel:{
            // interval: 0,
            // rotate:36,
            // formatter: function (value:any) {
            //   if (value.length > 6) {
            //    value = value.substring(0, 5) + "..";
            //   }
            //   return value;
            // },
          },
        },
        yAxis: {
          type: 'value'
        },
        series:result?.y
      };
    } else if (flag === 'ball') {  //水球
      // console.log('storage', storage)
      option = {
        // tooltip: {
        //   trigger: 'item',
        //   formatter: "{a} <br/>{b}: {c} ({d}%)"
        // },
        series: [{
          name: intl.formatMessage({ id: '资源占用空间' }),
          type: 'liquidFill',
          radius: '95%',
          label: {
            show: true,
            position: 'inside',
            formatter: function (item: any) {
              //传过来的默认是GB
              const userSize = byteTransfer(Number(storage?.userSize.toFixed(1))*1024*1024*1024);
              const active = isNaN(item.value) ? 0 :(Number(item.value) * 100).toFixed(1) ;
              return '{name|' + ''+intl.formatMessage({ id: '已使用' })+'' + '}' + '\n\r' + '{total2|' + userSize + '}' + '\n\r' + '{active|'+ active+ '%}'
              // return '{total2|' + storage?.userSize.toFixed(2) + 'GB}' + '\n\r' + '{active|'+item.value * 100 + '%}'
            },
            rich: {
              name: {
                fontSize: scaleFlag?12:14,
                fontFamily: "微软雅黑",
                color:'#76A1F9',
                marginTop:20
              }, total2: {
                fontSize: scaleFlag ? 18 : 25,
                fontFamily: "微软雅黑",
                color:'#76A1F9',
                marginTop:40
              },
              active: {
                // fontFamily: "微软雅黑",
                fontSize: scaleFlag ? 18 : 38,
                color:'#76A1F9'
              },
            }
          },
          waveLength: '105',
          amplitude: '5',
          // shape: 'path://M367.855,428.202c-3.674-1.385-7.452-1.966-11.146-1.794c0.659-2.922,0.844-5.85,0.58-8.719 c-0.937-10.407-7.663-19.864-18.063-23.834c-10.697-4.043-22.298-1.168-29.902,6.403c3.015,0.026,6.074,0.594,9.035,1.728 c13.626,5.151,20.465,20.379,15.32,34.004c-1.905,5.02-5.177,9.115-9.22,12.05c-6.951,4.992-16.19,6.536-24.777,3.271 c-13.625-5.137-20.471-20.371-15.32-34.004c0.673-1.768,1.523-3.423,2.526-4.992h-0.014c0,0,0,0,0,0.014 c4.386-6.853,8.145-14.279,11.146-22.187c23.294-61.505-7.689-130.278-69.215-153.579c-61.532-23.293-130.279,7.69-153.579,69.202 c-6.371,16.785-8.679,34.097-7.426,50.901c0.026,0.554,0.079,1.121,0.132,1.688c4.973,57.107,41.767,109.148,98.945,130.793 c58.162,22.008,121.303,6.529,162.839-34.465c7.103-6.893,17.826-9.444,27.679-5.719c11.858,4.491,18.565,16.6,16.719,28.643 c4.438-3.126,8.033-7.564,10.117-13.045C389.751,449.992,382.411,433.709,367.855,428.202z',
          outline: {
            show: false
          },
          color:['#9dc0fb','#79a3f9'],
          phase: 10,
          backgroundStyle: {
            borderColor: '#156ACF',
            // color: '#ff9800', //背景颜色
            // background: 'linear-gradient(180deg, #AECFFD 0%, #76A1F9 100%)',
            borderWidth: 1,
            // shadowColor: 'rgba(0, 0, 0, 0.4)',
            // shadowBlur: 20
          },
          data: [(storage?.userSize / storage?.totalSize)]
        }]
      };
    } else if (flag === 'rose') { //玫瑰图
      option = {
        tooltip: {
          trigger: 'item',
          formatter: "{b}: {c} ({d}%)"
        },
        // legend: {
        //   top: '18%',
        //   left: style ? style : 'left',
        //   orient: 'vertical',
        //   icon: 'circle'
        // },
        toolbox: {
          show: false,
          feature: {
            mark: { show: true },
            dataView: { show: true, readOnly: false },
            restore: { show: true },
            saveAsImage: { show: true }
          }
        },
        series: [
          {
            label: {
              show: true
            },
            name: '',
            type: 'pie',
            radius: [0, 80],
            center: ['50%', '50%'],
            roseType: 'area',
            itemStyle: {
              borderRadius: 8
            },
            data: dataList
          }
        ]
      };
    } else if (flag === 'column') { //柱状图
      option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '1%',
          right: '1%',
          bottom: '10%',
          top:'5%',
          containLabel: true
        },
        dataZoom:zoomData?[  // 有滚动条 平移
          {
              type: 'slider',
              realtime: true,
              start: 0,
              end: college?.xAxis?.length>22?(2200/college?.xAxis?.length).toFixed(0):100,  // 初始展示20%
              height: 4,
              fillerColor: "rgba(17, 100, 210, 0.42)", // 滚动条颜色
              borderColor: "rgba(17, 100, 210, 0.12)", 
              handleSize:0, // 两边手柄尺寸
              showDetail: false, // 拖拽时是否展示滚动条两侧的文字
              top:'96%',
              
              // zoomLock:true, // 是否只平移不缩放
              // moveOnMouseMove:true, //鼠标移动能触发数据窗口平移
              // zoomOnMouseWheel :true, //鼠标移动能触发数据窗口缩放
          },
          { 
            type: "inside",  // 支持内部鼠标滚动平移
            start: 0,
            end: 10,
            zoomOnMouseWheel: false,  // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true  // 鼠标移动能触发数据窗口平移 
          }
      ]:[],
        xAxis: [
          {
            type: 'category',
            data: college?.xAxis,
            axisLabel:{
              interval: 0,
              // rotate:36,
              fontSize: scaleFlag ? 10 : 12,
              formatter: function (value:any) {
                let newName = '';
                const valueLenth = value.length;
                const provideNumber = 3 //每行能显示的字的个数；
                // const row = Math.ceil(valueLenth / provideNumber)//换行的话，需要显示几行，向上取整
                const row = 2 //最多显示两行；
                if (valueLenth > provideNumber) {
                  for(let p=0; p<row; p++){
                    let tempstr = '';
                    const start = p* provideNumber;
                    const end = start + provideNumber;
                    if(p ===row - 1 ){
                      //最后一次不换行
                      // tempstr = value.substring(start,valueLenth);
                      tempstr = value.substring(start,6)+`${valueLenth>6?'..':''}`;

                    }else{
                      tempstr = value.substring(start,end) + '\n';
                    }
                    newName += tempstr;
                  }
                }else{
                  newName = value
                }
                return newName;
              },
            },
            axisTick: {
              alignWithLabel: true
            }
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            name: college?.tips || '资源个数',
            type: 'bar',
            barWidth: '50%',
            color:'#7EA2FF',
            data: college?.yAxis
          }
        ]
      };
    }else if(flag === 'wordcloud'){  //词云
      option = {
        series: [
          {
            type: 'wordCloud',
            gridSize: 8,// 网格大小，值越大，词语越密集
            sizeRange: [4, 48],
            rotationRange: [0, 90],
            rotationStep:90, // 旋转角度
            keepAspectRatio: true, // 是否保持长宽比
            // left:0,
            // top:0,
            // left: 'center',
            // top: 'center',
            // right: null,
            // bottom: null,
            shape: 'pentagon',  //ellipse 椭圆  circle 圆形  triangle-forward 三角形 rectangle 长方形  triangle 三角形  diamond 菱形  pentagon 五角形  star 星形
            drawOutOfBound: false, // 允许词太大的时候，超出画布的范围
            layoutAnimation: true, // 是否开启动画
            // shape: 'line',
            width: '95%',
            height: '96%',
            textStyle: {
              color: function() {
                return (
                  'rgb(' +
                  [
                    Math.round(Math.random() * 255),
                    Math.round(Math.random() * 255),
                    Math.round(Math.random() * 255),
                  ].join(',') +
                  ')'
                );
              },
              normal: {
              },
              emphasis: {
                shadowBlur: 10,
                shadowColor: '#333',
              },
            },
            data: dataList,
          },
        ],
      }
    }
    return option
  }

  return (
    <div className={`circleChart_statistisc`} >
      {/*memo在部分浏览器好像没用还是会疯狂重复渲染  只好采取useMemo了 */}
      {(() => (
          useMemo(
            () => (
              <ReactEcharts ref={echartRef} onEvents={clickCallback} option={option ? option : {}} style={{ width: '100%', height: '100%' }} className={''} />
            ),
            [option],
          )))()}
      
    </div>
  )
}

export default React.memo(EchartsGroup)