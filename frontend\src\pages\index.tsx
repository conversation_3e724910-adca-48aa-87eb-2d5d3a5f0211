import React, { useEffect, useState, useMemo } from 'react';
import { useIntl, setLocale, getLocale, useRequest } from 'umi';
import { connect } from 'umi';
import Loading from '@/components/loading/loading';
import './index.less';
import contentListApis from '@/service/contentListApis';

const Home = (props: any) => {
  const [count, setCount] = useState(0);
  const [name, setName] = useState('name');

  const changeCount = () => {
    setCount(count + 1);
  };

  return (
    <div>
      <button onClick={changeCount}>+</button>
      {count}
      <Loading />
    </div>
  );
};

export default connect(({ config }: any) => {
  return config;
})(Home);
