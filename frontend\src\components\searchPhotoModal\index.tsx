import React, { useState, useEffect, useRef } from 'react';
import { Modal, Select, Upload, Input, Divider, message, Spin } from 'antd';
import {
    Player,
} from '@/components';
import { copyObject, asyncLoadScript, getGuid } from '@/utils';
import uploadApi from '@/service/uploadApis';
import WebUploader from './WebUploader';
import _ from 'lodash';
import './index.less'
import { useIntl, useSelector, useDispatch, useHistory } from 'umi';
const { Dragger } = Upload;
const { Search } = Input;
interface searchPhotoModalProps {
    visible: boolean;
    onClose: () => void;
    folderPath: string;
    searchFolderList?: (ids: any, path?: any) => void;
    setPhotoPath?: (path: any) => void;
    setPeopleIds?: (path: any) => void;
    setCurrent?: (current: number) => void;
}
const searchPhotoModal: React.FC<searchPhotoModalProps> = ({ visible, folderPath, onClose, searchFolderList, setPhotoPath, setPeopleIds, setCurrent }) => {
    const intl = useIntl();
    const [loading, setLoading] = useState<boolean>(false)
    const {
        taskPanls,
        fields,
    } = useSelector<{ upload: IUpload }, IUpload>(({ upload }) => {
        return upload;
    });
    const stateRef = useRef<any>()
    const history = useHistory();
    useEffect(() => {
        stateRef.current = taskPanls
    }, [taskPanls])
    const dispatch = useDispatch();
    useEffect(() => {
        asyncLoadScript('/rman/libs/webuploader/webuploader.js');
    }, [])
    const draggerConfig = {
        name: 'file',
        multiple: false,
        maxCount: 1,
        showUploadList: false,
        accept: '.jpg, .png, .jpeg',
        onChange(info: any) {
            const isJpgOrPng = info.file.type === 'image/png' || info.file.type === 'image/jpeg';
            if (!isJpgOrPng) {
                message.error('暂不支持该格式图片检索，请重新上传')
                return
            }
            uploadFile(info.fileList)
        },
        beforeUpload: (file) => {
            return true;
        },
        className: 'dragger',
        // ...uploadProps,
    };

    const uploadFile = async (files) => {
        setLoading(true)
        const newTaskPanls: any = copyObject(taskPanls);
        let showFlag = false;
        //获取每一个上传对象的存储方式 start
        const param = _.map(files, (item) => ({
            fileLength: item.size,
            fileName: item.name,
            fileType: item.type.includes("image")
                ? "picture"
                : item.type === "video"
                    ? "video"
                    : "other",
            poolType: window.localStorage.getItem('upform_platform') === 'Lark' ? 'ROLE' : '',
            // isPrivate:props.targetFolder.includes('global_sobey_defaultclass/private') //v3 暂时弃用
            pathType: 1
        }));
        const storageConfig: any = await uploadApi.storageConfig(param);
        if (!storageConfig?.success) return;
        //获取每一个上传对象的存储方式 end
        for (let index = 0; index < files.length; index++) {
            const item: any = files[index];
            item.uid = getGuid()
            const storage_: any = storageConfig.data[index];
            const metadataTypes = fields[item.type.includes("image")
                ? "picture"
                : item.type === "video"
                    ? "video"
                    : "other"];

            const uploadMetas = _.map(metadataTypes, (item) => ({
                ...item,
                value:
                    item.fieldName === "name" || item.fieldName === "name_"
                        ? files[index].name.substring(0, files[index].name.lastIndexOf("."))
                        : item.value,
            }));

            //判断存储方式
            if (!storage_.access_type) continue;
            if (storage_.access_type === 'NAS') {
                showFlag = true;
                const file = new (window as any).WebUploader.Lib.File(
                    (window as any).WebUploader.Base.guid(),
                    // orginTasks[index],
                    item.originFileObj
                    // item.file.originFileObj?.size || item.file //兼容拖拽有originFileObj的bug
                );
                file.guid = (window as any).WebUploader.Base.guid();
                file.metadata = uploadMetas;
                file.folderPath = folderPath;
                file.fileGuid = storage_.path;//针对同名文件增加不同标识
                console.log('NAS', file)
                // @ts-ignore
                const webUploader = new WebUploader({
                    uploadError,
                    uploadComplete,
                    uploadProgress
                });
                webUploader.addFiles(file);
                const files = webUploader.getFiles();
                files.forEach((item: any) => {
                    if (
                        newTaskPanls.filter(i => i.guid === item.source.guid).length === 0
                    ) {
                        newTaskPanls.push({
                            uploader: webUploader,
                            name: item.name,
                            size: item.size,
                            status: 0,
                            progress: 0,
                            index,
                            guid: item.source.guid,
                            uploading: false,
                            pause: false,
                        });
                    }
                });
                dispatch({
                    type: 'upload/setTaskPanls',
                    payload: {
                        value: newTaskPanls,
                    },
                });
            }
        }
    };
    const uploadError = (file: any) => {
        dispatch({
            type: 'upload/errorTask',
            payload: {
                value: file.source.guid,
            },
        });
        message.error(
            file.name +
            intl.formatMessage({
                id: 'upload-error',
                defaultMessage: '上传失败',
            }),
        );
    };
    const uploadDefault = ()=>{
        console.log('阻止upload的默认上传')
        return true
    }
    const uploadProgress = (file: any, progress: number) => {
        const p = progress >= 1 ? 0.999 : progress;
        dispatch({
            type: 'upload/updateTaskPanls',
            payload: {
                value: { guid: file.source.guid, progress: p },
            },
        });
    };
    const uploadComplete = (file: any, newWebUploader: any) => {
        uploadApi.filemerge(file.source.guid, file.name, file.source.fileGuid).then(res => {
            if (res?.success) {
                pollMergeStatus(file, newWebUploader);
            } else {
                uploadError(file);
            }
        });
    };
    const onSearch = (value: string) => {
        setLoading(true)
        uploadApi
            .initiateFaceImage(value)
            .then(dd => {
                console.log(dd, 'dd')
                if (dd && dd.data && dd.success) {
                    let count = 0
                    const timer = setInterval(async () => {
                        const res1 = await uploadApi.getFaceImage(dd.data.data.guid)
                        if (res1?.success && res1.data?.code === 0) {
                            setLoading(false)
                            const ids = res1.data?.data?.peopleIds || []
                            if (history.location.pathname.indexOf('similarityGraph') === -1) {
                                history.push({
                                    pathname: '/basic/similarityGraph',
                                    query: {
                                        path: value,
                                        ids,
                                        folderPath
                                    }
                                })
                            }
                            else {
                                onClose()
                                clearInterval(timer);
                                searchFolderList && searchFolderList(ids);
                                setPeopleIds && setPeopleIds(ids)
                                setCurrent && setCurrent(1)
                            }
                        }
                        if (count === 60 || res1?.data.code === 10000) {
                            clearInterval(timer);
                            if (history.location.pathname.indexOf('similarityGraph') === -1) {
                                history.push({
                                    pathname: '/basic/similarityGraph',
                                    query: {
                                        path: value,
                                        ids: [],
                                        folderPath
                                    }
                                })
                            }
                            else {
                                onClose()
                                clearInterval(timer);
                                searchFolderList && searchFolderList([]);
                                setCurrent && setCurrent(1)
                            }
                        }
                        count++
                    }, 1000)
                } else {
                    onClose()
                    setLoading(false)
                }
            })
    }
    // const getResult =
    /**
* 轮询合并状态
* @param file
* @param newWebUploader
*/
    const pollMergeStatus = async (file: any, newWebUploader: any) => {
        const res = await uploadApi.fetchMergeStatus(file.source.guid);
        if (res?.data?.state === 1 && res.data.finalFilePath) {
            setPhotoPath && setPhotoPath(res.data.finalFilePath)
            uploadApi
                .initiateFaceImage(res.data.finalFilePath)
                .then(dd => {
                    if (dd && dd.data && dd.success) {
                        let count = 0
                        const timer = setInterval(async () => {
                            const res1 = await uploadApi.getFaceImage(dd.data.data.guid)
                            if (res1?.success && res1.data?.code === 0) {
                                setLoading(false)
                                const ids = res1.data?.data?.peopleIds || []
                                if (history.location.pathname.indexOf('similarityGraph') === -1) {
                                    history.push({
                                        pathname: '/basic/similarityGraph',
                                        query: {
                                            path: res?.data?.finalFilePath,
                                            ids,
                                            folderPath
                                        }
                                    })
                                }
                                else {
                                    onClose()
                                    clearInterval(timer);
                                    searchFolderList && searchFolderList(ids);
                                    setPeopleIds && setPeopleIds(ids)
                                    setCurrent && setCurrent(1)
                                }
                            }
                            if (count === 60 || res1?.data.code === 10000) {
                                clearInterval(timer);
                                if (history.location.pathname.indexOf('similarityGraph') === -1) {
                                    history.push({
                                        pathname: '/basic/similarityGraph',
                                        query: {
                                            path: res?.data?.finalFilePath,
                                            ids: [],
                                            folderPath
                                        }
                                    })
                                }
                                else {
                                    onClose()
                                    clearInterval(timer);
                                    searchFolderList && searchFolderList([]);
                                    setPeopleIds && setPeopleIds([])
                                    setCurrent && setCurrent(1)
                                }
                            }
                            count++
                        }, 1000)
                        dispatch({
                            type: 'upload/updateTaskPanls',
                            payload: {
                                value: { guid: file.source.guid, progress: 1 },
                            },
                        });

                        // 从队列中删除
                        newWebUploader.removeFile(file, true);

                        // 删除task
                        // 弹窗提示
                        // message.success(
                        //     file.name +
                        //     intl.formatMessage({
                        //         id: 'upload-success',
                        //         defaultMessage: '上传成功',
                        //     }),
                        // );
                    } else {
                        onClose()
                        uploadError(file);
                    }
                });
        } else if (res?.data?.state === 0) {
            // 手动移除掉的任务 停止轮询
            const realTaskPanls = stateRef.current;
            if (realTaskPanls.some((item: any) => item.guid === file.source.guid)) {
                setTimeout(() => {
                    pollMergeStatus(file, newWebUploader);
                }, 500);
            }
        } else if (res?.data?.state === -1 && res?.data.errorMsg) {
            message.error(res?.data.errorMsg);
            uploadError(file);
        } else {
            uploadError(file);
        }
    };
    return (
        <Modal
            title={intl.formatMessage({ id: '以图搜图' })}
            visible={visible}
            footer={null}
            maskClosable={false}
            onCancel={() => {
                onClose()
                setLoading(false)
            }}
            className='searchPhotoModal'
            width={500}
        >
            <div className="content">
                {
                    loading ? <div className="spin">
                        <Spin tip="正在上传...">
                        </Spin>
                    </div> : <div className="upload">
                        <Dragger {...draggerConfig} customRequest={uploadDefault}>
                            <p className="ant-upload-drag-icon">
                                {/* <CloudUploadOutlined /> */}
                            </p>
                            <div className='uploadText'>
                                <img src={require('@/images/contentlibrary/drag_drop_illustration.svg')} alt="" />
                                <div className='strong'>
                                    将图片拖到此处，或者上传文件
                                    <p>请上传jpg、png格式图片</p>
                                </div>
                            </div>
                        </Dragger>
                        <Divider>或</Divider>
                        <Search
                            placeholder="请粘贴图片的链接"
                            allowClear
                            enterButton="搜索"
                            size="large"
                            onSearch={onSearch}
                        />
                    </div>
                }
            </div>

        </Modal >
    );
};

export default searchPhotoModal;