!function(e){function t(o){if(n[o])return n[o].exports;var r=n[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,t),r.l=!0,r.exports}var n={};t.m=e,t.c=n,t.d=function(e,n,o){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:o})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="/dist/",t(t.s=102)}([function(e,t){var n=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},function(e,t,n){var o=n(29)("wks"),r=n(18),a=n(0).Symbol,i="function"==typeof a;(e.exports=function(e){return o[e]||(o[e]=i&&a[e]||(i?a:r)("Symbol."+e))}).store=o},function(e,t){var n=e.exports={version:"2.6.12"};"number"==typeof __e&&(__e=n)},function(e,t,n){var o=n(6);e.exports=function(e){if(!o(e))throw TypeError(e+" is not an object!");return e}},function(e,t,n){var o=n(5),r=n(17);e.exports=n(7)?function(e,t,n){return o.f(e,t,r(1,n))}:function(e,t,n){return e[t]=n,e}},function(e,t,n){var o=n(3),r=n(39),a=n(26),i=Object.defineProperty;t.f=n(7)?Object.defineProperty:function(e,t,n){if(o(e),t=a(t,!0),o(n),r)try{return i(e,t,n)}catch(e){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(e[t]=n.value),e}},function(e,t){e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},function(e,t,n){e.exports=!n(16)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},function(e,t){var n={}.hasOwnProperty;e.exports=function(e,t){return n.call(e,t)}},function(e,t,n){var o=n(59),r=n(24);e.exports=function(e){return o(r(e))}},function(e,t){e.exports=!0},function(e,t,n){var o=n(0),r=n(2),a=n(14),i=n(4),s=n(8),l=function(e,t,n){var u,f,c,p=e&l.F,d=e&l.G,h=e&l.S,m=e&l.P,g=e&l.B,b=e&l.W,y=d?r:r[t]||(r[t]={}),v=y.prototype,x=d?o:h?o[t]:(o[t]||{}).prototype;d&&(n=t);for(u in n)(f=!p&&x&&void 0!==x[u])&&s(y,u)||(c=f?x[u]:n[u],y[u]=d&&"function"!=typeof x[u]?n[u]:g&&f?a(c,o):b&&x[u]==c?function(e){var t=function(t,n,o){if(this instanceof e){switch(arguments.length){case 0:return new e;case 1:return new e(t);case 2:return new e(t,n)}return new e(t,n,o)}return e.apply(this,arguments)};return t.prototype=e.prototype,t}(c):m&&"function"==typeof c?a(Function.call,c):c,m&&((y.virtual||(y.virtual={}))[u]=c,e&l.R&&v&&!v[u]&&i(v,u,c)))};l.F=1,l.G=2,l.S=4,l.P=8,l.B=16,l.W=32,l.U=64,l.R=128,e.exports=l},function(e,t){e.exports={}},function(e,t){var n={}.toString;e.exports=function(e){return n.call(e).slice(8,-1)}},function(e,t,n){var o=n(15);e.exports=function(e,t,n){if(o(e),void 0===t)return e;switch(n){case 1:return function(n){return e.call(t,n)};case 2:return function(n,o){return e.call(t,n,o)};case 3:return function(n,o,r){return e.call(t,n,o,r)}}return function(){return e.apply(t,arguments)}}},function(e,t){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},function(e,t){e.exports=function(e){try{return!!e()}catch(e){return!0}}},function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},function(e,t){var n=0,o=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++n+o).toString(36))}},function(e,t,n){var o=n(5).f,r=n(8),a=n(1)("toStringTag");e.exports=function(e,t,n){e&&!r(e=n?e:e.prototype,a)&&o(e,a,{configurable:!0,value:t})}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(54),r=function(e){return e&&e.__esModule?e:{default:e}}(o),a={asyncLoadedScripts:{},asyncLoadedScriptsCallbackQueue:{},getScriptDomFromUrl:function(e){var t;return/.+\.js$/.test(e)?(t=document.createElement("SCRIPT"),t.setAttribute("type","text/javascript"),t.setAttribute("src",e)):/.+\.css$/.test(e)&&(t=document.createElement("link"),t.href=e,t.type="text/css",t.rel="stylesheet"),t},asyncLoadScript:function(e,t){var n=a;if(void 0!=n.asyncLoadedScripts[e])return void(t&&"function"==typeof t&&(0==n.asyncLoadedScripts[e]?(n.asyncLoadedScriptsCallbackQueue[e]||(n.asyncLoadedScriptsCallbackQueue[e]=[]),n.asyncLoadedScriptsCallbackQueue[e].push(t)):t.apply(n,[])));n.asyncLoadedScripts[e]=0;var o=n.getScriptDomFromUrl(e);o.readyState?o.onreadystatechange=function(){if(("loaded"==o.readyState||"complete"==o.readyState)&&(o.onreadystatechange=null,n.asyncLoadedScripts[e]=1,t&&"function"==typeof t&&t.apply(n,[]),n.asyncLoadedScriptsCallbackQueue[e])){for(var r=0,a=n.asyncLoadedScriptsCallbackQueue[e].length;r<a;r++)n.asyncLoadedScriptsCallbackQueue[e][r].apply(n,[]);n.asyncLoadedScriptsCallbackQueue[e]=void 0}}:o.onload=function(){if(n.asyncLoadedScripts[e]=1,t&&"function"==typeof t&&t.apply(n,[]),n.asyncLoadedScriptsCallbackQueue[e]){for(var o=0,r=n.asyncLoadedScriptsCallbackQueue[e].length;o<r;o++)n.asyncLoadedScriptsCallbackQueue[e][o].apply(n,[]);n.asyncLoadedScriptsCallbackQueue[e]=void 0}},document.getElementsByTagName("head")[0].appendChild(o)},getFileNameFromUrl:function(e){return e.substring(e.lastIndexOf("/")+1,e.length)},isIncludeScript:function(e){for(var t=/js$/i.test(e),n=document.getElementsByTagName(t?"script":"link"),o=0;o<n.length;o++)if(-1!=n[o][t?"src":"href"].indexOf(e))return!0;return!1},loadScripts:function(e){if(e instanceof Array){for(var t=[],n=0;n<e.length;n++)t.push(new r.default(function(t,o){a.isIncludeScript(a.getFileNameFromUrl(e[n]))?t():a.asyncLoadScript(e[n],function(){t()})}));return r.default.all(t)}return new r.default(function(e,t){e()})},getExtensions:function(e){if(!e)return"";for(var t=e.length;0!=t;t--)if("."==e[t])return e.substring(t,e.length);return""},getExtension:function(e){var t=a.getExtensions(e);return t.length>0?t.substring(1,t.length).toLowerCase():""},getFileName:function(e){for(var t=e.length,n=e.length;0!=n;n--)if("."!=e[n]||t!=e.length){if("\\"==e[n])return e.substring(n+1,t)}else t=n;return e.substring(0,t)},getFullFileName:function(e){for(var t=e.length;0!=t;t--)if("\\"==e[t])return e.substring(t+1,e.length);return e},getTypeByExt:function(e,t){e=e.toLowerCase(),0!==e.indexOf(".")&&(e="."+e);var n=_.get(window,"nxt.config.entityTypes",[]);t&&0===n.length&&(n=_.get(t,"entityTypes",[]));for(var o=0;o<n.length;o++)if(n[o].extensions&&-1!=n[o].extensions.indexOf(e))return n[o];return _.find(n,{code:"other"})},formatSize:function(e,t,n){var o;for(n=n||["B","KB","MB","GB","TB"];(o=n.shift())&&e>1024;)e/=1024;return("B"===o?e:e.toFixed(t||2))+" "+o},prompt:function(e){mam&&mam.prompt&&mam.prompt(e)},msgOk:function(e){mam&&mam.message&&mam.message.ok&&mam.message.ok(e)},addUrlParam:function(e,t){return e+=(-1!==e.indexOf("?")?"&":"?")+t},getCookie:function(e){for(var t=e+"=",n=document.cookie.split(";"),o=0;o<n.length;o++){var r=n[o].trim();if(0==r.indexOf(t))return r.substring(t.length,r.length)}return""}};t.default=a},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o={};t.default=o},function(e,t,n){e.exports={default:n(80),__esModule:!0}},function(e,t){var n=Math.ceil,o=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?o:n)(e)}},function(e,t){e.exports=function(e){if(void 0==e)throw TypeError("Can't call method on  "+e);return e}},function(e,t,n){var o=n(6),r=n(0).document,a=o(r)&&o(r.createElement);e.exports=function(e){return a?r.createElement(e):{}}},function(e,t,n){var o=n(6);e.exports=function(e,t){if(!o(e))return e;var n,r;if(t&&"function"==typeof(n=e.toString)&&!o(r=n.call(e)))return r;if("function"==typeof(n=e.valueOf)&&!o(r=n.call(e)))return r;if(!t&&"function"==typeof(n=e.toString)&&!o(r=n.call(e)))return r;throw TypeError("Can't convert object to primitive value")}},function(e,t,n){var o=n(42),r=n(30);e.exports=Object.keys||function(e){return o(e,r)}},function(e,t,n){var o=n(29)("keys"),r=n(18);e.exports=function(e){return o[e]||(o[e]=r(e))}},function(e,t,n){var o=n(2),r=n(0),a=r["__core-js_shared__"]||(r["__core-js_shared__"]={});(e.exports=function(e,t){return a[e]||(a[e]=void 0!==t?t:{})})("versions",[]).push({version:o.version,mode:n(10)?"pure":"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})},function(e,t){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(e,t,n){"use strict";function o(e){var t,n;this.promise=new e(function(e,o){if(void 0!==t||void 0!==n)throw TypeError("Bad Promise constructor");t=e,n=o}),this.resolve=r(t),this.reject=r(n)}var r=n(15);e.exports.f=function(e){return new o(e)}},function(e,t,n){t.f=n(1)},function(e,t,n){var o=n(0),r=n(2),a=n(10),i=n(32),s=n(5).f;e.exports=function(e){var t=r.Symbol||(r.Symbol=a?{}:o.Symbol||{});"_"==e.charAt(0)||e in t||s(t,e,{value:i.f(e)})}},function(e,t){t.f={}.propertyIsEnumerable},function(e,t,n){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0});var r=n(22),a=o(r),i=n(20),s=o(i),u=function(e,t){t=t.toLowerCase().replace(/[\[\]]/g,"\\$&");var n=new RegExp("[?&]"+t+"(=([^&#]*)|&|#|$)"),o=n.exec(e.toLowerCase());return o?o[2]?decodeURIComponent(o[2].replace(/\+/g," ")):"":null},f=function(e){return u(window.location.href,e)},c=function(e,t){return e+=(-1!==e.indexOf("?")?"&":"?")+t},p={getGetParamStr:function(e,t){var n="";if(void 0!=e&&!$.isEmptyObject(e)){for(var o in e)n+=t?"/"+e[o]:o+"="+e[o]+"&";t||(n="?"+n.substring(0,n.length-1))}return n},get:function(e,t){var n=$.Deferred(),o=e;t||(t={}),o=e+p.getGetParamStr(t);var r=f("token");return r&&(o=c(o,"token="+r)),$.ajax({type:"get",xhrFields:{withCredentials:!(e.indexOf("token=")>-1)},url:o}).then(function(e){e.success?n.resolve(e):(console.error("response",e),s.default.prompt(l("system."+e.data.code,e.data.title)),n.reject(e))},function(e){n.reject(e)}),n},post:function(e,t,n){var o=$.Deferred();t||(t={});var r=f("token");r&&(e=c(e,"token="+r));var i={type:"post",data:t,contentType:"application/json",processData:!1,xhrFields:{withCredentials:!(e.indexOf("token=")>-1)},url:e};return n&&void 0!==n.contentType&&(i.contentType=n.contentType),n&&void 0!==n.processData&&(i.processData=n.processData),"application/json"===i.contentType&&(i.data=(0,a.default)(t)),$.ajax(i).then(function(e){e.success?o.resolve(e):(console.error("response",e),s.default.prompt(l("system."+e.error.code,e.error.title)),o.reject(e))},function(e){o.reject(e)}),o},delete:function(e,t,n){var o=$.Deferred();t||(t={});var r=f("token");r&&(e=c(e,"token="+r));var i={type:"delete",data:t,contentType:"application/json",processData:!1,xhrFields:{withCredentials:!(e.indexOf("token=")>-1)},url:e};return n&&void 0!==n.contentType&&(i.contentType=n.contentType),n&&void 0!==n.processData&&(i.processData=n.processData),"application/json"===i.contentType&&(i.data=(0,a.default)(t)),$.ajax(i).then(function(e){e.success?o.resolve(e):(console.error("response",e),s.default.prompt(l("system."+e.error.code,e.error.title)),o.reject(e))},function(e){o.reject(e)}),o}};t.default=p},function(e,t){},function(e,t,n){"use strict";var o=n(56)(!0);n(38)(String,"String",function(e){this._t=String(e),this._i=0},function(){var e,t=this._t,n=this._i;return n>=t.length?{value:void 0,done:!0}:(e=o(t,n),this._i+=e.length,{value:e,done:!1})})},function(e,t,n){"use strict";var o=n(10),r=n(11),a=n(40),i=n(4),s=n(12),l=n(57),u=n(19),f=n(62),c=n(1)("iterator"),p=!([].keys&&"next"in[].keys()),d=function(){return this};e.exports=function(e,t,n,h,m,g,b){l(n,t,h);var y,v,x,k=function(e){if(!p&&e in S)return S[e];switch(e){case"keys":case"values":return function(){return new n(this,e)}}return function(){return new n(this,e)}},w=t+" Iterator",T="values"==m,I=!1,S=e.prototype,P=S[c]||S["@@iterator"]||m&&S[m],_=P||k(m),E=m?T?k("entries"):_:void 0,C="Array"==t?S.entries||P:P;if(C&&(x=f(C.call(new e)))!==Object.prototype&&x.next&&(u(x,w,!0),o||"function"==typeof x[c]||i(x,c,d)),T&&P&&"values"!==P.name&&(I=!0,_=function(){return P.call(this)}),o&&!b||!p&&!I&&S[c]||i(S,c,_),s[t]=_,s[w]=d,m)if(y={values:T?_:k("values"),keys:g?_:k("keys"),entries:E},b)for(v in y)v in S||a(S,v,y[v]);else r(r.P+r.F*(p||I),t,y);return y}},function(e,t,n){e.exports=!n(7)&&!n(16)(function(){return 7!=Object.defineProperty(n(25)("div"),"a",{get:function(){return 7}}).a})},function(e,t,n){e.exports=n(4)},function(e,t,n){var o=n(3),r=n(58),a=n(30),i=n(28)("IE_PROTO"),s=function(){},l=function(){var e,t=n(25)("iframe"),o=a.length;for(t.style.display="none",n(44).appendChild(t),t.src="javascript:",e=t.contentWindow.document,e.open(),e.write("<script>document.F=Object<\/script>"),e.close(),l=e.F;o--;)delete l.prototype[a[o]];return l()};e.exports=Object.create||function(e,t){var n;return null!==e?(s.prototype=o(e),n=new s,s.prototype=null,n[i]=e):n=l(),void 0===t?n:r(n,t)}},function(e,t,n){var o=n(8),r=n(9),a=n(60)(!1),i=n(28)("IE_PROTO");e.exports=function(e,t){var n,s=r(e),l=0,u=[];for(n in s)n!=i&&o(s,n)&&u.push(n);for(;t.length>l;)o(s,n=t[l++])&&(~a(u,n)||u.push(n));return u}},function(e,t,n){var o=n(23),r=Math.min;e.exports=function(e){return e>0?r(o(e),9007199254740991):0}},function(e,t,n){var o=n(0).document;e.exports=o&&o.documentElement},function(e,t,n){var o=n(24);e.exports=function(e){return Object(o(e))}},function(e,t,n){n(63);for(var o=n(0),r=n(4),a=n(12),i=n(1)("toStringTag"),s="CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,TextTrackList,TouchList".split(","),l=0;l<s.length;l++){var u=s[l],f=o[u],c=f&&f.prototype;c&&!c[i]&&r(c,i,u),a[u]=a.Array}},function(e,t,n){var o=n(13),r=n(1)("toStringTag"),a="Arguments"==o(function(){return arguments}()),i=function(e,t){try{return e[t]}catch(e){}};e.exports=function(e){var t,n,s;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=i(t=Object(e),r))?n:a?o(t):"Object"==(s=o(t))&&"function"==typeof t.callee?"Arguments":s}},function(e,t,n){var o=n(3),r=n(15),a=n(1)("species");e.exports=function(e,t){var n,i=o(e).constructor;return void 0===i||void 0==(n=o(i)[a])?t:r(n)}},function(e,t,n){var o,r,a,i=n(14),s=n(72),l=n(44),u=n(25),f=n(0),c=f.process,p=f.setImmediate,d=f.clearImmediate,h=f.MessageChannel,m=f.Dispatch,g=0,b={},y=function(){var e=+this;if(b.hasOwnProperty(e)){var t=b[e];delete b[e],t()}},v=function(e){y.call(e.data)};p&&d||(p=function(e){for(var t=[],n=1;arguments.length>n;)t.push(arguments[n++]);return b[++g]=function(){s("function"==typeof e?e:Function(e),t)},o(g),g},d=function(e){delete b[e]},"process"==n(13)(c)?o=function(e){c.nextTick(i(y,e,1))}:m&&m.now?o=function(e){m.now(i(y,e,1))}:h?(r=new h,a=r.port2,r.port1.onmessage=v,o=i(a.postMessage,a,1)):f.addEventListener&&"function"==typeof postMessage&&!f.importScripts?(o=function(e){f.postMessage(e+"","*")},f.addEventListener("message",v,!1)):o="onreadystatechange"in u("script")?function(e){l.appendChild(u("script")).onreadystatechange=function(){l.removeChild(this),y.call(e)}}:function(e){setTimeout(i(y,e,1),0)}),e.exports={set:p,clear:d}},function(e,t){e.exports=function(e){try{return{e:!1,v:e()}}catch(e){return{e:!0,v:e}}}},function(e,t,n){var o=n(3),r=n(6),a=n(31);e.exports=function(e,t){if(o(e),r(t)&&t.constructor===e)return t;var n=a.f(e);return(0,n.resolve)(t),n.promise}},function(e,t){t.f=Object.getOwnPropertySymbols},function(e,t,n){var o=n(42),r=n(30).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return o(e,r)}},function(e,t,n){e.exports={default:n(55),__esModule:!0}},function(e,t,n){n(36),n(37),n(46),n(66),n(78),n(79),e.exports=n(2).Promise},function(e,t,n){var o=n(23),r=n(24);e.exports=function(e){return function(t,n){var a,i,s=String(r(t)),l=o(n),u=s.length;return l<0||l>=u?e?"":void 0:(a=s.charCodeAt(l),a<55296||a>56319||l+1===u||(i=s.charCodeAt(l+1))<56320||i>57343?e?s.charAt(l):a:e?s.slice(l,l+2):i-56320+(a-55296<<10)+65536)}}},function(e,t,n){"use strict";var o=n(41),r=n(17),a=n(19),i={};n(4)(i,n(1)("iterator"),function(){return this}),e.exports=function(e,t,n){e.prototype=o(i,{next:r(1,n)}),a(e,t+" Iterator")}},function(e,t,n){var o=n(5),r=n(3),a=n(27);e.exports=n(7)?Object.defineProperties:function(e,t){r(e);for(var n,i=a(t),s=i.length,l=0;s>l;)o.f(e,n=i[l++],t[n]);return e}},function(e,t,n){var o=n(13);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==o(e)?e.split(""):Object(e)}},function(e,t,n){var o=n(9),r=n(43),a=n(61);e.exports=function(e){return function(t,n,i){var s,l=o(t),u=r(l.length),f=a(i,u);if(e&&n!=n){for(;u>f;)if((s=l[f++])!=s)return!0}else for(;u>f;f++)if((e||f in l)&&l[f]===n)return e||f||0;return!e&&-1}}},function(e,t,n){var o=n(23),r=Math.max,a=Math.min;e.exports=function(e,t){return e=o(e),e<0?r(e+t,0):a(e,t)}},function(e,t,n){var o=n(8),r=n(45),a=n(28)("IE_PROTO"),i=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=r(e),o(e,a)?e[a]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?i:null}},function(e,t,n){"use strict";var o=n(64),r=n(65),a=n(12),i=n(9);e.exports=n(38)(Array,"Array",function(e,t){this._t=i(e),this._i=0,this._k=t},function(){var e=this._t,t=this._k,n=this._i++;return!e||n>=e.length?(this._t=void 0,r(1)):"keys"==t?r(0,n):"values"==t?r(0,e[n]):r(0,[n,e[n]])},"values"),a.Arguments=a.Array,o("keys"),o("values"),o("entries")},function(e,t){e.exports=function(){}},function(e,t){e.exports=function(e,t){return{value:t,done:!!e}}},function(e,t,n){"use strict";var o,r,a,i,s=n(10),l=n(0),u=n(14),f=n(47),c=n(11),p=n(6),d=n(15),h=n(67),m=n(68),g=n(48),b=n(49).set,y=n(73)(),v=n(31),x=n(50),k=n(74),w=n(51),T=l.TypeError,I=l.process,S=I&&I.versions,P=S&&S.v8||"",_=l.Promise,E="process"==f(I),C=function(){},U=r=v.f,A=!!function(){try{var e=_.resolve(1),t=(e.constructor={})[n(1)("species")]=function(e){e(C,C)};return(E||"function"==typeof PromiseRejectionEvent)&&e.then(C)instanceof t&&0!==P.indexOf("6.6")&&-1===k.indexOf("Chrome/66")}catch(e){}}(),O=function(e){var t;return!(!p(e)||"function"!=typeof(t=e.then))&&t},B=function(e,t){if(!e._n){e._n=!0;var n=e._c;y(function(){for(var o=e._v,r=1==e._s,a=0;n.length>a;)!function(t){var n,a,i,s=r?t.ok:t.fail,l=t.resolve,u=t.reject,f=t.domain;try{s?(r||(2==e._h&&F(e),e._h=1),!0===s?n=o:(f&&f.enter(),n=s(o),f&&(f.exit(),i=!0)),n===t.promise?u(T("Promise-chain cycle")):(a=O(n))?a.call(n,l,u):l(n)):u(o)}catch(e){f&&!i&&f.exit(),u(e)}}(n[a++]);e._c=[],e._n=!1,t&&!e._h&&R(e)})}},R=function(e){b.call(l,function(){var t,n,o,r=e._v,a=j(e);if(a&&(t=x(function(){E?I.emit("unhandledRejection",r,e):(n=l.onunhandledrejection)?n({promise:e,reason:r}):(o=l.console)&&o.error&&o.error("Unhandled promise rejection",r)}),e._h=E||j(e)?2:1),e._a=void 0,a&&t.e)throw t.v})},j=function(e){return 1!==e._h&&0===(e._a||e._c).length},F=function(e){b.call(l,function(){var t;E?I.emit("rejectionHandled",e):(t=l.onrejectionhandled)&&t({promise:e,reason:e._v})})},N=function(e){var t=this;t._d||(t._d=!0,t=t._w||t,t._v=e,t._s=2,t._a||(t._a=t._c.slice()),B(t,!0))},M=function(e){var t,n=this;if(!n._d){n._d=!0,n=n._w||n;try{if(n===e)throw T("Promise can't be resolved itself");(t=O(e))?y(function(){var o={_w:n,_d:!1};try{t.call(e,u(M,o,1),u(N,o,1))}catch(e){N.call(o,e)}}):(n._v=e,n._s=1,B(n,!1))}catch(e){N.call({_w:n,_d:!1},e)}}};A||(_=function(e){h(this,_,"Promise","_h"),d(e),o.call(this);try{e(u(M,this,1),u(N,this,1))}catch(e){N.call(this,e)}},o=function(e){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1},o.prototype=n(75)(_.prototype,{then:function(e,t){var n=U(g(this,_));return n.ok="function"!=typeof e||e,n.fail="function"==typeof t&&t,n.domain=E?I.domain:void 0,this._c.push(n),this._a&&this._a.push(n),this._s&&B(this,!1),n.promise},catch:function(e){return this.then(void 0,e)}}),a=function(){var e=new o;this.promise=e,this.resolve=u(M,e,1),this.reject=u(N,e,1)},v.f=U=function(e){return e===_||e===i?new a(e):r(e)}),c(c.G+c.W+c.F*!A,{Promise:_}),n(19)(_,"Promise"),n(76)("Promise"),i=n(2).Promise,c(c.S+c.F*!A,"Promise",{reject:function(e){var t=U(this);return(0,t.reject)(e),t.promise}}),c(c.S+c.F*(s||!A),"Promise",{resolve:function(e){return w(s&&this===i?_:this,e)}}),c(c.S+c.F*!(A&&n(77)(function(e){_.all(e).catch(C)})),"Promise",{all:function(e){var t=this,n=U(t),o=n.resolve,r=n.reject,a=x(function(){var n=[],a=0,i=1;m(e,!1,function(e){var s=a++,l=!1;n.push(void 0),i++,t.resolve(e).then(function(e){l||(l=!0,n[s]=e,--i||o(n))},r)}),--i||o(n)});return a.e&&r(a.v),n.promise},race:function(e){var t=this,n=U(t),o=n.reject,r=x(function(){m(e,!1,function(e){t.resolve(e).then(n.resolve,o)})});return r.e&&o(r.v),n.promise}})},function(e,t){e.exports=function(e,t,n,o){if(!(e instanceof t)||void 0!==o&&o in e)throw TypeError(n+": incorrect invocation!");return e}},function(e,t,n){var o=n(14),r=n(69),a=n(70),i=n(3),s=n(43),l=n(71),u={},f={},t=e.exports=function(e,t,n,c,p){var d,h,m,g,b=p?function(){return e}:l(e),y=o(n,c,t?2:1),v=0;if("function"!=typeof b)throw TypeError(e+" is not iterable!");if(a(b)){for(d=s(e.length);d>v;v++)if((g=t?y(i(h=e[v])[0],h[1]):y(e[v]))===u||g===f)return g}else for(m=b.call(e);!(h=m.next()).done;)if((g=r(m,y,h.value,t))===u||g===f)return g};t.BREAK=u,t.RETURN=f},function(e,t,n){var o=n(3);e.exports=function(e,t,n,r){try{return r?t(o(n)[0],n[1]):t(n)}catch(t){var a=e.return;throw void 0!==a&&o(a.call(e)),t}}},function(e,t,n){var o=n(12),r=n(1)("iterator"),a=Array.prototype;e.exports=function(e){return void 0!==e&&(o.Array===e||a[r]===e)}},function(e,t,n){var o=n(47),r=n(1)("iterator"),a=n(12);e.exports=n(2).getIteratorMethod=function(e){if(void 0!=e)return e[r]||e["@@iterator"]||a[o(e)]}},function(e,t){e.exports=function(e,t,n){var o=void 0===n;switch(t.length){case 0:return o?e():e.call(n);case 1:return o?e(t[0]):e.call(n,t[0]);case 2:return o?e(t[0],t[1]):e.call(n,t[0],t[1]);case 3:return o?e(t[0],t[1],t[2]):e.call(n,t[0],t[1],t[2]);case 4:return o?e(t[0],t[1],t[2],t[3]):e.call(n,t[0],t[1],t[2],t[3])}return e.apply(n,t)}},function(e,t,n){var o=n(0),r=n(49).set,a=o.MutationObserver||o.WebKitMutationObserver,i=o.process,s=o.Promise,l="process"==n(13)(i);e.exports=function(){var e,t,n,u=function(){var o,r;for(l&&(o=i.domain)&&o.exit();e;){r=e.fn,e=e.next;try{r()}catch(o){throw e?n():t=void 0,o}}t=void 0,o&&o.enter()};if(l)n=function(){i.nextTick(u)};else if(!a||o.navigator&&o.navigator.standalone)if(s&&s.resolve){var f=s.resolve(void 0);n=function(){f.then(u)}}else n=function(){r.call(o,u)};else{var c=!0,p=document.createTextNode("");new a(u).observe(p,{characterData:!0}),n=function(){p.data=c=!c}}return function(o){var r={fn:o,next:void 0};t&&(t.next=r),e||(e=r,n()),t=r}}},function(e,t,n){var o=n(0),r=o.navigator;e.exports=r&&r.userAgent||""},function(e,t,n){var o=n(4);e.exports=function(e,t,n){for(var r in t)n&&e[r]?e[r]=t[r]:o(e,r,t[r]);return e}},function(e,t,n){"use strict";var o=n(0),r=n(2),a=n(5),i=n(7),s=n(1)("species");e.exports=function(e){var t="function"==typeof r[e]?r[e]:o[e];i&&t&&!t[s]&&a.f(t,s,{configurable:!0,get:function(){return this}})}},function(e,t,n){var o=n(1)("iterator"),r=!1;try{var a=[7][o]();a.return=function(){r=!0},Array.from(a,function(){throw 2})}catch(e){}e.exports=function(e,t){if(!t&&!r)return!1;var n=!1;try{var a=[7],i=a[o]();i.next=function(){return{done:n=!0}},a[o]=function(){return i},e(a)}catch(e){}return n}},function(e,t,n){"use strict";var o=n(11),r=n(2),a=n(0),i=n(48),s=n(51);o(o.P+o.R,"Promise",{finally:function(e){var t=i(this,r.Promise||a.Promise),n="function"==typeof e;return this.then(n?function(n){return s(t,e()).then(function(){return n})}:e,n?function(n){return s(t,e()).then(function(){throw n})}:e)}})},function(e,t,n){"use strict";var o=n(11),r=n(31),a=n(50);o(o.S,"Promise",{try:function(e){var t=r.f(this),n=a(e);return(n.e?t.reject:t.resolve)(n.v),t.promise}})},function(e,t,n){var o=n(2),r=o.JSON||(o.JSON={stringify:JSON.stringify});e.exports=function(e){return r.stringify.apply(r,arguments)}},function(e,t,n){"use strict";(function(e){function o(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0});var r=n(22),a=o(r),i=n(20),s=o(i),u=(n(21).default,n(35).default),f=n(87),c=function(t){function n(){return window.nxt&&window.nxt.config?window.nxt.config:t.configInst?t.configInst:void 0}function o(){if(C.getFilesByStatus("progress","deleteing").length>0)return l("upload.closePageTip","当前页面存在未完成的上传任务，关闭页面会导致上传失败，你确定要关闭吗？")}function r(e){return _.isUndefined(e.lastModifiedDate)?e.lastModified:e.lastModifiedDate.getTime()}function i(e){var t=_.find(n().entityTypes,{code:e.entityType});return null==t&&(t=_.find(n().entityTypes,{isOther:!0})),null==t?"":t.keyframe.replace("~","")}function c(){_.forEach(C.tasks,function(e){"added"!==e.status&&"success"!==e.status&&(e.status="error")}),$(window).off("beforeunload",o),s.default.prompt(l("upload.unauthorized","你未登录或已超时，请重新登录。")),location.href=n().loginUrl+"?login_backUrl="+location.href}function p(e){var e=e||"";null==S&&(P=_.uniqueId("mam-web-transfer-"),S=void 0===t.isMultiple||t.isMultiple?$('<input id="'+P+'" accept="'+e+'" type="file" multiple style="display:none"/>'):$('<input id="'+P+'" accept="'+e+'" type="file" style="display:none"/>'),$("body").append(S))}function d(){null!=S&&(S.unbind("change"),S.remove(),S=null)}function h(e,t){$(C).trigger(e,t)}function m(e,t){if(e){if("TYY_S3"===e.manageType)return b(e,t);if("ALY_OSS"!==e.manageType){var n={Bucket:e.bucketName,Key:t.relativePath.replace(/\\/g,"/")};if(g(e),!(t.fileSize<=5242880)){var o=E.createMultipartUpload(n).promise();t.createUploadPromise=o;var r=$.Deferred();return t.createUploadPromise.then(function(e){t.uploadId=e.UploadId,r.resolve()}).catch(function(e){r.reject(),console.info(e)}),r.promise()}}}}function g(e){var t={accessKeyId:e.accessKeyId,secretAccessKey:e.accessKeySecret,endpoint:e.endpoint,sslEnabled:!e.useHttp,s3ForcePathStyle:!0,signatureVersion:"v"+e.version,apiVersion:"2006-03-01"};if(e.region&&(AWS.config.region=e.region),n().isHandleHttpPath&&t.endpoint){var o=window.location.protocol+"//"+window.location.host;t.endpoint=o}E="TYY_S3"!==e.manageType?new AWS.S3(t):new OOS.S3(t)}function b(e,t){if(e&&"TYY_S3"===e.manageType&&(g(e),!(t.fileSize<=5242880))){var n={Bucket:e.bucketName,Key:t.relativePath.replace(/\\/g,"/")},o=E.createMultipartUpload(n).promise();t.createUploadPromise=o;var r=$.Deferred();return t.createUploadPromise.then(function(e){t.uploadId=e.UploadId,r.resolve()}).catch(function(e){r.reject(),console.info(e)}),r.promise()}}function y(e){h("task-init-before",e);var o=s.default.getCookie("apiVersion"),r=n().server+"/upload/multipart/init";if(o&&"mamcore2.3"===o&&(r=n().server+"/sflud/v1/upload/multipart/init"),t.loginToken&&(r+="?token="+t.loginToken),e.metadata&&e.metadata.field){var a=e.metadata.field.find(function(e){return"filesize"===e.fieldName});a&&(a.value=String(a.value))}u.post(r,e).then(function(t){t=t.data;for(var n=0;n<t.files.length;n++){var o=e.files[n].file;e.files[n]=t.files[n],e.files[n].file=o,e.files[n].status="prepared",e.files[n].fileSizeString=s.default.formatSize(t.files[n].fileSize),e.files[n].progress=0,e.sizeTotal+=t.files[n].fileSize,e.chunkTotal+=t.files[n].chunkTotal}e.taskId=t.taskId,e.entityType=t.entityType,e.fileTotal=t.fileTotal,e.targetFolder=t.targetFolder,e.targetFolderName=t.targetFolderName,e.targetType=t.targetType,e.keyframe=t.keyframe,e.status="prepared",e.inited=!0,e.sizeTotalString=s.default.formatSize(e.sizeTotal),e.isJsUpload=t.isJsUpload,e.ossClientInfo=t.ossClientInfo,h("task-init-success",e),C.prepareUpload()},function(t){if(h("task-init-error",[e,t]),401===t.status)return void c(C.tasks);e.status="error",_.forEach(e.files,function(e){e.status="error"}),s.default.prompt(l("upload.","上传失败：${text}",{text:t.error.desc||t.error.title})),C.prepareUpload()})}function v(e,t,o){n().webUploadMd5Enable?(null==e.file.fileReader&&(e.file.fileReader=new FileReader),e.file.fileReader.onload=function(e){var t=new f;t.appendBinary(e.target.result);var n=t.end();t.destroy(),o(n)},e.file.fileReader.onerror=function(t){k(e)},e.file.fileReader.readAsBinaryString(t)):o("")}function x(e,t){null!=t.startTime&&(_.isNumber(t.surplusTime)||(t.surplusTime=0),t.surplusTime=(new Date-t.startTime)*(t.chunkTotal-t.chunkIndex),_.isNumber(e.surplusTime)||(e.surplusTime=0),e.surplusTime=(new Date-t.startTime)*(e.chunkTotal-e.chunkFinished)),t.startTime=new Date}function k(e){e.file.hasOwnProperty("errorCount")||(e.file.errorCount=0),e.file.errorCount<4?(e.file.errorCount++,U[e.file.fileId]=setTimeout(function(){C.upload(e)},3e3)):(e.task.status=e.file.status="error",h("task-upload-error",e.task),C.prepareUpload())}function w(e,t){if(e==t)return 100;var n=e/t*100;return-1==n.toString().indexOf(".")?n:n.toFixed(2)}function T(e){return w(e.chunkIndex,e.chunkTotal)}function I(e){for(var t=0,n=0;n<e.files.length;n++)t+=e.files[n].chunkIndex;return e.chunkFinished=t,w(e.chunkFinished,e.chunkTotal)}var S,P,E,C=this,U={};this.tasks=[],this.on=function(e,t){$(C).on(e,t)},this.off=function(e,t){$(C).off(e,t)},this.getGroupTask=function(e,t,o,r,a){var i=_.find(n().entityTypes,{code:t});return{entityType:i?i.code:"",files:e.map(function(e){return{fileName:e.fileName,file:e.file,metadata:e.metadata}}),metadata:{name:""},targetFolder:o,taskType:r,transferType:a,status:"added",progress:0}},this.getTasksByFiles=function(e){var t=[];return _.forEach(e,function(e){var o="."+s.default.getExtension(e.name);t.push({entityType:s.default.getTypeByExt(o,n()).code,fileName:e.name,metadata:{name:s.default.getFileName(e.name),ext:o},status:"added",progress:0,file:e})}),t},this.openFileSelector=function(e,t,n){var o=this;d(),p(n),!0===t?S.removeAttr("multiple"):S.attr("multiple","multiple"),S.on("change",function(){if(""!==S.val()){var r=S[0].files;!0===t&&(r=r[0]),e(o.getTasksByFiles(r)),p(n)}}),S.click(function(e){e.stopPropagation()}),S.trigger("click")},this.createTask=function(e,t){var n=[];switch(t.taskType){case 1:_.isArray(e)||(e=[e]),_.forEach(e,function(e){e.files&&e.files.length>0?(e.files=_.map(e.files,function(e){return{file:e.file,fileName:e.file.name,fileSize:e.file.size,fileLastModifiedDate:r(e.file),type:Number(e.type)?Number(e.type):0}}),e.file&&e.files.unshift({file:e.file,fileName:e.file.name,fileSize:e.file.size,fileLastModifiedDate:r(e.file),type:void 0!==e.type?Number(e.type):0})):e.files=[{file:e.file,fileName:e.file.name,fileSize:e.file.size,fileLastModifiedDate:r(e.file),type:void 0!==e.type?Number(e.type):0}],delete e.file,n.push(e)});break;case 2:case 3:_.forEach(e.files,function(e){e.fileName=e.file.name,e.fileSize=e.file.size,e.fileLastModifiedDate=r(e.file)}),n.push(e)}_.forEach(n,function(e){e.taskType=t.taskType,e.transferType=t.transferType,e.targetFolder=t.targetFolder,e.relationContentType=t.relationContentType,e.relationContentId=t.relationContentId,C.addTask(e)})},this.addTask=function(e){e.status="init",e.progress=e.sizeTotal=e.chunkFinished=e.chunkTotal=0,e.keyframe=i(e),C.tasks.push(e),y(e)},this.prepareUpload=function(){var e=C.getFilesByStatus("progress"),t=n().webUploadThreads-e.length;if(!(t<=0)){var o=C.getFilesByStatus("prepared");if(t>o.length&&(t=o.length),0!==t)for(var r=0;r<t;r++)o[r].task.status=o[r].file.status="progress",C.upload(o[r])}},this.upload=function(e){e.task.ossClientInfo?"ALY_OSS"===e.task.ossClientInfo.manageType?this.uploadByAliOss(e):"TYY_S3"===e.task.ossClientInfo.manageType?this.uploadByOos(e):"KSYUN"===e.task.ossClientInfo.manageType?this.uploadByKsyun(e):this.uploadByS3(e):this.uploadByBackend(e)},this.uploadByBackend=function(e){var o=e.file,r=e.task;if(o.chunkIndex<o.chunkTotal){x(r,o);var a=o.chunkIndex*o.chunkSize,i=Math.min(o.fileSize,a+o.chunkSize),l=new FormData,f=o.file.slice(a,i);if(null==f||0==f.size)return e.task.status=e.file.status="error",h("task-upload-error",e.task),e.file.file=null,void C.prepareUpload();v(e,f,function(a){if(l.append("fileData",f),l.append("taskId",o.taskId),l.append("fileId",o.fileId),l.append("chunkIndex",o.chunkIndex),l.append("md5",a),"deleteing"===r.status)return void C.clearTask(r);var i=s.default.getCookie("apiVersion"),p="/upload/multipart";i&&"mamcore2.3"===i&&(p="/sflud/v1/upload/multipart"),t.loginToken&&(p+="?token="+t.loginToken),u.post(n().server+p,l,{contentType:!1,processData:!1}).then(function(t){if("deleteing"===r.status)return void C.clearTask(r);t=t.data,o=_.find(r.files,{fileId:t.fileId}),o.chunkIndex=t.chunkIndex,3===t.taskStatus?(o.progress=r.progress=100,o.surplusTime=r.surplusTime=null,delete o.fileReader,o.status=r.status="success",C.prepareUpload(),O(r).then(function(){h("task-upload-success",r)})):(r.progress=I(r),o.progress=T(o),o.status=r.status="progress",h("task-upload-progress",r),o.errorCount=0,C.upload(e))},function(t){if(401===t.status)return void c();k(e)})})}else o.hasOwnProperty("errorCount")&&(delete U[o.fileId],delete o.errorCount),delete o.fileReader,o.surplusTime=null,o.progress=100,o.status="success",r.progress=I(r),h("task-upload-success",r),C.prepareUpload()};var A=function(e){var o=new FormData;o.append("taskId",e.taskId),o.append("fileId",e.fileId),o.append("chunkIndex",e.chunkIndex),o.append("partInfo",(0,a.default)(e.partInfo)),o.append("uploadId",e.uploadId),o.append("checkPoint",e.checkPoint);var r=s.default.getCookie("apiVersion"),i="/upload/multipart";return r&&"mamcore2.3"===r&&(i="/sflud/v1/upload/multipart"),t.loginToken&&(i+="?token="+t.loginToken),u.post(n().server+i,o,{contentType:!1,processData:!1})},O=function(e){var t=n().server,o=s.default.getCookie("apiVersion"),r="/upload/multipart/complete?taskId="+e.taskId;return o&&"mamcore2.3"===o&&(r="/sflud/v1/upload/multipart/complete?taskId="+e.taskId),u.post(t+r)};this.uploadByAliOssWithServerCallback=function(o){var r=o.file,i=o.task,l={region:i.ossClientInfo.region,accessKeyId:i.ossClientInfo.accessKeyId,accessKeySecret:i.ossClientInfo.accessKeySecret,bucket:i.ossClientInfo.bucketName};i.ossClientInfo.endpoint&&(l.endpoint=i.ossClientInfo.endpoint);var u=new this.OSS.Wrapper(l);if(r.fileSize<=102400)o.fileReader=new FileReader,o.fileReader.onload=function(t){u.put(r.relativePath,new e(this.result)).then(function(e){A(r).then(function(e){e=e.data,3===e.taskStatus?(r.progress=i.progress=100,r.surplusTime=i.surplusTime=null,delete r.fileReader,r.status=i.status="success",O(i).then(function(){h("task-upload-success",i)})):(r.status="success",r.progress=100),C.prepareUpload()})},function(e){console.error(e)})},o.fileReader.readAsArrayBuffer(r.file);else{var f=s.default.addUrlParam(n().ossUpCallbackUrl,"taskId="+i.taskId);t.loginToken&&(f=s.default.addUrlParam(f,"token="+t.loginToken));var p={partSize:r.chunkSize,progress:function(e,t,n){return function(e){if("deleteing"===i.status)C.clearTask(i);else if("success"===i.status)e();else{if(0===t.doneParts.length)return void e();r.partInfo=t.doneParts[t.doneParts.length-1],r.uploadId=t.uploadId,r.checkPoint=(0,a.default)(t,function(e,t){if("file"!==e)return t}),r.chunkIndex=t.doneParts.length-1,A(r).then(function(t){if("deleteing"===i.status)return void C.clearTask(i);t=t.data,r.chunkIndex=t.chunkIndex,3===t.taskStatus?(r.progress=i.progress=100,r.surplusTime=i.surplusTime=null,delete r.fileReader,r.status=i.status="success",h("task-upload-success",i),C.prepareUpload()):r.chunkIndex<r.chunkTotal?(i.progress=I(i),r.progress=T(r),r.status=i.status="progress",h("task-upload-progress",i),r.errorCount=0):(r.hasOwnProperty("errorCount")&&(delete U[r.fileId],delete r.errorCount),r.surplusTime=null,r.progress=100,r.status="success",i.progress=I(i),h("task-upload-success",i),C.prepareUpload()),e()},function(e){if(401===e.status)return void c();k(o)})}}},callback:{callbackUrl:f,callbackBody:"bucket=${bucket}&object=${object}&etag=${etag}&size=${size}&mimeType=${mimeType}&imageInfo.height=${imageInfo.height}&imageInfo.width=${imageInfo.width}&imageInfo.format=${imageInfo.format}"}};r.checkPoint&&(p.checkpoint=JSON.parse(r.checkPoint),p.checkpoint.file=r.file),u.multipartUpload(r.relativePath,r.file,p).then(function(e){console.log("upload success",e)},function(e){console.log(e)})}},this.uploadByAliOss=function(t){var n=t.file,o=t.task,r={region:o.ossClientInfo.region,accessKeyId:o.ossClientInfo.accessKeyId,accessKeySecret:o.ossClientInfo.accessKeySecret,bucket:o.ossClientInfo.bucketName};o.ossClientInfo.endpoint&&(r.endpoint=o.ossClientInfo.endpoint);var i=new this.OSS.Wrapper(r);if(n.fileSize<=102400)t.fileReader=new FileReader,t.fileReader.onload=function(t){i.put(n.relativePath,new e(this.result)).then(function(e){A(n).then(function(e){e=e.data,3===e.taskStatus?(n.progress=o.progress=100,n.surplusTime=o.surplusTime=null,delete n.fileReader,n.status=o.status="success",O(o).then(function(){h("task-upload-success",o)})):(n.status="success",n.progress=100),C.prepareUpload()})},function(e){console.error(e)})},t.fileReader.readAsArrayBuffer(n.file);else{var s={partSize:n.chunkSize,progress:function(e,r,i){return function(e){if("deleteing"===o.status)C.clearTask(o);else if("success"===o.status)e();else{if(0===r.doneParts.length)return void e();n.partInfo=r.doneParts[r.doneParts.length-1],n.uploadId=r.uploadId,n.checkPoint=(0,a.default)(r,function(e,t){if("file"!==e)return t}),n.chunkIndex=r.doneParts.length-1,A(n).then(function(t){if("deleteing"===o.status)return void C.clearTask(o);t=t.data,n.chunkIndex=t.chunkIndex,3===t.taskStatus?(n.progress=o.progress=100,n.surplusTime=o.surplusTime=null,delete n.fileReader,n.status=o.status="success",C.prepareUpload(),O(o).then(function(){h("task-upload-success",o)})):n.chunkIndex<n.chunkTotal?(o.progress=I(o),n.progress=T(n),n.status=o.status="progress",h("task-upload-progress",o),n.errorCount=0):(n.hasOwnProperty("errorCount")&&(delete U[n.fileId],delete n.errorCount),n.surplusTime=null,n.progress=100,n.status="success",o.progress=I(o),h("task-upload-success",o),C.prepareUpload()),e()},function(e){if(401===e.status)return void c();k(t)})}}}};n.checkPoint&&(s.checkpoint=JSON.parse(n.checkPoint),s.checkpoint.file=n.file),i.multipartUpload(n.relativePath,n.file,s).then(function(e){console.log("upload success",e)},function(e){console.log(e)})}},this.doUploadByKsyun=function(e){var t=e.file,n=e.task,o=t.chunkIndex*t.chunkSize,r=Math.min(t.fileSize,o+t.chunkSize),a=t.file.slice(o,r),i={Bucket:n.ossClientInfo.bucketName,Key:t.relativePath.replace(/\\/g,"/"),PartNumber:t.chunkIndex+1,UploadId:t.uploadId,body:a};Ks3.upload_part(i,function(o,r,a){if(o)return void console.error(o);"deleteing"===n.status?C.clearTask(n):A(t).then(function(o){if("deleteing"===n.status)return void C.clearTask(n);o=o.data,t.chunkIndex=o.chunkIndex,3===o.taskStatus?(t.progress=n.progress=100,t.surplusTime=n.surplusTime=null,delete t.fileReader,t.status=n.status="success",C.prepareUpload(),O(n).then(function(){h("task-upload-success",n)})):t.chunkIndex<t.chunkTotal?(n.progress=I(n),t.progress=T(t),t.status=n.status="progress",h("task-upload-progress",n),t.errorCount=0,C.upload(e)):(t.hasOwnProperty("errorCount")&&(delete U[t.fileId],delete t.errorCount),t.surplusTime=null,t.progress=100,t.status="success",n.progress=I(n),h("task-upload-success",n),C.prepareUpload())},function(t){if(401===t.status)return void c();k(e)})})},this.uploadByKsyun=function(e){var t=e.file,n=e.task;n.ossClientInfo.accessKeyId&&n.ossClientInfo.accessKeySecret&&(Ks3.config.AK=n.ossClientInfo.accessKeyId,Ks3.config.SK=n.ossClientInfo.accessKeySecret),Ks3.config.region=n.ossClientInfo.region,Ks3.ENDPOINT[""+n.ossClientInfo.region]=n.ossClientInfo.serviceUrl;var o={KSSAccessKeyId:n.ossClientInfo.accessKeyId,signature:n.ossClientInfo.accessKeySecret,bucket_name:n.ossClientInfo.bucketName,key:t.relativePath.replace(/\\/g,"/"),acl:"public-read",uploadDomain:n.ossClientInfo.serviceUrl+"/"+n.ossClientInfo.bucketName,autoStart:!1},r={drop_element:document.body,url:n.ossClientInfo.serviceUrl};new ks3FileUploader(o,r);if(t.fileSize<=104857600){var a={Bucket:n.ossClientInfo.bucketName,Key:t.relativePath.replace(/\\/g,"/"),File:t.file,ACL:"public-read"};Ks3.putObject(a,function(o){o?(console.info(o,o.error),k(e)):A(t).then(function(e){e=e.data,3===e.taskStatus?(t.progress=n.progress=100,t.surplusTime=n.surplusTime=null,delete t.fileReader,t.status=n.status="success",O(n).then(function(){h("task-upload-success",n)})):(t.status="success",t.progress=100),C.prepareUpload()})})}else{var a={Bucket:n.ossClientInfo.bucketName,Key:t.relativePath.replace(/\\/g,"/"),ACL:"public-read"};t.uploadId?this.doUploadByKsyun(e):Ks3.multitpart_upload_init(a,function(n,o){n?console.error(n):(t.uploadId=o,this.doUploadByKsyun(e))})}},this.uploadByS3=function(e){var o=e.file,r=e.task;if(E||g(e.task.ossClientInfo),o.chunkIndex<o.chunkTotal){var a=o.chunkIndex*o.chunkSize,i=Math.min(o.fileSize,a+o.chunkSize),l=new FormData,f=o.file.slice(a,i);if(null==f||0==f.size)return e.task.status=e.file.status="error",h("task-upload-error",e.task),e.file.file=null,void C.prepareUpload();var p=function(o,r){if(l.append("taskId",r.taskId),l.append("fileId",r.fileId),l.append("chunkIndex",r.chunkIndex),l.append("partInfo",r.partInfo),l.append("uploadId",r.uploadId),"deleteing"===o.status)return void C.clearTask(o);var a=s.default.getCookie("apiVersion"),i="/upload/multipart";a&&"mamcore2.3"===a&&(i="/sflud/v1/upload/multipart"),t.loginToken&&(i+="?token="+t.loginToken),u.post(n().server+i,l,{contentType:!1,processData:!1}).then(function(t){if("deleteing"===o.status)return void C.clearTask(o);t=t.data,r=_.find(o.files,{fileId:t.fileId}),r.chunkIndex=t.chunkIndex,3===t.taskStatus?(r.progress=o.progress=100,r.surplusTime=o.surplusTime=null,delete r.fileReader,r.status=o.status="success",C.prepareUpload(),O(o).then(function(){h("task-upload-success",o)})):(o.progress=I(o),r.progress=T(r),r.status=o.status="progress",h("task-upload-progress",o),r.errorCount=0,C.upload(e))},function(t){if(401===t.status)return void c();k(e)})};if(o.fileSize<=5242880)E.putObject({Body:o.file,Bucket:r.ossClientInfo.bucketName,Key:o.relativePath.replace(/\\/g,"/")},function(t,n){t?(console.info(t,t.stack),k(e)):(n.ETag&&(o.partInfo=n.ETag.replace(/"/g,"")),p(r,o),console.info(n))});else{if(!o.uploadId){var d=m(r.ossClientInfo,o);return void(d&&d.then(function(){C.upload(e)}))}var b={Body:f,Bucket:r.ossClientInfo.bucketName,Key:o.relativePath.replace(/\\/g,"/"),PartNumber:o.chunkIndex+1,UploadId:o.uploadId};E.uploadPart(b,function(t,n){t?(console.info(t,t.stack),k(e)):(n.ETag&&(o.partInfo=n.ETag.replace(/"/g,"")),p(r,o),console.info(n))})}}else o.hasOwnProperty("errorCount")&&(delete U[o.fileId],delete o.errorCount),delete o.fileReader,o.surplusTime=null,o.progress=100,o.status="success",r.progress=I(r),h("task-upload-success",r),C.prepareUpload()},this.uploadByOos=function(e){var n=e.file,o=e.task;if(n.chunkIndex<n.chunkTotal){var r=n.chunkIndex*n.chunkSize,a=Math.min(n.fileSize,r+n.chunkSize),i=new FormData,l=n.file.slice(r,a);if(null==l||0==l.size)return e.task.status=e.file.status="error",h("task-upload-error",e.task),e.file.file=null,void C.prepareUpload();var f=function(n,o){if(i.append("taskId",o.taskId),i.append("fileId",o.fileId),i.append("chunkIndex",o.chunkIndex),i.append("partInfo",o.partInfo),i.append("uploadId",o.uploadId),"deleteing"===n.status)return void C.clearTask(n);var r=s.default.getCookie("apiVersion"),a="/upload/multipart";r&&"mamcore2.3"===r&&(a="/sflud/v1/upload/multipart"),t.loginToken&&(a+="?token="+t.loginToken),u.post(a,i,{contentType:!1,processData:!1}).then(function(t){if("deleteing"===n.status)return void C.clearTask(n);t=t.data,o=_.find(n.files,{fileId:t.fileId}),o.chunkIndex=t.chunkIndex,3===t.taskStatus?(o.progress=n.progress=100,o.surplusTime=n.surplusTime=null,delete o.fileReader,o.status=n.status="success",C.prepareUpload(),O(n).then(function(){h("task-upload-success",n)})):(n.progress=I(n),o.progress=T(o),o.status=n.status="progress",h("task-upload-progress",n),o.errorCount=0,C.upload(e))},function(t){if(401===t.status)return void c();k(e)})};if(E||g(o.ossClientInfo),n.fileSize<=5242880)E.putObject({Body:n.file,Bucket:o.ossClientInfo.bucketName,Key:n.relativePath.replace(/\\/g,"/")},function(t,r){t?(console.info(t,t.stack),k(e)):(r.ETag&&(n.partInfo=r.ETag.replace(/"/g,"")),f(o,n),console.info(r))});else{if(!n.uploadId){var p=m(o.ossClientInfo,n);return void(p&&p.then(function(){C.upload(e)}))}var d={Body:l,Bucket:o.ossClientInfo.bucketName,Key:n.relativePath.replace(/\\/g,"/"),PartNumber:n.chunkIndex+1,UploadId:n.uploadId};E.uploadPart(d,function(t,r){t?(console.info(t,t.stack),k(e)):(r.ETag&&(n.partInfo=r.ETag.replace(/"/g,"")),f(o,n),console.info(r))})}}else n.hasOwnProperty("errorCount")&&(delete U[n.fileId],delete n.errorCount),delete n.fileReader,n.surplusTime=null,n.progress=100,n.status="success",o.progress=I(o),h("task-upload-success",o),C.prepareUpload()},this.continueUpload=function(e,t,n){null==t.file?C.openFileSelector(function(o){var r=C.calcFileMd5(o[0].file);if(t.fileMd5!==r)return s.default.prompt(l("upload.fileDiffer","选择的文件不一致，请重新上传")),void(n&&n.apply(window,[l("upload.fileDiffer","选择的文件不一致，请重新上传")]));t.file=o[0].file,e.status=t.status="prepared",C.prepareUpload()},!1):e.inited?(e.status=t.status="prepared",C.prepareUpload()):y(e)},this.getUnfinishedTask=function(e,o,r){var a=$.Deferred();null==e&&(e=""),_.isNumber(o)||(o=0),_.isNumber(o)||(o=1);var i=s.default.getCookie("apiVersion"),l="/upload/get-unfinished-task?relationId="+e+"&relationContentType="+o+"&targetType="+r+(t.loginToken?"&token="+t.loginToken:"");return i&&"mamcore2.3"===i&&(l="/sflud/v1/upload/unfinished-task?relationId="+e+"&relationContentType="+o+"&targetType="+r+(t.loginToken?"&token="+t.loginToken:"")),u.get(n().server+l).then(function(e){var t=[];_.forEach(e.data,function(e){e.status="error",e.inited=!0,e.sizeTotal=e.chunkFinished=e.chunkTotal=0,_.forEach(e.files,function(t){t.fileSizeString=s.default.formatSize(t.fileSize),t.progress=T(t),t.status=100===t.progress?"success":"error",e.sizeTotal+=t.fileSize,e.chunkTotal+=t.chunkTotal}),e.progress=I(e),e.sizeTotalString=s.default.formatSize(e.sizeTotal),C.tasks.push(e),t.push(e)}),a.resolve(t)},function(e){a.reject(e)}),a},this.canDeleteTask=function(e){return null!=e&&("init"!=e.status&&("deleteing"!=e.status&&("progress"!=e.status||e.chunkFinished!=e.chunkTotal-1)))},this.removeTask=function(e){_.remove(C.tasks,function(t){return t.taskId===e.taskId})},this.deleteTask=function(e){if(this.canDeleteTask(e))if(!0===e.inited)switch(_.forEach(e.files,function(e){null!=U[e.fileId]&&(timeout.cancel(U[e.fileId]),delete U[e.fileId])}),e.status){case"progress":return void(e.status="deleteing");case"prepared":case"error":return void C.clearTask(e);default:C.removeTask(e),h("task-delete-success",e)}else C.removeTask(e),h("task-delete-success",e)},this.clearTask=function(e){var o={};e.files&&e.files.length>0&&_.forEach(e.files,function(e){o[e.fileId]=e.uploadId});var r=s.default.getCookie("apiVersion"),a="/upload/delete-task";r&&"mamcore2.3"===r&&(a="/sflud/v1/upload/task"),t.loginToken&&(a+="?token="+t.loginToken),r&&"mamcore2.1"!==r?u.delete(n().server+a,{taskId:e.taskId,fileAndTag:o}).then(function(t){C.removeTask(e),h("task-delete-success",e),C.prepareUpload()},function(t){h("task-delete-error",[e,t])}):u.post(n().server+a,{taskId:e.taskId,fileAndTag:o}).then(function(t){C.removeTask(e),h("task-delete-success",e),C.prepareUpload()},function(t){h("task-delete-error",[e,t])})},this.calcFileMd5=function(e){return f.hash(e.name+e.size+r(e))},this.getFilesByStatus=function(){for(var e=[].slice.call(arguments,0),t=[],n=0;n<C.tasks.length;n++)for(var o=0;o<C.tasks[n].files.length;o++)for(var r=0;r<e.length;r++)if(C.tasks[n].files[o].status===e[r]){t.push({task:C.tasks[n],file:C.tasks[n].files[o]});break}return t},function(){$(window).on("beforeunload",o)}()};t.default=c}).call(t,n(82).Buffer)},function(e,t,n){"use strict";(function(e){function o(){return a.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function r(e,t){if(o()<t)throw new RangeError("Invalid typed array length");return a.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t),e.__proto__=a.prototype):(null===e&&(e=new a(t)),e.length=t),e}function a(e,t,n){if(!(a.TYPED_ARRAY_SUPPORT||this instanceof a))return new a(e,t,n);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return u(this,e)}return i(this,e,t,n)}function i(e,t,n,o){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?p(e,t,n,o):"string"==typeof t?f(e,t,n):d(e,t)}function s(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function l(e,t,n,o){return s(t),t<=0?r(e,t):void 0!==n?"string"==typeof o?r(e,t).fill(n,o):r(e,t).fill(n):r(e,t)}function u(e,t){if(s(t),e=r(e,t<0?0:0|h(t)),!a.TYPED_ARRAY_SUPPORT)for(var n=0;n<t;++n)e[n]=0;return e}function f(e,t,n){if("string"==typeof n&&""!==n||(n="utf8"),!a.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var o=0|g(t,n);e=r(e,o);var i=e.write(t,n);return i!==o&&(e=e.slice(0,i)),e}function c(e,t){var n=t.length<0?0:0|h(t.length);e=r(e,n);for(var o=0;o<n;o+=1)e[o]=255&t[o];return e}function p(e,t,n,o){if(t.byteLength,n<0||t.byteLength<n)throw new RangeError("'offset' is out of bounds");if(t.byteLength<n+(o||0))throw new RangeError("'length' is out of bounds");return t=void 0===n&&void 0===o?new Uint8Array(t):void 0===o?new Uint8Array(t,n):new Uint8Array(t,n,o),a.TYPED_ARRAY_SUPPORT?(e=t,e.__proto__=a.prototype):e=c(e,t),e}function d(e,t){if(a.isBuffer(t)){var n=0|h(t.length);return e=r(e,n),0===e.length?e:(t.copy(e,0,0,n),e)}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||q(t.length)?r(e,0):c(e,t);if("Buffer"===t.type&&Z(t.data))return c(e,t.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function h(e){if(e>=o())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+o().toString(16)+" bytes");return 0|e}function m(e){return+e!=e&&(e=0),a.alloc(+e)}function g(e,t){if(a.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var n=e.length;if(0===n)return 0;for(var o=!1;;)switch(t){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return K(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return J(e).length;default:if(o)return K(e).length;t=(""+t).toLowerCase(),o=!0}}function b(e,t,n){var o=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if(n>>>=0,t>>>=0,n<=t)return"";for(e||(e="utf8");;)switch(e){case"hex":return O(this,t,n);case"utf8":case"utf-8":return E(this,t,n);case"ascii":return U(this,t,n);case"latin1":case"binary":return A(this,t,n);case"base64":return _(this,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return B(this,t,n);default:if(o)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),o=!0}}function y(e,t,n){var o=e[t];e[t]=e[n],e[n]=o}function v(e,t,n,o,r){if(0===e.length)return-1;if("string"==typeof n?(o=n,n=0):n>2147483647?n=2147483647:n<-2147483648&&(n=-2147483648),n=+n,isNaN(n)&&(n=r?0:e.length-1),n<0&&(n=e.length+n),n>=e.length){if(r)return-1;n=e.length-1}else if(n<0){if(!r)return-1;n=0}if("string"==typeof t&&(t=a.from(t,o)),a.isBuffer(t))return 0===t.length?-1:x(e,t,n,o,r);if("number"==typeof t)return t&=255,a.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?r?Uint8Array.prototype.indexOf.call(e,t,n):Uint8Array.prototype.lastIndexOf.call(e,t,n):x(e,[t],n,o,r);throw new TypeError("val must be string, number or Buffer")}function x(e,t,n,o,r){function a(e,t){return 1===i?e[t]:e.readUInt16BE(t*i)}var i=1,s=e.length,l=t.length;if(void 0!==o&&("ucs2"===(o=String(o).toLowerCase())||"ucs-2"===o||"utf16le"===o||"utf-16le"===o)){if(e.length<2||t.length<2)return-1;i=2,s/=2,l/=2,n/=2}var u;if(r){var f=-1;for(u=n;u<s;u++)if(a(e,u)===a(t,-1===f?0:u-f)){if(-1===f&&(f=u),u-f+1===l)return f*i}else-1!==f&&(u-=u-f),f=-1}else for(n+l>s&&(n=s-l),u=n;u>=0;u--){for(var c=!0,p=0;p<l;p++)if(a(e,u+p)!==a(t,p)){c=!1;break}if(c)return u}return-1}function k(e,t,n,o){n=Number(n)||0;var r=e.length-n;o?(o=Number(o))>r&&(o=r):o=r;var a=t.length;if(a%2!=0)throw new TypeError("Invalid hex string");o>a/2&&(o=a/2);for(var i=0;i<o;++i){var s=parseInt(t.substr(2*i,2),16);if(isNaN(s))return i;e[n+i]=s}return i}function w(e,t,n,o){return W(K(t,e.length-n),e,n,o)}function T(e,t,n,o){return W(G(t),e,n,o)}function I(e,t,n,o){return T(e,t,n,o)}function S(e,t,n,o){return W(J(t),e,n,o)}function P(e,t,n,o){return W(V(t,e.length-n),e,n,o)}function _(e,t,n){return 0===t&&n===e.length?Q.fromByteArray(e):Q.fromByteArray(e.slice(t,n))}function E(e,t,n){n=Math.min(e.length,n);for(var o=[],r=t;r<n;){var a=e[r],i=null,s=a>239?4:a>223?3:a>191?2:1;if(r+s<=n){var l,u,f,c;switch(s){case 1:a<128&&(i=a);break;case 2:l=e[r+1],128==(192&l)&&(c=(31&a)<<6|63&l)>127&&(i=c);break;case 3:l=e[r+1],u=e[r+2],128==(192&l)&&128==(192&u)&&(c=(15&a)<<12|(63&l)<<6|63&u)>2047&&(c<55296||c>57343)&&(i=c);break;case 4:l=e[r+1],u=e[r+2],f=e[r+3],128==(192&l)&&128==(192&u)&&128==(192&f)&&(c=(15&a)<<18|(63&l)<<12|(63&u)<<6|63&f)>65535&&c<1114112&&(i=c)}}null===i?(i=65533,s=1):i>65535&&(i-=65536,o.push(i>>>10&1023|55296),i=56320|1023&i),o.push(i),r+=s}return C(o)}function C(e){var t=e.length;if(t<=X)return String.fromCharCode.apply(String,e);for(var n="",o=0;o<t;)n+=String.fromCharCode.apply(String,e.slice(o,o+=X));return n}function U(e,t,n){var o="";n=Math.min(e.length,n);for(var r=t;r<n;++r)o+=String.fromCharCode(127&e[r]);return o}function A(e,t,n){var o="";n=Math.min(e.length,n);for(var r=t;r<n;++r)o+=String.fromCharCode(e[r]);return o}function O(e,t,n){var o=e.length;(!t||t<0)&&(t=0),(!n||n<0||n>o)&&(n=o);for(var r="",a=t;a<n;++a)r+=Y(e[a]);return r}function B(e,t,n){for(var o=e.slice(t,n),r="",a=0;a<o.length;a+=2)r+=String.fromCharCode(o[a]+256*o[a+1]);return r}function R(e,t,n){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>n)throw new RangeError("Trying to access beyond buffer length")}function j(e,t,n,o,r,i){if(!a.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>r||t<i)throw new RangeError('"value" argument is out of bounds');if(n+o>e.length)throw new RangeError("Index out of range")}function F(e,t,n,o){t<0&&(t=65535+t+1);for(var r=0,a=Math.min(e.length-n,2);r<a;++r)e[n+r]=(t&255<<8*(o?r:1-r))>>>8*(o?r:1-r)}function N(e,t,n,o){t<0&&(t=4294967295+t+1);for(var r=0,a=Math.min(e.length-n,4);r<a;++r)e[n+r]=t>>>8*(o?r:3-r)&255}function M(e,t,n,o,r,a){if(n+o>e.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function L(e,t,n,o,r){return r||M(e,t,n,4,3.4028234663852886e38,-3.4028234663852886e38),H.write(e,t,n,o,23,4),n+4}function z(e,t,n,o,r){return r||M(e,t,n,8,1.7976931348623157e308,-1.7976931348623157e308),H.write(e,t,n,o,52,8),n+8}function D(e){if(e=$(e).replace(ee,""),e.length<2)return"";for(;e.length%4!=0;)e+="=";return e}function $(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}function Y(e){return e<16?"0"+e.toString(16):e.toString(16)}function K(e,t){t=t||1/0;for(var n,o=e.length,r=null,a=[],i=0;i<o;++i){if((n=e.charCodeAt(i))>55295&&n<57344){if(!r){if(n>56319){(t-=3)>-1&&a.push(239,191,189);continue}if(i+1===o){(t-=3)>-1&&a.push(239,191,189);continue}r=n;continue}if(n<56320){(t-=3)>-1&&a.push(239,191,189),r=n;continue}n=65536+(r-55296<<10|n-56320)}else r&&(t-=3)>-1&&a.push(239,191,189);if(r=null,n<128){if((t-=1)<0)break;a.push(n)}else if(n<2048){if((t-=2)<0)break;a.push(n>>6|192,63&n|128)}else if(n<65536){if((t-=3)<0)break;a.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;a.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return a}function G(e){for(var t=[],n=0;n<e.length;++n)t.push(255&e.charCodeAt(n));return t}function V(e,t){for(var n,o,r,a=[],i=0;i<e.length&&!((t-=2)<0);++i)n=e.charCodeAt(i),o=n>>8,r=n%256,a.push(r),a.push(o);return a}function J(e){return Q.toByteArray(D(e))}function W(e,t,n,o){for(var r=0;r<o&&!(r+n>=t.length||r>=e.length);++r)t[r+n]=e[r];return r}function q(e){return e!==e}/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <http://feross.org>
 * @license  MIT
 */
var Q=n(84),H=n(85),Z=n(86);t.Buffer=a,t.SlowBuffer=m,t.INSPECT_MAX_BYTES=50,a.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}(),t.kMaxLength=o(),a.poolSize=8192,a._augment=function(e){return e.__proto__=a.prototype,e},a.from=function(e,t,n){return i(null,e,t,n)},a.TYPED_ARRAY_SUPPORT&&(a.prototype.__proto__=Uint8Array.prototype,a.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&a[Symbol.species]===a&&Object.defineProperty(a,Symbol.species,{value:null,configurable:!0})),a.alloc=function(e,t,n){return l(null,e,t,n)},a.allocUnsafe=function(e){return u(null,e)},a.allocUnsafeSlow=function(e){return u(null,e)},a.isBuffer=function(e){return!(null==e||!e._isBuffer)},a.compare=function(e,t){if(!a.isBuffer(e)||!a.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var n=e.length,o=t.length,r=0,i=Math.min(n,o);r<i;++r)if(e[r]!==t[r]){n=e[r],o=t[r];break}return n<o?-1:o<n?1:0},a.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(e,t){if(!Z(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return a.alloc(0);var n;if(void 0===t)for(t=0,n=0;n<e.length;++n)t+=e[n].length;var o=a.allocUnsafe(t),r=0;for(n=0;n<e.length;++n){var i=e[n];if(!a.isBuffer(i))throw new TypeError('"list" argument must be an Array of Buffers');i.copy(o,r),r+=i.length}return o},a.byteLength=g,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)y(this,t,t+1);return this},a.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)y(this,t,t+3),y(this,t+1,t+2);return this},a.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)y(this,t,t+7),y(this,t+1,t+6),y(this,t+2,t+5),y(this,t+3,t+4);return this},a.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?E(this,0,e):b.apply(this,arguments)},a.prototype.equals=function(e){if(!a.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===a.compare(this,e)},a.prototype.inspect=function(){var e="",n=t.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,n).match(/.{2}/g).join(" "),this.length>n&&(e+=" ... ")),"<Buffer "+e+">"},a.prototype.compare=function(e,t,n,o,r){if(!a.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===n&&(n=e?e.length:0),void 0===o&&(o=0),void 0===r&&(r=this.length),t<0||n>e.length||o<0||r>this.length)throw new RangeError("out of range index");if(o>=r&&t>=n)return 0;if(o>=r)return-1;if(t>=n)return 1;if(t>>>=0,n>>>=0,o>>>=0,r>>>=0,this===e)return 0;for(var i=r-o,s=n-t,l=Math.min(i,s),u=this.slice(o,r),f=e.slice(t,n),c=0;c<l;++c)if(u[c]!==f[c]){i=u[c],s=f[c];break}return i<s?-1:s<i?1:0},a.prototype.includes=function(e,t,n){return-1!==this.indexOf(e,t,n)},a.prototype.indexOf=function(e,t,n){return v(this,e,t,n,!0)},a.prototype.lastIndexOf=function(e,t,n){return v(this,e,t,n,!1)},a.prototype.write=function(e,t,n,o){if(void 0===t)o="utf8",n=this.length,t=0;else if(void 0===n&&"string"==typeof t)o=t,n=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(n)?(n|=0,void 0===o&&(o="utf8")):(o=n,n=void 0)}var r=this.length-t;if((void 0===n||n>r)&&(n=r),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");o||(o="utf8");for(var a=!1;;)switch(o){case"hex":return k(this,e,t,n);case"utf8":case"utf-8":return w(this,e,t,n);case"ascii":return T(this,e,t,n);case"latin1":case"binary":return I(this,e,t,n);case"base64":return S(this,e,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return P(this,e,t,n);default:if(a)throw new TypeError("Unknown encoding: "+o);o=(""+o).toLowerCase(),a=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var X=4096;a.prototype.slice=function(e,t){var n=this.length;e=~~e,t=void 0===t?n:~~t,e<0?(e+=n)<0&&(e=0):e>n&&(e=n),t<0?(t+=n)<0&&(t=0):t>n&&(t=n),t<e&&(t=e);var o;if(a.TYPED_ARRAY_SUPPORT)o=this.subarray(e,t),o.__proto__=a.prototype;else{var r=t-e;o=new a(r,void 0);for(var i=0;i<r;++i)o[i]=this[i+e]}return o},a.prototype.readUIntLE=function(e,t,n){e|=0,t|=0,n||R(e,t,this.length);for(var o=this[e],r=1,a=0;++a<t&&(r*=256);)o+=this[e+a]*r;return o},a.prototype.readUIntBE=function(e,t,n){e|=0,t|=0,n||R(e,t,this.length);for(var o=this[e+--t],r=1;t>0&&(r*=256);)o+=this[e+--t]*r;return o},a.prototype.readUInt8=function(e,t){return t||R(e,1,this.length),this[e]},a.prototype.readUInt16LE=function(e,t){return t||R(e,2,this.length),this[e]|this[e+1]<<8},a.prototype.readUInt16BE=function(e,t){return t||R(e,2,this.length),this[e]<<8|this[e+1]},a.prototype.readUInt32LE=function(e,t){return t||R(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},a.prototype.readUInt32BE=function(e,t){return t||R(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},a.prototype.readIntLE=function(e,t,n){e|=0,t|=0,n||R(e,t,this.length);for(var o=this[e],r=1,a=0;++a<t&&(r*=256);)o+=this[e+a]*r;return r*=128,o>=r&&(o-=Math.pow(2,8*t)),o},a.prototype.readIntBE=function(e,t,n){e|=0,t|=0,n||R(e,t,this.length);for(var o=t,r=1,a=this[e+--o];o>0&&(r*=256);)a+=this[e+--o]*r;return r*=128,a>=r&&(a-=Math.pow(2,8*t)),a},a.prototype.readInt8=function(e,t){return t||R(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},a.prototype.readInt16LE=function(e,t){t||R(e,2,this.length);var n=this[e]|this[e+1]<<8;return 32768&n?4294901760|n:n},a.prototype.readInt16BE=function(e,t){t||R(e,2,this.length);var n=this[e+1]|this[e]<<8;return 32768&n?4294901760|n:n},a.prototype.readInt32LE=function(e,t){return t||R(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},a.prototype.readInt32BE=function(e,t){return t||R(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},a.prototype.readFloatLE=function(e,t){return t||R(e,4,this.length),H.read(this,e,!0,23,4)},a.prototype.readFloatBE=function(e,t){return t||R(e,4,this.length),H.read(this,e,!1,23,4)},a.prototype.readDoubleLE=function(e,t){return t||R(e,8,this.length),H.read(this,e,!0,52,8)},a.prototype.readDoubleBE=function(e,t){return t||R(e,8,this.length),H.read(this,e,!1,52,8)},a.prototype.writeUIntLE=function(e,t,n,o){if(e=+e,t|=0,n|=0,!o){j(this,e,t,n,Math.pow(2,8*n)-1,0)}var r=1,a=0;for(this[t]=255&e;++a<n&&(r*=256);)this[t+a]=e/r&255;return t+n},a.prototype.writeUIntBE=function(e,t,n,o){if(e=+e,t|=0,n|=0,!o){j(this,e,t,n,Math.pow(2,8*n)-1,0)}var r=n-1,a=1;for(this[t+r]=255&e;--r>=0&&(a*=256);)this[t+r]=e/a&255;return t+n},a.prototype.writeUInt8=function(e,t,n){return e=+e,t|=0,n||j(this,e,t,1,255,0),a.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},a.prototype.writeUInt16LE=function(e,t,n){return e=+e,t|=0,n||j(this,e,t,2,65535,0),a.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):F(this,e,t,!0),t+2},a.prototype.writeUInt16BE=function(e,t,n){return e=+e,t|=0,n||j(this,e,t,2,65535,0),a.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):F(this,e,t,!1),t+2},a.prototype.writeUInt32LE=function(e,t,n){return e=+e,t|=0,n||j(this,e,t,4,4294967295,0),a.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):N(this,e,t,!0),t+4},a.prototype.writeUInt32BE=function(e,t,n){return e=+e,t|=0,n||j(this,e,t,4,4294967295,0),a.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):N(this,e,t,!1),t+4},a.prototype.writeIntLE=function(e,t,n,o){if(e=+e,t|=0,!o){var r=Math.pow(2,8*n-1);j(this,e,t,n,r-1,-r)}var a=0,i=1,s=0;for(this[t]=255&e;++a<n&&(i*=256);)e<0&&0===s&&0!==this[t+a-1]&&(s=1),this[t+a]=(e/i>>0)-s&255;return t+n},a.prototype.writeIntBE=function(e,t,n,o){if(e=+e,t|=0,!o){var r=Math.pow(2,8*n-1);j(this,e,t,n,r-1,-r)}var a=n-1,i=1,s=0;for(this[t+a]=255&e;--a>=0&&(i*=256);)e<0&&0===s&&0!==this[t+a+1]&&(s=1),this[t+a]=(e/i>>0)-s&255;return t+n},a.prototype.writeInt8=function(e,t,n){return e=+e,t|=0,n||j(this,e,t,1,127,-128),a.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},a.prototype.writeInt16LE=function(e,t,n){return e=+e,t|=0,n||j(this,e,t,2,32767,-32768),a.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):F(this,e,t,!0),t+2},a.prototype.writeInt16BE=function(e,t,n){return e=+e,t|=0,n||j(this,e,t,2,32767,-32768),a.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):F(this,e,t,!1),t+2},a.prototype.writeInt32LE=function(e,t,n){return e=+e,t|=0,n||j(this,e,t,4,2147483647,-2147483648),a.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):N(this,e,t,!0),t+4},a.prototype.writeInt32BE=function(e,t,n){return e=+e,t|=0,n||j(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),a.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):N(this,e,t,!1),t+4},a.prototype.writeFloatLE=function(e,t,n){return L(this,e,t,!0,n)},a.prototype.writeFloatBE=function(e,t,n){return L(this,e,t,!1,n)},a.prototype.writeDoubleLE=function(e,t,n){return z(this,e,t,!0,n)},a.prototype.writeDoubleBE=function(e,t,n){return z(this,e,t,!1,n)},a.prototype.copy=function(e,t,n,o){if(n||(n=0),o||0===o||(o=this.length),t>=e.length&&(t=e.length),t||(t=0),o>0&&o<n&&(o=n),o===n)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(o<0)throw new RangeError("sourceEnd out of bounds");o>this.length&&(o=this.length),e.length-t<o-n&&(o=e.length-t+n);var r,i=o-n;if(this===e&&n<t&&t<o)for(r=i-1;r>=0;--r)e[r+t]=this[r+n];else if(i<1e3||!a.TYPED_ARRAY_SUPPORT)for(r=0;r<i;++r)e[r+t]=this[r+n];else Uint8Array.prototype.set.call(e,this.subarray(n,n+i),t);return i},a.prototype.fill=function(e,t,n,o){if("string"==typeof e){if("string"==typeof t?(o=t,t=0,n=this.length):"string"==typeof n&&(o=n,n=this.length),1===e.length){var r=e.charCodeAt(0);r<256&&(e=r)}if(void 0!==o&&"string"!=typeof o)throw new TypeError("encoding must be a string");if("string"==typeof o&&!a.isEncoding(o))throw new TypeError("Unknown encoding: "+o)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(n<=t)return this;t>>>=0,n=void 0===n?this.length:n>>>0,e||(e=0);var i;if("number"==typeof e)for(i=t;i<n;++i)this[i]=e;else{var s=a.isBuffer(e)?e:K(new a(e,o).toString()),l=s.length;for(i=0;i<n-t;++i)this[i+t]=s[i%l]}return this};var ee=/[^+\/0-9A-Za-z-_]/g}).call(t,n(83))},function(e,t){var n;n=function(){return this}();try{n=n||Function("return this")()||(0,eval)("this")}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t,n){"use strict";function o(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var n=e.indexOf("=");return-1===n&&(n=t),[n,n===t?0:4-n%4]}function r(e){var t=o(e),n=t[0],r=t[1];return 3*(n+r)/4-r}function a(e,t,n){return 3*(t+n)/4-n}function i(e){var t,n,r=o(e),i=r[0],s=r[1],l=new p(a(e,i,s)),u=0,f=s>0?i-4:i;for(n=0;n<f;n+=4)t=c[e.charCodeAt(n)]<<18|c[e.charCodeAt(n+1)]<<12|c[e.charCodeAt(n+2)]<<6|c[e.charCodeAt(n+3)],l[u++]=t>>16&255,l[u++]=t>>8&255,l[u++]=255&t;return 2===s&&(t=c[e.charCodeAt(n)]<<2|c[e.charCodeAt(n+1)]>>4,l[u++]=255&t),1===s&&(t=c[e.charCodeAt(n)]<<10|c[e.charCodeAt(n+1)]<<4|c[e.charCodeAt(n+2)]>>2,l[u++]=t>>8&255,l[u++]=255&t),l}function s(e){return f[e>>18&63]+f[e>>12&63]+f[e>>6&63]+f[63&e]}function l(e,t,n){for(var o,r=[],a=t;a<n;a+=3)o=(e[a]<<16&16711680)+(e[a+1]<<8&65280)+(255&e[a+2]),r.push(s(o));return r.join("")}function u(e){for(var t,n=e.length,o=n%3,r=[],a=0,i=n-o;a<i;a+=16383)r.push(l(e,a,a+16383>i?i:a+16383));return 1===o?(t=e[n-1],r.push(f[t>>2]+f[t<<4&63]+"==")):2===o&&(t=(e[n-2]<<8)+e[n-1],r.push(f[t>>10]+f[t>>4&63]+f[t<<2&63]+"=")),r.join("")}t.byteLength=r,t.toByteArray=i,t.fromByteArray=u;for(var f=[],c=[],p="undefined"!=typeof Uint8Array?Uint8Array:Array,d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",h=0,m=d.length;h<m;++h)f[h]=d[h],c[d.charCodeAt(h)]=h;c["-".charCodeAt(0)]=62,c["_".charCodeAt(0)]=63},function(e,t){/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
t.read=function(e,t,n,o,r){var a,i,s=8*r-o-1,l=(1<<s)-1,u=l>>1,f=-7,c=n?r-1:0,p=n?-1:1,d=e[t+c];for(c+=p,a=d&(1<<-f)-1,d>>=-f,f+=s;f>0;a=256*a+e[t+c],c+=p,f-=8);for(i=a&(1<<-f)-1,a>>=-f,f+=o;f>0;i=256*i+e[t+c],c+=p,f-=8);if(0===a)a=1-u;else{if(a===l)return i?NaN:1/0*(d?-1:1);i+=Math.pow(2,o),a-=u}return(d?-1:1)*i*Math.pow(2,a-o)},t.write=function(e,t,n,o,r,a){var i,s,l,u=8*a-r-1,f=(1<<u)-1,c=f>>1,p=23===r?Math.pow(2,-24)-Math.pow(2,-77):0,d=o?0:a-1,h=o?1:-1,m=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(s=isNaN(t)?1:0,i=f):(i=Math.floor(Math.log(t)/Math.LN2),t*(l=Math.pow(2,-i))<1&&(i--,l*=2),t+=i+c>=1?p/l:p*Math.pow(2,1-c),t*l>=2&&(i++,l/=2),i+c>=f?(s=0,i=f):i+c>=1?(s=(t*l-1)*Math.pow(2,r),i+=c):(s=t*Math.pow(2,c-1)*Math.pow(2,r),i=0));r>=8;e[n+d]=255&s,d+=h,s/=256,r-=8);for(i=i<<r|s,u+=r;u>0;e[n+d]=255&i,d+=h,i/=256,u-=8);e[n+d-h]|=128*m}},function(e,t){var n={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==n.call(e)}},function(e,t,n){"use strict";var o,r,a=n(88),i=function(e){return e&&e.__esModule?e:{default:e}}(a);!function(a){if("object"===(0,i.default)(t))e.exports=a();else{o=a,void 0!==(r="function"==typeof o?o.call(t,n,t,e):o)&&(e.exports=r)}}(function(e){var t=function(e,t){return e+t&4294967295},n=function(e,n,o,r,a,i){return n=t(t(n,e),t(r,i)),t(n<<a|n>>>32-a,o)},o=function(e,t,o,r,a,i,s){return n(t&o|~t&r,e,t,a,i,s)},r=function(e,t,o,r,a,i,s){return n(t&r|o&~r,e,t,a,i,s)},a=function(e,t,o,r,a,i,s){return n(t^o^r,e,t,a,i,s)},i=function(e,t,o,r,a,i,s){return n(o^(t|~r),e,t,a,i,s)},s=function(e,n){var s=e[0],l=e[1],u=e[2],f=e[3];s=o(s,l,u,f,n[0],7,-680876936),f=o(f,s,l,u,n[1],12,-389564586),u=o(u,f,s,l,n[2],17,606105819),l=o(l,u,f,s,n[3],22,-1044525330),s=o(s,l,u,f,n[4],7,-176418897),f=o(f,s,l,u,n[5],12,1200080426),u=o(u,f,s,l,n[6],17,-1473231341),l=o(l,u,f,s,n[7],22,-45705983),s=o(s,l,u,f,n[8],7,1770035416),f=o(f,s,l,u,n[9],12,-1958414417),u=o(u,f,s,l,n[10],17,-42063),l=o(l,u,f,s,n[11],22,-1990404162),s=o(s,l,u,f,n[12],7,1804603682),f=o(f,s,l,u,n[13],12,-40341101),u=o(u,f,s,l,n[14],17,-1502002290),l=o(l,u,f,s,n[15],22,1236535329),s=r(s,l,u,f,n[1],5,-165796510),f=r(f,s,l,u,n[6],9,-1069501632),u=r(u,f,s,l,n[11],14,643717713),l=r(l,u,f,s,n[0],20,-373897302),s=r(s,l,u,f,n[5],5,-701558691),f=r(f,s,l,u,n[10],9,38016083),u=r(u,f,s,l,n[15],14,-660478335),l=r(l,u,f,s,n[4],20,-405537848),s=r(s,l,u,f,n[9],5,568446438),f=r(f,s,l,u,n[14],9,-1019803690),u=r(u,f,s,l,n[3],14,-187363961),l=r(l,u,f,s,n[8],20,1163531501),s=r(s,l,u,f,n[13],5,-1444681467),f=r(f,s,l,u,n[2],9,-51403784),u=r(u,f,s,l,n[7],14,1735328473),l=r(l,u,f,s,n[12],20,-1926607734),s=a(s,l,u,f,n[5],4,-378558),f=a(f,s,l,u,n[8],11,-2022574463),u=a(u,f,s,l,n[11],16,1839030562),l=a(l,u,f,s,n[14],23,-35309556),s=a(s,l,u,f,n[1],4,-1530992060),f=a(f,s,l,u,n[4],11,1272893353),u=a(u,f,s,l,n[7],16,-155497632),l=a(l,u,f,s,n[10],23,-1094730640),s=a(s,l,u,f,n[13],4,681279174),f=a(f,s,l,u,n[0],11,-358537222),u=a(u,f,s,l,n[3],16,-722521979),l=a(l,u,f,s,n[6],23,76029189),s=a(s,l,u,f,n[9],4,-640364487),f=a(f,s,l,u,n[12],11,-421815835),u=a(u,f,s,l,n[15],16,530742520),l=a(l,u,f,s,n[2],23,-995338651),s=i(s,l,u,f,n[0],6,-198630844),f=i(f,s,l,u,n[7],10,1126891415),u=i(u,f,s,l,n[14],15,-1416354905),l=i(l,u,f,s,n[5],21,-57434055),s=i(s,l,u,f,n[12],6,1700485571),f=i(f,s,l,u,n[3],10,-1894986606),u=i(u,f,s,l,n[10],15,-1051523),l=i(l,u,f,s,n[1],21,-2054922799),s=i(s,l,u,f,n[8],6,1873313359),f=i(f,s,l,u,n[15],10,-30611744),u=i(u,f,s,l,n[6],15,-1560198380),l=i(l,u,f,s,n[13],21,1309151649),s=i(s,l,u,f,n[4],6,-145523070),f=i(f,s,l,u,n[11],10,-1120210379),u=i(u,f,s,l,n[2],15,718787259),l=i(l,u,f,s,n[9],21,-343485551),e[0]=t(s,e[0]),e[1]=t(l,e[1]),e[2]=t(u,e[2]),e[3]=t(f,e[3])},l=function(e){var t,n=[];for(t=0;t<64;t+=4)n[t>>2]=e.charCodeAt(t)+(e.charCodeAt(t+1)<<8)+(e.charCodeAt(t+2)<<16)+(e.charCodeAt(t+3)<<24);return n},u=function(e){var t,n=[];for(t=0;t<64;t+=4)n[t>>2]=e[t]+(e[t+1]<<8)+(e[t+2]<<16)+(e[t+3]<<24);return n},f=function(e){var t,n,o,r,a,i,u=e.length,f=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=u;t+=64)s(f,l(e.substring(t-64,t)));for(e=e.substring(t-64),n=e.length,o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],t=0;t<n;t+=1)o[t>>2]|=e.charCodeAt(t)<<(t%4<<3);if(o[t>>2]|=128<<(t%4<<3),t>55)for(s(f,o),t=0;t<16;t+=1)o[t]=0;return r=8*u,r=r.toString(16).match(/(.*?)(.{0,8})$/),a=parseInt(r[2],16),i=parseInt(r[1],16)||0,o[14]=a,o[15]=i,s(f,o),f},c=function(e){var t,n,o,r,a,i,l=e.length,f=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=l;t+=64)s(f,u(e.subarray(t-64,t)));for(e=t-64<l?e.subarray(t-64):new Uint8Array(0),n=e.length,o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],t=0;t<n;t+=1)o[t>>2]|=e[t]<<(t%4<<3);if(o[t>>2]|=128<<(t%4<<3),t>55)for(s(f,o),t=0;t<16;t+=1)o[t]=0;return r=8*l,r=r.toString(16).match(/(.*?)(.{0,8})$/),a=parseInt(r[2],16),i=parseInt(r[1],16)||0,o[14]=a,o[15]=i,s(f,o),f},p=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"],d=function(e){var t,n="";for(t=0;t<4;t+=1)n+=p[e>>8*t+4&15]+p[e>>8*t&15];return n},h=function(e){var t;for(t=0;t<e.length;t+=1)e[t]=d(e[t]);return e.join("")},m=function(){this.reset()};return"5d41402abc4b2a76b9719d911017c592"!==function(e){return h(f(e))}("hello")&&(t=function(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}),m.prototype.append=function(e){return/[\u0080-\uFFFF]/.test(e)&&(e=unescape(encodeURIComponent(e))),this.appendBinary(e),this},m.prototype.appendBinary=function(e){this._buff+=e,this._length+=e.length;var t,n=this._buff.length;for(t=64;t<=n;t+=64)s(this._state,l(this._buff.substring(t-64,t)));return this._buff=this._buff.substr(t-64),this},m.prototype.end=function(e){var t,n,o=this._buff,r=o.length,a=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<r;t+=1)a[t>>2]|=o.charCodeAt(t)<<(t%4<<3);return this._finish(a,r),n=e?this._state:h(this._state),this.reset(),n},m.prototype._finish=function(e,t){var n,o,r,a=t;if(e[a>>2]|=128<<(a%4<<3),a>55)for(s(this._state,e),a=0;a<16;a+=1)e[a]=0;n=8*this._length,n=n.toString(16).match(/(.*?)(.{0,8})$/),o=parseInt(n[2],16),r=parseInt(n[1],16)||0,e[14]=o,e[15]=r,s(this._state,e)},m.prototype.reset=function(){return this._buff="",this._length=0,this._state=[1732584193,-271733879,-1732584194,271733878],this},m.prototype.destroy=function(){delete this._state,delete this._buff,delete this._length},m.hash=function(e,t){/[\u0080-\uFFFF]/.test(e)&&(e=unescape(encodeURIComponent(e)));var n=f(e);return t?n:h(n)},m.hashBinary=function(e,t){var n=f(e);return t?n:h(n)},m.ArrayBuffer=function(){this.reset()},m.ArrayBuffer.prototype.append=function(e){var t,n=this._concatArrayBuffer(this._buff,e),o=n.length;for(this._length+=e.byteLength,t=64;t<=o;t+=64)s(this._state,u(n.subarray(t-64,t)));return this._buff=t-64<o?n.subarray(t-64):new Uint8Array(0),this},m.ArrayBuffer.prototype.end=function(e){var t,n,o=this._buff,r=o.length,a=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<r;t+=1)a[t>>2]|=o[t]<<(t%4<<3);return this._finish(a,r),n=e?this._state:h(this._state),this.reset(),n},m.ArrayBuffer.prototype._finish=m.prototype._finish,m.ArrayBuffer.prototype.reset=function(){return this._buff=new Uint8Array(0),this._length=0,this._state=[1732584193,-271733879,-1732584194,271733878],this},m.ArrayBuffer.prototype.destroy=m.prototype.destroy,m.ArrayBuffer.prototype._concatArrayBuffer=function(e,t){var n=e.length,o=new Uint8Array(n+t.byteLength);return o.set(e),o.set(new Uint8Array(t),n),o},m.ArrayBuffer.hash=function(e,t){var n=c(new Uint8Array(e));return t?n:h(n)},m})},function(e,t,n){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}t.__esModule=!0;var r=n(89),a=o(r),i=n(91),s=o(i),l="function"==typeof s.default&&"symbol"==typeof a.default?function(e){return typeof e}:function(e){return e&&"function"==typeof s.default&&e.constructor===s.default&&e!==s.default.prototype?"symbol":typeof e};t.default="function"==typeof s.default&&"symbol"===l(a.default)?function(e){return void 0===e?"undefined":l(e)}:function(e){return e&&"function"==typeof s.default&&e.constructor===s.default&&e!==s.default.prototype?"symbol":void 0===e?"undefined":l(e)}},function(e,t,n){e.exports={default:n(90),__esModule:!0}},function(e,t,n){n(37),n(46),e.exports=n(32).f("iterator")},function(e,t,n){e.exports={default:n(92),__esModule:!0}},function(e,t,n){n(93),n(36),n(99),n(100),e.exports=n(2).Symbol},function(e,t,n){"use strict";var o=n(0),r=n(8),a=n(7),i=n(11),s=n(40),l=n(94).KEY,u=n(16),f=n(29),c=n(19),p=n(18),d=n(1),h=n(32),m=n(33),g=n(95),b=n(96),y=n(3),v=n(6),x=n(45),k=n(9),w=n(26),T=n(17),I=n(41),S=n(97),P=n(98),_=n(52),E=n(5),C=n(27),U=P.f,A=E.f,O=S.f,B=o.Symbol,R=o.JSON,j=R&&R.stringify,F=d("_hidden"),N=d("toPrimitive"),M={}.propertyIsEnumerable,L=f("symbol-registry"),z=f("symbols"),D=f("op-symbols"),$=Object.prototype,Y="function"==typeof B&&!!_.f,K=o.QObject,G=!K||!K.prototype||!K.prototype.findChild,V=a&&u(function(){return 7!=I(A({},"a",{get:function(){return A(this,"a",{value:7}).a}})).a})?function(e,t,n){var o=U($,t);o&&delete $[t],A(e,t,n),o&&e!==$&&A($,t,o)}:A,J=function(e){var t=z[e]=I(B.prototype);return t._k=e,t},W=Y&&"symbol"==typeof B.iterator?function(e){return"symbol"==typeof e}:function(e){return e instanceof B},q=function(e,t,n){return e===$&&q(D,t,n),y(e),t=w(t,!0),y(n),r(z,t)?(n.enumerable?(r(e,F)&&e[F][t]&&(e[F][t]=!1),n=I(n,{enumerable:T(0,!1)})):(r(e,F)||A(e,F,T(1,{})),e[F][t]=!0),V(e,t,n)):A(e,t,n)},Q=function(e,t){y(e);for(var n,o=g(t=k(t)),r=0,a=o.length;a>r;)q(e,n=o[r++],t[n]);return e},H=function(e,t){return void 0===t?I(e):Q(I(e),t)},Z=function(e){var t=M.call(this,e=w(e,!0));return!(this===$&&r(z,e)&&!r(D,e))&&(!(t||!r(this,e)||!r(z,e)||r(this,F)&&this[F][e])||t)},X=function(e,t){if(e=k(e),t=w(t,!0),e!==$||!r(z,t)||r(D,t)){var n=U(e,t);return!n||!r(z,t)||r(e,F)&&e[F][t]||(n.enumerable=!0),n}},ee=function(e){for(var t,n=O(k(e)),o=[],a=0;n.length>a;)r(z,t=n[a++])||t==F||t==l||o.push(t);return o},te=function(e){for(var t,n=e===$,o=O(n?D:k(e)),a=[],i=0;o.length>i;)!r(z,t=o[i++])||n&&!r($,t)||a.push(z[t]);return a};Y||(B=function(){if(this instanceof B)throw TypeError("Symbol is not a constructor!");var e=p(arguments.length>0?arguments[0]:void 0),t=function(n){this===$&&t.call(D,n),r(this,F)&&r(this[F],e)&&(this[F][e]=!1),V(this,e,T(1,n))};return a&&G&&V($,e,{configurable:!0,set:t}),J(e)},s(B.prototype,"toString",function(){return this._k}),P.f=X,E.f=q,n(53).f=S.f=ee,n(34).f=Z,_.f=te,a&&!n(10)&&s($,"propertyIsEnumerable",Z,!0),h.f=function(e){return J(d(e))}),i(i.G+i.W+i.F*!Y,{Symbol:B});for(var ne="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),oe=0;ne.length>oe;)d(ne[oe++]);for(var re=C(d.store),ae=0;re.length>ae;)m(re[ae++]);i(i.S+i.F*!Y,"Symbol",{for:function(e){return r(L,e+="")?L[e]:L[e]=B(e)},keyFor:function(e){if(!W(e))throw TypeError(e+" is not a symbol!");for(var t in L)if(L[t]===e)return t},useSetter:function(){G=!0},useSimple:function(){G=!1}}),i(i.S+i.F*!Y,"Object",{create:H,defineProperty:q,defineProperties:Q,getOwnPropertyDescriptor:X,getOwnPropertyNames:ee,getOwnPropertySymbols:te});var ie=u(function(){_.f(1)});i(i.S+i.F*ie,"Object",{getOwnPropertySymbols:function(e){return _.f(x(e))}}),R&&i(i.S+i.F*(!Y||u(function(){var e=B();return"[null]"!=j([e])||"{}"!=j({a:e})||"{}"!=j(Object(e))})),"JSON",{stringify:function(e){for(var t,n,o=[e],r=1;arguments.length>r;)o.push(arguments[r++]);if(n=t=o[1],(v(t)||void 0!==e)&&!W(e))return b(t)||(t=function(e,t){if("function"==typeof n&&(t=n.call(this,e,t)),!W(t))return t}),o[1]=t,j.apply(R,o)}}),B.prototype[N]||n(4)(B.prototype,N,B.prototype.valueOf),c(B,"Symbol"),c(Math,"Math",!0),c(o.JSON,"JSON",!0)},function(e,t,n){var o=n(18)("meta"),r=n(6),a=n(8),i=n(5).f,s=0,l=Object.isExtensible||function(){return!0},u=!n(16)(function(){return l(Object.preventExtensions({}))}),f=function(e){i(e,o,{value:{i:"O"+ ++s,w:{}}})},c=function(e,t){if(!r(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!a(e,o)){if(!l(e))return"F";if(!t)return"E";f(e)}return e[o].i},p=function(e,t){if(!a(e,o)){if(!l(e))return!0;if(!t)return!1;f(e)}return e[o].w},d=function(e){return u&&h.NEED&&l(e)&&!a(e,o)&&f(e),e},h=e.exports={KEY:o,NEED:!1,fastKey:c,getWeak:p,onFreeze:d}},function(e,t,n){var o=n(27),r=n(52),a=n(34);e.exports=function(e){var t=o(e),n=r.f;if(n)for(var i,s=n(e),l=a.f,u=0;s.length>u;)l.call(e,i=s[u++])&&t.push(i);return t}},function(e,t,n){var o=n(13);e.exports=Array.isArray||function(e){return"Array"==o(e)}},function(e,t,n){var o=n(9),r=n(53).f,a={}.toString,i="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],s=function(e){try{return r(e)}catch(e){return i.slice()}};e.exports.f=function(e){return i&&"[object Window]"==a.call(e)?s(e):r(o(e))}},function(e,t,n){var o=n(34),r=n(17),a=n(9),i=n(26),s=n(8),l=n(39),u=Object.getOwnPropertyDescriptor;t.f=n(7)?u:function(e,t){if(e=a(e),t=i(t,!0),l)try{return u(e,t)}catch(e){}if(s(e,t))return r(!o.f.call(e,t),e[t])}},function(e,t,n){n(33)("asyncIterator")},function(e,t,n){n(33)("observable")},function(e,t,n){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0});var r=n(22),a=o(r),i=n(20),s=o(i),u=(n(21).default,n(35).default),f=function(e){function t(){return window.nxt&&window.nxt.config?window.nxt.config:e.configInst?e.configInst:void 0}function n(){var e="http://"+location.host,t=s.default.getCookie("apiVersion"),n="/upload/complete";return t&&"mamcore2.3"===t&&(n="/sflud/v1/upload/complete"),e+n}function o(e){var o={TaskName:e.metadata.name,TaskGuid:e.taskId,TransType:"Upload",TransFile:[],UserInfo:{UserId:nxt.user.current.id.toString(),UserName:nxt.user.current.nickName||nxt.user.current.loginName,UserCode:nxt.user.current.userCode},ExtendeAttr:[]};return 1==t().vtubeInfo.importType&&(o.CallBackUrl=n()),e.serverInfo&&(o.ServerInfo={HostName:e.serverInfo.hostName,Port:e.serverInfo.port,Scheme:e.serverInfo.scheme,UserName:e.serverInfo.userName,Password:e.serverInfo.password,PathRoot:e.serverInfo.pathRoot}),o.ExtendeAttr=_.map(e.metadata.field,function(e){var t={ItemCode:e.fieldName,ItemName:e.alias};if(8==e.controlType)if(null!=e.value&&""!=e.value&&"[]"!=e.value)try{t.Value=JSON.parse(e.value)[0]}catch(e){t.Value=""}else t.Value="";else t.Value=e.value||"";return t}),o.ExtendeAttr.push({ItemCode:"tree",ItemName:"目录树",Value:e.tree}),_.remove(o.ExtendeAttr,{ItemCode:"cliptype"}),o.ExtendeAttr.push({ItemCode:"cliptype",ItemName:"素材类型",Value:e.entityType}),o}var r=this,i="http://127.0.0.1:8084/";this.openFileSelector=function(e,n){$.ajax({url:i+"request/getuploadfiles?user_token=&filetype=all",success:function(n){var o=_.isString(n)?JSON.parse(n):n;if(0!==o.FileCount){var r=[];_.forEach(o.FileList,function(e){var n="."+s.default.getExtension(e.FilePath);r.push({entityType:s.default.getTypeByExt(n,t()).code,fileName:s.default.getFullFileName(e.FilePath),metadata:{name:s.default.getFileName(e.FilePath),ext:n},status:"added",progress:0,file:e})}),e(r)}},error:n||function(e){console.error(e);var n=t().vtubeDownloadPath||t().server+"/assets/Sobey_vRocket_v2.0_Setup.exe";s.default.prompt(l("upload.clientInstallTip",'<p>打开客户端失败</p><p>请确定已经正常开启客户端或重启客户端再重试。</p><p>如没有安装，请点击下面地址。</p><p><a href="${path}"><i class="icon iconfont icon-iconfontxiazai" style="margin-right:5px"></i>点击客户端下载</a></p>',{path:n}))}})},this.createTask=function(e,t){var n=[];switch(t.taskType){case 1:_.isArray(e)||(e=[e]),_.forEach(e,function(e){e.file?(e.files=[{file:e.file,fileName:e.fileName,fileSize:e.file.FileSize}],delete e.file,n.push(e)):e.files&&e.files.length>0&&(e.files=_.map(e.files,function(e){return{file:e.file,fileName:e.fileName,fileSize:e.fileSize}}),n.push(e))});break;case 2:case 3:n.push(e)}_.forEach(n,function(e){e.taskType=t.taskType,e.transferType=t.transferType,e.targetFolder=t.targetFolder,e.relationContentType=t.relationContentType,e.relationContentId=t.relationContentId,r.addTask(e)})},this.addTask=function(n){var r=s.default.getCookie("apiVersion"),f="/upload/multipart/init?token="+e.loginToken;r&&"mamcore2.3"===r&&(f="/sflud/v1/upload/multipart/init?token="+e.loginToken),u.post(t().server+f,n).then(function(e){function r(n){if(t().isHandleHttpPath&&e&&e.ossClientInfo){var o=window.location.hostname,r=n.absolutePath.split("@");if(r.length>1){var a=r[1].indexOf(":")>-1?":":"/";n.absolutePath=r[0]+"@"+o+r[1].substr(r[1].indexOf(a))}}return e&&e.ossClientInfo?n.absolutePath:n.relativePath}e=e.data;var u=o(e);switch(u.ExtendData=e.userToken,n.taskType){case 1:u.TransFile.push({SourceFile:n.files[0].file.FilePath,DestFile:r(e.files[0])});break;case 2:case 3:_.forEach(e.files,function(e){u.TransFile.push({SourceFile:e.fileName,DestFile:r(e)})})}$.ajax({url:i+"request/addtask?user_token=",type:"POST",contentType:"application/json",data:(0,a.default)(u),success:function(e){"string"==typeof e&&(e=JSON.parse(e)),1==e.Result?s.default.msgOk(l("upload.addTaskOk","添加任务成功")):s.default.prompt(l("upload.addTaskError","添加任务失败：")+e.Msg),console.info(e)},error:function(e){console.info(e),s.default.prompt(l("upload.addTaskError","添加任务失败"))}})},function(e){s.default.prompt(l("upload.uploadError","上传失败：")+e.data.desc)})}};t.default=f},function(e,t,n){"use strict";var o=n(54),r=function(e){return e&&e.__esModule?e:{default:e}}(o),a=n(103).default,i=n(20).default,s=n(21).default,l=n(35).default,u={loadFrameworkScripts:function(e,t){return new r.default(function(n,o){i.loadScripts([e.basePath+"/libs/jquery.min.js",e.basePath+"/libs/lodash.min.js",e.basePath+"/libs/angular.min.js"]).then(function(){t?i.loadScripts([e.basePath+"/libs/mam-base/dist/mam-base.js",e.basePath+"/libs/oss/aliyun-oss-sdk.min.js",e.basePath+"/libs/oss-s3/aws-sdk.min.js",e.basePath+"/libs/oos/oos-sdk.min.js",e.basePath+"/libs/font-awesome/css/font-awesome.min.css",e.basePath+"/libs/bootstrap/css/bootstrap.min.css",e.basePath+"/libs/iconfont/iconfont.css"]).then(function(){n(e)}):i.loadScripts([e.basePath+"/libs/angular-ui-router.min.js",e.basePath+"/libs/angular-sanitize.min.js",e.basePath+"/libs/angular-ui/ui-bootstrap-tpls-2.5.0.min.js",e.basePath+"/libs/datetimepicker/jquery.datetimepicker.full.min.js",e.basePath+"/libs/ng-tags-input/ng-tags-input.js",e.basePath+"/libs/ui-select/select.min.js",e.basePath+"/libs/mam-base/dist/mam-base.js",e.basePath+"/libs/mam-timecode-convert/dist/mam-timecode-convert.min.js",e.basePath+"/libs/mam-ng/dist/mam-ng.js",e.basePath+"/libs/mam-metadata/dist/mam-metadata.js",e.basePath+"/libs/oss/aliyun-oss-sdk.min.js",e.basePath+"/libs/oss-s3/aws-sdk.min.js",e.basePath+"/libs/oos/oos-sdk.min.js",e.basePath+"/libs/font-awesome/css/font-awesome.min.css",e.basePath+"/libs/bootstrap/css/bootstrap.min.css",e.basePath+"/libs/datetimepicker/jquery.datetimepicker.css",e.basePath+"/libs/ng-tags-input/ng-tags-input.min.css",e.basePath+"/libs/ng-tags-input/ng-tags-input.bootstrap.min.css",e.basePath+"/libs/ui-select/select.min.css",e.basePath+"/libs/iconfont/iconfont.css"]).then(function(){n(e)})})})},loadConfig:function(e){return new r.default(function(t,n){if(window.nxt=window.nxt||{app:{},task:{},config:{},entity:{},currentUser:null},!window.nxt.config||$.isEmptyObject(window.nxt.config)){var o=e.server+"/config/get-base-info";e.loginToken&&(o+="?token="+e.loginToken),l.get(o).then(function(n){!0===n.success&&(window.nxt.config=n.data.config,nxt.config.server||(nxt.config.server=e.server),nxt.user.current=n.data.currentUser,mam.entity.types=n.data.config.entityTypes,t(e))})}else t(e)})},initUploadFramework:function(e){var t=$.Deferred();return u.loadFrameworkScripts(e).then(function(e){u.loadConfig(e).then(function(e){u.initApp(e),t.resolve()})}),t},initNoNg:function(e){var t=$.Deferred();return u.loadFrameworkScripts(e,!0).then(function(e){u.loadConfig(e).then(function(e){u.uploader=new a(e),t.resolve()})}),t},initApp:function(e){u.uploader=new a(e);var t=angular.module("mam-upload",["mam-metadata","mam-ng","ngTagsInput","ui.select","ngSanitize","ui.bootstrap"]);return mam.ng.config(t),e.el&&mam.ng.httpProvider(t,{server:nxt.config.server}),n(109),n(110),n(112),n(114),n(116),n(117),n(118),e.el&&(t.controller("mamUploadCtrl",["$scope","$http","$uibModal",function(e,t,n){u.uploader.init(n)}]),angular.bootstrap($(e.el)[0],["mam-upload"])),e.loginToken,t}};s.mamUpload=u;var f=window.define;"function"==typeof f&&f.amd?f("mam-upload",[],u):window.mamUpload=u},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(22),r=function(e){return e&&e.__esModule?e:{default:e}}(o),a=n(81),i=n(101),s=n(20).default;n(104);var l=function(e){function t(e,t){var n=JSON.parse((0,r.default)(e));return n.value=t,[n]}function n(t,n){var r="general";for(var a in o.taskType)if(o.taskType.hasOwnProperty(a)&&o.taskType[a]===t.uploadParams.taskType){r=a;break}s.asyncLoadScript(e.basePath+"/directives/"+r+"/modal-"+r+".js",function(){$.get(e.basePath+"/directives/"+r+"/modal-"+r+".html").then(function(e){o.$uibModal.open({template:e,controller:"uploadModal"+r+"Ctrl",windowClass:"modal-upload-"+r,resolve:{opts:function(){return t}}}).result.then(function(e){n(e)})})})}var o=this;this.transferType={web:1,vtube:2},this.taskType={general:1,group:2,picturePackage:3,materialPackage:4},this.relationContentType={none:0,manuscript:1,dataset:2,attachment:3},this.modules={yunpan:"yunpan",webupload:"webupload",necs:"necs"},this.module=this.modules.yunpan,this.web=new a.default(e),this.vtube=new i.default(e),this.transfers={},this.transfers[this.transferType.web]=this.web,this.transfers[this.transferType.vtube]=this.vtube,this.init=function(e,t){window.OSS&&(this.web.OSS=window.OSS),e&&(o.$uibModal=e),t&&(o.web.OSS=t)},this.createTask=function(e){var r=$.extend({},{module:o.module,taskType:o.taskType.general,transferType:o.transferType.web,targetFolder:"",relationContentType:o.relationContentType.none,relationContentId:"",writeMetadataBefore:function(){}},e),a=o.transfers[r.transferType];a.openFileSelector(function(i){if(!1!==r.writeMetadataBefore(i,r)){n({uploadParams:r,files:i,extraField:e.extraField},function(e){if(null!=e)if(4===r.relationContentType&&e.files&&e.files.length>0){var n=[],i=_.find(e.metadata.field,{fieldName:"name_"}),s=e.relationContentId;e.files.map(function(e){var o=e.fileName.substring(0,e.fileName.lastIndexOf(".")),r=e.fileName.substring(e.fileName.lastIndexOf("."),e.fileName.length),a="image"===e.file.type.substring(0,e.file.type.indexOf("/"))?"picture":e.file.type.substring(0,e.file.type.indexOf("/"));n.push({entityType:a,files:[e],metadata:{name:o,ext:r,field:t(i,o)},status:"added",progress:0})}),n.map(function(e){r.taskType=o.taskType.general,r.relationContentId=s,a.createTask(e,r)})}else a.createTask(e,r)})}},"",r.accept)},this.validateExt=function(e){if(3===nxt.config.uploadExtensionLimit.type){if(e===_.find(nxt.config.uploadExtensionLimit.extensions,function(t){return t===e}))return!1}else if(2===nxt.config.uploadExtensionLimit.type&&e!==_.find(nxt.config.uploadExtensionLimit.extensions,function(t){return t===e}))return!1;return!0}};t.default=l},function(e,t,n){var o=n(105);"string"==typeof o&&(o=[[e.i,o,""]]);var r={};r.transform=void 0;n(107)(o,r);o.locals&&(e.exports=o.locals)},function(e,t,n){t=e.exports=n(106)(void 0),t.push([e.i,".modal{position:fixed;top:0;right:0;bottom:0;left:0;z-index:1050;display:none;overflow:hidden;-webkit-overflow-scrolling:touch;outline:0}@media (-ms-high-contrast:none),screen and (-ms-high-contrast:active){.modal .modal-dialog{height:1px}}.modal .modal-dialog{display:flex;min-height:100vh;min-width:100vw;align-items:center;justify-content:center;margin:0 auto;padding:30px 0}.modal .modal-dialog .modal-content{display:flex;flex-direction:column}.modal .modal-dialog .modal-header{height:56px;min-height:56px;background:#212121;color:#fff}.modal .modal-dialog .modal-header button{outline:none;text-shadow:unset;transition:all .2s}.modal .modal-dialog .modal-body{overflow:auto;flex:1 0 auto}.modal .modal-dialog .modal-body a:hover{color:#e98b11}.modal .modal-dialog .modal-body .nav-tabs li a:hover{color:#fff}.modal .modal-dialog .modal-footer{min-height:65px;text-align:center}.modal .modal-dialog .modal-footer button{margin:0 5px}.modal .mam-metadata-form .mmf-group .mmf-head{color:#333}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-datetime input,.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-nanosecond-to-timecode input,.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-size input,.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-text input{background:#fff;border-color:#ccc;color:#555}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-datetime input[readonly],.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-nanosecond-to-timecode input[readonly],.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-size input[readonly],.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-text input[readonly]{background:#eee}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-textarea textarea,.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-tree .items{background:#fff;border-color:#ccc;color:#555}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-tag .tags-input .tags{background:#fff;border-color:#ccc}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-tag .tags-input .tags input{background-color:#fff;color:#555}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-tag .tags-input .tags .tag-item{background:#337ab7}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-select .form-control{background-color:#2c2c2c;background-image:none;border:1px solid #444}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-select .ui-select-bootstrap .ui-select-choices-row.active>span{background-color:#e98b11}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-select .ui-select-bootstrap .ui-select-choices-row>span{color:#fff}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-select .ui-select-bootstrap .ui-select-choices-row>span:hover{background-color:#62b0ef}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-select .dropdown-menu{background-color:#5c5c5c}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-select .btn-default{color:#555;background-color:#fff;border-color:#ccc}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-select .btn-default .close{color:#fff}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-select .btn[disabled]{background-color:#b4b4b4!important;color:#555!important}body>.ui-select-bootstrap.open{z-index:1050!important}.btn-icon{border:none;background:none;outline:none;padding:0}.modal-upload-general .modal-body{width:980px;height:600px;display:flex}.modal-upload-general .box-header-title{font-size:15px;padding:0 14px;height:36px;line-height:36px;display:inline-block;color:#fff;border-radius:3px 3px 0 0;background:#337ab7}.modal-upload-general .task-list-box{display:flex;flex-direction:column;width:430px;min-width:430px;margin-right:10px;position:relative;overflow:hidden}.modal-upload-general .task-list-box .box-header{display:flex;align-items:flex-end}.modal-upload-general .task-list-box .box-header .box-header-btns{display:flex;justify-content:flex-end;flex:1 0 auto}.modal-upload-general .task-list-box .box-header .box-header-btns button{outline:none;border:none;color:#fff;width:42px;height:32px}.modal-upload-general .task-list-box .box-header .box-header-btns button:hover i{transform:scale(1.2)}.modal-upload-general .task-list-box .box-header .box-header-btns button i{transition:all .3s;font-size:18px}.modal-upload-general .task-list-box .box-header .box-header-btns button span{display:none}.modal-upload-general .task-list-box .box-header .box-header-btns .delete{background:#4fcaca}.modal-upload-general .task-list-box .box-header .box-header-btns .add{background:#ccc}.modal-upload-general .task-list-box .box-header .box-header-btns .edit{background:#feb872}.modal-upload-general .task-list-box .box-header .box-header-btns .upload{background:#3c0}.modal-upload-general .task-list-box .box-toolbar{height:47px;min-height:47px;background:#f9f9f9;border:1px solid #e9e9e9;border-bottom:none}.modal-upload-general .task-list-box .box-toolbar .box-toolbar-container{display:flex;align-items:center;padding-left:15px;padding-right:8px;height:42px;min-height:42px;box-shadow:0 2px 1px rgba(0,0,0,.1)}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter{flex:1 0 auto;text-align:right}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox{min-width:inherit}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox:after,.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox:before{display:none}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox.unchecked span{opacity:.6;transform:scale(.8)}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox.unchecked span:hover{opacity:.9;transform:scale(.8)}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox.checked span{transform:scale(1);opacity:1}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox .tooltip{min-width:40px}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox span{margin:0 4px 0 0;display:inline-block;min-width:22px;padding:0 4px;text-align:center;transition:all .3s;border-radius:2px;color:#fff}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox span:hover{opacity:1;transform:scale(1)}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox:first-child span{background-color:#9cf}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox:nth-child(2) span{background-color:#6c9}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox:nth-child(3) span{background-color:#fc6}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox:nth-child(4) span{background-color:#c9f}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox:nth-child(5) span{background-color:#c90}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox svg{display:none}.modal-upload-general .task-list-box .list-list{flex:1 0 auto;border:1px solid #e9e9e9;border-top:none}.modal-upload-general .task-list-box .list-list .item{display:flex;height:50px;align-items:center;position:relative;border-bottom:1px solid rgba(0,0,0,.1)}.modal-upload-general .task-list-box .list-list .item:nth-child(2n){background:#f9f9f9}.modal-upload-general .task-list-box .list-list .item:hover .op-btns{opacity:1}.modal-upload-general .task-list-box .list-list .item.active .title a{font-weight:700;color:#22262e}.modal-upload-general .task-list-box .list-list .item.validate-error .title a{color:red}.modal-upload-general .task-list-box .list-list .item .mam-checkbox{margin-left:15px;margin-right:8px}.modal-upload-general .task-list-box .list-list .item .no{margin-right:4px}.modal-upload-general .task-list-box .list-list .item .title{flex:1 0 auto;width:1px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.modal-upload-general .task-list-box .list-list .item .title a{display:block;height:50px;line-height:50px;transition:none}.modal-upload-general .task-list-box .list-list .item .type{display:inline-block;background:#00a8ff;color:#fff;width:18px;height:18px;border-radius:2px;text-align:center;line-height:18px;margin-right:4px}.modal-upload-general .task-list-box .list-list .item .op-btns{position:absolute;right:-1px;top:1px;opacity:0;transition:all .3s}.modal-upload-general .metadata-box{display:flex;flex-direction:column;width:1px;flex:1 0 auto;position:relative}.modal-upload-general .metadata-box .box-content{border:1px solid #e9e9e9;border-bottom:none;display:flex;flex-direction:column;height:1px;flex:1 0 auto}.modal-upload-general .metadata-box .box-content .metadata-form-wrap{flex:1 0 auto;height:1px;display:flex}.modal-upload-general .metadata-box .box-content .mam-metadata-form{flex:1 0 auto;height:1px}.modal-upload-general .metadata-box .box-content .mam-metadata-form .mmf-group{display:flex;flex-wrap:wrap;margin-bottom:5px;padding:10px}.modal-upload-general .metadata-box .box-footer{padding:10px;text-align:center;border:1px solid #e9e9e9;background:#fff;z-index:10}.modal-upload-general .metadata-box .box-footer button{margin:0 5px}.modal-upload-general .mask-layer{background:rgba(0,0,0,.3);position:absolute;left:0;top:0;width:100%;height:100%}.modal-upload-group .modal-body{display:flex;flex-direction:row;width:850px;height:500px}.modal-upload-group .box-header-title{font-size:15px;padding:0 14px;height:36px;line-height:36px;display:inline-block;color:#fff;border-radius:3px 3px 0 0;background:#337ab7}.modal-upload-group .box-content{border:1px solid #e9e9e9;display:flex;flex-direction:column;height:434px}.modal-upload-group .metadata-box{display:flex;flex-direction:column;width:380px;position:relative}.modal-upload-group .metadata-box .box-content .mam-metadata-form{flex:1 0 auto;height:1px}.modal-upload-group .metadata-box .box-content .mam-metadata-form .mmf-group{display:flex;flex-wrap:wrap;margin-bottom:5px;padding:10px}.modal-upload-group .files-box{width:460px;margin-left:10px}.modal-upload-group .files-box .box-header{display:flex;align-items:flex-end}.modal-upload-group .files-box .box-header .box-header-btns{display:flex;justify-content:flex-end;flex:1 0 auto}.modal-upload-group .files-box .box-header .box-header-btns button{outline:none;border:none;color:#fff;display:flex;align-items:center;justify-content:center;width:38px;height:32px}.modal-upload-group .files-box .box-header .box-header-btns button:hover i{transform:scale(1.2)}.modal-upload-group .files-box .box-header .box-header-btns button i{transition:all .3s;font-size:18px}.modal-upload-group .files-box .box-header .box-header-btns button span{display:none}.modal-upload-group .files-box .box-header .box-header-btns .add{background:#feb872}.modal-upload-group .files-box .box-header .box-header-btns .add i{margin-top:3px;margin-left:2px}.modal-upload-group .files-box .file-group .group-title{font-size:16px;border-bottom:1px solid #e5e5e5;padding-bottom:10px;padding-left:10px;margin-bottom:8px}.modal-upload-group .files-box .file-group .item{display:flex;height:30px;line-height:30px;padding:0 10px}.modal-upload-group .files-box .file-group .item .no{min-width:25px}.modal-upload-group .files-box .file-group .item .name{flex:1 0 auto;width:1px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;cursor:move}.modal-upload-group .files-box .file-group .item .btns .delete{font-size:14px}.modal-upload-group .files-box .file-group .ui-sortable-helper{background:hsla(0,0%,100%,.7)}.modal-upload-group .files-box .file-group .no-data{font-size:16px;padding:15px 0;text-align:center}.modal-upload-group .files-box .video-group{margin-top:10px;margin-bottom:10px}.modal-upload-group .files-box .video-group .name{cursor:default!important}.modal-upload-picturePackage .modal-body{display:flex;flex-direction:row;width:850px;height:500px}.modal-upload-picturePackage .box-header-title{font-size:15px;padding:0 14px;height:36px;line-height:36px;display:inline-block;color:#fff;border-radius:3px 3px 0 0;background:#337ab7}.modal-upload-picturePackage .box-content{border:1px solid #e9e9e9;display:flex;flex-direction:column;height:434px}.modal-upload-picturePackage .metadata-box{display:flex;flex-direction:column;width:380px;position:relative}.modal-upload-picturePackage .metadata-box .box-content .mam-metadata-form{flex:1 0 auto;height:1px}.modal-upload-picturePackage .metadata-box .box-content .mam-metadata-form .mmf-group{display:flex;flex-wrap:wrap;margin-bottom:5px;padding:10px}.modal-upload-picturePackage .files-box{width:460px;margin-left:10px}.modal-upload-picturePackage .files-box .box-header{display:flex;align-items:flex-end}.modal-upload-picturePackage .files-box .box-header .box-header-btns{display:flex;justify-content:flex-end;flex:1 0 auto}.modal-upload-picturePackage .files-box .box-header .box-header-btns button{outline:none;border:none;color:#fff;display:flex;align-items:center;justify-content:center;width:38px;height:32px}.modal-upload-picturePackage .files-box .box-header .box-header-btns button:hover i{transform:scale(1.2)}.modal-upload-picturePackage .files-box .box-header .box-header-btns button i{transition:all .3s;font-size:18px}.modal-upload-picturePackage .files-box .box-header .box-header-btns button span{display:none}.modal-upload-picturePackage .files-box .box-header .box-header-btns .add{background:#feb872}.modal-upload-picturePackage .files-box .items-box{display:flex;flex-wrap:wrap}.modal-upload-picturePackage .files-box .item{position:relative;cursor:move;width:130px;min-height:73px;margin:10px 8px 6px;background-color:#eee}.modal-upload-picturePackage .files-box .item .keyframe{width:130px;height:73px;display:flex;justify-content:center;align-items:center}.modal-upload-picturePackage .files-box .item .keyframe img{max-width:130px;max-height:73px}.modal-upload-picturePackage .files-box .item .name{height:30px;line-height:30px;width:130px;padding-left:6px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;background-color:#fff}.modal-upload-picturePackage .files-box .item .btns{cursor:pointer;position:absolute;right:-7px;top:-12px;font-size:16px;color:#5f5f5f}.modal-upload-picturePackage .files-box .item .btns i:hover{color:#000}.modal-upload-picturePackage .files-box .item-add{margin:10px 8px 6px}.modal-upload-picturePackage .files-box .item-add .btn-add{width:130px;height:73px;display:flex;background:#eee;justify-content:center;align-items:center;font-size:38px;font-weight:400;display:block;color:#717171;border:none}.modal-upload-picturePackage .files-box .item-add .btn-add:hover{color:#000}.modal-upload-materialPackage .modal-body{display:flex;flex-direction:row;width:850px;height:500px}.modal-upload-materialPackage .box-header-title{font-size:15px;padding:0 14px;height:36px;line-height:36px;display:inline-block;color:#fff;border-radius:3px 3px 0 0;background:#337ab7}.modal-upload-materialPackage .box-content{border:1px solid #e9e9e9;display:flex;flex-direction:column;height:434px}.modal-upload-materialPackage .metadata-box{display:flex;flex-direction:column;width:380px;position:relative}.modal-upload-materialPackage .metadata-box .box-content .mam-metadata-form{flex:1 0 auto;height:1px}.modal-upload-materialPackage .metadata-box .box-content .mam-metadata-form .mmf-group{display:flex;flex-wrap:wrap;margin-bottom:5px;padding:10px}.modal-upload-materialPackage .files-box{width:460px;margin-left:10px}.modal-upload-materialPackage .files-box .box-header{display:flex;align-items:flex-end}.modal-upload-materialPackage .files-box .box-header .box-header-btns{display:flex;justify-content:flex-end;flex:1 0 auto}.modal-upload-materialPackage .files-box .box-header .box-header-btns button{outline:none;border:none;color:#fff;display:flex;align-items:center;justify-content:center;width:38px;height:32px}.modal-upload-materialPackage .files-box .box-header .box-header-btns button:hover i{transform:scale(1.2)}.modal-upload-materialPackage .files-box .box-header .box-header-btns button i{transition:all .3s;font-size:18px}.modal-upload-materialPackage .files-box .box-header .box-header-btns button span{display:none}.modal-upload-materialPackage .files-box .box-header .box-header-btns .add{background:#feb872}.modal-upload-materialPackage .files-box .items-box{max-height:100%;overflow-y:auto}.modal-upload-materialPackage .files-box .item{width:100%;display:flex;align-items:center;position:relative;height:40px;min-height:40px;padding:10px 8px 6px}.modal-upload-materialPackage .files-box .item:nth-child(2n){background-color:#eee}.modal-upload-materialPackage .files-box .item .name{flex:1;width:1%;height:30px;line-height:30px;padding-left:6px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.modal-upload-materialPackage .files-box .item .btns{width:20px;cursor:pointer;font-size:16px;color:#5f5f5f}.modal-upload-materialPackage .files-box .item .btns i:hover{color:#000}.modal-upload-materialPackage .files-box .item-add{margin:10px 8px 6px}.modal-upload-materialPackage .files-box .item-add .btn-add{width:130px;height:73px;display:flex;background:#eee;justify-content:center;align-items:center;font-size:38px;font-weight:400;display:block;color:#717171;border:none}.modal-upload-materialPackage .files-box .item-add .btn-add:hover{color:#000}.mam-upload-task-panel{position:fixed;right:-768px;bottom:88px;width:768px;height:373px;background:#fff;box-shadow:0 0 14px rgba(0,0,0,.2);z-index:99;transition:right .3s}.mam-upload-task-panel .panel-control{width:48px;height:48px;background-color:#e98b11;color:#fff;position:absolute;left:-48px;top:0;box-shadow:0 0 10px rgba(0,0,0,.1);z-index:0;cursor:pointer;display:flex;align-items:center;justify-content:center}.mam-upload-task-panel .panel-control .fa{font-size:18px}.mam-upload-task-panel .panel-control.close{transition:opacity .3s;opacity:.4}.mam-upload-task-panel .panel-control.close:hover{opacity:1}.mam-upload-task-panel .box{padding:18px;height:inherit;display:flex;flex-direction:column}.mam-upload-task-panel .header{border-bottom:1px solid #e4e4e4;margin-bottom:18px}.mam-upload-task-panel .box-header-title{font-size:16px;color:#272727;margin-bottom:15px}.mam-upload-task-panel .task-list{flex:1;overflow:auto}.mam-upload-task-panel .task-list .item-th{display:flex;margin-bottom:15px;color:#535353}.mam-upload-task-panel .task-list .item-tr{color:#494949}.mam-upload-task-panel .task-list .item-tr .task{display:flex;height:48px;line-height:48px;border-top:1px solid #eee;position:relative}.mam-upload-task-panel .task-list .item-tr .files{border-top:1px solid #eee}.mam-upload-task-panel .task-list .item-tr .files .file{display:flex;position:relative;height:48px;line-height:48px;margin-left:24px;border-top:1px solid #eee;border-left:1px solid #eee}.mam-upload-task-panel .task-list .item-tr .files .file:first-child{border-top:none}.mam-upload-task-panel .task-list .item-tr .upload-progress-bar{position:absolute;z-index:-1;width:100%;left:0;top:0;height:inherit}.mam-upload-task-panel .task-list .item-tr .upload-progress-bar span{display:block;height:inherit;background-color:#d9f3ff;border-top:2px solid transparent;border-bottom:2px solid transparent;border-right:2px solid #bae9ff;transition:width .6s}.mam-upload-task-panel .task-list .item-tr .surplus-time{position:absolute;left:43px;top:28px;color:#949494;height:20px;line-height:20px;font-size:12px;text-shadow:0 1px 1px #fff}.mam-upload-task-panel .task-list .status{width:44px;text-align:center}.mam-upload-task-panel .task-list .status i{display:inline-block}.mam-upload-task-panel .task-list .status i.icon-gou1{color:green}.mam-upload-task-panel .task-list .name{flex:1;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.mam-upload-task-panel .task-list .name-link{cursor:pointer}.mam-upload-task-panel .task-list .name-link:hover{color:#e98b11}.mam-upload-task-panel .task-list .size{width:88px}.mam-upload-task-panel .task-list .folder{width:86px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.mam-upload-task-panel .task-list .upload-progress{width:48px;text-align:center}.mam-upload-task-panel .task-list .control{width:65px;text-align:center}.mam-upload-task-panel .task-list .control .fa{transform:translateY(-2px);color:#999}.mam-upload-task-panel .task-list .control .fa:hover{color:#000}.mam-upload-task-panel .no-data{flex:1;display:flex;justify-content:center;align-items:center;padding-bottom:34px;color:#3d3d3d;font-size:20px}.mam-upload-button-group{height:36px}.mam-upload-button-group .btn-group,.mam-upload-button-group .btn-group .btn{height:100%}.mam-upload-button-group .upload-btn-group{position:relative;display:inline-block;height:100%}.mam-upload-button-group .upload-btn-group>button{background:#e98b11;color:#fff;height:100%}.mam-upload-button-group .upload-btn-group>ul{display:none;background-color:#fff;padding:6px 0;position:absolute;top:36px;border:1px solid rgba(0,0,0,.15);border-radius:4px;min-width:160px;z-index:1000;list-style:none;right:0}.mam-upload-button-group .upload-btn-group>ul.left{left:0;right:auto}.mam-upload-button-group .upload-btn-group>ul>li:hover{background:#f5f5f5;color:#262626}.mam-upload-button-group .upload-btn-group>ul>li:hover>a{color:#262626}.mam-upload-button-group .upload-btn-group>ul>li>a{display:flex;align-items:center;padding:3px 20px}.mam-upload-button-group .upload-btn-group>ul>li>a>i{font-size:14px}.mam-upload-button-group .upload-btn-group>ul>li>a:hover{text-decoration:none}.mam-upload-button-group .upload-btn-group:hover>ul{display:block}",""])},function(e,t){function n(e,t){var n=e[1]||"",r=e[3];if(!r)return n;if(t&&"function"==typeof btoa){var a=o(r);return[n].concat(r.sources.map(function(e){return"/*# sourceURL="+r.sourceRoot+e+" */"})).concat([a]).join("\n")}return[n].join("\n")}function o(e){return"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(e))))+" */"}e.exports=function(e){var t=[];return t.toString=function(){return this.map(function(t){var o=n(t,e);return t[2]?"@media "+t[2]+"{"+o+"}":o}).join("")},t.i=function(e,n){"string"==typeof e&&(e=[[null,e,""]]);for(var o={},r=0;r<this.length;r++){var a=this[r][0];"number"==typeof a&&(o[a]=!0)}for(r=0;r<e.length;r++){var i=e[r];"number"==typeof i[0]&&o[i[0]]||(n&&!i[2]?i[2]=n:n&&(i[2]="("+i[2]+") and ("+n+")"),t.push(i))}},t}},function(e,t,n){function o(e,t){for(var n=0;n<e.length;n++){var o=e[n],r=h[o.id];if(r){r.refs++;for(var a=0;a<r.parts.length;a++)r.parts[a](o.parts[a]);for(;a<o.parts.length;a++)r.parts.push(f(o.parts[a],t))}else{for(var i=[],a=0;a<o.parts.length;a++)i.push(f(o.parts[a],t));h[o.id]={id:o.id,refs:1,parts:i}}}}function r(e,t){for(var n=[],o={},r=0;r<e.length;r++){var a=e[r],i=t.base?a[0]+t.base:a[0],s=a[1],l=a[2],u=a[3],f={css:s,media:l,sourceMap:u};o[i]?o[i].parts.push(f):n.push(o[i]={id:i,parts:[f]})}return n}function a(e,t){var n=g(e.insertInto);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var o=v[v.length-1];if("top"===e.insertAt)o?o.nextSibling?n.insertBefore(t,o.nextSibling):n.appendChild(t):n.insertBefore(t,n.firstChild),v.push(t);else{if("bottom"!==e.insertAt)throw new Error("Invalid value for parameter 'insertAt'. Must be 'top' or 'bottom'.");n.appendChild(t)}}function i(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e);var t=v.indexOf(e);t>=0&&v.splice(t,1)}function s(e){var t=document.createElement("style");return e.attrs.type="text/css",u(t,e.attrs),a(e,t),t}function l(e){var t=document.createElement("link");return e.attrs.type="text/css",e.attrs.rel="stylesheet",u(t,e.attrs),a(e,t),t}function u(e,t){Object.keys(t).forEach(function(n){e.setAttribute(n,t[n])})}function f(e,t){var n,o,r,a;if(t.transform&&e.css){if(!(a=t.transform(e.css)))return function(){};e.css=a}if(t.singleton){var u=y++;n=b||(b=s(t)),o=c.bind(null,n,u,!1),r=c.bind(null,n,u,!0)}else e.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(n=l(t),o=d.bind(null,n,t),r=function(){i(n),n.href&&URL.revokeObjectURL(n.href)}):(n=s(t),o=p.bind(null,n),r=function(){i(n)});return o(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;o(e=t)}else r()}}function c(e,t,n,o){var r=n?"":o.css;if(e.styleSheet)e.styleSheet.cssText=k(t,r);else{var a=document.createTextNode(r),i=e.childNodes;i[t]&&e.removeChild(i[t]),i.length?e.insertBefore(a,i[t]):e.appendChild(a)}}function p(e,t){var n=t.css,o=t.media;if(o&&e.setAttribute("media",o),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}function d(e,t,n){var o=n.css,r=n.sourceMap,a=void 0===t.convertToAbsoluteUrls&&r;(t.convertToAbsoluteUrls||a)&&(o=x(o)),r&&(o+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(r))))+" */");var i=new Blob([o],{type:"text/css"}),s=e.href;e.href=URL.createObjectURL(i),s&&URL.revokeObjectURL(s)}var h={},m=function(e){var t;return function(){return void 0===t&&(t=e.apply(this,arguments)),t}}(function(){return window&&document&&document.all&&!window.atob}),g=function(e){var t={};return function(n){return void 0===t[n]&&(t[n]=e.call(this,n)),t[n]}}(function(e){return document.querySelector(e)}),b=null,y=0,v=[],x=n(108);e.exports=function(e,t){if("undefined"!=typeof DEBUG&&DEBUG&&"object"!=typeof document)throw new Error("The style-loader cannot be used in a non-browser environment");t=t||{},t.attrs="object"==typeof t.attrs?t.attrs:{},t.singleton||(t.singleton=m()),t.insertInto||(t.insertInto="head"),t.insertAt||(t.insertAt="bottom");var n=r(e,t);return o(n,t),function(e){for(var a=[],i=0;i<n.length;i++){var s=n[i],l=h[s.id];l.refs--,a.push(l)}if(e){o(r(e,t),t)}for(var i=0;i<a.length;i++){var l=a[i];if(0===l.refs){for(var u=0;u<l.parts.length;u++)l.parts[u]();delete h[l.id]}}}};var k=function(){var e=[];return function(t,n){return e[t]=n,e.filter(Boolean).join("\n")}}()},function(e,t){e.exports=function(e){var t="undefined"!=typeof window&&window.location;if(!t)throw new Error("fixUrls requires window.location");if(!e||"string"!=typeof e)return e;var n=t.protocol+"//"+t.host,o=n+t.pathname.replace(/\/[^\/]*$/,"/");return e.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,function(e,t){var r=t.trim().replace(/^"(.*)"$/,function(e,t){return t}).replace(/^'(.*)'$/,function(e,t){return t});if(/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/)/i.test(r))return e;var a;return a=0===r.indexOf("//")?r:0===r.indexOf("/")?n+r:o+r.replace(/^\.\//,""),"url("+JSON.stringify(a)+")"})}},function(e,t,n){"use strict";angular.module("mam-upload").directive("uploadStatusIcon",function(){return{restrict:"E",template:'<i class="status-icon fa"></i>',scope:{status:"="},link:function(e,t,n){function o(e){switch(e){case"added":return"";case"prepared":return"fa-clock-o";case"success":return"fa-check-circle";case"error":return"fa-times-circle";case"deleteing":return"fa-recycle";default:return"fa-circle-o-notch fa-spin"}}var r=t.find("i");e.$watch("status",function(){r.removeClass("fa-check-circle fa-times-circle fa-recycle fa-circle-o-notch fa-spin"),r.addClass(o(e.status))}),r.addClass(o(e.status))}}})},function(e,t,n){"use strict";var o=n(21).default;angular.module("mam-upload").directive("uploadButtonGroup",["$http","$uibModal","$q","$timeout",function(e,t,r,a){return{restrict:"E",template:n(111),replace:!0,scope:{targetFolder:"<",module:"=",picturePackageEnable:"<",fileGroupEnable:"<",writeMetadataBefore:"&?",callBack:"&?",relationType:"@",relationContentId:"=",needCopyright:"@",extraField:"&?"},link:function(e,t,n){$(t);e.l=window.l,e.transferType,e.webUploadEnable=nxt.config.webUploadEnable,e.vtubeEnable=nxt.config.vtubeEnable,e.isZB="zb"===nxt.config.theme.name;var r=[{type:1,title:l("upload.fileUpload","文件上传"),icon:"fa fa-file-o"},{type:3,title:l("upload.picturePackageUpload","图片包上传"),icon:"fa fa-picture-o"},{type:2,title:l("upload.groupUpload","视音频分离上传"),icon:"fa fa-file-o"}];e.btns=r,e.transferType=localStorage.getItem("mam-upload-transfer-type"),null==e.transferType?e.transferType=1:e.transferType=parseInt(e.transferType),2==e.transferType?e.vtubeEnable||(e.transferType=1):e.transferType=e.webUploadEnable?1:2,e.changeTransfer=function(t){e.transferType=t,localStorage.setItem("mam-upload-transfer-type",t)},e.upload=function(t){var n=e.targetFolder;null!=n&&""!==n||(n="doha");var r={};r.taskType=t,r.transferType=e.transferType,r.targetFolder=n,r.module=e.module,e.writeMetadataBefore&&(r.writeMetadataBefore=function(t,n){return e.writeMetadataBefore({files:t,params:n})}),e.relationType==o.mamUpload.uploader.relationContentType.dataset&&(r.relationContentType=o.mamUpload.uploader.relationContentType.dataset,r.relationContentId=e.relationContentId,r.needCopyright=e.needCopyright,_.isFunction(e.extraField)&&(r.extraField=e.extraField())),o.mamUpload.uploader.createTask(r,e.callBack)}}}}])},function(e,t){e.exports='<div class=mam-upload-button-group ng-show="webUploadEnable || vtubeEnable"> <div class="btn-group transfer-type" mam-dropdown ng-if=webUploadEnable&&vtubeEnable||webUploadEnable&&isZB> <button type=button class="btn btn-default dropdown-toggle"> <i ng-if="transferType==1" class="fa fa-internet-explorer"></i> <i ng-if="transferType==2" class="fa fa-windows"></i> </button> <ul class=dropdown-menu> <li><a ng-click=changeTransfer(1)><i class="fa fa-internet-explorer"></i>{{l(\'upload.webUpload\',\'Web上传\')}}</a></li> <li><a ng-click=changeTransfer(2)><i class="fa fa-windows"></i>{{l(\'upload.clientUpload\',\'客户端上传\')}}</a></li> </ul> </div> <div class="btn-group task-type" mam-dropdown> <button type=button class="btn btn-default dropdown-toggle"> <i class="fa fa-upload"></i> {{l(\'upload.upload\',\'上传\')}} </button> <ul class=dropdown-menu> <li> <a ng-click=upload(btn.type) ng-repeat="btn in btns" ng-if="btn.type == 1 || (btn.type == 2 && fileGroupEnable) || (btn.type == 3 && picturePackageEnable)" ng-hide="isZB&&(btn.type == 2)"> <i class={{btn.icon}}></i>{{btn.title}} </a> </li> </ul> </div> </div> '},function(e,t,n){"use strict";var o=n(21).default;angular.module("mam-upload").directive("newUploadButtonGroup",["$http","$uibModal","$q","$timeout",function(e,t,r,a){return{restrict:"E",template:n(113),replace:!0,scope:{targetFolder:"<",module:"=",picturePackageEnable:"<",fileGroupEnable:"<",materialPackageEnable:"<",writeMetadataBefore:"&?",callBack:"&?",relationType:"@",relationContentId:"=",needCopyright:"@",extraField:"&?"},link:function(e,t,n){$(t);e.l=window.l,e.transferType,e.webUploadEnable=nxt.config.webUploadEnable,e.vtubeEnable=nxt.config.vtubeEnable;var r=_.find(nxt.config.entityTypes,{code:"materialpackage"}),a=[{type:1,title:l("upload.fileUpload","文件上传"),icon:"fa fa-file-o"},{type:3,title:l("upload.picturePackageUpload","图片包上传"),icon:"fa fa-picture-o"},{type:2,title:l("upload.groupUpload","视音频分离上传"),icon:"fa fa-file-o"},{type:4,title:r&&r.name?r.name+l("upload.upload","上传"):l("upload.materialPackageUpload","素材包上传"),icon:"fa fa-picture-o"}];e.btns=a,e.transferType=localStorage.getItem("mam-upload-transfer-type"),null==e.transferType?e.transferType=1:e.transferType=parseInt(e.transferType),2==e.transferType?e.vtubeEnable||(e.transferType=1):e.transferType=e.webUploadEnable?1:2,e.changeTransfer=function(t){e.transferType=t,localStorage.setItem("mam-upload-transfer-type",t)},e.singleTransfer=function(t){e.picturePackageEnable||e.fileGroupEnable||e.upload(t,1)},e.upload=function(t,n){var a=e.targetFolder;null!=a&&""!==a||(a="doha");var i={};i.taskType=n,i.transferType=t,i.targetFolder=a,i.module=e.module,e.writeMetadataBefore&&(i.writeMetadataBefore=function(t,n){return e.writeMetadataBefore({files:t,params:n})}),e.relationType==o.mamUpload.uploader.relationContentType.dataset&&(i.relationContentType=o.mamUpload.uploader.relationContentType.dataset,i.relationContentId=e.relationContentId,i.needCopyright=e.needCopyright,_.isFunction(e.extraField)&&(i.extraField=e.extraField())),4==n?(i.accept=r&&r.accept?r.accept:"",i.relationContentType=4,i.relationContentId=e.relationContentId,o.mamUpload.uploader.createTask(i,e.callBack)):o.mamUpload.uploader.createTask(i,e.callBack)}}}}])},function(e,t){e.exports='<div class=mam-upload-button-group ng-show="webUploadEnable || vtubeEnable"> <div class=upload-btn-group ng-if=webUploadEnable> <button type=button class="btn btn-default" ng-click=singleTransfer(1) ng-mouseover=changeTransfer(1)> {{l(\'upload.webUpload\',\'Web上传\')}} </button> <ul ng-if="fileGroupEnable || picturePackageEnable" ng-class="{\'left\': vtubeEnable && webUploadEnable}"> <li ng-repeat="btn in btns" ng-if="btn.type == 1 || (btn.type == 2 && fileGroupEnable) || (btn.type == 3 && picturePackageEnable) || (btn.type == 4 && materialPackageEnable)"> <a ng-click=upload(1,btn.type)> <i class={{btn.icon}}></i>{{btn.title}} </a> </li> </ul> </div> <div class=upload-btn-group ng-if=vtubeEnable> <button type=button class="btn btn-default" ng-click=singleTransfer(2) ng-mouseover=changeTransfer(2)> {{l(\'upload.clientUpload\',\'客户端上传\')}} </button> <ul ng-if="fileGroupEnable || picturePackageEnable"> <li ng-repeat="btn in btns" ng-if="btn.type == 1 || (btn.type == 2 && fileGroupEnable) || (btn.type == 3 && picturePackageEnable)"> <a ng-click=upload(2,btn.type)> <i class={{btn.icon}}></i>{{btn.title}} </a> </li> </ul> </div> </div> '},function(e,t,n){"use strict";var o=n(21).default;angular.module("mam-upload").directive("uploadTaskPanel",["$http","$q","$timeout",function(e,t,r){return{restrict:"E",template:n(115),transclude:!0,scope:{succeed:"&",afterGetUnfinished:"&",contentId:"=",relationContentType:"=",targetType:"="},link:function(e,t,n){e.l=window.l,e.showTaskPanel=!1,e.tasks=[],e.loaded=!1,e.taskTimeTiker=[],e.webUploadEnable=nxt.config.webUploadEnable,o.mamUpload.uploader.web.on("task-init-before",function(t,n){e.tasks.push(n),e.showTaskPanel=!0}),o.mamUpload.uploader.web.on("task-init-success",function(e,t){}),o.mamUpload.uploader.web.on("task-upload-progress",function(t,n){e.$applyAsync()}),o.mamUpload.uploader.web.on("task-delete-success",function(t,n){_.remove(e.tasks,function(e){return e.taskId==n.taskId}),e.$applyAsync()}),o.mamUpload.uploader.web.on("task-upload-success",function(t,n){e.$applyAsync()}),e.continueFile=o.mamUpload.uploader.web.continueUpload,e.canDelete=o.mamUpload.uploader.web.canDeleteTask,e.delete=function(e){o.mamUpload.uploader.web.deleteTask(e)},o.mamUpload.uploader.web.getUnfinishedTask(e.contentId,e.relationContentType,e.targetType).then(function(t){e.tasks=_.concat(e.tasks,t),_.isFunction(e.afterGetUnfinished)&&e.afterGetUnfinished(),e.loaded=!0})}}}])},function(e,t){e.exports="<div class=mam-upload-task-panel ng-style=\"{right: showTaskPanel ? '0' : '-768px'}\"> <div class=panel-control ng-click=\"showTaskPanel=!showTaskPanel\" ng-class=\"showTaskPanel?'open':'close'\" title=\"{{showTaskPanel?l('upload.hideTask','隐藏上传任务'):l('upload.showTask','展开上传任务')}}\"> <i class=\"fa fa-bars\"></i> </div> <div class=box> <div class=header> <div class=box-header-title>{{l('upload.task','上传任务')}}</div> </div> <div ng-if=\"loaded && tasks.length>0\" class=task-list> <div class=item-th> <div class=status>{{l('upload.status','状态')}}</div> <div class=name>{{l('upload.name','名称')}}</div> <div class=size>{{l('upload.size','大小')}}</div> <div class=folder>{{l('upload.location','位置')}}</div> <div class=upload-progress>{{l('upload.progress','进度')}}</div> <div class=control>{{l('upload.control','操作')}}</div> </div> <div class=item-tr ng-repeat=\"task in tasks\" ng-init=\"task.showFiles=task.files.length>1 && task.status=='error'\"> <div class=task> <div class=status title=\"{{task.status | uploadStatus}}\"> <upload-status-icon status=task.status></upload-status-icon> </div> <div class=name ng-if=\"task.files.length<2\" ng-bind=task.metadata.name></div> <div class=\"name name-link\" ng-if=\"task.files.length>1\" ng-bind=task.metadata.name ng-click=\"task.showFiles=!task.showFiles\"></div> <div class=size ng-bind=task.sizeTotalString></div> <div class=folder title={{task.targetFolderName}}> <a ng-if=\"task.targetType==0\" ui-sref=\"main.cc.cloud({isPublic:true,folder: task.targetFolder, page:1, keyword:'',searchType:'',condition:[]})\" ng-bind=task.targetFolderName></a> <a ng-if=\"task.targetType==1\" ui-sref=\"main.cc.cloud({isPublic:false,folder: task.targetFolder, page:1, keyword:'',searchType:'',condition:[]})\" ng-bind=task.targetFolderName></a> <a ng-if=\"task.targetType==5\" mam-href=~/search/#/search/ ng-bind=task.targetFolderName></a> <a ng-if=\"task.targetType==6\" ui-sref=\"main.cc.workspace({folder: task.targetFolder, page:1, keyword:''})\" ng-bind=task.targetFolderName></a> </div> <div class=surplus-time ng-if=\"task.surplusTime!=null\">{{l('upload.surplusTime','大约剩余：')}}<span>{{task.surplusTime | msToSurplusTime}}</span></div> <div class=upload-progress>{{task.progress==100?l('upload.finish','完成'):(task.progress+'%')}}</div> <div class=control> <button class=\"uplaod-options btn-icon\" title=\"{{l('upload.continueUpload','继续上传')}}\" ng-click=continueFile(task,task.files[0]) ng-if=\"task.files.length==1 && (task.status == 'error')\"> <i class=\"icon iconfont icon-right1\"></i> </button> <button class=\"uplaod-options btn-icon\" title=\"{{l('upload.delete','删除')}}\" ng-if=canDelete(task) ng-click=delete(task)><i class=\"icon iconfont icon-icon13\"></i></button> </div> <div class=upload-progress-bar> <span ng-style=\"{width: task.progress + '%'}\"></span> </div> </div> <div class=files ng-if=\"task.files.length>1 && task.showFiles\"> <div class=file ng-repeat=\"file in task.files\"> <div class=status title=\"{{file.status | uploadStatus}}\"> <upload-status-icon status=file.status></upload-status-icon> </div> <div class=name ng-bind=file.fileName></div> <div class=size ng-bind=file.fileSizeString></div> <div class=folder></div> <div class=surplus-time ng-if=\"file.surplusTime!=null\">{{l('upload.surplusTime','大约剩余：')}}<span>{{file.surplusTime | msToSurplusTime}}</span></div> <div class=upload-progress>{{file.progress==100?l('upload.finish','完成'):(file.progress+'%')}}</div> <div class=control> <button class=\"uplaod-options btn-icon\" title=\"{{l('upload.continueUpload','继续上传')}}\" ng-click=continueFile(task,file) ng-if=\"file.status == 'error'\"> <i class=\"icon iconfont icon-right1\"></i> </button> </div> <div class=upload-progress-bar> <span ng-style=\"{width: file.progress + '%'}\"></span> </div> </div> </div> </div> </div> <div ng-if=\"tasks.length==0\" class=no-data>{{l('upload.noTask','暂无任务')}}</div> </div> </div>"},function(e,t,n){"use strict";angular.module("mam-upload").filter("uploadStatus",function(){return function(e){return"added"==e?l("upload.statusAdded","等待上传"):"init"==e?l("upload.statusInit","正在初始化"):"prepared"==e?l("upload.statusPrepared","排队中"):"progress"==e?l("upload.statusProgress","上传中"):"error"==e?l("upload.statusError","错误"):"success"==e?l("upload.statusSuccess","完成"):"deleteing"==e?l("upload.statusDeleteing","删除中"):e}})},function(e,t,n){"use strict";angular.module("mam-upload").filter("remainTime",function(){return function(e){if(!_.isNumber(e))return"";var t,n,o=0;if((t=Math.round(e/1e3))<60)return t+l("upload.second","秒");n=parseInt(t/60),t%=60,n>=60&&(o=parseInt(n/60),n%=60);var r="";return 0!==o&&(r+=o+l("upload.hour","小时")),0!==n&&(r+=n+l("upload.minute","分")),0!==t&&(r+=t+l("upload.second","秒")),r}})},function(e,t,n){"use strict";angular.module("mam-upload").filter("msToSurplusTime",function(){return function(e){if(!_.isNumber(e))return"";var t,n,o=0;if((t=Math.round(e/1e3))<60)return t+"秒";n=parseInt(t/60),t%=60,n>=60&&(o=parseInt(n/60),n%=60);var r="";return 0!==o&&(r+=o+"小时"),0!==n&&(r+=n+"分"),0!==t&&(r+=t+"秒"),r}})}]);
//# sourceMappingURL=mam-upload.min.js.map