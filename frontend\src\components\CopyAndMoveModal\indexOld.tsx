import React, {
  useState,
  useEffect,
  KeyboardEvent,
  FocusEvent,
  useRef,
} from 'react';
import { Modal, Input, Button, Tree, message, Form, Spin } from 'antd';
import { formatMessage, useIntl, useSelector, useDispatch } from 'umi';
import copyandmoveApis from '@/service/copyandmoveApis';
import contentListApis from '@/service/contentListApis';
import deleteApis from '@/service/deleteApis';
import copyandmoveTypes from '@/types/copyandmoveTypes';
import searchTypes from '@/types/searchTypes';
import './index.less';

const { DirectoryTree } = Tree;

interface CreateModalProps {
  modalVisible: boolean;
  modalClose: () => void;
  refresh: () => void; // 刷新
  copyAndMovelist: searchTypes.ISearchData[];
  CopyOrMove: boolean;
}

interface ITree {
  title: string;
  key: string;
  id: string;
  children: ITree[];
}

const _disableIds = ['global_sobey_defaultclass/public'];
const CopyAndMoveModal: React.FC<CreateModalProps> = props => {
  const {
    modalClose,
    modalVisible,
    refresh,
    copyAndMovelist,
    CopyOrMove,
  } = props;
  const [expandedKeys, setExpandedKeys] = useState<React.ReactText[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<React.ReactText[]>([]);
  const [selectedDetail, setSelectedDetail] = useState<
    copyandmoveTypes.Imovedetail
  >();
  const [inputVisible, setInputVisible] = useState(false);
  const [treeData, setTreeData] = useState<ITree[]>([]);
  const [onNew, setOnNew] = useState<boolean>(false);
  const changenameRef = useRef<Input | null>(null);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  // const [upLoadClicktag, setUpLoadClicktag] = useState(true);
  let upLoadClicktag = true;
  useEffect(() => {
    if (modalVisible) {
      form.resetFields();
      setExpandedKeys([]);
      setSelectedKeys([]);
      setSelectedDetail(undefined);
      setOnNew(false);
      searchpublic();
    }
  }, [modalVisible]);
  const searchpublic = async () => {
    setLoading(true);
    await copyandmoveApis.movetree().then((res: any) => {
      setLoading(false);
      upLoadClicktag = true;
      if (res && res.data && res.success) {
        let data = res.data;
        removeTreeListItem(data, '录播资源');
        let list = getTree(data);
        setTreeData(list);
      }
    });
  };
  const removeTreeListItem = (treeList: any, name: string) => {
    // 根据id属性从数组（树结构）中移除元素
    if (!treeList || !treeList.length) {
      return;
    }
    for (let i = 0; i < treeList.length; i++) {
      if (treeList[i].name === name) {
        treeList.splice(i, 1);
        break;
      }
      removeTreeListItem(treeList[i].children, name);
    }
  };
  const getTree = (tree: any) => {
    return tree.map((item: any) => {
      if (item.name === '录播资源') {
        // console.log('sadfsdaf')
        return {
          key: item.code,
          title: item.name,
          id: item.id,
        };
      } else {
        return {
          key: item.code,
          title: item.name,
          id: item.id,
          children: item.children ? getTree(item.children) : [],
        };
      }
    });
  };
  const onSelect = (keys: React.ReactText[], { node }: any) => {
    setSelectedDetail({
      key: node.key,
      title: node.title,
      id: node.id,
    });
    setSelectedKeys(keys);
    if (_disableIds.includes(node.id) && !inputVisible) {
      setOnNew(true);
    } else if (!inputVisible) {
      setOnNew(false);
    }
  };
  const onExpand = (keys: React.ReactText[]) => {
    setExpandedKeys(keys);
  };
  // --------新建文件夹
  const newfolder = () => {
    if (!selectedDetail) {
      message.info('请选择文件夹');
      return;
    }
    setExpandedKeys(origin =>
      origin.includes(selectedDetail.key)
        ? origin
        : [...origin, selectedDetail.key],
    );
    let list = forTree(treeData);
    setTreeData(list);
    setInputVisible(true);
    setOnNew(true);
  };
  useEffect(() => {
    if (inputVisible) {
      setTimeout(() => {
        changenameRef.current?.focus();
      }, 200);
    }
  }, [treeData]);
  const changeName = async (
    e: KeyboardEvent<HTMLInputElement> | FocusEvent<HTMLInputElement> | any,
  ) => {
    if (upLoadClicktag) {
      console.log(upLoadClicktag);
      upLoadClicktag = false;
      if (!selectedDetail) {
        message.info('请选择文件夹');
        upLoadClicktag = true;
        return;
      }
      e.preventDefault();
      let name = e.target.value;
      if (name === '') {
        message.error('文件或文件夹姓名不能为空');
        upLoadClicktag = true;
        return;
      }
      let re = /^[^#\x22]*$/;
      if (!re.test(name)) {
        message.error('文件夹姓名中不能包含#');
        upLoadClicktag = true;
        return;
      }
      if (selectedDetail.id.indexOf('public') > -1) {
        await contentListApis
          .newpublicfolder({
            users: [],
            name: name,
            parentId: selectedDetail.key,
            privilege: 'public',
          })
          .then(async res => {
            if (res && res.success && res.data) {
              message.success('新建文件夹成功');
              setSelectedKeys([res.data.contentId]);
              setSelectedDetail({
                key: res.data.contentId,
                title: res.data.name,
                id: res.data.path,
              });
            } else {
              message.error('新建文件夹失败');
            }
            setOnNew(false);
          });
      } else {
        await contentListApis
          .newprivatefolder({
            users: [],
            name: name,
            parentId: selectedDetail.key,
            privilege: 'private',
          })
          .then(async res => {
            if (res && res.success && res.data) {
              message.success('新建文件夹成功');
              setSelectedKeys([res.data.contentId]);
              setSelectedDetail({
                key: res.data.contentId,
                title: res.data.name,
                id: res.data.path,
              });
            } else {
              message.error('新建文件夹失败');
            }
            setOnNew(false);
          });
      }
      searchpublic();
    }
  };
  const forTree = (tree: any) => {
    return tree.map((item: ITree) => {
      if (item.key === selectedKeys[0]) {
        let data = [];
        if (item.children) {
          data = [
            ...item.children,
            {
              title: (
                <Input
                  defaultValue="新建文件夹"
                  onPressEnter={changeName}
                  onBlur={changeName}
                  ref={changenameRef}
                  autoComplete={'off'}
                />
              ),
              key: 'add',
              id: 'new',
            },
          ];
        } else {
          data = [
            {
              title: (
                <Input
                  defaultValue="新建文件夹"
                  onPressEnter={changeName}
                  onBlur={changeName}
                  ref={changenameRef}
                  autoComplete={'off'}
                />
              ),
              key: 'add',
              id: 'new',
            },
          ];
        }
        return {
          ...item,
          children: data,
        };
      } else if (item.children) {
        return {
          ...item,
          children: forTree(item.children),
        };
      }
      return item;
    });
  };
  // --------新建文件夹end
  const modalOk = () => {
    if (!selectedDetail) {
      message.info('请选择文件夹');
      return;
    }
    if (selectedDetail.title === '公共资源') {
      message.info(
        CopyOrMove
          ? '不能复制文件或文件夹到根目录'
          : '不能移动文件或文件夹到根目录',
      );
      return;
    }
    if (CopyOrMove) {
      // 复制
      // let namelist: Array<string> = []
      // let idlist: Array<string> = []
      // copyAndMovelist.forEach((item: any) => {
      //   idlist.push(item.contentId_)
      //   namelist.push(item.contentId_)
      //   console.log(form.getFieldsValue()[item.contentId_])
      // })
      let datd: any = {
        contentId: copyAndMovelist[0].contentId_,
        copyEntity: {
          tree: [selectedDetail.id],
          type: copyAndMovelist[0].type_,
        },
      };
      if (form.getFieldsValue()[copyAndMovelist[0].contentId_]) {
        datd.name = form.getFieldsValue()[copyAndMovelist[0].contentId_];
      }
      copyandmoveApis.entityCopy(datd).then(async res => {
        if (res && res.success && res.data) {
          message.success('复制成功');
          refresh();
          modalClose();
        } else {
          message.error('复制失败');
          refresh();
          modalClose();
        }
      });
    } else {
      // 移动
      let idlist: Array<string> = [];
      copyAndMovelist.forEach((item: searchTypes.ISearchData) => {
        idlist.push(item.contentId_);
      });
      copyandmoveApis
        .entityMove({
          checkPermission: false,
          paramType: 'RESOURCEID',
          source: idlist,
          target: selectedDetail.key,
          tryMerge: true,
        })
        .then(async res => {
          if (res && res.success && res.data) {
            let detint = setInterval(() => {
              deleteApis.deleteResult(res.data.process_id).then(resdata => {
                if (resdata && resdata.success && resdata.data) {
                  if (resdata.data.status === 'FINISH') {
                    clearInterval(detint);
                    resdata.data.failed_count === 0
                      ? message.success('移动成功')
                      : message.error('移动失败');
                    refresh();
                    modalClose();
                  }
                } else {
                  clearInterval(detint);
                  message.error('移动失败');
                  refresh();
                  modalClose();
                }
              });
            }, 500);
          } else {
            message.error('移动失败');
            refresh();
            modalClose();
          }
        });
    }
  };
  return (
    <Modal
      destroyOnClose
      title={CopyOrMove ? '复制到' : '移动到'}
      visible={modalVisible}
      closable={false}
      width={400}
      footer={[
        <Button key="new" type="primary" onClick={newfolder} disabled={onNew}>
          新建文件夹
        </Button>,
        <Button key="submit" type="primary" onClick={modalOk}>
          确定
        </Button>,
        <Button key="back" onClick={modalClose}>
          取消
        </Button>,
      ]}
    >
      <div className="copy-move-modal">
        <Form name="basic" form={form}>
          {CopyOrMove && <div>重命名</div>}
          {CopyOrMove &&
            copyAndMovelist.map(
              (item: searchTypes.ISearchData, index: number) => (
                <Form.Item
                  className="rename"
                  key={item.contentId_}
                  name={item.contentId_}
                  label={index + 1}
                >
                  <Input />
                </Form.Item>
              ),
            )}
        </Form>
        <div className="movetreebox">
          {loading ? (
            <Spin style={{ marginTop: 230 }} />
          ) : (
            <DirectoryTree
              multiple
              expandedKeys={expandedKeys}
              selectedKeys={selectedKeys}
              onSelect={onSelect}
              onExpand={onExpand}
              treeData={treeData}
            />
          )}
        </div>
      </div>
    </Modal>
  );
};

export default CopyAndMoveModal;
