import React, { useState, useEffect } from 'react';
import { Modal, Button, Form, message, Table, TreeSelect, Popconfirm } from 'antd';
import { useIntl } from 'umi';
import './index.less';
import {
  PlusCircleOutlined
} from '@ant-design/icons';
import contentListApis from '@/service/contentListApis';
import { initGroupUsers } from '@/service/choicepeople';
import rmanApis from '@/service/rman';
interface CreateModalProps {
  modalVisible: boolean;
  modalClose: () => void;
  refresh?: () => void;
  detail: any;
  resourceGroup: any;
}

const MajorTypeModal: React.FC<CreateModalProps> = props => {
  const {
    modalClose,
    modalVisible,
    refresh,
    resourceGroup,
    detail
  } = props;
  
  //递归修改参数
  const getfordata = (folderTree: []): any => {
    return (folderTree && folderTree.map((item: any) => {
      return {
        title: item.categoryName,
        value: `${item.categoryName}_${item.categoryCode}`,
        key: item.categoryName,
        children: item.children.length > 0 ? getfordata(item.children) : []
      }
    }))
  }

  useEffect(() => {
    contentListApis.getTree({category_type: 'theme'}).then((res: any) => {
      if (res?.extendMessage) {
        setMajorCategoryList(getfordata(res?.extendMessage));
      }
    })
  }, [])
  const [userList, setUserlist] = useState<any>([]);
  const [dataList, setDataList] = useState<any[]>([]);
  const [majorCategoryList, setMajorCategoryList] = useState<any>([]);
  const [selectRows, setSelectedRows] = useState<any>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  const intl = useIntl();
  // const [selectedList, setTelectedList] = useState<any>([]);
  const [form] = Form.useForm();
  useEffect(() => {
    setDataList(detail?.major_categories?.map(item => ({majorCategory: item, random: Math.random()})) || [])
  }, [detail])
  useEffect(() => {
    if (modalVisible) {
      //初始化数据
      // fetchGroupUser()
    }
  }, [modalVisible]);
  const columns: any = [
    {
      title: intl.formatMessage({ id: '专业所属大类' }),
      dataIndex: 'majorCategory',
      align: 'center',
      key: 'majorCategory',
      render: (value: string, row: any, index: number) => {
        return <TreeSelect
        style={{ width: '100%' }}
        // placeholder={showplaceholder}
        treeData={majorCategoryList}
        value={value}
        onSelect={(value: any) =>{
          setDataList(dataList.map(item => {
            if (item.random === row.random) {
              return {
                ...item,
                majorCategory: value
              }
            } else {
              return item
            }
          }
          ))
        }}
        // loadData={onLoadChild}
    />;
      },
    },
    {
      title: intl.formatMessage({ id: '操作' }),
      dataIndex: 'orgname',
      key: 'orgname',
      align: 'center',
      render: (value: string, row: any, index: number) => {
        return <Popconfirm
        title="确认删除该条数据？"
        okText="是"
        cancelText="否"
        onConfirm={() =>{
          setDataList(dataList.filter((item: any) => item.random !== row.random))
        }}
      >
        <span style={{ color: 'var(--primary-color)', cursor: 'pointer'}}>删除</span>
      </Popconfirm>
      },
    },
  ]
  const rowSelection = {
    type: 'checkbox',
    onChange: (newSelectedRowKeys: Array<any>, newSelectedRows: any) => {
      setSelectedRowKeys(newSelectedRowKeys.filter(Boolean));
      setSelectedRows(newSelectedRows.filter(Boolean)); //得处理勾选移除后的残余空值对象
    },
    preserveSelectedRowKeys: true,
    selectedRowKeys,
  };
  const fetchGroupUser = () => {
    console.log(resourceGroup)
    initGroupUsers({ contentId: resourceGroup.contentId_ }).then((res: any) => {
      const temp = res?.data?.map((item: any, index: number) => {
        return {
          accountshow: item.userCode,
          user_code: item.userCode,
          majorCategory: item.userName,
          orgname: item.userOrganizeName,
          jurisdiction: item.all ? 3 : (item.canWrite ? 2 : 1)
        }
      })
      console.log('初始值', temp);
      setUserlist(temp)
    })
  };
  const handleCancel = () => {
    modalClose();
  };
  const addMajory = () => {
    // console.log(resourceGroup)
    setDataList([...dataList, { majorCategory: '', random: Math.random() }])
  };
  const removeUser = (e: any) => {
    console.log(selectRows);
    setDataList(dataList.filter((item: any) => selectRows.map(item => item.random).indexOf(item.random) === -1))
  };
  const onFinish = () => {
    if(dataList.filter(item => item.majorCategory === '').length > 0) {
      message.error(intl.formatMessage({ id: '请选择专业所属大类' }))
      return
    }
    rmanApis.addMajorCategory({
      "folderContentId": detail.contentId_,
      "folderPath": detail.tree_[0],
      "majorCategories": dataList.map(item => item.majorCategory)})
    .then((res: any) => {
      if (res?.success) {
        message.success(intl.formatMessage({ id: '设置成功' }))
        refresh?.()
        handleCancel()
      }
    })
  }
  return (
      <Modal
        destroyOnClose={true}
        className='majorTypeModal'
        title={intl.formatMessage({ id: '专业所属大类设置' })}
        visible={modalVisible}
        onCancel={handleCancel}
        footer={[
          <Button
            type='primary'
            onClick={onFinish}
          >
            {intl.formatMessage({ id: '保存' })}
          </Button>
        ]}
        width={680}
      >
        <div className='content'>
          <div className='opt_btn'>
            <Button
              type='primary'
              style={{marginRight: 10}}
              onClick={addMajory}
            >
              <PlusCircleOutlined />
              {intl.formatMessage({ id: '专业所属大类' })}
            </Button>
            <Popconfirm
              title="确认删除该条数据？"
              okText="是"
              cancelText="否"
              onConfirm={removeUser}
            >
        <Button  disabled={selectRows.length === 0}  >
              {intl.formatMessage({ id: '批量移除' })}
            </Button>
      </Popconfirm>
            
          </div>
          <div>
            <Table
              dataSource={dataList}
              columns={columns}
              rowKey='random'
              rowSelection={rowSelection as any}
              // pagination={{
              //   position: ['bottomCenter'],
              //   pageSizeOptions: ['10', '20', '30', '50'],
              //   showSizeChanger: true,
              //   total: userList.length,
              //   showQuickJumper: true,
              //   // onChange: (page: number) =>
              //   //   setQuery({
              //   //     ...query,
              //   //     page,
              //   //   }),
              //   showTotal: total => `共 ${total} 条`,
              // }}
              scroll={{ y: '400px' }}
            />
          </div>
        </div>
      </Modal>
  );
};

export default MajorTypeModal;