import React, { FC, useState, useMemo, useRef, useEffect } from 'react'
import { IBaseEntityTypes } from '@/types/entityTypes'
import * as Pdf from 'react-pdf'
import { Pagination } from 'antd'
import Loading from '@/loading'

interface IDocumentProps extends IBaseEntityTypes {

}

const Documnet: FC<IDocumentProps> = ({ src, onError }) => {
    const [node, setNode] = useState<HTMLDivElement | null>(null)
    const [numPages, setNumPages] = useState<number>(0);
    const [pageNumber, setPageNumber] = useState<number>(1);
    const onDocumentLoadSuccess = (op: Pdf.pdfjs.PDFDocumentProxy) => {
        setNumPages(op.numPages)
    }
    return (
        <div className="entity-document">
            <Pdf.Document
                file={src}
                onLoadError={onError}
                onSourceError={onError}
                onLoadSuccess={onDocumentLoadSuccess}
                loading={Loading}
                inputRef={(ref) => setNode(ref)}
            >
                {
                    Array.from(new Array(numPages), (el, index) => (
                        <Pdf.Page key={`page_${index + 1}`} width={1000} pageNumber={index + 1}></Pdf.Page>
                    ))
                }
            </Pdf.Document>
        </div>
    )
}

export default Documnet