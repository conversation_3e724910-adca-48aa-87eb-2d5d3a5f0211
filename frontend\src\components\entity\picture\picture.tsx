import React, { FC, useRef, useEffect, useState } from 'react';
import { IBaseEntityTypes } from '@/types/entityTypes';
import { useSelector } from 'umi';
import { IPermission } from '@/models/permission';
import globalParams from '@/permission/globalParams';
import './index.less'
interface IPictureProps extends IBaseEntityTypes { }

const Picture: FC<IPictureProps> = ({ src, onError, linkWatermark }) => {
  const imgNode = useRef<HTMLImageElement>(null);
  const imgParentNode = useRef<HTMLDivElement>(null);
  const [style, setStyle] = useState({});
  const { rmanGlobalParameter } = useSelector<{ permission: any }, IPermission>(({ permission }) => permission);
  const userInfo = (window as any).login_useInfo

  const onLoad = () => {
    if (imgNode && imgNode.current && imgParentNode && imgParentNode.current) {
      if (
        imgNode.current.clientWidth / imgNode.current.clientHeight >
        imgParentNode.current.clientWidth / imgParentNode.current.clientHeight
      ) {
        setStyle({
          width: '100%',
        });
      } else {
        setStyle({
          height: '100%',
        });
      }
    }
  };
  // useEffect(()=>{
  //     window.addEventListener('resize',onLoad)
  //     return () => {
  //         window.removeEventListener('resize',onLoad)
  //     }
  // },[])'
  return (
    <div className="entity-img" ref={imgParentNode}>
      <img
        src={src}
        // style={style}
        // onLoad={onLoad}
        onError={onError}
        ref={imgNode}
        alt=""
      />
      {/* 水印 */}
      {
        rmanGlobalParameter.includes(globalParams.watermark_display) && linkWatermark &&
        <div className='watermarkShow' style={{width: imgNode?.current?.clientWidth, height: imgNode?.current?.clientHeight,left: imgNode?.current?.offsetLeft,top: imgNode?.current?.offsetTop}}>
          {Array(20).fill(null).map((_, i) => (
            <div key={i}>{linkWatermark || ''} </div>
          ))}
        </div>
      }
    </div>
  );
};

export default Picture;
