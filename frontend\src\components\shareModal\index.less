.shareModal{
  .ant-modal-header{
    border-bottom:0px;
  }
  .ant-modal-content{
    .ant-modal-body{
      padding: 0px 20px;
      .ant-form-item{

      }
      .userFinish{
        text-align: center;
        padding-bottom: 20px;
      }
    }
    .row1{
      display: flex;
      .span{
        display: flex;
        width: 70px;
        /* text-align: right; */
        align-items: center;
        justify-content: flex-end;
      }
      .ant-select{
        width: 360px;
        margin-left: 20px;
      }
    }
    .row2{
      display: flex;
      padding-top: 30px;
      .span{
        display: flex;
        width: 70px;
        align-items: center;
        justify-content: flex-end;
      }
      .ant-radio-group{
        margin-left: 20px;
      }
    }
  }
}
.linksetmodal{
  .ant-modal-body{
    >div{
      >div{
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 10px;
        .ant-input{
          width: auto;
        }
        .extra{
          font-size: 12px;
          color: rgba(0, 0, 0, 0.45);
          margin-left: 1%;
        }
      }
    }
  }
}
//移动端样式处理
@media screen and (max-width:768px) {
  .shareModal{
    .ant-modal-content{
      .ant-modal-body{
        .ant-form{
          .ant-form-item{
            .ant-row{
              flex-wrap:nowrap;
              .ant-form-item-label{
                flex: none !important;
                &::after{
                  content: '：';
                }
              }
              .ant-form-item-control{
                flex: 1 1 !important;
                .ant-form-item-control-input{
                  .ant-form-item-control-input-content{
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space:nowrap;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

}