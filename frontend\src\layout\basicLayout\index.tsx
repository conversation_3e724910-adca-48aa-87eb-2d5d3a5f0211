import React, { FC, useEffect, useState  } from 'react';
import {
  Link,
  setLocale,
  getLocale,
  IRouteComponentProps,
  history,
  useDispatch,
  useSelector,
  useHistory
} from 'umi';
import { Menu, Dropdown, Button, Space, message } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import UserMenu from '@/components/UserMenu';
import loginApis from '@/service/loginApis';
import './index.less';
import { Select } from 'antd';
import perCfg from '@/permission/config';
import { IPermission } from '@/models/permission';
import { IconFont } from '@/components';
import Header from "@/components/header"
import { ModuleCfg } from "@/permission/globalParams"
import contentListApis from '@/service/contentListApis';
import axios from 'axios'

const { Option } = Select;

const BasicLayout: FC<IRouteComponentProps> = ({
  children,
  location: { pathname },
}) => {
  const [showTop, setShowTop] = useState<Number>(0);
  const [loginName, setLoginname] = useState<string>('');
  let history: any = useHistory();
  const dispatch = useDispatch();
  const changeLocale = (value: string) => {
    setLocale(value);
  };
  const { parameterConfig, permissions, modules } = useSelector<{ permission: any }, IPermission>(
    ({ permission }) => permission,
  );
  const { title, isShow, managementLogo } = useSelector(({ themes }: any) => {
    return themes;
  });
  const [userInfo, setUserInfo] = useState<any>(null);
  const active = pathname?.split("/")?.[2] ?? ''
  useEffect(() => {
    if (
      pathname.includes('/basic/contentDetail') ||
      pathname.includes('/basic/demo') ||
      pathname.includes('/basic/shareDetail') ||
      pathname.includes('/basic/out') ||
      pathname.includes('basic/resourceSelect')
    ) {
      setShowTop(0);
    } else if (pathname.includes("/basic/rmanCenterList")|| pathname.includes("/basic/rmanDetail") || pathname.includes("/task/taskprogress")|| pathname.includes("/basic/similarityGraph")) {
      setShowTop(3)
    } else if (
      pathname.includes("/basic/rmanList")
    ) {
      setShowTop(1);
    } else {
      setShowTop(2);
    }
  }, [pathname]);
  useEffect(() => {
    setLocale(localStorage.getItem("lang") ==='en'? 'en-US': 'zh-CN')
    loginApis.fetchUserInfo().then((item: any) => {
      if (item && item?.errorCode === "success") {
        setLoginname(item.extendMessage?.nickName);
        localStorage.setItem("userinfo",JSON.stringify(item.extendMessage));//便于资源详情页获取
        (window as any).login_useInfo = item.extendMessage
        setUserInfo(item.extendMessage)
      }
    });
    screenResize();
    window.addEventListener('resize',function(){
      //移动端判定
      screenResize();
      return ()=>{
        window.removeEventListener('resize',screenResize);
      }
    })
  }, []);
  const screenResize = ()=>{
    //移动端判定
    if (
      navigator.userAgent.match(/Mobi/i) ||
      navigator.userAgent.match(/Android/i) ||
      navigator.userAgent.match(/iPhone/i) ||
      window.innerWidth<768
    ) {
      dispatch({
        type: 'config/updateState',
        payload: {
          mobileFlag: true,
          leftMenuExpand : false,
        },
      });
    }else{
      dispatch({
        type: 'config/updateState',
        payload: {
          mobileFlag: false,
          leftMenuExpand : true,
        },
      });
    }
  }
  const locale = getLocale();
  // $('basic_height').bind("selectstart", function() {return false;});
  return (
    <div className="basic_height" style={{height: parameterConfig.target_customer as string === 'npu'?'calc(100vh - 70px)': '100vh'}}>
      {showTop === 0 ? (
        ''
      ) : showTop === 1 ? (
        <Header
          subtitle={'资源管理'}
          showNav={false}
          ifBack={modules.includes(ModuleCfg.jove) && modules.includes(ModuleCfg.teacher)}
          user_Info={userInfo}
          navActive={""}
        />) : showTop === 3 ? (
        <Header
          ifBack={modules.includes(ModuleCfg.jove) && modules.includes(ModuleCfg.teacher)}
          user_Info={userInfo}
          showNav={true}
          navActive={active}
        />) : (
        <div className="basic_top">
          <div className="basic_left">
            <div className="logo">
              <img
                src={
                  managementLogo ||
                  require(`../../images/contentlibrary/logo.png`)
                }
              />
              <div>{isShow ? title : null}</div>
            </div>
            {/*<div className="administration administration1">*/}
            {/*  <span>资源管理</span>*/}
            {/*</div>*/}
            {/*<div*/}
            {/*  className="administration"*/}
            {/*  onClick={() => window.open('/cvodweb/manager')}*/}
            {/*>*/}
            {/*  <span>发布管理</span>*/}
            {/*</div>*/}
          </div>
          <div className="link">
            {/* <div>
                <Select onChange={changeLocale} value={locale}>
                  <Option value="zh-CN">中文</Option>
                  <Option value="en-US">英文</Option>
                </Select>
              </div> */}
            <Space>
              {permissions.includes(perCfg.jove) && (
                <Button
                  type="link"
                  size="small"
                  className="linkbox"
                  onClick={() => window.open('/joveone/?siteCode=S1')}
                >
                  <IconFont type="iconjianji" />
                  <span>视频剪辑</span>
                </Button>
              )}
              {permissions.includes(perCfg.cvod) && (
                <Button
                  type="link"
                  size="small"
                  className="linkbox"
                  onClick={() => window.open('/cvodweb')}
                >
                  <IconFont type="iconzaixianjiaoyu" />
                  <span>在线教学</span>
                </Button>
              )}
            </Space>
            <div className="divider" />
            <UserMenu>
              <div className="linkbox">
                <img src={require('../../images/contentlibrary/yh.png')} />
                <span>
                  {loginName}
                  <DownOutlined className="icon" />
                </span>
              </div>
            </UserMenu>
          </div>
        </div>
      )}
      <div className="basic_content">{children}</div>
    </div>
  );
};

export default BasicLayout;
