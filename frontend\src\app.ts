import { RequestConfig, getDvaApp, ErrorShowType } from 'umi';
import { RequestOptionsInit } from 'umi-request';
import { history } from 'umi';
import { IResponse } from './types/requsetTypes';

// 请求的数量
let requsetCount: number = 0;

/**
 * 拦截请求，显示loading
 * @param {boolean} ifLoading 当前接口是否显示loading
 */
const requestLoadingShow = (ifLoading = true) => {
  if (ifLoading) {
    const { _store } = getDvaApp();
    _store.dispatch({
      type: 'config/changeShowLoading',
      payload: {
        value: true,
      },
    });
    requsetCount++;
  }
};

/**
 * 拦截响应，隐藏loading
 *
 * @param {boolean} isSleep
 */
const requestLoadingHide = async (ifLoading = true) => {
  if (ifLoading) {
    requsetCount--;
    if (requsetCount === 0) {
      const { _store } = getDvaApp();
      _store.dispatch({
        type: 'config/changeShowLoading',
        payload: {
          value: false,
        },
      });
    }
  }
};
const getQueryString = (url: any) => {
  const regex = /[?&]([^=#]+)=([^&#]*)/g;
  const params = {} as any
  let match;
  while (match = regex.exec(url)) {
    params[decodeURIComponent(match[1])] = decodeURIComponent(match[2]);
  }
  return params;
}

const requestInterceptors = (
  url: string,
  options: RequestOptionsInit & { ifLoading?: boolean }
) => {
  requestLoadingShow(options.ifLoading);
  let baseUrl = '';
  /**
   * 此处通过 url上有'v1'是rman外部地址，无'v1'为rman内部地址
   */
  //  console.log('rman_app_start',url,options)
   options.headers={
      ...options.headers,
     'Content-Type': 'application/json'
   }
   if(getQueryString(window.location.href)["x-cas-token"]){
    localStorage.setItem("x-cas-token-app",getQueryString(window.location.href)["x-cas-token"]  || '')
  }
  if(localStorage["x-cas-token-app"]){
    options.headers["x-cas-token"]=localStorage["x-cas-token-app"]|| ''
  }
  const isOtherSystem = url.indexOf('/v1') > -1 || url.includes('/learn') || url.includes('/textclip') ;
  if (process.env.NODE_ENV === 'development' && !isOtherSystem && !url.includes('exam-api')&& !url.includes('flowbpm') && !url.includes('sensitiveword') && !url.includes('ipingestman')){
    //   baseUrl = '/rman/safeproxy';
    //   options.headers={
    //     ...options.headers,
    //     realurl:'rman/v1'+url
    //   }
    //   console.log(options)
    //   return {
    //     url: baseUrl,
    //     options:options,
    //   };
    // }else{
    //   baseUrl = '/api'; 
    // }
    baseUrl = '/api';
  }
  else if(url.includes('exam-api') || url.includes('ipingestman') || url.includes('flowbpm')) {
    baseUrl = ''
  }
  else if(url.includes('sensitiveword')){
    baseUrl = ''
  } 
  else if(url.includes('terminator')){
    baseUrl = '';
  }
  else if (process.env.NODE_ENV === 'development' && isOtherSystem) {
    baseUrl = '/osapi';
  } else if (!isOtherSystem) {
    if(options.flag){
      baseUrl = '/rman/safeproxy';
      options.headers={
        ...options.headers,
        realurl:'/rman/v1'+url
      }
      console.log(options)
      return {
        url: baseUrl,
        options:options,
      };
    }else{
      baseUrl = '/rman/v1';
    }
  }else if(isOtherSystem){
    if(options.flag){
      baseUrl =  url.split('/').slice(0,2).join('/')+`/safeproxy`;
      options.headers={
        ...options.headers,
        realurl:url
      }
      // console.log(options)
      return {
        url: baseUrl,
        options:options,
      };
    }
  }
  // console.log('rman_app_end',baseUrl + url,options)
  return {
    url: baseUrl + url,
    options,
  };
};

function sleep(ns: number) {
  return new Promise(resolve =>
    setTimeout(() => {
      resolve(null);
    }, ns),
  );
}

const responseInterceptors = async (response: Response, options: any) => {
  if (options.isSleep) {
    await sleep(1000);
  }
  requestLoadingHide(options.ifLoading);
  // return Promise.resolve(response);
  return response;
};

// const SleepResponseInterceptors = async (response: Response, options: any) => {
//   if (!options.isSleep) {
//     return response;
//   } else {
//     await sleep(1000);
//     return response;
//   }
// }

const getRequest = (): RequestConfig => {
  return {
    timeout: 60000,
    errorConfig: {
      adaptor: (res: IResponse<any>, ctx) => {
        return {
          success: res.success,
          data: res.data,
          errorCode: res.error && res.error.code ? res.error.code : '',
          errorMessage: res.error && res.error.title ? res.error.title : '',
          showType:
            res.error && res.error.code && res.error.code === '401'
              ? ErrorShowType.REDIRECT
              : ErrorShowType.ERROR_MESSAGE,
          traceId: res.requestId,
          host: res.hostId,
        };
      },
      errorPage: '/login',
    },
    middlewares: [],
    requestInterceptors: [requestInterceptors],
    responseInterceptors: [responseInterceptors],
  };
};

export const request: RequestConfig = getRequest();

export const render = (oldRender: () => void) => {
  oldRender(); // 必须调用
};

export const locale = {
  default: 'zh-CN',
};