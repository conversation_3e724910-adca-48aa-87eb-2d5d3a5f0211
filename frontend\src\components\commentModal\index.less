.commentModal {
  .ant-modal-body {
    height: 300px;
    overflow: auto;
    padding: 24px;
    .title{
      margin-bottom: 15px;
    }
    img {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      margin-right: 6px;
    }
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      button {
        height: 60px;
      }
    }

    .list {
      display: flex;
      img {
        margin-right: 15px;
      }

      .list-content {
        flex: 1;
        .top {
          margin-bottom: 20x;
          span {
            color: #000;
          }
          .nickname {
            color: #6f6e6f;
            margin-right: 10px;
          }
          .time{
            color: #b8b8b8;
            font-size: 12px;
          }
        }
      }
      span.delete{
        cursor: pointer;
        color: #1890ff;
      }
    }
  }

  // .ant-modal-body{
  //   .title{
  //     margin: 0 0 10px 55px;
  //   }
  //   .list_item{
  //     margin-bottom: 10px;
  //     margin-left: 55px;
  //     >a{
  //       margin-left: 20px;
  //     }
  //   }
  //   .tips{
  //     opacity: 0.5;
  //     margin-left: 55px;
  //   }
  // }
}