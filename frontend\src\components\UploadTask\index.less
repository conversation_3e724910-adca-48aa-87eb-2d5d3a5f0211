.upload_task {
  position: fixed;
  right: 0px;
  bottom: 45px;
  transition: all .5s;
  display: flex;
  z-index: 2000;
  .open {
    font-size: 20px;
    color: #ffffff;
    height: 40px;
    width: 50px;
    background-color: var(--primary-color);
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    left: -50px;
  }
  .uploadtask_box {
    box-shadow: 0 0 10px #666;
    padding: 18px;
    width: 700px;
    height: 400px;
    background-color: #fff;
    overflow: auto;
    .header {
      border-bottom: 1px solid #e4e4e4;
      // margin-bottom: 18px;
      font-size: 16px;
      height: 40px;
    }

    .task_hearder {
      display: flex;
      height: 50px;
      align-items: center;
      position: relative;

      .progress_bar {
        position: absolute;
        height: 50px;
        top: 0px;
        left: 0px;
        background-color: rgba(242, 148, 2, 0.3);
        width: 50%;
        transition: all .2s linear 0s;
        pointer-events: none;
        &.progress_bar_success{
          background-color: rgba(0, 172, 0, 0.3);
        }
        &.progress_bar_error{
          background-color: rgba(245, 34, 45, 0.4);
          width: 100% !important;
        }
      }

      .opt-btn{
        z-index: 10;
        cursor: pointer;
        //&.no-close{
        //  cursor: not-allowed;
        //}
        &:hover{
          color: var(--primary-color);
        }

      }

      div {
        flex: 1;
      }

      div:nth-of-type(1) {
        text-align: center;
        flex: 0.7;
      }
      div:nth-of-type(2) {
        flex: 5.5;
        max-width: 350px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      div:nth-of-type(3) {
        flex: 1.5;
      }

      div:nth-of-type(4) {
        flex: 1.5;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      div:nth-of-type(6) {
        text-align: center;
        flex: 0.7;
      }
    }
  }
}
.close_task{
  right: -700px;
  .open{
    opacity: 0.4;
  }
}
.mobile_task.close_task{
  right: -90% !important;
}
.mobile_task{
  width:90% !important;
  .open{
    width: 30px !important;
    left: -30px !important;
  }
  .uploadtask_box{
    padding: 10px;
    .task_hearder{
      justify-content:space-between;
      >div{
        &:first-child{
          flex: 0.7 1 !important;
        }
        flex: 1 1 !important;
        white-space: nowrap;
        text-align: center;
      }
    }
  }
  
}
