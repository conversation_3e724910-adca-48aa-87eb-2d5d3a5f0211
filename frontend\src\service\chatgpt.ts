import { message } from 'antd';

// 智能详解 带上请求头token 如果带上了token就使用heard的token 反之使用服务器配置的token
// export function chatsteme(obj:any) {
//   var data = JSON.stringify(obj);
//   var config = {
//     method: 'post',
//     url: 'https://gpt.sobeydev.com/api/chat',
//     headers: {
//       'content-type': 'application/json',
//       // 'token': '***************************************************',
//       'X-CSRF-Token': '123123123'
//     },
//     data : data
//   };
//   return axios(config)
//     .then(function (response:any) {
//       return response.data.choices;
//     })
//     .catch(function (error:any) {
//       message.info('智能解析服务异常，请稍后再试！')
//     });

// }


export async function getSmartExplain(data:any,callback:any,overback:any){
  try {
    const controller = new AbortController();
    const res = await fetch("https://gpt.sobeydev.com/api/chat-stream", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        // "token": "***************************************************",
        'X-CSRF-Token': '123123123'
      },
      //[{"role":"user","content":label}],
      body: JSON.stringify({"model":"gpt-3.5-turbo","messages":data,"stream":true,"temperature":1,"max_tokens":2000,"presence_penalty":0}),
      signal: controller.signal,
    });
    let responseText = "";
    let uUint8arr: any = [];

    if (res.ok) {
      const reader = res.body?.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const content = await reader?.read();
        if (content?.value) {
          uUint8arr.push(...content.value);
          responseText = decoder.decode(new Uint8Array(uUint8arr));
        }
        // const text = decoder.decode(content?.value);
        // responseText += text;
        callback(responseText);
        const done = !content || content.done;
        if (done) {
          overback(responseText);
          break;
        }
      }

    } else if (res.status === 401) {
      console.error("Anauthorized");
      message.info('智能解析服务未授权，请稍后再试！')
      overback();

    } else {
      console.error("Stream Error");
      message.info('智能解析流异常，请稍后再试！')
      overback();
    }
  } catch (err) {
    console.error("NetWork Error", err);
    message.info('智能解析网络异常，请稍后再试！')
    overback();
  }
}
