/*
 * @Description: 
 * @Author: liulu
 * @Date: 2020-12-29 10:26:11
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2021-11-29 15:37:15
 */
import React from 'react';

export interface MenuItem {
  name?: string;
  label: string;
  value?: string;
  icon?: React.Component | any;
}
export interface ITree {
  title: string;
  key: string;
  children?: ITree[];
  icon?: React.ReactNode;
}
export interface DownLoad {
  downloadlink: string;
  downloadname: string;
}
export interface BreadCrumb {
  isUpload?: any;
  item?: any;
  name: string;
  folderId: string;
  path: string;
}

export interface MaterialList {
  userName?: string;
  isRead?: boolean;
  uniqueCode?: any;
  isCollection: boolean;
  contentId_: string;
  contentIds?: any;
  link?: any;
  linkPassword?: any;
  shareType?: any;
  createDate_: string;
  createUser_: string;
  keyframe?: string;
  keyframe_?: string;
  name_: string;
  privilege_?: string;
  operateCode_: number;
  order_: number;
  site_: string;
  system_: string;
  tree_: Array<string>;
  type_: string;
  duration: number;
  filesize: number;
  fileext: string;
  asr_status?:number;
}
export interface ISortFields {
  field: string;
  isDesc: boolean;
}
export interface Iconditions {
  field: string;
  searchRelation: number;
  value: string[];
}
export interface ISearchlist {
  folderId: string;
  sortFields: ISortFields[];
  pageIndex: number;
  pageSize: number;
  keyword?: string[];
  conditions?: Iconditions[];
  folderPath?: string
}

export interface Idetail {
  type_: string;
  name_: string;
  fatherTreeId: string;
  fatherTreePath?: string;
  keyframe: string;
}
export interface IrequestsearchData {
  starttime: Array<string>;
  endtime: Array<string>;
  teacher: Array<string>;
  college: Array<string>;
  keyword?: Array<string>;
  name_: string;
  major: Array<string>;
  type: Array<string>;
  usestatus: string;
  cataloging_status: string;
  sintelligence?: string;
  smart_status?: string;
  labels?: string;
  word?: string;
  seat?: string;
  semester?: string;
  week?: string;
  source?:Array<string>;
  sasr_status?: string;
  socr_status?: string;
  createUser_?:Array<string>;
  creator:Array<string>;
  knowledge_point_key?: string;
}
