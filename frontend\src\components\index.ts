import MenuTree from './menuTree';
import ContentItem from './ContentItem';
import SearchBox from './search';
import ListTop from './ContentItem/listtop';
import NewFolder from './ContentItem/newfolder';
import UploadBox from './UploadBox';
import UploadTask from './UploadTask';
import LinkBox from './LinkBox';
import DeleteModal from './deleteModal';
import SearchTree from './SearchTree/SearchTree';
import ShareModal from '@/components/shareModal';
import ResourceGroupModal from '@/components/resourceGroupModal';
import Loading from './loading/loading';
import { IconFont } from './iconFont/iconFont';
import UploadButton from './upload/uploadButton/uploadButton';
import SortForm from '@/components/sortForm';
import DownloadModal from '@/components/downloadModal';
import IntelligentAnalysisModal from '@/components/IntelligentAnalysisModal';
import ReservationModal from '@/components/ReservationModal';


import H5Player, { Player } from '@/components/h5player';
import AddTeacherModal from '@/components/AddTeacherModal';
import ProcessModal from './processModal'
export {
  MenuTree,
  ContentItem,
  SearchBox,
  ListTop,
  UploadBox,
  UploadTask,
  LinkBox,
  SearchTree,
  ShareModal,
  ResourceGroupModal,
  Loading,
  IconFont,
  UploadButton,
  DeleteModal,
  NewFolder,
  SortForm,
  DownloadModal,
  H5Player,
  IntelligentAnalysisModal,
  AddTeacherModal,
  ReservationModal,
  ProcessModal
};
export type { Player };

