import React, { FC, useEffect, useRef, useState } from 'react';
import MetadataService from '@/service/metadataService';
import {
  Table,
  Button,
  Input,
  Space,
  Modal,
  Form,
  Select,
  InputNumber,
  Checkbox,
  message,
  Tooltip,
  Progress
} from 'antd';
import { Link } from 'umi';
import './index.less';

const formLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 12 },
};
/**
 * hive基础数据
 * @constructor
 */
const MetadataBasic: FC = () => {
  const [dataLoading, setDataLoading] = useState(false);
  const [typeList, setTypeList] = useState<MetadataTypes.HiveMetadataType[]>(
    [],
  );
  const [totalNumber, setTotalNumber] = useState(0);
  const [modalVisible, setModalVisible] = useState(false);
  const [isAdd, setIsAdd] = useState(false);
  const [editMetadataForm] = Form.useForm();
  const [query, setQuery] = useState<{
    code?: string;
    name?: string;
    page: number;
    size: number;
  }>({
    page: 1,
    size: 20,
  });
  const [showmodal, setShowmodal] = useState(false);
  //当前重建索引的id
  const [currentId, setCurrentId] = useState<any>(null); 
  // 计时器1
  let timer1:any= useRef(null);
  // 重建的数据
  const [rebuildData, setRebuildData] = useState<any>(null);
  // 是否正在加载
  const [loading, setLoading] = useState(false);


  useEffect(() => {
    setDataLoading(true);
    MetadataService.fetchHiveMetadataTypeList(query)
      .then(res => {
        if (res?.success && res.data) {
          setTypeList(res.data.data.results);
          setTotalNumber(res.data.data.total);
        }
        setDataLoading(false);
      })
      .catch(err => {
        setDataLoading(false);
      });
  }, [query]);

  const handleFormFinish = async (values: any) => {
    // 添加
    if (isAdd) {
      delete values.id;
      const res = await MetadataService.addHiveMetadataType(values);
      if (res?.success) {
        message.success('新增业务对象成功');
        setModalVisible(false);
        setQuery({ ...query });
      }
    } else {
      const res = await MetadataService.updateHiveMetadataType(values);
      if (res?.success) {
        message.success('修改业务对象成功');
        setModalVisible(false);
        setQuery({ ...query });
      }
    }
  };

  const columns = [
    {
      title: 'code',
      dataIndex: 'code',
    },
    {
      title: '名字',
      dataIndex: 'name',
    },
    {
      title: '操作',
      dataIndex: 'code',
      render: (code: string, record: any) => (
        <Space>
          <Button
            size="small"
            type="primary"
            onClick={() => {
              editMetadataForm.setFieldsValue(record);
              setIsAdd(false);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Link to={`/basicentity/${code}/${record.id}`}>
            <Button type="primary" size="small">
              编辑基础元数据
            </Button>
          </Link>
          <Link to={`/entity/basic/${code}`}>
            <Button type="primary" size="small">
              编辑预览元数据
            </Button>
          </Link>
          <Link to={`/entity/upload/${code}`}>
            <Button type="primary" size="small">
              编辑上传元数据
            </Button>
          </Link>
        </Space>
      ),
    },
  ];


  useEffect(() => {
    if(currentId){
      if(timer1.current){
        clearInterval(timer1.current);
        timer1.current = null;
      }
      queryprogress();
      timer1.current = setInterval(() => {
        queryprogress();
      }, 5000);
    }

    return ()=>{
      clearInterval(timer1.current);
      timer1.current = null;
    }
  }, [currentId]);

  // 重建索引
  const restindex = () => {
    setLoading(true);
    MetadataService.rebuildindex().then((res:any) => {      
      if(res.data.isSuccess){
        setCurrentId(res.data.data);    
        setLoading(false);
      }
    })
  }

  //重置索引
  const newindex = () => {
    setLoading(true);
    MetadataService.resetsearch().then((res:any) => {
      if(res.data.isSuccess){
        setCurrentId(res.data.data);
        setLoading(false);
      }
    })
  }


  // 查询进度
  const queryprogress = () => {
    MetadataService.getprocess(currentId).then((res:any) => {
      if(res.data.isSuccess){
        setRebuildData(res.data.data);
        if(res.data.data.status == 'FINISH'){
          clearInterval(timer1.current);
          timer1.current = null;
          setCurrentId(null);
        }
      }
    })
  }


  return (
    <div className="metadata_basic_container">
      <div className="metadata_basic_header">
        <div>
          <Button
            type="primary"
            onClick={() => {
              editMetadataForm.setFieldsValue({
                id: '',
                name: '',
                code: '',
                order: null,
              });
              setIsAdd(true);
              setModalVisible(true);
            }}
          >
            新增
          </Button>
          <Button
            type="primary" danger style={{marginLeft: 20}}
            onClick={() => {setShowmodal(true);setCurrentId(null);}}
          >
            修改索引
          </Button>
        </div>
        <div>
          <Input.Search
            placeholder="请输入code"
            enterButton="搜索"
            onSearch={value => setQuery({ ...query, code: value })}
          />
        </div>
      </div>
      <Table
        columns={columns}
        dataSource={typeList}
        rowKey="id"
        loading={dataLoading}
        pagination={{
          position: ['bottomCenter'],
          pageSize: 20,
          total: totalNumber,
          showQuickJumper: false,
          onChange: (page: number) =>
            setQuery({
              ...query,
              page,
            }),
          showTotal: total => `共 ${total} 条`,
        }}
      />
      <Modal
        title={isAdd ? '新增业务对象' : '编辑业务对象'}
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => editMetadataForm.submit()}
      >
        <Form
          name="editMetadata"
          {...formLayout}
          initialValues={{}}
          onFinish={handleFormFinish}
          form={editMetadataForm}
        >
          <Form.Item name="id" hidden={true} noStyle />
          <Form.Item
            label="Code"
            name="code"
            rules={[{ required: true, message: '请填写Code' }]}
          >
            <Input disabled={!isAdd} />
          </Form.Item>
          <Form.Item
            label="名称"
            name="name"
            rules={[{ required: true, message: '请填写名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="顺序"
            name="order"
            rules={[{ required: true, message: '请填写顺序' }]}
          >
            <InputNumber />
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        title={currentId!=null ? '进度' :"索引" }
        visible={showmodal}
        onCancel={()=>setShowmodal(false)}
        footer={[
          <Button key="rest" disabled={currentId!=null || loading} type="primary" onClick={restindex} danger>重建索引</Button>,
          <Button key="new" disabled={currentId!=null || loading} type="primary" onClick={newindex} danger>重置索引</Button>,
          <Button key="back" disabled={currentId!=null ||loading} type="primary" onClick={()=>{setShowmodal(false)}}>取消</Button>
        ]}
      >
        {
          rebuildData?
          <div style={{width:'100%'}}>
            <Progress percent={Number((((rebuildData.success_count + rebuildData.failed_count)/rebuildData.total_count) * 100).toFixed(0))} />
            <p>总数：{rebuildData.total_count} / 成功：{rebuildData.success_count} / 失败：{rebuildData.failed_count}</p>
          </div>
          :
          <>
            <p>重建索引是先把ES数据库里面的删除，再从mongo数据库里面复制到ES</p>
            <span>重置索引是从mongo数据库里面复制到ES新建一个库，原来的保留</span>
          </>
        }
      </Modal>
    </div>
  );
};
export default MetadataBasic;
