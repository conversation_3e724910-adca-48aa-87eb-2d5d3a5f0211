import React, { FC, useState } from 'react';
import { TreeItemProps } from '@/components/menuTree/type';
import {
  RightOutlined,
  DownOutlined,
  UserOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import './index.less';

const TreeItem: FC<TreeItemProps> = ({
  title,
  isExpand = false,
  isSelected = false,
  isPerson = false,
  onClick,
  afterDom = null,
  children,
}) => {
  const [expand, setExpand] = useState<boolean>(isExpand);
  return (
    <div className="tree_node">
      <div className="tree_content">
        {!isPerson && expand ? (
          <DownOutlined onClick={_ => setExpand(false)} />
        ) : !isPerson && !expand ? (
          <RightOutlined onClick={_ => setExpand(true)} />
        ) : (
          <span style={{ width: 7 }} />
        )}
        <span
          className={isSelected ? 'title active' : 'title'}
          onClick={e => onClick(e)}
        >
          {isPerson ? <UserOutlined /> : <TeamOutlined />}
          {title}
        </span>
        {isSelected && afterDom}
      </div>
      {expand && <div className="tree_children">{children}</div>}
    </div>
  );
};

export default TreeItem;
