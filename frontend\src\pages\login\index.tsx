import React, { FC, useState, useEffect } from 'react';
import { Form, Input, Button, Checkbox } from 'antd';
import { Store } from 'antd/lib/form/interface';
import { RouteComponentProps } from 'react-router-dom';
import './index.less';
import { useIntl, history, useDispatch, useSelector } from 'umi';
import loginApis from '@/service/loginApis';
import Loading from '@/components/loading/loading';
import { Base64 } from 'js-base64';

interface ILoginParams {
  username: string;
  password: string;
  remember: boolean;
}

const LoginPage: FC<RouteComponentProps> = props => {
  const [loading, setLoading] = useState<boolean>(false);
  const dispatch = useDispatch();
  const { title, logoUrl } = useSelector(({ themes }: any) => {
    return themes;
  });
  let username: string = '';
  let password: string = '';
  try {
    username = localStorage.getItem('mamusername') || '';
    password = Base64.decode(localStorage.getItem('mampassword') || '');
  } catch (err) {
    localStorage.removeItem('mamusername');
    localStorage.removeItem('mampassword');
  }
  const initialValues: ILoginParams = {
    username,
    password,
    remember: username && password ? true : false,
  };
  const inil = useIntl();
  useEffect(() => {
    // 初始化主题
    dispatch({
      type: 'themes/initTheme',
    });
  }, []);
  const onFinish = async (values: Store) => {
    setLoading(true);
    if (values.remember) {
      localStorage.setItem('mamusername', values.username);
      localStorage.setItem('mampassword', Base64.encode(values.password));
    } else {
      localStorage.removeItem('mamusername');
      localStorage.removeItem('mampassword');
    }
    await loginApis
      .login({
        loginName: values.username,
        password: values.password,
      })
      .then(res => {
        if (res && res.data) {
          history.push('/basic');
        }
      });
    setLoading(false);
  };
  return (
    <div className="login-container">
      <Loading />
      <img
        className="login-bg"
        src={require(`../../images/login/login_bg.png`)}
        alt="bg"
      />
      {/*<img src={require('../../images/login/logo.png')} alt="logo" className="login-logo" />*/}
      <div className="login-main">
        {/*<div className="login-top">*/}
        {/*  <div />*/}
        {/*</div>*/}
        <div className="login-bottom">
          <div className="logo-title-wrapper">
            <img
              src={logoUrl || require(`../../images/login/logo.png`)}
              alt="logo"
            />
            <h1>{title || '智慧教学资源管理系统'}</h1>
          </div>
          <Form
            className="login-form"
            initialValues={initialValues}
            onFinish={onFinish}
            size="large"
          >
            <Form.Item
              name="username"
              rules={[
                {
                  required: true,
                  message: inil.formatMessage({
                    id: 'please-enter-username',
                    defaultMessage: '请输入用户名',
                  }),
                },
              ]}
            >
              <Input
                className="login-border login-input"
                placeholder={inil.formatMessage({
                  id: 'username',
                  defaultMessage: '用户名',
                })}
              />
            </Form.Item>
            <Form.Item
              name="password"
              rules={[
                {
                  required: true,
                  message: inil.formatMessage({
                    id: 'please-enter-password',
                    defaultMessage: '请输入密码',
                  }),
                },
              ]}
            >
              <Input
                type="password"
                placeholder={inil.formatMessage({
                  id: 'password',
                  defaultMessage: '密码',
                })}
                className="login-border login-input"
              />
            </Form.Item>
            <Form.Item name="remember" valuePropName="checked">
              <Checkbox>
                {inil.formatMessage({
                  id: 'remember-password',
                  defaultMessage: '记住密码',
                })}
              </Checkbox>
            </Form.Item>
            <Form.Item>
              <Button
                loading={loading}
                className="login-border login-bottoms"
                htmlType="submit"
                block
              >
                {inil.formatMessage({ id: 'login', defaultMessage: '登录' })}
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
