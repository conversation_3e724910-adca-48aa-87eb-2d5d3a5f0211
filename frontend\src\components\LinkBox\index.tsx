import React, { FC, useState, useMemo, useEffect, useRef } from 'react';
import { Space, Button, message, Modal, Popover } from 'antd';
import {
  CopyFilled,
  SwapOutlined,
  UndoOutlined,
  DeleteOutlined,
  ArrowDownOutlined,
} from '@ant-design/icons';
import './style.less';
import {
  formatMessage,
  useIntl,
  useSelector,
  useDispatch,
  IPermission,
} from 'umi';
import searchTypes from '@/types/searchTypes';
import CopyAndMoveModal from '../CopyAndMoveModal';
import {
  IntelligentAnalysisModal,
  DeleteModal,
  IconFont,
  ShareModal,
  ProcessModal
} from '../index';
import './style.less';
import { debounce, operateCode, sleep } from '@/utils';
import perCfg from '@/permission/config';
import globalParams from '@/permission/globalParams';
import BindKnowledgeMap from '../bindKnowledgeMap';
import contentListApis from '@/service/contentListApis';
import progressApis from '@/service/progress';

interface LinkBoxProps {
  newFolder: () => void; // 新建文件夹
  refresh: (page?: number, key?: any, id?: any) => void; // 刷新
  LinkBoxList: searchTypes.ISearchData[]; //选中的数据
  downloadBoxOpen: () => void;
  fetchSharedNums?: () => void;
  setCheckedList?: (data: any) => void;
  recycleBin: boolean;
  myVideo: boolean;
  departmentVideo: boolean;
  myCollection: boolean;
  copyAddMove: boolean;
  myVideoFolder: boolean;
  datalength: number;
  pubOrPri: boolean;
  shareEnble?: boolean;
  vedioResource?: boolean;
  currentPath?: any;
  targetFolder: any
  setPublishVisible?: (visible: boolean) => void;
  getDetail?: (detailItem: any) => void;
  searchMyVideoDetail: () => void;
  getFolderObj: (msg: any) => void;
  setDownloadFolderVisible: (detailItem: any) => void;
  classifiedByschedule?: boolean;
}

const LinkBox: FC<LinkBoxProps> = (props: LinkBoxProps) => {
  const intl = useIntl();
  const {
    newFolder,
    refresh,
    LinkBoxList,
    setCheckedList,
    downloadBoxOpen,
    fetchSharedNums,
    myVideoFolder,
    recycleBin,
    myVideo,
    departmentVideo,
    myCollection,
    copyAddMove,
    datalength,
    pubOrPri,
    vedioResource,
    currentPath,
    targetFolder,
    shareEnble,
    searchMyVideoDetail,
    getFolderObj,
    setDownloadFolderVisible,
    setPublishVisible,
    getDetail,
    classifiedByschedule,
  } = props;
  
  const [analysisResource, setAnalysisResource] = useState<boolean>(false);
  const [copyShow, setCopyShow] = useState<boolean>(false);
  const [isAllFolder, setIsAllFolder] = useState<boolean>(false);
  const [editPermission, setEditPermission] = useState<boolean>(false);
  const [damaged, setDamaged] = useState<boolean>(false);
  const [deleteShow, setDeleteShow] = useState<boolean>(false);
  const [knowledgeShow, setKnowledgeShow] = useState<boolean>(false);
  const [userInfo, setUserInfo] = useState<any>(null);
  const [deleteModalVisible, setDeleteModalVisible] = useState<boolean>(false);
  const [shareVisible, setShareVisible] = useState<boolean>(false);
  const [copyAndMoveModallVisible, setCopyAndMoveModalVisible] = useState<
    boolean
  >(false);
  const [
    intelligentAnalysisModalVisible,
    setIntelligentAnalysisModalVisible,
  ] = useState<boolean>(false);
  const [processModalVisible, setProcessModalVisible] = useState<boolean>(false);
  const [bindKnowledgeModalVisible, setBindKnowledgeModalVisible] = useState<
    boolean
  >(false);

  const [copyOrMove, setCopyOrMove] = useState<number>(0);
  const [clickedBottom, setClickedBottom] = useState<string>('');
  const dispatch = useDispatch();
  const Secondarydirectory = useSelector<{ jurisdiction: any }, any>(
    ({ jurisdiction }) => {
      return jurisdiction.Secondarydirectory;
    },
  );
  const { permissions, rmanGlobalParameter } = useSelector<
    { permission: any },
    IPermission
  >(({ permission }) => permission);
  const { mobileFlag } = useSelector<{ config: any }, any>(
    ({ config }) => config,
  );
  const [collectionVisible, setCollectionVisible] = useState<boolean>(false);
  const [operatMenuVisible, setOpreatMenuVisible] = useState<boolean>(false);
  useEffect(() => {
    if ((window as any).login_useInfo) {
      // console.log((window as any).login_useInfo)
      setUserInfo((window as any).login_useInfo);
    }
  }, [(window as any).login_useInfo]);
  useEffect(() => {
    // 判断是否包含文件夹
    let i = LinkBoxList.some((item: any) => {
      return item.type_ === 'folder';
    });
    // 判断是否有编辑权限
    let e = LinkBoxList.every((item: any) => {
      return operateCode(item.operateCode_).includes(2);
    });
    // 判断是否包含损坏文件 有损坏文件不可下载
    let d = LinkBoxList.some((item: any) => item.damaged);
    // ['biz_sobey_video','biz_sobey_audio','biz_sobey_picture','biz_sobey_document']
    let k = LinkBoxList.every((item: any) => {
      return [
        'biz_sobey_video',
        'biz_sobey_audio',
        'biz_sobey_picture',
        'biz_sobey_document',
      ].includes(item.type_);
    });
    // 判断是否全为视频、文档、图片 供绑定知识点使用
    let l = LinkBoxList.every((item: any) => {
      return (
        item.type_ === 'biz_sobey_video' ||
        item.type_ === 'biz_sobey_picture' ||
        item.type_ === 'biz_sobey_audio' ||
        item.type_ === 'biz_sobey_document'
      );
    });
    // 判断是否全都有删除权限
    let j = LinkBoxList.every((item: any) => {
      return operateCode(item.operateCode_).includes(8);
    });
    // 判断是否全部都是文件夹
    let f = LinkBoxList.every((item: any) => {
      return item.type_ === 'folder'; 
    })
    setDeleteShow(j);
    setAnalysisResource(k);
    setKnowledgeShow(l);
    setCopyShow(i);
    setEditPermission(e);
    setDamaged(d);
    setIsAllFolder(f)
  }, [LinkBoxList]);

  const copyAndMove = (item: number) => {
    setCopyOrMove(item);
    setCopyAndMoveModalVisible(true);
  };
  const publish = () => {
    const params = LinkBoxList?.map((item:any) => {
      return  {
        "resourceId": item?.contentId_,
        "resourceName": item?.name_,
        "directory": item.tree_[0]        ,
        "createUserOrg": (window as any).login_useInfo.extend.orgcodepath,
        "resourceType": item?.type_,
        "fileSize": item?.filesize,
        "keyframe": item?.keyframe_
      }
    })
    Modal.confirm({
      title: `是否确认发布该资源？`,
      okText: '确定',
      cancelText: '取消',
      onOk() {
        contentListApis.publishResource(params).then(res => {
          if (res?.success) {
            message.success('发起成功');
            const randomInteger = Math.floor(Math.random() * 1000000) + 1;
            sessionStorage.setItem('publishResource', randomInteger.toString());
          }
          setCheckedList?.([])
        })
      }
    });
  }
  // 发布至专题
  const publishTopicResource = () => {
    setPublishVisible?.(true)
    const params = LinkBoxList?.map((item:any) => {
      return  {
        "resourceId": item?.contentId_,
        "resourceName": item?.name_,
        "directory": item.tree_[0]        ,
        "createUserOrg": (window as any).login_useInfo.extend.orgcodepath,
        "resourceType": item?.type_,
        "fileSize": item?.filesize,
        "keyframe": item?.keyframe_
      }
    })
    getDetail?.(params)
  }
  const opendownloadbox = () => {
    dispatch({
      type: 'download/changedownload',
      payload: {
        value: LinkBoxList,
      },
    });
    downloadBoxOpen();
  };

  // 下载文件夹
  const folderDownloadFun = async () => {
    const detail = LinkBoxList[0]
    const params = {
      folderPath: detail?.tree_[0] + '/' + detail?.name_,
      folderName: detail?.name_
    }
    progressApis.folderDownload(params).then((res: any) => {
      if (res?.success) {
        setDownloadFolderVisible(true)
        getFolderObj({taskId: res?.data, fileName: detail?.name_})
      }
    })
  }
  
  const emptyBottomClick = () => {
    setDeleteModalVisible(true);
    setClickedBottom('empty');
  };
  const deleteBottomClick = () => {
    setDeleteModalVisible(true);
    setClickedBottom('delete');
  };
  const otherBottomClick = () => {
    setDeleteModalVisible(true);
    setClickedBottom('reduction');
  };

  const permiseifshow = () => {
    if (
      rmanGlobalParameter.includes(globalParams.speech_analysis_display) ||
      rmanGlobalParameter.includes(globalParams.knowledge_analysis_display) ||
      rmanGlobalParameter.includes(
        globalParams.sensitiveface_analysis_display,
      ) ||
      rmanGlobalParameter.includes(globalParams.smart_tag_display)
    ) {
      //2022-2-24 统一了规则：如果模块配置有其一则显示智能按钮 但如果没有智能分析权限 则按钮置灰
      return true;
    }
    return false;
  };
  //添加我的收藏
  const addMyCollection = async () => {
    console.log(LinkBoxList);
    contentListApis
      .addmycollectionlists(LinkBoxList[0]?.entityData? LinkBoxList.map((item: any) => item.entityData.contentId_):  LinkBoxList.map((item: any) => item.contentId_))
      .then(async (res: any) => {
        // console.log(res)
        if (res.success) {
          message.success('收藏成功');
          // sleep(1000)//延时刷新 以便更新回传字段
          if(LinkBoxList[0]?.entityData){
            searchMyVideoDetail()
          }
          else{
            await refresh(
              undefined,
              LinkBoxList.map((item: any) => item.contentId_),
            );
          }
        } else {
          message.error('收藏失败！');
        }
      });
  };
  // 批量取消收藏
  const batchCancleCollect = async () => {
    // console.log(checkedList)
    contentListApis
      .deletemycollectionlist(LinkBoxList.map((item: any) => item.contentId_))
      .then((res: any) => {
        // console.log(res)
        if (res.success) {
          // refresh(undefined, '', checkedList.map((item: any) => item.contentId_))
          refresh(undefined, {
            name: '取消收藏',
            lists: LinkBoxList.map((item: any) => item.contentId_),
          });
          message.success(res.data);
        } else {
          message.error('取消收藏失败！');
        }
      });
  };
  // 按钮整理
  let buttonList: any = [];
  if (!recycleBin) {
    if (
      (!pubOrPri || permissions.includes(perCfg.resource_create)) &&
      !myVideo && !departmentVideo &&
      !myCollection
    ) {
      buttonList.push({
        title: intl.formatMessage({ id: '新建文件夹' }),
        func: () => newFolder(),
        disabled: Secondarydirectory || !currentPath&&currentPath[0].isCreateFolder,
        dom: <IconFont type="iconxinjianwenjianjia1" />,
      });
    }
    if (
      !pubOrPri || ( permissions.includes(perCfg.resource_download)  && !departmentVideo &&!myCollection) || (permissions.includes(perCfg.resource_download) && myVideo) ) {
      buttonList.push({
        title: intl.formatMessage({ id: '下载' }),
        func: isAllFolder ? folderDownloadFun : opendownloadbox,
        // isAllFolder 判断是否全部都是文件夹 copyShow为true代表包含文件夹  damaged代表是否有损坏文件
        // disabled: (isAllFolder ? !(isAllFolder && LinkBoxList.length === 1) : (LinkBoxList.length === 0 || copyShow || damaged)),  
        // func: () => opendownloadbox(),
        disabled: LinkBoxList.length === 0 || copyShow || damaged,
        dom: <ArrowDownOutlined className="icon" />,
      });
    }
    if (rmanGlobalParameter.includes(globalParams.my_collection_display)) {
      myCollection
        ? buttonList.push({
            title: intl.formatMessage({ id: '取消收藏' }),
            func: () => setCollectionVisible(true),
            disabled: LinkBoxList.length === 0,
            dom: <IconFont type="icona-shoucangbeifen2" className="icon" />,
          })
        : buttonList.push({
            title: intl.formatMessage({ id: '收藏' }),
            func: () => addMyCollection(),
            disabled: LinkBoxList.length === 0 || copyShow || damaged,
            dom: <IconFont type="iconxingzhuang2" />,
          });
    }
    if (
      rmanGlobalParameter.includes(globalParams.my_shared_display) &&
      !myCollection
    ) {
      buttonList.push({
        title: intl.formatMessage({ id: '分享' }),
        func: () => setShareVisible(true),
        disabled:
          LinkBoxList.length === 0 ||
          !(
            shareEnble ||
            LinkBoxList.every(
              (item: any) =>
                item.createUser_ === (window as any).login_useInfo?.userCode,
            )
          ) ||
          (vedioResource && copyShow) ||
          damaged,
        dom: <IconFont type="icona-xingzhuangbeifen2" />,
      });
    }
    if (
      permissions.includes(perCfg.resource_move) &&
      !myVideo && !departmentVideo &&
      !myCollection
    ) {
      buttonList.push({
        title: intl.formatMessage({ id: '移动' }),
        func: () => copyAndMove(1),
        disabled:
          LinkBoxList.length === 0 ||
          !deleteShow ||
          damaged ||
          currentPath[currentPath.length - 1]?.name === '群组资源' ||
          (currentPath[1]?.name === '群组资源' &&
            LinkBoxList.some((item: any) => item.operateCode_ < 15)),
        dom: <IconFont type="iconyidong" />,
      });
    }
    if (permiseifshow() && !myCollection) {
      buttonList.push({
        title: intl.formatMessage({ id: '智能分析' }),
        func: () => setIntelligentAnalysisModalVisible(true),
        disabled:
          myVideo ? LinkBoxList.length === 0 :
          (pubOrPri
            ? !permissions.includes(perCfg.resource_analysis)
            : false) ||
          LinkBoxList.length === 0 ||
          copyShow ||
          !editPermission ||
          !analysisResource ||
          damaged,
        dom: <IconFont type="iconzhinengfenxi" />,
      });
    }
    if ((rmanGlobalParameter.includes(globalParams.titles_trailer_display) || rmanGlobalParameter.includes(globalParams.watermark_display)) 
      && rmanGlobalParameter.includes(globalParams.batch_process_display) && (!myVideo || (myVideo && classifiedByschedule))) {
      buttonList.push({
        title: intl.formatMessage({ id: '批量加工' }),
        func: () => {
          if(mobileFlag) {
            message.info(intl.formatMessage({ id: '暂不支持手机端，请前往电脑端操作' }));
            return
          }
          setProcessModalVisible(true)},
        disabled:
        //   (pubOrPri
        //     ? !permissions.includes(perCfg.resource_analysis)
        //     : false) ||
        myVideo ? LinkBoxList.length === 0 :
          LinkBoxList.length === 0||
          LinkBoxList.some((item: any) => item?.type_ !== 'biz_sobey_video' )||
          copyShow ||
        //   !editPermission ||
        //   !analysisResource ||
          damaged,
        dom: <IconFont type="iconshezhi1" />,
      });
    }
    if (
      rmanGlobalParameter.includes(globalParams.knowledge_map_display) &&
      !mobileFlag && (!myVideo || (myVideo && classifiedByschedule)) &&
      window.localStorage.getItem('upform_platform') !== 'Lark'
    ) {
      buttonList.push({
        title: intl.formatMessage({ id: '知识点绑定' }),
        func: () => setBindKnowledgeModalVisible(true),
        disabled:
        myVideo ? LinkBoxList.length === 0 :
          LinkBoxList.length === 0 ||
          copyShow ||
          !editPermission ||
          !knowledgeShow ||
          damaged,
        dom: <IconFont type="iconditu" />,
      });
    }
    //仅在个人资源中才能共享
    if (
      permissions.includes(perCfg.publish_resource) &&
      window.localStorage.getItem('upform_platform') !== 'Lark' && (!myVideo || (myVideo && classifiedByschedule))
    ) {
      buttonList.push({
        title: intl.formatMessage({ id: '发布' }),
        func: () => {publish()},
        disabled: LinkBoxList.length === 0 ||  copyShow || damaged,
        dom: <IconFont type="iconparticipation" />,
      });
    }
    if (permissions.includes(perCfg.publish_topic_resource) && window.localStorage.getItem('upform_platform') !== 'Lark'){
        buttonList.push({
          title: intl.formatMessage({ id: '发布至专题' }),
          func: publishTopicResource,
          disabled: LinkBoxList.length === 0 ||  copyShow || damaged,
          dom: <IconFont type="iconrelease" />,
        });
    }
    if (
      (!pubOrPri || permissions.includes(perCfg.resource_delete)) &&
      (!myVideo || !classifiedByschedule) && !departmentVideo &&
      !myCollection
    ) {
      buttonList.push({
        title: intl.formatMessage({ id: '删除' }),
        func: () => setDeleteModalVisible(true),
        disabled: LinkBoxList.length === 0 || !deleteShow,
        dom: <IconFont type="icondelete" />,
      });
    }
    buttonList.push({
      title: intl.formatMessage({ id: '刷新' }),
      func: () => refresh(),
      dom: <IconFont type="iconshuaxin" />,
    });
  } else {
    buttonList.push({
      title: intl.formatMessage({ id: '刷新' }),
      func: () => refresh(),
      dom: <IconFont type="iconshuaxin" />,
    });
    if (permissions.includes(perCfg.recycle_restore)) {
      buttonList.push({
        title: intl.formatMessage({ id: '还原' }),
        func: () => otherBottomClick(),
        disabled: LinkBoxList.length == 0,
        dom: <IconFont type="iconhuifushanchu" />,
      });
    }
    if (permissions.includes(perCfg.recycle_delete)) {
      buttonList.push({
        title: intl.formatMessage({ id: '彻底删除' }),
        func: () => deleteBottomClick(),
        disabled: LinkBoxList.length == 0,
        dom: <IconFont type="iconchedishanchu" />,
      });
      buttonList.push({
        title: intl.formatMessage({ id: '清空' }),
        func: () => emptyBottomClick(),
        disabled: !datalength,
        dom: <IconFont type="iconqingkong" />,
      });
    }
  }
  return (
    <div className={`links_box`}>
      {mobileFlag ? (
          <>
          {
            myCollection && buttonList.map((item: any, index: number) => {
              if (index == 0 || index == buttonList.length - 1) {
                return (
                  <Button
                    key={index}
                    className={item.disabled ? 'disabled' : ''}
                    onClick={() => {
                      if (!item.disabled) {
                        setOpreatMenuVisible(false);
                        item.func();
                      }
                    }}
                  >
                    {item.dom}
                    {item.title}
                  </Button>
                );
              }
            })
          }
          
            {buttonList.length > 0 && !myCollection && (
              <Popover
                className="mobile_btns_popover"
                onOpenChange={(newOpen: boolean) =>
                  setOpreatMenuVisible(newOpen)
                }
                open={operatMenuVisible}
                content={
                  <div className="mobile_btns">
                    {buttonList
                      .map((item: any, index: number) => {
                        return (
                          <div
                            key={index}
                            className={item.disabled ? 'disabled' : ''}
                            onClick={() => {
                              if (!item.disabled) {
                                setOpreatMenuVisible(false);
                                item.func();
                              }
                            }}
                          >
                            {item.dom}
                            <span>{item.title}</span>
                          </div>
                        );
                      })}
                  </div>
                }
              >
                <Button
                  onClick={(e: any) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setOpreatMenuVisible(!operatMenuVisible);
                  }}
                >
                  <IconFont type="iconziyuanku1" />{intl.formatMessage({ id: '管理' })}
                </Button>
              </Popover>
            )}
          </>
        ): (
        buttonList.map((item: any, index: number) => {
          return (
            <div
              key={index}
              className={item.disabled ? 'disabled' : ''}
              onClick={!item.disabled && item.func}
            >
              {item.dom}
              <span>{item.title}</span>
            </div>
          );
        })
      )}
      {deleteModalVisible && <DeleteModal
        modalVisible={deleteModalVisible}
        modalClose={() => setDeleteModalVisible(false)}
        refresh={refresh}
        deletelist={LinkBoxList}
        recycleBin={recycleBin}
        clickedBottom={clickedBottom}
      />}
      <CopyAndMoveModal
        modalVisible={copyAndMoveModallVisible}
        modalClose={() => setCopyAndMoveModalVisible(false)}
        refresh={refresh}
        copyAndMovelist={LinkBoxList}
        operatType={copyOrMove}
        pubOrPri={pubOrPri}
        // currentPath={currentPath}
      />
      <IntelligentAnalysisModal
        modalVisible={intelligentAnalysisModalVisible}
        modalClose={() => setIntelligentAnalysisModalVisible(false)}
        analysislist={LinkBoxList}
        voiceContent={LinkBoxList[0]?.asr_status !== '2'}
        pubOrPri={pubOrPri}
      />
      <BindKnowledgeMap
        modalVisible={bindKnowledgeModalVisible}
        modalClose={() => setBindKnowledgeModalVisible(false)}
        analysislist={LinkBoxList}
      />
      {shareVisible && <ShareModal
        modalVisible={shareVisible}
        modalClose={() => {
          setShareVisible(false);
          // refresh();
        }}
        sharelist={LinkBoxList}
        fetchSharedNums={fetchSharedNums}
      />}
      <ProcessModal
        modalVisible={processModalVisible}
        setProcessModalVisible={setProcessModalVisible}
        processlist={LinkBoxList}
        refresh={refresh}
        targetFolder={targetFolder}
      />
      <Modal
        destroyOnClose={true}
        title={intl.formatMessage({ id: '取消收藏' })}
        visible={collectionVisible}
        footer={[
          <Button key="back" onClick={() => setCollectionVisible(false)}>
            {intl.formatMessage({ id: '取消' })}
          </Button>,
          <Button key="submit" type="primary" onClick={batchCancleCollect}>
            {intl.formatMessage({ id: '确定' })}
          </Button>,
        ]}
      >
        <span>{intl.formatMessage({ id: '确定要取消所选收藏吗' })}</span>
      </Modal>
    </div>
  );
};

export default LinkBox;
