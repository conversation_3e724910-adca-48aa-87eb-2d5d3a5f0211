import React, {
  FC,
  useEffect,
  useRef,
  useState,
  KeyboardEvent,
  FocusEvent,
  CSSProperties,
  useCallback,
} from 'react';
import {
  H5Player,
  Player,
  DownloadModal,
  IconFont,
  IntelligentAnalysisModal,
  ShareModal,
} from '@/components';
import {
  Tabs,
  Tag,
  Timeline,
  Empty,
  Form,
  Modal,
  Input,
  Button,
  message,
  Checkbox,
  PageHeader,
  Tooltip,
  Select,
  Spin,
  Popover,
  Drawer,
  Radio,
  Slider,
  Menu,
  Dropdown,
  Pagination,
  Popconfirm,
  MenuProps,
  TreeSelect,
  Collapse
} from 'antd';
// import { useInfiniteScroll } from 'ahooks';
import {
  UpOutlined, ArrowDownOutlined, ExclamationCircleOutlined, CheckCircleOutlined, FileTextOutlined, CloseCircleOutlined,
  DownOutlined, RetweetOutlined, CloseOutlined, LeftOutlined, RightOutlined, ScissorOutlined, DoubleRightOutlined, CheckOutlined
} from '@ant-design/icons';
import { useParams, useDispatch, useSelector, useHistory, useIntl } from 'umi';
import _ from 'lodash';
import SmartTypes from '../../../types/smartTypes';
const { confirm } = Modal;
import './index.less';
import entityApis from '@/service/entityApis';
import SmartService from '@/service/smartService';
import { ExifConfig, IEntity, IExifInfo, IFormItem } from '@/types/entityTypes';
import BasicMetadata from '@/components/basicMetadata/basicMetadata';
import {
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { copyObject, debounce, getType, getVideoTime, HexToRgb, operateCode, searchKeywords, changesize, sleep, timeTransfer, updateScrollPosition } from '@/utils';
import Entity from '@/components/entity/entity';
import Error from '@/components/entity/error/error';
import PointsItem from './components/pointsItem';
import ProductItem from './components/productItem';
import Auditrousece from './components/Auditrousece';
import TimelineItem from './components/TimelineItem';
import VoiceInput from './components/voiceInput';
import VoiceText from './components/VoiceText';
import PreviewModal from './components/previewModal'
import Contentlogs from './components/Contentlogs';
import AssociatedResource from './components/AssociatedResource';
import Auditlogs from './components/Auditlogs';
import { IPermission } from '@/models/permission';
import perCfg from '@/permission/config';
import MergeSettingModal from '@/components/mergeSettingModal/mergeSettingModal';
import globalParams, { ModuleCfg } from '@/permission/globalParams';
import { check } from 'prettier';
import BindKnowledgeMap from '@/components/bindKnowledgeMap';
import contentListApis from '@/service/contentListApis';
import timecodeconvert from '@/components/time-code/timeCode';
import RelationVideo from './components/relationVideo';
import QuestionList from './components/QuestionList'
import QualityCheck from './components/QualityCheck'
import EchartsGroup from '@/components/EchartsGroup';
import TextclipModal from '@/components/textclipModal';
import CommentModal from '@/components/commentModal';
import CopyAndMoveModal from '@/components/CopyAndMoveModal';
import TopicSelectModal from "@/components/TopicSelectModal";
import BasicInput from '@/components/basicMetadata/basicInput';
import BasicNumber from '@/components/basicMetadata/basicNumber';
import TeacherItem from '@/components/formItemBox/teacherItem';
import BasicDate from '@/components/basicMetadata/basicDate';
import BasicInputTextArea from '@/components/basicMetadata/basicInputTextArea';
import BasicEnum from '@/components/basicMetadata/basicEnum';
import BasicTree from '@/components/basicMetadata/basicTree';
import BasicTag from '@/components/basicMetadata/basicTag';
import TopicTypeSelectModal from "@/components/TopicTypeSelectModal";
import { getSmartExplain } from '@/service/chatgpt';
import { optionType_ } from "@/utils";
import moment, { Moment } from 'moment';
import { updateHomework, createHomework, getHomeworkDetail, addHomeworkTopic, updateTopic, sortTopic, updateScore, deleteTopic, batchDeleteTopic } from "@/service/homework";
import { getSensitiveWord } from '@/utils';
import rmanApis from '@/service/rman';

const { TabPane } = Tabs;
const { Option } = Select;
let sectionData: SmartTypes.SequenceMeta = {}

const EntityDetail: FC = () => {
  let history: any = useHistory();
  const joveonetype: any = ['video', 'audio', 'picture']
  // let ispublic = false;
  let hidecatalogue = history.location?.query?.hidecatalogue || '0';
  let target = history.location?.query?.target || ''
  // let dirTree = '';
  let isaudit = history.location?.query?.isaudit || false;
  let showBtn = history.location?.query?.showBtn || false;
  let instanceId = history.location?.query?.instanceId || '';
  let showArrow = history.location?.query?.showArrow || false;
  const intl = useIntl();
  const [ispublic, setIspublic] = useState<boolean>(false);
  const [dirTree, setDirTree] = useState<string>('');
  const win = window as any;
  const player = useRef<Player>();
  const tabListRef = useRef<any>(null);
  const dispatch = useDispatch();
  const [pointsForm] = Form.useForm();
  const [versionForm] = Form.useForm();
  const params = useParams<{ contentId: string }>();
  const [entity, setEntity] = useState<IEntity>();
  const [metadata, setMetadata] = useState<IFormItem[]>([]);
  const [title, setTitle] = useState<string>('');
  const [frameRate, setFrameRate] = useState<number>(25);
  const [duration, setDuration] = useState<number>(25);
  const [type, setType] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [isVision, setIsVision] = useState(false);
  const [voiceCheck, setVoiceCheck] = useState<any>(false);
  const [btnStyle, setBtnStyle] = useState<CSSProperties>({});
  const [startSpan, setStartSpan] = React.useState(0);
  const [endSpan, setEndSpan] = React.useState(0);
  const [fragmentNum, setFragmentNum] = React.useState(0);
  const [commentTotal, setCommentTotal] = React.useState(0)
  const [isComposite, setIsComposite] = React.useState(false);
  const [isTransCode, setIsTransCode] = useState<boolean>(true);
  const [contentDetail, setContentDetail] = useState<any>({});
  const [knowKey, setKnowKey] = useState<string>('')
  const [currentPage, setCurrentPage] = useState<number>(0);
  const [errorCode, setErrorCode] = useState<number>(0);
  const [fontSize, setFontSize] = useState<number>(14)
  const contentId_ = params.contentId.split('_')[0]; //兼容链接分享 避免登录验证
  const shareFlag_ = params.contentId.split('_')[1];
  const shareLink = params.contentId.split('_')[2]; //通过分享点过来的链接
  const doPop = (x: number, y: number) => {
    setBtnStyle({
      top: x - 26 - document.querySelector('.paragraph-content').scrollTop,
      left: '46%',
      display: 'block',
    });
  };
  const { modules, permissions, rmanGlobalParameter, rmanGlobalText } = useSelector<
    { permission: any },
    IPermission
  >(({ permission }) => permission);
  const handleOk = (e: any) => {
    e.stopPropagation();
    handleSection(
      '',
      '',
      voice[startSpan]._in / 10000000,
      voice[endSpan]._out / 10000000,
    );
  };

  const key = 'updatable';
  const openMessage = () => {
    message.success({ content: `${intl.formatMessage({ id: '合成成品片段成功' })}`, key });

    setTimeout(() => {
      setFragmentNum(checkedList.length);
    }, 10000);
  };

  // 片段
  const [sequencemeta, setSequencemeta] = useState<SmartTypes.SequenceMeta[]>(
    [],
  );
  const [keyframeFlag, setKeyFrameFlag] = useState<boolean>(false);
  const preSelect = useRef<any>({
    index: -1,
    name: ''
  })
  let timer: any;
  const [subtitles, setSubtitles] = useState<SmartTypes.Lyrics[]>([]);
  const [voice, setVoice] = useState<SmartTypes.Lyrics[]>([]);
  const [searchVoiceWord, setSearchVoiceWord] = useState<string>('');
  const [activeTabs, setActiveTabs] = useState<string>('1');
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const formRef = useRef<any>(null);
  const [section, setSection] = useState<SmartTypes.SequenceMeta>();
  const [currentFrame, setCurrentFrame] = useState<number>(0);
  const [currentPlayTime, setCurrentPlayTime] = useState<number>(0);
  const [editVisible, setEditVisible] = useState<boolean>(false);
  const [repairVisible, setRepairVisible] = useState<boolean>(false);
  const [downloadModalVisible, setDownloadModalVisible] = useState<boolean>(
    false,
  );
  const [
    intelligentAnalysisModalVisible,
    setIntelligentAnalysisModalVisible,
  ] = useState<boolean>(false);
  const [searchIndex, setSearchIndex] = useState<number[]>([]);
  const [searchCurrent, setSearchCurrent] = useState<number>(0);
  const metadataRef = useRef<any>();
  const [cur, setCur] = useState<any>(-1)
  //全选start
  const [checkedList, setCheckedList] = useState<any[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<any[]>([]);
  const [indeterminate, setIndeterminate] = useState(false);
  const [checkAll, setCheckAll] = useState(false);

  const [product, setProduct] = useState<any[]>([]);
  const [courseware, setCourseware] = useState<any[]>([]);//课件
  const [coursewarePage, setCoursewarePage] = useState<number>(1);//
  const [coursewarePageSize, setCoursewarePageSize] = useState<number>(10);//
  const [coursewareTotal, setCoursewareTotal] = useState<number>(0);//
  const [tabName, setTabName] = useState<sring>('')
  const [intelligentSummary, setIntelligentSummary] = useState<any>(null);//智能小结
  const [intelligentSummaryEdit, setIntelligentSummaryEdit] = useState<boolean>(false);
  const [intelligentSummaryText, setIntelligentSummaryText] = useState<any>(null);//编辑暂存
  const [intelligentSummaryLoading, setIntelligentSummaryLoading] = useState<boolean>(false);

  const [currentCourseware, setCurrentCourseware] = useState<any>('');//当前选中课件
  const [currentLanguage, setCurrentLanguage] = useState<any>('cn');//当前选中语言
  const [translateFlag, setTranslateFlag] = useState<boolean>(false);//有无翻译标识
  const [qvModalVisible, setQvModalVisible] = useState<boolean>(false)
  const [settingModalVisible, setsmVisible] = useState(false);
  const [showAddTopic, setShowAddTopic] = useState<boolean>(false);

  const [videoKeyframe, setVideoKeyframe] = useState('');

  const [bindKnowledgeModalVisible, setBindKnowledgeModalVisible] = useState<
    boolean
  >(false);
  const [commentModalVisible, setcommentModalVisible] = useState<
    boolean
  >(false);
  const [deletePointsVisible, setDeletePointsVisible] = useState<
    boolean
  >(false);
  const [addPointsVisible, setAddPointsVisible] = useState<boolean>(false);
  const [brightness, setBrightness] = useState<number>(50)
  const [contrast, setContrast] = useState<number>(50)
  const [saturate, setSaturate] = useState<number>(50)
  const [blur, setBlur] = useState<number>(0)
  const [grayscale , setGrayscale] = useState<number>(0)
  const [invert, setInvert] = useState<number>(0)
  const [opacity , setOpacity] = useState<number>(100)
  const [sepia , setSepia] = useState<number>(0)
  const [hueRotate, setHueRotate] = useState<number>(0)

  //语音、知识点状态
  const [voiceStatus, setVoiceStatus] = useState<string>('');
  const [knowledgeStatus, setKnowledgeStatus] = useState<string>('');
  const onChange = (list: any) => {
    setIndeterminate(list.length != 0 && list.length !== sequencemeta.length);
    setCheckAll(list.length == sequencemeta.length);
    setCheckedList(
      sequencemeta.filter((item: any) => list.includes(item.guid_)),
    );
    setCheckedKeys(list);
  };
  const [modal, contextHolder] = Modal.useModal();
  //有无字幕
  const [track, setTrack] = useState<boolean>(false);
  const [initFlag, setInitFlag] = useState<boolean>(false);

  const [playFlag, setPlayFlag] = useState<boolean>(false);
  // exif
  const [formatExif, setFormatExif] = useState<
    {
      alias: string;
      value: any;
      fieldName: string;
    }[]
  >([]);
  const [canEdit, setCanEdit] = useState<boolean>(false)
  const currentTime = useRef<any>(undefined);
  const currentTop = useRef<any>(0);
  const dragObj = useRef<any>({
    container: '',
    resizeAble: false,
    clientX: 0,
    clientY: 0,
    minW: 318,
    minH: 8,
    direc: ''
  });
  const [substitles, setSubstitles] = useState<any>([])
  const [languageList, setLanguageList] = useState<SmartTypes.Languages[]>([]);
  const [shareVisible, setShareVisible] = useState<boolean>(false);
  const [changeFlag, setChangeFlag] = useState<boolean>(false);
  const [keywords, setKeywords] = useState<any>([]);
  const [sensitiveWords, setSensitiveWords] = useState<any>([]);
  const [sensitivePersons, setSensitivePersons] = useState<any>([]);
  const [sensitiveImages, setSensitiveImages] = useState<any>([]);
  const [textclipLists, setsetTextclipLists] = useState<any>([]);
  const [sequencemetaTransEdit, setSequencemetaTransEdit] = useState<boolean>(true);
  const sequencemetaTransName = useRef<any>();
  const [sequencemetaEdit, setSequencemetaEdit] = useState<boolean>(true);
  const [editIndex, setEditIndex] = useState<number>(0);
  const [textclipVisible, setTextclipVisible] = useState<boolean>(false);
  const [auditlogsVisible, setAuditlogsVisible] = useState<boolean>(false);
  //字幕样式
  const [fontStyle, setFontStyle] = useState<any>({
    left: 0.05, //字幕左边距
    top: 0.9,  // 字幕上边距
  })
  //相关视频显示
  const [relationVideoVisible, setRelationVideoVisible] = useState<boolean>(false);
  const [operatType, setOperatType] = useState<number>(0);
  const [copyAndMoveModallVisible, setCopyAndMoveModalVisible] = useState<boolean>(false);
  const [dragCurrent, setdragCurrent] = useState<any>(0);
  const [topicVisible, setTopicVisible] = useState<boolean>(false);
  const [questionVersion, setQuestionVersion] = useState<any>([])
  const [questionRecordTotal, setQuestionRecordTotal] = useState<number>(0)
  const [dataSource, setDataSource] = useState([]);
  const [topicTypeSelectVisible, setTopicTypeSelectVisible] = useState<boolean>(false);
  const [topicType, setTopicType] = useState<number>(0);
  const [versionName, setVersionName] = useState<string>('')
  const [versionCode, setVersionCode] = useState<string>('')
  const [selectType, setSelectType] = useState<"checkbox" | "radio">("checkbox");
  const [questionList, setQuesionList] = useState<any>([])
  const [current, setCurrent] = useState<number>(1); //当前页码
  const [pageSize, setPageSize] = useState<number>(30); //每页条数
  const [totalPage, setTotalPage] = useState<number>(0)
  const [page, setPage] = useState<number>(1)
  const [versionPageSize, setVersionPageSize] = useState<number>(10)
  const [previewModalVisible, setPreviewModalVisible] = useState<boolean>(false)
  const [questionPoint, setQuestionPoint] = useState<any>([])
  const [viewTopicModalVisible, setviewTopicModalVisible] = useState<boolean>(false); //每页条数
  const [operation, setOperation] = useState<string>('ADD')
  const questionRef = useRef<any>(null)
  const homeworkId = useRef<string>("");
  const firstTimeRender = useRef(true);
  const pageType = useRef<'resource' | 'template'>(location.pathname.includes("tempatedetail") ? 'template' : 'resource');
  const [questionItem, setQuestionItem] = useState<any>({})
  const [questionPointList, setQuestionPointList] = useState<any>([])
  const idx = useRef<number>(0);
  const [reviewVideoInfo, setReviewVideoInfo] = useState<any>([])
  const originReviewVideoInfo = useRef<any>({})
  const [repairForm] = Form.useForm();
  const [form0] = Form.useForm();
  const [form1] = Form.useForm();
  const [form2] = Form.useForm();
  const [form3] = Form.useForm();
  const [knowledgePoint, setKnowledgePoint] = useState<any>([])
  const [qualityCheckList, setQualityCheckList] = useState<any>([])
  const [treeData, setTreeData] = useState<any>([]);
  const [showplaceholder, setShowplaceholder] = useState<string>('');
  const [gifModalVisible, setGifModalVisible] = useState<boolean>(false);
  const [selectedKey, setSelectedKey] = useState<any>('');
  const [contentids, setContentids] = useState<any>([])
  const [contentidNum, setContentidNum] = useState<number>(0)
  const [associatedResource, setAssociatedResource] = useState<any>([])
  const [associatedResourceTotal, setAssociatedResourceTotal] = useState<any>([])
  const { mobileFlag } = useSelector<{ config: any }, any>(
    ({ config }) => config
  );
  const [otherFormData, setOtherFormData] = useState<any>({})
  const [permission, setPermission] = useState<boolean>(false);
  const [intelligentObject, setIntelligentObject] = useState<any>([])
  const [intelligentPeople, setIntelligentPeople] = useState<any>([])
  const [intelligentFace, setIntelligentFace] = useState<any>([])
  const [controlBox, setControlBox] = useState<boolean>(false);

  const setFilter= () => {
    setSaturate(50)
    setContrast(50)
    setBrightness(50)
    setOpacity(100)
    setBlur(0)
    setGrayscale(0)
    setSepia(0)
    setHueRotate(0)
    setInvert(0)
  }

  const fetchTree = () => {
    rmanApis.gettreebylevel(2).then((res: any) => {
      if (res && res.data && res.success) {
        let data = res.data;
        let newData: any = [];
        data.map((item: any) => {
          if (item.name === '公共资源') {
            item.children?.forEach((item: any) => {
              newData.push({ ...item, layer: 1 });
            });
          } else {
            newData.push(item);
          }
        });
        newData = newData.filter(Boolean); //过滤空对象
        newData = newData.filter((item: any) => item.name != '录播资源');
        const rootData = newData.map((item: any) => {
          return {
            key: item.path,
            value: item.path,
            title: item.name,
            id: item.id,
            disabled: (item.name === '群组资源') ? true : false,
          };
        });
        setTreeData(rootData);
      }
    });
  };
  useEffect(() => {
    rmanGlobalParameter.length > 0 && getMetaData();
  }, [rmanGlobalParameter])
  useEffect(() => {
    // dragInit()
    entityApis.getIntelligentObject({ contentId: contentId_ }).then(res => {
      if (res?.success) {
        setIntelligentObject(res.data)
      }
    })
    entityApis.getIntelligentPeople({ contentId: contentId_ }).then(res => {
      if (res?.success) {
        setIntelligentPeople(res.data)
      }
    })
    entityApis.getIntelligentFace({ contentId: contentId_ }).then(res => {
      if (res?.success) {
        let arr = res.data?.map(item => ({ ...item, sensitiveWordGroups: item.segment }))
        setIntelligentFace(arr)
      }
    })
    qualityCheckMeta()
    //刷新界面时
    setTimeout(() => {
      setPlayerStart()
    }, 3000)
    window.onbeforeunload = (e: any) => {
      setPlayerStart();
    }
    window.addEventListener("message", handleMessage);
    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, []);
  useEffect(() => {
    if (gifModalVisible) {
      fetchTree();
    }
  }, [gifModalVisible]);
  const onLoadChild = (node: any, isRoot: boolean = false) => {
    const { key, children, code, title } = node;
    return new Promise(async (resolve) => {
      if (key === 'add') {
        resolve(null);
        return;
      }
      if (children) {
        resolve(null);
        return;
      }
      function updateTreeData(list: any, key: React.Key, children: any): any {
        return list.map((node: any) => {
          if (node.key === key) {
            return {
              ...node,
              children,
            };
          } else if (node.children) {
            return {
              ...node,
              children: updateTreeData(node.children, key, children),
            };
          }
          return node;
        });
      }
      const res: any = await rmanApis.loadChild(key, false);
      if (res && res.data && res.success) {
        let treeList = res.data;
        treeList = treeList.filter((item: any) => {
          return item.name !== '录播资源';
        });
        setTreeData((origin: any) =>
          updateTreeData(
            origin,
            key,
            treeList.map((item: any) => {
              return {
                key: item.path,
                title: item.name,
                id: item.contentId,
                value: item.path,
              };
            }),
          ),
        );
        resolve(null);
      }
    });
  };
  const [counter, setCounter] = useState<any>({
    like: 0,//点赞
    collect: 0,//收藏
    share: 0,//分享
    download: 0,//下载
    copy: 0,//复制
    micro: 0,//发微课
    knowledge: 0,//知识点绑定
    comment: 0,
  })
  const handleScroll = (e: any) => {
    const { target } = e;
    const total = target.scrollTop + target.offsetHeight;
    const scrollHeight = target.scrollHeight;
    if (
      total >= scrollHeight - 1 &&
      total < scrollHeight + 1 &&
      page < Math.ceil(totalPage / versionPageSize) //向上取整
    ) {
      setPage(page + 1)
    }
  };

  //获取鼠标所在div位置
  const getDirection = (e: any) => {
    let xP = e.offsetX, yP = e.offsetY, offset = 10, dir = '';
    if (yP < offset) {
      dir = 'n';
    } else if (yP > dragObj.current.container.offsetHeight - offset) {
      dir = 's';
    };
    if (xP < offset) {
      dir = 'w';
    } else if (xP > dragObj.current.container.offsetHeight - offset) {
      dir = 'e';
    }
    return dir;
  }
  const move = (e: any) => {
    let d = getDirection(e);
    let cursor;
    //暂且只开放左侧拖动；
    if (d !== 'w') cursor = 'default';
    else cursor = d + '-resize';
    // 修改鼠标显示效果
    dragObj.current.container.style.cursor = cursor;
    // 当开启尺寸修改时，鼠标移动会修改div尺寸
    if (dragObj.current.resizeAble) {
      // 暂时只开放左边
      // 鼠标按下的位置在左边，修改宽度
      if (dragObj.current.direc.indexOf('w') !== -1) {
        dragObj.current.container.style.width = Math.max(dragObj.current.minW, dragObj.current.container.offsetWidth + (dragObj.current.clientX - e.clientX)) + 'px';
        dragObj.current.clientX = e.clientX;
        setdragCurrent(Math.max(dragObj.current.minW, dragObj.current.container.offsetWidth + (dragObj.current.clientX - e.clientX)));
      }
    }
  }
  const onmouseup = () => {
    dragObj.current.resizeAble = false;
  }
  const onmousedown = (e: any) => {
    const d = getDirection(e);
    // 当位置为四个边和四个角时才开启尺寸修改
    if (d !== '') {
      dragObj.current = {
        ...dragObj.current,
        direc: d,
        clientX: e.clientX,
        clientY: e.clientY,
        resizeAble: true
      }
    }
  }
  //实现边框拖拽缩放
  const dragInit = () => {
    const rightDiv: any = document.getElementsByClassName('entity_info_wrapper')[0];
    dragObj.current.container = rightDiv;
    document.getElementById('root')?.addEventListener('mousemove', move);
    rightDiv.addEventListener('mousedown', onmousedown);
    document.getElementById('root')?.addEventListener('mouseup', onmouseup);
  }
  useEffect(() => {
    if (dirTree) {
      setShowplaceholder(getDirTeeStr1(dirTree));
      setSelectedKey(dirTree);
    }
  }, [dirTree]);
  const onSelect = (key: any) => {
    setShowplaceholder(getDirTeeStr1(key));
    setSelectedKey(key)
  };
  useEffect(() => {
    setIndeterminate(
      checkedKeys.length != 0 && checkedKeys.length !== sequencemeta.length,
    );
    setCheckAll(checkedKeys.length == sequencemeta.length);
  }, [sequencemeta]);
  useEffect(() => {
    if (metadata.length && repairVisible) {
      const obj: { [key: string]: string } = {
        campus: `${intl.formatMessage({ id: '校区' })}`,
        academic_building: `${intl.formatMessage({ id: '教学楼' })}`,
        classroom_number: `${intl.formatMessage({ id: '教室编码' })}`,
        course_number: `${intl.formatMessage({ id: '课程号' })}`,
        serial_number: `${intl.formatMessage({ id: '课序号' })}`,
        course_name: `${intl.formatMessage({ id: '课程名' })}`,
        course_week: `${intl.formatMessage({ id: '周次' })}`,
        course_begin: `${intl.formatMessage({ id: '开始时间' })}`,
        course_end: `${intl.formatMessage({ id: '结束时间' })}`,
        schedule_id: `${intl.formatMessage({ id: '课程多画面标识' })}`,
        seat: `${intl.formatMessage({ id: '画面名称' })}`,
        semester: `${intl.formatMessage({ id: '学期' })}`,
        section: `${intl.formatMessage({ id: '节次' })}`,
        college: `${intl.formatMessage({ id: '学院' })}`,
        major: `${intl.formatMessage({ id: '专业' })}`,
        teacher: `${intl.formatMessage({ id: '教师' })}`
      }
      const major = metadata.find(item => item.fieldName === 'major') || {}
      const college = metadata.find(item => item.fieldName === 'college') || {}
      console.log(metadata)
      contentListApis.getRepairInfo({
        contentId: contentId_
      }).then((res: any) => {
        originReviewVideoInfo.current = res?.data
        const result = res?.data && Object.keys(res?.data).map((item: any) => {
          const controlTypeObj: { [key: string]: number } = { major: 2, college: 3, teacher: 3 }
          return {
            value: ['teacher', 'major', 'college'].includes(item) ? res.data[item].map((item: { code: any; }) => item.code) : res.data[item],
            alias: obj[item],
            fieldName: item,
            isReadOnly: typeof res.data[item] === 'object' ? res.data[item].length ? true : false : !!res.data[item],
            controlType: typeof res.data[item] === 'object' ? controlTypeObj[item] : 1,
            controlData: ['major', 'college'].includes(item) ? { major, college }[item].controlData || [] : ''
          }
        })
        setReviewVideoInfo(result)
      })
    }
  }, [metadata, repairVisible])
  const repairInfo = () => {
    repairForm
      .validateFields()
      .then(res => {
        let str = ''
        for (const key in res) {
          // getSensitiveWord(res[key], obj[key], () => {
          if (['college', 'teacher'].includes(key)) {
            let val = metadata.find(item => item.fieldName === key)
            if (val?.controlData && res[key]) {
              originReviewVideoInfo.current[key] = [res[key]].map((item: any) => {
                return {
                  code: item,
                  name: JSON.parse(val.controlData)[item]
                }
              })
            }
          }
          if (['course_end', 'course_begin'].includes(key)) {
            console.log(res[key], 'fsaf ')
            if (!originReviewVideoInfo.current[key]) {
              message.warning(`${intl.formatMessage({ id: '请填写时间' })}`)
              return
            }
            originReviewVideoInfo.current[key] = moment(res[key]).format('YYYY-MM-DD HH:mm:ss')
          }
          else if (key === 'major' && res[key]) {
            let name = ''
            let majorval = metadata.find((item: any) => item.fieldName === 'major')
            originReviewVideoInfo.current[key] = res[key].map((item: any) => {
              if (majorval?.controlData) {
                JSON.parse(majorval.controlData).forEach((children: { children: any[]; }) => {
                  children.children.length && children.children.forEach(_item => {
                    if (_item.categoryCode === item) {
                      name = _item.categoryName
                    }
                  })
                })
                return {
                  code: item,
                  name
                }
              }
            })
          }
          else {
            originReviewVideoInfo.current[key] = res[key] ? res[key] : originReviewVideoInfo.current[key]
            str += originReviewVideoInfo.current[key]
          }
          // })
        }
        if (moment(originReviewVideoInfo.current['course_begin']).diff(moment(originReviewVideoInfo.current['course_begin']))) {
          message.warning(`${intl.formatMessage({ id: '开始时间不能大于结束时间' })}`)
          return
        }
        getSensitiveWord(str, `${intl.formatMessage({ id: '录播信息' })}`, () => {
          contentListApis.repairInfo({ contentId: contentId_ }, originReviewVideoInfo.current
          ).then(_res => {
            if (_res?.success) {
              setRepairVisible(false)
              message.success(`${intl.formatMessage({ id: '录播信息填写成功' })}`)
            }
          })
        })
      })
  }
  const onCheckAllChange = (e: any) => {
    setCheckedList(e.target.checked ? sequencemeta.map((e: any) => e) : []);
    setCheckedKeys(
      e.target.checked ? sequencemeta.map((item: any) => item.guid_) : [],
    );
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  };
  const getProduct = async (type?: any) => {
    let temp_ = '';
    if (shareFlag_ === 'share') {
      temp_ = '?isSysAuth=true'
    }
    if (type === 'loop') {//针对异步更新不及时 只能采取循环调用
      let times = 0;
      setRelationVideoVisible(true);
      setLoading(true)
      const timer = setInterval(async () => {
        const res = await SmartService.getFinishProduct(contentId_, temp_);
        if (res?.data.data.length > product.length || times > 10) { //如果有新增 则默认查询成功了 最多查询10次
          clearInterval(timer);
          const temp = res?.data.data.map((item: any, index: number) => {
            return {
              ...item,
              selectFlag: false, // 选中标记
            };
          });
          setProduct(temp);
          setLoading(false)
        }
        times++;
      }, 500)
    } else {
      const res = await SmartService.getFinishProduct(contentId_, temp_);
      const temp = res?.data.data.map((item: any, index: number) => {
        return {
          ...item,
          selectFlag: false, // 选中标记
        };
      });
      setProduct(temp);
    }
  };
  const previous_page = () => {
    setCurrentPage((pre: any) => {
      return pre = pre - 1;
    })
  };
  const next_page = () => {
    setCurrentPage((pre: any) => {
      return pre = pre + 1;
    })
  };
  useEffect(() => {
    getQuestionVersion()
  }, [page, versionPageSize])
  useEffect(() => {
    if (firstTimeRender.current) {
      firstTimeRender.current = false
      return
    }
    getQuestionList()
  }, [versionCode, current, pageSize])
  const getKnowledgePoint = () => {
    if (contentDetail.entityName) {
      contentListApis.getKnowledgePoints({ contentId: contentId_, entityName: contentDetail.entityName }).then(res => {
        if (res?.success) {
          setKnowledgePoint(res?.data)
        }
      })
    }
  }
  useEffect(() => {
    getKnowledgePoint()
  }, [contentDetail])
  const getQuestionVersion = () => {
    contentListApis.getQuestionVersion({
      resourceId: contentId_,
      pageIndex: page,
      pageSize: versionPageSize,
      sourceId: ''
    }).then(res => {
      if (page === 1) {
        setQuestionVersion(res?.data?.data)
      } else {
        setQuestionVersion([...questionVersion, ...res?.data?.data])
      }
      setTotalPage(res?.data?.recordTotal)
      if (res?.data?.data?.length) {
        setVersionCode(res?.data?.data[0].code)
      }
      else {
        setVersionCode('')
      }
    })
  }
  const getQuestionList = () => {
    contentListApis.getListByVersion({
      resourceId: contentId_,
      versionName: versionCode,
      pageIndex: current,
      pageSize,
    }).then(res => {
      if (res?.data.data.length) {
        contentListApis.getQuestionsByIds(res?.data.data.map((item: any) => item.questionId)).then((res2: any) => {
          setQuestionRecordTotal(res?.data?.recordTotal)
          const list = res?.data.data.map((item: any) => ({
            ...item,
            ...(res2.data?.find((_item: any) => _item.id === item.questionId) || {})
          }))
          setQuesionList(list)
        })
      }
      else {
        current > 1 && setCurrent(current - 1)
        setQuesionList([])
        setQuestionRecordTotal(0)
      }
    })
  }
  // 页码切换
  const changepage = (page: number, size: any) => {
    setCurrent(page);
    setPageSize(size || 0);
  };
  const changeCoursewarePage = (page: number, size: any) => {
    setCoursewarePage(page);
  };
  useEffect(() => {
    getCourseware()
  }, [coursewarePage, coursewarePageSize])
  const getCourseware = async () => {
    let temp_ = '';
    if (shareFlag_ === 'share') {
      temp_ = '?isSysAuth=true'
    }
    const res = await entityApis.getKeyframes({
      contentId: contentId_,
      page: coursewarePage,
      size: coursewarePageSize
    }, temp_);
    const temp = res?.data?.data.sort((x: any, y: any) => {
      return x.keyFrameNo - y.keyFrameNo
    })
    setCoursewareTotal(res?.data?.recordTotal)
    setCourseware(temp?.map((item: any, index: number) => {
      return {
        ...item,
        hover: false
      }
    }));
  };

  const getIntelligentSummary = async () => {
    const res: any = await entityApis.getIntelligentSummary(contentId_);
    if (res?.status == 200 && res.data) {
      setIntelligentSummary(res.data.videoText);
      setIntelligentSummaryText(res.data.videoText);
    } else {
      setIntelligentSummary(null);
    }
  };
  const deleteIntelligentSummary = async () => {
    const res: any = await entityApis.deleteIntelligentSummary(contentId_);
    if (res?.status == 200 && res.data) {
      message.success(`${intl.formatMessage({ id: '删除成功' })}`);
      setIntelligentSummary(null);
      setIntelligentSummaryText(null);
    } else {
      message.error(`${intl.formatMessage({ id: '删除失败' })}`);
    }
  };
  const addIntelligentSummary = async (text?: string) => {
    //修改
    if (text) {
      entityApis.addIntelligentSummary({
        videoId: contentId_,
        videoText: text
      }).then((res: any) => {
        if (res.status == 200) {
          message.success(`${intl.formatMessage({ id: '保存成功' })}`);
          setIntelligentSummary(text);
          setIntelligentSummaryText(text);
        } else {
          message.error(`${intl.formatMessage({ id: '保存失败' })}`);
        }
      })
      return
    }
    setIntelligentSummaryLoading(true);
    setIntelligentSummary(null);
    //新增
    if (voice.length == 0) return;
    let data: any = [];
    let label = '';
    voice.map((item: any, index: number) => {
      if ((label + item.text).length < 800) {
        label += item.text;
        if (index == voice.length - 1) {
          data.push({
            role: 'user',
            content: label
          })
        }
      } else {
        //[{"role":"user","content":label}],
        data.push({
          role: 'user',
          content: label
        })
        label = '';
      }
    })
    data.push({
      role: 'user',
      content: `${intl.formatMessage({ id: '帮我总结一下以上内容知识点' })}`
    })
    console.log('data', data)
    function callback(res: any) {
      // console.log('callback',res)
      setIntelligentSummaryLoading(false);
      setIntelligentSummary(res);
    }
    function overback(res: any) {
      console.log('overback', res);
      setIntelligentSummaryLoading(false);
      entityApis.addIntelligentSummary({
        videoId: contentId_,
        videoText: res
      }).then((ress: any) => {
        if (ress.status == 200) {
          setIntelligentSummary(res);
          setIntelligentSummaryText(res);
        } else {

        }
      })
    }
    getSmartExplain(data, callback, overback);
  };
  const getKnowledgeMap = () => {
    setTabName('map')
    contentListApis.getKnowledgeMap({ contentId: contentId_, entityName: contentDetail.entityName }).then(res => {
      window.open(
        `/learn/workbench/#/perviewemap?mapid=${res?.data.mapId}`,
      );
    })
  }
  const coursewareClick = (item: any) => {
    setCurrentCourseware(item.fileGUID);
    const time = win.TimeCodeConvert.frame2Second(item.keyFrameNo, frameRate);//帧转秒
    player.current?.setCurrentTime(time); //时间是秒
  };
  const coursewareDelete = async (e: any, item: any) => {
    e.stopPropagation();
    // alert(item.fileGUID)
    const res: any = await entityApis.deleteKeyframes([
      {
        contentId: contentId_,
        keyFrameNos: [item.keyFrameNo]
      }
    ]);
    if (res.success) {
      message.success(`${intl.formatMessage({ id: '删除成功' })}`);
      setTimeout(() => getCourseware(), 1000);
    } else {
      message.error(`${intl.formatMessage({ id: '删除失败' })}`);
    }
  };
  const getExif = async () => {
    let temp = '';
    if (shareFlag_ === 'share') {
      temp = '?isSysAuth=true'
    }
    const res: any = await entityApis.getExif(contentId_, temp);
    if (res?.success === true) {
      let exifs: IExifInfo = (res.data || [])[0];
      let exif = exifs?.exif || {};
      let formatExif: {
        alias: string;
        value: any;
        fieldName: string;
      }[] = [];
      if (exif) {
        Object.keys(exif).forEach((item: string) => {
          if (ExifConfig[item]) {
            formatExif.push({
              alias: ExifConfig[item],
              value: exif[item],
              fieldName: item,
            });
          }
        });
      }
      setFormatExif(formatExif);
    }
  };
  const refreshTab = (tag: any, voice: any, point: any) => {
    if (!tag && !voice && !point) return;
    //查询基本信息
    getMetaData(); //先立即查一次 以便更新tab状态
    (window as any).setIntervalForMetaData = setInterval(() => {
      getMetaData();
    }, 5000);
    if (tag) setActiveTabs('1');
    if (point) setActiveTabs('4');
    if (voice) setActiveTabs('3');  //优先切换到语音
  };

  // const checkClear = (e: any) => {
  //   setCheckedList([]);
  //   openMessage();
  // }

  /**
   * 生成单个成品
   * @param values
   */
  const finishProduct = async (values: any) => {
    if (values.colorImprove) {
      values.colorImprove = HexToRgb(values.colorImprove).toString();
    }
    let data = checkedList.map((e: any) => {
      const { title, inpoint, outpoint } = e;
      return { saveName: title, inPoint: inpoint, outPoint: outpoint };
    });
    const res = await SmartService.createProduct(
      contentId_,
      data,
      values,
    );
    if (res?.success) {
      message.success(`${intl.formatMessage({ id: '提交合成成功' })}`);
      // setActiveTabs('5');
      getProduct('loop');
      setCheckedList([]);
      setCheckedKeys([]);
      setsmVisible(false);
      setCheckAll(false);
      setIndeterminate(false);
    }
  };
  /**
   * 合成成品（多合一）
   * @param values
   */
  const compositeVideo = async (values: any) => {
    if (values.colorImprove) {
      values.colorImprove = HexToRgb(values.colorImprove).toString();
    }
    let res;
    if (values.beginId || values.endId) {
      const data = checkedList.map(({ inpoint, outpoint }: any) => ({
        inPoint: inpoint,
        outPoint: outpoint,
        contentId: contentId_,
      }));
      if (values.beginId) {
        data.unshift({
          contentId: values.beginId,
          inPoint: -1,
          outPoint: -1,
        });
        delete values.beginId;
      }
      if (values.endId) {
        data.push({
          contentId: values.endId,
          inPoint: -1,
          outPoint: -1,
        });
        delete values.endId;
      }
      res = await SmartService.mergeProduct2(data, values);
    } else {
      const data = checkedList.map(({ inpoint, outpoint }: any) => ({
        inPoint: inpoint,
        outPoint: outpoint,
      }));
      res = await SmartService.mergeProduct(contentId_, data, values);
    }
    if (res?.success) {
      message.success(`${intl.formatMessage({ id: '提交合成成功' })}`);
      // setActiveTabs('5');
      getProduct('loop');
      setsmVisible(false);
      setCheckedList([]);
      setCheckedKeys([]);
      setCheckAll(false);
      setIndeterminate(false);
    }
  };

  const handleSuccess = (play: Player) => {
    console.log('video started success');
    // console.log('permissions', permissions)
    console.log('ispublic', ispublic)
    console.log('track', track)
    player.current = play;
    console.info('play', play);
    if (!(play as any).isVideo) {  //如果不是视频就中断
      return
    }
    setInitFlag(true)//判断插件已加载完毕
    getPlayerStart();
    // !mobileFlag && initTrack(track);
    initTrack(track)
  };
  // console.log('keyPoints',keyPoints);
  //知识点片段同步到视频的关键帧里
  useEffect(() => {
    if (initFlag && sequencemeta.length > 0) {
      //加延时是因为插件初始化完成时 作用域以及视频总时长可能为空
      setTimeout(() => {
        setPointKey()
      }, 300);
    }
  }, [sequencemeta, initFlag])
  const setPointKey = (arr?: any) => {
    //每次进来先清空进度条上的点 防止敏感词的点和知识点同时存在
    const temp: any = document.getElementsByClassName('.mejs__keypoint');
    while (temp.length > 0) {
      temp[0].remove();
    }
    let keyPoints: any;
    if (arr) {
      keyPoints = arr.sensitiveWordGroups.map((item: any, index: number) => {
        //由于父级节点定宽 中文字符便会垂直显示 只能动态设置了
        const content = $(`<span>${arr.sensitive_word || arr.type_chinese}</span>`);
        $('body').append(content)
        const width = content.width();
        content.remove();
        // console.info(width)
        return {
          'frameNo': item.point_in || item.inpoint,
          'content': `<div style="text-align:center;width:${width}px">${arr.sensitive_word || arr.type_chinese}</div>`
        }
      })
    } else {
      if (sequencemeta.length == 0) return;
      keyPoints = sequencemeta.map((item: any, index: number) => {
        //由于父级节点定宽 中文字符便会垂直显示 只能动态设置了
        const content = $(`<span>${item.title}</span>`);
        $('body').append(content)
        const width = content.width();
        content.remove();
        // console.info(width)
        return {
          'frameNo': item.inpoint,
          'content': `<div style="text-align:center;width:${width}px">${item.title}</div>`
        }
      })
    }
    // console.log('keyPoints',keyPoints);
    player.current?.addKeyPoint(keyPoints);
    const keypoint: any = document.getElementsByClassName('mejs__keypoint');
    for (let i = 0; i < keypoint.length; i++) {
      keypoint[i].onclick = arr ? sensitivePointClick : keyPointClick;
      // keypoint[i].addEventListener('click', ()=>keyPointClick(keyPoints[i]));
    }
  }
  const keyPointClick = (item: any) => {
    console.log('keyPointClick', item);
    player.current?.play(); //点击知识点立即播放；
    setPlayFlag(true);
    setActiveTabs('4');
  }
  //敏感词点击
  const sensitivePointClick = (item: any) => {
    console.log('sensitivePointClick', item);
    player.current?.play(); //点击知识点立即播放；
    setPlayFlag(true);
  }
  const autoTrack = () => {
    //初始化默认打开字幕
    const trackSecond: any = document.getElementsByClassName('mejs__captions-selector-list-item');
    // console.log('trackSecond',trackSecond)
    if (trackSecond.length > 1) {
      const input = trackSecond[1].getElementsByClassName('mejs__captions-selector-input');
      input[0].click();
      console.log(`${intl.formatMessage({ id: '自动显示弹幕了' })}`)
    }
  }
  const initTrack = (flag: boolean) => {
    const button: any = document.querySelector('.mejs__captions-button>button');
    const selector: any = document.querySelector('.mejs__captions-button>.mejs__captions-selector');
    if(button && selector){
      if (!flag) {
        selector.style.display = 'none';
        button.title = `${intl.formatMessage({ id: '打开字幕需先完成语音分析' })}`;
        button.classList.add('no_tracks')
      } else {
        button.title = `${intl.formatMessage({ id: '字幕' })}`;
        selector.style.display = 'block';
        button.classList.remove('no_tracks')
        const trackList: any = document.querySelector('.mejs__captions-selector-list');
        //修改关闭弹幕的名字
        const trackFirstLabel: any = document.querySelector('.mejs__captions-selector-list-item label');
        // console.log(trackFirstLabel);
        trackFirstLabel.innerText = `${intl.formatMessage({ id: '关闭' })}`;
        trackList.addEventListener('click', function () {
          const trackChecked: any = document.querySelector('.mejs__captions-selector-list .mejs__captions-selected');
          // console.log(trackChecked);
          if (!trackChecked.getAttribute('for').includes('captions_none')) {
            button.classList.add('on_tracks')
          } else {
            button.classList.remove('on_tracks')
            const span: any = document.getElementsByClassName('mejs__captions-text')?.[0];
            if (span) {
              span.innerText = '';
            }
          }
        });
        setTimeout(() => autoTrack(), 500) //由于组件初始化需要时间 需要添加延时才行
        dragTrack();
      }
    }
    //监听播放与暂停事件
    // const video: any = document.getElementById('h5Player_html5');
    const video: any = document.getElementById('h5Player') || document.getElementById('h5Player_html5');
    video.addEventListener('play', function (e: any) {
      setPlayFlag(true);
    });
    // video.addEventListener('pause', function (e: any) {
    //   console.log('暂停了')
    //   var aa = document.querySelector('.mejs__layers')
    //   aa?.classList.remove('pointer_event')
    // });
  };
  useEffect(() => {
    if (initFlag) {
      trackFontStyle();
      adjustPosition();
    }
  }, [fontStyle, initFlag])
  const trackFontStyle = () => {
    const spanDiv: any = document.getElementsByClassName('mejs__captions-position')[0];
    const span: any = document.getElementsByClassName('mejs__captions-text')[0];
    if (span) {
      const video: any = document.querySelector('#h5Player video');
      let realX = video.clientWidth, realY = video.clientHeight;//计算偏移量
      const moveX = realX * fontStyle.left;
      const moveY = realY * fontStyle.top;
      span.style.cssText +=
        `left:${moveX + 'px'};
       width:auto;
       text-align:left;
       word-break:break-word;
       ${`top:+${moveY}px`};`
    }
  }
  //监听视频窗口大小调整字幕想对位置
  const adjustPosition = () => {
    const video: any = document.querySelector('#h5Player video');
    //对于局部dom大小变化监听 最好用观察者模式
    let MutationObserver = window.MutationObserver;
    let observer = new MutationObserver((mutations: any) => {
      setTimeout(() => { //防止全屏切换 视频宽高取的是变化前的bug
        trackFontStyle();
      })
    })
    observer.observe(video, {
      attributes: true,
      attributeFilter: ['style'], //只对样式监听,
      attributeOldValue: true
    });
    //没有变化也要更新位置；
    trackFontStyle();
  }
  //字幕拖拽功能初始化
  const dragTrack = () => {
    //拖拽：在div上按下鼠标，移动鼠标，移动过程中让div跟随鼠标移动
    const trackDiv: any = document.getElementsByClassName('mejs__captions-text')[0];
    trackDiv.addEventListener('mousedown', function (e: any) {
      const x1 = e.clientX - trackDiv.offsetLeft, y1 = e.clientY - trackDiv.offsetTop;
      document.onmousemove = (e_: any) => {
        trackDiv.classList.add('mejs__captions-text_drag');
        const video: any = document.getElementById('h5Player');
        let realX = video.clientWidth, realY = video.clientHeight;
        const maxX = realX - trackDiv.offsetWidth;//X轴可移动最大距离
        const maxY = realY - trackDiv.offsetHeight;//Y轴可移动最大距离
        const x2 = e_.clientX, y2 = e_.clientY;
        const moveX = Math.min(maxX, Math.max(0, x2 - x1));
        const moveY = Math.min(maxY, Math.max(0, y2 - y1));
        trackDiv.style.left = moveX + 'px';
        trackDiv.style.top = moveY + 'px';
        //计算出相对于视频位置的比例以便适应各种分辨率
        const _x = moveX / realX;
        const _y = moveY / realY;
        window.onmouseup = () => {
          document.onmousemove = null;
          setFontStyle((pre: any) => {
            return {
              left: Number(_x.toFixed(2)),
              top: Number(_y.toFixed(2))
            }
          })
          window.onmouseup = null;
        }
      }
    })
  }
  useEffect(() => {
    if (track && initFlag) {
      initTrack(true);
    }

  }, [track, initFlag, mobileFlag])

  /**
   * 设置片段
   * @param media
   * @param img
   * @param trimin
   * @param trimout
   */
  const handleSection = (
    media: any,
    img: string,
    trimin: number,
    trimout: number,
  ) => {
    setActiveTabs('4');
    setModalVisible(true);
    setSection({
      inpoint: parseInt((trimin * 10000000).toString()),
      outpoint: parseInt((trimout * 10000000).toString()),
      keyframepath: img,
    });
  };
  /**
   * 截取当前关键帧
   * @param media
   * @param currentTime
   * @param img
   */
  const handleKeyframe = async (
    media: any,
    currentTime: number,
    img: string,
  ) => {
    // console.log(media,currentTime)
    const res: any = await entityApis.setKeyframe({
      contentId: contentId_,
      ns100: currentTime * 10000000
    });
    console.log('handleKeyframe', res)
    if (res.success) {
      message.success(`${intl.formatMessage({ id: '标记成功' })}`);
      setTimeout(getCourseware, 2000)
      setActiveTabs('7');
    } else {
      message.error(`${intl.formatMessage({ id: '标记失败' })}`);
    }
  };
  /**
   * 保存sec
   * @param values
   */
  const handleSubmitSec = async (values: any) => {
    console.log(section, 'section')
    // if (sequencemeta.length === 0) {
    let keyframeno = win.TimeCodeConvert.l100Ns2FrameNoCorrecte(
      section?.inpoint,
      frameRate,
    );
    getSensitiveWord(values.title, '标题', async () => {
      const res = await SmartService.addSequencemetaNew(contentId_, [
        {
          keyframeno,
          ...section,
          keyframepath: '',
          ...values,
        },
      ]);
      setModalVisible(false);
      pointsForm.resetFields();
      if (res?.success) {
        let a = 1;
        setKeyFrameFlag(true) //只在新建知识点才打开验证开关
        timer = setInterval(() => {
          getQuerySequencemeta(a);
          a++;
          if (a > 10) {
            clearInterval(timer);
            setKeyFrameFlag(false)
          }
        }, 1000)
        // setTimeout(()=>{
        //   clearInterval(timer);
        //   setKeyFrameFlag(false)
        // },10000)
        message.success('新建成功');
      }
    })
  };
  //获取播放时间起点
  const getPlayerStart = async () => {
    // 判断是不是课程地图跳转过来的 带了知识点的inpoint
    if (history.location?.query.inpoint) {
      player.current?.setCurrentTime(win.TimeCodeConvert.l100Ns2Second(history.location?.query.inpoint));
    } else {
      //获取上次播放时间节点
      const platform = window.localStorage.getItem('upform_platform');
      if (platform === 'Lark' || shareFlag_ === 'share') return;
      const res: any = await entityApis.getVideoHistory({ videoId: contentId_ });
      if (res?.status === 200 && res?.data) {
        player.current?.setCurrentTime(res.data?.watchTime ? res.data?.watchTime / 10000000 : 0); //时间是秒
        currentTime.current = res.data?.id;
      }
    }
  }
  //设置播放时间起点
  const setPlayerStart = async () => {
    //记录当前播放时间节点
    const platform = window.localStorage.getItem('upform_platform');
    if (platform === 'Lark' || shareFlag_ === 'share') return;
    const current: any = player.current?.getCurrentTime() ? player.current?.getCurrentTime() : 0; //时间是秒
    console.log('记录当前播放时间节点', current)
    if (currentTime.current) {
      const ress: any = await entityApis.updateVideoHistory({ id: currentTime.current, watchTime: current * 10000000 });
      console.log('更新', ress)
    } else {
      const ress: any = await entityApis.setVideoHistory({ videoId: contentId_, watchTime: current * 10000000 });
      console.log('新增', ress)
    }
  }
  const setPlayerSec = (inpoint: number, outpoint: number, data: any, canModify: boolean = true) => {
    setKnowKey(data.guid_)
    sectionData = { ...data };
    const _inpoint = win.TimeCodeConvert.l100Ns2Second(inpoint),
      _outpoint = win.TimeCodeConvert.l100Ns2Second(outpoint);
    // console.log(inpoint, outpoint);
    // console.log(_inpoint, _outpoint);
    player.current?.hideModifyBtn(true);
    player.current?.setTrimin(_inpoint);
    player.current?.setTrimout(_outpoint);
    player.current?.setCurrentTime(_inpoint);
    if (canModify) {
      player.current?.showModifyBtn();
    }
    player.current?.play();
    setPlayFlag(true);
  };
  const handleSecDel = async (ids: any[]) => {
    if (ids.length == 0) return;
    const res = await SmartService.deleteSequencemetaNew(contentId_, ids);
    if (res?.success) {
      message.success(`${intl.formatMessage({ id: '删除成功' })}`)
      getQuerySequencemeta();
      if (ids.includes(knowKey)) {
        setKnowKey('');
        setPlayerSec(0, 0, {}, false);
      }
      setCheckedList(
        checkedList.filter(checkItem => {
          // return checkItem.guid_ !== id;
          return !ids.includes(checkItem.guid_);
        }),
      );
      setCheckedKeys(
        checkedKeys.filter(checkKey => {
          // return checkKey !== id;
          return !ids?.includes(checkKey);
        }),
      );
    }
  };

  const handleModifySection = (media: any, img: string, trimin: number, trimout: number) => {
    const { guid_, title, keyframeno, fragment_description, inpoint } = sectionData;
    const frame = inpoint !== parseInt((trimin * 10000000).toString())
    if (activeTabs === '11') {
      let data = qualityCheckList.map((item: any) => {
        if (item.guid_ === guid_) {
          return { ...item, inpoint: parseInt((trimin * 10000000).toString()), outpoint: parseInt((trimout * 10000000).toString()) }
        }
        return item
      });
      modal.confirm({
        title: `${intl.formatMessage({ id: '修改片段' })}`,
        content: `${intl.formatMessage({ id: '是否确认修改该条片段' })}？`,
        onOk: () => {
          contentListApis.qualityCheckMetaUpdate(contentId_, data).then((res: any) => {
            if (res.success) {
              message.success(`${intl.formatMessage({ id: '修改成功' })}`);
              qualityCheckMeta()
              sectionData = { ...sectionData, inpoint: parseInt((trimin * 10000000).toString()), outpoint: parseInt((trimout * 10000000).toString()) }
            } else {
              message.error(`${intl.formatMessage({ id: '修改失败' })}`);
            }
            player.current?.hideModifyBtn();
          })
        }
      });
    }
    else {
      modal.confirm({
        title: `${intl.formatMessage({ id: '修改知识点' })}`,
        content: `${intl.formatMessage({ id: '是否确认修改该条知识点' })}？`,
        onOk: () => {
          SmartService.updatepartSequencemetaNew(frame, contentId_,
            [
              {
                guid_,
                title,
                inpoint: parseInt((trimin * 10000000).toString()),
                outPoint: parseInt((trimout * 10000000).toString()),
                keyframeno,
                fragment_description
              }
            ]
          ).then((res: any) => {
            if (res.success) {
              message.success(`${intl.formatMessage({ id: '修改成功' })}`);
              getQuerySequencemeta();
              sectionData = { ...sectionData, inpoint: parseInt((trimin * 10000000).toString()), outpoint: parseInt((trimout * 10000000).toString()) }
            } else {
              message.error(`${intl.formatMessage({ id: '修改失败' })}`);
            }
            player.current?.hideModifyBtn();
          })
        }
      });
    }
  };
  const handleResetSection = (callback: any) => {
    const _inpoint = win.TimeCodeConvert.l100Ns2Second(sectionData.inpoint),
      _outpoint = win.TimeCodeConvert.l100Ns2Second(sectionData.outpoint);
    callback(_inpoint, _outpoint)
  }
  useEffect(() => {
    getQuestionPoint()
  }, [versionCode])
  const getQuestionPoint = () => {
    contentListApis.getQuestionPoint({
      versionName: versionCode,
      resourceId: contentId_,
      sourceId: ''
    }).then(res => {
      setQuestionPoint(res?.data)
    })
  }
  const handlePlayChange = (point: number) => {
    setCurrentFrame(point);
    setCurrentPlayTime(timecodeconvert.l100Ns2Tc$1(point, frameRate, false))
  };
  const setCurrentTime = (point: number) => {
    player.current?.setCurrentTime(win.TimeCodeConvert.l100Ns2Second(point));
  };
  const isInViewport = (ele: { getBoundingClientRect: () => { top: any; right: any; bottom: any; left: any; }; }) => {
    let { top, right, bottom, left } = ele.getBoundingClientRect()
    let w = window.innerWidth
    let h = window.innerHeight
    if (bottom < 0 || top > h) {
      // y 轴方向
      return false
    }
    if (right < 0 || left > w) {
      // x 轴方向
      return false
    }
    return true
  }
  // 设置自动滚动到正在播放的语音信息、知识点
  useEffect(() => {
    if (!playFlag) {
      return
    }
    if (activeTabs === '3') { //语音信息
      if (isVision) {
        const domObj: any = document.getElementsByClassName('lyric_item active')[0];
        let timelineObj: any = document.getElementById('timeline');
        // console.log(domObj?.parentElement?.parentElement?.offsetTop)
        if (domObj) {
          updateScrollPosition({
            dom: timelineObj,
            dis: domObj.parentElement?.parentElement?.offsetTop - 123
          })
        }
      } else {
        const domObj_: any = document.getElementsByClassName('currentPlay')[0];

        let timelineObj_: any = document.getElementsByClassName('paragraph-content')[0];
        // console.log(domObj?.parentElement?.parentElement?.offsetTop)
        if (domObj_) {
          // console.log(domObj_.getBoundingClientRect().top);
          updateScrollPosition({
            dom: timelineObj_,
            dis: domObj_.offsetTop - 123
          })
        }
      }

    } else if (activeTabs === '4') {
      const domObj: any = document.getElementsByClassName('knowledgeCheckGroup')[0];
      const curDomObj: any = document.getElementsByClassName('sequence_item_wrap active')[0];
      if (curDomObj && domObj && currentTop.current !== curDomObj.offsetTop) { //必须把top值保存起来 不然无法滚动
        if (!isInViewport(curDomObj)) {
          currentTop.current = curDomObj.offsetTop;
          updateScrollPosition({
            dom: domObj,
            dis: curDomObj.offsetTop - 42
          })
        }
      }
    } else {

    }
  }, [currentFrame, playFlag, sequencemeta, isVision]);
  const cancelMetadata = () => {
    // 点击取消的方法
    if (metadataRef.current) {
      metadataRef.current.getOldItems();
      setEditVisible(false);
    }
  };
  const addTheme = (newValue) => {
    let theme = newValue.find((item: any) => item.fieldName === 'theme')
    if (theme.value.length) {
      let themeAssociations = theme.value.map(item => {
        let child = getAChild(JSON.parse(theme.controlData), 'categoryCode', item)
        return {
          "themeCode": child[0].categoryCode,
          "themeName": child[0].categoryName,
          "parentCode": child[0].categoryParent,
          "level": getDepth(JSON.parse(theme.controlData), child[0])
        }
      })
      contentListApis.addTheme({ contentId: contentId_, themeAssociations })
    }
  }
  // data 是树型数组
  // key 是String 对象中的key值
  // value 是key值符合的条件
  const getAChild = (data: any, key: string, value: string) => {
    let result: any[] = [];
    let fn = function (data) {
      if (Array.isArray(data)) { // 判断是否是数组并且没有的情况下，
        data.forEach(item => {
          if (item[key] === value) { // 数据循环每个子项，并且判断子项下边是否有id值
            result.push({ ...item }); // 返回的结果等于每一项
          } else if (item.children.length) {
            fn(item.children); // 递归调用下边的子项
          }
        })
      }
    }
    fn(data); // 调用一下
    return result;
  }
  const getDepth = (data, target) => {
    let depth = 0;
    function findDepth(data, currentDepth) {
      for (const node of data) {
        if (node.categoryCode === target.categoryCode) {
          depth = currentDepth;
          return;
        }
        if (node.children) {
          for (let i = 0; i < node.children.length; i++) {
            findDepth([node.children[i]], currentDepth + 1);
          }
        }
      }
    }
    findDepth(data, 1);
    return depth;
  }
  const submitMetadata = (newValue: any) => {
    // 点击提交的方法
    let str = ''
    if (newValue) {
      const newMetadata = copyObject(newValue);
      const oldMetadata = copyObject(metadata);
      let majorval = newMetadata.find((item: any) => item.fieldName === 'major');
      if (majorval) {
        if (majorval.value.length > 32) {
          message.info(`${intl.formatMessage({ id: '专业最多只能选择32个' })}`);
          return;
        }
      }
      newMetadata.forEach((item: any) => {
        if (!item.isReadOnly) {
          str += item.value
        }
        if (item.controlType === 8) {
          item.value = JSON.stringify(item.value);
        } else if (item.controlType === 14) {
          item.value = JSON.stringify(item.value);
        } else if (item.controlType === 12) {
          item.value = JSON.stringify(item.value);
        }
        item.edit = false
      });
      getSensitiveWord(str, `${intl.formatMessage({ id: '基本信息' })}`, () => {
        entityApis
          .setEntityMetadata(
            contentId_,
            {
              oldValue: oldMetadata,
              newValue: newMetadata,
            },
            ispublic,
          )
          .then(res => {
            if (res && res.data && res.success) {
              message.success(`${intl.formatMessage({ id: '修改成功' })}`);
              setMetadata(newMetadata)
              const name = newMetadata.find(
                (item: any) => item.fieldName === 'name',
              );
              if (name && name.value !== title) {
                setTitle(name.value as string);
              }
              setEditVisible(false);
            } else {
              message.error(`${intl.formatMessage({ id: '修改失败' })}`);
            }
          });
      }).catch(() => {
        message.error(`${intl.formatMessage({ id: '您还有数据没有通过验证' })}`);
      })
    }
  }
  const sortFuc = (a: SmartTypes.SequenceMeta, b: SmartTypes.SequenceMeta) =>
    a.inpoint - b.inpoint;
  const isBasicFormat = (src: string) => {
    return (
      src?.indexOf('.html') > -1 ||
      src?.indexOf('.htm') > -1 ||
      src?.indexOf('.jpg') > -1 ||
      src?.indexOf('.mp4') > -1 ||
      src?.indexOf('.mp3') > -1 ||
      src?.indexOf('.pdf') > -1
    );
  };
  const qualityCheckMeta = () => {
    contentListApis.qualityCheckMeta(contentId_).then(res => {
      if (res?.success) {
        let result = res.data.filter((item: { audit_delete: string; }) => item.audit_delete === '0')
        setQualityCheckList(result)
      }
    })
  }
  const handleMessage = (e: any) => {
    if (e.data && typeof e.data === "string") {
      const { action, data } = JSON.parse(e.data);
      if (action === "addTopicBack") {
        setShowAddTopic(false);
      }
    }
  };
  useEffect(() => {
    if (!commentModalVisible) {
      contentListApis.getCommentTotal({ contentId: contentId_, isSysAuth: true }).then(res => {
        setCommentTotal(res?.data)
      })
    }
  }, [commentModalVisible])

  const isFileSize = (fieldName: string) =>
    ['fileSize', 'fileSize_'].includes(fieldName);

  //获取基本数据
  const getMetaData = () => {
    let temp = contentId_;
    entityApis.getEntityMetadata(temp).then(res => {
      if (res && res.data && res.success) {
        getEntityData();//当资源存在才继续调用
        if (Array.isArray(res.data)) {
          res.data.forEach(item => {
            if (item.alias === '上传用户') {
              item.alias = '上传人'
            }
            if (!item.value) {
              item.value = '';
            }
            if (!isNaN(item.value as any) && isFileSize(item.fieldName)) {
              item.value = changesize(item.value);
              item.isReadOnly = true;
            }
          });
          let arr = res.data.filter(item => item.fieldName !== "createUser_")
          arr.sort((a, b) => a.order - b.order)
          setMetadata(arr);
        }
      } else {
        //401没权限 404资源已删除
        if (['401', '404'].includes(res?.error?.code || '')) {
          setErrorCode(Number(res?.error?.code))
        } else {
          getEntityData();
        }
      }
    });
  };
  const initValues = (data: any) => {
    const obj: { [key: string]: string | Moment | number | string[] } = {};
    data.forEach(item => {
      if (item.controlType === 8 || item.controlType === 12) {
        obj[item.fieldName] = item.value
          ? Array.isArray(item.value)
            ? item.value
            : JSON.parse(item.value + '')
          : [];
      } else if (item.controlType === 1) {
        obj[item.fieldName] = moment(item.value || '');
      } else if (item.controlType === 14) {
        obj[item.fieldName] = item.value
          ? Array.isArray(item.value)
            ? item.value
            : JSON.parse(item.value + '')
          : [];
      } else {
        obj[item.fieldName] = item.value || '';
      }
    });
    form0.setFieldsValue(obj)
    form1.setFieldsValue(obj)
    form2.setFieldsValue(obj)
    form3.setFieldsValue(obj)
  };
  //获取网络带宽
  const getNetworkSpeed = () => {
    // 判断浏览器是否支持navigator.connection属性
    if ((navigator as any)?.connection) {
      // 获取当前网络连接信息
      var connection = (navigator as any)?.connection;
      // 如果浏览器支持downlink属性，则输出当前网络的下行带宽
      if (connection.downlink) {
        return connection.downlink
      } else {
        return
      }
    } else {
      return 0
    }
  }
  useEffect(() => {
    let temp: any = {};
    if (rmanGlobalParameter.includes(globalParams.sensitive_word_display)) {
      temp.sensitiveWords = sensitiveWords
    }
    if (rmanGlobalParameter.includes(globalParams.sensitive_person_display) && ['biz_sobey_video', 'biz_sobey_picture'].includes(contentDetail.type)) {
      temp.sensitivePersons = sensitivePersons
    }
    if (rmanGlobalParameter.includes(globalParams.sensitive_image_enable)) {
      temp.sensitiveImages = sensitiveImages
    }
    setOtherFormData(temp)
  }, [sensitivePersons, sensitiveWords, sensitiveImages, contentDetail])
  const getEntityData = () => {
    let temp = contentId_;
    if (shareFlag_ === 'share') {
      temp = contentId_ + '?isSysAuth=true'
    }
    entityApis.checksharepublish(temp, true, shareFlag_ === 'share').then((res: any) => {
      if (res && res.data && res.success) {
        //给语音、知识点设置状态
        let flag = 0;
        if (String(res.data.asr_status) === '1') {
          flag++;
          setVoiceStatus(String(res.data.asr_status));
        }
        if (String(res.data.ocr_status) === '1') {
          flag++;
          setKnowledgeStatus(String(res.data.ocr_status));
        }
        if (String(res.data.smart_status) === '1') {
          flag++;
        }
        // console.log('dangqianflag',flag)
        if (flag > 0 && (window as any).setIntervalForMetaData) {
          clearInterval((window as any).setIntervalForMetaData);
        }
        setDirTree(res.data.path);
        setContentDetail(res.data)

        console.log(temp, 'temp')
        setVoiceCheck(res.data.voice_text_check == 'true');
        setCounter({
          like: res.data.total_like || 0,
          collect: res.data.total_collect || 0,
          download: res.data.total_download || 0,
          share: res.data.total_share || 0,
          copy: res.data.total_copy || 0,
          micro: res.data.total_micr || 0,
          knowledge: res.data.total_knowledge || 0,
        })
        //dirTree 更新不及时不能采用
        if (res.data.path?.indexOf('public') != -1) {
          setIspublic(true);
        }
        let dataType = res.data.type;
        // 不同素材不同调用接口
        if (dataType === 'biz_sobey_picture') {
          getExif();
          if (hidecatalogue !== '1' && rmanGlobalParameter.includes(globalParams.sensitive_person_display)) {
            getSensitivePersons()
          }
        } else if (hidecatalogue !== '1') {
          let temp = '';
          if (shareFlag_ === 'share') {
            temp = '?isSysAuth=true'
          }
          SmartService.querySubtitle(contentId_, temp).then(res => {
            if (res?.data && res.data.data.length > 0) {
              setSubtitles(res.data.data[0].metadata);
            }
          });
          getQueryVoice();
          getQuerySequencemeta();
          getQueryKeywords();
          getProduct();
          getIntelligentSummary();
          if (dataType == 'biz_sobey_video'
            && hidecatalogue !== '1'
            && rmanGlobalParameter.includes(globalParams.sensitive_person_display)) {
            getSensitivePersons();
          }
        }
        if (hidecatalogue !== '1' && rmanGlobalParameter.includes(globalParams.sensitive_word_display)) {
          getSensitiveWords()
        }
        setCanEdit(res.data.extensions?.canEdit);
        if (res.data.fileGroups.length === 0) { //文件分析失败时 不给访问
          setErrorCode(403);
        }
        let path = _.find(res.data.fileGroups, (f: any) => {
          return f.typeCode === (dataType === 'biz_sobey_document' || dataType === 'biz_sobey_picture' ? 'sourcefile' : 'previewfile');
        });
        if (!path) {
          path = _.find(res.data.fileGroups, (f: any) => {
            return f.typeCode === 'sourcefile';
          });
          if (
            dataType === 'biz_sobey_picture' ||
            dataType === 'biz_sobey_audio' ||
            dataType === 'biz_sobey_document' ||
            dataType === 'biz_sobey_video'
          ) {
            path?.fileItems[0] &&
              setIsTransCode(isBasicFormat(path?.fileItems[0].displayPath));
          }
        }
        if (path) {
          setFrameRate(path.fileItems[0].frameRate);
          setDuration(path.fileItems[0].duration);
          setType(res.data.type);
        }
        //自动适应清晰度
        let autoPathObj = path ? path.fileItems[0] : {}
        if (path && path.fileItems && path.fileItems.length) {
          let speed = getNetworkSpeed()
          if (speed < 1) {
            autoPathObj = path.fileItems.find((p: any) => p.bitRate <= 512 * 1000) || path.fileItems[0]
          } else if (speed < 3) {
            autoPathObj = path.fileItems.find((p: any) => p.bitRate <= 1024 * 1000) || path.fileItems[0]
          } else if (speed < 5) {
            autoPathObj = path.fileItems.find((p: any) => p.bitRate <= 2048 * 1000) || path.fileItems[0]
          } else {
            autoPathObj = path.fileItems[path.fileItems.length - 1]
          }
        }
        let autoPath = autoPathObj.displayPath
        setEntity({
          path: autoPath,
          keyframes:
            res.data.keyframe && res.data.keyframe.filePath
              ? res.data.keyframe.filePath
              : '',
          type: getType(res.data.type),
        });
        getSensitiveImages(getType(res.data.type))
        setTitle(res.data.entityName);
        document.title = res.data.entityName;
        // localStorage.setItem('rman_resource_title', JSON.stringify(res.data.entityName));
        var parentData = {
          type: 'rman_resource_title',
          data: res.data.entityName,
        };
        win.parent.postMessage(JSON.stringify(parentData), win.location.origin);
      }
    });
  }
  // 获取语音
  const getQueryVoice = () => {
    let temp = '';
    if (shareFlag_ === 'share') {
      temp = '?isSysAuth=true'
    }
    SmartService.queryVoice(contentId_,temp).then(res => {
          if (res?.data && res.data.data.length > 0) {
            setTrack(true);
            setLanguageList(res.data.data[0].languageList);
            setSubstitles(res.data.data[0].languageList?.map(item => Object.keys(item)[0]));
            setCurrentLanguage(Object.keys(res.data.data[0]?.languageList?.[0])[0]|| '');
            const list = res.data.data[0].metadata.sort((a, b) => a._in - b._in);
            const temp = JSON.parse(window.localStorage.getItem('searchKeyWord')||'{}');
            if(temp?.word){
              list?.map((item:any)=>{
                searchKeywords(item.text,'voice');
              })
            }
            setVoice(list);
            //新增语音翻译功能标识
            const flag = res.data.data[0].metadata[0]?.text_en?true:false;
            setTranslateFlag(flag);
          } else {
            setTrack(false)
            setVoice([]);
          }
        });
  };
  // 获取关键词
  const getQueryKeywords = () => {
    SmartService.queryKeywordNew(contentId_, shareFlag_ == 'share').then((res: any) => {
      if (res?.success) {
        let result_: any = [], temp_ = res.data;
        temp_?.sort((a: any, b: any) => b.value - a.value).map((item: any, index: number) => {
          let result, average = 1 / (res.data.length - 1)//平均梯度;
          if (index == 0) {
            result = 1  //以最大的为单位1
          } else {
            const scale = item.value / temp_[index - 1].value; //与上一个值的比例
            result = (Math.max(scale, (1 - average))) * result_[index - 1].value; //统一阶梯比例在0.9 - 1 之间
          }
          result_.push({
            name: item.name,
            value: result
          })
        });
        setKeywords(result_)
      } else {
        setKeywords([])
      }
    });
  };
  //获取敏感词
  const getSensitiveWords = () => {
    SmartService.querySensitiveWords(contentId_, shareFlag_ === 'share').then((res: any) => {
      if (res?.success) {
        setSensitiveWords(res.data || []);
      } else {
        setSensitiveWords([]);
      }
    })
  }
  //获取敏感人物
  const getSensitivePersons = () => {
    SmartService.querySensitivePersons(contentId_, shareFlag_ === 'share').then((res: any) => {
      if (res?.success) {
        setSensitivePersons(res.data || []);
      } else {
        setSensitivePersons([]);
      }
    })
  }
  //获取敏感画面
  const getSensitiveImages = (type) => {
    let api = `get${type}Security`
    contentListApis[api]({ contentId: contentId_ }).then((res: any) => {
      if (res?.success) {
        setSensitiveImages(res.data || []);
      } else {
        setSensitiveImages([]);
      }
    })
  }
  useEffect(() => {
    if (keywords.length > 0 && voice.length > 0 && !changeFlag) {
      const temp = keywords.map((item: any) => {
        const obj = getVideoTime(item.name, voice);
        return {
          ...item,
          ...obj
        }
      })
      setKeywords(temp);
      setChangeFlag(true);
    }
  }, [voice, keywords])
  useEffect(() => {
    if (localStorage.params && shareFlag_!== 'share') {
      contentListApis.getContentids({ apiName: JSON.parse(localStorage.params).apiName }, JSON.parse(localStorage.params)).then(res => {
        if (res?.success) {
          setContentids(res?.data)
          setContentidNum(res?.data.indexOf(contentId_))
        }
      })
    }
    associateResource({contentId: contentId_, isSysAuth: true})
  }, [])
  const associateResource = (params: any) => {
    contentListApis.associatedResource(params).then(res => {
      if (res?.success) {
        setAssociatedResource(res?.data.results)
        setAssociatedResourceTotal(res.data.total)
      }
    })
  }
  const voicePosition = () => {
    const temp = JSON.parse(window.localStorage.getItem('searchKeyWord') || '{}');
    if (!temp?.voice) return;
    if (isVision) {
      setTimeout(() => {
        let domObj: any = document.getElementsByClassName('key-search-value')[0]?.parentElement?.parentElement?.parentElement?.parentElement?.parentElement?.parentElement;
        let timelineObj: any = document.getElementById('timeline');
        if (domObj) {
          timelineObj.scrollTo({
            top: domObj.offsetTop - 180,
            // behavior: 'smooth',
          });
        }
      });
    } else {
      setTimeout(() => {
        let domObj: any = document.getElementsByClassName('key-search-value')[0]?.parentElement?.parentElement;
        let timelineObj: any = document.getElementsByClassName('paragraph-content')[0];
        if (domObj) {
          timelineObj.scrollTo({
            top: domObj.offsetTop - 100,
            // behavior: 'smooth',
          });
        }
      });
    }
  }
  useEffect(() => {
    voicePosition();
  }, [isVision])
  useEffect(() => {
    const temp = JSON.parse(window.localStorage.getItem('searchKeyWord') || '{}');
    if (temp?.knowledege) {
      setActiveTabs('4');
      setTimeout(() => {
        const domObj: any = document.getElementsByClassName('knowledgeCheckGroup')[0];
        let curDomObj: any = document.getElementsByClassName('key-search-value')[0]?.parentElement?.parentElement?.parentElement?.parentElement?.parentElement?.parentElement?.parentElement;
        if (curDomObj) {
          domObj?.scrollTo({ top: curDomObj.offsetTop - 42, behavior: 'smooth', });
        }
      })
    }
    if (temp?.voice) {
      setActiveTabs('3');
      voicePosition();
    }
    if (temp?.basic) {
      setActiveTabs('1');
      setTimeout(() => {
        const domObj: any = document.getElementsByClassName('ant-tabs-tabpane-active')[0];
        let curDomObj: any = document.getElementsByClassName('tag_highlight')[0];  //如果是标签
        let curDomObj_: any = document.getElementsByClassName('describe_highlight')[0]; //如果是描述
        if (curDomObj_) {
          domObj?.scrollTo({ top: curDomObj_.offsetTop, behavior: 'smooth', });
        }
        if (curDomObj) {
          domObj?.scrollTo({ top: curDomObj.offsetTop, behavior: 'smooth', });
        }
      })
    }
  }, [window.localStorage.getItem('searchKeyWord'), sequencemeta])
  // 获取知识点列表
  const getQuerySequencemeta = (times?: number) => {
    SmartService.querySequencemetaNew(contentId_, shareFlag_ === 'share').then(res => {
      const result = res as any;
      if (result?.success && result.data?.length > 0) {
        if (times === 1) {
          const metadata = result.data;
          const curData = metadata[metadata.length - 1]
          setKnowKey(curData.guid_);
          setPlayerSec(curData.inpoint, curData.outpoint, curData);
          setActiveTabs('4');
          const domObj: any = document.querySelector('.sequence_list')?.parentElement;
          if (domObj?.scrollTo) {
            setTimeout(() => {
              const curDomObj: any = document.querySelector('.sequence_item_wrap.active');
              domObj.scrollTo({ top: curDomObj.offsetTop });
            }, 100);
          }
        }
        const arr = result.data.sort(sortFuc);
        //新增语音翻译功能标识
        if (keyframeFlag) {
          if (arr.every((item: any) => item.keyframepath)) { //如果每个知识点都有封面帧 循环结束
            clearInterval(timer);
            setKeyFrameFlag(false)
          }
        }
        const temp = JSON.parse(window.localStorage.getItem('searchKeyWord') || '{}');
        if (temp?.word) {
          arr?.map((item: any) => {
            searchKeywords(item.title, 'knowledege');
          })
        }
        setSequencemeta(arr);
        // 判断是否有inpoint
        if (history.location?.query.inpoint) {
          setActiveTabs('4');
        }
      } else {
        setSequencemeta([]);
      }
    });
  };
  const searchVoive = (
    e: KeyboardEvent<HTMLInputElement> | FocusEvent<HTMLInputElement> | any,
  ) => {
    e.preventDefault();
    let name = e.target.value;
    let Index: number[] = [];
    voice.forEach((item, index) => {
      if (item.text?.indexOf(name) > -1) {
        Index.push(index);
      }
    });
    setSearchCurrent(0);
    setSearchIndex(Index);
    setSearchVoiceWord(name);
  };
  const changeCurrent = (direction: string) => {
    let current = searchCurrent;
    if (direction === 'up') {
      current = current - 1;
      if (current < 0) {
        current = searchIndex.length - 1;
      }
      setSearchCurrent(current);
    } else {
      current = current + 1;
      if (current === searchIndex.length) {
        current = 0;
      }
      setSearchCurrent(current);
    }
  };

  const onSelection = () => {
    const selection = window.getSelection();
    if (!selection?.isCollapsed) {
      const baseNode = parseInt(
        selection?.anchorNode?.parentElement?.parentElement?.dataset.id || '',
      );
      const focusNode = parseInt(
        selection?.focusNode?.parentElement?.parentElement?.dataset.id || '',
      );
      if (baseNode - focusNode > 0) {
        setStartSpan(focusNode);
        setEndSpan(baseNode);
        doPop(
          selection?.focusNode?.parentElement?.parentElement?.offsetTop || 0,
          selection?.focusNode?.parentElement?.parentElement?.offsetLeft || 0,
        );
      } else {
        setStartSpan(baseNode);
        setEndSpan(focusNode);
        doPop(
          selection?.anchorNode?.parentElement?.parentElement?.offsetTop || 0,
          selection?.anchorNode?.parentElement?.parentElement?.offsetLeft || 0,
        );
      }
    } else {
      setBtnStyle({
        display: 'none',
      });
    }
  };
  const permiseifshow = () => {
    if (
      rmanGlobalParameter.includes(globalParams.speech_analysis_display) ||
      rmanGlobalParameter.includes(globalParams.knowledge_analysis_display) ||
      rmanGlobalParameter.includes(globalParams.sensitiveface_analysis_display) ||
      rmanGlobalParameter.includes(globalParams.smart_tag_display)
    ) {
      //2022-2-24 统一了规则：如果模块配置有其一则显示智能按钮 但如果没有智能分析权限 则按钮置灰
      return true
    }
    return false;
  };

  useEffect(() => {
    if (searchIndex.length) {
      if (isVision) {
        setTimeout(() => {
          let domObj: any = document.getElementsByClassName(
            'mao' + searchIndex[searchCurrent],
          )[0];
          let timelineObj: any = document.getElementById('timeline');
          if (domObj) {
            timelineObj.scrollTo({
              top: domObj.offsetTop - 180,
              behavior: 'smooth',
            });
          }
        }, 500);
      } else {
        setTimeout(() => {
          let domObj: any = document.getElementsByClassName(
            'mao_' + searchIndex[searchCurrent],
          )[0];
          let timelineObj: any = document.getElementsByClassName('paragraph-content')[0];
          if (domObj) {
            timelineObj.scrollTo({
              top: domObj.offsetTop - 240,
              behavior: 'smooth',
            });
          }
        }, 500);
      }
    }
  }, [searchVoiceWord, searchCurrent]);

  const Redovoic = async () => {
    confirm({
      title: `${intl.formatMessage({ id: '系统提示' })}`,
      content: `${intl.formatMessage({ id: '确认对当前文件，重新执行语音分析' })}？`,
      onOk: async () => {
        setVoiceStatus('1');
        const res = await SmartService.sendFlow([
          {
            contentId: contentId_,
            type: 'asr',
          },
        ]);
        if (res?.success) {
          message.success(`${intl.formatMessage({ id: '语音分析任务发起成功' })}！`);
          refreshTab(false, true, false);
        } else {
          message.success(`${intl.formatMessage({ id: '语音分析任务发起失败' })}！`);
        }
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  };
  const Redoknowledge = async () => {
    confirm({
      title: `${intl.formatMessage({ id: '系统提示' })}`,
      content: `${intl.formatMessage({ id: '确认对当前文件，重新执行知识点分析' })}？`,
      onOk: async () => {
        setKnowledgeStatus('1');
        const res = await SmartService.sendFlow([
          {
            contentId: contentId_,
            type: 'ocr',
          },
        ]);
        if (res?.success) {
          message.success(`${intl.formatMessage({ id: '知识点分析任务发起成功' })}！`);
          refreshTab(false, false, true);
        } else {
          message.success(`${intl.formatMessage({ id: '知识点分析任务发起失败' })}！`);
        }
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  };
  // 路径转译
  const getDirTeeStr = (dirTree?: string): string => {
    if (!dirTree) {
      return ''
    }
    if (dirTree.includes('global_sobey_defaultclass/public')) {
      return dirTree.replace('global_sobey_defaultclass/public', '公共资源').replace('/', '\u00A0>\u00A0') + '\u00A0>\u00A0';
    } else if (dirTree.includes('global_sobey_defaultclass/private')) {
      let newdir = dirTree.replace(
        'global_sobey_defaultclass/private',
        '个人资源',
      );
      if (newdir.includes('/')) {
        let alldir = newdir.split('/');
        if (alldir.length >= 2) {
          newdir = newdir.replace('/' + alldir[1], '');
        }
      }
      return newdir.replace('/', ' > ') + `\u00A0>\u00A0`;
    } else {
      return dirTree.replace('/', ' > ') + '\u00A0>\u00A0';
    }
  };
  // 路径转译
  const getDirTeeStr1 = (dirTree?: string): string => {
    if (!dirTree) {
      return ''
    }
    if (dirTree.includes('global_sobey_defaultclass/public')) {
      return dirTree.replace('global_sobey_defaultclass/public', '').replace('/', '');
    } else if (dirTree.includes('global_sobey_defaultclass/private')) {
      let newdir = dirTree.replace(
        'global_sobey_defaultclass/private',
        '个人资源',
      );
      if (newdir.includes('/')) {
        let alldir = newdir.split('/');
        if (alldir.length >= 2) {
          newdir = newdir.replace('/' + alldir[1], '');
        }
      }
      return newdir;
    } else {
      return dirTree;
    }
  };
  //添加我的收藏
  const addMyCollection = debounce(async () => {
    contentListApis.addmycollectionlist(contentId_)
      .then(async (res: any) => {
        if (res.success) {
          message.success(`${intl.formatMessage({ id: '收藏成功' })}`);
          setContentDetail({
            ...contentDetail,
            isCollection: true
          })
          setCounter({
            ...counter,
            collect: res.data
          })
        } else {
          message.error(`${intl.formatMessage({ id: '收藏失败！' })}`);
        }
      })
  }, 300)
  //取消收藏
  const deleteMyCollection = async () => {
    console.log(contentDetail)
    contentListApis.deletemycollectionlist([contentId_])
      .then(async (res: any) => {
        if (res.success) {
          message.success(`${intl.formatMessage({ id: '取消收藏成功' })}`);
          setContentDetail({
            ...contentDetail,
            isCollection: false
          })
          setCounter({
            ...counter,
            collect: Object.values(res.data)[0]
          })
        } else {
          message.error(`${intl.formatMessage({ id: '取消收藏失败' })}`);
        }
      })
  }
  const handleAddTopicConfirm = (data: any, isAdd?: boolean) => {
    if (questionItem.uniqueId) {
      contentListApis.replaceQuestion({
        uniqueId: questionItem.uniqueId,
        versionName: versionCode,
        courseName: "",
        pointIn: parseInt((currentFrame / 10000000).toString()) * 10000000,
        questionId: +data.map((item: any) => +`${item.id}`),
        resourceId: contentId_,
        sourceId: ''
      }).then(_res => {
        message.success(`${intl.formatMessage({ id: '替换成功' })}`);
        getQuestionList()
        getQuestionPoint()
        setTopicVisible(false);
      })
    }
    else {
      contentListApis.addQuesion({
        versionName: versionCode,
        courseName: "",
        pointIn: parseInt((currentFrame / 10000000).toString()) * 10000000,
        questionIds: data.map((item: any) => +`${item.id}`),
        resourceId: contentId_,
        sourceId: ''
      }).then(_res => {
        message.success(`${intl.formatMessage({ id: '添加成功' })}`);
        getQuestionList()
        getQuestionPoint()
        setTopicVisible(false);
      })
    }
  };
  // 发微课
  const createMicroHandle = (knowledgeResources?: any) => {
    if (!permissions.includes(perCfg.microcourse_new) || entity?.type !== 'video') return
    console.info(metadata)
    const dataMap: any = {}
    metadata.forEach((item: any) => {
      dataMap[item.fieldName] = item.value
    })
    const data = {
      pageIndex: 1,
      pageSize: 10,
      conditions: [{
        field: "contentId_",
        searchRelation: 0,
        value: [contentId_]
      }]
    }
    entityApis.getDetail(data).then((res: any) => {
      if (res.success) {
        let courseResources = res.data?.data
        let videoDuration = ""
        if (courseResources?.length > 0) {
          courseResources = [{
            ...courseResources[0],
            contentId: courseResources[0].contentId_,
            name: courseResources[0].name_
          }]
          videoDuration = courseResources[0].duration?.toString() || ""
        }
        entityApis.getCoverList().then((res: any) => {
          if (res.status == 200) {
            const data = res.data
            if (data.length > 0) {
              const param: any = {
                contentId: '',
                name: knowledgeResources ? knowledgeResources.title : dataMap.name,
                entityData: {
                  contentId: "",
                  courseType: 0,
                  publishStatus: 0,
                  classification: [],
                  top: 0,
                  describe: "",
                  teacher: dataMap.createUser_ ? [dataMap.createUser_] : [],
                  school: "",
                  college: JSON.parse(dataMap.college || "[]"),
                  subject: [],
                  is_attachment_ownload: 1,
                  major: JSON.parse(dataMap.major || "[]"),
                  cover: data.map((item: any) => item.url)[Math.floor(Math.random() * data.length)],
                  name_: knowledgeResources ? knowledgeResources.title : dataMap.name,
                  check: 0,
                  teacher_names: dataMap.importuser ? [dataMap.importuser] : [],
                  collegeName: [],
                  majorName: [],
                  subjectName: [],
                  classificationName: [],
                  videoDuration,
                  courseResources,
                },
                fileGroups: [
                  {
                    groupType: "other",
                    status: "ready",
                    groupName: "attachmentgroup",
                    fileItems: [],
                  },
                ],
              }
              if (knowledgeResources) {
                param.entityData.knowledgeResources = [{
                  ...knowledgeResources,
                  videoId: courseResources?.[0].contentId_
                }];
              }
              entityApis.saveCourse(param).then((res: any) => {
                if (res.message === 'OK') {
                  entityApis.resourceOptionsCount(contentId_, 'micr').then((res: any) => {
                    if (res.success) {
                      setCounter({
                        ...counter,
                        micro: res.data[contentId_]
                      })
                    }
                  })
                  window.open(`/learn/workbench/#/course/resource?${res.data}`);
                } else {
                  message.error(res.message)
                }
              });
            }
          }
        })

      } else {
        message.error(`${intl.formatMessage({ id: '获取失败' })}`);

      }
    })
  }
  const languageChange = (e: any) => {
    setCurrentLanguage(e);
  }
  const fetchTextclipList = async () => {
    let result: any = [];
    const res: any = await contentListApis.getTextclipLists({
      contentId: contentId_,
      pageIndex: 1,
      pageSize: 10 //当前仅展示前十个
    });
    console.log(res);
    if (res?.statusCode === 200) {
      result = res.data.items;
    } else {
      message.error(`${intl.formatMessage({ id: '任务列表接口错误' })}`);
    }
    return result;
  }
  const newTextClip = async () => {
    //需要创建任务
    message.loading(`${intl.formatMessage({ id: '正在创建语音任务...请稍等' })}`);
    const res: any = await contentListApis.createTextClip({
      contentId: contentId_,
      keyFrames: entity?.keyframes,
      taskName: title
    });
    if (res?.statusCode === 200) {
      if (res.data) {
        window.location.href = `${window.location.origin}/textclip/#/clip/textClipDetail/${res.data}`;
      } else {
        message.error(res.message);
      }
    } else {
      message.error(`${intl.formatMessage({ id: '服务器错误' })}`);
    }
  }
  const goTextClip = async () => {
    // 创建任务前 先查询当前素材有无语音任务
    const list: any = await fetchTextclipList();
    if (list.length > 0) {
      //打开列表对话框
      setsetTextclipLists(list);
      setTextclipVisible(true);
    } else {
      newTextClip();
    }
  };
  const copyAndMove = (item: number) => {
    setOperatType(item);
    setCopyAndMoveModalVisible(true);
  };
  const handleAddTopic = () => {
    setTopicVisible(true);
  };
  const clipBtnList: any = [
    {
      key: 'joveone',
      name: `${intl.formatMessage({ id: '在线剪辑' })}`,
      icon: <IconFont type='iconzaixianbianji' />,
      func: () => {
        entityApis.joveoneLog(contentId_, contentDetail.entityName)
        window.open(`/joveone/?contentid=${contentId_}&siteCode=S1`)
      },
      target: "joveone",
      disabled:
        !modules.includes(ModuleCfg.jove) || !joveonetype.includes(entity?.type),
    },
    {
      key: 'textclip',
      name: `${intl.formatMessage({ id: '语音剪辑' })}`,
      icon: <IconFont type='iconyuyinbianji' />,
      func: () => goTextClip(),
      target: "textclip",
      disabled: (!modules.includes(ModuleCfg.textclip) || voice.length === 0),
    }
  ]
  const menu = (
    <Menu>
      {clipBtnList
        .filter((l: any) => !l.disabled)
        .map((item: any, index: number) => {
          return < Menu.Item key={item.key}>
            <a className='other' onClick={item.func} target={item.target}>{(item.icon)}{item.name}</a>
          </Menu.Item>
        }
        )}
    </Menu>
  )
  const items: MenuProps['items'] = [
    {
      key: '1',
      label: (
        <a rel="noopener noreferrer" onClick={() => voiceFileDownload('txt')}>
          {intl.formatMessage({ id: '下载txt格式' })}
        </a>
      ),
    },
    {
      key: '2',
      label: (
        <a rel="noopener noreferrer" onClick={() => voiceFileDownload('srt')}>
          {intl.formatMessage({ id: '下载srt格式' })}
        </a>
      ),
    },
  ]
  const searchVoice = (tit: any) => {
    const temp = searchKeywords(tit, 'voice');
    if (!searchVoiceWord && temp.index > -1) {
      return <span>
        {temp.beforeStr}
        <span className="key-search-value">{temp.word}</span>
        {temp.afterStr}
      </span>
    }
    if (searchVoiceWord) { //在检索时 需要置空
      window.localStorage.setItem('searchKeyWord', '{}');
    }
    const index = tit?.indexOf(searchVoiceWord);
    const beforeStr = tit?.substr(0, index);
    const afterStr = tit?.substr(index + searchVoiceWord.length);
    const title =
      index > -1 ? (
        <span>
          {beforeStr}
          <span className="voice-search-value">{searchVoiceWord}</span>
          {afterStr}
        </span>
      ) : (
        <span>{tit}</span>
      );
    return title;
    // setVoiceTitle(title)
  };
  // 点赞 为true 点赞 否则取消点赞
  const resourceLikeChange = (flag: boolean) => {
    if (flag) {
      entityApis.resourceLike(contentId_).then((res: any) => {
        if (res.success) {
          message.success(`${intl.formatMessage({ id: '点赞成功' })}`);
          setContentDetail({
            ...contentDetail,
            isLike: true
          })
          setCounter({
            ...counter,
            like: res.data
          })
        } else {
          message.error(`${intl.formatMessage({ id: '点赞失败' })}`);
        }
      })
    } else {
      entityApis.resourceCancleLike(contentId_).then((res: any) => {
        if (res.success) {
          message.success(`${intl.formatMessage({ id: '取消点赞成功' })}`);
          setContentDetail({
            ...contentDetail,
            isLike: false
          })
          setCounter({
            ...counter,
            like: res.data
          })
        } else {
          message.error(`${intl.formatMessage({ id: '取消点赞失败' })}`);
        }
      })
    }
  }
  //动态建立video_btn
  let video_btn: any = [];
  if ((rmanGlobalParameter.includes(globalParams.resource_like_display))) {
    if (contentDetail.isLike) { //已点赞
      video_btn.push({
        func: () => resourceLikeChange(false),
        align: 'left',
        title: `${intl.formatMessage({ id: '已点赞' })}`,
        dom: <>
          <IconFont className='favoritedBtn' type="icona-xingzhuang11"></IconFont>
          <span>{counter.like}</span>
        </>
      })
    } else {
      video_btn.push({
        func: () => resourceLikeChange(true),
        align: 'left',
        title: `${intl.formatMessage({ id: '点赞' })}`,
        dom: <>
          <IconFont className='canclefavoriteBtn' type="icona-xingzhuang11"></IconFont>
          <span>{counter.like || 0}</span>
        </>
      })
    }
  }
  if ((rmanGlobalParameter.includes(globalParams.my_collection_display))) {
    if (!contentDetail.isCollection) {
      video_btn.push({
        func: () => addMyCollection(),
        align: 'left',
        title: `${intl.formatMessage({ id: '收藏' })}`,
        dom: <>
          <IconFont
            className='canclefavoriteBtn'
            type='iconlujing'
          />

          <span>{counter.collect}</span>
        </>
      })
    } else {
      video_btn.push({
        func: () => {
          Modal.confirm({
            title: `${intl.formatMessage({ id: '确定取消收藏吗' })}`,
            onOk: deleteMyCollection
          })
        },
        title: `${intl.formatMessage({ id: '取消收藏' })}`,
        align: 'left',
        dom: <>
          <IconFont
            className='favoritedBtn'
            type='iconlujing'
          />
          <span>{counter.collect}</span>
        </>
      })
    }
  }
  if ((!ispublic || permissions.includes(perCfg.resource_download)) && !contentDetail.onlyRead) {
    video_btn.push({
      func: () => setDownloadModalVisible(true),
      align: 'left',
      title: `${intl.formatMessage({ id: '下载' })}`,
      dom: <>
        <IconFont
          className='canclefavoriteBtn'
          type='iconxingzhuang4'
        />
        <span>{counter.download}</span>
      </>
    })
  }
  if (rmanGlobalParameter.includes(globalParams.my_shared_display)) {
    video_btn.push({
      func: () => setShareVisible(true),
      align: 'left',
      title: `${intl.formatMessage({ id: '分享' })}`,
      dom: <>
        <IconFont
          className='canclefavoriteBtn'
          type='iconlujing2'
        />
        <span>{counter.share}</span>
      </>
    })
  }
  if (permissions.includes(perCfg.resource_copy)) {
    video_btn.push({
      func: () => copyAndMove(0),
      align: 'left',
      title: `${intl.formatMessage({ id: '复制' })}`,
      dom: <>
        <IconFont
          className='canclefavoriteBtn'
          type='iconxingzhuangjiehe1'
        />
        <span>{counter.copy}</span>
      </>
    })
  }
  if (permissions.includes(perCfg.microcourse_new)
    && operateCode(contentDetail?.operateCode_).includes(8)
    && !mobileFlag
    && entity?.type === 'video') {
    video_btn.push({
      func: () => createMicroHandle(),
      title: `${intl.formatMessage({ id: '发微课' })}`,
      align: 'left',
      dom: <>
        <IconFont
          className='canclefavoriteBtn'
          type='iconlujing1'
        />
        <span>{counter.micro}</span>
      </>
    })
  }
  if (rmanGlobalParameter.includes(globalParams.knowledge_map_display)
    && operateCode(contentDetail?.operateCode_).includes(8)
    && !mobileFlag
    && hidecatalogue !== '1') {
    video_btn.push({
      func: () => setBindKnowledgeModalVisible(true),
      align: 'left',
      title: `${intl.formatMessage({ id: '知识点绑定' })}`,
      dom: <>
        <IconFont
          className='canclefavoriteBtn'
          type='iconlujing3'
        />
        <span>{counter.knowledge}</span>
      </>
    })
  }
  //仅在个人资源中才能共享
  if (
    permissions.includes(perCfg.resource_share) &&
    contentDetail?.path?.includes("global_sobey_defaultclass/private") &&
    [
      'biz_sobey_video',
      'biz_sobey_picture',
      'biz_sobey_document',
      'biz_sobey_audio',
    ].includes(contentDetail.type) &&
    window.localStorage.getItem('upform_platform') !== 'Lark'
  ) {
    video_btn.push({
      title: `${intl.formatMessage({ id: '共享' })}`,
      align: 'left',
      func: () => copyAndMove(2),
      dom: (
        <>
          <IconFont
            className='canclefavoriteBtn'
            type='icona-xingzhuang12'
          />
        </>
      ),
    });
  }
  if ((dirTree.includes('共享资源') || dirTree.includes('群组资源')) && rmanGlobalParameter.includes(globalParams.comment_display)) {
    video_btn.push({
      title: `${intl.formatMessage({ id: '评论' })}`,
      align: 'left',
      func: () => setcommentModalVisible(true),
      dom: (
        <>
          <IconFont
            className='canclefavoriteBtn'
            type='iconxingzhuang5'
          />
          <span>{commentTotal}</span>
        </>
      ),
    });
  }

  if (permiseifshow()
    && ((ispublic ? permissions.includes(perCfg.resource_analysis) : true)
      && operateCode(contentDetail?.operateCode_).includes(8)
      && (entity?.type === 'video' || entity?.type === 'audio' || entity?.type === 'picture' || entity?.type === 'document'))
    && !(hidecatalogue === '1')) {
    video_btn.push({
      func: () => setIntelligentAnalysisModalVisible(true),
      align: mobileFlag ? 'left' : 'right',
      dom: <>
        <img src={require('@/images/contentlibrary/zhineng.png')} />
        <span>{intl.formatMessage({ id: '智能分析' })}</span>
      </>
    })
  }
  if (!(hidecatalogue === '1') && !mobileFlag &&
    operateCode(contentDetail?.operateCode_).includes(8) &&
    clipBtnList.filter((l: any) => !l.disabled).length > 0) {
    video_btn.push({
      func: null,
      align: 'right',
      dom: <>
        <img src={require('@/images/contentlibrary/clip.png')} />
        <span>{intl.formatMessage({ id: '剪辑' })}</span>
      </>
    })
  }
  if (product.length > 0 && !mobileFlag) {
    video_btn.push({
      func: () => setRelationVideoVisible(!relationVideoVisible),
      align: 'right',
      dom: <>
        <DoubleRightOutlined className={relationVideoVisible ? 'open' : 'close'} />
        <span>{intl.formatMessage({ id: '相关资源' })}</span>
      </>
    })
  }
  //词云关键词点击事件
  const wordCloudclick = {
    'click': (params: any) => {
      console.log(params.data);
      jumpVideoTime(params.data._in);
    },
  }
  const chooseNext = () => {
    setQuestionItem(questionPointList[++idx.current])
  }
  const setIdx = (value: number) => {
    idx.current = value
  }
  const jumpVideoTime = (item: any, temp?: any) => {
    if (typeof (item) === 'undefined') return;
    if (temp) { //敏感词
      console.log('敏感词', temp)
      setPointKey(item);
      player.current?.setCurrentTime(win.TimeCodeConvert.l100Ns2Second(item.sensitiveWordGroups[temp.index].point_in || item.sensitiveWordGroups[temp.index].inpoint));
    } else {
      player.current?.setCurrentTime(win.TimeCodeConvert.l100Ns2Second(item));
    }
  }
  //敏感人物框选效果
  const drawPlot = (item: any, temp: any, remove?: boolean) => {
    const preNode = document.querySelector('.video_detail_wrapper .content .entity_view .entity_view_wrapper .entity-img .position_');
    if (remove) {
      preNode?.remove();
      return;
    }
    if (temp.index == -1) return;
    //获取图片父节点宽高 找相对原点位置
    let origin = {
      left: 0,
      top: 0
    }
    const imgDiv = document.querySelector('.video_detail_wrapper .content .entity_view .entity_view_wrapper .entity-img');
    const img = imgDiv?.firstElementChild;
    origin.left = ((imgDiv?.clientWidth || 0) - (img?.clientWidth || 0)) / 2
    origin.top = ((imgDiv?.clientHeight || 0) - (img?.clientHeight || 0)) / 2
    const location_ = item.sensitiveWordGroups[temp.index].location || item.sensitiveWordGroups[temp.index]; //兼容有些没有这个字段
    if (preNode) {
      preNode.remove();
    };
    const temp_: any = {
      left: (img?.clientWidth || 0) * location_.left_ratio,
      top: (img?.clientWidth || 0) * location_.top_ratio,
      width: (img?.clientWidth || 0) * location_.width * location_.left_ratio / location_.left,
      height: (img?.clientWidth || 0) * location_.height * location_.left_ratio / location_.left,
    }
    const element: any = $(`<div class='position_'
                            style='left:${origin.left}px; top:${origin.top}px;'>
                            <div class='rect'
                              style='left:${temp_.left}px;
                              top:${temp_.top}px;
                              width:${temp_.width}px;
                              height:${temp_.height}px;'
                              ></div>
                          </div>`);
    imgDiv?.appendChild(element[0]);
  }

  //更新封面
  const updateCover = async (item: any) => {
    let data = {
      fileGUID: item.fileGUID,
      fileFormat: {
        keyFrameFormat: {
          keyFrameNo: item.keyFrameNo,
          iconKeyframe: item.iconKeyframe == 0 ? 1 : 0 //1表示当前是封面 点击则取消
        }
      }
    }
    const res: any = await entityApis.updateKeyframes(contentId_, data);
    if (res.data && res.success) {
      message.success(`${intl.formatMessage({ id: '封面更新成功' })}`);
      <span></span>

      setTimeout(() => {
        getCourseware();
      }, 200)
    } else {
      message.error(`${intl.formatMessage({ id: '封面设置失败' })}`);
    }
  }
  //语音文本下载
  const voiceFileDownload = async (type: string) => {
    const res: any = await SmartService.resourceCaptionDownload(contentId_, contentDetail.entityName, true);
    if (res.success) {
      const a = document.createElement('a');
      a.href = res.data[type];
      a.download = contentDetail.entityName + '.' + type;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      message.success(`${intl.formatMessage({ id: '下载成功' })}`);
    } else {
      message.error(`${intl.formatMessage({ id: '下载出错了' })}`);
    }
  }

  //语音文本检查
  const voiceTextCheck = async () => {
    const res: any = await SmartService.voiceTextCheck(contentId_, true);
    if (res.success) {
      message.success(`${intl.formatMessage({ id: '检查成功' })}`);
      setVoiceCheck(true);
    } else {
      message.success(`${intl.formatMessage({ id: '检查失败' })}`);
    }
  }

  const addVersion = () => {
    getSensitiveWord(versionForm.getFieldsValue().title, `${intl.formatMessage({ id: '版本名' })}`, () => {
      contentListApis.addQuesionVersion({
        resourceId: contentId_,
        versionName: versionForm.getFieldsValue().title
      }).then(_res => {
        if (_res?.success) {
          getQuestionVersion()
          setPage(1)
          setQvModalVisible(false)
          versionForm.resetFields();
        }
      })
    })
  }

  const editVersion = () => {
    getSensitiveWord(versionForm.getFieldsValue().title, `${intl.formatMessage({ id: '版本名' })}`, () => {
      contentListApis.editQuesionVersion({
        versionName: versionCode,
        newVersionName: versionForm.getFieldsValue().title,
        resourceId: contentId_,
      }).then(_res => {
        if (_res?.success) {
          getQuestionVersion()
          setPage(1)
          setQvModalVisible(false)
          versionForm.resetFields();
        }
      })
    })
  }
  const getValue = (value: string) => {
    let res: string[] = [];
    try {
      const arr = Array.isArray(value) ? value : JSON.parse(value);
      if (Array.isArray(arr)) {
        res = arr;
      } else {
        res = [arr.toString()];
      }
    } catch (e) {
      console.error(e);
    }
    return res;
  }
  // 转gif
  const importGif = () => {
    console.log(player?.current)
    contentListApis.importGif([selectedKey], { contentId: contentId_, inPoint: parseInt((player?.current?.trimin * 10000000).toString()), outPoint: parseInt((player?.current?.trimout * 10000000).toString()) }).then(res => {
      if (res?.success) {
        message.success(`${intl.formatMessage({ id: '保存成功' })}`);
        setGifModalVisible(false)
      }
    })
  }
  const deleteVersion = () => {
    contentListApis.deleteQuesionVersion({
      versionName: versionCode,
      resourceId: contentId_,
    }).then(_res => {
      if (_res?.success) {
        getQuestionVersion()
        setPage(1)
      }
    })
  }
  const reply = (key: string) => {
    SmartService.sendFlow_new([{
      contentId: contentId_,
      asr: false,
      smartTag: false,
      translate: false,
      knowledge: false,
      face: false,
      word: false,
      summary: false,
      PptScreen: false,
      pdfAnalysis: false,
      videoQualityCheck: false,
      detectingCheck: false,
      [key]: true
    }]).then(res => {
      if (res?.success) {
        message.success(`${intl.formatMessage({ id: '发起成功' })}`);
      }
    })
  }
  useEffect(() => {
    if ((window as any).login_useInfo) {
      // console.log('myVideo',props.myVideo)
      // 判断当前用户是否有删除的权限
      const userInfo = (window as any).login_useInfo
      if (userInfo) {
        const roless = userInfo.roles?.map((item: any) => item.roleCode)
        // console.log('juese', roless)
        // 当前用户角色为zhiliao、sys和资源管理员
        if (roless?.includes('admin_S1') || roless?.includes('r_sys_manager') || roless?.includes('r_resource_manager')) {
          setPermission(true)
        } else {
          setPermission(false)
        }
      }
    }
  }, [(window as any).login_useInfo])
  const editBasicMetadata = (data: any, edit: boolean) => {
    let copyData = copyObject(metadata)
    copyData.forEach(item => {
      data.forEach(item_ => {
        if (item.id === item_.id) {
          item.edit = edit
        }
      })
    })
    setMetadata(copyData)
  }
  const loopClick = (item: any, key: string) => {
    const temp = preSelect.current;
    if (temp.name == '' && temp.index == -1) {
      temp.name = item[key];
      temp.index = 0;
    } else if (item[key] == temp.name) {
      if (temp.index < item.sensitiveWordGroups.length - 1) {
        temp.index++;
      } else {
        temp.index = -1;
      }
    } else {
      temp.index = 0
      temp.name = item[key];
    }
    preSelect.current = temp;
    if (contentDetail?.type == 'biz_sobey_video') {
      if (temp.index == -1) {
        jumpVideoTime?.(0); //重置到起点
      } else {
        jumpVideoTime?.(item, temp);
      }
    } else if (contentDetail?.type == 'biz_sobey_picture') {
      //图片的话 需要定位框起来 
      if (temp.index == -1) {
        drawPlot?.(item, temp, true);//移除
        window.removeEventListener('resize', function () {
          drawPlot?.(item, temp);
        });
      } else {
        drawPlot?.(item, temp);
        window.addEventListener('resize', function () {
          drawPlot?.(item, temp);
        })
      }
    }
    return temp
  }
  useEffect(() => {
    if (metadata.length) {
      initValues(metadata)
    }
  }, [JSON.stringify(metadata)])
  const getValues = (values: any) => {
    const newItems = copyObject(metadata);
    newItems.forEach(item => {
      if (item.controlType === 8 || item.controlType === 12) {
        // 判断是不是多选
        // if(item.isMultiSelect && item.fieldName == 'teacher'){
        //   item.value = values[item.fieldName]; //.map((item:any)=>item.key)
        // }else{

        item.value = values[item.fieldName] ? Array.isArray(values[item.fieldName])
          ? values[item.fieldName]
          : [values[item.fieldName]] : JSON.parse(item.value)
        // }
      } else if (item.controlType === 1) {
        item.value = values[item.fieldName] ? values[item.fieldName].format('YYYY-MM-DD HH:mm:ss') : item.value
      } else if (item.controlType === 14) {
        item.value = values[item.fieldName] ? Array.isArray(values[item.fieldName])
          ? values[item.fieldName]
          : [values[item.fieldName]] : JSON.parse(item.value)
      } else {
        item.value = values[item.fieldName] ? values[item.fieldName] : item.value
      }
    });
    // debugger
    return newItems;
  };

  return (
    <div style={{ height: '100%' }}>
      {showAddTopic ? <iframe src={`/exam/#/topic/manage?opt_type=new&type=${topicType}&from=out`} height="100%" width="100%" /> :
        <div className={`video_detail_wrapper${mobileFlag ? ' mobile_detail' : ''}`}>
          {
            errorCode ?
              <div className='errorContent'><Error errorCode={errorCode} /></div> : <>
                <div className="content">
                  <div className="entity_view">
                    {showArrow && contentidNum > 0 && <IconFont onClick={debounce(() => {
                      setContentidNum(contentidNum - 1)
                      const dev = process.env.NODE_ENV === 'development' ? "" : "/rman"
                      window.location.href = `${window.location.origin}${dev}/#/basic/rmanDetailV2/${contentids[contentidNum - 1]}`
                      location.reload()
                    }, 500)} type="iconleftarrow" className="left-arrow arrow" />}
                    {showArrow && contentidNum < contentids.length - 1 && <IconFont onClick={debounce(() => {
                      setContentidNum(contentidNum + 1)
                      const dev = process.env.NODE_ENV === 'development' ? "" : "/rman"
                      window.location.href = `${window.location.origin}${dev}/#/basic/rmanDetailV2/${contentids[contentidNum + 1]}`
                      location.reload()
                    }, 500)} type="iconrightarrow" className="right-arrow arrow" />}
                    <div className="title">
                      <div className="title_info">
                        {/* <div className="dic_tree" title={getDirTeeStr(dirTree) + title}>
                          <span>{getDirTeeStr(dirTree)}</span>
                          <span>{title}</span>
                        </div> */}
                        <div className="dic_tree" title={title}>
                          <span>{title}</span>
                        </div>
                      </div>
                    </div>
                    {entity && isTransCode ? (
                      <div className={`entity_view_wrapper ${entity.path.includes('.html') ? 'whiteBg' : ''}`}>
                        <Entity
                          id={'h5Player'}
                          type={entity.type}
                          src={entity.path}
                          frameRate={frameRate}
                          keyframes={entity.keyframes}
                          isEditPermission={
                            permissions.includes(perCfg.resource_edit) && canEdit
                          }
                          contrast={contrast*2}
                          brightness={brightness*2}
                          saturate={saturate*2}
                          blur={blur}
                          grayscale={grayscale}
                          invert={invert}
                          opacity={opacity}
                          sepia={sepia}
                          hueRotate={hueRotate}
                          errorCode={errorCode}
                          voice={voice}
                          contendId={contentId_}
                          onSuccess={handleSuccess}
                          shareFlag={shareFlag_ === 'share'}
                          setSection={handleSection}
                          translateFlag={translateFlag}
                          languageList={languageList}
                          substitles={substitles}
                          key={translateFlag as any}
                          getKeyframe={handleKeyframe}
                          onPlayChange={handlePlayChange}
                          modifySection={handleModifySection}
                          resetSection={handleResetSection}
                        />
                        {
                          !mobileFlag &&contentDetail.type === 'biz_sobey_video' && target !== 'custom' &&<div  onClick={() => {
                            setControlBox(!controlBox)
                          }}  className='controls-box'>
                          <IconFont type="icontiaojie" className="adjust" title='调节' />
                            {controlBox && <div className='controls' onClick={(e) => e.stopPropagation()}>
                              <div className='type_box'>
                                <div onClick={() =>{
                                  setFilter()
                                  setSaturate(100)
                                }}>清晰</div>
                                <div onClick={() =>{
                                  setFilter()
                                  setContrast(100)
                                }}>鲜艳</div>
                                <div onClick={() =>{
                                  {
                                    setFilter()
                                    setBrightness(100)
                                  }
                                }}>明亮</div>
                              </div>
                              <div className='contrao_param' onClick={(e) => e.stopPropagation()}>
                                <div  className='brightness'  >
                                  <Slider vertical value={brightness}  onChange={(e) => setBrightness(e)} />
                                  <div>亮度</div>
                                </div>
                                <div  className='contrast'  >
                                  <Slider vertical value={contrast} onChange={(e) => setContrast(e)} />
                                  <div>对比度</div>
                                </div>
                                <div  className='saturate'  >
                                  <Slider vertical value={saturate} onChange={(e) => setSaturate(e)} />
                                  <div>饱和度</div>
                                </div>
                                <div   className='blur' >
                                  <Slider vertical value={blur} onChange={(e) => setBlur(e)} />
                                  <div>模糊度</div>
                                </div>
                                <div   className='grayscale'  >
                                  <Slider vertical value={grayscale} onChange={(e) => setGrayscale(e)} />
                                  <div>灰度</div>
                                </div>
                                <div className='invert'  >
                                  <Slider vertical value={invert} onChange={(e) => setInvert(e)} />
                                  <div>反色</div>
                                </div>
                                <div  className='opacity'  >
                                  <Slider vertical value={opacity} onChange={(e) => setOpacity(e)} />
                                  <div>透明度</div>
                                </div>
                                <div   className='sepia'  >
                                  <Slider vertical value={sepia} onChange={(e) => setSepia(e)} />
                                  <div>棕褐色</div>
                                </div>
                                <div   className='hueRotate'  >
                                  <Slider vertical value={hueRotate} onChange={(e) => setHueRotate(e)} />
                                  <div>色相</div>
                                </div>
                              </div>
                              
                          </div>}
                        </div>
                        }
                        {
                          rmanGlobalParameter.includes(globalParams.generate_gif) && contentDetail.type === 'biz_sobey_video' && permissions.includes(perCfg.resource_edit) && canEdit && !mobileFlag && shareFlag_ !== 'share' && <div className='gif' onClick={() => {
                            if (player?.current?.trimin === 0 && player?.current?.trimout === 0) return
                            setGifModalVisible(true)
                          }}>
                            <IconFont type="icongif" className="icon" title='GIF' />
                            { }
                          </div>
                        }
                      </div>
                    ) : (
                      <div className='entity_view_wrapper'>
                        <div className="entity-error">
                          <img src={require('../../../images/contentlibrary/zmz.png')} />
                        </div>
                      </div>
                    )}
                    {
                      isaudit ? <Auditrousece instanceId={instanceId} showBtn={showBtn} contentId_={contentId_} looklog={() => setAuditlogsVisible(true)}></Auditrousece> :
                        !(shareFlag_ === 'share') && <div className="video_btn">
                          <div className='left_'>
                            {
                              video_btn.filter((item: any) => item.align == 'left').map((item: any, index: number) => {
                                return <div className='item_' title={item.title || ''} onClick={item.func} key={index}>
                                  {
                                    item.dom
                                  }
                                  {
                                    index !== video_btn.filter((item: any) => item.align == 'left').length - 1
                                    && <span className='split'></span>
                                  }
                                </div>
                              })
                            }
                          </div>
                          <div className='right_'>
                            {
                              video_btn.filter((item: any) => item.align == 'right').map((item: any, index: number) => {
                                if (item.func === null && clipBtnList.filter((l: any) => !l.disabled).length > 0)
                                  return <Dropdown overlay={menu} key={index}>
                                    <div className='item_'>
                                      <img src={require('@/images/contentlibrary/clip.png')} />
                                      <span>{intl.formatMessage({ id: '剪辑' })}</span>
                                    </div>
                                  </Dropdown>
                                return <div className='item_' key={index} onClick={item.func}>
                                  {
                                    item.dom
                                  }
                                </div>
                              })
                            }
                          </div>

                        </div>
                    }
                  </div>
                  {!mobileFlag && <div className={relationVideoVisible ? "entity_bottom open" : "entity_bottom close"}>
                    <div className='head'>
                      <span>{intl.formatMessage({ id: '相关资源' })}</span>
                      <CloseOutlined onClick={() => setRelationVideoVisible(false)} />
                    </div>
                    <div key={currentPage} className='center'>
                      {!loading ? <>{currentPage > 0 &&
                        <div className='previous_btn' onClick={previous_page} >
                          <LeftOutlined />
                        </div>
                      }
                        {product.slice(currentPage * 6, currentPage * 6 + 6).length > 0 ? product.slice(currentPage * 6, currentPage * 6 + 6)?.map((item: any, index: number) => {
                          return (
                            <div className={`sequence_item_wrap ${index === 0 ? 'firstItem' : ''}`} key={index}>
                              <div style={{ width: '100%' }}>
                                <RelationVideo
                                  detail={item}
                                  key={index}
                                  getQuerySequencemeta={getProduct}
                                />
                              </div>
                            </div>
                          );
                        }) : <span>暂无数据</span>}</> : <Spin tip={`${intl.formatMessage({ id: '正在合成中' })}...`} />}
                      {(currentPage * 6 + 6 < product.length) &&
                        <div className='next_btn' onClick={next_page}>
                          <RightOutlined />
                        </div>
                      }
                    </div>
                  </div>}
                </div>
                {
                  mobileFlag && product.length > 0 &&
                  <div className='mobile_relation_video'>
                    <div className='head'>{intl.formatMessage({ id: '相关资源' })}</div>
                    <div className='body'>
                      {
                        product.map((item: any, index: number) => {
                          return <RelationVideo
                            detail={item}
                            key={index}
                            getQuerySequencemeta={getProduct}
                          />
                        })
                      }
                    </div>
                  </div>
                }
              </>
          }
          {viewTopicModalVisible && <Modal
            visible={viewTopicModalVisible}
            title={intl.formatMessage({ id: '查看题目' })}
            style={{ zIndex: 9999 }}
            footer={null}
            maskClosable={false}
            onCancel={() => { setviewTopicModalVisible(false); idx.current = 0 }}
          >
            <div className='topicContent'>
              <div style={{ display: 'flex' }}>
                <span>({optionType_[questionItem.questions_type]})</span>
                <span style={{ margin: '0 10px' }} dangerouslySetInnerHTML={{ __html: questionItem.questions_content }}></span>
              </div>
              <div className="answers">
                {
                  questionItem.questions_type === 0 ?  // 单选
                    <Radio.Group value={questionItem.questions_answers?.[0]} disabled>
                      {questionItem.questions_options.map((item: any, seq: number) => (
                        <div className="answer-item" key={seq}>
                          <Radio value={String.fromCharCode(65 + seq)}>{String.fromCharCode(65 + seq)}</Radio>
                          <div className='radio-content special-dom' dangerouslySetInnerHTML={{ __html: item.content }}></div>
                        </div>
                      ))}
                    </Radio.Group> :
                    questionItem.questions_type === 1 ? //多选
                      <Checkbox.Group value={questionItem.questions_answers} disabled>
                        {questionItem.questions_options?.map((item: any, seq: number) =>
                          <div className='answer-item' key={seq}>
                            <Checkbox value={String.fromCharCode(65 + seq)}>{String.fromCharCode(65 + seq)}</Checkbox>
                            <div className="special-dom" dangerouslySetInnerHTML={{ __html: item.content }}></div>
                          </div>
                        )}
                      </Checkbox.Group> :
                      questionItem.questions_type === 2 ? // 填空题
                        questionItem.questions_options?.map((item: any, seq: number) =>
                          <div className='answer-item blanks' key={seq}>
                            <span>{intl.formatMessage({ id: '第空' }, { seq })}</span>
                            <span>{questionItem.questions_answers?.[seq]}</span>
                          </div>
                        ) :
                        questionItem.questions_type === 3 ? // 主观题
                          <div key={questionItem.id}>
                            <div className='answer-item'>
                            </div>
                          </div>
                          : <Radio.Group value={questionItem.questions_answers?.[0]} disabled>
                            {questionItem.questions_options?.map((item: any, seq: number) =>
                              <div className='answer-item' key={seq}>
                                <Radio value={String.fromCharCode(65 + seq)}>{String.fromCharCode(65 + seq)}</Radio>
                                <div className='radio-content'>{item.content}</div>
                              </div>
                            )}
                          </Radio.Group>
                }
              </div>
              {questionPointList.length > 1 && idx.current < questionPointList.length - 1 && previewModalVisible && <Button onClick={chooseNext}>下一题</Button>}
            </div>
          </Modal>}
          <Modal
            destroyOnClose
            maskClosable={false}
            visible={gifModalVisible}
            title={intl.formatMessage({ id: '保存路径' })}
            onOk={importGif}
            onCancel={() => setGifModalVisible(false)}
            className={`uploadbox${mobileFlag ? ' mobilebox' : ''}`}
            width={500}
          >
            <div className="right">
              <span>{intl.formatMessage({ id: '保存至' })}</span>
              <TreeSelect
                // placeholder={showplaceholder}
                treeData={treeData}
                value={showplaceholder}
                onSelect={onSelect}
                loadData={onLoadChild}
              />
            </div>
          </Modal>
          <Modal
            className="repairModal"
            visible={repairVisible}
            onCancel={() => setRepairVisible(false)}
            title={intl.formatMessage({ id: '修复' })}
            onOk={repairInfo}
          >
            <Form
              form={repairForm}
              layout="horizontal"
            // onValuesChange={onFormLayoutChange}
            >
              {reviewVideoInfo?.length && reviewVideoInfo.map((item, index) => {
                switch (item.controlType) {
                  case 1:
                    if (item.fieldName === 'course_begin' || item.fieldName === 'course_end') {
                      return <BasicDate item={item} key={index} edit={!item.isReadOnly} />
                    }
                    return <BasicInput item={item} key={index} edit={!item.isReadOnly} />;
                  case 2:
                    return <BasicTree item={item} key={index} edit={!item.isReadOnly} />;
                  case 3:
                    if (item.fieldName === 'teacher') {
                      return (
                        <TeacherItem
                          selectKeys={item.value as string}
                          edit={!item.isReadOnly}
                          multiple={item.isMultiSelect as any}
                          required={item.isMustInput as any}
                          message={intl.formatMessage({ id: '请选择教师' })}
                          label={item.alias}
                          name={item.fieldName}
                          key={item.fieldName}
                          className={'basicTeacherItem'}
                        />
                      );
                    } else {
                      return (
                        <BasicEnum item={{ ...item, value: JSON.stringify(item.value) }} key={index} edit={!item.isReadOnly} />
                      );
                    }
                }
              })}

            </Form>
          </Modal>
          <Modal
            visible={modalVisible}
            title={intl.formatMessage({ id: '添加标题' })}
            onOk={() => {
              if (formRef.current) {
                debounce(formRef.current.submit(), 500)
                setModalVisible(false)
              }
            }}
            onCancel={() => setModalVisible(false)}
          >
            <Form
              name="basic"
              labelCol={{ span: 4 }}
              wrapperCol={{ span: 16 }}
              ref={formRef}
              form={pointsForm}
              onFinish={handleSubmitSec}
            >
              <Form.Item
                label={intl.formatMessage({ id: '标题' })}
                name="title"
                rules={[{ required: true, message: `${intl.formatMessage({ id: '请填写标题' })}` }]}
              >
                <Input autoComplete="off" />
              </Form.Item>
            </Form>
          </Modal>
          <Modal
            visible={qvModalVisible}
            title={intl.formatMessage({ id: '版本' })}
            onOk={operation === 'ADD' ? addVersion : editVersion}
            onCancel={() => { setQvModalVisible(false); versionForm.resetFields() }}
          >
            <Form
              name="basic"
              labelCol={{ span: 4 }}
              wrapperCol={{ span: 16 }}
              form={versionForm}
            >
              <Form.Item
                label={intl.formatMessage({ id: '版本名' })}
                name="title"
                rules={[{ required: true, message: `${intl.formatMessage({ id: '请填写版本名' })}` }]}
              >
                <Input autoComplete="off" showCount maxLength={20} />
              </Form.Item>
            </Form>
          </Modal>
          <DownloadModal
            modalVisible={downloadModalVisible}
            modalClose={() => setDownloadModalVisible(false)}
            downloadlist={[{ contentId_: contentId_ }]}
            callback={(data: any) => (setCounter({ ...counter, download: data[0] }))}
            className_={mobileFlag ? 'downLoadBottomModal' : ''}
          />
          <ShareModal
            modalVisible={shareVisible}
            modalClose={() => { setShareVisible(false); }}
            sharelist={[{
              name_: contentDetail.entityName,
              keyframe_: contentDetail.keyframe?.filePath,
              contentId_
            }]}
            className_={mobileFlag ? 'mobileModal' : ''}
            callback={(data: any) => (setCounter({ ...counter, share: data }))}
          />
          <IntelligentAnalysisModal
            modalVisible={intelligentAnalysisModalVisible}
            modalClose={() => setIntelligentAnalysisModalVisible(false)}
            pubOrPri={ispublic}
            refresh={refreshTab}
            className_={mobileFlag ? 'mobileModal_' : ''}
            analysislist={[
              {
                contentId_: contentId_,
                duration: duration,
                frameRate: frameRate,
                name_: title,
                asr_status: contentDetail.asr_status,
                type_: type,
                fileext: contentDetail.fileext
              },
            ]}
          />
          <MergeSettingModal
            videoKeyframe={videoKeyframe}
            visible={settingModalVisible}
            onCancel={() => setsmVisible(false)}
            onOk={isComposite ? compositeVideo : finishProduct}
            showTitle={isComposite}
            checkSubTile={voice.length > 0}
          />

          <CommentModal
            commentVisible={commentModalVisible}
            onClose={() => setcommentModalVisible(false)}
            contentDetail={{
              contentId_,
              entityName: contentDetail.entityName,
              createUser_: contentDetail.createUser_
            }} />

          <BindKnowledgeMap
            modalVisible={bindKnowledgeModalVisible}
            modalClose={() => setBindKnowledgeModalVisible(false)}
            analysislist={[{
              type_: contentDetail.type,
              contentId_,
              name_: contentDetail.entityName,
              ...contentDetail
            }]}
            callback={(data: any) => (setCounter({ ...counter, knowledge: data }))}
          />
          <div>{contextHolder}</div>
          <Modal
            open={deletePointsVisible}
            className={mobileFlag ? ' mobileModal' : ''}
            title={intl.formatMessage({ id: '删除' })}
            onOk={() => {
              handleSecDel(checkedList.map((item: any) => item.guid_));
              setDeletePointsVisible(false);
            }}
            onCancel={() => setDeletePointsVisible(false)}
          >
            {intl.formatMessage({ id: '确定要删除所选知识点吗' })}
          </Modal>
          <TextclipModal
            textclipVisible={textclipVisible}
            clipList={textclipLists}
            onClose={() => setTextclipVisible(false)}
            onCreate={newTextClip}
          />
          <CopyAndMoveModal
            modalVisible={copyAndMoveModallVisible}
            modalClose={() => setCopyAndMoveModalVisible(false)}
            copyAndMovelist={[{
              type_: contentDetail.type,
              contentId_: contentId_,
              name_: contentDetail.entityName,
              keyframe_: contentDetail.keyframe?.filePath,
            }]}
            isShare={shareFlag_ == 'share'}
            operatType={operatType}
            pubOrPri={false}
            callback={(data: any) => (setCounter({ ...counter, copy: data }))}
            className_={mobileFlag ? 'copyOrMoveModal' : ''}
          />
          <Auditlogs logsVisible={auditlogsVisible} contentId={contentId_} onclose={() => setAuditlogsVisible(false)}></Auditlogs>
          <TopicSelectModal
            questionVersion={questionVersion}
            selectKeys={dataSource}
            disabled={[3]}
            currentPlayTime={currentPlayTime}
            setCurrentFrame={setCurrentFrame}
            versionCode={versionCode}
            visible={topicVisible}
            type={selectType}
            onAdd={() => setTopicTypeSelectVisible(true)}
            setVersionCode={setVersionCode}
            onConfirm={handleAddTopicConfirm}
            onclose={() => setTopicVisible(false)} />
          <TopicTypeSelectModal
            visible={topicTypeSelectVisible}
            onClose={() => setTopicTypeSelectVisible(false)}
            onConfirm={(type: number) => {
              setTopicType(type);
              setShowAddTopic(true);
            }} />

          {previewModalVisible && <PreviewModal
            entity={entity}
            visible={previewModalVisible}
            frameRate={frameRate}
            errorCode={errorCode}
            isTransCode={isTransCode}
            contentId={contentId_}
            questionList={questionList}
            questionPoint={questionPoint}
            versionCode={versionCode}
            current={current}
            pageSize={pageSize}
            setQuestionPointList={setQuestionPointList}
            setviewTopicModalVisible={setviewTopicModalVisible}
            setQuestionItem={setQuestionItem}
            setIdx={setIdx}
            onClose={() => setPreviewModalVisible(false)}
          />}
        </div>}
    </div>
  );
};
export default EntityDetail;
