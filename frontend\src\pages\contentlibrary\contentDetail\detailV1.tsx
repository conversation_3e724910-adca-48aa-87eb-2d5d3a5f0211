import React, {
  FC,
  useEffect,
  useRef,
  useState,
  KeyboardEvent,
  FocusEvent,
  CSSProperties,
} from 'react';
import {
  H5Player,
  Player,
  DownloadModal,
  IconFont,
  IntelligentAnalysisModal,
} from '@/components';
import {
  Tabs,
  Timeline,
  Empty,
  Form,
  Modal,
  Input,
  Button,
  message,
  Checkbox,
  Popover,
  Avatar,
  Badge,
} from 'antd';
import { UpOutlined, DownOutlined, RetweetOutlined } from '@ant-design/icons';
import { useParams, useDispatch, useSelector, useHistory } from 'umi';
import _ from 'lodash';
import SmartTypes from '../../../types/smartTypes';

import './index.less';
import entityApis from '@/service/entityApis';
import SmartService from '@/service/smartService';
import { IEntity, IFormItem } from '@/types/entityTypes';
import BasicMetadata from '@/components/basicMetadata/basicMetadata';
import {
  DeleteOutlined,
  CheckCircleFilled,
  CloseCircleFilled,
} from '@ant-design/icons';
import { copyObject, getType, HexToRgb } from '@/utils';
import Entity from '@/components/entity/entity';
import PointsItem from './components/pointsItem';
import ProductItem from './components/productItem';

import TimelineItem from './components/TimelineItem';
import { IPermission } from '@/models/permission';
import perCfg from '@/permission/config';
import MergeSettingModal from '@/components/mergeSettingModal/mergeSettingModal';
import globalParams from '@/permission/globalParams';
import { check } from 'prettier';
// import TitleModal from './components/videoTitleModal';

const { TabPane } = Tabs;

const EntityDetail: FC = () => {
  let history: any = useHistory();
  // 获取url参数
  const urlparams = JSON.parse(atob(history.location?.query?.params));
  const hidecatalogue = urlparams.hidecatalogue;
  const ispublic = urlparams.ispublic;

  const win = window as any;
  const player = useRef<Player>();
  const dispatch = useDispatch();
  const [pointsForm] = Form.useForm();
  const params = useParams<{ contentId: string }>();
  const [entityPath, setEntityPath] = useState<string>('');
  const [entity, setEntity] = useState<IEntity>();
  const [metadata, setMetadata] = useState<IFormItem[]>([]);
  const [title, setTitle] = useState<string>('');
  const [frameRate, setFrameRate] = useState<number>(25);
  const [duration, setDuration] = useState<number>(25);
  const [type, setType] = useState<string>('');

  const [isVision, setIsVision] = useState(false);
  const [btnStyle, setBtnStyle] = useState<CSSProperties>({});
  const [startSpan, setStartSpan] = React.useState(0);
  const [endSpan, setEndSpan] = React.useState(0);
  const [fragmentNum, setFragmentNum] = React.useState(0);
  const [isComposite, setIsComposite] = React.useState(false);
  const [isTransCode, setIsTransCode] = useState<boolean>(true);

  const doPop = (x: number, y: number) => {
    setBtnStyle({
      top: x - 26,
      left: '46%',
      display: 'block',
    });
  };

  const handleOk = (e: any) => {
    e.stopPropagation();
    handleSection(
      '',
      '',
      voice[startSpan]._in / 10000000,
      voice[endSpan]._out / 10000000,
    );
  };

  const key = 'updatable';
  const openMessage = () => {
    message.success({ content: '合成成品片段成功', key });

    setTimeout(() => {
      setFragmentNum(checkedList.length);
    }, 10000);
  };

  // 片段
  const [sequencemeta, setSequencemeta] = useState<SmartTypes.SequenceMeta[]>(
    [],
  );
  const [subtitles, setSubtitles] = useState<SmartTypes.Lyrics[]>([]);
  const [voice, setVoice] = useState<SmartTypes.Lyrics[]>([]);
  const [searchVoiceWord, setSearchVoiceWord] = useState<string>('');
  const [activeTabs, setActiveTabs] = useState<string>('1');
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const formRef = useRef<any>(null);
  const [section, setSection] = useState<SmartTypes.SequenceMeta>();
  const [currentFrame, setCurrentFrame] = useState<number>(0);
  const [editVisible, setEditVisible] = useState<boolean>(false);
  const [downloadModalVisible, setDownloadModalVisible] = useState<boolean>(
    false,
  );
  const [
    intelligentAnalysisModalVisible,
    setIntelligentAnalysisModalVisible,
  ] = useState<boolean>(false);
  const [searchIndex, setSearchIndex] = useState<number[]>([]);
  const [searchCurrent, setSearchCurrent] = useState<number>(0);
  const metadataRef = useRef<any>();

  //全选start
  const [checkedList, setCheckedList] = useState<any[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<any[]>([]);
  const [indeterminate, setIndeterminate] = useState(false);
  const [checkAll, setCheckAll] = useState(false);

  const [product, setProduct] = useState<SmartTypes.SequenceMeta[]>([]);
  const [settingModalVisible, setsmVisible] = useState(false);
  const [videoKeyframe, setVideoKeyframe] = useState('');

  const onChange = (list: any) => {
    // debugger;
    setIndeterminate(list.length != 0 && list.length !== sequencemeta.length);
    setCheckAll(list.length == sequencemeta.length);
    setCheckedList(
      sequencemeta.filter((item: any) => list.includes(item.guid_)),
    );
    setCheckedKeys(list);
  };

  useEffect(() => {
    setIndeterminate(
      checkedKeys.length != 0 && checkedKeys.length !== sequencemeta.length,
    );
    setCheckAll(checkedKeys.length == sequencemeta.length);
  }, [sequencemeta]);

  const onCheckAllChange = (e: any) => {
    setCheckedList(e.target.checked ? sequencemeta.map((e: any) => e) : []);
    setCheckedKeys(
      e.target.checked ? sequencemeta.map((item: any) => item.guid_) : [],
    );
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  };

  const getProduct = async () => {
    const res = await SmartService.getFinishProduct(params.contentId);
    setProduct(res?.data.data);
  };
  // const checkClear = (e: any) => {
  //   setCheckedList([]);
  //   openMessage();
  // }

  /**
   * 生成单个成品
   * @param values
   */
  const finishProduct = async (values: any) => {
    if (values.colorImprove) {
      values.colorImprove = HexToRgb(values.colorImprove).toString();
    }
    let data = checkedList.map((e: any) => {
      const { title, inpoint, outpoint } = e;
      return { saveName: title, inPoint: inpoint, outPoint: outpoint };
    });
    const res = await SmartService.createProduct(
      params.contentId,
      data,
      values,
    );
    if (res?.success) {
      message.success('提交合成成功');
      setCheckedList([]);
      setCheckedKeys([]);
      setsmVisible(false);
      setCheckAll(false);
      setIndeterminate(false);
    }
  };
  /**
   * 合成成品（多合一）
   * @param values
   */
  const compositeVideo = async (values: any) => {
    if (values.colorImprove) {
      values.colorImprove = HexToRgb(values.colorImprove).toString();
    }
    let res;
    if (values.beginId || values.endId) {
      const data = checkedList.map(({ inpoint, outpoint }: any) => ({
        inPoint: inpoint,
        outPoint: outpoint,
        contentId: params.contentId,
      }));
      if (values.beginId) {
        data.unshift({
          contentId: values.beginId,
          inPoint: -1,
          outPoint: -1,
        });
        delete values.beginId;
      }
      if (values.endId) {
        data.push({
          contentId: values.endId,
          inPoint: -1,
          outPoint: -1,
        });
        delete values.endId;
      }
      res = await SmartService.mergeProduct2(data, values);
    } else {
      const data = checkedList.map(({ inpoint, outpoint }: any) => ({
        inPoint: inpoint,
        outPoint: outpoint,
      }));
      res = await SmartService.mergeProduct(params.contentId, data, values);
    }
    if (res?.success) {
      message.success('提交合成成功');
      setsmVisible(false);
      setCheckedList([]);
      setCheckedKeys([]);
      setCheckAll(false);
      setIndeterminate(false);
    }
  };

  //全选end

  //   const searchIndex = useSelector<{ jurisdiction: any }, any>(({ jurisdiction }) => {
  //     return jurisdiction.searchIndex
  // })
  // const { permissions } = useSelector<{ permission: any }, IPermission>(
  //   ({ permission }) => permission,
  // );

  const { permissions, rmanGlobalParameter } = useSelector<
    { permission: any },
    IPermission
  >(({ permission }) => permission);

  const handleSuccess = (play: Player) => {
    console.log('video started success');
    player.current = play;
  };
  /**
   * 设置片段
   * @param media
   * @param img
   * @param trimin
   * @param trimout
   */
  const handleSection = (
    media: any,
    img: string,
    trimin: number,
    trimout: number,
  ) => {
    setActiveTabs('4');
    setModalVisible(true);
    setSection({
      inpoint: parseInt((trimin * 10000000).toString()),
      outpoint: parseInt((trimout * 10000000).toString()),
      keyframepath: img,
    });
  };
  /**
   * 保存sec
   * @param values
   */
  const handleSubmitSec = async (values: any) => {
    // if (sequencemeta.length === 0) {
    const res = await SmartService.addSequencemeta(params.contentId, [
      {
        ...section,
        keyframepath: '',
        ...values,
      },
    ]);
    setModalVisible(false);
    pointsForm.resetFields();
    if (res?.success) {
      setTimeout(() => {
        message.success('新建成功');
        getQuerySequencemeta();
      }, 1000);
    }
  };
  const setPlayerSec = (inpoint: number, outpoint: number) => {
    const _inpoint = win.TimeCodeConvert.l100Ns2Second(inpoint),
      _outpoint = win.TimeCodeConvert.l100Ns2Second(outpoint);
    // console.log(inpoint, outpoint);
    // console.log(_inpoint, _outpoint);

    player.current?.setTrimin(_inpoint);
    player.current?.setTrimout(_outpoint);
    player.current?.setCurrentTime(_inpoint);
    // player.current?.play();
  };
  const handleSecDel = async (id: string | undefined) => {
    const res = await SmartService.deleteSequencemeta(params.contentId, id);
    if (res?.success) {
      getQuerySequencemeta();
      setCheckedList(
        checkedList.filter(checkItem => {
          return checkItem.guid_ !== id;
        }),
      );
      setCheckedKeys(
        checkedKeys.filter(checkKey => {
          return checkKey !== id;
        }),
      );
    }
  };

  const handlePlayChange = (point: number) => {
    setCurrentFrame(point);
  };
  const setCurrentTime = (point: number) => {
    player.current?.setCurrentTime(win.TimeCodeConvert.l100Ns2Second(point));
  };

  // useEffect(() => {}, [currentFrame]);
  const submitMetadata = () => {
    // 点击提交的方法
    if (metadataRef.current) {
      metadataRef.current
        .getNewItems()
        .then((newValue: any) => {
          if (newValue) {
            const newMetadata = copyObject(newValue);
            const oldMetadata = copyObject(metadata);
            newMetadata.forEach((item: any) => {
              if (item.controlType === 8) {
                item.value = JSON.stringify(item.value);
              } else if (item.controlType === 14) {
                item.value = JSON.stringify(item.value);
              } else if (item.controlType === 12) {
                item.value = JSON.stringify(item.value);
              }
            });
            entityApis
              .setEntityMetadata(
                params.contentId,
                {
                  oldValue: oldMetadata,
                  newValue: newMetadata,
                },
                ispublic,
              )
              .then(res => {
                if (res && res.data && res.success) {
                  message.success('修改成功');
                  setMetadata(newMetadata);
                  const name = newMetadata.find(
                    (item: any) => item.fieldName === 'name',
                  );
                  if (name && name.value !== title) {
                    setTitle(name.value as string);
                  }
                  setEditVisible(false);
                } else {
                  message.error('修改失败');
                }
              });
          }
        })
        .catch(() => {
          message.error('您还有数据没有通过验证');
        });
    }
  };
  const cancelMetadata = () => {
    // 点击取消的方法
    if (metadataRef.current) {
      metadataRef.current.getOldItems();
      setEditVisible(false);
    }
  };

  const sortFuc = (a: SmartTypes.SequenceMeta, b: SmartTypes.SequenceMeta) =>
    a.inpoint - b.inpoint;
  const isBasicFormat = (src: string) => {
    return (
      src.indexOf('.html') > -1 ||
      src.indexOf('.htm') > -1 ||
      src.indexOf('.jpg') > -1 ||
      src.indexOf('.mp4') > -1 ||
      src.indexOf('.mp3') > -1 ||
      src.indexOf('.pdf') > -1
    );
  };

  useEffect(() => {
    const { contentId } = params;
    // SmartService.fetchDisplayPath(contentId).then(res => {
    //   if (res?.data) {
    //     setEntityPath(res?.data.displayPath);
    //     setFrameRate(res?.data.frameRate);
    //   }
    // });

    entityApis.getEntityMetadata(contentId).then(res => {
      if (res && res.data && res.success) {
        if (Array.isArray(res.data)) {
          res.data.forEach(item => {
            if (!item.value) {
              item.value = '';
            }
          });
          setMetadata(res.data);
        }
      }
    });
    entityApis.getEntity(contentId).then(res => {
      if (res && res.data && res.success) {
        let dataType = res.data.type;
        let path = _.find(res.data.fileGroups, (f: any) => {
          return f.typeCode === 'previewfile';
        });
        if (!path) {
          path = _.find(res.data.fileGroups, (f: any) => {
            return f.typeCode === 'sourcefile';
          });
          if (
            dataType === 'biz_sobey_picture' ||
            dataType === 'biz_sobey_audio' ||
            dataType === 'biz_sobey_document' ||
            dataType === 'biz_sobey_video'
          ) {
            path.fileItems[0] &&
              setIsTransCode(isBasicFormat(path.fileItems[0].displayPath));
          }
        }
        if (path) {
          setFrameRate(path.fileItems[0].frameRate);
          setDuration(path.fileItems[0].duration);
          setType(res.data.type);
        }
        setEntity({
          path: path ? path.fileItems[0].displayPath : '',
          keyframes:
            res.data.keyframe && res.data.keyframe.filePath
              ? res.data.keyframe.filePath
              : '',
          type: getType(res.data.type),
        });
        setTitle(res.data.entityName);
        document.title = res.data.entityName;
      }
    });
    SmartService.querySubtitle(contentId).then(res => {
      if (res?.data && res.data.data.length > 0) {
        setSubtitles(res.data.data[0].metadata);
      }
    });
    getQueryVoice();
    getQuerySequencemeta();
  }, []);
  // 获取语音
  const getQueryVoice = () => {
    const { contentId } = params;
    SmartService.queryVoice(contentId).then(res => {
      if (res?.data && res.data.data.length > 0) {
        setVoice(res.data.data[0].metadata.sort((a, b) => a._in - b._in));
      } else {
        setVoice([]);
      }
    });
  };
  // 获取知识点列表
  const getQuerySequencemeta = () => {
    const { contentId } = params;
    SmartService.querySequencemeta(contentId).then(res => {
      const result = res as any;
      if (result?.data && result.data.data.length > 0) {
        const arr = result.data.data[0].metadata.sort(sortFuc);
        setSequencemeta(arr);
        // let arrTem = [];
        // arr.forEach(e => {
        //   if (e.fragment_description) {
        //     arrTem.push(e)
        //   }
        // })
        // setFinishFragment(arrTem)
        // console.log(finishFragment)
      } else {
        setSequencemeta([]);
      }
    });
  };
  const searchVoive = (
    e: KeyboardEvent<HTMLInputElement> | FocusEvent<HTMLInputElement> | any,
  ) => {
    e.preventDefault();
    let name = e.target.value;
    let Index: number[] = [];
    voice.forEach((item, index) => {
      if (item.text.indexOf(name) > -1) {
        Index.push(index);
      }
    });
    setSearchCurrent(0);
    setSearchIndex(Index);
    setSearchVoiceWord(name);
  };
  const changeCurrent = (direction: string) => {
    let current = searchCurrent;
    if (direction === 'up') {
      current = current - 1;
      if (current < 0) {
        current = searchIndex.length - 1;
      }
      setSearchCurrent(current);
    } else {
      current = current + 1;
      if (current === searchIndex.length) {
        current = 0;
      }
      setSearchCurrent(current);
    }
  };

  const onSelection = () => {
    const selection = window.getSelection();
    if (!selection?.isCollapsed) {
      const baseNode = parseInt(
        selection?.anchorNode?.parentElement?.dataset.id || '',
      );
      const focusNode = parseInt(
        selection?.focusNode?.parentElement?.dataset.id || '',
      );
      if (baseNode - focusNode > 0) {
        setStartSpan(focusNode);
        setEndSpan(baseNode);
        doPop(
          selection?.focusNode?.parentElement?.offsetTop || 0,
          selection?.focusNode?.parentElement?.offsetLeft || 0,
        );
      } else {
        setStartSpan(baseNode);
        setEndSpan(focusNode);
        doPop(
          selection?.anchorNode?.parentElement?.offsetTop || 0,
          selection?.anchorNode?.parentElement?.offsetLeft || 0,
        );
      }
    } else {
      setBtnStyle({
        display: 'none',
      });
    }
  };

  const permiseifshow = () => {
    if (
      rmanGlobalParameter.includes(globalParams.speech_analysis_display) ||
      rmanGlobalParameter.includes(globalParams.knowledge_analysis_display)
    ) {
      if (!ispublic) {
        // debugger
        // 私有
        return true;
      } else {
        // debugger
        //公共
        if (permissions.includes(perCfg.resource_analysis)) {
          return true;
        }
      }
    }
    return false;
  };

  useEffect(() => {
    if (searchIndex.length) {
      setTimeout(() => {
        let domObj: any = document.getElementsByClassName(
          'mao' + searchIndex[searchCurrent],
        )[0];
        let timelineObj: any = document.getElementById('timeline');
        if (domObj) {
          timelineObj.scrollTo({
            top: domObj.offsetTop - 213,
            behavior: 'smooth',
          });
        }
      }, 500);
    }
  }, [searchVoiceWord, searchCurrent]);

  return (
    <div className="video_detail_wrapper">
      <div className="title">
        <h2>{title}</h2>
        <div>
          {permiseifshow() && (
            <Button
              type="primary"
              onClick={() => setIntelligentAnalysisModalVisible(true)}
              disabled={entity?.type !== 'video' && entity?.type !== 'audio'}
            >
              智能分析
            </Button>
          )}
          {((permissions.includes(perCfg.resource_download) && ispublic) ||
            !ispublic) && (
            <Button
              type="primary"
              onClick={() => setDownloadModalVisible(true)}
              icon={<IconFont type="iconxiazai" />}
            >
              下载
            </Button>
          )}
          {((permissions.includes(perCfg.jove) && ispublic) || !ispublic) && (
            <Button
              type="primary"
              onClick={() =>
                window.open(
                  `/joveone/?contentid=${params.contentId}&siteCode=S1`,
                )
              }
            >
              剪辑
            </Button>
          )}
        </div>
      </div>
      <div className="content">
        <div className="entity_view_wrapper">
          {/* {
            isTransCode?
          } */}
          {entity && isTransCode ? (
            <Entity
              type={entity.type}
              src={entity.path}
              keyframes={entity.keyframes}
              isEditPermission={
                (permissions.includes(perCfg.resource_edit) && ispublic) ||
                !ispublic
              }
              onSuccess={handleSuccess}
              setSection={handleSection}
              onPlayChange={handlePlayChange}
            />
          ) : (
            <div className="entity-error">
              <img src={require('../../../images/contentlibrary/zmz.png')} />
            </div>
          )}
        </div>
        <div className="entity_info_wrapper">
          <Tabs
            activeKey={activeTabs}
            onChange={activeTabs => {
              setActiveTabs(activeTabs);
              setBtnStyle({
                display: 'none',
              });
              if (activeTabs == '5') {
                setFragmentNum(0);
                getProduct();
              }
            }}
          >
            <TabPane key="1" tab="内容属性">
              {((permissions.includes(perCfg.resource_edit) && ispublic) ||
                !ispublic) &&
                (editVisible ? (
                  <div className="editor_selection">
                    <CheckCircleFilled
                      onClick={submitMetadata}
                      className="icon"
                    />
                    <CloseCircleFilled
                      onClick={cancelMetadata}
                      className="icon"
                    />
                  </div>
                ) : (
                  <div className="editor_selections">
                    <Button onClick={() => setEditVisible(true)} size="small">
                      编辑
                    </Button>
                  </div>
                ))}
              <BasicMetadata
                ref={metadataRef}
                items={metadata}
                edit={editVisible}
                frameRate={frameRate}
              />
            </TabPane>
            {subtitles.length > 0 &&
              (entity?.type === 'video' || entity?.type === 'audio') && (
                <TabPane key="2" tab="字幕信息">
                  <Timeline>
                    {subtitles.map((item, index) => (
                      <Timeline.Item
                        key={index}
                        color={
                          currentFrame >= item._in && currentFrame <= item._out
                            ? '#f0a95d'
                            : 'gray'
                        }
                      >
                        <div
                          className={
                            currentFrame >= item._in &&
                            currentFrame <= item._out
                              ? 'lyric_item active'
                              : 'lyric_item'
                          }
                          onClick={() => setCurrentTime(item._in)}
                        >
                          <div className="lyric_time">
                            {win.TimeCodeConvert.l100Ns2Tc$1(item._in, false)}
                          </div>
                          <span>{item.text}</span>
                        </div>
                      </Timeline.Item>
                    ))}
                  </Timeline>
                </TabPane>
              )}
            {(entity?.type === 'video' || entity?.type === 'audio') &&
              !(hidecatalogue === '1') && (
                <TabPane key="3" tab="语音信息" className="voice_div">
                  {voice.length > 0 ? (
                    <>
                      <div className="voice-input">
                        <Input
                          placeholder="检索语音信息"
                          style={{ width: 200 }}
                          onPressEnter={searchVoive}
                        />
                        <Button
                          icon={<UpOutlined />}
                          type="primary"
                          title="上一条"
                          onClick={() => {
                            changeCurrent('up');
                          }}
                        />
                        <Button
                          icon={<DownOutlined />}
                          type="primary"
                          title="下一条"
                          onClick={() => {
                            changeCurrent('down');
                          }}
                        />
                        <Button
                          onClick={() => {
                            const isVis = !isVision;
                            setIsVision(isVis);
                          }}
                          className="changeVision"
                          type="text"
                          icon={<RetweetOutlined />}
                        />
                      </div>
                      {isVision ? (
                        <div className="time-line" id="timeline">
                          <Timeline>
                            {voice.map((item, index) => (
                              <TimelineItem
                                key={index}
                                detail={item}
                                currentFrame={currentFrame}
                                setCurrentTime={() => {
                                  setCurrentTime(item._in);
                                }}
                                getQueryVoice={getQueryVoice}
                                searchVoiceWord={searchVoiceWord}
                                Timelineindex={index}
                              />
                            ))}
                          </Timeline>
                        </div>
                      ) : (
                        <div className="paragraph-wrapper">
                          <div
                            className="text_pop"
                            style={btnStyle}
                            onClick={handleOk}
                          >
                            添加片段
                          </div>
                          <div
                            onClick={e => {
                              e.stopPropagation();
                            }}
                            // onMouseDown={() => {
                            //   setBtnStyle({
                            //     display: 'none',
                            //   });
                            // }}
                            onMouseUp={onSelection}
                            onMouseLeave={onSelection}
                            className="paragraph-content"
                          >
                            {voice.map((item, index) => {
                              if ((index + 1) % 20 == 0) {
                                return (
                                  <span
                                    key={index}
                                    data-id={index}
                                    className={
                                      sequencemeta.some(
                                        s =>
                                          s.inpoint <= item._in &&
                                          s.outpoint >= item._out,
                                      )
                                        ? 'selected'
                                        : ''
                                    }
                                  >
                                    {item.text}
                                    <br />
                                  </span>
                                );
                              }
                              return (
                                <span
                                  key={index}
                                  data-id={index}
                                  className={
                                    sequencemeta.some(
                                      s =>
                                        s.inpoint <= item._in &&
                                        s.outpoint >= item._out,
                                    )
                                      ? 'selected'
                                      : ''
                                  }
                                >
                                  {item.text}
                                </span>
                              );
                            })}
                          </div>
                        </div>
                      )}
                    </>
                  ) : (
                    <Empty />
                  )}
                </TabPane>
              )}
            {(entity?.type === 'video' || entity?.type === 'audio') &&
              !(hidecatalogue === '1') && (
                <TabPane key="4" tab="知识点">
                  {sequencemeta.length > 0 ? (
                    <div className="sequence_list">
                      {rmanGlobalParameter.includes(
                        globalParams.composite_display,
                      ) ? (
                        <div className="sequence_operator">
                          <Checkbox
                            indeterminate={indeterminate}
                            onChange={onCheckAllChange}
                            checked={checkAll}
                          >
                            {' '}
                          </Checkbox>
                          <Button
                            type="primary"
                            className="create"
                            disabled={checkedList.length === 0}
                            onClick={() => {
                              setsmVisible(true);
                              setIsComposite(false);
                              setVideoKeyframe(
                                player.current?.getCurrentKeyframe() || '',
                              );
                            }}
                          >
                            独立生成
                          </Button>
                          <Button
                            type="primary"
                            className="create"
                            disabled={checkedList.length === 0}
                            onClick={() => {
                              setsmVisible(true);
                              setIsComposite(true);
                              setVideoKeyframe(
                                player.current?.getCurrentKeyframe() || '',
                              );
                            }}
                          >
                            拼接生成
                          </Button>

                          {/* <TitleModal
                        checkedListClear={checkClear}
                        checkedList={checkedList}
                        sequencemeta={sequencemeta}
                        contentId={params.contentId}
                      /> */}
                        </div>
                      ) : (
                        ''
                      )}

                      <Checkbox.Group
                        onChange={onChange}
                        value={checkedKeys as any}
                      >
                        {sequencemeta.map((item, index) => (
                          <div className="sequence_item_wrap" key={item.guid_}>
                            <Checkbox
                              className="sequence_item_checkbox"
                              value={item.guid_}
                            >
                              {' '}
                            </Checkbox>
                            <div style={{ width: '100%' }}>
                              <PointsItem
                                key={item.guid_}
                                detail={item}
                                handleSecDel={() => handleSecDel(item.guid_)}
                                setPlayerSec={() =>
                                  setPlayerSec(item.inpoint, item.outpoint)
                                }
                                getQuerySequencemeta={getQuerySequencemeta}
                                downVisible={false}
                              />
                            </div>
                          </div>
                        ))}
                      </Checkbox.Group>
                    </div>
                  ) : (
                    <Empty />
                  )}
                </TabPane>
              )}
            {(entity?.type === 'video' || entity?.type === 'audio') &&
              rmanGlobalParameter.includes(globalParams.composite_display) && (
                <TabPane
                  key="5"
                  tab={
                    <Badge count={fragmentNum} offset={[8, -2]}>
                      <span>成品片段</span>
                    </Badge>
                  }
                >
                  {sequencemeta.length > 0 ? (
                    <>
                      <Button onClick={getProduct} type="primary" size="small">
                        刷新
                      </Button>
                      <div className="sequence_list">
                        <div>
                          {product.map((item, index) => {
                            return (
                              <div className="sequence_item_wrap" key={index}>
                                <div style={{ width: '100%' }}>
                                  <ProductItem
                                    detail={item}
                                    handleSecDel={() =>
                                      handleSecDel(item.guid_)
                                    }
                                    getQuerySequencemeta={getProduct}
                                    downVisible={true}
                                  />
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    </>
                  ) : (
                    <Empty />
                  )}
                </TabPane>
              )}
          </Tabs>
        </div>
      </div>
      <Modal
        visible={modalVisible}
        title="添加标题"
        onOk={() => formRef && formRef.current.submit()}
        onCancel={() => setModalVisible(false)}
      >
        <Form
          name="basic"
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 16 }}
          ref={formRef}
          form={pointsForm}
          onFinish={handleSubmitSec}
        >
          <Form.Item
            label="标题"
            name="title"
            rules={[{ required: true, message: '请填写标题' }]}
          >
            <Input autoComplete="off" />
          </Form.Item>
        </Form>
      </Modal>
      <DownloadModal
        modalVisible={downloadModalVisible}
        modalClose={() => setDownloadModalVisible(false)}
        downloadlist={[{ contentId_: params.contentId }]}
      />
      <IntelligentAnalysisModal
        modalVisible={intelligentAnalysisModalVisible}
        modalClose={() => setIntelligentAnalysisModalVisible(false)}
        pubOrPri={ispublic}
        analysislist={[
          {
            contentId_: params.contentId,
            duration: duration,
            name_: title,
            type_: type,
          },
        ]}
      />
      <MergeSettingModal
        videoKeyframe={videoKeyframe}
        visible={settingModalVisible}
        onCancel={() => setsmVisible(false)}
        onOk={isComposite ? compositeVideo : finishProduct}
        showTitle={isComposite}
        checkSubTile={voice.length > 0}
      />
    </div>
  );
};
export default EntityDetail;
