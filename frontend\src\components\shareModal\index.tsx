import React, { useState, useEffect } from 'react';
import { Modal, Progress, Button, Form, message, Table, Select, Radio, Input, Switch, Tabs } from 'antd';
import deleteApis from '@/service/deleteApis';
import './index.less';
import TeacherItem from '../formItemBox/teacherItem';
import contentListApis from '@/service/contentListApis';
import { sleep } from '@/utils';
import InputItem from '../BasicInformationItem/inputitem';
import { useSelector, useIntl } from 'umi';
import { IConfig } from '@/models/config';
import { getSensitiveWord } from '@/utils';

interface CreateModalProps {
  modalVisible: boolean;
  modalClose: () => void;
  modalOpen?: () => void;
  fetchSharedNums?: () => void;
  sharelist: any;
  refresh?: () => void;
  className_?: string;
  callback?: (data:any) => void;
}
const ShareModal: React.FC<CreateModalProps> = props => {
  const {
    modalClose,
    modalOpen,
    fetchSharedNums,
    modalVisible,
    sharelist,
    refresh,
    className_,
    callback,
  } = props;
  const { TabPane } = Tabs;
  const [userlist, setUserlist] = useState<any>([]);
  const [link, setLink] = useState<any>({});
  const [userVisible, setUserVisible] = useState<boolean>(false);
  const [shareVisible, setShareVisible] = useState<boolean>(false);
  const [linkFinish, setLinkFinish] = useState<boolean>(false);
  const [userFinish, setUserFinish] = useState<boolean>(false);
  const [pass, setPass] = useState<boolean>(true);
  const [activeKey, setActiveKey] = useState<any>('1');
  const [watermark, setWatermark] = useState<boolean>(false); // 是否显示水印
  const [watermarkRight, setWatermarkRight] = useState<boolean>(false); // 是否显示水印
  const  {mobileFlag}  = useSelector<{ config: any }, any>(
    ({config})=>config
 );
  const [form] = Form.useForm();
  const [userform] = Form.useForm();
  const  configs:any  = useSelector<{ config: any }, IConfig>(
    ({config})=>config
 );
  const intl = useIntl();
  useEffect(() => {
    //初始化数据
    setUserlist([])
    // console.log('sendShare', sharelist);
  }, [sharelist])
  useEffect(() => {
    if (modalVisible) {
    }
  }, [modalVisible]);
  const handleCancel = () => {
    setUserFinish(false);
    setLinkFinish(false);
    setPass(true);
    setUserlist([]);
    setActiveKey('1')
    modalClose();
  };
  const handleShareCancel = () => {
    setShareVisible(false)
  };
  const handleUserCancel = () => {
    setUserVisible(false)
  };
  //复制链接 提取码
  const copylinkurl = () => {
      const input = document.createElement('input');
      input.setAttribute('readonly', 'readonly');
      const value = link.password === '' ?
        `链接：${configs.publicDomain || window.location.origin}/rman/#/basic/shareDetail/${link.link}_0` :
        `链接：${configs.publicDomain || window.location.origin}/rman/#/basic/shareDetail/${link.link}_1     获取密码：${link.password}`
      input.setAttribute('value', value);
      document.body.appendChild(input);
      input.select();
      if (document.execCommand('copy')) {
        document.execCommand('copy');
        message.success(intl.formatMessage({ id: '复制成功' }));
      }
      document.body.removeChild(input);
  };
  const dateChange = (e: any) => {
    console.log(e)
  };
  const tabChange = (key: any) => {
    console.log(key)
    setActiveKey(key)
  };
  const onchange = (node: any) => {
    console.log(node)
    setUserlist(node)
  };

  const sendShare = (node: any) => {
    console.log('userlist', userlist);
    // setUserVisible(false)
  };
  const encryption = (e: any) => {
    console.log('encryption', e);
    setPass(e);
    // setUserVisible(false)
  };
  const openLink = () => {
    setShareVisible(true)
    modalClose()
  };
  const goback = () => {
    setShareVisible(false)
    modalOpen && modalOpen();
  };
  //创建分享链接 无系统用户
  const shareLink = () => {
    form.validateFields().then( (value: any) => {
      getSensitiveWord(value.name, intl.formatMessage({ id: '分享主题' }), async()=> {
        let param = {
          ...value,
          enablePassword: pass,
          shareResourceInfos: sharelist?.map((item: any) => {
            return {
              contentId: item.contentId_,
              name: item.name_,
              keyFrame: item.keyframe_
            }
          }),
        };
        const res = await contentListApis.shareResourceNew(param);
        console.log(res)
        if (res?.success) {
          // modalClose();
          setLink(res.data)
          setLinkFinish(true)
          message.success(intl.formatMessage({ id: '创建成功' }));
          callback?.(Object.values(res.data.shareCount)[0]);
          fetchSharedNums?.();
          // setShareVisible(true)
        } else {
          setLinkFinish(false)
          message.error(intl.formatMessage({ id: '创建失败' }));
        }
      })
    })
  };
  const systemUser = () => {
    userform.validateFields().then(async (value: any) => {
      getSensitiveWord(value.name, intl.formatMessage({ id: '分享主题' }), async()=> {
        let param = {
          ...value,
          shareResourceInfos: sharelist?.map((item: any) => {
            return {
              contentId: item.contentId_,
              name: item.name_,
              keyFrame: item.keyframe_
            }
          }),
          shareUserInfos: userlist.map((item: any) => {
            return {
              shareUserCode: item.value,
              shareUserName: item.name
            }
          })
        };
        const res = await contentListApis.shareUserResourceNew(param);
        if (res?.success) {
          // modalClose();
          setLink(res.data)
          setUserFinish(true)
          message.success(intl.formatMessage({ id: '分享成功' }));
          callback?.(Object.values(res.data.shareCount)[0]);
          fetchSharedNums?.();
          // refresh(undefined, { name: '分享'})
          // setShareVisible(true)
        } else {
          setUserFinish(false)
          message.error(intl.formatMessage({ id: '分享失败' }));
        }
      })
    })
  };
  return (
    <Modal
      destroyOnClose={true}
      className={`shareModal ${className_}`}
      title={
        <Tabs defaultActiveKey={activeKey} onChange={tabChange}>
          <TabPane tab={intl.formatMessage({ id: '创建分享链接' })} key="1"></TabPane>
          <TabPane tab={intl.formatMessage({ id: '分享给系统用户' })} key="2"></TabPane>
        </Tabs>
      }
      visible={modalVisible}
      onCancel={handleCancel}
      footer={[
        activeKey === '1' ? <Button
          key={'copy'}
          type='primary'
          onClick={linkFinish ? copylinkurl : shareLink}
        >
          {linkFinish ? intl.formatMessage({ id: '复制链接及密码' }) : intl.formatMessage({ id: '创建分享链接' })}
        </Button> :
          <Button
            key={'finish'}
            type='primary'
            onClick={userFinish ? handleCancel : systemUser}
          >
            {userFinish ? intl.formatMessage({ id: '完成' }) : intl.formatMessage({ id: '分享' })}
          </Button>
      ]}
      width={490}
    >
      {
        activeKey === '1' &&
          (linkFinish ?
          <>
            <div> 
              {`链接：${configs.publicDomain || window.location.origin}/rman/#/basic/shareDetail/${link.link}${pass?'_1':'_0'}`} 
            </div>
            {pass && <div>
              {`获取密码：${link.password}`}
            </div>}
          </>
          :
          <Form
            name='shareForm'
            // onFinish={onFinish}
            labelCol={{ span: 5 }}
            form={form}
            initialValues={{
              name: sharelist?.length === 1 ? sharelist?.[0]?.name_ : sharelist?.[0]?.name_ + `等${sharelist?.length}项内容`,
              validity: 7,
              onlyRead: true,
            }}
          >
            <Form.Item
              label={intl.formatMessage({ id: '分享内容' })}
            >
              <span>{sharelist?.length === 1 ? sharelist?.[0]?.name_ : sharelist?.[0]?.name_ + `等${sharelist?.length}项内容`}</span>
            </Form.Item>
            <Form.Item
              label={intl.formatMessage({ id: '分享主题' })}
              name='name'
              rules={[{ required: true, message: intl.formatMessage({ id: '主题不能为空' }) }]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              name='validity'
              label={intl.formatMessage({ id: '分享有效期' })}
            // rules={[{ required: true, message: '请选择有效期' }]}
            >
              <Radio.Group onChange={dateChange}>
                <Radio value={7}>{intl.formatMessage({ id: '7天' })}</Radio>
                <Radio value={30}>{intl.formatMessage({ id: '30天' })}</Radio>
                <Radio value={-1}>{intl.formatMessage({ id: '永久有效' })}</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item
              name='onlyRead'
              label={intl.formatMessage({ id: '允许下载' })}
            >
              <Radio.Group>
                <Radio value={false}>{intl.formatMessage({ id: '是' })}</Radio>
                <Radio value={true}>{intl.formatMessage({ id: '否' })}</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item
              name='showWatermarkText'
              label={intl.formatMessage({ id: '启用水印' })}
            >
              <Switch defaultChecked={false} onChange={(e) => setWatermark(e)} />
            </Form.Item>
            {watermark && <Form.Item
              name='watermarkText'
              label={intl.formatMessage({ id: '设置水印' })}
            >
              <Input placeholder={intl.formatMessage({ id: '请输入水印文本信息' })} />
            </Form.Item>}
            <Form.Item
              name='enablePassword'
              label={intl.formatMessage({ id: '启用密码' })}
            >
              <Switch defaultChecked onChange={encryption} />
            </Form.Item>
            {pass && <Form.Item
              name='linkPassword'
              label={intl.formatMessage({ id: '设置密码' })}
              extra={intl.formatMessage({ id: '注：如果不填，则系统自动生成' })}
            >
              <Input placeholder={intl.formatMessage({ id: '自定义密码' })} />
            </Form.Item>}
          </Form>)
      }

      {
        activeKey === '2' &&
          (userFinish ?
          <div className='userFinish'>
            {intl.formatMessage({ id: '分享成功' })}
          </div>
          : <Form
            name='shareForm'
            // onFinish={onFinish}
            labelCol={{ span: 5 }}
            form={userform}
            initialValues={{
              name: sharelist?.length === 1 ? sharelist?.[0]?.name_ : sharelist?.[0]?.name_ + `等${sharelist?.length}项内容`,
              validity: 7,
              onlyRead: true,
            }}
          >
            <Form.Item
              label={intl.formatMessage({ id: '分享内容' })}
            >
              <span>{sharelist?.length === 1 ? sharelist?.[0]?.name_ : sharelist?.[0]?.name_ + `等${sharelist?.length}项内容`}</span>
            </Form.Item>
            <Form.Item
              label={intl.formatMessage({ id: '分享主题' })}
              name='name'
              rules={[{ required: true, message: '主题不能为空' }]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              name='validity'
              label={intl.formatMessage({ id: '分享有效期' })}
            // rules={[{ required: true, message: '请选择有效期' }]}
            >
              <Radio.Group onChange={dateChange}>
                <Radio value={7}>{intl.formatMessage({ id: '7天' })}</Radio>
                <Radio value={30}>{intl.formatMessage({ id: '30天' })}</Radio>
                <Radio value={-1}>{intl.formatMessage({ id: '永久有效' })}</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item
              label={intl.formatMessage({ id: '允许下载' })}
              name='onlyRead'
            >
              <Radio.Group>
                <Radio value={false}>{intl.formatMessage({ id: '是' })}</Radio>
                <Radio value={true}>{intl.formatMessage({ id: '否' })}</Radio>
              </Radio.Group>
            </Form.Item>
            {/* <Form.Item
              name='showWatermarkText'
              label={intl.formatMessage({ id: '启用水印' })}
            >
              <Switch defaultChecked={false} onChange={(e) => setWatermarkRight(e)} />
            </Form.Item> */}
            {/* {watermarkRight && <Form.Item
              name='watermarkText'
              label={intl.formatMessage({ id: '设置水印' })}
            >
              <Input placeholder={intl.formatMessage({ id: '请输入水印文本信息' })} />
            </Form.Item>} */}
            <TeacherItem
              multiple={true}
              required={true}
              message={intl.formatMessage({ id: '请选择系统用户' })}
              label={intl.formatMessage({ id: '分享给' })}
              onChange={onchange}
              myShared={true}
              name={'teacher'}
            />
          </Form>)
      }
    </Modal>
  );
};

export default ShareModal;