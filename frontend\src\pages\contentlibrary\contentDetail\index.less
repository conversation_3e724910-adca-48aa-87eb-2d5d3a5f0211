.text_overflow() {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.text_overflow_multi(@line) {
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: @line;
  -webkit-box-orient: vertical;
}

.video_detail_wrapper {
  overflow: hidden;
  // width: 1700px;
  // width: 1498px;
  width: 100%;
  height: 100%;
  background: #f7f9fa;
  position: relative;
  display: flex;
  // margin: 0 auto;
  .errorContent {
    width: 100%;
    height: calc(100vh - 100px);
    .entity-error {
      border: 0;
    }
  }
  .title {
    height: 46px;
    line-height: 46px;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    .title_info {
      overflow: hidden;
      width: 1054px;
      width: 100%;
      display: flex;
      align-items: center;
      flex-wrap: nowrap;
      justify-content: flex-start;
      .title_header {
        padding: 0;
        .ant-page-header-heading-title {
          margin-right: 0;
        }
        .pageHeader_title {
          > span:first-child {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          display: flex;
          align-items: center;
          .favoriteBtn {
            font-size: 17px;
            color: #9fa5ab;
            cursor: pointer;
          }
          .canclefavoriteBtn {
            font-size: 17px;
            color: var(--active-bg-color);
            cursor: pointer;
          }
          .favoritedBtn {
            font-size: 18px;
            color: var(--primary-color);
            cursor: pointer;
          }
        }
      }
      .dic_tree {
        display: flex;
        flex-direction: row;
        width: 100%;
        > span:first-child {
          color: #5d5d5d;
          display: block;
          white-space: nowrap;
          overflow: hidden;
        }
        > span:last-child {
          flex: 1;
          display: block;
          color: #5d5d5d;
          font-weight: bold;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }
    .title_btn {
      // flex: none;
      display: flex;
      > .ant-btn {
        margin-left: 20px;
      }
      > .ant-dropdown-trigger {
        width: 80px;
        margin-left: 20px;
        padding: 0px 15px;
        line-height: 32px;
        height: 32px;
        border: 1px solid #d9d9d9;
        > span:last-child {
          margin-left: 5px;
        }
      }
    }
    div {
      .vd_ghost_btn {
        color: var(--primary-color);
        border-color: var(--primary-color);
        margin-left: 20px;
      }
      .vd_ghost_btn_disabled {
        margin-left: 20px;
      }
    }
  }

  .content {
    flex: 1;
    margin: 0 50px;
    height: 100%;
    position: relative;
    min-width: 800px;
    .entity_view {
      position: relative;
      .arrow{
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        font-size: 39px;
        z-index: 999;
      }
      .left-arrow{
        left: -25px;
      }
      .right-arrow{
        right: -25px;
      }
      .whiteBg {
        background-color: #ffffff !important;
      }
      .entity_view_wrapper {
        .watermark{
          position: absolute;
          z-index: 99;
          text-align: center;
          width: 100px;
          img{
            width: 100px;
          }
          div{
            font-size: 20px;
            color: var(--primary-color);
          }
        }
        
        .lt{
          top: 0;
          left: 0;
        }
        .rt{
          right: 0;
          top: 0;
        }
        .lb{
          left: 0;
          bottom: 52px;
        }
        .rb{
          right: 38px;
          bottom: 52px;
        }
        .selection_box{
          position: absolute;
          z-index: 99999;
          pointer-events: none;
          width: 0px;
        }
        .controls-box{
          position: absolute;
          bottom: 3px;
          right: 177px;
          text-align: right;
          z-index: 999;
          .controls{
            width: 352px;
            position: absolute;
            bottom: 43px;
            left: -157px;
            z-index: 999;
            background: rgba(50, 50, 50, 0.7);
            .ant-slider-vertical{
              margin: 3px;
            }
            .contrao_param{
              height: 100%;
              >div {
                width: 39px;
                float: left;
                height: 150px;
                display: flex;
                align-items: center;
                flex-direction: column;
                div:last-of-type{
                  color: #fff;
                  margin-top: 10px;
                  font-size: 12px;
                }
              }
            }
            
            span{
              color: #fff;
            }
            .type_box{
              display: flex;
              margin: 10px;
              div{
                margin-right: 5px;
                cursor: pointer;
                padding: 1px 10px;
                border-radius: 15px;
                color: #fff;
                border: 1px solid #fff;
              }
            }
          }
          span.adjust{
            color: #fff;
            width: 55px;
            cursor: pointer;
            font-size: 20px;
          }
        }
        .gif{
          color: #fff;
          cursor: pointer;
          font-size: 21px;
          position: absolute;
          bottom: -2px;
          left: 305px;
          text-align: center;
          z-index: 999;
        }
        .subtitle_wrap{
          color: #fff;
          cursor: pointer;
          position: absolute;
          right: 156px;
          bottom: 6px;
          z-index: 999;
          &:hover .subtitle_btn{
            display: block;
          }
          .subtitle_btn{
            display: none;
            background: rgba(50, 50, 50, 0.7);
            border: solid 1px transparent;
            border-radius: 0;
            bottom: 100%;
            margin-right: -27px;
            overflow: hidden;
            padding: 0;
            position: absolute;
            right: 50%;
            visibility: visible;
            width: 53px;
            div{
              width: 60px;
              font-size: 10px;
              padding: 4px 10px 0;
              margin-bottom: 6px;
            }
          }
          .text{
            width: 30px;
            height: 20px;
            background-position: 0 0;
            background-repeat: no-repeat;
          }
        }
        .subtitles {
          position: absolute;
          box-shadow: none;
          cursor: move;
          z-index: 99;
          display: inline-table;
          bottom: 85px;
          left: 50%;
          transform: translateX(-50%);
          text-align: center;
          .subtitle_zh{
            font-size: 25px;
          }
          div{
              max-width: 1200px !important  ;
              background: rgba(20, 20, 20, 0.5);
              color: #fff;
              font-size: 18px;
              white-space: wrap;
          }
      }
        video#h5Player_native_hls::-webkit-media-text-track-container {
          display: none; //隐藏hls影子节点的字幕
        }
        overflow: hidden;
        position: relative;
        // width: 1300px;
        // height: calc((1300px - 37px) * 9 / 16 + 58px);
        // width: 1360px;
        // height: calc((1254px - 37px) * 9 / 16 + 63px);
        height: calc(100vh - 173px) !important;
        // background-color: #626262;
        background-color: #f1f1f1;
        .mejs__inner {
          .mejs__layers {
            z-index: 100;
            .mejs__poster {
              display: none;
            }
            .mejs__captions-layer {
              z-index: 999999;
              position: static;
              .mejs__captions-position-hover {
                position: static;
                bottom: 85px;
                font-size: 24px;
                line-height: 24px;
                .mejs__captions-text {
                  box-shadow: none;
                  cursor: move;
                  position: absolute;
                  z-index: 999999999;
                  display: inline-table;
                }
                // .mejs__captions-text_drag{
                //   position: absolute;
                // }
              }
            }
          }
          .pointer_event {
            pointer-events: none;
          }
          .mejs__controls {
            z-index: 999;
            .mejs__captions-button {
              // :hover{
              //   background-position: 0;
              //   background-image: url('/static/images/trackshover.png');
              //   background-repeat: no-repeat;
              //   width: 30px;
              // }
              display: inherit !important;
              position: absolute;
              right: 110px;
              width: 38px !important;
              bottom: 0;
              .no_tracks {
                background-position: 0;
                background-image: url('/rman/static/images/noTracks.svg');
                background-repeat: no-repeat;
                width: 30px;
              }
              button {
                background-position: 0;
                background-image: url('/rman/static/images/tracks.png');
                background-repeat: no-repeat;
                width: 30px;
              }
              .on_tracks,
              button:not(.no_tracks):hover {
                background-position: 0;
                background-image: url('/rman/static/images/trackshover.png');
                background-repeat: no-repeat;
                width: 30px;
              }
              .mejs__captions-selector {
                .mejs__captions-selector-list {
                  .mejs__captions-selector-list-item {
                    .mejs__captions-selected {
                      color: #00a8ff;
                    }
                  }
                }
              }
            }
            .mejs__time-rail {
              .mejs__time-slider {
                .mejs__keypoint {
                  cursor: pointer;
                  width: 8px;
                  &:hover {
                    background-color: #00a8ff;
                  }
                }
              }
            }
          }
          .mejs__time-container {
            .mejs__currenttime input {
              width: 80px;
            }
          }
        }
        .entity-img {
          position: relative;
          .position_ {
            position: absolute;
            .rect {
              position: absolute;
              border: 1px solid red;
            }
          }
        }
      }
      .video_btn {
        display: flex;
        justify-content: space-between;
        height: 50px;
        background: #ffffff;
        font-size: 14px;
        line-height: 50px;
        overflow: hidden;
        position: relative;
        .left_ {
          align-items: center;
          display: flex;
          overflow: hidden;
          text-overflow: ellipsis;
          & > span {
            white-space: nowrap;
          }
          .item_ {
            .anticon {
              font-size: 25px;
            }
            > span:nth-child(2) {
              color: gray;
            }
          }

          .audit_title {
            margin-left: 10px;
            line-height: 32px;
          }

          .audit_folder {
            color: rgba(0, 0, 0, 0.55);
            line-height: 32px;
          }

          .audit_option {
            margin-left: 20px;
            display: flex;
            .ant-btn-primary {
              height: 32px;
              background-color: #46c462;
              border: unset;
              &.ant-btn-dangerous {
                background-color: #fff;
                color: #ff4d4f;
                border: 1px solid #ff4d4f;
              }
            }
            
          }
        }
        .right_ {
          height: 100%;
          display: flex;
          margin-right: 15px;
          align-items: center;
          margin-left: 50px;
          .item_ {
            width: 110px;
            height: 32px;
            border-radius: 18px;
            background: var(--primary-color);
            &:hover {
              background: var(--second-color);
            }
            button {
              width: 100%;
              height: 100%;
              border-radius: 18px;
              background-color: var(--primary-color);
              border: 0;
              padding: 0;
              &:hover {
                background: var(--second-color);
              }
            }
            .ant-btn-loading {
              background-color: rgba(0, 0, 0, 0.04);
              &:hover {
                background-color: rgba(0, 0, 0, 0.04);
              }
            }
            span{
              color: #fff;
              margin-left: 5px;
            }
            .close > svg {
              transform: rotate(270deg);
            }
            .open > svg {
              transform: rotate(90deg);
            }
          }

          .disabled-chat {
            cursor: not-allowed;
            border: 1px solid #d9d9d9;
            background: rgba(0, 0, 0, 0.04);
            &:hover {
              background: rgba(0, 0, 0, 0.04);
            }
            span{
              color: rgba(0, 0, 0, 0.3);
            }
          }

          .logs_btn {
            padding-left: 10px;
            height: 100%;
            display: flex;
            align-items: center;
          }

          .look_log {
            margin-left: auto;
          }
        }
        .item_ {
          cursor: pointer;
          white-space: nowrap;
          display: flex;
          flex-wrap: nowrap;
          justify-content: center;
          align-items: center;
          &:hover {
            color: var(--primary-color);
          }
          // margin-left: 5%;
          margin-left: 15px;
          > span:first-child {
            color: var(--primary-bg-color);
            padding-right: 8px;
            // opacity: .3;
          }
          .split {
            width: 1px;
            height: 18px;
            background: #d9d9d9;
            margin-left: 15px;
          }
          span.favoritedBtn {
            font-size: 18px;
            color: var(--primary-color);
            cursor: pointer;
            opacity: 1;
          }
        }
      }
    }
    .entity_bottom {
      width: 100%;
      margin-top: 10px;
      position: absolute;
      left: 0;
      background-color: #ffffff;
      z-index: 99999999;
      transition: bottom 0.3s;
      &.open {
        bottom: 0;
      }
      &.close {
        bottom: -209px;
      }
      padding: 0 4%;
      .head {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        > span {
          display: block;
          padding: 14px 0;
          font-size: 14px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #4a4f64;
        }
        .anticon-close {
          width: 20px;
          cursor: pointer;
          position: absolute;
          right: 16px;
        }
      }

      .center {
        width: 100%;
        display: flex;
        flex-direction: row;
        min-height: 100px;
        // justify-content: space-between;
        .ant-spin, span{
          margin: 0 auto;
        }
        .previous_btn {
          position: absolute;
          cursor: pointer;
          left: 1%;
          top: 45%;
          transform: translate(0, -35%);
          width: 30px;
          height: 30px;
          background: #9d9d9d;
          // box-shadow: 0px 4px 10px 0px rgb(0 0 0 / 4%);
          opacity: 0.7;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 18px;
          z-index: 1;
          > span {
            font-size: 18px;
            color: #ffffff;
          }
        }
        .next_btn {
          position: absolute;
          cursor: pointer;
          right: 1%;
          top: 45%;
          transform: translate(0, -35%);
          width: 30px;
          height: 30px;
          background: #9d9d9d;
          // box-shadow: 0px 4px 10px 0px rgb(0 0 0 / 4%);
          opacity: 0.7;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 18px;
          z-index: 1;
          > span {
            font-size: 18px;
            color: #ffffff;
          }
        }
        .sequence_item_wrap:not(.firstItem) {
          margin-left: 2.5%;
        }
        .sequence_item_wrap {
          cursor: pointer;
          width: 14%;
          > div {
            .sequence_item {
              .sequence_item_content {
                position: relative;
                text-align: center;
                .imgContainer {
                  // width: 180px;
                  width: 100%;
                  height: 107px;
                  background: #f7f8fa;
                  border-radius: 4px;

                  > img {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                  }
                  .imgScale {
                    transform: scale(0.5);
                  }
                }
                .name_ {
                  white-space: nowrap;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  display: block;
                  padding: 10px 0 20px 0;
                }
              }
              .opacityDom {
                position: relative;
                opacity: 0.35;
              }
              .mixup_container_doing {
                display: flex;
                flex-direction: column;
                align-items: center;
                position: absolute;
                left: 50%;
                transform: translate(-50%, -35%);
                top: 35%;
                img {
                  width: 20px;
                }
                span:last-child {
                  color: #d5d8d8;
                }
              }
              .backgroundDom {
                background: black !important;
              }
              .mixup_container_failed {
                display: flex;
                flex-direction: column;
                align-items: center;
                position: absolute;
                left: 50%;
                transform: translate(-50%, -35%);
                top: 35%;
                img {
                  width: 20px;
                }
                > span {
                  color: #d5d8d8;
                }
              }
              .reStartMixup {
                border: 1px solid;
                border-radius: 5px;
              }
            }
          }
        }
      }
    }
    .opt_top {
      display: flex;
      align-items: center;
      label,
      button {
        margin-right: 16px;
      }
      .bindMap {
        line-height: 10px;
        height: 32px;
      }
      .ant-checkbox-wrapper {
        margin-left: 12px;
      }
    }
  }
  .tool_bar{
    width: 30px;
    background: #F7F9FA;
    .item{
      padding: 15px 0px;
      color: #fff;
      border-radius: 5px;
      cursor: pointer;
      font-size: 16px;
      color: #4A4F64;
      text-align: center;
      background-color: #fff;
      img{
        margin-bottom: 6px;
      }
      &.active{
        background-color: var(--primary-bg-color);
        color: var(--primary-color);
      }
    }
  }
  .entity_info_wrapper {
    padding: 0 15px;
    width: 470px;
    background: #ffffff;
    border: 1px solid rgba(241, 241, 241, 0.8);
    overflow: hidden;
    
    .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item, .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-submenu {
      padding: 0 10px;
    }
    .entity_info{
      margin-top: 20px;
      height: calc(100% - 67px);
      .voice_div{
        height: 100%;
      }
      .ant-empty{
        margin-top: 200px;
      }
      .item_ {
        width: 150px;
        margin: 20px auto;
        height: 36px;
        border-radius: 18px;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: var(--primary-color);
        opacity: 0.88;
        &:hover{
          background: var(--second-color);
        }
        span{
          color: #fff;
          margin-left: 5px;
        }
      }
      .entity_info_item{
        height: 100%;
        overflow: auto;
        cursor: pointer;
        .related_box{
          margin-bottom: 20px;
          .related_wrp{
            display: flex;
            align-items: center;
            justify-content: space-between;
            > span{
              cursor: pointer;
              color: var(--primary-color);
            }
          }
          
          .related_resource{
            display: flex;
            span{
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              cursor: pointer;
              color: var(--primary-color);
            }
          }
        }
        
        .ocr_wrap{
          margin-top: 15px;
          position: relative;
          
          .header{
            display: flex;
            justify-content: space-between;
            .time{
              background-color: #f2f2f2;
              padding: 0px 10px;
              color: #8a8a8a;
            }
            .remove{
              color: var(--primary-color);
              display: none;
            }
            
          }
          .ocr_content{
            user-select: text;
          }
          .picture{
            position: absolute;
            right: 0;
            top: 0;
          }
          &:hover{
            .time{
              background-color: var(--active-bg-color);
              color: var(--primary-color);
            }
            .header .remove{
              display: block;
            }
          }
        }
        .voice_knowledge_wrap{
          margin-bottom: 10px;
          padding:10px;
          cursor: pointer;
          background: var(--primary-bg-color);
          border-radius: 10px;
          p, .ant-typography{
            margin-bottom: 5px;
          }
          .voice_knowledge_time{
            margin-right: 10px;
            font-size: 12px;
          }
          .voice_knowledge_title{
            font-size: 15px;
          }
          .voice_knowledge{
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            div{
              color: var(--primary-color);
            }
            .voice_knowledge_time{
              font-size: 15px;
            }
            .voice_knowledge_title{
              font-size: 15px;
              font-weight: 600;
            }
            
          }
          .chapter_content{
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: normal;
          }
          .knowledgePoints{
            .knowledgePoint{
              display: flex;
              margin-bottom: 5px;
              align-items: center;
            }
          }
          &.active{
            background-color: var(--active-bg-color);
            color: var(--primary-color);
            .right{
              .knowledge, .ana{
                color: var(--primary-color);
              }
            }
          }
          .left{
            font-size: 15px;
            width: 22px;
            height: 22px;
            background-color: var(--active-bg-color);
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
            border-radius: 50%;
            text-align: center;
            line-height: 20px;
            margin-right: 15px;
          }
          .right{
            flex: 1;
            .knowledge{
              line-height: 1;
              margin-bottom: 10px;
              font-size: 16px;
              padding-bottom: 13px;
              color: #4A4F64;
              border-bottom: 1px dashed #CBCBCB;
            }
            .ana{
              color: #868686;
              font-size: 14px;
            }
          }
        }
        .basicMetaName {
          font-weight: 500;
          color: #4A4F64;
          font-size: 18px;
          position: relative;

          #name{
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .edit-confirm{
            font-size: 22px;
            svg{
              color: var(--primary-color);
            }
          }
          
          .ant-form {
            display: flex;
            align-items: center;
            justify-content: space-between;
          }
          .ant-form-item{
              margin-bottom: 0;
              width: 88%;
          }
          .guanbi{
            margin: 0px 15px 0 10px;
          }
          .edit{
            position: absolute;
            right: -20px;
            top: 0;
            margin-left: 10px;
            opacity: 0;
            color: var(--primary-color);
          }
          &:hover .edit{
            opacity: 1;
          }
        }
        .basicMetaKeyword, .basicMetaImportuser,.basicOther{
          position: relative;
          background: #F9FAFB;
          border-radius: 10px;
          margin-top: 15px;
          padding: 15px;
          
          .ant-select-selector{
            border: none;
            padding-left: 10px;
          }
          .ant-select-selection-overflow-item{
            position: relative;
            margin-right: 10px;
            margin-bottom: 18px;
          }
          .ant-form-item-control-input-content{
            color: #868686;
          }
          .ant-tag{
            border: none;
            color: var(--primary-color);
            padding: 0;
            margin-right: 22px;
          }
          .edit{
            position: absolute;
            right: 20px;
            top: 20px;
            opacity: 0;
            font-size: 18px;
            color: var(--primary-color);
          }
          &:hover .edit{
            opacity: 1;
          }
          .edit-confirm{
            position: absolute;
            right: 20px;
            top: 20px;
            svg{
              font-size: 22px;
              color: var(--primary-color);
            }
          }
          .vd_ghost_btn{
            position: absolute;
            right: 65px;
            top: 15px;
          }
        }
        .basicMetaKeyword{
          .timeRange{
            width: 100%;
            height: 5px;
            background-color: #d8e7fb;
            position: relative;
            border-radius: 5px;
            .dot{
              position: absolute;
              top: 0;
              width: 5px;
              height: 5px;
              border-radius: 50%;
              background-color: #549dff;
              &:hover .time{
                display: block;
              }
              .num{
                position: absolute;
                top: -23px;
                left: 0;
                color: #549dff;
              }
              .time{
                position: absolute;
                bottom: -23px;
                left: 0;
                display: none;
                background-color: #f2f2f2;
                color: #939393;
                font-size: 12px;
              }

            }
          }
          .ant-select-selection-item{
            height: 26px;
            line-height: 26px;
            padding:0 15px;
            background: var(--active-bg-color);
            border-radius: 13px;
            text-align: center;
            color: var(--primary-color);
            position: static;
            .ant-select-selection-item-remove{
              position: absolute;
              top: -8px;
              right: 5px;
              color: red;
            }
          }
          .ant-tag{
            line-height: 26px;
            padding: 0 15px;
            background: var(--primary-bg-color);
            border-radius: 13px;
            text-align: center;
            color: var(--primary-color);
            margin-right: 10px;
            .ant-tag-close-icon{
              position: absolute;
              top: -9px;
              right: 5px;
              color: red;
            }
            
          }
        }
        .basicMetaKeyword, .basicOther {
          .ant-row{
            flex-direction: column;
          }
          .sensitiveTag{
            margin-bottom: 5px;
          }
          .ant-form-item-label{
            padding-left: 16px;
            label{
            font-weight: 500;
            color: #4A4F64;
            }
          }
          label::after{
            content: '';
            width: 6px;
            height: 6px;
            background: var(--primary-color);;
            border-radius: 50%;
            position: absolute;
            left: -18px;
            top: 13px;
          }
        }
        .basicMetaImportuser{
          label::after{
            content: '';
          }
          .ant-form-item-label{
            width: 90px;
          }
          .ant-tag{
            color: #868686;
          }
        }
        .basicOther {
          .sensitive{
            display: flex;
            color: #4A4F64;
            align-items: center;
            margin-bottom: 8px;
          }
          .sensitiveTag{
            border: none;
            color: #549CFF;
            padding: 0;
            margin-left: 10px;
            margin-right: 0;
            display: inline-block;
            width: 80%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .ant-form-item-control-input-content{
            color: #FF6154;
          }
        }
        .time-line {
          height: 100%;
          .ant-timeline{
            max-height: calc(100% - 45px);
            overflow-y: auto;
          }
          .ant-timeline-item {
            padding-bottom: 16px;
          }
        }
        .lyric_item {
          display: flex;
          cursor: pointer;
          color: #363636;
          align-items: center;
  
          &:hover,
          &.active {
            color: #f0a95d;
          }
  
          .lyric_time {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 90px;
            height: 36px;
            background: #eeeff3;
          }
  
          .lyric_title {
            display: flex;
            align-items: flex-start;
            margin-left: 10px;
            flex-direction: column;
            .voice-search-value {
              color: red;
            }
            .lyric_title_orignal {
              display: flex;
              flex-direction: row;
            }
            .lyric_title_translate {
              display: flex;
              flex-direction: row;
            }
            .editDiv {
              display: flex;
              flex-direction: row;
              align-items: center;
              > span {
                font-size: 16px;
                margin-left: 10px;
                cursor: pointer;
              }
            }
          }
  
          .icon {
            display: flex;
            align-items: center;
            margin-left: 6px;
            opacity: 0;
          }
        }
  
        .lyric_item:hover {
          .icon {
            opacity: 1;
          }
        }
      }
    }

    .detail-resource-tabcontainer {
      width: 100%;
      height: calc(100% + 67px);
      margin-top: -20px;
      width: calc(100% + 40px);
      margin-left: -20px;
      background-color: var(--active-bg-color);

      .resource-chat-tabContent {
        width: 100%;
        height: 100%;

        .ant-spin-nested-loading {
          width: 100%;
          height: 100%;
        }
      
        .ant-spin-container {
          width: 100%;
          height: 100%;
        }
        #resource-chat-iframe, iframe,
        .resource-chat-style, .chat-center-container {
          background-color: var(--active-bg-color);
        }
      }
    }

    .sequence_list {
      position: relative;
      width: 100%;
      height: 100%;
      .ant-checkbox-group , .ant-radio-group{
        display: block;
        height: calc(100% - 45px);
        overflow-y: auto;
        font-size: 13px;
      }
      .sequence_operator {
        margin-left: 12px;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 30%;
        .vd_ghost_btn {
          color: var(--primary-color);
          border-color: var(--primary-color);
        }
        .create {
          margin-right: 16px !important;
        }
      }
      .sequence_item_wrap {
        padding: 0 12px;
        display: flex;
        // width: 364px;
        align-items: center;

        // .sequence_item_checkbox {
        //   // margin-right: 4%;
        // }
        &.active {
          background: var(--primary-bg-color);
        }
        .sequence_item {
          position: relative;
          display: flex;
          cursor: pointer;
          height: 72px;
          margin: 12px 0;
          .sequence_item_content {
            width: 130px;
            position: relative;
            text-align: center;
            .imgContainer {
              width: 130px;
              height: 100%;
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: center;
              > img {
                // width: 130px;
                // height: 100%;
                height: 72px;
                width: 128px;
                object-fit: contain;
                background: #0a2e4b;
              }
            }
            > img {
              // width: 130px;
              // height: 100%;
              height: 72px;
              width: 128px;
              object-fit: contain;
              background: #0a2e4b;
            }
            .ant-spin {
              position: absolute;
              top: 44%;
              left: 45%;
            }
            .timeSpan {
              // color: #747477;
              position: absolute;
              font-size: 12px;
              bottom: 0;
              color: white;
              background: black;
              width: 100;
              width: 100%;
              left: 0px;
              opacity: 0.5;
              display: flex;
              flex-direction: row;
              justify-content: flex-end;
              flex-wrap: nowrap;
              span {
                transform: scale(0.9);
                display: inline-block;
                margin-right: 4px;
              }
            }
          }
          .opacityDom {
            position: relative;
            opacity: 0.35;
          }
          .mixup_container_doing {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: absolute;
            left: 44px;
            top: 20%;
            img {
              width: 20px;
            }
            span:last-child {
              color: #d5d8d8;
            }
          }
          .backgroundDom {
            background: black !important;
          }
          .mixup_container_failed {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: absolute;
            left: 37px;
            top: 20%;
            img {
              width: 20px;
            }
            span:last-child {
              color: #d5d8d8;
            }
          }
          .reStartMixup {
            border: 1px solid;
            border-radius: 5px;
          }
          .sequence_content {
            // margin-right: 48px;
            height: 100%;
            display: flex;
            flex-direction: column;
            margin-left: 12px;
            justify-content: space-between;
            width: 100%;
            .sequence_content_top {
              display: flex;
              flex-direction: column;
              justify-content: space-between;
            }
            .sequence_title {
              .text_overflow_multi(1);
            }
            .keywords_modal{
              position: absolute;
              top: 0;
              left: 0;
              z-index: 1000;
              background: #F7F9FA;
              border-radius: 4px;
              padding: 10px 0;
            }
            .sequence_keywords_box{
              position: relative;
            }
            .keywords_modal, .sequence_keywords{
                width: 100%;
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
              .keyword {
                line-height: 25px;
                border-radius: 12px;
                padding: 0 10px;
                background-color: var(--active-bg-color);
                font-size: 13px;
                color: var(--primary-color);
              }
            }
            .sequence_keywords {
              padding-top: 10px;
              max-height: 35px;
              overflow: hidden;
            }

            span {
              font-size: 12px;
            }
          }

          .del_icon_product {
            z-index: 10;
            .ant-btn-link {
              width: 24px;
              height: 24px;
              span {
                font-size: 12px;
              }
            }
          }
        }
      }
    }
    .key-search-value {
      color: red;
    }
    .voic_loading {
      width: 100%;
      height: auto;
      display: flex;
      align-items: center;
      margin-top: 20%;
      justify-content: center;
      flex-wrap: wrap;

      .anticon {
        color: var(--primary-color);
        svg {
          width: 35px;
          height: 35px;
        }
      }
      .loading_img {
        width: 35px;
      }

      .loading_span {
        width: 100%;
        text-align: center;
        margin-top: 10px;
        font-size: 14px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: var(--primary-color);
      }
    }
    .voic_loading_failed {
      .anticon {
        color: red;
        svg {
          width: 35px;
          height: 35px;
        }
      }
      .loading_span {
        color: red;
      }
      // .ant-btn{
      //   background: red;
      //   border-color:red;
      // }
    }
    // 修改antd默认样式
    .ant-tabs-nav {
      // margin: 0 0 20px 0;
    }
    // .ant-tabs-nav-wrap {
    //   display: flex;
    //   justify-content: flex-start;

    //   .ant-tabs-tab:first-child {
    //     // margin: 0;
    //   }
    // }
    .ant-tabs-nav-operations {
      .ant-tabs-nav-more {
        cursor: pointer;
        margin: 0 !important;
        padding: 8px !important;
      }
    }
    .ant-tabs,
    .ant-tabs-content {
      height: 100%;
    }
    .ant-tabs-tab-btn {
      font-weight: 800;
    }

    .ant-form-item-label {
      text-align: left;
    }

    .ant-tabs-tabpane {
      height: 100%;
      // max-height: calc(100vh - 180px);
      overflow-y: auto;
    }

    .editor_selections {
      margin-bottom: 10px;
      // float: right;
      // .ant-btn{
      //   color: var(--primary-color);
      //   border-color: var(--primary-color);
      //   margin-left: 4.6%;
      // }
      .vd_ghost_btn {
        color: var(--primary-color);
        border-color: var(--primary-color);
        margin-left: 20px;
      }
      .vd_ghost_btn_disabled {
        margin-left: 4.6%;
      }
    }
    .editor_selection_confirm {
      margin-bottom: 10px;
      .ant-btn {
        color: var(--primary-color);
        border-color: var(--primary-color);
        margin-left: 20px;
      }
    }
    .editor_selection {
      float: right;
      width: 40px;
      font-size: 17px;
      display: flex;
      flex-direction: column;
      height: 50px;
      justify-content: space-between;
    }
    .basicMetaForm {
      .basicTeacherItem {
        .ant-form-item-control {
          width: 50%;
        }
      }
      .ant-form-item-control-input-content {
        #character {
          .ant-tag {
            border: 0;
            background-color: white;
            &.jumpTag {
              color: var(--primary-color);
            }
          }
        }
        .ant-tag.jumpTag {
          background-color: var(--active-bg-color);
          cursor: pointer;
        }
        .ant-tag.sensitiveTag {
          background-color: white;
          cursor: pointer;
          color: #f7a128;
          border: 0;
        }
      }
    }
    .interactiveTest {
      .ant-select-selection-item,
      .ant-select-item-option-content {
        display: flex;
      }
      .versionText {
        width: 100px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .icon:hover {
      color: var(--primary-color);
    }

    .voice_div {
      .voice-input {
        margin-bottom: 10px;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: space-evenly;
        .voice-input-inner {
          position: relative;
          width: 52%;
          .ant-input-group-addon {
            &:first-child {
              background: #ffffff;
            }
            .ant-select {
              .ant-select-selector {
                // padding: 0 4px;
              }
            }
            > .anticon-up {
              position: absolute;
              right: 4px;
              top: 2px;
              opacity: 0.7;
            }
            > .anticon-down {
              position: absolute;
              right: 4px;
              bottom: 2px;
              opacity: 0.7;
            }
          }
        }
        > .ant-select {
          width: 25%;
        }
        button {
          margin-left: 0px;
        }
        .text_check {
          padding: 4px 8px;
        }
        .checked {
          color: #a6a6a6;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
          > span {
            margin-right: 5px;
          }
        }
        // .changeVision {
        //   margin-left: 100px;
        // }
      }
      .time-line {
        height: 100%;
        max-height: calc(100% - 45px);
        padding-top: 20px;
        overflow-y: auto;

        .ant-timeline-item {
          padding-bottom: 16px;
        }
      }
      .lyric_item {
        display: flex;
        cursor: pointer;
        color: #363636;
        align-items: center;

        &:hover,
        &.active {
          color: #f0a95d;
        }

        .lyric_time {
          display: flex;
          align-items: center;
          justify-content: center;
          min-width: 90px;
          height: 36px;
          background: #eeeff3;
        }

        .lyric_title {
          display: flex;
          align-items: flex-start;
          margin-left: 10px;
          flex-direction: column;
          .voice-search-value {
            color: red;
          }
          .lyric_title_orignal {
            display: flex;
            flex-direction: row;
          }
          .lyric_title_translate {
            display: flex;
            flex-direction: row;
          }
          .editDiv {
            display: flex;
            flex-direction: row;
            align-items: center;
            > span {
              font-size: 16px;
              margin-left: 10px;
              cursor: pointer;
            }
          }
        }

        .icon {
          display: flex;
          align-items: center;
          margin-left: 6px;
          opacity: 0;
        }
      }

      .lyric_item:hover {
        .icon {
          opacity: 1;
        }
      }
      .paragraph-wrapper {
        position: relative;
        text-align: justify;
        height: 100%;
        max-height: calc(100% - 45px);
        .right_ {
          position: absolute;
          top: 0;
          right: 0;
        }
        .paragraph-content {
          user-select: text;
          padding: 20px 10px;
          height: calc(100% - 50px);
          overflow-y: auto;
          .text{
            word-break: break-all;
            hyphens: auto;
          }
          .voice-search-value {
            color: red;
          }
          .voicetext{
            .icon{
              margin: -3px 3px 0;
              display: none;
              cursor: pointer;
              color: var(--primary-color);
              font-size: 15px;
            }
            
            span :hover {
              background-color: var(--primary-color);
              color: #fff;
              padding: 2px 0px;
            }
          }
          .editDiv {
            input{
              width: 150px;
            }
            > span {
              font-size: 16px;
              margin-left: 10px;
              cursor: pointer;
            }
          }
          
          .selected {
            text-decoration: underline;
          }
          .currentPlay {
            color: var(--primary-color);
          }
        }

        .text_pop {
          display: none;
          position: absolute;
          min-width: 72px;
          top: 0;
          left: 3px;
          padding: 8px;
          background-color: #fff;
          color: var(--primary-color);
          border-radius: 3px;
          cursor: pointer;
          box-shadow: 0 0 8px rgba(0, 0, 0, 0.15);

          &::after {
            content: ' ';
            width: 0;
            height: 0;
            position: absolute;
            bottom: -6px;
            left: 50%;
            transform: translateX(-50%);
            border-top: 6px solid #fff;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
          }
        }
        .tips {
          width: 80%;
          color: #a6a6a6;
          font-size: 14px;
          font-weight: bold;
          margin: 5px 10px;
          .anticon-exclamation-circle {
            margin-right: 5px;
          }
        }
      }
    }
    .courseware_list {
      width: 60%;
      .pagination{
        width: 380px;
        text-align: center;
      }
      .ant-timeline {
        .ant-timeline-item {
          display: flex;
          flex-direction: row;
          cursor: pointer;
          .ant-timeline-item-label {
            top: 0;
          }
          .ant-timeline-item-tail {
            display: block;
          }
          .ant-timeline-item-head {
            top: 5px;
          }
          .ant-timeline-item-content {
            position: relative;
            top: 0px;
            width: 224px;
            height: 126px;
            img {
              width: 224px;
              height: 126px;
              object-fit: contain;
              background: #000000;
            }
            .number {
              position: absolute;
              top: 0;
              width: 100px;
              height: 20px;
              background: #00000050;
              color: white;
              // justify-content: center;
            }
            .currentCover {
              cursor: pointer;
              height: 18px;
              display: flex;
              position: absolute;
              right: 5px;
              bottom: 5px;
              padding: 0 3px;
              background: #00000050;
              color: #ffffff;
              border-radius: 5px;
              font-size: 14px;
              display: flex;
              justify-content: center;
              align-items: center;
            }
            .setCoverBtn {
              visibility: hidden;
              cursor: pointer;
              height: 18px;
              display: flex;
              position: absolute;
              right: 5px;
              bottom: 5px;
              padding: 0 3px;
              background: #00000050;
              color: #ffffff;
              border-radius: 5px;
              font-size: 14px;
              display: flex;
              justify-content: center;
              align-items: center;
            }
            .deleteBtnDiv {
              visibility: hidden;
              cursor: pointer;
              width: 18px;
              height: 18px;
              display: flex;
              position: absolute;
              right: 5px;
              top: 5px;
              background: #00000050;
              color: #ffffff;
              border-radius: 50%;
              font-size: 14px;
              flex-direction: row;
              align-items: center;
              justify-content: center;
            }
            &:hover {
              .deleteBtnDiv,
              .setCoverBtn {
                visibility: visible !important;
              }
            }
          }
        }
        .lastItem {
          .ant-timeline-item-tail {
            display: none;
          }
        }
      }
      .courseware_item_wrap.active {
        .ant-timeline-item-label {
          color: var(--primary-color);
        }
        .ant-timeline-item-content {
          img {
            border: 3px solid var(--primary-color);
          }
        }
      }
    }
    .msgCountNum {
      font-weight: 400;
      font-size: 12px;
    }
    .refreshBtn {
      display: flex;
      width: 60px;
      flex-direction: row;
      align-items: center;
      padding: 0px 5px;
      justify-content: space-between;
      img {
        width: 15px;
      }
    }
    .intelligentSummary {
      margin: 0 20px;
      height: 100%;
      > div {
        margin-bottom: 15px;
        .ant-btn {
          margin-right: 10px;
        }
      }
      textarea {
        height: calc(100% - 50px);
        resize: none;
        &[readonly] {
          border: 0;
        }
      }
    }
    .intelligentSummaryEmpty {
      display: flex;
      flex-direction: column;
      align-items: center;
      .ant-spin {
        margin: 20px 0;
      }
      .ant-btn {
        width: 200px;
        margin-top: 20px;
      }
    }
    .ant-tabs-content-holder {
      .ant-form {
        .ant-row .ant-col-9 {
          flex: none;
        }
      }
      .circleChart_statistisc {
      }
    }
    .quesionList {
      height: 100%;
      padding: 0 20px;
      .pagination {
        position: absolute;
        right: 10px;
        bottom: 10px;
      }
    }
    //针对视频弹性布局 其余都只居左显示
    .entity_video {
      // .ant-tabs-nav-list {
      //   width: 100%;
      //   display: flex;
      //   flex-direction: row;
      //   justify-content: space-evenly;
      //   .ant-tabs-tab{
      //     margin: 0;
      //   }
      // }
    }
  }
}

.full_screen_style {
  .content {
    display: none;
  }

  .entity_info_wrapper {
    width: calc(100% - 30px) !important;
  }
}

.topicContent {
  img {
    max-width: 50px;
    height: auto;
}
  .answers {
    margin-top: 10px;

    .answer-item {
      display: flex;
      // align-items: center;
      flex-direction: row;
      margin-bottom: 15px;

      .radio-content {
        font-size: 14px;
      }

      .ant-tag {
        margin-left: 10px;
      }
    }

    .parse-item {
      & > span {
        cursor: pointer;
        color: #aaa;
      }
    }

    .answer-item {
      > span:first-child {
        margin-right: 10px;
        width: 60px;
      }

      .ant-input {
        width: 300px;
      }
    }

    .subjective-topic-answer {
      width: 800px;
      border: 1px solid #d8d8d8;
      background: #f7f8fa;
      height: 100px;
      overflow: auto;
    }

    .mark-container {
      // color: var(--primary-color);
      display: flex;
      align-items: center;
      height: 20px;
    }

    .comment-container {
      display: flex;

      & > span {
        flex-shrink: 0;
        margin-right: 10px;
      }

      & > div {
        width: calc(100% - 119px);
      }
    }
  }
}
.intelligentDisassembly {
  .btn{
    margin-bottom: 20px;
    button {
      margin-right: 10px;
      &.active{
        background-color: #1890ff;
        color: #fff;
      }
    }
  }
  .knowledgePoint {
    display: flex;
  }
}
.repairModal {
  .ant-modal-body {
    height: 600px;
    overflow: auto;
  }
}
.createTitleModal{
  .ant-select-multiple{
    .ant-select-selection-overflow-item{
      position: relative;
      margin-right: 10px;
      margin-bottom: 18px;
    }
    .ant-select-selection-item{
      height: 26px;
      line-height: 26px;
      padding:0 15px;
      background: var(--active-bg-color);
      border-radius: 13px;
      text-align: center;
      color: var(--primary-color);
      position: static;
      .ant-select-selection-item-remove{
        position: absolute;
        top: -8px;
        right: 5px;
        color: red;
      }
    }
  }
}
.uploadbox{
  .right {
    display: flex;
    align-items: center;
    .ant-select {
      width: 300px;
      height: 32px;
      margin-left: 10px;
    }
  }
}
@media (max-width: 1789px) {
  .video_detail_wrapper {
    .content {
      flex: 1;
      min-width: 800px;
      // .entity_view{
      //   width: 1198px;
      //   .entity_view_wrapper {
      //     // width: 1100px;
      //     // 37px 为水平控件宽度，58px 为垂直控件高度
      //     // height: calc((1100px - 37px) * 9 / 16 + 58px);
      //     width: 1198px;
      //     // 37px 为水平控件宽度，58px 为垂直控件高度
      //     // height: calc((1198px - 37px) * 9 / 16 + 63px);
      //   }
      // }
    }
    .entity_info_wrapper {
      width: 430px;
    }
  }
}
@media (max-width: 1600px) {
  .video_detail_wrapper {
    .content {
      // width: 1398px;
      flex: 1;
      min-width: 800px;
      // .entity_view{
      // width: 1098px;
      // .entity_view_wrapper {
      //   width: 1098px;
      // }
      // }
    }
    .entity_info_wrapper {
      width: 430px;
    }
  }
}
@media (max-width: 1440px) {
  .video_detail_wrapper {
    .content {
      flex: 1;
      min-width: 800px;
      // .entity_view{
      //   width: 954px;
      //   .entity_view_wrapper {
      //     width: 954px;
      //     // height: calc((654px - 37px) * 9 / 16 + 63px);
      //   }
      // }
    }
    .entity_info_wrapper {
      width: 430px;
    }
  }
}
@media (max-width: 1366px) {
  .video_detail_wrapper {
    .content {
      flex: 1;
      min-width: 800px;
      .entity_view {
        // width: 754px;
        .entity_view_wrapper {
          // width: 754px;
          // height: calc((654px - 37px) * 9 / 16 + 63px);
        }
      }
      .video_btn {
        font-size: 12px !important;
        .left_ {
          .item_ {
            margin-left: 10px !important;
            // &:last-child{
            //   width: 110px;
            //   height: 32px;
            //   border-radius: 18px;
            //   background: linear-gradient(134deg, #549CFF 0%, #6083FF 100%);
            //   span{
            //     color: #fff !important;
            //   }
            // }
            .split {
              margin-left: 10px !important;
            }
          }
        }
      }
    }
    .entity_info_wrapper {
      width: 430px;
    }
  }
}
@media (max-width: 768px) {
  .ant-drawer-content-wrapper {
    width: 100% !important;
  }
  .resource_audit_logs_drawer > .ant-drawer-content-wrapper {
    width: 100% !important;
  }
  
  .video_detail_wrapper {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    align-content: center;
    align-items: center;
    min-height: calc(100vh - 52px) !important;
    .entity_info_wrapper {
      position: relative;
      margin-top: 17px;
    }
    .content {
      width: 100%;
      // margin-left: 0;
      flex: unset;
      min-width: unset;
      .entity_view {
        width: 100%;
        .title {
          padding: 0 1%;
        }
        .entity_view_wrapper {
          padding: 0 1%;
          width: 100%;
          height: calc(100vw * 9 / 16 + 160px) !important;
          iframe{
            position: unset !important;
          }
          .mejs__container {
            .mejs__volumn-track {
              display: none;
            }
          }
          .mejs__inner{
            .mejs__controls {
              .mejs__captions-button{
                right: 65px;
              }
              .mejs__speed-button{
                right: 35px;
              }
            }
          }
          .controls-box{
            bottom: 7px;
            right: 35px;
          }
        }
        .video_btn {
          height: auto;
          overflow-x: auto;
          display: block;
          border-radius: 11px;
          margin: 17px;
          padding: 17px;
          margin-bottom: 0;
          line-height: unset;
          white-space: nowrap;
          line-height: 1;
          .left_ {
            width: 100%;
            display: inline;
            clear: both;
            font-size: 15px !important;
            svg{
              color: var(--primary-color);
            }
            .anticon-close-circle {
              margin-left: 0 !important;
            }
            .audit_pass {
              display: inline-flex;
              vertical-align: middle;
              .anticon-check-circle {
                margin-left: 0 !important;
              }
            }
            & > span {
              vertical-align: middle;
            }
            .item_ {
              margin: 0;
              display: inline-flex;
              .split {
                margin-left: 10px;
              }
            }
            .audit_title {
              margin-left: 0;
            }
          }
          .right_ {
            display: inline;
            width: auto;
            min-width: unset;
            margin-left: 0;
            // margin-top: 17px;
            flex-wrap: wrap;
            .audit_option + .look_log {
              margin-left: 5vw;
            }
            .audit_reject {
              width: 100%;
              margin-left: 0;
              & + .look_log {
                margin-top: 10px;
              }
            }
            .look_log {
              height: 32px;
              line-height: 32px;
              padding: 0 12px;
              border-radius: 99px;
            }
            .logs_btn {
              display: inline-block;
              height: 34px;
              position: relative;
              margin-left: 15px;
              &::before {
                content: '';
                position: absolute;
                width: 1px;
                height: 18px;
                background-color: #d9d9d9;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
              }
            }
          }
        }
      }
    }
    .entity_info_wrapper {
      width: calc(100% - 34px) !important;
      margin-left: 0;
      border-radius: 11px;
      .ant-tabs-content-holder {
        .basicMetaForm {
          .ant-form-item {
            margin-bottom: 15px !important;
            .ant-row {
              .ant-col {
                flex: 1;
              }
              .ant-form-item-label {
                flex: none !important;
                &::after {
                  content: ':';
                  position: relative;
                  top: -0.5px;
                  margin: 0 8px 0 2px;
                }
              }
            }
          }
        }
      }
      .ant-tabs {
        padding: 0;
        box-sizing: border-box;
        margin-top: 6px;
        color: #9d9d9d;
        .ant-tabs-nav-list .ant-tabs-tab:first-child {
          margin-left: 0;
        }
        .ant-tabs-nav-wrap {
          &::before,
          &:after {
            display: none !important;
          }
        }
      }
      .editor_selections {
        text-align: center;
        .vd_ghost_btn {
          margin-left: 0;
          background-color: var(--active-bg-color);
          height: 32px;
          line-height: 32px;
          border-radius: 99px;
          padding: 0 22px;
          border: unset;
        }
      }
      .ant-tabs-content-holder .basicMetaForm {
        margin: 0;
        .ant-form-item {
          margin-bottom: 6px !important;
          font-size: 15px;
          .ant-form-item-control {
            text-align: right;
            color: #9d9d9d;
          }
          .ant-form-item-label > label {
            font-size: 15px;
          }
        }
      }
      font-size: 15px;
      .editor_selection_confirm {
        text-align: center;
        .ant-btn:first-child {
          margin-left: 0;
        }
      }
      .quesionList {
        padding: 0;
        padding-bottom: 60px;
        & > div {
          & > .ant-select {
            width: 88%!important;
            margin-right: 10px;
            margin-bottom: 10px;
          }
          .versionText{
            width: 181px;
          }
          & > .vd_ghost_btn{
            margin-left: 0!important;
          }
        }
        .pagination{
          right: 50%;
          transform: translateX(50%);
        }
      }
    }
  }
}
.mobile_detail {
  .content {
    .entity_view {
      .video_btn {
        .left_ {
          .item_ {
            .anticon {
              font-size: 20px !important;
            }
          }
        }
      }
    }
  }
  .mobile_relation_video {
    width: 100%;
    background: white;
    .head {
      font-size: 16px;
      margin-left: 3%;
      padding: 10px 0;
    }
    .body {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-wrap: wrap;
      .sequence_item {
        width: 41%;
        margin: 0 0 10px 6%;
        .sequence_item_content {
          position: relative;
          text-align: center;
          .imgContainer {
            // width: 130px;
            height: 107px;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            > img {
              width: 100%;
              height: 100%;
              object-fit: contain;
              background: #000000;
            }
            .imgScale {
              transform: scale(0.5);
            }
          }
          .name_ {
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            width: 100%;
            display: block;
          }
          > img {
            // width: 130px;
            // height: 100%;
            height: 72px;
            width: 128px;
            object-fit: contain;
            background: #0a2e4b;
          }
          .ant-spin {
            position: absolute;
            top: 44%;
            left: 45%;
          }
          .timeSpan {
            // color: #747477;
            position: absolute;
            font-size: 12px;
            bottom: 0;
            color: white;
            background: black;
            width: 100;
            width: 100%;
            left: 0px;
            opacity: 0.5;
            display: flex;
            flex-direction: row;
            justify-content: flex-end;
            flex-wrap: nowrap;
            span {
              transform: scale(0.9);
              display: inline-block;
              margin-right: 4px;
            }
          }
        }
        .opacityDom {
          position: relative;
          opacity: 0.35;
        }
        .mixup_container_doing {
          display: flex;
          flex-direction: column;
          align-items: center;
          position: absolute;
          left: 50%;
          transform: translate(-50%, -35%);
          top: 35%;
          img {
            width: 20px;
          }
          span:last-child {
            color: #d5d8d8;
          }
        }
        .backgroundDom {
          background: black !important;
        }
        .mixup_container_failed {
          display: flex;
          flex-direction: column;
          align-items: center;
          position: absolute;
          left: 50%;
          transform: translate(-50%, -35%);
          top: 35%;
          img {
            width: 20px;
          }
          > span {
            color: #d5d8d8;
          }
        }
        .reStartMixup {
          border: 1px solid;
          border-radius: 5px;
        }
      }
    }
  }
}

.mobileModal {
  top: 400px !important;
}
.relatedModal{
    .related_item{
      margin-bottom: 10px;
      display: flex;
      .associated_name{
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
  }
  
}

.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
  background-color: var(--primary-bg-color) !important;
}