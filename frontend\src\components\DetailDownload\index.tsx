import React, { useState, useEffect, useRef } from 'react';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';
import { Modal, Checkbox, Button, Table, Tag, Tooltip, Progress } from 'antd';
import deleteApis from '@/service/deleteApis';
import { IconFont } from '@/components/iconFont/iconFont';
import contentListApis from '@/service/contentListApis';
import './index.less';
import { IPermission } from '@/models/permission';
import { useSelector, useIntl } from 'umi';
import globalParams from '@/permission/globalParams';
import { debounce } from '@/utils';
import entityApis from '@/service/entityApis';
const CheckboxGroup = Checkbox.Group;
interface CreateModalProps {
  modalVisible: boolean;
  modalClose: () => void;
  shareFlag?: boolean;
  callback?: (data:any) => void;
  className_?: string;
  downloadlist: any;
}
interface Idowndata {
  entityName: string;
  contentId: string;
  orignal:any,
  transfer:any
}
interface DownLoad {
  downloadlink: string;
  downloadname: string;
}
const detailDownloadModal: React.FC<CreateModalProps> = props => {
  const intl = useIntl();
  const { modalClose, modalVisible, downloadlist, shareFlag, callback, className_} = props;
  const  {mobileFlag}  = useSelector<{ config: any }, any>(
    ({config})=>config
 );
  const [sourceData, setSourceData] = useState<Idowndata[]>([]);
  const [downloadindeterminate, setdownloadIndeterminate] = useState<boolean>(
    false,
  );
  const [orignalindeterminate, setOignalindeterminate] = useState<boolean>(false);
  const [transferindeterminate, setTransferindeterminate] = useState<boolean>(false);
  const [downloadcheckAll, setdownloadCheckAll] = useState<boolean>(false);
  const [downloadcheckedList, setdownloadCheckedList] = useState< Array<CheckboxValueType | Idowndata | any>>([]); //选中的列表
  const [orignalAllChecked,setOrignalAllChecked]= useState<boolean>(true);
  const [transferCheckFlag,setTransferCheckFlag]= useState<boolean>(true);
  const [transferAllChecked,setTransferAllChecked]= useState<boolean>(true);
  // const [authorityType_orignal,setAuthorityTypeOrignal]= useState<boolean>(true);
  // const [authorityType_transfer,setAuthorityTypeTansfer]= useState<boolean>(true);
  // 权限
  const { rmanGlobalParameter } = useSelector<
    { permission: any },
    IPermission
  >(({ permission }) => permission);
  const authorityType_orignal = rmanGlobalParameter.includes(globalParams.file_group_display)
  const authorityType_transfer = rmanGlobalParameter.includes(globalParams.file_group_proxy_display)
  const [downloadProgress, setDownloadProgress] = useState<number>(0);
  // true 有权限 ；false 无权限
  const downloadref = useRef<HTMLAnchorElement | null>(null);
  useEffect(()=>{ 
    // console.log('更改了',sourceData);
    console.log('原格式权限0',rmanGlobalParameter.includes(globalParams.file_group_display),rmanGlobalParameter.includes(globalParams.file_group_proxy_display));
    console.log('原格式权限1',authorityType_orignal,authorityType_transfer);
    if(!authorityType_orignal&&!authorityType_transfer) {
      setdownloadCheckAll(false)
      return
    };
    // console.log('更改了',sourceData);
    let orignalFlag = 0 
    let transferFlag = 0 
    let transferNum = 0 
    sourceData.map((item:any,index:number)=>{
      if(item.orignal.is_selected){
        orignalFlag++
      }
      if(authorityType_orignal){
        if(item.transfer?.ifFlag){  //有原格式的情形下转码格式的有效个数
          transferNum++
        }
      }else{
        if(item.transfer){  //无原格式的情形下转码格式的有效个数（就是所有有转码格式的都算）
          transferNum++
        }
      }
      if(item.transfer?.is_selected){
        transferFlag++
      }
    })
     // 先判断原格式全选状态
     if(authorityType_orignal){
        if(orignalFlag === sourceData.length){ //全选
          setOrignalAllChecked(true)
          setOignalindeterminate(false)
          if(!transferCheckFlag||
            (authorityType_transfer && transferFlag===transferNum)){  //不考虑转码格式勾选情况 以及有转码格式时 他也全勾选了
            setdownloadCheckAll(true)
            setdownloadIndeterminate(false)
          }else{
              setdownloadCheckAll(false)
              setdownloadIndeterminate(true)
          }
        }else if(orignalFlag === 0){  //一个都没有
          setOrignalAllChecked(false)
          setOignalindeterminate(false)
          if(transferCheckFlag){
            setdownloadCheckAll(false)
            setdownloadIndeterminate(false)
          }else{
            if(transferFlag===0){  //既没有原格式勾选也没有转码格式勾选 则重置全选
              setdownloadCheckAll(false)
              setdownloadIndeterminate(false)
            }else{
              setdownloadCheckAll(false)
              setdownloadIndeterminate(true)
            }
          }
        }else{  // 部分选中
          setOrignalAllChecked(false)
          setOignalindeterminate(true)
          setdownloadCheckAll(false)
          setdownloadIndeterminate(true)
        }
     }
    
     // 再判断转码格式全选状态
    if(authorityType_transfer){
      if(transferCheckFlag){ //转码格式一个都不可选
        setTransferAllChecked(false)
        setTransferindeterminate(false)
        if(!authorityType_orignal){ //如果也没有原格式
          setdownloadCheckAll(false)
          setdownloadIndeterminate(false)
        }
      }else{
        if(transferFlag === transferNum){ //全选  要除开原本没有复选框的
          setTransferAllChecked(true)
          setTransferindeterminate(false)
          if(orignalFlag === sourceData.length){  //如果原格式的也全部勾选
            setdownloadCheckAll(true)
          }else{
            if(authorityType_orignal){  //如果有原格式这一列 那么就半勾选
              setdownloadCheckAll(false)
              setdownloadIndeterminate(true)
            }else{
              setdownloadCheckAll(true)
              setdownloadIndeterminate(false)
            }
          }
        }else if(transferFlag === 0){  //一个都没有
          setTransferAllChecked(false)
          setTransferindeterminate(false)
          if(authorityType_orignal && orignalFlag !==0){
            setdownloadCheckAll(false)
            setdownloadIndeterminate(true)
          }else{
            setdownloadCheckAll(false)
            setdownloadIndeterminate(false)
          }
        }else{  // 部分选中
          setTransferAllChecked(false)
          setTransferindeterminate(true)
          setdownloadIndeterminate(true)
        }
      }
    }
  },[sourceData,modalVisible])
  useEffect(() => {
    if (modalVisible) {
      let list: Array<string> = [];
      downloadlist.length &&
        downloadlist.forEach((item: any) => {
          list.push(item.contentId_);
        });
      contentListApis.downloadentity([...list]).then(res => {
        if (res && res.success && res.data) {
          // 根据权限判断是否展示原格式和低质量
          // console.log(rmanGlobalParameter,'权限！！！！！！')
          let filearr:any = [];
          res.data.forEach((item:any)=>{
            filearr.push({
              contentId:item.contentId,
              downloadAddress:item.downloadAddress,
              entityName:item.entityName,
              fileGroups:item.fileGroups
            })
          })
          let _flag = true
          const tempObj = filearr.map((item:any,index:number)=>{
              if(
                  (!authorityType_orignal &&item.fileGroups.length>1)||  //如果没有原格式 我们规定就必有转码格式并且 允许勾选
                  (authorityType_orignal &&
                    authorityType_transfer&& item.fileGroups.length>1 
                    && item.fileGroups[1].files[0].fileSize!==item.fileGroups[0].files[0].fileSize)){
                  _flag = false //这是记录
              }
              return item.fileGroups.length>1?{  //说明原格式和转码格式都有
                contentId:item.contentId,
                orignal:{
                    ...(item.fileGroups[0].groupName.split('_').length === 1?item.fileGroups[0].files[0]:item.fileGroups[1].files[0]),
                    entityName:item.entityName,
                    is_selected:authorityType_orignal,//当有权限的时候 初始化默认选中
                    showName:item.fileGroups[0].groupName.split('_').length === 1?item.fileGroups[0].showName:item.fileGroups[1].showName
                  },
                transfer:{
                  ...(item.fileGroups[0].groupName.split('_').length === 1?item.fileGroups[1].files[0]:item.fileGroups[0].files[0]),
                  entityName:item.entityName,
                  ifFlag:item.fileGroups[1].files[0].fileSize!==item.fileGroups[0].files[0].fileSize,// 如果转码后文件大小一致则不允许勾选
                  is_selected:false,//初始化默认不选中
                  showName:item.fileGroups[0].groupName.split('_').length === 1?item.fileGroups[1].showName:item.fileGroups[0].showName
                },
              }:
              {
                contentId:item.contentId,
                orignal:{
                  ...(item.fileGroups[0]?.files[0]),
                  entityName:item.entityName,
                  is_selected:true,//初始化默认选中
                  showName:item.fileGroups[0]?.showName
                }
              }
           })
           setTransferCheckFlag(_flag)
          setSourceData(tempObj);//存储数据原对象
        }
      });
    }
  }, [modalVisible]);
  // 全选
  const ondownloadCheckAllChange = (e: CheckboxChangeEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if(e.target.checked){
      sourceData.forEach((item:any)=>{
        if(authorityType_orignal){ //有原格式下载权限才勾选
          item.orignal.is_selected=true;
          if(authorityType_transfer
              && item.transfer?.ifFlag){//有转码格式下载权限 并且与原格式大小不一致才准勾选
            item.transfer.is_selected=true;
          }
        }
        if(authorityType_transfer
            &&item.transfer){//当无原格式时 转码格式就不受原格式大小影响
          item.transfer.is_selected=true;
        }
      })
      setOrignalAllChecked(true)
      setTransferAllChecked(true)
    }else{
      sourceData.forEach((item:any)=>{
        if(authorityType_orignal){ //有原格式下载权限才操作
          item.orignal.is_selected=false;
          if(authorityType_transfer
              && item.transfer?.ifFlag){//有转码格式下载权限 并且与原格式大小不一致才准操作
            item.transfer.is_selected=false;
          }
        }
        if(authorityType_transfer
            &&item.transfer){//当无原格式时 转码格式就不受原格式大小影响
          item.transfer.is_selected=false;
        }
      })
      setOrignalAllChecked(false)
      setTransferAllChecked(false)
    }
    setdownloadIndeterminate(false);
    setdownloadCheckAll(e.target.checked);
  };
  // 原格式单选
  const onChangeOrignal = (e:any) => {
    const temp = e.target.value;
    const sourceDataTemp = JSON.parse(JSON.stringify(sourceData))
    if(e.target.checked){
      sourceDataTemp.forEach((item:any)=>{
        if(item.orignal?.showName === temp.showName && item.orignal?.fileGuid === temp.fileGuid) {
          // console.log(item,1111)
          item.orignal.is_selected =true
        }
      })
      setSourceData(sourceDataTemp)
    }else{
      sourceDataTemp.forEach((item:any)=>{
        if(item.orignal?.showName === temp.showName 
          && item.orignal?.fileGuid === temp.fileGuid)
        {
          item.orignal.is_selected =false
        }
      })
      setSourceData(sourceDataTemp)
    }
  };
  // 转码格式单选
  const onChangeTansfer = (e:any) => {
    const temp = e.target.value;
    const sourceDataTemp = JSON.parse(JSON.stringify(sourceData))
    if(e.target.checked){
      sourceDataTemp.forEach((item:any)=>{
        if(item.transfer?.showName === temp.showName && item.transfer?.fileGuid === temp.fileGuid)
        {
          item.transfer.is_selected =true
        }
      })
      setSourceData(sourceDataTemp)
    }else{
      sourceDataTemp.forEach((item:any)=>{
        if(item.transfer?.showName === temp.showName 
          && item.transfer?.fileGuid === temp.fileGuid)
        {
          item.transfer.is_selected =false
        }
      })
      setSourceData(sourceDataTemp)
    }
  };
  // 原格式、转码格式全选切换
  const selectSpecial = (e:any) => {
    const sourceDataTemp = JSON.parse(JSON.stringify(sourceData))
    if(e.target.value === 0){ //原格式全选
      if(e.target.checked){
        setOrignalAllChecked(true)
        sourceDataTemp.forEach((item:any)=>{
          item.orignal.is_selected = true;
        })
        setSourceData(sourceDataTemp)
      }else{
        setOrignalAllChecked(false)
        sourceDataTemp.forEach((item:any)=>{
          item.orignal.is_selected = false;
        })
        if(transferCheckFlag){ //不考虑转码格式勾选情况   因为useffect没有触发 不知道为啥 所以要单独处理
          setdownloadCheckAll(false)
        }else {
          setdownloadCheckAll(false)
          setdownloadIndeterminate(true)
        }
        setSourceData(sourceDataTemp)
      }
    }else{   //转码格式全选
      if(e.target.checked){
        setTransferAllChecked(true)
        // setTransferindeterminate(false)
        sourceDataTemp.forEach((item:any)=>{
          if(!authorityType_orignal){//如果无原格式权限
            if(item.transfer){     //有转码格式时
              item.transfer.is_selected = true;
            }
          }else{
            if(item.transfer?.ifFlag){     //有原格式权限时 且与原格式大小不一致时
              item.transfer.is_selected = true;
            }
          }
        })
        setSourceData(sourceDataTemp)
      }else{
        setTransferAllChecked(false)
        setTransferindeterminate(false)
        sourceDataTemp.forEach((item:any)=>{//取消勾选则不需要判断详细逻辑了
          if(item.transfer){
            item.transfer.is_selected = false;
          }
        })
        setSourceData(sourceDataTemp)
      }
    }
  };
  const columns = [
    {
      title: intl.formatMessage({ id: '素材名' }),
      dataIndex: 'entityName',
      key:'entityName',
      width:'40%',
      render: (value: string, row: Idowndata, index: number) => {
        return <div title={row.orignal.entityName}>{row.orignal.entityName}</div>;
      },
    },
    {
      title: 
      <div>
        <Checkbox value={0} 
          indeterminate={orignalindeterminate}
          checked={orignalAllChecked}
          onChange={(e)=>selectSpecial(e)}
          ></Checkbox>
        <span className='checkbox_span'>{intl.formatMessage({ id: '原格式' })}</span>
      </div>,
      dataIndex: 'orignalFile',
      width:'30%',
      key:'orignalFile',
      render: (value: string, row: Idowndata, index: number) => {
        return (<>
          <div className="downloadbox">
            <Checkbox value={row.orignal} checked={row.orignal.is_selected} onChange={(e:any)=>onChangeOrignal(e)}></Checkbox>
            <div className="downloadname">
              {'.'+row.orignal.downloadAddress.split('.')[row.orignal.downloadAddress.split('.').length-1].split('?')[0].toUpperCase()+' ('+row.orignal.fileSize+')'}
            </div>
          </div>
        </>);
      },
    },
    {
      title:
      <div>
        <Checkbox value={1} disabled={transferCheckFlag}
          indeterminate={transferindeterminate}
          onChange={(e)=>selectSpecial(e)}
          checked={!transferCheckFlag && transferAllChecked}
        ></Checkbox>
        <span className='checkbox_span'>{intl.formatMessage({ id: '转码格式' })}</span>
        <Tooltip
          title={<span style={{color:'white'}}>
              {intl.formatMessage({ id: '经转码后，视频形成.MP4文件,音频形成.MP3文件，文档形成.PDF文件，图片形成如.JPG、.PNG等位图格式文件' })}
            </span>}
        >
          <IconFont type='iconhollow-question' 
            style={{fontSize:'20px',color:'#a8abad'}}
            className='checkbox_span'
            title=''
          />
        </Tooltip>
        
      </div>,
      dataIndex: 'transFile',
      width:'30%',
      key:'transFile',
      render: (value: string, row: Idowndata, index: number) => {
        return (<>
          <div className="downloadbox">
            {(!row.transfer || !row.transfer.ifFlag &&  authorityType_orignal)?<span style={{color:'#A4A4A4'}}>{intl.formatMessage({ id: '未转码' })}</span>
              :<div className="downloadbox">
                <Checkbox value={row.transfer} checked={row.transfer.is_selected} onChange={(e:any)=>onChangeTansfer(e)}></Checkbox>
                <div className="downloadname">
                  {'.'+row.transfer.downloadAddress.split('.')[row.transfer.downloadAddress.split('.').length-1].split('?')[0].toUpperCase()+' ('+row.transfer.fileSize+')'}
                </div>
              </div>
            }
          </div>
        </>);
      },
    }
  ];

const download = async (url: string, fileName: string) => {
  setDownloadProgress(0); // 重置进度
  try {
    const response = await fetch(url);
    const contentLength = response.headers.get('content-length');
    if (!contentLength) {
      throw new Error('无法获取文件大小');
    }
    const total = parseInt(contentLength, 10);
    let loaded = 0;

    const newResponse = new Response(
      new ReadableStream({
        start(controller) {
          const reader = response.body!.getReader();

          function read() {
            reader.read().then(({ done, value }) => {
              if (done) {
                controller.close();
                return;
              }
              loaded += value!.byteLength;
              const progress = Math.round((loaded / total) * 100);
              setDownloadProgress(progress);
              controller.enqueue(value);
              read();
            }).catch(error => {
              controller.error(error);
            });
          }

          read();
        },
      })
    );

    const blob = await newResponse.blob();
    const objectUrl = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = objectUrl;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(objectUrl);
    document.body.removeChild(a);
  } catch (error) {
    console.error('下载出错:', error);
  }
};

const batchdownload = async () => {
  const downloadItems = sourceData;
  const len = downloadItems.length;

  const downloadNext = async (index: number) => {
    if (index >= len) {
      // 所有下载任务完成
      if (!shareFlag) {
        entityApis.resourceOptionsCount(downloadlist.map((item: any) => item.contentId_), 'download').then((res: any) => {});
      }
      closemodal();
      return;
    }
    const item = downloadItems[index];
    if (authorityType_orignal) {
      if (item.orignal.is_selected) {
        const suffix = item.orignal.downloadAddress.split('.');
        await download(item.orignal.downloadAddress, `${item.orignal.entityName}_${item.orignal.showName}.${suffix[suffix.length - 1]}`);
      }
    }

    if (authorityType_transfer) {
      const suffix = item.transfer?.downloadAddress.split('.');
      if (authorityType_orignal) {
        if (item.transfer?.ifFlag && item.transfer.is_selected) {
          await download(item.transfer.downloadAddress, `${item.transfer.entityName}_${item.transfer.showName}.${suffix[suffix.length - 1]}`);
        }
      } else {
        if (item.transfer?.is_selected) {
          await download(item.transfer.downloadAddress, `${item.transfer.entityName}_${item.transfer.showName}.${suffix[suffix.length - 1]}`);
        }
      }
    }

    // 延迟 100ms 后下载下一个
    setTimeout(() => {
      downloadNext(index + 1);
    }, 100);
  };

  // 开始第一个下载任务
  downloadNext(0);
};
const closemodal = () => {
  modalClose();
  setdownloadCheckedList([]);
  setdownloadCheckAll(false);
};
return (
  <Modal
    destroyOnClose={true}
    title={intl.formatMessage({ id: '下载' })}
    className={`batchDownload ${className_||''}`}
    open={modalVisible}
    onCancel={modalClose}
    width={1000}
    footer={[
      downloadProgress > 0 ? <Progress percent={downloadProgress}/> :
      <Button
        key="downloadyes"
        type="primary"
        onClick={debounce(batchdownload,300)}
        disabled={!downloadindeterminate&&!downloadcheckAll}
      >
        { intl.formatMessage({ id: '开始下载' })}
      </Button>
    ]}
  >
    {/* <CheckboxGroup
      value={downloadcheckedList}
      // onChange={onChange}
      className="downloadCheckbox"
    > */}
      <Table
        columns={
          (authorityType_orignal&&authorityType_transfer)?
          columns:
          (
             (authorityType_orignal
             &&!authorityType_transfer)? //只有原格式，没有转码格式的权限 则去掉最后一列
             [columns[0],columns[1]]:
             ( 
               (!authorityType_orignal
                  &&authorityType_transfer)? //只有转码格式，没有原格式的权限 则去掉第二列
                  [columns[0],columns[2]]:
                  [columns[0]]  //如果啥都没有 就只保留第一列名称
             )
          )
        }
        size={mobileFlag?'small':'middle'}
        dataSource={sourceData}
        bordered
        rowKey={'entityName'}
        scroll={{ y: 300 }}
        pagination={false}
        className="download_modal"
      />
      <a className="download-dis" href="" download="" ref={downloadref}></a>
      <div className='selectCounts'>
        {/* {intl.formatMessage({ id: '已选择文件' }, {total:sourceData.filter((item:any)=>(item.orignal.is_selected || item.transfer.is_selected)).length)}} */}
        {intl.formatMessage({ id: '已选择文件' },{total:sourceData.filter((item:any)=>(item.orignal.is_selected || item.transfer.is_selected)).length})}
      </div>
    {/* </CheckboxGroup> */}
    {/* <Checkbox
      indeterminate={downloadindeterminate}
      onChange={ondownloadCheckAllChange}
      checked={downloadcheckAll}
      disabled={(!authorityType_transfer&&!authorityType_orignal)||
        (transferCheckFlag&&!authorityType_orignal)
      }
    >
      全选
    </Checkbox> */}
  </Modal>
);
};

export default detailDownloadModal;
