.mobileGroupResource{
  background-color: #F7F8FA;
  height: calc(100vh - 52px);
  .top{
    display: flex;
    justify-content: space-between;
    padding:10px 20px;
    align-items: center;
    >.ant-btn{
      border-radius: 32px;
      &.ant-btn-default{
        background-color: #FFFFFF;
      }
    }
  }
  .center{
    display: flex;
    flex-direction: column;
    height: calc(100% - 52px);
    overflow-y: auto;
    .item_{
      display: flex;
      flex-direction: column;
      margin: 0 10px;
      background-color: #FFFFFF;
      padding: 10px;
      margin-top: 10px;
      >div{
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
        >span{
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        >label{
          white-space: nowrap;
          margin-right: 20px;
        }
      }
      &:last-child{
        margin-bottom: 10px;
      }
    }
  }
  .addTeacher{
    height: 100%;
    >.top{
      display: flex;
      align-items: center;
      .ant-input-group > .ant-input:first-child{
        border-top-left-radius: 32px;
        border-bottom-left-radius: 32px;
      }
      .ant-input-group>.ant-input-group-addon:first-child{
        // border-top-right-radius: 32px;
        // border-bottom-right-radius: 32px;
      }
      
    }
    >.cascader{
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 0 20px;
      .ant-select{
        flex:1 1;
        margin-left: 10px;
      }
      .ant-select-dropdown{
        max-width: 100vw;
        overflow: auto;
        .ant-cascader-menu-item-content{
          max-width: 33vw;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
    >.center{
      display: flex;
      flex-direction: column;
      height: calc(100% - 120px);
      margin-top: 10px;
      overflow-y: auto;
    }
    .bottom{
      display: flex;
      justify-content: center;
    }
  }
}