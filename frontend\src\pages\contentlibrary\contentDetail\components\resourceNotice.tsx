import React, { FC, ReactNode } from "react";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import { Modal, Button, Spin } from "antd";

const ResourceNoticeModal = (
    { visible, loading, embeddingProcess, handleResourceCancel, handleResourceOK, handleToChat }:
        {
            visible: boolean, loading: boolean, embeddingProcess: any, handleResourceCancel: () => void,
            handleToChat: () => void,
            handleResourceOK: () => void,
        }) => {

    const buttonNode: any = () => {
        const firstButtonNode: any = [<Button onClick={handleResourceCancel}>取消</Button>, <Button type="primary" onClick={handleResourceOK}>确认</Button>]
        const otherButtonNode: any = [<Button type="primary" onClick={handleResourceOK}>重新发起</Button>, <Button onClick={handleToChat}>进入问答</Button>]
        const cancelButton: any = [<Button onClick={handleResourceCancel}>取消</Button>]
        if (loading) {
            return cancelButton
        } else {
            if (embeddingProcess?.ready === null) {
                return firstButtonNode
            }
            return otherButtonNode
        }

    }

    return (
        <Modal
            width={600}
            title="资源问答"
            open={visible}
            onOk={handleResourceOK}
            onCancel={handleResourceCancel}
            footer={buttonNode()}
        >
            <Spin spinning={loading}>
                <div style={{ marginLeft: 8 }}>
                    <div style={{ fontSize: 15, fontWeight: 600, marginBottom: 20 }}>{embeddingProcess?.ready === null ? '确认针对该资源进行智能问答' : '该资源已发起资源问答,请勿短时间内重复发起！'}</div>
                    <p style={{ display: 'flex', alignItems: 'center', gap: 5, marginLeft: 10 }}>
                        <ExclamationCircleOutlined style={{ color: '#faad14' }} />
                        {embeddingProcess?.ready === null ? '发起后，对资源相关内容发起向量分析，需等待一段时间，请勿短时间内重复发起！' : '发起后，对资源相关内容发起向量分析，需等待一段时间！'}
                    </p>
                </div>
            </Spin>
        </Modal>
    )
}

export default ResourceNoticeModal;