.cover-modal {
  .ant-modal-body .ant-spin-container {
    display: flex;
    font-family: PingFangSC-Medium, PingFang SC;
  }

  .left-wrapper {
    width: 505px;
    padding-right: 25px;
    border-right: 1px solid #D8D8D8;

    .left-header {
      color: #4A4F64;
      display: flex;
      justify-content: space-between;
      margin: 10px 0 20px;

      .title {
        font-size: 16px;
        font-weight: 500;
        line-height: 32px;
      }

      .btn-wrapper{
        display: flex;
      }

      .ant-btn-primary {
        margin-right: 20px;
      }
    }

    .left-content {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .image-wrapper {

        width: 150px;
        height: 86px;
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 22px;
        border: 1px solid transparent;
        cursor: pointer;

        img {
          width: 100%;
          height: 100%;
        }

        &.active {
          border: 1px solid #DF4A43;
        }
      }
    }
  }

  .right-wrapper {
    padding-left: 25px;
    width: 685px;

    .right-header {
      color: #4A4F64;
      font-weight: 500;
      font-size: 16px;
      margin: 10px 0;
    }

    .right-opt-wrapper {
      display: flex;
      justify-content: end;

      .ant-select {
        width: 120px;
        height: 28px;
        margin-right: 20px;

        .ant-select-selector,
        input {
          height: 28px;
        }
      }

      .color-wrapper {
        display: flex;
      }

      .text {
        color: #9D9D9D;
        margin-right: 10px;
        line-height: 28px;
      }

      .color {
        width: 28px;
        height: 28px;
        // background: #FFA83C;
      }
    }

    .cover-wrapper-box {
      width: 660px;
      height: 380px;
      background: #E8E8E8;
      border-radius: 2px;
      margin: 20px 0 15px;
      position: relative;
      padding: 10px;

      .cover-content {

        // justify-content: right;
        height: 360px;
        text-align: right;
        background-position: center;
        background-size: cover !important;
        background-repeat: no-repeat;
        padding-top: 100px;
      }

      img {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 347px;
        height: 192px;
      }

      .title-input {
        // width: auto;
        text-align: center;
      }

      .title-content,
      .title-input {
        border-radius: 0;
        width: 100%;
        // margin: 0 10px 12px auto;
        font-size: 50px;
        font-weight: 600;
        line-height: 70px;
        margin-bottom: 30px;
        background: rgba(0, 0, 0, 0.2);
        border: none;
        // max-height: 150px;
        overflow: hidden;
      }

      .title-content {
        padding: 5px 11px;
        // height: 70px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        &.no-title {
          background: transparent;
        }
      }

      .teacher-input {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.6);
      }

      .teacher-content, .teacher-input {
        width: 620px;
        font-weight: 400;
        text-align: center;
        font-size: 24px;
        line-height: 24px;
        margin: 0 10px;
      }
    }

    .right-btn-wrapper {
      display: flex;

      .tips {
        color: #CBCBCB;
        line-height: 32px;
        margin-left: 10px;

        &::before {
          content: "*";
          color: #DF4A43;
        }
      }
    }
  }
}
