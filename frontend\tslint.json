{"extends": ["tslint:recommended", "tslint-react", "tslint-config-prettier"], "linterOptions": {"exclude": ["node_modules/**/*.ts", "mock/**", "config/**", "src/.umi/**"]}, "rules": {"no-console": false, "curly": true, "array-type": false, "interface-name": [true, "always-prefix"], "no-debugger": false, "no-empty": false, "no-unused-vars": [2, {"vars": "all", "args": "after-used"}], "no-namespace": false, "no-trailing-whitespace": true, "object-literal-sort-keys": false, "indent": [true, "spaces", 4], "jsx-self-close": false, "prefer-for-of": true, "prefer-const": false, "no-bitwise": false, "jsx-no-lambda": false, "jsx-boolean-value": false, "allowSyntheticDefaultImports": true}, "jsRules": {"object-literal-sort-keys": false}}