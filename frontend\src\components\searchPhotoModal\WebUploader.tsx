import config from '@/config';

interface WebUploaderProps {
  uploadProgress: (file: any, progress: number) => void;
  uploadComplete: (file: any, uploader: any) => void;
  uploadError: (file: any) => void;
}

export default function WebUploader({
  uploadProgress,
  uploadError,
  uploadComplete,
}: WebUploaderProps): any {
  let baseUrl = '';
  if (process.env.NODE_ENV === 'development') {
    baseUrl = '/api';
  } else {
    baseUrl = '/rman/v1';
  }
  const webUploader = new (window as any).WebUploader.Uploader({
    chunked: true,
    server: baseUrl + '/upload/filesave',
    runtimeOrder: 'html5,flash',
    threads: config.uploadConfig.threads, // 并发数
    compress: false,
  });
  webUploader.on('uploadBeforeSend', (object: any, data: any) => {
    data.guid = object.file.source.guid;
    data.fileGuid = object.file.source.fileGuid;
  });
  webUploader.on('uploadProgress', uploadProgress);
  webUploader.on('uploadSuccess', (file: any) => {
    uploadComplete(file, webUploader);
  });
  webUploader.on('uploadError', uploadError);

  return webUploader;
}
