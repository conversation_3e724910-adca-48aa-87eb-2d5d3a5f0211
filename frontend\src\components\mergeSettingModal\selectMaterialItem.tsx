import React, { FC, useEffect, useState } from 'react';
import { Button, Space, Tooltip } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import MaterialModal from '@/components/materialModal/materialModal';
import { MaterialList } from '@/pages/contentlibrary/contentList/type';
import './style.less';
import {  useIntl } from 'umi';

export interface IFormItem {
  value?: any;
  onChange?: (value: any) => void;
}

const SelectMaterialItem: FC<IFormItem> = ({ value, onChange }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const intl = useIntl();
  const [selected, setSelected] = useState<MaterialList | null>(null);
  useEffect(() => {
    if (!value) {
      setSelected(null);
    }
  }, [value]);

  return (
    <>
      {selected ? (
        <Space className="material-preview-wrapper" align="end">
          <div className="material-preview">
            <Tooltip title={selected.name_}>
              <img src={selected.keyframe_} />
            </Tooltip>
          </div>
          <span
            onClick={() => {
              setSelected(null);
              onChange && onChange('');
            }}
          >
            {intl.formatMessage({ id: '删除' })}
          </span>
        </Space>
      ) : (
        <Button size="small" onClick={() => setModalVisible(true)}>
          <PlusOutlined />
          {intl.formatMessage({ id: '添加' })}
        </Button>
      )}
      <MaterialModal
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={item => {
          setModalVisible(false);
          setSelected(item);
          onChange && onChange(item.contentId_);
        }}
      />
    </>
  );
};
export default SelectMaterialItem;
