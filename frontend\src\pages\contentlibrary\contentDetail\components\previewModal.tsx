import React, { useState, useEffect, useRef } from 'react';
import { Modal, Input, Table, Button, Select, Form } from 'antd';
import Entity from '@/components/entity/entity';
import {
    Player,
} from '@/components';
import { fetchTopicList } from '@/service/homework';
import contentListApis from '@/service/contentListApis';
import { optionType_ } from '@/utils';
import $ from "jquery";
import { useIntl } from 'umi';
const { Option } = Select;

interface previewEntityProps {
    selectKeys?: any,
    type?: string,// 单选还是多选
    visible: boolean,
    entity: any,
    frameRate: any,
    questionPoint: any,
    isTransCode: any,
    errorCode: number,
    contentId: string,
    versionCode: string,
    current: number,
    pageSize: number,
    questionList: any,
    setviewTopicModalVisible: (value: boolean) => void,
    setQuestionItem: (e: any) => void,
    setQuestionPointList: (e: any) => void,
    onClose: () => void,
    setIdx: (value: any) => void
    disabled?: [number],
    currentname?: string,
    iscoursemap?: boolean
}

const previewModal: React.FC<previewEntityProps> = ({ visible, entity, questionList, errorCode, versionCode, current, pageSize, setQuestionPointList, setQuestionItem, setIdx, onClose, setviewTopicModalVisible, frameRate, isTransCode, contentId, questionPoint }) => {
    const [initFlag, setInitFlag] = useState<boolean>(false);
    const player = useRef<Player>();
    const intl = useIntl();
    const currentPoint = useRef<number>(-1)
    const [currentFrame, setCurrentFrame] = useState<number>(0);
    const handleSuccess = (play: Player) => {
        player.current = play;
        if (!(play as any).isVideo) {  //如果不是视频就中断
            return
        }
        setInitFlag(true)//判断插件已加载完毕
    };
    useEffect(() => {
        if (initFlag && visible) {
            setTimeout(() => {
                const temp: any = document.querySelectorAll('.topic_modal .mejs__keypoint');
                while (temp.length > 0) {
                    temp[0].remove();
                }
                const keyPoints = questionPoint.map((item: any, index: number) => {
                    //由于父级节点定宽 中文字符便会垂直显示 只能动态设置了
                    // console.info(width)
                    return {
                        'frameNo': item,
                        'content': `<div style="text-align:center;</div>`
                    }
                })
                player.current?.addKeyPoint(keyPoints);
            }, 300);
        }
    }, [initFlag, visible])
    const handlePlayChange = (point: number) => {
        setCurrentFrame(point);
        let cur = parseInt((point / 10000000).toString()) * 10000000
        if (questionPoint.includes(cur)) {
            if (currentPoint.current != cur) {
                currentPoint.current = cur
                player.current?.pause();
                contentListApis.getListByVersion({
                    resourceId: contentId,
                    versionName: versionCode,
                    pointIn: currentPoint.current,
                    pageIndex: current,
                    pageSize,
                }).then(res => {
                    contentListApis.getQuestionsByIds(res?.data.data.map((item: any) => item.questionId)).then(res2 => {
                        const list = res?.data.data.map((item: any) => ({
                            ...item,
                            ...(res2?.data?.find((_item: any) => _item.id === item.questionId) || {})
                          }))
                          list.sort((a: { pointIn: number; }, b: { pointIn: number; }) => {
                            return a.pointIn - b.pointIn
                        })
                        let idx = list.findIndex((item: { pointIn: number; }) => item.pointIn === currentPoint.current)
                        setIdx(idx)
                        setQuestionPointList(list)
                        setQuestionItem(list && list[0] || {})
                        setviewTopicModalVisible(true)
                    })
                })
            }
        }
    };
    return (
        <Modal
            title={intl.formatMessage({ id: '预览' })}
            visible={visible}
            width={1000}
            style={{ height: '800px' }}
            footer={null}
            onCancel={onClose}
            className='topic_modal'
        >
            {entity && isTransCode ? (
                <div style={{ height: '600px' }} className={`entity_view_wrapper ${entity.path.includes('.html') ? 'whiteBg' : ''}`}>
                    <Entity
                        id='previewPlayer'
                        type={entity.type}
                        src={entity.path}
                        frameRate={frameRate}
                        keyframes={entity.keyframes}
                        errorCode={errorCode}
                        contendId={contentId}
                        onSuccess={handleSuccess}
                        onPlayChange={handlePlayChange}
                    />
                </div>
            ) : (
                <div className='entity_view_wrapper '>
                    <div className="entity-error">
                        <img src={require('../../../../images/contentlibrary/zmz.png')} />
                    </div>
                </div>
            )}
        </Modal >
    );
};

export default previewModal;
