/*
 * @Author: 李武林
 * @Date: 2021-08-23 11:37:32
 * @LastEditors: 李武林
 * @LastEditTime: 2022-05-31 14:22:20
 * @FilePath: \frontend\config\config.routes.ts
 * @Description:
 *
 * Copyright (c) 2022 by <PERSON>武林/索贝数码, All Rights Reserved.
 */
const routes = [
  {
    path: '/',
    component: '@/layout/guideLayout/index',
    routes: [{
      path: '/',
      component: '@/pages/index',
      title: 'home.title',
      redirect: '/basic',
    },
    {
      path: '/basic',
      component: '@/layout/basicLayout',
      routes: [
        {
          exact: true,
          path: '/basic',
          redirect: '/basic/rmanList',
        },
        {
          exact: true,
          path: '/basic/contentlist',
          component: '@/pages/contentlibrary/contentList',
          title: 'search.title',
        },
        {
          exact: true,
          path: '/basic/similarityGraph',
          component: '@/pages/contentlibrary/similarityGraph',
          title: 'search.title',
        },
        {
          exact: true,
          path: '/basic/rmanCenterList',
          component: '@/pages/contentlibrary/contentList',
          title: 'search.title',
        },
        {
          exact: true,
          path: '/basic/rmanCenterList/?hidebanner=true',
          component: '@/pages/contentlibrary/contentList',
          title: 'search.title',
        },
        {
          exact: true,
          path: '/basic/rmanList',
          component: '@/pages/contentlibrary/contentList',
          title: 'search.title',
        },
        {
          exact: true,
          path: '/basic/rmanList/mobileResourceGroup',
          component: '@/pages/mobile/resourceGroup.tsx',
          title: 'search.title',
        },
        {
          exact: true,
          path: '/basic/out',
          component: '@/pages/contentlibrary/contentList',
          title: 'search.title',
        },
        {
          exact: true,
          path: '/basic/resourceSelect',
          component: '@/components/ResourceSelect',
        },
        {
          exact: true,
          path: '/basic/rmanDetail/:contentId',
          component: '@/pages/contentlibrary/contentDetail/detail',
          title: '素材详情',
        },
        {
          exact: true,
          path: '/basic/rmanDetailV2/:contentId',
          component: '@/pages/contentlibrary/contentDetail/detailV2',
          title: '素材详情',
        },
        {
          exact: true,
          path: '/basic/rmanDetailV3/:contentId',
          component: '@/pages/contentlibrary/contentDetail/detailV3',
          title: '素材详情',
        },
        {
          exact: true,
          path: '/basic/contentDetail/:contentId',
          component: '@/pages/contentlibrary/contentDetail/detail',
          title: '素材详情',
        },
        {
          exact: true,
          path: '/basic/shareDetail/:link',
          component: '@/pages/shareDetail',
          title: '分享详情',
        },

      ],
    },
    {
      path: '/uploadMobile',
      component: '@/pages/uploadMobile',
    },
    {
      path: '/mapv3',
      title: '资源图谱',
      component: '@/pages/Mapv3',
    },
    {
      path: '/task',
      component: '@/layout/basicLayout',
      routes: [
        {
          exact: true,
          path: '/task/taskprogress',
          component: '@/pages/taskprogress',
          title: '任务进度',
        }
      ]
    },
    {
      path: '/login',
      component: '@/pages/login',
      title: 'login.title',
    },
    {
      path: '/upload',
      component: '@/pages/upload/upload',
      title: '上传测试',
    },
    {
      path: '/entitylist',
      component: '@/pages/management/metadata/basic',
      title: '元数据列表',
    },
    {
      path: '/authorization',
      component: '@/pages/management/metadata/authorization',
      title: 'API授权管理',
    },
    // 
    {
      path: '/callbacksettings',
      component: '@/pages/management/metadata/callbacksettings',
      title: '回调设置',
    },
    {
      path: '/entity/:moduleType/:code',
      component: '@/pages/management/metadata/detail',
      title: '元数据配置',
    },
    {
      path: '/basicentity/:code/:id',
      component: '@/pages/management/metadata/basic/detail',
      title: '基础元数据配置',
    },
    { component: '@/pages/404', title: '404' }]
  }
];

export default routes;
