import React, { FC, useEffect, useState } from 'react';
import { ReactSortable, ItemInterface } from 'react-sortablejs';
import {
  Row,
  Col,
  Form,
  Tooltip,
  Input,
  Select,
  InputNumber,
  Checkbox,
  Button,
} from 'antd';
import { formItems } from '@/components/sortForm/data';
import { IconFont } from '@/components/iconFont/iconFont';
import { getId } from '@/utils';
import FormItem from '@/components/sortForm/formItem';
import { DragOutlined, CopyOutlined, DeleteOutlined } from '@ant-design/icons';

import './index.less';

// 表单类型
declare const FormItemTypes: [
  'input',
  'input.textarea',
  'input.number',
  'radio',
  'checkbox',
  'time',
  'date',
  'date.time',
  'date.range',
  'select',
  'tag',
  'select.tree',
  'slider',
  'switch',
];
export declare type FormItemType = typeof FormItemTypes[number];

export interface IFormItem extends ItemInterface {
  type: FormItemType;
  icon?: string;
  id: string;
  name: string;
  config?: MetadataTypes.Field;
}

const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};

// 类型mapping
const typeMap: { [propName: string]: FormItemType } = {
  '1': 'date.time',
  '2': 'date',
  '3': 'time',
  '4': 'input.number',
  '5': 'input',
  '6': 'input.textarea',
  '7': 'radio',
  '8': 'select',
  '9': 'input',
  '10': 'input',
  '11': 'input',
  '12': 'tag',
  '13': 'input',
  '14': 'select.tree',
  '15': 'input',
  '16': 'date.range',
  '17': 'select.tree',
  '18': 'select.tree',
  '19': 'input.number',
  '20': 'input',
  '21': 'input.textarea',
};
const form2Type: {
  [propName: string]: string[];
} = {
  input: ['5', '9', '10', '11', '13', '15', '20'],
  'input.textarea': ['6', '21'],
  'input.number': ['4', '19'],
  radio: ['7'],
  checkbox: [],
  time: ['3'],
  date: ['2'],
  'date.time': ['1'],
  'date.range': ['16'],
  select: ['8'],
  tag: ['12'],
  'select.tree': ['14', '17', '18'],
  slider: [],
  switch: [],
};
const typeMap2: { [propName: string]: string } = {
  '1': '日期+时间',
  '2': '日期',
  '3': '时间',
  '4': '数字',
  '5': '单行文本',
  '6': '多行文本',
  '7': '单选按钮',
  '8': '下拉列表框',
  '9': '帧显示时码',
  '10': '存储容量',
  '11': '百纳秒显示时码',
  '12': '标签输入框',
  '13': '经纬度信息',
  '14': '树形数据',
  '15': '表格',
  '16': '开始-结束时间',
  '17': '用户选择框',
  '18': '部门选择框',
  '19': '小数',
  '20': 'Uri控件',
  '21': '富文本框',
};

const form2HiveType = {
  input: ['string'],
  'input.textarea': ['string'],
  'input.number': ['long', 'float', 'integer'],
  radio: ['string', 'long', 'float', 'integer'],
  checkbox: ['string', 'long', 'float', 'integer'],
  time: ['date'],
  date: ['date'],
  'date.time': ['date'],
  'date.range': ['date'],
  select: ['string', 'long', 'float', 'integer'],
  tag: ['string', 'long', 'float', 'integer'],
  'select.tree': ['string'],
  slider: [],
  switch: [],
};

const SortForm: FC<{
  dataSource: MetadataTypes.Field[];
  onSaveSetting: (values: any) => void;
  onBindField: (ableType: string[]) => void;
  onDelete: (id: number) => void;
}> = ({ dataSource, onSaveSetting, onBindField, onDelete }) => {
  const [formItemList, setFormItemList] = useState(formItems);
  const [editorItemList, setEditorItemList] = useState<IFormItem[]>([]);
  const [activeItem, setActiveItem] = useState<IFormItem>();
  const [showForm, setShowForm] = useState(false);
  const [ableType, setAbleType] = useState<string[]>([]);
  const [editItemSettingForm] = Form.useForm();

  useEffect(() => {
    if (dataSource.length) {
      const editorList: IFormItem[] = dataSource.map(item => ({
        type: typeMap[item.controlType],
        id: item.id ? String(item.id) : getId(),
        name: item.alias || '',
        config: item,
      }));
      setEditorItemList(editorList);
    }
  }, [dataSource]);
  /**
   * 表单选中
   * */
  const handleFormItemClick = (item: IFormItem) => {
    setActiveItem(item);
    if (item.config) {
      setShowForm(true);
      editItemSettingForm.setFieldsValue(item.config);
    } else {
      setAbleType(form2HiveType[item.type]);
      setShowForm(false);
    }
  };
  const handleCopyClick = (id: string) => {
    const index = editorItemList.findIndex(item => item.id === id);
    if (index > -1) {
      const newList = [...editorItemList];
      newList.splice(index, 0, {
        ...editorItemList[index],
        id: getId(),
      });
      setEditorItemList(newList);
    }
  };
  const handleDeleteClick = (id: string) => {
    const index = editorItemList.findIndex(item => item.id === id);
    if (index > -1) {
      const newList = [...editorItemList];
      newList.splice(index, 1);
      setEditorItemList(newList);
    }
  };
  const handleFieldNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (activeItem && e.target.value) {
      const newList = [...editorItemList];
      const index = newList.findIndex(item => item.id === activeItem.id);
      if (index > -1) {
        newList[index].name = e.target.value;
        setEditorItemList(newList);
      }

      // setActiveItem({
      //   ...activeItem,
      //   name: e.target.value,
      // });
    }
  };

  return (
    <div className="sort_form_wrapper">
      <Row>
        <Col span={4}>
          <div className="form_list_wrapper">
            <div className="form_cate">基础字段</div>
            <ReactSortable
              list={formItemList}
              setList={setFormItemList}
              animation={150}
              group={{ name: 'disable-group-name', pull: 'clone', put: false }}
              clone={item => ({ ...item, id: getId() })}
              sort={false}
              className="form_item_list"
            >
              {formItemList.map((item, index) => (
                <div key={index} className="form_item">
                  <IconFont type={item.icon} />
                  <span>{item.name}</span>
                </div>
              ))}
            </ReactSortable>
          </div>
        </Col>
        <Col span={16}>
          <div className="editor_wrapper">
            {editorItemList.length === 0 && (
              <span className="editor_tips">从左侧拖拽或点击来添加字段</span>
            )}
            <Form {...layout}>
              <ReactSortable
                list={editorItemList}
                setList={setEditorItemList}
                animation={150}
                group={{ name: 'disable-group-name', pull: 'clone' }}
                className="editor"
                handle=".form_item_drag"
              >
                {editorItemList.map(item => (
                  <div
                    className={
                      item.id === activeItem?.id
                        ? 'form_item_wrapper active'
                        : 'form_item_wrapper'
                    }
                    key={item.id}
                    onClick={e => {
                      e.stopPropagation();
                      handleFormItemClick(item);
                    }}
                  >
                    <FormItem {...item} />
                    {item.id === activeItem?.id && (
                      <>
                        <div className="form_item_drag">
                          <DragOutlined />
                        </div>
                        <div className="form_item_action">
                          {/*<Tooltip title="复制">*/}
                          {/*  <CopyOutlined*/}
                          {/*    onClick={() => handleCopyClick(item.id)}*/}
                          {/*  />*/}
                          {/*</Tooltip>*/}
                          <Tooltip title="删除">
                            <DeleteOutlined
                              onClick={() => onDelete(Number(item.id))}
                            />
                          </Tooltip>
                        </div>
                      </>
                    )}
                  </div>
                ))}
              </ReactSortable>
            </Form>
          </div>
        </Col>
        <Col span={4}>
          <div className="attr_wrapper">
            <div className="attr_header">
              属性配置{' '}
              <Button
                type="primary"
                size="small"
                onClick={() => editItemSettingForm.submit()}
              >
                保存配置
              </Button>
            </div>
            {activeItem ? (
              <div className="attr_content">
                {showForm ? (
                  <Form
                    name="editItemSetting"
                    layout="vertical"
                    initialValues={{}}
                    form={editItemSettingForm}
                    onFinish={onSaveSetting}
                  >
                    {/*隐藏字段*/}
                    <Form.Item name="id" noStyle={true} hidden={true} />
                    <Form.Item name="type" noStyle={true} hidden={true} />
                    <Form.Item
                      name="metadataType"
                      noStyle={true}
                      hidden={true}
                    />

                    <Form.Item
                      label="fieldName"
                      name="fieldName"
                      rules={[{ required: true, message: '请填写fieldName' }]}
                    >
                      <Input disabled={true} />
                    </Form.Item>
                    <Form.Item
                      label="hive字段名称"
                      name="alias"
                      rules={[
                        { required: true, message: '请填写hive字段名称' },
                      ]}
                    >
                      <Input disabled={true} />
                    </Form.Item>
                    <Form.Item
                      label="显示名称"
                      name="showName"
                      rules={[{ required: true, message: '请填写显示名称' }]}
                    >
                      <Input />
                    </Form.Item>
                    <Form.Item label="类型设置" name="controlType">
                      <Select style={{ width: 160 }}>
                        {Object.keys(typeMap2).map((key: string) => (
                          <Select.Option
                            value={Number(key)}
                            key={key}
                            disabled={
                              !form2Type[activeItem?.type].includes(key)
                            }
                          >
                            {typeMap2[key]}
                          </Select.Option>
                        ))}
                      </Select>
                    </Form.Item>
                    <Form.Item label="最小长度" name="minLength">
                      <InputNumber />
                    </Form.Item>
                    <Form.Item label="最大长度" name="maxLength">
                      <InputNumber />
                    </Form.Item>
                    <Form.Item
                      label="显示设置"
                      name="isEnable"
                      valuePropName="checked"
                    >
                      <Checkbox>显示</Checkbox>
                    </Form.Item>
                    <Form.Item
                      noStyle={true}
                      shouldUpdate={(pre, cur) =>
                        pre.isMustInput !== cur.isMustInput
                      }
                    >
                      {() => (
                        <Form.Item
                          label="编辑设置"
                          name="isReadOnly"
                          valuePropName="checked"
                        >
                          <Checkbox
                            disabled={(() =>
                              editItemSettingForm.getFieldValue(
                                'isMustInput',
                              ))()}
                          >
                            只读
                          </Checkbox>
                        </Form.Item>
                      )}
                    </Form.Item>
                    <Form.Item
                      noStyle={true}
                      shouldUpdate={(pre, cur) =>
                        pre.isReadOnly !== cur.isReadOnly
                      }
                    >
                      {() => (
                        <Form.Item
                          label="必填设置"
                          name="isMustInput"
                          valuePropName="checked"
                        >
                          <Checkbox
                            disabled={(() =>
                              editItemSettingForm.getFieldValue(
                                'isReadOnly',
                              ))()}
                          >
                            必填
                          </Checkbox>
                        </Form.Item>
                      )}
                    </Form.Item>
                    <Form.Item
                      noStyle={true}
                      shouldUpdate={(pre, cur) =>
                        pre.controlType !== cur.controlType
                      }
                    >
                      {() =>
                        ['8', '14', '17', '18'].includes(
                          editItemSettingForm.getFieldValue('controlType'),
                        ) && (
                          <Form.Item
                            label="是否多选"
                            name="isMultiSelect"
                            valuePropName="checked"
                            shouldUpdate
                          >
                            <Checkbox>多选</Checkbox>
                          </Form.Item>
                        )
                      }
                    </Form.Item>
                  </Form>
                ) : (
                  <Button
                    type="primary"
                    style={{ width: '100%' }}
                    onClick={() => onBindField(ableType)}
                  >
                    绑定基础元数据
                  </Button>
                )}
              </div>
            ) : (
              <div className="attr_tips">请选择字段</div>
            )}
          </div>
        </Col>
      </Row>
    </div>
  );
};
export default SortForm;
