/*
 * @Author: 李武林
 * @Date: 2022-05-31 14:23:07
 * @LastEditors: 李武林
 * @LastEditTime: 2022-05-31 14:26:25
 * @FilePath: \frontend\src\service\help.ts
 * @Description:
 *
 * Copyright (c) 2022 by 李武林/索贝数码, All Rights Reserved.
 */
import HTTP from '../http/http';
// 查询当前页面的指引
export function getguidedirectory(pageCode: string) {
    return HTTP(`/unifiedplatform/v1/newhandguide/guidedirectory?pageCode=${pageCode}&isAll=false`,{
        method: 'GET',
    });
}

// 查询系统seo配置
export function getSeoConfig() {
    return HTTP(`/unifiedplatform/v1/setting/seo`,{
      method: 'GET',
  });
}
