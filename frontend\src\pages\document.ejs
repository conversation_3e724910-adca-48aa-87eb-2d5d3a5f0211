<!doctype html>
<html lang="zh">
<head>
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-control" content="no-cache">
    <meta http-equiv="Cache" content="no-cache">
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
<!--    <link rel="stylesheet" href="/default.css">-->
    <title>智慧教学资源管理系统</title>
</head>
<body>
<link rel="stylesheet/less" type="text/css" href="<%= context.config.publicPath %>antdtheme.less" />
<script>
  window.less = {
    async: true,
    env: 'production'
  };
  function getQueryVariable(variable){
    var query = window.location.href.split('?');
    if(query.length>1){
        var vars = query[1].split("&");
        for (var i=0;i<vars.length;i++) {
            var pair = vars[i].split("=");
            if(pair[0] == variable){return pair[1];}
        }
    }
    return false;
  }
    var cookie = getQueryVariable('cookie');
    if(cookie){
        var exdate = new Date(); // 获取时间
        var exdays = 1;//保存1天
        var key = 'S1_rman_sid';
        exdate.setTime(exdate.getTime() + 24 * 60 * 60 * 1000 * exdays) // 保存的天数
        // 字符串拼接cookie
        window.document.cookie = key + '=' + cookie + ';path=/;expires=' + exdate.toGMTString()
        // window.document.cookie='id=fas'
    }
</script>
<script type="text/javascript" src="/learn/workbench/CommonMenu.min.js"></script>
<script type="text/javascript" src="/learn/static/js/bundle/FunASR.js"></script>
<!-- <script type="text/javascript" src="<%= context.config.publicPath %>libs/less.js"></script> -->
<script type="text/javascript" src="<%= context.config.publicPath %>libs/less.min.js"></script>
<script type="text/javascript" src="<%= context.config.publicPath %>libs/title.js"></script>
<script type="text/javascript" src="<%= context.config.publicPath %>oss/aliyun/aliyun-oss-sdk-6.1.1.min.js"></script>
<script type="text/javascript" src="<%= context.config.publicPath %>oss/ctyun/oos-sdk-6.0.min.js"></script>
<div id="root"></div>
</body>
</html>
