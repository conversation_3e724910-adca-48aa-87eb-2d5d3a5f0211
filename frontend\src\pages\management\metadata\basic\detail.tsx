import React, { FC, useEffect, useState } from 'react';
import MetadataService from '@/service/metadataService';
import {
  Row, 
  Col,
  Table,
  Button,
  Input,
  InputNumber,
  Space,
  Form,
  Checkbox,
  Modal,
  Popconfirm,
  message,
  AutoComplete,
  Select,
} from 'antd';
import './index.less';
import { useParams } from 'umi';
import { IResponse } from '@/types/requsetTypes';

const formLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 12 },
};

const _dataType = [
  {
    key: 'string',
    value: '字符串',
  },
  {
    key: 'long',
    value: '整形数字',
  },
  {
    key: 'float',
    value: '浮点数',
  },
  {
    key: 'boolean',
    value: '布尔型',
  },
  {
    key: 'date',
    value: '日期类型',
  },
];
const _analyzeList = [
  {
    key: '',
    value: '不分词'
  },
  {
    key: 'standard',
    value: '默认分词器',
  },
  {
    key: 'simple',
    value: '英文非字母分词器',
  },
  {
    key: 'whitespace',
    value: '空格分词器',
  },
  {
    key: 'stop',
    value: '英文停用词分词器',
  },
  {
    key: 'ik_smart',
    value: '中文分词器',
  },
  {
    key: 'ik_max_word',
    value: '中文精准分词器',
  },
  {
    key: 'default_folder_code_analyzer',
    value: '目录路径分词器',
  },
  {
    key: 'pinyin_analyzer_max_word',
    value: '中文拼音分词器',
  },
  {
    key: 'pinyin_analyzer_smart',
    value: '中文精准拼音分词器',
  },
];

const batchFetchTypeList = (pageTotal: number, page: number, size: number) => {
  let promise: Promise<any>[] = [];
  for (let i = page; i <= pageTotal; i++) {
    promise.push(
      MetadataService.fetchHiveMetadataTypeList({
        page: i,
        size,
      }),
    );
  }
  return Promise.all(promise);
};

const fetchAllTypeList = async (
  size: number,
): Promise<MetadataTypes.HiveMetadataType[]> => {
  const res = await MetadataService.fetchHiveMetadataTypeList({
    page: 1,
    size,
  });
  if (res?.success && res.data) {
    if (res.data.data.pageTotal > 1) {
      const batchResult = await batchFetchTypeList(
        res.data.data.pageTotal,
        2,
        size,
      );
      if (batchResult) {
        const batchData = batchResult.map(res => res.data.data.results);
        return [...res.data.data.results, ...batchData];
      }
    }
    return res.data.data.results;
  }
  return [];
};

const fetchAllFieldList = async () => {
  const _size = 50;
  const typeList = await fetchAllTypeList(_size);
  const promise: Promise<
    IResponse<MetadataTypes.HiveEntityData> | undefined
  >[] = [];
  typeList.forEach(({ code }) => {
    promise.push(
      MetadataService.fetchHiveMetadataFieldList({
        code,
      }),
    );
  });
  const result = await Promise.all(promise);
  const allFieldList = result
    .map(res => res?.data?.data.entity_data_defines)
    .flat(2);
  // 去重
  return allFieldList.filter(
    (entity, index) =>
      entity && allFieldList.findIndex(e => e?.code === entity.code) === index,
  );
};

/**
 * hive基础数据
 * @constructor
 */
const MetadataBasicDetail: FC = () => {
  const params = useParams<{ code: string; id: string }>();
  const [dataLoading, setDataLoading] = useState(false);
  const [fieldList, setFieldList] = useState<MetadataTypes.HiveEntity[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [isAdd, setIsAdd] = useState(false);
  const [allFields, setAllFields] = useState<MetadataTypes.HiveEntity[]>([]);
  const [codeOptions, setCodeOptions] = useState<{ value: string }[]>([]);
  const [editMetadataForm] = Form.useForm();

  useEffect(() => {
    fetchFieldList();
  }, []);

  useEffect(() => {
    if (modalVisible && isAdd && allFields.length === 0) {
      fetchAllFieldList().then(res => {
        setAllFields(res as MetadataTypes.HiveEntity[]);
      });
    }
  }, [modalVisible, isAdd]);

  const handleCodeChange = (code: string) => {
    const fitOptions = allFields
      .filter(field => field.code.indexOf(code) > -1)
      .map(field => ({
        value: field.code,
      }));
    setCodeOptions(fitOptions);
  };

  const handleCodeSelect = (code: string) => {
    const fitField = allFields.find(field => field.code === code);
    if (fitField) {
      editMetadataForm.setFieldsValue({
        ...fitField,
        index: fitField.mapping?.index,
        keyword: fitField.mapping?.keyword,
        analyze: fitField.mapping?.extend?.analyzer || _analyzeList[0].key,
      });
    }
  };

  const formatNumber = (num: number) => (isNaN(num) ? undefined : Number(num));

  const checkLegalField = (field: MetadataTypes.HiveEntity): boolean => {
    const fitField = allFields.find(f => f.code === field.code);
    if (fitField) {
      const isDifferent = Object.keys(field).some(key => {
        // @ts-ignore
        return JSON.stringify(field[key]) !== JSON.stringify(fitField[key]);

        // return !isDifferent;
      });
    }
    return true;
  };

  const fetchFieldList = async () => {
    setDataLoading(true);
    const { code } = params;
    const res = await MetadataService.fetchHiveMetadataFieldList({ code });
    if (res?.success && res.data) {
      setFieldList(
        res.data.data.entity_data_defines.sort((a, b) => a.order - b.order),
      );
    }
    setDataLoading(false);
  };

  const handleFormFinish = async (values: any) => {
    // 新增
    if (isAdd) {
      const index = values.index ? 1 : 0;

      values = {
        ...values,
        editable: values.editable ? 0 : 1,
        show: values.show ? 1 : 0,
        required: values.required ? 1 : 0,
        array: values.array ? 1 : 0,
        max_length: formatNumber(values.max_length),
        min_length: formatNumber(values.min_length),
        min_arr_num: formatNumber(values.min_arr_num),
        max_arr_num: formatNumber(values.max_arr_num),
        mapping: {
          keyword: values.keyword ? 1 : 0,
          index: values.index ? 1 : 0,
          analyze: values.index && values.analyze ? 1 : 0,
          extend:
            !values.analyze || values.analyze === _analyzeList[0].key
              ? {}
              : {
                  analyzer: values.analyze,
                },
        },
      };
      if (!checkLegalField(values)) {
        message.error(
          '已存在相同Code元数据字段，请更换Code，或者使用已存在的元数据字段',
        );
        return;
      }
      const res = await MetadataService.addHiveMetadataField(values);
      if (res?.success) {
        message.success('新增元数据成功');
        setModalVisible(false);
        fetchFieldList();
      }
    } else {
      values = {
        ...values,
        max_length: formatNumber(values.max_length),
        min_length: formatNumber(values.min_length),
        min_arr_num: formatNumber(values.min_arr_num),
        max_arr_num: formatNumber(values.max_arr_num),
        editable: values.editable ? 0 : 1,
        show: values.show ? 1 : 0,
        required: values.required ? 1 : 0,
        array: values.array ? 1 : 0,
        mapping: {
          keyword: values.keyword ? 1 : 0,
          index: values.index ? 1 : 0,
          analyze: values.index && values.analyze ? 1 : 0,
          extend:
            values.analyze === _analyzeList[0].key
              ? {}
              : {
                  analyzer: values.analyze,
                },
        },
      };
      const res = await MetadataService.updateHiveMetadataField(values);
      if (res?.success) {
        message.success('更新元数据成功');
        setModalVisible(false);
        fetchFieldList();
      }
    }
  };
  const handleDeleteEntity = async (id: string) => {
    const res = await MetadataService.deleteHiveMetadataField({
      id: [id],
      type_id: params.id,
    });
    if (res?.success) {
      message.success('删除元数据实体成功');
      fetchFieldList();
    }
  };
  /**
   * 修改字段检索
   * */
  const changeEntityIndex = async (entity: MetadataTypes.HiveEntity) => {
    if (entity.mapping.index) {
      const res = await MetadataService.removeHiveMetadataFieldIndex({
        ...entity,
        type_id: params.id,
      });
      if (res?.success) {
        message.success('删除字段索引成功');
        const newList = [...fieldList];
        const index = newList.findIndex(item => item.id === entity.id);
        if (index > -1) {
          newList[index] = {
            ...entity,
            mapping: {
              ...entity.mapping,
              index: 0,
            },
          };
          setFieldList(newList);
        }
      }
    } else {
      const res = await MetadataService.addHiveMetadataFieldIndex({
        ...entity,
        type_id: params.id,
      });
      if (res?.success) {
        message.success('添加字段索引成功');
        const newList = [...fieldList];
        const index = newList.findIndex(item => item.id === entity.id);
        if (index > -1) {
          newList[index] = {
            ...entity,
            mapping: {
              ...entity.mapping,
              index: 1,
            },
          };
          setFieldList(newList);
        }
      }
    }
  };
  /**
   * 修改全文检索
   * */
  const changeEntitySearchIndex = async (entity: MetadataTypes.HiveEntity) => {
    if (entity.mapping.keyword) {
      const res = await MetadataService.removeHiveMetadataSearchIndex({
        ...entity,
        type_id: params.id,
      });
      if (res?.success) {
        message.success('删除全文索引成功');
        const newList = [...fieldList];
        const index = newList.findIndex(item => item.id === entity.id);
        if (index > -1) {
          newList[index] = {
            ...entity,
            mapping: {
              ...entity.mapping,
              keyword: 0,
            },
          };
          setFieldList(newList);
        }
      }
    } else {
      const res = await MetadataService.addHiveMetadataSearchIndex({
        ...entity,
        type_id: params.id,
      });
      if (res?.success) {
        message.success('添加全文索引成功');
        const newList = [...fieldList];
        const index = newList.findIndex(item => item.id === entity.id);
        if (index > -1) {
          newList[index] = {
            ...entity,
            mapping: {
              ...entity.mapping,
              keyword: 1,
            },
          };
          setFieldList(newList);
        }
      }
    }
  };

  const columns = [
    {
      title: '顺序',
      dataIndex: 'order',
    },
    {
      title: 'Code',
      dataIndex: 'code',
    },
    {
      title: '显示名',
      dataIndex: 'name',
    },
    {
      title: '数据类型',
      dataIndex: 'type',
    },
    {
      title: '是否必填',
      dataIndex: 'required',
      render: (checked: boolean) => (
        <Checkbox checked={checked} disabled={true}>
          必填
        </Checkbox>
      ),
    },
    {
      title: '编辑设置',
      dataIndex: 'editable',
      render: (checked: boolean) => (
        <Checkbox checked={!checked} disabled={true}>
          只读
        </Checkbox>
      ),
    },
    {
      title: '是否显示',
      dataIndex: 'show',
      render: (checked: boolean) => (
        <Checkbox checked={checked} disabled={true}>
          显示
        </Checkbox>
      ),
    },
    {
      title: '是否数组',
      dataIndex: 'array',
      render: (checked: boolean) => (
        <Checkbox checked={checked} disabled={true}>
          是
        </Checkbox>
      ),
    },
    {
      title: '全文索引',
      dataIndex: 'mapping',
      render: (mapping: { keyword: boolean }, record: any) => (
        <Checkbox
          checked={mapping?.keyword}
          onClick={() => changeEntitySearchIndex(record)}
          disabled={true}
        >
          是
        </Checkbox>
      ),
    },
    {
      title: '字段索引',
      dataIndex: 'mapping',
      render: (mapping: { index: boolean }, record: any) => (
        <Checkbox
          checked={mapping?.index}
          onClick={() => changeEntityIndex(record)}
          disabled={true}
        >
          是
        </Checkbox>
      ),
    },
    {
      title: '长度限制',
      dataIndex: 'min_length',
      render: (text: number, record: any) =>
        text || text === 0 ? `${text} - ${record.max_length}` : '',
    },
    {
      title: '描述',
      dataIndex: 'description',
    },
    {
      title: '操作',
      dataIndex: 'id',
      render: (id: string, record: any) => (
        <Space>
          <Button
            type="primary"
            size="small"
            onClick={() => {
              setIsAdd(false);
              editMetadataForm.setFieldsValue({
                ...record,
                editable: !record.editable,
                index: record.mapping?.index || false,
                keyword: record.mapping?.keyword || false,
                analyze:
                  record.mapping?.extend?.analyzer || _analyzeList[0].key,
              });
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除该元数据字段？"
            okText="是"
            cancelText="否"
            onConfirm={() => handleDeleteEntity(id)}
          >
            <Button danger={true} size="small">
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="metadata_basic_container">
      <div className="metadata_basic_header">
        <Space>
          <Button onClick={() => history.back()}>返回</Button>
          <Button
            type="primary"
            onClick={() => {
              setIsAdd(true);
              editMetadataForm.resetFields();
              setModalVisible(true);
            }}
          >
            新增
          </Button>
        </Space>
      </div>
      <Table
        columns={columns}
        dataSource={fieldList}
        rowKey="code"
        loading={dataLoading}
        pagination={false}
      />
      <Modal
        title={isAdd ? '新增元数据字段' : '编辑元数据字段'}
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => editMetadataForm.submit()}
      >
        <Form
          name="editMetadata"
          {...formLayout}
          initialValues={{ type_id: params.id, min_length:0, max_length:255, min_arr_num:0, max_arr_num:10,analyze: '' }}
          onFinish={handleFormFinish}
          form={editMetadataForm}
        >
          <Form.Item name="type_id" hidden={true} noStyle={true} />
          {/*隐藏字段*/}
          {!isAdd && (
            <>
              <Form.Item name="id" hidden={true} noStyle={true} />
              <Form.Item name="mapping" hidden={true} noStyle={true} />
            </>
          )}
          <Form.Item
            label="Code"
            name="code"
            rules={[{ required: true, message: '请填写Code' }]}
            help={'可以选择已存在的元数据字段'}
          >
            <AutoComplete
              options={codeOptions}
              onSearch={handleCodeChange}
              onSelect={handleCodeSelect}
              disabled={!isAdd}
            />
            {/*<Input disabled={!isAdd} />*/}
          </Form.Item>
          <Form.Item
            label="显示名"
            name="name"
            rules={[{ required: true, message: '请填写显示名' }]}
          >
            <Input />
          </Form.Item>
          
          <Row>
            <Col span={10} order={1} offset={3}>
              <Form.Item
                label="数据类型"
                name="type"
                labelCol={{span: 9, offset: 0}}
                rules={[{ required: true, message: '请填写数据类型' }]}
              >
                <Select style={{ width: '100%' }}>
                  {_dataType.map(data => (
                    <Select.Option value={data.key} key={data.key}>
                      {data.value}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={10} order={1}>
              <Form.Item
                label="顺序"
                name="order"
                rules={[{ required: true, message: '请填写顺序' }]}
              >
                <InputNumber />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={7} order={1} offset={3}>
              <Form.Item
                noStyle={true}
                shouldUpdate={(pre, cur) => pre.editable !== cur.editable}
              >
                {() => (
                  <Form.Item
                    label=""
                    name="required"
                    valuePropName="checked"
                  >
                    <Checkbox
                      disabled={(() =>
                        editMetadataForm.getFieldValue('editable'))()}
                    >
                      必填
                    </Checkbox>
                  </Form.Item>
                )}
              </Form.Item>
            </Col>
            <Col span={7} order={2}>
              <Form.Item
                noStyle={true}
                shouldUpdate={(pre, cur) => pre.required !== cur.required}
              >
                {() => (
                  <Form.Item
                    label=""
                    name="editable"
                    valuePropName="checked"
                  >
                    <Checkbox
                      disabled={(() =>
                        editMetadataForm.getFieldValue('required'))()}
                    >
                      只读
                    </Checkbox>
                  </Form.Item>
                )}
              </Form.Item>
            </Col>
            <Col span={7} order={3}>
              <Form.Item label="" name="show" valuePropName="checked">
                <Checkbox>显示</Checkbox>
              </Form.Item>
            </Col>
          </Row>
          
          <Row>
            <Col span={7} order={1} offset={3}>
              <Form.Item label="" wrapperCol={{span: 16}} name="array" valuePropName="checked">
                <Checkbox>是否数组</Checkbox>
              </Form.Item>
            </Col>
            <Col span={7} order={2}>
              <Form.Item label="" wrapperCol={{span: 16}} name="keyword" valuePropName="checked">
                <Checkbox>全文索引</Checkbox>
              </Form.Item></Col>
          </Row>
          <Form.Item
            noStyle={true}
            shouldUpdate={(pre, cur) => pre.array !== cur.array}
          >
            {() =>
              editMetadataForm.getFieldValue('array') ? (
                <>
                  <Row>
                    <Col span={10} order={1} offset={2}>
                      <Form.Item label="数组最小长度" labelCol={{span: 12, offset: 0}}  name="min_arr_num">
                        <InputNumber />
                      </Form.Item>
                    </Col>
                    <Col span={10} order={2}>
                      <Form.Item label="数组最大长度" labelCol={{span: 12, offset: 0}}  name="max_arr_num">
                        <InputNumber />
                      </Form.Item>

                    </Col>
                  </Row>
                </>
              ) : null
            }
          </Form.Item>
          {/*{isAdd && (*/}
          {/*  <>*/}
          <Form.Item label="字段索引">
            <Form.Item name="index" valuePropName="checked" noStyle>
              <Checkbox>是</Checkbox>
            </Form.Item>
            <Form.Item
              noStyle
              shouldUpdate={(pre, cur) => pre.index !== cur.index}
            >
              {() =>
                editMetadataForm.getFieldValue('index') ? (
                  <Form.Item name="analyze" noStyle>
                    <Select
                      defaultValue={''}
                      placeholder="请选择分词器"
                      style={{ width: 160 }}
                    >
                      {_analyzeList.map(op => (
                        <Select.Option value={op.key} key={op.key}>
                          {op.value}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                ) : null
              }
            </Form.Item>
          </Form.Item>
          {/*  </>*/}
          {/*)}*/}
          <Row>
            <Col span={10} order={1} offset={2}>
              <Form.Item label="最小长度" labelCol={{span: 9, offset: 0}} name="min_length">
                <InputNumber />
              </Form.Item>
            </Col>
            <Col span={10} order={1}>
              <Form.Item label="最大长度" labelCol={{span: 9, offset: 0}} name="max_length">
                <InputNumber />
              </Form.Item></Col>
          </Row>
          <Form.Item label="描述" name="description">
            <Input.TextArea />
          </Form.Item>
          {['biz_sobey_video', 'biz_sobey_audio', 'biz_sobey_picture', 'biz_sobey_document', 'biz_sobey_other'].includes(params.code) &&<Form.Item label="默认值" name="default_value">
            <Input />
          </Form.Item>}
        </Form>
      </Modal>
    </div>
  );
};
export default MetadataBasicDetail;
