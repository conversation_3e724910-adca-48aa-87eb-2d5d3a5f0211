import React, {
  FC,
  useState,
  useRef,
  useEffect,
  RefObject,
  InputHTMLAttributes,
  ChangeEvent,
} from 'react';
import {
  Form,
  message,
  Button,
  Checkbox,
  Empty,
  Tooltip,
  Pagination,
  Select,
  Breadcrumb,
  Popconfirm,
  Tabs,
  Modal,
  Progress,
  Input,
  Popover,
  Badge
} from 'antd';
import './index.less';

import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import contentListApis from '@/service/contentListApis';
import copyandmoveApis from '@/service/copyandmoveApis';
import searchTreeApis from '@/service/searchTreeApis';
import {
  formatMessage,
  useIntl,
  useSelector,
  useDispatch,
  Idownlist,
  useHistory,
} from 'umi';
import {
  MenuItem,
  Idetail,
  IrequestsearchData,
  MaterialList,
  BreadCrumb,
  ISearchlist,
} from './type';
import { ContentItem, DownloadModal, IconFont, ListTop } from '@/components';
import SearchPhotoModal from '@/components/searchPhotoModal';
import { getSensitiveWord } from '@/utils';
const { Option } = Select;
const { TextArea } = Input;
const CheckboxGroup = Checkbox.Group;
const { TabPane } = Tabs;
const ContentList: FC<{}> = () => {
  const [columnSelect, setColumnSelect] = useState<any>(
    [
      'extension',
      'size'
    ]
  );
  const history = useHistory()
  const path = history.location.query?.path
  const ids = history.location.query?.ids || []
  const folderPath = history.location.query?.folderPath
  const [searchModalVisible, setSearchModalVisible] = useState<boolean>(false)
  const [current, setCurrent] = useState<number>(1); //当前页码
  const [pageSize, setPageSize] = useState<number>(30); //每页条数
  const [totalPage, setTotalPage] = useState<number>(0); //素材总数
  const [allList, setAllList] = useState<Array<any>>([]); //总的的列表
  const [peopleIds, setPeopleIds] = useState<any>([])
  const [photoPath, setPhotoPath] = useState<string>('')
  const intl = useIntl();
  useEffect(() => {
    searchFolderList(peopleIds.length ? peopleIds : ids)
  }, [current, pageSize])
  const processingData = (ids: any, path?: any) => {
    let list: ISearchlist = {
      folderId: "",
      folderPath,
      pageIndex: current,
      pageSize: pageSize,
    };
    list.conditions = [
      { field: "type_", searchRelation: 0, value: ["biz_sobey_picture"] },
      { field: "related_people_ids", searchRelation: 2, value: ids instanceof Array ? ids : [ids]}
    ];
    return list;
  };
  // 公共素材检索
  const searchFolderList = async (ids: any) => {
    let data: ISearchlist = processingData(ids, path);//针对别人分享个人文件夹无权限时；
    if (ids.length) {
      contentListApis.searchfolder(data).then(res => {
        if (res && res.data && res.success) {
          setTotalPage(res.data.recordTotal);
          if (res.data.pageTotal < 0 && current !== 1) {
            setCurrent(current - 1);
            return;
          }
          setAllList(res.data.data);
        } else {
          setAllList([]);
        }
      })
    }
    else {
      setAllList([]);
      setTotalPage(0)
    }

  }
  const detail = (item: any, type: number) => {
    //已经在内部 则直接页面跳转
    const dev = process.env.NODE_ENV === 'development' ? "" : "/rman"
    window.open(`${window.location.origin}${dev}/#/basic/rmanDetail/${item.contentId_}`);
  };
  // 页码切换
  const changepage = (page: number, size: any) => {
    setCurrent(page);
    setPageSize(size || 0);
  };
  const goBack = () => {
    history.push('/basic/rmanCenterList')
  }
  return (
    <div className="similarity-graph">
      <div className="left">
        <div className="top">
          <Button type="text" onClick={goBack}>&lt;返回</Button>
          <Button type="text" onClick={() => setSearchModalVisible(true)} icon={<IconFont type="iconshangchuan2" />}>上传</Button>
        </div>
        <div className="picture-box">
          <div className="picture">
            <div className="circle circle_1"></div>
            <div className="circle circle_2"></div>
            <div className="circle circle_3"></div>
            <div className="circle circle_4"></div>
            <img src={photoPath ? (photoPath.includes('/mstorage') ? photoPath?.substring(9, photoPath.length) : photoPath) : path.includes('/mstorage') ? path?.substring(9, path.length) : path} alt="" />
          </div>
        </div>
      </div>
      <div className="right">
        <div className='contentitem_box'>
          {allList.length ? allList.map((item, index) => (
            <ContentItem
              key={item.uniqueCode ? item.uniqueCode : item.contentId_} //针对我的分享里面会存在重复文件情况
              detail={item}
              recycleBin={false}
              myShare={1}
              noCheckbox={true}
              modal={true}
              columns={[]}
              showNoBtn={true}
              goDetail={(type: number) => detail(item, type)}
              shareEnble={false}
              resourceGroup={false}
            />
          )) : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description='糟糕，没有找到相似内容~' />}
        </div>
       {totalPage> 0 && <div className="pagination">
          <Pagination
            current={current}
            pageSize={pageSize}
            total={totalPage}
            size="small"
            showQuickJumper
            onChange={changepage}
            showTotal={total => intl.formatMessage({
              id: "共条"
            }, { total })}
            showSizeChanger={true}
            pageSizeOptions={['30', '40', '50', '100']}
          // showTotal={total => intl.formatMessage({
          //   id: "page-total",
          //   defaultMessage: `共 ${total} 页`
          // })}
          />
        </div>}
      </div>
      <SearchPhotoModal
        visible={searchModalVisible}
        onClose={() => setSearchModalVisible(false)}
        searchFolderList={searchFolderList}
        setPhotoPath={setPhotoPath}
        setPeopleIds={setPeopleIds}
        setCurrent={setCurrent}
      />
    </div>
  );
};
export default ContentList;