import React, { FC, useState, useRef, useEffect } from 'react';
import { message, Button } from 'antd';
import { CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons';
import './index.less';
import Entity from '@/components/entity/entity';
import entityApis from '@/service/entityApis';
import Loading from '@/components/loading/loading';
import { RouteComponentProps } from 'react-router-dom'
import BasicMetadata, { IBasicMetadataRef } from '@/components/basicMetadata/basicMetadata';
import { getType, copyObject } from '@/utils';
import { useIntl } from 'umi';
import { IEntity, IEntityMetadata, IFormItem } from '@/types/entityTypes';
import _ from 'lodash'

interface IContentDetailParams {
  contentId: string
}


const ContentDetail: FC<RouteComponentProps<IContentDetailParams>> = (props) => {
  const [editVisible, setEditVisible] = useState<boolean>(false);
  const [entity, setEntity] = useState<IEntity>()
  const [metadata, setMetadata] = useState<IFormItem[]>([])
  const [title, setTitle] = useState<string>('')
  const [canEdit, setCanEdit] = useState<boolean>(false)
  const [frameRate, setFrameRate] = useState<number>(25)
  const metadataRef = useRef<IBasicMetadataRef>(null)
  const intl = useIntl()
  useEffect(() => {
    const contentId = props.match.params.contentId
    entityApis.getEntityMetadata(contentId).then(res => {
      if (res && res.data && res.success) {
        if (Array.isArray(res.data)) {
          res.data.forEach(item => {
            if (!item.value) {
              item.value = ''
            }
          })
          setMetadata(res.data)
        }
      }
    })
    entityApis.getEntity(contentId).then(res => {
      if (res && res.data && res.success) {
        let path = _.find(res.data.fileGroups, (f) => {
          return f.typeCode === 'previewfile'
        })
        if (!path) {
          path = _.find(res.data.fileGroups, (f) => {
            return f.typeCode === 'sourcefile'
          })
        }
        if (path) {
          setFrameRate(path.fileItems[0].frameRate)
        }
        setEntity({
          path: path ? path.fileItems[0].filePath : '',
          keyframes: res.data.keyframe && res.data.keyframe.filePath ? res.data.keyframe.filePath : '',
          type: getType(res.data.type)
        })
        setCanEdit(res.data.extensions.canEdit)
        setTitle(res.data.entityName)
        document.title = res.data.entityName
      }
    })
  }, [])
  const show = (str: any) => {
    var rs = '';
    for (var i of str) {
      var code = i.codePointAt(0);
      if (code < 128) {
        rs += i;
      } else if (code > 127 && code < 2048) {
        rs += String.fromCharCode((code >> 6) | 192, (code & 63) | 128);
      } else if (code > 2047 && code < 65536) {
        rs += String.fromCharCode((code >> 12) | 224, ((code >> 6) & 63) | 128, (code & 63) | 128);
      } else if (code > 65536 && code < 1114112) {
        rs += String.fromCharCode((code >> 18) | 240, ((code >> 12) & 63) | 128, ((code >> 6) & 63) | 128, (code & 63) | 128);
      }
    }
    return rs;
  }
  const cancelMetadata = () => { // 点击取消的方法
    if (metadataRef.current) {
      metadataRef.current.getOldItems()
      setEditVisible(false)
    }
  }

  const submitMetadata = () => { // 点击提交的方法
    if (metadataRef.current) {
      metadataRef.current.getNewItems().then(newValue => {
        if (newValue) {
          const newMetadata = copyObject(newValue)
          const oldMetadata = copyObject(metadata)
          newMetadata.forEach(item => {
            if (item.controlType === 8) {
              item.value = JSON.stringify(item.value)
            } else if (item.controlType === 14) {
              item.value = JSON.stringify(item.value)
            } else if (item.controlType === 12) {
              item.value = JSON.stringify(item.value)
            }
          })
          entityApis.setEntityMetadata(props.match.params.contentId, {
            oldValue: oldMetadata,
            newValue: newMetadata
          }).then(res => {
            if (res && res.data && res.success) {
              message.success(intl.formatMessage({
                id: 'edit-success',
                defaultMessage: '修改成功'
              }))
              setMetadata(newMetadata)
              const name = newMetadata.find(item => item.fieldName === 'name')
              if (name && name.value !== title) {
                setTitle(name.value as string)
              }
              setEditVisible(false)
            } else {
              message.error(intl.formatMessage({
                id: 'edit-error',
                defaultMessage: '修改失败'
              }))
            }
          })
        }
      }).catch(err => {
        message.error(intl.formatMessage({
          id: 'no-verify',
          defaultMessage: '您还有数据没有通过验证'
        }))
      })
    }
  }
  return (
    <div className="contentdetail_container">
      <div className='contentdetail_top'>
        <div className='top_title'>{title}</div>
        {/* <div className='score'> // 没数据 先不要了
          <StarFilled style={{ color: '#F6BA43' }} />
          <StarFilled style={{ color: '#F6BA43' }} />
          <StarFilled style={{ color: '#F6BA43' }} />
          <StarFilled style={{ color: '#F6BA43' }} />
          <StarFilled style={{ color: '#F6BA43' }} />
          <span className='score_people'>4.7 (216 reviews)</span>
        </div> */}
      </div>
      <div className='contentdetail_bottom'>
        <div className='exhibition'>
          {
            entity && <Entity src={entity.path} keyframes={entity.keyframes} type={entity.type} />
          }
        </div>
        <div className='essential_information'>
          <div className='essential_title'>{intl.formatMessage({
            id: 'base-message',
            defaultMessage: '基本信息'
          })}</div>
          <div className='blue'></div>
          {
            canEdit && <div className='edit'>
              {
                editVisible ? (
                  <div className='editor_selection'>
                    <CheckCircleFilled onClick={submitMetadata} className='icon' />
                    <CloseCircleFilled onClick={cancelMetadata} className='icon' />
                  </div>
                ) : (
                    // <img src={require('../../../images/contentlibrary/edit.png')} onClick={() => setEditVisible(true)} />
                    <Button onClick={() => setEditVisible(true)} size="small">{intl.formatMessage({
                      id: 'edit',
                      defaultMessage: '编辑'
                    })}</Button>
                  )
              }
            </div>
          }
          <BasicMetadata ref={metadataRef} items={metadata} edit={editVisible} frameRate={frameRate} />
        </div>
      </div>
      <Loading></Loading>
    </div>

  );
};

export default ContentDetail;
