import React, { FC , useEffect , useRef} from "react";
import './index.less'
import { XMindEmbedViewer } from 'xmind-embed-viewer'

const Xmind: FC<any> = ({src}) => {
    const doms = useRef<any>(null);
    useEffect(()=>{
        init();
    },[])

    const init  = () => {
        const viewer = new XMindEmbedViewer({
            el: doms.current, // HTMLElement | HTMLIFrameElement | string
            // 如果在中国大陆境内速度慢，可以添加的参数 `region: 'cn'` 改为使用 xmind.cn 的图库作为依赖。
            // region: 'cn' //optinal, global(default) or cn
          })
          fetch(src)
            .then(res => res.arrayBuffer())
            .then(file => viewer.load(file))
    }

    return (
        <div className="xmind_container">
            <div className="xmind_content" ref={doms} style={{width:'100%',height:'100%'}}></div>
        </div>
    )
}


export default Xmind;