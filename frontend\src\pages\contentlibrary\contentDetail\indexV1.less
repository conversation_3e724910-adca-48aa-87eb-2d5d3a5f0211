.video_detail_wrapper {

  //padding: 20px;
  // h2 {
  //   padding: 12px 20px;
  //   //box-shadow: 0 2px 4px 0 rgba(197, 197, 197, 0.5);
  // }

  .title {
    padding: 12px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    div {
      button {
        margin-left: 20px;
      }
    }
  }

  .content {
    display: flex;
    padding: 20px 15px;
    background-color: #F2F2F2;

    .entity_view_wrapper {
      width: 70%;
      background-color: #fff;
      height: ~"calc(100vh - 110px)";
      //width: 70%;
      //height: calc(70vw * 10 / 16);
    }


    .entity_info_wrapper {
      position: relative;
      height: ~"calc(100vh - 110px)";
      flex: 1;
      margin-left: 24px;
      padding: 12px;
      background-color: #ffffff;


      .ant-tabs-tab-btn {
        font-weight: 800;
      }

      .ant-form-item-label {
        text-align: left;
      }

      .ant-tabs-tabpane {
        height: 100%;
        max-height: calc(100vh - 180px);
        overflow-y: auto;
      }

      .editor_selections {
        float: right;
      }

      .editor_selection {
        float: right;
        width: 40px;
        font-size: 17px;
        display: flex;
        flex-direction: column;
        height: 50px;
        justify-content: space-between;
      }

      .icon:hover {
        color: var(--primary-color);
      }

      .voice_div {
        .voice-input {
          margin-bottom: 10px;

          button {
            margin-left: 10px;
          }

          .changeVision {
            margin-left: 100px;
          }
        }

        .time-line {
          height: 100%;
          max-height: calc(100vh - 230px);
          padding-top: 20px;
          overflow-y: auto;

          .ant-timeline-item {
            padding-bottom: 16px;
          }
        }

        .paragraph-wrapper {
          position: relative;
          text-align: justify;
          height: 100%;
          max-height: calc(100vh - 230px);


          .paragraph-content {
            user-select: text;
            padding: 5px 10px;

            .selected {
              text-decoration: underline;
            }
          }

          .text_pop {
            display: none;
            position: absolute;
            min-width: 72px;
            top: 0;
            left: 3px;
            padding: 8px;
            background-color: #fff;
            color: var(--primary-color);
            border-radius: 3px;
            cursor: pointer;
            box-shadow: 0 0 8px rgba(0, 0, 0, .15);

            &::after {
              content: ' ';
              width: 0;
              height: 0;
              position: absolute;
              bottom: -6px;
              left: 50%;
              transform: translateX(-50%);
              border-top: 6px solid #fff;
              border-left: 6px solid transparent;
              border-right: 6px solid transparent;
            }
          }

          span:hover {
            background-color: var(--primary-color);
            color: #fff;
            padding: 2px 0px;
          }
        }
      }
    }

    .lyric_item {
      display: flex;
      cursor: pointer;
      color: #363636;
      align-items: center;

      &:hover,
      &.active {
        color: #f0a95d;
      }


      .lyric_time {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 90px;
        height: 36px;
        background: #eeeff3;
      }

      .lyric_title {
        display: flex;
        align-items: center;
        margin-left: 10px;

        .voice-search-value {
          color: red;
        }
      }

      .icon {
        display: flex;
        align-items: center;
        margin-left: 10px;
        opacity: 0;
      }
    }

    .lyric_item:hover {
      .icon {
        opacity: 1;
      }
    }

    .sequence_list {
      position: relative;
      width: 100%;

      .sequence_operator {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 30%;

        .create {
          margin-left: 16px !important;
        }
      }

      .sequence_item_wrap {
        display: flex;
        width: 400px;
        align-items: center;

        .sequence_item_checkbox {
          margin-right: 4%;
        }

        .sequence_item {
          position: relative;
          height: 72px;
          display: flex;
          cursor: pointer;
          margin: 12px 0;

          img {
            width: 130px;
            height: 100%;
          }

          .sequence_content {
            height: 100%;
            display: flex;
            flex-direction: column;
            margin-left: 12px;
            justify-content: space-between;

            // .sequence_title {
            //   flex: 1;
            // }

            span {
              font-size: 12px;
              color: #747477;
            }
          }

          .del_icon {
            position: absolute;
            right: 24px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 10;
          }
        }
      }

    }
  }
}

.title_modal {
  display: flex;

  .title_modal_right {
    .modal_input {
      display: flex;
      height: 34px;
      justify-content: space-evenly;
      align-items: center;
    }

    .title_modal_images {
      display: flex;
      flex-wrap: wrap;
      //justify-content: space-between;
      width: 100%;
      height: 60vh;
      padding: 0 48px;

      & > div {
        margin: 12px;
      }

      img {
        width: 170px;
        height: 100px;
      }

      .title_modal_title {
        display: inline-block;
        width: 120px;
        margin-left: 10px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }

    .title_modal_pag {
      display: flex;
      justify-content: center;
      width: 100%;
      margin-top: 10px;
    }
  }

}

.ant-popover-inner {
  width: 90px !important;
}

.ant-popover-content {
  width: 90px !important;
}

.ant-popover-inner-content {
  background: #fff !important;
}

.ant-popover-arrow {
  background-color: #fff !important;
}

a {
  color: #363636 !important;
}
