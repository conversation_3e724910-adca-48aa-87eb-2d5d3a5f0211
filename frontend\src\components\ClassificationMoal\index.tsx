import React, { FC, useState, useEffect } from 'react';
import './index.less';
import {
    Modal,
    Button,
    Spin,
    Tree,
    TreeProps,
} from 'antd';
import { IUpload, useDispatch, useIntl, useSelector } from 'umi';
import loading from '@/loading';
import contentListApis from '@/service/contentListApis';
interface TreeData {
    title: string;
    key: string;
    children: TreeData[];
}
const ClassificationModal: FC<{
    SavedValue: string
    visible: boolean;
    onConfirm: (res: any[]) => void;
    onCancel: () => void;
}> = ({ visible, onConfirm, onCancel, SavedValue }) => {

    const dispatch = useDispatch();
    const {
        tasks,
    } = useSelector<{ upload: IUpload }, IUpload>(({ upload }) => {
        return upload;
    });
    const intl = useIntl();
    const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
    const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
    const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
    const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);
    const [treeData, setTestTree] = useState<TreeData[]>([]);
    const [selectedItems, setselectedItems] = useState([]);

    const onExpand: TreeProps['onExpand'] = (expandedKeysValue) => {
        setExpandedKeys(expandedKeysValue);
        setAutoExpandParent(false);
    };
    const onCheck: TreeProps['onCheck'] = (checkedKeysValue, info) => {
        const selectedItemsList = checkedKeysValue.map((key: string | number) => {
            const selectedNode = info.checkedNodes.find(node => node.key === key);
            return selectedNode ? { theme_names: selectedNode.title, theme_codes: key } : null;
        }).filter((item: null) => item !== null);
        const selectedItems = extractLeafNodes(treeData, checkedKeysValue);
        const updatedTasks = tasks.map(item => {
            if (item.fileName === SavedValue) {
                return { ...item, selectedItems };
            }
            return item;
        });
        setselectedItems(selectedItems);
        dispatch({
            type: 'upload/changeTasks',
            payload: {
                value: updatedTasks,
            },
        });
        setCheckedKeys(checkedKeysValue as React.Key[]);
    };

    const onSelect: TreeProps['onSelect'] = (selectedKeysValue, info) => {
        setSelectedKeys(selectedKeysValue);
    };

    useEffect(() => {
        gettree();
    }, []);
    const extractLeafNodes = (treeData: any[], selectedKeys: Iterable<unknown> | null | undefined) => {
        const result: { theme_names: any; theme_codes: any; }[] = [];
        const selectedKeySet = new Set(selectedKeys);

        function traverse(node: { key: unknown; children: any[]; title: any; }) {
            if (selectedKeySet.has(node.key)) {
                // 如果是叶子节点或者虽然有children但children的key都不在selectedKeys中
                if (node.children.length === 0 ||
                    !node.children.some(child => selectedKeySet.has(child.key))) {
                    result.push({
                        theme_names: node.title,
                        theme_codes: node.key
                    });
                }
            }

            if (node.children && node.children.length > 0) {
                node.children.forEach(child => traverse(child));
            }
        }

        treeData.forEach(root => traverse(root));
        return result;
    };
    const gettree = () => {
        contentListApis.getTreetheme().then(res => {
            if (res && res.extendMessage) {
                const formatTreeData = (nodes: any[]): TreeData[] => {
                    return nodes.map(item => ({
                        title: item.categoryName,  // 确保标题字段映射
                        key: item.categoryCode,   // 确保键值字段映射
                        children: item.children?.length > 0
                            ? formatTreeData(item.children)  // 递归处理子节点
                            : [],  // 保持空数组结构
                    }));
                };

                const formattedData = formatTreeData(res.extendMessage);
                setTestTree(formattedData);
            } else {
                console.error('获取树主题失败，res未定义或extendMessage未定义');
            }
        }).catch(error => {
            console.error('请求失败:', error);
        });
    }

    const handleCancel = () => {
        onCancel();
        setselectedItems([])
        setCheckedKeys([])
    };

    const handleOk = () => {
        onConfirm(selectedItems);
        setselectedItems([])
        setCheckedKeys([])
    };

    return (
        <div className="classification-model">
            <Modal
                title={'选择分类'}
                className="ficationModal"
                style={{ height: '72vh' }}
                open={visible}
                onCancel={handleCancel}
                footer={[
                    <Button onClick={handleCancel}>{intl.formatMessage({ id: '取消' })}</Button>,
                    <Button type="primary" onClick={handleOk}>
                        {intl.formatMessage({ id: '确定' })}
                    </Button>,
                ]}
                destroyOnClose={true}
            >

                <div className="fication-conten">
                    <Tree
                        checkable
                        onExpand={onExpand}
                        expandedKeys={expandedKeys}
                        autoExpandParent={autoExpandParent}
                        onCheck={onCheck}
                        checkedKeys={checkedKeys}
                        onSelect={onSelect}
                        selectedKeys={selectedKeys}
                        treeData={treeData}
                    />
                </div>
            </Modal>
        </div>
    );
};

export default ClassificationModal;
