import React, { useState, useEffect, useRef } from 'react';
import { Modal, Checkbox, Button, Form, message, Input, List, Tree, Radio,Tabs,Empty,Table } from 'antd';
import { IconFont } from '@/components/iconFont/iconFont';
import SmartService from '@/service/smartService';
import './index.less';
import { IPermission } from '@/models/permission';
import { useSelector } from 'umi';
import globalParams from '@/permission/globalParams';
import perCfg from '@/permission/config';
import DownOutlined from '@ant-design/icons/lib/icons/DownOutlined';
import FormItem from '../sortForm/formItem';
import MapTable from './mapTable';
import { getGuid } from '@/utils';
import entityApis from '@/service/entityApis';

const CheckboxGroup = Checkbox.Group;

interface CreateModalProps {
  modalVisible: boolean;
  modalClose: () => void;
  callback?: (data:any) => void;
  analysislist: any;
}
const BindKnowledgeMap: React.FC<CreateModalProps> = props => {
  const { modalClose,callback, modalVisible, analysislist } = props;
  const propsList:any = Array.isArray(analysislist) ? analysislist : [analysislist]
  const [mapList, setMapList] = useState<any>([]);
  const [nodeList, setNodeList] = useState<any>([]);
  const [mapInput, setMapInput] = useState<any>('');
  const [mapId, setMapId] = useState<number>();
  const [nodeInput, setNodeInput] = useState<string>('');
  // 权限
  const { rmanGlobalParameter, permissions } = useSelector<
    { permission: any },
    IPermission
  >(({ permission }) => permission);
  const [mapListLoading, setMapListLoading] = useState<any>(true);
  const [knowledgeListLoading, setKnowledgeListLoading] = useState<any>(true);
  const [selectednode, setSelectednode] = useState<any>([]);
  const [knowledgeMapList, setKnowledgeMapList] = useState<any>([]);
  const { TabPane } = Tabs;
  const [activeTab, setActiveTab] = useState<any>('1');
  const [removeList, setRemoveList] = useState<any>([]);
  useEffect(() => {
    if (modalVisible) {
      selectMapList()
    }
  }, [modalVisible]);
  useEffect(() => {
    if (mapId || mapId === 0) {
      selectNodeList()
    }
  }, [mapId]);
  const closemodal = () => {
    modalClose();
    setSelectednode([]);
    setKnowledgeMapList([]);
    setMapList([]);
    setNodeList([]);
    setMapInput('');
    setMapId(undefined);
    setNodeInput('');
  };
  const mapNodeClick = (data: any) => {
    // console.log(data,mapList)
    sessionStorage.setItem('mapNodeSelectId', data.id);
    setMapId(data.id)
    const temp = JSON.parse(JSON.stringify(mapList));
    temp.map((item: any) => {
      if (data.id === item.id) {
        item.active = true
      } else {
        item.active = false
      }
      return item
    })
    setMapList(temp)
  };
  const mapChange = (e: any) => {
    // console.log(e.target.value)
    setMapInput(e.target.value)
  };
  const selectMapList = async () => {
    const res = await SmartService.fetchMapList(mapInput);
    const mapNodeSelectId = sessionStorage.getItem('mapNodeSelectId');
    setMapList(res?.data?.map((item: any, index: number) => {
      return { ...item, active: mapNodeSelectId ? (item.id === Number(mapNodeSelectId)) : (index === 0 ? true : false) }
    }))
    // console.log(res,res?.data?.list?.results[0].id)
    setMapId(mapNodeSelectId ? Number(mapNodeSelectId) : res?.data[0]?.id)
    setMapListLoading(false)
  };
  const selectNodeList = async () => {
    if (!mapId && mapId !== 0) {
      message.info('请先选择地图')
      return
    }
    setKnowledgeListLoading(true);
    let res;
    res = await SmartService.fetchNodeList(mapId, nodeInput);
    setNodeList(res?.data?.map((item:any)=>(
      {
        ...item,
        propertyValue:JSON.parse(item.propertyValue)
      }
    )))
    setKnowledgeListLoading(false);
  };
  const handleSave = async () => {
    console.log(propsList,selectednode)
    let params:any = []
    let newBindResource:any = [];
    selectednode.forEach((item:any)=>{
      propsList.forEach((item2:any)=>{
        // 没有才添加
        if(!item.propertyValue.bindresource?.some((temp:any)=>temp.contentId_===item2.contentId_)){
          item.propertyValue.bindresource.push({
            name:item2.name_,
            contentId:item2.contentId_,
            contentId_:item2.contentId_,
            type:item2.type_,
            keyframe_:item2.keyframe_,
            tempid:getGuid(),
          })
          newBindResource.push({
            "contentId": item2.contentId_, //资源id
            "name": item2.name_,  //资源名称
            "courseKnowledgeName": item.propertyValue.label,  //知识节点名称
            "knowledgeNames": []
          });
        }
      })
      params.push({
        nodeId:item.nodeId,
        propertyValue:JSON.stringify(item.propertyValue),
      });
    })
    console.log(params);
    const res = await SmartService.updateKnowledge(params);
    if (res?.message === 'OK') {
      message.success('更新成功')
      closemodal();
      entityApis.resourceOptionsCount(propsList.map((item:any)=>item.contentId_),'knowledge').then((res:any)=>{
        if(res.success){
          callback?.(Object.values(res.data)?.[0]);
        }else{
          console.log('知识点绑定计数失败');
        }
      })
    } else {
      message.error('更新失败')
    }
    // 添加资源日志记录
    const mapdata = mapList.find((item:any)=>item.id===mapId);
    const res2:any = await SmartService.addlog(mapdata.mapName,newBindResource);
    if(res2.success){
        console.log('添加资源日志记录成功！')
    }else{
        console.log('添加资源日志记录失败！')
    }

  };
  const handleRemove = async () => {
    if(removeList.length===0) return
    console.log('removeList',removeList);
    let params:any = [];
    removeList.map((item:any)=>{
      item.knowlege.map((item1:any)=>{
        const current = params?.findIndex((item2:any)=>item2.nodeId===item1.id); //当前节点id是否已经存在
        if(current>-1){
          params[current].propertyValue.bindresource.map((item2:any,index_:number)=>{
            if(item.contentId_ === item2.contentId_){
              params[current].propertyValue.bindresource.splice(index_,1); //去除当前绑定
            }
          })
        }else{
          item1.propertyValue.bindresource.map((item3:any,index_:number)=>{
            if(item.contentId_ === item3.contentId_){
              item1.propertyValue.bindresource.splice(index_,1); //去除当前绑定
            }
          })
          params.push({
            nodeId:item1.id,
            propertyValue:item1.propertyValue,
          });
        }
      })
    })
    console.log('updateKnowledge',params);
    const res = await SmartService.updateKnowledge(params.map((item:any)=>({
      ...item,
      propertyValue:JSON.stringify(item.propertyValue),
    })));
    if (res?.message === 'OK') {
      message.success('解绑成功')
      closemodal();
    } else {
      message.error('解绑失败')
    }
  };
  const tabChange = (key:any) => {
    setActiveTab(key)
    if(key === "2"){
      fetchBindKnowledge(propsList.map((item:any)=>item.contentId_))
    }
  };
  const fetchBindKnowledge = async (keys:any) => {
    const res = await SmartService.fetchBindKnowledges({
      resourceId:keys.join(',')
    });
    setKnowledgeMapList(res?.data)
  };
  const checkList = (videoId:any,rows:any)=>{
    console.log('checkList',videoId,rows)
    const temp = JSON.parse(JSON.stringify(removeList));
    if(temp.length===0){
      temp.push({
        contentId_:videoId,
        knowlege:rows
      })
    }else{
      for(let i=0; i<temp.length;i++){
        if(temp[i].contentId_ === videoId){
          if(rows.length===0){ //如果为空则剔除该节点
            temp.splice(i,1);
          }else{
            temp[i].knowlege=rows;
          }
          break;
        }
        if(i===temp.length-1){
          temp.push({
            contentId_:videoId,
            knowlege:rows
          })
        }
      }
    }
    // console.log('result1',temp)
    setRemoveList(temp)
  }
  const columns:any = [
    {
      title: '知识点名称',
      dataIndex: 'label',
      key: 'label',
      render: (text: any, record: any) => {
        return (
          <div className='label'>
            <span>{record.propertyValue.label}</span>
          </div>
        )
      }
    }
  ]

  return (
    <Modal
      className="bindKnowledgeModal"
      destroyOnClose={true}
      visible={modalVisible}
      closable={true}
      onCancel={closemodal}
      width={848}
      footer={[
        <div key={'left'} className={activeTab==='1'?'footerLeft':'footerLeft_'}>
        </div>,
        <div className='footerRight' key={'right'}>
          <div>
            <Button
              key="bindMap"
              type="primary"
              onClick={activeTab==='1'?handleSave:handleRemove}
              disabled={activeTab==='1'?(selectednode.length>0 ? false : true):(removeList.length===0 ? true : false)}
            >
              {activeTab==='1'?'绑定':'解绑'}
            </Button>
            <Button key="downloadno" onClick={closemodal}>
              取消
            </Button>
          </div>
        </div>
      ]}
    >
        <div className='modal_content'>
        <Tabs defaultActiveKey="1" className='tabs' onChange={tabChange}>
          <TabPane tab="全部地图" key="1">
            <div className='main'>
              <div className='searchMapContainer'>
                <div className='search_top'>
                  <div className='search'>
                    <Input
                      placeholder='输入地图名称'
                      value={mapInput}
                      onChange={(e) => mapChange(e)}
                      onPressEnter={selectMapList}
                    />
                    <IconFont
                      type='iconsousuo3'
                      className='searchIcon'
                      onClick={selectMapList}
                    />
                  </div>
                  <Button
                    onClick={() => { setMapInput(''); selectMapList() }}
                  >
                    重置
                  </Button>
                </div>
                <div className='mapListContainer'>
                  <List
                    dataSource={mapList}
                    loading={mapListLoading}
                    renderItem={(item: any, index: number) => {
                      return <List.Item key={item.id}
                        className={item.active ? 'activeItem' : ''}
                        onClick={() => mapNodeClick(item)}>
                        {item.mapName}
                      </List.Item>;
                    }
                    }
                  />
                </div>
              </div>
              <div className='searchNodeContainer'>
                <div className='searchNode_top'>
                  <div className='search'>
                    <Input
                      placeholder='输入节点名称'
                      value={nodeInput}
                      onChange={(e: any) => setNodeInput(e.target.value)}
                      onPressEnter={() => selectNodeList()}
                    />
                    <IconFont
                      type='iconsousuo3'
                      className='searchIcon'
                      onClick={() => selectNodeList()}
                    />
                  </div>
                  <Button
                    onClick={() => { setNodeInput(''); selectNodeList() }}
                  >
                    重置
                  </Button>
                </div>
                <div className='knowledgeListContainer'>
                  <Checkbox.Group
                    // onChange={onRadio}
                  >
                    <Table
                      dataSource={nodeList}
                      loading={knowledgeListLoading}
                      rowSelection={{
                        type: 'checkbox',
                        selectedRowKeys: selectednode.map((item: any) => item.id),
                        onChange: (selectedRowKeys: any, selectedRows: any) => {
                          setSelectednode(selectedRows)
                        },
                        // getCheckboxProps(record) {
                        //   return {
                        //     disabled: record.propertyValue.bindresource?.map((item:any)=>item),
                        //   };
                        // },
                      }}
                      rowKey='id'
                      pagination={false}
                      columns={columns}
                      scroll={{ y: 390 }}
                    />
                  </Checkbox.Group>
                </div>
              </div>
            </div>
          </TabPane>
          <TabPane tab="已绑定地图" key="2">
            <div className='knowledgeMapList'>
              {
                knowledgeMapList?.some((item:any)=>item.data.length>0)?
                  knowledgeMapList?.map((item:any)=>(
                    item.data.length>0 &&
                      <MapTable
                        columns={[
                          {
                            title:propsList.filter((item_:any)=>(item_.contentId_===item.resourceId))?.[0].name_,
                            dataIndex:'knowledgeName'
                          },
                          {title:'',dataIndex:'mapName',
                          render: (_text: any, record: any) => (
                            <span>来源：{_text}</span>
                          )}
                        ]}
                        visible={modalVisible}
                        videoId={item.resourceId}
                        dataSource={item.data?.map((item:any)=>({
                          ...item,
                          propertyValue:JSON.parse(item.propertyValue)
                        }))}
                        callback={checkList}
                      />
                  )):<div className='empty'><Empty/></div>
              }
            </div>
          </TabPane>
        </Tabs>
        </div>

    </Modal>
  );
};

export default BindKnowledgeMap;
