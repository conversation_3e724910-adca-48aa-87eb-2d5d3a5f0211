{"version": 3, "sources": ["webpack:///webpack/bootstrap 94bf1f25e6b099b77354", "webpack:///./src/common/commonUtil.js", "webpack:///./src/common/globalUtil.js", "webpack:///./src/common/httpUtil.js", "webpack:///./src/core/webTransfer.js", "webpack:///./node_modules/buffer/index.js", "webpack:///(webpack)/buildin/global.js", "webpack:///./node_modules/base64-js/index.js", "webpack:///./node_modules/ieee754/index.js", "webpack:///./node_modules/isarray/index.js", "webpack:///./src/libs/md5/spark-md5.js", "webpack:///./src/core/vtubeTransfer.js", "webpack:///./src/indexPure.js"], "names": [], "mappings": ";AAAA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,mCAA2B,0BAA0B,EAAE;AACvD,yCAAiC,eAAe;AAChD;AACA;AACA;;AAEA;AACA,8DAAsD,+DAA+D;;AAErH;AACA;;AAEA;AACA;;;;;;;;AC7DA;AAAA;AACA,2BAA2B;AAC3B,uCAAuC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8FAA8F,OAAO;AACrG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0FAA0F,OAAO;AACjG;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,oBAAoB,YAAY;AAChC;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,2BAA2B,sBAAsB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,8BAA8B,QAAQ;AACtC;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,8BAA8B,QAAQ;AACtC;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,8BAA8B,QAAQ;AACtC,+BAA+B;AAC/B;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,kBAAkB;AACzC;AACA;AACA;AACA;AACA,8BAA8B,kBAAkB;AAChD,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2E;;;;;;;AChMA;AAAA;AACA,2E;;;;;;;;ACDA;AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA;AACA;AACA,yE;;;;;;;;;ACvGA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,oDAAoD,wBAAwB;AAC5E;AACA,mDAAmD,gBAAgB;AACnE;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,qDAAqD;AACrD;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA,8BAA8B;AAC9B,SAAS;AACT;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA,qDAAqD;AACrD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA,8BAA8B;AAC9B,SAAS;AACT;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,sBAAsB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,uDAAuD,uBAAuB,EAAE;AAChF,wGAAuD,KAAK,IAAI,wCAAwC;AACxG;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,SAAS;AAChC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD,wBAAwB;AACjF;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE,mBAAmB,EAAE;AAC9F,SAAS;AACT;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,+CAA+C,qBAAqB;AACpE;AACA,+CAA+C;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,iBAAiB;AACjB;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,aAAa;AACb;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,+CAA+C,qBAAqB;AACpE;AACA,+CAA+C;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,qDAAqD;AACrD;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD;AACrD;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD;AAClD;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,+CAA+C,qBAAqB;AACpE;AACA,+CAA+C;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,qDAAqD;AACrD;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD;AACrD;AACA;AACA;AACA;AACA,2DAA2D,oEAAoE;AAC/H;AACA,oEAAoE;AACpE;AACA,oCAAoC;AACpC;AACA;AACA;AACA,gCAAgC;AAChC,4BAA4B;AAC5B;AACA;AACA;AACA,sDAAsD;AACtD;AACA;AACA,iBAAiB;AACjB;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,gCAAgC,8CAA8C;AAC9E;AACA;AACA;AACA,aAAa;AACb;AACA,aAAa;AACb;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,6CAA6C,6BAA6B;AAC1E;;AAEA;AACA;AACA;AACA;AACA,uBAAuB,kBAAkB;AACzC,2BAA2B,2BAA2B;AACtD,+BAA+B,mBAAmB;AAClD;AACA,qCAAqC,0CAA0C;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,uBAAuB,uBAAuB;AAC9C;AACA;AACA;AACA;;AAEA;AACA;;AAEA,oE;;;;;;;;AChmCA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,qBAAqB,mDAAmD;AACxE;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,mBAAmB,UAAU;AAC7B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB,YAAY;AAC7B;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA,uCAAuC,SAAS;AAChD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,eAAe,iBAAiB;AAChC;AACA;AACA;;AAEA;AACA;AACA,aAAa,iBAAiB;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,gDAAgD,EAAE;AAClD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA,wBAAwB,eAAe;AACvC;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,wBAAwB,QAAQ;AAChC;AACA,qBAAqB,eAAe;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB,YAAY;AAC7B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,qBAAqB,SAAS;AAC9B;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,qBAAqB,SAAS;AAC9B;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA,qBAAqB,SAAS;AAC9B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB,kBAAkB;AACnC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,mBAAmB,cAAc;AACjC;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,uDAAuD,OAAO;AAC9D;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA,uDAAuD,OAAO;AAC9D;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,kBAAkB;AAClB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,qBAAqB,QAAQ;AAC7B;AACA;AACA,GAAG;AACH;AACA,eAAe,SAAS;AACxB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA,mBAAmB,SAAS;AAC5B;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,eAAe,iBAAiB;AAChC;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,iBAAiB,YAAY;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,iBAAiB,gBAAgB;AACjC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB,gBAAgB;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,iBAAiB,YAAY;AAC7B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;;;;;;AC5vDA;;AAEA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;;AAEA;AACA;AACA,4CAA4C;;AAE5C;;;;;;;;ACpBA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,kCAAkC,SAAS;AAC3C;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;;AAEA,aAAa,OAAO;AACpB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,qBAAqB,SAAS;AAC9B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0CAA0C,UAAU;AACpD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;;;;;;ACnHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,QAAQ,WAAW;;AAEnB;AACA;AACA;AACA,QAAQ,WAAW;;AAEnB;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA,QAAQ,WAAW;;AAEnB;AACA;AACA,QAAQ,UAAU;;AAElB;AACA;;;;;;;ACnFA,iBAAiB;;AAEjB;AACA;AACA;;;;;;;ACJA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;AACA,CAAC;;AAED;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA,KAAK;;AAEL;AACA;AACA,KAAK;;AAEL;AACA;AACA,KAAK;;AAEL;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;;AAEd,mBAAmB,QAAQ;AAC3B;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA,cAAc;;AAEd,mBAAmB,QAAQ;AAC3B;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA;AACA,mBAAmB,YAAY;AAC/B;AACA;AACA;AACA;AACA;AACA,uBAAuB,QAAQ;AAC/B;AACA;AACA;;AAEA;AACA;AACA,8CAA8C,IAAI;AAClD;AACA;;AAEA;AACA;;AAEA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,oBAAoB,QAAQ;AAC5B;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,mBAAmB,YAAY;AAC/B;AACA;;AAEA;AACA;AACA;AACA,uBAAuB,QAAQ;AAC/B;AACA;AACA;;AAEA;AACA;AACA,8CAA8C,IAAI;AAClD;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA,KAAK;;AAEL;;AAEA;AACA;AACA;AACA,mBAAmB,OAAO;AAC1B;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA,mBAAmB,cAAc;AACjC;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA,KAAK;;;;AAIL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA,eAAe,OAAO;AACtB;AACA,gBAAgB,SAAS;AACzB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,eAAe,OAAO;AACtB;AACA,gBAAgB,SAAS;AACzB;AACA;AACA;AACA;;AAEA;AACA;;AAEA,oBAAoB,aAAa;AACjC;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA,gBAAgB,aAAa;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,mBAAmB,YAAY;AAC/B;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,eAAe,MAAM;AACrB,eAAe,OAAO;AACtB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,uBAAuB,QAAQ;AAC/B;AACA;AACA;;AAEA;AACA;AACA;AACA,8CAA8C,IAAI;AAClD;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,gBAAgB,SAAS;AACzB;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA,eAAe,OAAO;AACtB,eAAe,QAAQ;AACvB;AACA,gBAAgB,aAAa;AAC7B;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,eAAe,OAAO;AACtB,eAAe,QAAQ;AACvB;AACA,gBAAgB,aAAa;AAC7B;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,eAAe,YAAY;AAC3B;AACA,gBAAgB,qBAAqB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,oBAAoB,aAAa;AACjC;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA,gBAAgB,aAAa;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,mBAAmB,YAAY;AAC/B;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,gBAAgB,qBAAqB;AACrC;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,gBAAgB,YAAY;AAC5B,gBAAgB,YAAY;AAC5B;AACA,gBAAgB,YAAY;AAC5B;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,eAAe,YAAY;AAC3B,eAAe,QAAQ;AACvB;AACA,gBAAgB,aAAa;AAC7B;AACA;AACA;;AAEA;AACA;;AAEA;AACA,CAAC,G;;;;;;;;;ACplBD;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA,iBAAiB;AACjB;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT,mCAAmC,uBAAuB;AAC1D;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,qBAAqB;AACrB,iBAAiB;AACjB;AACA,aAAa;AACb;AACA;AACA;AACA,+LAA8I,KAAK,+FAA+F,0BAA0B;AAC5Q;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA,SAAS;AACT;AACA;;AAEA,sE;;;;;;;;;;;;;;;;;;;;;;;AC1OA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,6B", "file": "mam-upload-pure.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, {\n \t\t\t\tconfigurable: false,\n \t\t\t\tenumerable: true,\n \t\t\t\tget: getter\n \t\t\t});\n \t\t}\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/dist/\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 28);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap 94bf1f25e6b099b77354", "var commonUtil = {\r\n    asyncLoadedScripts : {},\r\n    asyncLoadedScriptsCallbackQueue: {},\r\n    getScriptDomFromUrl : function(url){\r\n        var dom;\r\n        if (/.+\\.js$/.test(url))\r\n        {\r\n            dom = document.createElement(\"SCRIPT\");\r\n            dom.setAttribute(\"type\", \"text/javascript\");\r\n            dom.setAttribute(\"src\", url);\r\n        }\r\n        else if(/.+\\.css$/.test(url))\r\n        {\r\n            dom = document.createElement('link');\r\n            dom.href = url;\r\n            dom.type = \"text/css\";\r\n            dom.rel=\"stylesheet\";\r\n        }\r\n        return dom;\r\n    },\r\n    /**\r\n     * 异步加载script或css\r\n     */\r\n    asyncLoadScript: function(url, callback) {\r\n        var $this = commonUtil;\r\n        if ($this.asyncLoadedScripts[url] != undefined)//已加载script标签\r\n        {\r\n            if (callback && typeof(callback) == \"function\") {\r\n                if ($this.asyncLoadedScripts[url] == 0)//未执行首个script标签的回调\r\n                {\r\n                    if (!$this.asyncLoadedScriptsCallbackQueue[url]) {\r\n                        $this.asyncLoadedScriptsCallbackQueue[url] = [];\r\n                    }\r\n                    $this.asyncLoadedScriptsCallbackQueue[url].push(callback);\r\n                }\r\n                else {\r\n                    callback.apply($this, []);\r\n                }\r\n            }\r\n            return;\r\n        }\r\n        $this.asyncLoadedScripts[url] = 0;\r\n        var scriptDom = $this.getScriptDomFromUrl(url);\r\n        if (scriptDom.readyState) {\r\n            scriptDom.onreadystatechange = function () {\r\n                if (scriptDom.readyState == \"loaded\" || scriptDom.readyState == \"complete\") {\r\n                    scriptDom.onreadystatechange = null;\r\n                    $this.asyncLoadedScripts[url] = 1;\r\n                    if (callback && typeof(callback) == \"function\") {\r\n                        callback.apply($this, []);\r\n                    }\r\n                    if ($this.asyncLoadedScriptsCallbackQueue[url]) {\r\n                        for (var i = 0, j = $this.asyncLoadedScriptsCallbackQueue[url].length; i < j; i++) {\r\n                            $this.asyncLoadedScriptsCallbackQueue[url][i].apply($this, []);\r\n                        }\r\n                        $this.asyncLoadedScriptsCallbackQueue[url] = undefined;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            scriptDom.onload = function () {\r\n                $this.asyncLoadedScripts[url] = 1;\r\n                if (callback && typeof(callback) == \"function\") {\r\n                    callback.apply($this, []);\r\n                }\r\n                if ($this.asyncLoadedScriptsCallbackQueue[url]) {\r\n                    for (var i = 0, j = $this.asyncLoadedScriptsCallbackQueue[url].length; i < j; i++) {\r\n                        $this.asyncLoadedScriptsCallbackQueue[url][i].apply($this, []);\r\n                    }\r\n                    $this.asyncLoadedScriptsCallbackQueue[url] = undefined;\r\n                }\r\n            }\r\n        }\r\n        document.getElementsByTagName('head')[0].appendChild(scriptDom);\r\n    },\r\n    getFileNameFromUrl : function(url){\r\n        return url.substring(url.lastIndexOf(\"/\") + 1, url.length);\r\n    },\r\n    isIncludeScript : function(name){\r\n        var js = /js$/i.test(name);\r\n        var es=document.getElementsByTagName(js?'script':'link');\r\n        for(var i=0;i<es.length;i++)\r\n            if(es[i][js?'src':'href'].indexOf(name)!=-1)return true;\r\n        return false;\r\n    },\r\n    loadScripts : function(scriptArr){\r\n        if (scriptArr instanceof Array)\r\n        {\r\n            var promises = [];\r\n            for (var i = 0; i < scriptArr.length; i++)\r\n            {\r\n                promises.push(new Promise(function(resolve, reject){\r\n                    if (commonUtil.isIncludeScript(commonUtil.getFileNameFromUrl(scriptArr[i])))\r\n                    {\r\n                        resolve();\r\n                    }\r\n                    else\r\n                    {\r\n                        commonUtil.asyncLoadScript(scriptArr[i], function(){\r\n                            resolve();\r\n                        });\r\n                    }\r\n                }));\r\n            }\r\n            return Promise.all(promises);\r\n        }\r\n        else\r\n        {\r\n            return new Promise(function(resolve, reject){\r\n                resolve();\r\n            });\r\n        }\r\n    },\r\n    getExtensions: function (s) {\r\n        if (!s) {\r\n            return '';\r\n        }\r\n        for (var i = s.length; i != 0; i--) {\r\n            if (s[i] == '.') {\r\n                return s.substring(i, s.length);\r\n            }\r\n        }\r\n        return '';\r\n    },\r\n    getExtension: function (s) {\r\n        var e = commonUtil.getExtensions(s);\r\n        if (e.length > 0) {\r\n            return e.substring(1, e.length);\r\n        }\r\n        return '';\r\n    },\r\n    getFileName: function (s) {\r\n        var e = s.length;\r\n        for (var i = s.length; i != 0; i--) {\r\n            if (s[i] == '.' && e == s.length) {\r\n                e = i;\r\n                continue;\r\n            }\r\n            if (s[i] == '\\\\') { // 此处需要考虑路径的多种分隔符，暂时就写一种\r\n                return s.substring(i + 1, e);\r\n            }\r\n        }\r\n        return s.substring(0, e);\r\n    },\r\n    getFullFileName: function (s) {\r\n        for (var i = s.length; i != 0; i--) {\r\n            if (s[i] == '\\\\') { // 此处需要考虑路径的多种分隔符，暂时就写一种\r\n                return s.substring(i + 1, s.length);\r\n            }\r\n        }\r\n        return s;\r\n    },\r\n    getTypeByExt: function (ext, config) {\r\n        ext = ext.toLowerCase();\r\n        if (ext.indexOf(\".\") !== 0)\r\n        {\r\n            ext = \".\" + ext;\r\n        }\r\n        var types = _.get(window, 'nxt.config.entityTypes', []);\r\n        if (config && types.length === 0)\r\n        {\r\n            types = _.get(config, 'entityTypes', []);\r\n        }\r\n        for (var i = 0; i < types.length; i++) {\r\n            if (types[i].extensions.indexOf(ext) != -1) {\r\n                return types[i];\r\n            }\r\n        }\r\n        return _.find(types, { 'code': 'other' });\r\n    },\r\n    formatSize: function (size, pointLength, units) {\r\n        var unit;\r\n        units = units || ['B', 'KB', 'MB', 'GB', 'TB'];\r\n        while ((unit = units.shift()) && size > 1024) {\r\n            size = size / 1024;\r\n        }\r\n        return (unit === 'B' ? size : size.toFixed(pointLength || 2)) + ' ' + unit;\r\n    },\r\n    prompt: function(msg){\r\n        if (mam && mam.prompt)\r\n        {\r\n            mam.prompt(msg);\r\n        }\r\n    },\r\n    msgOk: function(msg){\r\n        if (mam && mam.message && mam.message.ok)\r\n        {\r\n            mam.message.ok(msg);\r\n        }\r\n    }\r\n};\r\nexport default commonUtil;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/common/commonUtil.js\n// module id = 0\n// module chunks = 0 1", "var globalUtil = {};\r\nexport default globalUtil;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/common/globalUtil.js\n// module id = 1\n// module chunks = 0 1", "/**\r\n * Created by heju on 2017/5/31.\r\n */\r\nimport commonUtil from \"@/common/commonUtil\";\r\n\r\nvar httpUtil = {\r\n    getGetParamStr : function(param, restful){\r\n        var str = \"\";\r\n        if (param != undefined && !$.isEmptyObject(param))\r\n        {\r\n            for (var key in param)\r\n            {\r\n                if (!restful)\r\n                {\r\n                    str += key + \"=\" + param[key] + \"&\";\r\n                }\r\n                else\r\n                {\r\n                    str += \"/\" + param[key];\r\n                }\r\n            }\r\n            if (!restful)\r\n            {\r\n                str = \"?\" + str.substring(0, str.length - 1);\r\n            }\r\n        }\r\n        return str;\r\n    },\r\n    get : function(url, param){\r\n        var defer = $.Deferred();\r\n        var purl = url;\r\n        if (!param)\r\n        {\r\n            param = {};\r\n        }\r\n        purl = url + httpUtil.getGetParamStr(param);\r\n        $.ajax({\r\n            type : \"get\",\r\n            xhrFields: {\r\n                withCredentials: url.indexOf('token=') > -1 ? false : true//如果传了token，就不用传cookie了\r\n            },\r\n            url : purl\r\n        }).then(function(res){\r\n            if (!res.success)\r\n            {\r\n                console.error('response', res);\r\n                commonUtil.prompt(l('system.' + res.data.code, res.data.title));\r\n                defer.reject(res);\r\n            }\r\n            else\r\n            {\r\n                defer.resolve(res);\r\n            }\r\n        }, function(res){\r\n            defer.reject(res);\r\n        });\r\n        return defer;\r\n    },\r\n    post : function(url, param, opts){\r\n        var defer = $.Deferred();\r\n        if (!param)\r\n        {\r\n            param = {};\r\n        }\r\n        var postConfig = {\r\n            type : \"post\",\r\n            data :  param,\r\n            contentType: 'application/json',\r\n            processData: false,\r\n            xhrFields: {\r\n                withCredentials: url.indexOf('token=') > -1 ? false : true//如果传了token，就不用传cookie了\r\n            },\r\n            url : url\r\n        };\r\n        if (opts && opts.contentType !== undefined)\r\n        {\r\n            postConfig.contentType = opts.contentType;\r\n        }\r\n        if (opts && opts.processData !== undefined)\r\n        {\r\n            postConfig.processData = opts.processData;\r\n        }\r\n        if (postConfig.contentType === 'application/json')\r\n        {\r\n            postConfig.data = JSON.stringify(param);\r\n        }\r\n        $.ajax(postConfig).then(function(res){\r\n            if (!res.success)\r\n            {\r\n                console.error('response', res);\r\n                commonUtil.prompt(l('system.' + res.error.code, res.error.title));\r\n                defer.reject(res);\r\n            }\r\n            else\r\n            {\r\n                defer.resolve(res);\r\n            }\r\n        }, function(res){\r\n            defer.reject(res);\r\n        });\r\n        return defer;\r\n    }\r\n};\r\nexport default httpUtil;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/common/httpUtil.js\n// module id = 2\n// module chunks = 0 1", "import commonUtil from \"@/common/commonUtil\";\r\n\r\nvar globalUtil = require('@/common/globalUtil.js').default;\r\nvar httpUtil = require('@/common/httpUtil.js').default;\r\nvar sparkmd5 = require('@/libs/md5/spark-md5.js');\r\n\r\nvar web = function (mainOpts) {\r\n    var self = this,\r\n        $control, controlId,\r\n        tasks = [], //任务列表\r\n        uploadRetryTimer = {}, //文件上传重试计时器\r\n        s3;\r\n\r\n    function getConfig(){\r\n        if (window.nxt && window.nxt.config)\r\n        {\r\n            return window.nxt.config;\r\n        }\r\n        else if (mainOpts.configInst)\r\n        {\r\n            return mainOpts.configInst;\r\n        }\r\n    }\r\n\r\n    function windowClose() {\r\n        if (self.getFilesByStatus('progress', 'deleteing').length > 0) {\r\n            return l('upload.closePageTip', '当前页面存在未完成的上传任务，关闭页面会导致上传失败，你确定要关闭吗？');\r\n        }\r\n    }\r\n\r\n    function init() {\r\n        $(window).on('beforeunload', windowClose);\r\n    }\r\n\r\n    //获取文件最后修改时间\r\n    function getFileTime(file) {\r\n        //兼容不同浏览器\r\n        if (_.isUndefined(file.lastModifiedDate)) {\r\n            return file.lastModified;\r\n        }\r\n        return file.lastModifiedDate.getTime();\r\n    }\r\n\r\n    //获取默认关键帧\r\n    function getDefaultKeyframe(task) {\r\n        var type = _.find(getConfig().entityTypes, { code: task.entityType });\r\n        if (type == null) {\r\n            type = _.find(getConfig().entityTyps, { isOther: true });\r\n        }\r\n        if (type == null) {\r\n            return '';\r\n        }\r\n        return type.keyframe.replace('~', '');\r\n    }\r\n\r\n    //未授权提示\r\n    function unauthorizedTip() {\r\n        _.forEach(tasks, function (item) {\r\n            if (item.status !== 'added' && item.status !== 'success')\r\n                item.status = 'error';\r\n        });\r\n        $(window).off('beforeunload', windowClose);\r\n        commonUtil.prompt(l('upload.unauthorized', '你未登录或已超时，请重新登录。'));\r\n        location.href = getConfig().loginUrl + '?login_backUrl=' + location.href;\r\n    }\r\n\r\n    function initControl() {\r\n        if ($control == null) {\r\n            controlId = _.uniqueId('mam-web-transfer-');\r\n            $control = $('<input id=\"' + controlId + '\" type=\"file\" multiple style=\"display:none\"/>');\r\n            $('body').append($control);\r\n        }\r\n    }\r\n\r\n    function destroyControl() {\r\n        if ($control != null) {\r\n            $control.unbind('change');\r\n            $control.remove(); //因为IE10 重置文件框的值比较麻烦，如果不重置，无法重复选择。所以每次初始化来兼容IE10\r\n            $control = null;\r\n        }\r\n    }\r\n\r\n    //触发事件\r\n    function trigger(event, data) {\r\n        $(self).trigger(event, data);\r\n    }\r\n\r\n    //订阅事件\r\n    this.on = function (name, call) {\r\n        //利用jquery的事件，因为HTML5的自定义事件不兼容IE10。\r\n        $(self).on(name, call);\r\n    }\r\n    this.off = function (name, call) {\r\n        $(self).off(name, call);\r\n    }\r\n\r\n    //打开文件选取框\r\n    this.openFileSelector = function (callback, single) {\r\n        destroyControl();\r\n        initControl();\r\n        if (single === true) {\r\n            $control.removeAttr('multiple');\r\n        } else {\r\n            $control.attr('multiple', 'multiple');\r\n        }\r\n        $control.on('change', function () {\r\n            if ($control.val() === '') {\r\n                return;\r\n            }\r\n            var files = $control[0].files;\r\n            if (single === true) {\r\n                files = files[0];\r\n            }\r\n            var result = [];\r\n            _.forEach(files, function (item) {\r\n                var ext = '.' + commonUtil.getExtension(item.name);\r\n                result.push({\r\n                    entityType: commonUtil.getTypeByExt(ext, mainOpts.configInst).code,\r\n                    fileName: item.name,\r\n                    metadata: {\r\n                        name: commonUtil.getFileName(item.name),\r\n                        ext: ext\r\n                    },\r\n                    status: 'added',\r\n                    progress: 0,\r\n                    file: item\r\n                });\r\n            });\r\n            callback(result);\r\n            initControl();\r\n        });\r\n        $control.click(function (e) {\r\n            e.stopPropagation();\r\n        });\r\n        $control.trigger('click');\r\n    }\r\n\r\n    //创建任务\r\n    this.createTask = function (dto, params) {\r\n        var list = [];\r\n        switch (params.taskType) {\r\n            case 1: //普通类型\r\n                if (!_.isArray(dto)) {\r\n                    dto = [dto];\r\n                }\r\n                _.forEach(dto, function (task) {\r\n                    if (task.files && task.files.length > 0) {\r\n                        task.files = _.map(task.files, function (file) {\r\n                            return {\r\n                                file: file.file,\r\n                                fileName: file.file.name,\r\n                                fileSize: file.file.size,\r\n                                fileLastModifiedDate: getFileTime(file.file),\r\n                                type: file.type\r\n                            };\r\n                        });\r\n                        task.files.unshift({\r\n                            file: task.file,\r\n                            fileName: task.file.name,\r\n                            fileSize: task.file.size,\r\n                            fileLastModifiedDate: getFileTime(task.file),\r\n                            type: task.type !== undefined ? task.type : '0'\r\n                        });\r\n                    }\r\n                    else {\r\n                        task.files = [{\r\n                            file: task.file,\r\n                            fileName: task.file.name,\r\n                            fileSize: task.file.size,\r\n                            fileLastModifiedDate: getFileTime(task.file),\r\n                            type: task.type !== undefined ? task.type : '0'\r\n                        }];\r\n                    }\r\n                    delete task.file;\r\n                    list.push(task);\r\n                });\r\n                break;\r\n            case 2:\r\n            case 3:\r\n                _.forEach(dto.files, function (file) {\r\n                    file.fileName = file.file.name;\r\n                    file.fileSize = file.file.size;\r\n                    file.fileLastModifiedDate = getFileTime(file.file);\r\n                });\r\n                list.push(dto);\r\n                break;\r\n        }\r\n        _.forEach(list, function (task) {\r\n            task.taskType = params.taskType;\r\n            task.transferType = params.transferType;\r\n            task.targetFolder = params.targetFolder;\r\n            task.relationContentType = params.relationContentType;\r\n            task.relationContentId = params.relationContentId;\r\n            self.addTask(task);\r\n        });\r\n    }\r\n\r\n    //亚马逊OSS初始化\r\n    function initS3MultipartUpload(ossClientInfo, file) {\r\n        if (getConfig().storageType == 6)\r\n            return initOssMultipartUpload(ossClientInfo, file);\r\n        if (getConfig().storageType != 4)\r\n            return;\r\n        var params = {\r\n            Bucket: ossClientInfo.bucket,\r\n            Key: file.location.replace(/\\\\/g, '/')\r\n        };\r\n        // var credentials = {\r\n        //     accessKeyId: 'A7CW9Z9O340SDNIX1JEH',// ossClientInfo.accessKeyId,\r\n        //     secretAccessKey: 'We9Y5L83Qcnu0zcroDLIDnRMMoVC6G5L4PKgeDr4', // ossClientInfo.accessKeySecret\r\n        //     endpoint: '*************:9001',// ossClientInfo.serviceURL\r\n        //     sslEnabled: false,// !ossClientInfo.useHttp\r\n        //     s3ForcePathStyle: true,// ossClientInfo.forcePathStyle\r\n        //     // signatureVersion:'4'// ossClientInfo.version\r\n        // };\r\n        var credentials = {\r\n            accessKeyId: ossClientInfo.keyId,\r\n            secretAccessKey: ossClientInfo.secretKey,\r\n            endpoint: ossClientInfo.serviceURL,\r\n            sslEnabled: !ossClientInfo.useHttp,\r\n            s3ForcePathStyle: true,\r\n            signatureVersion: ossClientInfo.version\r\n        }; // 秘钥形式的登录上传\r\n        AWS.config.update(credentials);\r\n        if (ossClientInfo && ossClientInfo.region)\r\n            AWS.config.region = ossClientInfo.region; // 设置0000000区域\r\n        // if (!s3)\r\n        s3 = new AWS.S3(\r\n            { apiVersion: '2006-03-01' }\r\n        );\r\n        if (file.fileSize <= (5 * 1024 * 1024))\r\n            return;\r\n        var createUploadPromise = s3.createMultipartUpload(params).promise();\r\n        file.createUploadPromise = createUploadPromise;\r\n        var defer = $.Deferred();\r\n        file.createUploadPromise.then(function (initData) {\r\n            file.uploadId = initData.UploadId;\r\n            /* initData = {\r\n                Bucket: \"examplebucket\", \r\n                Key: \"largeobject\", \r\n                UploadId: \"ibZBv_75gd9r8lH_gqXatLdxMVpAlj6ZQjEs.OwyF3953YdwbcQnMA2BLGn8Lx12fQNICtMw5KyteFeHw.Sjng--\"\r\n            } */\r\n            defer.resolve();\r\n        }).catch(function (err) {\r\n            defer.reject();\r\n            console.info(err);// an error occurred\r\n        });\r\n        return defer.promise();\r\n    }\r\n\r\n    function getS3Client(ossClientInfo) {\r\n        var credentials = {\r\n            accessKeyId: ossClientInfo.keyId,\r\n            secretAccessKey: ossClientInfo.secretKey,\r\n            endpoint: ossClientInfo.serviceURL,\r\n            sslEnabled: !ossClientInfo.useHttp,\r\n            s3ForcePathStyle: true,\r\n            signatureVersion: 'v' + ossClientInfo.version,\r\n            apiVersion: '2006-03-01'\r\n        };// 秘钥形式的登录上传\r\n        if (ossClientInfo.region)\r\n            AWS.config.region = ossClientInfo.region; // 设置0000000区域\r\n        s3 = new OOS.S3(credentials);\r\n    }\r\n\r\n    //天翼云OSS初始化\r\n    function initOssMultipartUpload(ossClientInfo, file) {\r\n        if (getConfig().storageType != 6)\r\n            return;\r\n        // ossClientInfo = {\r\n        //     bucket: '224201137256489665z',\r\n        //     accessKeyId: '8ed2a9104850592cb3db',\r\n        //     accessKeySecret: '71b66f5aaa6732350274b8e7de3c3438d1287ebf',\r\n        //     serviceURL: 'oos-js.ctyunapi.cn',\r\n        //     useHttp: true,\r\n        //     forcePathStyle: true,\r\n        //     version: 'v2'\r\n        // };\r\n        getS3Client(ossClientInfo);\r\n        if (file.fileSize <= (5 * 1024 * 1024))\r\n            return;\r\n        var params = {\r\n            Bucket: ossClientInfo.bucket,\r\n            Key: file.location.replace(/\\\\/g, '/')\r\n        };\r\n        var createUploadPromise = s3.createMultipartUpload(params).promise();\r\n        file.createUploadPromise = createUploadPromise;\r\n        var defer = $.Deferred();\r\n        file.createUploadPromise.then(function (initData) {\r\n            file.uploadId = initData.UploadId;\r\n            /* initData = {\r\n                Bucket: \"examplebucket\", \r\n                Key: \"largeobject\", \r\n                UploadId: \"ibZBv_75gd9r8lH_gqXatLdxMVpAlj6ZQjEs.OwyF3953YdwbcQnMA2BLGn8Lx12fQNICtMw5KyteFeHw.Sjng--\"\r\n            } */\r\n            defer.resolve();\r\n        }).catch(function (err) {\r\n            defer.reject();\r\n            console.info(err);// an error occurred\r\n        });\r\n        return defer.promise();\r\n    }\r\n\r\n    //初始化任务\r\n    function initTask(task) {\r\n        trigger('task-init-before', task);\r\n        var s3Promise = [];\r\n        var url = getConfig().server + '/upload/multipart/init';\r\n        if (mainOpts.loginToken) {\r\n            url += \"?token=\" + mainOpts.loginToken;\r\n        }\r\n        httpUtil.post(url, task).then(function (res) {\r\n            res = res.data;\r\n            for (var i = 0; i < res.files.length; i++) {\r\n                var file = task.files[i].file;\r\n                task.files[i] = res.files[i];\r\n                task.files[i].file = file;\r\n                task.files[i].status = 'prepared';\r\n                task.files[i].fileSizeString = commonUtil.formatSize(res.files[i].fileSize);\r\n                task.files[i].progress = 0;\r\n                task.sizeTotal += res.files[i].fileSize;\r\n                task.chunkTotal += res.files[i].chunkTotal;\r\n                var createPromise = initS3MultipartUpload(res.ossClientInfo, task.files[i]);\r\n                if (createPromise)\r\n                    s3Promise.push(createPromise);\r\n            }\r\n            task.taskId = res.taskId;\r\n            task.entityType = res.entityType;\r\n            task.fileTotal = res.fileTotal;\r\n            task.targetFolder = res.targetFolder;\r\n            task.targetFolderName = res.targetFolderName;\r\n            task.targetType = res.targetType;\r\n            task.keyframe = res.keyframe;\r\n            task.status = 'prepared';\r\n            task.inited = true;\r\n            task.sizeTotalString = commonUtil.formatSize(task.sizeTotal);\r\n            task.isJsUpload = res.isJsUpload;\r\n            task.ossClientInfo = res.ossClientInfo;\r\n            trigger('task-init-success', task);\r\n            if ((getConfig().storageType == 4 || getConfig().storageType == 6) && task.isJsUpload && s3Promise.length > 0) {\r\n                // q.all(s3Promise).then(function () {\r\n                //     self.prepareUpload();\r\n                // });\r\n                $.when.apply($, s3Promise).done(function () {\r\n                    self.prepareUpload();\r\n                });\r\n            } else\r\n                self.prepareUpload();\r\n        }, function (res) {\r\n            trigger('task-init-error', [task, res]);\r\n            if (res.status === 401) {\r\n                unauthorizedTip(tasks);\r\n                return;\r\n            } else {\r\n                task.status = 'error';\r\n                _.forEach(task.files, function (file) { file.status = 'error'; });\r\n                commonUtil.prompt(l('upload.', '上传失败：${text}', { text: res.data.desc || res.data.title }));\r\n                self.prepareUpload();\r\n            }\r\n        });\r\n    }\r\n\r\n    //添加任务\r\n    this.addTask = function (task) {\r\n        task.status = 'init';\r\n        task.progress = task.sizeTotal = task.chunkFinished = task.chunkTotal = 0;\r\n        task.keyframe = getDefaultKeyframe(task);\r\n        tasks.push(task);\r\n        initTask(task);\r\n    }\r\n\r\n    //准备上传\r\n    this.prepareUpload = function () {\r\n        var progress = self.getFilesByStatus('progress');\r\n        var len = getConfig().webUploadThreads - progress.length;\r\n        if (len <= 0)\r\n            return;\r\n        var prepared = self.getFilesByStatus('prepared');\r\n        if (len > prepared.length)\r\n            len = prepared.length;\r\n        if (len === 0) //判断是否存在需要上传的任务\r\n            return;\r\n        for (var i = 0; i < len; i++) {\r\n            prepared[i].task.status = prepared[i].file.status = 'progress';\r\n            self.upload(prepared[i]);\r\n        }\r\n    }\r\n\r\n    //计算分片数据的MD5\r\n    function calcChunkMd5(item, data, callback) {\r\n        if (getConfig().webUploadMd5Enable) {\r\n            if (item.file.fileReader == null)\r\n                item.file.fileReader = new FileReader();\r\n            item.file.fileReader.onload = function (e) {\r\n                var spark = new sparkmd5();\r\n                spark.appendBinary(e.target.result);\r\n                var md5 = spark.end();\r\n                spark.destroy();\r\n                callback(md5);\r\n            };\r\n            item.file.fileReader.onerror = function (e) { uploadErrorRetry(item); };\r\n            item.file.fileReader.readAsBinaryString(data);\r\n        } else {\r\n            callback('');\r\n        }\r\n    }\r\n\r\n    //计算剩余时间\r\n    function calcSurplusTime(task, file) {\r\n        if (file.startTime != null) {\r\n            if (!_.isNumber(file.surplusTime))\r\n                file.surplusTime = 0;\r\n            file.surplusTime = (new Date() - file.startTime) * (file.chunkTotal - file.chunkIndex);\r\n\r\n            if (!_.isNumber(task.surplusTime))\r\n                task.surplusTime = 0;\r\n            task.surplusTime = (new Date() - file.startTime) * (task.chunkTotal - task.chunkFinished);\r\n        }\r\n        file.startTime = new Date();\r\n    }\r\n\r\n    //上传失败重试\r\n    function uploadErrorRetry(item) {\r\n        if (!item.file.hasOwnProperty('errorCount'))\r\n            item.file.errorCount = 0;\r\n        if (item.file.errorCount < 4) {\r\n            item.file.errorCount++;\r\n            uploadRetryTimer[item.file.fileId] = setTimeout(function () { self.upload(item); }, 3000);\r\n        } else {\r\n            item.task.status = item.file.status = 'error';\r\n            trigger('task-upload-error', item.task);\r\n            self.prepareUpload();\r\n        }\r\n    }\r\n\r\n    this.uploadByBackend = function (item) {\r\n        var file = item.file;\r\n        var task = item.task;\r\n        if (file.chunkIndex < file.chunkTotal) {\r\n            calcSurplusTime(task, file);\r\n            var start = file.chunkIndex * file.chunkSize,\r\n                end = Math.min(file.fileSize, start + file.chunkSize),\r\n                form = new FormData(),\r\n                data = file.file.slice(start, end);\r\n            if (data == null || data.size == 0) {\r\n                item.task.status = item.file.status = 'error';\r\n                trigger('task-upload-error', item.task);\r\n                item.file.file = null;\r\n                self.prepareUpload();\r\n                return;\r\n            }\r\n            calcChunkMd5(item, data, function (md5) {\r\n                form.append('fileData', data);\r\n                form.append('taskId', file.taskId);\r\n                form.append('fileId', file.fileId);\r\n                form.append('chunkIndex', file.chunkIndex);\r\n                form.append('md5', md5);\r\n                //form.append('uploadId', file.tag);\r\n\r\n                //下面还写一次是因为有2种情况，必须在这里也要删除才合理，1,初始化成功准备上传，2，上传一片后上传下一片之前\r\n                //之所有放在构造formData之后，是因为构造formData是需要一点点时间的。避免时间差带来的偶现bug\r\n                if (task.status === 'deleteing') {\r\n                    self.clearTask(task);\r\n                    return;\r\n                }\r\n                var url = getConfig().server + '/upload/multipart';\r\n                if (mainOpts.loginToken) {\r\n                    url += \"?token=\" + mainOpts.loginToken;\r\n                }\r\n                httpUtil.post(url, form, {\r\n                    contentType: false,\r\n                    processData: false\r\n                }).then(function (res) {\r\n                    if (task.status === 'deleteing') {\r\n                        self.clearTask(task);\r\n                        return;\r\n                    }\r\n                    res = res.data;\r\n                    file = _.find(task.files, { fileId: res.fileId });\r\n                    file.chunkIndex = res.chunkIndex;\r\n                    if (res.taskStatus === 3) { // 3：任务完成\r\n                        file.progress = task.progress = 100;\r\n                        file.surplusTime = task.surplusTime = null;\r\n                        delete file.fileReader;\r\n                        file.status = task.status = 'success';\r\n                        trigger('task-upload-success', task);\r\n                        self.prepareUpload();\r\n                    } else {\r\n                        task.progress = calcTaskProgress(task);\r\n                        file.progress = calcFileProgress(file);\r\n                        file.status = task.status = 'progress';\r\n                        trigger('task-upload-progress', task);\r\n                        file.errorCount = 0;\r\n                        self.upload(item);\r\n                    }\r\n                }, function (res) {\r\n                    if (res.status === 401) {\r\n                        unauthorizedTip();\r\n                        return;\r\n                    }\r\n                    uploadErrorRetry(item);\r\n                });\r\n            });\r\n        } else {\r\n            if (file.hasOwnProperty('errorCount')) {\r\n                delete uploadRetryTimer[file.fileId];\r\n                delete file.errorCount;\r\n            }\r\n            delete file.fileReader;\r\n            file.surplusTime = null;\r\n            file.progress = 100;\r\n            file.status = 'success';\r\n            task.progress = calcTaskProgress(task);\r\n            trigger('task-upload-success', task);\r\n            self.prepareUpload();\r\n        }\r\n    }\r\n\r\n    var callUpload = function (file) {\r\n        var form = new FormData();\r\n        form.append('taskId', file.taskId);\r\n        form.append('fileId', file.fileId);\r\n        form.append('chunkIndex', file.chunkIndex);\r\n        form.append('partInfo', JSON.stringify(file.partInfo));\r\n        form.append('uploadId', file.uploadId);\r\n        form.append('checkPoint', file.checkPoint);\r\n        var url = getConfig().server + '/upload/multipart';\r\n        if (mainOpts.loginToken) {\r\n            url += \"?token=\" + mainOpts.loginToken;\r\n        }\r\n        return httpUtil.post(url, form, {\r\n            contentType: false,\r\n            processData: false\r\n        })\r\n    }\r\n\r\n    this.uploadByWeb = function (item) {\r\n        var file = item.file;\r\n        var task = item.task;\r\n\r\n        var clientOpt = {\r\n            region: task.ossClientInfo.region,\r\n            accessKeyId: task.ossClientInfo.accessKeyId,\r\n            accessKeySecret: task.ossClientInfo.accessKeySecret,\r\n            bucket: task.ossClientInfo.bucketName\r\n        };\r\n        if (task.ossClientInfo.endpoint) {\r\n            clientOpt.endpoint = task.ossClientInfo.endpoint;\r\n        }\r\n        var client = new this.OSS.Wrapper(clientOpt);\r\n        // var client = new OSS.Wrapper({\r\n        //     region: 'oss-cn-beijing',\r\n        //     accessKeyId: 'LTAIJNTTrKH8Aoat',\r\n        //     accessKeySecret: 'HjYCH0K4bYXZQ3I0JIoJ8Rbp2zYK0J',\r\n        //     bucket: 'bucket-pan-test'\r\n        // });\r\n\r\n        if (file.fileSize <= 102400)// 小文件不能分片上传\r\n        {\r\n            item.fileReader = new FileReader();\r\n            item.fileReader.onload = function (e) {\r\n                client.put(file.location, new Buffer(this.result)).then(function (res) {\r\n                    callUpload(file).then(function (res) {\r\n                        file.progress = task.progress = 100;\r\n                        file.surplusTime = task.surplusTime = null;\r\n                        delete file.fileReader;\r\n                        file.status = task.status = 'success';\r\n                        trigger('task-upload-success', task);\r\n                        self.prepareUpload();\r\n                    });\r\n                }, function (res) {\r\n                    console.error(res);\r\n                });\r\n            };\r\n            item.fileReader.readAsArrayBuffer(file.file);\r\n        }\r\n        else {\r\n            var opts = {\r\n                partSize: file.chunkSize,\r\n                progress: function (p, checkpoint, res) {\r\n                    return function (done) {\r\n                        if (task.status === 'deleteing') {\r\n                            self.clearTask(task);\r\n                        }\r\n                        else {\r\n                            file.partInfo = checkpoint.doneParts[checkpoint.doneParts.length - 1];\r\n                            file.uploadId = checkpoint.uploadId;\r\n                            file.checkPoint = JSON.stringify(checkpoint, function (key, value) {\r\n                                if (key === 'file') {\r\n                                    return undefined;\r\n                                }\r\n                                return value;\r\n                            });\r\n                            callUpload(file).then(function (res) {\r\n                                if (task.status === 'deleteing') {\r\n                                    self.clearTask(task);\r\n                                    return;\r\n                                }\r\n                                res = res.data;\r\n                                file.chunkIndex = res.chunkIndex;\r\n                                if (res.taskStatus === 3) { // 3：任务完成\r\n                                    file.progress = task.progress = 100;\r\n                                    file.surplusTime = task.surplusTime = null;\r\n                                    delete file.fileReader;\r\n                                    file.status = task.status = 'success';\r\n                                    trigger('task-upload-success', task);\r\n                                    self.prepareUpload();\r\n                                    done();\r\n                                } else {\r\n                                    task.progress = calcTaskProgress(task);\r\n                                    file.progress = calcFileProgress(file);\r\n                                    file.status = task.status = 'progress';\r\n                                    trigger('task-upload-progress', task);\r\n                                    file.errorCount = 0;\r\n                                    done();\r\n                                }\r\n                            }, function (res) {\r\n                                if (res.status === 401) {\r\n                                    unauthorizedTip();\r\n                                    return;\r\n                                }\r\n                                uploadErrorRetry(item);\r\n                            });\r\n                        }\r\n                    };\r\n                }\r\n            };\r\n            if (file.checkPoint)//断点续传\r\n            {\r\n                opts.checkpoint = JSON.parse(file.checkPoint);\r\n                opts.checkpoint.file = file.file;\r\n            }\r\n            client.multipartUpload(file.location, file.file, opts).then(function (res) {\r\n                console.log('upload success', res);\r\n            }, function (res) {\r\n                console.log(res);\r\n            });\r\n        }\r\n    };\r\n\r\n    this.uploadByS3 = function (item) {\r\n        var file = item.file;\r\n        var task = item.task;\r\n\r\n        if (file.chunkIndex < file.chunkTotal) {\r\n            var start = file.chunkIndex * file.chunkSize,\r\n                end = Math.min(file.fileSize, start + file.chunkSize),\r\n                form = new FormData(),\r\n                data = file.file.slice(start, end);\r\n            if (data == null || data.size == 0) {\r\n                item.task.status = item.file.status = 'error';\r\n                trigger('task-upload-error', item.task);\r\n                item.file.file = null;\r\n                self.prepareUpload();\r\n                return;\r\n            }\r\n            var uploadAfter = function (task, file) {\r\n                form.append('taskId', file.taskId);\r\n                form.append('fileId', file.fileId);\r\n                form.append('chunkIndex', file.chunkIndex);\r\n                form.append('partInfo', file.partInfo);\r\n                form.append('uploadId', file.uploadId);\r\n\r\n                // 下面还写一次是因为有2种情况，必须在这里也要删除才合理，1,初始化成功准备上传，2，上传一片后上传下一片之前\r\n                // 之所有放在构造formData之后，是因为构造formData是需要一点点时间的。避免时间差带来的偶现bug\r\n                if (task.status === 'deleteing') {\r\n                    self.clearTask(task);\r\n                    return;\r\n                }\r\n\r\n                var url = getConfig().server + '/upload/multipart';\r\n                if (mainOpts.loginToken) {\r\n                    url += \"?token=\" + mainOpts.loginToken;\r\n                }\r\n                httpUtil.post(url, form, {\r\n                    contentType: false,\r\n                    processData: false\r\n                }).then(function (res) {\r\n                    if (task.status === 'deleteing') {\r\n                        self.clearTask(task);\r\n                        return;\r\n                    }\r\n                    res = res.data;\r\n                    file = _.find(task.files, { fileId: res.fileId });\r\n                    file.chunkIndex = res.chunkIndex;\r\n                    if (res.taskStatus === 3) { // 3：任务完成\r\n                        file.progress = task.progress = 100;\r\n                        file.surplusTime = task.surplusTime = null;\r\n                        delete file.fileReader;\r\n                        file.status = task.status = 'success';\r\n                        trigger('task-upload-success', task);\r\n                        self.prepareUpload();\r\n                    } else {\r\n                        task.progress = calcTaskProgress(task);\r\n                        file.progress = calcFileProgress(file);\r\n                        file.status = task.status = 'progress';\r\n                        trigger('task-upload-progress', task);\r\n                        file.errorCount = 0;\r\n                        self.upload(item);\r\n                    }\r\n                }, function (res) {\r\n                    if (res.status === 401) {\r\n                        unauthorizedTip();\r\n                        return;\r\n                    }\r\n                    uploadErrorRetry(item);\r\n                });\r\n            };\r\n\r\n            if (file.fileSize <= (5 * 1024 * 1024))// 小于5M文件不用分片上传\r\n            {\r\n                s3.putObject({\r\n                    Body: file.file,\r\n                    Bucket: task.ossClientInfo.bucket,\r\n                    Key: file.location.replace(/\\\\/g, '/')\r\n                }, function (err, successData) {\r\n                    if (err) {\r\n                        console.info(err, err.stack); // an error occurred\r\n                        uploadErrorRetry(item);\r\n                    }\r\n                    else {\r\n                        if (successData.ETag)\r\n                            file.partInfo = successData.ETag;\r\n                        uploadAfter(task, file);\r\n                        console.info(successData); // successful response\r\n                    }\r\n                    /*\r\n                    data = {\r\n                        ETag: \"\\\"6805f2cfc46c0f04559748bb039d69ae\\\"\",\r\n                        VersionId: \"Kirh.unyZwjQ69YxcQLA8z4F5j3kJJKr\"\r\n                    }\r\n                   */\r\n                });\r\n            } else {\r\n                var params = {\r\n                    Body: data,\r\n                    Bucket: task.ossClientInfo.bucket,\r\n                    Key: file.location.replace(/\\\\/g, '/'),\r\n                    PartNumber: file.chunkIndex + 1,\r\n                    UploadId: file.uploadId\r\n                };\r\n                s3.uploadPart(params, function (err, successData) {\r\n                    if (err) {\r\n                        console.info(err, err.stack); // an error occurred\r\n                        uploadErrorRetry(item);\r\n                    }\r\n                    else {\r\n                        if (successData.ETag)\r\n                            file.partInfo = successData.ETag.replace(/\"/g, '');\r\n                        uploadAfter(task, file);\r\n                        console.info(successData); // successful response\r\n                        /*\r\n                        successData = {\r\n                            ETag: \"\\\"d8c2eafd90c266e19ab9dcacc479f8af\\\"\"\r\n                       }\r\n                       */\r\n                    }\r\n                });\r\n            }\r\n        } else {\r\n            if (file.hasOwnProperty('errorCount')) {\r\n                delete uploadRetryTimer[file.fileId];\r\n                delete file.errorCount;\r\n            }\r\n            delete file.fileReader;\r\n            file.surplusTime = null;\r\n            file.progress = 100;\r\n            file.status = 'success';\r\n            task.progress = calcTaskProgress(task);\r\n            trigger('task-upload-success', task);\r\n            self.prepareUpload();\r\n        }\r\n    }\r\n\r\n    this.uploadByOos = function (item) {\r\n        var file = item.file;\r\n        var task = item.task;\r\n\r\n        if (file.chunkIndex < file.chunkTotal) {\r\n            var start = file.chunkIndex * file.chunkSize,\r\n                end = Math.min(file.fileSize, start + file.chunkSize),\r\n                form = new FormData(),\r\n                data = file.file.slice(start, end);\r\n            if (data == null || data.size == 0) {\r\n                item.task.status = item.file.status = 'error';\r\n                trigger('task-upload-error', item.task);\r\n                item.file.file = null;\r\n                self.prepareUpload();\r\n                return;\r\n            }\r\n            var uploadAfter = function (task, file) {\r\n                form.append('taskId', file.taskId);\r\n                form.append('fileId', file.fileId);\r\n                form.append('chunkIndex', file.chunkIndex);\r\n                form.append('partInfo', file.partInfo);\r\n                form.append('uploadId', file.uploadId);\r\n\r\n                // 下面还写一次是因为有2种情况，必须在这里也要删除才合理，1,初始化成功准备上传，2，上传一片后上传下一片之前\r\n                // 之所有放在构造formData之后，是因为构造formData是需要一点点时间的。避免时间差带来的偶现bug\r\n                if (task.status === 'deleteing') {\r\n                    self.clearTask(task);\r\n                    return;\r\n                }\r\n\r\n                var url = getConfig().server + '/upload/multipart';\r\n                if (mainOpts.loginToken) {\r\n                    url += \"?token=\" + mainOpts.loginToken;\r\n                }\r\n                httpUtil.post(url, form, {\r\n                    contentType: false,\r\n                    processData: false\r\n                }).then(function (res) {\r\n                    if (task.status === 'deleteing') {\r\n                        self.clearTask(task);\r\n                        return;\r\n                    }\r\n                    res = res.data;\r\n                    file = _.find(task.files, { fileId: res.fileId });\r\n                    file.chunkIndex = res.chunkIndex;\r\n                    if (res.taskStatus === 3) { // 3：任务完成\r\n                        file.progress = task.progress = 100;\r\n                        file.surplusTime = task.surplusTime = null;\r\n                        delete file.fileReader;\r\n                        file.status = task.status = 'success';\r\n                        trigger('task-upload-success', task);\r\n                        self.prepareUpload();\r\n                    } else {\r\n                        task.progress = calcTaskProgress(task);\r\n                        file.progress = calcFileProgress(file);\r\n                        file.status = task.status = 'progress';\r\n                        trigger('task-upload-progress', task);\r\n                        file.errorCount = 0;\r\n                        self.upload(item);\r\n                    }\r\n                }, function (res) {\r\n                    if (res.status === 401) {\r\n                        unauthorizedTip();\r\n                        return;\r\n                    }\r\n                    uploadErrorRetry(item);\r\n                });\r\n            };\r\n            if (!s3) {\r\n                getS3Client(task.ossClientInfo);\r\n            }\r\n            if (file.fileSize <= (5 * 1024 * 1024))// 小于5M文件不用分片上传\r\n            {\r\n                s3.putObject({\r\n                    Body: file.file,\r\n                    Bucket: task.ossClientInfo.bucket,\r\n                    Key: file.location.replace(/\\\\/g, '/')\r\n                }, function (err, successData) {\r\n                    if (err) {\r\n                        console.info(err, err.stack); // an error occurred\r\n                        uploadErrorRetry(item);\r\n                    }\r\n                    else {\r\n                        if (successData.ETag)\r\n                            file.partInfo = successData.ETag;\r\n                        uploadAfter(task, file);\r\n                        console.info(successData); // successful response\r\n                    }\r\n                    /*\r\n                    data = {\r\n                        ETag: \"\\\"6805f2cfc46c0f04559748bb039d69ae\\\"\",\r\n                        VersionId: \"Kirh.unyZwjQ69YxcQLA8z4F5j3kJJKr\"\r\n                    }\r\n                   */\r\n                });\r\n            } else {\r\n                var params = {\r\n                    Body: data,\r\n                    Bucket: task.ossClientInfo.bucket,\r\n                    Key: file.location.replace(/\\\\/g, '/'),\r\n                    PartNumber: file.chunkIndex + 1,\r\n                    UploadId: file.uploadId\r\n                };\r\n                s3.uploadPart(params, function (err, successData) {\r\n                    if (err) {\r\n                        console.info(err, err.stack); // an error occurred\r\n                        uploadErrorRetry(item);\r\n                    }\r\n                    else {\r\n                        // if (params.PartNumber == file.chunkTotal) {\r\n                        //     s3.completeMultipartUpload({ Bucket: params.Bucket, Key: params.Key, UploadId: params.UploadId }, function (err, data) {\r\n                        //         if (err) {\r\n                        //             console.info(err, err.stack); // an error occurred\r\n                        //             uploadErrorRetry(item);\r\n                        //         } else {\r\n                        //             console.log('upload complete', data);\r\n                        //             uploadAfter(task, file);\r\n                        //         }\r\n                        //     });\r\n                        // } else {\r\n                            if (successData.ETag)\r\n                                file.partInfo = successData.ETag.replace(/\"/g, '');\r\n                            uploadAfter(task, file);\r\n                            console.info(successData); // successful response\r\n                        // }\r\n                    }\r\n                });\r\n            }\r\n        } else {\r\n            if (file.hasOwnProperty('errorCount')) {\r\n                delete uploadRetryTimer[file.fileId];\r\n                delete file.errorCount;\r\n            }\r\n            delete file.fileReader;\r\n            file.surplusTime = null;\r\n            file.progress = 100;\r\n            file.status = 'success';\r\n            task.progress = calcTaskProgress(task);\r\n            trigger('task-upload-success', task);\r\n            self.prepareUpload();\r\n        }\r\n    }\r\n    //上传\r\n    this.upload = function (item) {\r\n        if (item.task.isJsUpload) {\r\n            if (getConfig().storageType == 4)\r\n                this.uploadByS3(item);\r\n            else if (getConfig().storageType == 6)\r\n                this.uploadByOos(item);\r\n            else\r\n                this.uploadByWeb(item);\r\n        }\r\n        else {\r\n            this.uploadByBackend(item);\r\n        }\r\n    }\r\n\r\n    //继续上传\r\n    this.continueUpload = function (task, file, errCallback) {\r\n        if (file.file == null)\r\n            self.openFileSelector(function (result) {\r\n                var md5 = self.calcFileMd5(result[0].file);\r\n                if (file.fileMd5 !== md5) {\r\n                    commonUtil.prompt(l('upload.fileDiffer', '选择的文件不一致，请重新上传'));\r\n                    if (errCallback)\r\n                    {\r\n                        errCallback.apply(window, [l('upload.fileDiffer', '选择的文件不一致，请重新上传')]);\r\n                    }\r\n                    return;\r\n                }\r\n                file.file = result[0].file;\r\n                task.status = file.status = 'prepared';\r\n                self.prepareUpload();\r\n            }, false);\r\n        else {\r\n            if (task.inited) {\r\n                task.status = file.status = 'prepared';\r\n                self.prepareUpload();\r\n            } else {\r\n                initTask(task);\r\n            }\r\n        }\r\n    }\r\n\r\n    //获取未完成需要断点续传的任务\r\n    this.getUnfinishedTask = function (relationId, relationContentType, targetType) {\r\n        var deferred = $.Deferred();\r\n        if (relationId == null)\r\n            relationId = '';\r\n        if (!_.isNumber(relationContentType))\r\n            relationContentType = 0;\r\n        if (!_.isNumber(relationContentType))\r\n            relationContentType = 1;\r\n        var url = getConfig().server + '/upload/get-unfinished-task?relationId=' + relationId + '&relationContentType=' + relationContentType + '&targetType=' + targetType;\r\n        if (mainOpts.loginToken) {\r\n            url += '&token=' + mainOpts.loginToken;\r\n        }\r\n        httpUtil.get(url).then(function (res) {\r\n            var result = [];\r\n            _.forEach(res.data, function (task) {\r\n                task.status = 'error';\r\n                task.inited = true;\r\n                task.sizeTotal = task.chunkFinished = task.chunkTotal = 0;\r\n                _.forEach(task.files, function (file) {\r\n                    file.fileSizeString = commonUtil.formatSize(file.fileSize);\r\n                    file.progress = calcFileProgress(file);\r\n                    file.status = file.progress === 100 ? 'success' : 'error';\r\n                    task.sizeTotal += file.fileSize;\r\n                    task.chunkTotal += file.chunkTotal;\r\n                });\r\n                task.progress = calcTaskProgress(task);\r\n                task.sizeTotalString = commonUtil.formatSize(task.sizeTotal);\r\n                tasks.push(task);\r\n                result.push(task);\r\n            });\r\n            deferred.resolve(result);\r\n        }, function (res) {\r\n            deferred.reject(res);\r\n        });\r\n        return deferred;\r\n    }\r\n\r\n    //是否能删除任务\r\n    this.canDeleteTask = function (task) {\r\n        if (task == null)\r\n            return false;\r\n        if (task.status == 'init')\r\n            return false;\r\n        if (task.status == 'deleteing')\r\n            return false;\r\n        if (task.status == 'progress' && task.chunkFinished == (task.chunkTotal - 1))\r\n            return false;\r\n        return true;\r\n    }\r\n\r\n    this.removeTask = function (task){\r\n        _.remove(tasks, function(ta){\r\n            return ta.taskId === task.taskId;\r\n        });\r\n    }\r\n\r\n    //删除任务\r\n    this.deleteTask = function (task) {\r\n        if (!this.canDeleteTask(task))\r\n            return;\r\n        if (task.inited === true) {\r\n            _.forEach(task.files, function (file) {\r\n                if (uploadRetryTimer[file.fileId] != null) {\r\n                    timeout.cancel(uploadRetryTimer[file.fileId]);\r\n                    delete uploadRetryTimer[file.fileId];\r\n                }\r\n            });\r\n            switch (task.status) {\r\n                case 'progress':\r\n                    task.status = 'deleteing';\r\n                    return;\r\n                case 'prepared':\r\n                case 'error':\r\n                    self.clearTask(task);\r\n                    return;\r\n                default:\r\n                    self.removeTask(task);\r\n                    trigger('task-delete-success', task);\r\n            }\r\n        } else {\r\n            self.removeTask(task);\r\n            trigger('task-delete-success', task);\r\n        }\r\n    }\r\n\r\n    //清理任务\r\n    this.clearTask = function (task) {\r\n        var fileAndTag = {};\r\n        if (task.files && task.files.length > 0) {\r\n            _.forEach(task.files, function (file) {\r\n                fileAndTag[file.fileId] = file.uploadId;\r\n            });\r\n            var url = getConfig().server + '/upload/delete-task';\r\n            if (mainOpts.loginToken) {\r\n                url += \"?token=\" + mainOpts.loginToken;\r\n            }\r\n            httpUtil.post(url, { taskId: task.taskId, fileAndTag: fileAndTag }).then(function (res) {\r\n                self.removeTask(task);\r\n                trigger('task-delete-success', task);\r\n                self.prepareUpload();\r\n            }, function (res) {\r\n                trigger('task-delete-error', [task, res]);\r\n            });\r\n        }\r\n    }\r\n\r\n    //计算文件MD5\r\n    this.calcFileMd5 = function (file) {\r\n        //只是简单计算一下，没办法整个文件进行MD5计算。现在这种方式肯定是会有bug的。只能应对大部分情况。\r\n        return sparkmd5.hash(file.name + file.size + getFileTime(file));\r\n    }\r\n\r\n    //获取一个任务\r\n    //this.getTask = function (taskId) {\r\n    //    return _.find(tasks, function (n) { return n.taskId === taskId });\r\n    //}\r\n\r\n    //根据状态获取文件，参数可传入多个状态code\r\n    this.getFilesByStatus = function () {\r\n        var status = [].slice.call(arguments, 0);\r\n        var result = [];\r\n        for (var x = 0; x < tasks.length; x++) {\r\n            for (var y = 0; y < tasks[x].files.length; y++) {\r\n                for (var z = 0; z < status.length; z++) {\r\n                    if (tasks[x].files[y].status === status[z]) {\r\n                        result.push({ task: tasks[x], file: tasks[x].files[y] });\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        return result;\r\n    }\r\n\r\n    //计算进度\r\n    function calcProgress(current, total) {\r\n        if (current == total)\r\n            return 100;\r\n        var result = ((current / total) * 100);\r\n        if (result.toString().indexOf('.') == -1)\r\n            return result;\r\n        return result.toFixed(2);\r\n    }\r\n\r\n    //计算文件进度\r\n    function calcFileProgress(file) {\r\n        return calcProgress(file.chunkIndex, file.chunkTotal);\r\n    }\r\n\r\n    //计算任务进度\r\n    function calcTaskProgress(task) {\r\n        var count = 0;\r\n        for (var i = 0; i < task.files.length; i++)\r\n            count += task.files[i].chunkIndex;\r\n        task.chunkFinished = count;\r\n        return calcProgress(task.chunkFinished, task.chunkTotal);\r\n    }\r\n\r\n    init();\r\n}\r\n\r\nexport default web;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/core/webTransfer.js\n// module id = 3\n// module chunks = 0 1", "/*!\n * The buffer module from node.js, for the browser.\n *\n * <AUTHOR> <<EMAIL>> <http://feross.org>\n * @license  MIT\n */\n/* eslint-disable no-proto */\n\n'use strict'\n\nvar base64 = require('base64-js')\nvar ieee754 = require('ieee754')\nvar isArray = require('isarray')\n\nexports.Buffer = Buffer\nexports.SlowBuffer = SlowBuffer\nexports.INSPECT_MAX_BYTES = 50\n\n/**\n * If `Buffer.TYPED_ARRAY_SUPPORT`:\n *   === true    Use Uint8Array implementation (fastest)\n *   === false   Use Object implementation (most compatible, even IE6)\n *\n * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,\n * Opera 11.6+, iOS 4.2+.\n *\n * Due to various browser bugs, sometimes the Object implementation will be used even\n * when the browser supports typed arrays.\n *\n * Note:\n *\n *   - Firefox 4-29 lacks support for adding new properties to `Uint8Array` instances,\n *     See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438.\n *\n *   - Chrome 9-10 is missing the `TypedArray.prototype.subarray` function.\n *\n *   - IE10 has a broken `TypedArray.prototype.subarray` function which returns arrays of\n *     incorrect length in some situations.\n\n * We detect these buggy browsers and set `Buffer.TYPED_ARRAY_SUPPORT` to `false` so they\n * get the Object implementation, which is slower but behaves correctly.\n */\nBuffer.TYPED_ARRAY_SUPPORT = global.TYPED_ARRAY_SUPPORT !== undefined\n  ? global.TYPED_ARRAY_SUPPORT\n  : typedArraySupport()\n\n/*\n * Export kMaxLength after typed array support is determined.\n */\nexports.kMaxLength = kMaxLength()\n\nfunction typedArraySupport () {\n  try {\n    var arr = new Uint8Array(1)\n    arr.__proto__ = {__proto__: Uint8Array.prototype, foo: function () { return 42 }}\n    return arr.foo() === 42 && // typed array instances can be augmented\n        typeof arr.subarray === 'function' && // chrome 9-10 lack `subarray`\n        arr.subarray(1, 1).byteLength === 0 // ie10 has broken `subarray`\n  } catch (e) {\n    return false\n  }\n}\n\nfunction kMaxLength () {\n  return Buffer.TYPED_ARRAY_SUPPORT\n    ? 0x7fffffff\n    : 0x3fffffff\n}\n\nfunction createBuffer (that, length) {\n  if (kMaxLength() < length) {\n    throw new RangeError('Invalid typed array length')\n  }\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = new Uint8Array(length)\n    that.__proto__ = Buffer.prototype\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    if (that === null) {\n      that = new Buffer(length)\n    }\n    that.length = length\n  }\n\n  return that\n}\n\n/**\n * The Buffer constructor returns instances of `Uint8Array` that have their\n * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of\n * `Uint8Array`, so the returned instances will have all the node `Buffer` methods\n * and the `Uint8Array` methods. Square bracket notation works as expected -- it\n * returns a single octet.\n *\n * The `Uint8Array` prototype remains unmodified.\n */\n\nfunction Buffer (arg, encodingOrOffset, length) {\n  if (!Buffer.TYPED_ARRAY_SUPPORT && !(this instanceof Buffer)) {\n    return new Buffer(arg, encodingOrOffset, length)\n  }\n\n  // Common case.\n  if (typeof arg === 'number') {\n    if (typeof encodingOrOffset === 'string') {\n      throw new Error(\n        'If encoding is specified then the first argument must be a string'\n      )\n    }\n    return allocUnsafe(this, arg)\n  }\n  return from(this, arg, encodingOrOffset, length)\n}\n\nBuffer.poolSize = 8192 // not used by this implementation\n\n// TODO: Legacy, not needed anymore. Remove in next major version.\nBuffer._augment = function (arr) {\n  arr.__proto__ = Buffer.prototype\n  return arr\n}\n\nfunction from (that, value, encodingOrOffset, length) {\n  if (typeof value === 'number') {\n    throw new TypeError('\"value\" argument must not be a number')\n  }\n\n  if (typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer) {\n    return fromArrayBuffer(that, value, encodingOrOffset, length)\n  }\n\n  if (typeof value === 'string') {\n    return fromString(that, value, encodingOrOffset)\n  }\n\n  return fromObject(that, value)\n}\n\n/**\n * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError\n * if value is a number.\n * Buffer.from(str[, encoding])\n * Buffer.from(array)\n * Buffer.from(buffer)\n * Buffer.from(arrayBuffer[, byteOffset[, length]])\n **/\nBuffer.from = function (value, encodingOrOffset, length) {\n  return from(null, value, encodingOrOffset, length)\n}\n\nif (Buffer.TYPED_ARRAY_SUPPORT) {\n  Buffer.prototype.__proto__ = Uint8Array.prototype\n  Buffer.__proto__ = Uint8Array\n  if (typeof Symbol !== 'undefined' && Symbol.species &&\n      Buffer[Symbol.species] === Buffer) {\n    // Fix subarray() in ES2016. See: https://github.com/feross/buffer/pull/97\n    Object.defineProperty(Buffer, Symbol.species, {\n      value: null,\n      configurable: true\n    })\n  }\n}\n\nfunction assertSize (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('\"size\" argument must be a number')\n  } else if (size < 0) {\n    throw new RangeError('\"size\" argument must not be negative')\n  }\n}\n\nfunction alloc (that, size, fill, encoding) {\n  assertSize(size)\n  if (size <= 0) {\n    return createBuffer(that, size)\n  }\n  if (fill !== undefined) {\n    // Only pay attention to encoding if it's a string. This\n    // prevents accidentally sending in a number that would\n    // be interpretted as a start offset.\n    return typeof encoding === 'string'\n      ? createBuffer(that, size).fill(fill, encoding)\n      : createBuffer(that, size).fill(fill)\n  }\n  return createBuffer(that, size)\n}\n\n/**\n * Creates a new filled Buffer instance.\n * alloc(size[, fill[, encoding]])\n **/\nBuffer.alloc = function (size, fill, encoding) {\n  return alloc(null, size, fill, encoding)\n}\n\nfunction allocUnsafe (that, size) {\n  assertSize(size)\n  that = createBuffer(that, size < 0 ? 0 : checked(size) | 0)\n  if (!Buffer.TYPED_ARRAY_SUPPORT) {\n    for (var i = 0; i < size; ++i) {\n      that[i] = 0\n    }\n  }\n  return that\n}\n\n/**\n * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.\n * */\nBuffer.allocUnsafe = function (size) {\n  return allocUnsafe(null, size)\n}\n/**\n * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.\n */\nBuffer.allocUnsafeSlow = function (size) {\n  return allocUnsafe(null, size)\n}\n\nfunction fromString (that, string, encoding) {\n  if (typeof encoding !== 'string' || encoding === '') {\n    encoding = 'utf8'\n  }\n\n  if (!Buffer.isEncoding(encoding)) {\n    throw new TypeError('\"encoding\" must be a valid string encoding')\n  }\n\n  var length = byteLength(string, encoding) | 0\n  that = createBuffer(that, length)\n\n  var actual = that.write(string, encoding)\n\n  if (actual !== length) {\n    // Writing a hex string, for example, that contains invalid characters will\n    // cause everything after the first invalid character to be ignored. (e.g.\n    // 'abxxcd' will be treated as 'ab')\n    that = that.slice(0, actual)\n  }\n\n  return that\n}\n\nfunction fromArrayLike (that, array) {\n  var length = array.length < 0 ? 0 : checked(array.length) | 0\n  that = createBuffer(that, length)\n  for (var i = 0; i < length; i += 1) {\n    that[i] = array[i] & 255\n  }\n  return that\n}\n\nfunction fromArrayBuffer (that, array, byteOffset, length) {\n  array.byteLength // this throws if `array` is not a valid ArrayBuffer\n\n  if (byteOffset < 0 || array.byteLength < byteOffset) {\n    throw new RangeError('\\'offset\\' is out of bounds')\n  }\n\n  if (array.byteLength < byteOffset + (length || 0)) {\n    throw new RangeError('\\'length\\' is out of bounds')\n  }\n\n  if (byteOffset === undefined && length === undefined) {\n    array = new Uint8Array(array)\n  } else if (length === undefined) {\n    array = new Uint8Array(array, byteOffset)\n  } else {\n    array = new Uint8Array(array, byteOffset, length)\n  }\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = array\n    that.__proto__ = Buffer.prototype\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    that = fromArrayLike(that, array)\n  }\n  return that\n}\n\nfunction fromObject (that, obj) {\n  if (Buffer.isBuffer(obj)) {\n    var len = checked(obj.length) | 0\n    that = createBuffer(that, len)\n\n    if (that.length === 0) {\n      return that\n    }\n\n    obj.copy(that, 0, 0, len)\n    return that\n  }\n\n  if (obj) {\n    if ((typeof ArrayBuffer !== 'undefined' &&\n        obj.buffer instanceof ArrayBuffer) || 'length' in obj) {\n      if (typeof obj.length !== 'number' || isnan(obj.length)) {\n        return createBuffer(that, 0)\n      }\n      return fromArrayLike(that, obj)\n    }\n\n    if (obj.type === 'Buffer' && isArray(obj.data)) {\n      return fromArrayLike(that, obj.data)\n    }\n  }\n\n  throw new TypeError('First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.')\n}\n\nfunction checked (length) {\n  // Note: cannot use `length < kMaxLength()` here because that fails when\n  // length is NaN (which is otherwise coerced to zero.)\n  if (length >= kMaxLength()) {\n    throw new RangeError('Attempt to allocate Buffer larger than maximum ' +\n                         'size: 0x' + kMaxLength().toString(16) + ' bytes')\n  }\n  return length | 0\n}\n\nfunction SlowBuffer (length) {\n  if (+length != length) { // eslint-disable-line eqeqeq\n    length = 0\n  }\n  return Buffer.alloc(+length)\n}\n\nBuffer.isBuffer = function isBuffer (b) {\n  return !!(b != null && b._isBuffer)\n}\n\nBuffer.compare = function compare (a, b) {\n  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {\n    throw new TypeError('Arguments must be Buffers')\n  }\n\n  if (a === b) return 0\n\n  var x = a.length\n  var y = b.length\n\n  for (var i = 0, len = Math.min(x, y); i < len; ++i) {\n    if (a[i] !== b[i]) {\n      x = a[i]\n      y = b[i]\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n}\n\nBuffer.isEncoding = function isEncoding (encoding) {\n  switch (String(encoding).toLowerCase()) {\n    case 'hex':\n    case 'utf8':\n    case 'utf-8':\n    case 'ascii':\n    case 'latin1':\n    case 'binary':\n    case 'base64':\n    case 'ucs2':\n    case 'ucs-2':\n    case 'utf16le':\n    case 'utf-16le':\n      return true\n    default:\n      return false\n  }\n}\n\nBuffer.concat = function concat (list, length) {\n  if (!isArray(list)) {\n    throw new TypeError('\"list\" argument must be an Array of Buffers')\n  }\n\n  if (list.length === 0) {\n    return Buffer.alloc(0)\n  }\n\n  var i\n  if (length === undefined) {\n    length = 0\n    for (i = 0; i < list.length; ++i) {\n      length += list[i].length\n    }\n  }\n\n  var buffer = Buffer.allocUnsafe(length)\n  var pos = 0\n  for (i = 0; i < list.length; ++i) {\n    var buf = list[i]\n    if (!Buffer.isBuffer(buf)) {\n      throw new TypeError('\"list\" argument must be an Array of Buffers')\n    }\n    buf.copy(buffer, pos)\n    pos += buf.length\n  }\n  return buffer\n}\n\nfunction byteLength (string, encoding) {\n  if (Buffer.isBuffer(string)) {\n    return string.length\n  }\n  if (typeof ArrayBuffer !== 'undefined' && typeof ArrayBuffer.isView === 'function' &&\n      (ArrayBuffer.isView(string) || string instanceof ArrayBuffer)) {\n    return string.byteLength\n  }\n  if (typeof string !== 'string') {\n    string = '' + string\n  }\n\n  var len = string.length\n  if (len === 0) return 0\n\n  // Use a for loop to avoid recursion\n  var loweredCase = false\n  for (;;) {\n    switch (encoding) {\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return len\n      case 'utf8':\n      case 'utf-8':\n      case undefined:\n        return utf8ToBytes(string).length\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return len * 2\n      case 'hex':\n        return len >>> 1\n      case 'base64':\n        return base64ToBytes(string).length\n      default:\n        if (loweredCase) return utf8ToBytes(string).length // assume utf8\n        encoding = ('' + encoding).toLowerCase()\n        loweredCase = true\n    }\n  }\n}\nBuffer.byteLength = byteLength\n\nfunction slowToString (encoding, start, end) {\n  var loweredCase = false\n\n  // No need to verify that \"this.length <= MAX_UINT32\" since it's a read-only\n  // property of a typed array.\n\n  // This behaves neither like String nor Uint8Array in that we set start/end\n  // to their upper/lower bounds if the value passed is out of range.\n  // undefined is handled specially as per ECMA-262 6th Edition,\n  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.\n  if (start === undefined || start < 0) {\n    start = 0\n  }\n  // Return early if start > this.length. Done here to prevent potential uint32\n  // coercion fail below.\n  if (start > this.length) {\n    return ''\n  }\n\n  if (end === undefined || end > this.length) {\n    end = this.length\n  }\n\n  if (end <= 0) {\n    return ''\n  }\n\n  // Force coersion to uint32. This will also coerce falsey/NaN values to 0.\n  end >>>= 0\n  start >>>= 0\n\n  if (end <= start) {\n    return ''\n  }\n\n  if (!encoding) encoding = 'utf8'\n\n  while (true) {\n    switch (encoding) {\n      case 'hex':\n        return hexSlice(this, start, end)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Slice(this, start, end)\n\n      case 'ascii':\n        return asciiSlice(this, start, end)\n\n      case 'latin1':\n      case 'binary':\n        return latin1Slice(this, start, end)\n\n      case 'base64':\n        return base64Slice(this, start, end)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return utf16leSlice(this, start, end)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = (encoding + '').toLowerCase()\n        loweredCase = true\n    }\n  }\n}\n\n// The property is used by `Buffer.isBuffer` and `is-buffer` (in Safari 5-7) to detect\n// Buffer instances.\nBuffer.prototype._isBuffer = true\n\nfunction swap (b, n, m) {\n  var i = b[n]\n  b[n] = b[m]\n  b[m] = i\n}\n\nBuffer.prototype.swap16 = function swap16 () {\n  var len = this.length\n  if (len % 2 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 16-bits')\n  }\n  for (var i = 0; i < len; i += 2) {\n    swap(this, i, i + 1)\n  }\n  return this\n}\n\nBuffer.prototype.swap32 = function swap32 () {\n  var len = this.length\n  if (len % 4 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 32-bits')\n  }\n  for (var i = 0; i < len; i += 4) {\n    swap(this, i, i + 3)\n    swap(this, i + 1, i + 2)\n  }\n  return this\n}\n\nBuffer.prototype.swap64 = function swap64 () {\n  var len = this.length\n  if (len % 8 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 64-bits')\n  }\n  for (var i = 0; i < len; i += 8) {\n    swap(this, i, i + 7)\n    swap(this, i + 1, i + 6)\n    swap(this, i + 2, i + 5)\n    swap(this, i + 3, i + 4)\n  }\n  return this\n}\n\nBuffer.prototype.toString = function toString () {\n  var length = this.length | 0\n  if (length === 0) return ''\n  if (arguments.length === 0) return utf8Slice(this, 0, length)\n  return slowToString.apply(this, arguments)\n}\n\nBuffer.prototype.equals = function equals (b) {\n  if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer')\n  if (this === b) return true\n  return Buffer.compare(this, b) === 0\n}\n\nBuffer.prototype.inspect = function inspect () {\n  var str = ''\n  var max = exports.INSPECT_MAX_BYTES\n  if (this.length > 0) {\n    str = this.toString('hex', 0, max).match(/.{2}/g).join(' ')\n    if (this.length > max) str += ' ... '\n  }\n  return '<Buffer ' + str + '>'\n}\n\nBuffer.prototype.compare = function compare (target, start, end, thisStart, thisEnd) {\n  if (!Buffer.isBuffer(target)) {\n    throw new TypeError('Argument must be a Buffer')\n  }\n\n  if (start === undefined) {\n    start = 0\n  }\n  if (end === undefined) {\n    end = target ? target.length : 0\n  }\n  if (thisStart === undefined) {\n    thisStart = 0\n  }\n  if (thisEnd === undefined) {\n    thisEnd = this.length\n  }\n\n  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {\n    throw new RangeError('out of range index')\n  }\n\n  if (thisStart >= thisEnd && start >= end) {\n    return 0\n  }\n  if (thisStart >= thisEnd) {\n    return -1\n  }\n  if (start >= end) {\n    return 1\n  }\n\n  start >>>= 0\n  end >>>= 0\n  thisStart >>>= 0\n  thisEnd >>>= 0\n\n  if (this === target) return 0\n\n  var x = thisEnd - thisStart\n  var y = end - start\n  var len = Math.min(x, y)\n\n  var thisCopy = this.slice(thisStart, thisEnd)\n  var targetCopy = target.slice(start, end)\n\n  for (var i = 0; i < len; ++i) {\n    if (thisCopy[i] !== targetCopy[i]) {\n      x = thisCopy[i]\n      y = targetCopy[i]\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n}\n\n// Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,\n// OR the last index of `val` in `buffer` at offset <= `byteOffset`.\n//\n// Arguments:\n// - buffer - a Buffer to search\n// - val - a string, Buffer, or number\n// - byteOffset - an index into `buffer`; will be clamped to an int32\n// - encoding - an optional encoding, relevant is val is a string\n// - dir - true for indexOf, false for lastIndexOf\nfunction bidirectionalIndexOf (buffer, val, byteOffset, encoding, dir) {\n  // Empty buffer means no match\n  if (buffer.length === 0) return -1\n\n  // Normalize byteOffset\n  if (typeof byteOffset === 'string') {\n    encoding = byteOffset\n    byteOffset = 0\n  } else if (byteOffset > 0x7fffffff) {\n    byteOffset = 0x7fffffff\n  } else if (byteOffset < -0x80000000) {\n    byteOffset = -0x80000000\n  }\n  byteOffset = +byteOffset  // Coerce to Number.\n  if (isNaN(byteOffset)) {\n    // byteOffset: it it's undefined, null, NaN, \"foo\", etc, search whole buffer\n    byteOffset = dir ? 0 : (buffer.length - 1)\n  }\n\n  // Normalize byteOffset: negative offsets start from the end of the buffer\n  if (byteOffset < 0) byteOffset = buffer.length + byteOffset\n  if (byteOffset >= buffer.length) {\n    if (dir) return -1\n    else byteOffset = buffer.length - 1\n  } else if (byteOffset < 0) {\n    if (dir) byteOffset = 0\n    else return -1\n  }\n\n  // Normalize val\n  if (typeof val === 'string') {\n    val = Buffer.from(val, encoding)\n  }\n\n  // Finally, search either indexOf (if dir is true) or lastIndexOf\n  if (Buffer.isBuffer(val)) {\n    // Special case: looking for empty string/buffer always fails\n    if (val.length === 0) {\n      return -1\n    }\n    return arrayIndexOf(buffer, val, byteOffset, encoding, dir)\n  } else if (typeof val === 'number') {\n    val = val & 0xFF // Search for a byte value [0-255]\n    if (Buffer.TYPED_ARRAY_SUPPORT &&\n        typeof Uint8Array.prototype.indexOf === 'function') {\n      if (dir) {\n        return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset)\n      } else {\n        return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset)\n      }\n    }\n    return arrayIndexOf(buffer, [ val ], byteOffset, encoding, dir)\n  }\n\n  throw new TypeError('val must be string, number or Buffer')\n}\n\nfunction arrayIndexOf (arr, val, byteOffset, encoding, dir) {\n  var indexSize = 1\n  var arrLength = arr.length\n  var valLength = val.length\n\n  if (encoding !== undefined) {\n    encoding = String(encoding).toLowerCase()\n    if (encoding === 'ucs2' || encoding === 'ucs-2' ||\n        encoding === 'utf16le' || encoding === 'utf-16le') {\n      if (arr.length < 2 || val.length < 2) {\n        return -1\n      }\n      indexSize = 2\n      arrLength /= 2\n      valLength /= 2\n      byteOffset /= 2\n    }\n  }\n\n  function read (buf, i) {\n    if (indexSize === 1) {\n      return buf[i]\n    } else {\n      return buf.readUInt16BE(i * indexSize)\n    }\n  }\n\n  var i\n  if (dir) {\n    var foundIndex = -1\n    for (i = byteOffset; i < arrLength; i++) {\n      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {\n        if (foundIndex === -1) foundIndex = i\n        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize\n      } else {\n        if (foundIndex !== -1) i -= i - foundIndex\n        foundIndex = -1\n      }\n    }\n  } else {\n    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength\n    for (i = byteOffset; i >= 0; i--) {\n      var found = true\n      for (var j = 0; j < valLength; j++) {\n        if (read(arr, i + j) !== read(val, j)) {\n          found = false\n          break\n        }\n      }\n      if (found) return i\n    }\n  }\n\n  return -1\n}\n\nBuffer.prototype.includes = function includes (val, byteOffset, encoding) {\n  return this.indexOf(val, byteOffset, encoding) !== -1\n}\n\nBuffer.prototype.indexOf = function indexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, true)\n}\n\nBuffer.prototype.lastIndexOf = function lastIndexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, false)\n}\n\nfunction hexWrite (buf, string, offset, length) {\n  offset = Number(offset) || 0\n  var remaining = buf.length - offset\n  if (!length) {\n    length = remaining\n  } else {\n    length = Number(length)\n    if (length > remaining) {\n      length = remaining\n    }\n  }\n\n  // must be an even number of digits\n  var strLen = string.length\n  if (strLen % 2 !== 0) throw new TypeError('Invalid hex string')\n\n  if (length > strLen / 2) {\n    length = strLen / 2\n  }\n  for (var i = 0; i < length; ++i) {\n    var parsed = parseInt(string.substr(i * 2, 2), 16)\n    if (isNaN(parsed)) return i\n    buf[offset + i] = parsed\n  }\n  return i\n}\n\nfunction utf8Write (buf, string, offset, length) {\n  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nfunction asciiWrite (buf, string, offset, length) {\n  return blitBuffer(asciiToBytes(string), buf, offset, length)\n}\n\nfunction latin1Write (buf, string, offset, length) {\n  return asciiWrite(buf, string, offset, length)\n}\n\nfunction base64Write (buf, string, offset, length) {\n  return blitBuffer(base64ToBytes(string), buf, offset, length)\n}\n\nfunction ucs2Write (buf, string, offset, length) {\n  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nBuffer.prototype.write = function write (string, offset, length, encoding) {\n  // Buffer#write(string)\n  if (offset === undefined) {\n    encoding = 'utf8'\n    length = this.length\n    offset = 0\n  // Buffer#write(string, encoding)\n  } else if (length === undefined && typeof offset === 'string') {\n    encoding = offset\n    length = this.length\n    offset = 0\n  // Buffer#write(string, offset[, length][, encoding])\n  } else if (isFinite(offset)) {\n    offset = offset | 0\n    if (isFinite(length)) {\n      length = length | 0\n      if (encoding === undefined) encoding = 'utf8'\n    } else {\n      encoding = length\n      length = undefined\n    }\n  // legacy write(string, encoding, offset, length) - remove in v0.13\n  } else {\n    throw new Error(\n      'Buffer.write(string, encoding, offset[, length]) is no longer supported'\n    )\n  }\n\n  var remaining = this.length - offset\n  if (length === undefined || length > remaining) length = remaining\n\n  if ((string.length > 0 && (length < 0 || offset < 0)) || offset > this.length) {\n    throw new RangeError('Attempt to write outside buffer bounds')\n  }\n\n  if (!encoding) encoding = 'utf8'\n\n  var loweredCase = false\n  for (;;) {\n    switch (encoding) {\n      case 'hex':\n        return hexWrite(this, string, offset, length)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Write(this, string, offset, length)\n\n      case 'ascii':\n        return asciiWrite(this, string, offset, length)\n\n      case 'latin1':\n      case 'binary':\n        return latin1Write(this, string, offset, length)\n\n      case 'base64':\n        // Warning: maxLength not taken into account in base64Write\n        return base64Write(this, string, offset, length)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return ucs2Write(this, string, offset, length)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = ('' + encoding).toLowerCase()\n        loweredCase = true\n    }\n  }\n}\n\nBuffer.prototype.toJSON = function toJSON () {\n  return {\n    type: 'Buffer',\n    data: Array.prototype.slice.call(this._arr || this, 0)\n  }\n}\n\nfunction base64Slice (buf, start, end) {\n  if (start === 0 && end === buf.length) {\n    return base64.fromByteArray(buf)\n  } else {\n    return base64.fromByteArray(buf.slice(start, end))\n  }\n}\n\nfunction utf8Slice (buf, start, end) {\n  end = Math.min(buf.length, end)\n  var res = []\n\n  var i = start\n  while (i < end) {\n    var firstByte = buf[i]\n    var codePoint = null\n    var bytesPerSequence = (firstByte > 0xEF) ? 4\n      : (firstByte > 0xDF) ? 3\n      : (firstByte > 0xBF) ? 2\n      : 1\n\n    if (i + bytesPerSequence <= end) {\n      var secondByte, thirdByte, fourthByte, tempCodePoint\n\n      switch (bytesPerSequence) {\n        case 1:\n          if (firstByte < 0x80) {\n            codePoint = firstByte\n          }\n          break\n        case 2:\n          secondByte = buf[i + 1]\n          if ((secondByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0x1F) << 0x6 | (secondByte & 0x3F)\n            if (tempCodePoint > 0x7F) {\n              codePoint = tempCodePoint\n            }\n          }\n          break\n        case 3:\n          secondByte = buf[i + 1]\n          thirdByte = buf[i + 2]\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | (thirdByte & 0x3F)\n            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {\n              codePoint = tempCodePoint\n            }\n          }\n          break\n        case 4:\n          secondByte = buf[i + 1]\n          thirdByte = buf[i + 2]\n          fourthByte = buf[i + 3]\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | (fourthByte & 0x3F)\n            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {\n              codePoint = tempCodePoint\n            }\n          }\n      }\n    }\n\n    if (codePoint === null) {\n      // we did not generate a valid codePoint so insert a\n      // replacement char (U+FFFD) and advance only 1 byte\n      codePoint = 0xFFFD\n      bytesPerSequence = 1\n    } else if (codePoint > 0xFFFF) {\n      // encode to utf16 (surrogate pair dance)\n      codePoint -= 0x10000\n      res.push(codePoint >>> 10 & 0x3FF | 0xD800)\n      codePoint = 0xDC00 | codePoint & 0x3FF\n    }\n\n    res.push(codePoint)\n    i += bytesPerSequence\n  }\n\n  return decodeCodePointsArray(res)\n}\n\n// Based on http://stackoverflow.com/a/22747272/680742, the browser with\n// the lowest limit is Chrome, with 0x10000 args.\n// We go 1 magnitude less, for safety\nvar MAX_ARGUMENTS_LENGTH = 0x1000\n\nfunction decodeCodePointsArray (codePoints) {\n  var len = codePoints.length\n  if (len <= MAX_ARGUMENTS_LENGTH) {\n    return String.fromCharCode.apply(String, codePoints) // avoid extra slice()\n  }\n\n  // Decode in chunks to avoid \"call stack size exceeded\".\n  var res = ''\n  var i = 0\n  while (i < len) {\n    res += String.fromCharCode.apply(\n      String,\n      codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH)\n    )\n  }\n  return res\n}\n\nfunction asciiSlice (buf, start, end) {\n  var ret = ''\n  end = Math.min(buf.length, end)\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i] & 0x7F)\n  }\n  return ret\n}\n\nfunction latin1Slice (buf, start, end) {\n  var ret = ''\n  end = Math.min(buf.length, end)\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i])\n  }\n  return ret\n}\n\nfunction hexSlice (buf, start, end) {\n  var len = buf.length\n\n  if (!start || start < 0) start = 0\n  if (!end || end < 0 || end > len) end = len\n\n  var out = ''\n  for (var i = start; i < end; ++i) {\n    out += toHex(buf[i])\n  }\n  return out\n}\n\nfunction utf16leSlice (buf, start, end) {\n  var bytes = buf.slice(start, end)\n  var res = ''\n  for (var i = 0; i < bytes.length; i += 2) {\n    res += String.fromCharCode(bytes[i] + bytes[i + 1] * 256)\n  }\n  return res\n}\n\nBuffer.prototype.slice = function slice (start, end) {\n  var len = this.length\n  start = ~~start\n  end = end === undefined ? len : ~~end\n\n  if (start < 0) {\n    start += len\n    if (start < 0) start = 0\n  } else if (start > len) {\n    start = len\n  }\n\n  if (end < 0) {\n    end += len\n    if (end < 0) end = 0\n  } else if (end > len) {\n    end = len\n  }\n\n  if (end < start) end = start\n\n  var newBuf\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    newBuf = this.subarray(start, end)\n    newBuf.__proto__ = Buffer.prototype\n  } else {\n    var sliceLen = end - start\n    newBuf = new Buffer(sliceLen, undefined)\n    for (var i = 0; i < sliceLen; ++i) {\n      newBuf[i] = this[i + start]\n    }\n  }\n\n  return newBuf\n}\n\n/*\n * Need to make sure that buffer isn't trying to write out of bounds.\n */\nfunction checkOffset (offset, ext, length) {\n  if ((offset % 1) !== 0 || offset < 0) throw new RangeError('offset is not uint')\n  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length')\n}\n\nBuffer.prototype.readUIntLE = function readUIntLE (offset, byteLength, noAssert) {\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  var val = this[offset]\n  var mul = 1\n  var i = 0\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul\n  }\n\n  return val\n}\n\nBuffer.prototype.readUIntBE = function readUIntBE (offset, byteLength, noAssert) {\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) {\n    checkOffset(offset, byteLength, this.length)\n  }\n\n  var val = this[offset + --byteLength]\n  var mul = 1\n  while (byteLength > 0 && (mul *= 0x100)) {\n    val += this[offset + --byteLength] * mul\n  }\n\n  return val\n}\n\nBuffer.prototype.readUInt8 = function readUInt8 (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length)\n  return this[offset]\n}\n\nBuffer.prototype.readUInt16LE = function readUInt16LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  return this[offset] | (this[offset + 1] << 8)\n}\n\nBuffer.prototype.readUInt16BE = function readUInt16BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  return (this[offset] << 8) | this[offset + 1]\n}\n\nBuffer.prototype.readUInt32LE = function readUInt32LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return ((this[offset]) |\n      (this[offset + 1] << 8) |\n      (this[offset + 2] << 16)) +\n      (this[offset + 3] * 0x1000000)\n}\n\nBuffer.prototype.readUInt32BE = function readUInt32BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset] * 0x1000000) +\n    ((this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    this[offset + 3])\n}\n\nBuffer.prototype.readIntLE = function readIntLE (offset, byteLength, noAssert) {\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  var val = this[offset]\n  var mul = 1\n  var i = 0\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul\n  }\n  mul *= 0x80\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength)\n\n  return val\n}\n\nBuffer.prototype.readIntBE = function readIntBE (offset, byteLength, noAssert) {\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  var i = byteLength\n  var mul = 1\n  var val = this[offset + --i]\n  while (i > 0 && (mul *= 0x100)) {\n    val += this[offset + --i] * mul\n  }\n  mul *= 0x80\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength)\n\n  return val\n}\n\nBuffer.prototype.readInt8 = function readInt8 (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length)\n  if (!(this[offset] & 0x80)) return (this[offset])\n  return ((0xff - this[offset] + 1) * -1)\n}\n\nBuffer.prototype.readInt16LE = function readInt16LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  var val = this[offset] | (this[offset + 1] << 8)\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n}\n\nBuffer.prototype.readInt16BE = function readInt16BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  var val = this[offset + 1] | (this[offset] << 8)\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n}\n\nBuffer.prototype.readInt32LE = function readInt32LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset]) |\n    (this[offset + 1] << 8) |\n    (this[offset + 2] << 16) |\n    (this[offset + 3] << 24)\n}\n\nBuffer.prototype.readInt32BE = function readInt32BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset] << 24) |\n    (this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    (this[offset + 3])\n}\n\nBuffer.prototype.readFloatLE = function readFloatLE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n  return ieee754.read(this, offset, true, 23, 4)\n}\n\nBuffer.prototype.readFloatBE = function readFloatBE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n  return ieee754.read(this, offset, false, 23, 4)\n}\n\nBuffer.prototype.readDoubleLE = function readDoubleLE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length)\n  return ieee754.read(this, offset, true, 52, 8)\n}\n\nBuffer.prototype.readDoubleBE = function readDoubleBE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length)\n  return ieee754.read(this, offset, false, 52, 8)\n}\n\nfunction checkInt (buf, value, offset, ext, max, min) {\n  if (!Buffer.isBuffer(buf)) throw new TypeError('\"buffer\" argument must be a Buffer instance')\n  if (value > max || value < min) throw new RangeError('\"value\" argument is out of bounds')\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n}\n\nBuffer.prototype.writeUIntLE = function writeUIntLE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1\n    checkInt(this, value, offset, byteLength, maxBytes, 0)\n  }\n\n  var mul = 1\n  var i = 0\n  this[offset] = value & 0xFF\n  while (++i < byteLength && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeUIntBE = function writeUIntBE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1\n    checkInt(this, value, offset, byteLength, maxBytes, 0)\n  }\n\n  var i = byteLength - 1\n  var mul = 1\n  this[offset + i] = value & 0xFF\n  while (--i >= 0 && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeUInt8 = function writeUInt8 (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0)\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value)\n  this[offset] = (value & 0xff)\n  return offset + 1\n}\n\nfunction objectWriteUInt16 (buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffff + value + 1\n  for (var i = 0, j = Math.min(buf.length - offset, 2); i < j; ++i) {\n    buf[offset + i] = (value & (0xff << (8 * (littleEndian ? i : 1 - i)))) >>>\n      (littleEndian ? i : 1 - i) * 8\n  }\n}\n\nBuffer.prototype.writeUInt16LE = function writeUInt16LE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value & 0xff)\n    this[offset + 1] = (value >>> 8)\n  } else {\n    objectWriteUInt16(this, value, offset, true)\n  }\n  return offset + 2\n}\n\nBuffer.prototype.writeUInt16BE = function writeUInt16BE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 8)\n    this[offset + 1] = (value & 0xff)\n  } else {\n    objectWriteUInt16(this, value, offset, false)\n  }\n  return offset + 2\n}\n\nfunction objectWriteUInt32 (buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffffffff + value + 1\n  for (var i = 0, j = Math.min(buf.length - offset, 4); i < j; ++i) {\n    buf[offset + i] = (value >>> (littleEndian ? i : 3 - i) * 8) & 0xff\n  }\n}\n\nBuffer.prototype.writeUInt32LE = function writeUInt32LE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset + 3] = (value >>> 24)\n    this[offset + 2] = (value >>> 16)\n    this[offset + 1] = (value >>> 8)\n    this[offset] = (value & 0xff)\n  } else {\n    objectWriteUInt32(this, value, offset, true)\n  }\n  return offset + 4\n}\n\nBuffer.prototype.writeUInt32BE = function writeUInt32BE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 24)\n    this[offset + 1] = (value >>> 16)\n    this[offset + 2] = (value >>> 8)\n    this[offset + 3] = (value & 0xff)\n  } else {\n    objectWriteUInt32(this, value, offset, false)\n  }\n  return offset + 4\n}\n\nBuffer.prototype.writeIntLE = function writeIntLE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1)\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit)\n  }\n\n  var i = 0\n  var mul = 1\n  var sub = 0\n  this[offset] = value & 0xFF\n  while (++i < byteLength && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {\n      sub = 1\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeIntBE = function writeIntBE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1)\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit)\n  }\n\n  var i = byteLength - 1\n  var mul = 1\n  var sub = 0\n  this[offset + i] = value & 0xFF\n  while (--i >= 0 && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {\n      sub = 1\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeInt8 = function writeInt8 (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80)\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value)\n  if (value < 0) value = 0xff + value + 1\n  this[offset] = (value & 0xff)\n  return offset + 1\n}\n\nBuffer.prototype.writeInt16LE = function writeInt16LE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value & 0xff)\n    this[offset + 1] = (value >>> 8)\n  } else {\n    objectWriteUInt16(this, value, offset, true)\n  }\n  return offset + 2\n}\n\nBuffer.prototype.writeInt16BE = function writeInt16BE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 8)\n    this[offset + 1] = (value & 0xff)\n  } else {\n    objectWriteUInt16(this, value, offset, false)\n  }\n  return offset + 2\n}\n\nBuffer.prototype.writeInt32LE = function writeInt32LE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value & 0xff)\n    this[offset + 1] = (value >>> 8)\n    this[offset + 2] = (value >>> 16)\n    this[offset + 3] = (value >>> 24)\n  } else {\n    objectWriteUInt32(this, value, offset, true)\n  }\n  return offset + 4\n}\n\nBuffer.prototype.writeInt32BE = function writeInt32BE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)\n  if (value < 0) value = 0xffffffff + value + 1\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 24)\n    this[offset + 1] = (value >>> 16)\n    this[offset + 2] = (value >>> 8)\n    this[offset + 3] = (value & 0xff)\n  } else {\n    objectWriteUInt32(this, value, offset, false)\n  }\n  return offset + 4\n}\n\nfunction checkIEEE754 (buf, value, offset, ext, max, min) {\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n  if (offset < 0) throw new RangeError('Index out of range')\n}\n\nfunction writeFloat (buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38)\n  }\n  ieee754.write(buf, value, offset, littleEndian, 23, 4)\n  return offset + 4\n}\n\nBuffer.prototype.writeFloatLE = function writeFloatLE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, true, noAssert)\n}\n\nBuffer.prototype.writeFloatBE = function writeFloatBE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, false, noAssert)\n}\n\nfunction writeDouble (buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308)\n  }\n  ieee754.write(buf, value, offset, littleEndian, 52, 8)\n  return offset + 8\n}\n\nBuffer.prototype.writeDoubleLE = function writeDoubleLE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, true, noAssert)\n}\n\nBuffer.prototype.writeDoubleBE = function writeDoubleBE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, false, noAssert)\n}\n\n// copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)\nBuffer.prototype.copy = function copy (target, targetStart, start, end) {\n  if (!start) start = 0\n  if (!end && end !== 0) end = this.length\n  if (targetStart >= target.length) targetStart = target.length\n  if (!targetStart) targetStart = 0\n  if (end > 0 && end < start) end = start\n\n  // Copy 0 bytes; we're done\n  if (end === start) return 0\n  if (target.length === 0 || this.length === 0) return 0\n\n  //  conditions\n  if (targetStart < 0) {\n    throw new RangeError('targetStart out of bounds')\n  }\n  if (start < 0 || start >= this.length) throw new RangeError('sourceStart out of bounds')\n  if (end < 0) throw new RangeError('sourceEnd out of bounds')\n\n  // Are we oob?\n  if (end > this.length) end = this.length\n  if (target.length - targetStart < end - start) {\n    end = target.length - targetStart + start\n  }\n\n  var len = end - start\n  var i\n\n  if (this === target && start < targetStart && targetStart < end) {\n    // descending copy from end\n    for (i = len - 1; i >= 0; --i) {\n      target[i + targetStart] = this[i + start]\n    }\n  } else if (len < 1000 || !Buffer.TYPED_ARRAY_SUPPORT) {\n    // ascending copy from start\n    for (i = 0; i < len; ++i) {\n      target[i + targetStart] = this[i + start]\n    }\n  } else {\n    Uint8Array.prototype.set.call(\n      target,\n      this.subarray(start, start + len),\n      targetStart\n    )\n  }\n\n  return len\n}\n\n// Usage:\n//    buffer.fill(number[, offset[, end]])\n//    buffer.fill(buffer[, offset[, end]])\n//    buffer.fill(string[, offset[, end]][, encoding])\nBuffer.prototype.fill = function fill (val, start, end, encoding) {\n  // Handle string cases:\n  if (typeof val === 'string') {\n    if (typeof start === 'string') {\n      encoding = start\n      start = 0\n      end = this.length\n    } else if (typeof end === 'string') {\n      encoding = end\n      end = this.length\n    }\n    if (val.length === 1) {\n      var code = val.charCodeAt(0)\n      if (code < 256) {\n        val = code\n      }\n    }\n    if (encoding !== undefined && typeof encoding !== 'string') {\n      throw new TypeError('encoding must be a string')\n    }\n    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {\n      throw new TypeError('Unknown encoding: ' + encoding)\n    }\n  } else if (typeof val === 'number') {\n    val = val & 255\n  }\n\n  // Invalid ranges are not set to a default, so can range check early.\n  if (start < 0 || this.length < start || this.length < end) {\n    throw new RangeError('Out of range index')\n  }\n\n  if (end <= start) {\n    return this\n  }\n\n  start = start >>> 0\n  end = end === undefined ? this.length : end >>> 0\n\n  if (!val) val = 0\n\n  var i\n  if (typeof val === 'number') {\n    for (i = start; i < end; ++i) {\n      this[i] = val\n    }\n  } else {\n    var bytes = Buffer.isBuffer(val)\n      ? val\n      : utf8ToBytes(new Buffer(val, encoding).toString())\n    var len = bytes.length\n    for (i = 0; i < end - start; ++i) {\n      this[i + start] = bytes[i % len]\n    }\n  }\n\n  return this\n}\n\n// HELPER FUNCTIONS\n// ================\n\nvar INVALID_BASE64_RE = /[^+\\/0-9A-Za-z-_]/g\n\nfunction base64clean (str) {\n  // Node strips out invalid characters like \\n and \\t from the string, base64-js does not\n  str = stringtrim(str).replace(INVALID_BASE64_RE, '')\n  // Node converts strings with length < 2 to ''\n  if (str.length < 2) return ''\n  // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not\n  while (str.length % 4 !== 0) {\n    str = str + '='\n  }\n  return str\n}\n\nfunction stringtrim (str) {\n  if (str.trim) return str.trim()\n  return str.replace(/^\\s+|\\s+$/g, '')\n}\n\nfunction toHex (n) {\n  if (n < 16) return '0' + n.toString(16)\n  return n.toString(16)\n}\n\nfunction utf8ToBytes (string, units) {\n  units = units || Infinity\n  var codePoint\n  var length = string.length\n  var leadSurrogate = null\n  var bytes = []\n\n  for (var i = 0; i < length; ++i) {\n    codePoint = string.charCodeAt(i)\n\n    // is surrogate component\n    if (codePoint > 0xD7FF && codePoint < 0xE000) {\n      // last char was a lead\n      if (!leadSurrogate) {\n        // no lead yet\n        if (codePoint > 0xDBFF) {\n          // unexpected trail\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n          continue\n        } else if (i + 1 === length) {\n          // unpaired lead\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n          continue\n        }\n\n        // valid lead\n        leadSurrogate = codePoint\n\n        continue\n      }\n\n      // 2 leads in a row\n      if (codePoint < 0xDC00) {\n        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n        leadSurrogate = codePoint\n        continue\n      }\n\n      // valid surrogate pair\n      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000\n    } else if (leadSurrogate) {\n      // valid bmp char, but last char was a lead\n      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n    }\n\n    leadSurrogate = null\n\n    // encode utf8\n    if (codePoint < 0x80) {\n      if ((units -= 1) < 0) break\n      bytes.push(codePoint)\n    } else if (codePoint < 0x800) {\n      if ((units -= 2) < 0) break\n      bytes.push(\n        codePoint >> 0x6 | 0xC0,\n        codePoint & 0x3F | 0x80\n      )\n    } else if (codePoint < 0x10000) {\n      if ((units -= 3) < 0) break\n      bytes.push(\n        codePoint >> 0xC | 0xE0,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      )\n    } else if (codePoint < 0x110000) {\n      if ((units -= 4) < 0) break\n      bytes.push(\n        codePoint >> 0x12 | 0xF0,\n        codePoint >> 0xC & 0x3F | 0x80,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      )\n    } else {\n      throw new Error('Invalid code point')\n    }\n  }\n\n  return bytes\n}\n\nfunction asciiToBytes (str) {\n  var byteArray = []\n  for (var i = 0; i < str.length; ++i) {\n    // Node's code seems to be doing this and not & 0x7F..\n    byteArray.push(str.charCodeAt(i) & 0xFF)\n  }\n  return byteArray\n}\n\nfunction utf16leToBytes (str, units) {\n  var c, hi, lo\n  var byteArray = []\n  for (var i = 0; i < str.length; ++i) {\n    if ((units -= 2) < 0) break\n\n    c = str.charCodeAt(i)\n    hi = c >> 8\n    lo = c % 256\n    byteArray.push(lo)\n    byteArray.push(hi)\n  }\n\n  return byteArray\n}\n\nfunction base64ToBytes (str) {\n  return base64.toByteArray(base64clean(str))\n}\n\nfunction blitBuffer (src, dst, offset, length) {\n  for (var i = 0; i < length; ++i) {\n    if ((i + offset >= dst.length) || (i >= src.length)) break\n    dst[i + offset] = src[i]\n  }\n  return i\n}\n\nfunction isnan (val) {\n  return val !== val // eslint-disable-line no-self-compare\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/buffer/index.js\n// module id = 4\n// module chunks = 0 1", "var g;\r\n\r\n// This works in non-strict mode\r\ng = (function() {\r\n\treturn this;\r\n})();\r\n\r\ntry {\r\n\t// This works if eval is allowed (see CSP)\r\n\tg = g || Function(\"return this\")() || (1,eval)(\"this\");\r\n} catch(e) {\r\n\t// This works if the window reference is available\r\n\tif(typeof window === \"object\")\r\n\t\tg = window;\r\n}\r\n\r\n// g can still be undefined, but nothing to do about it...\r\n// We return undefined, instead of nothing here, so it's\r\n// easier to handle this case. if(!global) { ...}\r\n\r\nmodule.exports = g;\r\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// (webpack)/buildin/global.js\n// module id = 5\n// module chunks = 0 1", "'use strict'\n\nexports.byteLength = byteLength\nexports.toByteArray = toByteArray\nexports.fromByteArray = fromByteArray\n\nvar lookup = []\nvar revLookup = []\nvar Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array\n\nvar code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'\nfor (var i = 0, len = code.length; i < len; ++i) {\n  lookup[i] = code[i]\n  revLookup[code.charCodeAt(i)] = i\n}\n\n// Support decoding URL-safe base64 strings, as Node.js does.\n// See: https://en.wikipedia.org/wiki/Base64#URL_applications\nrevLookup['-'.charCodeAt(0)] = 62\nrevLookup['_'.charCodeAt(0)] = 63\n\nfunction placeHoldersCount (b64) {\n  var len = b64.length\n  if (len % 4 > 0) {\n    throw new Error('Invalid string. Length must be a multiple of 4')\n  }\n\n  // the number of equal signs (place holders)\n  // if there are two placeholders, than the two characters before it\n  // represent one byte\n  // if there is only one, then the three characters before it represent 2 bytes\n  // this is just a cheap hack to not do indexOf twice\n  return b64[len - 2] === '=' ? 2 : b64[len - 1] === '=' ? 1 : 0\n}\n\nfunction byteLength (b64) {\n  // base64 is 4/3 + up to two characters of the original data\n  return (b64.length * 3 / 4) - placeHoldersCount(b64)\n}\n\nfunction toByteArray (b64) {\n  var i, l, tmp, placeHolders, arr\n  var len = b64.length\n  placeHolders = placeHoldersCount(b64)\n\n  arr = new Arr((len * 3 / 4) - placeHolders)\n\n  // if there are placeholders, only get up to the last complete 4 chars\n  l = placeHolders > 0 ? len - 4 : len\n\n  var L = 0\n\n  for (i = 0; i < l; i += 4) {\n    tmp = (revLookup[b64.charCodeAt(i)] << 18) | (revLookup[b64.charCodeAt(i + 1)] << 12) | (revLookup[b64.charCodeAt(i + 2)] << 6) | revLookup[b64.charCodeAt(i + 3)]\n    arr[L++] = (tmp >> 16) & 0xFF\n    arr[L++] = (tmp >> 8) & 0xFF\n    arr[L++] = tmp & 0xFF\n  }\n\n  if (placeHolders === 2) {\n    tmp = (revLookup[b64.charCodeAt(i)] << 2) | (revLookup[b64.charCodeAt(i + 1)] >> 4)\n    arr[L++] = tmp & 0xFF\n  } else if (placeHolders === 1) {\n    tmp = (revLookup[b64.charCodeAt(i)] << 10) | (revLookup[b64.charCodeAt(i + 1)] << 4) | (revLookup[b64.charCodeAt(i + 2)] >> 2)\n    arr[L++] = (tmp >> 8) & 0xFF\n    arr[L++] = tmp & 0xFF\n  }\n\n  return arr\n}\n\nfunction tripletToBase64 (num) {\n  return lookup[num >> 18 & 0x3F] + lookup[num >> 12 & 0x3F] + lookup[num >> 6 & 0x3F] + lookup[num & 0x3F]\n}\n\nfunction encodeChunk (uint8, start, end) {\n  var tmp\n  var output = []\n  for (var i = start; i < end; i += 3) {\n    tmp = ((uint8[i] << 16) & 0xFF0000) + ((uint8[i + 1] << 8) & 0xFF00) + (uint8[i + 2] & 0xFF)\n    output.push(tripletToBase64(tmp))\n  }\n  return output.join('')\n}\n\nfunction fromByteArray (uint8) {\n  var tmp\n  var len = uint8.length\n  var extraBytes = len % 3 // if we have 1 byte left, pad 2 bytes\n  var output = ''\n  var parts = []\n  var maxChunkLength = 16383 // must be multiple of 3\n\n  // go through the array every three bytes, we'll deal with trailing stuff later\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(encodeChunk(uint8, i, (i + maxChunkLength) > len2 ? len2 : (i + maxChunkLength)))\n  }\n\n  // pad the end with zeros, but make sure to not forget the extra bytes\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1]\n    output += lookup[tmp >> 2]\n    output += lookup[(tmp << 4) & 0x3F]\n    output += '=='\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + (uint8[len - 1])\n    output += lookup[tmp >> 10]\n    output += lookup[(tmp >> 4) & 0x3F]\n    output += lookup[(tmp << 2) & 0x3F]\n    output += '='\n  }\n\n  parts.push(output)\n\n  return parts.join('')\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/base64-js/index.js\n// module id = 6\n// module chunks = 0 1", "exports.read = function (buffer, offset, isLE, mLen, nBytes) {\n  var e, m\n  var eLen = (nBytes * 8) - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var nBits = -7\n  var i = isLE ? (nBytes - 1) : 0\n  var d = isLE ? -1 : 1\n  var s = buffer[offset + i]\n\n  i += d\n\n  e = s & ((1 << (-nBits)) - 1)\n  s >>= (-nBits)\n  nBits += eLen\n  for (; nBits > 0; e = (e * 256) + buffer[offset + i], i += d, nBits -= 8) {}\n\n  m = e & ((1 << (-nBits)) - 1)\n  e >>= (-nBits)\n  nBits += mLen\n  for (; nBits > 0; m = (m * 256) + buffer[offset + i], i += d, nBits -= 8) {}\n\n  if (e === 0) {\n    e = 1 - eBias\n  } else if (e === eMax) {\n    return m ? NaN : ((s ? -1 : 1) * Infinity)\n  } else {\n    m = m + Math.pow(2, mLen)\n    e = e - eBias\n  }\n  return (s ? -1 : 1) * m * Math.pow(2, e - mLen)\n}\n\nexports.write = function (buffer, value, offset, isLE, mLen, nBytes) {\n  var e, m, c\n  var eLen = (nBytes * 8) - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var rt = (mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0)\n  var i = isLE ? 0 : (nBytes - 1)\n  var d = isLE ? 1 : -1\n  var s = value < 0 || (value === 0 && 1 / value < 0) ? 1 : 0\n\n  value = Math.abs(value)\n\n  if (isNaN(value) || value === Infinity) {\n    m = isNaN(value) ? 1 : 0\n    e = eMax\n  } else {\n    e = Math.floor(Math.log(value) / Math.LN2)\n    if (value * (c = Math.pow(2, -e)) < 1) {\n      e--\n      c *= 2\n    }\n    if (e + eBias >= 1) {\n      value += rt / c\n    } else {\n      value += rt * Math.pow(2, 1 - eBias)\n    }\n    if (value * c >= 2) {\n      e++\n      c /= 2\n    }\n\n    if (e + eBias >= eMax) {\n      m = 0\n      e = eMax\n    } else if (e + eBias >= 1) {\n      m = ((value * c) - 1) * Math.pow(2, mLen)\n      e = e + eBias\n    } else {\n      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen)\n      e = 0\n    }\n  }\n\n  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}\n\n  e = (e << mLen) | m\n  eLen += mLen\n  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}\n\n  buffer[offset + i - d] |= s * 128\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/ieee754/index.js\n// module id = 7\n// module chunks = 0 1", "var toString = {}.toString;\n\nmodule.exports = Array.isArray || function (arr) {\n  return toString.call(arr) == '[object Array]';\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/isarray/index.js\n// module id = 8\n// module chunks = 0 1", "﻿(function (factory) {\r\n    if (typeof exports === 'object') {\r\n        // Node/CommonJS\r\n        module.exports = factory();\r\n    } else if (typeof define === 'function' && define.amd) {\r\n        // AMD\r\n        define(factory);\r\n    } else {\r\n        // Browser globals (with support for web workers)\r\n        var glob;\r\n\r\n        try {\r\n            glob = window;\r\n        } catch (e) {\r\n            glob = self;\r\n        }\r\n\r\n        glob.SparkMD5 = factory();\r\n    }\r\n}(function (undefined) {\r\n\r\n    'use strict';\r\n\r\n    ////////////////////////////////////////////////////////////////////////////\r\n\r\n    /*\r\n     * Fastest md5 implementation around (JKM md5)\r\n     * Credits: <PERSON>\r\n     *\r\n     * @see http://www.myersdaily.org/joseph/javascript/md5-text.html\r\n     * @see http://jsperf.com/md5-shootout/7\r\n     */\r\n\r\n    /* this function is much faster,\r\n      so if possible we use it. Some IEs\r\n      are the only ones I know of that\r\n      need the idiotic second function,\r\n      generated by an if clause.  */\r\n    var add32 = function (a, b) {\r\n        return (a + b) & 0xFFFFFFFF;\r\n    },\r\n\r\n    cmn = function (q, a, b, x, s, t) {\r\n        a = add32(add32(a, q), add32(x, t));\r\n        return add32((a << s) | (a >>> (32 - s)), b);\r\n    },\r\n\r\n    ff = function (a, b, c, d, x, s, t) {\r\n        return cmn((b & c) | ((~b) & d), a, b, x, s, t);\r\n    },\r\n\r\n    gg = function (a, b, c, d, x, s, t) {\r\n        return cmn((b & d) | (c & (~d)), a, b, x, s, t);\r\n    },\r\n\r\n    hh = function (a, b, c, d, x, s, t) {\r\n        return cmn(b ^ c ^ d, a, b, x, s, t);\r\n    },\r\n\r\n    ii = function (a, b, c, d, x, s, t) {\r\n        return cmn(c ^ (b | (~d)), a, b, x, s, t);\r\n    },\r\n\r\n    md5cycle = function (x, k) {\r\n        var a = x[0],\r\n            b = x[1],\r\n            c = x[2],\r\n            d = x[3];\r\n\r\n        a = ff(a, b, c, d, k[0], 7, -680876936);\r\n        d = ff(d, a, b, c, k[1], 12, -389564586);\r\n        c = ff(c, d, a, b, k[2], 17, 606105819);\r\n        b = ff(b, c, d, a, k[3], 22, -1044525330);\r\n        a = ff(a, b, c, d, k[4], 7, -176418897);\r\n        d = ff(d, a, b, c, k[5], 12, 1200080426);\r\n        c = ff(c, d, a, b, k[6], 17, -1473231341);\r\n        b = ff(b, c, d, a, k[7], 22, -45705983);\r\n        a = ff(a, b, c, d, k[8], 7, 1770035416);\r\n        d = ff(d, a, b, c, k[9], 12, -1958414417);\r\n        c = ff(c, d, a, b, k[10], 17, -42063);\r\n        b = ff(b, c, d, a, k[11], 22, -1990404162);\r\n        a = ff(a, b, c, d, k[12], 7, 1804603682);\r\n        d = ff(d, a, b, c, k[13], 12, -40341101);\r\n        c = ff(c, d, a, b, k[14], 17, -1502002290);\r\n        b = ff(b, c, d, a, k[15], 22, 1236535329);\r\n\r\n        a = gg(a, b, c, d, k[1], 5, -165796510);\r\n        d = gg(d, a, b, c, k[6], 9, -1069501632);\r\n        c = gg(c, d, a, b, k[11], 14, 643717713);\r\n        b = gg(b, c, d, a, k[0], 20, -373897302);\r\n        a = gg(a, b, c, d, k[5], 5, -701558691);\r\n        d = gg(d, a, b, c, k[10], 9, 38016083);\r\n        c = gg(c, d, a, b, k[15], 14, -660478335);\r\n        b = gg(b, c, d, a, k[4], 20, -405537848);\r\n        a = gg(a, b, c, d, k[9], 5, 568446438);\r\n        d = gg(d, a, b, c, k[14], 9, -1019803690);\r\n        c = gg(c, d, a, b, k[3], 14, -187363961);\r\n        b = gg(b, c, d, a, k[8], 20, 1163531501);\r\n        a = gg(a, b, c, d, k[13], 5, -1444681467);\r\n        d = gg(d, a, b, c, k[2], 9, -51403784);\r\n        c = gg(c, d, a, b, k[7], 14, 1735328473);\r\n        b = gg(b, c, d, a, k[12], 20, -1926607734);\r\n\r\n        a = hh(a, b, c, d, k[5], 4, -378558);\r\n        d = hh(d, a, b, c, k[8], 11, -2022574463);\r\n        c = hh(c, d, a, b, k[11], 16, 1839030562);\r\n        b = hh(b, c, d, a, k[14], 23, -35309556);\r\n        a = hh(a, b, c, d, k[1], 4, -1530992060);\r\n        d = hh(d, a, b, c, k[4], 11, 1272893353);\r\n        c = hh(c, d, a, b, k[7], 16, -155497632);\r\n        b = hh(b, c, d, a, k[10], 23, -1094730640);\r\n        a = hh(a, b, c, d, k[13], 4, 681279174);\r\n        d = hh(d, a, b, c, k[0], 11, -358537222);\r\n        c = hh(c, d, a, b, k[3], 16, -722521979);\r\n        b = hh(b, c, d, a, k[6], 23, 76029189);\r\n        a = hh(a, b, c, d, k[9], 4, -640364487);\r\n        d = hh(d, a, b, c, k[12], 11, -421815835);\r\n        c = hh(c, d, a, b, k[15], 16, 530742520);\r\n        b = hh(b, c, d, a, k[2], 23, -995338651);\r\n\r\n        a = ii(a, b, c, d, k[0], 6, -198630844);\r\n        d = ii(d, a, b, c, k[7], 10, 1126891415);\r\n        c = ii(c, d, a, b, k[14], 15, -1416354905);\r\n        b = ii(b, c, d, a, k[5], 21, -57434055);\r\n        a = ii(a, b, c, d, k[12], 6, 1700485571);\r\n        d = ii(d, a, b, c, k[3], 10, -1894986606);\r\n        c = ii(c, d, a, b, k[10], 15, -1051523);\r\n        b = ii(b, c, d, a, k[1], 21, -2054922799);\r\n        a = ii(a, b, c, d, k[8], 6, 1873313359);\r\n        d = ii(d, a, b, c, k[15], 10, -30611744);\r\n        c = ii(c, d, a, b, k[6], 15, -1560198380);\r\n        b = ii(b, c, d, a, k[13], 21, 1309151649);\r\n        a = ii(a, b, c, d, k[4], 6, -145523070);\r\n        d = ii(d, a, b, c, k[11], 10, -1120210379);\r\n        c = ii(c, d, a, b, k[2], 15, 718787259);\r\n        b = ii(b, c, d, a, k[9], 21, -343485551);\r\n\r\n        x[0] = add32(a, x[0]);\r\n        x[1] = add32(b, x[1]);\r\n        x[2] = add32(c, x[2]);\r\n        x[3] = add32(d, x[3]);\r\n    },\r\n\r\n    /* there needs to be support for Unicode here,\r\n       * unless we pretend that we can redefine the MD-5\r\n       * algorithm for multi-byte characters (perhaps\r\n       * by adding every four 16-bit characters and\r\n       * shortening the sum to 32 bits). Otherwise\r\n       * I suggest performing MD-5 as if every character\r\n       * was two bytes--e.g., 0040 0025 = @%--but then\r\n       * how will an ordinary MD-5 sum be matched?\r\n       * There is no way to standardize text to something\r\n       * like UTF-8 before transformation; speed cost is\r\n       * utterly prohibitive. The JavaScript standard\r\n       * itself needs to look at this: it should start\r\n       * providing access to strings as preformed UTF-8\r\n       * 8-bit unsigned value arrays.\r\n       */\r\n    md5blk = function (s) {\r\n        var md5blks = [],\r\n            i; /* Andy King said do it this way. */\r\n\r\n        for (i = 0; i < 64; i += 4) {\r\n            md5blks[i >> 2] = s.charCodeAt(i) + (s.charCodeAt(i + 1) << 8) + (s.charCodeAt(i + 2) << 16) + (s.charCodeAt(i + 3) << 24);\r\n        }\r\n        return md5blks;\r\n    },\r\n\r\n    md5blk_array = function (a) {\r\n        var md5blks = [],\r\n            i; /* Andy King said do it this way. */\r\n\r\n        for (i = 0; i < 64; i += 4) {\r\n            md5blks[i >> 2] = a[i] + (a[i + 1] << 8) + (a[i + 2] << 16) + (a[i + 3] << 24);\r\n        }\r\n        return md5blks;\r\n    },\r\n\r\n    md51 = function (s) {\r\n        var n = s.length,\r\n            state = [1732584193, -271733879, -1732584194, 271733878],\r\n            i,\r\n            length,\r\n            tail,\r\n            tmp,\r\n            lo,\r\n            hi;\r\n\r\n        for (i = 64; i <= n; i += 64) {\r\n            md5cycle(state, md5blk(s.substring(i - 64, i)));\r\n        }\r\n        s = s.substring(i - 64);\r\n        length = s.length;\r\n        tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\r\n        for (i = 0; i < length; i += 1) {\r\n            tail[i >> 2] |= s.charCodeAt(i) << ((i % 4) << 3);\r\n        }\r\n        tail[i >> 2] |= 0x80 << ((i % 4) << 3);\r\n        if (i > 55) {\r\n            md5cycle(state, tail);\r\n            for (i = 0; i < 16; i += 1) {\r\n                tail[i] = 0;\r\n            }\r\n        }\r\n\r\n        // Beware that the final length might not fit in 32 bits so we take care of that\r\n        tmp = n * 8;\r\n        tmp = tmp.toString(16).match(/(.*?)(.{0,8})$/);\r\n        lo = parseInt(tmp[2], 16);\r\n        hi = parseInt(tmp[1], 16) || 0;\r\n\r\n        tail[14] = lo;\r\n        tail[15] = hi;\r\n\r\n        md5cycle(state, tail);\r\n        return state;\r\n    },\r\n\r\n    md51_array = function (a) {\r\n        var n = a.length,\r\n            state = [1732584193, -271733879, -1732584194, 271733878],\r\n            i,\r\n            length,\r\n            tail,\r\n            tmp,\r\n            lo,\r\n            hi;\r\n\r\n        for (i = 64; i <= n; i += 64) {\r\n            md5cycle(state, md5blk_array(a.subarray(i - 64, i)));\r\n        }\r\n\r\n        // Not sure if it is a bug, however IE10 will always produce a sub array of length 1\r\n        // containing the last element of the parent array if the sub array specified starts\r\n        // beyond the length of the parent array - weird.\r\n        // https://connect.microsoft.com/IE/feedback/details/771452/typed-array-subarray-issue\r\n        a = (i - 64) < n ? a.subarray(i - 64) : new Uint8Array(0);\r\n\r\n        length = a.length;\r\n        tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\r\n        for (i = 0; i < length; i += 1) {\r\n            tail[i >> 2] |= a[i] << ((i % 4) << 3);\r\n        }\r\n\r\n        tail[i >> 2] |= 0x80 << ((i % 4) << 3);\r\n        if (i > 55) {\r\n            md5cycle(state, tail);\r\n            for (i = 0; i < 16; i += 1) {\r\n                tail[i] = 0;\r\n            }\r\n        }\r\n\r\n        // Beware that the final length might not fit in 32 bits so we take care of that\r\n        tmp = n * 8;\r\n        tmp = tmp.toString(16).match(/(.*?)(.{0,8})$/);\r\n        lo = parseInt(tmp[2], 16);\r\n        hi = parseInt(tmp[1], 16) || 0;\r\n\r\n        tail[14] = lo;\r\n        tail[15] = hi;\r\n\r\n        md5cycle(state, tail);\r\n\r\n        return state;\r\n    },\r\n\r\n    hex_chr = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'],\r\n\r\n    rhex = function (n) {\r\n        var s = '',\r\n            j;\r\n        for (j = 0; j < 4; j += 1) {\r\n            s += hex_chr[(n >> (j * 8 + 4)) & 0x0F] + hex_chr[(n >> (j * 8)) & 0x0F];\r\n        }\r\n        return s;\r\n    },\r\n\r\n    hex = function (x) {\r\n        var i;\r\n        for (i = 0; i < x.length; i += 1) {\r\n            x[i] = rhex(x[i]);\r\n        }\r\n        return x.join('');\r\n    },\r\n\r\n    md5 = function (s) {\r\n        return hex(md51(s));\r\n    },\r\n\r\n\r\n\r\n    ////////////////////////////////////////////////////////////////////////////\r\n\r\n    /**\r\n     * SparkMD5 OOP implementation.\r\n     *\r\n     * Use this class to perform an incremental md5, otherwise use the\r\n     * static methods instead.\r\n     */\r\n    SparkMD5 = function () {\r\n        // call reset to init the instance\r\n        this.reset();\r\n    };\r\n\r\n\r\n    // In some cases the fast add32 function cannot be used..\r\n    if (md5('hello') !== '5d41402abc4b2a76b9719d911017c592') {\r\n        add32 = function (x, y) {\r\n            var lsw = (x & 0xFFFF) + (y & 0xFFFF),\r\n                msw = (x >> 16) + (y >> 16) + (lsw >> 16);\r\n            return (msw << 16) | (lsw & 0xFFFF);\r\n        };\r\n    }\r\n\r\n\r\n    /**\r\n     * Appends a string.\r\n     * A conversion will be applied if an utf8 string is detected.\r\n     *\r\n     * @param {String} str The string to be appended\r\n     *\r\n     * @return {SparkMD5} The instance itself\r\n     */\r\n    SparkMD5.prototype.append = function (str) {\r\n        // converts the string to utf8 bytes if necessary\r\n        if (/[\\u0080-\\uFFFF]/.test(str)) {\r\n            str = unescape(encodeURIComponent(str));\r\n        }\r\n\r\n        // then append as binary\r\n        this.appendBinary(str);\r\n\r\n        return this;\r\n    };\r\n\r\n    /**\r\n     * Appends a binary string.\r\n     *\r\n     * @param {String} contents The binary string to be appended\r\n     *\r\n     * @return {SparkMD5} The instance itself\r\n     */\r\n    SparkMD5.prototype.appendBinary = function (contents) {\r\n        this._buff += contents;\r\n        this._length += contents.length;\r\n\r\n        var length = this._buff.length,\r\n            i;\r\n\r\n        for (i = 64; i <= length; i += 64) {\r\n            md5cycle(this._state, md5blk(this._buff.substring(i - 64, i)));\r\n        }\r\n\r\n        this._buff = this._buff.substr(i - 64);\r\n\r\n        return this;\r\n    };\r\n\r\n    /**\r\n     * Finishes the incremental computation, reseting the internal state and\r\n     * returning the result.\r\n     * Use the raw parameter to obtain the raw result instead of the hex one.\r\n     *\r\n     * @param {Boolean} raw True to get the raw result, false to get the hex result\r\n     *\r\n     * @return {String|Array} The result\r\n     */\r\n    SparkMD5.prototype.end = function (raw) {\r\n        var buff = this._buff,\r\n            length = buff.length,\r\n            i,\r\n            tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],\r\n            ret;\r\n\r\n        for (i = 0; i < length; i += 1) {\r\n            tail[i >> 2] |= buff.charCodeAt(i) << ((i % 4) << 3);\r\n        }\r\n\r\n        this._finish(tail, length);\r\n        ret = !!raw ? this._state : hex(this._state);\r\n\r\n        this.reset();\r\n\r\n        return ret;\r\n    };\r\n\r\n    /**\r\n     * Finish the final calculation based on the tail.\r\n     *\r\n     * @param {Array}  tail   The tail (will be modified)\r\n     * @param {Number} length The length of the remaining buffer\r\n     */\r\n    SparkMD5.prototype._finish = function (tail, length) {\r\n        var i = length,\r\n            tmp,\r\n            lo,\r\n            hi;\r\n\r\n        tail[i >> 2] |= 0x80 << ((i % 4) << 3);\r\n        if (i > 55) {\r\n            md5cycle(this._state, tail);\r\n            for (i = 0; i < 16; i += 1) {\r\n                tail[i] = 0;\r\n            }\r\n        }\r\n\r\n        // Do the final computation based on the tail and length\r\n        // Beware that the final length may not fit in 32 bits so we take care of that\r\n        tmp = this._length * 8;\r\n        tmp = tmp.toString(16).match(/(.*?)(.{0,8})$/);\r\n        lo = parseInt(tmp[2], 16);\r\n        hi = parseInt(tmp[1], 16) || 0;\r\n\r\n        tail[14] = lo;\r\n        tail[15] = hi;\r\n        md5cycle(this._state, tail);\r\n    };\r\n\r\n    /**\r\n     * Resets the internal state of the computation.\r\n     *\r\n     * @return {SparkMD5} The instance itself\r\n     */\r\n    SparkMD5.prototype.reset = function () {\r\n        this._buff = '';\r\n        this._length = 0;\r\n        this._state = [1732584193, -271733879, -1732584194, 271733878];\r\n\r\n        return this;\r\n    };\r\n\r\n    /**\r\n     * Releases memory used by the incremental buffer and other aditional\r\n     * resources. If you plan to use the instance again, use reset instead.\r\n     */\r\n    SparkMD5.prototype.destroy = function () {\r\n        delete this._state;\r\n        delete this._buff;\r\n        delete this._length;\r\n    };\r\n\r\n\r\n    /**\r\n     * Performs the md5 hash on a string.\r\n     * A conversion will be applied if utf8 string is detected.\r\n     *\r\n     * @param {String}  str The string\r\n     * @param {Boolean} raw True to get the raw result, false to get the hex result\r\n     *\r\n     * @return {String|Array} The result\r\n     */\r\n    SparkMD5.hash = function (str, raw) {\r\n        // converts the string to utf8 bytes if necessary\r\n        if (/[\\u0080-\\uFFFF]/.test(str)) {\r\n            str = unescape(encodeURIComponent(str));\r\n        }\r\n\r\n        var hash = md51(str);\r\n\r\n        return !!raw ? hash : hex(hash);\r\n    };\r\n\r\n    /**\r\n     * Performs the md5 hash on a binary string.\r\n     *\r\n     * @param {String}  content The binary string\r\n     * @param {Boolean} raw     True to get the raw result, false to get the hex result\r\n     *\r\n     * @return {String|Array} The result\r\n     */\r\n    SparkMD5.hashBinary = function (content, raw) {\r\n        var hash = md51(content);\r\n\r\n        return !!raw ? hash : hex(hash);\r\n    };\r\n\r\n    /**\r\n     * SparkMD5 OOP implementation for array buffers.\r\n     *\r\n     * Use this class to perform an incremental md5 ONLY for array buffers.\r\n     */\r\n    SparkMD5.ArrayBuffer = function () {\r\n        // call reset to init the instance\r\n        this.reset();\r\n    };\r\n\r\n    ////////////////////////////////////////////////////////////////////////////\r\n\r\n    /**\r\n     * Appends an array buffer.\r\n     *\r\n     * @param {ArrayBuffer} arr The array to be appended\r\n     *\r\n     * @return {SparkMD5.ArrayBuffer} The instance itself\r\n     */\r\n    SparkMD5.ArrayBuffer.prototype.append = function (arr) {\r\n        // TODO: we could avoid the concatenation here but the algorithm would be more complex\r\n        //       if you find yourself needing extra performance, please make a PR.\r\n        var buff = this._concatArrayBuffer(this._buff, arr),\r\n            length = buff.length,\r\n            i;\r\n\r\n        this._length += arr.byteLength;\r\n\r\n        for (i = 64; i <= length; i += 64) {\r\n            md5cycle(this._state, md5blk_array(buff.subarray(i - 64, i)));\r\n        }\r\n\r\n        // Avoids IE10 weirdness (documented above)\r\n        this._buff = (i - 64) < length ? buff.subarray(i - 64) : new Uint8Array(0);\r\n\r\n        return this;\r\n    };\r\n\r\n    /**\r\n     * Finishes the incremental computation, reseting the internal state and\r\n     * returning the result.\r\n     * Use the raw parameter to obtain the raw result instead of the hex one.\r\n     *\r\n     * @param {Boolean} raw True to get the raw result, false to get the hex result\r\n     *\r\n     * @return {String|Array} The result\r\n     */\r\n    SparkMD5.ArrayBuffer.prototype.end = function (raw) {\r\n        var buff = this._buff,\r\n            length = buff.length,\r\n            tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],\r\n            i,\r\n            ret;\r\n\r\n        for (i = 0; i < length; i += 1) {\r\n            tail[i >> 2] |= buff[i] << ((i % 4) << 3);\r\n        }\r\n\r\n        this._finish(tail, length);\r\n        ret = !!raw ? this._state : hex(this._state);\r\n\r\n        this.reset();\r\n\r\n        return ret;\r\n    };\r\n\r\n    SparkMD5.ArrayBuffer.prototype._finish = SparkMD5.prototype._finish;\r\n\r\n    /**\r\n     * Resets the internal state of the computation.\r\n     *\r\n     * @return {SparkMD5.ArrayBuffer} The instance itself\r\n     */\r\n    SparkMD5.ArrayBuffer.prototype.reset = function () {\r\n        this._buff = new Uint8Array(0);\r\n        this._length = 0;\r\n        this._state = [1732584193, -271733879, -1732584194, 271733878];\r\n\r\n        return this;\r\n    };\r\n\r\n    /**\r\n     * Releases memory used by the incremental buffer and other aditional\r\n     * resources. If you plan to use the instance again, use reset instead.\r\n     */\r\n    SparkMD5.ArrayBuffer.prototype.destroy = SparkMD5.prototype.destroy;\r\n\r\n    /**\r\n     * Concats two array buffers, returning a new one.\r\n     *\r\n     * @param  {ArrayBuffer} first  The first array buffer\r\n     * @param  {ArrayBuffer} second The second array buffer\r\n     *\r\n     * @return {ArrayBuffer} The new array buffer\r\n     */\r\n    SparkMD5.ArrayBuffer.prototype._concatArrayBuffer = function (first, second) {\r\n        var firstLength = first.length,\r\n            result = new Uint8Array(firstLength + second.byteLength);\r\n\r\n        result.set(first);\r\n        result.set(new Uint8Array(second), firstLength);\r\n\r\n        return result;\r\n    };\r\n\r\n    /**\r\n     * Performs the md5 hash on an array buffer.\r\n     *\r\n     * @param {ArrayBuffer} arr The array buffer\r\n     * @param {Boolean}     raw True to get the raw result, false to get the hex result\r\n     *\r\n     * @return {String|Array} The result\r\n     */\r\n    SparkMD5.ArrayBuffer.hash = function (arr, raw) {\r\n        var hash = md51_array(new Uint8Array(arr));\r\n\r\n        return !!raw ? hash : hex(hash);\r\n    };\r\n\r\n    return SparkMD5;\r\n}));\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/libs/md5/spark-md5.js\n// module id = 9\n// module chunks = 0 1", "import commonUtil from \"@/common/commonUtil\";\r\n\r\nvar globalUtil = require('@/common/globalUtil.js').default;\r\nvar httpUtil = require('@/common/httpUtil.js').default;\r\n\r\nvar vtube = function (mainOpts) {\r\n    var self = this;\r\n    var baseUrl = 'http://127.0.0.1:8084/';\r\n\r\n    function getConfig(){\r\n        if (window.nxt && window.nxt.config)\r\n        {\r\n            return window.nxt.config;\r\n        }\r\n        else if (mainOpts.configInst)\r\n        {\r\n            return mainOpts.configInst;\r\n        }\r\n    }\r\n\r\n    function getCompleteUrl() {\r\n        var callUrl = 'http://' + location.host + '/';\r\n        // if (nxt.config.server != '') {\r\n        //     callUrl = callUrl + nxt.config.server + '/';\r\n        // }\r\n        return callUrl + 'upload/complete/?user_token=';\r\n    }\r\n\r\n    function getRequestParams(task) {\r\n        var res = {\r\n            TaskName: task.metadata.name,\r\n            TaskGuid: task.taskId,\r\n            TransType: 'Upload',\r\n            TransFile: [],\r\n            UserInfo: {\r\n                UserId: nxt.user.current.id.toString(),\r\n                UserName: nxt.user.current.nickName || nxt.user.current.loginName,\r\n                UserCode: nxt.user.current.userCode,\r\n            },\r\n            ExtendeAttr: []\r\n        };\r\n        if (getConfig().vtubeInfo.importType == 1) {\r\n            res.CallBackUrl = getCompleteUrl();\r\n        }\r\n        if (getConfig().vtubeInfo.usedConfig) {\r\n            res.ServerInfo = {\r\n                HostName: getConfig().vtubeInfo.address,\r\n                Port: parseInt(getConfig().vtubeInfo.port),\r\n                Scheme: 'FTP',\r\n                UserName: getConfig().vtubeInfo.userName,\r\n                Password: getConfig().vtubeInfo.password,\r\n                PathRoot: ''\r\n            };\r\n        }\r\n        if (getConfig().storageType == 2) {\r\n            res.ServerInfo = {\r\n                HostName: '',\r\n                Port: 0,\r\n                Scheme: 'ALIS3',\r\n                UserName: '',\r\n                Password: '',\r\n                PathRoot: ''\r\n            };\r\n        }\r\n        res.ExtendeAttr = _.map(task.metadata.field, function (item) {\r\n            var n = { ItemCode: item.fieldName, ItemName: item.alias };\r\n            if (item.controlType == 8) {\r\n                if (item.value != null && item.value != '' && item.value != '[]') {\r\n                    try {\r\n                        n.Value = JSON.parse(item.value)[0];\r\n                    } catch (e) {\r\n                        n.Value = '';\r\n                    }\r\n                } else {\r\n                    n.Value = '';\r\n                }\r\n            } else {\r\n                n.Value = item.value || '';\r\n            }\r\n            return n;\r\n        });\r\n        res.ExtendeAttr.push({\r\n            ItemCode: 'tree',\r\n            ItemName: '目录树',\r\n            Value: task.tree\r\n        });\r\n        _.remove(res.ExtendeAttr, { ItemCode: 'cliptype' });\r\n        res.ExtendeAttr.push({\r\n            ItemCode: 'cliptype',\r\n            ItemName: '素材类型',\r\n            Value: task.entityType\r\n        });\r\n        return res;\r\n    }\r\n\r\n    this.openFileSelector = function (callback) {\r\n        $.ajax({\r\n            url: baseUrl + 'request/getuploadfiles?user_token=&filetype=all',\r\n            success: function (res) {\r\n                var data = _.isString(res) ? JSON.parse(res) : res;\r\n                if (data.FileCount === 0)\r\n                    return;\r\n                var result = [];\r\n                _.forEach(data.FileList, function (item) {\r\n                    var ext = '.' + commonUtil.getExtension(item.FilePath);\r\n                    result.push({\r\n                        entityType: commonUtil.getTypeByExt(ext, mainOpts.configInst).code,\r\n                        fileName: commonUtil.getFullFileName(item.FilePath),\r\n                        metadata: {\r\n                            name: commonUtil.getFileName(item.FilePath),\r\n                            ext: ext\r\n                        },\r\n                        status: 'added',\r\n                        progress: 0,\r\n                        file: item\r\n                    });\r\n                });\r\n                callback(result);\r\n            },\r\n            error: function (res) {\r\n                console.error(res);\r\n                var vtubeDownloadPath = getConfig().vtubeDownloadPath || getConfig().server + '/assets/Sobey_vRocket_v2.0_Setup.exe'\r\n                commonUtil.prompt(l('upload.clientInstallTip', '<p>打开客户端失败</p><p>请确定已经正常开启客户端或重启客户端再重试。</p><p>如没有安装，请点击下面地址。</p><p><a href=\"${path}\"><i class=\"icon iconfont icon-iconfontxiazai\" style=\"margin-right:5px\"></i>点击客户端下载</a></p>', { path : vtubeDownloadPath}));\r\n            }\r\n        });\r\n    }\r\n\r\n    this.createTask = function (dto, params) {\r\n        var list = [];\r\n        switch (params.taskType) {\r\n            case 1: //普通类型\r\n                if (!_.isArray(dto)) {\r\n                    dto = [dto];\r\n                }\r\n                _.forEach(dto, function (task) {\r\n                    if (task.file) {\r\n                        task.files = [{\r\n                            file: task.file,\r\n                            fileName: task.fileName,\r\n                            fileSize: task.file.FileSize\r\n                        }];\r\n                        delete task.file;\r\n                        list.push(task);\r\n                    }\r\n                    else if (task.files && task.files.length > 0) {\r\n                        task.files = _.map(task.files, function (file) {\r\n                            return {\r\n                                file : file.file,\r\n                                fileName : file.fileName,\r\n                                fileSize : file.fileSize\r\n                            };\r\n                        });\r\n                        list.push(task);\r\n                    }\r\n                });\r\n                break;\r\n            case 2:\r\n            case 3: //普通类型\r\n                list.push(dto);\r\n                break;\r\n        }\r\n        _.forEach(list, function (task) {\r\n            task.taskType = params.taskType;\r\n            task.transferType = params.transferType;\r\n            task.targetFolder = params.targetFolder;\r\n            task.relationContentType = params.relationContentType;\r\n            task.relationContentId = params.relationContentId;\r\n            self.addTask(task);\r\n        });\r\n    }\r\n\r\n    this.addTask = function (task) {\r\n        var url = getConfig().server + '/upload/multipart/init';\r\n        if (mainOpts.loginToken)\r\n        {\r\n            url += \"?token=\" + mainOpts.loginToken;\r\n        }\r\n        httpUtil.post(url, task).then(function (res) {\r\n            res = res.data;\r\n            var parms = getRequestParams(res);\r\n            parms.ExtendData = res.userCode;\r\n            function getPath(file) {\r\n                if (_.isString(file.location) && file.location.indexOf('oss:') == -1) {\r\n                    return getConfig().vtubeInfo.path + file.location.replace(/\\\\/g, '/');\r\n                }\r\n                return file.location;\r\n            }\r\n\r\n            switch (task.taskType) {\r\n                case 1:\r\n                    parms.TransFile.push({\r\n                        SourceFile: task.files[0].file.FilePath,\r\n                        DestFile: getPath(res.files[0])\r\n                    });\r\n                    break;\r\n                case 2:\r\n                case 3:\r\n                    _.forEach(res.files, function (file) {\r\n                        parms.TransFile.push({\r\n                            SourceFile: file.fileName,\r\n                            DestFile: getPath(file)\r\n                        });\r\n                    });\r\n                    break;\r\n                default:\r\n            }\r\n            $.ajax({\r\n                url: baseUrl + 'request/addtask?user_token=',\r\n                type: 'POST',\r\n                contentType: 'application/json',\r\n                data: JSON.stringify(parms),\r\n                success: function (result) {\r\n                    if (typeof(result) === 'string')\r\n                    {\r\n                        result = JSON.parse(result);\r\n                    }\r\n                    if (result.Result == 1) {\r\n                        commonUtil.msgOk(l('upload.addTaskOk', '添加任务成功'));\r\n                    } else {\r\n                        commonUtil.prompt(l('upload.addTaskError', '添加任务失败：') + result.Msg);\r\n                    }\r\n                    console.info(result);\r\n                },\r\n                error: function (result) {\r\n                    console.info(result);\r\n                    commonUtil.prompt(l('upload.addTaskError', '添加任务失败'));\r\n                }\r\n            });\r\n        }, function (res) {\r\n            commonUtil.prompt(l('upload.uploadError', '上传失败：') + res.data.desc);\r\n        });\r\n    }\r\n}\r\n\r\nexport default vtube;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/core/vtubeTransfer.js\n// module id = 10\n// module chunks = 0 1", "var web = require('./core/webTransfer.js');\r\nvar vtube = require('./core/vtubeTransfer.js');\r\n\r\nvar mamUpload = {\r\n    init: function(options){\r\n        mamUpload.web = new web.default(options);\r\n        mamUpload.vtube = new vtube.default(options);\r\n    }\r\n}\r\n\r\nwindow.mamUpload = mamUpload;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/indexPure.js\n// module id = 28\n// module chunks = 1"], "sourceRoot": ""}