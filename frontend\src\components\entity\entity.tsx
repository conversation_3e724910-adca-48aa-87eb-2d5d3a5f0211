import React, { FC, useEffect, useMemo, useRef, useState } from 'react';
import Video from './video/video';
import Picture from './picture/picture';
import Documnet from './document/document';
import PDF from '@/components/entity/pdf';
import HTML from '@/components/entity/html';
import Error from './error/error';
import WPS from '@/components/entity/wps';
import './style.less';
import { entityType } from '@/types/entityTypes';
import { useIntl } from 'umi';
import Audio from '@/components/h5player/audio';
import Xmind from '@/components/entity/xmind';
import { H5PlayerProps } from '@/components/h5player';
import { H5Player } from '@/components';
import { DocumentEditor } from '@onlyoffice/document-editor-react';
import CryptoJS from 'crypto-js';
import { IPermission } from '@/models/permission';
import globalParams from '@/permission/globalParams';
import { useSelector, useParams } from 'umi';
import rmanApis from '@/service/rman';
interface IEntityProps extends H5PlayerProps {
  type: entityType;
  src: string;
  mobileFlag?: boolean;
  controlBox?: boolean;
  keyframes?: string;
  frameRate?: number;
  shareFlag?: boolean;
}

const isHtml = (src: string) =>
  src && (src.indexOf('.html') > -1 || src.indexOf('.htm') > -1);

const Entity: FC<IEntityProps> = ({
  type,
  id,
  src,
  substitles,
  controlBox,
  frameRate,
  shareFlag,
  onSuccess,
  setSection,
  getKeyframe,
  translateFlag,
  onPlayChange,
  modifySection,
  languageList,
  errorCode,
  voice,
  resetSection,
  config,
  isEditPermission,
  contendId,
  contrast,
  brightness,
  saturate,
  blur,
  grayscale,
  invert,
  opacity,
  sepia,
  hueRotate,
  zhFlag,
  enFlag,
  mobileFlag
}) => {
  const [error, setError] = useState<boolean>(false);
  const intl = useIntl();
  
  const { rmanGlobalParameter } = useSelector<{ permission: any }, IPermission>(({ permission }) => permission);
  const userInfo = (window as any).login_useInfo
  const [linkWatermark, setLinkWatermark] = useState<string>('');
  const params = useParams<{ contentId: string }>();
  const onDocumentReady = (e: any) => {
    console.log('onDocumentReady', e);
  };
  const getDocumentType = (fileExtension: string) => {
    const w = [
      'doc',
      'docm',
      'docx',
      'docxf',
      'dot',
      'dotm',
      'dotx',
      'epub',
      'fodt',
      'fb2',
      'htm',
      'html',
      'mht',
      'odt',
      'oform',
      'ott',
      'oxps',
      'pdf',
      'rtf',
      'txt',
      'djvu',
      'xml',
      'xps',
    ];
    const c = [
      'csv',
      'fods',
      'ods',
      'ots',
      'xls',
      'xlsb',
      'xlsm',
      'xlsx',
      'xlt',
      'xltm',
      'xltx',
    ];
    const s = [
      'fodp',
      'odp',
      'otp',
      'pot',
      'potm',
      'potx',
      'pps',
      'ppsm',
      'ppsx',
      'ppt',
      'pptm',
      'pptx',
    ];
    let documentType = '';
    if (w.indexOf(fileExtension) > -1) {
      documentType = 'word';
    } else if (c.indexOf(fileExtension) > -1) {
      documentType = 'cell';
    } else if (s.indexOf(fileExtension) > -1) {
      documentType = 'slide';
    }
    return documentType;
  };
  const getFileTypeByPath = (path: string) => {
    const href = path.split('?')[0];
    const index = href.lastIndexOf('.'); // lastIndexOf("/")  找到最后一个  /  的位置
    const fileType = href.substring(index + 1); // substr() 截取剩余的字符，即文件名doc
    // console.log(fileType);
    return fileType;
  };
  const getFileNameByPath = (path: string) => {
    const href = path.split('?')[0];
    const index = href.lastIndexOf('/'); // lastIndexOf("/")  找到最后一个  /  的位置
    const fileName = href.substring(index + 1); // substr() 截取剩余的字符，即得文件名xxx.doc
    return fileName;
  };

  const base64ToUrlData = (base64data: string) => {
    const base64UrlData = base64data
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/\=/g, '');
    return base64UrlData;
  };

  const getBase64UrlData = (data: any) => {
    let dataStr = '';
    if (typeof data == 'string') {
      dataStr = data;
    } else {
      dataStr = JSON.stringify(data);
    }
    dataStr = CryptoJS.enc.Utf8.parse(dataStr);
    let base64UrlData = base64ToUrlData(CryptoJS.enc.Base64.stringify(dataStr));
    return base64UrlData;
  };

  const timestamp = () => {
    let date = new Date();
    date.setMinutes(date.getMinutes() + 5);
    let outcome = Math.round(date.getTime() / 1000).toString();
    return outcome;
  };
  const jwtSign = function(payload: any, secretKey: string) {
    // key = CryptoJS.enc.Utf8.parse(key);
    let header = '{"alg":"HS256","typ":"JWT","exp":' + timestamp() + '}';
    let headerBase64 = getBase64UrlData(header);
    let payloadBase64 = getBase64UrlData(payload);
    let base64Token = headerBase64 + '.' + payloadBase64;
    var base64Signature = base64ToUrlData(
      CryptoJS.HmacSHA256(base64Token, secretKey).toString(CryptoJS.enc.Base64),
    );
    let jwt = base64Token + '.' + base64Signature;
    return jwt;
  };
  //console.log('src', src, type);
  const config_document: any = {
    document: {
      fileType: getFileTypeByPath(src),
      key: contendId,
      title: decodeURIComponent(getFileNameByPath(src)),
      // url: src.includes('http:') ? src : 'https://course.ynu.edu.cn/' + src,
      //debug
      url: src.includes('http') ? src : location.origin + src,
      //debug
    },
    documentType: getDocumentType(getFileTypeByPath(src)),
    height: '100%',
    type: mobileFlag?'mobile': 'desktop', //desktop mobile embedded
    width: '100%',
    lang: 'zh-cn',
    editorConfig: {
      // "coEditing": {
      //     "mode": "fast",
      //     "change": true
      // },
      lang: 'zh-cn',
      region: 'zh-CN',
      location: 'cn',
      mode: 'view', //view edit
      user: {
        // "group": "Group1",
        id: (window as any).login_useInfo?.userCode,
        name: (window as any).login_useInfo?.nickName,
      },
      plugins: {},
      customization: {
        plugins: false,
        help: false,
        hideNotes: true,
        comments: true,
        chat: false,
        integrationMode: 'embed',
        logo: {
          image:
            '/documentserver/web-apps/apps/common/main/resources/img/header/header-logo_s.png',
          imageDark:
            '/documentserver/web-apps/apps/common/main/resources/img/header/dark-logo_s.png',
          url: 'http://www.sobey.com',
        },
        uiTheme: 'theme-classic-light', //theme-light, theme-classic-light, theme-dark, theme-contrast-dark default-dark, default-light default:theme-classic-light
      },
    },
  };
  config_document.token = jwtSign(config_document, 'tUVvbKk2mX4T40MBRrtj');
  // console.log("config", config_document);

  useEffect(()=>{
      // 水印
      if (params.contentId?.split('_')[2]) {
        rmanApis.getLinkWatermark(params.contentId.split('_')[2]).then((res)=>{
          if(res?.success) {
            res?.data?.showWatermarkText ? setLinkWatermark(res?.data.watermarkText || '') : setLinkWatermark('')
            
          }
        })
      } else {
        setLinkWatermark(userInfo?.userCode + ' ' +  userInfo?.nickName)
      }
  }, [])

  const render = () => {
    if (error || errorCode) {
      return <Error errorCode={errorCode} />;
    }
    console.log('src', src);
    switch (type) {
      case 'video':
        return (
          <H5Player
            brightness={brightness}
            contrast={contrast}
            saturate={saturate}
            blur={blur}
            grayscale={grayscale}
            invert={invert}
            controlBox={controlBox}
            opacity={opacity}
            sepia={sepia}
            substitles={substitles}
            languageList={languageList}
            voice={voice}
            hueRotate={hueRotate}
            src={src}
            id={id}
            zhFlag={zhFlag}
            enFlag={enFlag}
            config={config}
            onError={() => setError(true)}
            onSuccess={onSuccess}
            frameRate={frameRate}
            setSection={setSection}
            translateFlag={translateFlag}
            getKeyframe={getKeyframe}
            onPlayChange={onPlayChange}
            modifySection={modifySection}
            resetSection={resetSection}
            isEditPermission={isEditPermission}
            contendId={contendId}
            shareFlag={shareFlag}
            linkWatermark={linkWatermark}
          />
        );
      case 'audio':
        return (
          <Audio
            src={src}
            config={config}
            setSection={setSection}
            onSuccess={onSuccess}
            modifySection={modifySection}
            resetSection={resetSection}
            isEditPermission={isEditPermission}
            onError={() => setError(true)}
            linkWatermark={linkWatermark}
          />
        );
      case 'picture':
        return <Picture src={src} onError={() => setError(true)} linkWatermark={linkWatermark}/>;
      // case 'document':
      //     return <Documnet src={src} onError={() => setError(true)}></Documnet>
      case 'document':
        // if (isHtml(src)) {
        //   return <HTML src={src} onError={() => setError(true)} />
        // } else if (src.indexOf('.xls') > -1 || src.indexOf('.xlsx') > -1) {
        //使用wps，需要先调用后台服务wps相关接口，获取文件预览链接、token等数据，然后传入wps前端组件初始化
        //return <WPS src={src} onError={() => setError(true)} />;

        return (() =>
          useMemo(
            () => (
              <div className='document_container'>
                <DocumentEditor
                  key={contendId}
                  id="docxEditor"
                  // documentServerUrl="http://172.16.151.202/documentserver/"
                  documentServerUrl={`${location.origin}/documentserver/`}
                  config={config_document}
                  events_onAppReady={onDocumentReady}
                />
                  {
                    rmanGlobalParameter.includes(globalParams.watermark_display) && linkWatermark &&
                    <div className='watermarkShow'>
                      {Array(20).fill(null).map((_, i) => (
                        <div key={i}>{linkWatermark}</div>
                      ))}
                    </div>
                  }
              </div>
            ),
            [contendId, linkWatermark],
          ))();

      // }else{
      //   return <PDF src={src} onError={() => setError(true)} />
      // }
      // return isHtml(src) ? (
      //   <HTML src={src} onError={() => setError(true)} />
      // ) : (
      //   <PDF src={src} onError={() => setError(true)} />
      // );
      default:
        if(src.indexOf('.xmind') > -1){
          return <Xmind src={src} onError={() => setError(true)} />
        }else{
          return <Error errorCode={errorCode} describe="暂不支持的类型" />;
        }
    }
  };
  return render();
};

export default Entity;
