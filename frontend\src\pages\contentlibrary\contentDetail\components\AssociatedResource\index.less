.associatedResource {
  button {
    border-radius: 18px;
    margin-right: 15px;
    margin-bottom: 15px;

    &.active {
      background: var(--primary-color);
      color: #fff;
    }
  }

  .mapX6 {
    width: 100%;
    height: 380px;
    position: relative;
    margin-bottom: 30px;
  }

  .video-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 15px;

    .left {
      width: 80%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  body {
    min-width: 0;
  }
}