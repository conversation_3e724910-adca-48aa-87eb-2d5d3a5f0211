import React, { useImperativeHandle, forwardRef, useEffect, useRef } from 'react';
import { changesize, copyObject } from '@/utils';
import BasicInput from './basicInput';
import { Form,Tag } from 'antd';
import BasicDate from './basicDate';
import BasicInputTextArea from './basicInputTextArea';
import BasicEnum from './basicEnum';
import BasicTree from './basicTree';
import BasicTag from './basicTag';
import moment, { Moment } from 'moment';
import { IEntityMetadata, IFormItem } from '@/types/entityTypes';
import BasicNumber from './basicNumber';
import TeacherItem from '../formItemBox/teacherItem';

interface IBasicMetadataProps {
  items: IFormItem[];
  edit: boolean;
  path?: any;
  voiceList?: any;
  onValueChange?: (items: IFormItem[]) => void;
  jumpVideoTime?: (e:any,key?:any) => void;
  drawPlot?: (e:any,key:any,remove?:boolean) => void;
  frameRate?: number;
  otherFormData?: any;
  detail?: any;
}

export interface IBasicItemProps {
  item: IFormItem;
  edit: boolean;
  path?: any;
  treeDatas?: any;
  voiceList?: any;
  jumpVideoTime?: (e:any)=> void;
}

export interface IBasicMetadataRef {
  getOldItems: () => void;
  getNewItems: () => Promise<IFormItem[] | null>;
  resetFields: () => void;
}

export const getValue = (value: string) => {
  let res: string[] = [];
  try {
    const arr = Array.isArray(value) ? value :JSON.parse(value);
    if (Array.isArray(arr)) {
      res = arr;
    } else {
      res = [arr.toString()];
    }
  } catch (e) {
    console.error(e);
  }
  return res;
};

const isFileSize = (fieldName: string) =>
  ['fileSize', 'fileSize_'].includes(fieldName);

const BasicMetadata = forwardRef<IBasicMetadataRef, IBasicMetadataProps>(
  (props, ref) => {
    useImperativeHandle(ref, () => {
      return {
        getOldItems: setOldItems,
        getNewItems,
        resetFields: () => formRef.resetFields(Object.keys(initValues())),
      };
    });

    const [formRef] = Form.useForm();
    const preSelect = useRef<any>({
      index:-1,
      name:''
    })
    const setOldItems = () => {
      formRef.resetFields();
    };

    const getNewItems = (): Promise<IFormItem[] | null> => {
      return new Promise((resolve, reject) => {
        formRef
          .validateFields()
          .then(res => {
            const newItems = getValues(props.items, res);
            resolve(newItems);
          })
          .catch(err => {
            reject(null);
          });
      });
    };

    const initValues = () => {
      const obj: { [key: string]: string | Moment | number | string[] } = {};
      props.items.forEach(item => {
        if (item.controlType === 8 || item.controlType === 12) {
          obj[item.fieldName] = item.value
            ? Array.isArray(item.value)
              ? item.value
              : JSON.parse(item.value + '')
            : [];
        } else if (item.controlType === 1) {
          obj[item.fieldName] = moment(item.value || '');
        } else if (item.controlType === 14) {
          // obj[item.fieldName] = item.value
          //   ? Array.isArray(item.value)
          //     ? item.value
          //     : JSON.parse(item.value + '')
          //   : [];
          obj[item.fieldName] = item.value || '';
        } else {
          obj[item.fieldName] = item.value || '';
        }
      });
      return obj;
    };
    if (props.items.length === 0) {
      return null;
    }
    const getValues = (items: IFormItem[], values: any) => {
      const newItems = copyObject(props.items);
      newItems.forEach(item => {
        if (item.controlType === 8 || item.controlType === 12) {
          // 判断是不是多选
          // if(item.isMultiSelect && item.fieldName == 'teacher'){
          //   item.value = values[item.fieldName]; //.map((item:any)=>item.key)
          // }else{
            item.value = Array.isArray(values[item.fieldName])
            ? values[item.fieldName]
            : [values[item.fieldName]];
          // }
        } else if (item.controlType === 1) {
          item.value = values[item.fieldName].format('YYYY-MM-DD HH:mm:ss');
        } else if (item.controlType === 14) {
          item.value = Array.isArray(values[item.fieldName])
            ? values[item.fieldName]
            : [values[item.fieldName]];
        } else {
          item.value = values[item.fieldName];
        }
      });
      // debugger
      return newItems;
    };

    const onValuesChange = (changedValues: any, values: any) => {
      const newItems = getValues(props.items, values);
      if (props.onValueChange) {
        props.onValueChange(newItems);
      }
    };
    //
    const loopClick =(item:any)=>{
      const temp = preSelect.current;
      if(temp.name=='' && temp.index==-1){
        temp.name = item.sensitive_word;
        temp.index = 0;
      }else if(item.sensitive_word == temp.name){
        if(temp.index<item.sensitiveWordGroups.length-1){
          temp.index++;
        }else{
          temp.index=-1;
        }
      }else{
        temp.index = 0
        temp.name = item.sensitive_word;
      }
      preSelect.current = temp;
      if(props.detail?.type=='biz_sobey_video'){
        if(temp.index==-1){
          props.jumpVideoTime?.(0); //重置到起点
        }else{
          props.jumpVideoTime?.(item,temp);
        }
      }else if(props.detail?.type=='biz_sobey_picture'){
        //图片的话 需要定位框起来 
        if(temp.index==-1){
          props.drawPlot?.(item,temp,true);//移除
          window.removeEventListener('resize',function(){
            props.drawPlot?.(item,temp);
          });
        }else{
          props.drawPlot?.(item,temp);
          window.addEventListener('resize',function(){
            props.drawPlot?.(item,temp);
          })
        }
      }else if(props.detail?.type=='biz_sobey_document'){

      }else{

      }
      return temp
    }
    return (
      <Form
        initialValues={initValues()}
        className='basicMetaForm'
        form={formRef}
        onValuesChange={onValuesChange}
      >
        {[...props.items]
          .filter(item => item.fieldName !== "createUser_")
          .sort((a, b) => a.order - b.order)
          .map((item, index) => {
            /**
             * 对filesize特殊处理
             */
            if (!isNaN(item.value as any) && isFileSize(item.fieldName)) {
              item.value = changesize(item.value);
              item.isReadOnly = true;
            }
            switch (item.controlType) {
              case 1: // 时间
                return <BasicDate item={item} key={index} edit={props.edit} />;
              case 4: // 数字
                return (
                  <BasicNumber
                    item={item}
                    key={index}
                    edit={props.edit}
                    frameRate={props.frameRate || 25}
                  />
                );
              case 5: // 文本
                return <BasicInput item={item} key={index} path={props.path} edit={props.edit} />;
              case 6: // 多行文本
                return (
                  <BasicInputTextArea
                    item={item}
                    key={index}
                    edit={props.edit}
                  />
                );
              case 8: // 枚举
                if (item.fieldName === 'teacher') {
                  return (
                    <TeacherItem
                      selectKeys={getValue(item.value as string)}
                      edit={!!(props.edit && !item.isReadOnly)}
                      multiple={item.isMultiSelect as any}
                      required={item.isMustInput as any}
                      message={'请选择教师'}
                      label={item.alias}
                      name={item.fieldName}
                      key={item.fieldName}
                      className={'basicTeacherItem'}
                    />
                  );
                } else {
                  return (
                    <BasicEnum item={item} key={index} edit={props.edit} />
                  );
                }
              case 14: // 树形结构
                return <BasicTree item={item} key={index} edit={props.edit} />;
              case 12: // 标签
                return <BasicTag item={item} voiceList={props.voiceList} jumpVideoTime={props.jumpVideoTime} key={index} edit={props.edit} />;
              default:
                return <BasicInput item={item} key={index} edit={props.edit} />;
            }
          })}
          {
            Object.keys(props.otherFormData||{}).map((item:any,index:number)=>{
              switch (item){
                case 'sensitiveWords': //敏感词
                  return <Form.Item
                            label='敏感词'
                            key={'sensitiveWords'}
                          >
                            {
                              props.otherFormData[item].map((item_:any,index_:number)=>{
                                return <Tag key={index_} className='sensitiveTag' onClick={()=>loopClick(item_)}>
                                          {item_.sensitive_word}
                                       </Tag>
                              })
                            }
                          </Form.Item>
                case 'sensitivePersons': //敏感人物
                  return <Form.Item
                            label='敏感人物'
                            key={'sensitivePersons'}
                          >
                            {
                              props.otherFormData[item].map((item_:any,index_:number)=>{
                                return <Tag key={index_} className='sensitiveTag' onClick={()=>loopClick(item_)}>
                                          {item_.sensitive_word}
                                       </Tag>
                              })
                            }
                          </Form.Item>
              }
            })
          }
      </Form>
    );
  },
);

export default BasicMetadata;
