import http from '../http/http';


namespace AuditService {
   /**
   * 查询片段
   * @param contentid
   */
   export const auditprocess = (params:any) =>
   http<any>(`/unifiedplatform/v1/audit/process`, {
     method: 'POST',
     params,
     headers: {
       'Content-Type': 'application/json',
     },
   });
 /**
  * 查询片段 新
  * @param contentid
  */
 export const getResourceAuditInfo = (instanceId: string,) =>
 http<any>(`/unifiedplatform/v1/audit/instance/resource?instanceId=${instanceId}`, {
   method: 'GET',
   headers: {
     'Content-Type': 'application/json',
   }
  })
}

/**
 * 查询资源审核日志
 */
export const getResourceAuditLog = (contentId: string,params:any) =>
    http<any>(`/unifiedplatform/v1/audit/log?contentId=${contentId}`, {
        method: 'GET',
        params,
        headers: {
            'Content-Type': 'application/json',
        },
    });






export default AuditService;
