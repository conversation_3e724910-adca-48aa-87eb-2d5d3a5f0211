.metadata_detail_wrapper {
  position: relative;

  .metadata_detail_header {
    height: 60px;
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #d3d3d3;

    .view_switch_wrapper {
      .view_switch_item {
        font-size: 18px;
        cursor: pointer;

        &.active {
          color: var(--primary-color);
        }
      }
    }
  }

}

#root {
  height: 100%;
  overflow-y: auto;
}
