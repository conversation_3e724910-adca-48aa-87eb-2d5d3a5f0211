import React, { <PERSON> } from 'react';
import { Form, Select } from 'antd';
import { IBasicItemProps } from './basicMetadata';
import { getValue } from './basicMetadata';

const { Option } = Select;

const BasicEnum: FC<IBasicItemProps> = props => {
  const options = props.item.controlData.length
    ? JSON.parse(props.item.controlData)
    : {};
  const optionsEnum=[
    '未分析',
    '分析中',
    '分析完成',
  ]
  return (
    <Form.Item
      label={props.item.alias}
      name={props.item.fieldName}
      rules={[
        {
          required:
            props.item.isReadOnly || !props.edit
              ? false
              : props.item.isMustInput,
        },
      ]}
    >
      {props.item.isReadOnly || !props.edit ? (
        <div
          style={{
            lineHeight: '32px',
            wordBreak: 'break-all',
            whiteSpace: 'break-spaces',
          }}
        >
          {
            props.item.value ? (
              getValue(props.item.value as string).map(
                (item: string, index: number) => {
                  if (options[item]) {
                    return (index === 0 ? '' : ',') + options[item];
                  } else {
                    // 针对语音识别、分析、标签分析状态 0 未分析 1分析中 2分析完成  -1分析 失败
                    if(
                        props.item.fieldName==='ocr_status'||
                        props.item.fieldName==='smart_status'||
                        props.item.fieldName==='asr_status'
                        )
                      return (index === 0 ? '' : ',') + item==='-1'?'分析失败':optionsEnum[Number(item)];
                    else
                      return (index === 0 ? '' : ',') + item;
                  }
                },
              )
            ) : (
              ''
            )}
        </div>
      ) : (
        <Select
          mode={props.item.isMultiSelect ? 'multiple' : undefined} 
          showSearch allowClear
          filterOption={(input: string, option: any) =>
            option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
          }
        >
          {Object.keys(options).map(key => {
            return (
              <Option value={key} key={key}>
                {options[key]}
              </Option>
            );
          })}
        </Select>
      )}
    </Form.Item>
  );
};

export default BasicEnum;
