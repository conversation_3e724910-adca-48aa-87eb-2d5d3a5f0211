{"compilerOptions": {"experimentalDecorators": true, "lib": ["dom", "dom.iterable", "esnext"], "skipLibCheck": true, "target": "es5", "module": "esnext", "moduleResolution": "node", "importHelpers": true, "resolveJsonModule": true, "jsx": "react", "esModuleInterop": true, "sourceMap": true, "baseUrl": "./", "strict": true, "isolatedModules": true, "paths": {"@/*": ["src/*"], "@@/*": ["src/.umi/*"]}, "allowSyntheticDefaultImports": true}, "include": ["mock/**/*", "src/**/*", "config/**/*", ".umirc.ts", "typings.d.ts"]}