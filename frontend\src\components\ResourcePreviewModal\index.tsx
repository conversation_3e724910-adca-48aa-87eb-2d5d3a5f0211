/*
 * @Author: 李晋
 * @Date: 2021-11-10 14:18:37
 * @Email: <EMAIL>
 * @LastEditTime: 2021-11-17 11:03:51
 * @Description: file information
 * @Company: Sobey
 */
import { Modal, Spin, Button } from 'antd';
import React, { FC, useEffect, useState } from 'react';
import Entity from '@/components/entity/entity';
import './index.less';
import rmanApis from '@/service/rman';
interface ResourcePreviewModalProps {
  modalVisible: boolean;
  modalClose: () => void;
  resource: {
    id?: string;
    src?: string; //如果有src 则直接预览
    type: string;
    name: string;
  };
}
const ResourcePreviewModal: FC<ResourcePreviewModalProps> = ({
  modalVisible,
  modalClose,
  resource,
}) => {
  const [previewEntity, setPreviewEntity] = useState<any>(null);
  const [entityLoading, setEntityLoading] = useState<boolean>(false);
  useEffect(() => {
    if (resource?.id) {
      onEntityPreview(resource.id, {
        name: resource.type,
        type: resource.type,
      });
    }
    if (resource?.src) {
      setPreviewEntity(resource);
    }
  }, [resource]);
  /**
   * resource资源对象中取资源url
   *
   * @param {*} resource
   * @return {*}
   */
  const getPreviewPath = (resource: any) => {
    if (resource && resource.fileGroups) {
      const { fileGroups } = resource;
      const previewFile = fileGroups.filter((file: any) => file.typeCode === 'previewfile');
      const videogroupFile = fileGroups.filter((file: any) => file.typeCode === 'sourcefile');
      return previewFile.length > 0
        ? previewFile[0].fileItems[0].filePath
        : videogroupFile[0].fileItems[0].filePath;
    }
    return '';
  };
  /**
   * 查询资源预览网址，显示资源详情modal
   *
   * @param {string} id
   * @param {*} { name, type }
   */
  const onEntityPreview = (id: string, { name, type }: any) => {
    setEntityLoading(true);
    rmanApis
      .resourceDetail(id)
      .then((res: any) => {
        if (res.success) {
          setPreviewEntity({
            src: getPreviewPath(res.data),
            name,
            type,
          });
          // setEntityModalVisible(true);
        }
      })
      .finally(() => setEntityLoading(false));
  };

  // const editHandle = () => {
  //   window.open(`/rman/#/basic/rmanDetail/${resource.id}`);
  // };
  return (
    <Modal
      width="70vw"
      style={{ top: 40 }}
      zIndex={10000}
      title={resource?.name}
      visible={modalVisible}
      footer={null}
      onCancel={modalClose}
    >
      <Spin spinning={entityLoading}>
        <div className="entity-preview">
          {/* <Button onClick={editHandle}>编辑</Button> */}
          <div className="video-wrap">
          {previewEntity ? <Entity type={previewEntity?.type} src={previewEntity?.src} id={'previewVideo'} />:null} 
          </div>
        </div>
      </Spin>
    </Modal>
  );
};

export default ResourcePreviewModal;
