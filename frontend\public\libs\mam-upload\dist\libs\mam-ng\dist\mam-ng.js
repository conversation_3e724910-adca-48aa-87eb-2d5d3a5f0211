/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// identity function for calling harmony imports with the correct context
/******/ 	__webpack_require__.i = function(value) { return value; };
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, {
/******/ 				configurable: false,
/******/ 				enumerable: true,
/******/ 				get: getter
/******/ 			});
/******/ 		}
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = 81);
/******/ })
/************************************************************************/
/******/ ([
/* 0 */
/***/ (function(module, exports) {

/*
	MIT License http://www.opensource.org/licenses/mit-license.php
	Author Tobias Koppers @sokra
*/
// css base code, injected by the css-loader
module.exports = function(useSourceMap) {
	var list = [];

	// return the list of modules as css string
	list.toString = function toString() {
		return this.map(function (item) {
			var content = cssWithMappingToString(item, useSourceMap);
			if(item[2]) {
				return "@media " + item[2] + "{" + content + "}";
			} else {
				return content;
			}
		}).join("");
	};

	// import a list of modules into the list
	list.i = function(modules, mediaQuery) {
		if(typeof modules === "string")
			modules = [[null, modules, ""]];
		var alreadyImportedModules = {};
		for(var i = 0; i < this.length; i++) {
			var id = this[i][0];
			if(typeof id === "number")
				alreadyImportedModules[id] = true;
		}
		for(i = 0; i < modules.length; i++) {
			var item = modules[i];
			// skip already imported module
			// this implementation is not 100% perfect for weird media query combinations
			//  when a module is imported multiple times with different media queries.
			//  I hope this will never occur (Hey this way we have smaller bundles)
			if(typeof item[0] !== "number" || !alreadyImportedModules[item[0]]) {
				if(mediaQuery && !item[2]) {
					item[2] = mediaQuery;
				} else if(mediaQuery) {
					item[2] = "(" + item[2] + ") and (" + mediaQuery + ")";
				}
				list.push(item);
			}
		}
	};
	return list;
};

function cssWithMappingToString(item, useSourceMap) {
	var content = item[1] || '';
	var cssMapping = item[3];
	if (!cssMapping) {
		return content;
	}

	if (useSourceMap && typeof btoa === 'function') {
		var sourceMapping = toComment(cssMapping);
		var sourceURLs = cssMapping.sources.map(function (source) {
			return '/*# sourceURL=' + cssMapping.sourceRoot + source + ' */'
		});

		return [content].concat(sourceURLs).concat([sourceMapping]).join('\n');
	}

	return [content].join('\n');
}

// Adapted from convert-source-map (MIT)
function toComment(sourceMap) {
	// eslint-disable-next-line no-undef
	var base64 = btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap))));
	var data = 'sourceMappingURL=data:application/json;charset=utf-8;base64,' + base64;

	return '/*# ' + data + ' */';
}


/***/ }),
/* 1 */
/***/ (function(module, exports, __webpack_require__) {

/*
	MIT License http://www.opensource.org/licenses/mit-license.php
	Author Tobias Koppers @sokra
*/

var stylesInDom = {};

var	memoize = function (fn) {
	var memo;

	return function () {
		if (typeof memo === "undefined") memo = fn.apply(this, arguments);
		return memo;
	};
};

var isOldIE = memoize(function () {
	// Test for IE <= 9 as proposed by Browserhacks
	// @see http://browserhacks.com/#hack-e71d8692f65334173fee715c222cb805
	// Tests for existence of standard globals is to allow style-loader
	// to operate correctly into non-standard environments
	// @see https://github.com/webpack-contrib/style-loader/issues/177
	return window && document && document.all && !window.atob;
});

var getElement = (function (fn) {
	var memo = {};

	return function(selector) {
		if (typeof memo[selector] === "undefined") {
			memo[selector] = fn.call(this, selector);
		}

		return memo[selector]
	};
})(function (target) {
	return document.querySelector(target)
});

var singleton = null;
var	singletonCounter = 0;
var	stylesInsertedAtTop = [];

var	fixUrls = __webpack_require__(77);

module.exports = function(list, options) {
	if (typeof DEBUG !== "undefined" && DEBUG) {
		if (typeof document !== "object") throw new Error("The style-loader cannot be used in a non-browser environment");
	}

	options = options || {};

	options.attrs = typeof options.attrs === "object" ? options.attrs : {};

	// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>
	// tags it will allow on a page
	if (!options.singleton) options.singleton = isOldIE();

	// By default, add <style> tags to the <head> element
	if (!options.insertInto) options.insertInto = "head";

	// By default, add <style> tags to the bottom of the target
	if (!options.insertAt) options.insertAt = "bottom";

	var styles = listToStyles(list, options);

	addStylesToDom(styles, options);

	return function update (newList) {
		var mayRemove = [];

		for (var i = 0; i < styles.length; i++) {
			var item = styles[i];
			var domStyle = stylesInDom[item.id];

			domStyle.refs--;
			mayRemove.push(domStyle);
		}

		if(newList) {
			var newStyles = listToStyles(newList, options);
			addStylesToDom(newStyles, options);
		}

		for (var i = 0; i < mayRemove.length; i++) {
			var domStyle = mayRemove[i];

			if(domStyle.refs === 0) {
				for (var j = 0; j < domStyle.parts.length; j++) domStyle.parts[j]();

				delete stylesInDom[domStyle.id];
			}
		}
	};
};

function addStylesToDom (styles, options) {
	for (var i = 0; i < styles.length; i++) {
		var item = styles[i];
		var domStyle = stylesInDom[item.id];

		if(domStyle) {
			domStyle.refs++;

			for(var j = 0; j < domStyle.parts.length; j++) {
				domStyle.parts[j](item.parts[j]);
			}

			for(; j < item.parts.length; j++) {
				domStyle.parts.push(addStyle(item.parts[j], options));
			}
		} else {
			var parts = [];

			for(var j = 0; j < item.parts.length; j++) {
				parts.push(addStyle(item.parts[j], options));
			}

			stylesInDom[item.id] = {id: item.id, refs: 1, parts: parts};
		}
	}
}

function listToStyles (list, options) {
	var styles = [];
	var newStyles = {};

	for (var i = 0; i < list.length; i++) {
		var item = list[i];
		var id = options.base ? item[0] + options.base : item[0];
		var css = item[1];
		var media = item[2];
		var sourceMap = item[3];
		var part = {css: css, media: media, sourceMap: sourceMap};

		if(!newStyles[id]) styles.push(newStyles[id] = {id: id, parts: [part]});
		else newStyles[id].parts.push(part);
	}

	return styles;
}

function insertStyleElement (options, style) {
	var target = getElement(options.insertInto)

	if (!target) {
		throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");
	}

	var lastStyleElementInsertedAtTop = stylesInsertedAtTop[stylesInsertedAtTop.length - 1];

	if (options.insertAt === "top") {
		if (!lastStyleElementInsertedAtTop) {
			target.insertBefore(style, target.firstChild);
		} else if (lastStyleElementInsertedAtTop.nextSibling) {
			target.insertBefore(style, lastStyleElementInsertedAtTop.nextSibling);
		} else {
			target.appendChild(style);
		}
		stylesInsertedAtTop.push(style);
	} else if (options.insertAt === "bottom") {
		target.appendChild(style);
	} else {
		throw new Error("Invalid value for parameter 'insertAt'. Must be 'top' or 'bottom'.");
	}
}

function removeStyleElement (style) {
	if (style.parentNode === null) return false;
	style.parentNode.removeChild(style);

	var idx = stylesInsertedAtTop.indexOf(style);
	if(idx >= 0) {
		stylesInsertedAtTop.splice(idx, 1);
	}
}

function createStyleElement (options) {
	var style = document.createElement("style");

	options.attrs.type = "text/css";

	addAttrs(style, options.attrs);
	insertStyleElement(options, style);

	return style;
}

function createLinkElement (options) {
	var link = document.createElement("link");

	options.attrs.type = "text/css";
	options.attrs.rel = "stylesheet";

	addAttrs(link, options.attrs);
	insertStyleElement(options, link);

	return link;
}

function addAttrs (el, attrs) {
	Object.keys(attrs).forEach(function (key) {
		el.setAttribute(key, attrs[key]);
	});
}

function addStyle (obj, options) {
	var style, update, remove, result;

	// If a transform function was defined, run it on the css
	if (options.transform && obj.css) {
	    result = options.transform(obj.css);

	    if (result) {
	    	// If transform returns a value, use that instead of the original css.
	    	// This allows running runtime transformations on the css.
	    	obj.css = result;
	    } else {
	    	// If the transform function returns a falsy value, don't add this css.
	    	// This allows conditional loading of css
	    	return function() {
	    		// noop
	    	};
	    }
	}

	if (options.singleton) {
		var styleIndex = singletonCounter++;

		style = singleton || (singleton = createStyleElement(options));

		update = applyToSingletonTag.bind(null, style, styleIndex, false);
		remove = applyToSingletonTag.bind(null, style, styleIndex, true);

	} else if (
		obj.sourceMap &&
		typeof URL === "function" &&
		typeof URL.createObjectURL === "function" &&
		typeof URL.revokeObjectURL === "function" &&
		typeof Blob === "function" &&
		typeof btoa === "function"
	) {
		style = createLinkElement(options);
		update = updateLink.bind(null, style, options);
		remove = function () {
			removeStyleElement(style);

			if(style.href) URL.revokeObjectURL(style.href);
		};
	} else {
		style = createStyleElement(options);
		update = applyToTag.bind(null, style);
		remove = function () {
			removeStyleElement(style);
		};
	}

	update(obj);

	return function updateStyle (newObj) {
		if (newObj) {
			if (
				newObj.css === obj.css &&
				newObj.media === obj.media &&
				newObj.sourceMap === obj.sourceMap
			) {
				return;
			}

			update(obj = newObj);
		} else {
			remove();
		}
	};
}

var replaceText = (function () {
	var textStore = [];

	return function (index, replacement) {
		textStore[index] = replacement;

		return textStore.filter(Boolean).join('\n');
	};
})();

function applyToSingletonTag (style, index, remove, obj) {
	var css = remove ? "" : obj.css;

	if (style.styleSheet) {
		style.styleSheet.cssText = replaceText(index, css);
	} else {
		var cssNode = document.createTextNode(css);
		var childNodes = style.childNodes;

		if (childNodes[index]) style.removeChild(childNodes[index]);

		if (childNodes.length) {
			style.insertBefore(cssNode, childNodes[index]);
		} else {
			style.appendChild(cssNode);
		}
	}
}

function applyToTag (style, obj) {
	var css = obj.css;
	var media = obj.media;

	if(media) {
		style.setAttribute("media", media)
	}

	if(style.styleSheet) {
		style.styleSheet.cssText = css;
	} else {
		while(style.firstChild) {
			style.removeChild(style.firstChild);
		}

		style.appendChild(document.createTextNode(css));
	}
}

function updateLink (link, options, obj) {
	var css = obj.css;
	var sourceMap = obj.sourceMap;

	/*
		If convertToAbsoluteUrls isn't defined, but sourcemaps are enabled
		and there is no publicPath defined then lets turn convertToAbsoluteUrls
		on by default.  Otherwise default to the convertToAbsoluteUrls option
		directly
	*/
	var autoFixUrls = options.convertToAbsoluteUrls === undefined && sourceMap;

	if (options.convertToAbsoluteUrls || autoFixUrls) {
		css = fixUrls(css);
	}

	if (sourceMap) {
		// http://stackoverflow.com/a/26603875
		css += "\n/*# sourceMappingURL=data:application/json;base64," + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + " */";
	}

	var blob = new Blob([css], { type: "text/css" });

	var oldSrc = link.href;

	link.href = URL.createObjectURL(blob);

	if(oldSrc) URL.revokeObjectURL(oldSrc);
}


/***/ }),
/* 2 */
/***/ (function(module, exports) {

angular.module('mam-ng')
    .directive('mamDropdown', function () {
        return {
            restrict: 'A',
            link: function (scope, element, attr) {scope.mode = 'click';
                var $e = $(element);
                var toggle = $e.find('.dropdown-toggle');
                var menu = $e.find('.dropdown-menu');
                menu.hide();
                var isShow = false;

                var timer = 0;
                //var menuHeight = menu.height();

                if (toggle.parent().css('position') == 'static') {//当div高度固定很短的时候显示不出来时
                    var top = toggle.position().top + toggle.outerHeight() - 2;
                    var left = toggle.position().left;
                    menu.css('top', top);
                    menu.css('left', left);
                } else {
                    $e.css('position', 'relative');
                }

                var changeMenu = function () {
                    isShow = !isShow;
                    toggle.toggleClass("dropdown-toggle-active");
                    menu.not(menu).stop().slideUp(200).parent().find(".dropdown-toggle"); //.removeClass("dropdown-toggle-active");
                    menu.stop().slideToggle(200);
                }

                if (scope.mode == 'hover') {
                    //比较高度，是为了防止在关闭动画的时候display不为none,也进行切换的问题
                    $e.hover(function () {
                        clearTimeout(timer);
                        if (!isShow) {
                            changeMenu();
                        }
                        return false;
                    }, function () {
                        clearTimeout(timer);
                        if (isShow) {
                            timer = setTimeout(changeMenu, 250);//添加延迟关闭
                        }
                        return false;
                    })
                    menu.hover(function () { 
                        clearTimeout(timer);
                    }, function () {
                        clearTimeout(timer);
                        if (isShow) {
                            timer = setTimeout(changeMenu, 250);//添加延迟关闭
                        }
                        return false;
                    })
                } else {
                    toggle.on('click', function () {
                        changeMenu();
                        return false;
                    });
                }

                menu.find('li').on('click', function () {
                    clearTimeout(timer);
                    isShow = false;
                    toggle.removeClass('dropdown-toggle-active');
                    menu.stop().slideUp(200);
                });

                $('html').on('click', function () {
                    clearTimeout(timer);
                    if (isShow) {
                        isShow = false;
                        toggle.removeClass('dropdown-toggle-active');
                        menu.stop().slideUp(200);
                    }
                });
            }
        };
    });

/***/ }),
/* 3 */
/***/ (function(module, exports) {

module.exports = "<svg t=\"1501119020784\" class=\"icon\" style viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"3157\" xmlns:xlink=\"http://www.w3.org/1999/xlink\"><defs><style type=\"text/css\"></style></defs><path d=\"M528.612 146.023v771.24c0 9.23-7.383 16.612-16.612 16.612s-16.612-7.383-16.612-16.612v-770.977l-103.623 103.623c-6.328 6.328-16.875 6.328-23.203 0s-6.328-16.875 0-23.203l131.836-131.836c6.328-6.328 16.875-6.328 23.203 0l131.836 131.836c6.328 6.328 6.328 16.875 0 23.203s-16.875 6.328-23.203 0l-103.623-103.887z\" p-id=\"3158\"></path></svg>"

/***/ }),
/* 4 */
/***/ (function(module, exports) {


mam.ng.config = function (ngApp) {
    ngApp.config([
        '$controllerProvider', '$compileProvider', '$filterProvider', '$provide',
        function ($controllerProvider, $compileProvider, $filterProvider, $provide) {
            ngApp.registerController = $controllerProvider.register;
            ngApp.registerDirective = $compileProvider.directive;
            ngApp.registerFilter = $filterProvider.register;
            ngApp.registerFactory = $provide.factory;
            ngApp.registerService = $provide.service;
        }
    ]);

    ngApp.run([
        '$rootScope', '$state', '$stateParams',
        function ($rootScope, $state, $stateParams) {
            $rootScope.$state = $state;
            $rootScope.params = $stateParams;
            $rootScope.config = _.get(window, 'nxt.config');
            $rootScope.l = window.l;
        }
    ]);
    return ngApp;
}

/***/ }),
/* 5 */
/***/ (function(module, exports) {

function addUrlParam(url, param) {
    url += (url.indexOf('?') !== -1 ? '&' : '?') + param;
    return url;
}

mam.ng.handleUrl = function (url, options) {
    var opts = $.extend({}, { r: true }, options);

    if (opts.r) {
        url = addUrlParam(url, 'r=' + parseInt(100 * Math.random()) + new Date().getTime());
    }

    var token = mam.utils.getUrlQueryParam('token');
    if (token != null) {
        url = addUrlParam(url, 'token=' + encodeURI(token).replace(/\+/g, '%2B'));
    }

    var opsite = mam.utils.getUrlQueryParam('opsite', true);
    if (opsite != null) {
        url = addUrlParam(url, 'opsite=' + opsite)
    }

    var site = mam.utils.getUrlQueryParam('site');
    if (site == null) {
        site = mam.utils.getUrlQueryParam('sitecode');
    }
    if (site != null) {
        url = addUrlParam(url, 'site=' + site);
        $.cookie('site', encodeURIComponent(site), { path: '/' });
    }
    return url;
}

/***/ }),
/* 6 */
/***/ (function(module, exports) {

mam.ng.httpProvider = function (ngApp, options) {
    var opts = $.extend({}, {
        name: 'mamHttp',
        server: '',
        urlPrefix: '~/',
        cache: false,
        withCredentials: true,
        loaderTemplate: '<div id="mam-loader" class="mam-loader">Loading...</div>',
        loginUrl: '/#!/login?login_backUrl=${url}',
        requestErrorTip: '请求失败，请稍后再试。'
    }, options);

    ngApp.config([
        '$provide', '$httpProvider',
        function ($provide, $httpProvider) {
            var requestCount = 0;
            var $loader = $(opts.loaderTemplate);
            $('body').append($loader);
            $loader.hide();

            function hideLoader(config) {
                if (config.interfaceCall && config.showLoader !== false) {
                    requestCount--;
                    if (requestCount <= 0) {
                        $loader.hide();
                    }
                }
            }

            function goLoginPage() {
                if (location.href.split('?login_backUrl=').length < 2) {
                    location.href = _.template(opts.loginUrl)({ url: escape(location.href) });
                }
            }

            /** 判断url前缀是否是其他服务器的前缀标识 */
            function indexOfOtherServersPrefix(url) {
                var index = -1;
                if (opts.otherServers && opts.otherServers instanceof Array) {
                    _.forEach(opts.otherServers, function (item) {
                        if (index === -1) {
                            index = url.indexOf(item.prefix);
                        }
                    });
                }
                return index;
            }

            function getServerUrl(url) {
                if (url.indexOf(opts.urlPrefix) === 0) {
                    return opts.server;
                }
                else if (opts.otherServers && opts.otherServers instanceof Array) {
                    var retUrl = "";
                    _.forEach(opts.otherServers, function (item) {
                        if (url.indexOf(item.prefix) === 0) {
                            retUrl = item.server;
                        }
                    });
                    return retUrl;
                }
                return "";
            }

            $provide.factory(opts.name, ['$q', function ($q) {
                return {
                    'request': function (config) {
                        if (config.url.indexOf(opts.urlPrefix) === 0 || indexOfOtherServersPrefix(config.url) === 0) {
                            config.interfaceCall = true;
                            config.url = getServerUrl(config.url) + config.url.substring(1, config.url.length);
                            config.url = mam.ng.handleUrl(config.url);
                            if (config.showLoader !== false) {
                                requestCount++;
                                $loader.show();
                            }
                        }
                        var productName =_.get(nxt,'product.name','');
                        if (productName) {
                            if (!config.headers)
                                config.headers = {};
                            config.headers['mam-product'] = productName;
                        }
                        return config;
                    },
                    'requestError': function (rejection) {
                        hideLoader(rejection.config);
                        if (rejection.config.interfaceCall) {
                            console.error('requestError', rejection);
                            mam.prompt(opts.requestErrorTip);
                        }
                        return $q.reject(rejection);
                    },
                    'response': function (res) {
                        if (res.config.interfaceCall !== true) {
                            return res;
                        }
                        hideLoader(res.config);
                        if (res.status === 200) {
                            if (res.data.success) {
                                res.data = res.data.data;
                            } else {
                                res.data = res.data.error;
                                res.data.success = false;
                                console.error('response', res);
                                if (res.config.errorHandle !== false) {
                                    mam.prompt(l('system.' + res.data.code, res.data.title));
                                }
                                if (res.config.errorReject !== false) { //客户端上传的地方用了q.all，所以希望失败了也要返回结果。
                                    return $q.reject(res);
                                }
                                return $q.resolve(res);
                            }
                        }
                        return res;
                    },
                    'responseError': function (res) {
                        hideLoader(res.config);
                        if (res.config.interfaceCall) {
                            console.error('responseError', res);
                            if (res.status === -1) {
                                mam.prompt(l('system.500', '系统错误，请稍后再试！'));
                                return $q.reject(res);
                            }
                            res.data = res.data.error;
                            if (res.status === 401) {
                                if (res.config.errorHandle !== false) {
                                    goLoginPage();
                                }
                                if (res.config.errorReject !== false) {
                                    return $q.reject(res);
                                }
                                return $q.resolve(res);
                            }
                            if (res.config.errorHandle !== false) {
                                if (res.status === 403) {
                                    mam.prompt(l('system.403', '错误代码：403，服务器拒绝请求！'));
                                }
                                if (res.status === 404) {
                                    mam.prompt(l('system.404', '错误代码：404，未找到请求地址！'));
                                }
                                else
                                {
                                    var requestId = res.headers('request-id');
                                    var outer = $('<div class="system-error-tip-box"></div>');
                                    outer.append('<div class="error-tip">'+l('system.500', res.data.title)+'</div>');
                                    if (requestId)
                                    {
                                        var aEle = $('<a class="error-info-btn">复制错误代码<a>');
                                        aEle.click(function(){
                                            var input = document.createElement('input');
                                            document.body.appendChild(input);
                                            input.setAttribute('value', requestId);
                                            input.select();
                                            if (document.execCommand('copy')) {
                                                document.execCommand('copy');
                                                mam.message.success('复制成功！')
                                            }
                                            document.body.removeChild(input);
                                        });
                                        outer.append(aEle);
                                    }
                                    mam.prompt(outer);
                                }
                            }
                            if (res.config.errorReject !== false) {
                                return $q.reject(res);
                            }
                            return $q.resolve(res);
                        } else {
                            if (res.status === 401) {
                                goLoginPage();
                            }
                        }
                        return $q.reject(res);
                    }
                };
            }]);

            $httpProvider.defaults.withCredentials = opts.withCredentials;
            $httpProvider.defaults.cache = opts.cache;
            $httpProvider.interceptors.push(opts.name);
        }
    ]);

    return ngApp;
}

/***/ }),
/* 7 */
/***/ (function(module, exports) {

window.mam.module = window.mam.module || {};

mam.module.add = function (opts) {
    var module = {
        name: opts.name,
        app: opts.app,
        version: opts.version,
        path: opts.requireModule.uri.substr(0, opts.requireModule.uri.lastIndexOf('/') + 1),
        routes: {},
        options: {}
    };
    mam.module[opts.name] = module;

    module.init = function (options) {
        module.options = $.extend({}, {
            parentRoute: ''
        }, options);
        if (opts.init(module, options) !== true) {
            mam.ng.config(module.app);
            mam.ng.registerRoutes(module.app, opts.routes, {
                path: module.path,
                parentRoute: module.options.parentRoute,
                ctrlPrefix: module.name
            });

            _.forEach(mam.ng.handleRoutes(opts.routes, ''), function (val, key) {
                module.routes[key] = module.options.parentRoute + key;
            });

            mam.language.append(module.path + 'assets/lang/${lang}.js');
        }
        return module;
    }

    return module;
}

/***/ }),
/* 8 */
/***/ (function(module, exports) {


mam.ng.registerRoutes = function (ngApp, routes, options) {
    var opts = $.extend({}, {
        parentRoute: '',
        path: '',
        ctrlPrefix: '',
        ctrlSuffix: 'Ctrl',
        viewDir: 'view',
        controllerDir: 'controllers'
    }, options);
    if (_.isFunction(routes)) {
        routes = routes();
    }

    ngApp.config([
        '$urlRouterProvider', '$stateProvider', '$controllerProvider',
        function ($urlRouterProvider, $stateProvider, $controllerProvider) {
            function loader(app, dependencies, ctrlName) {
                var definition = {
                    resolver: ['$q', '$rootScope',
                        function ($q, $rootScope) {
                            var defered = $q.defer();
                            var require = window.require;
                            require(dependencies, function (item) {
                                $rootScope.$apply(function () {
                                    $controllerProvider.register(item.name || ctrlName, item);
                                    defered.resolve();
                                });
                            });
                            return defered.promise;
                        }]
                };
                return definition;
            }

            function handleName(name) {
                return name.replace(/\/([a-z])/g, function (all, letter) {
                    return letter.toUpperCase();
                });
            }

            angular.forEach(mam.ng.handleRoutes(routes, ''), function (route, name) {
                if (route.home) {
                    $urlRouterProvider.when('', route.url);
                    $urlRouterProvider.when('/', route.url);
                }
                if (route.cv != null) {
                    route.templateUrl = opts.path + opts.viewDir + '/' + route.cv + '.html';
                    route.ctrl = route.cv;
                }
                if (route.ctrl != null) {
                    var ctrl = route.ctrl;
                    if (opts.ctrlPrefix != '') {
                        ctrl = opts.ctrlPrefix + '/' + route.ctrl;
                    }
                    route.controller = handleName(ctrl) + opts.ctrlSuffix;
                    route.resolve = [opts.path + opts.controllerDir + '/' + route.ctrl];
                }
                if (_.isArray(route.resolve)) {
                    route.resolve = loader(ngApp, route.resolve, route.controller);
                }
                $stateProvider.state(opts.parentRoute + name, route);
            });
        }
    ]);
    return ngApp;
}

mam.ng.handleRoutes = function (routes, initRoutes) {
    var tempRoutes = {};
    var selfRoutes = '';
    var parentRoutes = initRoutes ? initRoutes : '';

    (function handleRoutes(routes, parentRoutes) {
        for (var i in routes) {
            if (routes[i].children && !_.isEmpty(routes[i].children)) {
                selfRoutes += i + '.';
                tempRoutes[parentRoutes + i] = routes[i];
                if (typeof (routes[i].children) === 'object') {
                    handleRoutes(routes[i].children, selfRoutes)
                }
            } else {
                tempRoutes[parentRoutes + i] = routes[i];
            }
            if (parentRoutes !== selfRoutes) {
                selfRoutes = parentRoutes;
            }
        }
        return tempRoutes;
    })(routes, parentRoutes)
    return tempRoutes;
}

/***/ }),
/* 9 */
/***/ (function(module, exports) {

angular.module('mam-ng')
    .directive('mamAllCheckbox', function () {
        return {
            restrict: 'A',
            link: function (scope, element, attr) {
                attr.property = attr.property || 'selected';

                element.bind('change', function (e) {
                    scope.$apply(function () {
                        var value = element.prop('checked');
                        angular.forEach(scope.$eval(attr.collection), function (item) {
                            item[attr.property] = value;
                        });
                    });
                });
                
                scope.$watch(function () { //仅监听对应属性，避免过多的响应和遇到特殊属性导致js报错
                    return _.map(scope.$eval(attr.collection), function (item) {
                        return item[attr.property];
                    });
                }, function (newVal) {
                    var hasTrue, hasFalse;
                    angular.forEach(newVal, function (item) {
                        if (item) {
                            hasTrue = true;
                        } else {
                            hasFalse = true;
                        }
                    });
                    scope.$eval(attr.ngModel + ' = ' + ((hasTrue && hasFalse) ? false : hasTrue));
                }, true);
            }
        };
    });

/***/ }),
/* 10 */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less__ = __webpack_require__(62);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__style_less__);
 

angular.module('mam-ng')
    .provider('$mamBackToTop', function () {
        var defaults = this.defaults = {
            text: '回到顶部',
            threshold: 400,
            icon: __webpack_require__(78)
        }
        this.$get = function () {
            return { defaults: defaults };
        };
    })
    .directive('mamBackToTop', ['$mamBackToTop', function ($mamBackToTop) {
        var opts = $mamBackToTop.defaults;
        return {
            restrict: "E",
            replace: true,
            template: '<div class="mam-back-top" title="{{text}}">' + opts.icon + '</div>',
            scope: {
                container: '@?',
                text: '@?',
                threshold: '@?'
            },
            link: function (scope, element, attr) {
                scope.text = scope.text || opts.text;
                var $e = $(element);
                var container = $(scope.container || window);
                if (_.isUndefined(scope.threshold)) {
                    scope.threshold = opts.threshold;
                } else {
                    scope.threshold = _.toNumber(scope.threshold);
                }

                function check() {
                    if (container.scrollTop() > scope.threshold) {
                        $e.fadeIn(200);
                    } else {
                        $e.fadeOut(200);
                    }
                }

                container.scroll(function () { check(); });

                $e.click(function () {
                    if (container[0] == window) {
                        $('body,html').animate({ scrollTop: 0 }, 500);
                    } else {
                        container.animate({ scrollTop: 0 }, 500);
                    }
                });

                check();
            }
        };
    }]);

/***/ }),
/* 11 */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less__ = __webpack_require__(63);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__style_less__);


angular.module('mam-ng')
    .directive('mamBadge', function () {
        return {
            restrict: 'E',
            template: '<div class="mam-badge"><span ng-if="count>0">{{count}}</span></div>',
            scope: {
                count: '<',
                overflowCount: '<?'
            },
            link: function (scope, element, attr) {

                scope.$watch('count', function () {
                    

                });
            }
        };
    });

/***/ }),
/* 12 */
/***/ (function(module, exports) {

/** 文本超出，中间打省略号，element.parent必须固定宽度，element的white-space必须为nowrap */
angular.module('mam-ng')
	.directive('calCenterEllipsis', ["$timeout", function ($timeout) {
		return {
            restrict: 'A',
			link: function (scope, element, attr) {
                $timeout(function(){
                    if (element.outerWidth() > element.parent().width())
                    {
                        var middleIndex;
                        while (element.outerWidth() > element.parent().width())
                        {
                            element.text(element.text().replace(/\.\.\./, ""));
                            middleIndex = element.text().length / 2;
                            element.text(element.text().substring(0, middleIndex)
                                + "..." + element.text().substring(middleIndex + 1, element.text().length));
                        }
                    }
                });
			}
		};
	}]);

/***/ }),
/* 13 */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less__ = __webpack_require__(64);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__style_less__);


angular.module('mam-ng')
	.directive('mamCaptcha', [function () {
		return {
			restrict: 'E',
			replace: true,
			template: '<img class="mam-captcha"/>',
			link: function (scope, element, attr) {

				function load() {
					element.attr('src', mam.path('~/user/captcha?t=' + new Date().getTime()))
				}
		
				scope.$on('mam-captcha-refresh' , load);

				element.on('click', load);

				load();
			}
		};
	}]);

/***/ }),
/* 14 */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less__ = __webpack_require__(65);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__style_less__);


angular.module('mam-ng')
    .provider('$mamCheckbox', function () {
        var defaults = this.defaults = {
            class: 'mam-checkbox',
            icon: __webpack_require__(79)
        }
        this.$get = function () {
            return { defaults: defaults };
        };
    })
    .directive('mamCheckbox', ['$parse', '$mamCheckbox', function ($parse, $mamCheckbox) {
        var opts = $mamCheckbox.defaults;
        return {
            restrict: 'A',
            link: function (scope, element, attr) {
                var label = element.parent('label');
                if (!label.hasClass(opts.class)) {
                    label.addClass(opts.class);
                }
                label.append(opts.icon);

                //ng-checked模式
                if (attr.ngChecked) {
                    scope.$watch(attr.ngChecked, function (newVal) {
                        setCheckedStyle(newVal);
                    }, false);
                }
                //ng-model模式
                if (attr.ngModel) {
                    scope.$watch(attr.ngModel, function (newVal) {
                        setCheckedStyle(newVal);
                    }, false);
                }
                function setCheckedStyle(val) {
                    if (angular.isString(val)) {
                        val = (val.toLowerCase() == 'true');
                    }
                    if (val) {
                        label.addClass('checked').removeClass('unchecked');
                    } else {
                        label.addClass('unchecked').removeClass('checked');
                    }
                }

                element.on('focus', function () {
                    label.addClass('focus');
                }).on('blur', function () {
                    label.removeClass('focus');
                });
            }
        };
    }]);

/***/ }),
/* 15 */
/***/ (function(module, exports) {

angular.module('mam-ng')
    .directive('mamDatepicker', [function () {
        return {
            restrict: 'EA',
            scope: {
                minDate : "@",
                maxDate : "@",
                ctrlType : "@",
                showTime : "@"
            },
            link: function (scope, element, attr) {
                $.datetimepicker.setLocale('ch');
                var obj = { showSecond: true, formatTime: 'H:m:s', format: 'Y-m-d H:m:s' };

                if (scope.minDate !== 'no') {
                    obj.minDate = scope.minDate;
                }
                if (scope.maxDate !== 'no') {
                    obj.maxDate = scope.maxDate;
                }

                obj.onClose = function () {
                    if (element.val() != "") {
                        scope.ngModel = element.val();

                    }
                    element.find("input[type=text]").blur();
                    scope.$apply();
                }
                if (scope.ctrlType === "3") {
                    obj.datepicker = false;
                    obj.format = 'H:i:s';
                }
                if (scope.ctrlType === "2") {
                    obj.timepicker = false;
                    obj.format = 'Y-m-d';
                    if (scope.ngModel != null && scope.ngModel !== '' && scope.ngModel.length > 18)
                        scope.ngModel = scope.ngModel.substring(0, 10);
                }

                if (scope.step !== 'no' && typeof (scope.step) !== "undefined") {
                    obj.step = scope.step;
                }

                if (scope.ctrlType !== 3 && scope.controlData != undefined) {
                    switch (scope.controlData.type) {
                        case "onlypass":
                            obj.maxDate = '0'; //只能选今天以前-仅过去时间
                            break;
                        case "onlyfuture":
                            obj.minDate = '0';//只能选今天以后-仅未来时间
                            break;
                    }
                }
                element.datetimepicker(obj);
            }
        };
    }]);

/***/ }),
/* 16 */
/***/ (function(module, exports) {

angular.module('mam-ng')
    .directive('mamEntityView', function () {
        return {
            restrict: 'A',
            scope: {
                viewtype : "@",  //type='edit' or type='browse'
                entity : "<"
            },
            link: function (scope, element, attr) {
                var url = mam.entity.getViewEntityUrl(scope.entity, scope.viewtype);

                function init(){
                    if (element[0].tagName === "A")
                    {
                        element.attr("href", url);
                        element.attr("target", "_blank");
                    }
                }

                init();
            }
        };
    });

/***/ }),
/* 17 */
/***/ (function(module, exports) {

angular.module('mam-ng').directive('mamHref', function () {
    return {
        restrict: 'A',
        link: function (scope, element, attr) {
            var href = mam.path(attr.mamHref);
            element.attr('href', href);
        }
    };
});

/***/ }),
/* 18 */
/***/ (function(module, exports) {

angular.module('mam-ng')
    .directive('imageFileSelector', function () {
        return {
            restrict: 'E',
            template: "<input type='file' style='display:none' id='{{inputId}}' accept='image/gif,image/jpeg,image/jpg,image/png' />",
            scope: {
                inputId: '@', //源地址
                onChange: '&',
                onError: '&'
            },
            replace: true,
            link: function (scope, element, attr) {
                element.bind('change', function (event) {
                    var file = element[0].files[0];
                    if (file == null) {
                        return;
                    }
                    var reader = new FileReader();
                    reader.onload = function () {
                        scope.onChange({
                            file: file,
                            base64: this.result
                        });
                        element.val('');
                    }
                    reader.onerror = function () {
                        scope.onError();
                    }
                    reader.readAsDataURL(file);
                });
            }
        };
    });

/***/ }),
/* 19 */
/***/ (function(module, exports) {

angular.module('mam-ng').directive('mamImage', function () {
    return {
        restrict: 'E',
        template: '<img />',
        scope: {
            url: '<',   //需要加载的图片地址
            def: '@',   //图片地址为空时使用的地址
            error: '@?',//加载失败时的图片
        },
        transclude: false,
        replace: true,
        link: function (scope, element, attr) {
            var $img = $(element);

            if (_.isEmpty(scope.error)) {
                scope.error = scope.def;
            }

            scope.$watch('url', function () {
                var src = _.isEmpty(scope.url) ? scope.def : scope.url;
                src = mam.path(src);
                $img.one('error', function () {
                    $img.attr('src', mam.path(scope.error));
                });
                $img.attr('src', src);
            });

        }
    };
});

/***/ }),
/* 20 */
/***/ (function(module, exports) {

angular.module('mam-ng')
    .directive('mamInputLimit', function () {
        return {
            restrict: 'A',
            scope: {
                limitStr: '@?',
                ngModel: '='
            },
            link: function (scope, element, attr) {
                var $e = $(element);
                var chars = ['\\', '/', '"', ':', '*', '?', '<', '>', '|'];
                var isShow = false;
                var timer = 0;
                if (scope.limitStr) {
                    var chars = scope.limitStr.split('');
                }
                eval("scope.limitStr = /[" + chars.join('\\') + "]/g");
                var tip = $('<div style="display:none;" class="mam-input-limit"><span>文件名不能包含以下任何字符：<p>' + chars.join(' ') + '</p></span></div>');
                $e.after(tip);

                function autoClose(){
                    timer = setTimeout(function () {
                        tip.fadeToggle();
                        isShow = false;
                    }, 3000);
                }

                $e.on("keyup", function (e) {
                    if (e.keyCode != 16 && e.keyCode != 17) {
                        clearTimeout(timer);
                        if (scope.limitStr.test(scope.ngModel)) {
                            if (!isShow) {
                                isShow = true;
                                tip.fadeToggle();
                            } 
                            scope.ngModel = scope.ngModel.replace(scope.limitStr, '');
                            scope.$apply();
                            autoClose();
                        } else {
                            if (isShow) {
                                isShow = false;
                                tip.fadeToggle();
                            }
                        }
                    }
                });
            }
        };
    });

/***/ }),
/* 21 */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less__ = __webpack_require__(66);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__style_less__);


angular.module('mam-ng')
    .provider('$mamKeyframe', function () {
        var defaults = {
            extKeyframes: [],
            typeKeyframes: [],
            other: '',
            folder: ''
        };
        function getKeyframeByCode(keyframes, code) {
            if (_.isArray(keyframes)) {
                var item = _.find(keyframes, { 'code': code });
                if (item != null) {
                    return item.keyframe;
                }
            }
            return '';
        }
        this.setDefaults = function (options) {
            defaults = $.extend({}, defaults, options);
            if (_.isEmpty(defaults.folder)) {
                defaults.folder = getKeyframeByCode(defaults.extKeyframes, 'folder');
            }
            if (_.isEmpty(defaults.other)) {
                defaults.other = getKeyframeByCode(defaults.typeKeyframes, 'other');
            }
        }

        this.$get = function () {
            return { defaults: defaults };
        };
    })
    .directive('mamKeyframe', ['$mamKeyframe', function ($mamKeyframe) {
        var opts = $mamKeyframe.defaults;

        return {
            restrict: 'E',
            template: '<div class="mam-keyframe"><img /></div>',
            scope: {
                src: '=',   //关键帧地址
                ext: '<?',  //素材扩展名
                type: '<?'  //素材类型
            },
            transclude: false,
            replace: true,
            link: function (scope, element, attr) {

                var $img = element.find('img');

                function useDefault() {
                    element.addClass('mam-keyframe-default');
                    if (scope.type == 'folder') {
                        return load(opts.folder);
                    }

                    if (!_.isEmpty(scope.ext)) {
                        var item = _.find(opts.extKeyframes, function (n) {
                            if (_.isArray(n.extensions)) {
                                return n.extensions.indexOf(scope.ext) != -1;
                            }
                            return false;
                        });
                        if (item != null) {
                            return load(item.keyframe);
                        }
                    }
                    if (!_.isEmpty(scope.type)) {
                        var item = _.find(opts.typeKeyframes, { code: scope.type });
                        if (item != null) {
                            return load(item.keyframe);
                            $img.attr('src', mam.path(item.keyframe));
                            return;
                        }
                    }
                    load(opts.other);
                }

                function load(src) {
                    $img.removeClass('mam-keyframe-loaded');
                    $img.attr('src', mam.path(src));
                }
                //todo:增加mam-keyframe-loaded目的是为了解决在图片未加载出来前，视频类型的出现黑色背景的问题
                //     但相关代码，有点问题。问题是解决。暂时先这样。
                $img.on('load', function () {
                    element.addClass('mam-keyframe-loaded');
                });

                scope.$watchGroup(['src', 'type', 'ext'], function (newValue, oldValue, scope) {
                    if (!_.isEmpty(scope.ext)) {
                        scope.ext = scope.ext.toLowerCase();
                    }

                    if (!_.isEmpty(scope.type)) {
                        //正常情况下type只会从没值变化成有值，不会出现从 video 变成  audio
                        element.addClass('mam-keyframe-type-' + scope.type);
                    }

                    if (_.isEmpty(scope.src)) {
                        useDefault();
                    } else {
                        element.removeClass('mam-keyframe-default');
                        $img.one('error', useDefault);
                        load(scope.src);
                    }

                });

            }
        };
    }]);

/***/ }),
/* 22 */
/***/ (function(module, exports, __webpack_require__) {

__webpack_require__(67);

angular.module('mam-ng')
    .directive('mamLogo', ['$sce', function ($sce) {
        return {
            restrict: 'E',
            template: __webpack_require__(54),
            transclude: true,
            replace: true,
            scope: {
                config : "<"
            },
            link: function (scope, element, attr) {
                scope.config = scope.config || nxt.config;

                function init() {
                }

                init();
            }

        };
    }]);

/***/ }),
/* 23 */
/***/ (function(module, exports, __webpack_require__) {

__webpack_require__(68);

angular.module('mam-ng')
    .provider('$mamPager', function () {
        var defaults = this.defaults = {
            text: {
                total: l('com.pageTotal', '总共'),
                record: l('com.pageRecord', '条'),
                index: l('com.pageIndex', '页码'),
                first: l('com.pageFirst', '首页'),
                last: l('com.pageLast', '尾页'),
                prev: l('com.pagePrev', '上一页'),
                next: l('com.pageNext', '下一页')
            }
        };
        this.$get = function () {
            return { defaults: defaults };
        };
    })
    .directive('mamPager', ['$mamPager', function ($mamPager) {
        var opts = $mamPager.defaults;
        return {
            restrict: 'E',
            template: __webpack_require__(55),
            replace: true,
            scope: {
                recordTotal: '<?',
                pageIndex: '<?',
                pageSize: '<?',
                pageTotal: '<?',
                showText: '<?',
                pageChanged: '&?',
                text: '<?',
                maxSize: '@?'
            },
            link: function (scope, element, attr) {
                //debugger;
                scope.textInfo = scope.text || opts.text;
                scope.maxSize = scope.maxSize || 7;

                if (scope.showText == undefined) {
                    scope.showText = true;
                }

                scope.$watch('pageTotal', function (item) {
                    //如果在html上用ng-if来做这个事情。会因为ng-if会产生一个作用域，导致一个bug。
                    if (scope.pageTotal > 1) {
                        element.find('.pagination').show();
                    } else {
                        element.find('.pagination').hide();
                    }
                });

                scope.$watch('text', function () {
                    if (scope.text)
                    {
                        scope.textInfo = $.extend({}, opts.text, scope.text);
                    }
                });

                scope.change = function (item) {
                    scope.pageChanged({ page: item });
                }

                // 监控你的页码 ， 发生改变既请求
                scope.$watch('recordTotal', function (item) {
                    scope.pageTotal = Math.ceil(scope.recordTotal / scope.pageSize);
                    if (scope.pageIndex > 1 && scope.pageIndex > scope.pageTotal) {
                        scope.change(scope.pageTotal);
                    }
                });
            }
        };
    }]);

/***/ }),
/* 24 */
/***/ (function(module, exports, __webpack_require__) {

__webpack_require__(69);

angular.module('mam-ng')
    .provider('$mamRadio', function () {
        var defaults = this.defaults = {
            className: 'mam-radio',
            icon: __webpack_require__(80)
        };

        this.$get = function () {
            return { defaults: defaults };
        };
    })
    .directive('mamRadioGroup', function () {
        return {
            restrict: 'A',
            require: 'ngModel',
            compile: function (element, attr) {
                element.removeAttr('ng-model');
                var items = element.find('input[type="radio"]');
                angular.forEach(items, function (item) {
                    angular.element(item).attr('ng-model', attr.ngModel);
                });
            }
        };

    })
    .directive('mamRadio', ['$mamRadio', function ($mamRadio) {
        var opts = $mamRadio.defaults;
        return {
            restrict: 'A',
            link: function (scope, element, attr, controller) {

                var label = element.parent('label');
                if (!label.hasClass(opts.className)) {
                    label.addClass(opts.className);
                }

                label.append(opts.icon);

                function set(model) {
                    var val = attr.ngValue == null ? element.attr('value') : scope.$eval(attr.ngValue);
                    if (val == model) {
                        label.addClass('checked').removeClass('unchecked');
                    } else {
                        label.addClass('unchecked').removeClass('checked');
                    }
                }

                if (attr.ngModel != null && attr.ngModel != '') {
                    scope.$watch(attr.ngModel, function (newVal) {
                        set(newVal);
                    }, false);
                    if (attr.ngValue != null && attr.ngValue != '') {
                        scope.$watch(attr.ngValue, function (newVal) {
                            set(scope.$eval(attr.ngModel));
                        }, false);
                    }
                    set(scope.$eval(attr.ngModel));
                }

                element.on('focus', function () {
                    label.addClass('focus');
                }).on('blur', function () {
                    label.removeClass('focus');
                });
            }
        };
    }]);

/***/ }),
/* 25 */
/***/ (function(module, exports) {

/*
 *指令名：mam-resize-table,属性指令
 *scope属性：scroll-x。值为true(表格溢出滚动）或false（表格不会溢出）
 *此指令应用于表格的body下的行，必须与ng-repeat指令处于同一位置
 *（由于指令的解析顺序问题，如果此指令位于ng-repeat指令之前，会使得body部分的内容还未解析而取不到）
 *表格的head行部分必须使用'.flex-head'作为类名
 *body部分的每一行必须使用'.flex-item'作为类名
 **/

angular.module('mam-ng').directive('mamResizeTable', function($timeout) {
    return {
        restrict: 'A',
        scope: {
            scrollX: '='
        },
        link: function(scope, element, attrs) {
            $timeout(function() {
                var $table = element.parent().parent(); //表格
                var $rol = $table.find('.flex-item,.flex-head'); //所有行
                var $ceils = $rol.children(); //所有单元格
                var $head = $table.find('.flex-head');
                var $headceils = $head.children();

                $rol.css('width', $table.width() - 2 + 'px');
                $ceils.css('position', 'relative');
                $table.css('overflow-x', scope.scrollX ? 'scroll' : 'hidden');
                $table.css('overflow-y', 'hidden');

                var $resizeline = $('<div class="resize-line"></div>');
                $resizeline.attr('draggable', true);
                $resizeline.css({
                    'width': '0',
                    'height': '100%',
                    'position': 'absolute',
                    'z-index': '9999',
                    'right': '-2px',
                    'top': '0',
                    'border-right': '5px solid transparent',
                    'cursor': 'e-resize'
                });
                var $dashline = $('<div class="dash-line"></div>')
                $dashline.css({
                    'width': '0',
                    'height': $table.height() + 'px',
                    'position': 'absolute',
                    'z-index': '-9999',
                    'right': '0',
                    'top': '0',
                    'border-right': '2px dashed transparent'
                });

                var startPosition = 0;
                var startWidth = 0;
                var currentColumn = 0; //当前单元格所在列的列序号
                var currentArray = []; //当前单元格所在列的所有单元格所组成的数组
                var currentDashLine = '';

                var dragStartFunc = function(e, t) {
                    e.originalEvent.dataTransfer.effectAllowed = "move";
                    startPosition = e.clientX;
                    startWidth = $(t).parent().width();
                    for (var i = 0; i < $(t).parent().parent().children().length; i++) {
                        if ($(t).parent().get(0) == $(t).parent().parent().children().get(i)) {
                            currentColumn = i;
                            currentDashLine = $headceils.find('.dash-line').get(i);
                            break;
                        }
                    }
                    currentArray = lineArr[currentColumn];
                }
                var draggingFunc = function(e) {
                    $(currentDashLine).css('right', -(e.clientX - startPosition) + 'px');
                    $(currentDashLine).css('border-right', '2px dashed gray');
                }
                var dragFunc = function(e) {
                    $(currentDashLine).css('right', '0');
                    $(currentDashLine).css('border-right', '2px dashed transparent');
                    angular.forEach(currentArray, function(o) {
                        $(o).css('flex', '0 1 auto');
                        if (scope.scrollX) {
                            $(o).css('min-width', startWidth + e.clientX - startPosition + 'px');
                        } else {
                            $(o).css('width', startWidth + e.clientX - startPosition + 'px');
                        }
                    })
                }
                $resizeline.on('dragstart', function(e) {
                    var t = this;
                    dragStartFunc(e, t);
                });
                $resizeline.on('drag', function(e) {
                    draggingFunc(e);
                });
                $resizeline.on('dragend', function(e) {
                    dragFunc(e);
                });
                $ceils.on('dragover', function(e) {
                    e.preventDefault();
                    e.originalEvent.dataTransfer.dropEffect = "move";
                    return true;
                });

                $ceils.find('.resize-line').remove(); //先移除后添加，避免ng-repeat造成每个单元格上重复出现多个resize-line
                $ceils.append($resizeline);
                $headceils.find('.dash-line').remove();
                $headceils.append($dashline); //拖动时看到的虚线

                var lineArr = new Array($rol.eq(0).find('.resize-line').length);
                for (var i = 0; i < lineArr.length; i++) {
                    lineArr[i] = [];
                }
                angular.forEach($rol, function(rol, index) {
                    angular.forEach($(rol).find('.resize-line'), function(o, i) {
                        lineArr[i].push($(o).parent());
                    })
                })
            });
        }
    };
});

/***/ }),
/* 26 */
/***/ (function(module, exports) {

angular.module('mam-ng').directive('mamResizer', function () {
    return {
        restrict: 'E',
        scope: {
            direction: '@?',
            width: '@?', //宽度，单位像素，不用带 px
            height: '@?',
            offset: '@?',
            max: '@?',
            left: '@?',
            right: '@?',
            top: '@?',
            bottom: '@?'
        },
        link: function (scope, element, attrs) {
            scope.direction = scope.direction || 'h';

            if (scope.direction == 'h') {
                element.css('cursor', 'ew-resize');
            } else {
                element.css('cursor', 'ns-resize');
            }

            element.on('mousedown', function (event) {
                event.preventDefault();
                element.addClass('active');
                $(document).on('mousemove', mousemove);
                $(document).on('mouseup', mouseup);
            });

            function mousemove(event) {
                if (scope.direction == 'h') {
                    var x = event.pageX;
                    if (scope.offset) {
                        x += parseInt(scope.offset);
                    }
                    if (scope.max && x > scope.max) {
                        x = parseInt(scope.max);
                    }
                    element.css({ left: x + 'px' });
                    $(scope.left).css({ width: x + 'px' });
                    $(scope.right).css({
                        left: (x + parseInt(scope.width)) + 'px'
                    });
                } else {
                    var y = event.pageY;
                    if (scope.offset) {
                        y += parseInt(scope.offset);
                    }
                    if (scope.max && y > scope.max) {
                        y = parseInt(scope.max);
                    }
                    element.css({ top: y + 'px' });
                    $(scope.top).css({ height: y + 'px' });
                    $(scope.bottom).css({ 
                        top: (y + parseInt(scope.height)) + 'px' 
                    });
                }
            }

            function mouseup() {
                element.removeClass('active');
                $(document).unbind('mousemove', mousemove);
                $(document).unbind('mouseup', mouseup);
            }
        }
    };
});

/***/ }),
/* 27 */
/***/ (function(module, exports, __webpack_require__) {

__webpack_require__(70);

angular.module('mam-ng')
    .directive('mamSearchInput', ['$http', function ($http) {
        return {
            restrict: 'E',
            template: __webpack_require__(56),
            replace: true,
            scope: {
                search: '&',
                keywords: '=',
                useHistory: '<?',
                placeholder: '@?'
            },
            link: function (scope, element, attr) {
                var $e = $(element);
                var boxId = '#mam-history-box';
                var historyBox = $e.find(boxId);
                var index = -1;
                var isShow = false;
                scope.text = {
                    placeholder: scope.placeholder || 'keyword'
                };
                scope.selected;

                function SearchHistory() {
                    var self = this;
                    this.items = [];

                    function changeBox() {
                        historyBox.not(historyBox).stop().slideUp(100).parent().find(boxId);
                        historyBox.stop().slideToggle(100);
                    };

                    this.showBox = function ($event, op) {
                        if (!scope.useHistory) return;
                        if ($event != undefined)
                            $event.stopPropagation();
                        function show() {
                            if (!isShow && self.items.length > 0) {
                                scope.selected = undefined;
                                index = -1;
                                isShow = true;
                                changeBox();
                            }
                        }
                        setTimeout(function () {
                            var res = self.get(op ? scope.keywords : undefined);
                            if (res) {
                                res.then(function () {
                                    show();
                                });
                            } else
                                show();
                        }, 100);
                    };

                    this.close = function () {
                        if (isShow) {
                            isShow = false;
                            changeBox();
                        }
                    }

                    this.delete = function (id, $event) {
                        if (!scope.useHistory) return;
                        $event.stopPropagation();
                        $http.delete('~/search/history/' + id, { errorHandle: false }).then(function (res) {
                            if (res.data) {
                                self.get();
                            }
                        }, function (res) {
                            console.error(res);
                        });
                    };

                    this.getBy = function () {
                        self.get(scope.keywords);
                    }

                    this.get = function (keyword) {
                        if (scope.useHistory) {
                            var url = '~/search/tip-search?keyword=';
                            if (keyword)
                                url += keyword;
                            return $http.get(url, { errorHandle: false, showLoader: false }).then(function (res) {
                                if (keyword == scope.keywords || keyword == undefined)
                                    self.items = res.data;
                            }, function (res) {
                                if (res.status == 404) {
                                    self.items = [];
                                    console.info('需要/search/tip-search接口');
                                }
                                console.error(res);
                            });
                        }
                    };

                    (function () {
                        if (!historyBox) {
                            scope.useHistory = false;
                        } else {
                            historyBox.hide();
                            setTimeout(self.get, 5000);
                        }
                    })();
                }

                scope.submit = function (keyword) {
                    if (keyword)
                        scope.keywords = keyword;
                    setTimeout(function () {
                        scope.$apply(function () {
                            scope.search();
                        });
                    }, 50)

                    scope.sh.close();
                }

                scope.changeModel = function (keyword) {
                    if (!keyword)
                        scope.selected = undefined;
                    else
                        scope.selected = keyword;
                }

                function init() {
                    if (scope.keywords == undefined)
                        console.error('检索输入框插件：检索keywords初始值不能为undefined！');
                    scope.sh = new SearchHistory(historyBox);
                    if (scope.useHistory)
                        $e.on('keydown', function (e) {
                            var length = scope.sh.items.length;
                            if (e.keyCode == 38) {
                                index--;
                                if (index <= -2) index = 0;
                                if (index < 0) index = length - 1;
                                scope.selected = scope.keywords = scope.sh.items[index].keyword;
                            } else if (e.keyCode == 40) {
                                index++;
                                if (index >= length) index = 0;
                                scope.selected = scope.keywords = scope.sh.items[index].keyword;
                            } else if (e.keyCode == 27) {
                                scope.selected = undefined;
                                scope.sh.close();
                            } else if (e.keyCode == 13) {
                                element.find('input').blur();
                                scope.submit();
                            }
                            scope.$apply();
                        });

                    $(document).on('click', scope.sh.close);
                }

                init();
            }
        };
    }]);

/***/ }),
/* 28 */
/***/ (function(module, exports) {

angular.module('mam-ng').directive('mamSelectable', function () {
    return {
        restrict: 'A',
        link: function (scope, element, attr) {
            element.on('mousedown', init);

            var box = element,
                mode = attr.mode;

            function segmentsIntr(a, b, c, d) {

                // 三角形abc 面积的2倍
                var area_abc = (a.x - c.x) * (b.y - c.y) - (a.y - c.y) * (b.x - c.x);

                // 三角形abd 面积的2倍
                var area_abd = (a.x - d.x) * (b.y - d.y) - (a.y - d.y) * (b.x - d.x);

                // 面积符号相同则两点在线段同侧,不相交;
                if (area_abc * area_abd >= 0) {
                    return false;
                }

                // 三角形cda 面积的2倍
                var area_cda = (c.x - a.x) * (d.y - a.y) - (c.y - a.y) * (d.x - a.x);
                // 三角形cdb 面积的2倍
                // 注意: 这里有一个小优化.不需要再用公式计算面积,而是通过已知的三个面积加减得出.
                var area_cdb = area_cda + area_abc - area_abd;
                if (area_cda * area_cdb >= 0) {
                    return false;
                }

                return true;
            }

            function init() {
                var firstTime = new Date().getTime(),
                    boxX = event.clientX - parseInt(box.offset().left),
                    boxY = event.clientY - parseInt(box.offset().top) + document.body.scrollTop;
                $(document).on('mouseup', { firstTime: firstTime }, end);

                box.append('<div class="mam-selectable-box"></div>').on('mousemove', { x: event.clientX, y: event.clientY + document.body.scrollTop }, move);

                box.css('position', 'relative');
                $('.mam-selectable-box').css({
                    'position': 'absolute',
                    'top': boxY,
                    'left': boxX,
                    'backgroundColor': 'rgba(0, 196, 244, 0.3)'
                });
                $('body').css('userSelect', 'none').attr('ondragstart', 'return false;');
                $('head').append("<style type='text/css' key='select'>::selection{ background: none; }</style>");
            }

            function move(e) {
                var boxX = event.clientX - e.data.x,
                    boxY = event.clientY - e.data.y + document.body.scrollTop,
                    selectItems = $(attr.itemname),
                    selectBox = $('.mam-selectable-box'),
                    selectBoxTop = selectBox.offset().top,
                    selectBoxBottom = selectBox.get(0).offsetHeight + selectBoxTop,
                    selectBoxLeft = selectBox.offset().left,
                    selectBoxRight = selectBox.get(0).offsetWidth + selectBoxLeft;

                if (boxX > 0 && boxY > 0) {
                    selectBox.css({
                        'width': boxX,
                        'height': boxY
                    })
                } else if (boxX > 0 && boxY < 0) {
                    selectBox.css({
                        'width': boxX,
                        'height': -boxY,
                        'top': event.clientY - parseInt(box.offset().top) + document.body.scrollTop
                    })
                } else if (boxX < 0 && boxY > 0) {
                    selectBox.css({
                        'width': -boxX,
                        'height': boxY,
                        'left': event.clientX - parseInt(box.offset().left)
                    })
                } else if (boxX < 0 && boxY < 0) {
                    selectBox.css({
                        'width': -boxX,
                        'height': -boxY,
                        'top': event.clientY - parseInt(box.offset().top) + document.body.scrollTop,
                        'left': event.clientX - parseInt(box.offset().left)
                    })
                }

                for (var i = 0; i < selectItems.length; i++) {
                    var itemLeft = selectItems.eq(i).offset().left,
                        itemRight = itemLeft + selectItems.eq(i).width(),
                        itemTop = selectItems.eq(i).offset().top,
                        itemBottom = itemTop + selectItems.eq(i).height();

                    if (mode === 'single') {
                        if (selectBoxTop > itemTop && selectBoxTop < itemBottom || selectBoxTop === itemTop) {
                            selectItems.eq(i).css('backgroundColor', 'rgba(0, 196, 244, 0.3)');
                        } else if (itemTop > selectBoxTop && itemBottom < selectBoxBottom) {
                            selectItems.eq(i).css('backgroundColor', 'rgba(0, 196, 244, 0.3)');
                        } else if (selectBoxBottom > itemTop && selectBoxBottom < itemBottom) {
                            selectItems.eq(i).css('backgroundColor', 'rgba(0, 196, 244, 0.3)');
                        } else {
                            selectItems.eq(i).css('backgroundColor', '');
                        }
                    } else if (mode === 'multi') {
                        //横向与纵向
                        if (segmentsIntr({ x: selectBoxLeft, y: selectBoxTop }, { x: selectBoxRight, y: selectBoxTop }, { x: itemLeft, y: itemTop }, { x: itemLeft, y: itemBottom }) ||
                            segmentsIntr({ x: selectBoxLeft, y: selectBoxTop }, { x: selectBoxRight, y: selectBoxTop }, { x: itemRight, y: itemTop }, { x: itemRight, y: itemBottom }) ||
                            segmentsIntr({ x: selectBoxLeft, y: selectBoxBottom }, { x: selectBoxRight, y: selectBoxBottom }, { x: itemLeft, y: itemTop }, { x: itemLeft, y: itemBottom }) ||
                            segmentsIntr({ x: selectBoxLeft, y: selectBoxBottom }, { x: selectBoxRight, y: selectBoxBottom }, { x: itemRight, y: itemTop }, { x: itemRight, y: itemBottom }) ||
                            //纵向与横向
                            segmentsIntr({ x: selectBoxLeft, y: selectBoxTop }, { x: selectBoxLeft, y: selectBoxBottom }, { x: itemLeft, y: itemTop }, { x: itemRight, y: itemTop }) ||
                            segmentsIntr({ x: selectBoxLeft, y: selectBoxTop }, { x: selectBoxLeft, y: selectBoxBottom }, { x: itemLeft, y: itemBottom }, { x: itemRight, y: itemBottom }) ||
                            segmentsIntr({ x: selectBoxRight, y: selectBoxTop }, { x: selectBoxRight, y: selectBoxBottom }, { x: itemLeft, y: itemTop }, { x: itemRight, y: itemTop }) ||
                            segmentsIntr({ x: selectBoxRight, y: selectBoxTop }, { x: selectBoxRight, y: selectBoxBottom }, { x: itemLeft, y: itemBottom }, { x: itemRight, y: itemBottom })) {
                            selectItems.eq(i).css('backgroundColor', 'rgba(0, 196, 244, 0.3)');
                        } else if (itemTop > selectBoxTop && itemBottom < selectBoxBottom && itemLeft > selectBoxLeft && itemRight < selectBoxRight ||
                            itemTop < selectBoxTop && itemBottom > selectBoxBottom && itemLeft < selectBoxLeft && itemRight > selectBoxRight) {
                            selectItems.eq(i).css('backgroundColor', 'rgba(0, 196, 244, 0.3)');
                        } else {
                            selectItems.eq(i).css('backgroundColor', '');
                        }
                    }
                }
            }

            function end(e) {
                var lastTime = new Date().getTime(),
                    selectItems = $(attr.itemname),
                    selectBox = $('.mam-selectable-box'),
                    selectBoxTop = selectBox.offset().top,
                    selectBoxBottom = selectBox.get(0).offsetHeight + selectBoxTop,
                    selectBoxLeft = selectBox.offset().left,
                    selectBoxRight = selectBox.get(0).offsetWidth + selectBoxLeft,
                    items = scope.$eval(attr.mamSelectable);

                if (lastTime - e.data.firstTime > 200) {
                    for (var i = 0; i < selectItems.length; i++) {
                        var itemLeft = selectItems.eq(i).offset().left,
                            itemRight = itemLeft + selectItems.eq(i).width(),
                            itemTop = selectItems.eq(i).offset().top,
                            itemBottom = itemTop + selectItems.eq(i).height();

                        if (mode === 'single') {
                            selectItems.eq(i).css('backgroundColor', '');
                            if (selectBoxTop > itemTop && selectBoxTop < itemBottom || selectBoxTop === itemTop) {
                                if (items[i].selected === undefined) {
                                    items[i].selected = true;
                                } else {
                                    items[i].selected = !items[i].selected;
                                }
                            } else if (itemTop > selectBoxTop && itemBottom < selectBoxBottom) {
                                if (items[i].selected === undefined) {
                                    items[i].selected = true;
                                } else {
                                    items[i].selected = !items[i].selected;
                                }
                            } else if (selectBoxBottom > itemTop && selectBoxBottom < itemBottom) {
                                if (items[i].selected === undefined) {
                                    items[i].selected = true;
                                } else {
                                    items[i].selected = !items[i].selected;
                                }
                            }
                        } else if (mode === 'multi') {
                            selectItems.eq(i).css('backgroundColor', '');
                            if (segmentsIntr({ x: selectBoxLeft, y: selectBoxTop }, { x: selectBoxRight, y: selectBoxTop }, { x: itemLeft, y: itemTop }, { x: itemLeft, y: itemBottom }) ||
                                segmentsIntr({ x: selectBoxLeft, y: selectBoxTop }, { x: selectBoxRight, y: selectBoxTop }, { x: itemRight, y: itemTop }, { x: itemRight, y: itemBottom }) ||
                                segmentsIntr({ x: selectBoxLeft, y: selectBoxBottom }, { x: selectBoxRight, y: selectBoxBottom }, { x: itemLeft, y: itemTop }, { x: itemLeft, y: itemBottom }) ||
                                segmentsIntr({ x: selectBoxLeft, y: selectBoxBottom }, { x: selectBoxRight, y: selectBoxBottom }, { x: itemRight, y: itemTop }, { x: itemRight, y: itemBottom }) ||
                                //纵向与横向
                                segmentsIntr({ x: selectBoxLeft, y: selectBoxTop }, { x: selectBoxLeft, y: selectBoxBottom }, { x: itemLeft, y: itemTop }, { x: itemRight, y: itemTop }) ||
                                segmentsIntr({ x: selectBoxLeft, y: selectBoxTop }, { x: selectBoxLeft, y: selectBoxBottom }, { x: itemLeft, y: itemBottom }, { x: itemRight, y: itemBottom }) ||
                                segmentsIntr({ x: selectBoxRight, y: selectBoxTop }, { x: selectBoxRight, y: selectBoxBottom }, { x: itemLeft, y: itemTop }, { x: itemRight, y: itemTop }) ||
                                segmentsIntr({ x: selectBoxRight, y: selectBoxTop }, { x: selectBoxRight, y: selectBoxBottom }, { x: itemLeft, y: itemBottom }, { x: itemRight, y: itemBottom })) {
                                if (items[i].selected === undefined) {
                                    items[i].selected = true;
                                } else {
                                    items[i].selected = !items[i].selected;
                                }
                            } else if (itemTop > selectBoxTop && itemBottom < selectBoxBottom && itemLeft > selectBoxLeft && itemRight < selectBoxRight ||
                                itemTop < selectBoxTop && itemBottom > selectBoxBottom && itemLeft < selectBoxLeft && itemRight > selectBoxRight) {
                                if (items[i].selected === undefined) {
                                    items[i].selected = true;
                                } else {
                                    items[i].selected = !items[i].selected;
                                }
                            }
                        }
                    }
                } else {
                    for (var q = 0; q < selectItems.length; q++) {
                        selectItems.eq(q).css('backgroundColor', '');
                    }
                }

                element.off('mousemove').children('.mam-selectable-box').remove();
                $(document).off();

                $('body').css('userSelect', '').attr('ondragstart', '');
                $('head').children('style[key="select"]').remove();

                scope.$applyAsync();
            }

        }
    };
});

/***/ }),
/* 29 */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less__ = __webpack_require__(71);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__style_less__);


angular.module('mam-ng')
    .directive('mamSortGroup', function () {
        return {
            restrict: 'E',
            template: __webpack_require__(57),
            replace: true,
            scope: {
                ngModel: '=',
                onChange: '&?' //排序方式发生改变时
            },
            compile: function (element, attr) {
                var icon = __webpack_require__(3);
                var $icon1 = $(icon);
                $icon1.attr('ng-if', 'ngModel.current.hideDirection!==true')
                    .attr('ng-class', '{active:ngModel.current.desc==\'desc\'}');
                element.find('button').append($icon1);

                var $icon2 = $(icon);
                $icon2.attr('ng-if', 'item.hideDirection!==true')
                    .attr('ng-class', '{active:item.desc==\'desc\'}');
                element.find('li a').append($icon2);

                return function (scope, element, attr) {
                    scope.icon = __webpack_require__(3);

                    scope.change = function (item) {
                        if (item != scope.ngModel.current) {
                            if (!_.isEmpty(scope.ngModel.storageKey)) {
                                localStorage.setItem(scope.ngModel.storageKey, JSON.stringify(item));
                            }
                            scope.ngModel.current = item;
                            scope.onChange({ item: item });
                        }
                    }

                    function init() {
                        if (scope.ngModel.current == null) {
                            if (scope.ngModel.storageKey != null) {
                                var item = localStorage.getItem(scope.ngModel.storageKey);
                                if (!_.isEmpty(item)) {
                                    scope.ngModel.current = JSON.parse(item);
                                    return;
                                }
                            }
                            scope.ngModel.current = _.find(scope.ngModel.items, { default: true });
                            if (scope.ngModel.current == null) {
                                scope.ngModel.current = scope.ngModel.items[0];
                            }
                        }
                    }

                    init();
                }
            }
        };
    });

/***/ }),
/* 30 */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less__ = __webpack_require__(72);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__style_less__);


angular.module('mam-ng').directive('mamSpin', function () {
    return {
        restrict: 'E',
        template: __webpack_require__(58),
        replace: true,
        scope: {
            text: '@?',
            show: '<'
        },
        link: function (scope, element, attr) {
            var parent = element.parent();
            if (parent.css('position') == 'static' || parent.css('position') == 'initial') {
                parent.css('position', 'relative');
            }
        }
    };
});

/***/ }),
/* 31 */
/***/ (function(module, exports, __webpack_require__) {

__webpack_require__(73);

angular.module('mam-ng')
    .directive('mamSwitchButton', [function () {
        return {
            restrict: 'EA',
            template: __webpack_require__(59),
            transclude: true,
            replace: true,
            require: "ngModel",
            scope: {
                onChange : "="
            },
            link: function (scope, element, attr,ngModel) {
                scope.changeEnable = function(){
                    scope.val = !ngModel.$viewValue;
                    ngModel.$setViewValue(scope.val);
                    if (scope.onChange && typeof(scope.onChange) == "function")
                    {
                        scope.onChange(scope.val);
                    }
                };

                ngModel.$render = function(){
                    scope.val = ngModel.$viewValue;
                };
            }

        };
    }]);

/***/ }),
/* 32 */
/***/ (function(module, exports, __webpack_require__) {

__webpack_require__(74);

//todo:我的消息，支持显示未读消息数量

angular.module('mam-ng')
    .directive('mamTopNav', ['$sce', '$timeout', '$http', function ($sce, $timeout, $http) {
        return {
            restrict: 'E',
            template: __webpack_require__(60),
            transclude: true,
            replace: true,
            scope: {
                ngModel: '<?',
                currentUser: '<?'
            },
            link: function (scope, element, attr) {
                var $e = $(element);
                var $navs = $e.find('.navs');
                var $more = $e.find('.more').hide();
                var $moreSubs = $more.find('.subs');
                var moreWidth;
                var navs = [];
                scope.logged = false;

                function prepare() {
                    var items = [];

                    for (var i = 0; i < scope.ngModel.length; i++) {
                        var item = scope.ngModel[i];
                        // 判断启用状态
                        if (!item.enable) {
                            continue;
                        }
                        // 判断分割线
                        if (item.content == '|') {
                            items.push(item);
                            continue;
                        }
                        //判断模块开关
                        if (_.isString(item.module)) {
                            var key = item.module.indexOf('Enable') == -1 ? (item.module + 'Enable') : item.module;
                            if (nxt.config[key] !== true) {
                                continue;
                            }
                        }
                        // 判断权限
                        if (_.isString(item.appPermission) && item.appPermission.length > 0) {
                            if (!nxt.permission.judge(item.appPermission)) {
                                continue;
                            }
                        }

                        if (_.isString(item.content)) {
                            item.content = $sce.trustAsHtml(item.content);
                        }
                        if (!item.target || item.target === 'auto') {
                            item.target = '_self';
                        }
                        item.href = mam.utils.eval(mam.path(item.href));
                        items.push(item);
                    }

                    for (var i = 0; i < items.length; i++) {
                        //去除第一个和连续的分割线
                        if (items[i].content == '|') {
                            if (i == 0 || items[i - 1].content == '|') {
                                continue;
                            }
                        }
                        // if (items[i].class == 'nav-message') {
                        //     getUnreadCount();
                        // }
                        var nav = renderNav(items[i]);
                        navs.push(nav);
                        $navs.append(nav);
                    }

                    _.forEach(navs, function (item, i) {
                        scope.ngModel[i].width = item.outerWidth();
                    });

                    moreWidth = $more.outerWidth();

                    if (navs.length > 0) {
                        $more.hover(function () {
                            $moreSubs.stop().css('height', 'auto').slideDown(300);
                        }, function () {
                            $moreSubs.stop().slideUp(300);
                        }).show();
                    } else {
                        $more.hide();
                    }

                    $timeout(function() {
                        resize();
                    }, 500);
                }

                function resize() {
                        $navs.html('');
                        $moreSubs.html('');
                        var boxWidth = $e.outerWidth();
                        var usedWidth = moreWidth;
                        var index = -1;
                        _.forEach(navs, function (item, i) {
                            usedWidth += scope.ngModel[i].width;
                            if (usedWidth <= boxWidth) {
                                $navs.append(item.removeClass('more-item').addClass('item'));
                            } else {
                                index = i;
                                return false;
                            }
                        });
                        if (index > -1) {
                            var width = 0;
                            for (var i = index; i < navs.length; i++) {
                                width += scope.ngModel[i].width;
                            }
                            if (width > moreWidth) {
                                for (var i = index; i < navs.length; i++) {
                                    $moreSubs.append(navs[i].removeClass('item').addClass('more-item'));
                                }
                                $moreSubs.css({ top: $more.height() });
                                $more.show();
                            } else {
                                for (var i = index; i < navs.length; i++) {
                                    $navs.append(navs[i].removeClass('more-item').addClass('item'));
                                }
                                $more.hide();
                            }
                        } else {
                            $more.hide();
                        }
                        activeCurrentItem();
                }

                function renderNav(item) {
                    var $item = $('<div class="item" title="' + item.tooltip + '">' +
                        '<a href="' + item.href + '">' + item.content + '</a>' +
                        '</div>');
                    if (item.class) {
                        $item.addClass(item.class);
                    }
                    return $item;
                }

                function activeCurrentItem() {
                    $navs.find('.item').each(function (index, item) {
                        var href = $(item).find('a').attr('href');
                        if (href.indexOf('#!') > -1)//去掉子路由防止顶部菜单不选中
                        {
                            href = href.substring(0, href.indexOf('#!'));
                        }
                        if (href != '' && href != '#' && location.href.indexOf(href) != -1) {
                            $(item).addClass('current');
                        } else {
                            $(item).removeClass('current');
                        }
                    });
                }

                //获取未读消息
                //获取未读消息这块的功能还没调整完
                function getUnreadCount() {
                    if (!_.get(window, 'nxt.config.deskEnable.unreadTip')) {
                        return
                    };
                    scope.badge = {
                        message: 0,
                        share: 0,
                        total: function () {
                            return this.message + this.share;
                        }
                    }
                    $timeout(function () {
                        $http.get('~/user/get-user-unread-count').then(function (res) {
                            scope.badge.message = !res.data ? 0 : res.data;
                        });
                        //获取未读分享
                        $http.get('~/share/get-user-unread-count').then(function (res) {
                            scope.badge.share = !res.data ? 0 : res.data;
                        });
                    }, 4000);
                }

                function init() {
                    scope.currentUser = scope.currentUser || _.get(window, 'nxt.user.current');
                    scope.logged = scope.currentUser != null && scope.currentUser.loginName != 'guest';

                    if (scope.ngModel == null || scope.ngModel == '') {
                        scope.ngModel = _.get(window, 'nxt.config.topNav', []);
                        prepare();
                    } else {
                        scope.$watch('ngModel', function (newValue, oldValue) {
                            if (_.isArray(newValue)) {
                                prepare();
                            }
                        });
                    }

                    scope.$on('refreshCount', function (e, data) {
                        if (_.get(window, 'nxt.config.deskEnable.unreadTip')) { //导航添加未读消息数量
                            if (data == undefined) {
                                scope.badge.message = 0;
                                scope.badge.share = 0;
                            } else {
                                if (data.message != undefined)
                                    scope.badge.message = data.message;
                                if (data.share != undefined)
                                    scope.badge.share = data.share;
                            }
                        }
                    });

                    $(window).on('resize', resize);

                    $(window).bind('hashchange', activeCurrentItem);
                }

                init();
            }

        };
    }]);

/***/ }),
/* 33 */
/***/ (function(module, exports, __webpack_require__) {

__webpack_require__(75);

angular.module('mam-ng').provider('$mamUserAvatar', function () {
    var defaults = this.defaults = {
        errorSrc: mam.path('~/test/img/error.jpg')
    }
    this.$get = function () {
        return { defaults: defaults };
    };
}).directive('mamUserAvatar', ['$timeout','$mamUserAvatar',function ($timeout,$mamUserAvatar) {
    var opts = $mamUserAvatar.defaults;
    return {
        restrict: 'E',
        template: '<div class="mam-user-avatar"><img /></div>',
        scope: {
            src: '<',
        },
        transclude: false,
        replace: true,
        link: function (scope, element, attr) {
            var $src,
                $errorSrc = opts.errorSrc;

            function init(){
                if(scope.src !== undefined) {
                    $src = scope.src;
                } else {
                    $src = $errorSrc;
                }

                var imgObj = new Image();
                $(imgObj).on('load',handleLoad);
                $(imgObj).on('error',handleError);
                imgObj.src = $src;
            }

            function handleLoad() {
                $(element).find('img').attr('src',$src);
            }

            function handleError() {
                $(element).find('img').attr('src',$errorSrc);
            }

            $timeout(function(){
                init();
            });

            scope.$watch('src', function(newval, oldval) {
                if(oldval !== newval) {
                    init();
                }
            })
        }
    }
}]);

/***/ }),
/* 34 */
/***/ (function(module, exports, __webpack_require__) {

__webpack_require__(76);

angular.module('mam-ng')
    .directive('mamUserInfo', ['$sce', function ($sce) {
        return {
            restrict: 'E',
            template: __webpack_require__(61),
            transclude: true,
            scope: {
                currentUser: "<"
            },
            link: function (scope, element, attr) {
                var $e = $(element);


                function init() {
                    scope.logged = true;
                    scope.currentUser = scope.currentUser || nxt.user.current;
                    if (!scope.currentUser || scope.currentUser.loginName === "guest") {
                        scope.logged = false;
                    } else {
                        var $sub = $e.find('.sub-nav');
                        $sub.css({ top: $e.height() });
                        element.hover(function () {
                            $sub.stop().css('height', 'auto').slideDown(300);
                        }, function () {
                            $sub.stop().slideUp(300);
                        });
                    }
                }

                init();

                scope.isCurrent = function (href) {
                    return window.location.href.indexOf(href.replace(/\~/g, '')) !== -1;
                }
            }

        };
    }]);

/***/ }),
/* 35 */
/***/ (function(module, exports) {

/**
 * 表单验证插件，包含directive和service
 *
 * 注：千万不要将form标签放到ng-if等会创建scope的指令内，不然使用此插件会有意想不到的坑
 */
angular.module('mam-ng')
    .service('mamValidationService', function() {
        //非空
        this.nullValidate = function(value) {
            if (value == undefined || value.toString().replace(/(^\s*)|(\s*$)/g, "").length == 0) {
                return false;
            } else {
                return true;
            }
        };

        //验证空格
        this.blankValidate = function(value) {
            if (value == undefined || value.toString().length == 0) {
                return true;
            }
            if (/(\s+)/g.test(value)) {
                return false;
            } else {
                return true;
            }
        };

        //长度
        this.lengthValidate = function(value, min, max) {
            if (value == undefined || value.toString().length == 0) {
                return true;
            }
            if (value.toString().length >= min && value.toString().length <= max) {
                return true;
            } else {
                return false;
            }
        };

        //integer大小
        this.numValueValidate = function(value, min, max) {
            if (value == undefined || value.toString().length == 0) {
                return true;
            }
            if (!isNaN(value) && parseInt(value, 10) >= min && parseInt(value, 10) <= max) {
                return true;
            } else {
                return false;
            }
        };

        //密码验证
        this.pswValidate = function(value, formName, confirmId, $scope) {
            if (!$scope || !$scope[formName]) {
                return true;
            }
            if (!value && !$scope[formName][confirmId]) {
                return true;
            }
            if ($scope[formName][confirmId] != value) {
                return false;
            } else {
                return true;
            }
        };

        //是否数字
        this.numValidate = function(value) {
            if (value == undefined || value.toString().length == 0) {
                return true;
            }
            if (isNaN(value)) {
                return false;
            } else {
                return true;
            }
        };

        //是否是整数
        this.integerValidate = function(value) {
            if (value == undefined || value.toString().length == 0) {
                return true;
            }
            if (!/^-?\d+$/.test(value)) {
                return false;
            }
            return true;
        };

        //是否是正数
        this.positiveNumberValidate = function(value) {
            if (value == undefined || value.toString().length == 0) {
                return true;
            }
            if (isNaN(value) || value < 0) {
                return false;
            }
            return true;
        };

        //日期格式
        this.dateValidate = function(value) {
            if (value == undefined || value.toString().length == 0) {
                return true;
            }
            var thePat = /^\d{4}-(0?[1-9]|1[0-2])-(0?[1-9]|[1-2]\d|3[0-1]) ([0-1]\d|2[0-3]):[0-5]\d:[0-5]\d$/; //带时分秒
            var theShortPat = /^\d{4}-(0?[1-9]|1[0-2])-(0?[1-9]|[1-2]\d|3[0-1])$/; //年月日
            var theShortPat2 = /^\d{4}-(0?[1-9]|1[0-2])$/; //年月
            var theShortPat3 = /^\d{4}$/; //年份
            var theShortPat4 = /^([0-1]\d|2[0-3]):[0-5]\d$/; //时分
            var theShortPat5 = /^([0-1]\d|2[0-3]):[0-5]\d:[0-5]\d$/; //时分秒
            if (thePat.test(value) || theShortPat.test(value) || theShortPat2.test(value) || theShortPat3.test(value) || theShortPat4.test(value) || theShortPat5.test(value)) {
                return true;
            } else {
                return false;
            }
        };

        //特殊字符验证
        this.specialCharValidate = function(value) {
            if (value == undefined || value.toString().length == 0) {
                return true;
            }
            var pattern = new RegExp("[`~!@%#$^&*()=|{}':;',\\[\\]<>/?\\.；：%……+￥（）【】‘”“'。，、？]");
            if (pattern.test(value)) {
                return false;
            } else {
                return true;
            }
        };

        //脚本验证
        this.scriptCharValidate = function(value) {
            if (value == undefined || value.toString().length == 0) {
                return true;
            }
            var pattern = new RegExp("<script(\\s.*)?>.*</script>");
            if (pattern.test(value)) {
                return false;
            } else {
                return true;
            }
        };

        //特殊字符验证
        this.noChineseValidate = function(value) {
            if (value == undefined || value.toString().length == 0) {
                return true;
            }
            if (value.match(/[^\x00-\xff]/ig)) {
                return false;
            } else {
                return true;
            }
        };

        //电话号码验证
        this.phoneValidate = function(value) {
            if (value == undefined || value.toString().length == 0) {
                return true;
            }
            var pat = /(^((([0-9]){3,4}[\\-])|([\\(]([0-9]){3,4}[\\)]))?[0-9]{4,8}$)|(^[0-9]{11}$)/;
            if (pat.test(value)) {
                return true;
            } else {
                return false;
            }
        };

        //验证email
        this.emailValidate = function(value) {
            if (value == undefined || value.toString().length == 0) {
                return true;
            }
            var pat = /^([a-zA-Z0-9_\\.]{1,127}){1}[@]{1}([a-zA-Z0-9_\\.]{1,127}){1}$/;
            if (pat.test(value)) {
                return true;
            } else {
                return false;
            }
        };

        //验证邮编
        this.postcodesValidate = function(value) {
            if (value == undefined || value.toString().length == 0) {
                return true;
            }
            var pat = /^[1-9][0-9]{5}$/;
            if (pat.test(value)) {
                return true;
            } else {
                return false;
            }
        };

        //验证ip
        this.ipValidate = function(value) {
            if (value == undefined || value.toString().length == 0) {
                return true;
            }
            var pat = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
            if (pat.test(value)) {
                return true;
            } else {
                return false;
            }
        };

        this.mobileValidate = function(value) {
            if (value == undefined || value.toString().length == 0) {
                return true;
            }
            var pat = /^[0-9]{11}$/;
            if (pat.test(value)) {
                return true;
            } else {
                return false;
            }
        };

        //UNC地址验证
        this.uncAddressValidate = function(value) {
            if (value == undefined || value.toString().length == 0) {
                return true;
            }
            var pat = /^\\\\.*\\.*$/;
            if (pat.test(value)) {
                return true;
            } else {
                return false;
            }
        };

        //FTP地址验证
        this.ftpAddressValidate = function(value) {
            if (value == undefined || value.toString().length == 0) {
                return true;
            }
            var pat = /^ftp:\/\//;
            if (pat.test(value)) {
                return true;
            } else {
                return false;
            }
        };

        //HDD地址验证
        this.hddAddressValidate = function(value) {
            if (value == undefined || value.toString().length == 0) {
                return true;
            }
            var pat = /^[A-Z,a-z]:\\/;
            if (pat.test(value)) {
                return true;
            } else {
                return false;
            }
        };

        //OSS地址验证
        this.ossAddressValidate = function(value) {
            if (value == undefined || value.toString().length == 0) {
                return true;
            }
            var pat = /^oss:\/\//;
            if (pat.test(value)) {
                return true;
            } else {
                return false;
            }
        };
    });

angular.module('mam-ng')
    .directive('mamValidationList', ['$rootScope', '$compile', '$timeout', '$q', 'mamValidationService', function($rootScope, $compile, $timeout, $q, mamValidationService) {
        function getParentForm($element) {
            if (!$element.parent()[0]) {
                return undefined;
            }
            if ($element.parent()[0].tagName == "HTML") {
                return undefined;
            }
            if ($element.parent()[0].tagName == "FORM") {
                return $element.parent();
            } else {
                return getParentForm($element.parent());
            }
        }

        //执行表单验证
        function validateAll(scope, formName) {
            var defer = $q.defer();
            if (!scope || !scope.$mamValidationInfo) {
                return;
            }
            scope.$mamValidationInfo.$hasValidateAll = true; //当执行全部表单验证方法后，$needValid的作用将取消
            var formValidator = scope.$mamValidationInfo.forms[formName];
            var elementValidator;
            var ngModel;
            for (var eleName in formValidator) {
                elementValidator = formValidator[eleName];
                ngModel = elementValidator.ngModel;
                angular.forEach(ngModel.$formatters, function(formatter) {
                    formatter(ngModel.$viewValue);
                });
            }
            defer.resolve();
            return defer.promise;
        };

        return {
            restrict: "A",
            require: "ngModel",
            compile: function() {
                return {
                    pre: function preLink($scope, $element, $attrs, $ngModel) {},
                    post: function postLink($scope, $element, $attrs, $ngModel) {
                        var $needValid = false; //定义此值避免首次加载页面执行验证，比如非空验证会导致输入框出现红框不好看
                        var form = getParentForm($element);
                        var formName;
                        if (form) {
                            formName = form.attr("name");

                            var formValidator;
                            var elementValidator;
                            if (!$scope.$mamValidationInfo) {
                                $scope.$mamValidationInfo = {};
                            }
                            if (!$scope.$mamValidationInfo.forms) {
                                $scope.$mamValidationInfo.forms = {};
                            }
                            if (!$scope.$mamValidationInfo.forms[formName]) {
                                $scope.$mamValidationInfo.forms[formName] = {};
                            }
                            if (!$scope.$mamValidationInfo.validateAll) {
                                $scope.$mamValidationInfo.validateAll = function(formName) {
                                    return validateAll($scope, formName);
                                };
                                mamValidationService.validateAll = function(formName) {
                                    return validateAll($scope, formName);
                                }
                            }
                            formValidator = $scope.$mamValidationInfo.forms[formName];
                            if (!formValidator[$element.attr("name")]) {
                                formValidator[$element.attr("name")] = {};
                            }
                            elementValidator = formValidator[$element.attr("name")];
                            elementValidator.element = $element;
                            elementValidator.ngModel = $ngModel;
                            if ($attrs.mamValidationList) {
                                var vList = JSON.parse($attrs.mamValidationList.replace(/'/g, "\""));
                                elementValidator.validateList = vList;
                            }

                            var validateList = elementValidator.validateList; //验证列表
                            //添加验证
                            var cusValidate = function(value) {
                                var validity = true;
                                if (validateList && typeof(validateList) == "object" && validateList instanceof Array) {
                                    var validate;
                                    var fun;
                                    var validResult;
                                    for (var i = 0, j = validateList.length; i < j; i++) {
                                        validate = validateList[i];
                                        if (!$needValid && !$scope.$mamValidationInfo.$hasValidateAll) {
                                            continue;
                                        }
                                        fun = mamValidationService[validate.name];
                                        var param = [];
                                        param.push(value);
                                        if (validate.param && typeof(validate.param) == "object" && validate.param instanceof Array) {
                                            for (var x = 0, y = validate.param.length; x < y; x++) {
                                                param.push(validate.param[x]);
                                            }
                                        }
                                        param.push($scope);

                                        validResult = fun.apply(this, param);
                                        if (typeof(validResult) == "boolean") //boolean
                                        {
                                            if (!validResult) {
                                                validity = false;
                                                $ngModel.$setValidity(validate.validity, false);
                                            } else {
                                                $ngModel.$setValidity(validate.validity, true);
                                            }
                                        } else if (typeof(validResult) == "object") //promise
                                        {
                                            validResult.then(function() {
                                                $ngModel.$setValidity(validate.validity, true);
                                            }, function() {
                                                validity = false;
                                                $ngModel.$setValidity(validate.validity, false);
                                            });
                                        }
                                    }
                                }
                                return validity ? value : undefined;
                            }
                            $ngModel.$parsers.push(cusValidate);
                            $ngModel.$formatters.push(cusValidate);
                        }

                        $element.on("blur keydown change", function() {
                            $needValid = true;
                        });
                    }
                };
            }
        };
    }]);

/***/ }),
/* 36 */
/***/ (function(module, exports) {

var app = angular.module('mam-ng');

app.filter('trusted', ['$sce', function ($sce) {
    return function (text) {
        if (_.isString(text)) {
            return $sce.trustAsHtml(text);
        }
        return text;
    };
}]);

app.filter('entityTypeName', function () {
    return function (code) {
        if (_.isEmpty(code)) {
            return '';
        }
        var type = _.find(mam.entity.types, { code: code });
        if (type == null) {
            if (code == 'hypermedia') {
                return l('com.' + code, '图文');
            }
            if (code == 'folder') {
                return l('com.' + code, '文件夹');
            }
            return code;
        }
        return l('com.' + code, type.name);
    }
});

app.filter('entityTypeShortName', function () {
    return function (code, ext) {
        if (nxt.config.extTagEnable) {
            if ((ext == null || ext == '')) {
                if (!nxt.config.typeTagEnable)
                    return '';
            } else{
                var ret = ext.replace('.', '').toUpperCase();
                if (ret.indexOf('?') > -1)
                {
                    ret = ret.substring(0, ret.indexOf('?'));
                }
                return ret;
            }
        }

        if (code == null || code == '' || !nxt.config.typeTagEnable) {
            return '';
        }
        var type = _.find(mam.entity.types, { code: code });
        if (type == null) {
            type = _.find(mam.entity.types, { namespace: code });
        }
        if (type != null) {
            if (type.shortCode != null && type.shortCode.length > 0) {
                return type.shortCode;
            }
        }

        switch (code.toLowerCase()) {
            case 'video':
            case 'biz_sobey_video':
                return 'V';

            case 'audio':
            case 'biz_sobey_audio':
                return 'A';

            case 'picture':
            case 'biz_sobey_picture':
                return 'P';

            case 'document':
            case 'biz_sobey_document':
                return 'D';

            case 'hypermedia':
            case 'biz_sobey_hypermedia':
                return 'H';

            case 'dataset':
                return 'G';

            case 'program':
            case 'sequence':
            case 'scene':
            case 'short':
                return 'C';

            case 'rundown':
            case 'biz_sobey_rundown':
                return 'R';

            case 'script':
            case 'biz_sobey_script':
                return 'S';

            case 'other':
            case 'biz_sobey_other':
                return 'O';

            default:
                return code;
        }
    }
});

app.filter('entityTypeIcon', function () {
    return function (code) {
        var type = _.find(mam.entity.types, { code: code });
        if (type != null && type.icon != null) {
            return type.icon;
        }
        switch (code) {
            case 'video':
                return 'fa fa-film';
            case 'audio':
                return 'fa fa-music';
            case 'picture':
                return 'fa fa-picture-o';
            case 'document':
                return 'fa fa-file-word-o';
            default:
                return 'fa fa-file-o';
        }
    };
});

app.filter('removeHtml', function () {
    return function (html) {
        return mam.utils.removeHtmlTag(html);
    };
});

app.filter('videoQualityFlag', function () {
    return function (val) {
        switch (val) {
            case 1:
                return 'flag-SD';
            case 2:
            case 3:
                return 'flag-HD';
            case 4:
                return 'flag-4K'
            default:
                return '';
        }
    };
});

app.filter('cutString', ['$sce', function ($sce) {
    return function (str, len) {
        var sum = 0,
            newStr = '';

        if (typeof (str) != 'string') { return null };
        if (!(/^[0-9]*[1-9][0-9]*$/).test(len) || len == 0) { return str; };

        var matchList = str.split(/(<font\s\S*>(\S+)<\/font>)/igm);
        // 过滤掉高亮标签
        for (var i = 1; i < matchList.length; i += 3) {
            str = str.replace(matchList[i], matchList[i + 1]);
        }

        //截取字符
        for (var i = 0; i < str.length; i++) {
            sum += str.charCodeAt(i) > 255 ? 2 : 1;
            if ((sum <= len - 2) || ((i == str.length - 1) && (sum > len - 2))) {
                newStr += str.charAt(i);
            } else {
                newStr += '...';
                break;
            }
        }
        // 还原高亮标签
        for (var i = 1; i < matchList.length; i += 3) {
            newStr = newStr.replace(matchList[i + 1], matchList[i]);
        }
        return newStr;
    };
}]);

app.filter('formatExt', function () {
    return function (ext) {
        if (!ext) {
            return '';
        }
        var res = ext.replace('.', '');
        res = res.toUpperCase();
        return res;
    };
});

app.filter('formatSize', function () {
    return function (size, custom) {
        if (size == null) {
            return '-';
        }
        var result = '';
        if (custom) {
            if (size == -1) {
                return '无限制';
            }
            size = size / 1024 / 1024 / 1024;
            if (size <= 0.01 && size > 0) {
                return '0.01 GB';
            } else {
                return (size.toFixed(2)) + ' GB';
            }
        } else {
            result = mam.utils.formatSize(size);
        }
        return result;
    };
});

app.filter('formatDate', function () {
    return function (time, format) {
        if (time == null || time == '') {
            return '';
        }
        var dateTime = new Date();
        if (angular.isDate(time)) {
            dateTime = time;
        } else {
            dateTime = new Date(time.replace(/-/g, '/')); //兼容IE、火狐
        }

        if (format == null || format == '') {
            return dateTime.format('yyyy-MM-dd hh:mm:ss');
        } else {
            return dateTime.format(format);
        }
    };
});

app.filter('comingDateTime', function () {
    return function (time) {
        var result = '';
        var oTime = time.replace(/\s|\-|\/|\:/g, ",");
        var timeArr = oTime.split(',');
        var gotDateTime = {
            y: timeArr[0],
            m: timeArr[1] - 1,
            d: timeArr[2],
            h: timeArr[3],
            mi: timeArr[4],
            s: timeArr[5]
        };
        var futureDate = new Date(gotDateTime.y, gotDateTime.m, gotDateTime.d, gotDateTime.h, gotDateTime.mi, gotDateTime.s);
        var nowDate = new Date();
        var dsec = futureDate.getTime() - nowDate.getTime(); //毫秒差

        var o = parseInt(dsec / 1000); //总秒数
        if (o < 0) {
            result = '0 秒后';
        } else if (o >= 0 && o < 60) {
            result = o + ' 秒后';
        } else if (o >= 60 && o < 3600) {
            result = parseInt(o / 60) + ' 分钟后';
        } else if (o >= 3600 && o < 3600 * 24) {
            result = parseInt(o / 3600) + ' 小时后';
        } else if (o >= 3600 * 24 && o < 3600 * 24 * 30) {
            result = parseInt(o / 3600 / 24) + ' 天后';
        } else if (o >= 3600 * 24 * 30 && o < 3600 * 24 * 30 * 12) {
            result = parseInt(o / 3600 / 24 / 30) + ' 个月后';
        } else if (o >= 3600 * 24 * 30 * 12) {
            result = parseInt(o / 3600 / 24 / 30 / 12) + ' 年后';
        }

        return result;
    };
});

/***/ }),
/* 37 */
/***/ (function(module, exports) {

window.nxt = window.nxt || {};

nxt.user = nxt.user || {};
nxt.permission = nxt.permission || {};

nxt.permission.judge = function (permissions) {
    if (permissions == null || permissions == '') {
        return true;
    }
    if (_.get(nxt, 'user.current') == null) {
        throw '判断前，必须给user.current赋值';
    }
    var user = nxt.user.current;
    if (!_.isArray(user.appPermission) || user.appPermission.length == 0) {
        return false;
    }
    var prefix = nxt.config.systemCode ? nxt.config.systemCode.toLowerCase() + '_' : '';

    if (permissions.indexOf(',') == -1) {
        return _.find(user.appPermission, function (item) { return item.toLowerCase() === (prefix + permissions.toLowerCase()) }) != null;
    } else {
        var items = permissions.split(',');
        var error = 0;
        for (var i = 0; i < items.length; i++) {
            if (_.find(user.appPermission, function (ap) { return ap.toLowerCase() === (prefix + items[i].toLowerCase()) }) == null) {
                error++;
            }
        }
        return error != items.length; //满足一个都算通过
    }
}

/***/ }),
/* 38 */
/***/ (function(module, exports) {

window.mam.utils = window.mam.utils || {};

mam.utils.comingDateTime = function (time, maxUnit, minTime, exactTo) {
    if (!time) {
        return '';
    }
    var result = '';
    var oTime = time.replace(/\s|\-|\/|\:/g, ",");
    var timeArr = oTime.split(',');
    var gotDateTime = {
        y: timeArr[0],
        m: timeArr[1] - 1,
        d: timeArr[2],
        h: timeArr[3],
        mi: timeArr[4],
        s: timeArr[5]
    };
    var futureDate = new Date(gotDateTime.y, gotDateTime.m, gotDateTime.d, gotDateTime.h, gotDateTime.mi, gotDateTime.s);
    var nowDate = new Date();
    var dsec = futureDate.getTime() - nowDate.getTime(); //毫秒差

    var o = parseInt(dsec / 1000); //总秒数
    if (minTime && o > minTime) {
        return '';
    }
    if (o < 0) {
        result = '';
    } else if (o >= 0 && o < 60) {
        result = o + ' 秒后';
    } else if (o >= 60 && o < 3600) {
        result = parseInt(o / 60) + '分钟后';
    } else if ((o >= 3600 && o < 3600 * 24) || maxUnit == 'hour') {
        result = parseInt(o / 3600) + '小时';
        if(exactTo == 'min')
        {
            result += parseInt((o % 3600) / 60) + '分钟';
        }
        result+='后';
    } else if (o >= 3600 * 24 && o < 3600 * 24 * 30) {
        result = parseInt(o / 3600 / 24) + '天后';
    } else if (o >= 3600 * 24 * 30 && o < 3600 * 24 * 30 * 12) {
        result = parseInt(o / 3600 / 24 / 30) + '个月后';
    } else if (o >= 3600 * 24 * 30 * 12) {
        result = parseInt(o / 3600 / 24 / 30 / 12) + '年后';
    }

    return result;
}

/***/ }),
/* 39 */
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(0)(undefined);
// imports


// module
exports.push([module.i, ".mam-back-top {\n  position: fixed;\n  display: none;\n  background-color: #FBFBFB;\n  color: #7B7B7B;\n  font-size: 46px;\n  bottom: 20px;\n  right: 20px;\n  overflow: hidden;\n  width: 46px;\n  height: 46px;\n  text-align: center;\n  line-height: 46px;\n  border: 1px solid #E7E7E7;\n  border-radius: 3px;\n  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);\n  cursor: pointer;\n  z-index: 11;\n  -webkit-transition: all 0.3s;\n  transition: all 0.3s;\n}\n.mam-back-top svg {\n  width: 24px;\n  height: 24px;\n  fill: #b7b7b7;\n  -webkit-transform: translate(0, -5px);\n          transform: translate(0, -5px);\n}\n.mam-back-top:hover {\n  background-color: #e98b11;\n}\n.mam-back-top:hover svg {\n  fill: #fff;\n}\n", ""]);

// exports


/***/ }),
/* 40 */
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(0)(undefined);
// imports


// module
exports.push([module.i, ".mam-badge {\n  background: red;\n  color: #fff;\n  border-radius: 50%;\n  text-align: center;\n}\n", ""]);

// exports


/***/ }),
/* 41 */
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(0)(undefined);
// imports


// module
exports.push([module.i, ".mam-captcha {\n  cursor: pointer;\n}\n", ""]);

// exports


/***/ }),
/* 42 */
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(0)(undefined);
// imports


// module
exports.push([module.i, ".mam-checkbox {\n  position: relative;\n  display: inline-block;\n  min-width: 22px;\n  height: 22px;\n  line-height: 21px;\n  cursor: pointer;\n  margin-bottom: 0;\n  font-weight: normal;\n}\n.mam-checkbox svg {\n  left: 3px;\n  top: 3px;\n  width: 14px;\n  height: 14px;\n  fill: #e98b11;\n  position: absolute;\n  font-size: 12px;\n  -webkit-transition: opacity .2s;\n  transition: opacity .2s;\n}\n.mam-checkbox:before {\n  content: \"\";\n  left: 0;\n  top: 0;\n  position: absolute;\n  border-radius: 4px;\n  border: 1px solid #ccc;\n  width: 20px;\n  height: 20px;\n  -webkit-transition: all 0.3s;\n  transition: all 0.3s;\n  background: #fff;\n  -moz-box-sizing: border-box;\n       box-sizing: border-box;\n}\n.mam-checkbox.checked:before {\n  border-color: #e98b11;\n}\n.mam-checkbox.checked svg {\n  opacity: 1;\n}\n.mam-checkbox.unchecked svg {\n  opacity: 0;\n}\n.mam-checkbox.focus:before,\n.mam-checkbox:hover:before {\n  border-color: #e98b11;\n  box-shadow: 0 0 6px rgba(233, 139, 17, 0.5);\n}\n.mam-checkbox.checked[disabled],\n.mam-checkbox[disabled] {\n  cursor: not-allowed;\n}\n.mam-checkbox.checked[disabled]:before,\n.mam-checkbox[disabled]:before {\n  background: #EAEAEA;\n  border-color: #dcdcdc;\n}\n.mam-checkbox.checked[disabled]:hover:before,\n.mam-checkbox[disabled]:hover:before {\n  border-color: #dcdcdc;\n  box-shadow: none;\n}\n.mam-checkbox.checked[disabled].checked svg,\n.mam-checkbox[disabled].checked svg {\n  fill: #666;\n}\n.mam-checkbox span {\n  margin-left: 26px;\n}\n.mam-checkbox input[type='checkbox'] {\n  position: absolute;\n  clip: rect(0, 0, 0, 0);\n  pointer-events: none;\n}\n.mam-checkbox.sm {\n  min-width: 18px;\n  height: 18px;\n  line-height: 17px;\n}\n.mam-checkbox.sm:before {\n  width: 18px;\n  height: 18px;\n}\n.mam-checkbox.sm svg {\n  width: 12px;\n  height: 12px;\n}\n", ""]);

// exports


/***/ }),
/* 43 */
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(0)(undefined);
// imports


// module
exports.push([module.i, ".mam-keyframe {\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n  -webkit-box-pack: center;\n  -webkit-justify-content: center;\n      -ms-flex-pack: center;\n          justify-content: center;\n}\n.mam-keyframe img {\n  max-width: 100%;\n  max-height: 100%;\n}\n.mam-keyframe-loaded.mam-keyframe-type-video {\n  background-color: #000;\n}\n.mam-keyframe-default.mam-keyframe-loaded.mam-keyframe-type-video {\n  background-color: transparent;\n}\n", ""]);

// exports


/***/ }),
/* 44 */
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(0)(undefined);
// imports


// module
exports.push([module.i, ".app-logo {\n  font-family: \"Microsoft YaHei\", \"Hiragino Sans GB\";\n  margin-right: 0;\n  margin-left: 0;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  float: left;\n  min-width: 230px;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n}\n.app-logo .app-logo-img {\n  -webkit-box-flex: 1;\n  -webkit-flex: 1 0 auto;\n      -ms-flex: 1 0 auto;\n          flex: 1 0 auto;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n  height: 100%;\n}\n.app-logo .app-logo-img img {\n  margin: 0 auto;\n  max-height: 100%;\n  max-width: 100%;\n}\n.app-logo .app-logo-txt .logo-title {\n  min-width: 125px;\n  font-size: 23px;\n}\n.app-logo .app-logo-txt .logo-subtitle {\n  min-width: 125px;\n  font-size: 12px;\n}\n", ""]);

// exports


/***/ }),
/* 45 */
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(0)(undefined);
// imports


// module
exports.push([module.i, ".mam-pager {\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n  -webkit-box-pack: center;\n  -webkit-justify-content: center;\n      -ms-flex-pack: center;\n          justify-content: center;\n}\n.mam-pager .page-total {\n  margin-right: 5px;\n}\n.mam-pager .page-total span {\n  margin: 0 4px;\n  color: #e98b11;\n}\n.mam-pager .pagination {\n  margin-left: 8px;\n}\n.mam-pager .pagination li.active a {\n  color: #e98b11;\n  font-weight: bold;\n}\n.mam-pager .pagination li.active a:hover {\n  background-color: transparent;\n}\n.mam-pager .pagination li.disabled a:hover {\n  color: #484848;\n}\n.mam-pager .pagination a {\n  background-color: transparent;\n  color: #484848;\n  border: none;\n}\n.mam-pager .pagination a:hover {\n  color: #e98b11;\n  background-color: transparent;\n}\n", ""]);

// exports


/***/ }),
/* 46 */
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(0)(undefined);
// imports


// module
exports.push([module.i, ".mam-radio {\n  position: relative;\n  display: inline-block;\n  min-width: 22px;\n  height: 22px;\n  line-height: 21px;\n  cursor: pointer;\n  margin-bottom: 0;\n}\n.mam-radio svg {\n  left: 6px;\n  top: 7px;\n  width: 6px;\n  height: 6px;\n  fill: #e98b11;\n  position: absolute;\n  opacity: 0;\n  -webkit-transition: opacity .2s;\n  transition: opacity .2s;\n}\n.mam-radio:before {\n  content: \"\";\n  left: 0;\n  top: 1px;\n  position: absolute;\n  border: 1px solid #ccc;\n  width: 18px;\n  height: 18px;\n  border-radius: 9px;\n  -webkit-transition: all 0.3s;\n  transition: all 0.3s;\n  -moz-box-sizing: border-box;\n       box-sizing: border-box;\n}\n.mam-radio.checked:before {\n  border-color: #e98b11;\n}\n.mam-radio.checked svg {\n  opacity: 1;\n}\n.mam-radio.unchecked svg {\n  opacity: 0;\n}\n.mam-radio.focus:before,\n.mam-radio:hover:before {\n  border-color: #e98b11;\n  box-shadow: 0 0 6px rgba(233, 139, 17, 0.5);\n}\n.mam-radio.checked[disabled],\n.mam-radio[disabled] {\n  cursor: not-allowed;\n}\n.mam-radio.checked[disabled]:before,\n.mam-radio[disabled]:before {\n  background: #EAEAEA;\n  border-color: #dcdcdc;\n}\n.mam-radio.checked[disabled]:hover:before,\n.mam-radio[disabled]:hover:before {\n  border-color: #dcdcdc;\n  box-shadow: none;\n}\n.mam-radio.checked[disabled].checked svg,\n.mam-radio[disabled].checked svg {\n  fill: #acacac;\n}\n.mam-radio span {\n  margin-left: 26px;\n}\n.mam-radio input[type='radio'] {\n  position: absolute;\n  clip: rect(0, 0, 0, 0);\n  pointer-events: none;\n}\n", ""]);

// exports


/***/ }),
/* 47 */
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(0)(undefined);
// imports


// module
exports.push([module.i, ".mam-search-input {\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-flex: 1;\n  -webkit-flex: 1 0 auto;\n      -ms-flex: 1 0 auto;\n          flex: 1 0 auto;\n  max-width: 800px;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n  position: relative;\n}\n.mam-search-input input {\n  -webkit-box-flex: 1;\n  -webkit-flex: 1 0 auto;\n      -ms-flex: 1 0 auto;\n          flex: 1 0 auto;\n  border: 1px solid #e1e3e2;\n  height: 38px;\n  line-height: 38px;\n  padding: 0 12px;\n  color: #8f8f8f;\n  font-size: 14px;\n  outline: none;\n  -webkit-transition: all 0.3s;\n  transition: all 0.3s;\n  z-index: 101;\n}\n.mam-search-input input:focus {\n  border-color: #e98b11;\n}\n.mam-search-input .mam-history-box {\n  width: 100%;\n  height: 200px;\n  position: absolute;\n  left: 0;\n  top: 38px;\n  z-index: 100;\n  background-color: white;\n  border-radius: 0 0 5px 5px;\n  box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.12);\n}\n.mam-search-input .mam-history-box .history-box {\n  height: 100%;\n  overflow-y: scroll;\n  border-bottom: 1px solid gainsboro;\n}\n.mam-search-input .mam-history-box .history-items {\n  height: 100%;\n  margin: 0;\n  padding-left: 0;\n}\n.mam-search-input .mam-history-box .history-items li {\n  padding: 10px 15px;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-pack: justify;\n  -webkit-justify-content: space-between;\n      -ms-flex-pack: justify;\n          justify-content: space-between;\n  cursor: pointer;\n}\n.mam-search-input .mam-history-box .history-items li .item-delete {\n  line-height: 20px;\n}\n.mam-search-input .mam-history-box .history-items .list-item:hover,\n.mam-search-input .mam-history-box .history-items .list-item-active {\n  background-color: #e98b11;\n}\n.mam-search-input .mam-history-box .history-items .list-item:hover span,\n.mam-search-input .mam-history-box .history-items .list-item-active span {\n  color: white;\n}\n", ""]);

// exports


/***/ }),
/* 48 */
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(0)(undefined);
// imports


// module
exports.push([module.i, ".mam-sort-dropdown button {\n  border-style: none;\n  background-color: transparent;\n}\n.mam-sort-dropdown button:hover {\n  background-color: #f7f7f7;\n}\n.mam-sort-dropdown button svg {\n  fill: #e98b11;\n}\n.mam-sort-dropdown svg {\n  width: 12px;\n  height: 13px;\n  -webkit-transform: translate(0, 2px);\n          transform: translate(0, 2px);\n}\n.mam-sort-dropdown svg.active {\n  -webkit-transform: rotate(180deg);\n          transform: rotate(180deg);\n}\n", ""]);

// exports


/***/ }),
/* 49 */
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(0)(undefined);
// imports


// module
exports.push([module.i, ".mam-spin {\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  background: #fff;\n  opacity: 0.7;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n  -webkit-flex-direction: column;\n      -ms-flex-direction: column;\n          flex-direction: column;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n  -webkit-box-pack: center;\n  -webkit-justify-content: center;\n      -ms-flex-pack: center;\n          justify-content: center;\n}\n.mam-spin .mam-spin-animation > div {\n  width: 12px;\n  height: 12px;\n  margin: 0 1px;\n  background-color: #5da2f0;\n  border-radius: 100%;\n  display: inline-block;\n  -webkit-animation: bouncedelay 1.4s infinite ease-in-out;\n          animation: bouncedelay 1.4s infinite ease-in-out;\n  -webkit-animation-fill-mode: both;\n          animation-fill-mode: both;\n}\n.mam-spin .mam-spin-animation .bounce1 {\n  -webkit-animation-delay: -0.32s;\n          animation-delay: -0.32s;\n}\n.mam-spin .mam-spin-animation .bounce2 {\n  -webkit-animation-delay: -0.16s;\n          animation-delay: -0.16s;\n}\n@-webkit-keyframes bouncedelay {\n  0%,\n  80%,\n  100% {\n    -webkit-transform: scale(0.4);\n            transform: scale(0.4);\n  }\n  40% {\n    -webkit-transform: scale(1);\n            transform: scale(1);\n  }\n}\n@keyframes bouncedelay {\n  0%,\n  80%,\n  100% {\n    -webkit-transform: scale(0.4);\n            transform: scale(0.4);\n  }\n  40% {\n    -webkit-transform: scale(1);\n            transform: scale(1);\n  }\n}\n.mam-spin .mam-spin-text {\n  font-size: 12px;\n}\n", ""]);

// exports


/***/ }),
/* 50 */
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(0)(undefined);
// imports


// module
exports.push([module.i, ".mam-switch-button {\n  display: inline-block;\n}\n.mam-switch-button[disabled=disabled] .mam-switch-button-normal {\n  opacity: .5;\n  cursor: default;\n}\n.mam-switch-button-normal {\n  width: 52px;\n  height: 32px;\n  background: #f0f0f0;\n  border-radius: 16px;\n  border: 1px solid #d4d4d4;\n  position: relative;\n  cursor: pointer;\n  -webkit-transition: background 300ms;\n  transition: background 300ms;\n}\n.mam-switch-button-normal:before {\n  content: '';\n  display: block;\n  width: 30px;\n  height: 30px;\n  border-radius: 50%;\n  background: #dddddd;\n  border: 1px solid #c2c2c2;\n  position: absolute;\n  -webkit-transition: left 300ms;\n  transition: left 300ms;\n  left: 0px;\n}\n.mam-switch-button-on {\n  background: #337ab7;\n}\n.mam-switch-button-on:before {\n  left: 20px;\n}\n", ""]);

// exports


/***/ }),
/* 51 */
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(0)(undefined);
// imports


// module
exports.push([module.i, ".mam-top-nav {\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n  -webkit-box-pack: end;\n  -webkit-justify-content: flex-end;\n      -ms-flex-pack: end;\n          justify-content: flex-end;\n  -webkit-box-flex: 1;\n  -webkit-flex: 1 1 auto;\n      -ms-flex: 1 1 auto;\n          flex: 1 1 auto;\n}\n.mam-top-nav .navs {\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-pack: end;\n  -webkit-justify-content: flex-end;\n      -ms-flex-pack: end;\n          justify-content: flex-end;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n  -webkit-box-flex: 1;\n  -webkit-flex: 1 0 auto;\n      -ms-flex: 1 0 auto;\n          flex: 1 0 auto;\n  height: 100%;\n}\n.mam-top-nav .more {\n  position: relative;\n  -webkit-flex-shrink: 0;\n      -ms-flex-negative: 0;\n          flex-shrink: 0;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-pack: end;\n  -webkit-justify-content: flex-end;\n      -ms-flex-pack: end;\n          justify-content: flex-end;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n  height: 100%;\n}\n.mam-top-nav .more .subs {\n  position: absolute;\n  display: none;\n  height: auto;\n  right: 0;\n  z-index: 1000;\n  background: #fff;\n  border: 1px solid #ccc;\n  box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);\n}\n.mam-top-nav .item {\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n  height: 100%;\n}\n.mam-top-nav .item a {\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  height: 100%;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n  -webkit-flex-direction: column;\n      -ms-flex-direction: column;\n          flex-direction: column;\n  -webkit-box-pack: center;\n  -webkit-justify-content: center;\n      -ms-flex-pack: center;\n          justify-content: center;\n}\n.mam-top-nav .more-item {\n  width: 110px;\n  border-bottom: 1px solid #ccc;\n  height: auto;\n  background: #fff;\n}\n.mam-top-nav .more-item:last-child {\n  border-bottom: none;\n}\n.mam-top-nav .more-item a {\n  display: block;\n  text-align: center;\n  padding: 10px 0;\n}\n.mam-top-nav .more-item i {\n  width: 20px;\n  text-align: center;\n  margin-right: 4px;\n}\n.mam-top-nav .more-item.nav-separator {\n  display: none;\n}\n", ""]);

// exports


/***/ }),
/* 52 */
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(0)(undefined);
// imports


// module
exports.push([module.i, ".mam-user-avatar img {\n  width: 40px;\n  height: 40px;\n}\n", ""]);

// exports


/***/ }),
/* 53 */
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(0)(undefined);
// imports


// module
exports.push([module.i, ".mam-top-nav-user {\n  position: relative;\n  cursor: pointer;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  height: 100%;\n}\n.mam-top-nav-user:hover .fa-angle-down {\n  -webkit-transform: rotate(180deg);\n          transform: rotate(180deg);\n}\n.mam-top-nav-user a.user-info {\n  padding-left: 18px;\n  padding-right: 9px;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n  text-align: center;\n}\n.mam-top-nav-user a.user-info img {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n}\n.mam-top-nav-user a.user-info span {\n  margin: 0 8px;\n  display: block;\n}\n.mam-top-nav-user a.user-info .fa-angle-down {\n  -webkit-transition: all 0.3s;\n  transition: all 0.3s;\n}\n.mam-top-nav-user a.user-info:hover {\n  background-color: transparent;\n}\n.mam-top-nav-user .sub-nav {\n  position: absolute;\n  right: 0;\n  width: 140px;\n  padding: 4px 0;\n  z-index: 1000;\n  background: #fff;\n  border: 1px solid #ccc;\n  box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);\n  display: none;\n}\n.mam-top-nav-user .sub-nav .fa {\n  margin-right: 10px;\n}\n.mam-top-nav-user .sub-nav a {\n  display: block;\n  height: 36px;\n  line-height: 36px;\n  padding: 0 0 0 20px;\n}\n.mam-top-nav-user-nologin {\n  width: auto;\n  height: 100%;\n  padding: 0px 20px;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n  -webkit-box-pack: center;\n  -webkit-justify-content: center;\n      -ms-flex-pack: center;\n          justify-content: center;\n}\n.mam-top-nav-user-nologin a {\n  margin: 0 8px;\n}\n.mam-top-nav-user-nologin a i {\n  display: none;\n}\n", ""]);

// exports


/***/ }),
/* 54 */
/***/ (function(module, exports) {

module.exports = "<div class=\"app-logo\">\r\n    <div class=\"app-logo-img\" ng-class=\"{'app-default-logo':(config.customLogo.logo==null||config.customLogo.logo=='')&& (config.globalLogo==''||config.globalLogo==null)}\">\r\n        <img ng-if=\"(config.customLogo.logo!=null&& config.customLogo.logo!='') || config.globalLogo!=''\" ng-src=\"{{config.customLogo.logo!=null && config.customLogo.logo!=''?config.customLogo.logo:config.globalLogo}}\" />\r\n    </div>\r\n    <div class=\"app-logo-txt\">\r\n        <div class=\"logo-title\" ng-if=\"config.customLogo.mainTitleEnable\" style=\"{{config.customLogo.mainTitleStyle}}\" ng-bind=\"config.customLogo.mainTitle\"></div>\r\n        <div class=\"logo-subtitle\" ng-if=\"config.customLogo.subTitleEnable\" style=\"{{config.customLogo.subTitleStyle}}\" ng-bind=\"config.customLogo.subTitle\"></div>\r\n    </div>\r\n</div>\r\n";

/***/ }),
/* 55 */
/***/ (function(module, exports) {

module.exports = "<div class=\"mam-pager\" ng-class=\"simple?'mam-pager-simple':'mam-pager-full'\">\r\n    <div class=\"page-info\">\r\n        <span ng-show=\"showText || pageTotal==1\" class=\"page-total\">{{textInfo.total}}<span>{{recordTotal}}</span>{{textInfo.record}}</span>\r\n        <span ng-show=\"showText && pageTotal>1\" class=\"page-code\">{{textInfo.index}}<span>{{pageIndex}}</span>/<span>{{pageTotal}}</span></span>\r\n    </div>\r\n    <ul uib-pagination total-items=\"recordTotal\" ng-model=\"pageIndex\" items-per-page=\"pageSize\" boundary-links=\"true\"\r\n        rotate=\"true\" num-pages=\"pageTotal\" previous-text=\"{{textInfo.prev}}\" next-text=\"{{textInfo.next}}\" first-text=\"{{textInfo.first}}\"\r\n        last-text=\"{{textInfo.last}}\" ng-change=\"change(pageIndex)\" boundary-link-numbers=\"true\" max-size=\"maxSize\"></ul>\r\n</div>";

/***/ }),
/* 56 */
/***/ (function(module, exports) {

module.exports = "<div class=\"mam-search-input\">\r\n    <input type=\"text\" ng-change=\"sh.getBy()\" ng-click=\"sh.showBox($event,'click')\" ng-model=\"keywords\"\r\n        placeholder=\"{{text.placeholder}}\">\r\n    <div class=\"mam-history-box\" id=\"mam-history-box\" style=\"display:none;\">\r\n        <div class=\"history-box\">\r\n            <ul class=\"history-items\">\r\n                <li class=\"list-item\" ng-class=\"{'list-item-active':item.keyword==selected}\" ng-repeat=\"item in sh.items\" ng-mouseover=\"changeModel(item.keyword)\"\r\n                    ng-click=\"submit(item.keyword)\" ng-mouseleave=\"changeModel()\">\r\n                    <span class=\"history-word\">{{item.keyword}}</span>\r\n                    <span class=\"item-delete fa fa-trash\" ng-if=\"item.type==0\" ng-click=\"sh.delete(item.id,$event)\"></span>\r\n                </li>\r\n            </ul>\r\n        </div>\r\n    </div>\r\n</div>";

/***/ }),
/* 57 */
/***/ (function(module, exports) {

module.exports = "<div class=\"mam-sort-dropdown btn-group\" mam-dropdown>\r\n    <button type=\"button\" class=\"btn btn-default dropdown-toggle\">\r\n        <span>{{ngModel.current.text}}</span>\r\n    </button>\r\n    <ul class=\"dropdown-menu\">\r\n        <li ng-repeat=\"item in ngModel.items\">\r\n            <a ng-click=\"change(item)\">\r\n                <span>{{item.text}}</span>\r\n            </a>\r\n        </li>\r\n    </ul>\r\n</div>";

/***/ }),
/* 58 */
/***/ (function(module, exports) {

module.exports = "<div class=\"mam-spin\" ng-if=\"show\">\r\n    <div class=\"mam-spin-animation\">\r\n        <div class=\"bounce1\"></div>\r\n        <div class=\"bounce2\"></div>\r\n        <div class=\"bounce3\"></div>\r\n    </div>\r\n    <span class=\"mam-spin-text\" ng-if=\"$parent.text != null && $parent.text.length > 0\">加载中</span>\r\n</div>";

/***/ }),
/* 59 */
/***/ (function(module, exports) {

module.exports = "<div class=\"mam-switch-button\">\r\n    <div class=\"mam-switch-button-normal\" ng-class=\"{true : 'mam-switch-button-on',false : ''}[val]\"\r\n         ng-click=\"changeEnable()\"></div>\r\n</div>";

/***/ }),
/* 60 */
/***/ (function(module, exports) {

module.exports = "<div class=\"mam-top-nav\">\r\n    <div class=\"navs\" ng-show=\"logged\"></div>\r\n    <div class=\"more\" ng-show=\"logged\">\r\n        <div class=\"item\">\r\n            <a>\r\n                <i class=\"fa fa-ellipsis-h\" aria-hidden=\"true\"></i>\r\n                <span>更多</span>\r\n            </a>\r\n        </div>\r\n        <div class=\"subs\"></div>\r\n    </div>\r\n</div>";

/***/ }),
/* 61 */
/***/ (function(module, exports) {

module.exports = "<div class=\"mam-top-nav-user\" ng-show=\"logged\">\r\n\r\n    <a class=\"user-info\">\r\n        <mam-user-avatar src='currentUser.avatarUrl'></mam-user-avatar>\r\n        <div>\r\n            <span class=\"nickName\">{{currentUser.nickName}}</span>\r\n            <span ng-if=\"currentUser.haveOrganization\">({{currentUser.organization.organizationName}})</span>\r\n        </div>\r\n        <i class=\"fa fa-angle-down\"></i>\r\n    </a>\r\n\r\n    <div class=\"sub-nav\">\r\n        <div ng-if=\"scoreEnable && currentUser.loginName !== 'guest'\" class=\"score\">\r\n            <a mam-href=\"~/user/#/center/score/\"><i class=\"fa fa-database\"></i>积分：{{currentUser.additional.score}}</a>\r\n        </div>\r\n        <div ng-show=\"deskShow.link\" ng-class=\"{'current':isCurrent('/desk/#/main/favorite/')}\" ng-if=\"currentUser.loginName !== 'guest'\">\r\n            <a mam-href=\"~/desk/#/main/favorite/\"><i class=\"fa fa-desktop\"></i>工作台</a>\r\n        </div>\r\n        <div ng-class=\"{'current':isCurrent('/desk/#/main/message/')}\" ng-if=\"currentUser.loginName !== 'guest'\">\r\n            <a href=\"/desk/#/main/message/?page=1\"><i class=\"fa fa-envelope-o\"></i>我的消息</a>\r\n        </div>\r\n        <div ng-class=\"{'current':isCurrent('/user/#/center/info')}\" ng-if=\"currentUser.loginName !== 'guest'\">\r\n            <a mam-href=\"~/user/#/center/info\"><i class=\"fa fa-user\"></i>个人中心</a>\r\n        </div>\r\n        <div ng-if=\"currentUser.isAdmin && currentUser.loginName !== 'guest'\" class=\"top-manage\" ng-class=\"{'current':iscurrent('/manage/')}\">\r\n            <a href=\"/manage/\"><i class=\"fa fa-cog\"></i>管理中心</a>\r\n        </div>\r\n        <div ng-if=\"currentUser.isSystemAdmin && currentUser.loginName !== 'guest'\" class=\"top-system\" ng-class=\"{'current':iscurrent('/sysmanage/')}\">\r\n            <a href=\"/sysmanage/\" target=\"_blank\"><i class=\"fa fa-cog\"></i>系统配置</a>\r\n        </div>\r\n        <div>\r\n            <a mam-href=\"~/user/exit\"><i class=\"fa fa-power-off\"></i>退出</a>\r\n        </div>\r\n    </div>\r\n\r\n</div>\r\n<div class=\"mam-top-nav-user-nologin\" ng-show=\"!logged\">\r\n    <a mam-href=\"~/signup/\"><i class=\"fa fa-user-plus\"></i>注册</a>\r\n    <a mam-href=\"~/login\"><i class=\"fa fa-sign-in\"></i>登录</a>\r\n</div>";

/***/ }),
/* 62 */
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(39);
if(typeof content === 'string') content = [[module.i, content, '']];
// Prepare cssTransformation
var transform;

var options = {}
options.transform = transform
// add the styles to the DOM
var update = __webpack_require__(1)(content, options);
if(content.locals) module.exports = content.locals;
// Hot Module Replacement
if(false) {
	// When the styles change, update the <style> tags
	if(!content.locals) {
		module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less", function() {
			var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less");
			if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
			update(newContent);
		});
	}
	// When the module is disposed, remove the <style> tags
	module.hot.dispose(function() { update(); });
}

/***/ }),
/* 63 */
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(40);
if(typeof content === 'string') content = [[module.i, content, '']];
// Prepare cssTransformation
var transform;

var options = {}
options.transform = transform
// add the styles to the DOM
var update = __webpack_require__(1)(content, options);
if(content.locals) module.exports = content.locals;
// Hot Module Replacement
if(false) {
	// When the styles change, update the <style> tags
	if(!content.locals) {
		module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less", function() {
			var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less");
			if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
			update(newContent);
		});
	}
	// When the module is disposed, remove the <style> tags
	module.hot.dispose(function() { update(); });
}

/***/ }),
/* 64 */
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(41);
if(typeof content === 'string') content = [[module.i, content, '']];
// Prepare cssTransformation
var transform;

var options = {}
options.transform = transform
// add the styles to the DOM
var update = __webpack_require__(1)(content, options);
if(content.locals) module.exports = content.locals;
// Hot Module Replacement
if(false) {
	// When the styles change, update the <style> tags
	if(!content.locals) {
		module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less", function() {
			var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less");
			if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
			update(newContent);
		});
	}
	// When the module is disposed, remove the <style> tags
	module.hot.dispose(function() { update(); });
}

/***/ }),
/* 65 */
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(42);
if(typeof content === 'string') content = [[module.i, content, '']];
// Prepare cssTransformation
var transform;

var options = {}
options.transform = transform
// add the styles to the DOM
var update = __webpack_require__(1)(content, options);
if(content.locals) module.exports = content.locals;
// Hot Module Replacement
if(false) {
	// When the styles change, update the <style> tags
	if(!content.locals) {
		module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less", function() {
			var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less");
			if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
			update(newContent);
		});
	}
	// When the module is disposed, remove the <style> tags
	module.hot.dispose(function() { update(); });
}

/***/ }),
/* 66 */
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(43);
if(typeof content === 'string') content = [[module.i, content, '']];
// Prepare cssTransformation
var transform;

var options = {}
options.transform = transform
// add the styles to the DOM
var update = __webpack_require__(1)(content, options);
if(content.locals) module.exports = content.locals;
// Hot Module Replacement
if(false) {
	// When the styles change, update the <style> tags
	if(!content.locals) {
		module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less", function() {
			var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less");
			if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
			update(newContent);
		});
	}
	// When the module is disposed, remove the <style> tags
	module.hot.dispose(function() { update(); });
}

/***/ }),
/* 67 */
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(44);
if(typeof content === 'string') content = [[module.i, content, '']];
// Prepare cssTransformation
var transform;

var options = {}
options.transform = transform
// add the styles to the DOM
var update = __webpack_require__(1)(content, options);
if(content.locals) module.exports = content.locals;
// Hot Module Replacement
if(false) {
	// When the styles change, update the <style> tags
	if(!content.locals) {
		module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less", function() {
			var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less");
			if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
			update(newContent);
		});
	}
	// When the module is disposed, remove the <style> tags
	module.hot.dispose(function() { update(); });
}

/***/ }),
/* 68 */
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(45);
if(typeof content === 'string') content = [[module.i, content, '']];
// Prepare cssTransformation
var transform;

var options = {}
options.transform = transform
// add the styles to the DOM
var update = __webpack_require__(1)(content, options);
if(content.locals) module.exports = content.locals;
// Hot Module Replacement
if(false) {
	// When the styles change, update the <style> tags
	if(!content.locals) {
		module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less", function() {
			var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less");
			if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
			update(newContent);
		});
	}
	// When the module is disposed, remove the <style> tags
	module.hot.dispose(function() { update(); });
}

/***/ }),
/* 69 */
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(46);
if(typeof content === 'string') content = [[module.i, content, '']];
// Prepare cssTransformation
var transform;

var options = {}
options.transform = transform
// add the styles to the DOM
var update = __webpack_require__(1)(content, options);
if(content.locals) module.exports = content.locals;
// Hot Module Replacement
if(false) {
	// When the styles change, update the <style> tags
	if(!content.locals) {
		module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less", function() {
			var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less");
			if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
			update(newContent);
		});
	}
	// When the module is disposed, remove the <style> tags
	module.hot.dispose(function() { update(); });
}

/***/ }),
/* 70 */
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(47);
if(typeof content === 'string') content = [[module.i, content, '']];
// Prepare cssTransformation
var transform;

var options = {}
options.transform = transform
// add the styles to the DOM
var update = __webpack_require__(1)(content, options);
if(content.locals) module.exports = content.locals;
// Hot Module Replacement
if(false) {
	// When the styles change, update the <style> tags
	if(!content.locals) {
		module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less", function() {
			var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less");
			if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
			update(newContent);
		});
	}
	// When the module is disposed, remove the <style> tags
	module.hot.dispose(function() { update(); });
}

/***/ }),
/* 71 */
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(48);
if(typeof content === 'string') content = [[module.i, content, '']];
// Prepare cssTransformation
var transform;

var options = {}
options.transform = transform
// add the styles to the DOM
var update = __webpack_require__(1)(content, options);
if(content.locals) module.exports = content.locals;
// Hot Module Replacement
if(false) {
	// When the styles change, update the <style> tags
	if(!content.locals) {
		module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less", function() {
			var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less");
			if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
			update(newContent);
		});
	}
	// When the module is disposed, remove the <style> tags
	module.hot.dispose(function() { update(); });
}

/***/ }),
/* 72 */
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(49);
if(typeof content === 'string') content = [[module.i, content, '']];
// Prepare cssTransformation
var transform;

var options = {}
options.transform = transform
// add the styles to the DOM
var update = __webpack_require__(1)(content, options);
if(content.locals) module.exports = content.locals;
// Hot Module Replacement
if(false) {
	// When the styles change, update the <style> tags
	if(!content.locals) {
		module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less", function() {
			var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less");
			if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
			update(newContent);
		});
	}
	// When the module is disposed, remove the <style> tags
	module.hot.dispose(function() { update(); });
}

/***/ }),
/* 73 */
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(50);
if(typeof content === 'string') content = [[module.i, content, '']];
// Prepare cssTransformation
var transform;

var options = {}
options.transform = transform
// add the styles to the DOM
var update = __webpack_require__(1)(content, options);
if(content.locals) module.exports = content.locals;
// Hot Module Replacement
if(false) {
	// When the styles change, update the <style> tags
	if(!content.locals) {
		module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less", function() {
			var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less");
			if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
			update(newContent);
		});
	}
	// When the module is disposed, remove the <style> tags
	module.hot.dispose(function() { update(); });
}

/***/ }),
/* 74 */
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(51);
if(typeof content === 'string') content = [[module.i, content, '']];
// Prepare cssTransformation
var transform;

var options = {}
options.transform = transform
// add the styles to the DOM
var update = __webpack_require__(1)(content, options);
if(content.locals) module.exports = content.locals;
// Hot Module Replacement
if(false) {
	// When the styles change, update the <style> tags
	if(!content.locals) {
		module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less", function() {
			var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less");
			if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
			update(newContent);
		});
	}
	// When the module is disposed, remove the <style> tags
	module.hot.dispose(function() { update(); });
}

/***/ }),
/* 75 */
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(52);
if(typeof content === 'string') content = [[module.i, content, '']];
// Prepare cssTransformation
var transform;

var options = {}
options.transform = transform
// add the styles to the DOM
var update = __webpack_require__(1)(content, options);
if(content.locals) module.exports = content.locals;
// Hot Module Replacement
if(false) {
	// When the styles change, update the <style> tags
	if(!content.locals) {
		module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less", function() {
			var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less");
			if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
			update(newContent);
		});
	}
	// When the module is disposed, remove the <style> tags
	module.hot.dispose(function() { update(); });
}

/***/ }),
/* 76 */
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(53);
if(typeof content === 'string') content = [[module.i, content, '']];
// Prepare cssTransformation
var transform;

var options = {}
options.transform = transform
// add the styles to the DOM
var update = __webpack_require__(1)(content, options);
if(content.locals) module.exports = content.locals;
// Hot Module Replacement
if(false) {
	// When the styles change, update the <style> tags
	if(!content.locals) {
		module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less", function() {
			var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less");
			if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
			update(newContent);
		});
	}
	// When the module is disposed, remove the <style> tags
	module.hot.dispose(function() { update(); });
}

/***/ }),
/* 77 */
/***/ (function(module, exports) {


/**
 * When source maps are enabled, `style-loader` uses a link element with a data-uri to
 * embed the css on the page. This breaks all relative urls because now they are relative to a
 * bundle instead of the current page.
 *
 * One solution is to only use full urls, but that may be impossible.
 *
 * Instead, this function "fixes" the relative urls to be absolute according to the current page location.
 *
 * A rudimentary test suite is located at `test/fixUrls.js` and can be run via the `npm test` command.
 *
 */

module.exports = function (css) {
  // get current location
  var location = typeof window !== "undefined" && window.location;

  if (!location) {
    throw new Error("fixUrls requires window.location");
  }

	// blank or null?
	if (!css || typeof css !== "string") {
	  return css;
  }

  var baseUrl = location.protocol + "//" + location.host;
  var currentDir = baseUrl + location.pathname.replace(/\/[^\/]*$/, "/");

	// convert each url(...)
	/*
	This regular expression is just a way to recursively match brackets within
	a string.

	 /url\s*\(  = Match on the word "url" with any whitespace after it and then a parens
	   (  = Start a capturing group
	     (?:  = Start a non-capturing group
	         [^)(]  = Match anything that isn't a parentheses
	         |  = OR
	         \(  = Match a start parentheses
	             (?:  = Start another non-capturing groups
	                 [^)(]+  = Match anything that isn't a parentheses
	                 |  = OR
	                 \(  = Match a start parentheses
	                     [^)(]*  = Match anything that isn't a parentheses
	                 \)  = Match a end parentheses
	             )  = End Group
              *\) = Match anything and then a close parens
          )  = Close non-capturing group
          *  = Match anything
       )  = Close capturing group
	 \)  = Match a close parens

	 /gi  = Get all matches, not the first.  Be case insensitive.
	 */
	var fixedCss = css.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi, function(fullMatch, origUrl) {
		// strip quotes (if they exist)
		var unquotedOrigUrl = origUrl
			.trim()
			.replace(/^"(.*)"$/, function(o, $1){ return $1; })
			.replace(/^'(.*)'$/, function(o, $1){ return $1; });

		// already a full url? no change
		if (/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/)/i.test(unquotedOrigUrl)) {
		  return fullMatch;
		}

		// convert the url to a full url
		var newUrl;

		if (unquotedOrigUrl.indexOf("//") === 0) {
		  	//TODO: should we add protocol?
			newUrl = unquotedOrigUrl;
		} else if (unquotedOrigUrl.indexOf("/") === 0) {
			// path should be relative to the base url
			newUrl = baseUrl + unquotedOrigUrl; // already starts with '/'
		} else {
			// path should be relative to current directory
			newUrl = currentDir + unquotedOrigUrl.replace(/^\.\//, ""); // Strip leading './'
		}

		// send back the fixed url(...)
		return "url(" + JSON.stringify(newUrl) + ")";
	});

	// send back the fixed css
	return fixedCss;
};


/***/ }),
/* 78 */
/***/ (function(module, exports) {

module.exports = "<svg t=\"1498805495948\" class=\"icon\" style viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"3582\" xmlns:xlink=\"http://www.w3.org/1999/xlink\"><defs><style type=\"text/css\"></style></defs><path d=\"M1009.762845 56.889457 14.237155 56.889457C6.386724 56.889457 0.01536 50.546537 0.01536 42.667662L0.01536 14.224071C0.01536 6.37364 6.386724 0.002276 14.237155 0.002276L1009.762845 0.002276C1017.613276 0.002276 1023.98464 6.37364 1023.98464 14.224071L1023.98464 42.667662C1023.98464 50.546537 1017.613276 56.889457 1009.762845 56.889457ZM473.800257 197.087918C476.67306 194.215116 480.00096 192.167177 483.556409 190.773441L483.556409 184.885617C483.556409 177.035187 489.927773 170.663822 497.778205 170.663822L526.221795 170.663822C534.072227 170.663822 540.443591 177.035187 540.443591 184.885617L540.443591 190.773441C543.99904 192.167177 547.32694 194.215116 550.199743 197.087918L997.560544 644.44872C1008.653544 655.570164 1008.653544 673.574957 997.560544 684.696401 986.4391 695.789402 968.434307 695.789402 957.341307 684.696401L540.443591 267.798686 540.443591 1009.749761C540.443591 1017.600192 534.072227 1023.971557 526.221795 1023.971557L497.778205 1023.971557C489.927773 1023.971557 483.556409 1017.600192 483.556409 1009.749761L483.556409 267.798686 66.658693 684.696401C55.565693 695.789402 37.532456 695.789402 26.439456 684.696401 15.318011 673.574957 15.318011 655.570164 26.439456 644.44872L473.800257 197.087918Z\" p-id=\"3583\"></path></svg>"

/***/ }),
/* 79 */
/***/ (function(module, exports) {

module.exports = "<svg t=\"1498706378122\" class=\"icon\" style viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"3878\" xmlns:xlink=\"http://www.w3.org/1999/xlink\"><defs><style type=\"text/css\"></style></defs><path d=\"M505.344 852.928l-434.944-315.392c-17.888-12.96-21.856-37.984-8.896-55.872 12.928-17.888 37.984-21.888 55.872-8.896l367.296 266.368 409.376-619.392c12.192-18.464 36.992-23.456 55.424-11.328 18.432 12.192 23.488 36.992 11.328 55.424L505.344 852.928z\" p-id=\"3879\"></path></svg>"

/***/ }),
/* 80 */
/***/ (function(module, exports) {

module.exports = "<svg t=\"1498738186413\" class=\"icon\" style viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"2536\" xmlns:xlink=\"http://www.w3.org/1999/xlink\"><defs><style type=\"text/css\"></style></defs><path d=\"M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z\" fill p-id=\"2537\"></path></svg>"

/***/ }),
/* 81 */
/***/ (function(module, exports, __webpack_require__) {

if (window.mam == null) {
    throw '该组件依赖 mam-base';
}
window.mam.ng = window.mam.ng || {};

var app = angular.module('mam-ng', ['ui.router']);
app.run([
    '$http',
    function ($http) {
        mam.ng.$http = $http;
    }
]);

__webpack_require__(4);
__webpack_require__(8);
__webpack_require__(6);
__webpack_require__(5);
__webpack_require__(7);

__webpack_require__(37);


__webpack_require__(36);
__webpack_require__(38);

__webpack_require__(9);
__webpack_require__(10);
__webpack_require__(14);
__webpack_require__(2);
__webpack_require__(17);
__webpack_require__(19);
__webpack_require__(18);
__webpack_require__(21);
__webpack_require__(23);
__webpack_require__(24);
__webpack_require__(25);
__webpack_require__(26);
__webpack_require__(28);
__webpack_require__(29);
__webpack_require__(30);
__webpack_require__(32);
__webpack_require__(22);
__webpack_require__(34);
__webpack_require__(33);
__webpack_require__(2);
__webpack_require__(15);
__webpack_require__(35);
__webpack_require__(31);
__webpack_require__(27);
__webpack_require__(20);
__webpack_require__(11);
__webpack_require__(13);
__webpack_require__(12);
__webpack_require__(16);

// require('./directives/inline-editor/') //开发中

/***/ })
/******/ ]);