export interface IBaseEntityTypes {
  src: string; // 资源的路径
  onError: () => void;
  linkWatermark: string
}

export type entityType =
  | 'video'
  | 'audio'
  | 'picture'
  | 'document'
  | 'folder'
  | 'course'
  | 'other'
  | null;

export interface IEntityRes {
  entityName: string;
  extensions: {
    canEdit: boolean;
  };
  fileGroups: any[];
  keyframe: {
    fileGroup: string;
    filePath: string;
    filePathPrefix: string;
    keyFrameNo: number;
  } | null;
  programeType: string;
  type: string;
  operateCode_:number
}

export interface IEntity {
  path: string;
  keyframes: string;
  type: entityType;
}

export interface IEntityMetadata {
  alias: string;
  controlData?: string;
  controlType: number;
  fieldName: string;
  fieldPath: string;
  id: number;
  isArray: boolean;
  isMultiSelect: boolean;
  isMustInput: boolean;
  isReadOnly: boolean;
  isShow: boolean;
  maxLength: number;
  minLength: number;
  value: string;
}

export interface IFormItem {
  /**
   * Id
   */
  id?: number;
  /**
   * 显示名
   */
  alias: string;
  /**
   * 控件类型
   */
  controlType?: number;
  /**
   * 字段名
   */
  fieldName: string;
  fieldPath?: string;
  /**
   * 最大长度
   */
  maxLength?: number;
  /**
   * 最小长度
   */
  minLength?: number;
  /**
   * 只读
   */
  isReadOnly?: boolean;
  // 是否可以编辑

  isEditField?: number;
  /**
   * 是否显示
   */
  isShow?: boolean;
  isMultiSelect?: boolean;
  /**
   * 必填
   */
  isMustInput?: boolean;
  /**
   * 是否数组
   */
  isArray?: boolean;
  controlData?: string;
  /**
   * 值
   */
  value: string | number | string[];
  /**
   * 绑定词库名称
   */
  lexiconName?: string;
  /**
   * 展示的名称
   */
  showName?: string;

  data?: string;
  order?: number;
}

export interface IExifInfo {
  fileGuid?: string;
  exif?: { [propName: string]: any };
}

export const ExifConfig: { [propName: string]: string } = {
  imageWidth: '水平像素',
  imageHeight: '垂直像素',
  pixelXDimension: '水平分辨率',
  pixelYDimension: '垂直分辨率',
  bitsPerSample: '每次采样位数',
  compression: '压缩率',
  photometricInterpretation: '光度解释',
  orientation: '方向',
  planarConfiguration: '平面配置',
  resolutionUnit: '分辨率单位',
  colorSpace: '色彩空间',
  exposureProgram: '曝光程序',
  isoSpeedRatings: 'ISO速度数值',
  meteringMode: '测光方式',
  lightSource: '光源',
  flash: '是否使用闪光灯',
  subjectArea: '目标区域',
  focalPlaneResolutionUnit: '焦平面分辨率单位',
  subjectLocation: '目标位置',
  sensingMethod: '测量方法',
  customRendered: '定制渲染',
  exposureMode: '曝光模式',
  whiteBalance: '白平衡',
  focalLengthIn35mmFilm: '35mm焦距',
  sceneCaptureType: '场景捕获类型',
  contrast: '对比度',
  saturation: '饱和度',
  sharpness: '锐化',
  subjectDistanceRange: '目标距离范围',
  shutterSpeedValue: '快门速度值',
  brightnessValue: '亮度值',
  exposureBiasValue: '曝光补偿',
  xResolution: 'X方向分辨率',
  yResolution: 'Y方向分辨率',
  whitePoint: '白点',
  primaryChromaticities: '主色度',
  referenceBlackWhite: '参考黑色白色',
  compressedBitsPerPixel: '压缩时每像素色彩位',
  exposureTime: '曝光时间',
  fNumber: '光圈系数',
  apertureValue: '光圈值',
  maxApertureValue: '最大光圈值',
  subjectDistance: '目标距离',
  focalLength: '焦距',
  flashEnergy: '闪光灯能量',
  focalPlaneXResolution: '焦平面X方向分辨率',
  focalPlaneYResolution: '焦平面Y方向分辨率',
  exposureIndex: '曝光索引',
  digitalZoomRatio: '数字缩放率',
  dateTime: '日期和时间',
  imageDescription: '图像描述',
  make: '生产商',
  model: '型号',
  artist: '作者',
  copyright: '版权信息',
  gPSLatitudeRef: 'GPS纬度参考',
  gPSLongitudeRef: 'GPS经度参考',
  gPSLatitude: 'GPS纬度',
  gPSLongitude: 'GPS经度',
  relatedSoundFile: '相关声音文件',
  dateTimeOriginal: '创建时间',
  dateTimeDigitized: '数字化时间',
  subSecTime: '亚秒时间',
  subSecTimeOriginal: '原始亚秒时间',
  subSecTimeDigitized: '数字化亚秒时间',
  imageUniqueID: '图像ID',
  spectralSensitivity: '光谱灵敏度',
  sceneType: '场景类型',
  deviceSettingDescription: '设备设置描述',
  exifVersion: 'Exif版本',
  fileCreatTime: '文件创建时间',
  fileType: '文件类型',
  fileSize: '文件大小',
};
