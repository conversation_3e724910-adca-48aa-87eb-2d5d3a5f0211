﻿import commonUtil from "@/common/commonUtil";

var mamUpload;
if (typeof define === "function" && define.amd)
{
    mamUpload = require('mam-upload');
}
else
{
    mamUpload = window.mamUpload;
}

var uploadModalpicturePackageCtrl = ["$scope", "$uibModalInstance", "$stateParams", "$state", "$http", "opts",
    function ($scope, $uibModalInstance, $stateParams, $state, $http, opts) {

    var transfer = mamUpload.uploader.transfers[opts.uploadParams.transferType];
    var validateFun;
    var type = _.find(nxt.config.entityTypes, { code: 'picture' });
    $scope.model = {};
    $scope.sortableOptions = {};


    $scope.addFiles = function () {
        transfer.openFileSelector(function (files) {
            handleFiles(files);
        });
    }

    $scope.ok = function () {
        var validateResult = validateFun();
        if (validateResult.success) {
            $scope.model.metadata.name = _.find($scope.model.metadata.field, { fieldName: 'name_' }).value;
            _.forEach($scope.model.files, function (item) {
                delete item.image;
            });
            $uibModalInstance.close($scope.model);
        } else {
            mam.message.error('元数据信息未通过验证，请修改后再试。');
        }
    }

    $scope.close = function () {
        $uibModalInstance.close(null);
    }

    $scope.getValidateFun = function (fun) {
        validateFun = fun;
    }

    function getPreviewImage(item) {
        item.image = type.keyframe.replace('~', '');
        if (opts.uploadParams.transferType == mamUpload.uploader.transferType.web) {
            //判断大小
            if (item.image.length >= (2 * 1024 * 1024)) {
                return;
            }
            //判断扩展名
            var allowExt = ['png', 'gif', 'jpg', 'bmp', 'jpeg'];
            var ext = mam.utils.getExtension(item.file.name).toLowerCase();
            if (_.indexOf(allowExt, ext) === -1) {
                return;
            }

            var reader = new FileReader();
            reader.onload = function () {
                item.image = this.result;
                $scope.$applyAsync();
            }
            reader.onerror = function () { }
            reader.readAsDataURL(item.file);
        } else { //vtube

        }
    }

    function checkExist(file) {
        var isHas;
        if (opts.uploadParams.transferType == mamUpload.uploader.transferType.web) {
            isHas = _.find($scope.model.files, function (item) {
                return file.file.name === item.file.name &&
                    file.file.size === item.file.size &&
                    file.file.lastModified === item.file.lastModified;
            });
        } else { //vtube
            isHas = _.find($scope.model.files, function (item) {
                return file.file.FilePath === item.file.FilePath;
            });
        }
        if (isHas != null) {
            mam.message.error('文件 ' + file.fileName + ' 已存在');
            return true;
        }
        return false;
    }

    function handleFiles(files) {
        var ext = [];
        var fileName = [];
        var flg = false;
        _.forEach(files, function (file, index) {
            if (checkExist(file)) {
                return;
            }
            if (_.indexOf(type.extensions, file.metadata.ext.toLowerCase()) > -1) { //检查是否为允许的格式
                if (index == 0 && ($scope.model.metadata.name == null || $scope.model.metadata.name == '')) {
                    $scope.model.metadata.name = file.metadata.name;
                    _.find($scope.model.metadata.field, { fieldName: 'name_' }).value = file.metadata.name;
                }
                var data = {
                    fileName: file.fileName,
                    file: file.file
                }
                $scope.model.files.push(data);
                getPreviewImage(data);
            } else {
                flg = true;
                var temp = _.filter(ext, function (item) { return item == file.metadata.ext });
                if (!temp || temp.length == 0) {
                    ext.push(file.metadata.ext);
                }
                fileName.push(file.file.name);
                //mam.prompt('对不起，图片包不支持 ' + file.metadata.ext + ' 格式的文件，' + file.file.name + ' 添加失败');
            }
        });
        if (flg) {
            var a = ext.join("、");
            var b = fileName.join("、");
            commonUtil.prompt('对不起，图片包不支持 ' + a + ' 格式的文件，' + b + ' 添加失败')
        }
        $scope.$applyAsync();
    }

    function getMetadata(callback) {
        var data = {
            source: opts.uploadParams.module
        }
        let apiVersion = commonUtil.getCookie('apiVersion');
        var url = '~/upload/get-all-fields-by-source'
        if (apiVersion && apiVersion === 'mamcore2.3'){
            url = '~/scntm/v1/old/upload/get-all-fields-by-source'
        }
        $http.post(url, data).then(function (res) {
            $scope.model.metadata.field = res.data[type.code];
            var metadata = res.data[type.code];
            if (type.code === 'video' && !(metadata instanceof Array))
            {
                metadata = metadata.data;
            }
            $scope.model.metadata.field = metadata;
            callback();
        });
    }

    function init() {
        if (type == null) {
            mam.message.error('系统没有配置图片类型，无法进行图片包上传。');
            $uibModalInstance.close(null);
        } else {
            $scope.model = {
                entityType: type.code,
                files: [],
                metadata: {},
                status: 'added',
                progress: 0
            }
            getMetadata(function () {
                handleFiles(opts.files);
            });
        }
    }

    init();

}];

angular.module('mam-upload').registerController('uploadModalpicturePackageCtrl', uploadModalpicturePackageCtrl);