import React, { FC, useEffect } from 'react';
import { Form, TreeSelect, Tag } from 'antd';
import { IBasicItemProps } from './basicMetadata';
import { getValue } from './basicMetadata';
import { CloseCircleOutlined } from '@ant-design/icons';

const { SHOW_CHILD } = TreeSelect;

interface IValue {
  key: string;
  title: string;
}

const BasicTree: FC<IBasicItemProps> = props => {
  let treeData: any[] = [];
  const setTreeData = (t: any[]): any => {
    return t?.map(item => {
      if (item.children && item.children.length > 0) {
        return {
          title: item.categoryName || item.title,
          key: item.categoryCode || item.key,
          value: item.categoryCode || item.key,
          children: setTreeData(item.children),
        };
      } else {
        return {
          title: item.categoryName || item.title,
          key: item.categoryCode || item.key,
          value: item.categoryCode || item.key,
          children: setTreeData(item.children),
        };
      }
    });
  };
  if (props.item.fieldName == 'theme_codes') {
    treeData = setTreeData(props.treeDatas); // 使用setTreeData处理list
  } else if (props.item.controlData?.length) {
    try {
      treeData = JSON.parse(props.item.controlData);
    } catch (err) {
      throw err;
    }
    if (treeData.length > 0) {
      treeData = setTreeData(treeData);
    }
  }

  const getValues = (value: any) => {
    if (!value || treeData.length === 0) {
      return null;
    }
    let values: string[] = [];
    // 数组直接使用
    if (Array.isArray(value)) {
      values = value;
    } else if (typeof value === 'string') {
      values = getValue(value);
    }

    const newValues = findValue(treeData, values);
    if (newValues.length === 0) {
      return values.map(item => <Tag key={item}>{item}</Tag>);
    } else {
      return newValues.map(item => {
        return <Tag key={item.key}>{item.title}</Tag>;
      });
    }
  };
  const v: IValue[] = [];
  const findValue = (tree: any[], value: string[]) => {
    tree.forEach(item => {
      if (value.includes(item.key)) {
        v.push({
          key: item.key,
          title: item.title,
        });
      }else {
        if (item.children && item.children.length > 0) {
          return findValue(item.children, value);
        }
      }
    });
    return v;
  };
  const tagRender = (value: { label: string; onClose: () => void }) => {
    return <Tag closable closeIcon={<CloseCircleOutlined />} onClose={value.onClose}>
              {value.label}
            </Tag>
  }
  return (
    <Form.Item
      label={props.item.alias}
      name={props.item.fieldName}
      rules={[
        {
          required:
            props.item.isReadOnly || !props.edit
              ? false
              : props.item.isMustInput,
        },
      ]}
    >
      {props.item.isReadOnly || !props.edit ? (
        getValues(props.item.value as string)
      ) : (
        <TreeSelect
          treeData={treeData}
          multiple
          treeCheckable
          tagRender={tagRender}
          showCheckedStrategy={SHOW_CHILD}
        />
      )}
    </Form.Item>
  );
};

export default BasicTree;
