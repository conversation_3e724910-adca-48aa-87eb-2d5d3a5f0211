import React, {
  FC,
  useEffect,
  useRef,
  useState,
  KeyboardEvent,
  FocusEvent,
} from 'react';
import { IconFont } from '@/components';
import { Input, Button, message, Modal,Spin } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import _ from 'lodash';
import '../index.less';
import SmartService from '@/service/smartService';
import { useParams, useSelector, useIntl } from 'umi';
import { IPermission } from '@/models/permission';
import perCfg from '@/permission/config';
import { searchKeywords } from '@/utils';

interface IData {
  detail: any;
  handleSecDel: any;
  setPlayerSec: any;
  getQuerySequencemeta: any;
  downVisible: any;
  keyframeLoading:boolean;
  disabled:boolean;
  sendMicro?:()=>void;
  setModalVisible:() => void;
  type?:string
}

const PointsItem: FC<IData> = props => {
  const downloadref = useRef<HTMLAnchorElement | null>(null)
  const intl = useIntl();

  const params = useParams<{ contentId: string }>();
  const {
    detail,
    handleSecDel,
    setPlayerSec,
    getQuerySequencemeta,
    downVisible,
    disabled,
    keyframeLoading,
    sendMicro,
    setModalVisible,
    type
  } = props;
  const win = window as any;
  const sequencemetaName = useRef<any>();
  const [sequencemetaEdit, setSequencemetaEdit] = useState<boolean>(true);
  const [deleteModalVisible, setdeleteModalVisible] = useState<boolean>(false);
  const { permissions } = useSelector<{ permission: any }, IPermission>(
    ({ permission }) => permission,
  );
  const  {mobileFlag}  = useSelector<{ config: any }, any>(
    ({config})=>config
 );
 const [keywords, setKeywords] = useState<string[]>([]);
 const [keywordsVisible, setKeywordsVisible] = useState<boolean>(false);
  const downloadVideo = () => {
    downloadref.current ? downloadref.current.href = "/bucket-z/u-cwcc268v239qk92m/video/2021/3/23/c36276fd-574c-4865-a639-3269f843ed18/%E8%AF%8A%E6%96%AD%E5%AD%A6%20%E6%99%BA%E8%83%BDdemo%2011.02.mp4" : ''
    downloadref.current ? downloadref.current.download = detail.title + '.mp4' : ''
    downloadref.current ? downloadref.current.click() : ''
  }

  useEffect(() => {
    if (!sequencemetaEdit) {
      sequencemetaName.current?.focus();
    }
  }, [sequencemetaEdit]);
  useEffect(() => {
    setKeywords(detail.keywords?.slice(0,4));
  }, [detail]);
  const sequencemetaChangeName = async (
    e: KeyboardEvent<HTMLInputElement> | FocusEvent<HTMLInputElement> | any,
    id: any,
  ) => {
    e.preventDefault();
    let name = e.target.value;
    if (name === '') {
      message.error(intl.formatMessage({ id: '标题不能为空' }));
      return;
    }
    const res = await SmartService.updatepartSequencemetaNew(false,params.contentId, [
      {
        guid_: id,
        title: e.target.value,
        inpoint: detail.inpoint,
        outpoint: detail.outpoint,
        keyframeno: detail.keyframeno,
        keyframepath: detail.keyframepath,
        fragment_description: detail.fragment_description,
      },
    ]);
    if (res?.success) {
      message.success(intl.formatMessage({ id: '编辑成功' }));
    } else {
      message.error(intl.formatMessage({ id: '编辑失败' }));
    }
    getQuerySequencemeta();
    setSequencemetaEdit(true);
  };
  //百纳秒转时分秒
  const timeFormat=(start:any,end:any)=>{
    const result = Math.floor((Number(end)-Number(start))/10000000); //百纳秒
    let h = Math.floor(result / 3600);
    let m = Math.floor((result / 60 % 60));
    let s = Math.floor((result % 60));
    let res = '';
    if(h!=0) res += `${h}h`;
    if(m!=0) res += `${m}min`;
    res += `${s}s`;
    return res;
  }
  let newTitle:any = detail.title;
  const temp = searchKeywords(newTitle,'knowledege');
  newTitle = temp.index>-1?
  <span>
      {temp.beforeStr}
      <span className="key-search-value">{temp.word}</span>
      {temp.afterStr}
  </span>
  :newTitle;
  return (
    <div className="sequence_item" onClick={() => setPlayerSec()}>
      <div className="sequence_item_content">
        <img src={detail.keyframepath || '/rman/static/images/video.png'} />
        {keyframeLoading && <Spin size='small'/>}
        {(((detail.inpoint===0||detail.inpoint)&&
          detail.outpoint&& type == 'video') ? true : false) &&
          <span className='timeSpan'>
            <span>
              {timeFormat(detail.inpoint,detail.outpoint)}
              {/* {win.TimeCodeConvert.l100Ns2Tc$1(detail.inpoint, false)}-
              {win.TimeCodeConvert.l100Ns2Tc$1(detail.outpoint, false)} */}
            </span>
          </span>
        }
      </div>

      <div className="sequence_content">
        {sequencemetaEdit ? (
          <div className='sequence_content_top'>
            <div className="sequence_title" title={detail.title}>{newTitle}</div>
            <div className="sequence_keywords_box" onMouseEnter={(e) => {
                e.stopPropagation()
                setKeywordsVisible(true)
              }} onMouseLeave={(e) => {
                e.stopPropagation()
                setKeywordsVisible(false)
              }
              }>
              {detail.keywords?.length > 0 && keywords.length > 0 && <div className="sequence_keywords" >
                {keywords?.map(item => (<div className='keyword'>{item}</div>))}
              </div>}
              {keywordsVisible && detail.keywords?.length>0 && <div className='keywords_modal'>
                  {detail.keywords?.map(item => (<div className='keyword'>{item}</div>))}
                </div>}
            </div>

            {detail.type === 'document' && <div className="sequence_page">{detail.inpoint}-{detail.outpoint}页</div>}
             
            {!location.hash.includes('hidebtn') && <div className="del_icon" onClick={(e: any) => e.stopPropagation()}>
              <a
                className='download-dis'
                href=''
                download=''
                ref={downloadref}
              >
              </a>
              {downVisible == true ? (
                <Button size="middle" type="link" onClick={downloadVideo} icon={<DownloadOutlined />} />

              ) : (
                <span></span>
              )}
              {permissions.includes(perCfg.resource_edit) && sequencemetaEdit && (
                <Button
                  size="small"
                  type="link"
                  title={intl.formatMessage({ id: '编辑' })}
                  disabled={disabled}
                  icon={<IconFont type="iconbianji17" />}
                  onClick={() => setModalVisible()}
                />
              )}
              {permissions.includes(perCfg.microcourse_new)
                && !mobileFlag && sequencemetaEdit && type == 'video' && (
                <Button
                  size="small"
                  type="link"
                  title={intl.formatMessage({ id: '发微课' })}
                  disabled={disabled}
                  icon={<IconFont type="iconfabu-heise1" />}
                  onClick={() => sendMicro?.()}
                />
              )}
              {permissions.includes(perCfg.resource_delete) && (
                <Button
                  size="small"
                  type="link"
                  title={intl.formatMessage({ id: '删除' })}
                  disabled={disabled}
                  onClick={() => setdeleteModalVisible(true)}
                  icon={<IconFont type="iconshanchu4" />}
                />
              )}
              <Modal
                open={deleteModalVisible}
                className={mobileFlag?' mobileModal':''}
                title={intl.formatMessage({ id: '删除' })}
                onOk={() => {
                  handleSecDel();
                  setdeleteModalVisible(false);
                }}
                onCancel={() => setdeleteModalVisible(false)}
              >
                {intl.formatMessage({ id: '确定要删除该知识点吗' })}
              </Modal>
            </div>
            }
          </div>
        ) : (
          <Input
            defaultValue={detail.title}
            ref={sequencemetaName}
            onPressEnter={e => {
              e.target.blur(e, true);
            }}
            onBlur={e => sequencemetaChangeName(e, detail.guid_)}
          />
        )}
      </div>
    </div>
  );
};
export default PointsItem;
