import React, { useState, useEffect, FC } from "react";
import './index.less'
import { Select, Radio, Button } from 'antd';
import { useLocation, useSelector, useDispatch } from 'umi';
import { DataUri } from '@antv/x6';
const Header: FC<any> = ({ graph, setInputtext, search, setSelecttype, typeonselect, querytype, inputtext }) => {
    const [checked, setChecked] = useState<boolean>(true);
    // 获取url参数
    const { query }: any = useLocation();

    return (
        <div className="map_heard_view">
            <div className="left">
                <div className="title">
                    <img className="icon1" src={require('../../../../images/coursemap/v3/icon1.png')} alt="" />
                    <span className="name">资源图谱</span>
                    <img className="icon2" src={require('../../../../images/coursemap/v3/icon2.png')} alt="" />
                </div>
            </div>
        </div>
    )
}

export default Header;