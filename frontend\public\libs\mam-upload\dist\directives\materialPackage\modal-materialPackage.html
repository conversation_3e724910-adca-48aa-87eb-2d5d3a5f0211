﻿<div class="modal-header">
    <button type="button" class="close" ng-click="close()"><i class="fa fa-times"></i></button>
    <h4 class="modal-title">{{titleName}}上传</h4>
</div>

<div class="modal-body">
    <div class="metadata-box">
        <div class="box-header">
            <div class="box-header-title">元数据</div>
        </div>
        <div class="box-content" scrollable>
            <mam2-metadata-form items="model.metadata.field" twoway="true" type="edit" get-func="getValidateFun(func)"></mam2-metadata-form>
        </div>
    </div>
    <div class="files-box">
        <div class="box-header">
            <div class="box-header-title">文件列表</div>
            <div class="box-header-btns">
                <button class="add" ng-click="addFiles()" title="添加新文件">
                    <i class="fa fa-plus"></i><span>添加文件</span>
                </button>
            </div>
        </div>
        <div class="box-content" scrollable>
            <div class="items-box" ui-sortable="sortableOptions" ng-model="model.files">
                <div class="item" ng-if="model.files && model.files.length > 0">
                    <div class="name">全部选择</div>
                    <div class="btns">
                        <i class="fa fa-times" title="全部删除" ng-click="removeAll(item)"></i>
                    </div>
                </div>
                <div class="item" ng-repeat="item in model.files track by $index">
                    <div class="name" title="{{item.fileName}">{{$index + 1}}、{{item.fileName}}</div>
                    <div class="btns">
                        <i class="fa fa-times" title="删除" ng-click="model.files.remove(item)"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal-footer">
    <button class="btn btn-primary" ng-click="ok()" ng-disabled="model.files==null||model.files.length==0">上传</button>
    <button class="btn btn-default" ng-click="close()">{{l('com.cancel','取消')}}</button>
</div>
