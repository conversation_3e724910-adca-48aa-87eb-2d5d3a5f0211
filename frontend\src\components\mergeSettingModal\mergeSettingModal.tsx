import React, { FC, useEffect, useState } from 'react';
import { Modal, Form, Checkbox, Input, Select, Slider, Space, TreeSelect, Spin } from 'antd';
import SelectMaterialItem from '@/components/mergeSettingModal/selectMaterialItem';
import SelectColorItem from '@/components/mergeSettingModal/selectColorItem';
import { IPermission, useSelector, useParams, useIntl } from 'umi';
import SmartService from '@/service/smartService';
import globalParams from '@/permission/globalParams';
import { CaretDownOutlined } from '@ant-design/icons';
import Item from 'antd/lib/list/Item';
import './mergeSettingModal.less';
import rmanApis from '@/service/rman';
interface IMergeSettingModal {
  visible: boolean;
  onCancel: () => void;
  onOk: (value: any) => void;
  videoKeyframe: string;
  showTitle: boolean;
  dirTree: string;
  checkSubTile: boolean;
}

const layout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 16,
  },
};
const TE_OPTIONS = [
  {
    label: '淡入淡出',
    value: 'default',
  },
  {
    label: '划像',
    value: 'wipe',
  },
  {
    label: '分割滑动',
    value: 'splitside',
  },
];

const MergeSettingModal: FC<IMergeSettingModal> = ({
  visible,
  onCancel,
  videoKeyframe,
  showTitle,
  dirTree,
  onOk,
  checkSubTile,
}) => {
  const params = useParams<{ contentId: string }>();
  const [form] = Form.useForm();
  const [modalVisible, setModalVisible] = useState(false);
  const [voice, setVoice] = useState(0);
  const [allDirectory, setAllDirectory] = useState<any>([]);
  const [currentDirectory, setCurrentDirectory] = useState<string>('');
  const [showplaceholder, setShowplaceholder] = useState<string>('');
  const intl = useIntl();
  // 权限
  const { rmanGlobalParameter } = useSelector<{ permission: any }, IPermission>(
    ({ permission }) => permission,
  );
  const  {mobileFlag}  = useSelector<{ config: any }, any>(
    ({config})=>config
 );
  useEffect(() => {
    setModalVisible(visible);
    // form.resetFields();
    setVoice(0);
    if (visible) {
      // getsplicingdirectory();
      fetchTree()
    }
    return (()=>{
      setAllDirectory([]);
      setCurrentDirectory('');
    })
  }, [visible]);
  const fetchTree = () => {
    rmanApis.gettreebylevel(2).then((res: any) => {
        if (res && res.data && res.success) {
            let data = res.data;
            let newData: any = [];
            data.map((item: any) => {
              if(item.name !== '公共资源'){
                newData.push(item);
              }
            });
            newData = newData.filter(Boolean); //过滤空对象
            const rootData = newData.map((item: any) => {
                return {
                    key: item.path,
                    value: item.id,
                    title: item.name,
                    id: item.path,
                    isLeaf: item.childCount === 0
                };
            });
            console.log(rootData[0], 'rootData');
            
            setCurrentDirectory(dirTree)
            setShowplaceholder(rootData[0].title)
            form.setFieldsValue({
              newFoderContentId: rootData[0].value
            })
            setAllDirectory(rootData);
        }
    });
  };
  const onLoadChild = (node: any, isRoot: boolean = false) => {
    const { id, children, code, title } = node;
    return new Promise(async (resolve) => {
        if (id === 'add') {
            resolve(null);
            return;
        }
        if (children) {
            resolve(null);
            return;
        }
        function updateTreeData(list: any, key: React.Key, children: any): any {
            return list.map((node: any) => {
                if (node.id === id) {
                    return {
                        ...node,
                        children,
                    };
                } else if (node.children) {
                    return {
                        ...node,
                        children: updateTreeData(node.children, id, children),
                    };
                }
                return node;
            });
        }
        const res: any = await rmanApis.loadChild(id, true);
        if (res && res.data && res.success) {
            let treeList = res.data;
            treeList = treeList.filter((item: any) => {
                return item.name !== '录播资源';
            });
            setAllDirectory((origin: any) =>
                updateTreeData(
                    origin,
                    id,
                    treeList.map((item: any) => {
                        return {
                            key: item.path,
                            title: item.name,
                            id: item.contentId,
                            value: item.contentId,
                            isLeaf: item.childCount === 0
                        };
                    }),
                ),
            );
            resolve(null);
        }
    });
  };
  // 获取当前素材所在的目录
  const getsplicingdirectory = async () => {
    const paths = ['global_sobey_defaultclass/private/sys', 'global_sobey_defaultclass/public/群组资源/', 'global_sobey_defaultclass/public/共享资源/', 'global_sobey_defaultclass/public/录播资源/']
    const path = paths.find(item => dirTree.includes(item)) || ''
    const res: any = await rmanApis.loadChild(path, true);
    // let arr = getfordata(res.data.folderTree);
    // arr.unshift({
    //   label: `原路径`,
    //   value: '',
    //   path: res.data.currentDirectory
    // })
    setAllDirectory(res.data.map(item => {
      return {
        key: item.path,
        value: item.path,
        title: item.name,
        isLeaf: item.childCount === 0,
        id: item.id,
      }}
    ));
    // setCurrentDirectory(res.data.currentDirectory);
    // setCurrentDirectory(arr[0].value);
    // form.setFieldsValue({
    //   newFoderContentId: arr[0].value
    // })
  }
   // 路径转译
   const getDirTeeStr1 = (dirTree?: string): string => {
    if (!dirTree) {
        return ''
    }
    if (dirTree.includes('global_sobey_defaultclass/public')) {
        return dirTree.replace('global_sobey_defaultclass/public', '').replace('/', '');
    } else if (dirTree.includes('global_sobey_defaultclass/private')) {
        let newdir = dirTree.replace(
            'global_sobey_defaultclass/private',
            '个人资源',
        );
        if (newdir.includes('/')) {
            let alldir = newdir.split('/');
            if (alldir.length >= 2) {
                newdir = newdir.replace('/' + alldir[1], '');
            }
        }
        return newdir;
    } else {
        return dirTree;
    }
  };

  //递归修改参数
  const getfordata = (folderTree: []): any => {
    return (folderTree && folderTree.map((item: any) => {
      return {
        title: item.name,
        value: item.id,
        children: item.children.length > 0 ? getfordata(item.children) : []
      }
    }))
  }

  // 转译path
  const getpath = (path: string = ''): string => {
    if (path.includes('global_sobey_defaultclass/public')) {
      return path.replace('global_sobey_defaultclass/public', "公共资源");
    } else if (path.includes('global_sobey_defaultclass/private')) {
      return path.replace('global_sobey_defaultclass/private', "个人资源");
    } else {
      return path
    }
  }

  // 选择
  const cascaderchange = (value: any, selectedOptions: any,info:any) => {
    setCurrentDirectory(value);
    setShowplaceholder(getDirTeeStr1(dirTree))
    form.setFieldsValue({
      newFoderContentId: value
    })
  }

  return (
    <Modal
      title={intl.formatMessage({ id: '参数设置' })}
      open={modalVisible}
      className={mobileFlag?'mobileModal':''}
      onCancel={onCancel}
      onOk={() => {
          form.submit()
      }}
    >
      <Form
        {...layout}
        name="settingName"
        form={form}
        initialValues={{
          transitionEffect: 'default',
          isSubtitle: checkSubTile,
        }}
        onFinish={values => {
          console.log('111111',values)
          delete values.enhance;
          onOk(values);
        }}
      >
        {showTitle && (
          <Form.Item
            name="newName"
            label={intl.formatMessage({ id: '标题' })}
            rules={[{ required: true, message: intl.formatMessage({ id: '请填写标题' }) }]}
          >
            <Input maxLength={50}/>
          </Form.Item>
        )}
        <Form.Item name="beginId" label={intl.formatMessage({ id: '片头' })}>
          <SelectMaterialItem />
        </Form.Item>
        <Form.Item name="endId" label={intl.formatMessage({ id: '片尾' })}>
          <SelectMaterialItem />
        </Form.Item>
        <Form.Item name="newFoderContentId" label={intl.formatMessage({ id: '保存至' })}>
          {/* <div className="select_view">
            {allDirectory.length > 0 ?
              <Cascader options={allDirectory} onChange={cascaderchange} changeOnSelect style={{ width: '100%' }}>
                <div className="select_item_view">
                  <span>{getpath(currentDirectory)}</span>
                  <CaretDownOutlined style={{ marginLeft: '10px', color: 'var(--primary-color)', fontSize: '16px' }} />
                </div>
              </Cascader> : ''
            }
          </div> */}
          {allDirectory.length > 0 ?<TreeSelect
              style={{ width: '100%' }}
              dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
              treeData={allDirectory}
              placeholder={intl.formatMessage({ id: '原格式' })}
              treeDefaultExpandAll
              onChange={cascaderchange}
              loadData={onLoadChild}
            />:<Spin/>
          }
        </Form.Item>
        {rmanGlobalParameter.includes(globalParams.colorimprove_display) && (
          <Form.Item name="colorImprove" label={intl.formatMessage({ id: '校色' })}>
            <SelectColorItem videoKeyframe={videoKeyframe} />
          </Form.Item>
        )}
        {rmanGlobalParameter.includes(globalParams.noisereduce_display) && (
          <Form.Item name="noiseReduce" label={intl.formatMessage({ id: '去噪' })} valuePropName="checked">
            <Checkbox>{intl.formatMessage({ id: '开启' })}</Checkbox>
          </Form.Item>
        )}
        {rmanGlobalParameter.includes(globalParams.enhance_display) && (
          <Form.Item name="enhance" label={intl.formatMessage({ id: '声音增强' })} valuePropName="checked">
            <Checkbox>{intl.formatMessage({ id: '开启' })}</Checkbox>
          </Form.Item>
        )}
        <Form.Item
          noStyle
          shouldUpdate={(pre, cur) => pre.enhance !== cur.enhance}
        >
          {() =>
            form.getFieldValue('enhance') && (
              <Form.Item
                wrapperCol={{
                  span: 16,
                  offset: 4,
                }}
              >
                <Space>
                  <Slider
                    value={voice}
                    onChange={setVoice}
                    style={{ width: 250 }}
                  />
                  <span>{voice}</span>
                </Space>
              </Form.Item>
            )
          }
        </Form.Item>
        {/* {checkSubTile && (
          <Form.Item name="isSubtitle" label="字幕" valuePropName="checked">
            <Checkbox disabled={!checkSubTile}>开启</Checkbox>
          </Form.Item>
        )} */}
        {/* <Form.Item
          shouldUpdate={(pv, cv) =>
            pv.beginId !== cv.beginId || pv.endId !== cv.endId
          }
        >
          {() =>
            (form.getFieldValue('beginId') || form.getFieldValue('endId')) && (
              <Form.Item
                name="transitionEffect"
                label="转场特技"
                rules={[{ required: true, message: '请选择转场特效' }]}
              >
                <Select options={TE_OPTIONS} style={{ width: 106 }} />
              </Form.Item>
            )
          }
        </Form.Item> */}
      </Form>
    </Modal>
  );
};
export default MergeSettingModal;
