import React, { FC, useState, useRef, useEffect } from 'react';
import {
  Form,
  Input,
  Button,
  DatePicker,
  TreeSelect,
  Row,
  Col,
  Select,
} from 'antd';
import { CaretDownOutlined,CaretUpOutlined,SearchOutlined,UndoOutlined ,CloseCircleOutlined} from '@ant-design/icons';
import contentListApis from '@/service/contentListApis';
import TeacherItem from '@/components/formItemBox/teacherItem';
import './index.less';
import {
  formatMessage,
  useIntl,
  useHistory,
  useSelector,
  IPermission,
} from 'umi';
import moment from "moment";

import { MenuItem } from './type';
import globalParams from '@/permission/globalParams';
import { IconFont } from '../iconFont/iconFont';
import Loading from '@/loading';
import SearchPhotoModal  from '../searchPhotoModal';
const { Search } = Input;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { SHOW_CHILD } = TreeSelect;
export const options: Array<MenuItem> = [
  { label: '全部', value: undefined },
  { label: '视频', value: 'biz_sobey_video' },
  { label: '文件夹', value: 'folder' },
];

export const fullOptions: Array<MenuItem> = [
  { label: '全部', value: undefined },
  { label: '音频', value: 'biz_sobey_audio' },
  { label: '视频', value: 'biz_sobey_video' },
  { label: '图片', value: 'biz_sobey_picture' },
  { label: '文档', value: 'biz_sobey_document' },
  { label: '文件夹', value: 'folder' },
  { label: '其他', value: 'biz_sobey_other' },
];

export const positions: Array<MenuItem> = [
  { label: '全部', value: undefined },
  { label: '教师近景画面', value: '教师近景画面' },
  { label: '教师全景画面', value: '教师全景画面' },
  { label: '教师画面', value: '教师画面' },
  { label: '学生画面', value: '学生画面' },
  { label: '屏幕画面', value: '屏幕画面' },
  { label: '导切画面', value: '导切画面' },

  
];

interface ContentData {
  // optionsChange?: (value: string) => void;
  resourceSearch: (data:any) => void;
  form: any;
  selected: any;
  currentSemester: any
  setCurrentSemester: (data:any) => void;
  folderPath: folderPath;
  expand: boolean;
  radioDetail: any;
  weeks: any
  reset: () => void;
  goDetail: (data:any) => void;
  filterOptions: boolean;
  loading: boolean;
  myVideoDetailFilter: boolean;
  classifiedByschedule: boolean; // 是否按课表
  classifiedByschedule: boolean; // 是否按课表
}

interface ITree {
  title: string;
  key: string;
  children?: ITree[];
}

const SearchBox: React.FC<ContentData> = props => {
  
  let history: any = useHistory();
  const hidecatalogue =
    JSON.stringify(history.location.query) === '{}'
      ? '0'
      : history.location.query.hidecatalogue;
  let target = history.location?.query?.target || '' 
  const initData:any = {
    format:undefined,
    name_:undefined,
    data:undefined,
    college:undefined,
    major:undefined,
    guideScreen:undefined,
    teacher:undefined,
    creator:undefined,
    seat:undefined,
    semester:undefined,
    week:undefined,
    usestatus:undefined,
    smart_status:undefined,
    asr_status:undefined,
    ocr_status:undefined,
  }
  const { optionsChange, resourceSearch, form, reset, expand, selected, loading, myVideoDetailFilter, goDetail, currentSemester, weeks, setCurrentSemester,filterOptions, classifiedByschedule } = props;
  const [teacherList, setTeacherList] = useState<{ [key: string]: string }>({});
  const [collegeList, setCollegeList] = useState<{ [key: string]: string }>({});
  const [majorList, setMajorList] = useState<ITree[]>([]);
  const [currentName, setCurrentName] = useState<any>('');
  const [formData, setFormData] = useState<any>(initData);
  const [semester, setSemester] = useState<any>([]);//学期
  const [record, setRecord] = useState<any>();// 语音识别
  const [asrActive, setAsrActive] = useState<boolean>(false);// 语音识别状态
  const [source, setSource] = useState<any>([]);//来源
  const [searchModalVisible, setSearchModalVisible] = useState<boolean>(false)
  // 权限
  const { rmanGlobalParameter, parameterConfig } = useSelector<{ permission: any }, IPermission>(
    ({ permission }) => permission,
  );
  // 是否是录播资源
  const [isMyVideo, setIsMyVideo] = useState<boolean>(false);
  // 是否没有启用智能标签
  const [isNoSmart, setIsNoSmart] = useState<boolean>(false);
  const intl = useIntl();

  useEffect(() => {
    if (parameterConfig.target_customer as string === 'sjtu') {
      if(selected === '录播资源'){
        setIsMyVideo(true)
      }else{
        setIsMyVideo(false)
      }
      if(['个人资源','群组资源','共享资源'].includes(selected)){
        setIsNoSmart(true)
      }else{
        setIsNoSmart(false)
      }
    }
  }, [selected])

  useEffect(() => {
    contentListApis.getfields().then((res: any) => {
      if (res && res.errorCode === 'success') {
        res.extendMessage.forEach((item: any) => {
          if (item.fieldName === 'teacher') {
            setTeacherList(
              item.controlData ? JSON.parse(item.controlData) : {},
            );
          }
          if (item.fieldName === 'college') {
            setCollegeList(
              item.controlData ? JSON.parse(item.controlData) : {},
            );
          }
          if (item.fieldName === 'major') {
            if (JSON.parse(item.controlData).length > 0) {
              // console.log(JSON.parse(item.controlData))
              setMajorList(setTreeData(JSON.parse(item.controlData)));
            }
          }
        });
      } else {
      }
    });
    contentListApis.getSemesterData().then((res:any)=>{
      if (res && res.errorCode === 'success') {
        setSemester(res.extendMessage.sort((a:any,b:any)=>moment(b.endTime).valueOf()-moment(a.endTime).valueOf()));
      }
    })
    contentListApis.getVideoSource().then((res:any)=>{
      if (res?.success) {
        setSource(res.data);
      }
    })
    
  }, []);
  const setTreeData = (t: any[]): any => {
    return t.map(item => {
      if (item.children && item.children.length > 0) {
        return {
          title: item.categoryName,
          key: item.categoryCode,
          children: setTreeData(item.children),
        };
      } else {
        return {
          title: item.categoryName,
          key: item.categoryCode,
        };
      }
    });
  };
  const searchList = () => {
    setFormData({
      ...formData,
      name_:currentName
    })
    //资源库模糊检索时，检索包含标题、标签、描述、语音文本、知识点，检索高亮优化
    window.localStorage.setItem('searchKeyWord',JSON.stringify({
      word:currentName || undefined,
      basic:false, //基本信息
      voice:false, //语音文本
      knowledege:false, //知识点
    }));
  };
  const formChange = (name:any,value:any) => {
    setFormData({
      ...formData,
      [name]:value || undefined
    })
  };
  const oldFormChange = () => {
    console.log(form.getFieldsValue());
    const temp = form.getFieldsValue();
    let param:any = {};
    if(temp.analysis_?.length>0){
      temp.analysis_.map((item:any)=>{
        param[item.split('~')[0]]=item.split('~')?.[1]
      })
    }
    setFormData({
      ...temp,
      ...param,
      name_:temp.search.name_,
      format:temp.search.format
    })
  };
  useEffect(()=>{
    //目的是为了不让第一次调用resourceSearch方法导致父组件方法会调用两次
    if(JSON.stringify(formData)!==JSON.stringify(initData)){
      resourceSearch(formData);
    }
  },[formData])
  useEffect(()=>{
    //一旦切换其他目录 重置搜索词
    if(selected!=='共享资源'){
      window.localStorage.setItem('searchKeyWord','{}');
      console.log('重置搜索词了');
    }
  },[selected])
  const resetForm = () => {
    setFormData(initData);
    resourceSearch(initData);
  };
  const openPhotoModal = () => {
    setSearchModalVisible(true)
  }
  const voiceInput = () => {
    if (record) {
      record?.stop()
      setRecord(false)
  } else {
      setAsrActive(true)
      let asr = new FunASR({})
      setRecord(asr)
      asr.on('ready', function (asr) {
          asr.start()
      })
      asr.on('msg', function (msg) {
        form.setFieldsValue({
          search: {
            format: '',
            name_: msg
          },
        })
      })
  }
  }

  return (
    <div className={`result_search`}>
      {/* 2023-2-15 个人、共享、群组资源目录样式统一 */}
      <div className={`result_search_header ${['个人资源','群组资源','共享资源'].includes(selected)?'':'none'}`}>
        <Form
          name="complex-form"
          layout="inline"
          form={form}
          initialValues={{
            search: {
              format: '',
            },
          }}
          style={{ width: '100%', height: '100%' }}
        >
          <Row style={{ width: '100%' }}>
            <Col span={target !=='custom'?6:3}>
              <Form.Item>
                <Input.Group compact>
                  {target !=='custom' && <Form.Item name={['search', 'format']} noStyle>
                    <Select className="type-width" onChange={oldFormChange}>
                      {(filterOptions ? options : fullOptions).map((item, index) => (
                        <Option value={item.value || ''} key={'type' + index}>
                          {item.label}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>}
                  <Form.Item name={['search', 'name_']} noStyle>
                    <Input
                      placeholder={intl.formatMessage({ id: '输入关键词' })}
                      allowClear
                      suffix={rmanGlobalParameter.includes(globalParams.is_show_voice_to_text) &&<IconFont title='按住说话' type='iconyuyinshibie' className={asrActive?'active':''} onMouseDown={voiceInput} onMouseUp={() => {
                        setAsrActive(false)
                        record?.stop()
                        setRecord(false)
                      }} />}
                      className={ target !=='custom'?'keyword-width': 'custom-keyword-width'}
                      autoComplete={'off'}
                      onPressEnter={oldFormChange}
                      // onSearch={oldFormChange}
                    />
                  </Form.Item>
                </Input.Group>
              </Form.Item>
            </Col>
            {
              hidecatalogue !== '1' && target !=='custom' &&
              <Col span={4}>
                <Form.Item
                  name="analysis_"
                  // label="智能标签"
                  className="align-form-item"
                >
                  <Select
                    placeholder={intl.formatMessage({ id: '是否有语音文本、知识点、关键词' })}
                    mode='multiple'
                    // onChange={oldFormChange}
                    allowClear>
                    {!isNoSmart && <Option value="asr_status~2" key="asr">
                      {intl.formatMessage({ id: '有语音文本' })}
                    </Option>}
                    <Option value="ocr_status~2" key="ocr">
                      {intl.formatMessage({ id: '有知识点' })}
                    </Option>
                    <Option value="knowledge_point_key~2" key="knowledge_point_key">
                    {intl.formatMessage({ id: '有知识要点' })}
                    </Option>
                    <Option value="labels~11" key="labels">
                      {intl.formatMessage({ id: '有标签' })}
                    </Option>
                    {!isNoSmart && <Option value="word~11" key="word">
                      {intl.formatMessage({ id: '有敏感内容' })}
                    </Option>}
                    {/* <Option value="word~11" key="word">
                      {intl.formatMessage({ id: '有摘要' })}
                    </Option>
                    <Option value="word~11" key="word">
                      {intl.formatMessage({ id: '有知识要点' })}
                    </Option> <Option value="word~11" key="word">
                      {intl.formatMessage({ id: '有PPT图片' })}
                    </Option> */}
                  </Select>
                </Form.Item>
              </Col>
            }
            {
              //兼容云雀那边
              hidecatalogue === '1' ? 
              (target !=='custom' && <>
                <Col span={5}>
                  <TeacherItem
                    multiple={false}
                    required={false}
                    message={intl.formatMessage({ id: '上传人' })}
                    label={''}
                    name={'creator'}
                    key="teacher2"
                  />
                </Col>
                <Col span={6}>
                  <Form.Item
                    name="data"
                  >
                    <RangePicker className="textcenter" style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={5}>
                  <div className="result_search_buttom">
                    <Button onClick={reset} type='link'>{intl.formatMessage({ id: '清空' })}<UndoOutlined /></Button>
                    <Button loading={loading} onClick={oldFormChange} type='primary'>{intl.formatMessage({ id: '搜索' })}<IconFont type='iconsousuo2'/></Button>
                    {rmanGlobalParameter.includes(globalParams.search_images_with_pictures) &&<IconFont type='iconxiangji2' className='photo' onClick={openPhotoModal}></IconFont>}
                  </div>
                </Col>
              </>)
              :''
            }

          {hidecatalogue === '1' ? (
            ''
          ) : (
            target !=='custom' ?<>
                <Col span={5}>
                  <Form.Item
                    name="data"
                  >
                    <RangePicker placeholder={['上传时间范围 起','止']} className="textcenter" style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Form.Item
                  noStyle
                  shouldUpdate
                >
                  {
                    ()=>{
                      if(form.getFieldValue('search').format!=='folder'){
                        return <Col span={3}>
                                  <Form.Item
                                    name="usestatus"
                                  >
                                    <Select
                                      allowClear
                                      placeholder={intl.formatMessage({ id: '资源使用状态' })}>
                                      {/* <Option value="" key="all">
                                        全部状态
                                      </Option> */}
                                      <Option value="true" key="true">
                                        {intl.formatMessage({ id: '已使用' })}
                                      </Option>
                                      <Option value="false" key="false">
                                        {intl.formatMessage({ id: '未使用' })}
                                      </Option>
                                    </Select>
                                  </Form.Item>
                                </Col>
                      }
                    }
                  }
                </Form.Item>
                {parameterConfig.target_customer as string === 'tvi' &&<Col span={2}>
                <Form.Item
                    name="cataloging_status"
                  >
                    <Select
                      allowClear
                      placeholder={intl.formatMessage({ id: '编目状态' })}>
                        
                        <Option value="1">
                          {intl.formatMessage({ id: '已编目' })}
                        </Option>
                        <Option value="0">
                          {intl.formatMessage({ id: '未编目' })}
                        </Option>
                    </Select>
                  </Form.Item>
                </Col>}
                <Col span={4}>
                  <div className="result_search_buttom result_search_button">
                    <Button onClick={reset} type='link'>{intl.formatMessage({ id: '清空' })}<UndoOutlined /></Button>
                    <Button loading={loading} onClick={oldFormChange} type='primary'>{intl.formatMessage({ id: '搜索' })}<IconFont type='iconsousuo2'/></Button>
                    {rmanGlobalParameter.includes(globalParams.search_images_with_pictures) &&<IconFont type='iconxiangji2' className='photo' onClick={openPhotoModal}></IconFont>}
                  </div>
                </Col>

            </>
            : <Col span={4}>
            <div className="result_search_buttom result_search_button">
              <Button onClick={reset} type='link'>{intl.formatMessage({ id: '清空' })}<UndoOutlined /></Button>
              <Button loading={loading} onClick={oldFormChange} type='primary'>{intl.formatMessage({ id: '搜索' })}<IconFont type='iconsousuo2'/></Button>
              <Button onClick={()=> goDetail(1)} type='primary' style={{margin: '0 15px'}}>确定</Button>
            </div>
           </Col>
          )}
          </Row>
        </Form>
      </div>
      {/* 包含录播资源/我的录播 */}
      <div className={`result_search_header ${target === 'custom'? 'chdMyvideo': selected === 'myVideo' ? (classifiedByschedule ? 'myVideo' : ''): 'departmentVideo'}
        ${['录播资源','myVideo', 'departmentVideo'].includes(selected)?'':'none'}`} style={{height: selected === 'myVideo' ? '32px': '80px'}}>
        <Form
          name="complex-form"
          layout="inline"
          form={form}
          initialValues={{
            search: {
              format: '',
            },
          }}
          style={{ width: '100%', height: '100%' }}
          // onValuesChange={oldFormChange}
        >
          <Row style={{ width: '100%' }}>
            {/* 录播资源/我的录播共有的 且 不是我的录播详情*/}
            {!myVideoDetailFilter &&
            <Col span={6}>
              {/* 我的录播 全部+关键词 */}
              <Form.Item>
                <Input.Group compact>
                  {(selected !== 'myVideo' ||  !classifiedByschedule) && target !=='custom' &&<Form.Item name={['search', 'format']} noStyle>
                    <Select className="type-width" onChange={oldFormChange}>
                      {(filterOptions ? options : fullOptions).map((item, index) => (
                        <Option value={item.value || ''} key={'type' + index}>
                          {item.label}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>}
                  <Form.Item name={['search', 'name_']} noStyle>
                    <Input
                      placeholder={intl.formatMessage({ id: '输入关键词' })}
                      className={`${selected !== 'myVideo' ? 'keyword-width' : ''}  ${target !=='custom'?'keyword-width': 'custom-keyword-width'}`}
                      allowClear
                      suffix={rmanGlobalParameter.includes(globalParams.is_show_voice_to_text) && <IconFont type='iconyuyinshibie' title='按住说话' className={asrActive?'active':''} onMouseDown={voiceInput} onMouseUp={() => {
                        setAsrActive(false)
                        record?.stop()
                        setRecord(false)
                      }} />}
                      autoComplete={'off'}
                      onPressEnter={oldFormChange}
                      // onSearch={oldFormChange}
                    />
                  </Form.Item>
                </Input.Group>
              </Form.Item>
            </Col>}
            {/* 我的录播不需要 */}
            {target !=='custom' &&selected !== 'myVideo' && !isMyVideo &&<Col span={4}>
              <Form.Item
                name="analysis_"
                // label="智能标签"
                className="align-form-item"
              >
                <Select
                  placeholder={intl.formatMessage({ id: '是否有语音文本、知识点、关键词' })}
                  mode='multiple'
                  allowClear>
                  <Option value="asr_status~2" key="asr_status">
                    {intl.formatMessage({ id: '有语音文本' })}
                  </Option>
                  <Option value="ocr_status~2" key="ocr_status">
                    {intl.formatMessage({ id: '有知识点' })}
                  </Option>
                  <Option value="knowledge_point_key~2" key="knowledge_point_key">
                    {intl.formatMessage({ id: '有知识要点' })}
                  </Option>
                  <Option value="labels~11" key="labels">
                    {intl.formatMessage({ id: '有标签' })}
                  </Option>
                  <Option value="word~11" key="word">
                    {intl.formatMessage({ id: '有敏感词' })}
                  </Option>
                </Select>
              </Form.Item>
            </Col>}
            {/* 我的录播不需要 */}
            {selected !== 'myVideo' &&<><Form.Item
              noStyle
              shouldUpdate
            >
              {
                ()=>{
                  if(form.getFieldValue('search').format!=='folder'){
                    return <Col span={5}>
                              <Form.Item
                                name="usestatus"
                              >
                                <Select
                                  allowClear
                                  placeholder={intl.formatMessage({ id: '资源使用状态' })}>
                                  <Option value="true" key="true">
                                    {intl.formatMessage({ id: '已使用' })}
                                  </Option>
                                  <Option value="false" key="false">
                                    {intl.formatMessage({ id: '未使用' })}
                                  </Option>
                                </Select>
                              </Form.Item>
                            </Col>
                  }

                }
              }
            </Form.Item>
            
            <Col span={5}>
              <Form.Item
                name="data"
              >
                <RangePicker placeholder={['上传时间范围 起','止']} className="textcenter" style={{ width: '100%' }} />
              </Form.Item>
            </Col></>}
            {/* 我的录播 且 是我的录播详情 */}
            {myVideoDetailFilter && selected === 'myVideo' && <>
              <Col span={6}>
                <Form.Item
                  name="seat"
                >
                  <Select className="guideScreen" placeholder={intl.formatMessage({ id: '录播机位' })}
                    mode='multiple'
                    allowClear
                  >
                    {positions.map((item, index) => (
                      <Option value={item.value || ''} key={'type' + index}>
                        {item.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="week"
                >
                  <Select className="source" placeholder={intl.formatMessage({ id: '周次' })}
                    allowClear
                  >
                    {Array.from({ length: 25 }, (_, index) => index + 1).map((item:any, index:any) => (
                      <Option value={item} key={index}>
                        第{item}周
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <div className="result_search_buttom result_search_button">
                <Button onClick={reset} type='link'>{intl.formatMessage({ id: '清空' })}<UndoOutlined /></Button>
                <Button loading={loading} onClick={oldFormChange} type='primary'>{intl.formatMessage({ id: '搜索' })}<IconFont type='iconsousuo2'/></Button>
                {rmanGlobalParameter.includes(globalParams.search_images_with_pictures) &&<IconFont type='iconxiangji2' className='photo' onClick={openPhotoModal}></IconFont>}
              </div>
              </>}
            {/* 我的录播 且 不是我的录播详情 */}
            {!myVideoDetailFilter && selected === 'myVideo' &&  <>
              <Col span={6}>
                <Form.Item
                  name="semester"
                >
                  <Select className="semester" onChange={(semester) => {
                    setCurrentSemester(semester)
                  }} defaultValue={classifiedByschedule ? currentSemester : null} placeholder={intl.formatMessage({ id: '学期' })}
                  >
                    {
                      semester?.map((item:any,index:number)=>{
                        return <Option value={item.name} key={index}>{item.showSemester}</Option>
                      })
                    }
                  </Select>
                </Form.Item>
              </Col>
              {/* 不按课表时显示录播机位 */}
              {!classifiedByschedule &&  <Col span={5}>
                <Form.Item name="seat">
                  <Select className="guideScreen" placeholder={intl.formatMessage({ id: '录播机位' })} mode='multiple' allowClear>
                    {positions.map((item, index) => (
                      <Option value={item.value || ''} key={'type' + index}>
                        {item.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>}
              {/* 按钮 */}
              <div className="result_search_buttom result_search_button">
                <Button onClick={() => {
                  reset()
                  setCurrentSemester('')
                }} type='link'>{intl.formatMessage({ id: '清空' })}<UndoOutlined /></Button>
                <Button loading={loading} onClick={oldFormChange} type='primary'>{intl.formatMessage({ id: '搜索' })}<IconFont type='iconsousuo2'/></Button>
                {rmanGlobalParameter.includes(globalParams.search_images_with_pictures) &&<IconFont type='iconxiangji2' className='photo' onClick={openPhotoModal}></IconFont>}
              </div></>}
            {/* 我的录播不需要 */}
            {selected !== 'myVideo' && target =='custom' && <Row style={{ width: '100%' }}>
              <Col span={4}>
                <div className="result_search_buttom result_search_button">
                  <Button onClick={reset} type='link'>{intl.formatMessage({ id: '清空' })}<UndoOutlined /></Button>
                  <Button loading={loading} onClick={oldFormChange} type='primary'>{intl.formatMessage({ id: '搜索' })}<IconFont type='iconsousuo2'/></Button>
                  <Button onClick={()=> goDetail(1)} type='primary' style={{margin: '0 15px'}}>确定</Button>
                  {rmanGlobalParameter.includes(globalParams.search_images_with_pictures) &&<IconFont type='iconxiangji2' className='photo' onClick={openPhotoModal}></IconFont>}
                </div>
              </Col>
            </Row>}
          </Row>
          {/* 我的录播不需要 */}
          {target !=='custom' && selected !== 'myVideo' &&  <Row style={{ width: '100%' }}>
           <Col span={6}>
              <Form.Item
                name="seat"
              >
                <Select className="guideScreen" placeholder={intl.formatMessage({ id: '录播机位' })}
                  mode='multiple'
                  allowClear
                >
                  {positions.map((item, index) => (
                    <Option value={item.value || ''} key={'type' + index}>
                      {item.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
           {/* <Col span={5}>
              <Form.Item
                name="source"
              >
                <Select className="source" placeholder={intl.formatMessage({ id: '来源' })}
                  mode='multiple'
                  allowClear
                  maxTagCount={1}
                >
                  {source.map((item:any, index:any) => (
                    <Option value={item || ''} key={index}>
                      {item}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col> */}
            <Col span={5}>
              <Form.Item
                name="semester"
              >
                <Select className="semester" placeholder={intl.formatMessage({ id: '学期' })}
                  allowClear
                >
                  {
                    semester?.map((item:any,index:number)=>{
                      return <Option value={item.name} key={index}>{item.showSemester}</Option>
                    })
                  }
                </Select>
              </Form.Item>
            </Col>
            {selected === '录播资源' && parameterConfig.target_customer as string === 'tvi' &&<Col span={2}>
                <Form.Item
                    name="cataloging_status"
                  >
                    <Select
                      allowClear
                      placeholder={intl.formatMessage({ id: '编目状态' })}>
                        
                        <Option value="1">
                          {intl.formatMessage({ id: '已编目' })}
                        </Option>
                        <Option value="0">
                          {intl.formatMessage({ id: '未编目' })}
                        </Option>
                    </Select>
                  </Form.Item>
            </Col>}
            <div className="result_search_buttom result_search_button">
               <Button onClick={reset} type='link'>{intl.formatMessage({ id: '清空' })}<UndoOutlined /></Button>
               <Button loading={loading} onClick={oldFormChange} type='primary'>{intl.formatMessage({ id: '搜索' })}<IconFont type='iconsousuo2'/></Button>
               {rmanGlobalParameter.includes(globalParams.search_images_with_pictures) &&<IconFont type='iconxiangji2' className='photo' onClick={openPhotoModal}></IconFont>}
            </div>
          </Row>}
        </Form>
      </div>
      <SearchPhotoModal 
        visible={searchModalVisible}
        folderPath={props.folderPath}
        onClose={() => setSearchModalVisible(false)}
      />
    </div>
  );
};

export default SearchBox;
