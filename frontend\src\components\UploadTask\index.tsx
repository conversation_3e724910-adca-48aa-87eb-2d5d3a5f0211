import React, { FC, useState, useRef, useEffect } from 'react';
import { Modal, Button, Checkbox } from 'antd';
import { CheckboxChangeEvent, CheckboxOptionType } from 'antd/lib/checkbox';
import {
  ForwardFilled,
  BackwardOutlined,
  CloseOutlined,
  CloseCircleFilled,
  CheckCircleFilled,
} from '@ant-design/icons';

const CheckboxGroup = Checkbox.Group;
import './index.less';
import { formatMessage, useIntl, useSelector, IUpload, useDispatch } from 'umi';
import uploader from '../upload/core/uploader';
import uploadTypes from '@/types/uploadTypes';
import Task from './task';

interface CreateModalProps {
  refresh: () => void;
  // onCancel: () => void;
}

const UploadTask: React.FC<CreateModalProps> = props => {
  const { showModal, tasks, fields, showPanl, taskPanls } = useSelector<
    { upload: IUpload },
    IUpload
  >(({ upload }) => {
    return upload;
  });
  const dispatch = useDispatch();
  const changeShowPanl = (show: boolean) => {
    dispatch({
      type: 'upload/changeShowPanl',
      payload: {
        value: show,
      },
    });
  };
  const  {mobileFlag}  = useSelector<{ config: any }, any>(
    ({config})=>config
 );
  const intl = useIntl();
  useEffect(() => {
    if (taskPanls.length > 0 && taskPanls.every(item => item.progress === 1)) {
      setTimeout(() => {
        props?.refresh();
      }, 1000); // 加个延迟 避免出现刷不出来的情况
    }
    window.onbeforeunload =
      taskPanls.length === 0 ||
      (taskPanls.length > 0 && taskPanls.every(item => item.progress === 1))
        ? null
        : () => 'exit?';
  }, [taskPanls]);
  return (
    <div className={`upload_task${showPanl ? '' : ' close_task'}${mobileFlag ? ' mobile_task' : ''}`}>
      <div className="open" onClick={() => changeShowPanl(!showPanl)}>
        {showPanl ? <ForwardFilled /> : <BackwardOutlined />}
      </div>
      <div className="uploadtask_box">
        <div className="header">
          {intl.formatMessage({
            id: 'upload_task',
            defaultMessage: '上传任务',
          })}
        </div>
        <div className="task_hearder">
          <div>
            {intl.formatMessage({
              id: 'state',
              defaultMessage: '状态',
            })}
          </div>
          <div>
            {intl.formatMessage({
              id: 'name',
              defaultMessage: '名称',
            })}
          </div>
          <div>
            {intl.formatMessage({
              id: 'size',
              defaultMessage: '大小',
            })}
          </div>
          <div>
            {intl.formatMessage({
              id: 'catalog',
              defaultMessage: '目录',
            })}
          </div>
          <div>
            {intl.formatMessage({
              id: 'progress_speed',
              defaultMessage: '进度',
            })}
          </div>
          <div>
            {intl.formatMessage({
              id: 'operation',
              defaultMessage: '操作',
            })}
          </div>
        </div>
        {taskPanls.map((item, index) => {
          return <Task task={item} key={index} />;
        })}
      </div>
    </div>
  );
};

export default UploadTask;
