.uploadMobile {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, .25);

    .popup-bottom {
        display: flex;
        align-items: center;
        justify-content: center;
        position: fixed;
        height: 10%;
        bottom: 0;
        left: 0;
        width: 100%;
        background-color: #fff;
        border-radius: 16px 16px 0 0;
        padding: 30px;
    }

    button {
        margin-right: 20px;
    }

    .popup-bottom1 {
        position: fixed;
        height: 70%;
        bottom: 0;
        left: 0;
        width: 100%;
        background-color: #fff;
        border-radius: 16px 16px 0 0;
        padding: 10px;

        p {
            text-align: center;
        }
        .close {
            position: absolute;
            right: 10px;
            top: 10px;
        }
        .treeSelect{
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: absolute;
            bottom: 10px;
            left: 10px;
            .upload-box{
                width: 80%;
                span{
                    margin-right: 10px;
                }
            }
            .ant-select {
                width: 70%;
                height: 32px;
                margin-left: 10px;
            }
        }
        
        .uploadList{
            height: 80%;
            overflow: auto;
        }
        .list-item{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            svg{
                font-size: 23px;
            }
            .name{
                margin-left: 10px;
                width: 90%;
                word-break: break-all;
            }
        }
    }
}