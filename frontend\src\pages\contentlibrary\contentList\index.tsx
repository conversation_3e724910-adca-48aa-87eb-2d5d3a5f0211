import React, {
  FC,
  useState,
  useRef,
  useEffect,
  RefObject,
  InputHTMLAttributes,
  ChangeEvent,
} from 'react';
import {
  Form,
  message,
  Button,
  Checkbox,
  Empty,
  Tooltip,
  Pagination,
  Select,
  Breadcrumb,
  Popconfirm,
  Tabs,
  Modal,
  Progress,
  Input,
  Popover,
  Radio,
  Badge
} from 'antd';
import './index.less';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import contentListApis from '@/service/contentListApis';
import entityApis from '@/service/entityApis';
import _ from 'lodash';
import copyandmoveApis from '@/service/copyandmoveApis';
import searchTreeApis from '@/service/searchTreeApis';
import {
  formatMessage,
  useIntl,
  useSelector,
  useDispatch,
  Idownlist,
  useHistory,
} from 'umi';
import {
  MenuItem,
  Idetail,
  IrequestsearchData,
  MaterialList,
  BreadCrumb,
  ISearchlist,
} from './type';
import searchTypes from '@/types/searchTypes';
import {
  SearchTree,
  ContentItem,
  SearchBox,
  ListTop,
  UploadBox,
  UploadTask,
  LinkBox,
  Loading,
  UploadButton,
  IconFont,
  NewFolder,
  DownloadModal,
  ReservationModal,
  IntelligentAnalysisModal,
} from '../../../components/index';
import { IPermission } from '@/models/permission';
import perCfg from '@/permission/config';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import ColumnSelect, { allColumns, allColumns2, allColumns3 } from '@/components/ColumnSelect';
import { byteTransfer, copyObject, sleep, timeTransfer } from '@/utils'
import Icon, { ArrowDownOutlined } from '@ant-design/icons';
import { ReactComponent as cancelshare_icon } from "@/images/icons/cancelshare.svg"
import { ReactComponent as remove_icon } from "@/images/icons/remove.svg"
import OOSDK from '@/otherStorage/ctyun';
import LeftMenu from '@/layout/leftMenu';
import { options } from '@/components/search';
import MobileSearch from '@/components/search/mobileSearch';
import ResourceVerify from '@/pages/myVerify';
import PublishManagement from '@/pages/publishManagement';

import { getSensitiveWord } from '@/utils';
import globalParams from '@/permission/globalParams';
import PublishRescourcemodal from '@/components/PublishRescourcemodal'
import DownloadFolderModal from '@/components/DownloadFolderModal'

const { Option } = Select;
const { TextArea } = Input;
const CheckboxGroup = Checkbox.Group;
const { TabPane } = Tabs;

const ContentList: FC<{}> = () => {
  // const RemoveIcon = (props: any) => <Icon component={remove_icon} {...props} />;
  // const CancelshareIcon = (props: any) => <Icon component={cancelshare_icon} {...props} />;
  let history: any = useHistory();
  const hidecatalogue =
    JSON.stringify(history.location.query) === '{}'
      ? '0'
      : history.location.query.hidecatalogue;
  const parentPath = history.location.query.parentPath || '';
  let showVideoResouce = history.location?.query?.showVideoResouce || ''
  let target = history.location?.query?.target || ''
  let id = history.location?.query?.id || sessionStorage.id || '';
  let js_id = history.location?.query?.jsid;
  let course_name = history.location?.query?.name;
  let course_id = history.location?.query?.course_id;
  let course_no = history.location?.query?.course_no;
  const { parameterConfig, njtcPlayAddress } = useSelector<{ permission: any }, IPermission>(
      ({ permission }) => permission,
    );
  const hiddenMenu = (parameterConfig.target_customer  as string == 'kczx') // 成信大隐藏左侧菜单
    
  const intl = useIntl();
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [recycleBin, setRecycleBin] = useState<boolean>(false); // 回收站
  const [myVideo, setMyVideo] = useState<boolean>(id === 'myVideo'); // 我的录播
  const [departmentVideo, setDepartmentVideo] = useState<boolean>(false); // 院系录播
  const [myCollection, setMyCollection] = useState<boolean>(false); // 我的收藏
  const [myShared, setMyShared] = useState<boolean>(id === 'myShared'); // 我的分享
  const [folderId, setFolderId] = useState<string>(id); //文件夹ID
  const [myVerify, setMyVerify] = useState<boolean>(false); // 我的审核
  // const [mySharedFlag, setMySharedFlag] = useState<any>(undefined); // 我的分享Flag
  const mySharedCurrentPath = useRef<any>(undefined); // 我的分享 当前文件名
  const [publishManagement, setPublishManagement] = useState<boolean>(false); // 发布管理
  const [myGroup, setMyGroup] = useState<boolean>(false); // 群组资源
  const [sharedActiveKey, setSharedActiveKey] = useState<string>('1'); // 我的分享tab
  const [unreadNums, setUnreadNums] = useState<number>(0);
  const [modeSwitch, setModeSwitch] = useState<boolean>(
    localStorage.getItem('view-mode') !== '0',
  ); // 视图切换
  const [indeterminate, setIndeterminate] = useState<boolean>(false);
  const [checkAll, setCheckAll] = useState<boolean>(false);
  const [checkedList, setCheckedList] = useState<Array<any>>([]); //选中的列表
  const [radioDetail, setRadioDetail] = useState<any>({});

  const [allList, setAllList] = useState<Array<any>>([]); //总的的列表
  const [mergedVideoResponses, setMergedVideoResponses] = useState<Array<any>>([]); //总的的列表

  // const [myVideoList, setMyVideoList] = useState<Array<MaterialList>>([]); //总的的列表
  const [currentSemester, setCurrentSemester] = useState<any>('')
  const [class_weekly, setClass_weekly] = useState<any>([])
  const [videoDetail, setVideoDetail] = useState<any>({ js_id, course_name, course_id, course_no }); //总的的列表
  const [singleOrAll, setSingleOrAll] = useState<boolean>(false);
  const [issearch, setIssearch] = useState<boolean>(false);
  const [publishVisible, setPublishVisible] = useState<boolean>(false) // 控制发布资源弹窗
  let hideBanner = history.location?.query?.hidebanner || false; // 是否显示导航栏
  const [downloadFolderVisible, setDownloadFolderVisible] = useState<boolean>(false); // 控制下载文件夹弹窗
  const [downloadFolderObj, setDownloadFolderObj] = useState<string>(''); // 下载文件夹id
  const [searchData, setSearchData] = useState<IrequestsearchData>({
    starttime: [], //开始时间
    endtime: [], //结束时间
    teacher: [], //老师
    college: [], //学院
    keyword: [], //关键词
    name_: '', //资源名
    major: [], //专业
    type: [], //素材类型
    usestatus: '', //素材类型
    cataloging_status: '', //编目状态
    smart_status: '', //智能
    labels: '', //标签
    word: '', //敏感词
    seat: '', //机位
    week: '', //周次
    sasr_status: '', //智能
    socr_status: '',
    creator: [], //上传人
  });
  const [currentPublish, setCurrentPublish] = useState<any>({})
  const sort: Array<MenuItem> = [
    {
      label: 'relevant',
      value: `${intl.formatMessage({ id: '相关度' })}`,
    },
    {
      label: 'createDate_,down',
      value: `${intl.formatMessage({ id: '上传时间' })}`,
      icon: <IconFont type="iconchangjiantou-xia" className="icon" />,
    },
    {
      label: 'createDate_,up',
      value: `${intl.formatMessage({ id: '上传时间' })}`,
      icon: <IconFont type="iconchangjiantou-shang" className="icon" />,
    },
    {
      label: 'name_,down',
      value: `${intl.formatMessage({ id: '名称' })}`,
      icon: <IconFont type="iconchangjiantou-xia" className="icon" />,
    },
    {
      label: 'name_,up',
      value: `${intl.formatMessage({ id: '名称' })}`,
      icon: <IconFont type="iconchangjiantou-shang" className="icon" />,
    },
  ];
  //供群组资源、共享资源目录使用
  const sort_public: Array<MenuItem> = [
    {
      label: 'relevant',
      value: `${intl.formatMessage({ id: '相关度' })}`,
    },
    {
      label: 'createDate_,down',
      value: `${intl.formatMessage({ id: '上传时间' })}`,
      icon: <IconFont type="iconchangjiantou-xia" className="icon" />,
    },
    {
      label: 'createDate_,up',
      value: `${intl.formatMessage({ id: '上传时间' })}`,
      icon: <IconFont type="iconchangjiantou-shang" className="icon" />,
    },
    {
      label: 'name_,down',
      value: `${intl.formatMessage({ id: '名称' })}`,
      icon: <IconFont type="iconchangjiantou-xia" className="icon" />,
    },
    {
      label: 'name_,up',
      value: `${intl.formatMessage({ id: '名称' })}`,
      icon: <IconFont type="iconchangjiantou-shang" className="icon" />,
    },
    {
      label: 'hits,down',
      value: `${intl.formatMessage({ id: '浏览量' })}`,
      icon: <IconFont type="iconchangjiantou-xia" className="icon" />,
    },
    {
      label: 'hits,up',
      value: `${intl.formatMessage({ id: '浏览量' })}`,
      icon: <IconFont type="iconchangjiantou-shang" className="icon" />,
    },
    {
      label: 'total_like,down',
      value: `${intl.formatMessage({ id: '点赞量' })}`,
      icon: <IconFont type="iconchangjiantou-xia" className="icon" />,
    },
    {
      label: 'total_like,up',
      value: `${intl.formatMessage({ id: '点赞量' })}`,
      icon: <IconFont type="iconchangjiantou-shang" className="icon" />,
    },
    {
      label: 'total_copy,down',
      value: `${intl.formatMessage({ id: '转存量' })}`,
      icon: <IconFont type="iconchangjiantou-xia" className="icon" />,
    },
    {
      label: 'total_copy,up',
      value: `${intl.formatMessage({ id: '转存量' })}`,
      icon: <IconFont type="iconchangjiantou-shang" className="icon" />,
    },
  ];
  const sort_myshare: Array<MenuItem> = [
    {
      label: 'createDate_,down',
      value: `${intl.formatMessage({ id: '分享时间' })}`,
      icon: <IconFont type="iconchangjiantou-xia" className="icon" />,
    },
    {
      label: 'createDate_,up',
      value: `${intl.formatMessage({ id: '分享时间' })}`,
      icon: <IconFont type="iconchangjiantou-shang" className="icon" />,
    },
  ];
  const sort_recycleBin: Array<MenuItem> = [
    {
      label: 'deleteTime_,down',
      value: `${intl.formatMessage({ id: '删除时间' })}`,
      icon: <IconFont type="iconchangjiantou-xia" className="icon" />,
    },
    {
      label: 'deleteTime_,up',
      value: `${intl.formatMessage({ id: '删除时间' })}`,
      icon: <IconFont type="iconchangjiantou-shang" className="icon" />,
    },
  ];
  const [collation, setCollation] = useState<string>('createDate_,down'); //排序
  const [totalPage, setTotalPage] = useState<number>(0); //素材总数
  const [current, setCurrent] = useState<number>(1); //当前页码
  const [pageSize, setPageSize] = useState<number>(50); //每页条数

  const [showMoreSearch, setShowMoreSearch] = useState<boolean>(false); //每页条数
  const [selectCondition, setSelectCondition] = useState<any>([])
  const [folderPath, setFolderPath] = useState<string>(''); //文件夹路径
  const [initPath, setInitPath] = useState<string>(''); //用于存取初始化的path
  const [getFolder, setGetFolder] = useState<Idetail | null>(null); //新建文件夹
  const [renameShow, setRenameShow] = useState<boolean>(false); // 重命名
  const [shareInnerFlag, setShareInnerFlag] = useState<any>(undefined); // 分享内部标记
  const [testTree, setTestTree] = useState<searchTypes.IFolder | any>([]);
  const [breadCrumb, setBreadCrumb] = useState<Array<BreadCrumb>>([
    { name: '', folderId: '', path: '', item: undefined },
  ]);
  const [downloadModalVisible, setDownloadModalVisible] = useState<boolean>(
    false,
  );
  const downlist = useSelector<{ download: any }, Idownlist>(({ download }) => {
    return download.downlist;
  });
  
  const { permissions, rmanGlobalParameter, classifiedByschedule } = useSelector<{ permission: any }, IPermission>(
    ({ permission }) => permission,
  );
  const localColumn = localStorage.getItem('rman_column_select');
  const [columnSelect, setColumnSelect] = useState(localColumn ? JSON.parse(localColumn) : []);
  const [copyShow, setCopyShow] = useState<boolean>(false);
  const [removeVisible, setRemoveVisible] = useState<boolean>(false);
  const [cancelshareVisible, setCancelshareVisible] = useState<boolean>(false);
  const uploadRef = useRef<any>(null);
  const referRef = useRef<any>(null);
  const [applyStorageVisible, setApplyStorageVisible] = useState<boolean>(false);
  const [applyStorageObj, setApplyStorageObj] = useState<any>(null);
  const [applyStorageTemp, setApplyStorageTemp] = useState<any>({});
  const [currentStorageObj, setCurrentStorageObj] = useState<any>({
    usedSpace: 0,
    totalSpace: 0
  });
  const [expand, setExpand] = useState<boolean>(false);
  const [currentSelected, setCurrentSelected] = useState<string>(id);
  const [searchVoiceWord, setSearchVoiceWord] = useState<string>('');
  const [
    intelligentAnalysisModalVisible,
    setIntelligentAnalysisModalVisible,
  ] = useState<boolean>(false);
  const [
    reservationModalVisible,
    setReservationModalVisible,
  ] = useState<boolean>(false);

  const [analysisCurrent, setAnalysisCurrent] = useState<any>([]);
  const { mobileFlag } = useSelector<{ config: any }, any>(
    ({ config }) => config
  );
  const [catalogueVisible, setCatalogueVisible] = useState<boolean>(true); //用于移动端适配仅展示目录标志
  const [myVideoDetailFilter, setMyVideoDetailFilter] = useState<boolean>(!!js_id);
  const [operatMenuVisible, setOpreatMenuVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [openId, setOpenId] = useState<string>('') // 上科从工作台主页我的资源点击跳转过来的文件夹id
  //对于分享添加link参数
  const shareLink = useRef<any>('');
  window.addEventListener('beforeunload', function (e) {
    sessionStorage.setItem('id', '');
  });

  useEffect(() => {
    const id = sessionStorage.getItem('my_resource_contentId')
    if (id) setOpenId(id)
    sessionStorage.removeItem('my_resource_contentId')
    sessionStorage.removeItem('publishResource')
  },[])

  useEffect(() => {
    let data: any;
    if (allList.length && openId) {
      allList.forEach((item: any) => {
        if (item.contentId_ === openId) {
          return data = item
        }
      })
      if (data && data.tree_) {
        detail(data, 1)
        setOpenId('')
      }
    }
  }, [openId, allList])
  useEffect(() => {
    contentListApis.getCurrentSemester().then((res: any) => {
      if (res?.errorCode === 'success') {
        setCurrentSemester(res.extendMessage?.name);
      }
    })
    // 初始化
    if (localStorage.getItem('rman_resource_size')) {
      console.log(localStorage.getItem('rman_resource_size'))
      setPageSize(Number(localStorage.getItem('rman_resource_size')))
      console.log(localStorage.getItem('rman_resource_size'))
    }
    //初始化未读消息
    fetchSharedNums();
    //获取用户空间
    userStorageObj();
    userApplyResult();
  }, []);
  useEffect(() => {
    if(sessionStorage?.id === 'myVerify' || sessionStorage.publishResource){
      setMyVerify(true)
      setFolderId('myVerify')
    }
  }, [sessionStorage?.id, sessionStorage.publishResource])
  useEffect(() => {
    shareLink.current = ''; //每次进来先置空 防止旧数据
    if (myVerify) {
      searchpublic()
      return
    };
    if (folderId !== '') {
      if (id === 'myShared' || id === 'myVideo') {
        searchpublic()
      }
      recycleBin
        ? searchRecycleBin() //回收站
        : myCollection ? searchMyCollection() //我的收藏
          : myVideo ?
           ( myVideoDetailFilter ? searchMyVideoDetail()
              : searchMyVideo()) //我的录播
            : departmentVideo ? searchDepartmentVideo()  // 院系录播
              : publishManagement ? searchPublishManagement()
                : myShared ? sharedActiveKey === '1' ? (shareInnerFlag ? fetchShareList(shareInnerFlag) : shareMyself()) : searchMyShared() //我的分享
                  // : myGroup ? searchMyGroup()
                  : singleOrAll //有筛选条件
                    ? searchAllList()
                    : searchFolderList();
    } else if (folderPath !== '') {
      toDirectory();
    } else {
      searchpublic();
    }
  }, [searchData, collation, current, pageSize, folderId, folderPath, sharedActiveKey, myVerify, myVideoDetailFilter]);
  useEffect(() => {
    if(myVideoDetailFilter){
      const mergedVideoResponses:any = [];
      allList.forEach(item => {
        mergedVideoResponses.push(...item.videoSearchResponses);
      });
      setMergedVideoResponses(mergedVideoResponses)
    }
  }, [allList])
  useEffect(() => {
    localStorage.setItem('view-mode', modeSwitch ? '1' : '0');
  }, [modeSwitch]);
  useEffect(() => {
    if(myVideo && classifiedByschedule){
      setModeSwitch(false)
    }
  }, [myVideo])
  const fetchSharedNums = async () => {
    contentListApis.searchUnreadNums().then((res: any) => {
      if (res?.success) {
        setUnreadNums(res.data)
      }
    })
  }
  useEffect(() => {
    // 判断是否包含文件夹
    let i = checkedList.some((item: any) => {
      return item.type_ === 'folder';
    });
    setCopyShow(i);
    dispatch({
      type: 'download/changedownload',
      payload: {
        value: checkedList,
      },
    });
  }, [checkedList]);

  // 点击所在目录跳转
  const toDirectory = async () => {
    setAllList([])
    setLoading(true);
    let data: ISearchlist = processingData();
    // await contentListApis.searchall(data).then(res => {
    //进入所在目录的路径，只展示当前目录下的文件夹和文件
    contentListApis.searchfolder(data).then(res => {
      if (res && res.data && res.success) {
        setTotalPage(res.data.recordTotal);
        if (res.data.pageTotal < 0 && current !== 1) {
          setCurrent(current - 1);
          return;
        }
        if (res.data.breadcrumbs[0].name === '公共资源') {
          res.data.breadcrumbs.splice(0, 1)
          console.log(res.data.breadcrumbs)
        }
        setAllList(res.data.data);
        setCheckedList([]);
        setIndeterminate(false);
        setCheckAll(false);
        setBreadCrumb(res.data.breadcrumbs);
        dispatch({
          type: 'jurisdiction/changeSecondarydirectory',
          payload: {
            value:
              (res.data.breadcrumbs.length === 1 &&
                res.data.breadcrumbs[0].name === '公共资源') ||
              (res.data.breadcrumbs[1] &&
                res.data.breadcrumbs[1].name === '录播资源'),
          },
        });
      } else {
        setAllList([]);
      }
    });
  };
  // 刷新当前节点
  const referCurrentFolder = (node?: any, key?: any, name?: any): any => {
    if (breadCrumb.length >= 5) {
      return;
    } else {
      referRef?.current.referCurrentFolder();
      // referRef?.current.referCurrentFolder(key,name);
    }
  };

  //目录树
  const searchpublic = async () => {
    searchTreeApis.gettreebylevel(2).then(res => {
      if (res && res.data && res.success) {
        let newData: any = [];
        res.data.map((item: any) => {
          if (item.name === '公共资源') {
            item.children?.forEach((item: any) => {
              newData.push({ ...item, layer: 1 })
            })
          } else {
            newData.push(item);
          }
        })
        newData = newData.filter(Boolean);//过滤空对象
        (target === 'custom' || !id) && setCurrentSelected(newData[0].name);

        if (!id) {
          if (folderId === '' && parentPath == '') {
            setFolderId(newData[0].id);
            // setFolderPath(newData[0].path);
            setInitPath(newData[0].path);
          } else {
            //可以通过外部传入指定跳转到一级的目录
            let arr = newData.filter(
              (item: any) => item.parentPath == parentPath,
            );
            arr[0]?.id && setFolderId(arr[0]?.id);
            arr[0]?.path && setFolderPath(arr[0]?.path);
          }
        }       
        setTestTree(newData);
      }
    });
  };
  // 公共素材检索
  const searchFolderList = async (path?: any, item?: any) => {
    setBreadCrumb([])
    setAllList([])
    setLoading(true);
    if (item) {
      const ress = await contentListApis.fetchShareLists({
        contentIds: item.contentIds,
        link: item.link,
        password: item.linkPassword,
        shareType: item.shareType,
        page: current,
        size: pageSize,
      });
      if (ress?.success) {
        setAllList(ress.data.data);
        setTotalPage(ress.data.recordTotal);
        setBreadCrumb([])
        setLoading(false);
      }
      return
    }
    let data: ISearchlist = processingData(path);
    localStorage.setItem('params', JSON.stringify({ ...data, apiName: singleOrAll ? 'all' : 'folder' }))
    if (myShared && sharedActiveKey === '1') { //针对别人分享个人文件夹无权限时；
      contentListApis.searchfolder_share(data, shareLink.current).then(res => {
        if (res && res.data && res.success) {
          setTotalPage(res.data.recordTotal);
          if (res.data.pageTotal < 0 && current !== 1) {
            setCurrent(current - 1);
            return;
          }
          if (res.data.breadcrumbs[0].name === '公共资源') {
            res.data.breadcrumbs.splice(0, 1)
            console.log(res.data.breadcrumbs)
          }
          setAllList(res.data.data);
          setCheckedList([]);
          setIndeterminate(false);
          setCheckAll(false);
          //对我的分享定制路径
          for (let i = 0; i < res.data.breadcrumbs.length; i++) {
            if (res.data.breadcrumbs[i].path === mySharedCurrentPath.current) {
              res.data.breadcrumbs.splice(0, i)
              break
            }
          }
          res.data.breadcrumbs.unshift({
            // name: sharedActiveKey === '1' ? '分享给我的' : '我分享的',
            name: shareInnerFlag.name_,
            item: shareInnerFlag,
            folderId: '',
            path: ''
          })
          setBreadCrumb(res.data.breadcrumbs);
          dispatch({
            type: 'jurisdiction/changeSecondarydirectory',
            payload: {
              value:
                (res.data.breadcrumbs.length === 1 &&
                  res.data.breadcrumbs[0].name === '公共资源') ||
                (res.data.breadcrumbs[1] &&
                  res.data.breadcrumbs[1].name === '录播资源'),
            },
          });
        } else {
          setAllList([]);
        }
        setLoading(false);
      });
      return;
    }
    contentListApis.searchfolder(data).then(res => {
      if (res && res.data && res.success) {
        setTotalPage(res.data.recordTotal);
        if (res.data.pageTotal < 0 && current !== 1) {
          setCurrent(current - 1);
          return;
        }
        if (res.data.breadcrumbs[0].name === '公共资源') {
          res.data.breadcrumbs.splice(0, 1)
          console.log(res.data.breadcrumbs)
        }
        setAllList(res.data?.data);
        setCheckedList([]);
        setIndeterminate(false);
        setCheckAll(false);
        //对我的分享定制路径
        if (myShared) {
          for (let i = 0; i < res.data.breadcrumbs.length; i++) {
            if (res.data.breadcrumbs[i].path === mySharedCurrentPath.current) {
              res.data.breadcrumbs.splice(0, i)
              break
            }
          }
          res.data.breadcrumbs.unshift({
            // name: sharedActiveKey === '1' ? '分享给我的' : '我分享的',
            name: shareInnerFlag?.name_,
            item: shareInnerFlag,
            folderId: '',
            path: ''
          })
        }
        setBreadCrumb(res.data.breadcrumbs);
        dispatch({
          type: 'jurisdiction/changeSecondarydirectory',
          payload: {
            value:
              (res.data.breadcrumbs.length === 1 &&
                res.data.breadcrumbs[0].name === '公共资源') ||
              (res.data.breadcrumbs[1] &&
                res.data.breadcrumbs[1].name === '录播资源'),
          },
        });
      } else {
        setAllList([]);
      }
      setLoading(false);
    });
  };
  // 全部素材检索
  const searchAllList = async () => {
    setLoading(true);
    setAllList([])
    setBreadCrumb([])
    let data: ISearchlist = processingData();
    localStorage.setItem('params', JSON.stringify({ ...data, apiName: singleOrAll ? 'all' : 'folder' }))
    data.folderPath = folderPath;
    contentListApis.searchall(data).then(res => {
      if (res && res.data && res.success) {
        setTotalPage(res.data.recordTotal);
        if (res.data.pageTotal < 0 && current !== 1) {
          setCurrent(current - 1);
          return;
        }
        if (res.data.breadcrumbs[0].name === '公共资源') {
          res.data.breadcrumbs.splice(0, 1)
          console.log(res.data.breadcrumbs)
        }
        setAllList(res.data.data);
        setCheckedList([]);
        setIndeterminate(false);
        setCheckAll(false);
        setBreadCrumb(res.data.breadcrumbs);
        setFolderId(res?.data?.breadcrumbs[0]?.folderId)
        dispatch({
          type: 'jurisdiction/changeSecondarydirectory',
          payload: {
            value:
              (res.data.breadcrumbs.length === 1 &&
                res.data.breadcrumbs[0].name === '公共资源') ||
              (res.data.breadcrumbs[1] &&
                res.data.breadcrumbs[1].name === '录播资源'),
          },
        });
      } else {
        setAllList([]);
      }
      setLoading(false);
    });
  };
  // 全部素材检索--查询上级目录节点key 供左侧树同步选中
  const searchFolderListForLeftTree = async (path: string, name: string) => {
    let key;
    setLoading(true);
    if (name === '个人资源' || name === '公共资源') { //单独处理
      await searchTreeApis.gettreebylevel(2).then(res => {
        if (res && res.data && res.success) {
          res.data.forEach((item: any) => {
            // console.log(item.name_)
            if (item.name === name) {
              key = item;
            }
          })
        }
        setLoading(false);
      });
    } else {
      let data: ISearchlist = processingDataForLeftTree(path);
      await contentListApis.searchfolder(data).then(res => {
        if (res && res.data && res.success) {
          res.data.data?.forEach((item: any) => {
            // console.log(item.name_)
            if (item.name_ === name) {
              key = item;
            }
          })
          // 左侧数选中
          setBreadCrumb(res.data.breadcrumbs);
          setFolderId(res?.data?.breadcrumbs[0]?.folderId)
        }
        setLoading(false);
      });
    }
    referRef?.current.selectnode(key);
    //选中完 则去除所在目录这一列
    setColumnSelect(
      (columnSelect.map((item: any) => {
        if (item !== 'directory')
          return item
      }).filter(Boolean)))
    reset(2);
  };
  const searchRecycleBin = async () => {
    setLoading(true);
    setBreadCrumb([]);
    contentListApis
      .getrecyclebinlist({
        pageIndex: current,
        pageSize: pageSize,
        sortFields: [
          {
            field: collation.indexOf(',') != -1 ? collation.split(',')[0] : 'name_',
            isDesc: collation.indexOf('down') !== -1,
          },
        ],
      })
      .then(res => {
        if (res && res.data && res.success) {
          setTotalPage(res.data.recordTotal);
          if (res.data.pageTotal < 0 && current !== 1) {
            setCurrent(current - 1);
            return;
          }
          setAllList(res.data.data);
          setCheckedList([]);
          setIndeterminate(false);
          setCheckAll(false);
          setBreadCrumb([]);
          dispatch({
            type: 'jurisdiction/changeSecondarydirectory',
            payload: {
              value: false
            },
          });
        } else {
          setAllList([]);
        }
        setLoading(false);
      });
  };
  const searchPublishManagement = async () => {
    setAllList([]);
    setBreadCrumb([]);
    // contentListApis[departmentVideo ? 'getdepartmentvideolist' : 'getMyVideo'](processingData())
    //   .then(res => {
    //     if (res && res.data && res.success) {
    //       setTotalPage(res.data.recordTotal);
    //       if (res.data.pageTotal < 0 && current !== 1) {
    //         setCurrent(current - 1);
    //         return;
    //       }
    //       setAllList(res.data.results);
    //       setCheckedList([]);
    //       setIndeterminate(false);
    //       setCheckAll(false);
    //       setBreadCrumb([]);
    //       dispatch({
    //         type: 'jurisdiction/changeSecondarydirectory',
    //         payload: {
    //           value: false
    //         },
    //       });
    //     } else {
    //       setAllList([]);
    //     }
    //   });
  }
  const searchDepartmentVideo = async () => {
    setAllList([]);
    contentListApis[departmentVideo ? 'getdepartmentvideolist' : 'getMyVideo'](processingData()).then(res => {
      if (res && res.data && res.success) {
        setTotalPage(res.data.recordTotal);
        if (res.data.pageTotal < 0 && current !== 1) {
          setCurrent(current - 1);
          return;
        }
        setAllList(res.data.data);
        setCheckedList([]);
        setIndeterminate(false);
        setCheckAll(false);
        setBreadCrumb([]);
        dispatch({
          type: 'jurisdiction/changeSecondarydirectory',
          payload: {
            value: false
          },
        });
      } else {
        setAllList([]);
      }
    });
  };
  const searchMyVideoDetail = () => {
    setBreadCrumb([{ path: '', folderId: '', name: '我的录播' }, {
      path: '',
      folderId: '', name: `${videoDetail.course_name}（课程号：${videoDetail.course_id}，课序号：${videoDetail.course_no}）`
    }])
    contentListApis.getMyVideoDetails({
      jsId: videoDetail.js_id,
      page: current,
      size: pageSize,
      seat: searchData.seat,
      semester: searchData.semester || currentSemester,
      courseId: videoDetail.course_id,
      courseNo: videoDetail.course_no,
      week: searchData.week
    }).then(res => {
      if (res?.success) {
        setTotalPage(res.data.count);
        res.data.results.forEach(i => {
          i.videoSearchResponses.forEach(j => {
            j.entityData.name_ = i.course_name
            j.entityData.type_ = 'biz_sobey_video'
          })
        })
        setAllList(res.data.results.map(result => ({
          ...result,
          allVideosSelected: false, // 添加一个allVideosSelected属性来标记该课程的所有视频是否被选中
          videoSearchResponses: result.videoSearchResponses.map(item => ({
            ...item,
            entityData: {
              ...item.entityData,
              name_: `${item.entityData.name_}_${item.entityData.seat}`,
            },
            ...item.entityData,
            name_: `${item.entityData.name_}_${item.entityData.seat}`,
            contentId_: item.entityData.contentId_,
            isSelected: false, // 添加一个isSelected属性来标记视频是否被选中
          }))
        })));
        setColumnSelect([
          'course_name',
          'week',
          'week_day',
          'person',
          'seat',
          'teacher_name',
          'filesize',
          'duration',
          'createDate_',
          'area_name'
        ])
      }
    })
  }
  const searchMyVideo = async () => {
    setLoading(true);
    setBreadCrumb([{
      path: '',
      folderId: '', name: '我的录播'
    }])
    setAllList([]);
    // 我的录播接口请求
    let params:any = {}
    // 不按课表分类
    if (!classifiedByschedule) {
      let conditions = processingData().conditions
      let sortFields:any = []
      if (collation.split(',')[0] !== 'relevant') {
        sortFields =[
          {
            field: collation.indexOf(',') != -1 ? collation.split(',')[0] : 'createDate_',
            isDesc: collation.indexOf('down') != -1 ? true : false,
          },
        ]
      }
      params = {
        pageIndex: current,
        pageSize: pageSize,
        // keyword: searchData.name_,
        conditions,
        sortFields
      }
      setColumnSelect([
        'name',
        'filesize',
        'createDate'
      ])
      contentListApis['getMyVideoNotByschedule'](params).then(res => {
        if(res?.success) {
          setTotalPage(res?.data?.recordTotal);
          setAllList(res?.data?.data);
        }
      }).finally(() => {
        setLoading(false);
      })
    } else {
      // 不按课表分类
      let params:any = {
        page: current,
        size: pageSize,
        courseName: searchData.name_,
        semester: searchData.semester || currentSemester
      }
      setColumnSelect([
        'area_name',
        'semester',
        'resource',
        'course_name',
        'teacher_name'
      ])
      contentListApis['getMyVideo'](params).then(res => {
        if(res?.success) {
          setTotalPage(res?.data?.count);
          setAllList(res?.data?.results);
        }
      }).finally(() => {
        setLoading(false);
      })
    }
  }
  const searchMyShared = async () => {
    setAllList([])
    contentListApis
      .searchMyShare({
        pageIndex: current,
        pageSize: pageSize,
        isDesc: collation.indexOf('down') != -1 ? true : false,
        // sortFields: [
        //   {
        //     field: collation.indexOf('time') != -1 ? 'createDate_' : 'name_',
        //     isDesc: collation.indexOf('down') != -1 ? true : false,
        //   },
        // ]
      })
      .then(res => {
        if (res && res.data && res.success) {
          setTotalPage(res.data.recordTotal);
          if (res.data.pageTotal < 0 && current !== 1) {
            setCurrent(current - 1);
            return;
          }
          setAllList(res.data.data);
          // setTotalPage(res.data?.total);
          setCheckedList([]);
          setIndeterminate(false);
          setCheckAll(false);
          setBreadCrumb([]);
          mySharedCurrentPath.current = '';//必须要清空之前选中的路径值
          dispatch({
            type: 'jurisdiction/changeSecondarydirectory',
            payload: {
              value: false
            },
          });
        } else {
          setAllList([]);
        }
      });
  };

  const shareMyself = async (flag?: boolean) => {
    setAllList([])
    setBreadCrumb([]);
    contentListApis
      .shareMyself({
        page: current,
        size: pageSize,
        isDesc: collation.indexOf('down') != -1 ? true : false,
        // sortFields: [
        //   {
        //     field: collation.indexOf('time') != -1 ? 'shareTime' : 'name_',
        //     isDesc: collation.indexOf('down') != -1 ? true : false,
        //   },
        // ]
      })
      .then(res => {
        if (res && res.data && res.success) {
          setTotalPage(res.data.recordTotal);
          if (res.data.pageTotal < 0 && current !== 1) {
            setCurrent(current - 1);
            return;
          }
          setAllList(res.data?.data);
          // setTotalPage(res.data?.pageTotal);
          setCheckedList([]);
          setIndeterminate(false);
          setCheckAll(false);
          setBreadCrumb([]);
          mySharedCurrentPath.current = '';//必须要清空之前选中的路径值
          dispatch({
            type: 'jurisdiction/changeSecondarydirectory',
            payload: {
              value: false
            },
          });
        } else {
          setAllList([]);
        }
      });
  };
  const searchMyCollection = async () => {
    setAllList([])
    setBreadCrumb([]);
    contentListApis
      .getmycollectionlist({
        pageIndex: current,
        pageSize: pageSize,
        sortFields: [
          {
            // field: collation.indexOf('time') != -1 ? 'createDate_' : 'name_',
            field: collation.indexOf(',') != -1 ? collation.split(',')[0] : 'name_',
            isDesc: collation.indexOf('down') != -1 ? true : false,
          },
        ]
      })
      .then(res => {
        if (res && res.data && res.success) {
          setTotalPage(res.data.total);
          if (res.data.pageTotal < 0 && current !== 1) {
            setCurrent(current - 1);
            return;
          }
          setAllList(res.data.results);
          setCheckedList([]);
          setIndeterminate(false);
          setCheckAll(false);
          setBreadCrumb([]);
          dispatch({
            type: 'jurisdiction/changeSecondarydirectory',
            payload: {
              value: false
            },
          });
        } else {
          setAllList([]);
        }
      });
  };
  useEffect(() => {
    setCollation(recycleBin ? 'deleteTime_,down' : 'createDate_,down')
  }, [recycleBin])
  // useEffect(() => {
  //   if(selectCondition.filter(item => item.field === 'name_').length>0){
  //     setCollation('relevant')
  //   }
  //   else{
  //     setCollation('createDate_,down')
  //   }
  // },  [JSON.stringify(selectCondition)])
  // 数据处理
  const processingData = (path?: any) => {
    let list: ISearchlist = {
      folderId: myShared || departmentVideo ? '' : folderPath ? '' : folderId, //我的分享不需要传 针对面包屑无id情况 就不能传ID了 不然是上选中文件夹的id 会查不出数据
      folderPath: path ? (path + '/') : folderPath + '/',
      sortFields: [
        {
          field: collation.indexOf(',') != -1 ? collation.split(',')[0] : 'name_',
          isDesc: collation.indexOf('down') != -1 ? true : false,
        },
      ],
      pageIndex: current,
      pageSize: pageSize,
    };
    collation === 'relevant' && (list.sortFields = []);
    searchData.keyword?.length !== 0 && (list.keyword = searchData.keyword);
    let condition: any = [];
    searchData.starttime.length !== 0 &&
      condition.push(
        {
          field: 'createDate_',
          searchRelation: 6,
          value: searchData.endtime,
        },
        {
          field: 'createDate_',
          searchRelation: 4,
          value: searchData.starttime,
        },
      );
    searchData.name_ !== '' &&
      condition.push({
        field: 'name_',
        searchRelation: 2,
        value: [searchData.name_],
      });
    searchData.type.length !== 0 &&
      condition.push({
        field: 'type_',
        searchRelation: 0,
        value: searchData.type,
      });
    searchData.creator.length !== 0 &&
      condition.push({
        field: 'creator',
        searchRelation: 0,
        value: searchData.creator,
      });
    searchData.teacher.length !== 0 &&
      condition.push({
        field: 'teacher',
        searchRelation: 0,
        value: searchData.teacher,
      });

    searchData.college.length !== 0 &&
      condition.push({
        field: 'college',
        searchRelation: 0,
        value: searchData.college,
      });
    searchData.major.length !== 0 &&
      condition.push({
        field: 'major',
        searchRelation: 0,
        value: searchData.major,
      });
    searchData.usestatus &&
      condition.push({
        field: 'usestatus',
        searchRelation: 0,
        value: [searchData.usestatus],
      });
    searchData.cataloging_status &&
      condition.push({
        field: 'cataloging_status',
        searchRelation: Number(searchData.cataloging_status) === 1 ? 0 : 1,
        value: ['1'],
      });
    searchData.smart_status &&
      condition.push({
        field: 'smart_status',
        searchRelation: 0,
        value: [searchData.smart_status],
      });
    searchData.labels &&
      condition.push({
        field: 'labels',
        searchRelation: Number(searchData.labels) || 0,
        value: [],
      });
    searchData.word &&
      condition.push({
        field: 'word',
        searchRelation: Number(searchData.word) || 0,
        value: [],
      });
    searchData.seat &&
      condition.push({
        field: 'seat',
        searchRelation: 0,
        value: searchData.seat,
      });
    searchData.sasr_status &&
      condition.push({
        field: 'asr_status',
        searchRelation: 0,
        value: [searchData.sasr_status],
      });
    searchData.socr_status &&
      condition.push({
        field: 'intelliState',
        searchRelation: 0,
        value: [searchData.socr_status],
      });
    //学期
    searchData.semester &&
      condition.push({
        field: 'semester',
        searchRelation: 0,
        value: currentSelected === '录播资源' || (myVideo && !classifiedByschedule) ? [searchData.semester]: [],
      });
    //来源
    searchData.source && searchData.source.length > 0 &&
      condition.push({
        field: 'source',
        searchRelation: 0,
        value: searchData.source,
      });
    searchData.knowledge_point_key &&
      condition.push({
        field: 'knowledge_point_key',
        searchRelation: 0,
        value: [searchData.knowledge_point_key],
      });
    list.conditions = condition;
    setSelectCondition(condition)
    return list;
  };
  // 数据处理-供同步左侧树用
  const processingDataForLeftTree = (path: string) => {
    let list: ISearchlist = {
      folderId: '',
      folderPath: path + '/',
      sortFields: [
        {
          field: collation.indexOf(',') != -1 ? collation.split(',')[0] : 'name_',
          isDesc: collation.indexOf('down') != -1 ? true : false,
        },
      ],
      pageIndex: current,
      pageSize: pageSize,
    };
    collation === 'relevant' && (list.sortFields = []);
    let condition: any = [];
    list.conditions = condition;
    return list;
  };
  // 获取最后一级的路径
  const getdirectory = (tree: any): string => {
    const directory = tree.split('/');
    if (directory.length > 0) {
      let str1 = directory[directory.length - 1];
      let str2 = directory[directory.length - 2]; //由于返回数据不同 得单独处理
      if (str1.includes('public')) {
        return '公共资源'
      } else if (str2.includes('private')) {
        return '个人资源'
      } else {
        return directory[directory.length - 1];
      }
    }
    return '';
  }
  //获取网络带宽
  const getNetworkSpeed = () => {
    // 判断浏览器是否支持navigator.connection属性
    if ((navigator as any)?.connection) {
      // 获取当前网络连接信息
      var connection = (navigator as any)?.connection;
      // 如果浏览器支持downlink属性，则输出当前网络的下行带宽
      if (connection.downlink) {
        return connection.downlink
      } else {
        return
      }
    } else {
      return 0
    }
  }
  //资源分享资源查询
  const fetchShareList = async (parent: any) => {
    shareLink.current = parent.link || '';
    console.log(parent.link);
    let params = {
      contentIds: parent.contentIds,
      link: parent.link,
      password: parent.linkPassword,
      shareType: parent.shareType,
      page: current,
      size: pageSize,
    }
    const ress: any = await contentListApis.fetchShareLists(params);
    console.log(ress);
    if (ress?.success) {
      setAllList(ress.data.data);
      setTotalPage(ress.data.recordTotal);
      setShareInnerFlag(parent);
      // 点击则调用已读接口
      // if (parent.isRead === false) { //每次点进来就算一次
      contentListApis.markRead(parent.link, parent.shareUserCode).then((res: any) => {
        if (res?.success) {
          fetchSharedNums()//标记完毕时 实时刷新未读数量；
        }
      });
      // }
      setBreadCrumb([{
        path: '',
        folderId: '',
        name: parent.name_
      }])
    }
  }
  // 跳转到详情  type  1是点击的文件名跳转  2是点击所在目录 跳转
  const detail = (item: any, type: any, item_?: any) => {
    console.log(item, type, item_,window.parent.location.pathname , window.parent.location.hash,'111111111111111111111111111111111');

    if (!renameShow && !recycleBin && !myVideo) {

      //针对我的分享一级目录单独处理 没有hive的属性
      if (!item.tree_) {
        fetchShareList(item)
      } else {
        //取上级路径备用
        const parentPath_ = item.tree_[0].split('/');
        parentPath_.pop();
        if (item.type_ === 'folder') {
          //针对我的分享得单独处理
          if (myShared) {
            // 手动传path 避免与其他混淆
            setShareInnerFlag({ ...shareInnerFlag, ...item });
            !mySharedCurrentPath.current ? mySharedCurrentPath.current = item.tree_[0] + '/' + item.name_ : '';
            searchFolderList(item.tree_[0] + '/' + item.name_);
            // 点击则调用已读接口
            // if (item.isRead === false) {
            //   contentListApis.markRead([item.uniqueCode]).then((res: any) => {
            //     if (res?.success) {
            //       fetchSharedNums()//标记完毕时 实时刷新未读数量；
            //     }
            //   });
            // }
          } else {
            // 文件夹
            if (type == 1) {
              // 点击名称
              setFolderId(item.contentId_);
              setFolderPath(item.tree_[0] + '/' + item.name_);
              referRef?.current.selectnode(item);
              reset(type);

            } else {
              // 点击目录
              setFolderId('');
              setFolderPath(item.tree_[0]);
              //所在目录在左侧树其实是选中上一级目录 只能重新查
              // console.log(parentPath_,parentPath_.join('/'))
              searchFolderListForLeftTree(parentPath_.join('/'), getdirectory(item.tree_[0]))
            }
          }

        } else {
          // 资源
          if (type == 1) {
            // 点击则调用已读接口
            if (item.isRead === false) {
              item.isRead = true; //手动标记已读
              // contentListApis.markRead([item.uniqueCode]).then((res: any) => {
              //   if (res?.success) {
              //     fetchSharedNums()//标记完毕时 实时刷新未读数量；
              //   }
              // });
            }
            // 点击名称
            // history.location.query.layout 网址参数 layout（layout === 1，跳转路径为window.parent的路径加上childurl参数；否则直接跳转）
            const layout = history.location.query?.layout || '0';
            let params = {
              hidecatalogue: hidecatalogue === '1' ? hidecatalogue : '0',
              ispublic: item.tree_[0].includes('public'),
              dirTree: item.tree_[0],
            };
            // let encodeParams = btoa(encodeURI(JSON.stringify(params)));
            // let path = `#/basic/contentDetail/${item.contentId_}?params=${encodeParams}`;
            // let path = "";
            if (layout === '0') {
              if (target === 'custom') {
                entityApis.getEntity(item.contentId_, true, false).then((res: any) => {
                  let dataType = res.data.type;
                  let path = _.find(res.data.fileGroups, (f: any) => {
                    return f.typeCode === (dataType === 'biz_sobey_document' || dataType === 'biz_sobey_picture' ? 'sourcefile' : 'previewfile');
                  });
                  if (!path) {
                    path = _.find(res.data.fileGroups, (f: any) => {
                      return f.typeCode === 'sourcefile';
                    });
                  }
                  //自动适应清晰度
                  let autoPathObj = path ? path.fileItems[0] : {}
                  if (path && path.fileItems && path.fileItems.length) {
                    let speed = getNetworkSpeed()
                    if (speed < 1) {
                      autoPathObj = path.fileItems.find((p: any) => p.bitRate <= 512 * 1000) || path.fileItems[0]
                    } else if (speed < 3) {
                      autoPathObj = path.fileItems.find((p: any) => p.bitRate <= 1024 * 1000) || path.fileItems[0]
                    } else if (speed < 5) {
                      autoPathObj = path.fileItems.find((p: any) => p.bitRate <= 2048 * 1000) || path.fileItems[0]
                    } else {
                      autoPathObj = path.fileItems[path.fileItems.length - 1]
                    }
                  }
                  let autoPath = autoPathObj.displayPath
                  const dev = process.env.NODE_ENV === 'development' ? "" : "/rman"
                  console.log({
                    createDate: item.createDate_,
                    contentId_: item.contentId_,
                    keyframe_: item.keyframe_,
                    name: item.name_,
                    type_: item.type_,
                    filePath: autoPath,
                    playAddress: `https://tpass.njtc.edu.cn/auth/cas/login?service=https://course.njtc.edu.cn/unifiedlogin/v1/cas/login?redirect_url=${njtcPlayAddress[0]}${dev}/#/basic/rmanDetail/${item.contentId_}?target=custom`,
                    action: 'selectResource'
                  })
                  if (parameterConfig.customustomer === 'custom') {
                    window.parent.postMessage(
                      JSON.stringify({
                        createDate: item.createDate_,
                        contentId_: item.contentId_,
                        keyframe_: item.keyframe_,
                        name: item.name_,
                        type_: item.type_,
                        filePath: autoPath,
                        playAddress: `https://tpass.njtc.edu.cn/auth/cas/login?service=https://course.njtc.edu.cn/unifiedlogin/v1/cas/login?redirect_url=${njtcPlayAddress[0]}${dev}/#/basic/rmanDetail/${item.contentId_}?target=custom`,
                        action: 'selectResource'
                      }),
                      '*'
                    );
                  }
                  else if (parameterConfig.customustomer === 'csu') {
                    contentListApis.filetochaoxing({
                      contentIds: [item.contentId_],
                      jsgh: (window as any).login_useInfo.userCode,
                    }).then(res => {
                      message.success('上传成功')
                    })
                  }
                })
              }
              else {
                // 跳转路径为window.parent的路径加上childurl参数
                let src = window.parent.location.pathname + window.parent.location.hash + `?contentid=${item.contentId_}`;
                if (src.includes('/teaching/rman')) {
                  src = src.replaceAll('/teaching/rman', `/teaching/contentDetail`);
                } else if (src.includes('/resource')) {
                  src = window.parent.location.pathname + `#/resource/contentDetail?contentid=${item.contentId_}`
                } else if (src.includes("/basic/rmanList") || src.includes("/basic/rmanCenterList")) {
                  const dev = process.env.NODE_ENV === 'development' ? "" : "/rman"
                  // src = `${window.location.origin}${dev}/#/basic/rmanDetail/${item.contentId_}`;
                  src = `${dev}/#/basic/rmanDetail/${item.contentId_}${(myShared && sharedActiveKey === '1') ? '_share_' + shareLink.current : ''}?showArrow=true`;
                  // if (hideBanner) {
                  //   src = src + `&hidebanner=${hideBanner}`;
                  // }
                }
                window.open(window.location.origin + src);
              }
            } else if (layout === '1') {
              let childurl = encodeURIComponent(`/rman/#/basic/contentDetail/${item.contentId_}?layout=${layout}&hidecatalogue=${hidecatalogue ? hidecatalogue : 0}`);
              let src = window.parent.location.origin + window.parent.location.pathname + '#/resource?childurl=' + childurl;
              window.open(src); 
            } else {
              console.log('其他情况');
            }
          } else {
            // 点击目录
            setFolderId('');
            setFolderPath(item.tree_[0]);
            searchFolderListForLeftTree(parentPath_.join('/'), getdirectory(item.tree_[0]))
            // referRef?.current.selectnode(item);
            // reset(type);
          }
        }
      }
    }
    if(myVideo){
      if (type == 'myVideo') {
        if (window.parent.location.hash.includes("/basic/rmanList") || window.parent.location.hash.includes("/basic/rmanCenterList")) {
          const dev = process.env.NODE_ENV === 'development' ? "" : "/rman"
          let src = `${dev}/#/basic/rmanDetail/${item?.contentId_}${(myShared && sharedActiveKey === '1') ? '_share_' + shareLink.current : ''}?showArrow=true`;
          window.open(window.location.origin+src);
          return false;
        }
      }
      if(item_?.entityData){
        let src = window.parent.location.pathname + window.parent.location.hash + `?contentid=${item_?.entityData?.contentId_}`;
        if (src.includes("/basic/rmanList") || src.includes("/basic/rmanCenterList")) {
          const dev = process.env.NODE_ENV === 'development' ? "" : "/rman"
          // src = `${window.location.origin}${dev}/#/basic/rmanDetail/${item.contentId_}`;
          src = `${dev}/#/basic/rmanDetail/${item_?.entityData?.contentId_}${(myShared && sharedActiveKey === '1') ? '_share_' + shareLink.current : ''}?showArrow=true`;
          // if (hideBanner) {
          //   src = src + `&hidebanner=${hideBanner}`;
          // }
        }
        window.open(window.location.origin + src);
      }
      else {
        setMyVideoDetailFilter(true)
        setVideoDetail(item)
      }
    }
  };

  // 排序切换
  const handleChange = (value: string) => {
    setCollation(value);
    setCurrent(1);
  };
  // 检索框样式切换
  const styleChange = () => {
    setExpand(!expand)
  };

  // // 格式切换
  // const optionsChange = (value: string) => {
  //   resourceSearch();
  // };
  const resourceSearch = (formData?: any) => {
    console.log(formData, 'formData');

    const tempData = formData || {}
    const {
      name_,
      format,
      data,
      college,
      major,
      teacher,
      creator,
      seat,
      semester,
      source,
      week,
      usestatus,
      smart_status,
      labels,
      word,
      asr_status,
      cataloging_status,
      ocr_status,
      knowledge_point_key,
    } = tempData;

    setSearchVoiceWord(name_ || '');
    setSingleOrAll(
      !!(
        // !!(search ? search.name_ || search.format : search) ||
        name_ ||
        format ||
        data ||
        college ||
        !!(major ? major.length : major) ||
        teacher ||
        usestatus ||
        smart_status ||
        labels ||
        word ||
        (seat && seat.length ? seat : '') ||
        semester ||
        week ||
        (source && source.length ? source : '') ||
        asr_status ||
        ocr_status ||
        knowledge_point_key ||
        cataloging_status ||
        creator
      ),
    );
    setSearchData({
      starttime: data ? [data[0].format('YYYY-MM-DD') + ' 00:00:00'] : [],
      endtime: data ? [data[1].format('YYYY-MM-DD') + ' 23:59:59'] : [],
      teacher: teacher ? [teacher] : [],
      college: college ? [college] : [],
      // keyword: search ? (search.keyword ? [search.keyword] : []) : [],
      // name_: search ? (search.name_ ? search.name_ : '') : '',
      name_: name_ ? name_ : '',
      major: major ? major : [],
      // type: search ? (search.format ? [search.format] : []) : [],
      type: format ? [format] : [],
      usestatus: usestatus || '',
      smart_status: smart_status || '',
      labels: labels || '',
      word: word || '',
      seat: seat || '',
      semester: semester || '',
      week: week || '',
      source: source || [],
      sasr_status: asr_status || '',
      cataloging_status: cataloging_status || '',
      socr_status: ocr_status || '',
      creator: creator ? [creator] : [],
      knowledge_point_key: knowledge_point_key || '',
    });
    setCurrent(1);
    setIssearch(true);
    if (name_) {
      setCollation('relevant')
    }
    else {
      setCollation('createDate_,down')
    }

  };
  // 页码切换
  const changepage = (page: number, size: any) => {
    if (page === 0) {
      setCurrent(1);
      setPageSize(size || 0);
    } else {
      setCurrent(page);
      setPageSize(size || 0);
    }
    localStorage.setItem('rman_resource_size', size)
  };
  const myVideoCheckAllChange = (event) => {
    const isAllSelected = event.target.checked;
    const updatedCourses = allList.map(course => ({
      ...course,
      allVideosSelected: isAllSelected,
      videoSearchResponses: course.videoSearchResponses.map(video => ({
        ...video,
        isSelected: isAllSelected
      }))
    }));
    setAllList(updatedCourses);
  };
  // 全选
  const onCheckAllChange = (e: CheckboxChangeEvent) => {
    setCheckedList(e.target.checked ? allList : []);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  };
  // 单选
  const onChange = (check: Array<any>) => {
    setCheckedList(check);
    setIndeterminate(!!check.length && check.length < allList.length);
    setCheckAll(check.length === allList.length);
  };
  const handleClick = (e: RadioChangeEvent) => {
    setRadioDetail(e.target.value);
  };
  // 刷新
  // const refresh = (page?: number, key?: any, id?: any, cancelShared?: any) => {
  const refresh = (page?: number, obj?: any) => {
    // setTimeout(() => {
    //   fetchSharedNums() //当触及刷新列表时都更新下未读数量 加延时是为了异步有时数据返回不及时
    // }, 300)
    //针对异步操作数据更新不及时 需要手动刷新的操作
    setCheckedList([]);
    setIndeterminate(false);
    // userApplyResult();
    userStorageObj();
    if (obj) {
      if (obj.name === '点赞') {
        let temp = copyObject(allList);
        for (var i = 0; i < obj.lists.length; i++) {
          for (var j = 0; j < temp.length; j++) {
            if (obj.lists[i] === temp[j].contentId_) {
              temp[j].isLike = true
              break;
            }
          }
        }
        setAllList(temp)
        return;
      } else if (obj.name === '取消点赞') {
        let temp1 = copyObject(allList);
        for (var i = 0; i < obj.lists.length; i++) {
          for (var j = 0; j < temp1.length; j++) {
            if (obj.lists[i] === temp1[j].contentId_) {
              temp1[j].isLike = false
              break;
            }
          }
        }
        setAllList(temp1)
        setTimeout(() => { refresh() }, 500)
        return;
      }
      if (obj.name === '收藏') {
        let temp = copyObject(allList);
        for (var i = 0; i < obj.lists.length; i++) {
          for (var j = 0; j < temp.length; j++) {
            if (obj.lists[i] === temp[j].contentId_) {
              temp[j].isCollection = true
              break;
            }
          }
        }
        setAllList(temp)
        return;
      } else if (obj.name === '取消收藏') {
        let temp1 = copyObject(allList);
        if (!myCollection) {//不在我的收藏列表里的取消
          for (var i = 0; i < obj.lists.length; i++) {
            for (var j = 0; j < temp1.length; j++) {
              if (obj.lists[i] === temp1[j].contentId_) {
                temp1[j].isCollection = false
                break;
              }
            }
          }
        } else {
          for (var i = 0; i < obj.lists.length; i++) {
            for (var j = 0; j < temp1.length; j++) {
              if (obj.lists[i] === temp1[j].contentId_) {
                temp1.splice(j, 1)
                break;
              }
            }
          }
        }
        setAllList(temp1)
        setTimeout(() => { refresh() }, 500)
        return;
      } else if (obj.name === '重命名') {
        let temp2 = copyObject(allList);
        referRef?.current.updateFolderName(obj.contentId_, obj.newName);
        for (var j = 0; j < temp2.length; j++) {
          if (obj.contentId_ === temp2[j].contentId_) {
            temp2[j].name_ = obj.newName
            break;
          }
        }
        setAllList(temp2)
        return;
      } else if (obj.name === '删除') {
        referRef?.current.deleteNode(obj.contentId_);
      }
      else if (obj.name === '新建') {
        referRef?.current.addNode(obj.item);
      }
      else if (obj.name === '取消分享') {
        let temp3 = copyObject(allList);
        for (var i = 0; i < obj.lists.length; i++) {
          for (var j = 0; j < temp3.length; j++) {
            if (obj.lists[i] === temp3[j].link) {
              temp3.splice(j, 1)
              break;
            }
          }
        }
        setAllList(temp3)
        setTimeout(() => { refresh() }, 500)
        return;
      } else {
      }
    }
    if (page && page !== current) {
      setCurrent(page);
      return Promise.resolve().then(() => setGetFolder(null));
    }
    // searchpublic方法会刷新左侧全部目录  要求只刷新当前目录
    return Promise.all([
      recycleBin
        ? searchRecycleBin()
        : myCollection ? searchMyCollection()
          : myVideo ? (myVideoDetailFilter ? searchMyVideoDetail() : searchMyVideo())
            : departmentVideo ? searchDepartmentVideo()
              : publishManagement ? searchPublishManagement()
                : myShared ? sharedActiveKey === '1' ? (shareInnerFlag ? shareInnerFlag.type_ === 'folder' ? searchFolderList(shareInnerFlag.tree_[0] + '/' + shareInnerFlag.name_) : fetchShareList(shareInnerFlag) : shareMyself()) : searchMyShared()
                  : singleOrAll //有筛选条件
                    ? searchAllList()
                    : folderId === '' && folderPath === '' ? '' : searchFolderList(),
      // searchpublic(),
      // referCurrentFolder(),
    ]).then(() => setGetFolder(null)); //待创建完毕后才消失
  };
  // 新建文件夹
  const newFolder = () => {
    setGetFolder({
      type_: 'folder',
      name_: `${intl.formatMessage({ id: '新建文件夹' })}`,
      fatherTreeId: folderPath ? '' : folderId,
      fatherTreePath: folderPath,//针对面包屑没有返回ID的情况 只能用path
      keyframe: myGroup ? '/rman/static/images/groupFolder.png' : '/rman/static/images/folder.png',
    });
  };
  // 批量移除
  const removeShared = async () => {
    setRemoveVisible(false)
    console.log(checkedList)
    contentListApis.removeShared(checkedList.map((item: any) => item.link))
      .then((res: any) => {
        if (res.success) {
          refresh();
          message.success(`${intl.formatMessage({ id: '移除成功' })}`)
        } else {
          message.error(`${intl.formatMessage({ id: '移除失败！' })}`)
        }
      })
  };
  // 批量取消分享
  const batchCancleShared = async () => {
    console.log(checkedList)
    setCancelshareVisible(false)
    //  contentListApis.cancelShared(checkedList.map((item: any) => item.link))
    contentListApis.cancelShared(checkedList.map((item: any) => {
      return {
        link: item.link,
        shareUserCodes: [item.shareUserCode]
      }
    }))
      .then((res: any) => {
        if (res.success) {
          // refresh(undefined, '', '', checkedList.map((item: any) => item.contentId_))
          refresh(undefined, { name: `${intl.formatMessage({ id: '取消分享' })}`, lists: checkedList.map((item: any) => item.link) })
          // fetchSharedNums()//标记完毕时 实时刷新未读数量；
          message.success(`${intl.formatMessage({ id: '移除成功' })}`)
        } else {
          message.error(`${intl.formatMessage({ id: '取消收藏失败' })}`)
        }
      })
  };
  // 目录树选择
  const selsctTree = async (id: string, path: string, node?: any) => {
    console.log('node', node)
    if (mobileFlag) {
      dispatch({
        type: 'config/updateState',
        payload: {
          leftMenuExpand: false
        }
      })
    }
    //判断当前在所在根目录 zzh 2022-08-09
    if (path) {
      if (path.includes('global_sobey_defaultclass/public/共享资源')) {
        setCurrentSelected('共享资源');
      } else if (path.includes('global_sobey_defaultclass/public/录播资源')) {
        setCurrentSelected('录播资源');
      } else if (path.includes('global_sobey_defaultclass/public/群组资源')) {
        setCurrentSelected('群组资源');
      } else if (path.includes('global_sobey_defaultclass/private')) {
        setCurrentSelected('个人资源');
      } else {

      }
    } else {
      setCurrentSelected(id);
    }
    setSharedActiveKey('1');//需要初始化
    setFolderId(id);
    setFolderPath(path);
    setRecycleBin(id === 'recyclebin');
    setMyVideo(id === 'myVideo');
    if (id !== 'myVideo') {
      setMyVideoDetailFilter(false)
    }
    setDepartmentVideo(id === 'departmentVideo');
    setMyShared(id === 'myShared');
    setMyVerify(id === 'myVerify');
    setPublishManagement(id === 'publishManagement')
    setMyGroup(node?.path?.includes('群组资源'));
    setMyCollection(id === 'myCollection');
    if (id === 'recyclebin') {
      columnSelect.splice(2, 0, 'deleteUser_')
      console.log('变更后', columnSelect)
      setColumnSelect(columnSelect)
    } else {
      if (columnSelect.includes('deleteUser_')) {
        columnSelect.splice(columnSelect.indexOf('deleteUser_'), 1)
        setColumnSelect(columnSelect)
      }
    }
    setCatalogueVisible(false);
    reset();
  };
  // 重置
  const reset = (type: number = 1) => {
    form.resetFields();
    resourceSearch();
    // issearch 是标识当前是不是在搜索  如果是搜索 就把所在目录展示出来
    if (type != 2) {
      setIssearch(false);
    }
  };
  const goback = () => {
    setShareInnerFlag(undefined)
    if (sharedActiveKey === '1') {
      shareMyself()
    } else {
      searchMyShared();
    }
  }
  const selectShareLists = async (item: any) => {
    const ress = await contentListApis.fetchShareLists({
      contentIds: item.contentIds,
      link: item.link,
      password: item.linkPassword,
      shareType: item.shareType,
    });
    if (ress?.success) {
      setAllList(ress.data.data);
      setTotalPage(ress.data.recordTotal);
      setBreadCrumb([{
        path: '',
        folderId: '',
        name: item.name_
      }])
    }
  }
  // 导航条跳转（手机端）
  const goBreadcrumb = (item: BreadCrumb) => {
    // console.log('item',item)
    // if (item.folderId !== folderId) {
    console.log(item.path, folderPath)
    //针对我的分享 文件夹回退
    if (myShared) {
      if (item.item) {
        selectShareLists(item.item);
      }
      else if (!item.path && !item.folderId) {
        // sharedActiveKey === '1' ? shareMyself() : searchMyShared();
        goback();
        setBreadCrumb([])
      } else {
        searchFolderList(item.path);
      }

    }
    else if (myVideo) {
      if (item.name === '我的录播') {
        setVideoDetail({})
        setMyVideoDetailFilter(false)
        reset()
      }
    }
    else {
      if (item.path !== folderPath) {
        // setFolderId(item.folderId);
        setFolderPath(item.path);

        //取上级路径备用
        const parentPath_ = item.path.split('/');
        if (parentPath_[parentPath_.length - 1] !== 'public') parentPath_.pop(); //回退到公共资源时不能剔除最后一级路径
        // 2021-12-15 处理导航条跳转重命名bug
        searchFolderListForLeftTree(parentPath_.join('/'), getdirectory(item.path))
        reset();
      }
    }
  };
  // 导航条跳转（pc）
  const goBreadcrumbPc = (item: BreadCrumb, itemPath: any) => {
    if (itemPath.value == '公共资源') {
      return
    }
    //针对我的分享 文件夹回退
    if (myShared) {
      if (item.item) {
        selectShareLists(item.item);
      } else if (!item.path && !item.folderId) {
        // sharedActiveKey === '1' ? shareMyself() : searchMyShared();
        goback();
        setBreadCrumb([])
      } else {
        searchFolderList(item.path);
      }

    } else if (myVideo) {
      if (item.name === '我的录播') {
        setVideoDetail({})
        setMyVideoDetailFilter(false)
        reset()
      }
    } else {
      if (itemPath.key !== folderPath  ) {
        setFolderPath(itemPath.key || item.path);
        //取上级路径备用
        const parentPath_ = item.path.split('/');
        if (parentPath_[parentPath_.length - 1] !== 'public') parentPath_.pop(); //回退到公共资源时不能剔除最后一级路径
        // 2021-12-15 处理导航条跳转重命名bug
        searchFolderListForLeftTree(itemPath.key, itemPath.value)
        reset();
      }
    }
  };
  const openMoreSearch = () => {
    setShowMoreSearch(!showMoreSearch);
  };

  const onColumnChange = (value: string[]) => {
    localStorage.setItem('rman_column_select', JSON.stringify(value));
    setColumnSelect(value);
  };

  const changeTabs = (key: any) => {
    console.log(key, 'key');
    setSharedActiveKey(key);
  };
  useEffect(() => {
    console.log('sharedActiveKey', sharedActiveKey);
    if (myShared) {
      if (shareInnerFlag) {
        setColumnSelect([
          'extension',
          'size',
          'person',
          'time',
        ])
      } else {
        if (sharedActiveKey === '1') {
          setColumnSelect(allColumns2)
        } else {
          setColumnSelect(allColumns3)
        }
      }
    }
  }, [myShared, sharedActiveKey, shareInnerFlag])
  const apply = () => {
    console.log('applyStorageObj', applyStorageObj);
    if (applyStorageObj?.auditStatus === 1 || applyStorageObj?.auditStatus === 2) {
      contentListApis.applyStorageLatestRead();
    }
    setApplyStorageVisible(true)
  }
  const applyClosed = () => {
    setApplyStorageVisible(false);
    userApplyResult();
  }
  //
  const sizeChange = (e: any, flag: any) => {
    // e.persiste();
    if (!flag) {
      // console.log('size',e.target.value);
      const num = Number(e.target.value);
      if (num < 0) {
        message.error(`${intl.formatMessage({ id: '大小不能为负数' })}`)
      }
      if (num > 50) {
        message.error(`${intl.formatMessage({ id: '大小不能超过50' })}`)
      }
      if (isNaN(num)) {
        message.error(`${intl.formatMessage({ id: '请设置有效数值' })}`)
      }
      setApplyStorageTemp({
        ...applyStorageTemp,
        applySpace: num
      })
    } else {
      setApplyStorageTemp({
        ...applyStorageTemp,
        applyReason: e.target.value
      })
    }
  }
  //提交申请
  const submitApply = () => {
    const num = Number(applyStorageTemp.applySpace);
    if (num < 0) {
      message.error(`${intl.formatMessage({ id: '大小不能为负数' })}`)
      return
    }
    if (num > 50) {
      message.error('大小不能超过50');
      message.error(`${intl.formatMessage({ id: '大小不能超过50' })}`)
      return
    }
    if (!num || isNaN(num)) {
      message.error(`${intl.formatMessage({ id: '请设置有效数值' })}`)
      return
    }
    getSensitiveWord(applyStorageTemp.applyReason, '申请理由', async () => {
      const res: any = await contentListApis.applyStorage({
        spaceSize: applyStorageTemp.applySpace,
        reason: applyStorageTemp.applyReason
      });
      // console.log('提交申请结果',res);
      if (res.errorCode === 'success') {
        message.success('提交成功');
        setApplyStorageVisible(false);
        userApplyResult();
      } else {
        message.error('提交失败');
      }
    })
  }
  //取消申请
  const cancleApply = () => {

  }
  //获取用户空间
  const userStorageObj = async () => {
    const res: any = await contentListApis.fetchUserStorage();
    if (res?.errorCode === 'success') {
      setCurrentStorageObj(res.extendMessage);
    } else {
      // message.error('获取用户空间失败');
    }
  }
  //获取用户审核情况
  const userApplyResult = async () => {
    const res: any = await contentListApis.applyStorageLatest();
    if (res?.errorCode === 'success') {
      setApplyStorageObj(res?.extendMessage);
    } else {
      // message.error('获取用户空间失败');
    }
  }
  //取消扩容申请
  const userApplyCancle = async () => {
    const res: any = await contentListApis.applyStorageCancle();
    if (res.errorCode === 'success') {
      message.success(`${intl.formatMessage({ id: '取消成功' })}`)
      setApplyStorageVisible(false);
      setApplyStorageTemp({
        ...applyStorageTemp,
        applyReason: ''
      })
      userApplyResult();
    } else {
      message.error(`${intl.formatMessage({ id: '取消失败' })}`)
    }
  }
  //只能分析回调 由于外层有CheckboxGroup 内部的checkbox都会触发选中事件报错 只能提出来做回调
  const analysisCallback = (current: any) => {
    setAnalysisCurrent(current);
    setIntelligentAnalysisModalVisible(true);
  }
  let linkComponents = null;
  if (!myShared) {
    linkComponents = <LinkBox
      newFolder={newFolder}
      refresh={refresh}
      LinkBoxList={(myVideo && classifiedByschedule)? mergedVideoResponses.filter(item => item.isSelected) : checkedList}
      setCheckedList={setCheckedList}
      key={checkedList as any} //必要 解决数据不及时更新问题
      downloadBoxOpen={() => setDownloadModalVisible(true)}
      fetchSharedNums={fetchSharedNums}
      recycleBin={recycleBin}
      myVideo={myVideo}
      departmentVideo={departmentVideo}
      searchMyVideoDetail={searchMyVideoDetail}
      myCollection={myCollection}
      // copyAddMove={
      //   breadCrumb.length === 1 &&
      //   breadCrumb[0].name === '公共资源'
      // }
      pubOrPri={currentSelected !== '个人资源'}
      vedioResource={breadCrumb[0]?.path.includes('录播资源')} // 录播里面的文件夹不可分享 其余可分享的标识
      shareEnble={(breadCrumb[0]?.path.includes('共享资源') || breadCrumb[0]?.path.includes('录播资源') || breadCrumb[0]?.path.includes('个人资源') || myVideo || departmentVideo || myGroup)}
      currentPath={breadCrumb}
      datalength={allList?.length}
      targetFolder={folderPath || initPath}
      setPublishVisible={setPublishVisible}
      getDetail={(detailArray: any) => {setCurrentPublish(detailArray)}}
      getFolderObj={(msg: any) => setDownloadFolderObj(msg)}
      setDownloadFolderVisible={setDownloadFolderVisible}
      classifiedByschedule={classifiedByschedule}
      setPublishVisible={setPublishVisible}
      getDetail={(detailArray: any) => {setCurrentPublish(detailArray)}}
    />
  }
  let share_btns = [];
  if (myShared) {
    if (!shareInnerFlag) {
      if (sharedActiveKey == '1') {
        share_btns.push({
          title: `${intl.formatMessage({ id: '移除' })}`,
          className: 'mysharedbtn',
          disabled: (checkedList.length == 0),
          func: () => setRemoveVisible(true),
          // dom:<RemoveIcon />
          dom: <IconFont type='iconremove' />
        })
      } else {
        share_btns.push({
          title: `${intl.formatMessage({ id: '取消分享' })}`,
          className: 'mysharedbtn',
          disabled: (checkedList.length == 0),
          func: () => setCancelshareVisible(true),
          dom: <IconFont type='iconcancelshare' />
        })
      }
      share_btns.push({
        title: `${intl.formatMessage({ id: '刷新' })}`,
        className: 'mysharedbtn mysharedrefreshbtn',
        func: () => refresh(),
        dom: <IconFont type='iconshuaxin' />
      })
    } else {
      share_btns.push({
        title: `${intl.formatMessage({ id: '下载' })}`,
        disabled: (checkedList.length === 0 || copyShow || shareInnerFlag?.onlyRead),
        func: () => setDownloadModalVisible(true),
        dom: <ArrowDownOutlined />
      })
    }
  }

  return (
    <div className="contentlist_container">
      <div className="content_bottom">
        {!hideBanner && !hiddenMenu && window.localStorage.getItem('upform_platform') === 'standard' && parameterConfig.target_customer != null && parameterConfig.target_customer as string !== 'npu' && target!=='custom' && <LeftMenu userInfo={(window as any).login_useInfo} />}
        <div className={`directory_tree${mobileFlag ? (!catalogueVisible ? ' none' : ' mobile_tree') : ''}`}>
          <SearchTree
            onSelect={selsctTree}
            loading={loading}
            basictree={testTree}
            ref={referRef}
            id={folderId}
            path={folderPath}
            unreadNums={unreadNums}
          />
          {parameterConfig.target_customer as string !== 'npu' && parameterConfig.target_customer as string !== 'cau' && target !== 'custom' && <Button className='resourcephoto' onClick={() => { window.open('#/mapv3') }} type='primary'>
            <img style={{ marginRight: '10px' }} src={require('../../../images/icons/resource.png')} alt="" />
            {intl.formatMessage({ id: '资源图谱' })}
          </Button>}
          {
            hidecatalogue !== '1' && target !== 'custom' &&
            <div
              className='personalStorage'
            >
              <div>
                <Progress percent={currentStorageObj.usedSpace * 100 / currentStorageObj.totalSpace} strokeWidth={12} showInfo={false} />
                <div>
                  <div>
                    <span>{intl.formatMessage({ id: '个人资源使用' })}&nbsp;</span>
                    <span>{`${byteTransfer(isNaN(currentStorageObj.usedSpace) ? 0 : Number(currentStorageObj.usedSpace))}/`}</span>
                    <span>{currentStorageObj.totalSpace === -1 ? '无限制' : `${byteTransfer(isNaN(currentStorageObj.totalSpace) ? 0 : Number(currentStorageObj.totalSpace))}`}</span>
                  </div>
                  <div className='applyContainer'>
                    <a onClick={apply}>
                      {/* {applyStorageObj?.auditStatus?(applyStorageObj?.isRead?(applyStorageObj?.auditStatus===1 ?'扩容成功':'扩容失败'):'申请扩容'):(applyStorageObj?.auditStatus===0?'扩容审核中':'申请扩容')} */}
                      {applyStorageObj ?
                        (applyStorageObj.auditStatus === 0 ? <span className='audit'>{intl.formatMessage({ id: '扩容审核中' })}</span>
                          : (applyStorageObj?.auditStatus === 1 ? <span className='success'>{intl.formatMessage({ id: '扩容成功' })}</span> : <span className='fail'>{intl.formatMessage({ id: '扩容失败' })}</span>))
                        : <span>{intl.formatMessage({ id: '申请扩容' })}</span>}
                    </a>
                  </div>
                </div>
              </div>
            </div>
          }
        </div>
        <div className={`content${mobileFlag ? (catalogueVisible ? ' none' : ' mobile_content') : ''}`}>
          {
            myVerify ? <ResourceVerify setCatalogueVisible={setCatalogueVisible} />
              : publishManagement ? <PublishManagement /> :
                <>
                  {
                    myShared && (!shareInnerFlag ?
                      <>
                        <Tabs defaultActiveKey={sharedActiveKey}
                          onChange={changeTabs} className='shareMyself'
                        >
                          <TabPane tab={
                            <>
                              <Badge
                                count={unreadNums}
                                overflowCount={99}
                                size='small'
                              >
                                <span>{intl.formatMessage({ id: '分享给我的' })}</span>
                              </Badge>
                              {/* <span>{unreadNums}</span> */}
                            </>
                          } key='1' className='shareMyself'>
                          </TabPane>
                          <TabPane tab={intl.formatMessage({ id: '我分享的' })} key='2'></TabPane>
                        </Tabs>
                      </> :
                      (
                        !mobileFlag &&
                        <div className='inner'>
                          <div className='goback' onClick={goback}>{`< ${shareInnerFlag.name_}`}</div>
                          <div className='shareinfo'>
                            <span>{`${intl.formatMessage({ id: '分享时间' })}：${timeTransfer(shareInnerFlag.shareTime)}`}</span>
                            <span>{`${intl.formatMessage({ id: '过期时间' })}：${shareInnerFlag.expirationTime}`}</span>
                          </div>
                        </div>
                      )
                    )
                  }

                  {/* 右侧部分内容 */}
                  <div className={!myShared ? "auto" : shareInnerFlag ? "auto_shared_inner" : "auto_shared"}>
                    {/* 搜索内容 */}
                    {(!recycleBin && !myCollection && !myShared) && (
                      <div
                        className={`top_search ${showMoreSearch ? 'top_search_down' : ''
                          }`}
                      >
                        <div className="drop_down">
                          {
                            mobileFlag ?
                              <MobileSearch
                                filterOptions={(breadCrumb[0]?.name == '录播资源')}
                                // optionsChange={optionsChange}
                                resourceSearch={resourceSearch}
                                expand={expand}
                                selected={currentSelected}
                                form={form}
                                reset={reset}
                              />
                              :
                              <SearchBox
                                classifiedByschedule={classifiedByschedule}
                                filterOptions={(breadCrumb[0]?.name == '录播资源')}
                                currentSemester={currentSemester}
                                setCurrentSemester={setCurrentSemester}
                                myVideoDetailFilter={myVideoDetailFilter}
                                // optionsChange={optionsChange}
                                resourceSearch={resourceSearch}
                                weeks={JSON.parse(videoDetail.class_weekly || '[]')}
                                expand={expand}
                                selected={currentSelected}
                                form={form}
                                reset={reset}
                                goDetail={(type: number) => detail(radioDetail, 1)}
                                radioDetail={radioDetail}
                                folderPath={folderPath || initPath}
                                loading={loading}
                              />
                          }
                        </div>
                      </div>
                    )}
                    {/* 主体内容 */}
                    <div className={`content_box ${recycleBin ? 'recyclebin_box' : ''}`}>
                      {/* 操作按钮内容 */}
                      {
                        // 我的录播不按课表分类时进行展示
                        ((!mobileFlag && (!myVideo || !classifiedByschedule) && target !== 'custom') || (myVideo && breadCrumb[0]?.name === '我的录播' && breadCrumb.length > 1)) &&
                        <div className="box_top">
                          <div className="top_left">
                            {(!recycleBin && !myCollection && !myVideo && !departmentVideo && !myShared && !publishManagement) && <UploadButton
                              targetFolder={folderPath || initPath}
                              disabled={(() => {
                                //完全采用后端给的判断标识 2022-04-22
                                if (breadCrumb[breadCrumb.length - 1]?.isUpload || showVideoResouce === 'true') {
                                  return false
                                } else {
                                  return true
                                }
                              })()}
                            />}
                            {
                              <div className='myshareDom'>
                                {
                                  share_btns.map((item: any, index: number) => {
                                    return <div
                                      className={`${item.className}${item.disabled ? ' disabled' : ''}`}
                                      onClick={() => {
                                        !item.disabled && item.func();
                                      }}
                                    >
                                      {item.dom}
                                      {item.title}
                                    </div>
                                  })
                                }
                              </div>
                            }
                            {linkComponents}
                          </div>
                          {parameterConfig.target_customer as string === 'cau' && myVideo && <Button onClick={() => setReservationModalVisible(true)}>{intl.formatMessage({ id: '预约入库' })}</Button>}
                        </div>
                      }
                      {/* 列表主体内容 */}
                      <div className={`box_bottom${['共享资源', '个人资源', '群组资源'].includes(currentSelected) ? '' : ' box_bottom_other'}${myCollection ? ' myCollection' : ''} 
                        ${(myVideo && breadCrumb[0]?.name === '我的录播' && breadCrumb.length > 1) ? 'hasTop_box_bottom' : ''}`}>

                        {/* title内容 */}
                        <div className='opera_div'>
                          <div className='_left'>
                            {/* 我的录播不按课表分类时进行展示 */}
                            {((breadCrumb[0]?.name !== '我的录播' && myVideo && breadCrumb.length > 1) || (!myVideo || !classifiedByschedule)) && target !== 'custom' && <Checkbox
                              indeterminate={indeterminate}
                              onChange={onCheckAllChange}
                              checked={checkAll}
                            >
                              {intl.formatMessage({
                                id: 'all',
                                defaultMessage: '全部',
                              })}
                            </Checkbox>}

                            {/* 我的录播按课表分类时进行展示 */}
                            {((breadCrumb[0]?.name === '我的录播' && myVideo && breadCrumb.length > 1 && classifiedByschedule)) && target !== 'custom' && <Checkbox
                              // indeterminate={indeterminate}
                              onChange={myVideoCheckAllChange}
                              indeterminate={!allList.every(item => item.allVideosSelected) && mergedVideoResponses.filter(item => item.isSelected).length > 0}
                              checked={allList.every(item => item.allVideosSelected) && allList.length > 0}
                            >
                              {intl.formatMessage({ id: 'all', defaultMessage: '全部' })}
                            </Checkbox>}
                            {!mobileFlag ? (!recycleBin && !myCollection && !departmentVideo && !publishManagement && (
                              <Breadcrumb separator={'>'}>
                                {
                                  breadCrumb.map((item: any, index) => {
                                    let pathArr: any = []
                                    if(item?.showPaths?.length > 0) {
                                      pathArr = item?.showPaths

                                    } else {
                                      pathArr = [{value: item?.name, key: item?.path}]
                                    }
                                    return <Breadcrumb.Item key={index} >
                                      <a >
                                        {
                                          pathArr?.map((ele: any, index: number) => {
                                            return <span onClick={() => goBreadcrumbPc(item, ele)} 
                                            style={{cursor: ele.value == '公共资源' || ele.key == folderPath ? '' : 'pointer'}}
                                            key={index}>{ele.value + (index + 1 == pathArr.length ? '' : ' > ')}</span> 
                                          })
                                        }
                                      </a>
                                    </Breadcrumb.Item>
                                  })
                                }
                              </Breadcrumb>
                            )) :
                              myShared ?
                                <>
                                  <div className='mobile_btns_myshare'>
                                    {
                                      share_btns.map((item: any, index: number) => {
                                        return <Button key={index}
                                          className={item.disabled ? 'disabled' : ''}
                                          onClick={() => {
                                            if (!item.disabled) {
                                              setOpreatMenuVisible(false);
                                              item.func();
                                            }
                                          }}>
                                          {item.dom}
                                          {item.title}
                                        </Button>
                                      })
                                    }
                                  </div>
                                  {
                                    mobileFlag && shareInnerFlag &&
                                    <div className='shareinfo'>
                                      <span>{`${intl.formatMessage({ id: '分享时间' })}${timeTransfer(shareInnerFlag.shareTime)}`}</span>
                                      <span>{`${intl.formatMessage({ id: '过期时间' })}：${shareInnerFlag.expirationTime}`}</span>
                                    </div>
                                  }
                                </>
                                :
                                <>
                                  {(!recycleBin && !myCollection && !myVideo && !departmentVideo && !myShared && !publishManagement) && <UploadButton
                                    targetFolder={folderPath || initPath}
                                    disabled={!breadCrumb[breadCrumb.length - 1]?.isUpload}
                                  />}
                                  {linkComponents}
                                </>
                            }
                          </div>
                          {/* 我的录播不按课表分类时进行展示 */}
                          {(!myVideo || !classifiedByschedule) && <div className="_right">
                            {target !== 'custom' && <div>
                              {!recycleBin && !shareInnerFlag && (
                                <Select
                                  value={collation}
                                  style={{ width: 120 }}
                                  onChange={handleChange}
                                >
                                  {(myShared ? sort_myshare :
                                    (['共享资源', '群组资源'].includes(currentSelected) ?
                                      sort_public : sort)).map((item, index) => (
                                        <Option value={item.label} key={index}>
                                          {item.value}
                                          {item.icon}
                                        </Option>
                                      ))}
                                </Select>
                              )}
                              {
                                recycleBin && <Select
                                  value={collation}
                                  style={{ width: 120 }}
                                  onChange={handleChange}
                                >
                                  {sort_recycleBin.map((item, index) => (
                                    <Option value={item.label} key={index}>
                                      {item.value}
                                      {item.icon}
                                    </Option>
                                  ))}
                                </Select>
                              }
                            </div>}
                            <div
                              onClick={() => setModeSwitch(true)}
                              className="mode_switch"
                            >
                              <Tooltip title={intl.formatMessage({ id: '图例模式' })}>
                                <IconFont
                                  type="iconhebingxingzhuangfuzhi2"
                                  className={modeSwitch ? 'active' : ''}
                                />
                              </Tooltip>
                            </div>
                            <div
                              onClick={() => setModeSwitch(false)}
                              className="mode_switch"
                            >
                              <Tooltip title={intl.formatMessage({ id: '列表模式' })}>
                                <IconFont
                                  type="iconliebiao"
                                  className={modeSwitch ? '' : 'active'}
                                />
                              </Tooltip>
                            </div>
                          </div>}
                        </div>

                        {/* 手机端内容 */}
                        {mobileFlag && <div className='mobile_left'>
                          <Breadcrumb separator={'>'}>
                            {
                              (recycleBin || myCollection || myVideo || departmentVideo) ?
                                <Breadcrumb.Item key={-1} className='root_'>
                                  <a onClick={() => setCatalogueVisible(true)}>{intl.formatMessage({ id: '返回目录' })}</a>
                                </Breadcrumb.Item> :
                                <>
                                  {
                                    !shareInnerFlag ?
                                      <Breadcrumb.Item key={-1} className='root_'>
                                        <a onClick={() => setCatalogueVisible(true)}>{intl.formatMessage({ id: '返回目录' })}</a>
                                      </Breadcrumb.Item> :
                                      <Breadcrumb.Item key={0} >
                                        <a onClick={goback}>{`${intl.formatMessage({ id: '返回' })}${sharedActiveKey == '1' ? `${intl.formatMessage({ id: '分享给我的' })}` : `${intl.formatMessage({ id: '我分享的' })}`}`}</a>
                                      </Breadcrumb.Item>
                                  }
                                  {
                                    mobileFlag ?
                                      //取最后两个 回退到上一级 跟当前目录
                                      breadCrumb.slice(-2).length == 1 ? //只有一级了
                                        <Breadcrumb.Item>
                                          <a onClick={() => goBreadcrumb(breadCrumb.slice(-2)[0])}>{breadCrumb.slice(-2)[0]?.name}</a>
                                        </Breadcrumb.Item> :
                                        (!myShared || shareInnerFlag) && <>
                                          <Breadcrumb.Item>
                                            <a onClick={() => goBreadcrumb(breadCrumb.slice(-2)[0])}>{intl.formatMessage({ id: '上一级' })}</a>
                                          </Breadcrumb.Item>
                                          <Breadcrumb.Item >
                                            <a onClick={() => goBreadcrumb(breadCrumb.slice(-2)[1])}>{breadCrumb.slice(-2)[1]?.name}</a>
                                          </Breadcrumb.Item>
                                        </>
                                      : breadCrumb.map((item, index) => (
                                        <Breadcrumb.Item key={index} >
                                          <a onClick={() => goBreadcrumb(item)}>{item?.name}</a>
                                        </Breadcrumb.Item>
                                      ))
                                  }
                                </>
                            }
                          </Breadcrumb>
                        </div>}

                        {/* 列表部分*/}
                        <div className={
                          (recycleBin || myCollection) ? "contentitem_box_nosearch"
                            : (myVideo ? "contentitem_box_video" :
                              departmentVideo ? "contentitem_box_video" :
                                myShared ? 'contentitem_shared_nosearch' :
                                  (allList.length === 0 && getFolder === null ? "contentitem_box_empty" : "contentitem_box"))
                        } style={!modeSwitch ? { minWidth: 900 } : {}}>

                          {/* 列表模式的列头 */}
                          {modeSwitch ? '' : (
                            <ListTop myVideo={myVideo} columns={columnSelect} classifiedByschedule={classifiedByschedule}> 
                              {(!myShared) && (!myVideo || !classifiedByschedule) &&
                                <ColumnSelect
                                  issearch={issearch}
                                  // pubOrPri={!!(breadCrumb[0]?.path.includes('public'))}
                                  onChange={onColumnChange}
                                  myShare={myShared}
                                  columns={columnSelect}
                                  breadCrumb={breadCrumb}
                                  noteClassifiedByschedule={myVideo && !classifiedByschedule}
                                />}
                            </ListTop>
                          )}
                          {allList.length === 0 && getFolder === null ? <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} /> : ''}

                          {/* 列表内容 */}
                          {
                            target === 'custom' ? <Radio.Group
                              onChange={handleClick}
                              value={radioDetail}
                              className={`${modeSwitch ? (allList.length === 0 && getFolder === null ? 'height1_empty' : (myCollection || recycleBin) ? 'height3' :
                                (myShared ? 'height3_shared' : 'height1'))
                                : (allList.length === 0 && getFolder === null ? 'height1_empty' : (myCollection || recycleBin) ? 'height3_list' :
                                  (myShared ? 'height3_shared_list' : 'height2'))} height`}
                              style={{ width: '100%' }}>
                              {allList.map((item, index) => (
                                <ContentItem
                                  key={item.uniqueCode ? item.uniqueCode : item.contentId_} //针对我的分享里面会存在重复文件情况
                                  columns={columnSelect}
                                  modal={modeSwitch}
                                  detail={item}
                                  searchVoiceWord={searchVoiceWord}
                                  goDetail={(type: number) => detail(item, type)}
                                  refresh={refresh as any}
                                  setallrename={item => setRenameShow(item)}
                                  downloadBoxOpen={() => setDownloadModalVisible(true)}
                                  recycleBin={recycleBin}
                                  selected={currentSelected}
                                  myCollectBin={myCollection}
                                  myShare={myShared ? sharedActiveKey === '1' ? 1 : 2 : 0}
                                  downEnable={myShared ? (sharedActiveKey === '1' ? (!shareInnerFlag?.onlyRead ? 1 : 2) : 1) : 0}
                                  shareEnble={(breadCrumb[0]?.path.includes('共享资源') || (breadCrumb[0]?.path.includes('录播资源') && item.type_ !== 'folder') || breadCrumb[0]?.path.includes('个人资源') || myVideo || myGroup)}
                                  resourceGroup={myGroup}
                                  analysisCallback={(current: any) => analysisCallback(current)}
                                  getFolderObj={(msg: any) => setDownloadFolderObj(msg)}
                                  setDownloadFolderVisible={setDownloadFolderVisible}    
                                  classifiedByschedule={classifiedByschedule}                            
                                  setCheckedList={setCheckedList}
                              />
                              ))}
                            </Radio.Group> :
                            // 我的录播且按课表分类时进行展示为true
                              (myVideo && classifiedByschedule) ? <div className={`${modeSwitch ? (allList.length === 0 && getFolder === null ? 'height1_empty' : (myCollection || recycleBin) ? 'height3' :
                                (myShared ? 'height3_shared' : 'height1')) : (allList.length === 0 && getFolder === null ? 'height1_empty' : (myCollection || recycleBin) ? 'height3_list' :
                                  (myShared ? 'height3_shared_list' : 'height2'))} height`} style={{ width: '100%' }}>
                                {
                                  allList.map((item, index) => (
                                    <ContentItem
                                      key={item.uniqueCode ? item.uniqueCode : item.contentId_} //针对我的分享里面会存在重复文件情况
                                      columns={columnSelect}
                                      modal={modeSwitch}
                                      detail={item}
                                      searchMyVideoDetail={searchMyVideoDetail}
                                      index={index}
                                      setAllList={setAllList}
                                      allList={allList}
                                      myVideoDetailFilter={myVideoDetailFilter}
                                      searchVoiceWord={searchVoiceWord}
                                      goDetail={(type: number, item_: any) => detail(item, type, item_)}
                                      refresh={refresh as any}
                                      setallrename={item => setRenameShow(item)}
                                      downloadBoxOpen={() => setDownloadModalVisible(true)}
                                      recycleBin={recycleBin}
                                      selected={currentSelected}
                                      myCollectBin={myCollection}
                                      myVideo={myVideo}
                                      myShare={myShared ? sharedActiveKey === '1' ? 1 : 2 : 0}
                                      downEnable={myShared ? (sharedActiveKey === '1' ? (!shareInnerFlag?.onlyRead ? 1 : 2) : 1) : 0}
                                      shareEnble={(breadCrumb[0]?.path.includes('共享资源') || (breadCrumb[0]?.path.includes('录播资源') && item.type_ !== 'folder') || breadCrumb[0]?.path.includes('个人资源') || myVideo || departmentVideo || myGroup)}
                                      resourceGroup={myGroup}
                                      analysisCallback={(current: any) => analysisCallback(current)}
                                      getFolderObj={(msg: any) => setDownloadFolderObj(msg)}
                                      setDownloadFolderVisible={setDownloadFolderVisible}      
                                      classifiedByschedule={classifiedByschedule}                              
                                      setCheckedList={setCheckedList}
                            />
                                  ))
                                }
                              </div> : <CheckboxGroup
                                value={checkedList}
                                onChange={onChange}
                                // className={`${modeSwitch ? (allList.length === 0 && getFolder === null ?'height1_empty':'height1') : 'height2'} height`}
                                // modeSwitch 为true时是图例模式
                                className={`${modeSwitch ? (allList.length === 0 && getFolder === null ? 'height1_empty' : (myCollection || recycleBin) ? 'height3' :
                                  (myShared ? 'height3_shared' : 'height1'))
                                  : (allList.length === 0 && getFolder === null ? 'height1_empty' : (myCollection || recycleBin) ? 'height3_list' :
                                    (myShared ? 'height3_shared_list' : 'height2'))} height`}
                                style={{ width: '100%' }}
                              >
                                {getFolder === null ? '' : <NewFolder
                                  modal={modeSwitch}
                                  detail={getFolder}
                                  resourceGroup={myGroup}
                                  refresh={refresh}
                                  setallrename={item => setRenameShow(item)}
                                  privateorpublic={
                                    breadCrumb.length > 0 &&
                                    (breadCrumb[0]?.path.includes('共享资源') || breadCrumb[0]?.path.includes('录播资源'))
                                  }
                                />
                                }
                                {allList.map((item, index) => (
                                  <ContentItem
                                    key={item.uniqueCode ? item.uniqueCode : item.contentId_} //针对我的分享里面会存在重复文件情况
                                    columns={columnSelect}
                                    modal={modeSwitch}
                                    detail={item}
                                    index={index}
                                    setAllList={setAllList}
                                    allList={allList}
                                    myVideoDetailFilter={myVideoDetailFilter}
                                    searchVoiceWord={searchVoiceWord}
                                    goDetail={(type: number, item_: any) => detail(item, type, item_)}
                                    refresh={refresh as any}
                                    setallrename={item => setRenameShow(item)}
                                    downloadBoxOpen={() => setDownloadModalVisible(true)}
                                    recycleBin={recycleBin}
                                    selected={currentSelected}
                                    myCollectBin={myCollection}
                                    myVideo={myVideo}
                                    myShare={myShared ? sharedActiveKey === '1' ? 1 : 2 : 0}
                                    downEnable={myShared ? (sharedActiveKey === '1' ? (!shareInnerFlag?.onlyRead ? 1 : 2) : 1) : 0}
                                    shareEnble={(breadCrumb[0]?.path.includes('共享资源') || (breadCrumb[0]?.path.includes('录播资源') && item.type_ !== 'folder') || breadCrumb[0]?.path.includes('个人资源') || myVideo || departmentVideo || myGroup)}
                                    resourceGroup={myGroup}
                                    analysisCallback={(current: any) => analysisCallback(current)}
                                    setPublishVisible={setPublishVisible} // 发布资源
                                    getDetail={(detailItem: any) => { setCurrentPublish([detailItem]) }} // 得到当前发布的信息
                                    getFolderObj={(msg: any) => setDownloadFolderObj(msg)}
                                    setDownloadFolderVisible={setDownloadFolderVisible}     
                                    classifiedByschedule={classifiedByschedule}

                                    setCheckedList={setCheckedList}
                              />
                                ))}
                              </CheckboxGroup>
                          }
                        </div>
                      </div>
                      {/* 分页 */}
                      <div className="pagination">
                        <Pagination
                          current={current}
                          pageSize={pageSize}
                          total={totalPage}
                          size="small"
                          showQuickJumper
                          onChange={changepage}
                          showTotal={total => intl.formatMessage({
                            id: "共条"
                          }, { total })}
                          showSizeChanger={true}
                          pageSizeOptions={['30', '40', '50', '100']}
                        // showTotal={total => intl.formatMessage({
                        //   id: "page-total",
                        //   defaultMessage: `共 ${total} 页`
                        // })}
                        />
                      </div>
                    </div>
                  </div>
                </>
          }
        </div>
      </div>

      <UploadTask refresh={refresh} />
      {/* 个人资源下不展示上传按钮 */}
      {/* {permissions.includes(perCfg.resource_create) && ()} */}
      <UploadBox
        refresh={refresh}
        // onCancel={() => setUploadBoxVisible(false)}
        // uploadFiles={uploadFile}
        targetFolder={folderPath || initPath}
        breadCrumb={breadCrumb}
        folderName={breadCrumb[breadCrumb.length - 1]?.name}
      />

      <DownloadModal
        modalVisible={downloadModalVisible}
        modalClose={() => setDownloadModalVisible(false)}
        downloadlist={downlist}
      />
      <Modal
        destroyOnClose={true}
        title={intl.formatMessage({ id: '移除' })}
        open={removeVisible}
        footer={[
          <Button key="back" onClick={() => setRemoveVisible(false)}>
            {intl.formatMessage({ id: '取消' })}
          </Button>,
          <Button key="submit" type="primary" onClick={removeShared}>
            {intl.formatMessage({ id: '确定' })}
          </Button>
        ]}
      >
        <span>{intl.formatMessage({ id: '确定要移除所选吗' })}</span>
      </Modal>
      <Modal
        destroyOnClose={true}
        title={intl.formatMessage({ id: '取消分享' })}
        open={cancelshareVisible}
        footer={[
          <Button key="back" onClick={() => setCancelshareVisible(false)}>
            {intl.formatMessage({ id: '取消' })}
          </Button>,
          <Button key="submit" type="primary" onClick={batchCancleShared}>
            {intl.formatMessage({ id: '确定' })}
          </Button>
        ]}
      >
        <span>{intl.formatMessage({ id: '确定要取消分享吗' })}</span>
      </Modal>
      <Loading />
      <Modal
        className='applyStorageModal'
        destroyOnClose={true}
        open={applyStorageVisible}
        onCancel={applyClosed}
        title={intl.formatMessage({ id: '扩容申请' })}
        footer={[
          applyStorageObj ?
            applyStorageObj.auditStatus === 0 ? <Button type='primary' onClick={userApplyCancle}>{intl.formatMessage({ id: '取消申请' })}</Button> : ''
            : <Button type='primary' onClick={submitApply}>{intl.formatMessage({ id: '提交申请' })}</Button>
        ]}
      >
        <div>
          <label>{intl.formatMessage({ id: '申请增加空间' })}</label>
          <Input type='number' placeholder={intl.formatMessage({ id: '最大不能超过50' })} onChange={(e) => sizeChange(e, false)} max={50} min={1} defaultValue={applyStorageObj?.applySpace} disabled={applyStorageObj} />
          <span>&nbsp;G</span>
        </div>
        <div className='applyReason'>
          <label>{intl.formatMessage({ id: '申请理由' })}</label>
          <TextArea rows={4} onChange={(e) => sizeChange(e, true)} defaultValue={applyStorageObj?.applyReason} disabled={applyStorageObj}></TextArea>
        </div>
        <div className={applyStorageObj ? applyStorageObj.auditStatus === 2 ? 'failResult' : 'result' : ''}>{
          applyStorageObj ?
            applyStorageObj.auditStatus === 0 ? <span className='audit'>{`${intl.formatMessage({ id: '资源管理员审核中' })}...`}</span> :
              applyStorageObj.auditStatus === 1 ? <span className='pass'>{intl.formatMessage({ id: '审核通过！已完成扩容' })}</span> :
                <>
                  <label>{intl.formatMessage({ id: '拒绝理由' })}</label>
                  <span className='fail'>{applyStorageObj.auditExplanation}</span>
                </>
            : ''
        }
        </div>
      </Modal>
      <IntelligentAnalysisModal
        modalVisible={intelligentAnalysisModalVisible}
        modalClose={() => setIntelligentAnalysisModalVisible(false)}
        analysislist={analysisCurrent}
        pubOrPri={false}
        voiceContent={analysisCurrent.asr_status !== '2'}
      />
      <ReservationModal
        modalVisible={reservationModalVisible}
        modalClose={() => setReservationModalVisible(false)}
      />
      {publishVisible && <PublishRescourcemodal
        currentPublish={currentPublish}
        visible={publishVisible}
        onCancel={() => setPublishVisible(false)}
        setCheckedList={setCheckedList}
      />}
      {
        downloadFolderVisible && <DownloadFolderModal
          modalVisible={downloadFolderVisible}
          setModalVisible={() => {setDownloadFolderVisible(false); setDownloadFolderObj({})}}
          folderObj={downloadFolderObj}
        />
      }

      {publishVisible && <PublishRescourcemodal
        currentPublish={currentPublish}
        visible={publishVisible}
        onCancel={() => setPublishVisible(false)}
      />}
    </div>
  );
};
export default ContentList;
