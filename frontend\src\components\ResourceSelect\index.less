.resource-select {
}
.resource-select-content {
  display: flex;

  .directory {
    width: 200px;
    // height: 680px;
    height: 65vh;
    overflow-y: auto;

    font-size: 14px !important;
    border: 1px solid #f0f0f0;

    .ant-tree .ant-tree-treenode {
      padding: 10px 0 13px 0;
    }

    .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .ant-tree-indent-unit {
      width: 12px;
    }
  }

  .content {
    flex: 1;
    margin-left: 20px;
    &> div:nth-child(2){
      margin-left: 0 !important;
    }

    .search-group {
      display: flex;
      height: 32px;
      margin-right: 32px;
      margin-bottom: 10px;

      .ant-picker {
        width: 70%;
        margin: 0 20px;
      }

      .mode-switch-wrapper {
        display: flex;
        margin-left: 24px;
        color: #919596;
        font-size: 16px;

        .mode-item {
          line-height: 32px;
          cursor: pointer;

          &:hover,
          &.active {
            color: var(--ant-primary-color);
          }
        }
      }
    }

    .breadcrumb {
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      width: 100%;
      height: 40px;
      padding-top: 10px;
      padding-right: 30px;
      padding-left: 10px;
      .ant-breadcrumb a {
        // color:rgba(0, 0, 0, 0.85)
        color: var(--ant-primary-color);
      }
      // .ant-breadcrumb a:hover{
      //   color:#eb766c
      // }
      .ant-breadcrumb > span:last-child a {
        color: rgba(0, 0, 0, 0.85);
      }
    }
    .pagination {
      height: 42px;
      padding-top: 10px;
    }

    .list {
      display: flex;
      // align-items: center;
      flex-wrap: wrap;
      justify-content: flex-start;
      width: 100%;
      height: calc(65vh - 32px - 40px - 42px - 20px);
      margin-top: 20px;
      overflow: auto;

      .list-item {
        width: 240px;
        height: 168px;
        margin-right: 15px;
        margin-bottom: 30px;
        padding: 6px;
        border: 1px solid rgba(221, 221, 221, 1);
        cursor: pointer;

        .img-box {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 127px;
          overflow: hidden;

          > .ant-skeleton-element {
            width: 100%;
            height: 100%;
          }

          > .ant-skeleton-element {
            .ant-skeleton-image {
              width: 100%;
              height: 100%;
            }
          }

          > img {
            max-width: 100%;
          }

          .img-title {
            position: absolute;
            bottom: 0;
            left: 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 26px;
            color: #fff;
            text-align: center;
            background: rgba(0, 0, 0, 1);
            opacity: 0.4;

            > p {
              margin: 0;
            }

            > p:first-child {
              width: calc(100% - 54px);
            }

            > p:last-child {
              width: 54px;
              height: 100%;
              line-height: 26px;
              background-color: #b63524;
            }
          }
        }

        .item-title {
          display: flex;
          align-items: center;
          padding: 5px 5px 0;
          font-size: 14px;

          .ant-checkbox-wrapper,
          .ant-radio-wrapper {
            width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }

      .list-item:nth-child(3n) {
        // margin-right: 0;
      }
    }
    .res-title {
      cursor: pointer;
    }

    .cover-img {
      max-width: 50px;
      max-height: 30px;
      margin-right: 6px;
    }

    .res-table {
      margin-top: 24px;
      font-size: 14px;
    }

    .ant-pagination {
      text-align: center;
    }
  }
}
@media screen and (min-width: 1400px) {
  .resourceModal {
    width: 1100px !important;
  }
}
@media screen and (max-width: 1400px) {
  .resourceModal {
    width: 800px !important;
  }
}
