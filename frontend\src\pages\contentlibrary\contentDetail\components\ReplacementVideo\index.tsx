import React, { useEffect, useRef, useState } from "react";
import { Form, Upload, message, Spin } from 'antd';
import uploadApis from '@/service/uploadApis'
import {  asyncLoadScript } from '@/utils';
import './index.less'
import WebUploader from '@/components/UploadBox/WebUploader';
import { useDispatch, useSelector, IUpload, useIntl } from 'umi';
import { LoadingOutlined } from '@ant-design/icons';
interface WebUploaderOptions {
    uploadError: (file: any) => void;
    uploadComplete: (file: any, uploader: any) => void;
    uploadProgress: (file: any, progress: number) => void;
}

interface WebUploader {
    new(options: WebUploaderOptions): any;
    addFiles: (file: any) => void;
}

interface ReplacementVideoProps {
    oldRecord: any
    contentId:string
}

// 确保已引入 WebUploader 库
declare global {
    interface Window {
        WebUploader: any;
    }
}

const ReplacementVideo = (props: ReplacementVideoProps) => {
    const { oldRecord, contentId } = props
    const dispatch = useDispatch();
    const intl = useIntl();
    const fileRef = useRef<any>({})
    const stateRef = useRef<any>()
    const { taskPanls } = useSelector<{ upload: IUpload }, IUpload>(({ upload }) => upload);
    const [fileName, setFileName] = useState<any>('')
    const [loading, setLoading] = useState(false)

    useEffect(() => {
        asyncLoadScript('/rman/libs/webuploader/webuploader.js');
    }, []);
    useEffect(() => {
        stateRef.current = taskPanls
    }, [taskPanls])

    // 限制上传文件类型和数量
    const beforeUpload = (file: any) => {
        const isVideo = file.type.startsWith('video/');
        if (!isVideo) {
            message.error('仅支持上传视频文件（MP4、WEBM 等）');
            return Upload.LIST_IGNORE; // 阻止上传
        }
        return true;
    };

    // 合并分片请求
    const uploadComplete = (file: any, newWebUploader: any) => {
        uploadApis.filemerge(file.source.guid, file.name, file.source.fileGuid).then(res => {
            if (res?.success) {
                pollMergeStatus(file, newWebUploader);
            } else {
                uploadError(file);
            }
        });
    };

    const uploadError = (file: any) => {
        dispatch({
            type: 'upload/errorTask',
            payload: {
                value: file.source.guid,
            },
        });
        message.error(
            file.name +
            intl.formatMessage({
                id: 'upload-error',
                defaultMessage: '上传失败',
            }),
        );
    };

    const pollMergeStatus = async (file: any, newWebUploader: any) => {
        const res = await uploadApis.fetchMergeStatus(file.source.guid);
        if (res?.data?.state === 1 && res.data.finalFilePath) {
            //    最终调用的上传接口
            const { course_name, course_number, serial_number, course_week, course_begin, course_end, section, campus,
                academic_building, classroom_number, seat, semester, college, major, teacher, schedule_id } = oldRecord
            let params = {
                oldContentId: contentId || '',
                fileLength: fileRef.current.size,
                filePath: res.data.finalFilePath,
                repairRecordVideo: {
                    courseName: course_name,
                    courseNumber: course_number,
                    serialNumber: serial_number,
                    courseWeek: course_week,
                    courseBegin: course_begin,
                    courseEnd: course_end,
                    section: section,
                    campus: campus,
                    academicBuilding: academic_building,
                    classroomNumber: classroom_number,
                    seat: seat,
                    semester: semester,
                    college: college,
                    major: major,
                    teacher: teacher,
                    scheduleId: schedule_id
                }
            }
            uploadApis.getReplacementVideo(params).then((rescult: any) => {
                if (rescult?.success) {
                    setLoading(false)
                    message.success('替换成功')
                    setFileName(fileRef.current.name)
                } else {
                    console.log(rescult?.message)
                    setLoading(false)
                }

            })
        } else if (res?.data?.state === 0) {
            // 手动移除掉的任务 停止轮询
            const realTaskPanls = stateRef.current;
            if (realTaskPanls.some((item: any) => item.guid === file.source.guid)) {
                setTimeout(() => {
                    pollMergeStatus(file, newWebUploader);
                }, 500);
            }
        } else if (res?.data?.state === -1 && res?.data.errorMsg) {
            message.error(res?.data.errorMsg);
            uploadError(file);
        } else {
            uploadError(file);
        }
    };

    const uploadProgress = (file: any, progress: number) => {
        const p = progress >= 1 ? 0.999 : progress;
        dispatch({
            type: 'upload/updateTaskPanls',
            payload: {
                value: { guid: file.source.guid, progress: p },
            },
        });
    };

    // 处理文件上传状态变化
    const handleChange = async (info: any) => {
        const file = info.file;
        fileRef.current = file
        setLoading(true)
        // 获取path
        const param = [
            {
                fileName: file.name,
                fileLength: file.size,
                poolType: window.localStorage.getItem('upform_platform') === 'Lark' ? 'ROLE' : '',
                fileType: "video",
                pathType: 0
            }
        ]
        let path = ''
        await uploadApis.storageConfig(param).then((res: any) => {
            if (res?.success && res?.data) {
                path = res?.data[0]?.path
            }
        })

        // 初始化 WebUploader
        if (!(window as any).WebUploader) {
            try {// 尝试加载WebUploader
                await asyncLoadScript('/rman/libs/webuploader/webuploader.js');
                if (!(window as any).WebUploader) {
                    message.error('WebUploader 加载失败,请刷新页面重试');
                    return;
                }
            } catch (err) {
                message.error('WebUploader 加载失败,请检查网络连接');
                return;
            }
        }

        const fileTemp = new (window as any).WebUploader.Lib.File(
            (window as any).WebUploader.Base.guid(),
            file.originFileObj || file //兼容拖拽有originFileObj的bug
        );
        fileTemp.guid = (window as any).WebUploader.Base.guid();
        fileTemp.fileGuid = path;//针对同名文件增加不同标识
        let webUploader = new (WebUploader as unknown as WebUploader)({
            uploadError: (file: any) => uploadError(file),
            uploadComplete: (file: any, uploader: any) => uploadComplete(file, uploader),
            uploadProgress: (file: any, progress: number) => uploadProgress(file, progress),
        })
        // 确保fileTemp有必要的属性以触发上传回调
        fileTemp.source = file.originFileObj;
        fileTemp.name = file.name;
        fileTemp.size = file.size;

        webUploader.addFiles(fileTemp);
        const files = webUploader.getFiles();

        const newTaskPanls: any = [];
        files.forEach((item: any) => {
            if (newTaskPanls.filter((i: any) => i.guid === item.source.guid).length === 0) {
                newTaskPanls.push({
                    uploader: webUploader,
                    name: item.name,
                    size: item.size,
                    status: 0,
                    progress: 0,
                    index: 0,
                    guid: item.source.guid,
                    uploading: false,
                    pause: false,
                    selectedItems: item.selectedItems
                });
            }
        });
        dispatch({
            type: 'upload/setTaskPanls',
            payload: {
                value: newTaskPanls,
            },
        });
    };

    const uploadDefault = () => {
        return true
    }

    return <Form.Item
        label="录播视频替换"
        name="replacementVideo"
        className="replacementVideo"
    >
        <div className="uploadName" title={fileName}>
            {loading && <Spin indicator={<LoadingOutlined spin />} size="small" />}
            {!loading && fileName  && <span>{fileName}</span>}
        </div>
        <Upload
            fileList={[]}
            showUploadList={false}
            beforeUpload={beforeUpload}
            onChange={handleChange}
            customRequest={uploadDefault}
            accept="video/*" // 限制文件类型为视频
        >
            <div className="uploadFile">上传文件</div>
        </Upload>
    </Form.Item>
}

export default ReplacementVideo


