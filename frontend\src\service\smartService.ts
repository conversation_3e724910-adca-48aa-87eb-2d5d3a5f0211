import http from '../http/http';
import SmartTypes from '@/types/smartTypes';

namespace SmartService {
  // 发送知识点分析流程
  export const sendpointsFlow = (data: string[]) =>
    http(`/entity/knowledge/point/analysis`, {
      method: 'POST',
      data: data,
    });
  // 发送智能分析流程
  export const sendFlow = (data: any) =>
    http<SmartTypes.LyricsRes>(`/intelligent/sendflow2`, {
      method: 'POST',
      body: JSON.stringify(data),
      headers: {
        'Content-Type': 'application/json',
      },
    });
  // 发送智能标签流程
  export const sendFlow2 = (data: any) =>
    http<SmartTypes.LyricsRes>(`/intelligent/sendflow2`, {
      method: 'POST',
      body: JSON.stringify(data),
      headers: {
        'Content-Type': 'application/json',
      },
    });
  // 发送分析（整合所有）
  export const sendFlow_new = (data: any) =>
    http<SmartTypes.LyricsRes>(`/rman/v1/intelligent/smart/flow`, {
      method: 'POST',
      body: JSON.stringify(data),
      headers: {
        'Content-Type': 'application/json',
      },
    });
  /**
   * 获取字幕
   * @param contentid
   */
  export const querySubtitle = (contentid: string, flag?: string) =>
    http<SmartTypes.LyricsRes>(`/smart/subtitles/select${flag}`, {
      method: 'POST',
      body: JSON.stringify({
        contentid,
        metadataType: 'model_sobey_smart_subtitles_',
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    });
  /**
   * 获取关键词
   * @param contentid
   */
  export const queryKeywords = (contentid: string, flag?: string) =>
    http<SmartTypes.LyricsRes>(`/entity/select/keyword/${contentid}${flag}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
  /**
   * 获取关键词(新接口)
   * @param contentid
   */
  export const queryKeywordNew = (contentid: string, flag?: boolean) =>
    http<SmartTypes.LyricsRes>(`/intelligent/select/keywords`, {
      method: 'GET',
      params: {
        contentId: contentid,
        isSysAuth: flag,
      },
      headers: {
        'Content-Type': 'application/json',
      },
    });
  /**
   * 获取敏感词
   * @param contentid
   */
  export const querySensitiveWords = (contentid: string, flag?: boolean) =>
    http<SmartTypes.LyricsRes>(`/sensitive/select/ocr/group`, {
      method: 'GET',
      params: {
        contentId: contentid,
        isSysAuth: flag,
      },
      headers: {
        'Content-Type': 'application/json',
      },
    });
  /**
   * 获取敏感人物
   * @param contentid
   */
  export const querySensitivePersons = (contentid: string, flag?: boolean) =>
    http<SmartTypes.LyricsRes>(`/sensitive/select/face/group`, {
      method: 'GET',
      params: {
        contentId: contentid,
        isSysAuth: flag,
      },
      headers: {
        'Content-Type': 'application/json',
      },
    });
  /**
   * 获取语音
   * @param contentid
   */
  export const queryVoice = (contentid: string, flag?: string) =>
    http<SmartTypes.LyricsRes>(`/smart/voice/select${flag}`, {
      method: 'POST',
      body: JSON.stringify({
        contentid,
        metadataType: 'model_sobey_smart_voice_',
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    });
  // 部分更新语音
  export const updatepartVoice = (contentid: string, metadata: any) =>
    http(`/smart/voice/part/updatewithfile`, {
      method: 'POST',
      data: {
        contentid,
        metadataType: 'model_sobey_smart_voice_',
        metadata,
      },
      headers: {
        'Content-Type': 'application/json',
      },
    });
  /**
   * 查询片段
   * @param contentid
   */
  export const querySequencemeta = (contentid: string, flag?: string) =>
    http<SmartTypes.SequenceRes>(`/cata/sequencemeta/select${flag}`, {
      method: 'POST',
      body: JSON.stringify({
        contentid,
        metadataType: 'model_sobey_cata_sequencemeta',
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    });
  /**
   * 查询片段 新
   * @param contentid
   */
  export const querySequencemetaNew = (contentid: string, flag?: boolean) =>
    http<SmartTypes.SequenceRes>(`/cata/sequencemeta/select`, {
      method: 'GET',
      params: {
        contentId: contentid,
        isSysAuth: flag,
      },
      headers: {
        'Content-Type': 'application/json',
      },
    });
  /**
   *
   * @param contentid
   * @param metadata
   */
  export const addSequencemeta = (
    contentid: string,
    metadata: SmartTypes.SequenceMeta[],
  ) =>
    http(`/cata/sequencemeta/add?contentId=${contentid}`, {
      method: 'POST',
      body: JSON.stringify([
        {
          type: 'model_sobey_cata_sequencemeta',
          metadata,
        },
      ]),
      headers: {
        'Content-Type': 'application/json',
      },
    });

  /**
   *
   * @param contentid 新
   * @param metadata
   */
  export const addSequencemetaNew = (contentid: string, metadata: any) =>
    http(`/cata/sequencemeta/add/knowledge/point?contentId=${contentid}`, {
      method: 'POST',
      data: metadata,
      headers: {
        'Content-Type': 'application/json',
      },
    });
    export const getRelatedLabels = (labelKey) =>
      http(`/rman/v1/cata/sequencemeta/knowledge/point/relatedLabels?labelKey=${labelKey}`, {
        method: 'POST',
      });
    
  export const modifySection = (frame: boolean, metadata: any) =>
    http('/rman/v1/cata/sequencemeta/part/update', {
      method: 'POST',
      params: { frame },
      body: JSON.stringify(metadata),
      headers: {
        'Content-Type': 'application/json',
      },
    });
  /**
   *
   * @param contentid
   * @param metadata
   */
  export const updateSequencemeta = (
    contentid: string,
    metadata: SmartTypes.SequenceMeta[],
  ) =>
    http(`/cata/sequencemeta/update`, {
      method: 'POST',
      body: JSON.stringify({
        contentid,
        metadataType: 'model_sobey_cata_sequencemeta',
        metadata,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    });
  // 部分更新知识点
  export const updatepartSequencemeta = (contentid: string, metadata: any) =>
    http(`/cata/sequencemeta/part/update`, {
      method: 'POST',
      body: JSON.stringify({
        contentid,
        metadataType: 'model_sobey_cata_sequencemeta',
        metadata,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    });
  export const updatepartSequencemetaNew = (
    frame: boolean,
    contentId: string,
    metadata: any,
  ) =>
    http('/rman/v1/cata/sequencemeta/update/knowledge/point', {
      method: 'POST',
      params: {
        frame,
        contentId,
      },
      data: metadata,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  // 删除
  export const deleteSequencemeta = (
    contentid: string,
    guid: string | undefined,
  ) =>
    http(`/cata/sequencemeta/delete`, {
      method: 'DELETE',
      body: JSON.stringify({
        contentid,
        metadataType: 'model_sobey_cata_sequencemeta',
        guid_: guid,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    });
  // 删除 新
  export const deleteSequencemetaNew = (contentId: string, guids: string[]) =>
    http(`/cata/sequencemeta/delete/knowledge/point`, {
      method: 'DELETE',
      params: {
        contentId,
      },
      data: guids,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  /**
   * 获取播放地址
   * @param contentId
   */
  export const fetchDisplayPath = (contentId: string) =>
    http<SmartTypes.FileEntity>(`/search/auth/display/path`, {
      method: 'POST',
      params: {
        contentId,
      },
    });

  /**
   * 生成成品片段
   */
  export const createProduct = (
    contentId: string,
    data: { saveName: string; inPoint: any; outPoint: any }[],
    params: {
      beginId?: string;
      endId?: string;
      colorImprove?: string;
      noiseReduce?: boolean;
      transitionEffect: string;
      isSubtitle?: boolean;
    },
  ) =>
    http<any>(`/entity/cutsave/${contentId}`, {
      method: 'POST',
      body: JSON.stringify(data),
      params,
    });

  /**
   * 获取成品片段
   */
  export const getFinishProduct = (contentId: string, flag?: string) =>
    http<any>(`/search/relation/cutvideo${flag}`, {
      method: 'GET',
      params: {
        contentId,
        tt: new Date().getTime(),
        isSysAuth: true,
      },
    });

  /**
   * 获取片段拼接目录
   */
  export const getsplicingdirectory = (entityContentId: string) =>
    http<any>('/rman/v1/folder/fragment/splicing/directory', {
      method: 'GET',
      params: {
        entityContentId,
      },
    });

  /**
   * 合成成品片段(没有片头片尾)
   * @param contentId
   * @param data
   * @param params
   */
  export const mergeProduct = (
    contentId: string,
    data: {
      inPoint: any;
      outPoint: any;
    }[],
    params: {
      newName: string;
      colorImprove?: string;
      noiseReduce?: boolean;
      transitionEffect: string;
      isSubtitle?: boolean;
    },
  ) =>
    http<any>(`/entity/mergesave/${contentId}`, {
      method: 'POST',
      body: JSON.stringify(data),
      params: params,
    });
  /**
   * 合成成品片段（包含片头片尾）
   * @param data
   * @param params
   */
  export const mergeProduct2 = (
    data: {
      contentId: string;
      inPoint: any;
      outPoint: any;
    }[],
    params: {
      newName: string;
      colorImprove?: string;
      noiseReduce?: boolean;
      transitionEffect: string;
      isSubtitle?: boolean;
    },
  ) =>
    http<any>(`/entity/muticlip/mergesave`, {
      method: 'POST',
      body: JSON.stringify(data),
      params,
    });

  /**
   * 查询地图节点
   *
   * @param name
   */
  export const fetchMapList = (name?: any, resourceId?: any) =>
    http<any>(`/learn/m1/knowledge/query/my/map`, {
      method: 'GET',
      // params:name?{name:name}:{}
      params: {
        name: name,
        resourceId: resourceId,
      },
    });
  /**
   * 查询当前地图下所有节点
   *
   * @param params
   */
  export const fetchNodeList = (id?: any, name?: string, resourceId?: any) =>
    http<any>(`/learn/m1/knowledge/course/find/knowledge`, {
      method: 'GET',
      params: {
        mapId: id,
        nodeName: name,
        resourceId: resourceId,
      },
    });
  /**
   * 检索节点
   *
   * @param params
   */
  export const fetchSingleNode = (name: string, id: number) =>
    http<any>(`/learn/course/query`, {
      method: 'GET',
      params: {
        name: name,
        nodeId: id,
        type: 2,
      },
    });
  /**
   * 更新节点
   *
   * @param data
   */
  export const updateKnowledge = (data: any) =>
    http<any>(`/learn/m1/knowledge/course/update/nodes/property`, {
      method: 'POST',
      data: data,
    });

  //绑定资源和知识点成功添加操作记录
  export const addlog = (courseMapNane: string, data: any) =>
    http<any>(
      '/rman/v1/operation/log/course/map?courseMapNane=' + courseMapNane,
      {
        method: 'POST',
        data: data,
      },
    );

  /**
   * 获取素材已绑定的知识点
   *
   * @param data
   */
  export const fetchBindKnowledges = (params: any) =>
    http<any>(`/learn/m1/knowledge/query/bind/resource`, {
      method: 'GET',
      params,
    });
  /**
   * 批量解绑知识点
   *
   * @param data
   */
  export const removeKnowledges = (data: any) =>
    http<any>(`/learn/knowledgeVideo/batchRemove`, {
      method: 'POST',
      data: data,
    });

  //字幕文件下载
  export const resourceCaptionDownload = (
    contentId: string,
    entityName: string,
    isRegenerate?: boolean,
  ) => {
    return http(`/rman/v1/smart/voice/file`, {
      method: 'POST',
      params: {
        contentId,
        entityName,
        isRegenerate: isRegenerate || false,
      },
    });
  };
  //字幕文件下载
  export const resourceCaptionTxtDownload = (
    contentId: string,
    asrFileFormat: number,
  ) => {
    return http(`/rman/v1/smart/voice/txt`, {
      method: 'POST',
      responseType: 'blob',
      params: {
        contentId,
        asrFileFormat,
      },
    });
  };
  //字幕文件下载
  export const resourceCaptionWordDownload = (
    contentId: string,
    asrFileFormat: number,
  ) => {
    return http(`/rman/v1/smart/voice/word`, {
      method: 'POST',
      responseType: 'blob',
      params: {
        contentId,
        asrFileFormat
      },
    });
  };
  //语音文本检查
  export const voiceTextCheck = (
    contentId: string,
    voiceTextCheck: boolean,
  ) => {
    return http(`/rman/v1/entity/voice/text/check`, {
      method: 'POST',
      params: {
        contentId,
        voiceTextCheck,
      },
    });
  };
  export const getKnowledgeSummary = (params: any) =>
    http<any>(`/rman/v1/intelligent/select/knowledge/summary`, {
      method: 'GET',
      params,
    });
  
}

export default SmartService;
