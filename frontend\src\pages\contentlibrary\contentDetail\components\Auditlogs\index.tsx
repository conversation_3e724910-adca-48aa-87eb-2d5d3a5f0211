import React ,{FC,useState,useEffect,useRef} from "react";
import './index.less';
import contentListApis from '@/service/contentListApis';
import entityApis from '@/service/entityApis';
import { Drawer,Input,Select,Timeline,message} from 'antd';
import { useHistory, useIntl } from 'umi';

const ContentLogs:FC<any> = ({logsVisible,contentId,onclose}) => {
  const [logsData, setLogsData] = useState<any>([]);
  const scrollableDiv = useRef<any>(null);
  let history: any = useHistory();
  let isPublish = history.location?.query?.isPublish || false;
  let processInstanceId = history.location?.query?.processInstanceId || '';
  const loading = useRef<any>(false);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [logtype,setLogtype] = useState<any>([]);
  const [selectype,setSelectype] = useState<any>(null);
  const intl = useIntl();
  const [keyword,setKeyword] = useState<any>('');
  // 分页
  const [pageinfo, setPageinfo] = useState<any>({
    page:1,
    size:99,
    total:0
  });
  // 获取日志的类型
  const getlogtype = () =>{
      entityApis.getlogtype().then((res:any)=>{
        if(res.success){
          setLogtype(res.data.map((item:any)=>{
            return { value: item, label: item }
          }));
        }else{
          message.error(intl.formatMessage({ id: '获取资源日志类型失败' }));
        }
      })
  }


  useEffect(() => {
    if(logsVisible){
      getlogs(keyword,selectype,1,pageinfo.size);
      // 防止请求多次
      if(logtype.length==0){
        getlogtype();
      }
    }else{
      setLogsData([]);
      setPageinfo({
        page:1,
        size:99,
        total:0
      })
    }
  }, [logsVisible]);

  // 获取资源绑定日志
  const getlogs = async (keyword:string,selectype:any,page:number,size:number)=>{
    let res = [] as any
    if(isPublish){
       res = await contentListApis.getProcessInstanceLogs(processInstanceId)
       if(res.code === 0){
        setLogsData(res.data.map(item =>({
          createTime: item.createTime,
          username: item.assigneeUser?.nickname,
          description: item.reason
        })))
       }
    }
    else{
      res = await entityApis.getAuditLog({
        contentId:contentId,
        page:page,
        size:size
      });
    }
    if(res.errorCode){
      if(page == 1){
        setLogsData(res.extendMessage.results);
      }else{
        setLogsData([...logsData,...res.extendMessage.results]);
      }
      if(res.extendMessage.results.length<pageinfo.size || ( res.extendMessage.results.length + logsData.length == res.extendMessage.recordTotal)){
        setHasMore(false);
      }
      setPageinfo({
        page:res.extendMessage.pageIndex,
        size:res.extendMessage.pageSize,
        total:res.extendMessage.recordTotal
      })
    }
    // 设置加载完毕标识符
    loading.current = false;
  }

  // 切换查询类型
  const handleChange = (value:any) => {
    setSelectype(value);
    getlogs(keyword,value,1,pageinfo.size);
  }

  const handleScroll = (e:any) => {
    let res=scrollableDiv.current.scrollHeight - scrollableDiv.current.clientHeight- scrollableDiv.current.scrollTop;
    console.log(res);
    if (res<=50) {
      // 如果还有数据就去加载更多
      if(hasMore){
        // 防止多次加载
        if(!loading.current){
          loading.current = true;
          getlogs(keyword,selectype,pageinfo.page+1,pageinfo.size);
        }
      }
    }
  }

  return (
  <Drawer title={intl.formatMessage({ id: '资源审核日志' })} width={500} placement="right" onClose={onclose} open={logsVisible} className="resource_audit_logs_drawer">
    {/* <div className="logs_search_box">
       <Input style={{ width: 180 }} allowClear value={keyword} onChange={(e)=>{
        setKeyword(e.target.value)
        if(e.target.value == '' || e.target.value == null){
          getlogs(e.target.value,selectype,1,pageinfo.size);
        }
       }} onPressEnter={(e:any)=>{
        getlogs(e.target.value,selectype,1,pageinfo.size);
       }} placeholder="请输入关键词搜索" />
       <Select allowClear showSearch
          style={{ width: 180 ,marginLeft:20}}
          onChange={handleChange}
          placeholder="请选择操作类型"
          options={logtype}
        />
    </div> */}
    <div ref={scrollableDiv}
      onScroll={handleScroll}
      style={{
        height: '100%',
        overflow: 'auto',
        padding: '0 16px',

      }}>
          <Timeline style={{marginTop:20}}>
            {
              logsData.map((item:any,index:number)=>{
                return (
                  <Timeline.Item key={index}>
                    <p className="log_create_time">{item.createTime}</p>
                    <p className="log_create_time">{item.username}</p>
                    <p>{item.description}</p>
                  </Timeline.Item>
                )
              })
            }
          </Timeline>
    </div>
  </Drawer>
  )
}

export default ContentLogs
