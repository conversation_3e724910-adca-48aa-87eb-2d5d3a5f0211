{"version": 3, "sources": ["webpack:///mam-ng.min.js", "webpack:///webpack/bootstrap f0883506d6790a6eb1cf", "webpack:///./~/css-loader/lib/css-base.js", "webpack:///./~/style-loader/lib/addStyles.js", "webpack:///./src/directives/dropdown/index.js", "webpack:///./src/directives/sort-group/icon.svg", "webpack:///./src/core/config.js", "webpack:///./src/core/handle-url.js", "webpack:///./src/core/http-provider.js", "webpack:///./src/core/module.js", "webpack:///./src/core/router.js", "webpack:///./src/directives/all-checkbox/index.js", "webpack:///./src/directives/back-to-top/index.js", "webpack:///./src/directives/badge/index.js", "webpack:///./src/directives/calCenterEllipsis/index.js", "webpack:///./src/directives/captcha/index.js", "webpack:///./src/directives/checkbox/index.js", "webpack:///./src/directives/datepicker/index.js", "webpack:///./src/directives/entityView/index.js", "webpack:///./src/directives/href/index.js", "webpack:///./src/directives/image-file-selector/index.js", "webpack:///./src/directives/image/index.js", "webpack:///./src/directives/input-limit/index.js", "webpack:///./src/directives/keyframe/index.js", "webpack:///./src/directives/logo/index.js", "webpack:///./src/directives/pager/index.js", "webpack:///./src/directives/radio/index.js", "webpack:///./src/directives/resize-table/index.js", "webpack:///./src/directives/resizer/index.js", "webpack:///./src/directives/search-input/index.js", "webpack:///./src/directives/selectable/index.js", "webpack:///./src/directives/sort-group/index.js", "webpack:///./src/directives/spin/index.js", "webpack:///./src/directives/switch-button/index.js", "webpack:///./src/directives/top-nav/index.js", "webpack:///./src/directives/user-avatar/index.js", "webpack:///./src/directives/user-info/index.js", "webpack:///./src/directives/validation/index.js", "webpack:///./src/filters/index.js", "webpack:///./src/user/index.js", "webpack:///./src/utils/index.js", "webpack:///./src/directives/back-to-top/style.less", "webpack:///./src/directives/badge/style.less", "webpack:///./src/directives/captcha/style.less", "webpack:///./src/directives/checkbox/style.less", "webpack:///./src/directives/keyframe/style.less", "webpack:///./src/directives/logo/style.less", "webpack:///./src/directives/pager/style.less", "webpack:///./src/directives/radio/style.less", "webpack:///./src/directives/search-input/style.less", "webpack:///./src/directives/sort-group/style.less", "webpack:///./src/directives/spin/style.less", "webpack:///./src/directives/switch-button/style.less", "webpack:///./src/directives/top-nav/style.less", "webpack:///./src/directives/user-avatar/style.less", "webpack:///./src/directives/user-info/style.less", "webpack:///./src/directives/logo/template.html", "webpack:///./src/directives/pager/template.html", "webpack:///./src/directives/search-input/template.html", "webpack:///./src/directives/sort-group/template.html", "webpack:///./src/directives/spin/template.html", "webpack:///./src/directives/switch-button/template.html", "webpack:///./src/directives/top-nav/template.html", "webpack:///./src/directives/user-info/template.html", "webpack:///./src/directives/back-to-top/style.less?040a", "webpack:///./src/directives/badge/style.less?06f9", "webpack:///./src/directives/captcha/style.less?1b1b", "webpack:///./src/directives/checkbox/style.less?6948", "webpack:///./src/directives/keyframe/style.less?7a3c", "webpack:///./src/directives/logo/style.less?1744", "webpack:///./src/directives/pager/style.less?ec28", "webpack:///./src/directives/radio/style.less?0276", "webpack:///./src/directives/search-input/style.less?d736", "webpack:///./src/directives/sort-group/style.less?e408", "webpack:///./src/directives/spin/style.less?07f6", "webpack:///./src/directives/switch-button/style.less?7fc1", "webpack:///./src/directives/top-nav/style.less?2f9d", "webpack:///./src/directives/user-avatar/style.less?6105", "webpack:///./src/directives/user-info/style.less?e1db", "webpack:///./~/style-loader/lib/urls.js", "webpack:///./src/directives/back-to-top/icon.svg", "webpack:///./src/directives/checkbox/icon.svg", "webpack:///./src/directives/radio/icon.svg", "webpack:///./src/index.js"], "names": ["modules", "__webpack_require__", "moduleId", "installedModules", "exports", "module", "i", "l", "call", "m", "c", "value", "d", "name", "getter", "o", "Object", "defineProperty", "configurable", "enumerable", "get", "n", "__esModule", "object", "property", "prototype", "hasOwnProperty", "p", "s", "cssWithMappingToString", "item", "useSourceMap", "content", "cssMapping", "btoa", "sourceMapping", "toComment", "concat", "sources", "map", "source", "sourceRoot", "join", "sourceMap", "unescape", "encodeURIComponent", "JSON", "stringify", "list", "toString", "this", "mediaQuery", "alreadyImportedModules", "length", "id", "push", "addStylesToDom", "styles", "options", "domStyle", "stylesInDom", "refs", "j", "parts", "addStyle", "listToStyles", "newStyles", "base", "css", "media", "part", "insertStyleElement", "style", "target", "getElement", "insertInto", "Error", "lastStyleElementInsertedAtTop", "stylesInsertedAtTop", "insertAt", "nextS<PERSON>ling", "insertBefore", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "removeStyleElement", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "idx", "indexOf", "splice", "createStyleElement", "document", "createElement", "attrs", "type", "addAttrs", "createLinkElement", "link", "rel", "el", "keys", "for<PERSON>ach", "key", "setAttribute", "obj", "update", "remove", "result", "transform", "singleton", "styleIndex", "singletonCounter", "applyToSingletonTag", "bind", "URL", "createObjectURL", "revokeObjectURL", "Blob", "updateLink", "href", "applyToTag", "newObj", "index", "styleSheet", "cssText", "replaceText", "cssNode", "createTextNode", "childNodes", "autoFixUrls", "undefined", "convertToAbsoluteUrls", "fixUrls", "blob", "oldSrc", "isOldIE", "fn", "memo", "apply", "arguments", "window", "all", "atob", "selector", "querySelector", "DEBUG", "newList", "<PERSON><PERSON><PERSON><PERSON>", "textStore", "replacement", "filter", "Boolean", "angular", "directive", "restrict", "scope", "element", "attr", "mode", "$e", "$", "toggle", "find", "menu", "hide", "isShow", "timer", "parent", "top", "position", "outerHeight", "left", "changeMenu", "toggleClass", "not", "stop", "slideUp", "slideToggle", "hover", "clearTimeout", "setTimeout", "on", "removeClass", "mam", "ng", "config", "ngApp", "$controllerProvider", "$compileProvider", "$filterProvider", "$provide", "registerController", "register", "registerDirective", "registerFilter", "registerFactory", "factory", "registerService", "service", "run", "$rootScope", "$state", "$stateParams", "params", "_", "addUrlParam", "url", "param", "handleUrl", "extend", "r", "parseInt", "Math", "random", "Date", "getTime", "token", "utils", "getUrlQueryParam", "encodeURI", "replace", "opsite", "site", "cookie", "path", "httpProvider", "opts", "server", "urlPrefix", "cache", "withCredentials", "loaderTemplate", "loginUrl", "requestErrorTip", "$httpProvider", "<PERSON><PERSON><PERSON><PERSON>", "interfaceCall", "<PERSON><PERSON><PERSON><PERSON>", "requestCount", "$loader", "goLoginPage", "location", "split", "template", "escape", "indexOfOtherServersPrefix", "otherServers", "Array", "prefix", "getServerUrl", "retUrl", "append", "$q", "request", "substring", "show", "productName", "nxt", "headers", "requestError", "rejection", "console", "error", "prompt", "reject", "response", "res", "status", "data", "success", "<PERSON><PERSON><PERSON><PERSON>", "code", "title", "errorReject", "resolve", "responseError", "requestId", "outer", "aEle", "click", "input", "body", "select", "execCommand", "message", "defaults", "interceptors", "add", "app", "version", "requireModule", "uri", "substr", "lastIndexOf", "routes", "init", "parentRoute", "registerRoutes", "ctrlPrefix", "handleRoutes", "val", "language", "ctrlSuffix", "viewDir", "controllerDir", "isFunction", "$urlRouterProvider", "$stateProvider", "loader", "dependencies", "ctrlName", "resolver", "defered", "defer", "require", "$apply", "promise", "handleName", "letter", "toUpperCase", "route", "home", "when", "cv", "templateUrl", "ctrl", "controller", "isArray", "state", "initRoutes", "tempRoutes", "selfRoutes", "parentRoutes", "children", "isEmpty", "e", "prop", "$eval", "collection", "$watch", "newVal", "hasTrue", "hasFalse", "ngModel", "__webpack_exports__", "__WEBPACK_IMPORTED_MODULE_0__style_less__", "provider", "text", "threshold", "icon", "$get", "$mamBackToTop", "container", "check", "scrollTop", "fadeIn", "fadeOut", "isUndefined", "toNumber", "scroll", "animate", "count", "overflowCount", "$timeout", "outerWidth", "width", "middleIndex", "load", "$on", "class", "$parse", "$mamCheckbox", "setCheckedStyle", "isString", "toLowerCase", "label", "addClass", "hasClass", "ngChecked", "minDate", "maxDate", "ctrlType", "showTime", "datetimepicker", "setLocale", "showSecond", "formatTime", "format", "onClose", "blur", "datepicker", "timepicker", "step", "controlData", "viewtype", "entity", "getViewEntityUrl", "tagName", "mam<PERSON><PERSON><PERSON>", "inputId", "onChange", "onError", "event", "file", "files", "reader", "FileReader", "onload", "base64", "onerror", "readAsDataURL", "def", "transclude", "$img", "src", "one", "limitStr", "autoClose", "tip", "fadeToggle", "chars", "eval", "after", "keyCode", "test", "getKeyframeByCode", "keyframes", "keyframe", "extKeyframes", "typeKeyframes", "other", "folder", "setDefaults", "$mamKeyframe", "ext", "useDefault", "extensions", "$watchGroup", "newValue", "oldValue", "$sce", "total", "record", "first", "last", "prev", "next", "$mamPager", "recordTotal", "pageIndex", "pageSize", "pageTotal", "showText", "pageChanged", "maxSize", "textInfo", "change", "page", "ceil", "className", "compile", "removeAttr", "items", "$mamRadio", "set", "model", "ngValue", "scrollX", "$table", "$rol", "$ceils", "$head", "$headceils", "$resizeline", "height", "z-index", "right", "border-right", "cursor", "$dashline", "startPosition", "startWidth", "currentColumn", "currentArray", "currentDashLine", "dragStartFunc", "t", "originalEvent", "dataTransfer", "effectAllowed", "clientX", "lineArr", "draggingFunc", "dragFunc", "preventDefault", "dropEffect", "eq", "rol", "direction", "offset", "max", "bottom", "mousemove", "x", "pageX", "y", "pageY", "mouseup", "unbind", "$http", "search", "keywords", "useHistory", "placeholder", "SearchHistory", "changeBox", "historyBox", "boxId", "self", "showBox", "$event", "op", "selected", "stopPropagation", "then", "close", "delete", "get<PERSON>y", "keyword", "info", "submit", "sh", "changeModel", "segmentsIntr", "a", "b", "area_abc", "area_abd", "area_cda", "firstTime", "boxX", "box", "boxY", "clientY", "end", "move", "backgroundColor", "selectItems", "itemname", "selectBox", "selectBoxTop", "selectBoxBottom", "offsetHeight", "selectBoxLeft", "selectBoxRight", "offsetWidth", "itemLeft", "itemRight", "itemTop", "itemBottom", "lastTime", "mamSelectable", "q", "off", "$applyAsync", "$icon1", "$icon2", "current", "storageKey", "localStorage", "setItem", "getItem", "parse", "default", "changeEnable", "$viewValue", "$setViewValue", "$render", "currentUser", "prepare", "enable", "appPermission", "permission", "judge", "trustAsHtml", "nav", "renderNav", "navs", "$navs", "moreWidth", "$more", "$moreSubs", "slideDown", "resize", "html", "boxWidth", "usedWidth", "activeCurrentItem", "$item", "tooltip", "each", "logged", "loginName", "badge", "share", "errorSrc", "$mamUserAvatar", "$src", "$errorSrc", "imgObj", "Image", "handleLoad", "handleError", "newval", "oldval", "user", "$sub", "isCurrent", "nullValidate", "blankValidate", "lengthValidate", "min", "numValueValidate", "isNaN", "pswValidate", "formName", "confirmId", "$scope", "numValidate", "integerValidate", "positiveNumberValidate", "dateValidate", "thePat", "theShortPat", "theShortPat2", "theShortPat3", "theShortPat4", "theShortPat5", "specialCharValidate", "RegExp", "scriptCharValidate", "noChineseValidate", "match", "phoneValidate", "emailValidate", "postcodesValidate", "ipValidate", "mobileValidate", "uncAddressValidate", "ftpAddressValidate", "hddAddressValidate", "ossAddressValidate", "$compile", "mamValidationService", "getParentForm", "$element", "validateAll", "$mamValidationInfo", "$hasValidateAll", "elementValidator", "formValidator", "forms", "eleName", "$formatters", "formatter", "pre", "$attrs", "$ngModel", "post", "$needValid", "form", "mamValidationList", "vList", "validateList", "cusValidate", "validity", "validate", "fun", "validResult", "$setValidity", "$parsers", "types", "extTagEnable", "ret", "typeTagEnable", "namespace", "shortCode", "removeHtmlTag", "str", "len", "sum", "newStr", "matchList", "charCodeAt", "char<PERSON>t", "size", "custom", "toFixed", "formatSize", "time", "dateTime", "isDate", "oTime", "timeArr", "gotDateTime", "h", "mi", "futureDate", "nowDate", "dsec", "permissions", "systemCode", "ap", "comingDateTime", "maxUnit", "minTime", "exactTo", "locals", "baseUrl", "protocol", "host", "currentDir", "pathname", "fullMatch", "origUrl", "unquotedOrigUrl", "trim", "$1", "newUrl"], "mappings": "CAAS,SAAUA,GCInB,QAAAC,GAAAC,GAGA,GAAAC,EAAAD,GACA,MAAAC,GAAAD,GAAAE,OAGA,IAAAC,GAAAF,EAAAD,IACAI,EAAAJ,EACAK,GAAA,EACAH,WAUA,OANAJ,GAAAE,GAAAM,KAAAH,EAAAD,QAAAC,IAAAD,QAAAH,GAGAI,EAAAE,GAAA,EAGAF,EAAAD,QAvBA,GAAAD,KA4BAF,GAAAQ,EAAAT,EAGAC,EAAAS,EAAAP,EAGAF,EAAAK,EAAA,SAAAK,GAA2C,MAAAA,IAG3CV,EAAAW,EAAA,SAAAR,EAAAS,EAAAC,GACAb,EAAAc,EAAAX,EAAAS,IACAG,OAAAC,eAAAb,EAAAS,GACAK,cAAA,EACAC,YAAA,EACAC,IAAAN,KAMAb,EAAAoB,EAAA,SAAAhB,GACA,GAAAS,GAAAT,KAAAiB,WACA,WAA2B,MAAAjB,GAAA,SAC3B,WAAiC,MAAAA,GAEjC,OADAJ,GAAAW,EAAAE,EAAA,IAAAA,GACAA,GAIAb,EAAAc,EAAA,SAAAQ,EAAAC,GAAsD,MAAAR,QAAAS,UAAAC,eAAAlB,KAAAe,EAAAC,IAGtDvB,EAAA0B,EAAA,GAGA1B,IAAA2B,EAAA,MDMM,SAAUvB,EAAQD,GErBxB,QAAAyB,GAAAC,EAAAC,GACA,GAAAC,GAAAF,EAAA,OACAG,EAAAH,EAAA,EACA,KAAAG,EACA,MAAAD,EAGA,IAAAD,GAAA,kBAAAG,MAAA,CACA,GAAAC,GAAAC,EAAAH,EAKA,QAAAD,GAAAK,OAJAJ,EAAAK,QAAAC,IAAA,SAAAC,GACA,uBAAAP,EAAAQ,WAAAD,EAAA,SAGAH,QAAAF,IAAAO,KAAA,MAGA,OAAAV,GAAAU,KAAA,MAIA,QAAAN,GAAAO,GAKA,yEAHAT,KAAAU,SAAAC,mBAAAC,KAAAC,UAAAJ,MAGA,MArEAtC,EAAAD,QAAA,SAAA2B,GACA,GAAAiB,KAwCA,OArCAA,GAAAC,SAAA,WACA,MAAAC,MAAAX,IAAA,SAAAT,GACA,GAAAE,GAAAH,EAAAC,EAAAC,EACA,OAAAD,GAAA,GACA,UAAAA,EAAA,OAAmCE,EAAA,IAEnCA,IAEGU,KAAA,KAIHM,EAAA1C,EAAA,SAAAN,EAAAmD,GACA,gBAAAnD,KACAA,IAAA,KAAAA,EAAA,KAEA,QADAoD,MACA9C,EAAA,EAAgBA,EAAA4C,KAAAG,OAAiB/C,IAAA,CACjC,GAAAgD,GAAAJ,KAAA5C,GAAA,EACA,iBAAAgD,KACAF,EAAAE,IAAA,GAEA,IAAAhD,EAAA,EAAYA,EAAAN,EAAAqD,OAAoB/C,IAAA,CAChC,GAAAwB,GAAA9B,EAAAM,EAKA,iBAAAwB,GAAA,IAAAsB,EAAAtB,EAAA,MACAqB,IAAArB,EAAA,GACAA,EAAA,GAAAqB,EACKA,IACLrB,EAAA,OAAAA,EAAA,aAAAqB,EAAA,KAEAH,EAAAO,KAAAzB,MAIAkB,IF0GM,SAAU3C,EAAQD,EAASH,GGxDjC,QAAAuD,GAAAC,EAAAC,GACA,OAAApD,GAAA,EAAgBA,EAAAmD,EAAAJ,OAAmB/C,IAAA,CACnC,GAAAwB,GAAA2B,EAAAnD,GACAqD,EAAAC,EAAA9B,EAAAwB,GAEA,IAAAK,EAAA,CACAA,EAAAE,MAEA,QAAAC,GAAA,EAAiBA,EAAAH,EAAAI,MAAAV,OAA2BS,IAC5CH,EAAAI,MAAAD,GAAAhC,EAAAiC,MAAAD,GAGA,MAAQA,EAAAhC,EAAAiC,MAAAV,OAAuBS,IAC/BH,EAAAI,MAAAR,KAAAS,EAAAlC,EAAAiC,MAAAD,GAAAJ,QAEG,CAGH,OAFAK,MAEAD,EAAA,EAAiBA,EAAAhC,EAAAiC,MAAAV,OAAuBS,IACxCC,EAAAR,KAAAS,EAAAlC,EAAAiC,MAAAD,GAAAJ,GAGAE,GAAA9B,EAAAwB,KAA2BA,GAAAxB,EAAAwB,GAAAO,KAAA,EAAAE,WAK3B,QAAAE,GAAAjB,EAAAU,GAIA,OAHAD,MACAS,KAEA5D,EAAA,EAAgBA,EAAA0C,EAAAK,OAAiB/C,IAAA,CACjC,GAAAwB,GAAAkB,EAAA1C,GACAgD,EAAAI,EAAAS,KAAArC,EAAA,GAAA4B,EAAAS,KAAArC,EAAA,GACAsC,EAAAtC,EAAA,GACAuC,EAAAvC,EAAA,GACAa,EAAAb,EAAA,GACAwC,GAAcF,MAAAC,QAAA1B,YAEduB,GAAAZ,GACAY,EAAAZ,GAAAS,MAAAR,KAAAe,GADAb,EAAAF,KAAAW,EAAAZ,IAAkDA,KAAAS,OAAAO,KAIlD,MAAAb,GAGA,QAAAc,GAAAb,EAAAc,GACA,GAAAC,GAAAC,EAAAhB,EAAAiB,WAEA,KAAAF,EACA,SAAAG,OAAA,8GAGA,IAAAC,GAAAC,IAAAzB,OAAA,EAEA,YAAAK,EAAAqB,SACAF,EAEGA,EAAAG,YACHP,EAAAQ,aAAAT,EAAAK,EAAAG,aAEAP,EAAAS,YAAAV,GAJAC,EAAAQ,aAAAT,EAAAC,EAAAU,YAMAL,EAAAvB,KAAAiB,OACE,eAAAd,EAAAqB,SAGF,SAAAH,OAAA,qEAFAH,GAAAS,YAAAV,IAMA,QAAAY,GAAAZ,GACA,UAAAA,EAAAa,WAAA,QACAb,GAAAa,WAAAC,YAAAd,EAEA,IAAAe,GAAAT,EAAAU,QAAAhB,EACAe,IAAA,GACAT,EAAAW,OAAAF,EAAA,GAIA,QAAAG,GAAAhC,GACA,GAAAc,GAAAmB,SAAAC,cAAA,QAOA,OALAlC,GAAAmC,MAAAC,KAAA,WAEAC,EAAAvB,EAAAd,EAAAmC,OACAtB,EAAAb,EAAAc,GAEAA,EAGA,QAAAwB,GAAAtC,GACA,GAAAuC,GAAAN,SAAAC,cAAA,OAQA,OANAlC,GAAAmC,MAAAC,KAAA,WACApC,EAAAmC,MAAAK,IAAA,aAEAH,EAAAE,EAAAvC,EAAAmC,OACAtB,EAAAb,EAAAuC,GAEAA,EAGA,QAAAF,GAAAI,EAAAN,GACA7E,OAAAoF,KAAAP,GAAAQ,QAAA,SAAAC,GACAH,EAAAI,aAAAD,EAAAT,EAAAS,MAIA,QAAAtC,GAAAwC,EAAA9C,GACA,GAAAc,GAAAiC,EAAAC,EAAAC,CAGA,IAAAjD,EAAAkD,WAAAJ,EAAApC,IAAA,CAGA,KAFAuC,EAAAjD,EAAAkD,UAAAJ,EAAApC,MASA,mBAJAoC,GAAApC,IAAAuC,EAUA,GAAAjD,EAAAmD,UAAA,CACA,GAAAC,GAAAC,GAEAvC,GAAAqC,MAAAnB,EAAAhC,IAEA+C,EAAAO,EAAAC,KAAA,KAAAzC,EAAAsC,GAAA,GACAJ,EAAAM,EAAAC,KAAA,KAAAzC,EAAAsC,GAAA,OAGAN,GAAA7D,WACA,kBAAAuE,MACA,kBAAAA,KAAAC,iBACA,kBAAAD,KAAAE,iBACA,kBAAAC,OACA,kBAAAnF,OAEAsC,EAAAwB,EAAAtC,GACA+C,EAAAa,EAAAL,KAAA,KAAAzC,EAAAd,GACAgD,EAAA,WACAtB,EAAAZ,GAEAA,EAAA+C,MAAAL,IAAAE,gBAAA5C,EAAA+C,SAGA/C,EAAAkB,EAAAhC,GACA+C,EAAAe,EAAAP,KAAA,KAAAzC,GACAkC,EAAA,WACAtB,EAAAZ,IAMA,OAFAiC,GAAAD,GAEA,SAAAiB,GACA,GAAAA,EAAA,CACA,GACAA,EAAArD,MAAAoC,EAAApC,KACAqD,EAAApD,QAAAmC,EAAAnC,OACAoD,EAAA9E,YAAA6D,EAAA7D,UAEA,MAGA8D,GAAAD,EAAAiB,OAEAf,MAeA,QAAAM,GAAAxC,EAAAkD,EAAAhB,EAAAF,GACA,GAAApC,GAAAsC,EAAA,GAAAF,EAAApC,GAEA,IAAAI,EAAAmD,WACAnD,EAAAmD,WAAAC,QAAAC,EAAAH,EAAAtD,OACE,CACF,GAAA0D,GAAAnC,SAAAoC,eAAA3D,GACA4D,EAAAxD,EAAAwD,UAEAA,GAAAN,IAAAlD,EAAAc,YAAA0C,EAAAN,IAEAM,EAAA3E,OACAmB,EAAAS,aAAA6C,EAAAE,EAAAN,IAEAlD,EAAAU,YAAA4C,IAKA,QAAAN,GAAAhD,EAAAgC,GACA,GAAApC,GAAAoC,EAAApC,IACAC,EAAAmC,EAAAnC,KAMA,IAJAA,GACAG,EAAA+B,aAAA,QAAAlC,GAGAG,EAAAmD,WACAnD,EAAAmD,WAAAC,QAAAxD,MACE,CACF,KAAAI,EAAAW,YACAX,EAAAc,YAAAd,EAAAW,WAGAX,GAAAU,YAAAS,SAAAoC,eAAA3D,KAIA,QAAAkD,GAAArB,EAAAvC,EAAA8C,GACA,GAAApC,GAAAoC,EAAApC,IACAzB,EAAA6D,EAAA7D,UAQAsF,MAAAC,KAAAxE,EAAAyE,uBAAAxF,GAEAe,EAAAyE,uBAAAF,KACA7D,EAAAgE,EAAAhE,IAGAzB,IAEAyB,GAAA,uDAAuDlC,KAAAU,SAAAC,mBAAAC,KAAAC,UAAAJ,MAAA,MAGvD,IAAA0F,GAAA,GAAAhB,OAAAjD,IAA6B0B,KAAA,aAE7BwC,EAAArC,EAAAsB,IAEAtB,GAAAsB,KAAAL,IAAAC,gBAAAkB,GAEAC,GAAApB,IAAAE,gBAAAkB,GA1VA,GAAA1E,MAWA2E,EATA,SAAAC,GACA,GAAAC,EAEA,mBAEA,WADA,KAAAA,MAAAD,EAAAE,MAAAxF,KAAAyF,YACAF,IAIA,WAMA,MAAAG,SAAAjD,mBAAAkD,MAAAD,OAAAE,OAGApE,EAAA,SAAA8D,GACA,GAAAC,KAEA,iBAAAM,GAKA,WAJA,KAAAN,EAAAM,KACAN,EAAAM,GAAAP,EAAAhI,KAAA0C,KAAA6F,IAGAN,EAAAM,KAEC,SAAAtE,GACD,MAAAkB,UAAAqD,cAAAvE,KAGAoC,EAAA,KACAE,EAAA,EACAjC,KAEAsD,EAAAnI,EAAA,GAEAI,GAAAD,QAAA,SAAA4C,EAAAU,GACA,sBAAAuF,eACA,gBAAAtD,UAAA,SAAAf,OAAA,+DAGAlB,SAEAA,EAAAmC,MAAA,gBAAAnC,GAAAmC,MAAAnC,EAAAmC,SAIAnC,EAAAmD,YAAAnD,EAAAmD,UAAA0B,KAGA7E,EAAAiB,aAAAjB,EAAAiB,WAAA,QAGAjB,EAAAqB,WAAArB,EAAAqB,SAAA,SAEA,IAAAtB,GAAAQ,EAAAjB,EAAAU,EAIA,OAFAF,GAAAC,EAAAC,GAEA,SAAAwF,GAGA,OAFAC,MAEA7I,EAAA,EAAiBA,EAAAmD,EAAAJ,OAAmB/C,IAAA,CACpC,GAAAwB,GAAA2B,EAAAnD,GACAqD,EAAAC,EAAA9B,EAAAwB,GAEAK,GAAAE,OACAsF,EAAA5F,KAAAI,GAGA,GAAAuF,EAAA,CAEA1F,EADAS,EAAAiF,EAAAxF,GACAA,GAGA,OAAApD,GAAA,EAAiBA,EAAA6I,EAAA9F,OAAsB/C,IAAA,CACvC,GAAAqD,GAAAwF,EAAA7I,EAEA,QAAAqD,EAAAE,KAAA,CACA,OAAAC,GAAA,EAAmBA,EAAAH,EAAAI,MAAAV,OAA2BS,IAAAH,EAAAI,MAAAD,WAE9CF,GAAAD,EAAAL,OA0LA,IAAAuE,GAAA,WACA,GAAAuB,KAEA,iBAAA1B,EAAA2B,GAGA,MAFAD,GAAA1B,GAAA2B,EAEAD,EAAAE,OAAAC,SAAA7G,KAAA,WHqOM,SAAUrC,EAAQD,GI/fxBoJ,QAAAnJ,OAAA,UACAoJ,UAAA,yBACA,OACAC,SAAA,IACAzD,KAAA,SAAA0D,EAAAC,EAAAC,GAAmDF,EAAAG,KAAA,OACnD,IAAAC,GAAAC,EAAAJ,GACAK,EAAAF,EAAAG,KAAA,oBACAC,EAAAJ,EAAAG,KAAA,iBACAC,GAAAC,MACA,IAAAC,IAAA,EAEAC,EAAA,CAGA,cAAAL,EAAAM,SAAAnG,IAAA,aACA,GAAAoG,GAAAP,EAAAQ,WAAAD,IAAAP,EAAAS,cAAA,EACAC,EAAAV,EAAAQ,WAAAE,IACAR,GAAA/F,IAAA,MAAAoG,GACAL,EAAA/F,IAAA,OAAAuG,OAEAZ,GAAA3F,IAAA,sBAGA,IAAAwG,GAAA,WACAP,KACAJ,EAAAY,YAAA,0BACAV,EAAAW,IAAAX,GAAAY,OAAAC,QAAA,KAAAT,SAAAL,KAAA,oBACAC,EAAAY,OAAAE,YAAA,KAGA,UAAAtB,EAAAG,MAEAC,EAAAmB,MAAA,WAKA,MAJAC,cAAAb,GACAD,GACAO,KAEA,GACqB,WAKrB,MAJAO,cAAAb,GACAD,IACAC,EAAAc,WAAAR,EAAA,OAEA,IAEAT,EAAAe,MAAA,WACAC,aAAAb,IACqB,WAKrB,MAJAa,cAAAb,GACAD,IACAC,EAAAc,WAAAR,EAAA,OAEA,KAGAX,EAAAoB,GAAA,mBAEA,MADAT,MACA,IAIAT,EAAAD,KAAA,MAAAmB,GAAA,mBACAF,aAAAb,GACAD,GAAA,EACAJ,EAAAqB,YAAA,0BACAnB,EAAAY,OAAAC,QAAA,OAGAhB,EAAA,QAAAqB,GAAA,mBACAF,aAAAb,GACAD,IACAA,GAAA,EACAJ,EAAAqB,YAAA,0BACAnB,EAAAY,OAAAC,QAAA,aJ0gBM,SAAU3K,EAAQD,GKnlBxBC,EAAAD,QAAA,ojBLylBM,SAAUC,EAAQD,GMxlBxBmL,IAAAC,GAAAC,OAAA,SAAAC,GAqBA,MApBAA,GAAAD,QACA,sEACA,SAAAE,EAAAC,EAAAC,EAAAC,GACAJ,EAAAK,mBAAAJ,EAAAK,SACAN,EAAAO,kBAAAL,EAAAnC,UACAiC,EAAAQ,eAAAL,EAAAG,SACAN,EAAAS,gBAAAL,EAAAM,QACAV,EAAAW,gBAAAP,EAAAQ,WAIAZ,EAAAa,KACA,qCACA,SAAAC,EAAAC,EAAAC,GACAF,EAAAC,SACAD,EAAAG,OAAAD,EACAF,EAAAf,OAAAmB,EAAAxL,IAAAwH,OAAA,cACA4D,EAAAjM,EAAAqI,OAAArI,KAGAmL,INgmBM,SAAUrL,EAAQD,GOtnBxB,QAAAyM,GAAAC,EAAAC,GAEA,MADAD,MAAA,IAAAA,EAAAtH,QAAA,cAAAuH,EAIAxB,IAAAC,GAAAwB,UAAA,SAAAF,EAAApJ,GACAsG,EAAAiD,WAA6BC,GAAA,GAAUxJ,GAEvCwJ,IACAJ,EAAAD,EAAAC,EAAA,KAAAK,SAAA,IAAAC,KAAAC,WAAA,GAAAC,OAAAC,WAGA,IAAAC,GAAAjC,IAAAkC,MAAAC,iBAAA,QACA,OAAAF,IACAV,EAAAD,EAAAC,EAAA,SAAAa,UAAAH,GAAAI,QAAA,cAGA,IAAAC,GAAAtC,IAAAkC,MAAAC,iBAAA,YACA,OAAAG,IACAf,EAAAD,EAAAC,EAAA,UAAAe,GAGA,IAAAC,GAAAvC,IAAAkC,MAAAC,iBAAA,OAQA,OAPA,OAAAI,IACAA,EAAAvC,IAAAkC,MAAAC,iBAAA,aAEA,MAAAI,IACAhB,EAAAD,EAAAC,EAAA,QAAAgB,GACA9D,EAAA+D,OAAA,OAAAlL,mBAAAiL,IAAoDE,KAAA,OAEpDlB,IP6nBM,SAAUzM,EAAQD,GQ3pBxBmL,IAAAC,GAAAyC,aAAA,SAAAvC,EAAAhI,GACA,GAAAwK,GAAAlE,EAAAiD,WACApM,KAAA,UACAsN,OAAA,GACAC,UAAA,KACAC,OAAA,EACAC,iBAAA,EACAC,eAAA,2DACAC,SAAA,iCACAC,gBAAA,eACK/K,EA8KL,OA5KAgI,GAAAD,QACA,2BACA,SAAAK,EAAA4C,GAMA,QAAAC,GAAAlD,GACAA,EAAAmD,gBAAA,IAAAnD,EAAAoD,cACAC,GACA,GACAC,EAAA3E,OAKA,QAAA4E,KACAC,SAAA1H,KAAA2H,MAAA,mBAAA7L,OAAA,IACA4L,SAAA1H,KAAAqF,EAAAuC,SAAAjB,EAAAM,WAA+D1B,IAAAsC,OAAAH,SAAA1H,SAK/D,QAAA8H,GAAAvC,GACA,GAAApF,IAAA,CAQA,OAPAwG,GAAAoB,cAAApB,EAAAoB,uBAAAC,QACA3C,EAAAvG,QAAA6H,EAAAoB,aAAA,SAAAxN,IACA,IAAA4F,IACAA,EAAAoF,EAAAtH,QAAA1D,EAAA0N,WAIA9H,EAGA,QAAA+H,GAAA3C,GACA,OAAAA,EAAAtH,QAAA0I,EAAAE,WACA,MAAAF,GAAAC,MAEA,IAAAD,EAAAoB,cAAApB,EAAAoB,uBAAAC,OAAA,CACA,GAAAG,GAAA,EAMA,OALA9C,GAAAvG,QAAA6H,EAAAoB,aAAA,SAAAxN,GACA,IAAAgL,EAAAtH,QAAA1D,EAAA0N,UACAE,EAAA5N,EAAAqM,UAGAuB,EAEA,SA9CA,GAAAZ,GAAA,EACAC,EAAA/E,EAAAkE,EAAAK,eACAvE,GAAA,QAAA2F,OAAAZ,GACAA,EAAA3E,OA8CA0B,EAAAM,QAAA8B,EAAArN,MAAA,cAAA+O,GACA,OACAC,QAAA,SAAApE,GACA,IAAAA,EAAAqB,IAAAtH,QAAA0I,EAAAE,YAAA,IAAAiB,EAAA5D,EAAAqB,OACArB,EAAAmD,eAAA,EACAnD,EAAAqB,IAAA2C,EAAAhE,EAAAqB,KAAArB,EAAAqB,IAAAgD,UAAA,EAAArE,EAAAqB,IAAAzJ,QACAoI,EAAAqB,IAAAvB,IAAAC,GAAAwB,UAAAvB,EAAAqB,MACA,IAAArB,EAAAoD,aACAC,IACAC,EAAAgB,QAGA,IAAAC,GAAApD,EAAAxL,IAAA6O,IAAA,kBAMA,OALAD,KACAvE,EAAAyE,UACAzE,EAAAyE,YACAzE,EAAAyE,QAAA,eAAAF,GAEAvE,GAEA0E,aAAA,SAAAC,GAMA,MALAzB,GAAAyB,EAAA3E,QACA2E,EAAA3E,OAAAmD,gBACAyB,QAAAC,MAAA,eAAAF,GACA7E,IAAAgF,OAAArC,EAAAO,kBAEAmB,EAAAY,OAAAJ,IAEAK,SAAA,SAAAC,GACA,QAAAA,EAAAjF,OAAAmD,cACA,MAAA8B,EAGA,IADA/B,EAAA+B,EAAAjF,QACA,MAAAiF,EAAAC,OAAA,CACA,IAAAD,EAAAE,KAAAC,QASA,MANAH,GAAAE,KAAAF,EAAAE,KAAAN,MACAI,EAAAE,KAAAC,SAAA,EACAR,QAAAC,MAAA,WAAAI,IACA,IAAAA,EAAAjF,OAAAqF,aACAvF,IAAAgF,OAAAhQ,EAAA,UAAAmQ,EAAAE,KAAAG,KAAAL,EAAAE,KAAAI,SAEA,IAAAN,EAAAjF,OAAAwF,YACArB,EAAAY,OAAAE,GAEAd,EAAAsB,QAAAR,EAXAA,GAAAE,KAAAF,EAAAE,UAcA,MAAAF,IAEAS,cAAA,SAAAT,GAEA,GADA/B,EAAA+B,EAAAjF,QACAiF,EAAAjF,OAAAmD,cAAA,CAEA,GADAyB,QAAAC,MAAA,gBAAAI,IACA,IAAAA,EAAAC,OAEA,MADApF,KAAAgF,OAAAhQ,EAAA,6BACAqP,EAAAY,OAAAE,EAGA,IADAA,EAAAE,KAAAF,EAAAE,KAAAN,MACA,MAAAI,EAAAC,OAIA,OAHA,IAAAD,EAAAjF,OAAAqF,aACA9B,KAEA,IAAA0B,EAAAjF,OAAAwF,YACArB,EAAAY,OAAAE,GAEAd,EAAAsB,QAAAR,EAEA,SAAAA,EAAAjF,OAAAqF,YAIA,GAHA,MAAAJ,EAAAC,QACApF,IAAAgF,OAAAhQ,EAAA,mCAEA,MAAAmQ,EAAAC,OACApF,IAAAgF,OAAAhQ,EAAA,uCAGA,CACA,GAAA6Q,GAAAV,EAAAR,QAAA,cACAmB,EAAArH,EAAA,2CAEA,IADAqH,EAAA1B,OAAA,0BAAApP,EAAA,aAAAmQ,EAAAE,KAAAI,OAAA,UACAI,EACA,CACA,GAAAE,GAAAtH,EAAA,sCACAsH,GAAAC,MAAA,WACA,GAAAC,GAAA7L,SAAAC,cAAA,QACAD,UAAA8L,KAAAvM,YAAAsM,GACAA,EAAAjL,aAAA,QAAA6K,GACAI,EAAAE,SACA/L,SAAAgM,YAAA,UACAhM,SAAAgM,YAAA,QACApG,IAAAqG,QAAAf,QAAA,UAEAlL,SAAA8L,KAAAnM,YAAAkM,KAEAH,EAAA1B,OAAA2B,GAEA/F,IAAAgF,OAAAc,GAGA,WAAAX,EAAAjF,OAAAwF,YACArB,EAAAY,OAAAE,GAEAd,EAAAsB,QAAAR,GAMA,MAJA,OAAAA,EAAAC,QACA3B,IAGAY,EAAAY,OAAAE,QAKAhC,EAAAmD,SAAAvD,gBAAAJ,EAAAI,gBACAI,EAAAmD,SAAAxD,MAAAH,EAAAG,MACAK,EAAAoD,aAAAvO,KAAA2K,EAAArN,SAIA6K,IRkqBM,SAAUrL,EAAQD,GS11BxBwI,OAAA2C,IAAAlL,OAAAuI,OAAA2C,IAAAlL,WAEAkL,IAAAlL,OAAA0R,IAAA,SAAA7D,GACA,GAAA7N,IACAQ,KAAAqN,EAAArN,KACAmR,IAAA9D,EAAA8D,IACAC,QAAA/D,EAAA+D,QACAjE,KAAAE,EAAAgE,cAAAC,IAAAC,OAAA,EAAAlE,EAAAgE,cAAAC,IAAAE,YAAA,QACAC,UACA5O,WAyBA,OAvBA6H,KAAAlL,OAAA6N,EAAArN,MAAAR,EAEAA,EAAAkS,KAAA,SAAA7O,GAkBA,MAjBArD,GAAAqD,QAAAsG,EAAAiD,WACAuF,YAAA,IACS9O,IACT,IAAAwK,EAAAqE,KAAAlS,EAAAqD,KACA6H,IAAAC,GAAAC,OAAApL,EAAA2R,KACAzG,IAAAC,GAAAiH,eAAApS,EAAA2R,IAAA9D,EAAAoE,QACAtE,KAAA3N,EAAA2N,KACAwE,YAAAnS,EAAAqD,QAAA8O,YACAE,WAAArS,EAAAQ,OAGA+L,EAAAvG,QAAAkF,IAAAC,GAAAmH,aAAAzE,EAAAoE,OAAA,aAAAM,EAAAtM,GACAjG,EAAAiS,OAAAhM,GAAAjG,EAAAqD,QAAA8O,YAAAlM,IAGAiF,IAAAsH,SAAAlD,OAAAtP,EAAA2N,KAAA,2BAEA3N,GAGAA,ITi2BM,SAAUA,EAAQD,GUl4BxBmL,IAAAC,GAAAiH,eAAA,SAAA/G,EAAA4G,EAAA5O,GACA,GAAAwK,GAAAlE,EAAAiD,WACAuF,YAAA,GACAxE,KAAA,GACA0E,WAAA,GACAI,WAAA,OACAC,QAAA,OACAC,cAAA,eACKtP,EAwDL,OAvDAkJ,GAAAqG,WAAAX,KACAA,OAGA5G,EAAAD,QACA,4DACA,SAAAyH,EAAAC,EAAAxH,GACA,QAAAyH,GAAApB,EAAAqB,EAAAC,GAeA,OAbAC,UAAA,kBACA,SAAA3D,EAAApD,GACA,GAAAgH,GAAA5D,EAAA6D,OAQA,QANAC,EADA9K,OAAA8K,SACAL,EAAA,SAAAvR,GACA0K,EAAAmH,OAAA,WACAhI,EAAAK,SAAAlK,EAAAjB,MAAAyS,EAAAxR,GACA0R,EAAAtC,cAGAsC,EAAAI,WAMA,QAAAC,GAAAhT,GACA,MAAAA,GAAA+M,QAAA,sBAAA/E,EAAAiL,GACA,MAAAA,GAAAC,gBAIAvK,QAAAnD,QAAAkF,IAAAC,GAAAmH,aAAAL,EAAA,aAAA0B,EAAAnT,GASA,GARAmT,EAAAC,OACAf,EAAAgB,KAAA,GAAAF,EAAAlH,KACAoG,EAAAgB,KAAA,IAAAF,EAAAlH,MAEA,MAAAkH,EAAAG,KACAH,EAAAI,YAAAlG,EAAAF,KAAAE,EAAA6E,QAAA,IAAAiB,EAAAG,GAAA,QACAH,EAAAK,KAAAL,EAAAG,IAEA,MAAAH,EAAAK,KAAA,CACA,GAAAA,GAAAL,EAAAK,IACA,KAAAnG,EAAAwE,aACA2B,EAAAnG,EAAAwE,WAAA,IAAAsB,EAAAK,MAEAL,EAAAM,WAAAT,EAAAQ,GAAAnG,EAAA4E,WACAkB,EAAA9C,SAAAhD,EAAAF,KAAAE,EAAA8E,cAAA,IAAAgB,EAAAK,MAEAzH,EAAA2H,QAAAP,EAAA9C,WACA8C,EAAA9C,QAAAkC,EAAA1H,EAAAsI,EAAA9C,QAAA8C,EAAAM,aAEAnB,EAAAqB,MAAAtG,EAAAsE,YAAA3R,EAAAmT,QAIAtI,GAGAH,IAAAC,GAAAmH,aAAA,SAAAL,EAAAmC,GACA,GAAAC,MACAC,EAAA,GACAC,EAAAH,GAAA,EAmBA,OAjBA,SAAA9B,GAAAL,EAAAsC,GACA,OAAAtU,KAAAgS,GACAA,EAAAhS,GAAAuU,WAAAjI,EAAAkI,QAAAxC,EAAAhS,GAAAuU,WACAF,GAAArU,EAAA,IACAoU,EAAAE,EAAAtU,GAAAgS,EAAAhS,GACA,gBAAAgS,GAAAhS,GAAA,UACAqS,EAAAL,EAAAhS,GAAAuU,SAAAF,IAGAD,EAAAE,EAAAtU,GAAAgS,EAAAhS,GAEAsU,IAAAD,IACAA,EAAAC,EAGA,OAAAF,IACKpC,EAAAsC,GACLF,IV04BM,SAAUrU,EAAQD,GWp+BxBoJ,QAAAnJ,OAAA,UACAoJ,UAAA,4BACA,OACAC,SAAA,IACAzD,KAAA,SAAA0D,EAAAC,EAAAC,GACAA,EAAArI,SAAAqI,EAAArI,UAAA,WAEAoI,EAAA3C,KAAA,kBAAA8N,GACApL,EAAAgK,OAAA,WACA,GAAAhT,GAAAiJ,EAAAoL,KAAA,UACAxL,SAAAnD,QAAAsD,EAAAsL,MAAApL,EAAAqL,YAAA,SAAApT,GACAA,EAAA+H,EAAArI,UAAAb,QAKAgJ,EAAAwL,OAAA,WACA,MAAAvI,GAAArK,IAAAoH,EAAAsL,MAAApL,EAAAqL,YAAA,SAAApT,GACA,MAAAA,GAAA+H,EAAArI,aAEiB,SAAA4T,GACjB,GAAAC,GAAAC,CACA9L,SAAAnD,QAAA+O,EAAA,SAAAtT,GACAA,EACAuT,GAAA,EAEAC,GAAA,IAGA3L,EAAAsL,MAAApL,EAAA0L,QAAA,SAAAF,IAAAC,IAAAD,MACiB,QX6+BX,SAAUhV,EAAQmV,EAAqBvV,GAE7C,YACAe,QAAOC,eAAeuU,EAAqB,cAAgB7U,OAAO,GY9gClE,IAAA8U,GAAAxV,EAAA,GAAAA,GAAAoB,EAAAoU,EAEAjM,SAAAnJ,OAAA,UACAqV,SAAA,2BACA,GAAA7D,GAAA3O,KAAA2O,UACA8D,KAAA,OACAC,UAAA,IACAC,KAAA5V,EAAA,IAEAiD,MAAA4S,KAAA,WACA,OAAoBjE,eAGpBpI,UAAA,yCAAAsM,GACA,GAAA7H,GAAA6H,EAAAlE,QACA,QACAnI,SAAA,IACAkE,SAAA,EACAuB,SAAA,8CAAgEjB,EAAA2H,KAAA,SAChElM,OACAqM,UAAA,KACAL,KAAA,KACAC,UAAA,MAEA3P,KAAA,SAAA0D,EAAAC,EAAAC,GAUA,QAAAoM,KACAD,EAAAE,YAAAvM,EAAAiM,UACA7L,EAAAoM,OAAA,KAEApM,EAAAqM,QAAA,KAbAzM,EAAAgM,KAAAhM,EAAAgM,MAAAzH,EAAAyH,IACA,IAAA5L,GAAAC,EAAAJ,GACAoM,EAAAhM,EAAAL,EAAAqM,WAAApN,OACAgE,GAAAyJ,YAAA1M,EAAAiM,WACAjM,EAAAiM,UAAA1H,EAAA0H,UAEAjM,EAAAiM,UAAAhJ,EAAA0J,SAAA3M,EAAAiM,WAWAI,EAAAO,OAAA,WAA8CN,MAE9ClM,EAAAwH,MAAA,WACAyE,EAAA,IAAApN,OACAoB,EAAA,aAAAwM,SAAgDN,UAAA,GAAe,KAE/DF,EAAAQ,SAA2CN,UAAA,GAAe,OAI1DD,UZwhCM,SAAU5V,EAAQmV,EAAqBvV,GAE7C,YACAe,QAAOC,eAAeuU,EAAqB,cAAgB7U,OAAO,GAC7C,IAAI8U,GAA4CxV,EAAoB,GACZA,GAAoBoB,EAAEoU,Ea/kCnGjM,SAAAnJ,OAAA,UACAoJ,UAAA,sBACA,OACAC,SAAA,IACAyF,SAAA,sEACAxF,OACA8M,MAAA,IACAC,cAAA,MAEAzQ,KAAA,SAAA0D,EAAAC,EAAAC,GAEAF,EAAAwL,OAAA,2Bb4lCM,SAAU9U,EAAQD,GcxmCxBoJ,QAAAnJ,OAAA,UACAoJ,UAAA,yCAAAkN,GACA,OACAjN,SAAA,IACAzD,KAAA,SAAA0D,EAAAC,EAAAC,GACA8M,EAAA,WACA,GAAA/M,EAAAgN,aAAAhN,EAAAW,SAAAsM,QAGA,IADA,GAAAC,GACAlN,EAAAgN,aAAAhN,EAAAW,SAAAsM,SAEAjN,EAAA+L,KAAA/L,EAAA+L,OAAA/H,QAAA,cACAkJ,EAAAlN,EAAA+L,OAAAtS,OAAA,EACAuG,EAAA+L,KAAA/L,EAAA+L,OAAA7F,UAAA,EAAAgH,GACA,MAAAlN,EAAA+L,OAAA7F,UAAAgH,EAAA,EAAAlN,EAAA+L,OAAAtS,iBdqnCM,SAAUhD,EAAQmV,EAAqBvV,GAE7C,YACAe,QAAOC,eAAeuU,EAAqB,cAAgB7U,OAAO,GAC7C,IAAI8U,GAA4CxV,EAAoB,GACZA,GAAoBoB,EAAEoU,EevoCnGjM,SAAAnJ,OAAA,UACAoJ,UAAA,yBACA,OACAC,SAAA,IACAkE,SAAA,EACAuB,SAAA,6BACAlJ,KAAA,SAAA0D,EAAAC,EAAAC,GAEA,QAAAkN,KACAnN,EAAAC,KAAA,MAAA0B,IAAAyC,KAAA,wBAAAV,OAAAC,YAGA5D,EAAAqN,IAAA,sBAAAD,GAEAnN,EAAAyB,GAAA,QAAA0L,GAEAA,UfipCM,SAAU1W,EAAQmV,EAAqBvV,GAE7C,YACAe,QAAOC,eAAeuU,EAAqB,cAAgB7U,OAAO,GAC7C,IAAI8U,GAA4CxV,EAAoB,GACZA,GAAoBoB,EAAEoU,EgBtqCnGjM,SAAAnJ,OAAA,UACAqV,SAAA,0BACA,GAAA7D,GAAA3O,KAAA2O,UACAoF,MAAA,eACApB,KAAA5V,EAAA,IAEAiD,MAAA4S,KAAA,WACA,OAAoBjE,eAGpBpI,UAAA,gDAAAyN,EAAAC,GACA,GAAAjJ,GAAAiJ,EAAAtF,QACA,QACAnI,SAAA,IACAzD,KAAA,SAAA0D,EAAAC,EAAAC,GAmBA,QAAAuN,GAAAxE,GACApJ,QAAA6N,SAAAzE,KACAA,EAAA,QAAAA,EAAA0E,eAEA1E,EACA2E,EAAAC,SAAA,WAAAlM,YAAA,aAEAiM,EAAAC,SAAA,aAAAlM,YAAA,WAzBA,GAAAiM,GAAA3N,EAAAW,OAAA,QACAgN,GAAAE,SAAAvJ,EAAA+I,QACAM,EAAAC,SAAAtJ,EAAA+I,OAEAM,EAAA5H,OAAAzB,EAAA2H,MAGAhM,EAAA6N,WACA/N,EAAAwL,OAAAtL,EAAA6N,UAAA,SAAAtC,GACAgC,EAAAhC,KACqB,GAGrBvL,EAAA0L,SACA5L,EAAAwL,OAAAtL,EAAA0L,QAAA,SAAAH,GACAgC,EAAAhC,KACqB,GAarBxL,EAAAyB,GAAA,mBACAkM,EAAAC,SAAA,WACiBnM,GAAA,kBACjBkM,EAAAjM,YAAA,iBhBirCM,SAAUjL,EAAQD,GiBluCxBoJ,QAAAnJ,OAAA,UACAoJ,UAAA,4BACA,OACAC,SAAA,KACAC,OACAgO,QAAA,IACAC,QAAA,IACAC,SAAA,IACAC,SAAA,KAEA7R,KAAA,SAAA0D,EAAAC,EAAAC,GACAG,EAAA+N,eAAAC,UAAA,KACA,IAAAxR,IAA2ByR,YAAA,EAAAC,WAAA,QAAAC,OAAA,cAgC3B,IA9BA,OAAAxO,EAAAgO,UACAnR,EAAAmR,QAAAhO,EAAAgO,SAEA,OAAAhO,EAAAiO,UACApR,EAAAoR,QAAAjO,EAAAiO,SAGApR,EAAA4R,QAAA,WACA,IAAAxO,EAAAgJ,QACAjJ,EAAA4L,QAAA3L,EAAAgJ,OAGAhJ,EAAAM,KAAA,oBAAAmO,OACA1O,EAAAgK,UAEA,MAAAhK,EAAAkO,WACArR,EAAA8R,YAAA,EACA9R,EAAA2R,OAAA,SAEA,MAAAxO,EAAAkO,WACArR,EAAA+R,YAAA,EACA/R,EAAA2R,OAAA,QACA,MAAAxO,EAAA4L,SAAA,KAAA5L,EAAA4L,SAAA5L,EAAA4L,QAAAlS,OAAA,KACAsG,EAAA4L,QAAA5L,EAAA4L,QAAAzF,UAAA,QAGA,OAAAnG,EAAA6O,UAAA,KAAA7O,EAAA,OACAnD,EAAAgS,KAAA7O,EAAA6O,MAGA,IAAA7O,EAAAkO,cAAA3P,IAAAyB,EAAA8O,YACA,OAAA9O,EAAA8O,YAAA3S,MACA,eACAU,EAAAoR,QAAA,GACA,MACA,kBACApR,EAAAmR,QAAA,IAIA/N,EAAAmO,eAAAvR,SjB2uCM,SAAUnG,EAAQD,GkBjyCxBoJ,QAAAnJ,OAAA,UACAoJ,UAAA,2BACA,OACAC,SAAA,IACAC,OACA+O,SAAA,IACAC,OAAA,KAEA1S,KAAA,SAAA0D,EAAAC,EAAAC,GACA,GAAAiD,GAAAvB,IAAAoN,OAAAC,iBAAAjP,EAAAgP,OAAAhP,EAAA+O,WAEA,WACA,MAAA9O,EAAA,GAAAiP,UAEAjP,EAAAC,KAAA,OAAAiD,GACAlD,EAAAC,KAAA,4BlB8yCM,SAAUxJ,EAAQD,GmB7zCxBoJ,QAAAnJ,OAAA,UAAAoJ,UAAA,qBACA,OACAC,SAAA,IACAzD,KAAA,SAAA0D,EAAAC,EAAAC,GACA,GAAAtC,GAAAgE,IAAAyC,KAAAnE,EAAAiP,QACAlP,GAAAC,KAAA,OAAAtC,QnBs0CM,SAAUlH,EAAQD,GoB30CxBoJ,QAAAnJ,OAAA,UACAoJ,UAAA,+BACA,OACAC,SAAA,IACAyF,SAAA,gHACAxF,OACAoP,QAAA,IACAC,SAAA,IACAC,QAAA,KAEArL,SAAA,EACA3H,KAAA,SAAA0D,EAAAC,EAAAC,GACAD,EAAA3C,KAAA,kBAAAiS,GACA,GAAAC,GAAAvP,EAAA,GAAAwP,MAAA,EACA,UAAAD,EAAA,CAGA,GAAAE,GAAA,GAAAC,WACAD,GAAAE,OAAA,WACA5P,EAAAqP,UACAG,OACAK,OAAAtW,KAAAyD,SAEAiD,EAAAgJ,IAAA,KAEAyG,EAAAI,QAAA,WACA9P,EAAAsP,WAEAI,EAAAK,cAAAP,WpBq1CM,SAAU9Y,EAAQD,GqBj3CxBoJ,QAAAnJ,OAAA,UAAAoJ,UAAA,sBACA,OACAC,SAAA,IACAyF,SAAA,UACAxF,OACAmD,IAAA,IACA6M,IAAA,IACArJ,MAAA,MAEAsJ,YAAA,EACAhM,SAAA,EACA3H,KAAA,SAAA0D,EAAAC,EAAAC,GACA,GAAAgQ,GAAA7P,EAAAJ,EAEAgD,GAAAkI,QAAAnL,EAAA2G,SACA3G,EAAA2G,MAAA3G,EAAAgQ,KAGAhQ,EAAAwL,OAAA,iBACA,GAAA2E,GAAAlN,EAAAkI,QAAAnL,EAAAmD,KAAAnD,EAAAgQ,IAAAhQ,EAAAmD,GACAgN,GAAAvO,IAAAyC,KAAA8L,GACAD,EAAAE,IAAA,mBACAF,EAAAhQ,KAAA,MAAA0B,IAAAyC,KAAArE,EAAA2G,UAEAuJ,EAAAhQ,KAAA,MAAAiQ,UrB43CM,SAAUzZ,OAAQD,SsBp5CxBoJ,QAAAnJ,OAAA,UACAoJ,UAAA,2BACA,OACAC,SAAA,IACAC,OACAqQ,SAAA,KACAzE,QAAA,KAEAtP,KAAA,SAAA0D,MAAAC,QAAAC,MAYA,QAAAoQ,aACA3P,MAAAc,WAAA,WACA8O,IAAAC,aACA9P,QAAA,GACqB,KAfrB,GAAAN,IAAAC,EAAAJ,SACAwQ,OAAA,sCACA/P,QAAA,EACAC,MAAA,CACA,IAAAX,MAAAqQ,SACA,GAAAI,OAAAzQ,MAAAqQ,SAAA9K,MAAA,GAEAmL,MAAA,sBAAAD,MAAA1X,KAAA,YACA,IAAAwX,KAAAlQ,EAAA,6EAAsDoQ,MAAA1X,KAAA,yBACtDqH,IAAAuQ,MAAAJ,KASAnQ,GAAAsB,GAAA,iBAAA0J,GACA,IAAAA,EAAAwF,SAAA,IAAAxF,EAAAwF,UACApP,aAAAb,OACAX,MAAAqQ,SAAAQ,KAAA7Q,MAAA4L,UACAlL,SACAA,QAAA,EACA6P,IAAAC,cAEAxQ,MAAA4L,QAAA5L,MAAA4L,QAAA3H,QAAAjE,MAAAqQ,SAAA,IACArQ,MAAAgK,SACAsG,aAEA5P,SACAA,QAAA,EACA6P,IAAAC,sBtBi6CM,SAAU9Z,EAAQmV,EAAqBvV,GAE7C,YACAe,QAAOC,eAAeuU,EAAqB,cAAgB7U,OAAO,GAC7C,IAAI8U,GAA4CxV,EAAoB,GACZA,GAAoBoB,EAAEoU,EuB78CnGjM,SAAAnJ,OAAA,UACAqV,SAAA,0BAOA,QAAA+E,GAAAC,EAAA3J,GACA,GAAAnE,EAAA2H,QAAAmG,GAAA,CACA,GAAA5Y,GAAA8K,EAAA1C,KAAAwQ,GAA8C3J,QAC9C,UAAAjP,EACA,MAAAA,GAAA6Y,SAGA,SAbA,GAAA9I,IACA+I,gBACAC,iBACAC,MAAA,GACAC,OAAA,GAWA7X,MAAA8X,YAAA,SAAAtX,GACAmO,EAAA7H,EAAAiD,UAAkC4E,EAAAnO,GAClCkJ,EAAAkI,QAAAjD,EAAAkJ,UACAlJ,EAAAkJ,OAAAN,EAAA5I,EAAA+I,aAAA,WAEAhO,EAAAkI,QAAAjD,EAAAiJ,SACAjJ,EAAAiJ,MAAAL,EAAA5I,EAAAgJ,cAAA,WAIA3X,KAAA4S,KAAA,WACA,OAAoBjE,eAGpBpI,UAAA,uCAAAwR,GACA,GAAA/M,GAAA+M,EAAApJ,QAEA,QACAnI,SAAA,IACAyF,SAAA,0CACAxF,OACAmQ,IAAA,IACAoB,IAAA,KACApV,KAAA,MAEA8T,YAAA,EACAhM,SAAA,EACA3H,KAAA,SAAA0D,EAAAC,EAAAC,GAIA,QAAAsR,KAEA,GADAvR,EAAA4N,SAAA,wBACA,UAAA7N,EAAA7D,KACA,MAAAiR,GAAA7I,EAAA6M,OAGA,KAAAnO,EAAAkI,QAAAnL,EAAAuR,KAAA,CACA,GAAApZ,GAAA8K,EAAA1C,KAAAgE,EAAA0M,aAAA,SAAAvZ,GACA,QAAAuL,EAAA2H,QAAAlT,EAAA+Z,cACA,GAAA/Z,EAAA+Z,WAAA5V,QAAAmE,EAAAuR,MAIA,UAAApZ,EACA,MAAAiV,GAAAjV,EAAA6Y,UAGA,IAAA/N,EAAAkI,QAAAnL,EAAA7D,MAAA,CACA,GAAAhE,GAAA8K,EAAA1C,KAAAgE,EAAA2M,eAA+D9J,KAAApH,EAAA7D,MAC/D,UAAAhE,EACA,MAAAiV,GAAAjV,EAAA6Y,UAKA5D,EAAA7I,EAAA4M,OAGA,QAAA/D,GAAA+C,GACAD,EAAAvO,YAAA,uBACAuO,EAAAhQ,KAAA,MAAA0B,IAAAyC,KAAA8L,IAhCA,GAAAD,GAAAjQ,EAAAM,KAAA,MAoCA2P,GAAAxO,GAAA,kBACAzB,EAAA4N,SAAA,yBAGA7N,EAAA0R,aAAA,6BAAAC,EAAAC,EAAA5R,GACAiD,EAAAkI,QAAAnL,EAAAuR,OACAvR,EAAAuR,IAAAvR,EAAAuR,IAAA5D,eAGA1K,EAAAkI,QAAAnL,EAAA7D,OAEA8D,EAAA4N,SAAA,qBAAA7N,EAAA7D,MAGA8G,EAAAkI,QAAAnL,EAAAmQ,KACAqB,KAEAvR,EAAA0B,YAAA,wBACAuO,EAAAE,IAAA,QAAAoB,GACApE,EAAApN,EAAAmQ,cvB29CM,SAAUzZ,EAAQD,EAASH,GwBlkDjCA,EAAA,IAEAuJ,QAAAnJ,OAAA,UACAoJ,UAAA,2BAAA+R,GACA,OACA9R,SAAA,IACAyF,SAAAlP,EAAA,IACA2Z,YAAA,EACAhM,SAAA,EACAjE,OACA8B,OAAA,KAEAxF,KAAA,SAAA0D,EAAAC,EAAAC,GACAF,EAAA8B,OAAA9B,EAAA8B,QAAAwE,IAAAxE,axBilDM,SAAUpL,EAAQD,EAASH,GyB9lDjCA,EAAA,IAEAuJ,QAAAnJ,OAAA,UACAqV,SAAA,uBACA,GAAA7D,GAAA3O,KAAA2O,UACA8D,MACA8F,MAAAlb,EAAA,sBACAmb,OAAAnb,EAAA,sBACAmH,MAAAnH,EAAA,sBACAob,MAAApb,EAAA,sBACAqb,KAAArb,EAAA,qBACAsb,KAAAtb,EAAA,sBACAub,KAAAvb,EAAA,uBAGA2C,MAAA4S,KAAA,WACA,OAAoBjE,eAGpBpI,UAAA,iCAAAsS,GACA,GAAA7N,GAAA6N,EAAAlK,QACA,QACAnI,SAAA,IACAyF,SAAAlP,EAAA,IACA2N,SAAA,EACAjE,OACAqS,YAAA,KACAC,UAAA,KACAC,SAAA,KACAC,UAAA,KACAC,SAAA,KACAC,YAAA,KACA1G,KAAA,KACA2G,QAAA,MAEArW,KAAA,SAAA0D,EAAAC,EAAAC,GAEAF,EAAA4S,SAAA5S,EAAAgM,MAAAzH,EAAAyH,KACAhM,EAAA2S,QAAA3S,EAAA2S,SAAA,MAEApU,IAAAyB,EAAAyS,WACAzS,EAAAyS,UAAA,GAGAzS,EAAAwL,OAAA,qBAAArT,GAEA6H,EAAAwS,UAAA,EACAvS,EAAAM,KAAA,eAAA6F,OAEAnG,EAAAM,KAAA,eAAAE,SAIAT,EAAAwL,OAAA,kBACAxL,EAAAgM,OAEAhM,EAAA4S,SAAAvS,EAAAiD,UAAoDiB,EAAAyH,KAAAhM,EAAAgM,SAIpDhM,EAAA6S,OAAA,SAAA1a,GACA6H,EAAA0S,aAAuCI,KAAA3a,KAIvC6H,EAAAwL,OAAA,uBAAArT,GACA6H,EAAAwS,UAAA/O,KAAAsP,KAAA/S,EAAAqS,YAAArS,EAAAuS,UACAvS,EAAAsS,UAAA,GAAAtS,EAAAsS,UAAAtS,EAAAwS,WACAxS,EAAA6S,OAAA7S,EAAAwS,mBzBymDM,SAAU9b,EAAQD,EAASH,G0B7qDjCA,EAAA,IAEAuJ,QAAAnJ,OAAA,UACAqV,SAAA,uBACA,GAAA7D,GAAA3O,KAAA2O,UACA8K,UAAA,YACA9G,KAAA5V,EAAA,IAGAiD,MAAA4S,KAAA,WACA,OAAoBjE,eAGpBpI,UAAA,2BACA,OACAC,SAAA,IACAgK,QAAA,UACAkJ,QAAA,SAAAhT,EAAAC,GACAD,EAAAiT,WAAA,WACA,IAAAC,GAAAlT,EAAAM,KAAA,sBACAV,SAAAnD,QAAAyW,EAAA,SAAAhb,GACA0H,QAAAI,QAAA9H,GAAA+H,KAAA,WAAAA,EAAA0L,eAMA9L,UAAA,iCAAAsT,GACA,GAAA7O,GAAA6O,EAAAlL,QACA,QACAnI,SAAA,IACAzD,KAAA,SAAA0D,EAAAC,EAAAC,EAAAyK,GASA,QAAA0I,GAAAC,IACA,MAAApT,EAAAqT,QAAAtT,EAAAC,KAAA,SAAAF,EAAAsL,MAAApL,EAAAqT,WACAD,EACA1F,EAAAC,SAAA,WAAAlM,YAAA,aAEAiM,EAAAC,SAAA,aAAAlM,YAAA,WAZA,GAAAiM,GAAA3N,EAAAW,OAAA,QACAgN,GAAAE,SAAAvJ,EAAAyO,YACApF,EAAAC,SAAAtJ,EAAAyO,WAGApF,EAAA5H,OAAAzB,EAAA2H,MAWA,MAAAhM,EAAA0L,SAAA,IAAA1L,EAAA0L,UACA5L,EAAAwL,OAAAtL,EAAA0L,QAAA,SAAAH,GACA4H,EAAA5H,KACqB,GACrB,MAAAvL,EAAAqT,SAAA,IAAArT,EAAAqT,SACAvT,EAAAwL,OAAAtL,EAAAqT,QAAA,SAAA9H,GACA4H,EAAArT,EAAAsL,MAAApL,EAAA0L,YACyB,GAEzByH,EAAArT,EAAAsL,MAAApL,EAAA0L,WAGA3L,EAAAyB,GAAA,mBACAkM,EAAAC,SAAA,WACiBnM,GAAA,kBACjBkM,EAAAjM,YAAA,iB1BurDM,SAAUjL,EAAQD,G2B9uDxBoJ,QAAAnJ,OAAA,UAAAoJ,UAAA,0BAAAkN,GACA,OACAjN,SAAA,IACAC,OACAwT,QAAA,KAEAlX,KAAA,SAAA0D,EAAAC,EAAA/D,GACA8Q,EAAA,WACA,GAAAyG,GAAAxT,EAAAW,kBACA8S,EAAAD,EAAAlT,KAAA,yBACAoT,EAAAD,EAAAxI,WACA0I,EAAAH,EAAAlT,KAAA,cACAsT,EAAAD,EAAA1I,UAEAwI,GAAAjZ,IAAA,QAAAgZ,EAAAvG,QAAA,QACAyG,EAAAlZ,IAAA,uBACAgZ,EAAAhZ,IAAA,aAAAuF,EAAAwT,QAAA,mBACAC,EAAAhZ,IAAA,sBAEA,IAAAqZ,GAAAzT,EAAA,kCACAyT,GAAA5T,KAAA,gBACA4T,EAAArZ,KACAyS,MAAA,IACA6G,OAAA,OACAjT,SAAA,WACAkT,UAAA,OACAC,MAAA,OACApT,IAAA,IACAqT,eAAA,wBACAC,OAAA,YAEA,IAAAC,GAAA/T,EAAA,gCACA+T,GAAA3Z,KACAyS,MAAA,IACA6G,OAAAN,EAAAM,SAAA,KACAjT,SAAA,WACAkT,UAAA,QACAC,MAAA,IACApT,IAAA,IACAqT,eAAA,0BAGA,IAAAG,GAAA,EACAC,EAAA,EACAC,EAAA,EACAC,KACAC,EAAA,GAEAC,EAAA,SAAAtJ,EAAAuJ,GACAvJ,EAAAwJ,cAAAC,aAAAC,cAAA,OACAT,EAAAjJ,EAAA2J,QACAT,EAAAjU,EAAAsU,GAAA/T,SAAAsM,OACA,QAAAvW,GAAA,EAAmCA,EAAA0J,EAAAsU,GAAA/T,kBAAAsK,WAAAxR,OAA8C/C,IACjF,GAAA0J,EAAAsU,GAAA/T,SAAAnJ,IAAA,IAAA4I,EAAAsU,GAAA/T,kBAAAsK,WAAAzT,IAAAd,GAAA,CACA4d,EAAA5d,EACA8d,EAAAZ,EAAAtT,KAAA,cAAA9I,IAAAd,EACA,OAGA6d,EAAAQ,EAAAT,IAEAU,EAAA,SAAA7J,GACA/K,EAAAoU,GAAAha,IAAA,UAAA2Q,EAAA2J,QAAAV,GAAA,MACAhU,EAAAoU,GAAAha,IAAA,mCAEAya,EAAA,SAAA9J,GACA/K,EAAAoU,GAAAha,IAAA,aACA4F,EAAAoU,GAAAha,IAAA,yCACAoF,QAAAnD,QAAA8X,EAAA,SAAApd,GACAiJ,EAAAjJ,GAAAqD,IAAA,mBACAuF,EAAAwT,QACAnT,EAAAjJ,GAAAqD,IAAA,YAAA6Z,EAAAlJ,EAAA2J,QAAAV,EAAA,MAEAhU,EAAAjJ,GAAAqD,IAAA,QAAA6Z,EAAAlJ,EAAA2J,QAAAV,EAAA,QAIAP,GAAApS,GAAA,qBAAA0J,GAEAsJ,EAAAtJ,EADA7R,QAGAua,EAAApS,GAAA,gBAAA0J,GACA6J,EAAA7J,KAEA0I,EAAApS,GAAA,mBAAA0J,GACA8J,EAAA9J,KAEAuI,EAAAjS,GAAA,oBAAA0J,GAGA,MAFAA,GAAA+J,iBACA/J,EAAAwJ,cAAAC,aAAAO,WAAA,QACA,IAGAzB,EAAApT,KAAA,gBAAAxD,SACA4W,EAAA3N,OAAA8N,GACAD,EAAAtT,KAAA,cAAAxD,SACA8W,EAAA7N,OAAAoO,EAGA,QADAY,GAAA,GAAApP,OAAA8N,EAAA2B,GAAA,GAAA9U,KAAA,gBAAA7G,QACA/C,EAAA,EAA+BA,EAAAqe,EAAAtb,OAAoB/C,IACnDqe,EAAAre,KAEAkJ,SAAAnD,QAAAgX,EAAA,SAAA4B,EAAAvX,GACA8B,QAAAnD,QAAA2D,EAAAiV,GAAA/U,KAAA,yBAAAnJ,EAAAT,GACAqe,EAAAre,GAAAiD,KAAAyG,EAAAjJ,GAAAwJ,qB3BmwDM,SAAUlK,EAAQD,G4Bp3DxBoJ,QAAAnJ,OAAA,UAAAoJ,UAAA,wBACA,OACAC,SAAA,IACAC,OACAuV,UAAA,KACArI,MAAA,KACA6G,OAAA,KACAyB,OAAA,KACAC,IAAA,KACAzU,KAAA,KACAiT,MAAA,KACApT,IAAA,KACA6U,OAAA,MAEApZ,KAAA,SAAA0D,EAAAC,EAAA/D,GAgBA,QAAAyZ,GAAApG,GACA,QAAAvP,EAAAuV,UAAA,CACA,GAAAK,GAAArG,EAAAsG,KACA7V,GAAAwV,SACAI,GAAApS,SAAAxD,EAAAwV,SAEAxV,EAAAyV,KAAAG,EAAA5V,EAAAyV,MACAG,EAAApS,SAAAxD,EAAAyV,MAEAxV,EAAAxF,KAAiCuG,KAAA4U,EAAA,OACjCvV,EAAAL,EAAAgB,MAAAvG,KAAuCyS,MAAA0I,EAAA,OACvCvV,EAAAL,EAAAiU,OAAAxZ,KACAuG,KAAA4U,EAAApS,SAAAxD,EAAAkN,OAAA,WAEiB,CACjB,GAAA4I,GAAAvG,EAAAwG,KACA/V,GAAAwV,SACAM,GAAAtS,SAAAxD,EAAAwV,SAEAxV,EAAAyV,KAAAK,EAAA9V,EAAAyV,MACAK,EAAAtS,SAAAxD,EAAAyV,MAEAxV,EAAAxF,KAAiCoG,IAAAiV,EAAA,OACjCzV,EAAAL,EAAAa,KAAApG,KAAsCsZ,OAAA+B,EAAA,OACtCzV,EAAAL,EAAA0V,QAAAjb,KACAoG,IAAAiV,EAAAtS,SAAAxD,EAAA+T,QAAA,QAKA,QAAAiC,KACA/V,EAAA0B,YAAA,UACAtB,EAAArE,UAAAia,OAAA,YAAAN,GACAtV,EAAArE,UAAAia,OAAA,UAAAD,GAhDAhW,EAAAuV,UAAAvV,EAAAuV,WAAA,IAEA,KAAAvV,EAAAuV,UACAtV,EAAAxF,IAAA,sBAEAwF,EAAAxF,IAAA,sBAGAwF,EAAAyB,GAAA,qBAAA6N,GACAA,EAAA4F,iBACAlV,EAAA4N,SAAA,UACAxN,EAAArE,UAAA0F,GAAA,YAAAiU,GACAtV,EAAArE,UAAA0F,GAAA,UAAAsU,U5Bk6DM,SAAUtf,EAAQD,EAASH,G6B77DjCA,EAAA,IAEAuJ,QAAAnJ,OAAA,UACAoJ,UAAA,mCAAAoW,GACA,OACAnW,SAAA,IACAyF,SAAAlP,EAAA,IACA2N,SAAA,EACAjE,OACAmW,OAAA,IACAC,SAAA,IACAC,WAAA,KACAC,YAAA,MAEAha,KAAA,SAAA0D,EAAAC,EAAAC,GAWA,QAAAqW,KAIA,QAAAC,KACAC,EAAAtV,IAAAsV,GAAArV,OAAAC,QAAA,KAAAT,SAAAL,KAAAmW,GACAD,EAAArV,OAAAE,YAAA,KALA,GAAAqV,GAAApd,IACAA,MAAA4Z,SAOA5Z,KAAAqd,QAAA,SAAAC,EAAAC,GAIA,QAAA1Q,MACA1F,GAAAiW,EAAAxD,MAAAzZ,OAAA,IACAsG,EAAA+W,aAAAxY,GACAR,GAAA,EACA2C,GAAA,EACA8V,KARAxW,EAAAqW,iBACA9X,IAAAsY,GACAA,EAAAG,kBASAvV,WAAA,WACA,GAAAsF,GAAA4P,EAAAlf,IAAAqf,EAAA9W,EAAAoW,aAAA7X,GACAwI,GACAA,EAAAkQ,KAAA,WACA7Q,MAGAA,KACyB,OAGzB7M,KAAA2d,MAAA,WACAxW,IACAA,GAAA,EACA8V,MAIAjd,KAAA4d,OAAA,SAAAxd,EAAAkd,GACA7W,EAAAqW,aACAQ,EAAAG,kBACAd,EAAAiB,OAAA,oBAAAxd,GAAgEwN,aAAA,IAAqB8P,KAAA,SAAAlQ,GACrFA,EAAAE,MACA0P,EAAAlf,OAEyB,SAAAsP,GACzBL,QAAAC,MAAAI,OAIAxN,KAAA6d,MAAA,WACAT,EAAAlf,IAAAuI,EAAAoW,WAGA7c,KAAA9B,IAAA,SAAA4f,GACA,GAAArX,EAAAqW,WAAA,CACA,GAAAlT,GAAA,8BAGA,OAFAkU,KACAlU,GAAAkU,GACAnB,EAAAze,IAAA0L,GAAmDgE,aAAA,EAAAjC,YAAA,IAAwC+R,KAAA,SAAAlQ,GAC3FsQ,GAAArX,EAAAoW,cAAA7X,IAAA8Y,IACAV,EAAAxD,MAAApM,EAAAE,OAC6B,SAAAF,GAC7B,KAAAA,EAAAC,SACA2P,EAAAxD,SACAzM,QAAA4Q,KAAA,2BAEA5Q,QAAAC,MAAAI,OAKA,WACA0P,GAGAA,EAAAhW,OACAgB,WAAAkV,EAAAlf,IAAA,MAHAuI,EAAAqW,YAAA,KArFA,GAAAjW,GAAAC,EAAAJ,GACAyW,EAAA,mBACAD,EAAArW,EAAAG,KAAAmW,GACA3Y,GAAA,EACA2C,GAAA,CACAV,GAAAgM,MACAsK,YAAAtW,EAAAsW,aAAA,WAEAtW,EAAA+W,SAqFA/W,EAAAuX,OAAA,SAAAF,GACAA,IACArX,EAAAoW,SAAAiB,GACA5V,WAAA,WACAzB,EAAAgK,OAAA,WACAhK,EAAAmW,YAEqB,IAErBnW,EAAAwX,GAAAN,SAGAlX,EAAAyX,YAAA,SAAAJ,GAIArX,EAAA+W,SAHAM,OACA9Y,IAKA,eACAA,IAAAyB,EAAAoW,UACA1P,QAAAC,MAAA,sCACA3G,EAAAwX,GAAA,GAAAjB,GAAAE,GACAzW,EAAAqW,YACAjW,EAAAsB,GAAA,mBAAA0J,GACA,GAAA1R,GAAAsG,EAAAwX,GAAArE,MAAAzZ,MACA,KAAA0R,EAAAwF,SACA7S,IACAA,IAAA,IAAAA,EAAA,GACAA,EAAA,IAAAA,EAAArE,EAAA,GACAsG,EAAA+W,SAAA/W,EAAAoW,SAAApW,EAAAwX,GAAArE,MAAApV,GAAAsZ,SAC6B,IAAAjM,EAAAwF,SAC7B7S,IACAA,GAAArE,IAAAqE,EAAA,GACAiC,EAAA+W,SAAA/W,EAAAoW,SAAApW,EAAAwX,GAAArE,MAAApV,GAAAsZ,SAC6B,IAAAjM,EAAAwF,SAC7B5Q,EAAA+W,aAAAxY,GACAyB,EAAAwX,GAAAN,SAC6B,IAAA9L,EAAAwF,UAC7B3Q,EAAAM,KAAA,SAAAmO,OACA1O,EAAAuX,UAEAvX,EAAAgK,WAGA3J,EAAArE,UAAA0F,GAAA,QAAA1B,EAAAwX,GAAAN,gB7By8DM,SAAUxgB,EAAQD,G8BlmExBoJ,QAAAnJ,OAAA,UAAAoJ,UAAA,2BACA,OACAC,SAAA,IACAzD,KAAA,SAAA0D,EAAAC,EAAAC,GAMA,QAAAwX,GAAAC,EAAAC,EAAA7gB,EAAAE,GAGA,GAAA4gB,IAAAF,EAAA/B,EAAA7e,EAAA6e,IAAAgC,EAAA9B,EAAA/e,EAAA+e,IAAA6B,EAAA7B,EAAA/e,EAAA+e,IAAA8B,EAAAhC,EAAA7e,EAAA6e,GAGAkC,GAAAH,EAAA/B,EAAA3e,EAAA2e,IAAAgC,EAAA9B,EAAA7e,EAAA6e,IAAA6B,EAAA7B,EAAA7e,EAAA6e,IAAA8B,EAAAhC,EAAA3e,EAAA2e,EAGA,IAAAiC,EAAAC,GAAA,EACA,QAIA,IAAAC,IAAAhhB,EAAA6e,EAAA+B,EAAA/B,IAAA3e,EAAA6e,EAAA6B,EAAA7B,IAAA/e,EAAA+e,EAAA6B,EAAA7B,IAAA7e,EAAA2e,EAAA+B,EAAA/B,EAIA,SAAAmC,GADAA,EAAAF,EAAAC,IACA,GAOA,QAAAlP,KACA,GAAAoP,IAAA,GAAArU,OAAAC,UACAqU,EAAA1I,MAAAwF,QAAAvR,SAAA0U,EAAA1C,SAAAxU,MACAmX,EAAA5I,MAAA6I,QAAA5U,SAAA0U,EAAA1C,SAAA3U,KAAA7E,SAAA8L,KAAAyE,SACAlM,GAAArE,UAAA0F,GAAA,WAA2CsW,aAAuBK,GAElEH,EAAAlS,OAAA,0CAAAtE,GAAA,aAAsFkU,EAAArG,MAAAwF,QAAAe,EAAAvG,MAAA6I,QAAApc,SAAA8L,KAAAyE,WAA+D+L,GAErJJ,EAAAzd,IAAA,uBACA4F,EAAA,uBAAA5F,KACAqG,SAAA,WACAD,IAAAsX,EACAnX,KAAAiX,EACAM,gBAAA,2BAEAlY,EAAA,QAAA5F,IAAA,qBAAAyF,KAAA,+BACAG,EAAA,QAAA2F,OAAA,gFAGA,QAAAsS,GAAAlN,GACA,GAAA6M,GAAA1I,MAAAwF,QAAA3J,EAAAnE,KAAA2O,EACAuC,EAAA5I,MAAA6I,QAAAhN,EAAAnE,KAAA6O,EAAA9Z,SAAA8L,KAAAyE,UACAiM,EAAAnY,EAAAH,EAAAuY,UACAC,EAAArY,EAAA,uBACAsY,EAAAD,EAAAlD,SAAA3U,IACA+X,EAAAF,EAAAjhB,IAAA,GAAAohB,aAAAF,EACAG,EAAAJ,EAAAlD,SAAAxU,KACA+X,EAAAL,EAAAjhB,IAAA,GAAAuhB,YAAAF,CAEAb,GAAA,GAAAE,EAAA,EACAO,EAAAje,KACAyS,MAAA+K,EACAlE,OAAAoE,IAEiBF,EAAA,GAAAE,EAAA,EACjBO,EAAAje,KACAyS,MAAA+K,EACAlE,QAAAoE,EACAtX,IAAA0O,MAAA6I,QAAA5U,SAAA0U,EAAA1C,SAAA3U,KAAA7E,SAAA8L,KAAAyE,YAEiB0L,EAAA,GAAAE,EAAA,EACjBO,EAAAje,KACAyS,OAAA+K,EACAlE,OAAAoE,EACAnX,KAAAuO,MAAAwF,QAAAvR,SAAA0U,EAAA1C,SAAAxU,QAEiBiX,EAAA,GAAAE,EAAA,GACjBO,EAAAje,KACAyS,OAAA+K,EACAlE,QAAAoE,EACAtX,IAAA0O,MAAA6I,QAAA5U,SAAA0U,EAAA1C,SAAA3U,KAAA7E,SAAA8L,KAAAyE,UACAvL,KAAAuO,MAAAwF,QAAAvR,SAAA0U,EAAA1C,SAAAxU,OAIA,QAAArK,GAAA,EAA+BA,EAAA6hB,EAAA9e,OAAwB/C,IAAA,CACvD,GAAAsiB,GAAAT,EAAAnD,GAAA1e,GAAA6e,SAAAxU,KACAkY,EAAAD,EAAAT,EAAAnD,GAAA1e,GAAAuW,QACAiM,EAAAX,EAAAnD,GAAA1e,GAAA6e,SAAA3U,IACAuY,EAAAD,EAAAX,EAAAnD,GAAA1e,GAAAod,QAEA,YAAA5T,EACAwY,EAAAQ,GAAAR,EAAAS,GAAAT,IAAAQ,EACAX,EAAAnD,GAAA1e,GAAA8D,IAAA,4CACyB0e,EAAAR,GAAAS,EAAAR,EACzBJ,EAAAnD,GAAA1e,GAAA8D,IAAA,4CACyBme,EAAAO,GAAAP,EAAAQ,EACzBZ,EAAAnD,GAAA1e,GAAA8D,IAAA,4CAEA+d,EAAAnD,GAAA1e,GAAA8D,IAAA,sBAEqB,UAAA0F,IAErBuX,GAA0C9B,EAAAkD,EAAAhD,EAAA6C,IAAuC/C,EAAAmD,EAAAjD,EAAA6C,IAAwC/C,EAAAqD,EAAAnD,EAAAqD,IAA6BvD,EAAAqD,EAAAnD,EAAAsD,KACtJ1B,GAA0C9B,EAAAkD,EAAAhD,EAAA6C,IAAuC/C,EAAAmD,EAAAjD,EAAA6C,IAAwC/C,EAAAsD,EAAApD,EAAAqD,IAA8BvD,EAAAsD,EAAApD,EAAAsD,KACvJ1B,GAA0C9B,EAAAkD,EAAAhD,EAAA8C,IAA0ChD,EAAAmD,EAAAjD,EAAA8C,IAA2ChD,EAAAqD,EAAAnD,EAAAqD,IAA6BvD,EAAAqD,EAAAnD,EAAAsD,KAC5J1B,GAA0C9B,EAAAkD,EAAAhD,EAAA8C,IAA0ChD,EAAAmD,EAAAjD,EAAA8C,IAA2ChD,EAAAsD,EAAApD,EAAAqD,IAA8BvD,EAAAsD,EAAApD,EAAAsD,KAE7J1B,GAA0C9B,EAAAkD,EAAAhD,EAAA6C,IAAuC/C,EAAAkD,EAAAhD,EAAA8C,IAA0ChD,EAAAqD,EAAAnD,EAAAqD,IAA6BvD,EAAAsD,EAAApD,EAAAqD,KACxJzB,GAA0C9B,EAAAkD,EAAAhD,EAAA6C,IAAuC/C,EAAAkD,EAAAhD,EAAA8C,IAA0ChD,EAAAqD,EAAAnD,EAAAsD,IAAgCxD,EAAAsD,EAAApD,EAAAsD,KAC3J1B,GAA0C9B,EAAAmD,EAAAjD,EAAA6C,IAAwC/C,EAAAmD,EAAAjD,EAAA8C,IAA2ChD,EAAAqD,EAAAnD,EAAAqD,IAA6BvD,EAAAsD,EAAApD,EAAAqD,KAC1JzB,GAA0C9B,EAAAmD,EAAAjD,EAAA6C,IAAwC/C,EAAAmD,EAAAjD,EAAA8C,IAA2ChD,EAAAqD,EAAAnD,EAAAsD,IAAgCxD,EAAAsD,EAAApD,EAAAsD,IAC7JZ,EAAAnD,GAAA1e,GAAA8D,IAAA,4CACyB0e,EAAAR,GAAAS,EAAAR,GAAAK,EAAAH,GAAAI,EAAAH,GACzBI,EAAAR,GAAAS,EAAAR,GAAAK,EAAAH,GAAAI,EAAAH,EACAP,EAAAnD,GAAA1e,GAAA8D,IAAA,4CAEA+d,EAAAnD,GAAA1e,GAAA8D,IAAA,wBAMA,QAAA4d,GAAAjN,GACA,GAAAiO,IAAA,GAAA1V,OAAAC,UACA4U,EAAAnY,EAAAH,EAAAuY,UACAC,EAAArY,EAAA,uBACAsY,EAAAD,EAAAlD,SAAA3U,IACA+X,EAAAF,EAAAjhB,IAAA,GAAAohB,aAAAF,EACAG,EAAAJ,EAAAlD,SAAAxU,KACA+X,EAAAL,EAAAjhB,IAAA,GAAAuhB,YAAAF,EACA3F,EAAAnT,EAAAsL,MAAApL,EAAAoZ,cAEA,IAAAD,EAAAjO,EAAAnE,KAAA+Q,UAAA,IACA,OAAArhB,GAAA,EAAmCA,EAAA6hB,EAAA9e,OAAwB/C,IAAA,CAC3D,GAAAsiB,GAAAT,EAAAnD,GAAA1e,GAAA6e,SAAAxU,KACAkY,EAAAD,EAAAT,EAAAnD,GAAA1e,GAAAuW,QACAiM,EAAAX,EAAAnD,GAAA1e,GAAA6e,SAAA3U,IACAuY,EAAAD,EAAAX,EAAAnD,GAAA1e,GAAAod,QAEA,YAAA5T,GACAqY,EAAAnD,GAAA1e,GAAA8D,IAAA,sBACAke,EAAAQ,GAAAR,EAAAS,GAAAT,IAAAQ,MACA5a,KAAA4U,EAAAxc,GAAAogB,SACA5D,EAAAxc,GAAAogB,UAAA,EAEA5D,EAAAxc,GAAAogB,UAAA5D,EAAAxc,GAAAogB,SAE6BoC,EAAAR,GAAAS,EAAAR,MAC7Bra,KAAA4U,EAAAxc,GAAAogB,SACA5D,EAAAxc,GAAAogB,UAAA,EAEA5D,EAAAxc,GAAAogB,UAAA5D,EAAAxc,GAAAogB,SAE6B6B,EAAAO,GAAAP,EAAAQ,QAC7B7a,KAAA4U,EAAAxc,GAAAogB,SACA5D,EAAAxc,GAAAogB,UAAA,EAEA5D,EAAAxc,GAAAogB,UAAA5D,EAAAxc,GAAAogB,WAGyB,UAAA5W,IACzBqY,EAAAnD,GAAA1e,GAAA8D,IAAA,sBACAid,GAA8C9B,EAAAkD,EAAAhD,EAAA6C,IAAuC/C,EAAAmD,EAAAjD,EAAA6C,IAAwC/C,EAAAqD,EAAAnD,EAAAqD,IAA6BvD,EAAAqD,EAAAnD,EAAAsD,KAC1J1B,GAA8C9B,EAAAkD,EAAAhD,EAAA6C,IAAuC/C,EAAAmD,EAAAjD,EAAA6C,IAAwC/C,EAAAsD,EAAApD,EAAAqD,IAA8BvD,EAAAsD,EAAApD,EAAAsD,KAC3J1B,GAA8C9B,EAAAkD,EAAAhD,EAAA8C,IAA0ChD,EAAAmD,EAAAjD,EAAA8C,IAA2ChD,EAAAqD,EAAAnD,EAAAqD,IAA6BvD,EAAAqD,EAAAnD,EAAAsD,KAChK1B,GAA8C9B,EAAAkD,EAAAhD,EAAA8C,IAA0ChD,EAAAmD,EAAAjD,EAAA8C,IAA2ChD,EAAAsD,EAAApD,EAAAqD,IAA8BvD,EAAAsD,EAAApD,EAAAsD,KAEjK1B,GAA8C9B,EAAAkD,EAAAhD,EAAA6C,IAAuC/C,EAAAkD,EAAAhD,EAAA8C,IAA0ChD,EAAAqD,EAAAnD,EAAAqD,IAA6BvD,EAAAsD,EAAApD,EAAAqD,KAC5JzB,GAA8C9B,EAAAkD,EAAAhD,EAAA6C,IAAuC/C,EAAAkD,EAAAhD,EAAA8C,IAA0ChD,EAAAqD,EAAAnD,EAAAsD,IAAgCxD,EAAAsD,EAAApD,EAAAsD,KAC/J1B,GAA8C9B,EAAAmD,EAAAjD,EAAA6C,IAAwC/C,EAAAmD,EAAAjD,EAAA8C,IAA2ChD,EAAAqD,EAAAnD,EAAAqD,IAA6BvD,EAAAsD,EAAApD,EAAAqD,KAC9JzB,GAA8C9B,EAAAmD,EAAAjD,EAAA6C,IAAwC/C,EAAAmD,EAAAjD,EAAA8C,IAA2ChD,EAAAqD,EAAAnD,EAAAsD,IAAgCxD,EAAAsD,EAAApD,EAAAsD,QACjK7a,KAAA4U,EAAAxc,GAAAogB,SACA5D,EAAAxc,GAAAogB,UAAA,EAEA5D,EAAAxc,GAAAogB,UAAA5D,EAAAxc,GAAAogB,UAE6BoC,EAAAR,GAAAS,EAAAR,GAAAK,EAAAH,GAAAI,EAAAH,GAC7BI,EAAAR,GAAAS,EAAAR,GAAAK,EAAAH,GAAAI,EAAAH,SACAxa,KAAA4U,EAAAxc,GAAAogB,SACA5D,EAAAxc,GAAAogB,UAAA,EAEA5D,EAAAxc,GAAAogB,UAAA5D,EAAAxc,GAAAogB,eAMA,QAAAwC,GAAA,EAAmCA,EAAAf,EAAA9e,OAAwB6f,IAC3Df,EAAAnD,GAAAkE,GAAA9e,IAAA,qBAIAwF,GAAAuZ,IAAA,aAAAtO,SAAA,uBAAAnO,SACAsD,EAAArE,UAAAwd,MAEAnZ,EAAA,QAAA5F,IAAA,iBAAAyF,KAAA,kBACAG,EAAA,QAAA6K,SAAA,uBAAAnO,SAEAiD,EAAAyZ,cAvMAxZ,EAAAyB,GAAA,YAAAkH,EAEA,IAAAsP,GAAAjY,EACAE,EAAAD,EAAAC,U9BizEM,SAAUzJ,EAAQmV,EAAqBvV,GAE7C,YACAe,QAAOC,eAAeuU,EAAqB,cAAgB7U,OAAO,GAC7C,IAAI8U,GAA4CxV,EAAoB,GACZA,GAAoBoB,EAAEoU,E+B3zEnGjM,SAAAnJ,OAAA,UACAoJ,UAAA,0BACA,OACAC,SAAA,IACAyF,SAAAlP,EAAA,IACA2N,SAAA,EACAjE,OACA4L,QAAA,IACAyD,SAAA,MAEA4D,QAAA,SAAAhT,EAAAC,GACA,GAAAgM,GAAA5V,EAAA,GACAojB,EAAArZ,EAAA6L,EACAwN,GAAAxZ,KAAA,gDACAA,KAAA,oDACAD,EAAAM,KAAA,UAAAyF,OAAA0T,EAEA,IAAAC,GAAAtZ,EAAA6L,EAKA,OAJAyN,GAAAzZ,KAAA,qCACAA,KAAA,yCACAD,EAAAM,KAAA,QAAAyF,OAAA2T,GAEA,SAAA3Z,EAAAC,EAAAC,GACAF,EAAAkM,KAAA5V,EAAA,GAEA0J,EAAA6S,OAAA,SAAA1a,GACAA,GAAA6H,EAAA4L,QAAAgO,UACA3W,EAAAkI,QAAAnL,EAAA4L,QAAAiO,aACAC,aAAAC,QAAA/Z,EAAA4L,QAAAiO,WAAA1gB,KAAAC,UAAAjB,IAEA6H,EAAA4L,QAAAgO,QAAAzhB,EACA6H,EAAAqP,UAA4ClX,WAI5C,WACA,SAAA6H,EAAA4L,QAAAgO,QAAA,CACA,SAAA5Z,EAAA4L,QAAAiO,WAAA,CACA,GAAA1hB,GAAA2hB,aAAAE,QAAAha,EAAA4L,QAAAiO,WACA,KAAA5W,EAAAkI,QAAAhT,GAEA,YADA6H,EAAA4L,QAAAgO,QAAAzgB,KAAA8gB,MAAA9hB,IAIA6H,EAAA4L,QAAAgO,QAAA3W,EAAA1C,KAAAP,EAAA4L,QAAAuH,OAAiF+G,SAAA,IACjF,MAAAla,EAAA4L,QAAAgO,UACA5Z,EAAA4L,QAAAgO,QAAA5Z,EAAA4L,QAAAuH,MAAA,c/B20EM,SAAUzc,EAAQmV,EAAqBvV,GAE7C,YACAe,QAAOC,eAAeuU,EAAqB,cAAgB7U,OAAO,GAC7C,IAAI8U,GAA4CxV,EAAoB,GACZA,GAAoBoB,EAAEoU,EgC93EnGjM,SAAAnJ,OAAA,UAAAoJ,UAAA,qBACA,OACAC,SAAA,IACAyF,SAAAlP,EAAA,IACA2N,SAAA,EACAjE,OACAgM,KAAA,KACA5F,KAAA,KAEA9J,KAAA,SAAA0D,EAAAC,EAAAC,GACA,GAAAU,GAAAX,EAAAW,QACA,WAAAA,EAAAnG,IAAA,wBAAAmG,EAAAnG,IAAA,aACAmG,EAAAnG,IAAA,4BhCy4EM,SAAU/D,EAAQD,EAASH,GiCv5EjCA,EAAA,IAEAuJ,QAAAnJ,OAAA,UACAoJ,UAAA,8BACA,OACAC,SAAA,KACAyF,SAAAlP,EAAA,IACA2Z,YAAA,EACAhM,SAAA,EACA8F,QAAA,UACA/J,OACAqP,SAAA,KAEA/S,KAAA,SAAA0D,EAAAC,EAAAC,EAAA0L,GACA5L,EAAAma,aAAA,WACAna,EAAAiJ,KAAA2C,EAAAwO,WACAxO,EAAAyO,cAAAra,EAAAiJ,KACAjJ,EAAAqP,UAAA,kBAAArP,GAAA,UAEAA,EAAAqP,SAAArP,EAAAiJ,MAIA2C,EAAA0O,QAAA,WACAta,EAAAiJ,IAAA2C,EAAAwO,kBjCk6EM,SAAU1jB,EAAQD,EAASH,GkC17EjCA,EAAA,IAIAuJ,QAAAnJ,OAAA,UACAoJ,UAAA,gDAAA+R,EAAA7E,EAAAkJ,GACA,OACAnW,SAAA,IACAyF,SAAAlP,EAAA,IACA2Z,YAAA,EACAhM,SAAA,EACAjE,OACA4L,QAAA,KACA2O,YAAA,MAEAje,KAAA,SAAA0D,EAAAC,EAAAC,GASA,QAAAsa,KAGA,OAFArH,MAEAxc,EAAA,EAAmCA,EAAAqJ,EAAA4L,QAAAlS,OAA0B/C,IAAA,CAC7D,GAAAwB,GAAA6H,EAAA4L,QAAAjV,EAEA,IAAAwB,EAAAsiB,OAIA,QAAAtiB,EAAAE,QAAA,CAKA,GAAA4K,EAAAyK,SAAAvV,EAAAzB,QAAA,CACA,GAAAiG,IAAA,GAAAxE,EAAAzB,OAAAmF,QAAA,UAAA1D,EAAAzB,OAAA,SAAAyB,EAAAzB,MACA,SAAA4P,IAAAxE,OAAAnF,GACA,SAIAsG,EAAAyK,SAAAvV,EAAAuiB,gBAAAviB,EAAAuiB,cAAAhhB,OAAA,IACA4M,IAAAqU,WAAAC,MAAAziB,EAAAuiB,iBAKAzX,EAAAyK,SAAAvV,EAAAE,WACAF,EAAAE,QAAAwZ,EAAAgJ,YAAA1iB,EAAAE,UAEAF,EAAA2C,QAAA,SAAA3C,EAAA2C,SACA3C,EAAA2C,OAAA,SAEA3C,EAAAyF,KAAAgE,IAAAkC,MAAA4M,KAAA9O,IAAAyC,KAAAlM,EAAAyF,OACAuV,EAAAvZ,KAAAzB,QAxBAgb,GAAAvZ,KAAAzB,GA2BA,OAAAxB,GAAA,EAAmCA,EAAAwc,EAAAzZ,OAAkB/C,IAErD,QAAAwc,EAAAxc,GAAA0B,SACA,GAAA1B,GAAA,KAAAwc,EAAAxc,EAAA,GAAA0B,QADA,CAQA,GAAAyiB,GAAAC,EAAA5H,EAAAxc,GACAqkB,GAAAphB,KAAAkhB,GACAG,EAAAjV,OAAA8U,GAGA7X,EAAAvG,QAAAse,EAAA,SAAA7iB,EAAAxB,GACAqJ,EAAA4L,QAAAjV,GAAAuW,MAAA/U,EAAA8U,eAGAiO,EAAAC,EAAAlO,aAEA+N,EAAAthB,OAAA,EACAyhB,EAAA5Z,MAAA,WACA6Z,EAAAha,OAAA3G,IAAA,iBAAA4gB,UAAA,MACyB,WACzBD,EAAAha,OAAAC,QAAA,OACyB+E,OAEzB+U,EAAA1a,OAGAuM,EAAA,WACAsO,KACqB,KAGrB,QAAAA,KACAL,EAAAM,KAAA,IACAH,EAAAG,KAAA,GACA,IAAAC,GAAApb,EAAA6M,aACAwO,EAAAP,EACAnd,GAAA,CAUA,IATAkF,EAAAvG,QAAAse,EAAA,SAAA7iB,EAAAxB,GAEA,MADA8kB,GAAAzb,EAAA4L,QAAAjV,GAAAuW,QACAsO,GAIA,MADAzd,GAAApH,GACA,CAHAskB,GAAAjV,OAAA7N,EAAAwJ,YAAA,aAAAkM,SAAA,WAMA9P,GAAA,GAEA,OADAmP,GAAA,EACAvW,EAAAoH,EAA+CpH,EAAAqkB,EAAAthB,OAAiB/C,IAChEuW,GAAAlN,EAAA4L,QAAAjV,GAAAuW,KAEA,IAAAA,EAAAgO,EAAA,CACA,OAAAvkB,GAAAoH,EAAmDpH,EAAAqkB,EAAAthB,OAAiB/C,IACpEykB,EAAApV,OAAAgV,EAAArkB,GAAAgL,YAAA,QAAAkM,SAAA,aAEAuN,GAAA3gB,KAA+CoG,IAAAsa,EAAApH,WAC/CoH,EAAA/U,WAC6B,CAC7B,OAAAzP,GAAAoH,EAAmDpH,EAAAqkB,EAAAthB,OAAiB/C,IACpEskB,EAAAjV,OAAAgV,EAAArkB,GAAAgL,YAAA,aAAAkM,SAAA,QAEAsN,GAAA1a,YAGA0a,GAAA1a,MAEAib,KAGA,QAAAX,GAAA5iB,GACA,GAAAwjB,GAAAtb,EAAA,4BAAAlI,EAAAyjB,QAAA,cACAzjB,EAAAyF,KAAA,KAAAzF,EAAAE,QAAA,aAKA,OAHAF,GAAAmV,OACAqO,EAAA9N,SAAA1V,EAAAmV,OAEAqO,EAGA,QAAAD,KACAT,EAAA1a,KAAA,SAAAsb,KAAA,SAAA9d,EAAA5F,GACA,GAAAyF,GAAAyC,EAAAlI,GAAAoI,KAAA,KAAAL,KAAA,OACAtC,GAAA/B,QAAA,WAEA+B,IAAAuI,UAAA,EAAAvI,EAAA/B,QAAA,QAEA,IAAA+B,GAAA,KAAAA,IAAA,GAAA0H,SAAA1H,KAAA/B,QAAA+B,GACAyC,EAAAlI,GAAA0V,SAAA,WAEAxN,EAAAlI,GAAAwJ,YAAA,aA5IA,GAIAuZ,GAJA9a,EAAAC,EAAAJ,GACAgb,EAAA7a,EAAAG,KAAA,SACA4a,EAAA/a,EAAAG,KAAA,SAAAE,OACA2a,EAAAD,EAAA5a,KAAA,SAEAya,IACAhb,GAAA8b,QAAA,EAmKA,WACA9b,EAAAua,YAAAva,EAAAua,aAAAtX,EAAAxL,IAAAwH,OAAA,oBACAe,EAAA8b,OAAA,MAAA9b,EAAAua,aAAA,SAAAva,EAAAua,YAAAwB,UAEA,MAAA/b,EAAA4L,SAAA,IAAA5L,EAAA4L,SACA5L,EAAA4L,QAAA3I,EAAAxL,IAAAwH,OAAA,wBACAub,KAEAxa,EAAAwL,OAAA,mBAAAmG,EAAAC,GACA3O,EAAA2H,QAAA+G,IACA6I,MAKAxa,EAAAqN,IAAA,wBAAAjC,EAAAnE,GACAhE,EAAAxL,IAAAwH,OAAA,yCACAV,IAAA0I,GACAjH,EAAAgc,MAAA/T,QAAA,EACAjI,EAAAgc,MAAAC,MAAA,QAEA1d,IAAA0I,EAAAgB,UACAjI,EAAAgc,MAAA/T,QAAAhB,EAAAgB,aACA1J,IAAA0I,EAAAgV,QACAjc,EAAAgc,MAAAC,MAAAhV,EAAAgV,WAKA5b,EAAApB,QAAAyC,GAAA,SAAA4Z,GAEAjb,EAAApB,QAAA3B,KAAA,aAAAoe,YlCu8EM,SAAUhlB,EAAQD,EAASH,GmC/pFjCA,EAAA,IAEAuJ,QAAAnJ,OAAA,UAAAqV,SAAA,4BACA,GAAA7D,GAAA3O,KAAA2O,UACAgU,SAAAta,IAAAyC,KAAA,wBAEA9K,MAAA4S,KAAA,WACA,OAAgBjE,eAEfpI,UAAA,sDAAAkN,EAAAmP,GACD,GAAA5X,GAAA4X,EAAAjU,QACA,QACAnI,SAAA,IACAyF,SAAA,6CACAxF,OACAmQ,IAAA,KAEAF,YAAA,EACAhM,SAAA,EACA3H,KAAA,SAAA0D,EAAAC,EAAAC,GAIA,QAAA0I,KAEAwT,MADA7d,KAAAyB,EAAAmQ,IACAnQ,EAAAmQ,IAEAkM,CAGA,IAAAC,GAAA,GAAAC,MACAlc,GAAAic,GAAA5a,GAAA,OAAA8a,GACAnc,EAAAic,GAAA5a,GAAA,QAAA+a,GACAH,EAAAnM,IAAAiM,EAGA,QAAAI,KACAnc,EAAAJ,GAAAM,KAAA,OAAAL,KAAA,MAAAkc,GAGA,QAAAK,KACApc,EAAAJ,GAAAM,KAAA,OAAAL,KAAA,MAAAmc,GArBA,GAAAD,GACAC,EAAA9X,EAAA2X,QAuBAlP,GAAA,WACApE,MAGA5I,EAAAwL,OAAA,eAAAkR,EAAAC,GACAA,IAAAD,GACA9T,YnC0qFM,SAAUlS,EAAQD,EAASH,GoC5tFjCA,EAAA,IAEAuJ,QAAAnJ,OAAA,UACAoJ,UAAA,+BAAA+R,GACA,OACA9R,SAAA,IACAyF,SAAAlP,EAAA,IACA2Z,YAAA,EACAjQ,OACAua,YAAA,KAEAje,KAAA,SAAA0D,EAAAC,EAAAC,GACA,GAAAE,GAAAC,EAAAJ,IAGA,WAGA,GAFAD,EAAA8b,QAAA,EACA9b,EAAAua,YAAAva,EAAAua,aAAAjU,IAAAsW,KAAAhD,QACA5Z,EAAAua,aAAA,UAAAva,EAAAua,YAAAwB,UAEqB,CACrB,GAAAc,GAAAzc,EAAAG,KAAA,WACAsc,GAAApiB,KAAkCoG,IAAAT,EAAA2T,WAClC9T,EAAAsB,MAAA,WACAsb,EAAAzb,OAAA3G,IAAA,iBAAA4gB,UAAA,MACyB,WACzBwB,EAAAzb,OAAAC,QAAA,WAPArB,GAAA8b,QAAA,KAcA9b,EAAA8c,UAAA,SAAAlf,GACA,WAAAqB,OAAAqG,SAAA1H,KAAA/B,QAAA+B,EAAAqG,QAAA,kBpCuuFM,SAAUvN,EAAQD,GqCpwFxBoJ,QAAAnJ,OAAA,UACAiM,QAAA,kCAEApJ,KAAAwjB,aAAA,SAAA/lB,GACA,WAAAuH,IAAAvH,GAAA,GAAAA,EAAAsC,WAAA2K,QAAA,qBAAAvK,QAQAH,KAAAyjB,cAAA,SAAAhmB,GACA,WAAAuH,IAAAvH,GAAA,GAAAA,EAAAsC,WAAAI,SAGA,SAAAmX,KAAA7Z,IAQAuC,KAAA0jB,eAAA,SAAAjmB,EAAAkmB,EAAAzH,GACA,WAAAlX,IAAAvH,GAAA,GAAAA,EAAAsC,WAAAI,QAGA1C,EAAAsC,WAAAI,QAAAwjB,GAAAlmB,EAAAsC,WAAAI,QAAA+b,GAQAlc,KAAA4jB,iBAAA,SAAAnmB,EAAAkmB,EAAAzH,GACA,WAAAlX,IAAAvH,GAAA,GAAAA,EAAAsC,WAAAI,SAGA0jB,MAAApmB,IAAAwM,SAAAxM,EAAA,KAAAkmB,GAAA1Z,SAAAxM,EAAA,KAAAye,GAQAlc,KAAA8jB,YAAA,SAAArmB,EAAAsmB,EAAAC,EAAAC,GACA,OAAAA,MAAAF,MAGAtmB,IAAAwmB,EAAAF,GAAAC,IAGAC,EAAAF,GAAAC,IAAAvmB,IAQAuC,KAAAkkB,YAAA,SAAAzmB,GACA,WAAAuH,IAAAvH,GAAA,GAAAA,EAAAsC,WAAAI,SAGA0jB,MAAApmB,IAQAuC,KAAAmkB,gBAAA,SAAA1mB,GACA,WAAAuH,IAAAvH,GAAA,GAAAA,EAAAsC,WAAAI,UAGA,UAAAmX,KAAA7Z,IAOAuC,KAAAokB,uBAAA,SAAA3mB,GACA,WAAAuH,IAAAvH,GAAA,GAAAA,EAAAsC,WAAAI,UAGA0jB,MAAApmB,MAAA,IAOAuC,KAAAqkB,aAAA,SAAA5mB,GACA,OAAAuH,IAAAvH,GAAA,GAAAA,EAAAsC,WAAAI,OACA,QAEA,IAAAmkB,GAAA,qFACAC,EAAA,oDACAC,EAAA,2BACAC,EAAA,UACAC,EAAA,6BACAC,EAAA,oCACA,UAAAL,EAAAhN,KAAA7Z,IAAA8mB,EAAAjN,KAAA7Z,IAAA+mB,EAAAlN,KAAA7Z,IAAAgnB,EAAAnN,KAAA7Z,IAAAinB,EAAApN,KAAA7Z,IAAAknB,EAAArN,KAAA7Z,KAQAuC,KAAA4kB,oBAAA,SAAAnnB,GACA,WAAAuH,IAAAvH,GAAA,GAAAA,EAAAsC,WAAAI,SAGA,GAAA0kB,QAAA,2DACAvN,KAAA7Z,IAQAuC,KAAA8kB,mBAAA,SAAArnB,GACA,WAAAuH,IAAAvH,GAAA,GAAAA,EAAAsC,WAAAI,SAGA,GAAA0kB,QAAA,gCACAvN,KAAA7Z,IAQAuC,KAAA+kB,kBAAA,SAAAtnB,GACA,WAAAuH,IAAAvH,GAAA,GAAAA,EAAAsC,WAAAI,SAGA1C,EAAAunB,MAAA,mBAQAhlB,KAAAilB,cAAA,SAAAxnB,GACA,WAAAuH,IAAAvH,GAAA,GAAAA,EAAAsC,WAAAI,UAGA,8EACAmX,KAAA7Z,IAQAuC,KAAAklB,cAAA,SAAAznB,GACA,WAAAuH,IAAAvH,GAAA,GAAAA,EAAAsC,WAAAI,UAGA,iEACAmX,KAAA7Z,IAQAuC,KAAAmlB,kBAAA,SAAA1nB,GACA,WAAAuH,IAAAvH,GAAA,GAAAA,EAAAsC,WAAAI,UAGA,kBACAmX,KAAA7Z,IAQAuC,KAAAolB,WAAA,SAAA3nB,GACA,WAAAuH,IAAAvH,GAAA,GAAAA,EAAAsC,WAAAI,UAGA,2IACAmX,KAAA7Z,IAOAuC,KAAAqlB,eAAA,SAAA5nB,GACA,WAAAuH,IAAAvH,GAAA,GAAAA,EAAAsC,WAAAI,UAGA,cACAmX,KAAA7Z,IAQAuC,KAAAslB,mBAAA,SAAA7nB,GACA,WAAAuH,IAAAvH,GAAA,GAAAA,EAAAsC,WAAAI,UAGA,eACAmX,KAAA7Z,IAQAuC,KAAAulB,mBAAA,SAAA9nB,GACA,WAAAuH,IAAAvH,GAAA,GAAAA,EAAAsC,WAAAI,UAGA,YACAmX,KAAA7Z,IAQAuC,KAAAwlB,mBAAA,SAAA/nB,GACA,WAAAuH,IAAAvH,GAAA,GAAAA,EAAAsC,WAAAI,UAGA,gBACAmX,KAAA7Z,IAQAuC,KAAAylB,mBAAA,SAAAhoB,GACA,WAAAuH,IAAAvH,GAAA,GAAAA,EAAAsC,WAAAI,UAGA,YACAmX,KAAA7Z,MAQA6I,QAAAnJ,OAAA,UACAoJ,UAAA,6FAAA+C,EAAAoc,EAAAjS,EAAA/G,EAAAiZ,GACA,QAAAC,GAAAC,GACA,GAAAA,EAAAxe,SAAA,IAGA,QAAAwe,EAAAxe,SAAA,GAAAsO,QAGA,cAAAkQ,EAAAxe,SAAA,GAAAsO,QACAkQ,EAAAxe,SAEAue,EAAAC,EAAAxe,UAKA,QAAAye,GAAArf,EAAAsd,GACA,GAAAxT,GAAA7D,EAAA6D,OACA,IAAA9J,KAAAsf,mBAAA,CAGAtf,EAAAsf,mBAAAC,iBAAA,CACA,IACAC,GACA5T,EAFA6T,EAAAzf,EAAAsf,mBAAAI,MAAApC,EAGA,QAAAqC,KAAAF,GACAD,EAAAC,EAAAE,GACA/T,EAAA4T,EAAA5T,QACA/L,QAAAnD,QAAAkP,EAAAgU,YAAA,SAAAC,GACAA,EAAAjU,EAAAwO,aAIA,OADAtQ,GAAAvC,UACAuC,EAAAG,SAGA,OACAlK,SAAA,IACAgK,QAAA,UACAkJ,QAAA,WACA,OACA6M,IAAA,SAAAtC,EAAA4B,EAAAW,EAAAC,KACAC,KAAA,SAAAzC,EAAA4B,EAAAW,EAAAC,GACA,GAEA1C,GAFA4C,GAAA,EACAC,EAAAhB,EAAAC,EAEA,IAAAe,EAAA,CACA7C,EAAA6C,EAAAjgB,KAAA,OAEA,IAAAuf,GACAD,CAyBA,IAxBAhC,EAAA8B,qBACA9B,EAAA8B,uBAEA9B,EAAA8B,mBAAAI,QACAlC,EAAA8B,mBAAAI,UAEAlC,EAAA8B,mBAAAI,MAAApC,KACAE,EAAA8B,mBAAAI,MAAApC,OAEAE,EAAA8B,mBAAAD,cACA7B,EAAA8B,mBAAAD,YAAA,SAAA/B,GACA,MAAA+B,GAAA7B,EAAAF,IAEA4B,EAAAG,YAAA,SAAA/B,GACA,MAAA+B,GAAA7B,EAAAF,KAGAmC,EAAAjC,EAAA8B,mBAAAI,MAAApC,GACAmC,EAAAL,EAAAlf,KAAA,WACAuf,EAAAL,EAAAlf,KAAA,aAEAsf,EAAAC,EAAAL,EAAAlf,KAAA,SACAsf,EAAAvf,QAAAmf,EACAI,EAAA5T,QAAAoU,EACAD,EAAAK,kBAAA,CACA,GAAAC,GAAAlnB,KAAA8gB,MAAA8F,EAAAK,kBAAAnc,QAAA,UACAub,GAAAc,aAAAD,EAGA,GAAAC,GAAAd,EAAAc,aAEAC,EAAA,SAAAvpB,GACA,GAAAwpB,IAAA,CACA,IAAAF,GAAA,oBAAAA,YAAA1a,OAIA,OAHA6a,GACAC,EACAC,EACAhqB,EAAA,EAAAwD,EAAAmmB,EAAA5mB,OAA4E/C,EAAAwD,EAAOxD,IAEnF,GADA8pB,EAAAH,EAAA3pB,GACAupB,GAAA1C,EAAA8B,mBAAAC,gBAAA,CAGAmB,EAAAxB,EAAAuB,EAAAvpB,KACA,IAAAkM,KAEA,IADAA,EAAAxJ,KAAA5C,GACAypB,EAAArd,OAAA,gBAAAqd,GAAA,OAAAA,EAAArd,gBAAAwC,OACA,OAAAgQ,GAAA,EAAAE,EAAA2K,EAAArd,MAAA1J,OAAsFkc,EAAAE,EAAOF,IAC7FxS,EAAAxJ,KAAA6mB,EAAArd,MAAAwS,GAGAxS,GAAAxJ,KAAA4jB,GAEAmD,EAAAD,EAAA3hB,MAAAxF,KAAA6J,GACA,oBAEAud,EAIAX,EAAAY,aAAAH,EAAAD,UAAA,IAHAA,GAAA,EACAR,EAAAY,aAAAH,EAAAD,UAAA,IAIyC,oBAEzCG,EAAA1J,KAAA,WACA+I,EAAAY,aAAAH,EAAAD,UAAA,IAC6C,WAC7CA,GAAA,EACAR,EAAAY,aAAAH,EAAAD,UAAA,KAKA,MAAAA,GAAAxpB,MAAAuH,GAEAyhB,GAAAa,SAAAjnB,KAAA2mB,GACAP,EAAAJ,YAAAhmB,KAAA2mB,GAGAnB,EAAA1d,GAAA,iCACAwe,GAAA,YrCqxFM,SAAUxpB,EAAQD,GsC1qGxB,GAAA4R,GAAAxI,QAAAnJ,OAAA,SAEA2R,GAAA1I,OAAA,2BAAAkS,GACA,gBAAA7F,GACA,MAAA/I,GAAAyK,SAAA1B,GACA6F,EAAAgJ,YAAA7O,GAEAA,MAIA3D,EAAA1I,OAAA,4BACA,gBAAAyH,GACA,GAAAnE,EAAAkI,QAAA/D,GACA,QAEA,IAAAjL,GAAA8G,EAAA1C,KAAAqB,IAAAoN,OAAA8R,OAA6C1Z,QAC7C,cAAAjL,EACA,cAAAiL,EACAxQ,EAAA,OAAAwQ,EAAA,MAEA,UAAAA,EACAxQ,EAAA,OAAAwQ,EAAA,OAEAA,EAEAxQ,EAAA,OAAAwQ,EAAAjL,EAAAjF,SAIAmR,EAAA1I,OAAA,iCACA,gBAAAyH,EAAAmK,GACA,GAAAjL,IAAAxE,OAAAif,aAAA,CACA,SAAAxP,GAAA,IAAAA,EAGa,CACb,GAAAyP,GAAAzP,EAAAtN,QAAA,QAAAmG,aAKA,OAJA4W,GAAAnlB,QAAA,UAEAmlB,IAAA7a,UAAA,EAAA6a,EAAAnlB,QAAA,OAEAmlB,EARA,IAAA1a,IAAAxE,OAAAmf,cACA,SAWA,SAAA7Z,GAAA,IAAAA,IAAAd,IAAAxE,OAAAmf,cACA,QAEA,IAAA9kB,GAAA8G,EAAA1C,KAAAqB,IAAAoN,OAAA8R,OAA6C1Z,QAI7C,IAHA,MAAAjL,IACAA,EAAA8G,EAAA1C,KAAAqB,IAAAoN,OAAA8R,OAA6CI,UAAA9Z,KAE7C,MAAAjL,GACA,MAAAA,EAAAglB,WAAAhlB,EAAAglB,UAAAznB,OAAA,EACA,MAAAyC,GAAAglB,SAIA,QAAA/Z,EAAAuG,eACA,YACA,sBACA,SAEA,aACA,sBACA,SAEA,eACA,wBACA,SAEA,gBACA,yBACA,SAEA,kBACA,2BACA,SAEA,eACA,SAEA,eACA,eACA,YACA,YACA,SAEA,eACA,wBACA,SAEA,cACA,uBACA,SAEA,aACA,sBACA,SAEA,SACA,MAAAvG,OAKAiB,EAAA1I,OAAA,4BACA,gBAAAyH,GACA,GAAAjL,GAAA8G,EAAA1C,KAAAqB,IAAAoN,OAAA8R,OAA6C1Z,QAC7C,UAAAjL,GAAA,MAAAA,EAAA+P,KACA,MAAA/P,GAAA+P,IAEA,QAAA9E,GACA,YACA,kBACA,aACA,mBACA,eACA,uBACA,gBACA,yBACA,SACA,yBAKAiB,EAAA1I,OAAA,wBACA,gBAAA4b,GACA,MAAA3Z,KAAAkC,MAAAsd,cAAA7F,MAIAlT,EAAA1I,OAAA,8BACA,gBAAAsJ,GACA,OAAAA,GACA,OACA,eACA,QACA,OACA,eACA,QACA,eACA,SACA,aAKAZ,EAAA1I,OAAA,6BAAAkS,GACA,gBAAAwP,EAAAC,GACA,GAAAC,GAAA,EACAC,EAAA,EAEA,uBAAuC,WACvC,2BAAA3Q,KAAAyQ,IAAA,GAAAA,EAA6D,MAAAD,EAI7D,QAFAI,GAAAJ,EAAA9b,MAAA,iCAEA5O,EAAA,EAAuBA,EAAA8qB,EAAA/nB,OAAsB/C,GAAA,EAC7C0qB,IAAApd,QAAAwd,EAAA9qB,GAAA8qB,EAAA9qB,EAAA,GAIA,QAAAA,GAAA,EAAuBA,EAAA0qB,EAAA3nB,OAAgB/C,IAAA,CAEvC,MADA4qB,GAAAF,EAAAK,WAAA/qB,GAAA,UACA2qB,EAAA,GAAA3qB,GAAA0qB,EAAA3nB,OAAA,GAAA6nB,EAAAD,EAAA,GAEa,CACbE,GAAA,KACA,OAHAA,GAAAH,EAAAM,OAAAhrB,GAOA,OAAAA,GAAA,EAAuBA,EAAA8qB,EAAA/nB,OAAsB/C,GAAA,EAC7C6qB,IAAAvd,QAAAwd,EAAA9qB,EAAA,GAAA8qB,EAAA9qB,GAEA,OAAA6qB,OAIAnZ,EAAA1I,OAAA,uBACA,gBAAA4R,GACA,IAAAA,EACA,QAEA,IAAAxK,GAAAwK,EAAAtN,QAAA,OAEA,OADA8C,KAAAqD,iBAKA/B,EAAA1I,OAAA,wBACA,gBAAAiiB,EAAAC,GACA,SAAAD,EACA,SAGA,OAAAC,IACA,GAAAD,EACA,OAEAA,IAAA,eACAA,GAAA,KAAAA,EAAA,EACA,UAEAA,EAAAE,QAAA,UAGAlgB,IAAAkC,MAAAie,WAAAH,MAMAvZ,EAAA1I,OAAA,wBACA,gBAAAqiB,EAAAxT,GACA,SAAAwT,GAAA,IAAAA,EACA,QAEA,IAAAC,GAAA,GAAAte,KAOA,OALAse,GADApiB,QAAAqiB,OAAAF,GACAA,EAEA,GAAAre,MAAAqe,EAAA/d,QAAA,WAGA,MAAAuK,GAAA,IAAAA,EACAyT,EAAAzT,OAAA,uBAEAyT,EAAAzT,aAKAnG,EAAA1I,OAAA,4BACA,gBAAAqiB,GACA,GAAAhlB,GAAA,GACAmlB,EAAAH,EAAA/d,QAAA,oBACAme,EAAAD,EAAA5c,MAAA,KACA8c,GACAvM,EAAAsM,EAAA,GACAtrB,EAAAsrB,EAAA,KACAnrB,EAAAmrB,EAAA,GACAE,EAAAF,EAAA,GACAG,GAAAH,EAAA,GACAnqB,EAAAmqB,EAAA,IAEAI,EAAA,GAAA7e,MAAA0e,EAAAvM,EAAAuM,EAAAvrB,EAAAurB,EAAAprB,EAAAorB,EAAAC,EAAAD,EAAAE,GAAAF,EAAApqB,GACAwqB,EAAA,GAAA9e,MACA+e,EAAAF,EAAA5e,UAAA6e,EAAA7e,UAEAxM,EAAAoM,SAAAkf,EAAA,IAiBA,OAhBAtrB,GAAA,EACA4F,EAAA,OACS5F,GAAA,GAAAA,EAAA,GACT4F,EAAA5F,EAAA,MACSA,GAAA,IAAAA,EAAA,KACT4F,EAAAwG,SAAApM,EAAA,WACSA,GAAA,MAAAA,EAAA,MACT4F,EAAAwG,SAAApM,EAAA,aACSA,GAAA,OAAAA,EAAA,OACT4F,EAAAwG,SAAApM,EAAA,eACSA,GAAA,QAAAA,EAAA,QACT4F,EAAAwG,SAAApM,EAAA,mBACSA,GAAA,UACT4F,EAAAwG,SAAApM,EAAA,sBAGA4F,MtCkrGM,SAAUtG,EAAQD,GuCh8GxBwI,OAAAqH,IAAArH,OAAAqH,QAEAA,IAAAsW,KAAAtW,IAAAsW,SACAtW,IAAAqU,WAAArU,IAAAqU,eAEArU,IAAAqU,WAAAC,MAAA,SAAA+H,GACA,SAAAA,GAAA,IAAAA,EACA,QAEA,UAAA1f,EAAAxL,IAAA6O,IAAA,gBACA,4BAEA,IAAAsW,GAAAtW,IAAAsW,KAAAhD,OACA,KAAA3W,EAAA2H,QAAAgS,EAAAlC,gBAAA,GAAAkC,EAAAlC,cAAAhhB,OACA,QAEA,IAAAmM,GAAAS,IAAAxE,OAAA8gB,WAAAtc,IAAAxE,OAAA8gB,WAAAjV,cAAA,MAEA,QAAAgV,EAAA9mB,QAAA,KACA,MAAgI,OAAhIoH,EAAA1C,KAAAqc,EAAAlC,cAAA,SAAAviB,GAA2D,MAAAA,GAAAwV,gBAAA9H,EAAA8c,EAAAhV,eAI3D,QAFAwF,GAAAwP,EAAApd,MAAA,KACAoB,EAAA,EACAhQ,EAAA,EAAuBA,EAAAwc,EAAAzZ,OAAkB/C,IACiF,MAA1HsM,EAAA1C,KAAAqc,EAAAlC,cAAA,SAAAmI,GAA0D,MAAAA,GAAAlV,gBAAA9H,EAAAsN,EAAAxc,GAAAgX,iBAC1DhH,GAGA,OAAAA,IAAAwM,EAAAzZ,SvCw8GM,SAAUhD,EAAQD,GwCp+GxBwI,OAAA2C,IAAAkC,MAAA7E,OAAA2C,IAAAkC,UAEAlC,IAAAkC,MAAAgf,eAAA,SAAAd,EAAAe,EAAAC,EAAAC,GACA,IAAAjB,EACA,QAEA,IAAAhlB,GAAA,GACAmlB,EAAAH,EAAA/d,QAAA,oBACAme,EAAAD,EAAA5c,MAAA,KACA8c,GACAvM,EAAAsM,EAAA,GACAtrB,EAAAsrB,EAAA,KACAnrB,EAAAmrB,EAAA,GACAE,EAAAF,EAAA,GACAG,GAAAH,EAAA,GACAnqB,EAAAmqB,EAAA,IAEAI,EAAA,GAAA7e,MAAA0e,EAAAvM,EAAAuM,EAAAvrB,EAAAurB,EAAAprB,EAAAorB,EAAAC,EAAAD,EAAAE,GAAAF,EAAApqB,GACAwqB,EAAA,GAAA9e,MACA+e,EAAAF,EAAA5e,UAAA6e,EAAA7e,UAEAxM,EAAAoM,SAAAkf,EAAA,IACA,OAAAM,IAAA5rB,EAAA4rB,EACA,IAEA5rB,EAAA,EACA4F,EAAA,GACK5F,GAAA,GAAAA,EAAA,GACL4F,EAAA5F,EAAA,MACKA,GAAA,IAAAA,EAAA,KACL4F,EAAAwG,SAAApM,EAAA,UACKA,GAAA,MAAAA,EAAA,eAAA2rB,GACL/lB,EAAAwG,SAAApM,EAAA,WACA,OAAA6rB,IAEAjmB,GAAAwG,SAAApM,EAAA,eAEA4F,GAAA,KACK5F,GAAA,OAAAA,EAAA,OACL4F,EAAAwG,SAAApM,EAAA,cACKA,GAAA,QAAAA,EAAA,QACL4F,EAAAwG,SAAApM,EAAA,kBACKA,GAAA,UACL4F,EAAAwG,SAAApM,EAAA,qBAGA4F,KxC2+GM,SAAUtG,EAAQD,EAASH,GyCzhHjCG,EAAAC,EAAAD,QAAAH,EAAA,OAAAiI,IAKA9H,EAAAmD,MAAAlD,EAAAC,EAAA,8hBAAqjB,MzCkiH/iB,SAAUD,EAAQD,EAASH,G0CviHjCG,EAAAC,EAAAD,QAAAH,EAAA,OAAAiI,IAKA9H,EAAAmD,MAAAlD,EAAAC,EAAA,4EAAmG,M1CgjH7F,SAAUD,EAAQD,EAASH,G2CrjHjCG,EAAAC,EAAAD,QAAAH,EAAA,OAAAiI,IAKA9H,EAAAmD,MAAAlD,EAAAC,EAAA,+BAAsD,M3C8jHhD,SAAUD,EAAQD,EAASH,G4CnkHjCG,EAAAC,EAAAD,QAAAH,EAAA,OAAAiI,IAKA9H,EAAAmD,MAAAlD,EAAAC,EAAA,86CAAu8C,M5C4kHj8C,SAAUD,EAAQD,EAASH,G6CjlHjCG,EAAAC,EAAAD,QAAAH,EAAA,OAAAiI,IAKA9H,EAAAmD,MAAAlD,EAAAC,EAAA,8eAAqgB,M7C0lH/f,SAAUD,EAAQD,EAASH,G8C/lHjCG,EAAAC,EAAAD,QAAAH,EAAA,OAAAiI,IAKA9H,EAAAmD,MAAAlD,EAAAC,EAAA,gpBAAuqB,M9CwmHjqB,SAAUD,EAAQD,EAASH,G+C7mHjCG,EAAAC,EAAAD,QAAAH,EAAA,OAAAiI,IAKA9H,EAAAmD,MAAAlD,EAAAC,EAAA,wvBAA+wB,M/CsnHzwB,SAAUD,EAAQD,EAASH,GgD3nHjCG,EAAAC,EAAAD,QAAAH,EAAA,OAAAiI,IAKA9H,EAAAmD,MAAAlD,EAAAC,EAAA,2rCAAotC,MhDooH9sC,SAAUD,EAAQD,EAASH,GiDzoHjCG,EAAAC,EAAAD,QAAAH,EAAA,OAAAiI,IAKA9H,EAAAmD,MAAAlD,EAAAC,EAAA,0oDAAiqD,MjDkpH3pD,SAAUD,EAAQD,EAASH,GkDvpHjCG,EAAAC,EAAAD,QAAAH,EAAA,OAAAiI,IAKA9H,EAAAmD,MAAAlD,EAAAC,EAAA,kXAAyY,MlDgqHnY,SAAUD,EAAQD,EAASH,GmDrqHjCG,EAAAC,EAAAD,QAAAH,EAAA,OAAAiI,IAKA9H,EAAAmD,MAAAlD,EAAAC,EAAA,swCAA6xC,MnD8qHvxC,SAAUD,EAAQD,EAASH,GoDnrHjCG,EAAAC,EAAAD,QAAAH,EAAA,OAAAiI,IAKA9H,EAAAmD,MAAAlD,EAAAC,EAAA,unBAAgpB,MpD4rH1oB,SAAUD,EAAQD,EAASH,GqDjsHjCG,EAAAC,EAAAD,QAAAH,EAAA,OAAAiI,IAKA9H,EAAAmD,MAAAlD,EAAAC,EAAA,i0DAAw1D,MrD0sHl1D,SAAUD,EAAQD,EAASH,GsD/sHjCG,EAAAC,EAAAD,QAAAH,EAAA,OAAAiI,IAKA9H,EAAAmD,MAAAlD,EAAAC,EAAA,+CAAsE,MtDwtHhE,SAAUD,EAAQD,EAASH,GuD7tHjCG,EAAAC,EAAAD,QAAAH,EAAA,OAAAiI,IAKA9H,EAAAmD,MAAAlD,EAAAC,EAAA,w+CAA+/C,MvDsuHz/C,SAAUD,EAAQD,GwD3uHxBC,EAAAD,QAAA,krBxDivHM,SAAUC,EAAQD,GyDjvHxBC,EAAAD,QAAA,+sBzDuvHM,SAAUC,EAAQD,G0DvvHxBC,EAAAD,QAAA,ypB1D6vHM,SAAUC,EAAQD,G2D7vHxBC,EAAAD,QAAA,oT3DmwHM,SAAUC,EAAQD,G4DnwHxBC,EAAAD,QAAA,2P5DywHM,SAAUC,EAAQD,G6DzwHxBC,EAAAD,QAAA,wK7D+wHM,SAAUC,EAAQD,G8D/wHxBC,EAAAD,QAAA,yO9DqxHM,SAAUC,EAAQD,G+DrxHxBC,EAAAD,QAAA,kyD/D2xHM,SAAUC,EAAQD,EAASH,GgExxHjC,GAAA+B,GAAA/B,EAAA,GACA,iBAAA+B,SAAA3B,EAAAC,EAAA0B,EAAA,KAEA,IAEA0B,KACAA,GAAAkD,cAHAA,EAKA3G,GAAA,GAAA+B,EAAA0B,EACA1B,GAAA6qB,SAAAxsB,EAAAD,QAAA4B,EAAA6qB,ShE8yHM,SAAUxsB,EAAQD,EAASH,GiEvzHjC,GAAA+B,GAAA/B,EAAA,GACA,iBAAA+B,SAAA3B,EAAAC,EAAA0B,EAAA,KAEA,IAEA0B,KACAA,GAAAkD,cAHAA,EAKA3G,GAAA,GAAA+B,EAAA0B,EACA1B,GAAA6qB,SAAAxsB,EAAAD,QAAA4B,EAAA6qB,SjE60HM,SAAUxsB,EAAQD,EAASH,GkEt1HjC,GAAA+B,GAAA/B,EAAA,GACA,iBAAA+B,SAAA3B,EAAAC,EAAA0B,EAAA,KAEA,IAEA0B,KACAA,GAAAkD,cAHAA,EAKA3G,GAAA,GAAA+B,EAAA0B,EACA1B,GAAA6qB,SAAAxsB,EAAAD,QAAA4B,EAAA6qB,SlE42HM,SAAUxsB,EAAQD,EAASH,GmEr3HjC,GAAA+B,GAAA/B,EAAA,GACA,iBAAA+B,SAAA3B,EAAAC,EAAA0B,EAAA,KAEA,IAEA0B,KACAA,GAAAkD,cAHAA,EAKA3G,GAAA,GAAA+B,EAAA0B,EACA1B,GAAA6qB,SAAAxsB,EAAAD,QAAA4B,EAAA6qB,SnE24HM,SAAUxsB,EAAQD,EAASH,GoEp5HjC,GAAA+B,GAAA/B,EAAA,GACA,iBAAA+B,SAAA3B,EAAAC,EAAA0B,EAAA,KAEA,IAEA0B,KACAA,GAAAkD,cAHAA,EAKA3G,GAAA,GAAA+B,EAAA0B,EACA1B,GAAA6qB,SAAAxsB,EAAAD,QAAA4B,EAAA6qB,SpE06HM,SAAUxsB,EAAQD,EAASH,GqEn7HjC,GAAA+B,GAAA/B,EAAA,GACA,iBAAA+B,SAAA3B,EAAAC,EAAA0B,EAAA,KAEA,IAEA0B,KACAA,GAAAkD,cAHAA,EAKA3G,GAAA,GAAA+B,EAAA0B,EACA1B,GAAA6qB,SAAAxsB,EAAAD,QAAA4B,EAAA6qB,SrEy8HM,SAAUxsB,EAAQD,EAASH,GsEl9HjC,GAAA+B,GAAA/B,EAAA,GACA,iBAAA+B,SAAA3B,EAAAC,EAAA0B,EAAA,KAEA,IAEA0B,KACAA,GAAAkD,cAHAA,EAKA3G,GAAA,GAAA+B,EAAA0B,EACA1B,GAAA6qB,SAAAxsB,EAAAD,QAAA4B,EAAA6qB,StEw+HM,SAAUxsB,EAAQD,EAASH,GuEj/HjC,GAAA+B,GAAA/B,EAAA,GACA,iBAAA+B,SAAA3B,EAAAC,EAAA0B,EAAA,KAEA,IAEA0B,KACAA,GAAAkD,cAHAA,EAKA3G,GAAA,GAAA+B,EAAA0B,EACA1B,GAAA6qB,SAAAxsB,EAAAD,QAAA4B,EAAA6qB,SvEugIM,SAAUxsB,EAAQD,EAASH,GwEhhIjC,GAAA+B,GAAA/B,EAAA,GACA,iBAAA+B,SAAA3B,EAAAC,EAAA0B,EAAA,KAEA,IAEA0B,KACAA,GAAAkD,cAHAA,EAKA3G,GAAA,GAAA+B,EAAA0B,EACA1B,GAAA6qB,SAAAxsB,EAAAD,QAAA4B,EAAA6qB,SxEsiIM,SAAUxsB,EAAQD,EAASH,GyE/iIjC,GAAA+B,GAAA/B,EAAA,GACA,iBAAA+B,SAAA3B,EAAAC,EAAA0B,EAAA,KAEA,IAEA0B,KACAA,GAAAkD,cAHAA,EAKA3G,GAAA,GAAA+B,EAAA0B,EACA1B,GAAA6qB,SAAAxsB,EAAAD,QAAA4B,EAAA6qB,SzEqkIM,SAAUxsB,EAAQD,EAASH,G0E9kIjC,GAAA+B,GAAA/B,EAAA,GACA,iBAAA+B,SAAA3B,EAAAC,EAAA0B,EAAA,KAEA,IAEA0B,KACAA,GAAAkD,cAHAA,EAKA3G,GAAA,GAAA+B,EAAA0B,EACA1B,GAAA6qB,SAAAxsB,EAAAD,QAAA4B,EAAA6qB,S1EomIM,SAAUxsB,EAAQD,EAASH,G2E7mIjC,GAAA+B,GAAA/B,EAAA,GACA,iBAAA+B,SAAA3B,EAAAC,EAAA0B,EAAA,KAEA,IAEA0B,KACAA,GAAAkD,cAHAA,EAKA3G,GAAA,GAAA+B,EAAA0B,EACA1B,GAAA6qB,SAAAxsB,EAAAD,QAAA4B,EAAA6qB,S3EmoIM,SAAUxsB,EAAQD,EAASH,G4E5oIjC,GAAA+B,GAAA/B,EAAA,GACA,iBAAA+B,SAAA3B,EAAAC,EAAA0B,EAAA,KAEA,IAEA0B,KACAA,GAAAkD,cAHAA,EAKA3G,GAAA,GAAA+B,EAAA0B,EACA1B,GAAA6qB,SAAAxsB,EAAAD,QAAA4B,EAAA6qB,S5EkqIM,SAAUxsB,EAAQD,EAASH,G6E3qIjC,GAAA+B,GAAA/B,EAAA,GACA,iBAAA+B,SAAA3B,EAAAC,EAAA0B,EAAA,KAEA,IAEA0B,KACAA,GAAAkD,cAHAA,EAKA3G,GAAA,GAAA+B,EAAA0B,EACA1B,GAAA6qB,SAAAxsB,EAAAD,QAAA4B,EAAA6qB,S7EisIM,SAAUxsB,EAAQD,EAASH,G8E1sIjC,GAAA+B,GAAA/B,EAAA,GACA,iBAAA+B,SAAA3B,EAAAC,EAAA0B,EAAA,KAEA,IAEA0B,KACAA,GAAAkD,cAHAA,EAKA3G,GAAA,GAAA+B,EAAA0B,EACA1B,GAAA6qB,SAAAxsB,EAAAD,QAAA4B,EAAA6qB,S9EguIM,SAAUxsB,EAAQD,G+E9tIxBC,EAAAD,QAAA,SAAAgE,GAEA,GAAA6K,GAAA,mBAAArG,gBAAAqG,QAEA,KAAAA,EACA,SAAArK,OAAA,mCAIA,KAAAR,GAAA,gBAAAA,GACA,MAAAA,EAGA,IAAA0oB,GAAA7d,EAAA8d,SAAA,KAAA9d,EAAA+d,KACAC,EAAAH,EAAA7d,EAAAie,SAAAtf,QAAA,gBA2DA,OA/BAxJ,GAAAwJ,QAAA,+DAAAuf,EAAAC,GAEA,GAAAC,GAAAD,EACAE,OACA1f,QAAA,oBAAA7M,EAAAwsB,GAAwC,MAAAA,KACxC3f,QAAA,oBAAA7M,EAAAwsB,GAAwC,MAAAA,IAGxC,mDAAA/S,KAAA6S,GACA,MAAAF,EAIA,IAAAK,EAcA,OAVAA,GAFA,IAAAH,EAAA7nB,QAAA,MAEA6nB,EACG,IAAAA,EAAA7nB,QAAA,KAEHsnB,EAAAO,EAGAJ,EAAAI,EAAAzf,QAAA,YAIA,OAAA9K,KAAAC,UAAAyqB,GAAA,Q/EwvIM,SAAUntB,EAAQD,GgF30IxBC,EAAAD,QAAA,i9ChFi1IM,SAAUC,EAAQD,GiFj1IxBC,EAAAD,QAAA,sfjFu1IM,SAAUC,EAAQD,GkFv1IxBC,EAAAD,QAAA,6TlF61IM,SAAUC,EAAQD,EAASH,GmF71IjC,SAAA2I,OAAA2C,IACA,qBAEA3C,QAAA2C,IAAAC,GAAA5C,OAAA2C,IAAAC,OAEAhC,QAAAnJ,OAAA,wBACAkM,KACA,QACA,SAAAsT,GACAtU,IAAAC,GAAAqU,WAIA5f,EAAA,GACAA,EAAA,GACAA,EAAA,GACAA,EAAA,GACAA,EAAA,GAEAA,EAAA,IAGAA,EAAA,IACAA,EAAA,IAEAA,EAAA,GACAA,EAAA,IACAA,EAAA,IACAA,EAAA,GACAA,EAAA,IACAA,EAAA,IACAA,EAAA,IACAA,EAAA,IACAA,EAAA,IACAA,EAAA,IACAA,EAAA,IACAA,EAAA,IACAA,EAAA,IACAA,EAAA,IACAA,EAAA,IACAA,EAAA,IACAA,EAAA,IACAA,EAAA,IACAA,EAAA,IACAA,EAAA,GACAA,EAAA,IACAA,EAAA,IACAA,EAAA,IACAA,EAAA,IACAA,EAAA,IACAA,EAAA,IACAA,EAAA,IACAA,EAAA,IACAA,EAAA", "file": "mam-ng.min.js", "sourcesContent": ["/******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// identity function for calling harmony imports with the correct context\n/******/ \t__webpack_require__.i = function(value) { return value; };\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, {\n/******/ \t\t\t\tconfigurable: false,\n/******/ \t\t\t\tenumerable: true,\n/******/ \t\t\t\tget: getter\n/******/ \t\t\t});\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 81);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports) {\n\n/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor Tobias Koppers @sokra\n*/\n// css base code, injected by the css-loader\nmodule.exports = function(useSourceMap) {\n\tvar list = [];\n\n\t// return the list of modules as css string\n\tlist.toString = function toString() {\n\t\treturn this.map(function (item) {\n\t\t\tvar content = cssWithMappingToString(item, useSourceMap);\n\t\t\tif(item[2]) {\n\t\t\t\treturn \"@media \" + item[2] + \"{\" + content + \"}\";\n\t\t\t} else {\n\t\t\t\treturn content;\n\t\t\t}\n\t\t}).join(\"\");\n\t};\n\n\t// import a list of modules into the list\n\tlist.i = function(modules, mediaQuery) {\n\t\tif(typeof modules === \"string\")\n\t\t\tmodules = [[null, modules, \"\"]];\n\t\tvar alreadyImportedModules = {};\n\t\tfor(var i = 0; i < this.length; i++) {\n\t\t\tvar id = this[i][0];\n\t\t\tif(typeof id === \"number\")\n\t\t\t\talreadyImportedModules[id] = true;\n\t\t}\n\t\tfor(i = 0; i < modules.length; i++) {\n\t\t\tvar item = modules[i];\n\t\t\t// skip already imported module\n\t\t\t// this implementation is not 100% perfect for weird media query combinations\n\t\t\t//  when a module is imported multiple times with different media queries.\n\t\t\t//  I hope this will never occur (Hey this way we have smaller bundles)\n\t\t\tif(typeof item[0] !== \"number\" || !alreadyImportedModules[item[0]]) {\n\t\t\t\tif(mediaQuery && !item[2]) {\n\t\t\t\t\titem[2] = mediaQuery;\n\t\t\t\t} else if(mediaQuery) {\n\t\t\t\t\titem[2] = \"(\" + item[2] + \") and (\" + mediaQuery + \")\";\n\t\t\t\t}\n\t\t\t\tlist.push(item);\n\t\t\t}\n\t\t}\n\t};\n\treturn list;\n};\n\nfunction cssWithMappingToString(item, useSourceMap) {\n\tvar content = item[1] || '';\n\tvar cssMapping = item[3];\n\tif (!cssMapping) {\n\t\treturn content;\n\t}\n\n\tif (useSourceMap && typeof btoa === 'function') {\n\t\tvar sourceMapping = toComment(cssMapping);\n\t\tvar sourceURLs = cssMapping.sources.map(function (source) {\n\t\t\treturn '/*# sourceURL=' + cssMapping.sourceRoot + source + ' */'\n\t\t});\n\n\t\treturn [content].concat(sourceURLs).concat([sourceMapping]).join('\\n');\n\t}\n\n\treturn [content].join('\\n');\n}\n\n// Adapted from convert-source-map (MIT)\nfunction toComment(sourceMap) {\n\t// eslint-disable-next-line no-undef\n\tvar base64 = btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap))));\n\tvar data = 'sourceMappingURL=data:application/json;charset=utf-8;base64,' + base64;\n\n\treturn '/*# ' + data + ' */';\n}\n\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor Tobias Koppers @sokra\n*/\n\nvar stylesInDom = {};\n\nvar\tmemoize = function (fn) {\n\tvar memo;\n\n\treturn function () {\n\t\tif (typeof memo === \"undefined\") memo = fn.apply(this, arguments);\n\t\treturn memo;\n\t};\n};\n\nvar isOldIE = memoize(function () {\n\t// Test for IE <= 9 as proposed by Browserhacks\n\t// @see http://browserhacks.com/#hack-e71d8692f65334173fee715c222cb805\n\t// Tests for existence of standard globals is to allow style-loader\n\t// to operate correctly into non-standard environments\n\t// @see https://github.com/webpack-contrib/style-loader/issues/177\n\treturn window && document && document.all && !window.atob;\n});\n\nvar getElement = (function (fn) {\n\tvar memo = {};\n\n\treturn function(selector) {\n\t\tif (typeof memo[selector] === \"undefined\") {\n\t\t\tmemo[selector] = fn.call(this, selector);\n\t\t}\n\n\t\treturn memo[selector]\n\t};\n})(function (target) {\n\treturn document.querySelector(target)\n});\n\nvar singleton = null;\nvar\tsingletonCounter = 0;\nvar\tstylesInsertedAtTop = [];\n\nvar\tfixUrls = __webpack_require__(77);\n\nmodule.exports = function(list, options) {\n\tif (typeof DEBUG !== \"undefined\" && DEBUG) {\n\t\tif (typeof document !== \"object\") throw new Error(\"The style-loader cannot be used in a non-browser environment\");\n\t}\n\n\toptions = options || {};\n\n\toptions.attrs = typeof options.attrs === \"object\" ? options.attrs : {};\n\n\t// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n\t// tags it will allow on a page\n\tif (!options.singleton) options.singleton = isOldIE();\n\n\t// By default, add <style> tags to the <head> element\n\tif (!options.insertInto) options.insertInto = \"head\";\n\n\t// By default, add <style> tags to the bottom of the target\n\tif (!options.insertAt) options.insertAt = \"bottom\";\n\n\tvar styles = listToStyles(list, options);\n\n\taddStylesToDom(styles, options);\n\n\treturn function update (newList) {\n\t\tvar mayRemove = [];\n\n\t\tfor (var i = 0; i < styles.length; i++) {\n\t\t\tvar item = styles[i];\n\t\t\tvar domStyle = stylesInDom[item.id];\n\n\t\t\tdomStyle.refs--;\n\t\t\tmayRemove.push(domStyle);\n\t\t}\n\n\t\tif(newList) {\n\t\t\tvar newStyles = listToStyles(newList, options);\n\t\t\taddStylesToDom(newStyles, options);\n\t\t}\n\n\t\tfor (var i = 0; i < mayRemove.length; i++) {\n\t\t\tvar domStyle = mayRemove[i];\n\n\t\t\tif(domStyle.refs === 0) {\n\t\t\t\tfor (var j = 0; j < domStyle.parts.length; j++) domStyle.parts[j]();\n\n\t\t\t\tdelete stylesInDom[domStyle.id];\n\t\t\t}\n\t\t}\n\t};\n};\n\nfunction addStylesToDom (styles, options) {\n\tfor (var i = 0; i < styles.length; i++) {\n\t\tvar item = styles[i];\n\t\tvar domStyle = stylesInDom[item.id];\n\n\t\tif(domStyle) {\n\t\t\tdomStyle.refs++;\n\n\t\t\tfor(var j = 0; j < domStyle.parts.length; j++) {\n\t\t\t\tdomStyle.parts[j](item.parts[j]);\n\t\t\t}\n\n\t\t\tfor(; j < item.parts.length; j++) {\n\t\t\t\tdomStyle.parts.push(addStyle(item.parts[j], options));\n\t\t\t}\n\t\t} else {\n\t\t\tvar parts = [];\n\n\t\t\tfor(var j = 0; j < item.parts.length; j++) {\n\t\t\t\tparts.push(addStyle(item.parts[j], options));\n\t\t\t}\n\n\t\t\tstylesInDom[item.id] = {id: item.id, refs: 1, parts: parts};\n\t\t}\n\t}\n}\n\nfunction listToStyles (list, options) {\n\tvar styles = [];\n\tvar newStyles = {};\n\n\tfor (var i = 0; i < list.length; i++) {\n\t\tvar item = list[i];\n\t\tvar id = options.base ? item[0] + options.base : item[0];\n\t\tvar css = item[1];\n\t\tvar media = item[2];\n\t\tvar sourceMap = item[3];\n\t\tvar part = {css: css, media: media, sourceMap: sourceMap};\n\n\t\tif(!newStyles[id]) styles.push(newStyles[id] = {id: id, parts: [part]});\n\t\telse newStyles[id].parts.push(part);\n\t}\n\n\treturn styles;\n}\n\nfunction insertStyleElement (options, style) {\n\tvar target = getElement(options.insertInto)\n\n\tif (!target) {\n\t\tthrow new Error(\"Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.\");\n\t}\n\n\tvar lastStyleElementInsertedAtTop = stylesInsertedAtTop[stylesInsertedAtTop.length - 1];\n\n\tif (options.insertAt === \"top\") {\n\t\tif (!lastStyleElementInsertedAtTop) {\n\t\t\ttarget.insertBefore(style, target.firstChild);\n\t\t} else if (lastStyleElementInsertedAtTop.nextSibling) {\n\t\t\ttarget.insertBefore(style, lastStyleElementInsertedAtTop.nextSibling);\n\t\t} else {\n\t\t\ttarget.appendChild(style);\n\t\t}\n\t\tstylesInsertedAtTop.push(style);\n\t} else if (options.insertAt === \"bottom\") {\n\t\ttarget.appendChild(style);\n\t} else {\n\t\tthrow new Error(\"Invalid value for parameter 'insertAt'. Must be 'top' or 'bottom'.\");\n\t}\n}\n\nfunction removeStyleElement (style) {\n\tif (style.parentNode === null) return false;\n\tstyle.parentNode.removeChild(style);\n\n\tvar idx = stylesInsertedAtTop.indexOf(style);\n\tif(idx >= 0) {\n\t\tstylesInsertedAtTop.splice(idx, 1);\n\t}\n}\n\nfunction createStyleElement (options) {\n\tvar style = document.createElement(\"style\");\n\n\toptions.attrs.type = \"text/css\";\n\n\taddAttrs(style, options.attrs);\n\tinsertStyleElement(options, style);\n\n\treturn style;\n}\n\nfunction createLinkElement (options) {\n\tvar link = document.createElement(\"link\");\n\n\toptions.attrs.type = \"text/css\";\n\toptions.attrs.rel = \"stylesheet\";\n\n\taddAttrs(link, options.attrs);\n\tinsertStyleElement(options, link);\n\n\treturn link;\n}\n\nfunction addAttrs (el, attrs) {\n\tObject.keys(attrs).forEach(function (key) {\n\t\tel.setAttribute(key, attrs[key]);\n\t});\n}\n\nfunction addStyle (obj, options) {\n\tvar style, update, remove, result;\n\n\t// If a transform function was defined, run it on the css\n\tif (options.transform && obj.css) {\n\t    result = options.transform(obj.css);\n\n\t    if (result) {\n\t    \t// If transform returns a value, use that instead of the original css.\n\t    \t// This allows running runtime transformations on the css.\n\t    \tobj.css = result;\n\t    } else {\n\t    \t// If the transform function returns a falsy value, don't add this css.\n\t    \t// This allows conditional loading of css\n\t    \treturn function() {\n\t    \t\t// noop\n\t    \t};\n\t    }\n\t}\n\n\tif (options.singleton) {\n\t\tvar styleIndex = singletonCounter++;\n\n\t\tstyle = singleton || (singleton = createStyleElement(options));\n\n\t\tupdate = applyToSingletonTag.bind(null, style, styleIndex, false);\n\t\tremove = applyToSingletonTag.bind(null, style, styleIndex, true);\n\n\t} else if (\n\t\tobj.sourceMap &&\n\t\ttypeof URL === \"function\" &&\n\t\ttypeof URL.createObjectURL === \"function\" &&\n\t\ttypeof URL.revokeObjectURL === \"function\" &&\n\t\ttypeof Blob === \"function\" &&\n\t\ttypeof btoa === \"function\"\n\t) {\n\t\tstyle = createLinkElement(options);\n\t\tupdate = updateLink.bind(null, style, options);\n\t\tremove = function () {\n\t\t\tremoveStyleElement(style);\n\n\t\t\tif(style.href) URL.revokeObjectURL(style.href);\n\t\t};\n\t} else {\n\t\tstyle = createStyleElement(options);\n\t\tupdate = applyToTag.bind(null, style);\n\t\tremove = function () {\n\t\t\tremoveStyleElement(style);\n\t\t};\n\t}\n\n\tupdate(obj);\n\n\treturn function updateStyle (newObj) {\n\t\tif (newObj) {\n\t\t\tif (\n\t\t\t\tnewObj.css === obj.css &&\n\t\t\t\tnewObj.media === obj.media &&\n\t\t\t\tnewObj.sourceMap === obj.sourceMap\n\t\t\t) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tupdate(obj = newObj);\n\t\t} else {\n\t\t\tremove();\n\t\t}\n\t};\n}\n\nvar replaceText = (function () {\n\tvar textStore = [];\n\n\treturn function (index, replacement) {\n\t\ttextStore[index] = replacement;\n\n\t\treturn textStore.filter(Boolean).join('\\n');\n\t};\n})();\n\nfunction applyToSingletonTag (style, index, remove, obj) {\n\tvar css = remove ? \"\" : obj.css;\n\n\tif (style.styleSheet) {\n\t\tstyle.styleSheet.cssText = replaceText(index, css);\n\t} else {\n\t\tvar cssNode = document.createTextNode(css);\n\t\tvar childNodes = style.childNodes;\n\n\t\tif (childNodes[index]) style.removeChild(childNodes[index]);\n\n\t\tif (childNodes.length) {\n\t\t\tstyle.insertBefore(cssNode, childNodes[index]);\n\t\t} else {\n\t\t\tstyle.appendChild(cssNode);\n\t\t}\n\t}\n}\n\nfunction applyToTag (style, obj) {\n\tvar css = obj.css;\n\tvar media = obj.media;\n\n\tif(media) {\n\t\tstyle.setAttribute(\"media\", media)\n\t}\n\n\tif(style.styleSheet) {\n\t\tstyle.styleSheet.cssText = css;\n\t} else {\n\t\twhile(style.firstChild) {\n\t\t\tstyle.removeChild(style.firstChild);\n\t\t}\n\n\t\tstyle.appendChild(document.createTextNode(css));\n\t}\n}\n\nfunction updateLink (link, options, obj) {\n\tvar css = obj.css;\n\tvar sourceMap = obj.sourceMap;\n\n\t/*\n\t\tIf convertToAbsoluteUrls isn't defined, but sourcemaps are enabled\n\t\tand there is no publicPath defined then lets turn convertToAbsoluteUrls\n\t\ton by default.  Otherwise default to the convertToAbsoluteUrls option\n\t\tdirectly\n\t*/\n\tvar autoFixUrls = options.convertToAbsoluteUrls === undefined && sourceMap;\n\n\tif (options.convertToAbsoluteUrls || autoFixUrls) {\n\t\tcss = fixUrls(css);\n\t}\n\n\tif (sourceMap) {\n\t\t// http://stackoverflow.com/a/26603875\n\t\tcss += \"\\n/*# sourceMappingURL=data:application/json;base64,\" + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + \" */\";\n\t}\n\n\tvar blob = new Blob([css], { type: \"text/css\" });\n\n\tvar oldSrc = link.href;\n\n\tlink.href = URL.createObjectURL(blob);\n\n\tif(oldSrc) URL.revokeObjectURL(oldSrc);\n}\n\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports) {\n\nangular.module('mam-ng')\r\n    .directive('mamDropdown', function () {\r\n        return {\r\n            restrict: 'A',\r\n            link: function (scope, element, attr) {scope.mode = 'click';\r\n                var $e = $(element);\r\n                var toggle = $e.find('.dropdown-toggle');\r\n                var menu = $e.find('.dropdown-menu');\r\n                menu.hide();\r\n                var isShow = false;\r\n\r\n                var timer = 0;\r\n                //var menuHeight = menu.height();\r\n\r\n                if (toggle.parent().css('position') == 'static') {//当div高度固定很短的时候显示不出来时\r\n                    var top = toggle.position().top + toggle.outerHeight() - 2;\r\n                    var left = toggle.position().left;\r\n                    menu.css('top', top);\r\n                    menu.css('left', left);\r\n                } else {\r\n                    $e.css('position', 'relative');\r\n                }\r\n\r\n                var changeMenu = function () {\r\n                    isShow = !isShow;\r\n                    toggle.toggleClass(\"dropdown-toggle-active\");\r\n                    menu.not(menu).stop().slideUp(200).parent().find(\".dropdown-toggle\"); //.removeClass(\"dropdown-toggle-active\");\r\n                    menu.stop().slideToggle(200);\r\n                }\r\n\r\n                if (scope.mode == 'hover') {\r\n                    //比较高度，是为了防止在关闭动画的时候display不为none,也进行切换的问题\r\n                    $e.hover(function () {\r\n                        clearTimeout(timer);\r\n                        if (!isShow) {\r\n                            changeMenu();\r\n                        }\r\n                        return false;\r\n                    }, function () {\r\n                        clearTimeout(timer);\r\n                        if (isShow) {\r\n                            timer = setTimeout(changeMenu, 250);//添加延迟关闭\r\n                        }\r\n                        return false;\r\n                    })\r\n                    menu.hover(function () { \r\n                        clearTimeout(timer);\r\n                    }, function () {\r\n                        clearTimeout(timer);\r\n                        if (isShow) {\r\n                            timer = setTimeout(changeMenu, 250);//添加延迟关闭\r\n                        }\r\n                        return false;\r\n                    })\r\n                } else {\r\n                    toggle.on('click', function () {\r\n                        changeMenu();\r\n                        return false;\r\n                    });\r\n                }\r\n\r\n                menu.find('li').on('click', function () {\r\n                    clearTimeout(timer);\r\n                    isShow = false;\r\n                    toggle.removeClass('dropdown-toggle-active');\r\n                    menu.stop().slideUp(200);\r\n                });\r\n\r\n                $('html').on('click', function () {\r\n                    clearTimeout(timer);\r\n                    if (isShow) {\r\n                        isShow = false;\r\n                        toggle.removeClass('dropdown-toggle-active');\r\n                        menu.stop().slideUp(200);\r\n                    }\r\n                });\r\n            }\r\n        };\r\n    });\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports) {\n\nmodule.exports = \"<svg t=\\\"1501119020784\\\" class=\\\"icon\\\" style viewBox=\\\"0 0 1024 1024\\\" version=\\\"1.1\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" p-id=\\\"3157\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\"><defs><style type=\\\"text/css\\\"></style></defs><path d=\\\"M528.612 146.023v771.24c0 9.23-7.383 16.612-16.612 16.612s-16.612-7.383-16.612-16.612v-770.977l-103.623 103.623c-6.328 6.328-16.875 6.328-23.203 0s-6.328-16.875 0-23.203l131.836-131.836c6.328-6.328 16.875-6.328 23.203 0l131.836 131.836c6.328 6.328 6.328 16.875 0 23.203s-16.875 6.328-23.203 0l-103.623-103.887z\\\" p-id=\\\"3158\\\"></path></svg>\"\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports) {\n\n\r\nmam.ng.config = function (ngApp) {\r\n    ngApp.config([\r\n        '$controllerProvider', '$compileProvider', '$filterProvider', '$provide',\r\n        function ($controllerProvider, $compileProvider, $filterProvider, $provide) {\r\n            ngApp.registerController = $controllerProvider.register;\r\n            ngApp.registerDirective = $compileProvider.directive;\r\n            ngApp.registerFilter = $filterProvider.register;\r\n            ngApp.registerFactory = $provide.factory;\r\n            ngApp.registerService = $provide.service;\r\n        }\r\n    ]);\r\n\r\n    ngApp.run([\r\n        '$rootScope', '$state', '$stateParams',\r\n        function ($rootScope, $state, $stateParams) {\r\n            $rootScope.$state = $state;\r\n            $rootScope.params = $stateParams;\r\n            $rootScope.config = _.get(window, 'nxt.config');\r\n            $rootScope.l = window.l;\r\n        }\r\n    ]);\r\n    return ngApp;\r\n}\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports) {\n\nfunction addUrlParam(url, param) {\r\n    url += (url.indexOf('?') !== -1 ? '&' : '?') + param;\r\n    return url;\r\n}\r\n\r\nmam.ng.handleUrl = function (url, options) {\r\n    var opts = $.extend({}, { r: true }, options);\r\n\r\n    if (opts.r) {\r\n        url = addUrlParam(url, 'r=' + parseInt(100 * Math.random()) + new Date().getTime());\r\n    }\r\n\r\n    var token = mam.utils.getUrlQueryParam('token');\r\n    if (token != null) {\r\n        url = addUrlParam(url, 'token=' + encodeURI(token).replace(/\\+/g, '%2B'));\r\n    }\r\n\r\n    var opsite = mam.utils.getUrlQueryParam('opsite', true);\r\n    if (opsite != null) {\r\n        url = addUrlParam(url, 'opsite=' + opsite)\r\n    }\r\n\r\n    var site = mam.utils.getUrlQueryParam('site');\r\n    if (site == null) {\r\n        site = mam.utils.getUrlQueryParam('sitecode');\r\n    }\r\n    if (site != null) {\r\n        url = addUrlParam(url, 'site=' + site);\r\n        $.cookie('site', encodeURIComponent(site), { path: '/' });\r\n    }\r\n    return url;\r\n}\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports) {\n\nmam.ng.httpProvider = function (ngApp, options) {\r\n    var opts = $.extend({}, {\r\n        name: 'mamHttp',\r\n        server: '',\r\n        urlPrefix: '~/',\r\n        cache: false,\r\n        withCredentials: true,\r\n        loaderTemplate: '<div id=\"mam-loader\" class=\"mam-loader\">Loading...</div>',\r\n        loginUrl: '/#!/login?login_backUrl=${url}',\r\n        requestErrorTip: '请求失败，请稍后再试。'\r\n    }, options);\r\n\r\n    ngApp.config([\r\n        '$provide', '$httpProvider',\r\n        function ($provide, $httpProvider) {\r\n            var requestCount = 0;\r\n            var $loader = $(opts.loaderTemplate);\r\n            $('body').append($loader);\r\n            $loader.hide();\r\n\r\n            function hideLoader(config) {\r\n                if (config.interfaceCall && config.showLoader !== false) {\r\n                    requestCount--;\r\n                    if (requestCount <= 0) {\r\n                        $loader.hide();\r\n                    }\r\n                }\r\n            }\r\n\r\n            function goLoginPage() {\r\n                if (location.href.split('?login_backUrl=').length < 2) {\r\n                    location.href = _.template(opts.loginUrl)({ url: escape(location.href) });\r\n                }\r\n            }\r\n\r\n            /** 判断url前缀是否是其他服务器的前缀标识 */\r\n            function indexOfOtherServersPrefix(url) {\r\n                var index = -1;\r\n                if (opts.otherServers && opts.otherServers instanceof Array) {\r\n                    _.forEach(opts.otherServers, function (item) {\r\n                        if (index === -1) {\r\n                            index = url.indexOf(item.prefix);\r\n                        }\r\n                    });\r\n                }\r\n                return index;\r\n            }\r\n\r\n            function getServerUrl(url) {\r\n                if (url.indexOf(opts.urlPrefix) === 0) {\r\n                    return opts.server;\r\n                }\r\n                else if (opts.otherServers && opts.otherServers instanceof Array) {\r\n                    var retUrl = \"\";\r\n                    _.forEach(opts.otherServers, function (item) {\r\n                        if (url.indexOf(item.prefix) === 0) {\r\n                            retUrl = item.server;\r\n                        }\r\n                    });\r\n                    return retUrl;\r\n                }\r\n                return \"\";\r\n            }\r\n\r\n            $provide.factory(opts.name, ['$q', function ($q) {\r\n                return {\r\n                    'request': function (config) {\r\n                        if (config.url.indexOf(opts.urlPrefix) === 0 || indexOfOtherServersPrefix(config.url) === 0) {\r\n                            config.interfaceCall = true;\r\n                            config.url = getServerUrl(config.url) + config.url.substring(1, config.url.length);\r\n                            config.url = mam.ng.handleUrl(config.url);\r\n                            if (config.showLoader !== false) {\r\n                                requestCount++;\r\n                                $loader.show();\r\n                            }\r\n                        }\r\n                        var productName =_.get(nxt,'product.name','');\r\n                        if (productName) {\r\n                            if (!config.headers)\r\n                                config.headers = {};\r\n                            config.headers['mam-product'] = productName;\r\n                        }\r\n                        return config;\r\n                    },\r\n                    'requestError': function (rejection) {\r\n                        hideLoader(rejection.config);\r\n                        if (rejection.config.interfaceCall) {\r\n                            console.error('requestError', rejection);\r\n                            mam.prompt(opts.requestErrorTip);\r\n                        }\r\n                        return $q.reject(rejection);\r\n                    },\r\n                    'response': function (res) {\r\n                        if (res.config.interfaceCall !== true) {\r\n                            return res;\r\n                        }\r\n                        hideLoader(res.config);\r\n                        if (res.status === 200) {\r\n                            if (res.data.success) {\r\n                                res.data = res.data.data;\r\n                            } else {\r\n                                res.data = res.data.error;\r\n                                res.data.success = false;\r\n                                console.error('response', res);\r\n                                if (res.config.errorHandle !== false) {\r\n                                    mam.prompt(l('system.' + res.data.code, res.data.title));\r\n                                }\r\n                                if (res.config.errorReject !== false) { //客户端上传的地方用了q.all，所以希望失败了也要返回结果。\r\n                                    return $q.reject(res);\r\n                                }\r\n                                return $q.resolve(res);\r\n                            }\r\n                        }\r\n                        return res;\r\n                    },\r\n                    'responseError': function (res) {\r\n                        hideLoader(res.config);\r\n                        if (res.config.interfaceCall) {\r\n                            console.error('responseError', res);\r\n                            if (res.status === -1) {\r\n                                mam.prompt(l('system.500', '系统错误，请稍后再试！'));\r\n                                return $q.reject(res);\r\n                            }\r\n                            res.data = res.data.error;\r\n                            if (res.status === 401) {\r\n                                if (res.config.errorHandle !== false) {\r\n                                    goLoginPage();\r\n                                }\r\n                                if (res.config.errorReject !== false) {\r\n                                    return $q.reject(res);\r\n                                }\r\n                                return $q.resolve(res);\r\n                            }\r\n                            if (res.config.errorHandle !== false) {\r\n                                if (res.status === 403) {\r\n                                    mam.prompt(l('system.403', '错误代码：403，服务器拒绝请求！'));\r\n                                }\r\n                                if (res.status === 404) {\r\n                                    mam.prompt(l('system.404', '错误代码：404，未找到请求地址！'));\r\n                                }\r\n                                else\r\n                                {\r\n                                    var requestId = res.headers('request-id');\r\n                                    var outer = $('<div class=\"system-error-tip-box\"></div>');\r\n                                    outer.append('<div class=\"error-tip\">'+l('system.500', res.data.title)+'</div>');\r\n                                    if (requestId)\r\n                                    {\r\n                                        var aEle = $('<a class=\"error-info-btn\">复制错误代码<a>');\r\n                                        aEle.click(function(){\r\n                                            var input = document.createElement('input');\r\n                                            document.body.appendChild(input);\r\n                                            input.setAttribute('value', requestId);\r\n                                            input.select();\r\n                                            if (document.execCommand('copy')) {\r\n                                                document.execCommand('copy');\r\n                                                mam.message.success('复制成功！')\r\n                                            }\r\n                                            document.body.removeChild(input);\r\n                                        });\r\n                                        outer.append(aEle);\r\n                                    }\r\n                                    mam.prompt(outer);\r\n                                }\r\n                            }\r\n                            if (res.config.errorReject !== false) {\r\n                                return $q.reject(res);\r\n                            }\r\n                            return $q.resolve(res);\r\n                        } else {\r\n                            if (res.status === 401) {\r\n                                goLoginPage();\r\n                            }\r\n                        }\r\n                        return $q.reject(res);\r\n                    }\r\n                };\r\n            }]);\r\n\r\n            $httpProvider.defaults.withCredentials = opts.withCredentials;\r\n            $httpProvider.defaults.cache = opts.cache;\r\n            $httpProvider.interceptors.push(opts.name);\r\n        }\r\n    ]);\r\n\r\n    return ngApp;\r\n}\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports) {\n\nwindow.mam.module = window.mam.module || {};\r\n\r\nmam.module.add = function (opts) {\r\n    var module = {\r\n        name: opts.name,\r\n        app: opts.app,\r\n        version: opts.version,\r\n        path: opts.requireModule.uri.substr(0, opts.requireModule.uri.lastIndexOf('/') + 1),\r\n        routes: {},\r\n        options: {}\r\n    };\r\n    mam.module[opts.name] = module;\r\n\r\n    module.init = function (options) {\r\n        module.options = $.extend({}, {\r\n            parentRoute: ''\r\n        }, options);\r\n        if (opts.init(module, options) !== true) {\r\n            mam.ng.config(module.app);\r\n            mam.ng.registerRoutes(module.app, opts.routes, {\r\n                path: module.path,\r\n                parentRoute: module.options.parentRoute,\r\n                ctrlPrefix: module.name\r\n            });\r\n\r\n            _.forEach(mam.ng.handleRoutes(opts.routes, ''), function (val, key) {\r\n                module.routes[key] = module.options.parentRoute + key;\r\n            });\r\n\r\n            mam.language.append(module.path + 'assets/lang/${lang}.js');\r\n        }\r\n        return module;\r\n    }\r\n\r\n    return module;\r\n}\n\n/***/ }),\n/* 8 */\n/***/ (function(module, exports) {\n\n\r\nmam.ng.registerRoutes = function (ngApp, routes, options) {\r\n    var opts = $.extend({}, {\r\n        parentRoute: '',\r\n        path: '',\r\n        ctrlPrefix: '',\r\n        ctrlSuffix: 'Ctrl',\r\n        viewDir: 'view',\r\n        controllerDir: 'controllers'\r\n    }, options);\r\n    if (_.isFunction(routes)) {\r\n        routes = routes();\r\n    }\r\n\r\n    ngApp.config([\r\n        '$urlRouterProvider', '$stateProvider', '$controllerProvider',\r\n        function ($urlRouterProvider, $stateProvider, $controllerProvider) {\r\n            function loader(app, dependencies, ctrlName) {\r\n                var definition = {\r\n                    resolver: ['$q', '$rootScope',\r\n                        function ($q, $rootScope) {\r\n                            var defered = $q.defer();\r\n                            var require = window.require;\r\n                            require(dependencies, function (item) {\r\n                                $rootScope.$apply(function () {\r\n                                    $controllerProvider.register(item.name || ctrlName, item);\r\n                                    defered.resolve();\r\n                                });\r\n                            });\r\n                            return defered.promise;\r\n                        }]\r\n                };\r\n                return definition;\r\n            }\r\n\r\n            function handleName(name) {\r\n                return name.replace(/\\/([a-z])/g, function (all, letter) {\r\n                    return letter.toUpperCase();\r\n                });\r\n            }\r\n\r\n            angular.forEach(mam.ng.handleRoutes(routes, ''), function (route, name) {\r\n                if (route.home) {\r\n                    $urlRouterProvider.when('', route.url);\r\n                    $urlRouterProvider.when('/', route.url);\r\n                }\r\n                if (route.cv != null) {\r\n                    route.templateUrl = opts.path + opts.viewDir + '/' + route.cv + '.html';\r\n                    route.ctrl = route.cv;\r\n                }\r\n                if (route.ctrl != null) {\r\n                    var ctrl = route.ctrl;\r\n                    if (opts.ctrlPrefix != '') {\r\n                        ctrl = opts.ctrlPrefix + '/' + route.ctrl;\r\n                    }\r\n                    route.controller = handleName(ctrl) + opts.ctrlSuffix;\r\n                    route.resolve = [opts.path + opts.controllerDir + '/' + route.ctrl];\r\n                }\r\n                if (_.isArray(route.resolve)) {\r\n                    route.resolve = loader(ngApp, route.resolve, route.controller);\r\n                }\r\n                $stateProvider.state(opts.parentRoute + name, route);\r\n            });\r\n        }\r\n    ]);\r\n    return ngApp;\r\n}\r\n\r\nmam.ng.handleRoutes = function (routes, initRoutes) {\r\n    var tempRoutes = {};\r\n    var selfRoutes = '';\r\n    var parentRoutes = initRoutes ? initRoutes : '';\r\n\r\n    (function handleRoutes(routes, parentRoutes) {\r\n        for (var i in routes) {\r\n            if (routes[i].children && !_.isEmpty(routes[i].children)) {\r\n                selfRoutes += i + '.';\r\n                tempRoutes[parentRoutes + i] = routes[i];\r\n                if (typeof (routes[i].children) === 'object') {\r\n                    handleRoutes(routes[i].children, selfRoutes)\r\n                }\r\n            } else {\r\n                tempRoutes[parentRoutes + i] = routes[i];\r\n            }\r\n            if (parentRoutes !== selfRoutes) {\r\n                selfRoutes = parentRoutes;\r\n            }\r\n        }\r\n        return tempRoutes;\r\n    })(routes, parentRoutes)\r\n    return tempRoutes;\r\n}\n\n/***/ }),\n/* 9 */\n/***/ (function(module, exports) {\n\nangular.module('mam-ng')\r\n    .directive('mamAllCheckbox', function () {\r\n        return {\r\n            restrict: 'A',\r\n            link: function (scope, element, attr) {\r\n                attr.property = attr.property || 'selected';\r\n\r\n                element.bind('change', function (e) {\r\n                    scope.$apply(function () {\r\n                        var value = element.prop('checked');\r\n                        angular.forEach(scope.$eval(attr.collection), function (item) {\r\n                            item[attr.property] = value;\r\n                        });\r\n                    });\r\n                });\r\n                \r\n                scope.$watch(function () { //仅监听对应属性，避免过多的响应和遇到特殊属性导致js报错\r\n                    return _.map(scope.$eval(attr.collection), function (item) {\r\n                        return item[attr.property];\r\n                    });\r\n                }, function (newVal) {\r\n                    var hasTrue, hasFalse;\r\n                    angular.forEach(newVal, function (item) {\r\n                        if (item) {\r\n                            hasTrue = true;\r\n                        } else {\r\n                            hasFalse = true;\r\n                        }\r\n                    });\r\n                    scope.$eval(attr.ngModel + ' = ' + ((hasTrue && hasFalse) ? false : hasTrue));\r\n                }, true);\r\n            }\r\n        };\r\n    });\n\n/***/ }),\n/* 10 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\nObject.defineProperty(__webpack_exports__, \"__esModule\", { value: true });\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less__ = __webpack_require__(62);\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__style_less__);\n \r\n\r\nangular.module('mam-ng')\r\n    .provider('$mamBackToTop', function () {\r\n        var defaults = this.defaults = {\r\n            text: '回到顶部',\r\n            threshold: 400,\r\n            icon: __webpack_require__(78)\r\n        }\r\n        this.$get = function () {\r\n            return { defaults: defaults };\r\n        };\r\n    })\r\n    .directive('mamBackToTop', ['$mamBackToTop', function ($mamBackToTop) {\r\n        var opts = $mamBackToTop.defaults;\r\n        return {\r\n            restrict: \"E\",\r\n            replace: true,\r\n            template: '<div class=\"mam-back-top\" title=\"{{text}}\">' + opts.icon + '</div>',\r\n            scope: {\r\n                container: '@?',\r\n                text: '@?',\r\n                threshold: '@?'\r\n            },\r\n            link: function (scope, element, attr) {\r\n                scope.text = scope.text || opts.text;\r\n                var $e = $(element);\r\n                var container = $(scope.container || window);\r\n                if (_.isUndefined(scope.threshold)) {\r\n                    scope.threshold = opts.threshold;\r\n                } else {\r\n                    scope.threshold = _.toNumber(scope.threshold);\r\n                }\r\n\r\n                function check() {\r\n                    if (container.scrollTop() > scope.threshold) {\r\n                        $e.fadeIn(200);\r\n                    } else {\r\n                        $e.fadeOut(200);\r\n                    }\r\n                }\r\n\r\n                container.scroll(function () { check(); });\r\n\r\n                $e.click(function () {\r\n                    if (container[0] == window) {\r\n                        $('body,html').animate({ scrollTop: 0 }, 500);\r\n                    } else {\r\n                        container.animate({ scrollTop: 0 }, 500);\r\n                    }\r\n                });\r\n\r\n                check();\r\n            }\r\n        };\r\n    }]);\n\n/***/ }),\n/* 11 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\nObject.defineProperty(__webpack_exports__, \"__esModule\", { value: true });\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less__ = __webpack_require__(63);\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__style_less__);\n\r\n\r\nangular.module('mam-ng')\r\n    .directive('mamBadge', function () {\r\n        return {\r\n            restrict: 'E',\r\n            template: '<div class=\"mam-badge\"><span ng-if=\"count>0\">{{count}}</span></div>',\r\n            scope: {\r\n                count: '<',\r\n                overflowCount: '<?'\r\n            },\r\n            link: function (scope, element, attr) {\r\n\r\n                scope.$watch('count', function () {\r\n                    \r\n\r\n                });\r\n            }\r\n        };\r\n    });\n\n/***/ }),\n/* 12 */\n/***/ (function(module, exports) {\n\n/** 文本超出，中间打省略号，element.parent必须固定宽度，element的white-space必须为nowrap */\r\nangular.module('mam-ng')\r\n\t.directive('calCenterEllipsis', [\"$timeout\", function ($timeout) {\r\n\t\treturn {\r\n            restrict: 'A',\r\n\t\t\tlink: function (scope, element, attr) {\r\n                $timeout(function(){\r\n                    if (element.outerWidth() > element.parent().width())\r\n                    {\r\n                        var middleIndex;\r\n                        while (element.outerWidth() > element.parent().width())\r\n                        {\r\n                            element.text(element.text().replace(/\\.\\.\\./, \"\"));\r\n                            middleIndex = element.text().length / 2;\r\n                            element.text(element.text().substring(0, middleIndex)\r\n                                + \"...\" + element.text().substring(middleIndex + 1, element.text().length));\r\n                        }\r\n                    }\r\n                });\r\n\t\t\t}\r\n\t\t};\r\n\t}]);\n\n/***/ }),\n/* 13 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\nObject.defineProperty(__webpack_exports__, \"__esModule\", { value: true });\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less__ = __webpack_require__(64);\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__style_less__);\n\r\n\r\nangular.module('mam-ng')\r\n\t.directive('mamCaptcha', [function () {\r\n\t\treturn {\r\n\t\t\trestrict: 'E',\r\n\t\t\treplace: true,\r\n\t\t\ttemplate: '<img class=\"mam-captcha\"/>',\r\n\t\t\tlink: function (scope, element, attr) {\r\n\r\n\t\t\t\tfunction load() {\r\n\t\t\t\t\telement.attr('src', mam.path('~/user/captcha?t=' + new Date().getTime()))\r\n\t\t\t\t}\r\n\t\t\r\n\t\t\t\tscope.$on('mam-captcha-refresh' , load);\r\n\r\n\t\t\t\telement.on('click', load);\r\n\r\n\t\t\t\tload();\r\n\t\t\t}\r\n\t\t};\r\n\t}]);\n\n/***/ }),\n/* 14 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\nObject.defineProperty(__webpack_exports__, \"__esModule\", { value: true });\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less__ = __webpack_require__(65);\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__style_less__);\n\r\n\r\nangular.module('mam-ng')\r\n    .provider('$mamCheckbox', function () {\r\n        var defaults = this.defaults = {\r\n            class: 'mam-checkbox',\r\n            icon: __webpack_require__(79)\r\n        }\r\n        this.$get = function () {\r\n            return { defaults: defaults };\r\n        };\r\n    })\r\n    .directive('mamCheckbox', ['$parse', '$mamCheckbox', function ($parse, $mamCheckbox) {\r\n        var opts = $mamCheckbox.defaults;\r\n        return {\r\n            restrict: 'A',\r\n            link: function (scope, element, attr) {\r\n                var label = element.parent('label');\r\n                if (!label.hasClass(opts.class)) {\r\n                    label.addClass(opts.class);\r\n                }\r\n                label.append(opts.icon);\r\n\r\n                //ng-checked模式\r\n                if (attr.ngChecked) {\r\n                    scope.$watch(attr.ngChecked, function (newVal) {\r\n                        setCheckedStyle(newVal);\r\n                    }, false);\r\n                }\r\n                //ng-model模式\r\n                if (attr.ngModel) {\r\n                    scope.$watch(attr.ngModel, function (newVal) {\r\n                        setCheckedStyle(newVal);\r\n                    }, false);\r\n                }\r\n                function setCheckedStyle(val) {\r\n                    if (angular.isString(val)) {\r\n                        val = (val.toLowerCase() == 'true');\r\n                    }\r\n                    if (val) {\r\n                        label.addClass('checked').removeClass('unchecked');\r\n                    } else {\r\n                        label.addClass('unchecked').removeClass('checked');\r\n                    }\r\n                }\r\n\r\n                element.on('focus', function () {\r\n                    label.addClass('focus');\r\n                }).on('blur', function () {\r\n                    label.removeClass('focus');\r\n                });\r\n            }\r\n        };\r\n    }]);\n\n/***/ }),\n/* 15 */\n/***/ (function(module, exports) {\n\nangular.module('mam-ng')\r\n    .directive('mamDatepicker', [function () {\r\n        return {\r\n            restrict: 'EA',\r\n            scope: {\r\n                minDate : \"@\",\r\n                maxDate : \"@\",\r\n                ctrlType : \"@\",\r\n                showTime : \"@\"\r\n            },\r\n            link: function (scope, element, attr) {\r\n                $.datetimepicker.setLocale('ch');\r\n                var obj = { showSecond: true, formatTime: 'H:m:s', format: 'Y-m-d H:m:s' };\r\n\r\n                if (scope.minDate !== 'no') {\r\n                    obj.minDate = scope.minDate;\r\n                }\r\n                if (scope.maxDate !== 'no') {\r\n                    obj.maxDate = scope.maxDate;\r\n                }\r\n\r\n                obj.onClose = function () {\r\n                    if (element.val() != \"\") {\r\n                        scope.ngModel = element.val();\r\n\r\n                    }\r\n                    element.find(\"input[type=text]\").blur();\r\n                    scope.$apply();\r\n                }\r\n                if (scope.ctrlType === \"3\") {\r\n                    obj.datepicker = false;\r\n                    obj.format = 'H:i:s';\r\n                }\r\n                if (scope.ctrlType === \"2\") {\r\n                    obj.timepicker = false;\r\n                    obj.format = 'Y-m-d';\r\n                    if (scope.ngModel != null && scope.ngModel !== '' && scope.ngModel.length > 18)\r\n                        scope.ngModel = scope.ngModel.substring(0, 10);\r\n                }\r\n\r\n                if (scope.step !== 'no' && typeof (scope.step) !== \"undefined\") {\r\n                    obj.step = scope.step;\r\n                }\r\n\r\n                if (scope.ctrlType !== 3 && scope.controlData != undefined) {\r\n                    switch (scope.controlData.type) {\r\n                        case \"onlypass\":\r\n                            obj.maxDate = '0'; //只能选今天以前-仅过去时间\r\n                            break;\r\n                        case \"onlyfuture\":\r\n                            obj.minDate = '0';//只能选今天以后-仅未来时间\r\n                            break;\r\n                    }\r\n                }\r\n                element.datetimepicker(obj);\r\n            }\r\n        };\r\n    }]);\n\n/***/ }),\n/* 16 */\n/***/ (function(module, exports) {\n\nangular.module('mam-ng')\r\n    .directive('mamEntityView', function () {\r\n        return {\r\n            restrict: 'A',\r\n            scope: {\r\n                viewtype : \"@\",  //type='edit' or type='browse'\r\n                entity : \"<\"\r\n            },\r\n            link: function (scope, element, attr) {\r\n                var url = mam.entity.getViewEntityUrl(scope.entity, scope.viewtype);\r\n\r\n                function init(){\r\n                    if (element[0].tagName === \"A\")\r\n                    {\r\n                        element.attr(\"href\", url);\r\n                        element.attr(\"target\", \"_blank\");\r\n                    }\r\n                }\r\n\r\n                init();\r\n            }\r\n        };\r\n    });\n\n/***/ }),\n/* 17 */\n/***/ (function(module, exports) {\n\nangular.module('mam-ng').directive('mamHref', function () {\r\n    return {\r\n        restrict: 'A',\r\n        link: function (scope, element, attr) {\r\n            var href = mam.path(attr.mamHref);\r\n            element.attr('href', href);\r\n        }\r\n    };\r\n});\n\n/***/ }),\n/* 18 */\n/***/ (function(module, exports) {\n\nangular.module('mam-ng')\r\n    .directive('imageFileSelector', function () {\r\n        return {\r\n            restrict: 'E',\r\n            template: \"<input type='file' style='display:none' id='{{inputId}}' accept='image/gif,image/jpeg,image/jpg,image/png' />\",\r\n            scope: {\r\n                inputId: '@', //源地址\r\n                onChange: '&',\r\n                onError: '&'\r\n            },\r\n            replace: true,\r\n            link: function (scope, element, attr) {\r\n                element.bind('change', function (event) {\r\n                    var file = element[0].files[0];\r\n                    if (file == null) {\r\n                        return;\r\n                    }\r\n                    var reader = new FileReader();\r\n                    reader.onload = function () {\r\n                        scope.onChange({\r\n                            file: file,\r\n                            base64: this.result\r\n                        });\r\n                        element.val('');\r\n                    }\r\n                    reader.onerror = function () {\r\n                        scope.onError();\r\n                    }\r\n                    reader.readAsDataURL(file);\r\n                });\r\n            }\r\n        };\r\n    });\n\n/***/ }),\n/* 19 */\n/***/ (function(module, exports) {\n\nangular.module('mam-ng').directive('mamImage', function () {\r\n    return {\r\n        restrict: 'E',\r\n        template: '<img />',\r\n        scope: {\r\n            url: '<',   //需要加载的图片地址\r\n            def: '@',   //图片地址为空时使用的地址\r\n            error: '@?',//加载失败时的图片\r\n        },\r\n        transclude: false,\r\n        replace: true,\r\n        link: function (scope, element, attr) {\r\n            var $img = $(element);\r\n\r\n            if (_.isEmpty(scope.error)) {\r\n                scope.error = scope.def;\r\n            }\r\n\r\n            scope.$watch('url', function () {\r\n                var src = _.isEmpty(scope.url) ? scope.def : scope.url;\r\n                src = mam.path(src);\r\n                $img.one('error', function () {\r\n                    $img.attr('src', mam.path(scope.error));\r\n                });\r\n                $img.attr('src', src);\r\n            });\r\n\r\n        }\r\n    };\r\n});\n\n/***/ }),\n/* 20 */\n/***/ (function(module, exports) {\n\nangular.module('mam-ng')\r\n    .directive('mamInputLimit', function () {\r\n        return {\r\n            restrict: 'A',\r\n            scope: {\r\n                limitStr: '@?',\r\n                ngModel: '='\r\n            },\r\n            link: function (scope, element, attr) {\r\n                var $e = $(element);\r\n                var chars = ['\\\\', '/', '\"', ':', '*', '?', '<', '>', '|'];\r\n                var isShow = false;\r\n                var timer = 0;\r\n                if (scope.limitStr) {\r\n                    var chars = scope.limitStr.split('');\r\n                }\r\n                eval(\"scope.limitStr = /[\" + chars.join('\\\\') + \"]/g\");\r\n                var tip = $('<div style=\"display:none;\" class=\"mam-input-limit\"><span>文件名不能包含以下任何字符：<p>' + chars.join(' ') + '</p></span></div>');\r\n                $e.after(tip);\r\n\r\n                function autoClose(){\r\n                    timer = setTimeout(function () {\r\n                        tip.fadeToggle();\r\n                        isShow = false;\r\n                    }, 3000);\r\n                }\r\n\r\n                $e.on(\"keyup\", function (e) {\r\n                    if (e.keyCode != 16 && e.keyCode != 17) {\r\n                        clearTimeout(timer);\r\n                        if (scope.limitStr.test(scope.ngModel)) {\r\n                            if (!isShow) {\r\n                                isShow = true;\r\n                                tip.fadeToggle();\r\n                            } \r\n                            scope.ngModel = scope.ngModel.replace(scope.limitStr, '');\r\n                            scope.$apply();\r\n                            autoClose();\r\n                        } else {\r\n                            if (isShow) {\r\n                                isShow = false;\r\n                                tip.fadeToggle();\r\n                            }\r\n                        }\r\n                    }\r\n                });\r\n            }\r\n        };\r\n    });\n\n/***/ }),\n/* 21 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\nObject.defineProperty(__webpack_exports__, \"__esModule\", { value: true });\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less__ = __webpack_require__(66);\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__style_less__);\n\r\n\r\nangular.module('mam-ng')\r\n    .provider('$mamKeyframe', function () {\r\n        var defaults = {\r\n            extKeyframes: [],\r\n            typeKeyframes: [],\r\n            other: '',\r\n            folder: ''\r\n        };\r\n        function getKeyframeByCode(keyframes, code) {\r\n            if (_.isArray(keyframes)) {\r\n                var item = _.find(keyframes, { 'code': code });\r\n                if (item != null) {\r\n                    return item.keyframe;\r\n                }\r\n            }\r\n            return '';\r\n        }\r\n        this.setDefaults = function (options) {\r\n            defaults = $.extend({}, defaults, options);\r\n            if (_.isEmpty(defaults.folder)) {\r\n                defaults.folder = getKeyframeByCode(defaults.extKeyframes, 'folder');\r\n            }\r\n            if (_.isEmpty(defaults.other)) {\r\n                defaults.other = getKeyframeByCode(defaults.typeKeyframes, 'other');\r\n            }\r\n        }\r\n\r\n        this.$get = function () {\r\n            return { defaults: defaults };\r\n        };\r\n    })\r\n    .directive('mamKeyframe', ['$mamKeyframe', function ($mamKeyframe) {\r\n        var opts = $mamKeyframe.defaults;\r\n\r\n        return {\r\n            restrict: 'E',\r\n            template: '<div class=\"mam-keyframe\"><img /></div>',\r\n            scope: {\r\n                src: '=',   //关键帧地址\r\n                ext: '<?',  //素材扩展名\r\n                type: '<?'  //素材类型\r\n            },\r\n            transclude: false,\r\n            replace: true,\r\n            link: function (scope, element, attr) {\r\n\r\n                var $img = element.find('img');\r\n\r\n                function useDefault() {\r\n                    element.addClass('mam-keyframe-default');\r\n                    if (scope.type == 'folder') {\r\n                        return load(opts.folder);\r\n                    }\r\n\r\n                    if (!_.isEmpty(scope.ext)) {\r\n                        var item = _.find(opts.extKeyframes, function (n) {\r\n                            if (_.isArray(n.extensions)) {\r\n                                return n.extensions.indexOf(scope.ext) != -1;\r\n                            }\r\n                            return false;\r\n                        });\r\n                        if (item != null) {\r\n                            return load(item.keyframe);\r\n                        }\r\n                    }\r\n                    if (!_.isEmpty(scope.type)) {\r\n                        var item = _.find(opts.typeKeyframes, { code: scope.type });\r\n                        if (item != null) {\r\n                            return load(item.keyframe);\r\n                            $img.attr('src', mam.path(item.keyframe));\r\n                            return;\r\n                        }\r\n                    }\r\n                    load(opts.other);\r\n                }\r\n\r\n                function load(src) {\r\n                    $img.removeClass('mam-keyframe-loaded');\r\n                    $img.attr('src', mam.path(src));\r\n                }\r\n                //todo:增加mam-keyframe-loaded目的是为了解决在图片未加载出来前，视频类型的出现黑色背景的问题\r\n                //     但相关代码，有点问题。问题是解决。暂时先这样。\r\n                $img.on('load', function () {\r\n                    element.addClass('mam-keyframe-loaded');\r\n                });\r\n\r\n                scope.$watchGroup(['src', 'type', 'ext'], function (newValue, oldValue, scope) {\r\n                    if (!_.isEmpty(scope.ext)) {\r\n                        scope.ext = scope.ext.toLowerCase();\r\n                    }\r\n\r\n                    if (!_.isEmpty(scope.type)) {\r\n                        //正常情况下type只会从没值变化成有值，不会出现从 video 变成  audio\r\n                        element.addClass('mam-keyframe-type-' + scope.type);\r\n                    }\r\n\r\n                    if (_.isEmpty(scope.src)) {\r\n                        useDefault();\r\n                    } else {\r\n                        element.removeClass('mam-keyframe-default');\r\n                        $img.one('error', useDefault);\r\n                        load(scope.src);\r\n                    }\r\n\r\n                });\r\n\r\n            }\r\n        };\r\n    }]);\n\n/***/ }),\n/* 22 */\n/***/ (function(module, exports, __webpack_require__) {\n\n__webpack_require__(67);\r\n\r\nangular.module('mam-ng')\r\n    .directive('mamLogo', ['$sce', function ($sce) {\r\n        return {\r\n            restrict: 'E',\r\n            template: __webpack_require__(54),\r\n            transclude: true,\r\n            replace: true,\r\n            scope: {\r\n                config : \"<\"\r\n            },\r\n            link: function (scope, element, attr) {\r\n                scope.config = scope.config || nxt.config;\r\n\r\n                function init() {\r\n                }\r\n\r\n                init();\r\n            }\r\n\r\n        };\r\n    }]);\n\n/***/ }),\n/* 23 */\n/***/ (function(module, exports, __webpack_require__) {\n\n__webpack_require__(68);\r\n\r\nangular.module('mam-ng')\r\n    .provider('$mamPager', function () {\r\n        var defaults = this.defaults = {\r\n            text: {\r\n                total: l('com.pageTotal', '总共'),\r\n                record: l('com.pageRecord', '条'),\r\n                index: l('com.pageIndex', '页码'),\r\n                first: l('com.pageFirst', '首页'),\r\n                last: l('com.pageLast', '尾页'),\r\n                prev: l('com.pagePrev', '上一页'),\r\n                next: l('com.pageNext', '下一页')\r\n            }\r\n        };\r\n        this.$get = function () {\r\n            return { defaults: defaults };\r\n        };\r\n    })\r\n    .directive('mamPager', ['$mamPager', function ($mamPager) {\r\n        var opts = $mamPager.defaults;\r\n        return {\r\n            restrict: 'E',\r\n            template: __webpack_require__(55),\r\n            replace: true,\r\n            scope: {\r\n                recordTotal: '<?',\r\n                pageIndex: '<?',\r\n                pageSize: '<?',\r\n                pageTotal: '<?',\r\n                showText: '<?',\r\n                pageChanged: '&?',\r\n                text: '<?',\r\n                maxSize: '@?'\r\n            },\r\n            link: function (scope, element, attr) {\r\n                //debugger;\r\n                scope.textInfo = scope.text || opts.text;\r\n                scope.maxSize = scope.maxSize || 7;\r\n\r\n                if (scope.showText == undefined) {\r\n                    scope.showText = true;\r\n                }\r\n\r\n                scope.$watch('pageTotal', function (item) {\r\n                    //如果在html上用ng-if来做这个事情。会因为ng-if会产生一个作用域，导致一个bug。\r\n                    if (scope.pageTotal > 1) {\r\n                        element.find('.pagination').show();\r\n                    } else {\r\n                        element.find('.pagination').hide();\r\n                    }\r\n                });\r\n\r\n                scope.$watch('text', function () {\r\n                    if (scope.text)\r\n                    {\r\n                        scope.textInfo = $.extend({}, opts.text, scope.text);\r\n                    }\r\n                });\r\n\r\n                scope.change = function (item) {\r\n                    scope.pageChanged({ page: item });\r\n                }\r\n\r\n                // 监控你的页码 ， 发生改变既请求\r\n                scope.$watch('recordTotal', function (item) {\r\n                    scope.pageTotal = Math.ceil(scope.recordTotal / scope.pageSize);\r\n                    if (scope.pageIndex > 1 && scope.pageIndex > scope.pageTotal) {\r\n                        scope.change(scope.pageTotal);\r\n                    }\r\n                });\r\n            }\r\n        };\r\n    }]);\n\n/***/ }),\n/* 24 */\n/***/ (function(module, exports, __webpack_require__) {\n\n__webpack_require__(69);\r\n\r\nangular.module('mam-ng')\r\n    .provider('$mamRadio', function () {\r\n        var defaults = this.defaults = {\r\n            className: 'mam-radio',\r\n            icon: __webpack_require__(80)\r\n        };\r\n\r\n        this.$get = function () {\r\n            return { defaults: defaults };\r\n        };\r\n    })\r\n    .directive('mamRadioGroup', function () {\r\n        return {\r\n            restrict: 'A',\r\n            require: 'ngModel',\r\n            compile: function (element, attr) {\r\n                element.removeAttr('ng-model');\r\n                var items = element.find('input[type=\"radio\"]');\r\n                angular.forEach(items, function (item) {\r\n                    angular.element(item).attr('ng-model', attr.ngModel);\r\n                });\r\n            }\r\n        };\r\n\r\n    })\r\n    .directive('mamRadio', ['$mamRadio', function ($mamRadio) {\r\n        var opts = $mamRadio.defaults;\r\n        return {\r\n            restrict: 'A',\r\n            link: function (scope, element, attr, controller) {\r\n\r\n                var label = element.parent('label');\r\n                if (!label.hasClass(opts.className)) {\r\n                    label.addClass(opts.className);\r\n                }\r\n\r\n                label.append(opts.icon);\r\n\r\n                function set(model) {\r\n                    var val = attr.ngValue == null ? element.attr('value') : scope.$eval(attr.ngValue);\r\n                    if (val == model) {\r\n                        label.addClass('checked').removeClass('unchecked');\r\n                    } else {\r\n                        label.addClass('unchecked').removeClass('checked');\r\n                    }\r\n                }\r\n\r\n                if (attr.ngModel != null && attr.ngModel != '') {\r\n                    scope.$watch(attr.ngModel, function (newVal) {\r\n                        set(newVal);\r\n                    }, false);\r\n                    if (attr.ngValue != null && attr.ngValue != '') {\r\n                        scope.$watch(attr.ngValue, function (newVal) {\r\n                            set(scope.$eval(attr.ngModel));\r\n                        }, false);\r\n                    }\r\n                    set(scope.$eval(attr.ngModel));\r\n                }\r\n\r\n                element.on('focus', function () {\r\n                    label.addClass('focus');\r\n                }).on('blur', function () {\r\n                    label.removeClass('focus');\r\n                });\r\n            }\r\n        };\r\n    }]);\n\n/***/ }),\n/* 25 */\n/***/ (function(module, exports) {\n\n/*\r\n *指令名：mam-resize-table,属性指令\r\n *scope属性：scroll-x。值为true(表格溢出滚动）或false（表格不会溢出）\r\n *此指令应用于表格的body下的行，必须与ng-repeat指令处于同一位置\r\n *（由于指令的解析顺序问题，如果此指令位于ng-repeat指令之前，会使得body部分的内容还未解析而取不到）\r\n *表格的head行部分必须使用'.flex-head'作为类名\r\n *body部分的每一行必须使用'.flex-item'作为类名\r\n **/\r\n\r\nangular.module('mam-ng').directive('mamResizeTable', function($timeout) {\r\n    return {\r\n        restrict: 'A',\r\n        scope: {\r\n            scrollX: '='\r\n        },\r\n        link: function(scope, element, attrs) {\r\n            $timeout(function() {\r\n                var $table = element.parent().parent(); //表格\r\n                var $rol = $table.find('.flex-item,.flex-head'); //所有行\r\n                var $ceils = $rol.children(); //所有单元格\r\n                var $head = $table.find('.flex-head');\r\n                var $headceils = $head.children();\r\n\r\n                $rol.css('width', $table.width() - 2 + 'px');\r\n                $ceils.css('position', 'relative');\r\n                $table.css('overflow-x', scope.scrollX ? 'scroll' : 'hidden');\r\n                $table.css('overflow-y', 'hidden');\r\n\r\n                var $resizeline = $('<div class=\"resize-line\"></div>');\r\n                $resizeline.attr('draggable', true);\r\n                $resizeline.css({\r\n                    'width': '0',\r\n                    'height': '100%',\r\n                    'position': 'absolute',\r\n                    'z-index': '9999',\r\n                    'right': '-2px',\r\n                    'top': '0',\r\n                    'border-right': '5px solid transparent',\r\n                    'cursor': 'e-resize'\r\n                });\r\n                var $dashline = $('<div class=\"dash-line\"></div>')\r\n                $dashline.css({\r\n                    'width': '0',\r\n                    'height': $table.height() + 'px',\r\n                    'position': 'absolute',\r\n                    'z-index': '-9999',\r\n                    'right': '0',\r\n                    'top': '0',\r\n                    'border-right': '2px dashed transparent'\r\n                });\r\n\r\n                var startPosition = 0;\r\n                var startWidth = 0;\r\n                var currentColumn = 0; //当前单元格所在列的列序号\r\n                var currentArray = []; //当前单元格所在列的所有单元格所组成的数组\r\n                var currentDashLine = '';\r\n\r\n                var dragStartFunc = function(e, t) {\r\n                    e.originalEvent.dataTransfer.effectAllowed = \"move\";\r\n                    startPosition = e.clientX;\r\n                    startWidth = $(t).parent().width();\r\n                    for (var i = 0; i < $(t).parent().parent().children().length; i++) {\r\n                        if ($(t).parent().get(0) == $(t).parent().parent().children().get(i)) {\r\n                            currentColumn = i;\r\n                            currentDashLine = $headceils.find('.dash-line').get(i);\r\n                            break;\r\n                        }\r\n                    }\r\n                    currentArray = lineArr[currentColumn];\r\n                }\r\n                var draggingFunc = function(e) {\r\n                    $(currentDashLine).css('right', -(e.clientX - startPosition) + 'px');\r\n                    $(currentDashLine).css('border-right', '2px dashed gray');\r\n                }\r\n                var dragFunc = function(e) {\r\n                    $(currentDashLine).css('right', '0');\r\n                    $(currentDashLine).css('border-right', '2px dashed transparent');\r\n                    angular.forEach(currentArray, function(o) {\r\n                        $(o).css('flex', '0 1 auto');\r\n                        if (scope.scrollX) {\r\n                            $(o).css('min-width', startWidth + e.clientX - startPosition + 'px');\r\n                        } else {\r\n                            $(o).css('width', startWidth + e.clientX - startPosition + 'px');\r\n                        }\r\n                    })\r\n                }\r\n                $resizeline.on('dragstart', function(e) {\r\n                    var t = this;\r\n                    dragStartFunc(e, t);\r\n                });\r\n                $resizeline.on('drag', function(e) {\r\n                    draggingFunc(e);\r\n                });\r\n                $resizeline.on('dragend', function(e) {\r\n                    dragFunc(e);\r\n                });\r\n                $ceils.on('dragover', function(e) {\r\n                    e.preventDefault();\r\n                    e.originalEvent.dataTransfer.dropEffect = \"move\";\r\n                    return true;\r\n                });\r\n\r\n                $ceils.find('.resize-line').remove(); //先移除后添加，避免ng-repeat造成每个单元格上重复出现多个resize-line\r\n                $ceils.append($resizeline);\r\n                $headceils.find('.dash-line').remove();\r\n                $headceils.append($dashline); //拖动时看到的虚线\r\n\r\n                var lineArr = new Array($rol.eq(0).find('.resize-line').length);\r\n                for (var i = 0; i < lineArr.length; i++) {\r\n                    lineArr[i] = [];\r\n                }\r\n                angular.forEach($rol, function(rol, index) {\r\n                    angular.forEach($(rol).find('.resize-line'), function(o, i) {\r\n                        lineArr[i].push($(o).parent());\r\n                    })\r\n                })\r\n            });\r\n        }\r\n    };\r\n});\n\n/***/ }),\n/* 26 */\n/***/ (function(module, exports) {\n\nangular.module('mam-ng').directive('mamResizer', function () {\r\n    return {\r\n        restrict: 'E',\r\n        scope: {\r\n            direction: '@?',\r\n            width: '@?', //宽度，单位像素，不用带 px\r\n            height: '@?',\r\n            offset: '@?',\r\n            max: '@?',\r\n            left: '@?',\r\n            right: '@?',\r\n            top: '@?',\r\n            bottom: '@?'\r\n        },\r\n        link: function (scope, element, attrs) {\r\n            scope.direction = scope.direction || 'h';\r\n\r\n            if (scope.direction == 'h') {\r\n                element.css('cursor', 'ew-resize');\r\n            } else {\r\n                element.css('cursor', 'ns-resize');\r\n            }\r\n\r\n            element.on('mousedown', function (event) {\r\n                event.preventDefault();\r\n                element.addClass('active');\r\n                $(document).on('mousemove', mousemove);\r\n                $(document).on('mouseup', mouseup);\r\n            });\r\n\r\n            function mousemove(event) {\r\n                if (scope.direction == 'h') {\r\n                    var x = event.pageX;\r\n                    if (scope.offset) {\r\n                        x += parseInt(scope.offset);\r\n                    }\r\n                    if (scope.max && x > scope.max) {\r\n                        x = parseInt(scope.max);\r\n                    }\r\n                    element.css({ left: x + 'px' });\r\n                    $(scope.left).css({ width: x + 'px' });\r\n                    $(scope.right).css({\r\n                        left: (x + parseInt(scope.width)) + 'px'\r\n                    });\r\n                } else {\r\n                    var y = event.pageY;\r\n                    if (scope.offset) {\r\n                        y += parseInt(scope.offset);\r\n                    }\r\n                    if (scope.max && y > scope.max) {\r\n                        y = parseInt(scope.max);\r\n                    }\r\n                    element.css({ top: y + 'px' });\r\n                    $(scope.top).css({ height: y + 'px' });\r\n                    $(scope.bottom).css({ \r\n                        top: (y + parseInt(scope.height)) + 'px' \r\n                    });\r\n                }\r\n            }\r\n\r\n            function mouseup() {\r\n                element.removeClass('active');\r\n                $(document).unbind('mousemove', mousemove);\r\n                $(document).unbind('mouseup', mouseup);\r\n            }\r\n        }\r\n    };\r\n});\n\n/***/ }),\n/* 27 */\n/***/ (function(module, exports, __webpack_require__) {\n\n__webpack_require__(70);\r\n\r\nangular.module('mam-ng')\r\n    .directive('mamSearchInput', ['$http', function ($http) {\r\n        return {\r\n            restrict: 'E',\r\n            template: __webpack_require__(56),\r\n            replace: true,\r\n            scope: {\r\n                search: '&',\r\n                keywords: '=',\r\n                useHistory: '<?',\r\n                placeholder: '@?'\r\n            },\r\n            link: function (scope, element, attr) {\r\n                var $e = $(element);\r\n                var boxId = '#mam-history-box';\r\n                var historyBox = $e.find(boxId);\r\n                var index = -1;\r\n                var isShow = false;\r\n                scope.text = {\r\n                    placeholder: scope.placeholder || 'keyword'\r\n                };\r\n                scope.selected;\r\n\r\n                function SearchHistory() {\r\n                    var self = this;\r\n                    this.items = [];\r\n\r\n                    function changeBox() {\r\n                        historyBox.not(historyBox).stop().slideUp(100).parent().find(boxId);\r\n                        historyBox.stop().slideToggle(100);\r\n                    };\r\n\r\n                    this.showBox = function ($event, op) {\r\n                        if (!scope.useHistory) return;\r\n                        if ($event != undefined)\r\n                            $event.stopPropagation();\r\n                        function show() {\r\n                            if (!isShow && self.items.length > 0) {\r\n                                scope.selected = undefined;\r\n                                index = -1;\r\n                                isShow = true;\r\n                                changeBox();\r\n                            }\r\n                        }\r\n                        setTimeout(function () {\r\n                            var res = self.get(op ? scope.keywords : undefined);\r\n                            if (res) {\r\n                                res.then(function () {\r\n                                    show();\r\n                                });\r\n                            } else\r\n                                show();\r\n                        }, 100);\r\n                    };\r\n\r\n                    this.close = function () {\r\n                        if (isShow) {\r\n                            isShow = false;\r\n                            changeBox();\r\n                        }\r\n                    }\r\n\r\n                    this.delete = function (id, $event) {\r\n                        if (!scope.useHistory) return;\r\n                        $event.stopPropagation();\r\n                        $http.delete('~/search/history/' + id, { errorHandle: false }).then(function (res) {\r\n                            if (res.data) {\r\n                                self.get();\r\n                            }\r\n                        }, function (res) {\r\n                            console.error(res);\r\n                        });\r\n                    };\r\n\r\n                    this.getBy = function () {\r\n                        self.get(scope.keywords);\r\n                    }\r\n\r\n                    this.get = function (keyword) {\r\n                        if (scope.useHistory) {\r\n                            var url = '~/search/tip-search?keyword=';\r\n                            if (keyword)\r\n                                url += keyword;\r\n                            return $http.get(url, { errorHandle: false, showLoader: false }).then(function (res) {\r\n                                if (keyword == scope.keywords || keyword == undefined)\r\n                                    self.items = res.data;\r\n                            }, function (res) {\r\n                                if (res.status == 404) {\r\n                                    self.items = [];\r\n                                    console.info('需要/search/tip-search接口');\r\n                                }\r\n                                console.error(res);\r\n                            });\r\n                        }\r\n                    };\r\n\r\n                    (function () {\r\n                        if (!historyBox) {\r\n                            scope.useHistory = false;\r\n                        } else {\r\n                            historyBox.hide();\r\n                            setTimeout(self.get, 5000);\r\n                        }\r\n                    })();\r\n                }\r\n\r\n                scope.submit = function (keyword) {\r\n                    if (keyword)\r\n                        scope.keywords = keyword;\r\n                    setTimeout(function () {\r\n                        scope.$apply(function () {\r\n                            scope.search();\r\n                        });\r\n                    }, 50)\r\n\r\n                    scope.sh.close();\r\n                }\r\n\r\n                scope.changeModel = function (keyword) {\r\n                    if (!keyword)\r\n                        scope.selected = undefined;\r\n                    else\r\n                        scope.selected = keyword;\r\n                }\r\n\r\n                function init() {\r\n                    if (scope.keywords == undefined)\r\n                        console.error('检索输入框插件：检索keywords初始值不能为undefined！');\r\n                    scope.sh = new SearchHistory(historyBox);\r\n                    if (scope.useHistory)\r\n                        $e.on('keydown', function (e) {\r\n                            var length = scope.sh.items.length;\r\n                            if (e.keyCode == 38) {\r\n                                index--;\r\n                                if (index <= -2) index = 0;\r\n                                if (index < 0) index = length - 1;\r\n                                scope.selected = scope.keywords = scope.sh.items[index].keyword;\r\n                            } else if (e.keyCode == 40) {\r\n                                index++;\r\n                                if (index >= length) index = 0;\r\n                                scope.selected = scope.keywords = scope.sh.items[index].keyword;\r\n                            } else if (e.keyCode == 27) {\r\n                                scope.selected = undefined;\r\n                                scope.sh.close();\r\n                            } else if (e.keyCode == 13) {\r\n                                element.find('input').blur();\r\n                                scope.submit();\r\n                            }\r\n                            scope.$apply();\r\n                        });\r\n\r\n                    $(document).on('click', scope.sh.close);\r\n                }\r\n\r\n                init();\r\n            }\r\n        };\r\n    }]);\n\n/***/ }),\n/* 28 */\n/***/ (function(module, exports) {\n\nangular.module('mam-ng').directive('mamSelectable', function () {\r\n    return {\r\n        restrict: 'A',\r\n        link: function (scope, element, attr) {\r\n            element.on('mousedown', init);\r\n\r\n            var box = element,\r\n                mode = attr.mode;\r\n\r\n            function segmentsIntr(a, b, c, d) {\r\n\r\n                // 三角形abc 面积的2倍\r\n                var area_abc = (a.x - c.x) * (b.y - c.y) - (a.y - c.y) * (b.x - c.x);\r\n\r\n                // 三角形abd 面积的2倍\r\n                var area_abd = (a.x - d.x) * (b.y - d.y) - (a.y - d.y) * (b.x - d.x);\r\n\r\n                // 面积符号相同则两点在线段同侧,不相交;\r\n                if (area_abc * area_abd >= 0) {\r\n                    return false;\r\n                }\r\n\r\n                // 三角形cda 面积的2倍\r\n                var area_cda = (c.x - a.x) * (d.y - a.y) - (c.y - a.y) * (d.x - a.x);\r\n                // 三角形cdb 面积的2倍\r\n                // 注意: 这里有一个小优化.不需要再用公式计算面积,而是通过已知的三个面积加减得出.\r\n                var area_cdb = area_cda + area_abc - area_abd;\r\n                if (area_cda * area_cdb >= 0) {\r\n                    return false;\r\n                }\r\n\r\n                return true;\r\n            }\r\n\r\n            function init() {\r\n                var firstTime = new Date().getTime(),\r\n                    boxX = event.clientX - parseInt(box.offset().left),\r\n                    boxY = event.clientY - parseInt(box.offset().top) + document.body.scrollTop;\r\n                $(document).on('mouseup', { firstTime: firstTime }, end);\r\n\r\n                box.append('<div class=\"mam-selectable-box\"></div>').on('mousemove', { x: event.clientX, y: event.clientY + document.body.scrollTop }, move);\r\n\r\n                box.css('position', 'relative');\r\n                $('.mam-selectable-box').css({\r\n                    'position': 'absolute',\r\n                    'top': boxY,\r\n                    'left': boxX,\r\n                    'backgroundColor': 'rgba(0, 196, 244, 0.3)'\r\n                });\r\n                $('body').css('userSelect', 'none').attr('ondragstart', 'return false;');\r\n                $('head').append(\"<style type='text/css' key='select'>::selection{ background: none; }</style>\");\r\n            }\r\n\r\n            function move(e) {\r\n                var boxX = event.clientX - e.data.x,\r\n                    boxY = event.clientY - e.data.y + document.body.scrollTop,\r\n                    selectItems = $(attr.itemname),\r\n                    selectBox = $('.mam-selectable-box'),\r\n                    selectBoxTop = selectBox.offset().top,\r\n                    selectBoxBottom = selectBox.get(0).offsetHeight + selectBoxTop,\r\n                    selectBoxLeft = selectBox.offset().left,\r\n                    selectBoxRight = selectBox.get(0).offsetWidth + selectBoxLeft;\r\n\r\n                if (boxX > 0 && boxY > 0) {\r\n                    selectBox.css({\r\n                        'width': boxX,\r\n                        'height': boxY\r\n                    })\r\n                } else if (boxX > 0 && boxY < 0) {\r\n                    selectBox.css({\r\n                        'width': boxX,\r\n                        'height': -boxY,\r\n                        'top': event.clientY - parseInt(box.offset().top) + document.body.scrollTop\r\n                    })\r\n                } else if (boxX < 0 && boxY > 0) {\r\n                    selectBox.css({\r\n                        'width': -boxX,\r\n                        'height': boxY,\r\n                        'left': event.clientX - parseInt(box.offset().left)\r\n                    })\r\n                } else if (boxX < 0 && boxY < 0) {\r\n                    selectBox.css({\r\n                        'width': -boxX,\r\n                        'height': -boxY,\r\n                        'top': event.clientY - parseInt(box.offset().top) + document.body.scrollTop,\r\n                        'left': event.clientX - parseInt(box.offset().left)\r\n                    })\r\n                }\r\n\r\n                for (var i = 0; i < selectItems.length; i++) {\r\n                    var itemLeft = selectItems.eq(i).offset().left,\r\n                        itemRight = itemLeft + selectItems.eq(i).width(),\r\n                        itemTop = selectItems.eq(i).offset().top,\r\n                        itemBottom = itemTop + selectItems.eq(i).height();\r\n\r\n                    if (mode === 'single') {\r\n                        if (selectBoxTop > itemTop && selectBoxTop < itemBottom || selectBoxTop === itemTop) {\r\n                            selectItems.eq(i).css('backgroundColor', 'rgba(0, 196, 244, 0.3)');\r\n                        } else if (itemTop > selectBoxTop && itemBottom < selectBoxBottom) {\r\n                            selectItems.eq(i).css('backgroundColor', 'rgba(0, 196, 244, 0.3)');\r\n                        } else if (selectBoxBottom > itemTop && selectBoxBottom < itemBottom) {\r\n                            selectItems.eq(i).css('backgroundColor', 'rgba(0, 196, 244, 0.3)');\r\n                        } else {\r\n                            selectItems.eq(i).css('backgroundColor', '');\r\n                        }\r\n                    } else if (mode === 'multi') {\r\n                        //横向与纵向\r\n                        if (segmentsIntr({ x: selectBoxLeft, y: selectBoxTop }, { x: selectBoxRight, y: selectBoxTop }, { x: itemLeft, y: itemTop }, { x: itemLeft, y: itemBottom }) ||\r\n                            segmentsIntr({ x: selectBoxLeft, y: selectBoxTop }, { x: selectBoxRight, y: selectBoxTop }, { x: itemRight, y: itemTop }, { x: itemRight, y: itemBottom }) ||\r\n                            segmentsIntr({ x: selectBoxLeft, y: selectBoxBottom }, { x: selectBoxRight, y: selectBoxBottom }, { x: itemLeft, y: itemTop }, { x: itemLeft, y: itemBottom }) ||\r\n                            segmentsIntr({ x: selectBoxLeft, y: selectBoxBottom }, { x: selectBoxRight, y: selectBoxBottom }, { x: itemRight, y: itemTop }, { x: itemRight, y: itemBottom }) ||\r\n                            //纵向与横向\r\n                            segmentsIntr({ x: selectBoxLeft, y: selectBoxTop }, { x: selectBoxLeft, y: selectBoxBottom }, { x: itemLeft, y: itemTop }, { x: itemRight, y: itemTop }) ||\r\n                            segmentsIntr({ x: selectBoxLeft, y: selectBoxTop }, { x: selectBoxLeft, y: selectBoxBottom }, { x: itemLeft, y: itemBottom }, { x: itemRight, y: itemBottom }) ||\r\n                            segmentsIntr({ x: selectBoxRight, y: selectBoxTop }, { x: selectBoxRight, y: selectBoxBottom }, { x: itemLeft, y: itemTop }, { x: itemRight, y: itemTop }) ||\r\n                            segmentsIntr({ x: selectBoxRight, y: selectBoxTop }, { x: selectBoxRight, y: selectBoxBottom }, { x: itemLeft, y: itemBottom }, { x: itemRight, y: itemBottom })) {\r\n                            selectItems.eq(i).css('backgroundColor', 'rgba(0, 196, 244, 0.3)');\r\n                        } else if (itemTop > selectBoxTop && itemBottom < selectBoxBottom && itemLeft > selectBoxLeft && itemRight < selectBoxRight ||\r\n                            itemTop < selectBoxTop && itemBottom > selectBoxBottom && itemLeft < selectBoxLeft && itemRight > selectBoxRight) {\r\n                            selectItems.eq(i).css('backgroundColor', 'rgba(0, 196, 244, 0.3)');\r\n                        } else {\r\n                            selectItems.eq(i).css('backgroundColor', '');\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n            function end(e) {\r\n                var lastTime = new Date().getTime(),\r\n                    selectItems = $(attr.itemname),\r\n                    selectBox = $('.mam-selectable-box'),\r\n                    selectBoxTop = selectBox.offset().top,\r\n                    selectBoxBottom = selectBox.get(0).offsetHeight + selectBoxTop,\r\n                    selectBoxLeft = selectBox.offset().left,\r\n                    selectBoxRight = selectBox.get(0).offsetWidth + selectBoxLeft,\r\n                    items = scope.$eval(attr.mamSelectable);\r\n\r\n                if (lastTime - e.data.firstTime > 200) {\r\n                    for (var i = 0; i < selectItems.length; i++) {\r\n                        var itemLeft = selectItems.eq(i).offset().left,\r\n                            itemRight = itemLeft + selectItems.eq(i).width(),\r\n                            itemTop = selectItems.eq(i).offset().top,\r\n                            itemBottom = itemTop + selectItems.eq(i).height();\r\n\r\n                        if (mode === 'single') {\r\n                            selectItems.eq(i).css('backgroundColor', '');\r\n                            if (selectBoxTop > itemTop && selectBoxTop < itemBottom || selectBoxTop === itemTop) {\r\n                                if (items[i].selected === undefined) {\r\n                                    items[i].selected = true;\r\n                                } else {\r\n                                    items[i].selected = !items[i].selected;\r\n                                }\r\n                            } else if (itemTop > selectBoxTop && itemBottom < selectBoxBottom) {\r\n                                if (items[i].selected === undefined) {\r\n                                    items[i].selected = true;\r\n                                } else {\r\n                                    items[i].selected = !items[i].selected;\r\n                                }\r\n                            } else if (selectBoxBottom > itemTop && selectBoxBottom < itemBottom) {\r\n                                if (items[i].selected === undefined) {\r\n                                    items[i].selected = true;\r\n                                } else {\r\n                                    items[i].selected = !items[i].selected;\r\n                                }\r\n                            }\r\n                        } else if (mode === 'multi') {\r\n                            selectItems.eq(i).css('backgroundColor', '');\r\n                            if (segmentsIntr({ x: selectBoxLeft, y: selectBoxTop }, { x: selectBoxRight, y: selectBoxTop }, { x: itemLeft, y: itemTop }, { x: itemLeft, y: itemBottom }) ||\r\n                                segmentsIntr({ x: selectBoxLeft, y: selectBoxTop }, { x: selectBoxRight, y: selectBoxTop }, { x: itemRight, y: itemTop }, { x: itemRight, y: itemBottom }) ||\r\n                                segmentsIntr({ x: selectBoxLeft, y: selectBoxBottom }, { x: selectBoxRight, y: selectBoxBottom }, { x: itemLeft, y: itemTop }, { x: itemLeft, y: itemBottom }) ||\r\n                                segmentsIntr({ x: selectBoxLeft, y: selectBoxBottom }, { x: selectBoxRight, y: selectBoxBottom }, { x: itemRight, y: itemTop }, { x: itemRight, y: itemBottom }) ||\r\n                                //纵向与横向\r\n                                segmentsIntr({ x: selectBoxLeft, y: selectBoxTop }, { x: selectBoxLeft, y: selectBoxBottom }, { x: itemLeft, y: itemTop }, { x: itemRight, y: itemTop }) ||\r\n                                segmentsIntr({ x: selectBoxLeft, y: selectBoxTop }, { x: selectBoxLeft, y: selectBoxBottom }, { x: itemLeft, y: itemBottom }, { x: itemRight, y: itemBottom }) ||\r\n                                segmentsIntr({ x: selectBoxRight, y: selectBoxTop }, { x: selectBoxRight, y: selectBoxBottom }, { x: itemLeft, y: itemTop }, { x: itemRight, y: itemTop }) ||\r\n                                segmentsIntr({ x: selectBoxRight, y: selectBoxTop }, { x: selectBoxRight, y: selectBoxBottom }, { x: itemLeft, y: itemBottom }, { x: itemRight, y: itemBottom })) {\r\n                                if (items[i].selected === undefined) {\r\n                                    items[i].selected = true;\r\n                                } else {\r\n                                    items[i].selected = !items[i].selected;\r\n                                }\r\n                            } else if (itemTop > selectBoxTop && itemBottom < selectBoxBottom && itemLeft > selectBoxLeft && itemRight < selectBoxRight ||\r\n                                itemTop < selectBoxTop && itemBottom > selectBoxBottom && itemLeft < selectBoxLeft && itemRight > selectBoxRight) {\r\n                                if (items[i].selected === undefined) {\r\n                                    items[i].selected = true;\r\n                                } else {\r\n                                    items[i].selected = !items[i].selected;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                } else {\r\n                    for (var q = 0; q < selectItems.length; q++) {\r\n                        selectItems.eq(q).css('backgroundColor', '');\r\n                    }\r\n                }\r\n\r\n                element.off('mousemove').children('.mam-selectable-box').remove();\r\n                $(document).off();\r\n\r\n                $('body').css('userSelect', '').attr('ondragstart', '');\r\n                $('head').children('style[key=\"select\"]').remove();\r\n\r\n                scope.$applyAsync();\r\n            }\r\n\r\n        }\r\n    };\r\n});\n\n/***/ }),\n/* 29 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\nObject.defineProperty(__webpack_exports__, \"__esModule\", { value: true });\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less__ = __webpack_require__(71);\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__style_less__);\n\r\n\r\nangular.module('mam-ng')\r\n    .directive('mamSortGroup', function () {\r\n        return {\r\n            restrict: 'E',\r\n            template: __webpack_require__(57),\r\n            replace: true,\r\n            scope: {\r\n                ngModel: '=',\r\n                onChange: '&?' //排序方式发生改变时\r\n            },\r\n            compile: function (element, attr) {\r\n                var icon = __webpack_require__(3);\r\n                var $icon1 = $(icon);\r\n                $icon1.attr('ng-if', 'ngModel.current.hideDirection!==true')\r\n                    .attr('ng-class', '{active:ngModel.current.desc==\\'desc\\'}');\r\n                element.find('button').append($icon1);\r\n\r\n                var $icon2 = $(icon);\r\n                $icon2.attr('ng-if', 'item.hideDirection!==true')\r\n                    .attr('ng-class', '{active:item.desc==\\'desc\\'}');\r\n                element.find('li a').append($icon2);\r\n\r\n                return function (scope, element, attr) {\r\n                    scope.icon = __webpack_require__(3);\r\n\r\n                    scope.change = function (item) {\r\n                        if (item != scope.ngModel.current) {\r\n                            if (!_.isEmpty(scope.ngModel.storageKey)) {\r\n                                localStorage.setItem(scope.ngModel.storageKey, JSON.stringify(item));\r\n                            }\r\n                            scope.ngModel.current = item;\r\n                            scope.onChange({ item: item });\r\n                        }\r\n                    }\r\n\r\n                    function init() {\r\n                        if (scope.ngModel.current == null) {\r\n                            if (scope.ngModel.storageKey != null) {\r\n                                var item = localStorage.getItem(scope.ngModel.storageKey);\r\n                                if (!_.isEmpty(item)) {\r\n                                    scope.ngModel.current = JSON.parse(item);\r\n                                    return;\r\n                                }\r\n                            }\r\n                            scope.ngModel.current = _.find(scope.ngModel.items, { default: true });\r\n                            if (scope.ngModel.current == null) {\r\n                                scope.ngModel.current = scope.ngModel.items[0];\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    init();\r\n                }\r\n            }\r\n        };\r\n    });\n\n/***/ }),\n/* 30 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\nObject.defineProperty(__webpack_exports__, \"__esModule\", { value: true });\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less__ = __webpack_require__(72);\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__style_less___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__style_less__);\n\r\n\r\nangular.module('mam-ng').directive('mamSpin', function () {\r\n    return {\r\n        restrict: 'E',\r\n        template: __webpack_require__(58),\r\n        replace: true,\r\n        scope: {\r\n            text: '@?',\r\n            show: '<'\r\n        },\r\n        link: function (scope, element, attr) {\r\n            var parent = element.parent();\r\n            if (parent.css('position') == 'static' || parent.css('position') == 'initial') {\r\n                parent.css('position', 'relative');\r\n            }\r\n        }\r\n    };\r\n});\n\n/***/ }),\n/* 31 */\n/***/ (function(module, exports, __webpack_require__) {\n\n__webpack_require__(73);\r\n\r\nangular.module('mam-ng')\r\n    .directive('mamSwitchButton', [function () {\r\n        return {\r\n            restrict: 'EA',\r\n            template: __webpack_require__(59),\r\n            transclude: true,\r\n            replace: true,\r\n            require: \"ngModel\",\r\n            scope: {\r\n                onChange : \"=\"\r\n            },\r\n            link: function (scope, element, attr,ngModel) {\r\n                scope.changeEnable = function(){\r\n                    scope.val = !ngModel.$viewValue;\r\n                    ngModel.$setViewValue(scope.val);\r\n                    if (scope.onChange && typeof(scope.onChange) == \"function\")\r\n                    {\r\n                        scope.onChange(scope.val);\r\n                    }\r\n                };\r\n\r\n                ngModel.$render = function(){\r\n                    scope.val = ngModel.$viewValue;\r\n                };\r\n            }\r\n\r\n        };\r\n    }]);\n\n/***/ }),\n/* 32 */\n/***/ (function(module, exports, __webpack_require__) {\n\n__webpack_require__(74);\r\n\r\n//todo:我的消息，支持显示未读消息数量\r\n\r\nangular.module('mam-ng')\r\n    .directive('mamTopNav', ['$sce', '$timeout', '$http', function ($sce, $timeout, $http) {\r\n        return {\r\n            restrict: 'E',\r\n            template: __webpack_require__(60),\r\n            transclude: true,\r\n            replace: true,\r\n            scope: {\r\n                ngModel: '<?',\r\n                currentUser: '<?'\r\n            },\r\n            link: function (scope, element, attr) {\r\n                var $e = $(element);\r\n                var $navs = $e.find('.navs');\r\n                var $more = $e.find('.more').hide();\r\n                var $moreSubs = $more.find('.subs');\r\n                var moreWidth;\r\n                var navs = [];\r\n                scope.logged = false;\r\n\r\n                function prepare() {\r\n                    var items = [];\r\n\r\n                    for (var i = 0; i < scope.ngModel.length; i++) {\r\n                        var item = scope.ngModel[i];\r\n                        // 判断启用状态\r\n                        if (!item.enable) {\r\n                            continue;\r\n                        }\r\n                        // 判断分割线\r\n                        if (item.content == '|') {\r\n                            items.push(item);\r\n                            continue;\r\n                        }\r\n                        //判断模块开关\r\n                        if (_.isString(item.module)) {\r\n                            var key = item.module.indexOf('Enable') == -1 ? (item.module + 'Enable') : item.module;\r\n                            if (nxt.config[key] !== true) {\r\n                                continue;\r\n                            }\r\n                        }\r\n                        // 判断权限\r\n                        if (_.isString(item.appPermission) && item.appPermission.length > 0) {\r\n                            if (!nxt.permission.judge(item.appPermission)) {\r\n                                continue;\r\n                            }\r\n                        }\r\n\r\n                        if (_.isString(item.content)) {\r\n                            item.content = $sce.trustAsHtml(item.content);\r\n                        }\r\n                        if (!item.target || item.target === 'auto') {\r\n                            item.target = '_self';\r\n                        }\r\n                        item.href = mam.utils.eval(mam.path(item.href));\r\n                        items.push(item);\r\n                    }\r\n\r\n                    for (var i = 0; i < items.length; i++) {\r\n                        //去除第一个和连续的分割线\r\n                        if (items[i].content == '|') {\r\n                            if (i == 0 || items[i - 1].content == '|') {\r\n                                continue;\r\n                            }\r\n                        }\r\n                        // if (items[i].class == 'nav-message') {\r\n                        //     getUnreadCount();\r\n                        // }\r\n                        var nav = renderNav(items[i]);\r\n                        navs.push(nav);\r\n                        $navs.append(nav);\r\n                    }\r\n\r\n                    _.forEach(navs, function (item, i) {\r\n                        scope.ngModel[i].width = item.outerWidth();\r\n                    });\r\n\r\n                    moreWidth = $more.outerWidth();\r\n\r\n                    if (navs.length > 0) {\r\n                        $more.hover(function () {\r\n                            $moreSubs.stop().css('height', 'auto').slideDown(300);\r\n                        }, function () {\r\n                            $moreSubs.stop().slideUp(300);\r\n                        }).show();\r\n                    } else {\r\n                        $more.hide();\r\n                    }\r\n\r\n                    $timeout(function() {\r\n                        resize();\r\n                    }, 500);\r\n                }\r\n\r\n                function resize() {\r\n                        $navs.html('');\r\n                        $moreSubs.html('');\r\n                        var boxWidth = $e.outerWidth();\r\n                        var usedWidth = moreWidth;\r\n                        var index = -1;\r\n                        _.forEach(navs, function (item, i) {\r\n                            usedWidth += scope.ngModel[i].width;\r\n                            if (usedWidth <= boxWidth) {\r\n                                $navs.append(item.removeClass('more-item').addClass('item'));\r\n                            } else {\r\n                                index = i;\r\n                                return false;\r\n                            }\r\n                        });\r\n                        if (index > -1) {\r\n                            var width = 0;\r\n                            for (var i = index; i < navs.length; i++) {\r\n                                width += scope.ngModel[i].width;\r\n                            }\r\n                            if (width > moreWidth) {\r\n                                for (var i = index; i < navs.length; i++) {\r\n                                    $moreSubs.append(navs[i].removeClass('item').addClass('more-item'));\r\n                                }\r\n                                $moreSubs.css({ top: $more.height() });\r\n                                $more.show();\r\n                            } else {\r\n                                for (var i = index; i < navs.length; i++) {\r\n                                    $navs.append(navs[i].removeClass('more-item').addClass('item'));\r\n                                }\r\n                                $more.hide();\r\n                            }\r\n                        } else {\r\n                            $more.hide();\r\n                        }\r\n                        activeCurrentItem();\r\n                }\r\n\r\n                function renderNav(item) {\r\n                    var $item = $('<div class=\"item\" title=\"' + item.tooltip + '\">' +\r\n                        '<a href=\"' + item.href + '\">' + item.content + '</a>' +\r\n                        '</div>');\r\n                    if (item.class) {\r\n                        $item.addClass(item.class);\r\n                    }\r\n                    return $item;\r\n                }\r\n\r\n                function activeCurrentItem() {\r\n                    $navs.find('.item').each(function (index, item) {\r\n                        var href = $(item).find('a').attr('href');\r\n                        if (href.indexOf('#!') > -1)//去掉子路由防止顶部菜单不选中\r\n                        {\r\n                            href = href.substring(0, href.indexOf('#!'));\r\n                        }\r\n                        if (href != '' && href != '#' && location.href.indexOf(href) != -1) {\r\n                            $(item).addClass('current');\r\n                        } else {\r\n                            $(item).removeClass('current');\r\n                        }\r\n                    });\r\n                }\r\n\r\n                //获取未读消息\r\n                //获取未读消息这块的功能还没调整完\r\n                function getUnreadCount() {\r\n                    if (!_.get(window, 'nxt.config.deskEnable.unreadTip')) {\r\n                        return\r\n                    };\r\n                    scope.badge = {\r\n                        message: 0,\r\n                        share: 0,\r\n                        total: function () {\r\n                            return this.message + this.share;\r\n                        }\r\n                    }\r\n                    $timeout(function () {\r\n                        $http.get('~/user/get-user-unread-count').then(function (res) {\r\n                            scope.badge.message = !res.data ? 0 : res.data;\r\n                        });\r\n                        //获取未读分享\r\n                        $http.get('~/share/get-user-unread-count').then(function (res) {\r\n                            scope.badge.share = !res.data ? 0 : res.data;\r\n                        });\r\n                    }, 4000);\r\n                }\r\n\r\n                function init() {\r\n                    scope.currentUser = scope.currentUser || _.get(window, 'nxt.user.current');\r\n                    scope.logged = scope.currentUser != null && scope.currentUser.loginName != 'guest';\r\n\r\n                    if (scope.ngModel == null || scope.ngModel == '') {\r\n                        scope.ngModel = _.get(window, 'nxt.config.topNav', []);\r\n                        prepare();\r\n                    } else {\r\n                        scope.$watch('ngModel', function (newValue, oldValue) {\r\n                            if (_.isArray(newValue)) {\r\n                                prepare();\r\n                            }\r\n                        });\r\n                    }\r\n\r\n                    scope.$on('refreshCount', function (e, data) {\r\n                        if (_.get(window, 'nxt.config.deskEnable.unreadTip')) { //导航添加未读消息数量\r\n                            if (data == undefined) {\r\n                                scope.badge.message = 0;\r\n                                scope.badge.share = 0;\r\n                            } else {\r\n                                if (data.message != undefined)\r\n                                    scope.badge.message = data.message;\r\n                                if (data.share != undefined)\r\n                                    scope.badge.share = data.share;\r\n                            }\r\n                        }\r\n                    });\r\n\r\n                    $(window).on('resize', resize);\r\n\r\n                    $(window).bind('hashchange', activeCurrentItem);\r\n                }\r\n\r\n                init();\r\n            }\r\n\r\n        };\r\n    }]);\n\n/***/ }),\n/* 33 */\n/***/ (function(module, exports, __webpack_require__) {\n\n__webpack_require__(75);\r\n\r\nangular.module('mam-ng').provider('$mamUserAvatar', function () {\r\n    var defaults = this.defaults = {\r\n        errorSrc: mam.path('~/test/img/error.jpg')\r\n    }\r\n    this.$get = function () {\r\n        return { defaults: defaults };\r\n    };\r\n}).directive('mamUserAvatar', ['$timeout','$mamUserAvatar',function ($timeout,$mamUserAvatar) {\r\n    var opts = $mamUserAvatar.defaults;\r\n    return {\r\n        restrict: 'E',\r\n        template: '<div class=\"mam-user-avatar\"><img /></div>',\r\n        scope: {\r\n            src: '<',\r\n        },\r\n        transclude: false,\r\n        replace: true,\r\n        link: function (scope, element, attr) {\r\n            var $src,\r\n                $errorSrc = opts.errorSrc;\r\n\r\n            function init(){\r\n                if(scope.src !== undefined) {\r\n                    $src = scope.src;\r\n                } else {\r\n                    $src = $errorSrc;\r\n                }\r\n\r\n                var imgObj = new Image();\r\n                $(imgObj).on('load',handleLoad);\r\n                $(imgObj).on('error',handleError);\r\n                imgObj.src = $src;\r\n            }\r\n\r\n            function handleLoad() {\r\n                $(element).find('img').attr('src',$src);\r\n            }\r\n\r\n            function handleError() {\r\n                $(element).find('img').attr('src',$errorSrc);\r\n            }\r\n\r\n            $timeout(function(){\r\n                init();\r\n            });\r\n\r\n            scope.$watch('src', function(newval, oldval) {\r\n                if(oldval !== newval) {\r\n                    init();\r\n                }\r\n            })\r\n        }\r\n    }\r\n}]);\n\n/***/ }),\n/* 34 */\n/***/ (function(module, exports, __webpack_require__) {\n\n__webpack_require__(76);\r\n\r\nangular.module('mam-ng')\r\n    .directive('mamUserInfo', ['$sce', function ($sce) {\r\n        return {\r\n            restrict: 'E',\r\n            template: __webpack_require__(61),\r\n            transclude: true,\r\n            scope: {\r\n                currentUser: \"<\"\r\n            },\r\n            link: function (scope, element, attr) {\r\n                var $e = $(element);\r\n\r\n\r\n                function init() {\r\n                    scope.logged = true;\r\n                    scope.currentUser = scope.currentUser || nxt.user.current;\r\n                    if (!scope.currentUser || scope.currentUser.loginName === \"guest\") {\r\n                        scope.logged = false;\r\n                    } else {\r\n                        var $sub = $e.find('.sub-nav');\r\n                        $sub.css({ top: $e.height() });\r\n                        element.hover(function () {\r\n                            $sub.stop().css('height', 'auto').slideDown(300);\r\n                        }, function () {\r\n                            $sub.stop().slideUp(300);\r\n                        });\r\n                    }\r\n                }\r\n\r\n                init();\r\n\r\n                scope.isCurrent = function (href) {\r\n                    return window.location.href.indexOf(href.replace(/\\~/g, '')) !== -1;\r\n                }\r\n            }\r\n\r\n        };\r\n    }]);\n\n/***/ }),\n/* 35 */\n/***/ (function(module, exports) {\n\n/**\r\n * 表单验证插件，包含directive和service\r\n *\r\n * 注：千万不要将form标签放到ng-if等会创建scope的指令内，不然使用此插件会有意想不到的坑\r\n */\r\nangular.module('mam-ng')\r\n    .service('mamValidationService', function() {\r\n        //非空\r\n        this.nullValidate = function(value) {\r\n            if (value == undefined || value.toString().replace(/(^\\s*)|(\\s*$)/g, \"\").length == 0) {\r\n                return false;\r\n            } else {\r\n                return true;\r\n            }\r\n        };\r\n\r\n        //验证空格\r\n        this.blankValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            if (/(\\s+)/g.test(value)) {\r\n                return false;\r\n            } else {\r\n                return true;\r\n            }\r\n        };\r\n\r\n        //长度\r\n        this.lengthValidate = function(value, min, max) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            if (value.toString().length >= min && value.toString().length <= max) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        };\r\n\r\n        //integer大小\r\n        this.numValueValidate = function(value, min, max) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            if (!isNaN(value) && parseInt(value, 10) >= min && parseInt(value, 10) <= max) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        };\r\n\r\n        //密码验证\r\n        this.pswValidate = function(value, formName, confirmId, $scope) {\r\n            if (!$scope || !$scope[formName]) {\r\n                return true;\r\n            }\r\n            if (!value && !$scope[formName][confirmId]) {\r\n                return true;\r\n            }\r\n            if ($scope[formName][confirmId] != value) {\r\n                return false;\r\n            } else {\r\n                return true;\r\n            }\r\n        };\r\n\r\n        //是否数字\r\n        this.numValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            if (isNaN(value)) {\r\n                return false;\r\n            } else {\r\n                return true;\r\n            }\r\n        };\r\n\r\n        //是否是整数\r\n        this.integerValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            if (!/^-?\\d+$/.test(value)) {\r\n                return false;\r\n            }\r\n            return true;\r\n        };\r\n\r\n        //是否是正数\r\n        this.positiveNumberValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            if (isNaN(value) || value < 0) {\r\n                return false;\r\n            }\r\n            return true;\r\n        };\r\n\r\n        //日期格式\r\n        this.dateValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            var thePat = /^\\d{4}-(0?[1-9]|1[0-2])-(0?[1-9]|[1-2]\\d|3[0-1]) ([0-1]\\d|2[0-3]):[0-5]\\d:[0-5]\\d$/; //带时分秒\r\n            var theShortPat = /^\\d{4}-(0?[1-9]|1[0-2])-(0?[1-9]|[1-2]\\d|3[0-1])$/; //年月日\r\n            var theShortPat2 = /^\\d{4}-(0?[1-9]|1[0-2])$/; //年月\r\n            var theShortPat3 = /^\\d{4}$/; //年份\r\n            var theShortPat4 = /^([0-1]\\d|2[0-3]):[0-5]\\d$/; //时分\r\n            var theShortPat5 = /^([0-1]\\d|2[0-3]):[0-5]\\d:[0-5]\\d$/; //时分秒\r\n            if (thePat.test(value) || theShortPat.test(value) || theShortPat2.test(value) || theShortPat3.test(value) || theShortPat4.test(value) || theShortPat5.test(value)) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        };\r\n\r\n        //特殊字符验证\r\n        this.specialCharValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            var pattern = new RegExp(\"[`~!@%#$^&*()=|{}':;',\\\\[\\\\]<>/?\\\\.；：%……+￥（）【】‘”“'。，、？]\");\r\n            if (pattern.test(value)) {\r\n                return false;\r\n            } else {\r\n                return true;\r\n            }\r\n        };\r\n\r\n        //脚本验证\r\n        this.scriptCharValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            var pattern = new RegExp(\"<script(\\\\s.*)?>.*</script>\");\r\n            if (pattern.test(value)) {\r\n                return false;\r\n            } else {\r\n                return true;\r\n            }\r\n        };\r\n\r\n        //特殊字符验证\r\n        this.noChineseValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            if (value.match(/[^\\x00-\\xff]/ig)) {\r\n                return false;\r\n            } else {\r\n                return true;\r\n            }\r\n        };\r\n\r\n        //电话号码验证\r\n        this.phoneValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            var pat = /(^((([0-9]){3,4}[\\\\-])|([\\\\(]([0-9]){3,4}[\\\\)]))?[0-9]{4,8}$)|(^[0-9]{11}$)/;\r\n            if (pat.test(value)) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        };\r\n\r\n        //验证email\r\n        this.emailValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            var pat = /^([a-zA-Z0-9_\\\\.]{1,127}){1}[@]{1}([a-zA-Z0-9_\\\\.]{1,127}){1}$/;\r\n            if (pat.test(value)) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        };\r\n\r\n        //验证邮编\r\n        this.postcodesValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            var pat = /^[1-9][0-9]{5}$/;\r\n            if (pat.test(value)) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        };\r\n\r\n        //验证ip\r\n        this.ipValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            var pat = /^(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])$/;\r\n            if (pat.test(value)) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        };\r\n\r\n        this.mobileValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            var pat = /^[0-9]{11}$/;\r\n            if (pat.test(value)) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        };\r\n\r\n        //UNC地址验证\r\n        this.uncAddressValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            var pat = /^\\\\\\\\.*\\\\.*$/;\r\n            if (pat.test(value)) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        };\r\n\r\n        //FTP地址验证\r\n        this.ftpAddressValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            var pat = /^ftp:\\/\\//;\r\n            if (pat.test(value)) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        };\r\n\r\n        //HDD地址验证\r\n        this.hddAddressValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            var pat = /^[A-Z,a-z]:\\\\/;\r\n            if (pat.test(value)) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        };\r\n\r\n        //OSS地址验证\r\n        this.ossAddressValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            var pat = /^oss:\\/\\//;\r\n            if (pat.test(value)) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        };\r\n    });\r\n\r\nangular.module('mam-ng')\r\n    .directive('mamValidationList', ['$rootScope', '$compile', '$timeout', '$q', 'mamValidationService', function($rootScope, $compile, $timeout, $q, mamValidationService) {\r\n        function getParentForm($element) {\r\n            if (!$element.parent()[0]) {\r\n                return undefined;\r\n            }\r\n            if ($element.parent()[0].tagName == \"HTML\") {\r\n                return undefined;\r\n            }\r\n            if ($element.parent()[0].tagName == \"FORM\") {\r\n                return $element.parent();\r\n            } else {\r\n                return getParentForm($element.parent());\r\n            }\r\n        }\r\n\r\n        //执行表单验证\r\n        function validateAll(scope, formName) {\r\n            var defer = $q.defer();\r\n            if (!scope || !scope.$mamValidationInfo) {\r\n                return;\r\n            }\r\n            scope.$mamValidationInfo.$hasValidateAll = true; //当执行全部表单验证方法后，$needValid的作用将取消\r\n            var formValidator = scope.$mamValidationInfo.forms[formName];\r\n            var elementValidator;\r\n            var ngModel;\r\n            for (var eleName in formValidator) {\r\n                elementValidator = formValidator[eleName];\r\n                ngModel = elementValidator.ngModel;\r\n                angular.forEach(ngModel.$formatters, function(formatter) {\r\n                    formatter(ngModel.$viewValue);\r\n                });\r\n            }\r\n            defer.resolve();\r\n            return defer.promise;\r\n        };\r\n\r\n        return {\r\n            restrict: \"A\",\r\n            require: \"ngModel\",\r\n            compile: function() {\r\n                return {\r\n                    pre: function preLink($scope, $element, $attrs, $ngModel) {},\r\n                    post: function postLink($scope, $element, $attrs, $ngModel) {\r\n                        var $needValid = false; //定义此值避免首次加载页面执行验证，比如非空验证会导致输入框出现红框不好看\r\n                        var form = getParentForm($element);\r\n                        var formName;\r\n                        if (form) {\r\n                            formName = form.attr(\"name\");\r\n\r\n                            var formValidator;\r\n                            var elementValidator;\r\n                            if (!$scope.$mamValidationInfo) {\r\n                                $scope.$mamValidationInfo = {};\r\n                            }\r\n                            if (!$scope.$mamValidationInfo.forms) {\r\n                                $scope.$mamValidationInfo.forms = {};\r\n                            }\r\n                            if (!$scope.$mamValidationInfo.forms[formName]) {\r\n                                $scope.$mamValidationInfo.forms[formName] = {};\r\n                            }\r\n                            if (!$scope.$mamValidationInfo.validateAll) {\r\n                                $scope.$mamValidationInfo.validateAll = function(formName) {\r\n                                    return validateAll($scope, formName);\r\n                                };\r\n                                mamValidationService.validateAll = function(formName) {\r\n                                    return validateAll($scope, formName);\r\n                                }\r\n                            }\r\n                            formValidator = $scope.$mamValidationInfo.forms[formName];\r\n                            if (!formValidator[$element.attr(\"name\")]) {\r\n                                formValidator[$element.attr(\"name\")] = {};\r\n                            }\r\n                            elementValidator = formValidator[$element.attr(\"name\")];\r\n                            elementValidator.element = $element;\r\n                            elementValidator.ngModel = $ngModel;\r\n                            if ($attrs.mamValidationList) {\r\n                                var vList = JSON.parse($attrs.mamValidationList.replace(/'/g, \"\\\"\"));\r\n                                elementValidator.validateList = vList;\r\n                            }\r\n\r\n                            var validateList = elementValidator.validateList; //验证列表\r\n                            //添加验证\r\n                            var cusValidate = function(value) {\r\n                                var validity = true;\r\n                                if (validateList && typeof(validateList) == \"object\" && validateList instanceof Array) {\r\n                                    var validate;\r\n                                    var fun;\r\n                                    var validResult;\r\n                                    for (var i = 0, j = validateList.length; i < j; i++) {\r\n                                        validate = validateList[i];\r\n                                        if (!$needValid && !$scope.$mamValidationInfo.$hasValidateAll) {\r\n                                            continue;\r\n                                        }\r\n                                        fun = mamValidationService[validate.name];\r\n                                        var param = [];\r\n                                        param.push(value);\r\n                                        if (validate.param && typeof(validate.param) == \"object\" && validate.param instanceof Array) {\r\n                                            for (var x = 0, y = validate.param.length; x < y; x++) {\r\n                                                param.push(validate.param[x]);\r\n                                            }\r\n                                        }\r\n                                        param.push($scope);\r\n\r\n                                        validResult = fun.apply(this, param);\r\n                                        if (typeof(validResult) == \"boolean\") //boolean\r\n                                        {\r\n                                            if (!validResult) {\r\n                                                validity = false;\r\n                                                $ngModel.$setValidity(validate.validity, false);\r\n                                            } else {\r\n                                                $ngModel.$setValidity(validate.validity, true);\r\n                                            }\r\n                                        } else if (typeof(validResult) == \"object\") //promise\r\n                                        {\r\n                                            validResult.then(function() {\r\n                                                $ngModel.$setValidity(validate.validity, true);\r\n                                            }, function() {\r\n                                                validity = false;\r\n                                                $ngModel.$setValidity(validate.validity, false);\r\n                                            });\r\n                                        }\r\n                                    }\r\n                                }\r\n                                return validity ? value : undefined;\r\n                            }\r\n                            $ngModel.$parsers.push(cusValidate);\r\n                            $ngModel.$formatters.push(cusValidate);\r\n                        }\r\n\r\n                        $element.on(\"blur keydown change\", function() {\r\n                            $needValid = true;\r\n                        });\r\n                    }\r\n                };\r\n            }\r\n        };\r\n    }]);\n\n/***/ }),\n/* 36 */\n/***/ (function(module, exports) {\n\nvar app = angular.module('mam-ng');\r\n\r\napp.filter('trusted', ['$sce', function ($sce) {\r\n    return function (text) {\r\n        if (_.isString(text)) {\r\n            return $sce.trustAsHtml(text);\r\n        }\r\n        return text;\r\n    };\r\n}]);\r\n\r\napp.filter('entityTypeName', function () {\r\n    return function (code) {\r\n        if (_.isEmpty(code)) {\r\n            return '';\r\n        }\r\n        var type = _.find(mam.entity.types, { code: code });\r\n        if (type == null) {\r\n            if (code == 'hypermedia') {\r\n                return l('com.' + code, '图文');\r\n            }\r\n            if (code == 'folder') {\r\n                return l('com.' + code, '文件夹');\r\n            }\r\n            return code;\r\n        }\r\n        return l('com.' + code, type.name);\r\n    }\r\n});\r\n\r\napp.filter('entityTypeShortName', function () {\r\n    return function (code, ext) {\r\n        if (nxt.config.extTagEnable) {\r\n            if ((ext == null || ext == '')) {\r\n                if (!nxt.config.typeTagEnable)\r\n                    return '';\r\n            } else{\r\n                var ret = ext.replace('.', '').toUpperCase();\r\n                if (ret.indexOf('?') > -1)\r\n                {\r\n                    ret = ret.substring(0, ret.indexOf('?'));\r\n                }\r\n                return ret;\r\n            }\r\n        }\r\n\r\n        if (code == null || code == '' || !nxt.config.typeTagEnable) {\r\n            return '';\r\n        }\r\n        var type = _.find(mam.entity.types, { code: code });\r\n        if (type == null) {\r\n            type = _.find(mam.entity.types, { namespace: code });\r\n        }\r\n        if (type != null) {\r\n            if (type.shortCode != null && type.shortCode.length > 0) {\r\n                return type.shortCode;\r\n            }\r\n        }\r\n\r\n        switch (code.toLowerCase()) {\r\n            case 'video':\r\n            case 'biz_sobey_video':\r\n                return 'V';\r\n\r\n            case 'audio':\r\n            case 'biz_sobey_audio':\r\n                return 'A';\r\n\r\n            case 'picture':\r\n            case 'biz_sobey_picture':\r\n                return 'P';\r\n\r\n            case 'document':\r\n            case 'biz_sobey_document':\r\n                return 'D';\r\n\r\n            case 'hypermedia':\r\n            case 'biz_sobey_hypermedia':\r\n                return 'H';\r\n\r\n            case 'dataset':\r\n                return 'G';\r\n\r\n            case 'program':\r\n            case 'sequence':\r\n            case 'scene':\r\n            case 'short':\r\n                return 'C';\r\n\r\n            case 'rundown':\r\n            case 'biz_sobey_rundown':\r\n                return 'R';\r\n\r\n            case 'script':\r\n            case 'biz_sobey_script':\r\n                return 'S';\r\n\r\n            case 'other':\r\n            case 'biz_sobey_other':\r\n                return 'O';\r\n\r\n            default:\r\n                return code;\r\n        }\r\n    }\r\n});\r\n\r\napp.filter('entityTypeIcon', function () {\r\n    return function (code) {\r\n        var type = _.find(mam.entity.types, { code: code });\r\n        if (type != null && type.icon != null) {\r\n            return type.icon;\r\n        }\r\n        switch (code) {\r\n            case 'video':\r\n                return 'fa fa-film';\r\n            case 'audio':\r\n                return 'fa fa-music';\r\n            case 'picture':\r\n                return 'fa fa-picture-o';\r\n            case 'document':\r\n                return 'fa fa-file-word-o';\r\n            default:\r\n                return 'fa fa-file-o';\r\n        }\r\n    };\r\n});\r\n\r\napp.filter('removeHtml', function () {\r\n    return function (html) {\r\n        return mam.utils.removeHtmlTag(html);\r\n    };\r\n});\r\n\r\napp.filter('videoQualityFlag', function () {\r\n    return function (val) {\r\n        switch (val) {\r\n            case 1:\r\n                return 'flag-SD';\r\n            case 2:\r\n            case 3:\r\n                return 'flag-HD';\r\n            case 4:\r\n                return 'flag-4K'\r\n            default:\r\n                return '';\r\n        }\r\n    };\r\n});\r\n\r\napp.filter('cutString', ['$sce', function ($sce) {\r\n    return function (str, len) {\r\n        var sum = 0,\r\n            newStr = '';\r\n\r\n        if (typeof (str) != 'string') { return null };\r\n        if (!(/^[0-9]*[1-9][0-9]*$/).test(len) || len == 0) { return str; };\r\n\r\n        var matchList = str.split(/(<font\\s\\S*>(\\S+)<\\/font>)/igm);\r\n        // 过滤掉高亮标签\r\n        for (var i = 1; i < matchList.length; i += 3) {\r\n            str = str.replace(matchList[i], matchList[i + 1]);\r\n        }\r\n\r\n        //截取字符\r\n        for (var i = 0; i < str.length; i++) {\r\n            sum += str.charCodeAt(i) > 255 ? 2 : 1;\r\n            if ((sum <= len - 2) || ((i == str.length - 1) && (sum > len - 2))) {\r\n                newStr += str.charAt(i);\r\n            } else {\r\n                newStr += '...';\r\n                break;\r\n            }\r\n        }\r\n        // 还原高亮标签\r\n        for (var i = 1; i < matchList.length; i += 3) {\r\n            newStr = newStr.replace(matchList[i + 1], matchList[i]);\r\n        }\r\n        return newStr;\r\n    };\r\n}]);\r\n\r\napp.filter('formatExt', function () {\r\n    return function (ext) {\r\n        if (!ext) {\r\n            return '';\r\n        }\r\n        var res = ext.replace('.', '');\r\n        res = res.toUpperCase();\r\n        return res;\r\n    };\r\n});\r\n\r\napp.filter('formatSize', function () {\r\n    return function (size, custom) {\r\n        if (size == null) {\r\n            return '-';\r\n        }\r\n        var result = '';\r\n        if (custom) {\r\n            if (size == -1) {\r\n                return '无限制';\r\n            }\r\n            size = size / 1024 / 1024 / 1024;\r\n            if (size <= 0.01 && size > 0) {\r\n                return '0.01 GB';\r\n            } else {\r\n                return (size.toFixed(2)) + ' GB';\r\n            }\r\n        } else {\r\n            result = mam.utils.formatSize(size);\r\n        }\r\n        return result;\r\n    };\r\n});\r\n\r\napp.filter('formatDate', function () {\r\n    return function (time, format) {\r\n        if (time == null || time == '') {\r\n            return '';\r\n        }\r\n        var dateTime = new Date();\r\n        if (angular.isDate(time)) {\r\n            dateTime = time;\r\n        } else {\r\n            dateTime = new Date(time.replace(/-/g, '/')); //兼容IE、火狐\r\n        }\r\n\r\n        if (format == null || format == '') {\r\n            return dateTime.format('yyyy-MM-dd hh:mm:ss');\r\n        } else {\r\n            return dateTime.format(format);\r\n        }\r\n    };\r\n});\r\n\r\napp.filter('comingDateTime', function () {\r\n    return function (time) {\r\n        var result = '';\r\n        var oTime = time.replace(/\\s|\\-|\\/|\\:/g, \",\");\r\n        var timeArr = oTime.split(',');\r\n        var gotDateTime = {\r\n            y: timeArr[0],\r\n            m: timeArr[1] - 1,\r\n            d: timeArr[2],\r\n            h: timeArr[3],\r\n            mi: timeArr[4],\r\n            s: timeArr[5]\r\n        };\r\n        var futureDate = new Date(gotDateTime.y, gotDateTime.m, gotDateTime.d, gotDateTime.h, gotDateTime.mi, gotDateTime.s);\r\n        var nowDate = new Date();\r\n        var dsec = futureDate.getTime() - nowDate.getTime(); //毫秒差\r\n\r\n        var o = parseInt(dsec / 1000); //总秒数\r\n        if (o < 0) {\r\n            result = '0 秒后';\r\n        } else if (o >= 0 && o < 60) {\r\n            result = o + ' 秒后';\r\n        } else if (o >= 60 && o < 3600) {\r\n            result = parseInt(o / 60) + ' 分钟后';\r\n        } else if (o >= 3600 && o < 3600 * 24) {\r\n            result = parseInt(o / 3600) + ' 小时后';\r\n        } else if (o >= 3600 * 24 && o < 3600 * 24 * 30) {\r\n            result = parseInt(o / 3600 / 24) + ' 天后';\r\n        } else if (o >= 3600 * 24 * 30 && o < 3600 * 24 * 30 * 12) {\r\n            result = parseInt(o / 3600 / 24 / 30) + ' 个月后';\r\n        } else if (o >= 3600 * 24 * 30 * 12) {\r\n            result = parseInt(o / 3600 / 24 / 30 / 12) + ' 年后';\r\n        }\r\n\r\n        return result;\r\n    };\r\n});\n\n/***/ }),\n/* 37 */\n/***/ (function(module, exports) {\n\nwindow.nxt = window.nxt || {};\r\n\r\nnxt.user = nxt.user || {};\r\nnxt.permission = nxt.permission || {};\r\n\r\nnxt.permission.judge = function (permissions) {\r\n    if (permissions == null || permissions == '') {\r\n        return true;\r\n    }\r\n    if (_.get(nxt, 'user.current') == null) {\r\n        throw '判断前，必须给user.current赋值';\r\n    }\r\n    var user = nxt.user.current;\r\n    if (!_.isArray(user.appPermission) || user.appPermission.length == 0) {\r\n        return false;\r\n    }\r\n    var prefix = nxt.config.systemCode ? nxt.config.systemCode.toLowerCase() + '_' : '';\r\n\r\n    if (permissions.indexOf(',') == -1) {\r\n        return _.find(user.appPermission, function (item) { return item.toLowerCase() === (prefix + permissions.toLowerCase()) }) != null;\r\n    } else {\r\n        var items = permissions.split(',');\r\n        var error = 0;\r\n        for (var i = 0; i < items.length; i++) {\r\n            if (_.find(user.appPermission, function (ap) { return ap.toLowerCase() === (prefix + items[i].toLowerCase()) }) == null) {\r\n                error++;\r\n            }\r\n        }\r\n        return error != items.length; //满足一个都算通过\r\n    }\r\n}\n\n/***/ }),\n/* 38 */\n/***/ (function(module, exports) {\n\nwindow.mam.utils = window.mam.utils || {};\r\n\r\nmam.utils.comingDateTime = function (time, maxUnit, minTime, exactTo) {\r\n    if (!time) {\r\n        return '';\r\n    }\r\n    var result = '';\r\n    var oTime = time.replace(/\\s|\\-|\\/|\\:/g, \",\");\r\n    var timeArr = oTime.split(',');\r\n    var gotDateTime = {\r\n        y: timeArr[0],\r\n        m: timeArr[1] - 1,\r\n        d: timeArr[2],\r\n        h: timeArr[3],\r\n        mi: timeArr[4],\r\n        s: timeArr[5]\r\n    };\r\n    var futureDate = new Date(gotDateTime.y, gotDateTime.m, gotDateTime.d, gotDateTime.h, gotDateTime.mi, gotDateTime.s);\r\n    var nowDate = new Date();\r\n    var dsec = futureDate.getTime() - nowDate.getTime(); //毫秒差\r\n\r\n    var o = parseInt(dsec / 1000); //总秒数\r\n    if (minTime && o > minTime) {\r\n        return '';\r\n    }\r\n    if (o < 0) {\r\n        result = '';\r\n    } else if (o >= 0 && o < 60) {\r\n        result = o + ' 秒后';\r\n    } else if (o >= 60 && o < 3600) {\r\n        result = parseInt(o / 60) + '分钟后';\r\n    } else if ((o >= 3600 && o < 3600 * 24) || maxUnit == 'hour') {\r\n        result = parseInt(o / 3600) + '小时';\r\n        if(exactTo == 'min')\r\n        {\r\n            result += parseInt((o % 3600) / 60) + '分钟';\r\n        }\r\n        result+='后';\r\n    } else if (o >= 3600 * 24 && o < 3600 * 24 * 30) {\r\n        result = parseInt(o / 3600 / 24) + '天后';\r\n    } else if (o >= 3600 * 24 * 30 && o < 3600 * 24 * 30 * 12) {\r\n        result = parseInt(o / 3600 / 24 / 30) + '个月后';\r\n    } else if (o >= 3600 * 24 * 30 * 12) {\r\n        result = parseInt(o / 3600 / 24 / 30 / 12) + '年后';\r\n    }\r\n\r\n    return result;\r\n}\n\n/***/ }),\n/* 39 */\n/***/ (function(module, exports, __webpack_require__) {\n\nexports = module.exports = __webpack_require__(0)(undefined);\n// imports\n\n\n// module\nexports.push([module.i, \".mam-back-top{position:fixed;display:none;background-color:#fbfbfb;color:#7b7b7b;font-size:46px;bottom:20px;right:20px;overflow:hidden;width:46px;height:46px;text-align:center;line-height:46px;border:1px solid #e7e7e7;border-radius:3px;box-shadow:0 2px 3px rgba(0,0,0,.1);cursor:pointer;z-index:11;-webkit-transition:all .3s;transition:all .3s}.mam-back-top svg{width:24px;height:24px;fill:#b7b7b7;-webkit-transform:translateY(-5px);transform:translateY(-5px)}.mam-back-top:hover{background-color:#e98b11}.mam-back-top:hover svg{fill:#fff}\", \"\"]);\n\n// exports\n\n\n/***/ }),\n/* 40 */\n/***/ (function(module, exports, __webpack_require__) {\n\nexports = module.exports = __webpack_require__(0)(undefined);\n// imports\n\n\n// module\nexports.push([module.i, \".mam-badge{background:red;color:#fff;border-radius:50%;text-align:center}\", \"\"]);\n\n// exports\n\n\n/***/ }),\n/* 41 */\n/***/ (function(module, exports, __webpack_require__) {\n\nexports = module.exports = __webpack_require__(0)(undefined);\n// imports\n\n\n// module\nexports.push([module.i, \".mam-captcha{cursor:pointer}\", \"\"]);\n\n// exports\n\n\n/***/ }),\n/* 42 */\n/***/ (function(module, exports, __webpack_require__) {\n\nexports = module.exports = __webpack_require__(0)(undefined);\n// imports\n\n\n// module\nexports.push([module.i, \".mam-checkbox{position:relative;display:inline-block;min-width:22px;height:22px;line-height:21px;cursor:pointer;margin-bottom:0;font-weight:400}.mam-checkbox svg{left:3px;top:3px;width:14px;height:14px;fill:#e98b11;position:absolute;font-size:12px;-webkit-transition:opacity .2s;transition:opacity .2s}.mam-checkbox:before{content:\\\"\\\";left:0;top:0;position:absolute;border-radius:4px;border:1px solid #ccc;width:20px;height:20px;-webkit-transition:all .3s;transition:all .3s;background:#fff;-moz-box-sizing:border-box;box-sizing:border-box}.mam-checkbox.checked:before{border-color:#e98b11}.mam-checkbox.checked svg{opacity:1}.mam-checkbox.unchecked svg{opacity:0}.mam-checkbox.focus:before,.mam-checkbox:hover:before{border-color:#e98b11;box-shadow:0 0 6px rgba(233,139,17,.5)}.mam-checkbox.checked[disabled],.mam-checkbox[disabled]{cursor:not-allowed}.mam-checkbox.checked[disabled]:before,.mam-checkbox[disabled]:before{background:#eaeaea;border-color:#dcdcdc}.mam-checkbox.checked[disabled]:hover:before,.mam-checkbox[disabled]:hover:before{border-color:#dcdcdc;box-shadow:none}.mam-checkbox.checked[disabled].checked svg,.mam-checkbox[disabled].checked svg{fill:#666}.mam-checkbox span{margin-left:26px}.mam-checkbox input[type=checkbox]{position:absolute;clip:rect(0,0,0,0);pointer-events:none}.mam-checkbox.sm{min-width:18px;height:18px;line-height:17px}.mam-checkbox.sm:before{width:18px;height:18px}.mam-checkbox.sm svg{width:12px;height:12px}\", \"\"]);\n\n// exports\n\n\n/***/ }),\n/* 43 */\n/***/ (function(module, exports, __webpack_require__) {\n\nexports = module.exports = __webpack_require__(0)(undefined);\n// imports\n\n\n// module\nexports.push([module.i, \".mam-keyframe{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center}.mam-keyframe img{max-width:100%;max-height:100%}.mam-keyframe-loaded.mam-keyframe-type-video{background-color:#000}.mam-keyframe-default.mam-keyframe-loaded.mam-keyframe-type-video{background-color:transparent}\", \"\"]);\n\n// exports\n\n\n/***/ }),\n/* 44 */\n/***/ (function(module, exports, __webpack_require__) {\n\nexports = module.exports = __webpack_require__(0)(undefined);\n// imports\n\n\n// module\nexports.push([module.i, \".app-logo{font-family:Microsoft YaHei,Hiragino Sans GB;margin-right:0;margin-left:0;float:left;min-width:230px;-ms-flex-align:center}.app-logo,.app-logo .app-logo-img{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.app-logo .app-logo-img{-webkit-box-flex:1;-webkit-flex:1 0 auto;-ms-flex:1 0 auto;flex:1 0 auto;-ms-flex-align:center;height:100%}.app-logo .app-logo-img img{margin:0 auto;max-height:100%;max-width:100%}.app-logo .app-logo-txt .logo-title{min-width:125px;font-size:23px}.app-logo .app-logo-txt .logo-subtitle{min-width:125px;font-size:12px}\", \"\"]);\n\n// exports\n\n\n/***/ }),\n/* 45 */\n/***/ (function(module, exports, __webpack_require__) {\n\nexports = module.exports = __webpack_require__(0)(undefined);\n// imports\n\n\n// module\nexports.push([module.i, \".mam-pager{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center}.mam-pager .page-total{margin-right:5px}.mam-pager .page-total span{margin:0 4px;color:#e98b11}.mam-pager .pagination{margin-left:8px}.mam-pager .pagination li.active a{color:#e98b11;font-weight:700}.mam-pager .pagination li.active a:hover{background-color:transparent}.mam-pager .pagination li.disabled a:hover{color:#484848}.mam-pager .pagination a{background-color:transparent;color:#484848;border:none}.mam-pager .pagination a:hover{color:#e98b11;background-color:transparent}\", \"\"]);\n\n// exports\n\n\n/***/ }),\n/* 46 */\n/***/ (function(module, exports, __webpack_require__) {\n\nexports = module.exports = __webpack_require__(0)(undefined);\n// imports\n\n\n// module\nexports.push([module.i, \".mam-radio{position:relative;display:inline-block;min-width:22px;height:22px;line-height:21px;cursor:pointer;margin-bottom:0}.mam-radio svg{left:6px;top:7px;width:6px;height:6px;fill:#e98b11;position:absolute;opacity:0;-webkit-transition:opacity .2s;transition:opacity .2s}.mam-radio:before{content:\\\"\\\";left:0;top:1px;position:absolute;border:1px solid #ccc;width:18px;height:18px;border-radius:9px;-webkit-transition:all .3s;transition:all .3s;-moz-box-sizing:border-box;box-sizing:border-box}.mam-radio.checked:before{border-color:#e98b11}.mam-radio.checked svg{opacity:1}.mam-radio.unchecked svg{opacity:0}.mam-radio.focus:before,.mam-radio:hover:before{border-color:#e98b11;box-shadow:0 0 6px rgba(233,139,17,.5)}.mam-radio.checked[disabled],.mam-radio[disabled]{cursor:not-allowed}.mam-radio.checked[disabled]:before,.mam-radio[disabled]:before{background:#eaeaea;border-color:#dcdcdc}.mam-radio.checked[disabled]:hover:before,.mam-radio[disabled]:hover:before{border-color:#dcdcdc;box-shadow:none}.mam-radio.checked[disabled].checked svg,.mam-radio[disabled].checked svg{fill:#acacac}.mam-radio span{margin-left:26px}.mam-radio input[type=radio]{position:absolute;clip:rect(0,0,0,0);pointer-events:none}\", \"\"]);\n\n// exports\n\n\n/***/ }),\n/* 47 */\n/***/ (function(module, exports, __webpack_require__) {\n\nexports = module.exports = __webpack_require__(0)(undefined);\n// imports\n\n\n// module\nexports.push([module.i, \".mam-search-input{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-webkit-flex:1 0 auto;-ms-flex:1 0 auto;flex:1 0 auto;max-width:800px;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;position:relative}.mam-search-input input{-webkit-box-flex:1;-webkit-flex:1 0 auto;-ms-flex:1 0 auto;flex:1 0 auto;border:1px solid #e1e3e2;height:38px;line-height:38px;padding:0 12px;color:#8f8f8f;font-size:14px;outline:none;-webkit-transition:all .3s;transition:all .3s;z-index:101}.mam-search-input input:focus{border-color:#e98b11}.mam-search-input .mam-history-box{width:100%;height:200px;position:absolute;left:0;top:38px;z-index:100;background-color:#fff;border-radius:0 0 5px 5px;box-shadow:0 0 1px 1px rgba(0,0,0,.12)}.mam-search-input .mam-history-box .history-box{height:100%;overflow-y:scroll;border-bottom:1px solid #dcdcdc}.mam-search-input .mam-history-box .history-items{height:100%;margin:0;padding-left:0}.mam-search-input .mam-history-box .history-items li{padding:10px 15px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between;cursor:pointer}.mam-search-input .mam-history-box .history-items li .item-delete{line-height:20px}.mam-search-input .mam-history-box .history-items .list-item-active,.mam-search-input .mam-history-box .history-items .list-item:hover{background-color:#e98b11}.mam-search-input .mam-history-box .history-items .list-item-active span,.mam-search-input .mam-history-box .history-items .list-item:hover span{color:#fff}\", \"\"]);\n\n// exports\n\n\n/***/ }),\n/* 48 */\n/***/ (function(module, exports, __webpack_require__) {\n\nexports = module.exports = __webpack_require__(0)(undefined);\n// imports\n\n\n// module\nexports.push([module.i, \".mam-sort-dropdown button{border-style:none;background-color:transparent}.mam-sort-dropdown button:hover{background-color:#f7f7f7}.mam-sort-dropdown button svg{fill:#e98b11}.mam-sort-dropdown svg{width:12px;height:13px;-webkit-transform:translateY(2px);transform:translateY(2px)}.mam-sort-dropdown svg.active{-webkit-transform:rotate(180deg);transform:rotate(180deg)}\", \"\"]);\n\n// exports\n\n\n/***/ }),\n/* 49 */\n/***/ (function(module, exports, __webpack_require__) {\n\nexports = module.exports = __webpack_require__(0)(undefined);\n// imports\n\n\n// module\nexports.push([module.i, \".mam-spin{position:absolute;left:0;top:0;width:100%;height:100%;background:#fff;opacity:.7;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center}.mam-spin .mam-spin-animation>div{width:12px;height:12px;margin:0 1px;background-color:#5da2f0;border-radius:100%;display:inline-block;-webkit-animation:bouncedelay 1.4s infinite ease-in-out;animation:bouncedelay 1.4s infinite ease-in-out;-webkit-animation-fill-mode:both;animation-fill-mode:both}.mam-spin .mam-spin-animation .bounce1{-webkit-animation-delay:-.32s;animation-delay:-.32s}.mam-spin .mam-spin-animation .bounce2{-webkit-animation-delay:-.16s;animation-delay:-.16s}@-webkit-keyframes bouncedelay{0%,80%,to{-webkit-transform:scale(.4);transform:scale(.4)}40%{-webkit-transform:scale(1);transform:scale(1)}}@keyframes bouncedelay{0%,80%,to{-webkit-transform:scale(.4);transform:scale(.4)}40%{-webkit-transform:scale(1);transform:scale(1)}}.mam-spin .mam-spin-text{font-size:12px}\", \"\"]);\n\n// exports\n\n\n/***/ }),\n/* 50 */\n/***/ (function(module, exports, __webpack_require__) {\n\nexports = module.exports = __webpack_require__(0)(undefined);\n// imports\n\n\n// module\nexports.push([module.i, \".mam-switch-button{display:inline-block}.mam-switch-button[disabled=disabled] .mam-switch-button-normal{opacity:.5;cursor:default}.mam-switch-button-normal{width:52px;height:32px;background:#f0f0f0;border-radius:16px;border:1px solid #d4d4d4;position:relative;cursor:pointer;-webkit-transition:background .3s;transition:background .3s}.mam-switch-button-normal:before{content:\\\"\\\";display:block;width:30px;height:30px;border-radius:50%;background:#ddd;border:1px solid #c2c2c2;position:absolute;-webkit-transition:left .3s;transition:left .3s;left:0}.mam-switch-button-on{background:#337ab7}.mam-switch-button-on:before{left:20px}\", \"\"]);\n\n// exports\n\n\n/***/ }),\n/* 51 */\n/***/ (function(module, exports, __webpack_require__) {\n\nexports = module.exports = __webpack_require__(0)(undefined);\n// imports\n\n\n// module\nexports.push([module.i, \".mam-top-nav{-ms-flex-align:center;-ms-flex-pack:end;-webkit-flex:1 1 auto;-ms-flex:1 1 auto;flex:1 1 auto}.mam-top-nav,.mam-top-nav .navs{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:end;-webkit-justify-content:flex-end;justify-content:flex-end;-webkit-box-flex:1}.mam-top-nav .navs{-ms-flex-pack:end;-ms-flex-align:center;-webkit-flex:1 0 auto;-ms-flex:1 0 auto;flex:1 0 auto;height:100%}.mam-top-nav .more{position:relative;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:end;-webkit-justify-content:flex-end;-ms-flex-pack:end;justify-content:flex-end;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;height:100%}.mam-top-nav .more .subs{position:absolute;display:none;height:auto;right:0;z-index:1000;background:#fff;border:1px solid #ccc;box-shadow:0 0 4px rgba(0,0,0,.2)}.mam-top-nav .item,.mam-top-nav .item a{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;height:100%}.mam-top-nav .item a{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center}.mam-top-nav .more-item{width:110px;border-bottom:1px solid #ccc;height:auto;background:#fff}.mam-top-nav .more-item:last-child{border-bottom:none}.mam-top-nav .more-item a{display:block;text-align:center;padding:10px 0}.mam-top-nav .more-item i{width:20px;text-align:center;margin-right:4px}.mam-top-nav .more-item.nav-separator{display:none}\", \"\"]);\n\n// exports\n\n\n/***/ }),\n/* 52 */\n/***/ (function(module, exports, __webpack_require__) {\n\nexports = module.exports = __webpack_require__(0)(undefined);\n// imports\n\n\n// module\nexports.push([module.i, \".mam-user-avatar img{width:40px;height:40px}\", \"\"]);\n\n// exports\n\n\n/***/ }),\n/* 53 */\n/***/ (function(module, exports, __webpack_require__) {\n\nexports = module.exports = __webpack_require__(0)(undefined);\n// imports\n\n\n// module\nexports.push([module.i, \".mam-top-nav-user{position:relative;cursor:pointer;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;height:100%}.mam-top-nav-user:hover .fa-angle-down{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.mam-top-nav-user a.user-info{padding-left:18px;padding-right:9px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;text-align:center}.mam-top-nav-user a.user-info img{width:40px;height:40px;border-radius:50%}.mam-top-nav-user a.user-info span{margin:0 8px;display:block}.mam-top-nav-user a.user-info .fa-angle-down{-webkit-transition:all .3s;transition:all .3s}.mam-top-nav-user a.user-info:hover{background-color:transparent}.mam-top-nav-user .sub-nav{position:absolute;right:0;width:140px;padding:4px 0;z-index:1000;background:#fff;border:1px solid #ccc;box-shadow:0 0 4px rgba(0,0,0,.2);display:none}.mam-top-nav-user .sub-nav .fa{margin-right:10px}.mam-top-nav-user .sub-nav a{display:block;height:36px;line-height:36px;padding:0 0 0 20px}.mam-top-nav-user-nologin{width:auto;height:100%;padding:0 20px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center}.mam-top-nav-user-nologin a{margin:0 8px}.mam-top-nav-user-nologin a i{display:none}\", \"\"]);\n\n// exports\n\n\n/***/ }),\n/* 54 */\n/***/ (function(module, exports) {\n\nmodule.exports = \"<div class=app-logo> <div class=app-logo-img ng-class=\\\"{'app-default-logo':(config.customLogo.logo==null||config.customLogo.logo=='')&& (config.globalLogo==''||config.globalLogo==null)}\\\"> <img ng-if=\\\"(config.customLogo.logo!=null&& config.customLogo.logo!='') || config.globalLogo!=''\\\" ng-src=\\\"{{config.customLogo.logo!=null && config.customLogo.logo!=''?config.customLogo.logo:config.globalLogo}}\\\"/> </div> <div class=app-logo-txt> <div class=logo-title ng-if=config.customLogo.mainTitleEnable style=\\\"\\\" ng-bind=config.customLogo.mainTitle></div> <div class=logo-subtitle ng-if=config.customLogo.subTitleEnable style=\\\"\\\" ng-bind=config.customLogo.subTitle></div> </div> </div> \";\n\n/***/ }),\n/* 55 */\n/***/ (function(module, exports) {\n\nmodule.exports = \"<div class=mam-pager ng-class=\\\"simple?'mam-pager-simple':'mam-pager-full'\\\"> <div class=page-info> <span ng-show=\\\"showText || pageTotal==1\\\" class=page-total>{{textInfo.total}}<span>{{recordTotal}}</span>{{textInfo.record}}</span> <span ng-show=\\\"showText && pageTotal>1\\\" class=page-code>{{textInfo.index}}<span>{{pageIndex}}</span>/<span>{{pageTotal}}</span></span> </div> <ul uib-pagination total-items=recordTotal ng-model=pageIndex items-per-page=pageSize boundary-links=true rotate=true num-pages=pageTotal previous-text={{textInfo.prev}} next-text={{textInfo.next}} first-text={{textInfo.first}} last-text={{textInfo.last}} ng-change=change(pageIndex) boundary-link-numbers=true max-size=maxSize></ul> </div>\";\n\n/***/ }),\n/* 56 */\n/***/ (function(module, exports) {\n\nmodule.exports = \"<div class=mam-search-input> <input type=text ng-change=sh.getBy() ng-click=\\\"sh.showBox($event,'click')\\\" ng-model=keywords placeholder={{text.placeholder}}> <div class=mam-history-box id=mam-history-box style=display:none> <div class=history-box> <ul class=history-items> <li class=list-item ng-class=\\\"{'list-item-active':item.keyword==selected}\\\" ng-repeat=\\\"item in sh.items\\\" ng-mouseover=changeModel(item.keyword) ng-click=submit(item.keyword) ng-mouseleave=changeModel()> <span class=history-word>{{item.keyword}}</span> <span class=\\\"item-delete fa fa-trash\\\" ng-if=\\\"item.type==0\\\" ng-click=sh.delete(item.id,$event)></span> </li> </ul> </div> </div> </div>\";\n\n/***/ }),\n/* 57 */\n/***/ (function(module, exports) {\n\nmodule.exports = \"<div class=\\\"mam-sort-dropdown btn-group\\\" mam-dropdown> <button type=button class=\\\"btn btn-default dropdown-toggle\\\"> <span>{{ngModel.current.text}}</span> </button> <ul class=dropdown-menu> <li ng-repeat=\\\"item in ngModel.items\\\"> <a ng-click=change(item)> <span>{{item.text}}</span> </a> </li> </ul> </div>\";\n\n/***/ }),\n/* 58 */\n/***/ (function(module, exports) {\n\nmodule.exports = \"<div class=mam-spin ng-if=show> <div class=mam-spin-animation> <div class=bounce1></div> <div class=bounce2></div> <div class=bounce3></div> </div> <span class=mam-spin-text ng-if=\\\"$parent.text != null && $parent.text.length > 0\\\">加载中</span> </div>\";\n\n/***/ }),\n/* 59 */\n/***/ (function(module, exports) {\n\nmodule.exports = \"<div class=mam-switch-button> <div class=mam-switch-button-normal ng-class=\\\"{true : 'mam-switch-button-on',false : ''}[val]\\\" ng-click=changeEnable()></div> </div>\";\n\n/***/ }),\n/* 60 */\n/***/ (function(module, exports) {\n\nmodule.exports = \"<div class=mam-top-nav> <div class=navs ng-show=logged></div> <div class=more ng-show=logged> <div class=item> <a> <i class=\\\"fa fa-ellipsis-h\\\" aria-hidden=true></i> <span>更多</span> </a> </div> <div class=subs></div> </div> </div>\";\n\n/***/ }),\n/* 61 */\n/***/ (function(module, exports) {\n\nmodule.exports = \"<div class=mam-top-nav-user ng-show=logged> <a class=user-info> <mam-user-avatar src=currentUser.avatarUrl></mam-user-avatar> <div> <span class=nickName>{{currentUser.nickName}}</span> <span ng-if=currentUser.haveOrganization>({{currentUser.organization.organizationName}})</span> </div> <i class=\\\"fa fa-angle-down\\\"></i> </a> <div class=sub-nav> <div ng-if=\\\"scoreEnable && currentUser.loginName !== 'guest'\\\" class=score> <a mam-href=~/user/#/center/score/ ><i class=\\\"fa fa-database\\\"></i>积分：{{currentUser.additional.score}}</a> </div> <div ng-show=deskShow.link ng-class=\\\"{'current':isCurrent('/desk/#/main/favorite/')}\\\" ng-if=\\\"currentUser.loginName !== 'guest'\\\"> <a mam-href=~/desk/#/main/favorite/ ><i class=\\\"fa fa-desktop\\\"></i>工作台</a> </div> <div ng-class=\\\"{'current':isCurrent('/desk/#/main/message/')}\\\" ng-if=\\\"currentUser.loginName !== 'guest'\\\"> <a href=\\\"/desk/#/main/message/?page=1\\\"><i class=\\\"fa fa-envelope-o\\\"></i>我的消息</a> </div> <div ng-class=\\\"{'current':isCurrent('/user/#/center/info')}\\\" ng-if=\\\"currentUser.loginName !== 'guest'\\\"> <a mam-href=~/user/#/center/info><i class=\\\"fa fa-user\\\"></i>个人中心</a> </div> <div ng-if=\\\"currentUser.isAdmin && currentUser.loginName !== 'guest'\\\" class=top-manage ng-class=\\\"{'current':iscurrent('/manage/')}\\\"> <a href=/manage/ ><i class=\\\"fa fa-cog\\\"></i>管理中心</a> </div> <div ng-if=\\\"currentUser.isSystemAdmin && currentUser.loginName !== 'guest'\\\" class=top-system ng-class=\\\"{'current':iscurrent('/sysmanage/')}\\\"> <a href=/sysmanage/ target=_blank><i class=\\\"fa fa-cog\\\"></i>系统配置</a> </div> <div> <a mam-href=~/user/exit><i class=\\\"fa fa-power-off\\\"></i>退出</a> </div> </div> </div> <div class=mam-top-nav-user-nologin ng-show=!logged> <a mam-href=~/signup/ ><i class=\\\"fa fa-user-plus\\\"></i>注册</a> <a mam-href=~/login><i class=\\\"fa fa-sign-in\\\"></i>登录</a> </div>\";\n\n/***/ }),\n/* 62 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = __webpack_require__(39);\nif(typeof content === 'string') content = [[module.i, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = __webpack_require__(1)(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(false) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n/***/ }),\n/* 63 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = __webpack_require__(40);\nif(typeof content === 'string') content = [[module.i, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = __webpack_require__(1)(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(false) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n/***/ }),\n/* 64 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = __webpack_require__(41);\nif(typeof content === 'string') content = [[module.i, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = __webpack_require__(1)(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(false) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n/***/ }),\n/* 65 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = __webpack_require__(42);\nif(typeof content === 'string') content = [[module.i, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = __webpack_require__(1)(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(false) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n/***/ }),\n/* 66 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = __webpack_require__(43);\nif(typeof content === 'string') content = [[module.i, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = __webpack_require__(1)(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(false) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n/***/ }),\n/* 67 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = __webpack_require__(44);\nif(typeof content === 'string') content = [[module.i, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = __webpack_require__(1)(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(false) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n/***/ }),\n/* 68 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = __webpack_require__(45);\nif(typeof content === 'string') content = [[module.i, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = __webpack_require__(1)(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(false) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n/***/ }),\n/* 69 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = __webpack_require__(46);\nif(typeof content === 'string') content = [[module.i, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = __webpack_require__(1)(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(false) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n/***/ }),\n/* 70 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = __webpack_require__(47);\nif(typeof content === 'string') content = [[module.i, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = __webpack_require__(1)(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(false) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n/***/ }),\n/* 71 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = __webpack_require__(48);\nif(typeof content === 'string') content = [[module.i, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = __webpack_require__(1)(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(false) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n/***/ }),\n/* 72 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = __webpack_require__(49);\nif(typeof content === 'string') content = [[module.i, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = __webpack_require__(1)(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(false) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n/***/ }),\n/* 73 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = __webpack_require__(50);\nif(typeof content === 'string') content = [[module.i, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = __webpack_require__(1)(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(false) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n/***/ }),\n/* 74 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = __webpack_require__(51);\nif(typeof content === 'string') content = [[module.i, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = __webpack_require__(1)(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(false) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n/***/ }),\n/* 75 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = __webpack_require__(52);\nif(typeof content === 'string') content = [[module.i, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = __webpack_require__(1)(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(false) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n/***/ }),\n/* 76 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = __webpack_require__(53);\nif(typeof content === 'string') content = [[module.i, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = __webpack_require__(1)(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(false) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n/***/ }),\n/* 77 */\n/***/ (function(module, exports) {\n\n\n/**\n * When source maps are enabled, `style-loader` uses a link element with a data-uri to\n * embed the css on the page. This breaks all relative urls because now they are relative to a\n * bundle instead of the current page.\n *\n * One solution is to only use full urls, but that may be impossible.\n *\n * Instead, this function \"fixes\" the relative urls to be absolute according to the current page location.\n *\n * A rudimentary test suite is located at `test/fixUrls.js` and can be run via the `npm test` command.\n *\n */\n\nmodule.exports = function (css) {\n  // get current location\n  var location = typeof window !== \"undefined\" && window.location;\n\n  if (!location) {\n    throw new Error(\"fixUrls requires window.location\");\n  }\n\n\t// blank or null?\n\tif (!css || typeof css !== \"string\") {\n\t  return css;\n  }\n\n  var baseUrl = location.protocol + \"//\" + location.host;\n  var currentDir = baseUrl + location.pathname.replace(/\\/[^\\/]*$/, \"/\");\n\n\t// convert each url(...)\n\t/*\n\tThis regular expression is just a way to recursively match brackets within\n\ta string.\n\n\t /url\\s*\\(  = Match on the word \"url\" with any whitespace after it and then a parens\n\t   (  = Start a capturing group\n\t     (?:  = Start a non-capturing group\n\t         [^)(]  = Match anything that isn't a parentheses\n\t         |  = OR\n\t         \\(  = Match a start parentheses\n\t             (?:  = Start another non-capturing groups\n\t                 [^)(]+  = Match anything that isn't a parentheses\n\t                 |  = OR\n\t                 \\(  = Match a start parentheses\n\t                     [^)(]*  = Match anything that isn't a parentheses\n\t                 \\)  = Match a end parentheses\n\t             )  = End Group\n              *\\) = Match anything and then a close parens\n          )  = Close non-capturing group\n          *  = Match anything\n       )  = Close capturing group\n\t \\)  = Match a close parens\n\n\t /gi  = Get all matches, not the first.  Be case insensitive.\n\t */\n\tvar fixedCss = css.replace(/url\\s*\\(((?:[^)(]|\\((?:[^)(]+|\\([^)(]*\\))*\\))*)\\)/gi, function(fullMatch, origUrl) {\n\t\t// strip quotes (if they exist)\n\t\tvar unquotedOrigUrl = origUrl\n\t\t\t.trim()\n\t\t\t.replace(/^\"(.*)\"$/, function(o, $1){ return $1; })\n\t\t\t.replace(/^'(.*)'$/, function(o, $1){ return $1; });\n\n\t\t// already a full url? no change\n\t\tif (/^(#|data:|http:\\/\\/|https:\\/\\/|file:\\/\\/\\/)/i.test(unquotedOrigUrl)) {\n\t\t  return fullMatch;\n\t\t}\n\n\t\t// convert the url to a full url\n\t\tvar newUrl;\n\n\t\tif (unquotedOrigUrl.indexOf(\"//\") === 0) {\n\t\t  \t//TODO: should we add protocol?\n\t\t\tnewUrl = unquotedOrigUrl;\n\t\t} else if (unquotedOrigUrl.indexOf(\"/\") === 0) {\n\t\t\t// path should be relative to the base url\n\t\t\tnewUrl = baseUrl + unquotedOrigUrl; // already starts with '/'\n\t\t} else {\n\t\t\t// path should be relative to current directory\n\t\t\tnewUrl = currentDir + unquotedOrigUrl.replace(/^\\.\\//, \"\"); // Strip leading './'\n\t\t}\n\n\t\t// send back the fixed url(...)\n\t\treturn \"url(\" + JSON.stringify(newUrl) + \")\";\n\t});\n\n\t// send back the fixed css\n\treturn fixedCss;\n};\n\n\n/***/ }),\n/* 78 */\n/***/ (function(module, exports) {\n\nmodule.exports = \"<svg t=\\\"1498805495948\\\" class=\\\"icon\\\" style viewBox=\\\"0 0 1024 1024\\\" version=\\\"1.1\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" p-id=\\\"3582\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\"><defs><style type=\\\"text/css\\\"></style></defs><path d=\\\"M1009.762845 56.889457 14.237155 56.889457C6.386724 56.889457 0.01536 50.546537 0.01536 42.667662L0.01536 14.224071C0.01536 6.37364 6.386724 0.002276 14.237155 0.002276L1009.762845 0.002276C1017.613276 0.002276 1023.98464 6.37364 1023.98464 14.224071L1023.98464 42.667662C1023.98464 50.546537 1017.613276 56.889457 1009.762845 56.889457ZM473.800257 197.087918C476.67306 194.215116 480.00096 192.167177 483.556409 190.773441L483.556409 184.885617C483.556409 177.035187 489.927773 170.663822 497.778205 170.663822L526.221795 170.663822C534.072227 170.663822 540.443591 177.035187 540.443591 184.885617L540.443591 190.773441C543.99904 192.167177 547.32694 194.215116 550.199743 197.087918L997.560544 644.44872C1008.653544 655.570164 1008.653544 673.574957 997.560544 684.696401 986.4391 695.789402 968.434307 695.789402 957.341307 684.696401L540.443591 267.798686 540.443591 1009.749761C540.443591 1017.600192 534.072227 1023.971557 526.221795 1023.971557L497.778205 1023.971557C489.927773 1023.971557 483.556409 1017.600192 483.556409 1009.749761L483.556409 267.798686 66.658693 684.696401C55.565693 695.789402 37.532456 695.789402 26.439456 684.696401 15.318011 673.574957 15.318011 655.570164 26.439456 644.44872L473.800257 197.087918Z\\\" p-id=\\\"3583\\\"></path></svg>\"\n\n/***/ }),\n/* 79 */\n/***/ (function(module, exports) {\n\nmodule.exports = \"<svg t=\\\"1498706378122\\\" class=\\\"icon\\\" style viewBox=\\\"0 0 1024 1024\\\" version=\\\"1.1\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" p-id=\\\"3878\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\"><defs><style type=\\\"text/css\\\"></style></defs><path d=\\\"M505.344 852.928l-434.944-315.392c-17.888-12.96-21.856-37.984-8.896-55.872 12.928-17.888 37.984-21.888 55.872-8.896l367.296 266.368 409.376-619.392c12.192-18.464 36.992-23.456 55.424-11.328 18.432 12.192 23.488 36.992 11.328 55.424L505.344 852.928z\\\" p-id=\\\"3879\\\"></path></svg>\"\n\n/***/ }),\n/* 80 */\n/***/ (function(module, exports) {\n\nmodule.exports = \"<svg t=\\\"1498738186413\\\" class=\\\"icon\\\" style viewBox=\\\"0 0 1024 1024\\\" version=\\\"1.1\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" p-id=\\\"2536\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\"><defs><style type=\\\"text/css\\\"></style></defs><path d=\\\"M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z\\\" fill p-id=\\\"2537\\\"></path></svg>\"\n\n/***/ }),\n/* 81 */\n/***/ (function(module, exports, __webpack_require__) {\n\nif (window.mam == null) {\r\n    throw '该组件依赖 mam-base';\r\n}\r\nwindow.mam.ng = window.mam.ng || {};\r\n\r\nvar app = angular.module('mam-ng', ['ui.router']);\r\napp.run([\r\n    '$http',\r\n    function ($http) {\r\n        mam.ng.$http = $http;\r\n    }\r\n]);\r\n\r\n__webpack_require__(4);\r\n__webpack_require__(8);\r\n__webpack_require__(6);\r\n__webpack_require__(5);\r\n__webpack_require__(7);\r\n\r\n__webpack_require__(37);\r\n\r\n\r\n__webpack_require__(36);\r\n__webpack_require__(38);\r\n\r\n__webpack_require__(9);\r\n__webpack_require__(10);\r\n__webpack_require__(14);\r\n__webpack_require__(2);\r\n__webpack_require__(17);\r\n__webpack_require__(19);\r\n__webpack_require__(18);\r\n__webpack_require__(21);\r\n__webpack_require__(23);\r\n__webpack_require__(24);\r\n__webpack_require__(25);\r\n__webpack_require__(26);\r\n__webpack_require__(28);\r\n__webpack_require__(29);\r\n__webpack_require__(30);\r\n__webpack_require__(32);\r\n__webpack_require__(22);\r\n__webpack_require__(34);\r\n__webpack_require__(33);\r\n__webpack_require__(2);\r\n__webpack_require__(15);\r\n__webpack_require__(35);\r\n__webpack_require__(31);\r\n__webpack_require__(27);\r\n__webpack_require__(20);\r\n__webpack_require__(11);\r\n__webpack_require__(13);\r\n__webpack_require__(12);\r\n__webpack_require__(16);\r\n\r\n// require('./directives/inline-editor/') //开发中\n\n/***/ })\n/******/ ]);\n\n\n// WEBPACK FOOTER //\n// mam-ng.min.js", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// identity function for calling harmony imports with the correct context\n \t__webpack_require__.i = function(value) { return value; };\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, {\n \t\t\t\tconfigurable: false,\n \t\t\t\tenumerable: true,\n \t\t\t\tget: getter\n \t\t\t});\n \t\t}\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 81);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap f0883506d6790a6eb1cf", "/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor <PERSON> @sokra\n*/\n// css base code, injected by the css-loader\nmodule.exports = function(useSourceMap) {\n\tvar list = [];\n\n\t// return the list of modules as css string\n\tlist.toString = function toString() {\n\t\treturn this.map(function (item) {\n\t\t\tvar content = cssWithMappingToString(item, useSourceMap);\n\t\t\tif(item[2]) {\n\t\t\t\treturn \"@media \" + item[2] + \"{\" + content + \"}\";\n\t\t\t} else {\n\t\t\t\treturn content;\n\t\t\t}\n\t\t}).join(\"\");\n\t};\n\n\t// import a list of modules into the list\n\tlist.i = function(modules, mediaQuery) {\n\t\tif(typeof modules === \"string\")\n\t\t\tmodules = [[null, modules, \"\"]];\n\t\tvar alreadyImportedModules = {};\n\t\tfor(var i = 0; i < this.length; i++) {\n\t\t\tvar id = this[i][0];\n\t\t\tif(typeof id === \"number\")\n\t\t\t\talreadyImportedModules[id] = true;\n\t\t}\n\t\tfor(i = 0; i < modules.length; i++) {\n\t\t\tvar item = modules[i];\n\t\t\t// skip already imported module\n\t\t\t// this implementation is not 100% perfect for weird media query combinations\n\t\t\t//  when a module is imported multiple times with different media queries.\n\t\t\t//  I hope this will never occur (Hey this way we have smaller bundles)\n\t\t\tif(typeof item[0] !== \"number\" || !alreadyImportedModules[item[0]]) {\n\t\t\t\tif(mediaQuery && !item[2]) {\n\t\t\t\t\titem[2] = mediaQuery;\n\t\t\t\t} else if(mediaQuery) {\n\t\t\t\t\titem[2] = \"(\" + item[2] + \") and (\" + mediaQuery + \")\";\n\t\t\t\t}\n\t\t\t\tlist.push(item);\n\t\t\t}\n\t\t}\n\t};\n\treturn list;\n};\n\nfunction cssWithMappingToString(item, useSourceMap) {\n\tvar content = item[1] || '';\n\tvar cssMapping = item[3];\n\tif (!cssMapping) {\n\t\treturn content;\n\t}\n\n\tif (useSourceMap && typeof btoa === 'function') {\n\t\tvar sourceMapping = toComment(cssMapping);\n\t\tvar sourceURLs = cssMapping.sources.map(function (source) {\n\t\t\treturn '/*# sourceURL=' + cssMapping.sourceRoot + source + ' */'\n\t\t});\n\n\t\treturn [content].concat(sourceURLs).concat([sourceMapping]).join('\\n');\n\t}\n\n\treturn [content].join('\\n');\n}\n\n// Adapted from convert-source-map (MIT)\nfunction toComment(sourceMap) {\n\t// eslint-disable-next-line no-undef\n\tvar base64 = btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap))));\n\tvar data = 'sourceMappingURL=data:application/json;charset=utf-8;base64,' + base64;\n\n\treturn '/*# ' + data + ' */';\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/css-loader/lib/css-base.js\n// module id = 0\n// module chunks = 0", "/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor <PERSON> @sokra\n*/\n\nvar stylesInDom = {};\n\nvar\tmemoize = function (fn) {\n\tvar memo;\n\n\treturn function () {\n\t\tif (typeof memo === \"undefined\") memo = fn.apply(this, arguments);\n\t\treturn memo;\n\t};\n};\n\nvar isOldIE = memoize(function () {\n\t// Test for IE <= 9 as proposed by Browserhacks\n\t// @see http://browserhacks.com/#hack-e71d8692f65334173fee715c222cb805\n\t// Tests for existence of standard globals is to allow style-loader\n\t// to operate correctly into non-standard environments\n\t// @see https://github.com/webpack-contrib/style-loader/issues/177\n\treturn window && document && document.all && !window.atob;\n});\n\nvar getElement = (function (fn) {\n\tvar memo = {};\n\n\treturn function(selector) {\n\t\tif (typeof memo[selector] === \"undefined\") {\n\t\t\tmemo[selector] = fn.call(this, selector);\n\t\t}\n\n\t\treturn memo[selector]\n\t};\n})(function (target) {\n\treturn document.querySelector(target)\n});\n\nvar singleton = null;\nvar\tsingletonCounter = 0;\nvar\tstylesInsertedAtTop = [];\n\nvar\tfixUrls = require(\"./urls\");\n\nmodule.exports = function(list, options) {\n\tif (typeof DEBUG !== \"undefined\" && DEBUG) {\n\t\tif (typeof document !== \"object\") throw new Error(\"The style-loader cannot be used in a non-browser environment\");\n\t}\n\n\toptions = options || {};\n\n\toptions.attrs = typeof options.attrs === \"object\" ? options.attrs : {};\n\n\t// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n\t// tags it will allow on a page\n\tif (!options.singleton) options.singleton = isOldIE();\n\n\t// By default, add <style> tags to the <head> element\n\tif (!options.insertInto) options.insertInto = \"head\";\n\n\t// By default, add <style> tags to the bottom of the target\n\tif (!options.insertAt) options.insertAt = \"bottom\";\n\n\tvar styles = listToStyles(list, options);\n\n\taddStylesToDom(styles, options);\n\n\treturn function update (newList) {\n\t\tvar mayRemove = [];\n\n\t\tfor (var i = 0; i < styles.length; i++) {\n\t\t\tvar item = styles[i];\n\t\t\tvar domStyle = stylesInDom[item.id];\n\n\t\t\tdomStyle.refs--;\n\t\t\tmayRemove.push(domStyle);\n\t\t}\n\n\t\tif(newList) {\n\t\t\tvar newStyles = listToStyles(newList, options);\n\t\t\taddStylesToDom(newStyles, options);\n\t\t}\n\n\t\tfor (var i = 0; i < mayRemove.length; i++) {\n\t\t\tvar domStyle = mayRemove[i];\n\n\t\t\tif(domStyle.refs === 0) {\n\t\t\t\tfor (var j = 0; j < domStyle.parts.length; j++) domStyle.parts[j]();\n\n\t\t\t\tdelete stylesInDom[domStyle.id];\n\t\t\t}\n\t\t}\n\t};\n};\n\nfunction addStylesToDom (styles, options) {\n\tfor (var i = 0; i < styles.length; i++) {\n\t\tvar item = styles[i];\n\t\tvar domStyle = stylesInDom[item.id];\n\n\t\tif(domStyle) {\n\t\t\tdomStyle.refs++;\n\n\t\t\tfor(var j = 0; j < domStyle.parts.length; j++) {\n\t\t\t\tdomStyle.parts[j](item.parts[j]);\n\t\t\t}\n\n\t\t\tfor(; j < item.parts.length; j++) {\n\t\t\t\tdomStyle.parts.push(addStyle(item.parts[j], options));\n\t\t\t}\n\t\t} else {\n\t\t\tvar parts = [];\n\n\t\t\tfor(var j = 0; j < item.parts.length; j++) {\n\t\t\t\tparts.push(addStyle(item.parts[j], options));\n\t\t\t}\n\n\t\t\tstylesInDom[item.id] = {id: item.id, refs: 1, parts: parts};\n\t\t}\n\t}\n}\n\nfunction listToStyles (list, options) {\n\tvar styles = [];\n\tvar newStyles = {};\n\n\tfor (var i = 0; i < list.length; i++) {\n\t\tvar item = list[i];\n\t\tvar id = options.base ? item[0] + options.base : item[0];\n\t\tvar css = item[1];\n\t\tvar media = item[2];\n\t\tvar sourceMap = item[3];\n\t\tvar part = {css: css, media: media, sourceMap: sourceMap};\n\n\t\tif(!newStyles[id]) styles.push(newStyles[id] = {id: id, parts: [part]});\n\t\telse newStyles[id].parts.push(part);\n\t}\n\n\treturn styles;\n}\n\nfunction insertStyleElement (options, style) {\n\tvar target = getElement(options.insertInto)\n\n\tif (!target) {\n\t\tthrow new Error(\"Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.\");\n\t}\n\n\tvar lastStyleElementInsertedAtTop = stylesInsertedAtTop[stylesInsertedAtTop.length - 1];\n\n\tif (options.insertAt === \"top\") {\n\t\tif (!lastStyleElementInsertedAtTop) {\n\t\t\ttarget.insertBefore(style, target.firstChild);\n\t\t} else if (lastStyleElementInsertedAtTop.nextSibling) {\n\t\t\ttarget.insertBefore(style, lastStyleElementInsertedAtTop.nextSibling);\n\t\t} else {\n\t\t\ttarget.appendChild(style);\n\t\t}\n\t\tstylesInsertedAtTop.push(style);\n\t} else if (options.insertAt === \"bottom\") {\n\t\ttarget.appendChild(style);\n\t} else {\n\t\tthrow new Error(\"Invalid value for parameter 'insertAt'. Must be 'top' or 'bottom'.\");\n\t}\n}\n\nfunction removeStyleElement (style) {\n\tif (style.parentNode === null) return false;\n\tstyle.parentNode.removeChild(style);\n\n\tvar idx = stylesInsertedAtTop.indexOf(style);\n\tif(idx >= 0) {\n\t\tstylesInsertedAtTop.splice(idx, 1);\n\t}\n}\n\nfunction createStyleElement (options) {\n\tvar style = document.createElement(\"style\");\n\n\toptions.attrs.type = \"text/css\";\n\n\taddAttrs(style, options.attrs);\n\tinsertStyleElement(options, style);\n\n\treturn style;\n}\n\nfunction createLinkElement (options) {\n\tvar link = document.createElement(\"link\");\n\n\toptions.attrs.type = \"text/css\";\n\toptions.attrs.rel = \"stylesheet\";\n\n\taddAttrs(link, options.attrs);\n\tinsertStyleElement(options, link);\n\n\treturn link;\n}\n\nfunction addAttrs (el, attrs) {\n\tObject.keys(attrs).forEach(function (key) {\n\t\tel.setAttribute(key, attrs[key]);\n\t});\n}\n\nfunction addStyle (obj, options) {\n\tvar style, update, remove, result;\n\n\t// If a transform function was defined, run it on the css\n\tif (options.transform && obj.css) {\n\t    result = options.transform(obj.css);\n\n\t    if (result) {\n\t    \t// If transform returns a value, use that instead of the original css.\n\t    \t// This allows running runtime transformations on the css.\n\t    \tobj.css = result;\n\t    } else {\n\t    \t// If the transform function returns a falsy value, don't add this css.\n\t    \t// This allows conditional loading of css\n\t    \treturn function() {\n\t    \t\t// noop\n\t    \t};\n\t    }\n\t}\n\n\tif (options.singleton) {\n\t\tvar styleIndex = singletonCounter++;\n\n\t\tstyle = singleton || (singleton = createStyleElement(options));\n\n\t\tupdate = applyToSingletonTag.bind(null, style, styleIndex, false);\n\t\tremove = applyToSingletonTag.bind(null, style, styleIndex, true);\n\n\t} else if (\n\t\tobj.sourceMap &&\n\t\ttypeof URL === \"function\" &&\n\t\ttypeof URL.createObjectURL === \"function\" &&\n\t\ttypeof URL.revokeObjectURL === \"function\" &&\n\t\ttypeof Blob === \"function\" &&\n\t\ttypeof btoa === \"function\"\n\t) {\n\t\tstyle = createLinkElement(options);\n\t\tupdate = updateLink.bind(null, style, options);\n\t\tremove = function () {\n\t\t\tremoveStyleElement(style);\n\n\t\t\tif(style.href) URL.revokeObjectURL(style.href);\n\t\t};\n\t} else {\n\t\tstyle = createStyleElement(options);\n\t\tupdate = applyToTag.bind(null, style);\n\t\tremove = function () {\n\t\t\tremoveStyleElement(style);\n\t\t};\n\t}\n\n\tupdate(obj);\n\n\treturn function updateStyle (newObj) {\n\t\tif (newObj) {\n\t\t\tif (\n\t\t\t\tnewObj.css === obj.css &&\n\t\t\t\tnewObj.media === obj.media &&\n\t\t\t\tnewObj.sourceMap === obj.sourceMap\n\t\t\t) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tupdate(obj = newObj);\n\t\t} else {\n\t\t\tremove();\n\t\t}\n\t};\n}\n\nvar replaceText = (function () {\n\tvar textStore = [];\n\n\treturn function (index, replacement) {\n\t\ttextStore[index] = replacement;\n\n\t\treturn textStore.filter(Boolean).join('\\n');\n\t};\n})();\n\nfunction applyToSingletonTag (style, index, remove, obj) {\n\tvar css = remove ? \"\" : obj.css;\n\n\tif (style.styleSheet) {\n\t\tstyle.styleSheet.cssText = replaceText(index, css);\n\t} else {\n\t\tvar cssNode = document.createTextNode(css);\n\t\tvar childNodes = style.childNodes;\n\n\t\tif (childNodes[index]) style.removeChild(childNodes[index]);\n\n\t\tif (childNodes.length) {\n\t\t\tstyle.insertBefore(cssNode, childNodes[index]);\n\t\t} else {\n\t\t\tstyle.appendChild(cssNode);\n\t\t}\n\t}\n}\n\nfunction applyToTag (style, obj) {\n\tvar css = obj.css;\n\tvar media = obj.media;\n\n\tif(media) {\n\t\tstyle.setAttribute(\"media\", media)\n\t}\n\n\tif(style.styleSheet) {\n\t\tstyle.styleSheet.cssText = css;\n\t} else {\n\t\twhile(style.firstChild) {\n\t\t\tstyle.removeChild(style.firstChild);\n\t\t}\n\n\t\tstyle.appendChild(document.createTextNode(css));\n\t}\n}\n\nfunction updateLink (link, options, obj) {\n\tvar css = obj.css;\n\tvar sourceMap = obj.sourceMap;\n\n\t/*\n\t\tIf convertToAbsoluteUrls isn't defined, but sourcemaps are enabled\n\t\tand there is no publicPath defined then lets turn convertToAbsoluteUrls\n\t\ton by default.  Otherwise default to the convertToAbsoluteUrls option\n\t\tdirectly\n\t*/\n\tvar autoFixUrls = options.convertToAbsoluteUrls === undefined && sourceMap;\n\n\tif (options.convertToAbsoluteUrls || autoFixUrls) {\n\t\tcss = fixUrls(css);\n\t}\n\n\tif (sourceMap) {\n\t\t// http://stackoverflow.com/a/26603875\n\t\tcss += \"\\n/*# sourceMappingURL=data:application/json;base64,\" + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + \" */\";\n\t}\n\n\tvar blob = new Blob([css], { type: \"text/css\" });\n\n\tvar oldSrc = link.href;\n\n\tlink.href = URL.createObjectURL(blob);\n\n\tif(oldSrc) URL.revokeObjectURL(oldSrc);\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/style-loader/lib/addStyles.js\n// module id = 1\n// module chunks = 0", "angular.module('mam-ng')\r\n    .directive('mamDropdown', function () {\r\n        return {\r\n            restrict: 'A',\r\n            link: function (scope, element, attr) {scope.mode = 'click';\r\n                var $e = $(element);\r\n                var toggle = $e.find('.dropdown-toggle');\r\n                var menu = $e.find('.dropdown-menu');\r\n                menu.hide();\r\n                var isShow = false;\r\n\r\n                var timer = 0;\r\n                //var menuHeight = menu.height();\r\n\r\n                if (toggle.parent().css('position') == 'static') {//当div高度固定很短的时候显示不出来时\r\n                    var top = toggle.position().top + toggle.outerHeight() - 2;\r\n                    var left = toggle.position().left;\r\n                    menu.css('top', top);\r\n                    menu.css('left', left);\r\n                } else {\r\n                    $e.css('position', 'relative');\r\n                }\r\n\r\n                var changeMenu = function () {\r\n                    isShow = !isShow;\r\n                    toggle.toggleClass(\"dropdown-toggle-active\");\r\n                    menu.not(menu).stop().slideUp(200).parent().find(\".dropdown-toggle\"); //.removeClass(\"dropdown-toggle-active\");\r\n                    menu.stop().slideToggle(200);\r\n                }\r\n\r\n                if (scope.mode == 'hover') {\r\n                    //比较高度，是为了防止在关闭动画的时候display不为none,也进行切换的问题\r\n                    $e.hover(function () {\r\n                        clearTimeout(timer);\r\n                        if (!isShow) {\r\n                            changeMenu();\r\n                        }\r\n                        return false;\r\n                    }, function () {\r\n                        clearTimeout(timer);\r\n                        if (isShow) {\r\n                            timer = setTimeout(changeMenu, 250);//添加延迟关闭\r\n                        }\r\n                        return false;\r\n                    })\r\n                    menu.hover(function () { \r\n                        clearTimeout(timer);\r\n                    }, function () {\r\n                        clearTimeout(timer);\r\n                        if (isShow) {\r\n                            timer = setTimeout(changeMenu, 250);//添加延迟关闭\r\n                        }\r\n                        return false;\r\n                    })\r\n                } else {\r\n                    toggle.on('click', function () {\r\n                        changeMenu();\r\n                        return false;\r\n                    });\r\n                }\r\n\r\n                menu.find('li').on('click', function () {\r\n                    clearTimeout(timer);\r\n                    isShow = false;\r\n                    toggle.removeClass('dropdown-toggle-active');\r\n                    menu.stop().slideUp(200);\r\n                });\r\n\r\n                $('html').on('click', function () {\r\n                    clearTimeout(timer);\r\n                    if (isShow) {\r\n                        isShow = false;\r\n                        toggle.removeClass('dropdown-toggle-active');\r\n                        menu.stop().slideUp(200);\r\n                    }\r\n                });\r\n            }\r\n        };\r\n    });\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/dropdown/index.js\n// module id = 2\n// module chunks = 0", "module.exports = \"<svg t=\\\"1501119020784\\\" class=\\\"icon\\\" style viewBox=\\\"0 0 1024 1024\\\" version=\\\"1.1\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" p-id=\\\"3157\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\"><defs><style type=\\\"text/css\\\"></style></defs><path d=\\\"M528.612 146.023v771.24c0 9.23-7.383 16.612-16.612 16.612s-16.612-7.383-16.612-16.612v-770.977l-103.623 103.623c-6.328 6.328-16.875 6.328-23.203 0s-6.328-16.875 0-23.203l131.836-131.836c6.328-6.328 16.875-6.328 23.203 0l131.836 131.836c6.328 6.328 6.328 16.875 0 23.203s-16.875 6.328-23.203 0l-103.623-103.887z\\\" p-id=\\\"3158\\\"></path></svg>\"\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/sort-group/icon.svg\n// module id = 3\n// module chunks = 0", "\r\nmam.ng.config = function (ngApp) {\r\n    ngApp.config([\r\n        '$controllerProvider', '$compileProvider', '$filterProvider', '$provide',\r\n        function ($controllerProvider, $compileProvider, $filterProvider, $provide) {\r\n            ngApp.registerController = $controllerProvider.register;\r\n            ngApp.registerDirective = $compileProvider.directive;\r\n            ngApp.registerFilter = $filterProvider.register;\r\n            ngApp.registerFactory = $provide.factory;\r\n            ngApp.registerService = $provide.service;\r\n        }\r\n    ]);\r\n\r\n    ngApp.run([\r\n        '$rootScope', '$state', '$stateParams',\r\n        function ($rootScope, $state, $stateParams) {\r\n            $rootScope.$state = $state;\r\n            $rootScope.params = $stateParams;\r\n            $rootScope.config = _.get(window, 'nxt.config');\r\n            $rootScope.l = window.l;\r\n        }\r\n    ]);\r\n    return ngApp;\r\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/core/config.js\n// module id = 4\n// module chunks = 0", "function addUrlParam(url, param) {\r\n    url += (url.indexOf('?') !== -1 ? '&' : '?') + param;\r\n    return url;\r\n}\r\n\r\nmam.ng.handleUrl = function (url, options) {\r\n    var opts = $.extend({}, { r: true }, options);\r\n\r\n    if (opts.r) {\r\n        url = addUrlParam(url, 'r=' + parseInt(100 * Math.random()) + new Date().getTime());\r\n    }\r\n\r\n    var token = mam.utils.getUrlQueryParam('token');\r\n    if (token != null) {\r\n        url = addUrlParam(url, 'token=' + encodeURI(token).replace(/\\+/g, '%2B'));\r\n    }\r\n\r\n    var opsite = mam.utils.getUrlQueryParam('opsite', true);\r\n    if (opsite != null) {\r\n        url = addUrlParam(url, 'opsite=' + opsite)\r\n    }\r\n\r\n    var site = mam.utils.getUrlQueryParam('site');\r\n    if (site == null) {\r\n        site = mam.utils.getUrlQueryParam('sitecode');\r\n    }\r\n    if (site != null) {\r\n        url = addUrlParam(url, 'site=' + site);\r\n        $.cookie('site', encodeURIComponent(site), { path: '/' });\r\n    }\r\n    return url;\r\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/core/handle-url.js\n// module id = 5\n// module chunks = 0", "mam.ng.httpProvider = function (ngApp, options) {\r\n    var opts = $.extend({}, {\r\n        name: 'mamHttp',\r\n        server: '',\r\n        urlPrefix: '~/',\r\n        cache: false,\r\n        withCredentials: true,\r\n        loaderTemplate: '<div id=\"mam-loader\" class=\"mam-loader\">Loading...</div>',\r\n        loginUrl: '/#!/login?login_backUrl=${url}',\r\n        requestErrorTip: '请求失败，请稍后再试。'\r\n    }, options);\r\n\r\n    ngApp.config([\r\n        '$provide', '$httpProvider',\r\n        function ($provide, $httpProvider) {\r\n            var requestCount = 0;\r\n            var $loader = $(opts.loaderTemplate);\r\n            $('body').append($loader);\r\n            $loader.hide();\r\n\r\n            function hideLoader(config) {\r\n                if (config.interfaceCall && config.showLoader !== false) {\r\n                    requestCount--;\r\n                    if (requestCount <= 0) {\r\n                        $loader.hide();\r\n                    }\r\n                }\r\n            }\r\n\r\n            function goLoginPage() {\r\n                if (location.href.split('?login_backUrl=').length < 2) {\r\n                    location.href = _.template(opts.loginUrl)({ url: escape(location.href) });\r\n                }\r\n            }\r\n\r\n            /** 判断url前缀是否是其他服务器的前缀标识 */\r\n            function indexOfOtherServersPrefix(url) {\r\n                var index = -1;\r\n                if (opts.otherServers && opts.otherServers instanceof Array) {\r\n                    _.forEach(opts.otherServers, function (item) {\r\n                        if (index === -1) {\r\n                            index = url.indexOf(item.prefix);\r\n                        }\r\n                    });\r\n                }\r\n                return index;\r\n            }\r\n\r\n            function getServerUrl(url) {\r\n                if (url.indexOf(opts.urlPrefix) === 0) {\r\n                    return opts.server;\r\n                }\r\n                else if (opts.otherServers && opts.otherServers instanceof Array) {\r\n                    var retUrl = \"\";\r\n                    _.forEach(opts.otherServers, function (item) {\r\n                        if (url.indexOf(item.prefix) === 0) {\r\n                            retUrl = item.server;\r\n                        }\r\n                    });\r\n                    return retUrl;\r\n                }\r\n                return \"\";\r\n            }\r\n\r\n            $provide.factory(opts.name, ['$q', function ($q) {\r\n                return {\r\n                    'request': function (config) {\r\n                        if (config.url.indexOf(opts.urlPrefix) === 0 || indexOfOtherServersPrefix(config.url) === 0) {\r\n                            config.interfaceCall = true;\r\n                            config.url = getServerUrl(config.url) + config.url.substring(1, config.url.length);\r\n                            config.url = mam.ng.handleUrl(config.url);\r\n                            if (config.showLoader !== false) {\r\n                                requestCount++;\r\n                                $loader.show();\r\n                            }\r\n                        }\r\n                        var productName =_.get(nxt,'product.name','');\r\n                        if (productName) {\r\n                            if (!config.headers)\r\n                                config.headers = {};\r\n                            config.headers['mam-product'] = productName;\r\n                        }\r\n                        return config;\r\n                    },\r\n                    'requestError': function (rejection) {\r\n                        hideLoader(rejection.config);\r\n                        if (rejection.config.interfaceCall) {\r\n                            console.error('requestError', rejection);\r\n                            mam.prompt(opts.requestErrorTip);\r\n                        }\r\n                        return $q.reject(rejection);\r\n                    },\r\n                    'response': function (res) {\r\n                        if (res.config.interfaceCall !== true) {\r\n                            return res;\r\n                        }\r\n                        hideLoader(res.config);\r\n                        if (res.status === 200) {\r\n                            if (res.data.success) {\r\n                                res.data = res.data.data;\r\n                            } else {\r\n                                res.data = res.data.error;\r\n                                res.data.success = false;\r\n                                console.error('response', res);\r\n                                if (res.config.errorHandle !== false) {\r\n                                    mam.prompt(l('system.' + res.data.code, res.data.title));\r\n                                }\r\n                                if (res.config.errorReject !== false) { //客户端上传的地方用了q.all，所以希望失败了也要返回结果。\r\n                                    return $q.reject(res);\r\n                                }\r\n                                return $q.resolve(res);\r\n                            }\r\n                        }\r\n                        return res;\r\n                    },\r\n                    'responseError': function (res) {\r\n                        hideLoader(res.config);\r\n                        if (res.config.interfaceCall) {\r\n                            console.error('responseError', res);\r\n                            if (res.status === -1) {\r\n                                mam.prompt(l('system.500', '系统错误，请稍后再试！'));\r\n                                return $q.reject(res);\r\n                            }\r\n                            res.data = res.data.error;\r\n                            if (res.status === 401) {\r\n                                if (res.config.errorHandle !== false) {\r\n                                    goLoginPage();\r\n                                }\r\n                                if (res.config.errorReject !== false) {\r\n                                    return $q.reject(res);\r\n                                }\r\n                                return $q.resolve(res);\r\n                            }\r\n                            if (res.config.errorHandle !== false) {\r\n                                if (res.status === 403) {\r\n                                    mam.prompt(l('system.403', '错误代码：403，服务器拒绝请求！'));\r\n                                }\r\n                                if (res.status === 404) {\r\n                                    mam.prompt(l('system.404', '错误代码：404，未找到请求地址！'));\r\n                                }\r\n                                else\r\n                                {\r\n                                    var requestId = res.headers('request-id');\r\n                                    var outer = $('<div class=\"system-error-tip-box\"></div>');\r\n                                    outer.append('<div class=\"error-tip\">'+l('system.500', res.data.title)+'</div>');\r\n                                    if (requestId)\r\n                                    {\r\n                                        var aEle = $('<a class=\"error-info-btn\">复制错误代码<a>');\r\n                                        aEle.click(function(){\r\n                                            var input = document.createElement('input');\r\n                                            document.body.appendChild(input);\r\n                                            input.setAttribute('value', requestId);\r\n                                            input.select();\r\n                                            if (document.execCommand('copy')) {\r\n                                                document.execCommand('copy');\r\n                                                mam.message.success('复制成功！')\r\n                                            }\r\n                                            document.body.removeChild(input);\r\n                                        });\r\n                                        outer.append(aEle);\r\n                                    }\r\n                                    mam.prompt(outer);\r\n                                }\r\n                            }\r\n                            if (res.config.errorReject !== false) {\r\n                                return $q.reject(res);\r\n                            }\r\n                            return $q.resolve(res);\r\n                        } else {\r\n                            if (res.status === 401) {\r\n                                goLoginPage();\r\n                            }\r\n                        }\r\n                        return $q.reject(res);\r\n                    }\r\n                };\r\n            }]);\r\n\r\n            $httpProvider.defaults.withCredentials = opts.withCredentials;\r\n            $httpProvider.defaults.cache = opts.cache;\r\n            $httpProvider.interceptors.push(opts.name);\r\n        }\r\n    ]);\r\n\r\n    return ngApp;\r\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/core/http-provider.js\n// module id = 6\n// module chunks = 0", "window.mam.module = window.mam.module || {};\r\n\r\nmam.module.add = function (opts) {\r\n    var module = {\r\n        name: opts.name,\r\n        app: opts.app,\r\n        version: opts.version,\r\n        path: opts.requireModule.uri.substr(0, opts.requireModule.uri.lastIndexOf('/') + 1),\r\n        routes: {},\r\n        options: {}\r\n    };\r\n    mam.module[opts.name] = module;\r\n\r\n    module.init = function (options) {\r\n        module.options = $.extend({}, {\r\n            parentRoute: ''\r\n        }, options);\r\n        if (opts.init(module, options) !== true) {\r\n            mam.ng.config(module.app);\r\n            mam.ng.registerRoutes(module.app, opts.routes, {\r\n                path: module.path,\r\n                parentRoute: module.options.parentRoute,\r\n                ctrlPrefix: module.name\r\n            });\r\n\r\n            _.forEach(mam.ng.handleRoutes(opts.routes, ''), function (val, key) {\r\n                module.routes[key] = module.options.parentRoute + key;\r\n            });\r\n\r\n            mam.language.append(module.path + 'assets/lang/${lang}.js');\r\n        }\r\n        return module;\r\n    }\r\n\r\n    return module;\r\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/core/module.js\n// module id = 7\n// module chunks = 0", "\r\nmam.ng.registerRoutes = function (ngApp, routes, options) {\r\n    var opts = $.extend({}, {\r\n        parentRoute: '',\r\n        path: '',\r\n        ctrlPrefix: '',\r\n        ctrlSuffix: 'Ctrl',\r\n        viewDir: 'view',\r\n        controllerDir: 'controllers'\r\n    }, options);\r\n    if (_.isFunction(routes)) {\r\n        routes = routes();\r\n    }\r\n\r\n    ngApp.config([\r\n        '$urlRouterProvider', '$stateProvider', '$controllerProvider',\r\n        function ($urlRouterProvider, $stateProvider, $controllerProvider) {\r\n            function loader(app, dependencies, ctrlName) {\r\n                var definition = {\r\n                    resolver: ['$q', '$rootScope',\r\n                        function ($q, $rootScope) {\r\n                            var defered = $q.defer();\r\n                            var require = window.require;\r\n                            require(dependencies, function (item) {\r\n                                $rootScope.$apply(function () {\r\n                                    $controllerProvider.register(item.name || ctrlName, item);\r\n                                    defered.resolve();\r\n                                });\r\n                            });\r\n                            return defered.promise;\r\n                        }]\r\n                };\r\n                return definition;\r\n            }\r\n\r\n            function handleName(name) {\r\n                return name.replace(/\\/([a-z])/g, function (all, letter) {\r\n                    return letter.toUpperCase();\r\n                });\r\n            }\r\n\r\n            angular.forEach(mam.ng.handleRoutes(routes, ''), function (route, name) {\r\n                if (route.home) {\r\n                    $urlRouterProvider.when('', route.url);\r\n                    $urlRouterProvider.when('/', route.url);\r\n                }\r\n                if (route.cv != null) {\r\n                    route.templateUrl = opts.path + opts.viewDir + '/' + route.cv + '.html';\r\n                    route.ctrl = route.cv;\r\n                }\r\n                if (route.ctrl != null) {\r\n                    var ctrl = route.ctrl;\r\n                    if (opts.ctrlPrefix != '') {\r\n                        ctrl = opts.ctrlPrefix + '/' + route.ctrl;\r\n                    }\r\n                    route.controller = handleName(ctrl) + opts.ctrlSuffix;\r\n                    route.resolve = [opts.path + opts.controllerDir + '/' + route.ctrl];\r\n                }\r\n                if (_.isArray(route.resolve)) {\r\n                    route.resolve = loader(ngApp, route.resolve, route.controller);\r\n                }\r\n                $stateProvider.state(opts.parentRoute + name, route);\r\n            });\r\n        }\r\n    ]);\r\n    return ngApp;\r\n}\r\n\r\nmam.ng.handleRoutes = function (routes, initRoutes) {\r\n    var tempRoutes = {};\r\n    var selfRoutes = '';\r\n    var parentRoutes = initRoutes ? initRoutes : '';\r\n\r\n    (function handleRoutes(routes, parentRoutes) {\r\n        for (var i in routes) {\r\n            if (routes[i].children && !_.isEmpty(routes[i].children)) {\r\n                selfRoutes += i + '.';\r\n                tempRoutes[parentRoutes + i] = routes[i];\r\n                if (typeof (routes[i].children) === 'object') {\r\n                    handleRoutes(routes[i].children, selfRoutes)\r\n                }\r\n            } else {\r\n                tempRoutes[parentRoutes + i] = routes[i];\r\n            }\r\n            if (parentRoutes !== selfRoutes) {\r\n                selfRoutes = parentRoutes;\r\n            }\r\n        }\r\n        return tempRoutes;\r\n    })(routes, parentRoutes)\r\n    return tempRoutes;\r\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/core/router.js\n// module id = 8\n// module chunks = 0", "angular.module('mam-ng')\r\n    .directive('mamAllCheckbox', function () {\r\n        return {\r\n            restrict: 'A',\r\n            link: function (scope, element, attr) {\r\n                attr.property = attr.property || 'selected';\r\n\r\n                element.bind('change', function (e) {\r\n                    scope.$apply(function () {\r\n                        var value = element.prop('checked');\r\n                        angular.forEach(scope.$eval(attr.collection), function (item) {\r\n                            item[attr.property] = value;\r\n                        });\r\n                    });\r\n                });\r\n                \r\n                scope.$watch(function () { //仅监听对应属性，避免过多的响应和遇到特殊属性导致js报错\r\n                    return _.map(scope.$eval(attr.collection), function (item) {\r\n                        return item[attr.property];\r\n                    });\r\n                }, function (newVal) {\r\n                    var hasTrue, hasFalse;\r\n                    angular.forEach(newVal, function (item) {\r\n                        if (item) {\r\n                            hasTrue = true;\r\n                        } else {\r\n                            hasFalse = true;\r\n                        }\r\n                    });\r\n                    scope.$eval(attr.ngModel + ' = ' + ((hasTrue && hasFalse) ? false : hasTrue));\r\n                }, true);\r\n            }\r\n        };\r\n    });\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/all-checkbox/index.js\n// module id = 9\n// module chunks = 0", " import './style.less';\r\n\r\nangular.module('mam-ng')\r\n    .provider('$mamBackToTop', function () {\r\n        var defaults = this.defaults = {\r\n            text: '回到顶部',\r\n            threshold: 400,\r\n            icon: require('./icon.svg')\r\n        }\r\n        this.$get = function () {\r\n            return { defaults: defaults };\r\n        };\r\n    })\r\n    .directive('mamBackToTop', ['$mamBackToTop', function ($mamBackToTop) {\r\n        var opts = $mamBackToTop.defaults;\r\n        return {\r\n            restrict: \"E\",\r\n            replace: true,\r\n            template: '<div class=\"mam-back-top\" title=\"{{text}}\">' + opts.icon + '</div>',\r\n            scope: {\r\n                container: '@?',\r\n                text: '@?',\r\n                threshold: '@?'\r\n            },\r\n            link: function (scope, element, attr) {\r\n                scope.text = scope.text || opts.text;\r\n                var $e = $(element);\r\n                var container = $(scope.container || window);\r\n                if (_.isUndefined(scope.threshold)) {\r\n                    scope.threshold = opts.threshold;\r\n                } else {\r\n                    scope.threshold = _.toNumber(scope.threshold);\r\n                }\r\n\r\n                function check() {\r\n                    if (container.scrollTop() > scope.threshold) {\r\n                        $e.fadeIn(200);\r\n                    } else {\r\n                        $e.fadeOut(200);\r\n                    }\r\n                }\r\n\r\n                container.scroll(function () { check(); });\r\n\r\n                $e.click(function () {\r\n                    if (container[0] == window) {\r\n                        $('body,html').animate({ scrollTop: 0 }, 500);\r\n                    } else {\r\n                        container.animate({ scrollTop: 0 }, 500);\r\n                    }\r\n                });\r\n\r\n                check();\r\n            }\r\n        };\r\n    }]);\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/back-to-top/index.js\n// module id = 10\n// module chunks = 0", "import './style.less';\r\n\r\nangular.module('mam-ng')\r\n    .directive('mamBadge', function () {\r\n        return {\r\n            restrict: 'E',\r\n            template: '<div class=\"mam-badge\"><span ng-if=\"count>0\">{{count}}</span></div>',\r\n            scope: {\r\n                count: '<',\r\n                overflowCount: '<?'\r\n            },\r\n            link: function (scope, element, attr) {\r\n\r\n                scope.$watch('count', function () {\r\n                    \r\n\r\n                });\r\n            }\r\n        };\r\n    });\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/badge/index.js\n// module id = 11\n// module chunks = 0", "/** 文本超出，中间打省略号，element.parent必须固定宽度，element的white-space必须为nowrap */\r\nangular.module('mam-ng')\r\n\t.directive('calCenterEllipsis', [\"$timeout\", function ($timeout) {\r\n\t\treturn {\r\n            restrict: 'A',\r\n\t\t\tlink: function (scope, element, attr) {\r\n                $timeout(function(){\r\n                    if (element.outerWidth() > element.parent().width())\r\n                    {\r\n                        var middleIndex;\r\n                        while (element.outerWidth() > element.parent().width())\r\n                        {\r\n                            element.text(element.text().replace(/\\.\\.\\./, \"\"));\r\n                            middleIndex = element.text().length / 2;\r\n                            element.text(element.text().substring(0, middleIndex)\r\n                                + \"...\" + element.text().substring(middleIndex + 1, element.text().length));\r\n                        }\r\n                    }\r\n                });\r\n\t\t\t}\r\n\t\t};\r\n\t}]);\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/calCenterEllipsis/index.js\n// module id = 12\n// module chunks = 0", "import './style.less';\r\n\r\nangular.module('mam-ng')\r\n\t.directive('mamCaptcha', [function () {\r\n\t\treturn {\r\n\t\t\trestrict: 'E',\r\n\t\t\treplace: true,\r\n\t\t\ttemplate: '<img class=\"mam-captcha\"/>',\r\n\t\t\tlink: function (scope, element, attr) {\r\n\r\n\t\t\t\tfunction load() {\r\n\t\t\t\t\telement.attr('src', mam.path('~/user/captcha?t=' + new Date().getTime()))\r\n\t\t\t\t}\r\n\t\t\r\n\t\t\t\tscope.$on('mam-captcha-refresh' , load);\r\n\r\n\t\t\t\telement.on('click', load);\r\n\r\n\t\t\t\tload();\r\n\t\t\t}\r\n\t\t};\r\n\t}]);\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/captcha/index.js\n// module id = 13\n// module chunks = 0", "import './style.less';\r\n\r\nangular.module('mam-ng')\r\n    .provider('$mamCheckbox', function () {\r\n        var defaults = this.defaults = {\r\n            class: 'mam-checkbox',\r\n            icon: require('./icon.svg')\r\n        }\r\n        this.$get = function () {\r\n            return { defaults: defaults };\r\n        };\r\n    })\r\n    .directive('mamCheckbox', ['$parse', '$mamCheckbox', function ($parse, $mamCheckbox) {\r\n        var opts = $mamCheckbox.defaults;\r\n        return {\r\n            restrict: 'A',\r\n            link: function (scope, element, attr) {\r\n                var label = element.parent('label');\r\n                if (!label.hasClass(opts.class)) {\r\n                    label.addClass(opts.class);\r\n                }\r\n                label.append(opts.icon);\r\n\r\n                //ng-checked模式\r\n                if (attr.ngChecked) {\r\n                    scope.$watch(attr.ngChecked, function (newVal) {\r\n                        setCheckedStyle(newVal);\r\n                    }, false);\r\n                }\r\n                //ng-model模式\r\n                if (attr.ngModel) {\r\n                    scope.$watch(attr.ngModel, function (newVal) {\r\n                        setCheckedStyle(newVal);\r\n                    }, false);\r\n                }\r\n                function setCheckedStyle(val) {\r\n                    if (angular.isString(val)) {\r\n                        val = (val.toLowerCase() == 'true');\r\n                    }\r\n                    if (val) {\r\n                        label.addClass('checked').removeClass('unchecked');\r\n                    } else {\r\n                        label.addClass('unchecked').removeClass('checked');\r\n                    }\r\n                }\r\n\r\n                element.on('focus', function () {\r\n                    label.addClass('focus');\r\n                }).on('blur', function () {\r\n                    label.removeClass('focus');\r\n                });\r\n            }\r\n        };\r\n    }]);\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/checkbox/index.js\n// module id = 14\n// module chunks = 0", "angular.module('mam-ng')\r\n    .directive('mamDatepicker', [function () {\r\n        return {\r\n            restrict: 'EA',\r\n            scope: {\r\n                minDate : \"@\",\r\n                maxDate : \"@\",\r\n                ctrlType : \"@\",\r\n                showTime : \"@\"\r\n            },\r\n            link: function (scope, element, attr) {\r\n                $.datetimepicker.setLocale('ch');\r\n                var obj = { showSecond: true, formatTime: 'H:m:s', format: 'Y-m-d H:m:s' };\r\n\r\n                if (scope.minDate !== 'no') {\r\n                    obj.minDate = scope.minDate;\r\n                }\r\n                if (scope.maxDate !== 'no') {\r\n                    obj.maxDate = scope.maxDate;\r\n                }\r\n\r\n                obj.onClose = function () {\r\n                    if (element.val() != \"\") {\r\n                        scope.ngModel = element.val();\r\n\r\n                    }\r\n                    element.find(\"input[type=text]\").blur();\r\n                    scope.$apply();\r\n                }\r\n                if (scope.ctrlType === \"3\") {\r\n                    obj.datepicker = false;\r\n                    obj.format = 'H:i:s';\r\n                }\r\n                if (scope.ctrlType === \"2\") {\r\n                    obj.timepicker = false;\r\n                    obj.format = 'Y-m-d';\r\n                    if (scope.ngModel != null && scope.ngModel !== '' && scope.ngModel.length > 18)\r\n                        scope.ngModel = scope.ngModel.substring(0, 10);\r\n                }\r\n\r\n                if (scope.step !== 'no' && typeof (scope.step) !== \"undefined\") {\r\n                    obj.step = scope.step;\r\n                }\r\n\r\n                if (scope.ctrlType !== 3 && scope.controlData != undefined) {\r\n                    switch (scope.controlData.type) {\r\n                        case \"onlypass\":\r\n                            obj.maxDate = '0'; //只能选今天以前-仅过去时间\r\n                            break;\r\n                        case \"onlyfuture\":\r\n                            obj.minDate = '0';//只能选今天以后-仅未来时间\r\n                            break;\r\n                    }\r\n                }\r\n                element.datetimepicker(obj);\r\n            }\r\n        };\r\n    }]);\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/datepicker/index.js\n// module id = 15\n// module chunks = 0", "angular.module('mam-ng')\r\n    .directive('mamEntityView', function () {\r\n        return {\r\n            restrict: 'A',\r\n            scope: {\r\n                viewtype : \"@\",  //type='edit' or type='browse'\r\n                entity : \"<\"\r\n            },\r\n            link: function (scope, element, attr) {\r\n                var url = mam.entity.getViewEntityUrl(scope.entity, scope.viewtype);\r\n\r\n                function init(){\r\n                    if (element[0].tagName === \"A\")\r\n                    {\r\n                        element.attr(\"href\", url);\r\n                        element.attr(\"target\", \"_blank\");\r\n                    }\r\n                }\r\n\r\n                init();\r\n            }\r\n        };\r\n    });\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/entityView/index.js\n// module id = 16\n// module chunks = 0", "angular.module('mam-ng').directive('mamHref', function () {\r\n    return {\r\n        restrict: 'A',\r\n        link: function (scope, element, attr) {\r\n            var href = mam.path(attr.mamHref);\r\n            element.attr('href', href);\r\n        }\r\n    };\r\n});\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/href/index.js\n// module id = 17\n// module chunks = 0", "angular.module('mam-ng')\r\n    .directive('imageFileSelector', function () {\r\n        return {\r\n            restrict: 'E',\r\n            template: \"<input type='file' style='display:none' id='{{inputId}}' accept='image/gif,image/jpeg,image/jpg,image/png' />\",\r\n            scope: {\r\n                inputId: '@', //源地址\r\n                onChange: '&',\r\n                onError: '&'\r\n            },\r\n            replace: true,\r\n            link: function (scope, element, attr) {\r\n                element.bind('change', function (event) {\r\n                    var file = element[0].files[0];\r\n                    if (file == null) {\r\n                        return;\r\n                    }\r\n                    var reader = new FileReader();\r\n                    reader.onload = function () {\r\n                        scope.onChange({\r\n                            file: file,\r\n                            base64: this.result\r\n                        });\r\n                        element.val('');\r\n                    }\r\n                    reader.onerror = function () {\r\n                        scope.onError();\r\n                    }\r\n                    reader.readAsDataURL(file);\r\n                });\r\n            }\r\n        };\r\n    });\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/image-file-selector/index.js\n// module id = 18\n// module chunks = 0", "angular.module('mam-ng').directive('mamImage', function () {\r\n    return {\r\n        restrict: 'E',\r\n        template: '<img />',\r\n        scope: {\r\n            url: '<',   //需要加载的图片地址\r\n            def: '@',   //图片地址为空时使用的地址\r\n            error: '@?',//加载失败时的图片\r\n        },\r\n        transclude: false,\r\n        replace: true,\r\n        link: function (scope, element, attr) {\r\n            var $img = $(element);\r\n\r\n            if (_.isEmpty(scope.error)) {\r\n                scope.error = scope.def;\r\n            }\r\n\r\n            scope.$watch('url', function () {\r\n                var src = _.isEmpty(scope.url) ? scope.def : scope.url;\r\n                src = mam.path(src);\r\n                $img.one('error', function () {\r\n                    $img.attr('src', mam.path(scope.error));\r\n                });\r\n                $img.attr('src', src);\r\n            });\r\n\r\n        }\r\n    };\r\n});\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/image/index.js\n// module id = 19\n// module chunks = 0", "angular.module('mam-ng')\r\n    .directive('mamInputLimit', function () {\r\n        return {\r\n            restrict: 'A',\r\n            scope: {\r\n                limitStr: '@?',\r\n                ngModel: '='\r\n            },\r\n            link: function (scope, element, attr) {\r\n                var $e = $(element);\r\n                var chars = ['\\\\', '/', '\"', ':', '*', '?', '<', '>', '|'];\r\n                var isShow = false;\r\n                var timer = 0;\r\n                if (scope.limitStr) {\r\n                    var chars = scope.limitStr.split('');\r\n                }\r\n                eval(\"scope.limitStr = /[\" + chars.join('\\\\') + \"]/g\");\r\n                var tip = $('<div style=\"display:none;\" class=\"mam-input-limit\"><span>文件名不能包含以下任何字符：<p>' + chars.join(' ') + '</p></span></div>');\r\n                $e.after(tip);\r\n\r\n                function autoClose(){\r\n                    timer = setTimeout(function () {\r\n                        tip.fadeToggle();\r\n                        isShow = false;\r\n                    }, 3000);\r\n                }\r\n\r\n                $e.on(\"keyup\", function (e) {\r\n                    if (e.keyCode != 16 && e.keyCode != 17) {\r\n                        clearTimeout(timer);\r\n                        if (scope.limitStr.test(scope.ngModel)) {\r\n                            if (!isShow) {\r\n                                isShow = true;\r\n                                tip.fadeToggle();\r\n                            } \r\n                            scope.ngModel = scope.ngModel.replace(scope.limitStr, '');\r\n                            scope.$apply();\r\n                            autoClose();\r\n                        } else {\r\n                            if (isShow) {\r\n                                isShow = false;\r\n                                tip.fadeToggle();\r\n                            }\r\n                        }\r\n                    }\r\n                });\r\n            }\r\n        };\r\n    });\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/input-limit/index.js\n// module id = 20\n// module chunks = 0", "import './style.less';\r\n\r\nangular.module('mam-ng')\r\n    .provider('$mamKeyframe', function () {\r\n        var defaults = {\r\n            extKeyframes: [],\r\n            typeKeyframes: [],\r\n            other: '',\r\n            folder: ''\r\n        };\r\n        function getKeyframeByCode(keyframes, code) {\r\n            if (_.isArray(keyframes)) {\r\n                var item = _.find(keyframes, { 'code': code });\r\n                if (item != null) {\r\n                    return item.keyframe;\r\n                }\r\n            }\r\n            return '';\r\n        }\r\n        this.setDefaults = function (options) {\r\n            defaults = $.extend({}, defaults, options);\r\n            if (_.isEmpty(defaults.folder)) {\r\n                defaults.folder = getKeyframeByCode(defaults.extKeyframes, 'folder');\r\n            }\r\n            if (_.isEmpty(defaults.other)) {\r\n                defaults.other = getKeyframeByCode(defaults.typeKeyframes, 'other');\r\n            }\r\n        }\r\n\r\n        this.$get = function () {\r\n            return { defaults: defaults };\r\n        };\r\n    })\r\n    .directive('mamKeyframe', ['$mamKeyframe', function ($mamKeyframe) {\r\n        var opts = $mamKeyframe.defaults;\r\n\r\n        return {\r\n            restrict: 'E',\r\n            template: '<div class=\"mam-keyframe\"><img /></div>',\r\n            scope: {\r\n                src: '=',   //关键帧地址\r\n                ext: '<?',  //素材扩展名\r\n                type: '<?'  //素材类型\r\n            },\r\n            transclude: false,\r\n            replace: true,\r\n            link: function (scope, element, attr) {\r\n\r\n                var $img = element.find('img');\r\n\r\n                function useDefault() {\r\n                    element.addClass('mam-keyframe-default');\r\n                    if (scope.type == 'folder') {\r\n                        return load(opts.folder);\r\n                    }\r\n\r\n                    if (!_.isEmpty(scope.ext)) {\r\n                        var item = _.find(opts.extKeyframes, function (n) {\r\n                            if (_.isArray(n.extensions)) {\r\n                                return n.extensions.indexOf(scope.ext) != -1;\r\n                            }\r\n                            return false;\r\n                        });\r\n                        if (item != null) {\r\n                            return load(item.keyframe);\r\n                        }\r\n                    }\r\n                    if (!_.isEmpty(scope.type)) {\r\n                        var item = _.find(opts.typeKeyframes, { code: scope.type });\r\n                        if (item != null) {\r\n                            return load(item.keyframe);\r\n                            $img.attr('src', mam.path(item.keyframe));\r\n                            return;\r\n                        }\r\n                    }\r\n                    load(opts.other);\r\n                }\r\n\r\n                function load(src) {\r\n                    $img.removeClass('mam-keyframe-loaded');\r\n                    $img.attr('src', mam.path(src));\r\n                }\r\n                //todo:增加mam-keyframe-loaded目的是为了解决在图片未加载出来前，视频类型的出现黑色背景的问题\r\n                //     但相关代码，有点问题。问题是解决。暂时先这样。\r\n                $img.on('load', function () {\r\n                    element.addClass('mam-keyframe-loaded');\r\n                });\r\n\r\n                scope.$watchGroup(['src', 'type', 'ext'], function (newValue, oldValue, scope) {\r\n                    if (!_.isEmpty(scope.ext)) {\r\n                        scope.ext = scope.ext.toLowerCase();\r\n                    }\r\n\r\n                    if (!_.isEmpty(scope.type)) {\r\n                        //正常情况下type只会从没值变化成有值，不会出现从 video 变成  audio\r\n                        element.addClass('mam-keyframe-type-' + scope.type);\r\n                    }\r\n\r\n                    if (_.isEmpty(scope.src)) {\r\n                        useDefault();\r\n                    } else {\r\n                        element.removeClass('mam-keyframe-default');\r\n                        $img.one('error', useDefault);\r\n                        load(scope.src);\r\n                    }\r\n\r\n                });\r\n\r\n            }\r\n        };\r\n    }]);\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/keyframe/index.js\n// module id = 21\n// module chunks = 0", "require('./style.less');\r\n\r\nangular.module('mam-ng')\r\n    .directive('mamLogo', ['$sce', function ($sce) {\r\n        return {\r\n            restrict: 'E',\r\n            template: require('./template.html'),\r\n            transclude: true,\r\n            replace: true,\r\n            scope: {\r\n                config : \"<\"\r\n            },\r\n            link: function (scope, element, attr) {\r\n                scope.config = scope.config || nxt.config;\r\n\r\n                function init() {\r\n                }\r\n\r\n                init();\r\n            }\r\n\r\n        };\r\n    }]);\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/logo/index.js\n// module id = 22\n// module chunks = 0", "require('./style.less');\r\n\r\nangular.module('mam-ng')\r\n    .provider('$mamPager', function () {\r\n        var defaults = this.defaults = {\r\n            text: {\r\n                total: l('com.pageTotal', '总共'),\r\n                record: l('com.pageRecord', '条'),\r\n                index: l('com.pageIndex', '页码'),\r\n                first: l('com.pageFirst', '首页'),\r\n                last: l('com.pageLast', '尾页'),\r\n                prev: l('com.pagePrev', '上一页'),\r\n                next: l('com.pageNext', '下一页')\r\n            }\r\n        };\r\n        this.$get = function () {\r\n            return { defaults: defaults };\r\n        };\r\n    })\r\n    .directive('mamPager', ['$mamPager', function ($mamPager) {\r\n        var opts = $mamPager.defaults;\r\n        return {\r\n            restrict: 'E',\r\n            template: require('./template.html'),\r\n            replace: true,\r\n            scope: {\r\n                recordTotal: '<?',\r\n                pageIndex: '<?',\r\n                pageSize: '<?',\r\n                pageTotal: '<?',\r\n                showText: '<?',\r\n                pageChanged: '&?',\r\n                text: '<?',\r\n                maxSize: '@?'\r\n            },\r\n            link: function (scope, element, attr) {\r\n                //debugger;\r\n                scope.textInfo = scope.text || opts.text;\r\n                scope.maxSize = scope.maxSize || 7;\r\n\r\n                if (scope.showText == undefined) {\r\n                    scope.showText = true;\r\n                }\r\n\r\n                scope.$watch('pageTotal', function (item) {\r\n                    //如果在html上用ng-if来做这个事情。会因为ng-if会产生一个作用域，导致一个bug。\r\n                    if (scope.pageTotal > 1) {\r\n                        element.find('.pagination').show();\r\n                    } else {\r\n                        element.find('.pagination').hide();\r\n                    }\r\n                });\r\n\r\n                scope.$watch('text', function () {\r\n                    if (scope.text)\r\n                    {\r\n                        scope.textInfo = $.extend({}, opts.text, scope.text);\r\n                    }\r\n                });\r\n\r\n                scope.change = function (item) {\r\n                    scope.pageChanged({ page: item });\r\n                }\r\n\r\n                // 监控你的页码 ， 发生改变既请求\r\n                scope.$watch('recordTotal', function (item) {\r\n                    scope.pageTotal = Math.ceil(scope.recordTotal / scope.pageSize);\r\n                    if (scope.pageIndex > 1 && scope.pageIndex > scope.pageTotal) {\r\n                        scope.change(scope.pageTotal);\r\n                    }\r\n                });\r\n            }\r\n        };\r\n    }]);\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/pager/index.js\n// module id = 23\n// module chunks = 0", "require('./style.less');\r\n\r\nangular.module('mam-ng')\r\n    .provider('$mamRadio', function () {\r\n        var defaults = this.defaults = {\r\n            className: 'mam-radio',\r\n            icon: require('./icon.svg')\r\n        };\r\n\r\n        this.$get = function () {\r\n            return { defaults: defaults };\r\n        };\r\n    })\r\n    .directive('mamRadioGroup', function () {\r\n        return {\r\n            restrict: 'A',\r\n            require: 'ngModel',\r\n            compile: function (element, attr) {\r\n                element.removeAttr('ng-model');\r\n                var items = element.find('input[type=\"radio\"]');\r\n                angular.forEach(items, function (item) {\r\n                    angular.element(item).attr('ng-model', attr.ngModel);\r\n                });\r\n            }\r\n        };\r\n\r\n    })\r\n    .directive('mamRadio', ['$mamRadio', function ($mamRadio) {\r\n        var opts = $mamRadio.defaults;\r\n        return {\r\n            restrict: 'A',\r\n            link: function (scope, element, attr, controller) {\r\n\r\n                var label = element.parent('label');\r\n                if (!label.hasClass(opts.className)) {\r\n                    label.addClass(opts.className);\r\n                }\r\n\r\n                label.append(opts.icon);\r\n\r\n                function set(model) {\r\n                    var val = attr.ngValue == null ? element.attr('value') : scope.$eval(attr.ngValue);\r\n                    if (val == model) {\r\n                        label.addClass('checked').removeClass('unchecked');\r\n                    } else {\r\n                        label.addClass('unchecked').removeClass('checked');\r\n                    }\r\n                }\r\n\r\n                if (attr.ngModel != null && attr.ngModel != '') {\r\n                    scope.$watch(attr.ngModel, function (newVal) {\r\n                        set(newVal);\r\n                    }, false);\r\n                    if (attr.ngValue != null && attr.ngValue != '') {\r\n                        scope.$watch(attr.ngValue, function (newVal) {\r\n                            set(scope.$eval(attr.ngModel));\r\n                        }, false);\r\n                    }\r\n                    set(scope.$eval(attr.ngModel));\r\n                }\r\n\r\n                element.on('focus', function () {\r\n                    label.addClass('focus');\r\n                }).on('blur', function () {\r\n                    label.removeClass('focus');\r\n                });\r\n            }\r\n        };\r\n    }]);\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/radio/index.js\n// module id = 24\n// module chunks = 0", "/*\r\n *指令名：mam-resize-table,属性指令\r\n *scope属性：scroll-x。值为true(表格溢出滚动）或false（表格不会溢出）\r\n *此指令应用于表格的body下的行，必须与ng-repeat指令处于同一位置\r\n *（由于指令的解析顺序问题，如果此指令位于ng-repeat指令之前，会使得body部分的内容还未解析而取不到）\r\n *表格的head行部分必须使用'.flex-head'作为类名\r\n *body部分的每一行必须使用'.flex-item'作为类名\r\n **/\r\n\r\nangular.module('mam-ng').directive('mamResizeTable', function($timeout) {\r\n    return {\r\n        restrict: 'A',\r\n        scope: {\r\n            scrollX: '='\r\n        },\r\n        link: function(scope, element, attrs) {\r\n            $timeout(function() {\r\n                var $table = element.parent().parent(); //表格\r\n                var $rol = $table.find('.flex-item,.flex-head'); //所有行\r\n                var $ceils = $rol.children(); //所有单元格\r\n                var $head = $table.find('.flex-head');\r\n                var $headceils = $head.children();\r\n\r\n                $rol.css('width', $table.width() - 2 + 'px');\r\n                $ceils.css('position', 'relative');\r\n                $table.css('overflow-x', scope.scrollX ? 'scroll' : 'hidden');\r\n                $table.css('overflow-y', 'hidden');\r\n\r\n                var $resizeline = $('<div class=\"resize-line\"></div>');\r\n                $resizeline.attr('draggable', true);\r\n                $resizeline.css({\r\n                    'width': '0',\r\n                    'height': '100%',\r\n                    'position': 'absolute',\r\n                    'z-index': '9999',\r\n                    'right': '-2px',\r\n                    'top': '0',\r\n                    'border-right': '5px solid transparent',\r\n                    'cursor': 'e-resize'\r\n                });\r\n                var $dashline = $('<div class=\"dash-line\"></div>')\r\n                $dashline.css({\r\n                    'width': '0',\r\n                    'height': $table.height() + 'px',\r\n                    'position': 'absolute',\r\n                    'z-index': '-9999',\r\n                    'right': '0',\r\n                    'top': '0',\r\n                    'border-right': '2px dashed transparent'\r\n                });\r\n\r\n                var startPosition = 0;\r\n                var startWidth = 0;\r\n                var currentColumn = 0; //当前单元格所在列的列序号\r\n                var currentArray = []; //当前单元格所在列的所有单元格所组成的数组\r\n                var currentDashLine = '';\r\n\r\n                var dragStartFunc = function(e, t) {\r\n                    e.originalEvent.dataTransfer.effectAllowed = \"move\";\r\n                    startPosition = e.clientX;\r\n                    startWidth = $(t).parent().width();\r\n                    for (var i = 0; i < $(t).parent().parent().children().length; i++) {\r\n                        if ($(t).parent().get(0) == $(t).parent().parent().children().get(i)) {\r\n                            currentColumn = i;\r\n                            currentDashLine = $headceils.find('.dash-line').get(i);\r\n                            break;\r\n                        }\r\n                    }\r\n                    currentArray = lineArr[currentColumn];\r\n                }\r\n                var draggingFunc = function(e) {\r\n                    $(currentDashLine).css('right', -(e.clientX - startPosition) + 'px');\r\n                    $(currentDashLine).css('border-right', '2px dashed gray');\r\n                }\r\n                var dragFunc = function(e) {\r\n                    $(currentDashLine).css('right', '0');\r\n                    $(currentDashLine).css('border-right', '2px dashed transparent');\r\n                    angular.forEach(currentArray, function(o) {\r\n                        $(o).css('flex', '0 1 auto');\r\n                        if (scope.scrollX) {\r\n                            $(o).css('min-width', startWidth + e.clientX - startPosition + 'px');\r\n                        } else {\r\n                            $(o).css('width', startWidth + e.clientX - startPosition + 'px');\r\n                        }\r\n                    })\r\n                }\r\n                $resizeline.on('dragstart', function(e) {\r\n                    var t = this;\r\n                    dragStartFunc(e, t);\r\n                });\r\n                $resizeline.on('drag', function(e) {\r\n                    draggingFunc(e);\r\n                });\r\n                $resizeline.on('dragend', function(e) {\r\n                    dragFunc(e);\r\n                });\r\n                $ceils.on('dragover', function(e) {\r\n                    e.preventDefault();\r\n                    e.originalEvent.dataTransfer.dropEffect = \"move\";\r\n                    return true;\r\n                });\r\n\r\n                $ceils.find('.resize-line').remove(); //先移除后添加，避免ng-repeat造成每个单元格上重复出现多个resize-line\r\n                $ceils.append($resizeline);\r\n                $headceils.find('.dash-line').remove();\r\n                $headceils.append($dashline); //拖动时看到的虚线\r\n\r\n                var lineArr = new Array($rol.eq(0).find('.resize-line').length);\r\n                for (var i = 0; i < lineArr.length; i++) {\r\n                    lineArr[i] = [];\r\n                }\r\n                angular.forEach($rol, function(rol, index) {\r\n                    angular.forEach($(rol).find('.resize-line'), function(o, i) {\r\n                        lineArr[i].push($(o).parent());\r\n                    })\r\n                })\r\n            });\r\n        }\r\n    };\r\n});\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/resize-table/index.js\n// module id = 25\n// module chunks = 0", "angular.module('mam-ng').directive('mamResizer', function () {\r\n    return {\r\n        restrict: 'E',\r\n        scope: {\r\n            direction: '@?',\r\n            width: '@?', //宽度，单位像素，不用带 px\r\n            height: '@?',\r\n            offset: '@?',\r\n            max: '@?',\r\n            left: '@?',\r\n            right: '@?',\r\n            top: '@?',\r\n            bottom: '@?'\r\n        },\r\n        link: function (scope, element, attrs) {\r\n            scope.direction = scope.direction || 'h';\r\n\r\n            if (scope.direction == 'h') {\r\n                element.css('cursor', 'ew-resize');\r\n            } else {\r\n                element.css('cursor', 'ns-resize');\r\n            }\r\n\r\n            element.on('mousedown', function (event) {\r\n                event.preventDefault();\r\n                element.addClass('active');\r\n                $(document).on('mousemove', mousemove);\r\n                $(document).on('mouseup', mouseup);\r\n            });\r\n\r\n            function mousemove(event) {\r\n                if (scope.direction == 'h') {\r\n                    var x = event.pageX;\r\n                    if (scope.offset) {\r\n                        x += parseInt(scope.offset);\r\n                    }\r\n                    if (scope.max && x > scope.max) {\r\n                        x = parseInt(scope.max);\r\n                    }\r\n                    element.css({ left: x + 'px' });\r\n                    $(scope.left).css({ width: x + 'px' });\r\n                    $(scope.right).css({\r\n                        left: (x + parseInt(scope.width)) + 'px'\r\n                    });\r\n                } else {\r\n                    var y = event.pageY;\r\n                    if (scope.offset) {\r\n                        y += parseInt(scope.offset);\r\n                    }\r\n                    if (scope.max && y > scope.max) {\r\n                        y = parseInt(scope.max);\r\n                    }\r\n                    element.css({ top: y + 'px' });\r\n                    $(scope.top).css({ height: y + 'px' });\r\n                    $(scope.bottom).css({ \r\n                        top: (y + parseInt(scope.height)) + 'px' \r\n                    });\r\n                }\r\n            }\r\n\r\n            function mouseup() {\r\n                element.removeClass('active');\r\n                $(document).unbind('mousemove', mousemove);\r\n                $(document).unbind('mouseup', mouseup);\r\n            }\r\n        }\r\n    };\r\n});\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/resizer/index.js\n// module id = 26\n// module chunks = 0", "require('./style.less');\r\n\r\nangular.module('mam-ng')\r\n    .directive('mamSearchInput', ['$http', function ($http) {\r\n        return {\r\n            restrict: 'E',\r\n            template: require('./template.html'),\r\n            replace: true,\r\n            scope: {\r\n                search: '&',\r\n                keywords: '=',\r\n                useHistory: '<?',\r\n                placeholder: '@?'\r\n            },\r\n            link: function (scope, element, attr) {\r\n                var $e = $(element);\r\n                var boxId = '#mam-history-box';\r\n                var historyBox = $e.find(boxId);\r\n                var index = -1;\r\n                var isShow = false;\r\n                scope.text = {\r\n                    placeholder: scope.placeholder || 'keyword'\r\n                };\r\n                scope.selected;\r\n\r\n                function SearchHistory() {\r\n                    var self = this;\r\n                    this.items = [];\r\n\r\n                    function changeBox() {\r\n                        historyBox.not(historyBox).stop().slideUp(100).parent().find(boxId);\r\n                        historyBox.stop().slideToggle(100);\r\n                    };\r\n\r\n                    this.showBox = function ($event, op) {\r\n                        if (!scope.useHistory) return;\r\n                        if ($event != undefined)\r\n                            $event.stopPropagation();\r\n                        function show() {\r\n                            if (!isShow && self.items.length > 0) {\r\n                                scope.selected = undefined;\r\n                                index = -1;\r\n                                isShow = true;\r\n                                changeBox();\r\n                            }\r\n                        }\r\n                        setTimeout(function () {\r\n                            var res = self.get(op ? scope.keywords : undefined);\r\n                            if (res) {\r\n                                res.then(function () {\r\n                                    show();\r\n                                });\r\n                            } else\r\n                                show();\r\n                        }, 100);\r\n                    };\r\n\r\n                    this.close = function () {\r\n                        if (isShow) {\r\n                            isShow = false;\r\n                            changeBox();\r\n                        }\r\n                    }\r\n\r\n                    this.delete = function (id, $event) {\r\n                        if (!scope.useHistory) return;\r\n                        $event.stopPropagation();\r\n                        $http.delete('~/search/history/' + id, { errorHandle: false }).then(function (res) {\r\n                            if (res.data) {\r\n                                self.get();\r\n                            }\r\n                        }, function (res) {\r\n                            console.error(res);\r\n                        });\r\n                    };\r\n\r\n                    this.getBy = function () {\r\n                        self.get(scope.keywords);\r\n                    }\r\n\r\n                    this.get = function (keyword) {\r\n                        if (scope.useHistory) {\r\n                            var url = '~/search/tip-search?keyword=';\r\n                            if (keyword)\r\n                                url += keyword;\r\n                            return $http.get(url, { errorHandle: false, showLoader: false }).then(function (res) {\r\n                                if (keyword == scope.keywords || keyword == undefined)\r\n                                    self.items = res.data;\r\n                            }, function (res) {\r\n                                if (res.status == 404) {\r\n                                    self.items = [];\r\n                                    console.info('需要/search/tip-search接口');\r\n                                }\r\n                                console.error(res);\r\n                            });\r\n                        }\r\n                    };\r\n\r\n                    (function () {\r\n                        if (!historyBox) {\r\n                            scope.useHistory = false;\r\n                        } else {\r\n                            historyBox.hide();\r\n                            setTimeout(self.get, 5000);\r\n                        }\r\n                    })();\r\n                }\r\n\r\n                scope.submit = function (keyword) {\r\n                    if (keyword)\r\n                        scope.keywords = keyword;\r\n                    setTimeout(function () {\r\n                        scope.$apply(function () {\r\n                            scope.search();\r\n                        });\r\n                    }, 50)\r\n\r\n                    scope.sh.close();\r\n                }\r\n\r\n                scope.changeModel = function (keyword) {\r\n                    if (!keyword)\r\n                        scope.selected = undefined;\r\n                    else\r\n                        scope.selected = keyword;\r\n                }\r\n\r\n                function init() {\r\n                    if (scope.keywords == undefined)\r\n                        console.error('检索输入框插件：检索keywords初始值不能为undefined！');\r\n                    scope.sh = new SearchHistory(historyBox);\r\n                    if (scope.useHistory)\r\n                        $e.on('keydown', function (e) {\r\n                            var length = scope.sh.items.length;\r\n                            if (e.keyCode == 38) {\r\n                                index--;\r\n                                if (index <= -2) index = 0;\r\n                                if (index < 0) index = length - 1;\r\n                                scope.selected = scope.keywords = scope.sh.items[index].keyword;\r\n                            } else if (e.keyCode == 40) {\r\n                                index++;\r\n                                if (index >= length) index = 0;\r\n                                scope.selected = scope.keywords = scope.sh.items[index].keyword;\r\n                            } else if (e.keyCode == 27) {\r\n                                scope.selected = undefined;\r\n                                scope.sh.close();\r\n                            } else if (e.keyCode == 13) {\r\n                                element.find('input').blur();\r\n                                scope.submit();\r\n                            }\r\n                            scope.$apply();\r\n                        });\r\n\r\n                    $(document).on('click', scope.sh.close);\r\n                }\r\n\r\n                init();\r\n            }\r\n        };\r\n    }]);\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/search-input/index.js\n// module id = 27\n// module chunks = 0", "angular.module('mam-ng').directive('mamSelectable', function () {\r\n    return {\r\n        restrict: 'A',\r\n        link: function (scope, element, attr) {\r\n            element.on('mousedown', init);\r\n\r\n            var box = element,\r\n                mode = attr.mode;\r\n\r\n            function segmentsIntr(a, b, c, d) {\r\n\r\n                // 三角形abc 面积的2倍\r\n                var area_abc = (a.x - c.x) * (b.y - c.y) - (a.y - c.y) * (b.x - c.x);\r\n\r\n                // 三角形abd 面积的2倍\r\n                var area_abd = (a.x - d.x) * (b.y - d.y) - (a.y - d.y) * (b.x - d.x);\r\n\r\n                // 面积符号相同则两点在线段同侧,不相交;\r\n                if (area_abc * area_abd >= 0) {\r\n                    return false;\r\n                }\r\n\r\n                // 三角形cda 面积的2倍\r\n                var area_cda = (c.x - a.x) * (d.y - a.y) - (c.y - a.y) * (d.x - a.x);\r\n                // 三角形cdb 面积的2倍\r\n                // 注意: 这里有一个小优化.不需要再用公式计算面积,而是通过已知的三个面积加减得出.\r\n                var area_cdb = area_cda + area_abc - area_abd;\r\n                if (area_cda * area_cdb >= 0) {\r\n                    return false;\r\n                }\r\n\r\n                return true;\r\n            }\r\n\r\n            function init() {\r\n                var firstTime = new Date().getTime(),\r\n                    boxX = event.clientX - parseInt(box.offset().left),\r\n                    boxY = event.clientY - parseInt(box.offset().top) + document.body.scrollTop;\r\n                $(document).on('mouseup', { firstTime: firstTime }, end);\r\n\r\n                box.append('<div class=\"mam-selectable-box\"></div>').on('mousemove', { x: event.clientX, y: event.clientY + document.body.scrollTop }, move);\r\n\r\n                box.css('position', 'relative');\r\n                $('.mam-selectable-box').css({\r\n                    'position': 'absolute',\r\n                    'top': boxY,\r\n                    'left': boxX,\r\n                    'backgroundColor': 'rgba(0, 196, 244, 0.3)'\r\n                });\r\n                $('body').css('userSelect', 'none').attr('ondragstart', 'return false;');\r\n                $('head').append(\"<style type='text/css' key='select'>::selection{ background: none; }</style>\");\r\n            }\r\n\r\n            function move(e) {\r\n                var boxX = event.clientX - e.data.x,\r\n                    boxY = event.clientY - e.data.y + document.body.scrollTop,\r\n                    selectItems = $(attr.itemname),\r\n                    selectBox = $('.mam-selectable-box'),\r\n                    selectBoxTop = selectBox.offset().top,\r\n                    selectBoxBottom = selectBox.get(0).offsetHeight + selectBoxTop,\r\n                    selectBoxLeft = selectBox.offset().left,\r\n                    selectBoxRight = selectBox.get(0).offsetWidth + selectBoxLeft;\r\n\r\n                if (boxX > 0 && boxY > 0) {\r\n                    selectBox.css({\r\n                        'width': boxX,\r\n                        'height': boxY\r\n                    })\r\n                } else if (boxX > 0 && boxY < 0) {\r\n                    selectBox.css({\r\n                        'width': boxX,\r\n                        'height': -boxY,\r\n                        'top': event.clientY - parseInt(box.offset().top) + document.body.scrollTop\r\n                    })\r\n                } else if (boxX < 0 && boxY > 0) {\r\n                    selectBox.css({\r\n                        'width': -boxX,\r\n                        'height': boxY,\r\n                        'left': event.clientX - parseInt(box.offset().left)\r\n                    })\r\n                } else if (boxX < 0 && boxY < 0) {\r\n                    selectBox.css({\r\n                        'width': -boxX,\r\n                        'height': -boxY,\r\n                        'top': event.clientY - parseInt(box.offset().top) + document.body.scrollTop,\r\n                        'left': event.clientX - parseInt(box.offset().left)\r\n                    })\r\n                }\r\n\r\n                for (var i = 0; i < selectItems.length; i++) {\r\n                    var itemLeft = selectItems.eq(i).offset().left,\r\n                        itemRight = itemLeft + selectItems.eq(i).width(),\r\n                        itemTop = selectItems.eq(i).offset().top,\r\n                        itemBottom = itemTop + selectItems.eq(i).height();\r\n\r\n                    if (mode === 'single') {\r\n                        if (selectBoxTop > itemTop && selectBoxTop < itemBottom || selectBoxTop === itemTop) {\r\n                            selectItems.eq(i).css('backgroundColor', 'rgba(0, 196, 244, 0.3)');\r\n                        } else if (itemTop > selectBoxTop && itemBottom < selectBoxBottom) {\r\n                            selectItems.eq(i).css('backgroundColor', 'rgba(0, 196, 244, 0.3)');\r\n                        } else if (selectBoxBottom > itemTop && selectBoxBottom < itemBottom) {\r\n                            selectItems.eq(i).css('backgroundColor', 'rgba(0, 196, 244, 0.3)');\r\n                        } else {\r\n                            selectItems.eq(i).css('backgroundColor', '');\r\n                        }\r\n                    } else if (mode === 'multi') {\r\n                        //横向与纵向\r\n                        if (segmentsIntr({ x: selectBoxLeft, y: selectBoxTop }, { x: selectBoxRight, y: selectBoxTop }, { x: itemLeft, y: itemTop }, { x: itemLeft, y: itemBottom }) ||\r\n                            segmentsIntr({ x: selectBoxLeft, y: selectBoxTop }, { x: selectBoxRight, y: selectBoxTop }, { x: itemRight, y: itemTop }, { x: itemRight, y: itemBottom }) ||\r\n                            segmentsIntr({ x: selectBoxLeft, y: selectBoxBottom }, { x: selectBoxRight, y: selectBoxBottom }, { x: itemLeft, y: itemTop }, { x: itemLeft, y: itemBottom }) ||\r\n                            segmentsIntr({ x: selectBoxLeft, y: selectBoxBottom }, { x: selectBoxRight, y: selectBoxBottom }, { x: itemRight, y: itemTop }, { x: itemRight, y: itemBottom }) ||\r\n                            //纵向与横向\r\n                            segmentsIntr({ x: selectBoxLeft, y: selectBoxTop }, { x: selectBoxLeft, y: selectBoxBottom }, { x: itemLeft, y: itemTop }, { x: itemRight, y: itemTop }) ||\r\n                            segmentsIntr({ x: selectBoxLeft, y: selectBoxTop }, { x: selectBoxLeft, y: selectBoxBottom }, { x: itemLeft, y: itemBottom }, { x: itemRight, y: itemBottom }) ||\r\n                            segmentsIntr({ x: selectBoxRight, y: selectBoxTop }, { x: selectBoxRight, y: selectBoxBottom }, { x: itemLeft, y: itemTop }, { x: itemRight, y: itemTop }) ||\r\n                            segmentsIntr({ x: selectBoxRight, y: selectBoxTop }, { x: selectBoxRight, y: selectBoxBottom }, { x: itemLeft, y: itemBottom }, { x: itemRight, y: itemBottom })) {\r\n                            selectItems.eq(i).css('backgroundColor', 'rgba(0, 196, 244, 0.3)');\r\n                        } else if (itemTop > selectBoxTop && itemBottom < selectBoxBottom && itemLeft > selectBoxLeft && itemRight < selectBoxRight ||\r\n                            itemTop < selectBoxTop && itemBottom > selectBoxBottom && itemLeft < selectBoxLeft && itemRight > selectBoxRight) {\r\n                            selectItems.eq(i).css('backgroundColor', 'rgba(0, 196, 244, 0.3)');\r\n                        } else {\r\n                            selectItems.eq(i).css('backgroundColor', '');\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n            function end(e) {\r\n                var lastTime = new Date().getTime(),\r\n                    selectItems = $(attr.itemname),\r\n                    selectBox = $('.mam-selectable-box'),\r\n                    selectBoxTop = selectBox.offset().top,\r\n                    selectBoxBottom = selectBox.get(0).offsetHeight + selectBoxTop,\r\n                    selectBoxLeft = selectBox.offset().left,\r\n                    selectBoxRight = selectBox.get(0).offsetWidth + selectBoxLeft,\r\n                    items = scope.$eval(attr.mamSelectable);\r\n\r\n                if (lastTime - e.data.firstTime > 200) {\r\n                    for (var i = 0; i < selectItems.length; i++) {\r\n                        var itemLeft = selectItems.eq(i).offset().left,\r\n                            itemRight = itemLeft + selectItems.eq(i).width(),\r\n                            itemTop = selectItems.eq(i).offset().top,\r\n                            itemBottom = itemTop + selectItems.eq(i).height();\r\n\r\n                        if (mode === 'single') {\r\n                            selectItems.eq(i).css('backgroundColor', '');\r\n                            if (selectBoxTop > itemTop && selectBoxTop < itemBottom || selectBoxTop === itemTop) {\r\n                                if (items[i].selected === undefined) {\r\n                                    items[i].selected = true;\r\n                                } else {\r\n                                    items[i].selected = !items[i].selected;\r\n                                }\r\n                            } else if (itemTop > selectBoxTop && itemBottom < selectBoxBottom) {\r\n                                if (items[i].selected === undefined) {\r\n                                    items[i].selected = true;\r\n                                } else {\r\n                                    items[i].selected = !items[i].selected;\r\n                                }\r\n                            } else if (selectBoxBottom > itemTop && selectBoxBottom < itemBottom) {\r\n                                if (items[i].selected === undefined) {\r\n                                    items[i].selected = true;\r\n                                } else {\r\n                                    items[i].selected = !items[i].selected;\r\n                                }\r\n                            }\r\n                        } else if (mode === 'multi') {\r\n                            selectItems.eq(i).css('backgroundColor', '');\r\n                            if (segmentsIntr({ x: selectBoxLeft, y: selectBoxTop }, { x: selectBoxRight, y: selectBoxTop }, { x: itemLeft, y: itemTop }, { x: itemLeft, y: itemBottom }) ||\r\n                                segmentsIntr({ x: selectBoxLeft, y: selectBoxTop }, { x: selectBoxRight, y: selectBoxTop }, { x: itemRight, y: itemTop }, { x: itemRight, y: itemBottom }) ||\r\n                                segmentsIntr({ x: selectBoxLeft, y: selectBoxBottom }, { x: selectBoxRight, y: selectBoxBottom }, { x: itemLeft, y: itemTop }, { x: itemLeft, y: itemBottom }) ||\r\n                                segmentsIntr({ x: selectBoxLeft, y: selectBoxBottom }, { x: selectBoxRight, y: selectBoxBottom }, { x: itemRight, y: itemTop }, { x: itemRight, y: itemBottom }) ||\r\n                                //纵向与横向\r\n                                segmentsIntr({ x: selectBoxLeft, y: selectBoxTop }, { x: selectBoxLeft, y: selectBoxBottom }, { x: itemLeft, y: itemTop }, { x: itemRight, y: itemTop }) ||\r\n                                segmentsIntr({ x: selectBoxLeft, y: selectBoxTop }, { x: selectBoxLeft, y: selectBoxBottom }, { x: itemLeft, y: itemBottom }, { x: itemRight, y: itemBottom }) ||\r\n                                segmentsIntr({ x: selectBoxRight, y: selectBoxTop }, { x: selectBoxRight, y: selectBoxBottom }, { x: itemLeft, y: itemTop }, { x: itemRight, y: itemTop }) ||\r\n                                segmentsIntr({ x: selectBoxRight, y: selectBoxTop }, { x: selectBoxRight, y: selectBoxBottom }, { x: itemLeft, y: itemBottom }, { x: itemRight, y: itemBottom })) {\r\n                                if (items[i].selected === undefined) {\r\n                                    items[i].selected = true;\r\n                                } else {\r\n                                    items[i].selected = !items[i].selected;\r\n                                }\r\n                            } else if (itemTop > selectBoxTop && itemBottom < selectBoxBottom && itemLeft > selectBoxLeft && itemRight < selectBoxRight ||\r\n                                itemTop < selectBoxTop && itemBottom > selectBoxBottom && itemLeft < selectBoxLeft && itemRight > selectBoxRight) {\r\n                                if (items[i].selected === undefined) {\r\n                                    items[i].selected = true;\r\n                                } else {\r\n                                    items[i].selected = !items[i].selected;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                } else {\r\n                    for (var q = 0; q < selectItems.length; q++) {\r\n                        selectItems.eq(q).css('backgroundColor', '');\r\n                    }\r\n                }\r\n\r\n                element.off('mousemove').children('.mam-selectable-box').remove();\r\n                $(document).off();\r\n\r\n                $('body').css('userSelect', '').attr('ondragstart', '');\r\n                $('head').children('style[key=\"select\"]').remove();\r\n\r\n                scope.$applyAsync();\r\n            }\r\n\r\n        }\r\n    };\r\n});\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/selectable/index.js\n// module id = 28\n// module chunks = 0", "import './style.less'\r\n\r\nangular.module('mam-ng')\r\n    .directive('mamSortGroup', function () {\r\n        return {\r\n            restrict: 'E',\r\n            template: require('./template.html'),\r\n            replace: true,\r\n            scope: {\r\n                ngModel: '=',\r\n                onChange: '&?' //排序方式发生改变时\r\n            },\r\n            compile: function (element, attr) {\r\n                var icon = require('./icon.svg');\r\n                var $icon1 = $(icon);\r\n                $icon1.attr('ng-if', 'ngModel.current.hideDirection!==true')\r\n                    .attr('ng-class', '{active:ngModel.current.desc==\\'desc\\'}');\r\n                element.find('button').append($icon1);\r\n\r\n                var $icon2 = $(icon);\r\n                $icon2.attr('ng-if', 'item.hideDirection!==true')\r\n                    .attr('ng-class', '{active:item.desc==\\'desc\\'}');\r\n                element.find('li a').append($icon2);\r\n\r\n                return function (scope, element, attr) {\r\n                    scope.icon = require('./icon.svg');\r\n\r\n                    scope.change = function (item) {\r\n                        if (item != scope.ngModel.current) {\r\n                            if (!_.isEmpty(scope.ngModel.storageKey)) {\r\n                                localStorage.setItem(scope.ngModel.storageKey, JSON.stringify(item));\r\n                            }\r\n                            scope.ngModel.current = item;\r\n                            scope.onChange({ item: item });\r\n                        }\r\n                    }\r\n\r\n                    function init() {\r\n                        if (scope.ngModel.current == null) {\r\n                            if (scope.ngModel.storageKey != null) {\r\n                                var item = localStorage.getItem(scope.ngModel.storageKey);\r\n                                if (!_.isEmpty(item)) {\r\n                                    scope.ngModel.current = JSON.parse(item);\r\n                                    return;\r\n                                }\r\n                            }\r\n                            scope.ngModel.current = _.find(scope.ngModel.items, { default: true });\r\n                            if (scope.ngModel.current == null) {\r\n                                scope.ngModel.current = scope.ngModel.items[0];\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    init();\r\n                }\r\n            }\r\n        };\r\n    });\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/sort-group/index.js\n// module id = 29\n// module chunks = 0", "import './style.less'\r\n\r\nangular.module('mam-ng').directive('mamSpin', function () {\r\n    return {\r\n        restrict: 'E',\r\n        template: require('./template.html'),\r\n        replace: true,\r\n        scope: {\r\n            text: '@?',\r\n            show: '<'\r\n        },\r\n        link: function (scope, element, attr) {\r\n            var parent = element.parent();\r\n            if (parent.css('position') == 'static' || parent.css('position') == 'initial') {\r\n                parent.css('position', 'relative');\r\n            }\r\n        }\r\n    };\r\n});\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/spin/index.js\n// module id = 30\n// module chunks = 0", "require('./style.less');\r\n\r\nangular.module('mam-ng')\r\n    .directive('mamSwitchButton', [function () {\r\n        return {\r\n            restrict: 'EA',\r\n            template: require('./template.html'),\r\n            transclude: true,\r\n            replace: true,\r\n            require: \"ngModel\",\r\n            scope: {\r\n                onChange : \"=\"\r\n            },\r\n            link: function (scope, element, attr,ngModel) {\r\n                scope.changeEnable = function(){\r\n                    scope.val = !ngModel.$viewValue;\r\n                    ngModel.$setViewValue(scope.val);\r\n                    if (scope.onChange && typeof(scope.onChange) == \"function\")\r\n                    {\r\n                        scope.onChange(scope.val);\r\n                    }\r\n                };\r\n\r\n                ngModel.$render = function(){\r\n                    scope.val = ngModel.$viewValue;\r\n                };\r\n            }\r\n\r\n        };\r\n    }]);\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/switch-button/index.js\n// module id = 31\n// module chunks = 0", "require('./style.less');\r\n\r\n//todo:我的消息，支持显示未读消息数量\r\n\r\nangular.module('mam-ng')\r\n    .directive('mamTopNav', ['$sce', '$timeout', '$http', function ($sce, $timeout, $http) {\r\n        return {\r\n            restrict: 'E',\r\n            template: require('./template.html'),\r\n            transclude: true,\r\n            replace: true,\r\n            scope: {\r\n                ngModel: '<?',\r\n                currentUser: '<?'\r\n            },\r\n            link: function (scope, element, attr) {\r\n                var $e = $(element);\r\n                var $navs = $e.find('.navs');\r\n                var $more = $e.find('.more').hide();\r\n                var $moreSubs = $more.find('.subs');\r\n                var moreWidth;\r\n                var navs = [];\r\n                scope.logged = false;\r\n\r\n                function prepare() {\r\n                    var items = [];\r\n\r\n                    for (var i = 0; i < scope.ngModel.length; i++) {\r\n                        var item = scope.ngModel[i];\r\n                        // 判断启用状态\r\n                        if (!item.enable) {\r\n                            continue;\r\n                        }\r\n                        // 判断分割线\r\n                        if (item.content == '|') {\r\n                            items.push(item);\r\n                            continue;\r\n                        }\r\n                        //判断模块开关\r\n                        if (_.isString(item.module)) {\r\n                            var key = item.module.indexOf('Enable') == -1 ? (item.module + 'Enable') : item.module;\r\n                            if (nxt.config[key] !== true) {\r\n                                continue;\r\n                            }\r\n                        }\r\n                        // 判断权限\r\n                        if (_.isString(item.appPermission) && item.appPermission.length > 0) {\r\n                            if (!nxt.permission.judge(item.appPermission)) {\r\n                                continue;\r\n                            }\r\n                        }\r\n\r\n                        if (_.isString(item.content)) {\r\n                            item.content = $sce.trustAsHtml(item.content);\r\n                        }\r\n                        if (!item.target || item.target === 'auto') {\r\n                            item.target = '_self';\r\n                        }\r\n                        item.href = mam.utils.eval(mam.path(item.href));\r\n                        items.push(item);\r\n                    }\r\n\r\n                    for (var i = 0; i < items.length; i++) {\r\n                        //去除第一个和连续的分割线\r\n                        if (items[i].content == '|') {\r\n                            if (i == 0 || items[i - 1].content == '|') {\r\n                                continue;\r\n                            }\r\n                        }\r\n                        // if (items[i].class == 'nav-message') {\r\n                        //     getUnreadCount();\r\n                        // }\r\n                        var nav = renderNav(items[i]);\r\n                        navs.push(nav);\r\n                        $navs.append(nav);\r\n                    }\r\n\r\n                    _.forEach(navs, function (item, i) {\r\n                        scope.ngModel[i].width = item.outerWidth();\r\n                    });\r\n\r\n                    moreWidth = $more.outerWidth();\r\n\r\n                    if (navs.length > 0) {\r\n                        $more.hover(function () {\r\n                            $moreSubs.stop().css('height', 'auto').slideDown(300);\r\n                        }, function () {\r\n                            $moreSubs.stop().slideUp(300);\r\n                        }).show();\r\n                    } else {\r\n                        $more.hide();\r\n                    }\r\n\r\n                    $timeout(function() {\r\n                        resize();\r\n                    }, 500);\r\n                }\r\n\r\n                function resize() {\r\n                        $navs.html('');\r\n                        $moreSubs.html('');\r\n                        var boxWidth = $e.outerWidth();\r\n                        var usedWidth = moreWidth;\r\n                        var index = -1;\r\n                        _.forEach(navs, function (item, i) {\r\n                            usedWidth += scope.ngModel[i].width;\r\n                            if (usedWidth <= boxWidth) {\r\n                                $navs.append(item.removeClass('more-item').addClass('item'));\r\n                            } else {\r\n                                index = i;\r\n                                return false;\r\n                            }\r\n                        });\r\n                        if (index > -1) {\r\n                            var width = 0;\r\n                            for (var i = index; i < navs.length; i++) {\r\n                                width += scope.ngModel[i].width;\r\n                            }\r\n                            if (width > moreWidth) {\r\n                                for (var i = index; i < navs.length; i++) {\r\n                                    $moreSubs.append(navs[i].removeClass('item').addClass('more-item'));\r\n                                }\r\n                                $moreSubs.css({ top: $more.height() });\r\n                                $more.show();\r\n                            } else {\r\n                                for (var i = index; i < navs.length; i++) {\r\n                                    $navs.append(navs[i].removeClass('more-item').addClass('item'));\r\n                                }\r\n                                $more.hide();\r\n                            }\r\n                        } else {\r\n                            $more.hide();\r\n                        }\r\n                        activeCurrentItem();\r\n                }\r\n\r\n                function renderNav(item) {\r\n                    var $item = $('<div class=\"item\" title=\"' + item.tooltip + '\">' +\r\n                        '<a href=\"' + item.href + '\">' + item.content + '</a>' +\r\n                        '</div>');\r\n                    if (item.class) {\r\n                        $item.addClass(item.class);\r\n                    }\r\n                    return $item;\r\n                }\r\n\r\n                function activeCurrentItem() {\r\n                    $navs.find('.item').each(function (index, item) {\r\n                        var href = $(item).find('a').attr('href');\r\n                        if (href.indexOf('#!') > -1)//去掉子路由防止顶部菜单不选中\r\n                        {\r\n                            href = href.substring(0, href.indexOf('#!'));\r\n                        }\r\n                        if (href != '' && href != '#' && location.href.indexOf(href) != -1) {\r\n                            $(item).addClass('current');\r\n                        } else {\r\n                            $(item).removeClass('current');\r\n                        }\r\n                    });\r\n                }\r\n\r\n                //获取未读消息\r\n                //获取未读消息这块的功能还没调整完\r\n                function getUnreadCount() {\r\n                    if (!_.get(window, 'nxt.config.deskEnable.unreadTip')) {\r\n                        return\r\n                    };\r\n                    scope.badge = {\r\n                        message: 0,\r\n                        share: 0,\r\n                        total: function () {\r\n                            return this.message + this.share;\r\n                        }\r\n                    }\r\n                    $timeout(function () {\r\n                        $http.get('~/user/get-user-unread-count').then(function (res) {\r\n                            scope.badge.message = !res.data ? 0 : res.data;\r\n                        });\r\n                        //获取未读分享\r\n                        $http.get('~/share/get-user-unread-count').then(function (res) {\r\n                            scope.badge.share = !res.data ? 0 : res.data;\r\n                        });\r\n                    }, 4000);\r\n                }\r\n\r\n                function init() {\r\n                    scope.currentUser = scope.currentUser || _.get(window, 'nxt.user.current');\r\n                    scope.logged = scope.currentUser != null && scope.currentUser.loginName != 'guest';\r\n\r\n                    if (scope.ngModel == null || scope.ngModel == '') {\r\n                        scope.ngModel = _.get(window, 'nxt.config.topNav', []);\r\n                        prepare();\r\n                    } else {\r\n                        scope.$watch('ngModel', function (newValue, oldValue) {\r\n                            if (_.isArray(newValue)) {\r\n                                prepare();\r\n                            }\r\n                        });\r\n                    }\r\n\r\n                    scope.$on('refreshCount', function (e, data) {\r\n                        if (_.get(window, 'nxt.config.deskEnable.unreadTip')) { //导航添加未读消息数量\r\n                            if (data == undefined) {\r\n                                scope.badge.message = 0;\r\n                                scope.badge.share = 0;\r\n                            } else {\r\n                                if (data.message != undefined)\r\n                                    scope.badge.message = data.message;\r\n                                if (data.share != undefined)\r\n                                    scope.badge.share = data.share;\r\n                            }\r\n                        }\r\n                    });\r\n\r\n                    $(window).on('resize', resize);\r\n\r\n                    $(window).bind('hashchange', activeCurrentItem);\r\n                }\r\n\r\n                init();\r\n            }\r\n\r\n        };\r\n    }]);\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/top-nav/index.js\n// module id = 32\n// module chunks = 0", "require('./style.less');\r\n\r\nangular.module('mam-ng').provider('$mamUserAvatar', function () {\r\n    var defaults = this.defaults = {\r\n        errorSrc: mam.path('~/test/img/error.jpg')\r\n    }\r\n    this.$get = function () {\r\n        return { defaults: defaults };\r\n    };\r\n}).directive('mamUserAvatar', ['$timeout','$mamUserAvatar',function ($timeout,$mamUserAvatar) {\r\n    var opts = $mamUserAvatar.defaults;\r\n    return {\r\n        restrict: 'E',\r\n        template: '<div class=\"mam-user-avatar\"><img /></div>',\r\n        scope: {\r\n            src: '<',\r\n        },\r\n        transclude: false,\r\n        replace: true,\r\n        link: function (scope, element, attr) {\r\n            var $src,\r\n                $errorSrc = opts.errorSrc;\r\n\r\n            function init(){\r\n                if(scope.src !== undefined) {\r\n                    $src = scope.src;\r\n                } else {\r\n                    $src = $errorSrc;\r\n                }\r\n\r\n                var imgObj = new Image();\r\n                $(imgObj).on('load',handleLoad);\r\n                $(imgObj).on('error',handleError);\r\n                imgObj.src = $src;\r\n            }\r\n\r\n            function handleLoad() {\r\n                $(element).find('img').attr('src',$src);\r\n            }\r\n\r\n            function handleError() {\r\n                $(element).find('img').attr('src',$errorSrc);\r\n            }\r\n\r\n            $timeout(function(){\r\n                init();\r\n            });\r\n\r\n            scope.$watch('src', function(newval, oldval) {\r\n                if(oldval !== newval) {\r\n                    init();\r\n                }\r\n            })\r\n        }\r\n    }\r\n}]);\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/user-avatar/index.js\n// module id = 33\n// module chunks = 0", "require('./style.less');\r\n\r\nangular.module('mam-ng')\r\n    .directive('mamUserInfo', ['$sce', function ($sce) {\r\n        return {\r\n            restrict: 'E',\r\n            template: require('./template.html'),\r\n            transclude: true,\r\n            scope: {\r\n                currentUser: \"<\"\r\n            },\r\n            link: function (scope, element, attr) {\r\n                var $e = $(element);\r\n\r\n\r\n                function init() {\r\n                    scope.logged = true;\r\n                    scope.currentUser = scope.currentUser || nxt.user.current;\r\n                    if (!scope.currentUser || scope.currentUser.loginName === \"guest\") {\r\n                        scope.logged = false;\r\n                    } else {\r\n                        var $sub = $e.find('.sub-nav');\r\n                        $sub.css({ top: $e.height() });\r\n                        element.hover(function () {\r\n                            $sub.stop().css('height', 'auto').slideDown(300);\r\n                        }, function () {\r\n                            $sub.stop().slideUp(300);\r\n                        });\r\n                    }\r\n                }\r\n\r\n                init();\r\n\r\n                scope.isCurrent = function (href) {\r\n                    return window.location.href.indexOf(href.replace(/\\~/g, '')) !== -1;\r\n                }\r\n            }\r\n\r\n        };\r\n    }]);\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/user-info/index.js\n// module id = 34\n// module chunks = 0", "/**\r\n * 表单验证插件，包含directive和service\r\n *\r\n * 注：千万不要将form标签放到ng-if等会创建scope的指令内，不然使用此插件会有意想不到的坑\r\n */\r\nangular.module('mam-ng')\r\n    .service('mamValidationService', function() {\r\n        //非空\r\n        this.nullValidate = function(value) {\r\n            if (value == undefined || value.toString().replace(/(^\\s*)|(\\s*$)/g, \"\").length == 0) {\r\n                return false;\r\n            } else {\r\n                return true;\r\n            }\r\n        };\r\n\r\n        //验证空格\r\n        this.blankValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            if (/(\\s+)/g.test(value)) {\r\n                return false;\r\n            } else {\r\n                return true;\r\n            }\r\n        };\r\n\r\n        //长度\r\n        this.lengthValidate = function(value, min, max) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            if (value.toString().length >= min && value.toString().length <= max) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        };\r\n\r\n        //integer大小\r\n        this.numValueValidate = function(value, min, max) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            if (!isNaN(value) && parseInt(value, 10) >= min && parseInt(value, 10) <= max) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        };\r\n\r\n        //密码验证\r\n        this.pswValidate = function(value, formName, confirmId, $scope) {\r\n            if (!$scope || !$scope[formName]) {\r\n                return true;\r\n            }\r\n            if (!value && !$scope[formName][confirmId]) {\r\n                return true;\r\n            }\r\n            if ($scope[formName][confirmId] != value) {\r\n                return false;\r\n            } else {\r\n                return true;\r\n            }\r\n        };\r\n\r\n        //是否数字\r\n        this.numValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            if (isNaN(value)) {\r\n                return false;\r\n            } else {\r\n                return true;\r\n            }\r\n        };\r\n\r\n        //是否是整数\r\n        this.integerValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            if (!/^-?\\d+$/.test(value)) {\r\n                return false;\r\n            }\r\n            return true;\r\n        };\r\n\r\n        //是否是正数\r\n        this.positiveNumberValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            if (isNaN(value) || value < 0) {\r\n                return false;\r\n            }\r\n            return true;\r\n        };\r\n\r\n        //日期格式\r\n        this.dateValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            var thePat = /^\\d{4}-(0?[1-9]|1[0-2])-(0?[1-9]|[1-2]\\d|3[0-1]) ([0-1]\\d|2[0-3]):[0-5]\\d:[0-5]\\d$/; //带时分秒\r\n            var theShortPat = /^\\d{4}-(0?[1-9]|1[0-2])-(0?[1-9]|[1-2]\\d|3[0-1])$/; //年月日\r\n            var theShortPat2 = /^\\d{4}-(0?[1-9]|1[0-2])$/; //年月\r\n            var theShortPat3 = /^\\d{4}$/; //年份\r\n            var theShortPat4 = /^([0-1]\\d|2[0-3]):[0-5]\\d$/; //时分\r\n            var theShortPat5 = /^([0-1]\\d|2[0-3]):[0-5]\\d:[0-5]\\d$/; //时分秒\r\n            if (thePat.test(value) || theShortPat.test(value) || theShortPat2.test(value) || theShortPat3.test(value) || theShortPat4.test(value) || theShortPat5.test(value)) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        };\r\n\r\n        //特殊字符验证\r\n        this.specialCharValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            var pattern = new RegExp(\"[`~!@%#$^&*()=|{}':;',\\\\[\\\\]<>/?\\\\.；：%……+￥（）【】‘”“'。，、？]\");\r\n            if (pattern.test(value)) {\r\n                return false;\r\n            } else {\r\n                return true;\r\n            }\r\n        };\r\n\r\n        //脚本验证\r\n        this.scriptCharValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            var pattern = new RegExp(\"<script(\\\\s.*)?>.*</script>\");\r\n            if (pattern.test(value)) {\r\n                return false;\r\n            } else {\r\n                return true;\r\n            }\r\n        };\r\n\r\n        //特殊字符验证\r\n        this.noChineseValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            if (value.match(/[^\\x00-\\xff]/ig)) {\r\n                return false;\r\n            } else {\r\n                return true;\r\n            }\r\n        };\r\n\r\n        //电话号码验证\r\n        this.phoneValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            var pat = /(^((([0-9]){3,4}[\\\\-])|([\\\\(]([0-9]){3,4}[\\\\)]))?[0-9]{4,8}$)|(^[0-9]{11}$)/;\r\n            if (pat.test(value)) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        };\r\n\r\n        //验证email\r\n        this.emailValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            var pat = /^([a-zA-Z0-9_\\\\.]{1,127}){1}[@]{1}([a-zA-Z0-9_\\\\.]{1,127}){1}$/;\r\n            if (pat.test(value)) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        };\r\n\r\n        //验证邮编\r\n        this.postcodesValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            var pat = /^[1-9][0-9]{5}$/;\r\n            if (pat.test(value)) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        };\r\n\r\n        //验证ip\r\n        this.ipValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            var pat = /^(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])$/;\r\n            if (pat.test(value)) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        };\r\n\r\n        this.mobileValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            var pat = /^[0-9]{11}$/;\r\n            if (pat.test(value)) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        };\r\n\r\n        //UNC地址验证\r\n        this.uncAddressValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            var pat = /^\\\\\\\\.*\\\\.*$/;\r\n            if (pat.test(value)) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        };\r\n\r\n        //FTP地址验证\r\n        this.ftpAddressValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            var pat = /^ftp:\\/\\//;\r\n            if (pat.test(value)) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        };\r\n\r\n        //HDD地址验证\r\n        this.hddAddressValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            var pat = /^[A-Z,a-z]:\\\\/;\r\n            if (pat.test(value)) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        };\r\n\r\n        //OSS地址验证\r\n        this.ossAddressValidate = function(value) {\r\n            if (value == undefined || value.toString().length == 0) {\r\n                return true;\r\n            }\r\n            var pat = /^oss:\\/\\//;\r\n            if (pat.test(value)) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        };\r\n    });\r\n\r\nangular.module('mam-ng')\r\n    .directive('mamValidationList', ['$rootScope', '$compile', '$timeout', '$q', 'mamValidationService', function($rootScope, $compile, $timeout, $q, mamValidationService) {\r\n        function getParentForm($element) {\r\n            if (!$element.parent()[0]) {\r\n                return undefined;\r\n            }\r\n            if ($element.parent()[0].tagName == \"HTML\") {\r\n                return undefined;\r\n            }\r\n            if ($element.parent()[0].tagName == \"FORM\") {\r\n                return $element.parent();\r\n            } else {\r\n                return getParentForm($element.parent());\r\n            }\r\n        }\r\n\r\n        //执行表单验证\r\n        function validateAll(scope, formName) {\r\n            var defer = $q.defer();\r\n            if (!scope || !scope.$mamValidationInfo) {\r\n                return;\r\n            }\r\n            scope.$mamValidationInfo.$hasValidateAll = true; //当执行全部表单验证方法后，$needValid的作用将取消\r\n            var formValidator = scope.$mamValidationInfo.forms[formName];\r\n            var elementValidator;\r\n            var ngModel;\r\n            for (var eleName in formValidator) {\r\n                elementValidator = formValidator[eleName];\r\n                ngModel = elementValidator.ngModel;\r\n                angular.forEach(ngModel.$formatters, function(formatter) {\r\n                    formatter(ngModel.$viewValue);\r\n                });\r\n            }\r\n            defer.resolve();\r\n            return defer.promise;\r\n        };\r\n\r\n        return {\r\n            restrict: \"A\",\r\n            require: \"ngModel\",\r\n            compile: function() {\r\n                return {\r\n                    pre: function preLink($scope, $element, $attrs, $ngModel) {},\r\n                    post: function postLink($scope, $element, $attrs, $ngModel) {\r\n                        var $needValid = false; //定义此值避免首次加载页面执行验证，比如非空验证会导致输入框出现红框不好看\r\n                        var form = getParentForm($element);\r\n                        var formName;\r\n                        if (form) {\r\n                            formName = form.attr(\"name\");\r\n\r\n                            var formValidator;\r\n                            var elementValidator;\r\n                            if (!$scope.$mamValidationInfo) {\r\n                                $scope.$mamValidationInfo = {};\r\n                            }\r\n                            if (!$scope.$mamValidationInfo.forms) {\r\n                                $scope.$mamValidationInfo.forms = {};\r\n                            }\r\n                            if (!$scope.$mamValidationInfo.forms[formName]) {\r\n                                $scope.$mamValidationInfo.forms[formName] = {};\r\n                            }\r\n                            if (!$scope.$mamValidationInfo.validateAll) {\r\n                                $scope.$mamValidationInfo.validateAll = function(formName) {\r\n                                    return validateAll($scope, formName);\r\n                                };\r\n                                mamValidationService.validateAll = function(formName) {\r\n                                    return validateAll($scope, formName);\r\n                                }\r\n                            }\r\n                            formValidator = $scope.$mamValidationInfo.forms[formName];\r\n                            if (!formValidator[$element.attr(\"name\")]) {\r\n                                formValidator[$element.attr(\"name\")] = {};\r\n                            }\r\n                            elementValidator = formValidator[$element.attr(\"name\")];\r\n                            elementValidator.element = $element;\r\n                            elementValidator.ngModel = $ngModel;\r\n                            if ($attrs.mamValidationList) {\r\n                                var vList = JSON.parse($attrs.mamValidationList.replace(/'/g, \"\\\"\"));\r\n                                elementValidator.validateList = vList;\r\n                            }\r\n\r\n                            var validateList = elementValidator.validateList; //验证列表\r\n                            //添加验证\r\n                            var cusValidate = function(value) {\r\n                                var validity = true;\r\n                                if (validateList && typeof(validateList) == \"object\" && validateList instanceof Array) {\r\n                                    var validate;\r\n                                    var fun;\r\n                                    var validResult;\r\n                                    for (var i = 0, j = validateList.length; i < j; i++) {\r\n                                        validate = validateList[i];\r\n                                        if (!$needValid && !$scope.$mamValidationInfo.$hasValidateAll) {\r\n                                            continue;\r\n                                        }\r\n                                        fun = mamValidationService[validate.name];\r\n                                        var param = [];\r\n                                        param.push(value);\r\n                                        if (validate.param && typeof(validate.param) == \"object\" && validate.param instanceof Array) {\r\n                                            for (var x = 0, y = validate.param.length; x < y; x++) {\r\n                                                param.push(validate.param[x]);\r\n                                            }\r\n                                        }\r\n                                        param.push($scope);\r\n\r\n                                        validResult = fun.apply(this, param);\r\n                                        if (typeof(validResult) == \"boolean\") //boolean\r\n                                        {\r\n                                            if (!validResult) {\r\n                                                validity = false;\r\n                                                $ngModel.$setValidity(validate.validity, false);\r\n                                            } else {\r\n                                                $ngModel.$setValidity(validate.validity, true);\r\n                                            }\r\n                                        } else if (typeof(validResult) == \"object\") //promise\r\n                                        {\r\n                                            validResult.then(function() {\r\n                                                $ngModel.$setValidity(validate.validity, true);\r\n                                            }, function() {\r\n                                                validity = false;\r\n                                                $ngModel.$setValidity(validate.validity, false);\r\n                                            });\r\n                                        }\r\n                                    }\r\n                                }\r\n                                return validity ? value : undefined;\r\n                            }\r\n                            $ngModel.$parsers.push(cusValidate);\r\n                            $ngModel.$formatters.push(cusValidate);\r\n                        }\r\n\r\n                        $element.on(\"blur keydown change\", function() {\r\n                            $needValid = true;\r\n                        });\r\n                    }\r\n                };\r\n            }\r\n        };\r\n    }]);\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/validation/index.js\n// module id = 35\n// module chunks = 0", "var app = angular.module('mam-ng');\r\n\r\napp.filter('trusted', ['$sce', function ($sce) {\r\n    return function (text) {\r\n        if (_.isString(text)) {\r\n            return $sce.trustAsHtml(text);\r\n        }\r\n        return text;\r\n    };\r\n}]);\r\n\r\napp.filter('entityTypeName', function () {\r\n    return function (code) {\r\n        if (_.isEmpty(code)) {\r\n            return '';\r\n        }\r\n        var type = _.find(mam.entity.types, { code: code });\r\n        if (type == null) {\r\n            if (code == 'hypermedia') {\r\n                return l('com.' + code, '图文');\r\n            }\r\n            if (code == 'folder') {\r\n                return l('com.' + code, '文件夹');\r\n            }\r\n            return code;\r\n        }\r\n        return l('com.' + code, type.name);\r\n    }\r\n});\r\n\r\napp.filter('entityTypeShortName', function () {\r\n    return function (code, ext) {\r\n        if (nxt.config.extTagEnable) {\r\n            if ((ext == null || ext == '')) {\r\n                if (!nxt.config.typeTagEnable)\r\n                    return '';\r\n            } else{\r\n                var ret = ext.replace('.', '').toUpperCase();\r\n                if (ret.indexOf('?') > -1)\r\n                {\r\n                    ret = ret.substring(0, ret.indexOf('?'));\r\n                }\r\n                return ret;\r\n            }\r\n        }\r\n\r\n        if (code == null || code == '' || !nxt.config.typeTagEnable) {\r\n            return '';\r\n        }\r\n        var type = _.find(mam.entity.types, { code: code });\r\n        if (type == null) {\r\n            type = _.find(mam.entity.types, { namespace: code });\r\n        }\r\n        if (type != null) {\r\n            if (type.shortCode != null && type.shortCode.length > 0) {\r\n                return type.shortCode;\r\n            }\r\n        }\r\n\r\n        switch (code.toLowerCase()) {\r\n            case 'video':\r\n            case 'biz_sobey_video':\r\n                return 'V';\r\n\r\n            case 'audio':\r\n            case 'biz_sobey_audio':\r\n                return 'A';\r\n\r\n            case 'picture':\r\n            case 'biz_sobey_picture':\r\n                return 'P';\r\n\r\n            case 'document':\r\n            case 'biz_sobey_document':\r\n                return 'D';\r\n\r\n            case 'hypermedia':\r\n            case 'biz_sobey_hypermedia':\r\n                return 'H';\r\n\r\n            case 'dataset':\r\n                return 'G';\r\n\r\n            case 'program':\r\n            case 'sequence':\r\n            case 'scene':\r\n            case 'short':\r\n                return 'C';\r\n\r\n            case 'rundown':\r\n            case 'biz_sobey_rundown':\r\n                return 'R';\r\n\r\n            case 'script':\r\n            case 'biz_sobey_script':\r\n                return 'S';\r\n\r\n            case 'other':\r\n            case 'biz_sobey_other':\r\n                return 'O';\r\n\r\n            default:\r\n                return code;\r\n        }\r\n    }\r\n});\r\n\r\napp.filter('entityTypeIcon', function () {\r\n    return function (code) {\r\n        var type = _.find(mam.entity.types, { code: code });\r\n        if (type != null && type.icon != null) {\r\n            return type.icon;\r\n        }\r\n        switch (code) {\r\n            case 'video':\r\n                return 'fa fa-film';\r\n            case 'audio':\r\n                return 'fa fa-music';\r\n            case 'picture':\r\n                return 'fa fa-picture-o';\r\n            case 'document':\r\n                return 'fa fa-file-word-o';\r\n            default:\r\n                return 'fa fa-file-o';\r\n        }\r\n    };\r\n});\r\n\r\napp.filter('removeHtml', function () {\r\n    return function (html) {\r\n        return mam.utils.removeHtmlTag(html);\r\n    };\r\n});\r\n\r\napp.filter('videoQualityFlag', function () {\r\n    return function (val) {\r\n        switch (val) {\r\n            case 1:\r\n                return 'flag-SD';\r\n            case 2:\r\n            case 3:\r\n                return 'flag-HD';\r\n            case 4:\r\n                return 'flag-4K'\r\n            default:\r\n                return '';\r\n        }\r\n    };\r\n});\r\n\r\napp.filter('cutString', ['$sce', function ($sce) {\r\n    return function (str, len) {\r\n        var sum = 0,\r\n            newStr = '';\r\n\r\n        if (typeof (str) != 'string') { return null };\r\n        if (!(/^[0-9]*[1-9][0-9]*$/).test(len) || len == 0) { return str; };\r\n\r\n        var matchList = str.split(/(<font\\s\\S*>(\\S+)<\\/font>)/igm);\r\n        // 过滤掉高亮标签\r\n        for (var i = 1; i < matchList.length; i += 3) {\r\n            str = str.replace(matchList[i], matchList[i + 1]);\r\n        }\r\n\r\n        //截取字符\r\n        for (var i = 0; i < str.length; i++) {\r\n            sum += str.charCodeAt(i) > 255 ? 2 : 1;\r\n            if ((sum <= len - 2) || ((i == str.length - 1) && (sum > len - 2))) {\r\n                newStr += str.charAt(i);\r\n            } else {\r\n                newStr += '...';\r\n                break;\r\n            }\r\n        }\r\n        // 还原高亮标签\r\n        for (var i = 1; i < matchList.length; i += 3) {\r\n            newStr = newStr.replace(matchList[i + 1], matchList[i]);\r\n        }\r\n        return newStr;\r\n    };\r\n}]);\r\n\r\napp.filter('formatExt', function () {\r\n    return function (ext) {\r\n        if (!ext) {\r\n            return '';\r\n        }\r\n        var res = ext.replace('.', '');\r\n        res = res.toUpperCase();\r\n        return res;\r\n    };\r\n});\r\n\r\napp.filter('formatSize', function () {\r\n    return function (size, custom) {\r\n        if (size == null) {\r\n            return '-';\r\n        }\r\n        var result = '';\r\n        if (custom) {\r\n            if (size == -1) {\r\n                return '无限制';\r\n            }\r\n            size = size / 1024 / 1024 / 1024;\r\n            if (size <= 0.01 && size > 0) {\r\n                return '0.01 GB';\r\n            } else {\r\n                return (size.toFixed(2)) + ' GB';\r\n            }\r\n        } else {\r\n            result = mam.utils.formatSize(size);\r\n        }\r\n        return result;\r\n    };\r\n});\r\n\r\napp.filter('formatDate', function () {\r\n    return function (time, format) {\r\n        if (time == null || time == '') {\r\n            return '';\r\n        }\r\n        var dateTime = new Date();\r\n        if (angular.isDate(time)) {\r\n            dateTime = time;\r\n        } else {\r\n            dateTime = new Date(time.replace(/-/g, '/')); //兼容IE、火狐\r\n        }\r\n\r\n        if (format == null || format == '') {\r\n            return dateTime.format('yyyy-MM-dd hh:mm:ss');\r\n        } else {\r\n            return dateTime.format(format);\r\n        }\r\n    };\r\n});\r\n\r\napp.filter('comingDateTime', function () {\r\n    return function (time) {\r\n        var result = '';\r\n        var oTime = time.replace(/\\s|\\-|\\/|\\:/g, \",\");\r\n        var timeArr = oTime.split(',');\r\n        var gotDateTime = {\r\n            y: timeArr[0],\r\n            m: timeArr[1] - 1,\r\n            d: timeArr[2],\r\n            h: timeArr[3],\r\n            mi: timeArr[4],\r\n            s: timeArr[5]\r\n        };\r\n        var futureDate = new Date(gotDateTime.y, gotDateTime.m, gotDateTime.d, gotDateTime.h, gotDateTime.mi, gotDateTime.s);\r\n        var nowDate = new Date();\r\n        var dsec = futureDate.getTime() - nowDate.getTime(); //毫秒差\r\n\r\n        var o = parseInt(dsec / 1000); //总秒数\r\n        if (o < 0) {\r\n            result = '0 秒后';\r\n        } else if (o >= 0 && o < 60) {\r\n            result = o + ' 秒后';\r\n        } else if (o >= 60 && o < 3600) {\r\n            result = parseInt(o / 60) + ' 分钟后';\r\n        } else if (o >= 3600 && o < 3600 * 24) {\r\n            result = parseInt(o / 3600) + ' 小时后';\r\n        } else if (o >= 3600 * 24 && o < 3600 * 24 * 30) {\r\n            result = parseInt(o / 3600 / 24) + ' 天后';\r\n        } else if (o >= 3600 * 24 * 30 && o < 3600 * 24 * 30 * 12) {\r\n            result = parseInt(o / 3600 / 24 / 30) + ' 个月后';\r\n        } else if (o >= 3600 * 24 * 30 * 12) {\r\n            result = parseInt(o / 3600 / 24 / 30 / 12) + ' 年后';\r\n        }\r\n\r\n        return result;\r\n    };\r\n});\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/filters/index.js\n// module id = 36\n// module chunks = 0", "window.nxt = window.nxt || {};\r\n\r\nnxt.user = nxt.user || {};\r\nnxt.permission = nxt.permission || {};\r\n\r\nnxt.permission.judge = function (permissions) {\r\n    if (permissions == null || permissions == '') {\r\n        return true;\r\n    }\r\n    if (_.get(nxt, 'user.current') == null) {\r\n        throw '判断前，必须给user.current赋值';\r\n    }\r\n    var user = nxt.user.current;\r\n    if (!_.isArray(user.appPermission) || user.appPermission.length == 0) {\r\n        return false;\r\n    }\r\n    var prefix = nxt.config.systemCode ? nxt.config.systemCode.toLowerCase() + '_' : '';\r\n\r\n    if (permissions.indexOf(',') == -1) {\r\n        return _.find(user.appPermission, function (item) { return item.toLowerCase() === (prefix + permissions.toLowerCase()) }) != null;\r\n    } else {\r\n        var items = permissions.split(',');\r\n        var error = 0;\r\n        for (var i = 0; i < items.length; i++) {\r\n            if (_.find(user.appPermission, function (ap) { return ap.toLowerCase() === (prefix + items[i].toLowerCase()) }) == null) {\r\n                error++;\r\n            }\r\n        }\r\n        return error != items.length; //满足一个都算通过\r\n    }\r\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/user/index.js\n// module id = 37\n// module chunks = 0", "window.mam.utils = window.mam.utils || {};\r\n\r\nmam.utils.comingDateTime = function (time, maxUnit, minTime, exactTo) {\r\n    if (!time) {\r\n        return '';\r\n    }\r\n    var result = '';\r\n    var oTime = time.replace(/\\s|\\-|\\/|\\:/g, \",\");\r\n    var timeArr = oTime.split(',');\r\n    var gotDateTime = {\r\n        y: timeArr[0],\r\n        m: timeArr[1] - 1,\r\n        d: timeArr[2],\r\n        h: timeArr[3],\r\n        mi: timeArr[4],\r\n        s: timeArr[5]\r\n    };\r\n    var futureDate = new Date(gotDateTime.y, gotDateTime.m, gotDateTime.d, gotDateTime.h, gotDateTime.mi, gotDateTime.s);\r\n    var nowDate = new Date();\r\n    var dsec = futureDate.getTime() - nowDate.getTime(); //毫秒差\r\n\r\n    var o = parseInt(dsec / 1000); //总秒数\r\n    if (minTime && o > minTime) {\r\n        return '';\r\n    }\r\n    if (o < 0) {\r\n        result = '';\r\n    } else if (o >= 0 && o < 60) {\r\n        result = o + ' 秒后';\r\n    } else if (o >= 60 && o < 3600) {\r\n        result = parseInt(o / 60) + '分钟后';\r\n    } else if ((o >= 3600 && o < 3600 * 24) || maxUnit == 'hour') {\r\n        result = parseInt(o / 3600) + '小时';\r\n        if(exactTo == 'min')\r\n        {\r\n            result += parseInt((o % 3600) / 60) + '分钟';\r\n        }\r\n        result+='后';\r\n    } else if (o >= 3600 * 24 && o < 3600 * 24 * 30) {\r\n        result = parseInt(o / 3600 / 24) + '天后';\r\n    } else if (o >= 3600 * 24 * 30 && o < 3600 * 24 * 30 * 12) {\r\n        result = parseInt(o / 3600 / 24 / 30) + '个月后';\r\n    } else if (o >= 3600 * 24 * 30 * 12) {\r\n        result = parseInt(o / 3600 / 24 / 30 / 12) + '年后';\r\n    }\r\n\r\n    return result;\r\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/utils/index.js\n// module id = 38\n// module chunks = 0", "exports = module.exports = require(\"../../../node_modules/css-loader/lib/css-base.js\")(undefined);\n// imports\n\n\n// module\nexports.push([module.id, \".mam-back-top{position:fixed;display:none;background-color:#fbfbfb;color:#7b7b7b;font-size:46px;bottom:20px;right:20px;overflow:hidden;width:46px;height:46px;text-align:center;line-height:46px;border:1px solid #e7e7e7;border-radius:3px;box-shadow:0 2px 3px rgba(0,0,0,.1);cursor:pointer;z-index:11;-webkit-transition:all .3s;transition:all .3s}.mam-back-top svg{width:24px;height:24px;fill:#b7b7b7;-webkit-transform:translateY(-5px);transform:translateY(-5px)}.mam-back-top:hover{background-color:#e98b11}.mam-back-top:hover svg{fill:#fff}\", \"\"]);\n\n// exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/css-loader!./~/postcss-loader/lib!./~/less-loader/dist/cjs.js!./src/directives/back-to-top/style.less\n// module id = 39\n// module chunks = 0", "exports = module.exports = require(\"../../../node_modules/css-loader/lib/css-base.js\")(undefined);\n// imports\n\n\n// module\nexports.push([module.id, \".mam-badge{background:red;color:#fff;border-radius:50%;text-align:center}\", \"\"]);\n\n// exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/css-loader!./~/postcss-loader/lib!./~/less-loader/dist/cjs.js!./src/directives/badge/style.less\n// module id = 40\n// module chunks = 0", "exports = module.exports = require(\"../../../node_modules/css-loader/lib/css-base.js\")(undefined);\n// imports\n\n\n// module\nexports.push([module.id, \".mam-captcha{cursor:pointer}\", \"\"]);\n\n// exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/css-loader!./~/postcss-loader/lib!./~/less-loader/dist/cjs.js!./src/directives/captcha/style.less\n// module id = 41\n// module chunks = 0", "exports = module.exports = require(\"../../../node_modules/css-loader/lib/css-base.js\")(undefined);\n// imports\n\n\n// module\nexports.push([module.id, \".mam-checkbox{position:relative;display:inline-block;min-width:22px;height:22px;line-height:21px;cursor:pointer;margin-bottom:0;font-weight:400}.mam-checkbox svg{left:3px;top:3px;width:14px;height:14px;fill:#e98b11;position:absolute;font-size:12px;-webkit-transition:opacity .2s;transition:opacity .2s}.mam-checkbox:before{content:\\\"\\\";left:0;top:0;position:absolute;border-radius:4px;border:1px solid #ccc;width:20px;height:20px;-webkit-transition:all .3s;transition:all .3s;background:#fff;-moz-box-sizing:border-box;box-sizing:border-box}.mam-checkbox.checked:before{border-color:#e98b11}.mam-checkbox.checked svg{opacity:1}.mam-checkbox.unchecked svg{opacity:0}.mam-checkbox.focus:before,.mam-checkbox:hover:before{border-color:#e98b11;box-shadow:0 0 6px rgba(233,139,17,.5)}.mam-checkbox.checked[disabled],.mam-checkbox[disabled]{cursor:not-allowed}.mam-checkbox.checked[disabled]:before,.mam-checkbox[disabled]:before{background:#eaeaea;border-color:#dcdcdc}.mam-checkbox.checked[disabled]:hover:before,.mam-checkbox[disabled]:hover:before{border-color:#dcdcdc;box-shadow:none}.mam-checkbox.checked[disabled].checked svg,.mam-checkbox[disabled].checked svg{fill:#666}.mam-checkbox span{margin-left:26px}.mam-checkbox input[type=checkbox]{position:absolute;clip:rect(0,0,0,0);pointer-events:none}.mam-checkbox.sm{min-width:18px;height:18px;line-height:17px}.mam-checkbox.sm:before{width:18px;height:18px}.mam-checkbox.sm svg{width:12px;height:12px}\", \"\"]);\n\n// exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/css-loader!./~/postcss-loader/lib!./~/less-loader/dist/cjs.js!./src/directives/checkbox/style.less\n// module id = 42\n// module chunks = 0", "exports = module.exports = require(\"../../../node_modules/css-loader/lib/css-base.js\")(undefined);\n// imports\n\n\n// module\nexports.push([module.id, \".mam-keyframe{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center}.mam-keyframe img{max-width:100%;max-height:100%}.mam-keyframe-loaded.mam-keyframe-type-video{background-color:#000}.mam-keyframe-default.mam-keyframe-loaded.mam-keyframe-type-video{background-color:transparent}\", \"\"]);\n\n// exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/css-loader!./~/postcss-loader/lib!./~/less-loader/dist/cjs.js!./src/directives/keyframe/style.less\n// module id = 43\n// module chunks = 0", "exports = module.exports = require(\"../../../node_modules/css-loader/lib/css-base.js\")(undefined);\n// imports\n\n\n// module\nexports.push([module.id, \".app-logo{font-family:Microsoft YaHei,Hiragino Sans GB;margin-right:0;margin-left:0;float:left;min-width:230px;-ms-flex-align:center}.app-logo,.app-logo .app-logo-img{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.app-logo .app-logo-img{-webkit-box-flex:1;-webkit-flex:1 0 auto;-ms-flex:1 0 auto;flex:1 0 auto;-ms-flex-align:center;height:100%}.app-logo .app-logo-img img{margin:0 auto;max-height:100%;max-width:100%}.app-logo .app-logo-txt .logo-title{min-width:125px;font-size:23px}.app-logo .app-logo-txt .logo-subtitle{min-width:125px;font-size:12px}\", \"\"]);\n\n// exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/css-loader!./~/postcss-loader/lib!./~/less-loader/dist/cjs.js!./src/directives/logo/style.less\n// module id = 44\n// module chunks = 0", "exports = module.exports = require(\"../../../node_modules/css-loader/lib/css-base.js\")(undefined);\n// imports\n\n\n// module\nexports.push([module.id, \".mam-pager{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center}.mam-pager .page-total{margin-right:5px}.mam-pager .page-total span{margin:0 4px;color:#e98b11}.mam-pager .pagination{margin-left:8px}.mam-pager .pagination li.active a{color:#e98b11;font-weight:700}.mam-pager .pagination li.active a:hover{background-color:transparent}.mam-pager .pagination li.disabled a:hover{color:#484848}.mam-pager .pagination a{background-color:transparent;color:#484848;border:none}.mam-pager .pagination a:hover{color:#e98b11;background-color:transparent}\", \"\"]);\n\n// exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/css-loader!./~/postcss-loader/lib!./~/less-loader/dist/cjs.js!./src/directives/pager/style.less\n// module id = 45\n// module chunks = 0", "exports = module.exports = require(\"../../../node_modules/css-loader/lib/css-base.js\")(undefined);\n// imports\n\n\n// module\nexports.push([module.id, \".mam-radio{position:relative;display:inline-block;min-width:22px;height:22px;line-height:21px;cursor:pointer;margin-bottom:0}.mam-radio svg{left:6px;top:7px;width:6px;height:6px;fill:#e98b11;position:absolute;opacity:0;-webkit-transition:opacity .2s;transition:opacity .2s}.mam-radio:before{content:\\\"\\\";left:0;top:1px;position:absolute;border:1px solid #ccc;width:18px;height:18px;border-radius:9px;-webkit-transition:all .3s;transition:all .3s;-moz-box-sizing:border-box;box-sizing:border-box}.mam-radio.checked:before{border-color:#e98b11}.mam-radio.checked svg{opacity:1}.mam-radio.unchecked svg{opacity:0}.mam-radio.focus:before,.mam-radio:hover:before{border-color:#e98b11;box-shadow:0 0 6px rgba(233,139,17,.5)}.mam-radio.checked[disabled],.mam-radio[disabled]{cursor:not-allowed}.mam-radio.checked[disabled]:before,.mam-radio[disabled]:before{background:#eaeaea;border-color:#dcdcdc}.mam-radio.checked[disabled]:hover:before,.mam-radio[disabled]:hover:before{border-color:#dcdcdc;box-shadow:none}.mam-radio.checked[disabled].checked svg,.mam-radio[disabled].checked svg{fill:#acacac}.mam-radio span{margin-left:26px}.mam-radio input[type=radio]{position:absolute;clip:rect(0,0,0,0);pointer-events:none}\", \"\"]);\n\n// exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/css-loader!./~/postcss-loader/lib!./~/less-loader/dist/cjs.js!./src/directives/radio/style.less\n// module id = 46\n// module chunks = 0", "exports = module.exports = require(\"../../../node_modules/css-loader/lib/css-base.js\")(undefined);\n// imports\n\n\n// module\nexports.push([module.id, \".mam-search-input{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-webkit-flex:1 0 auto;-ms-flex:1 0 auto;flex:1 0 auto;max-width:800px;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;position:relative}.mam-search-input input{-webkit-box-flex:1;-webkit-flex:1 0 auto;-ms-flex:1 0 auto;flex:1 0 auto;border:1px solid #e1e3e2;height:38px;line-height:38px;padding:0 12px;color:#8f8f8f;font-size:14px;outline:none;-webkit-transition:all .3s;transition:all .3s;z-index:101}.mam-search-input input:focus{border-color:#e98b11}.mam-search-input .mam-history-box{width:100%;height:200px;position:absolute;left:0;top:38px;z-index:100;background-color:#fff;border-radius:0 0 5px 5px;box-shadow:0 0 1px 1px rgba(0,0,0,.12)}.mam-search-input .mam-history-box .history-box{height:100%;overflow-y:scroll;border-bottom:1px solid #dcdcdc}.mam-search-input .mam-history-box .history-items{height:100%;margin:0;padding-left:0}.mam-search-input .mam-history-box .history-items li{padding:10px 15px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between;cursor:pointer}.mam-search-input .mam-history-box .history-items li .item-delete{line-height:20px}.mam-search-input .mam-history-box .history-items .list-item-active,.mam-search-input .mam-history-box .history-items .list-item:hover{background-color:#e98b11}.mam-search-input .mam-history-box .history-items .list-item-active span,.mam-search-input .mam-history-box .history-items .list-item:hover span{color:#fff}\", \"\"]);\n\n// exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/css-loader!./~/postcss-loader/lib!./~/less-loader/dist/cjs.js!./src/directives/search-input/style.less\n// module id = 47\n// module chunks = 0", "exports = module.exports = require(\"../../../node_modules/css-loader/lib/css-base.js\")(undefined);\n// imports\n\n\n// module\nexports.push([module.id, \".mam-sort-dropdown button{border-style:none;background-color:transparent}.mam-sort-dropdown button:hover{background-color:#f7f7f7}.mam-sort-dropdown button svg{fill:#e98b11}.mam-sort-dropdown svg{width:12px;height:13px;-webkit-transform:translateY(2px);transform:translateY(2px)}.mam-sort-dropdown svg.active{-webkit-transform:rotate(180deg);transform:rotate(180deg)}\", \"\"]);\n\n// exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/css-loader!./~/postcss-loader/lib!./~/less-loader/dist/cjs.js!./src/directives/sort-group/style.less\n// module id = 48\n// module chunks = 0", "exports = module.exports = require(\"../../../node_modules/css-loader/lib/css-base.js\")(undefined);\n// imports\n\n\n// module\nexports.push([module.id, \".mam-spin{position:absolute;left:0;top:0;width:100%;height:100%;background:#fff;opacity:.7;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center}.mam-spin .mam-spin-animation>div{width:12px;height:12px;margin:0 1px;background-color:#5da2f0;border-radius:100%;display:inline-block;-webkit-animation:bouncedelay 1.4s infinite ease-in-out;animation:bouncedelay 1.4s infinite ease-in-out;-webkit-animation-fill-mode:both;animation-fill-mode:both}.mam-spin .mam-spin-animation .bounce1{-webkit-animation-delay:-.32s;animation-delay:-.32s}.mam-spin .mam-spin-animation .bounce2{-webkit-animation-delay:-.16s;animation-delay:-.16s}@-webkit-keyframes bouncedelay{0%,80%,to{-webkit-transform:scale(.4);transform:scale(.4)}40%{-webkit-transform:scale(1);transform:scale(1)}}@keyframes bouncedelay{0%,80%,to{-webkit-transform:scale(.4);transform:scale(.4)}40%{-webkit-transform:scale(1);transform:scale(1)}}.mam-spin .mam-spin-text{font-size:12px}\", \"\"]);\n\n// exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/css-loader!./~/postcss-loader/lib!./~/less-loader/dist/cjs.js!./src/directives/spin/style.less\n// module id = 49\n// module chunks = 0", "exports = module.exports = require(\"../../../node_modules/css-loader/lib/css-base.js\")(undefined);\n// imports\n\n\n// module\nexports.push([module.id, \".mam-switch-button{display:inline-block}.mam-switch-button[disabled=disabled] .mam-switch-button-normal{opacity:.5;cursor:default}.mam-switch-button-normal{width:52px;height:32px;background:#f0f0f0;border-radius:16px;border:1px solid #d4d4d4;position:relative;cursor:pointer;-webkit-transition:background .3s;transition:background .3s}.mam-switch-button-normal:before{content:\\\"\\\";display:block;width:30px;height:30px;border-radius:50%;background:#ddd;border:1px solid #c2c2c2;position:absolute;-webkit-transition:left .3s;transition:left .3s;left:0}.mam-switch-button-on{background:#337ab7}.mam-switch-button-on:before{left:20px}\", \"\"]);\n\n// exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/css-loader!./~/postcss-loader/lib!./~/less-loader/dist/cjs.js!./src/directives/switch-button/style.less\n// module id = 50\n// module chunks = 0", "exports = module.exports = require(\"../../../node_modules/css-loader/lib/css-base.js\")(undefined);\n// imports\n\n\n// module\nexports.push([module.id, \".mam-top-nav{-ms-flex-align:center;-ms-flex-pack:end;-webkit-flex:1 1 auto;-ms-flex:1 1 auto;flex:1 1 auto}.mam-top-nav,.mam-top-nav .navs{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:end;-webkit-justify-content:flex-end;justify-content:flex-end;-webkit-box-flex:1}.mam-top-nav .navs{-ms-flex-pack:end;-ms-flex-align:center;-webkit-flex:1 0 auto;-ms-flex:1 0 auto;flex:1 0 auto;height:100%}.mam-top-nav .more{position:relative;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:end;-webkit-justify-content:flex-end;-ms-flex-pack:end;justify-content:flex-end;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;height:100%}.mam-top-nav .more .subs{position:absolute;display:none;height:auto;right:0;z-index:1000;background:#fff;border:1px solid #ccc;box-shadow:0 0 4px rgba(0,0,0,.2)}.mam-top-nav .item,.mam-top-nav .item a{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;height:100%}.mam-top-nav .item a{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center}.mam-top-nav .more-item{width:110px;border-bottom:1px solid #ccc;height:auto;background:#fff}.mam-top-nav .more-item:last-child{border-bottom:none}.mam-top-nav .more-item a{display:block;text-align:center;padding:10px 0}.mam-top-nav .more-item i{width:20px;text-align:center;margin-right:4px}.mam-top-nav .more-item.nav-separator{display:none}\", \"\"]);\n\n// exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/css-loader!./~/postcss-loader/lib!./~/less-loader/dist/cjs.js!./src/directives/top-nav/style.less\n// module id = 51\n// module chunks = 0", "exports = module.exports = require(\"../../../node_modules/css-loader/lib/css-base.js\")(undefined);\n// imports\n\n\n// module\nexports.push([module.id, \".mam-user-avatar img{width:40px;height:40px}\", \"\"]);\n\n// exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/css-loader!./~/postcss-loader/lib!./~/less-loader/dist/cjs.js!./src/directives/user-avatar/style.less\n// module id = 52\n// module chunks = 0", "exports = module.exports = require(\"../../../node_modules/css-loader/lib/css-base.js\")(undefined);\n// imports\n\n\n// module\nexports.push([module.id, \".mam-top-nav-user{position:relative;cursor:pointer;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;height:100%}.mam-top-nav-user:hover .fa-angle-down{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.mam-top-nav-user a.user-info{padding-left:18px;padding-right:9px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;text-align:center}.mam-top-nav-user a.user-info img{width:40px;height:40px;border-radius:50%}.mam-top-nav-user a.user-info span{margin:0 8px;display:block}.mam-top-nav-user a.user-info .fa-angle-down{-webkit-transition:all .3s;transition:all .3s}.mam-top-nav-user a.user-info:hover{background-color:transparent}.mam-top-nav-user .sub-nav{position:absolute;right:0;width:140px;padding:4px 0;z-index:1000;background:#fff;border:1px solid #ccc;box-shadow:0 0 4px rgba(0,0,0,.2);display:none}.mam-top-nav-user .sub-nav .fa{margin-right:10px}.mam-top-nav-user .sub-nav a{display:block;height:36px;line-height:36px;padding:0 0 0 20px}.mam-top-nav-user-nologin{width:auto;height:100%;padding:0 20px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center}.mam-top-nav-user-nologin a{margin:0 8px}.mam-top-nav-user-nologin a i{display:none}\", \"\"]);\n\n// exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/css-loader!./~/postcss-loader/lib!./~/less-loader/dist/cjs.js!./src/directives/user-info/style.less\n// module id = 53\n// module chunks = 0", "module.exports = \"<div class=app-logo> <div class=app-logo-img ng-class=\\\"{'app-default-logo':(config.customLogo.logo==null||config.customLogo.logo=='')&& (config.globalLogo==''||config.globalLogo==null)}\\\"> <img ng-if=\\\"(config.customLogo.logo!=null&& config.customLogo.logo!='') || config.globalLogo!=''\\\" ng-src=\\\"{{config.customLogo.logo!=null && config.customLogo.logo!=''?config.customLogo.logo:config.globalLogo}}\\\"/> </div> <div class=app-logo-txt> <div class=logo-title ng-if=config.customLogo.mainTitleEnable style=\\\"\\\" ng-bind=config.customLogo.mainTitle></div> <div class=logo-subtitle ng-if=config.customLogo.subTitleEnable style=\\\"\\\" ng-bind=config.customLogo.subTitle></div> </div> </div> \";\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/logo/template.html\n// module id = 54\n// module chunks = 0", "module.exports = \"<div class=mam-pager ng-class=\\\"simple?'mam-pager-simple':'mam-pager-full'\\\"> <div class=page-info> <span ng-show=\\\"showText || pageTotal==1\\\" class=page-total>{{textInfo.total}}<span>{{recordTotal}}</span>{{textInfo.record}}</span> <span ng-show=\\\"showText && pageTotal>1\\\" class=page-code>{{textInfo.index}}<span>{{pageIndex}}</span>/<span>{{pageTotal}}</span></span> </div> <ul uib-pagination total-items=recordTotal ng-model=pageIndex items-per-page=pageSize boundary-links=true rotate=true num-pages=pageTotal previous-text={{textInfo.prev}} next-text={{textInfo.next}} first-text={{textInfo.first}} last-text={{textInfo.last}} ng-change=change(pageIndex) boundary-link-numbers=true max-size=maxSize></ul> </div>\";\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/pager/template.html\n// module id = 55\n// module chunks = 0", "module.exports = \"<div class=mam-search-input> <input type=text ng-change=sh.getBy() ng-click=\\\"sh.showBox($event,'click')\\\" ng-model=keywords placeholder={{text.placeholder}}> <div class=mam-history-box id=mam-history-box style=display:none> <div class=history-box> <ul class=history-items> <li class=list-item ng-class=\\\"{'list-item-active':item.keyword==selected}\\\" ng-repeat=\\\"item in sh.items\\\" ng-mouseover=changeModel(item.keyword) ng-click=submit(item.keyword) ng-mouseleave=changeModel()> <span class=history-word>{{item.keyword}}</span> <span class=\\\"item-delete fa fa-trash\\\" ng-if=\\\"item.type==0\\\" ng-click=sh.delete(item.id,$event)></span> </li> </ul> </div> </div> </div>\";\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/search-input/template.html\n// module id = 56\n// module chunks = 0", "module.exports = \"<div class=\\\"mam-sort-dropdown btn-group\\\" mam-dropdown> <button type=button class=\\\"btn btn-default dropdown-toggle\\\"> <span>{{ngModel.current.text}}</span> </button> <ul class=dropdown-menu> <li ng-repeat=\\\"item in ngModel.items\\\"> <a ng-click=change(item)> <span>{{item.text}}</span> </a> </li> </ul> </div>\";\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/sort-group/template.html\n// module id = 57\n// module chunks = 0", "module.exports = \"<div class=mam-spin ng-if=show> <div class=mam-spin-animation> <div class=bounce1></div> <div class=bounce2></div> <div class=bounce3></div> </div> <span class=mam-spin-text ng-if=\\\"$parent.text != null && $parent.text.length > 0\\\">加载中</span> </div>\";\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/spin/template.html\n// module id = 58\n// module chunks = 0", "module.exports = \"<div class=mam-switch-button> <div class=mam-switch-button-normal ng-class=\\\"{true : 'mam-switch-button-on',false : ''}[val]\\\" ng-click=changeEnable()></div> </div>\";\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/switch-button/template.html\n// module id = 59\n// module chunks = 0", "module.exports = \"<div class=mam-top-nav> <div class=navs ng-show=logged></div> <div class=more ng-show=logged> <div class=item> <a> <i class=\\\"fa fa-ellipsis-h\\\" aria-hidden=true></i> <span>更多</span> </a> </div> <div class=subs></div> </div> </div>\";\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/top-nav/template.html\n// module id = 60\n// module chunks = 0", "module.exports = \"<div class=mam-top-nav-user ng-show=logged> <a class=user-info> <mam-user-avatar src=currentUser.avatarUrl></mam-user-avatar> <div> <span class=nickName>{{currentUser.nickName}}</span> <span ng-if=currentUser.haveOrganization>({{currentUser.organization.organizationName}})</span> </div> <i class=\\\"fa fa-angle-down\\\"></i> </a> <div class=sub-nav> <div ng-if=\\\"scoreEnable && currentUser.loginName !== 'guest'\\\" class=score> <a mam-href=~/user/#/center/score/ ><i class=\\\"fa fa-database\\\"></i>积分：{{currentUser.additional.score}}</a> </div> <div ng-show=deskShow.link ng-class=\\\"{'current':isCurrent('/desk/#/main/favorite/')}\\\" ng-if=\\\"currentUser.loginName !== 'guest'\\\"> <a mam-href=~/desk/#/main/favorite/ ><i class=\\\"fa fa-desktop\\\"></i>工作台</a> </div> <div ng-class=\\\"{'current':isCurrent('/desk/#/main/message/')}\\\" ng-if=\\\"currentUser.loginName !== 'guest'\\\"> <a href=\\\"/desk/#/main/message/?page=1\\\"><i class=\\\"fa fa-envelope-o\\\"></i>我的消息</a> </div> <div ng-class=\\\"{'current':isCurrent('/user/#/center/info')}\\\" ng-if=\\\"currentUser.loginName !== 'guest'\\\"> <a mam-href=~/user/#/center/info><i class=\\\"fa fa-user\\\"></i>个人中心</a> </div> <div ng-if=\\\"currentUser.isAdmin && currentUser.loginName !== 'guest'\\\" class=top-manage ng-class=\\\"{'current':iscurrent('/manage/')}\\\"> <a href=/manage/ ><i class=\\\"fa fa-cog\\\"></i>管理中心</a> </div> <div ng-if=\\\"currentUser.isSystemAdmin && currentUser.loginName !== 'guest'\\\" class=top-system ng-class=\\\"{'current':iscurrent('/sysmanage/')}\\\"> <a href=/sysmanage/ target=_blank><i class=\\\"fa fa-cog\\\"></i>系统配置</a> </div> <div> <a mam-href=~/user/exit><i class=\\\"fa fa-power-off\\\"></i>退出</a> </div> </div> </div> <div class=mam-top-nav-user-nologin ng-show=!logged> <a mam-href=~/signup/ ><i class=\\\"fa fa-user-plus\\\"></i>注册</a> <a mam-href=~/login><i class=\\\"fa fa-sign-in\\\"></i>登录</a> </div>\";\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/user-info/template.html\n// module id = 61\n// module chunks = 0", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\nif(typeof content === 'string') content = [[module.id, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = require(\"!../../../node_modules/style-loader/lib/addStyles.js\")(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(module.hot) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/back-to-top/style.less\n// module id = 62\n// module chunks = 0", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\nif(typeof content === 'string') content = [[module.id, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = require(\"!../../../node_modules/style-loader/lib/addStyles.js\")(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(module.hot) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/badge/style.less\n// module id = 63\n// module chunks = 0", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\nif(typeof content === 'string') content = [[module.id, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = require(\"!../../../node_modules/style-loader/lib/addStyles.js\")(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(module.hot) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/captcha/style.less\n// module id = 64\n// module chunks = 0", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\nif(typeof content === 'string') content = [[module.id, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = require(\"!../../../node_modules/style-loader/lib/addStyles.js\")(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(module.hot) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/checkbox/style.less\n// module id = 65\n// module chunks = 0", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\nif(typeof content === 'string') content = [[module.id, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = require(\"!../../../node_modules/style-loader/lib/addStyles.js\")(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(module.hot) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/keyframe/style.less\n// module id = 66\n// module chunks = 0", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\nif(typeof content === 'string') content = [[module.id, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = require(\"!../../../node_modules/style-loader/lib/addStyles.js\")(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(module.hot) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/logo/style.less\n// module id = 67\n// module chunks = 0", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\nif(typeof content === 'string') content = [[module.id, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = require(\"!../../../node_modules/style-loader/lib/addStyles.js\")(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(module.hot) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/pager/style.less\n// module id = 68\n// module chunks = 0", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\nif(typeof content === 'string') content = [[module.id, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = require(\"!../../../node_modules/style-loader/lib/addStyles.js\")(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(module.hot) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/radio/style.less\n// module id = 69\n// module chunks = 0", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\nif(typeof content === 'string') content = [[module.id, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = require(\"!../../../node_modules/style-loader/lib/addStyles.js\")(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(module.hot) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/search-input/style.less\n// module id = 70\n// module chunks = 0", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\nif(typeof content === 'string') content = [[module.id, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = require(\"!../../../node_modules/style-loader/lib/addStyles.js\")(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(module.hot) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/sort-group/style.less\n// module id = 71\n// module chunks = 0", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\nif(typeof content === 'string') content = [[module.id, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = require(\"!../../../node_modules/style-loader/lib/addStyles.js\")(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(module.hot) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/spin/style.less\n// module id = 72\n// module chunks = 0", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\nif(typeof content === 'string') content = [[module.id, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = require(\"!../../../node_modules/style-loader/lib/addStyles.js\")(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(module.hot) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/switch-button/style.less\n// module id = 73\n// module chunks = 0", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\nif(typeof content === 'string') content = [[module.id, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = require(\"!../../../node_modules/style-loader/lib/addStyles.js\")(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(module.hot) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/top-nav/style.less\n// module id = 74\n// module chunks = 0", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\nif(typeof content === 'string') content = [[module.id, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = require(\"!../../../node_modules/style-loader/lib/addStyles.js\")(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(module.hot) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/user-avatar/style.less\n// module id = 75\n// module chunks = 0", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\nif(typeof content === 'string') content = [[module.id, content, '']];\n// Prepare cssTransformation\nvar transform;\n\nvar options = {}\noptions.transform = transform\n// add the styles to the DOM\nvar update = require(\"!../../../node_modules/style-loader/lib/addStyles.js\")(content, options);\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(module.hot) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\", function() {\n\t\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/postcss-loader/lib/index.js!../../../node_modules/less-loader/dist/cjs.js!./style.less\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/user-info/style.less\n// module id = 76\n// module chunks = 0", "\n/**\n * When source maps are enabled, `style-loader` uses a link element with a data-uri to\n * embed the css on the page. This breaks all relative urls because now they are relative to a\n * bundle instead of the current page.\n *\n * One solution is to only use full urls, but that may be impossible.\n *\n * Instead, this function \"fixes\" the relative urls to be absolute according to the current page location.\n *\n * A rudimentary test suite is located at `test/fixUrls.js` and can be run via the `npm test` command.\n *\n */\n\nmodule.exports = function (css) {\n  // get current location\n  var location = typeof window !== \"undefined\" && window.location;\n\n  if (!location) {\n    throw new Error(\"fixUrls requires window.location\");\n  }\n\n\t// blank or null?\n\tif (!css || typeof css !== \"string\") {\n\t  return css;\n  }\n\n  var baseUrl = location.protocol + \"//\" + location.host;\n  var currentDir = baseUrl + location.pathname.replace(/\\/[^\\/]*$/, \"/\");\n\n\t// convert each url(...)\n\t/*\n\tThis regular expression is just a way to recursively match brackets within\n\ta string.\n\n\t /url\\s*\\(  = Match on the word \"url\" with any whitespace after it and then a parens\n\t   (  = Start a capturing group\n\t     (?:  = Start a non-capturing group\n\t         [^)(]  = Match anything that isn't a parentheses\n\t         |  = OR\n\t         \\(  = Match a start parentheses\n\t             (?:  = Start another non-capturing groups\n\t                 [^)(]+  = Match anything that isn't a parentheses\n\t                 |  = OR\n\t                 \\(  = Match a start parentheses\n\t                     [^)(]*  = Match anything that isn't a parentheses\n\t                 \\)  = Match a end parentheses\n\t             )  = End Group\n              *\\) = Match anything and then a close parens\n          )  = Close non-capturing group\n          *  = Match anything\n       )  = Close capturing group\n\t \\)  = Match a close parens\n\n\t /gi  = Get all matches, not the first.  Be case insensitive.\n\t */\n\tvar fixedCss = css.replace(/url\\s*\\(((?:[^)(]|\\((?:[^)(]+|\\([^)(]*\\))*\\))*)\\)/gi, function(fullMatch, origUrl) {\n\t\t// strip quotes (if they exist)\n\t\tvar unquotedOrigUrl = origUrl\n\t\t\t.trim()\n\t\t\t.replace(/^\"(.*)\"$/, function(o, $1){ return $1; })\n\t\t\t.replace(/^'(.*)'$/, function(o, $1){ return $1; });\n\n\t\t// already a full url? no change\n\t\tif (/^(#|data:|http:\\/\\/|https:\\/\\/|file:\\/\\/\\/)/i.test(unquotedOrigUrl)) {\n\t\t  return fullMatch;\n\t\t}\n\n\t\t// convert the url to a full url\n\t\tvar newUrl;\n\n\t\tif (unquotedOrigUrl.indexOf(\"//\") === 0) {\n\t\t  \t//TODO: should we add protocol?\n\t\t\tnewUrl = unquotedOrigUrl;\n\t\t} else if (unquotedOrigUrl.indexOf(\"/\") === 0) {\n\t\t\t// path should be relative to the base url\n\t\t\tnewUrl = baseUrl + unquotedOrigUrl; // already starts with '/'\n\t\t} else {\n\t\t\t// path should be relative to current directory\n\t\t\tnewUrl = currentDir + unquotedOrigUrl.replace(/^\\.\\//, \"\"); // Strip leading './'\n\t\t}\n\n\t\t// send back the fixed url(...)\n\t\treturn \"url(\" + JSON.stringify(newUrl) + \")\";\n\t});\n\n\t// send back the fixed css\n\treturn fixedCss;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/style-loader/lib/urls.js\n// module id = 77\n// module chunks = 0", "module.exports = \"<svg t=\\\"1498805495948\\\" class=\\\"icon\\\" style viewBox=\\\"0 0 1024 1024\\\" version=\\\"1.1\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" p-id=\\\"3582\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\"><defs><style type=\\\"text/css\\\"></style></defs><path d=\\\"M1009.762845 56.889457 14.237155 56.889457C6.386724 56.889457 0.01536 50.546537 0.01536 42.667662L0.01536 14.224071C0.01536 6.37364 6.386724 0.002276 14.237155 0.002276L1009.762845 0.002276C1017.613276 0.002276 1023.98464 6.37364 1023.98464 14.224071L1023.98464 42.667662C1023.98464 50.546537 1017.613276 56.889457 1009.762845 56.889457ZM473.800257 197.087918C476.67306 194.215116 480.00096 192.167177 483.556409 190.773441L483.556409 184.885617C483.556409 177.035187 489.927773 170.663822 497.778205 170.663822L526.221795 170.663822C534.072227 170.663822 540.443591 177.035187 540.443591 184.885617L540.443591 190.773441C543.99904 192.167177 547.32694 194.215116 550.199743 197.087918L997.560544 644.44872C1008.653544 655.570164 1008.653544 673.574957 997.560544 684.696401 986.4391 695.789402 968.434307 695.789402 957.341307 684.696401L540.443591 267.798686 540.443591 1009.749761C540.443591 1017.600192 534.072227 1023.971557 526.221795 1023.971557L497.778205 1023.971557C489.927773 1023.971557 483.556409 1017.600192 483.556409 1009.749761L483.556409 267.798686 66.658693 684.696401C55.565693 695.789402 37.532456 695.789402 26.439456 684.696401 15.318011 673.574957 15.318011 655.570164 26.439456 644.44872L473.800257 197.087918Z\\\" p-id=\\\"3583\\\"></path></svg>\"\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/back-to-top/icon.svg\n// module id = 78\n// module chunks = 0", "module.exports = \"<svg t=\\\"1498706378122\\\" class=\\\"icon\\\" style viewBox=\\\"0 0 1024 1024\\\" version=\\\"1.1\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" p-id=\\\"3878\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\"><defs><style type=\\\"text/css\\\"></style></defs><path d=\\\"M505.344 852.928l-434.944-315.392c-17.888-12.96-21.856-37.984-8.896-55.872 12.928-17.888 37.984-21.888 55.872-8.896l367.296 266.368 409.376-619.392c12.192-18.464 36.992-23.456 55.424-11.328 18.432 12.192 23.488 36.992 11.328 55.424L505.344 852.928z\\\" p-id=\\\"3879\\\"></path></svg>\"\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/checkbox/icon.svg\n// module id = 79\n// module chunks = 0", "module.exports = \"<svg t=\\\"1498738186413\\\" class=\\\"icon\\\" style viewBox=\\\"0 0 1024 1024\\\" version=\\\"1.1\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" p-id=\\\"2536\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\"><defs><style type=\\\"text/css\\\"></style></defs><path d=\\\"M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z\\\" fill p-id=\\\"2537\\\"></path></svg>\"\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/directives/radio/icon.svg\n// module id = 80\n// module chunks = 0", "if (window.mam == null) {\r\n    throw '该组件依赖 mam-base';\r\n}\r\nwindow.mam.ng = window.mam.ng || {};\r\n\r\nvar app = angular.module('mam-ng', ['ui.router']);\r\napp.run([\r\n    '$http',\r\n    function ($http) {\r\n        mam.ng.$http = $http;\r\n    }\r\n]);\r\n\r\nrequire('./core/config');\r\nrequire('./core/router');\r\nrequire('./core/http-provider');\r\nrequire('./core/handle-url');\r\nrequire('./core/module.js');\r\n\r\nrequire('./user/index');\r\n\r\n\r\nrequire('./filters/index');\r\nrequire('./utils/index');\r\n\r\nrequire('./directives/all-checkbox/index');\r\nrequire('./directives/back-to-top/index');\r\nrequire('./directives/checkbox/index');\r\nrequire('./directives/dropdown/index');\r\nrequire('./directives/href/index');\r\nrequire('./directives/image/index');\r\nrequire('./directives/image-file-selector/index');\r\nrequire('./directives/keyframe/index');\r\nrequire('./directives/pager/index');\r\nrequire('./directives/radio/index');\r\nrequire('./directives/resize-table/index');\r\nrequire('./directives/resizer/index');\r\nrequire('./directives/selectable/index');\r\nrequire('./directives/sort-group/index');\r\nrequire('./directives/spin/index');\r\nrequire('./directives/top-nav/index');\r\nrequire('./directives/logo/index');\r\nrequire('./directives/user-info/index');\r\nrequire('./directives/user-avatar/index');\r\nrequire('./directives/dropdown/index');\r\nrequire('./directives/datepicker/index');\r\nrequire('./directives/validation/index');\r\nrequire('./directives/switch-button/index');\r\nrequire('./directives/search-input/index');\r\nrequire('./directives/input-limit/index');\r\nrequire('./directives/badge/index');\r\nrequire('./directives/captcha/index');\r\nrequire('./directives/calCenterEllipsis/index');\r\nrequire('./directives/entityView/index');\r\n\r\n// require('./directives/inline-editor/') //开发中\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/index.js\n// module id = 81\n// module chunks = 0"], "sourceRoot": ""}