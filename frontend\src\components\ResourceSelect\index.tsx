import React, { FC, useState, useEffect, ReactText, useRef } from 'react';
import rmanApis from '@/service/rman';
import './index.less';
import {
  Modal,
  Button,
  Input,
  Spin,
  message,
  Select,
  Pagination,
  Checkbox,
  Radio,
  Tree,
  Space,
  Table,
  Breadcrumb,
  Empty,
} from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { useSelector, useDispatch, useIntl } from 'umi';
import { IconFont } from '../iconFont/iconFont';
import { IPermission } from '@/models/permission';
import UploadButton from '../upload/uploadButton/uploadButton';
import UploadBox from '../UploadBox';
import UploadTask from '../UploadTask';
import entityApis from '@/service/entityApis';
import _ from 'lodash';
const { Option } = Select;
const { DirectoryTree } = Tree;
interface ITreeItemProps {
  id: string;
  title: string;
  key: string;
  children?: Array<ITreeItemProps>;
}
const ResourceSelect: FC<{
  onConfirm: (res: any[]) => void;
  onCancel: () => void;
  onShowDetail: (id: string, detail: { name: string; type: string }) => void;
  fileType?: string[];
  multi?: boolean;
  asr_status?: boolean;
}> = ({ onConfirm, onCancel, fileType, multi, asr_status, onShowDetail }) => {
  const options: Array<any> = [
    { label: '全部', value: '' },
    { label: '音频', value: 'biz_sobey_audio' },
    { label: '视频', value: 'biz_sobey_video' },
    { label: '图片', value: 'biz_sobey_picture' },
    { label: '文档', value: 'biz_sobey_document' },
    { label: '文件夹', value: 'folder' },
  ];
  // 权限
  const { rmanGlobalParameter } = useSelector<{ permission: any }, IPermission>(
    ({ permission }) => permission,
  );
  const [loading, setLoading] = useState<boolean>(false);
  const [searchWord, setSearchWord] = useState<string>('');
  const [modalTreeData, setModalTreeData] = useState<ResourceModal.treeItem[]>([]); // 资源树数据
  const [listData, setListData] = useState<any[]>([]);
  const [breadCrumb, setBreadCrumb] = useState<Array<any>>([{ name: '', folderId: '', path: '' }]);
  const [directoryId, setDirectoryId] = useState<ReactText>('');
  const [expandedKeys, setExpandedKeys] = useState<React.ReactText[]>([]);
  const [resType, setResType] = useState('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPage, setTotalPage] = useState<number>(1);
  const [starttime, setStarttime] = useState<string>('');
  const [endtime, setEndtime] = useState<string>('');
  const [folderPath, setFolderPath] = useState<any>('');
  const [loadKeys, setLoadKeys] = useState<any>([]);
  const [resource, setResource] = useState<any[]>([]);

  const [mode, setMode] = useState<'block' | 'list'>('block');
  const intl = useIntl();
  const dispatch = useDispatch();
  const [resourceTreeData, setTreeData] = useState<Array<any>>([]); //zzh
  const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);

  const [myVideo, setMyVideo] = useState<boolean>(false); // 我的录播
  const [departmentVideo, setDepartmentVideo] = useState<boolean>(false); // 院系录播
  const [myCollection, setMyCollection] = useState<boolean>(false); // 我的收藏
  const [myShared, setMyShared] = useState<boolean>(false); // 我的分享
  const mySharedCurrentPath = useRef<any>(undefined); // 我的分享 当前文件名

  // const confirmLoading: boolean = useSelector<Models.Store, boolean>(
  //   state => state.microCourse.confirmLoading,
  // );
  useEffect(() => {
    fetchTree()
  }, [])
  const fetchTree = () => {
    rmanApis.gettreebylevel(2).then((res: any) => {
        if (res && res.data && res.success) {
            let data = res.data;
            let newData: any = [];
            data.map((item: any) => {
                if (item.name === '公共资源') {
                    item.children?.forEach((item: any) => {
                        newData.push({ ...item, layer: 1 });
                    });
                } else {
                    newData.push(item);
                }
            });
            newData = newData.filter(Boolean); //过滤空对象
            const rootData = newData.map((item: any) => {
                return {
                    key: item.path,
                    value: item.path,
                    title: item.name,
                    id: item.id,
                };
            });
            setModalTreeData(rootData);
        }
    });
  };
  useEffect(() => {
    // 初始化list数据
    if (modalTreeData.length > 0) {
      setLoadKeys(['myVideo', 'departmentVideo', 'myCollection', 'myShared', ]);
      let newData: any = [];
      modalTreeData.map((item: any) => {
        if (item.title === '公共资源') {
          item.children?.forEach((item: any) => {
            newData.push({ ...item, layer: 1 });
          });
        } else {
          newData.push(item);
        }
      });
      newData = newData.filter(Boolean); //过滤空对象
      if (!rmanGlobalParameter.includes('folder_group_display')) {
        newData.forEach((item: any, index: number) => {
          if (item.title === '群组资源') {
            newData.splice(1, index);
          }
        });
      }
      if (rmanGlobalParameter.includes('department_video_display')) {
        newData.push({
          key: 'departmentVideo',
          title: '院系录播',
          icon: <IconFont type="iconwodelubo" />,
        });
      }
      if (rmanGlobalParameter.includes('my_video_display')) {
        newData.push({
          key: 'myVideo',
          title: '我的录播',
          icon: <IconFont type="iconwodelubo" />,
        });
      }
      
      console.log(newData[0]);
      setDirectoryId(newData[0].key);
      setFolderPath(newData[0].value);
      console.log(newData, 'newData')
      setTreeData(newData);
    }
  }, [modalTreeData]);
  useEffect(() => {
    if (folderPath ) {
      if (resType === '' && searchWord === '' && starttime === '' && endtime === '') {
        myCollection
          ? searchMyCollection()
          : myVideo || departmentVideo
          ? searchMyVideo()
          : myShared
          ? searchShareMyself()
          : getList('', folderPath, currentPage, resType);
      } else {
        myCollection
          ? searchMyCollection()
          : myVideo || departmentVideo
          ? searchMyVideo()
          : myShared
          ? searchShareMyself()
          : getResAll('', resType, folderPath, searchWord, starttime, endtime, currentPage);
      }
    }
  }, [folderPath, resType, currentPage]);
  useEffect(() => {
      const storageStr = sessionStorage.getItem('cvod_resource_modal');
      if (storageStr) {
        const { directoryId, expandedKeys } = JSON.parse(storageStr);
        setDirectoryId(directoryId);
        setExpandedKeys(expandedKeys);
        setMyVideo(directoryId === 'myVideo');
        setDepartmentVideo(directoryId === 'departmentVideo');
        setMyShared(directoryId === 'myShared');
        setMyCollection(directoryId === 'myCollection');
        // getList(directoryId, folderPath, 1, resType);
      }
  }, []);
  // 获取我的收藏
  const searchMyCollection = async () => {
    rmanApis
      .getmycollectionlist({
        pageIndex: currentPage,
        pageSize: 9,
        conditions: [],
        sortFields: [
          {
            field: 'createDate_',
            isDesc: true,
          },
        ],
      })
      .then((res: any) => {
        console.log('获取我的收藏', res);
        if (res && res.data && res.success) {
          const temp = res.data.results.filter((item: any) => item.isDelete !== 1); //过滤掉已失效的素材
          setTotalPage(res.data.total);
          setListData(temp);
          setResource([]);
          setBreadCrumb([]);
        } else {
          setListData([]);
        }
      });
  };
  // 获取我的录播
  const searchMyVideo = async () => {
    rmanApis[departmentVideo?'getdepartmentvideolist': 'getmyvideolist']({
        folderId: departmentVideo? '': directoryId,
        keyword: [searchWord],
        folderPath: 'undefined/',
        conditions: [],
        sortFields: [
          {
            field: 'createDate_',
            isDesc: true,
          },
        ],
        pageIndex: currentPage,
        pageSize: 9,
      })
      .then((res: any) => {
        if (res && res.data && res.success) {
          setTotalPage(res.data.recordTotal);
          setListData(res.data.data);
          setResource([]);
          setBreadCrumb([]);
        } else {
          setListData([]);
        }
      });
  };
  const searchShareMyself = async () => {
    rmanApis
      .shareMyself({
        pageIndex: currentPage,
        pageSize: 9,
        sortFields: [
          {
            field: 'createDate_',
            isDesc: true,
          },
        ],
      })
      .then((res: any) => {
        if (res && res.data && res.success) {
          mySharedCurrentPath.current = ''; //必须要清空之前选中的路径值
          setTotalPage(res.data.recordTotal);
          setListData(res.data.results);
          setTotalPage(res.data.total);
          setResource([]);
          setBreadCrumb([]);
        } else {
          setListData([]);
        }
      });
  };
  // 获取list数据
  const getList = (id: any, folderPath: any, currentPage: number, type?: string) => {
    setLoading(true);
    rmanApis
      .searchResList(id, folderPath, currentPage, type ? [type, 'folder'] : undefined)
      .then((res: any) => {
        setLoading(false);
        if (res && res.success) {
          //剔除公共资源
          if (res.data.breadcrumbs[0].name === '公共资源') {
            res.data.breadcrumbs.splice(0, 1);
          }
          //对我的分享定制路径
          if (myShared) {
            for (let i = 0; i < res.data.breadcrumbs.length; i++) {
              if (res.data.breadcrumbs[i].path === mySharedCurrentPath.current) {
                res.data.breadcrumbs.splice(0, i);
                break;
              }
            }
            res.data.breadcrumbs.unshift({
              name: '分享给我的',
              folderId: '',
              path: '',
            });
          }
          setBreadCrumb(res.data.breadcrumbs);
          !myShared && setFolderPath(res.data.breadcrumbs[res.data.breadcrumbs.length - 1].path);
          setListData(res.data.data);
          setTotalPage(res.data.recordTotal);
        } else {
          message.error(intl.formatMessage({ id: '接口返回错误!' }));
          setListData([]);
        }
      })
      .catch((error: any) => {
        console.log(error);
      });
  };
  // 深度搜索
  const getResAll = (
    id: any,
    type: string,
    path: string,
    keyword: string,
    starttime: string,
    endtime: string,
    current: number,
  ) => {
    setLoading(true);
    rmanApis
      .searchResAll(id, type, path, keyword, starttime, endtime, current, asr_status)
      .then((res: any) => {
        setLoading(false);
        if (res && res.success) {
          setListData(res.data.data);
          setTotalPage(res.data.recordTotal);
        } else {
          message.error(intl.formatMessage({ id: '接口返回错误!' }));
          setListData([]);
        }
      })
      .catch((error: any) => {
        console.log(error);
      });
  };
  const handleOk = () => {
    onConfirm(
      resource.map((r) => ({
        ...r,
        contentId: r.contentId_,
        name: r.name_,
      })),
    );
    // setDirectoryId('');
    !fileType && setResType('');
    setSearchWord('');
    setStarttime('');
    setEndtime('');
    setCurrentPage(1);
    setTotalPage(1);
    // setExpandedKeys([]);
    setResource([]);
  };
  const handleCancel = () => {
    onCancel();
    // setDirectoryId('');
    !fileType && setResType('');
    setSearchWord('');
    setStarttime('');
    setEndtime('');
    setCurrentPage(1);
    setTotalPage(1);
    // setExpandedKeys([]);
    setResource([]);
  };
  // 目录选中事件
  const onSelect = (keys: React.Key[], node: any) => {
    setMyVideo(keys[0] === 'myVideo');
    setDepartmentVideo(keys[0] === 'departmentVideo');
    setMyShared(keys[0] === 'myShared');
    setMyCollection(keys[0] === 'myCollection');
    // if(keys[0] === 'myShared'){

    //   return
    // }

    console.log(node);
    setDirectoryId(keys[0]);
    setExpandedKeys([...expandedKeys, keys[0]]);
    // setResType('');
    setSearchWord('');
    setStarttime('');
    setEndtime('');
    setCurrentPage(1);
    setTotalPage(1);
    if (node.node && node.node.key) {
      //点击的左侧目录树
      setFolderPath(node.node.key);
      // getList(keys[0], node.node.path, 1, resType);
    } else {
      //选中的右侧文件夹
      console.info('当前路径111', folderPath);
      getList(keys[0], folderPath, 1, resType);
    }
  };
  const loadData = (node: any, isRoot: boolean = false): Promise<any> => {
    const { children, id, key } = node;
    console.log(children, 'children')
    return new Promise(async (resolve) => {
      if (!loadKeys.includes(id)) {
        setLoadKeys([...loadKeys, id]);
      }
      if (children && children.length > 0) {
        resolve(null);
        return;
      }
      function updateTreeData(
        list: ITreeItemProps[],
        id: string,
        children: ITreeItemProps[],
      ): ITreeItemProps[] {
        return list.map((node) => {
          if (node.id === id) {
            return {
              ...node,
              children,
            };
          } else if (node.children) {
            return {
              ...node,
              children: updateTreeData(node.children, id, children),
            };
          }
          return node;
        });
      }

      rmanApis.loadChild(key).then((res: any) => {
        if (res && res.success === true && res.data.length) {
          setTreeData((origin) =>
            updateTreeData(
              origin,
              id,
              res.data.map((item: any) => {
                // 对无子节点设置已加载标识实现去掉展开符并保留文件夹的图标(ps:不能使用叶子节点isLeaf标识,不然图标就是文件图标)
                if (item.childCount === 0 && !loadKeys.includes(item.contentId)) {
                  setLoadKeys((origin: any) => origin.concat([item.contentId]));
                }
                return {
                  title: item.name,
                  id: item.contentId,
                  key: item.path,
                };
              }),
            ),
          );
          resolve(null);
        }
      });
    });
  };
  //zzh 2021-8-27 树形目录异步加载 封装 end
  // 目录展开事件
  const onExpand = (
    expandedKeys: React.ReactText[],
    info: {
      node: any;
      expanded: boolean;
      nativeEvent: MouseEvent;
    },
  ) => {
    setExpandedKeys(expandedKeys);
    setAutoExpandParent(false);
  };
  // 类型改变事件
  const typeChange = (value: string) => {
    setResType(value);
    setCurrentPage(1);
    // if (
    //   value === '' &&
    //   searchWord === '' &&
    //   starttime === '' &&
    //   endtime === ''
    // ) {
    //   getList(directoryId, folderPath, 1, resType);
    // } else {
    //   getResAll(
    //     directoryId,
    //     value,
    //     folderPath,
    //     searchWord,
    //     starttime,
    //     endtime,
    //     1,
    //   );
    // }
  };
  // 搜索框内容改变事件
  const searchWordChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value.replace(/(^\s*)|(\s*$)/g, '');
    setSearchWord(value);
  };
  // 搜索框失去焦点事件
  const inoutBlur = () => {
    setCurrentPage(1);
    // getResAll(
    //   directoryId,
    //   resType,
    //   folderPath,
    //   searchWord,
    //   starttime,
    //   endtime,
    //   1,
    // );
  };
  // 搜索按钮点击事件按
  const searchClick = () => {
    if (resType === '' && searchWord === '' && starttime === '' && endtime === '') {
      getList(directoryId, folderPath, currentPage, resType);
    } else {
      getResAll(directoryId, resType, folderPath, searchWord, starttime, endtime, currentPage);
    }
  };

  // 重置
  const reset = () => {
    searchClick();
  };

  // 导航条跳转
  const goBreadcrumb = (item: any) => {
    console.log(item);
    //针对我的分享 文件夹回退
    if (myShared) {
      if (!item.path && !item.folderId) {
        searchShareMyself();
        setBreadCrumb([]);
      } else {
        getList('', item.path, currentPage, resType);
      }
    } else {
      if (item.path !== folderPath) {
        setFolderPath(item.path);
        // getList(item.folderId, item.path, currentPage, resType);
        //取上级路径备用
        const parentPath_ = item.path.split('/');
        if (parentPath_[parentPath_.length - 1] !== 'public') parentPath_.pop(); //回退到公共资源时不能剔除最后一级路径
        // 2021-12-15 处理导航条跳转重命名bug
        searchFolderListForLeftTree(parentPath_.join('/'), getdirectory(item.path));
      }
    }
  };
  // 获取最后一级的路径
  const getdirectory = (tree: any): string => {
    const directory = tree.split('/');
    if (directory.length > 0) {
      let str1 = directory[directory.length - 1];
      let str2 = directory[directory.length - 2]; //由于返回数据不同 得单独处理
      if (str1.includes('public')) {
        return '公共资源';
      } else if (str2.includes('private')) {
        return '个人资源';
      } else {
        return directory[directory.length - 1];
      }
    }
    return '';
  };
  // 全部素材检索--查询上级目录节点key 供左侧树同步选中
  const searchFolderListForLeftTree = async (path: string, name: string) => {
    let node: any;
    if (name === '个人资源' || name === '公共资源') {
      //单独处理
      rmanApis.gettreebylevel().then((res: any) => {
        if (res && res.success) {
          res.data.forEach((item: any) => {
            // console.log(item.name_)
            if (item.name === name) {
              node = item;
            }
          });
          console.log('当前上级节点', node);
          setDirectoryId(node?.id);
        } else {
          console.error(res);
        }
      });
    } else {
      rmanApis
        .searchResList('', path, currentPage, undefined, 100) //100是为了保证一次性能查完所有数据 再大了后台接口不支持
        .then((res: any) => {
          if (res && res.success) {
            res.data.data?.forEach((item: any) => {
              // console.log(item.name_)
              if (item.name_ === name) {
                node = item;
              }
            });
          } else {
            message.error(intl.formatMessage({ id: '接口返回错误!' }));
            setListData([]);
          }
          console.log('当前上级节点', node);
          setDirectoryId(node?.contentId_);
        })
        .catch((error: any) => {
          console.log(error);
        });
    }
  };

  // 页码改变
  const pageChange = (page: number) => {
    setCurrentPage(page);
  };

  // 进入文件夹
  const resDetail = (item: any) => {
    if (item.type_ === 'folder') {
      //针对我的分享得单独处理
      if (myShared) {
        // 手动传path 避免与其他混淆
        !mySharedCurrentPath.current
          ? (mySharedCurrentPath.current = item.tree_[0] + '/' + item.name_)
          : '';
        getList('', item.tree_[0] + '/' + item.name_, 1);
      } else {
        onSelect([item.contentId_], item);
      }
    }
  };

  // 展示资源详情
  const showDetail = (item: any) => {
    let type_ = 'folder';
    let typeSplit = item.type_.split('_');
    type_ = typeSplit[typeSplit.length - 1];
    // if (type_ !== 'folder') {
    //   onShowDetail(item.contentId_, {
    //     name: item.name_,
    //     type: type_,
    //   });
    // }
  };
  // 选择资源
  const itemCheckedChange = (value: any[]) => {
    if (fileType && value && value.some((v) => !fileType.includes(v.type_))) {
      message.warning(intl.formatMessage({ id: '请选择视频素材!' }));
      return;
    }
    setResource(value);
  };

  const columns = [
    {
      title: intl.formatMessage({ id: '素材名' }),
      dataIndex: 'name_',
      render: (name: string, record: any) => (
        <div
          onClick={() => {
            resDetail(record);
            showDetail(record);
          }}
          className="res-title"
        >
          <img src={record.keyframe_ || record.keyframe} className="cover-img" />
          {name}
        </div>
      ),
    },
    {
      title: intl.formatMessage({ id: '扩展名' }),
      dataIndex: 'fileext',
      width: 120,
      render: (fileext: string) => fileext || '-',
    },
    {
      title: intl.formatMessage({ id: '入库时间' }),
      width: 180,
      dataIndex: 'createDate_',
    },
    {
      title: intl.formatMessage({ id: '语音识别' }),
      width: 120,
      dataIndex: 'asr_status',
      render: (status: number, record: any) =>
        record.type_ === 'biz_sobey_audio' || record.type_ === 'biz_sobey_video'
          ? status === 1
            ? intl.formatMessage({ id: '分析中' })
            : status === 2
            ? intl.formatMessage({ id: '已分析' })
            : status === -1
            ? intl.formatMessage({ id: '分析失败' })
            : intl.formatMessage({ id: '未分析' })
          : '-',
    },
    {
      title: intl.formatMessage({ id: '知识点识别' }),
      width: 120,
      dataIndex: 'ocr_status',
      render: (status: number, record: any) =>
        record.type_ === 'biz_sobey_video'
          ? status === 1
            ? intl.formatMessage({ id: '分析中' })
            : status === 2
            ? intl.formatMessage({ id: '已分析' })
            : status === -1
            ? intl.formatMessage({ id: '分析失败' })
            : intl.formatMessage({ id: '未分析' })
          : '-',
    },
  ];
  
    //获取网络带宽
    const getNetworkSpeed = () => {
      // 判断浏览器是否支持navigator.connection属性
      if ((navigator as any)?.connection) {
        // 获取当前网络连接信息
        var connection = (navigator as any)?.connection;
        // 如果浏览器支持downlink属性，则输出当前网络的下行带宽
        if (connection.downlink) {
          return connection.downlink
        } else {
          return
        }
      } else {
        return 0
      }
    }
  const selectResource = () => {
    entityApis.getEntity(resource[0].contentId_,true, false).then((res: any) => {
      let dataType = res.data.type;
      let path = _.find(res.data.fileGroups, (f: any) => {
        return f.typeCode === (dataType ==='biz_sobey_document' || dataType ==='biz_sobey_picture'?'sourcefile':'previewfile');
      });
      if (!path) {
        path = _.find(res.data.fileGroups, (f: any) => {
          return f.typeCode === 'sourcefile';
        });
      }
      //自动适应清晰度
      let autoPathObj = path ? path.fileItems[0] : {}
      if(path && path.fileItems && path.fileItems.length){
        let speed = getNetworkSpeed()
        if(speed < 1){
          autoPathObj = path.fileItems.find((p:any)=>p.bitRate <= 512 * 1000) || path.fileItems[0]
        }else if(speed < 3){
          autoPathObj = path.fileItems.find((p:any)=>p.bitRate <= 1024 * 1000) || path.fileItems[0]
        }else if(speed < 5){
          autoPathObj = path.fileItems.find((p:any)=>p.bitRate <= 2048 * 1000) || path.fileItems[0]
        }else{
          autoPathObj = path.fileItems[path.fileItems.length - 1]
        }
      }
      let autoPath = autoPathObj.displayPath
      const dev = process.env.NODE_ENV === 'development' ? "" : "/rman"
      console.log({
            createDate:resource[0].createDate_,
            contentId_:resource[0].contentId_,
            keyframe_:resource[0].keyframe_,
            name:resource[0].name_,
            type_:resource[0].type_,
            filePath: autoPath,
            playAddress : `https://lsfwzt.csiic.com/auth/cas/login?service=http://***********/unifiedlogin/v1/cas/login?redirect_url=${window.location.origin}${dev}/#/basic/rmanDetail/${resource[0].contentId_}`,
          action: 'selectResource'})
      window.parent.postMessage(
        JSON.stringify({
              createDate:resource[0].createDate_,
              contentId_:resource[0].contentId_,
              keyframe_:resource[0].keyframe_,
              name:resource[0].name_,
              type_:resource[0].type_,
              filePath: autoPath,
              playAddress : `https://lsfwzt.csiic.com/auth/cas/login?service=http://***********/unifiedlogin/v1/cas/login?redirect_url=${window.location.origin}${dev}/#/basic/rmanDetail/${resource[0].contentId_}`,
            action: 'selectResource'}),
        '*'
      );
    })
  }
  return (
    <div className="resource-select">
        <Spin tip="Loading..." spinning={loading}>
          <div className="resource-select-content">
            <div className="directory">
              <DirectoryTree
                defaultSelectedKeys={modalTreeData.length > 0 ? [modalTreeData[0].key] : []}
                onSelect={onSelect}
                autoExpandParent={autoExpandParent}
                selectedKeys={[directoryId]}
                loadedKeys={loadKeys}
                expandedKeys={expandedKeys}
                onExpand={onExpand}
                loadData={loadData}
                treeData={resourceTreeData}
              />
            </div>
            <div className="content">
              <div className="search-group">
                <div style={{display: 'flex'}}>
                  <Input.Group compact>
                  <Select
                    value={resType}
                    style={{ width: '85px', textAlign: 'center' }}
                    onChange={typeChange}
                  >
                    {options.map((item) => (
                      <Option
                        key={item.value}
                        value={item.value}
                      >
                        {item.label}
                      </Option>
                    ))}
                  </Select>
                  <Input
                    style={{ width: '70%' }}
                    defaultValue=""
                    value={searchWord}
                    suffix={
                      <SearchOutlined
                        onClick={() => {
                          if(searchWord || resType){
                            if (currentPage === 1) {
                              getResAll(
                                directoryId,
                                resType,
                                folderPath,
                                searchWord,
                                starttime,
                                endtime,
                                currentPage,
                              );
                            } else {
                              inoutBlur();
                            }
                          } 
                          else{
                            getList('', folderPath, currentPage, resType);
                          }
                        }}
                      />
                    }
                    onChange={searchWordChange}
                    onBlur={inoutBlur}
                    onPressEnter={inoutBlur}
                  />
                </Input.Group>
                <Button type='primary' onClick={selectResource} style={{margin: '0 15px'}}>确定</Button>
                </div>
                {/* <RangePicker
                  ranges={{
                    最近一周: [moment().subtract(6, 'days'), moment()],
                    最近一个月: [moment().subtract(30, 'days'), moment()],
                    最近三个月: [moment().subtract(90, 'days'), moment()],
                  }}
                  format="YYYY-MM-DD"
                  onChange={dateChange}
                />
                <Button type="primary" onClick={searchClick}>
                  搜索
                </Button> */}
                <Space className="mode-switch-wrapper">
                  <Button
                    onClick={() => searchClick()}
                    title={intl.formatMessage({ id: '刷新' })}
                    type="primary"
                    size="small"
                    style={{ marginRight: '10px' }}
                    icon={<IconFont type="iconshuaxin" />}
                  />
                  <div
                    className={mode === 'block' ? 'mode-item active' : 'mode-item'}
                    onClick={() => setMode('block')}
                  >
                    <IconFont type="iconhebingxingzhuangfuzhi2" />
                  </div>
                  <div
                    className={mode === 'list' ? 'mode-item active' : 'mode-item'}
                    onClick={() => setMode('list')}
                  >
                    <IconFont type="iconliebiao" />
                  </div>
                </Space>
              </div>
              {(!myCollection && !myVideo && !departmentVideo && !myShared) && <UploadButton
                            targetFolder={folderPath}
                            disabled={(() => {
                              //完全采用后端给的判断标识 2022-04-22
                              if (breadCrumb[breadCrumb.length - 1]?.isUpload) {
                                return false
                              } else {
                                return true
                              }
                            })()}
                          />}
              <div className="breadcrumb">
                <Breadcrumb>
                  {breadCrumb.length > 3
                    ? breadCrumb.map((item: any, index: number) => (
                        <>
                          {index === breadCrumb.length - 4 && ( //倒数第四个
                            <Breadcrumb.Item key={index}>
                              <a>{'...'}</a>
                            </Breadcrumb.Item>
                          )}
                          {index > breadCrumb.length - 4 && ( // 只显示后三级目录
                            <Breadcrumb.Item key={index}>
                              <a onClick={() => goBreadcrumb(item)} title={item.name}>
                                {item.name.length > 5
                                  ? item.name.substring(0, 5) + '...'
                                  : item.name}
                              </a>
                            </Breadcrumb.Item>
                          )}
                        </>
                      ))
                    : breadCrumb.map((item: any, index: number) => (
                        <Breadcrumb.Item key={index}>
                          <a onClick={() => goBreadcrumb(item)} title={item.name}>
                            {item.name.length > 5 ? item.name.substring(0, 5) + '...' : item.name}
                          </a>
                        </Breadcrumb.Item>
                      ))}
                </Breadcrumb>
              </div>
              {listData.length === 0 ? (
                <Empty description={asr_status ? intl.formatMessage({ id: '暂无已完成语音分析的视频' }) : intl.formatMessage({ id: '暂无视频' })} />
              ) : multi ? (
                <Checkbox.Group name="res_check" value={resource} onChange={itemCheckedChange}>
                  {mode === 'block' ? (
                    <div className="list">
                      {listData.map((item) => {
                        return (
                          <div
                            className="list-item"
                            key={item.contentId_}
                            onClick={() => {
                              resDetail(item);
                            }}
                          >
                            <div
                              className="img-box"
                              onClick={() => {
                                showDetail(item);
                              }}
                            >
                              {/* {item.keyframe_ ? (
                                <img src={item.keyframe_} alt="" />
                              ) : (
                                <img src={item.keyframe} alt="" />
                              )} */}
                              {item.keyframe_?.indexOf('/static/images/') != -1 ? (
                                <img
                                  style={{ maxWidth: '60%', maxHeight: '60%' }}
                                  src={item.keyframe_}
                                  alt=""
                                />
                              ) : (
                                <img src={item.keyframe_} alt="" />
                              )}
                            </div>
                            <div className="item-title" title={item.name_}>
                              {item.type_ !== 'folder' ? (
                                <Checkbox disabled={item.fileext === 'JPG'} value={item}>{item.name_}</Checkbox>
                              ) : (
                                item.name_
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <Table
                      columns={[
                        {
                          title: '',
                          dataIndex: 'check',
                          key: 'check',
                          width: 50,
                          render: (_text: any, record: any) =>
                            // <Checkbox value={record} />
                            record.type_ !== 'folder' ? <Checkbox value={record} /> : '',
                        },
                        ...columns,
                      ]}
                      rowKey={'contentId_'}
                      scroll={{ y: 'calc(100vh - 450px)' }}
                      dataSource={listData}
                      pagination={false}
                      className="res-table"
                    />
                  )}
                </Checkbox.Group>
              ) : (
                <Radio.Group
                  name="res_radio"
                  value={resource[0]}
                  onChange={(e) => itemCheckedChange([e.target.value])}
                >
                  {mode === 'block' ? (
                    <div className="list">
                      {listData.map((item) => {
                        return (
                          <div
                            className="list-item"
                            key={item.contentId_}
                            onClick={() => {
                              resDetail(item);
                            }}
                          >
                            <div
                              className="img-box"
                              onClick={() => {
                                showDetail(item);
                              }}
                            >
                              {/* {item.keyframe_ ? (
                                  <img src={item.keyframe_} alt="" />
                                ) : (
                                  <img src={item.keyframe} alt="" />
                                )} */}
                              {item.keyframe_?.indexOf('/static/images/') != -1 ? (
                                <img
                                  style={{ maxWidth: '60%', maxHeight: '60%' }}
                                  src={item.keyframe_}
                                  alt=""
                                />
                              ) : (
                                <img src={item.keyframe_} alt="" />
                              )}
                            </div>
                            <div className="item-title" title={item.name_}>
                              {item.type_ !== 'folder' ? (
                                <Radio disabled={item.fileext === 'ICO'} value={item}>{item.name_}</Radio>
                              ) : (
                                item.name_
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <Table
                      columns={[
                        {
                          title: '',
                          dataIndex: 'check',
                          key: 'check',
                          width: 50,
                          render: (_text: any, record: any) => <Radio value={record} />,
                        },
                        ...columns,
                      ]}
                      scroll={{ y: 'calc(100vh - 450px)' }}
                      dataSource={listData}
                      pagination={false}
                      className="res-table"
                    />
                  )}
                </Radio.Group>
              )}
              <Pagination
                className="pagination"
                defaultCurrent={1}
                total={totalPage}
                pageSize={9}
                showSizeChanger={false}
                hideOnSinglePage={true}
                onChange={pageChange}
                current={currentPage}
              />
            </div>
          </div>
          <UploadTask refresh={() =>{
            getList('', folderPath, currentPage, resType);
          }} />
          <UploadBox
            targetFolder={folderPath}
            breadCrumb={breadCrumb}
            folderName={breadCrumb[breadCrumb.length - 1]?.name}
          />
        </Spin>
    </div>
  );
};

export default ResourceSelect;
