{"name": "mam-webupload", "authors": ["胡耀 <<EMAIL>>"], "description": "", "main": "mam-webupload.js", "keywords": ["web-upload"], "license": "MIT", "homepage": "http://**************/pbu/fusionmedia/frontendbase/mam-upload.git", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests", "src", "webpack.config.js", "package-lock.json", "package.json", "bs-config.js", "bower.json", ".giti<PERSON>re", ".bower<PERSON>", "src/libs"], "dependencies": {"mam-base": "*", "mam-ng": "*", "mam-metadata": "*", "mam-timecode-convert": "*"}, "_release": "b6c11a3ff6", "_resolution": {"type": "branch", "branch": "master", "commit": "b6c11a3ff65eaca5b48c38ef85285f3a4b9c89a0"}, "_source": "http://heju@**************/front-end/mam-upload.git", "_target": "*", "_originalSource": "mam-upload"}