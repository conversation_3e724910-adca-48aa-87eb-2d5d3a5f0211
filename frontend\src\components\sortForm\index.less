.sort_form_wrapper {
  //height: 100vh;
  background-color: #ffffff;

  .form_list_wrapper {
    padding: 12px;

    .form_item_list {
      .form_item {
        width: 48%;
        height: 30px;
        line-height: 30px;
        float: left;
        margin: 1%;
        background-color: #f4f6fc;
        font-size: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: move;
        border: 1px solid #f4f6fc;

        &:hover {
          color: #409eff;
          border: 1px dashed #409eff;
        }

        span {
          display: inline-block;
          vertical-align: middle;
        }

        span:first-child {
          margin-right: 6px;
          margin-left: 8px;
          font-size: 14px;
        }
      }
    }
  }

  .editor_wrapper {
    position: relative;
    height: calc(100vh - 60px);
    padding: 12px;
    background-color: #fafafa;

    .editor_tips {
      position: absolute;
      text-align: center;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 20px;
      color: #ccc;
    }

    form {
      min-height: 100%;
      //border-left: 1px solid #e0e0e0;
      //border-right: 1px solid #e0e0e0;
      border: 1px dashed #999999;
      height: 100%;
      overflow-y: auto;

      .editor {
        min-height: 100vh;
        background-color: #ffffff;

        .form_item_wrapper {
          position: relative;
          //border: 1px dashed hsla(0, 0%, 66.7%, .5);
          //background-color: rgba(236, 245, 255, .3);
          //margin: 2px;
          padding-top: 24px;
          padding-right: 24px;
          border: 1px solid #ffffff;
          border-bottom: 1px dashed #999999;
          overflow: hidden;

          &:last-child {
            border-bottom: none;
          }

          &:hover {
            border: 1px solid #409eff;
          }

          &.active {
            border: 2px solid #409eff;
          }


          &:after {
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            display: block;
            z-index: 8;
            content: "";
          }

          .form_item_drag {
            width: 28px;
            height: 28px;
            position: absolute;
            top: -2px;
            left: -2px;
            text-align: center;
            background-color: #409eff;
            font-size: 18px;
            color: #ffffff;
            z-index: 10;
            cursor: move;
          }

          .form_item_action {
            display: flex;
            justify-content: space-evenly;
            align-items: center;
            width: 28px;
            height: 28px;
            position: absolute;
            right: -2px;
            bottom: -2px;
            background-color: #409eff;
            font-size: 18px;
            color: #ffffff;
            z-index: 10;

            span {
              cursor: pointer;
            }
          }
        }
      }
    }
  }

  .attr_wrapper {
    position: relative;
    height: calc(100vh - 60px);

    .attr_header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 45px;
      padding: 0 12px;
      color: #2c3e50;
      border-bottom: 1px solid #e5e7ec;
    }

    .attr_content {
      padding: 12px;
      height: calc(100vh - 105px);
      overflow-y: auto;
    }

    .attr_tips {
      position: absolute;
      text-align: center;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 20px;
      color: #ccc;
    }
  }
}
