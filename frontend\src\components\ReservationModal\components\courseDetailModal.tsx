import React, { useState } from 'react';
import { Modal, Table, message, DatePicker } from 'antd';
import contentListApis from '@/service/contentListApis';
import moment from "moment";

const { RangePicker } = DatePicker
interface CreateModalProps {
    modalVisible: boolean;
    modalClose: () => void;
    dataSource: any;
    courseDetail: any;
    semesterStartSate: any;
    className_?: string;
    pubOrPri: boolean;
    refresh?: (tag?: boolean, voice?: boolean, point?: boolean) => any;
}

const CourseDetailModal: React.FC<CreateModalProps> = props => {
    const { modalVisible, courseDetail, modalClose, semesterStartSate } = props;
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [selectedRows, setSelectedRows] = useState<any>([]);
    const [tipModalVisible, setTipModalVisible] = useState(false);
    const onConfirm = () => {
        contentListApis.reservationAdd(
            selectedRows.map(item => ({
                campus: item.area.split('-')[0],
                academicBuilding: item.area.split('-')[1],
                classroomNo: item.area.split('-')[2],
                classTime: item.classTime,
                classroomCode: item.area_code,
                courseId: item.course_number,
                courseName: item.course_name,
                courseNo: item.serial_number,
                teacherName: item.teacher[0].name,
                teacher: item.teacher[0].code
            }))
        ).then(res => {
            if (res?.status === 200) {
                setTipModalVisible(true)
                modalClose()
            }
        })
    }
    const columns = [
        {
            title: '周次',
            dataIndex: 'week',
            key: 'week'
        },
        {
            title: '上课日期',
            dataIndex: 'classTime',
            key: 'classTime',
        }
    ]
    const onSelectChange = (
        newSelectedRowKeys: any,
        selectedRows: any,
    ) => {
        setSelectedRowKeys(newSelectedRowKeys)
        setSelectedRows(selectedRows)
    };
    return (
        <div>
            {modalVisible &&<Modal
                className='courseDetailModal'
                destroyOnClose={true}
                title='时间'
                open={modalVisible}
                onOk={() => onConfirm()}
                onCancel={modalClose}
            >
                <Table
                    rowSelection={{
                        selectedRowKeys,
                        onChange: onSelectChange,
                    }}
                    rowKey={record => record.classTime}
                    dataSource={courseDetail.class_weekly.map(item => ({ ...courseDetail, week: item, classTime: item === 1 ? semesterStartSate : moment(semesterStartSate).add(courseDetail.week_day - 1, 'days').add(item - 1, 'weeks').format('YYYY-MM-DD') }))}
                    columns={columns}
                    bordered
                />
            </Modal>}
            <Modal
                className='tipModal'
                destroyOnClose={true}
                open={tipModalVisible}
                footer={null}
                onCancel={() => {
                    setTipModalVisible(false)
                    }
                }
            >
                <div>预约成功后，资源将在凌晨1点进行下载，入库成功后的资源，需经过剪辑保存至个人资源，未经过剪辑的资源，两个月后自动删除。</div>
            </Modal>
        </div>
    );
};

export default CourseDetailModal;
