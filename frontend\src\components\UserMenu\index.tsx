import React, { FC, useState, useRef, useEffect } from 'react';
import { Modal, Button, Checkbox } from 'antd';
import { Menu, Dropdown, message } from 'antd';
import { LogoutOutlined } from '@ant-design/icons';
import './index.less';
import { formatMessage, useIntl, history } from 'umi';
import loginApis from '@/service/loginApis';
import CreateModal from '@/components/CreateModal';

interface UserMenuProps {
  // modalVisible: boolean;
  // onCancel: () => void;
}

const UserMenu: React.FC<UserMenuProps> = props => {
  const [deleteModal, setDeleteModal] = useState<boolean>(false);
  const intl = useIntl();
  const loginOut = () => {
    window.location.href = `/unifiedlogin/v1/loginmanage/loginout/go`;
  };
  const menu = (
    <Menu className="menubox">
      <Menu.Item onClick={() => setDeleteModal(true)} className="logout">
        <LogoutOutlined className="logout-icon" />
        {intl.formatMessage({
          id: 'log-out',
          defaultMessage: '退出登录',
        })}
      </Menu.Item>
    </Menu>
  );
  return (
    <div>
      <Dropdown overlay={menu}>{props.children}</Dropdown>
      <CreateModal
        modalVisible={deleteModal}
        title={intl.formatMessage({
          id: 'log-out',
          defaultMessage: '退出登录',
        })}
        footers={[
          <Button key="back" onClick={() => setDeleteModal(false)}>
            {intl.formatMessage({
              id: 'cancel',
              defaultMessage: '取消',
            })}
          </Button>,
          <Button key="submit" type="primary" onClick={loginOut}>
            {intl.formatMessage({
              id: 'determine',
              defaultMessage: '确定',
            })}
          </Button>,
        ]}
      >
        <span>{intl.formatMessage({
          id: '确定要退出登录吗'
        })}</span>
      </CreateModal>
    </div>
  );
};

export default UserMenu;
