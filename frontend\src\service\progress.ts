import http from '../http/http';
import { IEntityRes, IEntityMetadata, IFormItem } from '@/types/entityTypes';

namespace progressApis {
  export const getconfig = () => {
    return http<IEntityRes>(`/rman/v1/personal/task/config`, {
      method: 'GET',
    });
  };

  export const searchprogress = (data: any) =>
  http('/rman/v1/personal/task/search', {
    method: "GET",
    params: data,
  })

  export const taskredo = (taskId: any) =>
  http('/rman/v1/personal/task/redo?taskId='+taskId, {
    method: "POST"
  })

  export const taskbulkredo = (data: any) =>
  http('/rman/v1/personal/task/bulk/redo', {
    method: "POST",
    data
  })

  export const taskcancel = (taskId: any) =>
  http('/rman/v1/personal/task/cancel?taskId='+taskId, {
    method: "POST"
  })

  export const taskbulkcancel = (data: any) =>
  http('/rman/v1/personal/task/bulk/cancel', {
    method: "POST",
    data
  })

  export const getprogressinfo = (taskId: any) =>
  http('/rman/v1/personal/task/search/info?taskId='+taskId, {
    method: "GET"
  })

  
  // 文件夹下载
  export const folderDownload = (data: any) =>  http('/rman/v1/folder/download', {
    method: 'POST',
    data,
  })

  // 查询文件夹打包下载任务
  export const folderDownloadProgess = (params: any) => http('/rman/v1/folder/download/progress', {
    method: 'GET',
    params
  })

  // 下载文件夹压缩ZIP文件
  export const folderDownloadZip = (data: any) => http('/rman/v1/folder/download/zip', {
    method: 'POST',
    data,
    responseType: 'blob'
  })

  export const folderDownloadDelete = (data: any) => http('/rman/v1/folder/download/delete', {
    method: 'DELETE',
    data,
  })
}

export default progressApis;
