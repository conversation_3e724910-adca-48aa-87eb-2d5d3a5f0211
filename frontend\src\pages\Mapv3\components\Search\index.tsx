import React, { FC, useState, useEffect, useImperativeHandle, forwardRef, useRef } from 'react';
import './index.less';
import {
    Input,
    Button,
    Select,
    Tooltip,
    message,
    Table,
    Modal,
    Drawer,
    List,
    Tag
} from 'antd';
import { IconFont } from '@/components/iconFont';

const Search: FC<any> = forwardRef(({ graph, selecttype, querytype, inputtext, visible, setVisible, centerednode }, ref) => {

    const [listdata, setListdata] = useState<any>([]);
    //原数据
    const [originaldata, setOriginaldata] = useState<any>([]);

    useImperativeHandle(ref, () => ({ // 暴露给父组件的方法
        querynode
    }))

    // 初始化方法
    useEffect(() => {
        if (graph && visible) {
            querynode();
        }
    }, [graph, visible,querytype]);

    // 检索知识点
    const querynode = () => {
        if(!graph){
            return
        }
        const data = graph.toJSON();
        let newdata: any = [];
        // 先过滤 数据
        newdata = data.cells.filter((item: any) => {
            if (item.shape == "react-shape") {
                return true
            } else {
                return false;
            }
        });

        // 名称搜索
        if(inputtext != ''){
            newdata = newdata.filter((item: any) => {
                if (item.data.label.indexOf(inputtext) != -1) {
                    return true;
                } else {
                    return false;
                }
            })
        }

        // 过滤筛选筛选的类型
        if (selecttype != '0') {
            newdata = newdata.filter((item: any) => {
                if (item.data.type == selecttype) {
                    return true;
                } else {
                    return false;
                }
            })
        }


        // 过滤筛疑难点 和 有讲解的节点
        if (querytype == '1') {
            newdata = newdata.filter((item: any) => {
                if (item.data.isyinandian) {
                    return true;
                } else {
                    return false;
                }
            })
        }else  if(querytype == '2'){
            newdata = newdata.filter((item: any) => {
                if (item.data.explanation!='' || item.data.bindresource.length) {
                    return true;
                } else {
                    return false;
                }
            })
        }if(querytype == '3'){
            newdata = newdata.filter((item: any) => {
                if (item.data.isDiscriminate) {
                    return true;
                } else {
                    return false;
                }
            })
        }

        setListdata(newdata);
        setOriginaldata(newdata);

    }

    return (
        <Drawer
            title={
                <div style={{position:'relative',width:'100%',height:'100%'}}>
                    <img style={{width:'100%',height:'auto'}} src={require('../../../../images/coursemap/v3/title_bg.png')}></img>
                    <img onClick={()=>setVisible(0)} style={{position:'absolute',right:'20px',width:'15px',top:'18px'}} src={require('../../../../images/coursemap/v3/close.png')} alt="" />
                </div>
            }
            placement="right"
            mask={false}
            closable={false}
            visible={visible == 6}
            getContainer={false}
            style={{ position: 'absolute' }}
            width="500px"
            className='newsearch'
        >   

            <List
                bordered
                locale={{ emptyText: '暂无搜索结果' }}
                dataSource={listdata}
                renderItem={(item: any) => (
                    <List.Item onClick={() => {
                        let cell = graph.getCellById(item.id);
                        graph.getNodes().forEach((item:any)=>{
                            // 设置节点透明
                            item.attr('foreignObject/opacity', 0.2);
                        })
                        graph.getEdges().forEach((item:any)=>{
                            // 设置节点透明
                            item.attr('line/stroke', '#333333');
                        })
                        // 设置节点高亮
                        cell.attr('foreignObject/opacity', 1);
                        graph.centerCell(cell, { animation: { duration: 400 } });
                    }} style={{ cursor: 'pointer' }}>
                      {
                        item.data.type == 1 ?
                        <Tag color="orange">分类节点</Tag>:
                        <Tag color="#f50">知识节点</Tag>
                      }
                        <span style={{color:'#fff'}}>{item.data.label}</span>
                    </List.Item>
                )}
            />
        </Drawer>
    )
});

export default Search;
