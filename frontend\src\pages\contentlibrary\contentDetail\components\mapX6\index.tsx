import React, { useState, useEffect, FC, useRef } from "react";
import ReactDOM from 'react-dom';
import './index.less'
import { Graph } from '@antv/x6';
// 引入react插件
import '@antv/x6-react-shape'
// 引入布局插件
import { Force2Layout } from '@antv/layout'


const MapX6: FC<any> = ({ mapdata, initover, nodeClick, current }) => {
    const container = useRef<any>(null);
    const graph = useRef<any>(null);  //全局方法
    let contnum: number = 1;  //1到5之间  轮流取值
    let colormodel: any = {}; //颜色字典表
    // 当前显示模式
    const [showmodel, setshowmodel] = useState<number>(1);  //1 全部 2 包含 3 后续 4 等价

    useEffect(() => {
        if (mapdata) {
            // 获取第二层的节点
            let towedges = mapdata.edges.filter((item: any) => item.source == mapdata.nodes[0].id);
            towedges.forEach((item: any) => {
                // 只有5个图标来随机 1到5之间
                if (contnum == 6) {
                    contnum = 1;
                }
                // 这里为了方便子集 取用父级的颜色 存了一个字典表
                colormodel[item.target] = contnum;
                contnum++
            })
            initmap();
        }
        
        return () => {
            contnum = 1;
            colormodel = {};
        }
    }, [mapdata])
    const registerstyle = () => {
        try {
            // 注册返回 React 组件的函数
            Graph.registerReactComponent('react-compont', (node) => {
                // 获取前序节点
                let prenode = graph.current.getPredecessors(node, { distance: 1 });
                // 当前随机出来的图标
                let iconnum = 1;
                const data = node.getData();

                // let config:any = null;
                // if(data.type == 4){
                //     config = MapConfig.marjor;
                // }else if (data.type == 3){
                //     config = MapConfig.course;
                // }else if (data.type == 2){
                //     config = MapConfig.fenlei;
                // }else if (data.type == 1){
                //     config = MapConfig.knowledge;
                // }
                // 二级后的节点取用上级的颜色
                if (prenode.length > 0) {
                    if (colormodel[node.id]) {
                        iconnum = colormodel[node.id];
                        // 获取所有的子节点 给所有子节点加上标识
                        let nextnode = graph.current.getSuccessors(node, { deep: true });
                        nextnode.forEach((item: any) => {
                            colormodel[item.id] = iconnum;
                        })
                    }
                }

                const imgdom = () => {
                    return (
                        <>
                            {data.type == 4 && <img style={{ width: '100%', height: 'auto' }} src={require(`@/images/coursemap/marjor${iconnum}.png`)}></img>}
                            {data.type == 3 && <img style={{ width: '100%', height: 'auto' }} src={require(`@/images/coursemap/course${iconnum}.png`)}></img>}
                            {data.type == 2 && <img style={{ width: '100%', height: 'auto' }} src={require(`@/images/coursemap/knowledge${iconnum}.png`)}></img>}
                            {data.type == 1 && <img style={{ width: '100%', height: 'auto' }} src={require(`@/images/coursemap/fenlei${iconnum}.png`)}></img>}
                        </>
                    )
                }

                return (
                    <div style={{ width: '100%', height: '100%', borderRadius: '50%', position: 'relative', display: 'flex', justifyContent: 'center', cursor: 'pointer', }} >
                        {/* className="marjor_node_img" */}
                        <div style={{ width: '100%', height: '100%' }}>
                            {
                                imgdom()
                            }
                        </div>
                        <div style={{
                            position: 'absolute',
                            width: '180px',
                            height: 'auto',
                            top: '100%',
                            textAlign: 'center',
                            whiteSpace: 'pre-wrap',
                            color: ' #000',
                            cursor: 'pointer'
                        }} className="name_view">
                            <span>{data.label}</span>
                        </div>
                    </div>
                )
            })
        } catch (error) {
            // console.log('注册组件报错！',error);
        }
    }
    const shownode = (node: any) => {
        // 设置当前边的筛选模式
        // setshowmodel(0);
        // 获取相邻节点
        let neinode = graph.current.getNeighbors(node);
        if (neinode.length) {
            neinode.forEach((item: any) => {
                item.attr('foreignObject/opacity', 1);
            })
            node.attr('foreignObject/opacity', 1);
        }
        // 获取相邻边
        let neiedge = graph.current.getConnectedEdges(node);
        graph.current.getEdges().forEach((item: any) => {
            // 设置节点透明
            item.attr('line/stroke', '#333333');
        })
        if (neiedge.length) {
            neiedge.forEach((item: any) => {
                item.attr('line/stroke', '#8A8B99');
            })
            node.attr('line/stroke', '#8A8B99');
        }
    }
    const initmap = () => {
        const containerdom: any = ReactDOM.findDOMNode(container.current);
        const width = containerdom.scrollWidth;
        const height = containerdom.scrollHeight || 1000;
        if (graph.current) {
            try {
                graph.current.dispose();
                Graph.unregisterReactComponent('react-compont');
            } catch (error) {
                console.log(error);
            }
        }
        // x6 注册react 组件
        registerstyle();
        try {
            // 实例初始化
            graph.current = new Graph({
                container: containerdom,
                autoResize: true,
                width,
                height,
                panning: {
                    enabled: true,
                },
                // 滚轮放大缩小
                mousewheel: {
                    enabled: true,
                    modifiers: [], //配置快捷键 触发
                    minScale: 0.2,
                },
            });
            const gridLayout = new Force2Layout({
                type: 'gForce',
                preventOverlap: true,      // 防止节点重叠     
                factor: 3,                 //  斥力的一个系数  这个比较重要
                // maxIteration: 10000,        // 布局迭代次数
                workerEnabled: true,
                linkDistance: (edge?: any, source?: any, target?: any) => {
                    return target.data.linkDistance + 100
                },
                // maxSpeed:100,
                // clustering: true,
                // nodeClusterBy: 'type',
                // clusterNodeStrength: 100,         
                // tick: () => {
                //     console.log('ticking');
                //     graph.current.fromJSON(model);
                // },
                onLayoutEnd: () => {
                    graph.current.fromJSON(mapdata);
                    initover && initover(graph.current);
                    graph.current.centerContent();

                }
            })
            gridLayout.layout(mapdata);
            graph.current.zoom(-0.3)
            graph.current.on('node:click', ({ e, x, y, node, view }: any) => {
                if (nodeClick) {
                    nodeClick(node)
                }
            })
        } catch (error) {
            console.log(error);
        }
    }

    return (
        <div className="mapv3_x6_view">
            <div className="map_canvas" ref={container}></div>
        </div>
    )
}

export default MapX6;