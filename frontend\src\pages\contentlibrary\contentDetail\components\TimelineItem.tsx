import React, {
  FC,
  useEffect,
  useRef,
  useState,
  KeyboardEvent,
  FocusEvent,
} from 'react';
import { IconFont } from '@/components';
import { Input, Button, message, Timeline, Anchor } from 'antd';
import { CheckOutlined,CloseOutlined } from '@ant-design/icons';
import _ from 'lodash';
import '../index.less';
import SmartService from '@/service/smartService';
import { useParams, useDispatch, useSelector } from 'umi';
import { IPermission } from '@/models/permission';
import perCfg from '@/permission/config';
import { searchKeywords } from '@/utils';

interface IData {
  detail: any;
  setCurrentTime: any;
  currentFrame: any;
  currentLanguage: any;
  getQueryVoice: any;
  searchVoiceWord: string;
  Timelineindex: number;
  player: any
}

const TimelineItem: FC<IData> = props => {
  const params = useParams<{ contentId: string }>();
  const dispatch = useDispatch();
  const {
    detail,
    setCurrentTime,
    currentFrame,
    currentLanguage,
    getQueryVoice,
    searchVoiceWord,
    Timelineindex,
    player
  } = props;
  const win = window as any;
  const sequencemetaName = useRef<any>();
  const sequencemetaTransName = useRef<any>();
  const [sequencemetaEdit, setSequencemetaEdit] = useState<boolean>(true);
  const [sequencemetaTransEdit, setSequencemetaTransEdit] = useState<boolean>(true); //翻译语言
  const [voiceTitle, setVoiceTitle] = useState<any>();
  const { permissions } = useSelector<{ permission: any }, IPermission>(
    ({ permission }) => permission,
  );

  useEffect(() => {
    if (!sequencemetaEdit) {
      sequencemetaName.current?.focus();
    }
    if (!sequencemetaTransEdit) {
      sequencemetaTransName.current?.focus();
    }
    if(sequencemetaEdit ||sequencemetaTransEdit){
        setCurrentTime()
    }
  }, [sequencemetaEdit,sequencemetaTransEdit]);
  // useEffect(() => {
  // }, [searchVoiceWord, detail]);
  const searchVoice = (tit: string) => {
    const temp = searchKeywords(tit,'voice');
    if(!searchVoiceWord && temp.index>-1){
      return <span>
                {temp.beforeStr}
                <span className="key-search-value">{temp.word}</span>
                {temp.afterStr}
            </span>
    }
    if(searchVoiceWord){ //在检索时 需要置空
      window.localStorage.setItem('searchKeyWord',JSON.stringify({}));
    }
    const index = tit?.indexOf(searchVoiceWord);
    const beforeStr = tit?.substr(0, index);
    const afterStr = tit?.substr(index + searchVoiceWord.length);
    const title =
      index > -1 ? (
        <span>
          {beforeStr}
          <span className="voice-search-value">{searchVoiceWord}</span>
          {afterStr}
        </span>
      ) : (
        <span>{tit}</span>
      );
    return title;
    // setVoiceTitle(title)
  };
  const sequencemetaChangeName = async (
    e: KeyboardEvent<HTMLInputElement> | FocusEvent<HTMLInputElement> | any,
    id: any,
  ) => {
    e.preventDefault();
    let name = sequencemetaName.current.input.value || e.target.value;
    if (name === '') {
      message.error('标题不能为空');
      return;
    }
    const res = await SmartService.updatepartVoice(params.contentId, [
      {
        guid_: id,
        textLanguage:{
          [`text_${currentLanguage}`]: name,
        },
        _in: detail._in,
        _out: detail._out,
      },
    ]);
    if (res?.success) {
      message.success('编辑成功');
      // player.current.tracks[0].entries[Timelineindex].text = name
      setSequencemetaEdit(true);
    } else {
      message.error('编辑失败');
    }
    getQueryVoice();
  };
  const sequencemetaTransChangeName = async (
    e: KeyboardEvent<HTMLInputElement> | FocusEvent<HTMLInputElement> | any,
    id: any,
  ) => {
    e.preventDefault();
    let name = sequencemetaTransName.current.input.value || e.target.value;
    if (name === '') {
      message.error('标题不能为空');
      return;
    }
    const res = await SmartService.updatepartVoice(params.contentId, [
      {
        guid_: id,
        textLanguage:{
          [`text_${currentLanguage}`]: name,
        },
        _in: detail._in,
        _out: detail._out,
      },
    ]);
    if (res?.success) {
      message.success('编辑成功');
      setSequencemetaTransEdit(true);
    } else {
      message.error('编辑失败');
    }
    getQueryVoice();
    setSequencemetaEdit(true);
  };
  return (
    <Timeline.Item
      color={
        currentFrame >= detail._in && currentFrame < detail._out
          ? '#f0a95d'
          : 'gray'
      }
      className={'mao' + Timelineindex}
    >
        <div
          className={
            currentFrame >= detail._in && currentFrame < detail._out
              ? 'lyric_item active'
              : 'lyric_item'
          }
          onClick={setCurrentTime}
        >
          <div className="lyric_time">
            {win.TimeCodeConvert.l100Ns2Tc$1(detail._in, false)}
          </div>
          <div className="lyric_title">
            {
              currentLanguage.includes(currentLanguage) && 
              (sequencemetaEdit?
              <div className='lyric_title_orignal'>
                {searchVoice(detail.textLanguage[`text_${currentLanguage}`])}
                {permissions.includes(perCfg.resource_edit) && (
                  <span className="icon" onClick={() => setSequencemetaEdit(false)}>
                    <IconFont type="iconbianji17" />
                  </span>
                )
                }
              </div>:
              <div className='editDiv'>
                <Input
                  defaultValue={detail.textLanguage[`text_${currentLanguage}`]}
                  className={'editInput'}
                  ref={sequencemetaName}
                  onPressEnter={e => sequencemetaChangeName(e, detail.guid_)}
                  // onBlur={()=>setSequencemetaEdit(true)}
                />
                <CheckOutlined onClick={(e:any)=>sequencemetaChangeName(e, detail.guid_)}/>
                <CloseOutlined onClick={(e:any)=>{
                  e.preventDefault();
                  e.stopPropagation();
                  setSequencemetaEdit(true);
                }}/>
              </div>
              )
            }

            {/* {
              currentLanguage.includes(currentLanguage) && 
              (sequencemetaTransEdit?
              <div className='lyric_title_translate'>
                {searchVoice(detail.textLanguage[`text_${currentLanguage}`])}
                {permissions.includes(perCfg.resource_edit) && (
                  <span className="icon" onClick={() => setSequencemetaTransEdit(false)}>
                    <IconFont type="iconbianji17" />
                  </span>
                )}
              </div>:
              <div className='editDiv'>
                <Input
                  defaultValue={detail.textLanguage[`text_${currentLanguage}`]}
                  className={'editInput'}
                  ref={sequencemetaTransName}
                  onPressEnter={e => sequencemetaTransChangeName(e, detail.guid_)}
                  // onBlur={()=>setSequencemetaTransEdit(true)}
                />
                <CheckOutlined onClick={(e:any)=>sequencemetaTransChangeName(e, detail.guid_)}/>
                <CloseOutlined onClick={(e:any)=>{
                    e.preventDefault();
                    e.stopPropagation();
                    setSequencemetaTransEdit(true);
                    }}/>
              </div>
              )
            } */}
          </div>
        </div>
    </Timeline.Item>
  );
};
export default TimelineItem;
