.uf-header-wrapper {
  position: relative;
  width: 100%;
  height: 52px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  font-size: 14px;
  color: #282C3C;
  padding: 0 40px 0 20px;
  flex-shrink: 0;
  border-bottom: 1px solid #E2E2E2;
  box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.05);
  z-index: 9;
  .drop-container {
    background: #fff;
    box-shadow: 1px 3px 6px 2px #eee;
    .drop-item {
      padding: 8px 16px;
      cursor: pointer;
      &:hover {
        background: #f9f9f9;
        color: var(--fourth-color);
      }
    }
  }

  .uf-header-left-part {
    display: flex;
    align-items: center;

    .home-part {
      display: flex;
      align-items: center;

      .go-back-btn {
        color: #282C3C;
        font-size: 16px;
        transform: rotate(180deg);
        margin-right: 18px;
      }
    }
    .icon_box{
      height: 60%;
      display: flex;
      padding-right: 18px;
      align-items: center;
      justify-content: center;
    }
    .uf-header-icon {
      //width: 30px;
      height: 32px;
    }

    h2 {
      //color: #ffffff;
      font-size: 20px;
      font-weight: 600;
      padding-left: 20px;
      margin-bottom: 0;
      position: relative;
      &::before {
        content: "";
        display: inline-block;
        height: 40px;
        width: 1px;
        left: 0;
        position: absolute;
        background-color: #cfcfcf;
        top: -6px;
      }
    }

    .vertical-line {
      font-size: 20px;
      border-left-color: #282C3C;
      margin-left: 16px;
      top: 0;
    }

    h4 {
      //color: #ffffff;
      margin-left: 16px;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .uf-header-right-part {
    display: flex;
    align-items: center;
    .ant-badge {
      cursor: pointer;
      margin-right: 20px;
      font-size: 20px;
    }
  }
  .other-nav-wrapper {
    display: flex;
    align-items: center;
    font-size: 14px;
    .ant-dropdown-trigger {
      display: flex;
      height: 32px;
      line-height: 30px;
      padding: 0 18px;
      border-radius: 16px;
      border: 1px solid #CBCBCB;
      margin-right: 40px;
      &:hover{
        color: var(--primary-color);
      }
      .anticon{
        margin-right: 5px;
      }
    }
    .anticon {
      font-size: 18px;
      height: 30px;
      display: flex;
      align-items: center;
    }
  }

  .nav-menu-wrapper {
    display: flex;
    align-items: center;
    margin-right: 33px;
    font-size: 14px;


    a {
      display: inline-block;
      padding: 0 17px;
      color: #666;
      //color: #ffffff;
      margin-left: 6px;
      border-radius: 2px;

      .nav-item {
        line-height: 52px;
        box-sizing: border-box;
        height: 52px;
        border-bottom: 4px solid transparent;
      }


      &:hover, &.active {
        font-weight: 800;
        color: var(--second-color);

        .nav-item {
          border-bottom: 4px solid var(--second-color);
        }
      }


    }

    span {
      margin-left: 6px;
      margin-right: 6px;
      line-height: 21px;
    }
  }

  .user-avatar-wrapper {
    display: flex;
    align-items: center;
    font-size: 14px;
    cursor: pointer;

    .user-name {
      max-width: 80px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      height: 18px;
      line-height: 18px;
      margin: 0;
    }


    .ant-image-img {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      margin-right: 6px;
    }
  }

}

.ant-dropdown-menu-item.ant-dropdown-menu-item-only-child {
  transition: none;
  text-align: center;
  &:hover{
    color: var(--primary-color);
  }
  .anticon{
    margin-right: 5px;
  }
}
.mobileContainer{
  .uf-header-left-part{
    .anticon{
      font-size: 22px;
      margin-right: 10px;
    }
    .home-part{
      .icon_box{
        width: unset !important;
        border: 0;
      }
      h2{
        margin-left: 10px;
      }
    }
  }
}
.message-popover {
  .ant-popover-inner-content {
    padding: 12px 0;
  }
}