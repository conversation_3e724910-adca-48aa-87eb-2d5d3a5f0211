
import DOMPurify from 'dompurify';
import React, { useEffect } from 'react';

const RenderHtml: React.FC<any> = ( props:any) => {
    //监听组件渲染完毕
    useEffect(()=>{
        setTimeout(() => {
            (window as any).MathJax.typeset()
        }, 100);
    },[props.dangerouslySetInnerHTML.__html])

    const createMarkup = (detail:any) => {
      if(detail && detail.indexOf('data-latex') != -1){
        return { __html: detail };
      }else{
        return { __html: DOMPurify.sanitize(detail)};
      }
    };

  if(props.domName == 'p'){
    return (
        <p {...props} ></p>
    );
  }else{
    return (
      <div {...props} dangerouslySetInnerHTML={createMarkup(props.dangerouslySetInnerHTML.__html)} ></div>
    );
  }
};

export default RenderHtml;