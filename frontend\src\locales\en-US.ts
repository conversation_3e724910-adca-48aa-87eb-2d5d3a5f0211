export default {
  'home.title': 'home',
  'search.title': 'retrieval',
  'page-total': '{total} pages in total',
  search: 'search',
  'login.title': 'login',
  all: 'all',
  upload: 'upload',
  'upload-file': 'upload file',
  upload_task: 'upload task',
  'upload-success': 'upload success',
  'upload-error': 'upload error',
  'select-all': 'select all',
  'uploaded-file': 'uploaded file',
  'basic-data': 'basic data',
  'http-error': 'http error,erro code:{code}',
  login: 'login',
  password: 'password',
  username: 'username',
  'remember-password': 'remember password',
  'please-enter-username': '请输入用户名',
  'please-enter-password': '请输入密码',
  state: '状态',
  name: '名称',
  size: '大小',
  catalog: '目录',
  progress_speed: '进度',
  operation: '操作',
  material_name: '素材名',
  extension: '扩展名',
  directory: '所在目录',
  warehousing_person: '上传人',
  teacher: '授课教师',
  storage_time: '上传时间',
  type: '类型',
  download: '下载',
  delete: '删除',
  edit: '编辑',
  refresh: '刷新',
  'create-folder': '新建文件夹',
  'no-verify': '您还有数据没有通过验证',
  'base-message': '基本信息',
  'edit-success': '修改成功',
  'edit-error': '修改失败',
  determine: '确定',
  cancel: '取消',
  'log-out': '退出登录',
  'success-log-out': '退出登录成功',
  'fail-log-out': '退出登录失败',
  'not-supported': '暂不支持的类型',
  'loading-error': '加载出错了',
  'management.metadata.list.header': '元数据',
  'management.metadata.detail.header': '元数据配置',
};
