.entity-img {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  > img {
    max-width: 100%;
    max-height: 100%;
    display: block;
  }
}

.entity-document {
  width: 100%;
  height: ~"calc(100vh - 110px)";
  overflow: auto;
  display: flex;
  justify-content: center;

  .react-pdf__message--loading {
    height: 100%;
  }
}

.entity-pdf,.entity-html {
  width: 100%;
  // height: ~"calc(100vh - 110px)";
  height: 100%;
  overflow: auto;
  display: flex;
  justify-content: center;
  iframe{
    border: none;
  }
}


.entity-wps {
  width: 100%;
  height: ~"calc(100vh - 110px)";
  overflow: auto;
  display: flex;
  justify-content: center;
}

.react-pdf__Document {
  height: 100%;
}

.vjs-remaining-time {
  display: none;
}

.exhibition {
  .video-js {
    max-height: 100%;

    .vjs-current-time,
    .vjs-time-divider,
    .vjs-duration {
      display: block;
    }
  }
}

.document_container {
  width: 100%;
  height: 100%;
  position: relative;
    .watermarkShow {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        pointer-events: none;
        opacity: 0.2;
        font-size: 20px;
        color: #fff;
        z-index: 99;
        overflow: hidden;
        div {
            padding: 50px;
            white-space: nowrap;
            transform: rotate(-30deg);
        }
    }
}