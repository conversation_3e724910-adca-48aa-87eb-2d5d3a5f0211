import http from '../http/http';
import { message } from 'antd';

// * 课程库

export function getHomeworkMapInfo(courseId: string) {
  return http.get(`/exam-api/resource/homework/map-info?courseId=${courseId}`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getHomeworkSubmitInfo(courseId: string) {
  return http.get(`/exam-api/submit/resource/submit-info?courseId=${courseId}`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function createHomework(pageType: 'resource' | 'template', data: any) {
  return http.post(`/exam-api/${pageType}/homework/pre`, data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function updateHomework(
  pageType: 'resource' | 'template',
  resourceHomework: any,
) {
  return http.post(`/exam-api/${pageType}/homework/update`, resourceHomework)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function fetchTopicList(data: any) {
  return http(`/exam-api/examination/list`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
//试题一键绑定
export function bindTopicList(data: any) {
  return http(`/exam-api/examination/point/list`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getHomeworkDetail(
  pageType: 'resource' | 'template',
  homeworkId: string,
  parentId: string | undefined,
  courseId: string,
) {
  return http.get(
    `/exam-api/${pageType}/homework/detail?id=${homeworkId}&courseId=${courseId}${
      parentId ? `&parentId=${parentId}` : ''
    }`,
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function addHomeworkTopic(pageType: 'resource' | 'template', data: any) {
  return http(`/exam-api/${pageType}/homework/question/create/batch`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function updateTopic(
  pageType: 'resource' | 'template',
  id: string,
  data: { sourceId: string },
) {
  return http(`/exam-api/${pageType}/homework/question/${id}/update`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function sortTopic(pageType: 'resource' | 'template', data: any) {
  return http(`/exam-api/${pageType}/homework/question/sort`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getPapers(data: any) {
  return http(`/exam-api/paper/list`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function updateScore(pageType: 'resource' | 'template', data: any) {
  return http(`/exam-api/${pageType}/homework/question/score/batch`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function deleteTopic(pageType: 'resource' | 'template', id: string) {
  return http(`/exam-api/${pageType}/homework/question/${id}/delete`, {
    method: 'POST',
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
export function batchDeleteTopic(pageType: 'resource' | 'template', ids: string[]) {
  return http.post(`/exam-api/${pageType}/homework/question/batch/delete`, ids)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getSubmissions(params: any) {
  return http(`/exam-api/resource/homework/records`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getSubmissionDetail(id: string) {
  return http.get(`/exam-api/resource/homework/record/${id}`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function submitScore(id: string, data: any) {
  return http(`/exam-api/resource/homework/record/${id}/check`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getTemplateHomeworkList(params: any) {
  return http(`/exam-api/template/homework/list`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getStuHomeworkDetail(
  homeworkId: string,
  parentId: string,
  courseId: string
) {
  return http.get(
    `/exam-api/submit/resource/homework?homeworkId=${homeworkId}&courseId=${courseId}${
      parentId ? `&parentId=${parentId}` : ''
    }`,
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getStuSubRecord(homeworkId: string, courseId: string, stuCode?: string) {
  return http.get(`/exam-api/submit/resource/record?homeworkId=${homeworkId}&courseId=${courseId}${stuCode ? `&stuCode=${stuCode}` : ''}`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function uploadFile(data: any) {
  return http.post(`/rman/v1/upload/save/file`, data, {
    headers: { 'Content-Type': 'multipart/form-data' },
  })
    .then(res => {
      if (res?.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}
export function deleteFile(data: any) {
  return http.post(`/rman/v1/recycle/delete?isPublic=true`, data)
    .then(res => {
      if (res?.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

export function submitRecord(data: any) {
  return http(`/exam-api/submit/resource`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function helpSubmitRecord(data: any) {
  return http(`/exam-api/resource/homework/help-submit`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function copyHomework(data: any) {
  return http(`/exam-api/resource/homework/copy`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function deleteHomework(data: any) {
  return http(`/exam-api/resource/homework/delete`, {
    method: 'POST',
    params: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function deleteTempHomework(data: any) {
  return http(`/exam-api/template/homework/delete`, {
    method: 'POST',
    params: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getLearnStatus(data: any) {
  return http('/learn/v1/learningsituation', {
    method: 'GET',
    params: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function updateLearnStatus(data: any) {
  return http(`/learn/v1/learningsituation`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function updateQuote(data: any) {
  return http(`/exam-api/template/homework/quote`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function deleteHomeworks(data: string[]) {
  return http(`/exam-api/resource/homework/batch/delete`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function deleteTempHomeworks(data: string[]) {
  return http(`/exam-api/template/homework/batch/delete`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function deleteHomeworkFile(data: any) {
  return http(`/exam-api/submit/resource/delete/attachment`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function downloadHomeworkFilesCheck(params: any) {
  return http(`/exam-api/resource/homework/download/batch/check`, {
    method: 'GET',
    params,
    // timeout: 3600000,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
export function downloadHomeworkFiles(params: any) {
  return http(`/exam-api/resource/homework/download/batch`, {
    method: 'GET',
    params,
    // timeout: 3600000,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
export function getDownLoadFiles(params: any) {
  return http(`/exam-api/resource/homework/download/batch/schedule`, {
    method: 'GET',
    params,
    // timeout: 3600000,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}


export function downloadSubmissions(params: any) {
  return http(`/exam-api/resource/homework/download`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function dragHomework(data: any) {
  return http(`/exam-api/resource/homework/drag/batch`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function dragHomeworkTemp(data: any) {
  return http(`/exam-api/template/homework/drag/batch`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function queryNoCheckHomework(params: any) {
  return http(`/exam-api/resource/homework/nocheck/num`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function queryHomeworkFallback(params: any) {
  return http(`/exam-api/resource/homework/fallback`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
export function createHomeworkFallback(data: any) {
  return http(`/exam-api/resource/homework/fallback`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function updateHomeworkFallback(data: any) {
  return http(`/exam-api/resource/homework/fallback/update`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function updateReadFallback(data: any) {
  return http(`/exam-api/resource/homework/fallback/status`, {
    method: 'POST',
    params: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function importScores(data: any, params: any) {
  return http(`/exam-api/resource/homework/batch-score`, {
    method: 'POST',
    data,
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
export function exportScoresTpl(params: any) {
  return http(`/exam-api/resource/homework/batch-score/template`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}