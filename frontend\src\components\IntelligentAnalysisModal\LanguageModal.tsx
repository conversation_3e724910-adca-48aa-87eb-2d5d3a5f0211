import React, { FC, useEffect, useState } from 'react';
import {
    Modal,
    Form,
    Input,
    Select,
    DatePicker,
    Button,
    Pagination,
    Checkbox,
    Space,
    message,
    Tree,
} from 'antd';
import { SearchTree } from '@/components';
import searchTreeApis from '@/service/searchTreeApis';
import searchTypes from '@/types/searchTypes';
import { options } from '@/components/search';
import contentListApis from '@/service/contentListApis';
import { MaterialList } from '@/pages/contentlibrary/contentList/type';
import ContentItem from '@/components/ContentItem';
import {
    useIntl
} from 'umi';
const CheckboxGroup = Checkbox.Group;
const {Option} = Select;

interface ILanguageVisible {
    visible: boolean;
    setLanguageModalVisible: (visible: boolean) => void;
    setTargetIds: (targetIds: string[]) => void;
    changeTranslateCheckList: () => void;
    onOk: (item: MaterialList) => void;
}

const PAGE_SIZE = 9;

const LanguageModal: FC<ILanguageVisible> = ({ visible, setLanguageModalVisible, setTargetIds, targetIds, sourceId, setSourceId ,changeTranslateCheckList }) => {
    const [folderId, setFolderId] = useState<any>('');
    const intl = useIntl();
    const [treeData, setTreeData] = useState<searchTypes.IFolder | any>([])
    const [targetData, setTargetData] = useState<searchTypes.IFolder | any>([])
const forTree = (tree: any) => {
    return tree.map((item: any) => {
      return {
        key: item.id,
        title: item.name,
        children: item.childs
          ? forTree(item.childs) : [],
      };
    });
  };
    const fetchTreeData = () => {
        contentListApis.getAllLanguageConfig().then(res => {
            setTreeData(res.extendMessage)
            setTargetData(res.extendMessage[0].childs[0].childs)
            setSourceId(res.extendMessage[0].childs[0].id)
        });
    };


    useEffect(() => {
            fetchTreeData();
    }, []);
    const onCheck: TreeProps['onCheck'] = (checkedKeys, info) => {
        console.log('onCheck', checkedKeys, info);
        setTargetIds(info.checkedNodes.filter(item => item.children.length === 0).map(item => item.key))
      };
    return (
        <Modal
            title='选择语言'
            visible={visible}
            onCancel={() => setLanguageModalVisible(false)}
            footer={
                <Space>
                    <Button onClick={() => setLanguageModalVisible(false) }>{intl.formatMessage({ id: '取消' })}</Button>
                    <Button type="primary" onClick={() => {
                        setLanguageModalVisible(false)
                    }}>
                        {intl.formatMessage({ id: '确定' })}
                    </Button>
                </Space>
            }
        >
            <div className="language-modal-content">
                {/* <Tree
                    checkable
                    onCheck={onCheck}
                    treeData={treeData}
                /> */}
                {/* <div className="left">{treeData[0]?.childs?.map(item => <div className={`left-item ${sourceId === item.id? 'active' : ''}`} onClick={() => {
                    setTargetData(item.childs)
                    setSourceId(item.id)
                    setTargetIds([])
                }}>{item.name}</div>)}</div> */}
                {treeData[0]?.childs.length >0 &&<div className='left'>
                <span>源语言：</span>
                     <Select defaultValue={treeData[0]?.childs[0].id} style={{ width: 100 }} onChange={(e) => {
                        setTargetData(treeData[0]?.childs.find(item => item.id === e)?.childs)
                        setSourceId(e)
                        setTargetIds([])
                    }}>
                        {treeData[0]?.childs?.map(item=>(<Option value={item.id}>{item.name}</Option>) )}
                    </Select>
                </div>}
                <div className="right">
                    <span>目标语言：</span>
                    <CheckboxGroup value={targetIds} onChange={(e) => {
                        setTargetIds(e)
                    }}>
                        {targetData?.map(item => <div className="right-item"><Checkbox value={item.id}>{item.name}</Checkbox></div>)}
                        </CheckboxGroup>
                </div>
            </div>
        </Modal>
    );
};
export default LanguageModal;
