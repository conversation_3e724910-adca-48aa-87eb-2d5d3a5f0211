import React, { FC } from 'react';
import { TreeMenuProps } from '@/components/menuTree/type';
import TreeItem from '@/components/menuTree/treeItem';

/**
 * 处理点击treeItem事件
 * @param e
 */
const handleTreeItemClick = (e: any) => {
  console.log(e);
};

const MenuTree: FC<TreeMenuProps> = props => {
  const { selectedKey, onItemClick, sourceData, operation } = props;
  return (
    <>
      {sourceData.map((item, index) => (
        <TreeItem
          key={index}
          title={item.name}
          onClick={() => onItemClick(item.key)}
          isSelected={selectedKey === item.key}
          isPerson={item.isPerson}
          afterDom={operation}
        >
          {item.children && item.children.length > 0 ? (
            <MenuTree {...props} sourceData={item.children} />
          ) : null}
        </TreeItem>
      ))}
    </>
  );
};

export default MenuTree;
