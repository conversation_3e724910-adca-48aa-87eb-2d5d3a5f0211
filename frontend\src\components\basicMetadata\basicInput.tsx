import React, { FC, useEffect, useState } from 'react';
import { IBasicItemProps } from './basicMetadata';
import { Input, Form } from 'antd';
import FormItem from '../sortForm/formItem';

const BasicInput: FC<IBasicItemProps> = props => {
  const [permission, setPermission] = useState<boolean>(false);
  // 获取当前用户信息
  useEffect(() => {
    if ((window as any).login_useInfo) {
      // console.log('myVideo',props.myVideo)
      // 判断当前用户是否有删除的权限
      const userInfo = (window as any).login_useInfo
      if (userInfo) {
        const roless = userInfo.roles?.map((item: any) => item.roleCode)
        // console.log('juese', roless)
        // 当前用户角色为zhiliao、sys和资源管理员
        if (roless?.includes('admin_S1') || roless?.includes('r_sys_manager') || roless?.includes('r_resource_manager')) {
          setPermission(true)
        } else {
          setPermission(false)
        }
      }
    }
  }, [(window as any).login_useInfo])
  return (
    <Form.Item
      label={props.item.alias}
      name={props.item.fieldName}
      rules={[
        {
          required:
            props.item.isReadOnly || !props.edit
              ? false
              : props.item.isMustInput,
        },
      ]}
      // hasFeedback={props.item.canEdit}
    >
      {props.item.isReadOnly || !props.edit ? (
        <div
          style={{
            lineHeight: '32px',
            wordBreak: 'break-all',
            whiteSpace: 'break-spaces',
            color: '#868686'
          }}
        >
          {props.item.value}
        </div>
      ) : (
        <Input autoComplete={'off'} disabled={(props.path?.includes('录播资源') || props.path?.includes('群组资源'))?(permission?false:props.item.fieldName==='name'):false}/>
      )}
    </Form.Item>
  );
};

export default BasicInput;
