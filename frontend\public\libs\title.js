/*
 * @Author: 李武林
 * @Date: 2022-04-20 11:44:27
 * @LastEditors: 李武林
 * @LastEditTime: 2022-04-21 19:14:34
 * @FilePath: \coursemanger\public\title.js
 * @Description: 
 * 
 * Copyright (c) 2022 by 李武林/索贝数码, All Rights Reserved. 
 */
const dealTitle = function () {
  var lang = localStorage.getItem('lang') || 'zh';
  var theme_config = JSON.parse(localStorage.getItem('theme_config'));
  if (theme_config) {
    setTimeout(() => {
      if (!document.title.includes(theme_config.browserTabAbbreviation) && theme_config.isShow == 1) {
        const browserTabAbbreviation = lang === "zh" ? theme_config.browserTabAbbreviation : theme_config.browserTabAbbreviation_en;
        let title = "";
        if (!document.title) {
          title = browserTabAbbreviation;
        } else if (!browserTabAbbreviation) {
          title = document.title;
        } else {
          title = document.title + "-" + browserTabAbbreviation
        }
        document.title = title;
      }
    }, 200)
  }
};

window.onload = dealTitle;

window.onhashchange = dealTitle;

window.addEventListener('popstate', dealTitle);

window.addEventListener('pushState', dealTitle);

window.addEventListener('replaceState', dealTitle);