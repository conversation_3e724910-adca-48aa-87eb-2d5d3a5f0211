import React, { useState, useEffect } from 'react';
import { Modal, Progress, Button, Input, message, Select, Cascader,Checkbox,Empty,Pagination } from 'antd';
import deleteApis from '@/service/deleteApis';
import './index.less';
import {
  PlusCircleOutlined
} from '@ant-design/icons';
import contentListApis from '@/service/contentListApis';
import { childOrganization, codeOrganization, getAllUserByOrg, initGroupUsers } from '@/service/choicepeople';
import { AddTeacherModal } from '@/components';
import { useHistory } from 'umi';
const { Option } = Select;
const { Search } = Input;
const MobileResourceGroup: React.FC<any> = () => {
  let history: any = useHistory();
  const contentId_ = history.location?.query?.id;
  const [userList, setUserlist] = useState<any>([]);
  const [batchSettingVisible, setBatchSettingVisible] = useState<boolean>(false);
  const [defaultValue, setDefaultValue] = useState<any>(1);
  const [teacherModalVisible, setTeacherModalVisible] = useState<boolean>(false);
  const [indeterminate, setIndeterminate] = useState<boolean>(false);
  const [checkAll, setCheckAll] = useState<boolean>(false);
  const [checkedList, setCheckedList] = useState<any>([]); //选中的列表

  //添加人员
  const [keywords, setKeywords] = useState<string>(''); //检索词
  const [loading, setLoading] = useState<boolean>(false); 
  const [params,setParmas] = useState<any>({
    keyword:'',
    selectCode:'',
    current:1,
    size:30,
    total:0,
  });
  const [teacherList, setTearchlist] = useState<any>([]); //添加人员列表
  const [options, setOptions] = useState<any>([]); //级联菜单树
  const [indeterminate_, setIndeterminate_] = useState<boolean>(false);
  const [checkAll_, setCheckAll_] = useState<boolean>(false);
  const [checkedList_, setCheckedList_] = useState<any>([]); //选中的列表
  useEffect(() => {
    fetchGroupUser();
  }, [])
  useEffect(() => {
    if(teacherModalVisible){
      getRoot();
    }
  }, [teacherModalVisible])
  useEffect(() => {
    if(teacherModalVisible){
      setIndeterminate_(checkedList_.length<teacherList.length && checkedList_.length>0)
      setCheckAll_(checkedList_.length == teacherList.length)
    }
  }, [checkedList_])
  useEffect(() => {
    if(teacherModalVisible){
      fetchAllUser()
    }
  }, [params.keyword,params.selectCode,params.current,params.size])
  const fetchGroupUser = () => {
    initGroupUsers({ contentId: contentId_ }).then((res: any) => {
      const temp = res?.data?.map((item: any, index: number) => {
        return {
          accountshow: item.userCode,
          user_code: item.userCode,
          nick_name: item.userName,
          orgname: item.userOrganizeName,
          jurisdiction: item.all ? 3 : (item.canWrite ? 2 : 1)
        }
      })
      console.log('初始值', temp);
      setUserlist(temp)
    })
  };
  const getRoot = () => {
    codeOrganization('r_teacher').then((res: any) => {
      console.log(res);
      if (res && res.errorCode === 'success') {
        const rootData = res.extendMessage.map((item: any) => {
          return {
            ...item,
            isLeaf:false
          };
        });
        setOptions(rootData);
        setParmas({
          ...params,
          selectCode:rootData?.[0]?.organizationCode
        })
      }
    });
  };
  const loadData = async (selectedOptions: any[]) => {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    targetOption.loading = true;
    const res: any = await childOrganization(targetOption.organizationCode);
      if (res && res.errorCode === 'success') {
        targetOption.loading = false;
        targetOption.children = res.extendMessage.map((item:any)=>({...item,isLeaf:false}));
        targetOption.isLeaf = res.extendMessage.length==0;
        setOptions([...options]);
      }
  };
  // 获取列表
  const fetchAllUser = async () => {
    setLoading(true);
    let param: any = {
      organizationCode: Array.isArray(params.selectCode)?params.selectCode[params.selectCode.length-1]:params.selectCode,
      keyword:params.keyword,
      page: params.current,
      size: params.size,
      include_roles: true,
      fill_detail: false,
      roleCode: "r_teacher,r_offcampusteam,r_course_manager,r_teacher_assistant",
      isIncludeSubUser: true
    };
    const res = await getAllUserByOrg(param);
    if (res && res.errorCode === 'success') {
      let data = res.extendMessage.results;
      data.map((item: any) => {
        item.sexshow = item.extend.sex;
        item.accountshow = item.extend.account;
        item.orgname = item.extend.school+(item.extend.college?'/'+item.extend.college:'');
        item.jurisdiction = 1;
        item.rolelist = item.roles;
        return item;
      });
      setTearchlist(data);
      setParmas({
        ...params,
        total:res.extendMessage.recordTotal
      });
    }
    setLoading(false);
  };
  // 全选
  const onCheckAllChange = (e: any) => {
    setCheckedList(e.target.checked ? userList : []);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  };
  // 全选
  const onCheckAllChange_ = (e: any) => {
    setCheckedList_(e.target.checked ? teacherList : []);
  };
  const checkBoxChange = (e: any) => {
    // console.log('checkBoxChange',e)
    if(e.target.checked){
      const tmep = checkedList.concat([e.target.value]);
      setCheckedList(tmep)
      setIndeterminate(tmep.length<userList.length)
      setCheckAll(tmep.length == userList.length)
    }else{
      const tmep = checkedList.filter((item:any)=>item.accountshow != e.target.value.accountshow);
      setCheckedList(tmep);
      setIndeterminate(tmep.length>0);
      setCheckAll(tmep.length == userList.length);
    }
  };
  const checkBoxChange_ = (e: any) => {
    if(e.target.checked){
      const tmep = checkedList_.concat([e.target.value]);
      setCheckedList_(tmep)
    }else{
      const tmep = checkedList_.filter((item:any)=>item.accountshow != e.target.value.accountshow);
      setCheckedList_(tmep);
    }
  };
  const addUser = () => {
    setTeacherModalVisible(true);
    setCheckedList_(userList);
  };
  const removeUser = (e: any) => {
    console.log(checkedList);
    contentListApis.deletegroupusers({
      contentIds: [contentId_],
      userCodes: checkedList.map((item: any) => item.accountshow)
    }).then((res: any) => {
      if (res?.success) {
        const temp = JSON.parse(JSON.stringify(userList))
        checkedList.forEach((item: any) => {
          temp.forEach((item_: any, index: number) => {
            if (item.user_code === item_.user_code) {
              temp.splice(index, 1);
            }
          })
        })
        console.log('变更hou', temp)
        setUserlist(temp)
        setCheckedList([]) //需要置空 防止立即批量操作权限遗留数据
        setCheckAll(false);
        setIndeterminate(false)
        message.success('移除成功')
      }
    })
  };
  const batchSettingModal = (e: any) => {
    setBatchSettingVisible(true)
  };
  const changeOption = (e: any, row: any) => {
    // console.log('变更前',userList)
    userList.forEach((item: any) => {
      if (item.user_code === row.user_code) {
        item.jurisdiction = e;
      }
    })
    console.log('变更hou',userList)
    setUserlist(userList)
  };
  const changeOptions = (e: any) => {
    console.log(e)
    setDefaultValue(e)
  };
  const onFinish = () => {
    console.log('Success:', userList);
    let userAuthorities: any = [];
    userList.forEach((item: any) => {
      userAuthorities.push({
        userCode: item.user_code,
        userName: item.nick_name,
        // userOrganizeCode:item.extend?.orgcodepath,
        userOrganizeName: item.orgname ? item.orgname : item.extend?.school + (item.extend?.college ? '/' + item.extend?.college : ''),
        canRead: item.jurisdiction >= 1 ? true : false,
        canWrite: item.jurisdiction >= 2 ? true : false,
        canDelete: false,  // 暂时未用到 
        all: item.jurisdiction === 3 ? true : false
      })
    });
    contentListApis.addgroupusers({
      contentIds: [contentId_],
      userAuthorities
    }).then((res: any) => {
      if (res?.success) {
        message.success('保存成功')
        setTimeout(()=>{
          window.close();
        },500)
      } else {
        message.error('保存失败')
      }
    })
  };
  const batchSettingFinish = () => {
    console.log('defaultValue', defaultValue)
    checkedList.forEach((item: any) => {
      userList.forEach((item_: any) => {
        if (item.user_code === item_.user_code) {
          item_.jurisdiction = defaultValue
        }
      })
    })
    console.log('变更完后', userList)
    setUserlist(userList)
    setBatchSettingVisible(false)
  };
  const search = (e:any)=>{
    setParmas({
      ...params,
      keyword:e
    })
  }
  const selectConfirm = ()=>{
    console.log('checkedList_',checkedList_)
    setUserlist(checkedList_);
    setTeacherModalVisible(false);
  }
  return (
    <div className='mobileGroupResource'>
      {
        teacherModalVisible?
        <div className='addTeacher'>
          <div className='top'>
            <Button
              onClick={()=>setTeacherModalVisible(false)}
            >
              返回
            </Button>
            <Search 
              placeholder="请输入姓名/学号" 
              onSearch={search} 
              style={{ width: 200 }} />
            <Button
              disabled={checkedList_.length === 0}
              onClick={selectConfirm}
              type='primary'
            >
              添加
            </Button>
          </div>
          <div className='cascader'>
            <Checkbox
              indeterminate={indeterminate_}
              onChange={onCheckAllChange_}
              checked={checkAll_}
            ></Checkbox>
            <Cascader 
              options={options} 
              key={options}
              fieldNames={{
                label:'organizationName',
                value:'organizationCode',
              }} 
              getPopupContainer={(e:any)=>e.parentElement}
              loadData={loadData}
              onChange={(e)=>{
                setParmas({
                  ...params,
                  selectCode:e||''
                })
              }}
              changeOnSelect
              defaultValue={[options?.[0]?.organizationCode]} />
          </div>
          <div className='center'>
            {
              teacherList.length>0?
              <>
                {
                  teacherList.map((item:any)=>{
                    return <div className='item_' key={item.accountshow}>
                      <Checkbox value={item} 
                        checked={checkedList_.some((item_:any)=>item_.accountshow==item.accountshow)} 
                        onChange={checkBoxChange_}></Checkbox>
                      <div>
                        <label>学工号</label>
                        <span>{item.accountshow}</span>
                      </div>
                      <div>
                        <label>姓名</label>
                        <span>{item.nick_name}</span>
                      </div>
                      <div>
                        <label>所属组织</label>
                        <span>{item.orgname || '--'}</span>
                      </div>
                    </div>
                  })
                }
              </>
              :<Empty/>
            }
          </div>
          <div className='bottom'>
            <Pagination
              size='small'
              total={params.total}
              current={params.current}
              showTotal={total => intl.formatMessage({
                id: "共条"
              },{total})}
              showSizeChanger= {true}
              showQuickJumper= {true}
              onChange={(page: number,size:any) =>{
                setParmas({
                  ...params,
                  current:page,
                  size
                })}
              }
              defaultPageSize= {params.size}
            />
          </div>
        </div>:
        <>
        <div className='top'>
          <Checkbox
            indeterminate={indeterminate}
            onChange={onCheckAllChange}
            checked={checkAll}
          >
          </Checkbox>
          <Button
            type='primary'
            onClick={addUser}
          >
            <PlusCircleOutlined />
            添加
          </Button>
          <Button
            disabled={checkedList.length === 0}
            onClick={removeUser}
          >
            移除
          </Button>
          <Button
            disabled={checkedList.length === 0}
            onClick={batchSettingModal}
          >
            批量配置
          </Button>
          <Button
            // disabled={checkedList.length === 0}
            onClick={onFinish}
          >
            保存
          </Button>
        </div>
        <div className='center' key={JSON.stringify(userList)}>
          {
            userList.length>0?
            userList.map((item:any)=>{
              return <div className='item_' key={item.accountshow}>
                <Checkbox value={item} 
                  checked={checkedList.some((item_:any)=>item_.accountshow==item.accountshow)} 
                  onChange={checkBoxChange}></Checkbox>
                <div>
                  <label>学工号</label>
                  <span>{item.accountshow}</span>
                </div>
                <div>
                  <label>姓名</label>
                  <span>{item.nick_name}</span>
                </div>
                <div>
                  <label>所属组织</label>
                  <span>{item.orgname || '--'}</span>
                </div>
                <div>
                  <label>权限</label>
                  <Select size='small' defaultValue={item.jurisdiction} key={item.jurisdiction} onChange={(e: any) => changeOption(e, item)}>
                    <Option value={1}>仅使用</Option>
                    <Option value={2}>编辑者</Option>
                    <Option value={3}>管理员</Option>
                  </Select>
                </div>
              </div>
            })
            :<Empty/>
          }
        </div>
        </>
      }
      <Modal
          className='batchSetting'
          title={'批量设置权限'}
          visible={batchSettingVisible}
          onCancel={() => setBatchSettingVisible(false)}
          footer={[
            <Button
              type='primary'
              onClick={batchSettingFinish}
            >
              确认
            </Button>
          ]}
          width={400}
        >
          <div>
            <span>已选{checkedList.length}人</span>
          </div>
          <div>
            <Select style={{ width: '100%' }} defaultValue={defaultValue} onChange={changeOptions}>
              <Option value={1}>仅使用</Option>
              <Option value={2}>编辑者</Option>
              <Option value={3}>管理员</Option>
            </Select>
          </div>
        </Modal>
    </div>
  );
};

export default MobileResourceGroup;