export default {
  'GET /api/get-entity': {
    data: {
      // 视频
      path:
        'http://hive.cetv.cn:19100/bucket-p/u-89kr94n8f7gm5hlw/2020/08/13/af95304a0dc042cdb47e0fe9e4fe6cb5_videogroup_0a840ac26e9348939ab9d481e02ee1e6_000.mp4',
      keyframes:
        'http://hive.cetv.cn:19100/bucket-k/u-en8iqw8bu91ixm8y/2020/08/13/7b1597b069bf44a1b59b9f55e8b625c4_videogroup_440_247_0.jpg',
      type: 'video',
      name: '测试视频',
      entityData: [
        {
          title: '标题',
          value: '测试视频',
          canEdit: false,
          type: 1,
          mustInput: true,
          filedName: 'title',
        },
        {
          title: '测试数据',
          value: '测试视频',
          canEdit: true,
          type: 1,
          mustInput: true,
          filedName: 'data',
        },
        {
          title: '测试数据',
          value: '测试视频',
          canEdit: true,
          type: 1,
          mustInput: true,
          filedName: 'data1',
        },
      ],
    },
    // data:{ // 音频
    //     path:'http://hive.cetv.cn:19100/bucket-p/u-yc8058ett2816715/2020/08/10/cc216a636cc343f0ac5f71846929c237_audiogroup_a218364eceb84b3c8b850e455d7004a0_000.mp3',
    //     keyframes:'http://hive.cetv.cn:19100/bucket-k/u-en8iqw8bu91ixm8y/2020/08/13/7b1597b069bf44a1b59b9f55e8b625c4_videogroup_440_247_0.jpg',
    //     type:'audio'
    // },
    // data:{ // 图片
    //     path:'http://hive.cetv.cn:19100/bucket-p/u-yc8058ett2816715/streamings/biz_sobey_picture/2020/08/10/70627e095e50477e961fcad54271fbc3.jpg',
    //     type:'picture'
    // },
    // data:{ // 文档
    //     path:'http://hive.cetv.cn:19100/bucket-p/u-yc8058ett2816715/10-1aaaaa.pdf',
    //     type:'document'
    // },
    success: true,
  },
};
