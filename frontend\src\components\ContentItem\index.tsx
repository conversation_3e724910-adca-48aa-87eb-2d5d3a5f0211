import React, {
  FC,
  useState,
  useRef,
  useEffect,
  KeyboardEvent,
  FocusEvent,
} from 'react';
import {
  message,
  Input,
  Checkbox,
  Tooltip,
  Button,
  Form,
  Radio,
  Popconfirm,
  Popover,
} from 'antd';
import {
  formatMessage,
  useIntl,
  useSelector,
  useDispatch,
  useHistory,
} from 'umi';
import Icon, {
  ArrowDownOutlined,
  EllipsisOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import './index.less';
import contentListApis from '@/service/contentListApis';
import DeleteModal from '../deleteModal';
import searchTypes from '@/types/resourcesType';
import Keyframe from '../keyframe/keyframe';
import {
  changesize,
  l100Ns2Tc$1,
  operateCode,
  sleep,
  timeTransfer,
} from '@/utils';
import { IconFont } from '@/components/iconFont/iconFont';
import { IPermission } from '@/models/permission';
import perCfg from '@/permission/config';
import globalParams, { ModuleCfg } from '@/permission/globalParams';
import BindKnowledgeMap from '../bindKnowledgeMap';
import ResourceGroupModal from '../resourceGroupModal';
import ShareModal from '../shareModal';
import CopyAndMoveModal from '../CopyAndMoveModal';
import { ReactComponent as cancelshare_icon } from '@/images/icons/cancelshare.svg';
import { ReactComponent as link_icon } from '@/images/icons/link.svg';
import { ReactComponent as remove_icon } from '@/images/icons/remove.svg';
import TextclipModal from '../textclipModal';
import entityApis from '@/service/entityApis';
import Auditlogs from '@/pages/contentlibrary/contentDetail/components/Auditlogs';
import CoverModal from '@/components/CoverModal';
import MajorTypeModal from '../MajorTypeModal';
import { getSensitiveWord } from '@/utils';
import progressApis from '@/service/progress';
import { initGroupUsers } from '@/service/choicepeople';
const options: { [propName: string]: string } = {
  biz_sobey_other: '其他',
  biz_sobey_audio: '音频',
  biz_sobey_video: '视频',
  biz_sobey_picture: '图片',
  biz_sobey_document: '文档',
  folder: '文件夹',
};

interface ContentData {
  modal: boolean;
  detail: any; // 详情数据
  goDetail?: (type: any, item?: any) => void;
  refresh?: (page?: any, key?: any, name?: any) => Promise<any>;
  setallrename?: (item: boolean) => void;
  analysisCallback?: (item: any) => void;
  downloadBoxOpen?: () => void;
  setCoverOpen?: () => void;
  revoke?: (item: any) => void;
  pass?: (flag: boolean, item?: any) => void;
  setInnerItem?: (item: any) => void;
  publish?: (item: any) => void;
  remove?: (item: any) => void;
  batchOperate?: (item: any, type: number) => void;
  recycleBin: boolean;
  columns: string[];
  hideBtn?: boolean;
  publishManagement?: string;
  myCollectBin?: boolean;
  resourceGroup?: boolean; // 代表群组资源
  myVerify?: string;
  myShare?: any;
  searchVoiceWord: string;
  selected?: any;
  shareEnble?: boolean;
  myVideo?: boolean;
  shareDetailHidden?: boolean;
  myVideoDetailFilter?: boolean;
  downEnable?: number;
  noCheckbox?: boolean;
  showNoBtn?: boolean
  searchMyVideoDetail?: any;
  setPublishVisible?: (visible: boolean) => void;
  getDetail?: (detailItem: any) => void;
  getFolderObj: (msg: any) => void;
  setDownloadFolderVisible: (item: any) => void;
  allList?: any;
  setAllList?: (item: any) => void;
  index?: number;
  searchMyVideoDetail?: any;
  classifiedByschedule?: boolean;
  mineVerify?: boolean
  setCheckedList?: (data: any) => void;
}

const ContentItem: React.FC<ContentData> = props => {
  const LinkIcon = (props: any) => <Icon component={link_icon} {...props} />;
  const {setCheckedList} = props
  const intl = useIntl();
  const CancelshareIcon = (props: any) => (
    <Icon component={cancelshare_icon} {...props} />
  );
  const RemoveIcon = (props: any) => (
    <Icon component={remove_icon} {...props} />
  );
  // export default {
  //   link:<LinkIcon></LinkIcon>,
  //   share:<CancelshareIcon/>
  // }
  let history: any = useHistory();
  let target = history.location?.query?.target || ''
  const joveonetype = [
    'biz_sobey_video',
    'biz_sobey_audio',
    'biz_sobey_picture',
  ];
  const {
    modal,
    detail = {},
    goDetail = (type: any) => Promise.resolve(),
    refresh = () => Promise.resolve(),
    setallrename,
    analysisCallback,
    downloadBoxOpen,
    revoke,
    pass,
    remove,
    publish,
    allList,
    setAllList,
    index,
    searchMyVideoDetail,
    setInnerItem,
    setCoverOpen,
    recycleBin,
    searchVoiceWord,
    myCollectBin,
    resourceGroup,
    myVerify,
    columns,
    hideBtn,
    shareEnble,
    shareDetailHidden,
    downEnable,
    selected,
    myShare,
    publishManagement,
    myVideoDetailFilter,
    showNoBtn,
    myVideo,
    noCheckbox,
    batchOperate,
    setPublishVisible,
    getDetail,
    getFolderObj,
    setDownloadFolderVisible,
      classifiedByschedule,
      mineVerify,
  } = props;

  const [rename, setRename] = useState<boolean>(false); // 重命名
  const [deleteShow, setDeleteShow] = useState<boolean>(false);
  const [deleteModal, setDeleteModal] = useState<boolean>(false);
  const [resourceGroupModal, setResourceGroupModal] = useState<boolean>(false);
  const [shareVisible, setShareVisible] = useState<boolean>(false);
  const [videoVisible, setVideoVisible] = useState<boolean>(false)
  const [videoSeat, setVideoSeat] = useState<any>({})
  const [videoPath, setVideoPath] = useState<string>('')
  const videoRef = useRef<any>(null);
  const [auditlogsVisible, setAuditlogsVisible] = useState<boolean>(false);
  const [userList, setUserList] = useState<string[]>([]) // 成员列表
  const isShowGroupSetting = detail?.operateCode_ == 15 || false; // 是否显示群组资源设置中的部分功能
  const inputRef = useRef<any>(null); const contentitemBox: HTMLDivElement | null = document.querySelector(
    '.contentitem_box',
  );
  const [bindKnowledgeModalVisible, setBindKnowledgeModalVisible] = useState<
    boolean
  >(false);
  const hidecatalogue =
    JSON.stringify(history.location.query) === '{}'
      ? '0'
      : history.location.query.hidecatalogue;
  const dispatch = useDispatch();
  const Secondarydirectory = useSelector<{ jurisdiction: any }, any>(
    ({ jurisdiction }) => {
      return jurisdiction.Secondarydirectory;
    },
  );
  // 权限
  const { modules, permissions, rmanGlobalParameter, parameterConfig } = useSelector<
    { permission: any },
    IPermission
  >(({ permission }) => permission);
  const { mobileFlag } = useSelector<{ config: any }, any>(
    ({ config }) => config,
  );
  const [copyAndMoveModallVisible, setCopyAndMoveModalVisible] = useState<
    boolean
  >(false);
  const [moreMenuVisible, setMoreMenuVisible] = useState<boolean>(false);
  const [operatType, setOperatType] = useState<number>(0);
  const [textclipLists, setsetTextclipLists] = useState<any>([]);
  const [textclipVisible, setTextclipVisible] = useState<boolean>(false);
  const [coverModalVisible, setCoverModalVisible] = useState<boolean>(false);
  const [majoryTypeModalVisible, setMajoryTypeModalVisible] = useState<boolean>(false);
  useEffect(() => {
    if (rename) {
      inputRef.current?.focus();
    }
  }, [rename]);
  useEffect(() => {
    if (videoVisible && detail.type_ === 'biz_sobey_video') {
      contentListApis.getFileInfo([detail.contentId_]).then(res => {
        if (res?.success) {
          setVideoPath(res?.data[0]?.downloadAddress[0])
          videoRef?.current?.play();
        }
      })
    }
  }, [videoVisible])
  useEffect(() => {
    // 判断是否有删除权限
    setDeleteShow(operateCode(detail.operateCode_).includes(8));
    // console.log(downEnable, myShare)
    // buttonInit();
  }, []);
  // 重命名
  const refilename = () => {
    setRename(true);
    setallrename && setallrename(true);
  };
  const gojove = () => {
    entityApis.joveoneLog(detail.contentId_, detail.name_);
    window.open(`/joveone/?contentid=${detail.contentId_}&siteCode=S1`);
  };
  const fetchTextclipList = async () => {
    let result: any = [];
    const res: any = await contentListApis.getTextclipLists({
      contentId: detail.contentId_,
      pageIndex: 1,
      pageSize: 10, //当前仅展示前十个
    });
    console.log(res);
    if (res?.statusCode === 200) {
      result = res.data.items;
    } else {
      message.error(intl.formatMessage({ id: '任务列表接口错误' }));
    }
    return result;
  };
  const newTextClip = async () => {
    //需要创建任务
    message.loading(intl.formatMessage({ id: '正在创建语音任务...请稍等' }));
    const res: any = await contentListApis.createTextClip({
      contentId: detail.contentId_,
      keyFrames: detail.keyframe_,
      taskName: detail.name_,
    });
    if (res?.statusCode === 200) {
      if (res.data) {
        window.location.href = `${window.location.origin}/textclip/#/clip/textClipDetail/${res.data}`;
      } else {
        message.error(res.message);
      }
    } else {
      message.error(intl.formatMessage({ id: '服务器错误' }));
    }
  };
  const goTextClip = async () => {
    // 创建任务前 先查询当前素材有无语音任务
    const list: any = await fetchTextclipList();
    console.log('语音任务', list);
    if (list.length > 0) {
      //打开列表对话框
      setsetTextclipLists(list);
      setTextclipVisible(true);
    } else {
      newTextClip();
    }
  };
  const findKeyWord = (txt: string) => {
    // 聚焦元素8
    const match = searchVoiceWord
    const reg = new RegExp(match.split('').join('|'), 'g')
    //内容超长了在前面加省略号
    let str = txt.trim()
    let arr = str.replace(reg, '***').split('***')
    // console.log(arr.length,arr[0],arr[0].length,maxLength)
    //关键词被省略看不见
    if (arr.length > 1 && arr[0].length >= 6) {
      let arr0 = arr.shift()
      if ((match + arr.join(match)).length >= 6) {
        //关键词后的内容足够长，删除前面内容
        txt = str.replace(arr0, '...')
      } else {
        //删除前面部分文字使关键词显示出来
        txt = str.replace(arr0, ('...' + arr0.slice(arr0.length + match.length - 6 + 1)))
      }
    }
    // txt = txt.substring(0, 6)

    let res = ''
    if (txt) {
      res = txt.replace(reg, item => {
        return `<span class="resource-search-value">${item}</span>`
      })
    }
    return res
  }
  //高亮检索
  const searchVoice = (tit: string) => {
    const index = tit?.indexOf(searchVoiceWord);
    const beforeStr = tit?.substr(0, index);
    const afterStr = tit?.substr(index + searchVoiceWord?.length);
    const title =
      index > -1 ? (
        <>
          <span className="title_">
            {beforeStr}
            <span className="resource-search-value">{searchVoiceWord}</span>
            {afterStr}
          </span>
          {detail.type_ !== 'folder' && ['录播资源'].includes(selected) && <ArrowDownOutlined onClick={(e) => {
            e.stopPropagation()
            opendownloadbox()
          }} className="icon" />}
        </>
      ) : (
        <>
          <span className="title_">{tit}</span>
          {detail.type_ !== 'folder' && ['录播资源'].includes(selected) && <ArrowDownOutlined onClick={(e) => {
            e.stopPropagation()
            opendownloadbox()
          }} className="icon" />}
        </>
      );
    return title;
  };
  const changeName = async (
    e: KeyboardEvent<HTMLInputElement> | FocusEvent<HTMLInputElement> | any,
  ) => {
    e.preventDefault();
    let name = e.target.value;
    if (detail.name_ === name) {
      //未做变更
      setRename(false);
      setallrename && setallrename(false);
      return;
    }
    if (name === '') {
      message.error(intl.formatMessage({ id: '文件或文件夹姓名不能为空' }));
      return;
    }
    let re = /^[^#\x22]*$/;
    if (!re.test(name) && detail.type_ === 'folder') {
      message.error(intl.formatMessage({ id: '文件夹姓名中不能包含' }));
      return;
    }
    if (detail.type_ === 'folder') {
      getSensitiveWord(name, '文件夹名', async () => {
        contentListApis
          .renamefolder({
            resourceId: detail.contentId_,
            newName: name,
            oldName: detail.name_,
          })
          .then(async res => {
            if (res && res.success && res.data) {
              message.success(intl.formatMessage({ id: '重命名成功' }));
              // await refresh('', detail.contentId_, name);
              await refresh('', {
                name: '重命名',
                contentId_: detail.contentId_,
                newName: name,
              });
            } else {
              message.error(intl.formatMessage({ id: '重命名失败' }));
              await refresh();
            }
          });
      });
    } else {
      // 资源是否为公共资源
      let ispublic = detail?.tree_?.[0]?.includes('public');
      getSensitiveWord(name, intl.formatMessage({ id: '文件名' }), async () => {
        contentListApis
          .renameentity(
            {
              resourceId: detail.contentId_,
              newName: name,
              oldName: detail.name_,
            },
            ispublic,
          )
          .then(async res => {
            if (res && res.success && res.data) {
              message.success(intl.formatMessage({ id: '重命名成功' }));
              await refresh('', detail.contentId_, name);
            } else {
              message.error(intl.formatMessage({ id: '重命名失败' }));
              await refresh();
            }
          });
      });
    }
    setRename(false);
    setallrename && setallrename(false);
  };

  // 下载文件
  const opendownloadbox = (entityData?: any) => {
    dispatch({
      type: 'download/changedownload',
      payload: {
        value: [entityData || detail],
      },
    });
    downloadBoxOpen && downloadBoxOpen();
  };

  // 文件夹下载
  function downloadFolder() {
    // 直接调用 showDirectoryPicker 方法让用户选择下载文件夹
    // const directoryHandle = await (window as any).showDirectoryPicker();
    // console.log(directoryHandle, '用户选择的下载文件夹句柄');
    const params = {
      folderPath: detail?.tree_[0] + '/' + detail?.name_,
      folderName: detail?.name_
    }
    progressApis.folderDownload(params).then((res: any) => {
      if (res?.success) {
        setDownloadFolderVisible(true)
        getFolderObj({taskId: res?.data, fileName: detail?.name_})
      }
    })
  }

  // 获取最后一级的路径
  const getdirectory = (tree: any): string => {
    const directory = tree[0].split('/');
    if (directory.length > 0) {
      let str = directory[directory.length - 1];
      if (str.includes('public')) {
        return '公共资源';
      } else if (str.includes('private') || str === (window as any).login_useInfo.userCode) {
        return '个人资源';
      }
      else {
        return directory[directory.length - 1];
      }
    }
    return '';
  };
  // 路径转译
  const gettip = (tree: any): string => {
    const directory = tree[0];
    if (directory.includes('global_sobey_defaultclass/public')) {
      return directory.replace('global_sobey_defaultclass/public', '公共资源');
    } else if (directory.includes('global_sobey_defaultclass/private')) {
      return directory.replace('global_sobey_defaultclass/private', '个人资源');
    } else {
      return directory;
    }
  };
  //复制链接 提取码
  const copylinkurl = () => {
    const input = document.createElement('input');
    input.setAttribute('readonly', 'readonly');
    const value =
      detail.linkPassword === ''
        ? `${intl.formatMessage({ id: '链接' })}：${window.location.origin}/rman/#/basic/shareDetail/${detail.link}_0`
        : `${intl.formatMessage({ id: '链接' })}：${window.location.origin}/rman/#/basic/shareDetail/${detail.link}_1     ${intl.formatMessage({ id: '获取密码' })}：${detail.linkPassword}`;
    input.setAttribute('value', value);
    document.body.appendChild(input);
    input.select();
    if (document.execCommand('copy')) {
      document.execCommand('copy');
      message.success(intl.formatMessage({ id: '复制成功' }));
    } else {
      message.error(intl.formatMessage({ id: '复制失败' }));
    }
    document.body.removeChild(input);
  };
  //移动
  const copyAndMove = (item: number) => {
    setOperatType(item);
    setCopyAndMoveModalVisible(true);
  };
  const permiseifshow = () => {
    if (
      rmanGlobalParameter.includes(globalParams.speech_analysis_display) ||
      rmanGlobalParameter.includes(globalParams.knowledge_analysis_display) ||
      rmanGlobalParameter.includes(
        globalParams.sensitiveface_analysis_display,
      ) ||
      rmanGlobalParameter.includes(globalParams.smart_tag_display)
    ) {
      //2022-2-24 统一了规则：如果模块配置有其一则显示智能按钮 但如果没有智能分析权限 则按钮置灰
      return true;
    }
    return false;
  };
  // 点赞 为true 点赞 否则取消点赞
  const resourceLikeChange = (flag: boolean) => {
    if (flag) {
      entityApis.resourceLike(detail.contentId_).then(async (res: any) => {
        if (res.success) {
          message.success(intl.formatMessage({ id: '点赞成功' }));
          await refresh('', { name: '点赞', lists: [detail.contentId_] });
        } else {
          message.error(intl.formatMessage({ id: '点赞失败' }));
        }
      });
    } else {
      entityApis.resourceCancleLike(detail.contentId_).then(async (res: any) => {
        if (res.success) {
          message.success(intl.formatMessage({ id: '取消点赞成功' }));
          await refresh('', { name: '取消点赞', lists: [detail.contentId_] });
        } else {
          message.error(intl.formatMessage({ id: '取消点赞失败' }));
        }
      });
    }
  };
  // 处理单个课程项选中的函数
  const handleCourseChange = (event: any, index: any) => {
    const updatedCourses = [...allList];
    const course = updatedCourses[index];
    const isCourseSelected = event.target.checked;
    updatedCourses[index] = {
      ...course,
      allVideosSelected: isCourseSelected
    };

    // 更新视频选中状态
    // 如果课程被选中，确保所有视频也被选中
    updatedCourses[index].videoSearchResponses = course.videoSearchResponses.map((video: any) => ({
      ...video,
      isSelected: isCourseSelected
    }));
    setAllList(updatedCourses);
  };

  // 处理单个视频选中的函数（可选，如果你需要单独控制视频的选中状态）
  const handleVideoChange = (event: any, courseIndex: any, videoIndex: any) => {
    const updatedCourses = [...allList];
    const course = updatedCourses[courseIndex];
    const isSelected = event.target.checked;
    course.videoSearchResponses[videoIndex] = { ...course.videoSearchResponses[videoIndex], isSelected }
    // 更新课程的allVideosSelected状态
    const allVideosSelected = course.videoSearchResponses.every(v => v.isSelected);
    updatedCourses[courseIndex].allVideosSelected = allVideosSelected
    // if (allVideosSelected && !course.isSelected) {
    //   updatedCourses[courseIndex] = { ...updatedCourses[courseIndex], isSelected: true };
    // }
    // else{
    //   updatedCourses[courseIndex] = { ...updatedCourses[courseIndex], isSelected: false };
    // }
    // 如果课程被选中但所有视频没有被选中，取消allVideosSelected（这步其实在上面已经处理了，因为上面已经更新了allVideosSelected）

    setAllList(updatedCourses);
  };

  const publishResource = () => {
    const params = [
      {
        "resourceId": detail?.contentId_,
        "resourceName": detail?.name_,
        "directory": detail.tree_[0]        ,
        "createUserOrg": (window as any).login_useInfo.extend.orgcodepath,
        "resourceType": detail?.type_,
        "fileSize": detail?.filesize,
        "keyframe": detail?.keyframe_
      }
    ]
    contentListApis.publishResource(params).then(res => {
      if (res?.success) {
        message.success('发起成功');
        const randomInteger = Math.floor(Math.random() * 1000000) + 1;
        sessionStorage.setItem('publishResource', randomInteger.toString());
        setCheckedList && setCheckedList([])
      }
    })
  }
  // 发布至专题
  const publishTopicResource = () => {
    setPublishVisible?.(true)
    const params = {
      "resourceId": detail?.contentId_,
      "resourceName": detail?.name_,
      "directory": detail.tree_[0]        ,
      "createUserOrg": (window as any).login_useInfo.extend.orgcodepath,
      "resourceType": detail?.type_,
      "fileSize": detail?.filesize,
      "keyframe": detail?.keyframe_
    }
    getDetail?.(params)
  }
  //新样式的按钮顺序 点赞、下载、分享、收藏、重命名、复制、移动、剪辑、只能分析、绑定地图、删除；
  // const buttonInit = ()=>{
  let buttonList: any = [];
  if (!rename) { // 代表不是重命名
    //我的审核
    if (myVerify) {

      //在待我审核内部
      if (myVerify == '0-1') {
        buttonList.push({
          title: intl.formatMessage({ id: '审核日志' }),
          func: () => setAuditlogsVisible(true),
          icon: (
            <div onClick={() => setAuditlogsVisible(true)}>
              <Tooltip title={intl.formatMessage({ id: '审核日志' })}>
                <FileTextOutlined className="icon" />
              </Tooltip>
            </div>
          ),
        });
      } else if (myVerify == '1-1') {
        //在我发起审核内部
        buttonList.push({
          title: intl.formatMessage({ id: '撤回' }),
          func: () => revoke?.(detail),
          icon: (
            <div onClick={() => revoke?.(detail)}>
              <Tooltip title={intl.formatMessage({ id: '撤回' })}>
                <IconFont type="iconrecall" className="icon" />
              </Tooltip>
            </div>
          ),
        });
        buttonList.push({
          title: intl.formatMessage({ id: '审核日志' }),
          func: () => setAuditlogsVisible(true),
          icon: (
            <div onClick={() => setAuditlogsVisible(true)}>
              <Tooltip title={intl.formatMessage({ id: '审核日志' })}>
                <FileTextOutlined className="icon" />
              </Tooltip>
            </div>
          ),
        });
      } else if(myVerify === 'verifyToMe'){
          if(detail.auditStateName === '待审核' && !mineVerify){
            buttonList.push({
              title: intl.formatMessage({ id: '通过' }),
              func: () => pass?.(true, [detail]),
              icon: (
                <div onClick={() => pass?.(true, [detail])}>
                  <Tooltip title={intl.formatMessage({ id: '通过' })}>
                  <IconFont type="iconrecall" className="icon" />
                  </Tooltip>
                </div>
              )
            })
            buttonList.push({
              title: intl.formatMessage({ id: '驳回' }),
              func: () => {
                pass?.(false, [detail])
                setInnerItem?.(detail)
              },
              icon: (
                <div onClick={() =>  {
                  pass?.(false, [detail])
                  setInnerItem?.(detail)
                }}>
                  <Tooltip title={intl.formatMessage({ id: '驳回' })}>
                    <FileTextOutlined className="icon" />
                  </Tooltip>
                </div>
              ),
            })}
          if (mineVerify  &&( detail.auditStateName === '待审核' || detail.auditStateName === '审核中') ) {
            buttonList.push({
              title: intl.formatMessage({ id: '通过' }),
              func: () => pass?.(true, [detail],0),
              icon: (
                <div onClick={() => pass?.(true, [detail],0)}>
                  <Tooltip title={intl.formatMessage({ id: '通过' })}>
                  <IconFont type="iconrecall" className="icon" />
                  </Tooltip>
                </div>
              )
            })
            buttonList.push({
              title: intl.formatMessage({ id: '驳回' }),
              func: () => {
                pass?.(false, [detail], 1)
                setInnerItem?.(detail)
              },
              icon: (
                <div onClick={() =>  {
                  pass?.(false, [detail], 1)
                  setInnerItem?.(detail)
                }}>
                  <Tooltip title={intl.formatMessage({ id: '驳回' })}>
                    <FileTextOutlined className="icon" />
                  </Tooltip>
                </div>
              ),
            })
          }
          { ['已驳回','已下架'].includes(detail.auditStateName ) && !mineVerify && 
          buttonList.push({
            title: intl.formatMessage({ id: '发布' }),
            func: () => publish?.([detail]),
            icon: (
              <div onClick={() => publish?.([detail])}>
                <Tooltip title={intl.formatMessage({ id: '发布' })}>
                  <FileTextOutlined className="icon" />
                </Tooltip>
              </div>
            ),
          })}
          { ['已通过','已发布'].includes(detail.auditStateName ) && !mineVerify &&
          buttonList.push({
            title: intl.formatMessage({ id: '下架' }),
            func: () => remove?.([detail]),
            icon: (
              <div onClick={() => remove?.([detail])}>
                <Tooltip title={intl.formatMessage({ id: '下架' })}>
                  <FileTextOutlined className="icon" />
                </Tooltip>
              </div>
            ),
          })}
          if (mineVerify && detail.auditStateName == '已通过') {
            buttonList.push({
              title: intl.formatMessage({ id: '下架' }),
              func: () => remove?.([detail]),
              icon: (
                <div onClick={() => remove?.([detail])}>
                  <Tooltip title={intl.formatMessage({ id: '下架' })}>
                    <FileTextOutlined className="icon" />
                  </Tooltip>
                </div>
              ),
            })
          }
      }
    } else {
      if (detail.type_ === 'folder' && !recycleBin) { // 代表是文件夹且不是回收站
        if (!Secondarydirectory) {
          if (
            rmanGlobalParameter.includes(globalParams.my_shared_display) &&
            shareEnble && detail.isDelete !== 1 && !detail.damaged && !myCollectBin) {
            buttonList.push({
              title: intl.formatMessage({ id: '分享' }),
              func: () => setShareVisible(true),
              icon: (
                <div onClick={() => setShareVisible(true)}>
                  <Tooltip title={intl.formatMessage({ id: '分享' })}>
                    <IconFont type="icona-xingzhuangbeifen2" className="icon" />
                  </Tooltip>
                </div>
              ),
            });
          }
        }
        if ( permissions.includes(perCfg.resource_download) ) {
            buttonList.push({
              title: intl.formatMessage({ id: '下载' }),
              func: downloadFolder,
              icon: (
                <div>
                  <Tooltip title={intl.formatMessage({ id: '下载' })}>
                    <ArrowDownOutlined className="icon" />
                  </Tooltip>
                </div>
              ),
            });
        }
        if (
          permissions.includes(perCfg.resource_edit) &&
          operateCode(detail.operateCode_).includes(2) &&
          !myCollectBin
        ) {
          buttonList.push({
            title: intl.formatMessage({ id: '重命名' }),
            func: () => refilename(),
            icon: (
              <div onClick={refilename} key="11">
                <Tooltip title={intl.formatMessage({ id: '重命名' })}>
                  <IconFont type="iconedit" className="icon" />
                </Tooltip>
              </div>
            ),
          });
        }
        if (selected === '群组资源' && isShowGroupSetting) {
          buttonList.push({
            title: intl.formatMessage({ id: '更新封面' }),
            func: () => setCoverModalVisible(true),
            icon: (
              <div onClick={() => setCoverModalVisible(true)}>
                <Tooltip title={intl.formatMessage({ id: '更新封面' })}>
                  <IconFont type="iconcopy" className="icon" />
                </Tooltip>
              </div>
            ),
          });
        }
        if (selected === '群组资源' && isShowGroupSetting && parameterConfig?.target_customer == 'chifeng'){
          buttonList.push({
            title: intl.formatMessage({ id: '专业所属大类设置' }),
            func: () => setMajoryTypeModalVisible(true),
            icon: (
              <div onClick={() => setMajoryTypeModalVisible(true)}>
                <Tooltip title={intl.formatMessage({ id: '专业所属大类设置' })}>
                  <IconFont type="iconcopy" className="icon" />
                </Tooltip>
              </div>
            ),
          });
        }
        if (resourceGroup && operateCode(detail.operateCode_).includes(8) &&
          detail?.tree_?.[0]?.split('/')[detail?.tree_?.[0]?.split('/').length - 1] === '群组资源') {
          buttonList.push({
            title: intl.formatMessage({ id: '查看' }),
            func: () => {
              mobileFlag ? window.open('#/basic/rmanList/mobileResourceGroup?id=' + detail.contentId_) : setResourceGroupModal(true);
            },
            icon: (
              <div
                onClick={() => {
                  mobileFlag ? window.open('#/basic/rmanList/mobileResourceGroup?id=' + detail.contentId_) : setResourceGroupModal(true);
                }}
              >
                <Tooltip title={intl.formatMessage({ id: '查看' })}>
                  <IconFont type="iconSettingsbeifen" className="icon" />
                </Tooltip>
              </div>
            ),
          });
        }
        if (
          permissions.includes(perCfg.resource_move) &&
          selected !== 'myVideo' && selected !== 'departmentVideo' &&
          detail?.tree_?.[0]?.split('/')[detail?.tree_?.[0]?.split('/').length - 1] !== '群组资源' &&
          !detail.damaged && deleteShow && !myCollectBin && isShowGroupSetting
        ) {
          buttonList.push({
            title: intl.formatMessage({ id: '移动' }),
            func: () => copyAndMove(1),
            icon: (
              <div onClick={() => copyAndMove(1)}>
                <Tooltip title={intl.formatMessage({ id: '移动' })}>
                  <IconFont type="iconyidong" className="icon" />
                </Tooltip>
              </div>
            ),
          });
        }
        if (
          permissions.includes(perCfg.resource_delete) &&
          deleteShow &&
          detail.isDelete !== 1 &&
          !myCollectBin && isShowGroupSetting
        ) {
          buttonList.push({
            title: intl.formatMessage({ id: '删除' }),
            func: () => setDeleteModal(true),
            icon: (
              <div onClick={() => setDeleteModal(true)}>
                <Tooltip title={intl.formatMessage({ id: '删除' })}>
                  <IconFont type="icondelete" className="icon" />
                </Tooltip>
              </div>
            ),
          });
        }
      } else { // 代表是文件或回收站
        //downEnable 不为0时代表不在我的分享目录
        if (Secondarydirectory && !downEnable && !recycleBin || myVideo) {
          if (
            permissions.includes(perCfg.resource_download) &&
            detail.isDelete !== 1
          ) {
            !detail.damaged &&
              buttonList.push({
                title: intl.formatMessage({ id: '下载' }),
                func: (entityData) => opendownloadbox(entityData),
                icon: (
                  <div onClick={opendownloadbox}>
                    <Tooltip title={intl.formatMessage({ id: '下载' })}>
                      <ArrowDownOutlined className="icon" />
                    </Tooltip>
                  </div>
                ),
              });
          }
          if (
            (rmanGlobalParameter.includes(globalParams.my_shared_display) &&
              shareEnble &&
              !detail.damaged &&
              detail.isDelete !== 1)
            || (rmanGlobalParameter.includes(globalParams.my_shared_display) && myVideo)
          ) {
            buttonList.push({
              title: intl.formatMessage({ id: '分享' }),
              func: () => setShareVisible(true),
              icon: (
                <div onClick={() => setShareVisible(true)}>
                  <Tooltip title={intl.formatMessage({ id: '分享' })}>
                    <IconFont type="icona-xingzhuangbeifen2" className="icon" />
                  </Tooltip>
                </div>
              ),
            });
          }
          if (
            rmanGlobalParameter.includes(globalParams.my_collection_display) && (myVideo && classifiedByschedule) &&
            ((!detail.damaged &&
              detail.isDelete !== 1))
          ) {
            if (myCollectBin || detail.isCollection) {
              buttonList.push({
                title: intl.formatMessage({ id: '取消收藏' }),
                func: () => deleteMyCollection(),
                icon: (
                  <div>
                    <Popconfirm
                      title={intl.formatMessage({ id: '确定要取消收藏吗' })}
                      onConfirm={() => deleteMyCollection()}
                      okText={intl.formatMessage({ id: '确定' })}
                      cancelText={intl.formatMessage({ id: '取消' })}
                    >
                      <Tooltip title={intl.formatMessage({ id: '取消收藏' })}>
                        <IconFont
                          type="icona-shoucangbeifen2"
                          className="icon"
                        />
                      </Tooltip>
                    </Popconfirm>
                  </div>
                ),
              });
            } else {
              buttonList.push({
                title: intl.formatMessage({ id: '收藏' }),
                func: (entityData) => addMyCollection(entityData),
                icon: (
                  <div onClick={(entityData) => addMyCollection(entityData)}>
                    <Tooltip title={intl.formatMessage({ id: '收藏' })}>
                      <IconFont type="iconxingzhuang2" className="icon" />
                    </Tooltip>
                  </div>
                ),
              });
            }
          }
          if (
            permissions.includes(perCfg.resource_copy) && (
              (!detail.damaged && 
                operateCode(detail.operateCode_).includes(4) && 
                detail.type_ !== 'folder') || myVideo) && (!myVideo || (myVideo && classifiedByschedule)) 
          ) {
            buttonList.push({
              title: intl.formatMessage({ id: '复制' }),
              func: () => copyAndMove(0),
              icon: (
                <div onClick={() => copyAndMove(0)}>
                  <Tooltip title={intl.formatMessage({ id: '复制' })}>
                    <IconFont type="iconcopy" className="icon" />
                  </Tooltip>
                </div>
              ),
            });
          }
          if (
            permissions.includes(perCfg.resource_move) &&
            selected !== 'myVideo' &&
            selected !== 'departmentVideo' &&
            detail?.tree_?.[0]?.split('/')[
            detail?.tree_?.[0]?.split('/').length - 1
            ] !== '群组资源' &&
            deleteShow &&
            !detail.damaged &&
            !myCollectBin
          ) {
            buttonList.push({
              title: intl.formatMessage({ id: '移动' }),
              func: () => copyAndMove(1),
              icon: (
                <div onClick={() => copyAndMove(1)}>
                  <Tooltip title={intl.formatMessage({ id: '移动' })}>
                    <IconFont type="iconyidong" className="icon" />
                  </Tooltip>
                </div>
              ),
            });
          }
          if (
            permissions.includes(perCfg.jove) && (!myVideo || (myVideo && classifiedByschedule)) &&
            deleteShow &&
            detail.isDelete !== 1 &&
            hidecatalogue === '0' &&
            !detail.damaged &&
            joveonetype.includes(detail.type_)
          ) {
            buttonList.push({
              title: intl.formatMessage({ id: '在线剪辑' }),
              func: () => gojove(),
              icon: (
                <div onClick={gojove}>
                  <Tooltip title={intl.formatMessage({ id: '在线剪辑' })}>
                    <IconFont type="iconzaixianbianji" className="icon" />
                  </Tooltip>
                </div>
              ),
            });
          }
          if (
            permissions.includes(perCfg.resource_analysis) &&
            permiseifshow() &&
            (([
              'biz_sobey_video',
              'biz_sobey_audio',
              'biz_sobey_picture',
              'biz_sobey_document',
            ].includes(detail.type_) &&
              operateCode(detail.operateCode_).includes(2) &&
              !detail.damaged &&
              !myShare &&
              !myCollectBin) || myVideo)
          ) {
            buttonList.push({
              title: intl.formatMessage({ id: '智能分析' }),
              func: (entityData: any) => analysisCallback?.([entityData || detail]),
              icon: (
                <div onClick={() => analysisCallback?.([detail])}>
                  <Tooltip title={intl.formatMessage({ id: '智能分析' })}>
                    <IconFont type="iconzhinengfenxi" className="icon" />
                  </Tooltip>
                </div>
              ),
            });
          }
          if (
            modules.includes(ModuleCfg.textclip) && (!myVideo || (myVideo && classifiedByschedule)) &&
            operateCode(detail.operateCode_).includes(2) &&
            detail.isDelete !== 1 &&
            hidecatalogue === '0' &&
            detail.asr_status === '2' &&
            !detail.damaged &&
            detail.type_ === 'biz_sobey_video' 
          ) {
            buttonList.push({
              title: intl.formatMessage({ id: '语音剪辑' }),
              func: () => goTextClip(),
              icon: (
                <div onClick={goTextClip}>
                  <Tooltip title={intl.formatMessage({ id: '语音剪辑' })}>
                    <IconFont type="iconyuyinbianji" className="icon" />
                  </Tooltip>
                </div>
              ),
            });
          }
          if (
            rmanGlobalParameter.includes(globalParams.knowledge_map_display) &&
            (((detail.type_ === 'biz_sobey_video' ||
              detail.type_ === 'biz_sobey_document' ||
              detail.type_ === 'biz_sobey_audio' ||
              detail.type_ === 'biz_sobey_picture') &&
              detail.isDelete !== 1 &&
              !detail.damaged &&
              hidecatalogue === '0') || myVideo) && (!myVideo || (myVideo && classifiedByschedule)) 
          ) {
            buttonList.push({
              title: intl.formatMessage({ id: '知识点绑定' }),
              func: () => setBindKnowledgeModalVisible(true),
              icon: (
                <div onClick={() => setBindKnowledgeModalVisible(true)}>
                  <Tooltip title={intl.formatMessage({ id: '知识点绑定' })}>
                    <IconFont type="iconditu" className="icon" />
                  </Tooltip>
                </div>
              ),
            });
          }
          if (
            permissions.includes(perCfg.resource_delete) &&
            deleteShow &&
            detail.isDelete !== 1
          ) {
            buttonList.push({
              title: intl.formatMessage({ id: '删除' }),
              func: () => setDeleteModal(true),
              icon: (
                <div onClick={() => setDeleteModal(true)}>
                  <Tooltip title={intl.formatMessage({ id: '删除' })}>
                    <IconFont type="icondelete" className="icon" />
                  </Tooltip>
                </div>
              ),
            });
          }
        } else { // 
          //针对我的分享首页没有hive字段属性
          if (!detail.tree_ && !recycleBin) {
            if (myShare === 1) {
              buttonList.push({
                title: intl.formatMessage({ id: '移除' }),
                func: () => removeShared(),
                icon: (
                  <div>
                    <Popconfirm
                      title={intl.formatMessage({ id: '确定要移除吗' })}
                      onConfirm={() => removeShared()}
                      okText={intl.formatMessage({ id: '确定' })}
                      cancelText={intl.formatMessage({ id: '取消' })}
                    >
                      <Tooltip title={intl.formatMessage({ id: '移除' })}>
                        <RemoveIcon className="icon" />
                      </Tooltip>
                    </Popconfirm>
                  </div>
                ),
              });
            } else {
              if (detail.shareType === 1) {
                buttonList.push({
                  title: intl.formatMessage({ id: '复制链接' }),
                  func: () => copylinkurl(),
                  icon: (
                    <div onClick={copylinkurl}>
                      <Tooltip title={intl.formatMessage({ id: '复制链接' })}>
                        <LinkIcon className="icon linkIcon" />
                      </Tooltip>
                    </div>
                  ),
                });
              }
              buttonList.push({
                title: intl.formatMessage({ id: '取消分享' }),
                func: () => batchCancleShared(),
                icon: (
                  <div>
                    <Popconfirm
                      title={intl.formatMessage({ id: '确定要取消分享吗' })}
                      onConfirm={() => batchCancleShared()}
                      okText={intl.formatMessage({ id: '确定' })}
                      cancelText={intl.formatMessage({ id: '取消' })}
                    >
                      <Tooltip placement="right" title={intl.formatMessage({ id: '取消分享' })}>
                        <CancelshareIcon className="icon" />
                      </Tooltip>
                    </Popconfirm>
                  </div>
                ),
              });
            }
          } else {
            if (!recycleBin) {
              if (
                rmanGlobalParameter.includes(
                  globalParams.resource_like_display,
                ) &&
                detail.isDelete !== 1 &&
                !detail.damaged
              ) {
                if (detail.isLike) {
                  //已点赞
                  buttonList.push({
                    title: intl.formatMessage({ id: '已赞' }),
                    func: () => resourceLikeChange(false),
                    icon: (
                      <div onClick={() => resourceLikeChange(false)}>
                        <Tooltip title={intl.formatMessage({ id: '取消点赞' })}>
                          <IconFont className="icon" type="iconyizan" />
                        </Tooltip>
                      </div>
                    ),
                  });
                } else {
                  !myShare &&
                    buttonList.push({
                      title: intl.formatMessage({ id: '点赞' }),
                      func: () => resourceLikeChange(true),
                      icon: (
                        <div onClick={() => resourceLikeChange(true)}>
                          <Tooltip title={intl.formatMessage({ id: '点赞' })}>
                            <IconFont className="icon" type="icondianzan" />
                          </Tooltip>
                        </div>
                      ),
                    });
                }
              }
              if (
                rmanGlobalParameter.includes(
                  globalParams.my_collection_display,
                ) &&
                !detail.damaged &&
                !myShare
              ) {
                if (myCollectBin || detail.isCollection) {
                  buttonList.push({
                    title: intl.formatMessage({ id: '取消收藏' }),
                    func: () => deleteMyCollection(),
                    icon: (
                      <div>
                        {/* <Popconfirm
                          title="确定要取消收藏吗?"
                          onConfirm={() => deleteMyCollection()}
                          okText="确定"
                          cancelText="取消"
                        > */}
                        <Tooltip title={intl.formatMessage({ id: '取消收藏' })}>
                          <IconFont onClick={() => deleteMyCollection()}
                            type="icona-shoucangbeifen2"
                            className="icon"
                          />
                        </Tooltip>
                        {/* </Popconfirm> */}
                      </div>
                    ),
                  });
                } else {
                  buttonList.push({
                    title: intl.formatMessage({ id: '收藏' }),
                    func: () => addMyCollection(),
                    icon: (
                      <div onClick={() => addMyCollection()}>
                        <Tooltip title={intl.formatMessage({ id: '收藏' })}>
                          <IconFont type="iconxingzhuang2" className="icon" />
                        </Tooltip>
                      </div>
                    ),
                  });
                }
              }

              if (detail.isSave) {
                //针对分享给我的 如果允许另存为 添加复制功能
                buttonList.push({
                  title: intl.formatMessage({ id: '保存至' }),
                  func: () => copyAndMove(0),
                  icon: (
                    <div onClick={() => copyAndMove(0)}>
                      <Tooltip title={intl.formatMessage({ id: '保存至' })}>
                        <IconFont type="iconcopy" className="icon" />
                      </Tooltip>
                    </div>
                  ),
                });
              }
              if (
                rmanGlobalParameter.includes(globalParams.my_shared_display) &&
                shareEnble &&
                detail.isDelete !== 1 &&
                !myShare &&
                !detail.damaged &&
                !myCollectBin
              ) {
                buttonList.push({
                  title: intl.formatMessage({ id: '分享' }),
                  func: () => setShareVisible(true),
                  icon: (
                    <div onClick={() => setShareVisible(true)}>
                      <Tooltip title={intl.formatMessage({ id: '分享' })}>
                        <IconFont
                          type="icona-xingzhuangbeifen2"
                          className="icon"
                        />
                      </Tooltip>
                    </div>
                  ),
                });
              }
              if (permissions.includes(perCfg.resource_download) && detail.isDelete !== 1 && (downEnable === 2 ? false : true)) {
                !detail.damaged &&
                  buttonList.push({
                    title: intl.formatMessage({ id: '下载' }),
                    func: () => opendownloadbox(),
                    icon: (
                      <div onClick={opendownloadbox}>
                        <Tooltip title={intl.formatMessage({ id: '下载' })}>
                          <ArrowDownOutlined className="icon" />
                        </Tooltip>
                      </div>
                    ),
                  });
              }

              if (
                permissions.includes(perCfg.resource_edit) &&
                // && operateCode(detail.operateCode_).includes(2)
                // 采用后端给的内容权限标识 枚举值 8：删除 4：执行 2：编辑 1：可读
                // &&(detail?.tree_?.[0]?.split('/')[detail?.tree_?.[0]?.split('/').length - 1] === '群组资源'
                //   ? detail.operateCode_ === 15 :
                //   operateCode(detail.operateCode_).includes(2)
                // )
                operateCode(detail.operateCode_).includes(2) &&
                detail.isDelete !== 1 &&
                !myShare &&
                !myCollectBin
              ) {
                buttonList.push({
                  title: intl.formatMessage({ id: '重命名' }),
                  func: () => refilename(),
                  icon: (
                    <div onClick={refilename}>
                      <Tooltip title={intl.formatMessage({ id: '重命名' })}>
                        <IconFont type="iconedit" className="icon" />
                      </Tooltip>
                    </div>
                  ),
                });
              }
              if (
                permissions.includes(perCfg.resource_copy) &&
                detail.type_ === 'biz_sobey_video' &&
                !detail.damaged &&
                !myShare
              ) {
                buttonList.push({
                  title: intl.formatMessage({ id: '更新封面' }),
                  func: () => setCoverModalVisible(true),
                  icon: (
                    <div onClick={() => setCoverModalVisible(true)}>
                      <Tooltip title={intl.formatMessage({ id: '更新封面' })}>
                        <IconFont type="iconcopy" className="icon" />
                      </Tooltip>
                    </div>
                  ),
                });
              }
              if (
                permissions.includes(perCfg.resource_copy) &&
                detail.type_ !== 'folder' &&
                operateCode(detail.operateCode_).includes(4) &&
                !detail.damaged &&
                !myShare &&
                (downEnable === 2 ? false : true)
              ) {
                buttonList.push({
                  title: intl.formatMessage({ id: '复制' }),
                  func: () => copyAndMove(0),
                  icon: (
                    <div onClick={() => copyAndMove(0)}>
                      <Tooltip title={intl.formatMessage({ id: '复制' })}>
                        <IconFont type="iconcopy" className="icon" />
                      </Tooltip>
                    </div>
                  ),
                });
              }
              if (
                permissions.includes(perCfg.resource_move) &&
                selected !== 'myVideo' &&
                selected !== 'departmentVideo' &&
                detail?.tree_?.[0]?.split('/')[
                detail?.tree_?.[0]?.split('/').length - 1
                ] !== '群组资源' &&
                deleteShow &&
                !detail.damaged &&
                !myShare &&
                !myCollectBin
              ) {
                buttonList.push({
                  title: intl.formatMessage({ id: '移动' }),
                  func: () => copyAndMove(1),
                  icon: (
                    <div onClick={() => copyAndMove(1)}>
                      <Tooltip title={intl.formatMessage({ id: '移动' })}>
                        <IconFont type="iconyidong" className="icon" />
                      </Tooltip>
                    </div>
                  ),
                });
              }
              if (
                permissions.includes(perCfg.resource_analysis) &&
                permiseifshow() &&
                ([
                  'biz_sobey_video',
                  'biz_sobey_audio',
                  'biz_sobey_picture',
                  'biz_sobey_document',
                ].includes(detail.type_) &&
                  operateCode(detail.operateCode_).includes(2) &&
                  !detail.damaged &&
                  !myShare &&
                  !myCollectBin)
              ) {
                buttonList.push({
                  title: intl.formatMessage({ id: '智能分析' }),
                  func: () => analysisCallback?.([detail]),
                  icon: (
                    <div onClick={() => analysisCallback?.([detail])}>
                      <Tooltip title={intl.formatMessage({ id: '智能分析' })}>
                        <IconFont type="iconzhinengfenxi" className="icon" />
                      </Tooltip>
                    </div>
                  ),
                });
              }
              if (
                permissions.includes(perCfg.jove) &&
                operateCode(detail.operateCode_).includes(2) &&
                detail.isDelete !== 1 &&
                hidecatalogue === '0' &&
                joveonetype.includes(detail.type_) &&
                !detail.damaged &&
                !myShare
              ) {
                buttonList.push({
                  title: intl.formatMessage({ id: '在线剪辑' }),
                  func: () => gojove(),
                  icon: (
                    <div onClick={gojove}>
                      <Tooltip title={intl.formatMessage({ id: '在线剪辑' })}>
                        <IconFont type="iconzaixianbianji" className="icon" />
                      </Tooltip>
                    </div>
                  ),
                });
              }
              if (
                modules.includes(ModuleCfg.textclip) &&
                operateCode(detail.operateCode_).includes(2) &&
                detail.isDelete !== 1 &&
                hidecatalogue === '0' &&
                detail.asr_status === '2' &&
                !detail.damaged &&
                detail.type_ === 'biz_sobey_video'
              ) {
                buttonList.push({
                  title: intl.formatMessage({ id: '语音剪辑' }),
                  func: () => goTextClip(),
                  icon: (
                    <div onClick={goTextClip}>
                      <Tooltip title={intl.formatMessage({ id: '语音剪辑' })}>
                        <IconFont type="iconyuyinbianji" className="icon" />
                      </Tooltip>
                    </div>
                  ),
                });
              }
              if (
                rmanGlobalParameter.includes(
                  globalParams.knowledge_map_display,
                ) &&
                (detail.type_ === 'biz_sobey_video' ||
                  detail.type_ === 'biz_sobey_document' ||
                  detail.type_ === 'biz_sobey_audio' ||
                  detail.type_ === 'biz_sobey_picture') &&
                operateCode(detail.operateCode_).includes(2) &&
                detail.isDelete !== 1 &&
                !myShare &&
                !detail.damaged &&
                hidecatalogue === '0'
              ) {
                buttonList.push({
                  title: intl.formatMessage({ id: '知识点绑定' }),
                  func: () => setBindKnowledgeModalVisible(true),
                  icon: (
                    <div onClick={() => setBindKnowledgeModalVisible(true)}>
                      <Tooltip title={intl.formatMessage({ id: '知识点绑定' })}>
                        <IconFont type="iconditu" className="icon" />
                      </Tooltip>
                    </div>
                  ),
                });
              }
              //仅在个人资源中才能共享
              if (
                permissions.includes(perCfg.publish_resource) &&
                [
                  'biz_sobey_video',
                  'biz_sobey_picture',
                  'biz_sobey_document',
                  'biz_sobey_audio',
                ].includes(detail.type_) &&
                window.localStorage.getItem('upform_platform') !== 'Lark'
              ) {
                buttonList.push({
                  title: intl.formatMessage({ id: '发布' }),
                  func: () => publishResource(),
                  icon: (
                    <div onClick={() => publishResource()}>
                      <Tooltip title={intl.formatMessage({ id: '发布' })}>
                        <IconFont type="iconparticipation" className="icon" />
                      </Tooltip>
                    </div>
                  ),
                });
              }
              //仅在个人资源中才能共享
              if (
                permissions.includes(perCfg.publish_topic_resource) &&
                [
                  'biz_sobey_video',
                  'biz_sobey_picture',
                  'biz_sobey_document',
                  'biz_sobey_audio',
                ].includes(detail.type_) &&
                window.localStorage.getItem('upform_platform') !== 'Lark'
              ) {
                buttonList.push({
                  title: intl.formatMessage({ id: '发布至专题' }),
                  func: () => publishTopicResource(),
                  icon: (
                    <div onClick={() => publishTopicResource()}>
                      <Tooltip title={intl.formatMessage({ id: '发布至专题' })}>
                        <IconFont type="iconrelease" className="icon" />
                      </Tooltip>
                    </div>
                  ),
                });
              }
              if (
                permissions.includes(perCfg.resource_delete) &&
                deleteShow && //针对传到公共资源情形
                detail.isDelete !== 1 &&
                !myShare &&
                !myCollectBin
              ) {
                buttonList.push({
                  title: intl.formatMessage({ id: '删除' }),
                  func: () => setDeleteModal(true),
                  icon: (
                    <div onClick={() => setDeleteModal(true)}>
                      <Tooltip title={intl.formatMessage({ id: '删除' })}>
                        <IconFont type="icondelete" className="icon" />
                      </Tooltip>
                    </div>
                  ),
                });
              }
            }
          }
        }
      }
    }
    // if(publishManagement){
    //   if(publishManagement === 'remove'){
    //     buttonList.push({
    //       title: intl.formatMessage({ id: '发布' }),
    //       func: () => batchOperate?.([detail], 0),
    //       icon: (
    //         <div onClick={() => batchOperate?.([detail], 0)}>
    //           <Tooltip title={intl.formatMessage({ id: '发布' })}>
    //           <IconFont type="iconparticipation" />
    //           </Tooltip>
    //         </div>
    //       ),
    //     })
    //   }
    //   else{
    //     buttonList.push({
    //       title: intl.formatMessage({ id: '下架' }),
    //       func: () => batchOperate?.([detail], 1),
    //       icon: (
    //         <div onClick={() => batchOperate?.([detail], 1)}>
    //           <Tooltip title={intl.formatMessage({ id: '下架' })}>
    //             <FileTextOutlined className="icon" />
    //           </Tooltip>
    //         </div>
    //       ),
    //     })
    //   }
    // }
  } else {
    buttonList = [];
  }
  if (shareDetailHidden) {
    buttonList = [];
  }

  // 标题
  let title = null;
  if (rename) {
    title = (
      <Input
        className="content_word"
        size="small"
        onPressEnter={changeName}
        onBlur={changeName}
        defaultValue={detail.name_}
        ref={inputRef}
        autoComplete={'off'}
      />
    );
  } else {
    // 是否是群组资源
    const isGroupResource = detail?.tree_?.[0]?.split('/')[detail?.tree_?.[0]?.split('/').length - 1] === '群组资源'
    title = (
      <span
        className={`content_word${mobileFlag ? ' mobile_word' : ''} ${searchVoiceWord && detail.model_sobey_keywords && detail.model_sobey_keywords.includes(searchVoiceWord) ||
          searchVoiceWord && detail.model_sobey_smart_voice_ && detail.model_sobey_smart_voice_.includes(searchVoiceWord) ||
          searchVoiceWord && detail.teacher_names && detail.teacher_names.includes(searchVoiceWord) ||
          searchVoiceWord && detail.importuser && detail.importuser.includes(searchVoiceWord) ||
          searchVoiceWord && detail.model_sobey_cata_sequencemeta && detail.model_sobey_cata_sequencemeta.includes(searchVoiceWord) ? 'search_word' : ''}`}
        onClick={() => {
          if (myVideo && !classifiedByschedule) {
            goDetail('myVideo')
            return
          }
            goDetail(1)
          }}
      >
        {myShare === 1 && detail.isRead === false && (
          <span className="unread"></span>
        )}
        {myVerify ? (
          <Tooltip
            title={detail.resourceName || detail.instanceName || '--'}
            placement="topLeft"
          >
            {searchVoice(detail.resourceName || detail.instanceName || '--')}
          </Tooltip>
        ) : (
          <Tooltip title={(isGroupResource && detail?.group_username) ? <div>
            <span>{detail.name_ || detail.resourceName}</span>
            {
              <div>{`成员：${detail?.group_username}`}</div>
            }
          </div> : (detail.name_ || detail.resourceName)} placement="topLeft">
            {searchVoice(detail.name_ || detail.resourceName)}
          </Tooltip>
        )}
        {mobileFlag && !recycleBin && buttonList?.length > 0 && (
          <Popover
            // placement="rightTop"
            getPopupContainer={(e: any) => e.parentElement.parentElement}
            open={moreMenuVisible}
            onOpenChange={(newOpen: boolean) => setMoreMenuVisible(newOpen)}
            content={
              <div className="moreOpt">
                {buttonList.map((item: any, index: number) => {
                  return (
                    <div
                      key={index}
                      onClick={(e: any) => {
                        e.preventDefault();
                        e.stopPropagation();
                        popoverClick(item.func);
                      }}
                    >
                      {item.icon}
                      <span>{item.title}</span>
                    </div>
                  );
                })}
              </div>
            }
          >
            <EllipsisOutlined
              className="mobile_single_btns"
              title={intl.formatMessage({ id: '更多操作' })}
              onClick={(e: any) => {
                e.preventDefault();
                e.stopPropagation();
                setMoreMenuVisible(!moreMenuVisible);
              }}
            />
          </Popover>
        )}
      </span>
    );
  }
  let det = (
    <>
      <CopyAndMoveModal
        modalVisible={copyAndMoveModallVisible}
        modalClose={() => setCopyAndMoveModalVisible(false)}
        refresh={refresh}
        copyAndMovelist={[Object.keys(videoSeat).length > 0 ? videoSeat : detail]}
        isShare={myShare}
        operatType={operatType}
        pubOrPri={false}
      />
      {deleteModal && <DeleteModal
        modalVisible={deleteModal}
        modalClose={() => setDeleteModal(false)}
        refresh={() => {
          refresh('', {
            name: '删除',
            contentId_: detail.contentId_,
          })
        }}
        deletelist={[detail]}
      />}
      <BindKnowledgeMap
        modalVisible={bindKnowledgeModalVisible}
        modalClose={() => setBindKnowledgeModalVisible(false)}
        analysislist={[Object.keys(videoSeat).length > 0 ? videoSeat : detail]}
      />
      {shareVisible && <ShareModal
        modalVisible={shareVisible}
        modalClose={() => setShareVisible(false)}
        modalOpen={() => setShareVisible(true)}
        sharelist={[Object.keys(videoSeat).length > 0 ? videoSeat : detail]}
        refresh={refresh}
      />}
      {auditlogsVisible && <Auditlogs logsVisible={auditlogsVisible} contentId={detail.resourceId} onclose={() => setAuditlogsVisible(false)}></Auditlogs>}
      <ResourceGroupModal
        modalVisible={resourceGroupModal}
        modalClose={() => setResourceGroupModal(false)}
        resourceGroup={detail}
      />
      <TextclipModal
        textclipVisible={textclipVisible}
        clipList={textclipLists}
        onClose={() => setTextclipVisible(false)}
        onCreate={newTextClip}
      />
      {coverModalVisible && <CoverModal
        detail={detail}
        visible={coverModalVisible}
        refresh={refresh}
        coverCancel={() => {
          setCoverModalVisible(false);
        }}
      />}
      {majoryTypeModalVisible && <MajorTypeModal
        detail={detail}
        modalVisible={majoryTypeModalVisible}
        refresh={refresh}
        modalClose={() => {
          setMajoryTypeModalVisible(false);
        }}
      />}
    </>
  );
  //添加收藏
  const addMyCollection = async (entityData?: any) => {
    contentListApis
      .addmycollectionlist(entityData?.contentId_ || detail?.contentId_)
      .then(async (res: any) => {
        // console.log(res)
        if (res.success) {
          message.success(intl.formatMessage({ id: '收藏成功' }));
          // sleep(1000)//延时刷新 以便更新回传字段
          if (entityData) {
            searchMyVideoDetail()
          }
          else {
            await refresh('', { name: '收藏', lists: [detail.contentId_] });
          }
        } else {
          message.error(intl.formatMessage({ id: '收藏失败！' }));
        }
      });
  };
  //取消收藏
  const deleteMyCollection = async (entityData?: any) => {
    console.log(detail);
    contentListApis
      .deletemycollectionlist([entityData.contentId_ || detail.contentId_])
      .then(async (res: any) => {
        console.log(res);
        if (res.success) {
          message.success(intl.formatMessage({ id: '取消成功' }));
          // sleep(1000)//延时刷新 以便更新回传字段
          await refresh('', { name: '取消收藏', lists: [detail.contentId_] });
        } else {
          message.error(intl.formatMessage({ id: '取消收藏失败' }));

        }
      });
  };
  //  移除分享给我的
  const removeShared = async () => {
    contentListApis.removeShared([detail.link]).then((res: any) => {
      if (res.success) {
        refresh();
        message.success(intl.formatMessage({ id: '移除成功' }));
      } else {
        message.error(intl.formatMessage({ id: '移除失败' }));
      }
    });
  };
  // 取消分享
  const batchCancleShared = async () => {
    // await contentListApis.cancelShared([detail.link])
    contentListApis
      .cancelShared(
        [
          {
            link: detail.link,
            shareUserCodes: [detail.shareUserCode],
          },
        ],
      )
      .then((res: any) => {
        if (res.success) {
          // refresh(undefined, '', '', checkedList.map((item: any) => item.contentId_))
          refresh(undefined, { name: '取消分享', lists: [detail.link] });
          // fetchSharedNums()//标记完毕时 实时刷新未读数量；
          message.success(intl.formatMessage({ id: '取消分享成功' }));
        } else {
          message.error(intl.formatMessage({ id: '取消分享失败' }));
        }
      });
  };
  const popoverClick = (func: any, entityData?: any) => {
    if (entityData) {
      setVideoSeat(entityData)
      func(entityData)
    }
    else {
      func()
    }
    setMoreMenuVisible(false);
  };
  useEffect(() => {
    if (videoVisible && detail.type_ === 'biz_sobey_video') {
      contentListApis.getFileInfo([detail.contentId_]).then(res => {
        if (res?.success) {
          setVideoPath(res?.data[0]?.downloadAddress[0])
          videoRef?.current?.play();
        }
      })
    }
  }, [videoVisible])
  const getButtonList = (entityData: any) => {
    let btn = []
    if (entityData.isCollection) {
      btn.push({
        title: intl.formatMessage({ id: '取消收藏' }),
        func: () => deleteMyCollection(entityData),
        icon: (
          <div>
            <Popconfirm
              title={intl.formatMessage({ id: '确定要取消收藏吗' })}
              onConfirm={() => deleteMyCollection(entityData)}
              okText={intl.formatMessage({ id: '确定' })}
              cancelText={intl.formatMessage({ id: '取消' })}
            >
              <Tooltip title={intl.formatMessage({ id: '取消收藏' })}>
                <IconFont
                  type="icona-shoucangbeifen2"
                  className="icon"
                />
              </Tooltip>
            </Popconfirm>
          </div>
        ),
      });
    } else {
      btn.push({
        title: intl.formatMessage({ id: '收藏' }),
        func: (entityData) => addMyCollection(entityData),
        icon: (
          <div onClick={(entityData) => addMyCollection(entityData)}>
            <Tooltip title={intl.formatMessage({ id: '收藏' })}>
              <IconFont type="iconxingzhuang2" className="icon" />
            </Tooltip>
          </div>
        ),
      });
    }
    return btn
  }
  if (modal) {
    return (
      <div className="content_item">
        <div className="imgbox" onMouseEnter={() => {
          setVideoVisible(true)
        }} onMouseLeave={() => {
          setVideoVisible(false)
        }} onClick={() => {
          if (myVideo && !classifiedByschedule) {
            goDetail('myVideo')
            return
          }
          goDetail(1)
        }}>
          {videoVisible && detail.type_ === 'biz_sobey_video' && <video  style={{ width: '100%', height: '100%' }} ref={videoRef} muted src={videoPath}></video>}
          <div className={detail.isDelete === 1 ? 'isDeleteDiv' : ''}>
            <Keyframe
              type={detail.type_}
              root={contentitemBox}
              damaged={detail.damaged}
              custom_keyframe={detail.custom_keyframe}
              src={detail.keyframe_ ? detail.keyframe_ : detail.keyframe}
              viewFlag={
                detail.keyframe_?.indexOf('/rman/static/images') > -1
                  ? true
                  : false
              }
              isDelete={detail.isDelete === 1}
              resourceGroup={resourceGroup}
            />
            {detail.isDelete === 1 && (
              <span className="isDeleteSpan">{intl.formatMessage({ id: '已失效' })}</span>
            )}
          </div>
          {detail.duration && <div className="img_mask"></div>}
          {detail.shared_state == 1 && ['个人资源', 'myVideo'].includes(selected) && (
            <div className="shared">
              已共享
            </div>
          )}
          {
            detail.type_ !== 'folder' && ['共享资源', '群组资源', '录播资源'].includes(selected) && (
              <div className="total_hits"><img style={{ marginRight: '5px' }} src={require('@/images/contentlibrary/view.png')} /><span>{detail.hits}</span></div>
            )}
          {detail.duration && (
            <div className="time1">
              {l100Ns2Tc$1(detail.duration, detail.framerate)}
            </div>
          )}
          {detail.fileext && (
            <div className={`time2 ${detail.type_}`}>
              <span>{detail.fileext}</span>
            </div>
          )}
        </div>
        <div className="content_title">
          {!noCheckbox && target !== 'custom' && <Checkbox value={detail} />}
          {detail.type_ !== 'folder' && target === 'custom' && <Radio value={detail} />}
          {title}
        </div>
        {detail.type_ !== "folder" && searchVoiceWord && <div className="resource">
          {searchVoiceWord && detail.model_sobey_cata_sequencemeta && detail.model_sobey_cata_sequencemeta.includes(searchVoiceWord) && <span>
            <span className="type_knowladge">{intl.formatMessage({ id: '知识点' })}</span>
            <span className="knowladge_data">{searchVoice(detail.model_sobey_cata_sequencemeta)}</span>
          </span>}

          {searchVoiceWord && detail.model_sobey_keywords && detail.model_sobey_keywords.includes(searchVoiceWord) && <span>
            <span className="type_knowladge">{intl.formatMessage({ id: '标签' })}</span>
            <span className="knowladge_data">{searchVoice(detail.model_sobey_keywords)}</span>
          </span>}

          {searchVoiceWord && detail.model_sobey_smart_voice_ && detail.model_sobey_smart_voice_.includes(searchVoiceWord) && <span>
            <span className="type_knowladge">{intl.formatMessage({ id: '语音' })}</span>
            <span className="knowladge_data" dangerouslySetInnerHTML={{ __html: findKeyWord(detail.model_sobey_smart_voice_) }}></span>
          </span>}

          {searchVoiceWord && detail.teacher_names && detail.teacher_names.includes(searchVoiceWord) && <span>
            <span className="type_knowladge">{intl.formatMessage({ id: '教师' })}</span>
            <span className="knowladge_data">{searchVoice(detail.teacher_names.join(','))}</span>
          </span>}
          {searchVoiceWord && detail.importuser && detail.importuser.includes(searchVoiceWord) && <span>
            <span className="type_knowladge">{intl.formatMessage({ id: '上传人' })}</span>
            <span className="knowladge_data">{searchVoice(detail.importuser)}</span>
          </span>}
        </div>}
        <div className="labelbox">
          {/* 课程使用 */}
          {/* {detail.release_status == 'yes' && (
            <div className="item">
              <img src={require('../../images/contentlibrary/yyz.svg')} />
            </div>
          )} */}
          {detail.release_status == 'yes' && (
            <div className="item">
              <IconFont type="iconkechengyishiyong" />
            </div>
          )}
          {/* 语音识别 */}
          {Number(detail.asr_status) === 1 && (
            <div className="item">
              <img src={require('../../images/contentlibrary/yyz.svg')} />
            </div>
          )}
          {Number(detail.asr_status) === 2 && (
            <div className="item">
              {/* <img src={require('../../images/contentlibrary/yyz.svg')} /> */}
              <img src={require('../../images/contentlibrary/yyw.svg')} />
              {/* <IconFont type="iconfenxiwancheng" /> */}
            </div>
          )}
          {/* 知识点识别 */}
          {Number(detail.ocr_status) === 1 && (
            <div className="item">
              <img src={require('../../images/contentlibrary/zsz.svg')} />
            </div>
          )}
          {Number(detail.ocr_status) === 2 && (
            <div className="item">
              <img src={require('../../images/contentlibrary/zsw.svg')} />
            </div>
          )}
        </div>
        {/* {!hideBtn && button} */}
        {!hideBtn && !mobileFlag && !showNoBtn && target !== 'custom' && (
          <div
            className={`content_links${moreMenuVisible ? ' content_links_focus' : ''
              }${buttonList.length == 0 ? ' empty' : ''}`}
          >
            <Popover
              placement="rightTop"
              // getPopupContainer={(e:any) => e.parentElement.parentElement}  //父级用了缩放后不能在使用了
              open={moreMenuVisible}
              onOpenChange={(newOpen: boolean) => setMoreMenuVisible(newOpen)}
              content={
                <div className="moreOpt">
                  {buttonList.map((item: any, index: number) => {
                    return (
                      <div
                        key={index}
                        onClick={(e) => {
                          e.stopPropagation()
                          popoverClick(item.func)
                        }}
                      >
                        {item.icon}
                        <span>{item.title}</span>
                      </div>
                    );
                  })}
                </div>
              }
            >
              <div
                onClick={(e: any) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setMoreMenuVisible(!moreMenuVisible);
                }}
              >
                <img src={require('@/images/contentlibrary/more.png')} title={intl.formatMessage({ id: '更多操作' })} />
              </div>
            </Popover>
          </div>
        )}
        {det}
      </div>
    );
  } else {
    return (
      <div className={`list_content${mobileFlag ? '' : ' list_content_web'}`}>
        {myVideo ? <>
          {myVideoDetailFilter && <div className="item checkbox">
            <Checkbox indeterminate={!allList[index].allVideosSelected && allList[index]?.videoSearchResponses?.filter(item => item.isSelected)?.length > 0} checked={detail.allVideosSelected}
              onChange={(e) => {
                handleCourseChange(e, index)
              }} />

          </div>}
          {
            !classifiedByschedule && <>
              <div className="item checkbox"><Checkbox value={detail} /></div>
              <div className={columns.length > 7 ? 'overFlowStyle item' : 'contenttitle item'}>
                {title}
                {!hideBtn && (
                  <div className={`content_links${buttonList.length == 0 ? ' empty' : '' }`} >
                    {buttonList.length > 0 && (
                      <Popover
                        placement="rightTop"
                        getPopupContainer={(e: any) => e.parentElement} //父级用了缩放后不能在使用了
                        open={moreMenuVisible}
                        onOpenChange={(newOpen: boolean) =>
                          setMoreMenuVisible(newOpen)
                        }
                        content={
                          <div className="moreOpt">
                            {buttonList.map((item: any, index: number) => {
                              return (
                                <div
                                  key={index}
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    popoverClick(item.func)
                                  }}
                                >
                                  {item.icon}
                                  <span>{item.title}</span>
                                </div>
                              );
                            })}
                          </div>
                        }
                      >
                        <div
                          onClick={(e: any) => {
                            e.preventDefault();
                            e.stopPropagation();
                            setMoreMenuVisible(!moreMenuVisible);
                          }}
                        >
                          <IconFont className="moreBtn" type="iconshiyongqingkuang" />
                        </div>
                      </Popover>
                    )}
                  </div>
                )}
              </div>
              {columns.includes('fileext') && <div className="extension item width1">
                {detail.fileext || '-'}
              </div> }
              {columns.includes('filesize') && <div className="size item width1">
                {detail.filesize ? changesize(detail.filesize) : '-'}
              </div> }
              {columns.includes('importuser') && <div className="people item width1">
                {detail.importuser || '-'}
              </div> }
              {columns.includes('createDate') && <div className="time item width1">
                {detail.createDate || '-'}
              </div> }
              {columns.includes('source') && <div className="people item width1">
                {detail.source || '-1'}
              </div> }
              {/* {columns.includes('intelliState') && <div className="semester item width1">
                {detail.intelliState || '-1'}
              </div> } */}
              {columns.includes('duration') && <div className="duration item width1">
                {detail.duration ? l100Ns2Tc$1(detail.duration, detail.framerate) : '0'}
              </div> }
              {/* {columns.includes('usestatus') && <div className="semester item width1">
                {detail.usestatus || '-'}
              </div> } */}
              {columns.includes('semester') && <div className="semester item width1">
                {detail.semester || '-'}
              </div> }
              {columns.includes('seat') && <div className="seat item width1">
                {detail.seat || '-'}
              </div> }
            </>
          }
          {columns.includes('course_name') && <div onClick={() => goDetail(1)} className="contenttitle item">
            {!detail.week && <div className='item checkbox' onClick={() => goDetail(1)}
            >
              {/* 对于我的审核外层不需要图标 */}
              <Keyframe type='folder' root={contentitemBox} src='/rman/static/images/folder.png' />
            </div>}
            <span className={`content_word ${!detail.videoSearchResponses ? '' : 'r_content_word'}`}>{detail.course_name ? detail.course_name : '-'}（课程号：{detail.course_id}，课序号：{detail.course_no}）</span>
          </div>}
          {columns.includes('resource') && <div className="resource item width1">
            {detail.videoCount || '-'}
          </div>}
          {columns.includes('teacher_name') && <div title={detail.teacher_name || '-'} style={{ width: columns.includes('week') ? '' : '20%' }} className="teacher_name item width1">
            <span>{detail.teacher_name || '-'}</span>
          </div>}
          {columns.includes('week') && <div className="week item width1">
            {detail.week || '-'}
          </div>}
          {columns.includes('week_day') && <div className="week_day item width1">
            周{['一', '二', '三', '四', '五', '六', '日'][detail.week_day - 1] || '-'}，{detail.course_section}节
          </div>}
          {classifiedByschedule && columns.includes('seat') && <div className="seat width1">
            {detail?.videoSearchResponses?.length > 0 ? detail.videoSearchResponses.map((videoSearchResponse: any, index_) => {
              return <div className='video_seat' onClick={() => {
                goDetail(1, videoSearchResponse)
              }}><Checkbox checked={videoSearchResponse.isSelected}
                onChange={(e) => {
                  handleVideoChange(e, index, index_)
                }} onClick={(e) => e.stopPropagation()}>{videoSearchResponse.entityData.seat}</Checkbox>
                {!hideBtn && (
                  <div
                    className={`content_links${buttonList.length == 0 ? ' empty' : ''
                      }`}
                  >
                    {buttonList.length > 0 && (
                      <Popover
                        placement="rightTop"
                        getPopupContainer={(e: any) => e.parentElement} //父级用了缩放后不能在使用了
                        open={moreMenuVisible}
                        onOpenChange={(newOpen: boolean) =>
                          setMoreMenuVisible(newOpen)
                        }
                        content={
                          <div className="moreOpt">
                            {getButtonList(videoSearchResponse.entityData).concat(buttonList).map((item: any, index: number) => {
                              return (
                                <div
                                  key={index}
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    popoverClick(item.func, videoSearchResponse.entityData)
                                  }}
                                >
                                  {item.icon}
                                  <span>{item.title}</span>
                                </div>
                              );
                            })}
                          </div>
                        }
                      >
                        <div
                          onClick={(e: any) => {
                            e.preventDefault();
                            e.stopPropagation();
                            setMoreMenuVisible(!moreMenuVisible);
                          }}
                        >
                          <IconFont className="moreBtn" type="iconshiyongqingkuang" />
                        </div>
                      </Popover>
                    )}
                  </div>
                )}
              </div>
            }) : '-'}
          </div>}
          {classifiedByschedule && columns.includes('duration') && <div className="duration width1">
            {detail?.videoSearchResponses?.length > 0 ? detail.videoSearchResponses?.map((item: any) => {
              return <div>{item.entityData.duration ? l100Ns2Tc$1(item.entityData.duration) : '-'}</div>
            }) : '-'}
          </div>}
          {classifiedByschedule && columns.includes('filesize') && <div className="size width1">
            {detail?.videoSearchResponses?.length > 0 ? detail.videoSearchResponses?.map((item: any) => {
              return <div>{item.entityData.filesize ? changesize(item.entityData.filesize) : '-'}</div>
            }) : '-'}
          </div>}
          {classifiedByschedule && columns.includes('createDate_') && <div className="time width1">
            {detail?.videoSearchResponses?.length > 0 ? detail.videoSearchResponses?.map((item: any) => {
              return <div>{item.entityData.createDate_}</div>
            }) : '-'}
          </div>}
          {columns.includes('area_name') && <div title={detail.area_name || '-'} className="area_name item width1">
            <span>{detail.area_name || '-'}</span>
          </div>}
          {classifiedByschedule && columns.includes('semester') && <div className="semester item width1">
            {detail.semester || '-'}
          </div>
          }
          {det}
        </> : <>
          {(myVerify != '0-0' && myVerify != '1-0') && <div className="item checkbox">
            {target === 'custom' ? detail.type_ !== 'folder' && <Radio value={detail} /> : <Checkbox value={detail} />}
            {myShare === 1 && detail.isRead === false && (
              <span className="unread"></span>
            )}
          </div>}
          <div
            className={
              columns.length > 7 ? 'overFlowStyle item' : 'contenttitle item'
            }
          >
            {myVerify != '1-1' && myVerify != '0-1' && <div
              className={
                detail.isDelete === 1
                  ? 'item checkbox isDeleteDiv'
                  : 'item checkbox'
              }
              onClick={() => goDetail(1)}
            >
              {/* 对于我的审核外层不需要图标 */}
              {(myVerify != '0-0' && myVerify != '1-0') && <Keyframe
                type={detail.type_}
                root={contentitemBox}
                damaged={detail.damaged}
                src={detail.keyframe_ ? detail.keyframe_ : detail.keyframe}
                isDelete={detail.isDelete === 1}
                resourceGroup={resourceGroup}
              />}
              {detail.isDelete === 1 && (
                <span className="isDeleteSpan">{intl.formatMessage({ id: '已失效' })}</span>
              )}
            </div>}
            {title}
            {publishManagement && (publishManagement === 'remove' ? <div style={{ cursor: 'pointer', marginRight: '20px' }} onClick={() => batchOperate?.([detail], 0)}>
              <Tooltip title={intl.formatMessage({ id: '发布' })}>
                <IconFont type="iconparticipation" />
              </Tooltip>
              <span style={{ marginLeft: '8px' }}>发布</span>
            </div> :
              <div style={{ cursor: 'pointer', marginRight: '20px' }} onClick={() => batchOperate?.([detail], 1)}>
                <Tooltip title={intl.formatMessage({ id: '下架' })}>
                  <IconFont type="iconparticipation" />
                </Tooltip>
                <span style={{ marginLeft: '8px' }}>下架</span>
              </div>)}
            {!hideBtn && (
              <div className={`content_links${buttonList.length == 0 ? ' empty' : '' }`} >
                {buttonList.length > 0 && (
                  <Popover
                    placement="rightTop"
                    getPopupContainer={(e: any) => e.parentElement} //父级用了缩放后不能在使用了
                    open={moreMenuVisible}
                    onOpenChange={(newOpen: boolean) =>
                      setMoreMenuVisible(newOpen)
                    }
                    content={
                      <div className="moreOpt">
                        {buttonList.map((item: any, index: number) => {
                          return (
                            <div
                              key={index}
                              onClick={(e) => {
                                e.stopPropagation()
                                popoverClick(item.func)
                              }}
                            >
                              {item.icon}
                              <span>{item.title}</span>
                            </div>
                          );
                        })}
                      </div>
                    }
                  >
                    <div
                      onClick={(e: any) => {
                        e.preventDefault();
                        e.stopPropagation();
                        setMoreMenuVisible(!moreMenuVisible);
                      }}
                    >
                      <IconFont className="moreBtn" type="iconshiyongqingkuang" />
                    </div>
                  </Popover>
                )}
              </div>
            )}
          </div>
          {columns.includes('userName') && (<div className="sharer item width1"> {detail.userName ? detail.userName : '-'}</div>
          )}
          {columns.includes('shareUserName') && (
            <div className={`item width1 ${detail.shareType === 1 ? 'linkshare' : '' }`}>
              {//0是系统用户分分享 1是链接分享
                detail.shareType === 0 ? (
                  detail.shareUserName ? (
                    <Tooltip title={detail.shareUserName}>
                      {detail.shareUserName.length > 15
                        ? detail.shareUserName.slice(0, 15) + '...'
                        : detail.shareUserName}
                    </Tooltip>
                  ) : (
                    '-'
                  )
                ) : intl.formatMessage({ id: '链接分享' })
              }
            </div>
          )}
          {columns.includes('readPeople') && (
            <div className="readPeople item width1">
              {detail.readPeople >= 0 ? detail.readPeople + intl.formatMessage({ id: '次' }) : '-'}
            </div>
          )}
          {columns.includes('validity') && (
            <div className="validity item width1">
              {detail.validity
                ? detail.validity === -1
                  ? intl.formatMessage({ id: '永久有效' })
                  : `${detail.validity}${intl.formatMessage({ id: '天' })}`
                : '-'}
            </div>
          )}
          {columns.includes('extension') && (
            <div className="extension item width1">
              {detail.fileext ? detail.fileext : '-'}
            </div>
          )}
          {columns.includes('size') && (
            <div className="size item width1">
              {detail.filesize ? changesize(detail.filesize) : ''}
            </div>
          )}
          {columns.includes('directory') && (
            <div>
              {detail?.tree_?.length > 0 || detail?.directory.length > 0 ? (
                <Tooltip title={gettip(detail.tree_ || [detail.directory])}>
                  <div className="people item width1">
                    <a
                      style={{
                        textDecoration: 'underline',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        width: '100%'
                      }}
                      onClick={() => goDetail(2)}
                    >
                      {getdirectory(detail.tree_ || [detail.directory])}
                    </a>
                  </div>
                </Tooltip>
              ) : (
                ''
              )}
            </div>
          )}
          {columns.includes('teacher') && (
            <div className="people item teacher_name">
              {detail.teacher_names ? (
                <Tooltip title={detail.teacher_names.toString()}>
                  <span>{detail.teacher_names.toString()}</span>
                </Tooltip>
              ) : (
                ''
              )}
            </div>
          )}
          {columns.includes('deleteUser_') && (
            <div className="people item width1">{detail.deleteUserName_}</div>
          )}
          {columns.includes('deleteTime_') && (
            <div className="time item width1">{detail.deleteTime_}</div>
          )}
          {columns.includes('source') && (
            <div className="people item width1">{detail.source}</div>
          )}
          {columns.includes('person') && (
            <div className="people item width1">
              {detail.type_ === 'folder'
                ? detail.createUserName
                : detail.importuser}
            </div>
          )}
          {columns.includes('semester') && (
            <div className="time item ">
              {detail.semester ? detail.semester : ''}
            </div>
          )}
          {columns.includes('time') && (
            <div className="time item ">{detail.createDate_}</div>
          )}
          {columns.includes('smarttag') &&
            rmanGlobalParameter.includes(globalParams.smart_tag_display) && (
              <div className="type item width1">
                {detail.type_ === 'biz_sobey_video'
                  ? detail.smart_status === '1'
                    ? intl.formatMessage({ id: '分析中' })
                    : detail.smart_status === '2'
                      ? intl.formatMessage({ id: '已分析' })
                      : detail.smart_status === -1
                        ? intl.formatMessage({ id: '分析失败' })
                        : intl.formatMessage({ id: '未分析' })
                  : '-'}
              </div>
            )}
          {columns.includes('voice') &&
            rmanGlobalParameter.includes(
              globalParams.speech_analysis_display,
            ) && (
              <div className="type item width1">
                {(() => {
                  if (detail.type_ === 'biz_sobey_audio' || detail.type_ === 'biz_sobey_video') {
                    if (Number(detail.asr_status) === 2) {
                      return intl.formatMessage({ id: '分析完成' })
                    } else if (Number(detail.asr_status) === 1) {
                      return intl.formatMessage({ id: '进行中' })
                    } else if (Number(detail.asr_status) === 0) {
                      return intl.formatMessage({ id: '未分析' })
                    } else if (Number(detail.asr_status) === -1) {
                      return intl.formatMessage({ id: '分析失败' })
                    } else {
                      return '-'
                    }
                  }
                })()
                }
              </div>
            )}
          {columns.includes('intelliState') &&
            rmanGlobalParameter.includes(
              globalParams.knowledge_analysis_display,
            ) && (
              <div className="type item width1">
                {/* {detail.type_ === 'biz_sobey_video'
                  ? Number(detail.ocr_status) === 1
                    ? '分析中'
                    : Number(detail.ocr_status) === 2
                      ? '已分析'
                      : Number(detail.ocr_status) === -1
                        ? '分析失败'
                        : '未分析'
                  : '-'} */}
                {detail.type_ === 'biz_sobey_video'
                  ? Number(detail.intelliState) === 2
                    ? intl.formatMessage({ id: '已有' })
                    : intl.formatMessage({ id: '暂无' })
                  : '-'}
              </div>
            )}
          {columns.includes('remarks') && (
            <div className="item width1">{
              detail?.remarks ? (() => {
                try {
                  const parsedRemarks = JSON.parse(detail.remarks);
                  return Array.isArray(parsedRemarks) ? parsedRemarks.map((item:any) => item.value).join(',') : '-';
                } catch (error) {
                  console.error('解析remarks失败:', error);
                  return '-';
                }
              })() : '-'
            }</div>
          )}
         
          {columns.includes('status') && (
            <div className="type item width1">
              {/* {(detail.type_ === 'folder') ? '-' : detail.release_status == 'yes' ? '已使用' : '未使用'} */}
              {detail.usestatus == 'true' ? intl.formatMessage({ id: '已使用' }) : intl.formatMessage({ id: '未使用' })}
            </div>
          )}
          {columns.includes('duration') && (
            <div className="type item width1">
              {detail.duration ? l100Ns2Tc$1(detail.duration, detail.framerate) : '-'}
            </div>
          )}
          {columns.includes('shared_state') && (
            <div className="type item width1">{detail.shared_state == '1' ? intl.formatMessage({ id: '已共享' }) : intl.formatMessage({ id: '未共享' })}</div>
          )}
          {columns.includes('deleteUserName_') && (
            <div className="deleteUserName_ item width1">{detail.deleteUserName_}</div>
          )}
          {columns.includes('type') && (
            <div className="type item width1">{options[detail.type_]}</div>
          )}
          {columns.includes('shareTime') && (
            <div className="shareTime item width1">
              {detail.shareTime ? timeTransfer(detail.shareTime) : '-'}
            </div>
          )}
          {columns.includes('publishUser') && (
            <div className="publishUser item width1">
              {detail.applyUserName || '-'}
            </div>
          )}
          {columns.includes('classificationNames') && (
            <div className="item width1">{detail.classificationNames || '-'}</div>
          )}
          {columns.includes('publishTime') && (
            <div className="time item width1">
              {detail.applyDateTime || '-'}
            </div>
          )}
          {columns.includes('auditUserName') && (
            <div className="auditUserName item width1">
              {detail.auditUserName || '-'}
            </div>
          )}
         
          {columns.includes('auditDateTime') && (
            <div className="time item width1">
              {detail.auditDateTime || '-'}
            </div>
          )}
          {/* 我的审核 start*/}
          {columns.includes('sensitive') && (
            <div className="item width1 type-name">{detail.isSensitive ? '是' : '否'}</div>
          )}
          {columns.includes('instanceType') && (
            <div className="item width1 type-name">{detail.instanceType || '-'}</div>
          )}
          {!mineVerify && columns.includes('auditState') && (
            <div className="item width1 audit_state">{detail.auditState || detail.auditStateName || '-'}</div>
          )}
          {
            mineVerify && columns.includes('auditState') && (
              <div className="item width1 audit_state">{detail.auditStateName || '-'}</div>
            )
          }
          {columns.includes('createUserName') && (
            <div className="item width1 teacher">{detail.createUserName || detail.applyUserName || '-'}</div>
          )}
          {columns.includes('applyUserName') && (
            <div className="item width1 teacher">{detail.applyUserName || '-'}</div>
          )}

          {columns.includes('auditCreator') && (
            <div className="item width1">{detail.auditCreator || detail.auditUserName || '-'}</div>
          )}
          {columns.includes('filesize') && (
            <div className="item width1 auditor">{(detail.filesize || detail.fileSize) ? changesize(detail.filesize || detail.fileSize) : '-'}</div>
          )}
          {columns.includes('createdDate') && (
            <div className="c_time item width1">{detail.createdDate || detail.applyDateTime || '-'}</div>
          )}
          {columns.includes('createTime') && (
            <div className="c_time item width1">{detail.createTime || detail.applyDateTime || '-'}</div>
          )}
          {columns.includes('auditTime') && (
            <div className="a_time item width1">{detail.auditTime || detail.auditDateTime || '-'}</div>
          )}
          {columns.includes('progress') && (
            <div className="item width1 progress">{detail.progress || detail.auditState || '-'}</div>
          )}
          {/* 我的审核 end*/}
          {det}
        </>}
      </div>
    );
  }
};


export default ContentItem; 
