# mam angular1 通用库
[TOC]

## 依赖
> [jQuery](http://jquery.com/)

> [lodash](https://lodash.com/)

> [angularjs](https://angularjs.org/)

> [angular-ui-router](https://ui-router.github.io/)

> [angular-ui-bootstrap](https://angular-ui.github.io/bootstrap/)

> [mam-base](http://172.16.128.150/front-end/mam-base)

> [requirejs](http://requirejs.org/)

## Core

### **mam.ng.core.registerRouter(ngapp,routers)**

#### 介绍

用于注册路由(依赖ui-router)

#### 参数
- ngapp：主模块
- routers：路由对象

> #### controller命名规则
- 文件夹结构:
> — app  
+-- controller  
+---- cc  
+------ main.js  
+------ search.js  
+---- login.js  
+---- main.js  
- 文件名：以模块名命名 如：main.js
- 控制器名：以文件夹结构加Ctrl，并且以小驼峰(lower camel case)规则命名，如ccSearchCtrl


#### 示例
- appjs

```js
var app = angular.module('app',['mam-ng', 'ui.router']);
mam.ng.core.registerRouter(app,routes);
```
- routersjs

```js
  {
    'login': {
      home: true, // 是否是主页
      url: '/login', // 页面路径
      cv: 'login' // 控制器于模板名
    },
    'main': {
      url: '/main',
      cv: 'main',
      children: { // 子路由
        'cc': {
          url: '/cc',
          cv: 'cc/main',
          children: {
            'search': {
              url: '/search?page&?size&?keywords', // ui-router传参定义
              cv: 'cc/search',
              params: {
                page: '1',
                size: '12',
                keywords: {value:[], array: true}
              }
            }
          }
        }
      }
    }
  }
```

---

## 指令

### **mam-checkbox** (A)

#### 介绍
用于代替略丑的默认checkbox

#### 参数
- ngModel：是否勾选，只能是布尔值或可转换成布尔值的。



``` html
<label class="mam-checkbox">
    <input type="checkbox" mam-checkbox ng-model="test1">
    <span>测试</span>
</label>
```
#### 说明
- label的class mam-checkbox 可不写，指令里面会自动添加
- input type="checkbox" 必须外层使用label包裹
- 文字建议使用 span 包裹

#### 尺寸
自带两种尺寸： 正常尺寸、小尺寸  
可自行通过编写css来实现更多尺寸  
> 小尺寸使用方法：在 label 上增加  `ms` class
``` html
<label class="mam-checkbox ms">
```

---

### **mam-resizer** (E)

#### 介绍
可实现左右或上下拖动来改变两个容器的宽度或高度。

#### 参数
- direction：方向，可选值：`h` (水平) 、 `v` (垂直)
- width：resizer自身的宽度，仅在 direction为 h 有效，不用带单位，内部默认为px。
- height：resizer自身的高度，仅在 direction为 v 有效，不用带单位，内部默认为px。

#### 示例
``` html
<div class="left1">A</div>
<mam-resizer class="resizer1" direction="h" width="10px" left=".left1" right=".right1"></mam-resizer>
<div class="right1">
```
#### 说明
- offset 参数是用来修正拖拽时的位置，主要是因为代码中没有自动去获取这部分距离，才让手动设置的。后期有需要再改进

---

### **mam-back-to-top** (E)
#### 介绍
点击回到顶部

#### 参数
- container：容器
  - 类型：jquery选择器(字符串)
  - 必填：否
  - 默认值：window
- text：Tooltip文本
  - 类型：字符串
  - 必填：否
  - 默认值：l('com.backTop', '回到顶部')
- threshold：阈值
  - 类型：数字
  - 必填：否
  - 默认值：400
  - 单位：像素

#### 示例
``` html
<mam-back-to-top text="回到顶部"></mam-back-to-top>
```

---

### **mam-all-checkbok** (A)
#### 介绍
全选复选框

#### 参数
- collection：关联集合
  - 类型：字符串
  - 必填：是
- property：关联集合中决定是否选中的属性
  - 类型：字符串
  - 必填：否
  - 默认值：selected
- ngModel：是否选中，参考checkbox的ngModel参数

#### 示例
``` html
<label class="mam-checkbox">
    <input type="checkbox" mam-checkbox ng-model="allSelected" mam-all-checkbox collection="items" property="selected">
    <span>全选</span>
</label>
```

---

### **mam-radio** (A)
#### 介绍
用于代替略丑的默认radio

#### 参数
- ngValue：关联的值
  - 类型：任意
  - 必填：是
- ngModel：判定是否勾选的值
  - 类型：任意
  - 必填：是

#### 示例
``` html
<label class="mam-radio">
    <input type="radio" mam-radio ng-model="test1" ng-value="1">
    <span>测试1</span>
</label>
<label class="mam-radio">
    <input type="radio" mam-radio ng-model="test1" ng-value="2">
    <span>测试1</span>
</label>
```

#### 说明
`没有测试过是否支持分组radio，可能不支持。`


---

### **mam-keyframe** (E)
#### 介绍
通用的关键帧显示组件，当后端没有返回关键帧或关键帧地址无法打开时，组件会根据一套规则使用其他图片作为关键帧。  
> 规则：优先使用自身的关键帧，如果没有或加载失败，根据素材扩展名去找关键帧，如果还没有则根据素材类型去照关键帧，如果也没找到则使用 其他类型的关键帧

#### 参数
- src：关键帧地址
  - 类型：字符串
  - 必填：是
- ext：扩展名
  - 类型：字符串
  - 必填：否
- type：素材类型
  - 类型：字符串
  - 必填：否

#### 示例
``` js
$scope.type = 'video';
$scope.ext = '.mp4';
$scope.src = 'http://xxxx.jpg';
```
``` html
<mam-keyframe src="src" type="type" ext="ext"></mam-keyframe>
```

#### 说明
`在使用前，务必进行关键帧的配置。`
``` js
app.config(function($mamKeyframeProvider){
    $mamKeyframeProvider.defaults.extKeyframes = [
        //.......
    ];
    $mamKeyframeProvider.defaults.typeKeyframes = [
        //.......
    ];
});
```