import React, { FC, useState, useEffect, useRef } from 'react';
import { Form, Input, Button, Tooltip, Modal, message, Breadcrumb, Empty, Pagination, Select, Checkbox } from 'antd';
import { Store } from 'antd/lib/form/interface';
import { RouteComponentProps } from 'react-router-dom';
import './index.less';
import { history, useDispatch, useSelector, useParams, Idownlist, useIntl } from 'umi';
import loginApis from '@/service/loginApis';
import Loading from '@/components/loading/loading';
import { Base64 } from 'js-base64';
import Header from '@/components/header';
import contentListApis from '@/service/contentListApis';
import { BreadCrumb, ISearchlist, MaterialList, MenuItem } from '../contentlibrary/contentList/type';
import { ContentItem, DownloadModal, IconFont, ListTop } from '@/components';
import ColumnSelect, { allColumns } from '@/components/ColumnSelect';
import { timeTransfer } from '@/utils';
import { IPermission } from '@/models/permission';

interface ILoginParams {
  username: string;
  password: string;
  remember: boolean;
}

const ShareDetail: FC<RouteComponentProps> = props => {
  const configs: any = useSelector<{ config: any }>(({ config }) => config);
  const [userInfo, setUserInfo] = useState<any>(null);
  const [password, setPassword] = useState<any>('');
  const [visible, setVisible] = useState<boolean>(false);
  const [details, setDetails] = useState<any>(undefined);
  const [downEnable, setDownEnable] = useState<boolean>(false);
  // const [selectRows, setSelectRows] = useState<any>([]);
  const [selectRowkeys, setSelectRowkey] = useState<any>([]);
  const [current, setCurrent] = useState<number>(1); //当前页码
  const [pageSize, setPageSize] = useState<number>(30); //每页条数
  const [collation, setCollation] = useState<string>('timedown'); //排序
  const [totalPage, setTotalPage] = useState<number>(0); //素材总数
  const mySharedCurrentPath = useRef<any>(undefined); // 我的分享 当前文件名
  const [breadCrumb, setBreadCrumb] = useState<Array<BreadCrumb>>([
    { name: '', folderId: '', path: '' },
  ]);
  const [columnSelect, setColumnSelect] = useState<any>(
    [
      'extension',
      'size'
    ]
  );
  const [indeterminate, setIndeterminate] = useState<boolean>(false);
  const [checkAll, setCheckAll] = useState<boolean>(false);
  const [checkedList, setCheckedList] = useState<Array<any>>([]); //选中的列表
  const [allList, setAllList] = useState<Array<any>>([]); //总的的列表
  const [folderId, setFolderId] = useState<string>(''); //文件夹ID
  const [folderPath, setFolderPath] = useState<string>(''); //文件夹路径
  const [modeSwitch, setModeSwitch] = useState<boolean>(
    localStorage.getItem('view-mode') === '1',
  ); // 视图切换
  const params = useParams<{ link: string }>();
  const CheckboxGroup = Checkbox.Group;
  const [copyShow, setCopyShow] = useState<boolean>(false);
  const dispatch = useDispatch();
  const [downloadModalVisible, setDownloadModalVisible] = useState<boolean>(
    false,
  );
  const downlist = useSelector<{ download: any }, Idownlist>(({ download }) => {
    return download.downlist;
  });
  const { parameterConfig } = useSelector<{ permission: any }, IPermission>(({ permission }) => permission);
  const intl = useIntl();
  //对于分享添加link参数
  const shareLink = useRef<any>('');
  useEffect(() => {
    console.log(params.link);
    const link = params.link?.split('_');
    if (link.length < 2) {
      message.error(intl.formatMessage({ id: '无效链接' }));
    } else {
      if (link[1] === '0') { //无密码
        setVisible(false)
        queryDetail(params.link.split('_')[0])
      } else if (link[1] === '1') { //有密码
        setVisible(true)
      }
    }
    if ((window as any).login_useInfo) {
      // console.log((window as any).login_useInfo)
      setUserInfo((window as any).login_useInfo)
    }
  }, [(window as any).login_useInfo])
  const passwordChange = (e: any) => {
    e.persist();
    console.log('passwordChange', e)
    setPassword(e.target.value)
  }
  const { Option } = Select;
  const login = () => {
    if (!password) {
      message.info(intl.formatMessage({ id: '请输入密码' }));
    } else {
      queryDetail(params.link.split('_')[0], password)
    }
  }
  const queryDetail = async (link: any, pass?: any) => {
    const ress = await contentListApis.fetchShareDetail(link, pass);
    contentListApis.markReadLink(link, '&isSysAuth=true');
    console.log(ress)
    if (ress?.success) {
      setVisible(false);
      setDetails(ress.data[0]);
      shareLists(ress.data[0]);
      setDownEnable(!ress.data[0].onlyRead);
    }
  }

  useEffect(() => {
    if(details) {
      // 判断是不是详情
      if(mySharedCurrentPath.current) {
        searchFolderList(mySharedCurrentPath.current)
      } else {
        shareLists(details);
      }
    }
  }, [current, pageSize]);
  //查询链接分享的
  const shareLists = async (detail: any, page?: number) => {
    shareLink.current = detail?.link || '';
    contentListApis
      .fetchShareLists({
        contentIds: detail.contentIds,
        link: detail.link,
        password: detail.linkPassword,
        shareType: detail.shareType,
        page: page || current,
        size: pageSize,
      })
      .then(res => {
        if (res && res.data && res.success) {
          setTotalPage(res.data.recordTotal);
          if (res.data.pageTotal < 0 && current !== 1) {
            setCurrent(current - 1);
            return;
          }
          setAllList(res.data.data);
          setTotalPage(res.data.recordTotal);
          // setSelectRows([]);
          setBreadCrumb([{
            path: '',
            folderId: '',
            name: detail?.name_,
            item: detail
          }]);
          mySharedCurrentPath.current = '';//必须要清空之前选中的路径值
        } else {
          setAllList([]);
        }
      });
  };
  // 导航条跳转
  const goBreadcrumb = (item: BreadCrumb) => {
    console.log('item', item)
    // if (item.folderId !== folderId) {
    console.log(item.path, folderPath)
    //针对我的分享 文件夹回退
    if (!item.path && !item.folderId) {
      mySharedCurrentPath.current = '';
      shareLists(details);
      setCurrent(1)
      setBreadCrumb([])
    } else {
      searchFolderList(item.path, 1);
    }
  };
  // 数据处理
  const processingData = (path?: any) => {
    let list: ISearchlist = {
      // folderId: folderPath ? '' : folderId, //针对面包屑无id情况 就不能传ID了 不然是上选中文件夹的id 会查不出数据
      folderId: folderPath ? '' : folderId, //针对面包屑无id情况 就不能传ID了 不然是上选中文件夹的id 会查不出数据
      folderPath: path ? (path + '/') : folderPath + '/',
      sortFields: [
        {
          field: collation.indexOf('time') != -1 ? 'createDate_' : 'name_',
          isDesc: collation.indexOf('down') != -1 ? true : false,
        },
      ],
      pageIndex: current,
      pageSize: pageSize,
    };
    list.conditions = [];
    return list;
  };
  // 公共素材检索
  const searchFolderList = async (path?: any, page?: number) => {
    let data: ISearchlist = processingData(path);//针对别人分享个人文件夹无权限时；
    if(page) {
      data.pageIndex = page
    }
    contentListApis.searchfolder_share(data,shareLink.current).then(res => {
      if (res && res.data && res.success) {
        setTotalPage(res.data.recordTotal);
        if (res.data.pageTotal < 0 && current !== 1) {
          setCurrent(current - 1);
          return;
        }
        if (res.data.breadcrumbs[0].name === '公共资源') {
          res.data.breadcrumbs.splice(0, 1)
          console.log(res.data.breadcrumbs)
        }
        setAllList(res.data.data);
        setCheckedList([]);
        // setSelectRows([]);
        setIndeterminate(false);
        setCheckAll(false);
        //对我的分享定制路径
        for (let i = 0; i < res.data.breadcrumbs.length; i++) {
          if (res.data.breadcrumbs[i].path === mySharedCurrentPath.current) {
            res.data.breadcrumbs.splice(0, i)
            break
          }
        }
        res.data.breadcrumbs.unshift({
          // name: sharedActiveKey === '1' ? '分享给我的' : '我分享的',
          name: details.name_,
          // item:shareInnerFlag,
          folderId: '',
          path: ''
        })
        setBreadCrumb(res.data.breadcrumbs);
      } else {
        setAllList([]);
      }
    })
  }
  // 页码切换
  const changepage = (page: number, size: any) => {
    if (page === 0) {
      setCurrent(1);
      setPageSize(size || 0);
    } else {
      setCurrent(page);
      setPageSize(size || 0);
    }
    localStorage.setItem('rman_resource_size', size)
  };
  // 跳转到详情  type  1是点击的文件名跳转  2是点击所在目录 跳转
  const detail = (item: MaterialList, type: number) => {
    //取上级路径备
    const parentPath_ = item.tree_[0].split('/');
    parentPath_.pop();
    if (item.type_ === 'folder') {
      //针对我的分享得单独处理
      // 手动传path 避免与其他混淆
      !mySharedCurrentPath.current ? mySharedCurrentPath.current = item.tree_[0] + '/' + item.name_ : '';
      setCurrent(1)
      searchFolderList(item.tree_[0] + '/' + item.name_, 1);
      // 点击则调用已读接口
      if(item.link){
        contentListApis.markReadLink(item.link);
      }
    } else {
      // 资源
      if (type == 1) {
        // 点击则调用已读接口
        if (item.isRead === false && item.link) {
          item.isRead = true; //手动标记已读
          contentListApis.markReadLink(item.link).then((res: any) => {
            if (res?.success) {
              // fetchSharedNums()//标记完毕时 实时刷新未读数量；
            }
          });
        }
        // 点击名称
        const dev = process.env.NODE_ENV === 'development' ? "" : "/rman";
        const src = `${window.location.origin}${dev}/#/basic/rmanDetailV2/${item.contentId_}_share_${shareLink.current}`;
        window.open(src);
      } else {
        // 点击目录
        setFolderId('');
        setFolderPath(item.tree_[0]);
        // searchFolderListForLeftTree(parentPath_.join('/'), getdirectory(item.tree_[0]))
        // referRef?.current.selectnode(item);
        // reset(type);
      }
    }
  };
  // 排序切换
  const handleChange = (value: string) => {
    setCollation(value);
    setCurrent(1);
  };
  // 全选
  const onCheckAllChange = (e: any) => {
    setCheckedList(e.target.checked ? allList : []);
    // setSelectRows(e.target.checked ? allList : []);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  };
  // 单选
  const onChange = (check: Array<any>) => {
    setCheckedList(check);
    // setSelectRows(check);
    setIndeterminate(!!check.length && check.length < allList.length);
    setCheckAll(check.length === allList.length);
  };
  useEffect(() => {
    // 判断是否包含文件夹
    let i = checkedList.some((item: any) => {
      return item.type_ === 'folder';
    });
    // 判断是否全为视频资源
    let k = checkedList.every((item: any) => {
      return (
        item.type_ === 'biz_sobey_video' || item.type_ === 'biz_sobey_audio'
      );
    });
    // 判断是否全为视频、文档、图片 供绑定知识点使用
    let l = checkedList.every((item: any) => {
      return (
        item.type_ === 'biz_sobey_video'
        || item.type_ === 'biz_sobey_picture'
        || item.type_ === 'biz_sobey_audio'
        || item.type_ === 'biz_sobey_document'
      );
    });
    setCopyShow(i);
  }, [checkedList]);
  const opendownloadbox = () => {
    dispatch({
      type: 'download/changedownload',
      payload: {
        value: checkedList,
      },
    });
    setDownloadModalVisible(true);
  };
  const opendownloadbox_single = () => {
    setDownloadModalVisible(true);
  };

  return (
    <div className={`share-container ${parameterConfig.target_customer as string =='npu' ? 'share-containeraxxt' : ''} 
    ${configs.mobileFlag ? 'share-containerMobile' : ''}`} >
      <Header
        subtitle={''}
        showNav={false}
        user_Info={userInfo}
      />
      <Modal
        title={intl.formatMessage({ id: '输入密码' })}
        open={visible}
        closable={false}
        className='modal_share'
        width={451}
        footer={[
          <Button
            type='primary'
            onClick={login}
          >
            {intl.formatMessage({ id: '登录' })}
          </Button>
        ]}
      >
        <Input placeholder={intl.formatMessage({ id: '请输入密码' })} onChange={passwordChange} onPressEnter={login} />
      </Modal>
      {
        details &&
        <div className='content'>
          <div className='shareinfos'>
            <div className='name'>{details.name_}</div>    
            <div className='timeList'>
                {
                  configs.mobileFlag ? <>
                    <div className="left">
                      <div><img src={require("@/images/icons/share.png")}/><span>{intl.formatMessage({ id: '分享人' })}：</span></div>
                      <div><img src={require("@/images/icons/time.png")}/>{intl.formatMessage({ id: '分享时间' })}：</div>
                      <div><img src={require("@/images/icons/time.png")}/>{intl.formatMessage({ id: '到期时间' })}：</div>
                    </div>
                    <div className="right">
                      <div>{details.userName}</div>
                      <div>{timeTransfer(details.shareTime)}</div>
                      <div>{details.expirationTime}</div>
                    </div>
                  </> : <>
                    <span className='text username'><img src={require("@/images/icons/share.png")}/>{`${intl.formatMessage({ id: '分享人' })}：${details.userName}`}</span>
                    <span className='text sharetime'><img src={require("@/images/icons/time.png")}/>{`${intl.formatMessage({ id: '分享时间' })}：${timeTransfer(details.shareTime)}`}</span>
                    <span className='text expirationtime'><img src={require("@/images/icons/time.png")}/>{`${intl.formatMessage({ id: '到期时间' })}：${details.expirationTime}`}</span>
                  </>
                }
            </div>
          </div>
          <div className='opt_btn'>
            <div className='top_left'>
              <Checkbox
                indeterminate={indeterminate}
                onChange={onCheckAllChange}
                checked={checkAll}
              >
              {intl.formatMessage({ id: '全部' })}
              </Checkbox>
              {/* <Button
                disabled={!userInfo || (!downEnable)}

              >保存至</Button> */}
              <Button
                // disabled={selectRows.length === 0 || !downEnable || breadCrumb.length < 2}
                disabled={checkedList.length === 0 || !downEnable || copyShow}
                onClick={opendownloadbox}
              >{intl.formatMessage({ id: '下载' })}</Button>
            </div>
            <div className='right_opt'>
              <div
                onClick={() => setModeSwitch(true)}
                className="mode_switch"
              >
                <Tooltip title={intl.formatMessage({ id: '图例模式' })}>
                  <IconFont
                    type="iconhebingxingzhuangfuzhi2"
                    className={modeSwitch ? 'active' : ''}
                  />
                </Tooltip>
              </div>
              <div
                onClick={() => setModeSwitch(false)}
                className="mode_switch"
              >
                <Tooltip title={intl.formatMessage({ id: '列表模式' })}>
                  <IconFont
                    type="iconliebiao"
                    className={modeSwitch ? '' : 'active'}
                  />
                </Tooltip>
              </div>
            </div>
          </div>
          <div className='body'>
            <div className="box_bottom">
              {(
                <Breadcrumb>
                  {breadCrumb.map((item, index) => (
                    <Breadcrumb.Item key={index}>
                      <a onClick={() => goBreadcrumb(item)}>{item?.name}</a>
                    </Breadcrumb.Item>
                  ))}
                </Breadcrumb>
              )}
              <div
                className={
                  (allList.length === 0 ? "contentitem_box_empty" : "contentitem_box")
                  + (!modeSwitch ? ' title_Header' : '')
                }
                style={!modeSwitch ? { minWidth: configs.mobileFlag ? '100%' : 900 } : {}}
              >
                {modeSwitch ? (
                  ''
                ) : (
                  <ListTop columns={columnSelect}/>
                )}
                {allList.length === 0 ? (
                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                ) : (
                  ''
                )}
                <CheckboxGroup
                  value={checkedList}
                  onChange={onChange}
                  // className={`${modeSwitch ? (allList.length === 0 && getFolder === null ?'height1_empty':'height1') : 'height2'} height`}
                  // modeSwitch 为true时是图例模式
                  className={`${modeSwitch ? 'height3_shared'
                    : 'height3_shared_list'} height`}
                  style={{ width: '100%' }}
                >
                  {allList.map((item, index) => (
                    <ContentItem
                      key={item.uniqueCode ? item.uniqueCode : item.contentId_} //针对我的分享里面会存在重复文件情况
                      columns={configs.mobileFlag ? [] : columnSelect}
                      modal={modeSwitch}
                      detail={item}
                      recycleBin={false}
                      downloadBoxOpen={opendownloadbox_single}
                      downEnable={downEnable ? 1 : 2}
                      shareDetailHidden={downEnable?false:true}
                      myShare={1}
                      goDetail={(type: number) => detail(item, type)}
                      // refresh={refresh}
                      shareEnble={false}
                      resourceGroup={false}
                    />
                  ))}
                </CheckboxGroup>
              </div>
            </div>
            <div className="pagination">
              <Pagination
                current={current}
                pageSize={pageSize}
                total={totalPage}
                size="small"
                showQuickJumper
                onChange={changepage}
                showTotal={total => intl.formatMessage({id: "共条"}, {total})}
                showSizeChanger={true}
                pageSizeOptions={['30', '40', '50', '100']}
              // showTotal={total => intl.formatMessage({
              //   id: "page-total",
              //   defaultMessage: `共 ${total} 页`
              // })}
              />
            </div>
          </div>
        </div>
      }
      <DownloadModal
        shareFlag={true}
        modalVisible={downloadModalVisible}
        modalClose={() => setDownloadModalVisible(false)}
        // downloadlist={checkedList}
        downloadlist={downlist}
      />
    </div >
  );
};

export default ShareDetail;
