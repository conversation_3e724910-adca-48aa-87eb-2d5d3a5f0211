.verify-container {
  padding: 0 0 0 15px;
  height: 100%;
  .body {
    .back {
      cursor: pointer;
      margin-bottom: 10px;
    }
    height: calc(100% - 118px);
    display: flex;
    flex-direction: column;
    .searchBox {
      display: flex;
      .ant-form {
        flex: 1;
        .ant-col {
          margin-right: 15px;
          .ant-select {
            width: 100%;
          }
        }
      }
      .ant-segmented {
        margin-right: 22px;
      }
      .result_search_buttom {
        display: flex;
      }
    }
    .operatBox {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 10px 20px 10px 0;
      .left_ {
        display: flex;
        justify-content: center;
        align-items: center;
        > div {
          height: 25px;
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 0 10px;
          position: relative;
          color: #525252;
          cursor: pointer;
          &:hover {
            color: var(--primary-color);
          }
          &[disabled] {
            color: rgba(0, 0, 0, 0.25);
          }
          > span:last-child {
            margin-left: 8px;
          }
          &.disabled {
            color: rgba(0, 0, 0, 0.25);
          }
          &:not(:last-child)::after {
            content: '';
            display: inline-block;
            width: 1px;
            height: 16px;
            background: #d8d8d8;
            right: 0;
            top: 4px;
            position: absolute;
          }
        }
      }
    }
    .list {
      height: calc(100% - 120px);
      overflow-y: auto;
    }
    .pagination {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
.pc_show {
  display: flex !important;
}

@media screen and (max-width: 768px) {
  .mobile_show {
    display: flex !important;
  }
  .pc_show {
    display: none !important;
  }
  .verify-container {
    padding: 0 15px;
    .root_{
      a{
        color:var(--primary-color) !important;
      }
    }
    &.verify-container-inner {
      .body {
        .operatBox {
          .left_ {
            .refresh_btn {
              display: none;
            }
            .ant-checkbox-wrapper {
              &::after {
                content: '全选';
                width: auto;
                color: var(--primary-color);
              }
              & > span {
                display: none;
              }
            }
          }
        }
        .searchBox{
          .back_btn{
            display: inline-block;
            width: 12px;
            height: 12px;
            border-top: 2px solid #000;
            border-left: 2px solid #000;
            transform: rotate(-45deg);
          }
        }
      }
      .list_content {
        .item {
          &.contenttitle {
            &::before {
              content: '名称';
            }
          }
          &.auditor {
            &::before {
              content: '审核人';
            }
          }
          &.audit_state {
            color: #ff9113;
            &::before {
              content: '审核状态';
            }
          }
          &.a_time {
            display: flex;
            &::before {
              content: '审核时间';
            }
          }
          &.c_time{
            display: flex;
            &::before {
              content: '发起时间';
            }
          }
          .moreBtn,
          .mobile_single_btns {
            display: none;
          }
          .ant-checkbox-inner {
            border-radius: 50%;
          }
        }
      }
    }
    .verify-tabs {
      padding-top: 10px;
      .ant-tabs-tab {
        padding: 6px 0;
        .ant-tabs-tab-btn {
          font-size: 15px;
          font-weight: 600;
          color: #9d9d9d;
        }
        &.ant-tabs-tab-active .ant-tabs-tab-btn {
          color: #272727;
        }
      }
      .ant-tabs-ink-bar {
        border-width: 0 6px;
        border-style: solid;
        border-color: #fff;
      }
    }
    .mobile_show {
      .ant-row {
        display: flex;
        align-items: center;
      }
      .search_box {
        border-radius: 99px;
        background-color: #f7f8fa;
        line-height: 38px;
        height: 38px;
        padding-left: 6px;
        input {
          border: unset !important;
          outline: unset !important;
          box-shadow: unset !important;
          background-color: unset !important;
        }
      }
    }
    .list_content {
      flex-direction: column;
      background-color: #fff !important;
      box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.07);
      border: 1px solid rgba(0, 0, 0, 0.01);
      border-radius: 11px;
      padding: 17px;
      box-sizing: border-box;
      margin-bottom: 15px;
      font-size: 15px;
      .item {
        width: 100%;
        justify-content: space-between;
        text-align: right;
        & + .item {
          margin-top: 13px;
        }
        &::before {
          padding-right: 20px;
          color: #9d9d9d;
        }
        &.contenttitle {
          padding-left: 0;
          &::before {
            content: '审核任务';
          }
        }
        &.type-name {
          display: none;
        }
        &.a_time {
          display: none;
        }
        &.c_time {
          display: none;
        }
        &.teacher {
          &::before {
            content: '发起人';
          }
        }
        &.progress {
          &::before {
            content: '审核进度';
          }
        }
        .checkbox {
          display: none;
        }
      }
    }
    .body {
      &>.back{
        display: none;
      }
      .list {
        height: calc(100% - 150px);
        margin-bottom: 12px;
      }
      .operatBox {
        margin-right: 0;
        margin-top: 17px;
        .left_ {
          & > div {
            white-space: nowrap;
            margin-left: 11px;
            &::after{
              display: none!important;
            }
            &:hover{
              color: inherit;
            }
          }
          .refresh_btn {
            background-color: var(--primary-color);
            color: #fff;
            border-radius: 99px;
            height: 32px;
            line-height: 32px;
            &:hover{
              color: #fff;
            }
          }
          .ant-checkbox-wrapper {
            span {
              white-space: nowrap;
            }
          }
          .pass_btn{
            background-color: #46C462;
            color: #fff;
            height: 32px;
            line-height: 32px;
            border-radius: 99px;
            span{
              color: #fff;
            }
            &.disabled{
              opacity: .6;
            }
          }
          .reject_btn{
            background-color: #FF575A;
            color: #fff;
            height: 32px;
            line-height: 32px;
            border-radius: 99px;
            span{
              color: #fff;
            }
            &.disabled{
              opacity: .6;
            }
          }
        }
        .right_ {
          .ant-select-selector {
            height: 32px;
            line-height: 32px;
            border: 1px solid #f0f0f0;
            border-radius: 99px;
          }
        }
      }
    }
  }
  .more_select_drawer {
    .ant-drawer-content-wrapper {
      height: auto !important;
      .ant-select {
        width: 100%;
      }
    }
    .footer {
      display: flex;
      justify-content: space-evenly;
      margin-top: 20px;
    }
    .ant-drawer-title {
      padding-right: 28px;
      text-align: center;
    }
  }
}
