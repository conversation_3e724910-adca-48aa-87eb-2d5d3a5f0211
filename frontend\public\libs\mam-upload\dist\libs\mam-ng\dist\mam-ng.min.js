!function(e){function t(o){if(n[o])return n[o].exports;var i=n[o]={i:o,l:!1,exports:{}};return e[o].call(i.exports,i,i.exports,t),i.l=!0,i.exports}var n={};t.m=e,t.c=n,t.i=function(e){return e},t.d=function(e,n,o){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:o})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=81)}([function(e,t){function n(e,t){var n=e[1]||"",i=e[3];if(!i)return n;if(t&&"function"==typeof btoa){var r=o(i);return[n].concat(i.sources.map(function(e){return"/*# sourceURL="+i.sourceRoot+e+" */"})).concat([r]).join("\n")}return[n].join("\n")}function o(e){return"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(e))))+" */"}e.exports=function(e){var t=[];return t.toString=function(){return this.map(function(t){var o=n(t,e);return t[2]?"@media "+t[2]+"{"+o+"}":o}).join("")},t.i=function(e,n){"string"==typeof e&&(e=[[null,e,""]]);for(var o={},i=0;i<this.length;i++){var r=this[i][0];"number"==typeof r&&(o[r]=!0)}for(i=0;i<e.length;i++){var a=e[i];"number"==typeof a[0]&&o[a[0]]||(n&&!a[2]?a[2]=n:n&&(a[2]="("+a[2]+") and ("+n+")"),t.push(a))}},t}},function(e,t,n){function o(e,t){for(var n=0;n<e.length;n++){var o=e[n],i=p[o.id];if(i){i.refs++;for(var r=0;r<i.parts.length;r++)i.parts[r](o.parts[r]);for(;r<o.parts.length;r++)i.parts.push(d(o.parts[r],t))}else{for(var a=[],r=0;r<o.parts.length;r++)a.push(d(o.parts[r],t));p[o.id]={id:o.id,refs:1,parts:a}}}}function i(e,t){for(var n=[],o={},i=0;i<e.length;i++){var r=e[i],a=t.base?r[0]+t.base:r[0],s=r[1],l=r[2],c=r[3],d={css:s,media:l,sourceMap:c};o[a]?o[a].parts.push(d):n.push(o[a]={id:a,parts:[d]})}return n}function r(e,t){var n=h(e.insertInto);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var o=b[b.length-1];if("top"===e.insertAt)o?o.nextSibling?n.insertBefore(t,o.nextSibling):n.appendChild(t):n.insertBefore(t,n.firstChild),b.push(t);else{if("bottom"!==e.insertAt)throw new Error("Invalid value for parameter 'insertAt'. Must be 'top' or 'bottom'.");n.appendChild(t)}}function a(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e);var t=b.indexOf(e);t>=0&&b.splice(t,1)}function s(e){var t=document.createElement("style");return e.attrs.type="text/css",c(t,e.attrs),r(e,t),t}function l(e){var t=document.createElement("link");return e.attrs.type="text/css",e.attrs.rel="stylesheet",c(t,e.attrs),r(e,t),t}function c(e,t){Object.keys(t).forEach(function(n){e.setAttribute(n,t[n])})}function d(e,t){var n,o,i,r;if(t.transform&&e.css){if(!(r=t.transform(e.css)))return function(){};e.css=r}if(t.singleton){var c=x++;n=v||(v=s(t)),o=u.bind(null,n,c,!1),i=u.bind(null,n,c,!0)}else e.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(n=l(t),o=f.bind(null,n,t),i=function(){a(n),n.href&&URL.revokeObjectURL(n.href)}):(n=s(t),o=m.bind(null,n),i=function(){a(n)});return o(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;o(e=t)}else i()}}function u(e,t,n,o){var i=n?"":o.css;if(e.styleSheet)e.styleSheet.cssText=w(t,i);else{var r=document.createTextNode(i),a=e.childNodes;a[t]&&e.removeChild(a[t]),a.length?e.insertBefore(r,a[t]):e.appendChild(r)}}function m(e,t){var n=t.css,o=t.media;if(o&&e.setAttribute("media",o),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}function f(e,t,n){var o=n.css,i=n.sourceMap,r=void 0===t.convertToAbsoluteUrls&&i;(t.convertToAbsoluteUrls||r)&&(o=y(o)),i&&(o+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(i))))+" */");var a=new Blob([o],{type:"text/css"}),s=e.href;e.href=URL.createObjectURL(a),s&&URL.revokeObjectURL(s)}var p={},g=function(e){var t;return function(){return void 0===t&&(t=e.apply(this,arguments)),t}}(function(){return window&&document&&document.all&&!window.atob}),h=function(e){var t={};return function(n){return void 0===t[n]&&(t[n]=e.call(this,n)),t[n]}}(function(e){return document.querySelector(e)}),v=null,x=0,b=[],y=n(77);e.exports=function(e,t){if("undefined"!=typeof DEBUG&&DEBUG&&"object"!=typeof document)throw new Error("The style-loader cannot be used in a non-browser environment");t=t||{},t.attrs="object"==typeof t.attrs?t.attrs:{},t.singleton||(t.singleton=g()),t.insertInto||(t.insertInto="head"),t.insertAt||(t.insertAt="bottom");var n=i(e,t);return o(n,t),function(e){for(var r=[],a=0;a<n.length;a++){var s=n[a],l=p[s.id];l.refs--,r.push(l)}if(e){o(i(e,t),t)}for(var a=0;a<r.length;a++){var l=r[a];if(0===l.refs){for(var c=0;c<l.parts.length;c++)l.parts[c]();delete p[l.id]}}}};var w=function(){var e=[];return function(t,n){return e[t]=n,e.filter(Boolean).join("\n")}}()},function(e,t){angular.module("mam-ng").directive("mamDropdown",function(){return{restrict:"A",link:function(e,t,n){e.mode="click";var o=$(t),i=o.find(".dropdown-toggle"),r=o.find(".dropdown-menu");r.hide();var a=!1,s=0;if("static"==i.parent().css("position")){var l=i.position().top+i.outerHeight()-2,c=i.position().left;r.css("top",l),r.css("left",c)}else o.css("position","relative");var d=function(){a=!a,i.toggleClass("dropdown-toggle-active"),r.not(r).stop().slideUp(200).parent().find(".dropdown-toggle"),r.stop().slideToggle(200)};"hover"==e.mode?(o.hover(function(){return clearTimeout(s),a||d(),!1},function(){return clearTimeout(s),a&&(s=setTimeout(d,250)),!1}),r.hover(function(){clearTimeout(s)},function(){return clearTimeout(s),a&&(s=setTimeout(d,250)),!1})):i.on("click",function(){return d(),!1}),r.find("li").on("click",function(){clearTimeout(s),a=!1,i.removeClass("dropdown-toggle-active"),r.stop().slideUp(200)}),$("html").on("click",function(){clearTimeout(s),a&&(a=!1,i.removeClass("dropdown-toggle-active"),r.stop().slideUp(200))})}}})},function(e,t){e.exports='<svg t="1501119020784" class="icon" style viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3157" xmlns:xlink="http://www.w3.org/1999/xlink"><defs><style type="text/css"></style></defs><path d="M528.612 146.023v771.24c0 9.23-7.383 16.612-16.612 16.612s-16.612-7.383-16.612-16.612v-770.977l-103.623 103.623c-6.328 6.328-16.875 6.328-23.203 0s-6.328-16.875 0-23.203l131.836-131.836c6.328-6.328 16.875-6.328 23.203 0l131.836 131.836c6.328 6.328 6.328 16.875 0 23.203s-16.875 6.328-23.203 0l-103.623-103.887z" p-id="3158"></path></svg>'},function(e,t){mam.ng.config=function(e){return e.config(["$controllerProvider","$compileProvider","$filterProvider","$provide",function(t,n,o,i){e.registerController=t.register,e.registerDirective=n.directive,e.registerFilter=o.register,e.registerFactory=i.factory,e.registerService=i.service}]),e.run(["$rootScope","$state","$stateParams",function(e,t,n){e.$state=t,e.params=n,e.config=_.get(window,"nxt.config"),e.l=window.l}]),e}},function(e,t){function n(e,t){return e+=(-1!==e.indexOf("?")?"&":"?")+t}mam.ng.handleUrl=function(e,t){$.extend({},{r:!0},t).r&&(e=n(e,"r="+parseInt(100*Math.random())+(new Date).getTime()));var o=mam.utils.getUrlQueryParam("token");null!=o&&(e=n(e,"token="+encodeURI(o).replace(/\+/g,"%2B")));var i=mam.utils.getUrlQueryParam("opsite",!0);null!=i&&(e=n(e,"opsite="+i));var r=mam.utils.getUrlQueryParam("site");return null==r&&(r=mam.utils.getUrlQueryParam("sitecode")),null!=r&&(e=n(e,"site="+r),$.cookie("site",encodeURIComponent(r),{path:"/"})),e}},function(e,t){mam.ng.httpProvider=function(e,t){var n=$.extend({},{name:"mamHttp",server:"",urlPrefix:"~/",cache:!1,withCredentials:!0,loaderTemplate:'<div id="mam-loader" class="mam-loader">Loading...</div>',loginUrl:"/#!/login?login_backUrl=${url}",requestErrorTip:"请求失败，请稍后再试。"},t);return e.config(["$provide","$httpProvider",function(e,t){function o(e){e.interfaceCall&&!1!==e.showLoader&&--s<=0&&c.hide()}function i(){location.href.split("?login_backUrl=").length<2&&(location.href=_.template(n.loginUrl)({url:escape(location.href)}))}function r(e){var t=-1;return n.otherServers&&n.otherServers instanceof Array&&_.forEach(n.otherServers,function(n){-1===t&&(t=e.indexOf(n.prefix))}),t}function a(e){if(0===e.indexOf(n.urlPrefix))return n.server;if(n.otherServers&&n.otherServers instanceof Array){var t="";return _.forEach(n.otherServers,function(n){0===e.indexOf(n.prefix)&&(t=n.server)}),t}return""}var s=0,c=$(n.loaderTemplate);$("body").append(c),c.hide(),e.factory(n.name,["$q",function(e){return{request:function(e){0!==e.url.indexOf(n.urlPrefix)&&0!==r(e.url)||(e.interfaceCall=!0,e.url=a(e.url)+e.url.substring(1,e.url.length),e.url=mam.ng.handleUrl(e.url),!1!==e.showLoader&&(s++,c.show()));var t=_.get(nxt,"product.name","");return t&&(e.headers||(e.headers={}),e.headers["mam-product"]=t),e},requestError:function(t){return o(t.config),t.config.interfaceCall&&(console.error("requestError",t),mam.prompt(n.requestErrorTip)),e.reject(t)},response:function(t){if(!0!==t.config.interfaceCall)return t;if(o(t.config),200===t.status){if(!t.data.success)return t.data=t.data.error,t.data.success=!1,console.error("response",t),!1!==t.config.errorHandle&&mam.prompt(l("system."+t.data.code,t.data.title)),!1!==t.config.errorReject?e.reject(t):e.resolve(t);t.data=t.data.data}return t},responseError:function(t){if(o(t.config),t.config.interfaceCall){if(console.error("responseError",t),-1===t.status)return mam.prompt(l("system.500","系统错误，请稍后再试！")),e.reject(t);if(t.data=t.data.error,401===t.status)return!1!==t.config.errorHandle&&i(),!1!==t.config.errorReject?e.reject(t):e.resolve(t);if(!1!==t.config.errorHandle)if(403===t.status&&mam.prompt(l("system.403","错误代码：403，服务器拒绝请求！")),404===t.status)mam.prompt(l("system.404","错误代码：404，未找到请求地址！"));else{var n=t.headers("request-id"),r=$('<div class="system-error-tip-box"></div>');if(r.append('<div class="error-tip">'+l("system.500",t.data.title)+"</div>"),n){var a=$('<a class="error-info-btn">复制错误代码<a>');a.click(function(){var e=document.createElement("input");document.body.appendChild(e),e.setAttribute("value",n),e.select(),document.execCommand("copy")&&(document.execCommand("copy"),mam.message.success("复制成功！")),document.body.removeChild(e)}),r.append(a)}mam.prompt(r)}return!1!==t.config.errorReject?e.reject(t):e.resolve(t)}return 401===t.status&&i(),e.reject(t)}}}]),t.defaults.withCredentials=n.withCredentials,t.defaults.cache=n.cache,t.interceptors.push(n.name)}]),e}},function(e,t){window.mam.module=window.mam.module||{},mam.module.add=function(e){var t={name:e.name,app:e.app,version:e.version,path:e.requireModule.uri.substr(0,e.requireModule.uri.lastIndexOf("/")+1),routes:{},options:{}};return mam.module[e.name]=t,t.init=function(n){return t.options=$.extend({},{parentRoute:""},n),!0!==e.init(t,n)&&(mam.ng.config(t.app),mam.ng.registerRoutes(t.app,e.routes,{path:t.path,parentRoute:t.options.parentRoute,ctrlPrefix:t.name}),_.forEach(mam.ng.handleRoutes(e.routes,""),function(e,n){t.routes[n]=t.options.parentRoute+n}),mam.language.append(t.path+"assets/lang/${lang}.js")),t},t}},function(e,t){mam.ng.registerRoutes=function(e,t,n){var o=$.extend({},{parentRoute:"",path:"",ctrlPrefix:"",ctrlSuffix:"Ctrl",viewDir:"view",controllerDir:"controllers"},n);return _.isFunction(t)&&(t=t()),e.config(["$urlRouterProvider","$stateProvider","$controllerProvider",function(n,i,r){function a(e,t,n){return{resolver:["$q","$rootScope",function(e,o){var i=e.defer();return(0,window.require)(t,function(e){o.$apply(function(){r.register(e.name||n,e),i.resolve()})}),i.promise}]}}function s(e){return e.replace(/\/([a-z])/g,function(e,t){return t.toUpperCase()})}angular.forEach(mam.ng.handleRoutes(t,""),function(t,r){if(t.home&&(n.when("",t.url),n.when("/",t.url)),null!=t.cv&&(t.templateUrl=o.path+o.viewDir+"/"+t.cv+".html",t.ctrl=t.cv),null!=t.ctrl){var l=t.ctrl;""!=o.ctrlPrefix&&(l=o.ctrlPrefix+"/"+t.ctrl),t.controller=s(l)+o.ctrlSuffix,t.resolve=[o.path+o.controllerDir+"/"+t.ctrl]}_.isArray(t.resolve)&&(t.resolve=a(e,t.resolve,t.controller)),i.state(o.parentRoute+r,t)})}]),e},mam.ng.handleRoutes=function(e,t){var n={},o="",i=t||"";return function e(t,i){for(var r in t)t[r].children&&!_.isEmpty(t[r].children)?(o+=r+".",n[i+r]=t[r],"object"==typeof t[r].children&&e(t[r].children,o)):n[i+r]=t[r],i!==o&&(o=i);return n}(e,i),n}},function(e,t){angular.module("mam-ng").directive("mamAllCheckbox",function(){return{restrict:"A",link:function(e,t,n){n.property=n.property||"selected",t.bind("change",function(o){e.$apply(function(){var o=t.prop("checked");angular.forEach(e.$eval(n.collection),function(e){e[n.property]=o})})}),e.$watch(function(){return _.map(e.$eval(n.collection),function(e){return e[n.property]})},function(t){var o,i;angular.forEach(t,function(e){e?o=!0:i=!0}),e.$eval(n.ngModel+" = "+((!o||!i)&&o))},!0)}}})},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(62);n.n(o);angular.module("mam-ng").provider("$mamBackToTop",function(){var e=this.defaults={text:"回到顶部",threshold:400,icon:n(78)};this.$get=function(){return{defaults:e}}}).directive("mamBackToTop",["$mamBackToTop",function(e){var t=e.defaults;return{restrict:"E",replace:!0,template:'<div class="mam-back-top" title="{{text}}">'+t.icon+"</div>",scope:{container:"@?",text:"@?",threshold:"@?"},link:function(e,n,o){function i(){a.scrollTop()>e.threshold?r.fadeIn(200):r.fadeOut(200)}e.text=e.text||t.text;var r=$(n),a=$(e.container||window);_.isUndefined(e.threshold)?e.threshold=t.threshold:e.threshold=_.toNumber(e.threshold),a.scroll(function(){i()}),r.click(function(){a[0]==window?$("body,html").animate({scrollTop:0},500):a.animate({scrollTop:0},500)}),i()}}}])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(63);n.n(o);angular.module("mam-ng").directive("mamBadge",function(){return{restrict:"E",template:'<div class="mam-badge"><span ng-if="count>0">{{count}}</span></div>',scope:{count:"<",overflowCount:"<?"},link:function(e,t,n){e.$watch("count",function(){})}}})},function(e,t){angular.module("mam-ng").directive("calCenterEllipsis",["$timeout",function(e){return{restrict:"A",link:function(t,n,o){e(function(){if(n.outerWidth()>n.parent().width())for(var e;n.outerWidth()>n.parent().width();)n.text(n.text().replace(/\.\.\./,"")),e=n.text().length/2,n.text(n.text().substring(0,e)+"..."+n.text().substring(e+1,n.text().length))})}}}])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(64);n.n(o);angular.module("mam-ng").directive("mamCaptcha",[function(){return{restrict:"E",replace:!0,template:'<img class="mam-captcha"/>',link:function(e,t,n){function o(){t.attr("src",mam.path("~/user/captcha?t="+(new Date).getTime()))}e.$on("mam-captcha-refresh",o),t.on("click",o),o()}}}])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(65);n.n(o);angular.module("mam-ng").provider("$mamCheckbox",function(){var e=this.defaults={class:"mam-checkbox",icon:n(79)};this.$get=function(){return{defaults:e}}}).directive("mamCheckbox",["$parse","$mamCheckbox",function(e,t){var n=t.defaults;return{restrict:"A",link:function(e,t,o){function i(e){angular.isString(e)&&(e="true"==e.toLowerCase()),e?r.addClass("checked").removeClass("unchecked"):r.addClass("unchecked").removeClass("checked")}var r=t.parent("label");r.hasClass(n.class)||r.addClass(n.class),r.append(n.icon),o.ngChecked&&e.$watch(o.ngChecked,function(e){i(e)},!1),o.ngModel&&e.$watch(o.ngModel,function(e){i(e)},!1),t.on("focus",function(){r.addClass("focus")}).on("blur",function(){r.removeClass("focus")})}}}])},function(e,t){angular.module("mam-ng").directive("mamDatepicker",[function(){return{restrict:"EA",scope:{minDate:"@",maxDate:"@",ctrlType:"@",showTime:"@"},link:function(e,t,n){$.datetimepicker.setLocale("ch");var o={showSecond:!0,formatTime:"H:m:s",format:"Y-m-d H:m:s"};if("no"!==e.minDate&&(o.minDate=e.minDate),"no"!==e.maxDate&&(o.maxDate=e.maxDate),o.onClose=function(){""!=t.val()&&(e.ngModel=t.val()),t.find("input[type=text]").blur(),e.$apply()},"3"===e.ctrlType&&(o.datepicker=!1,o.format="H:i:s"),"2"===e.ctrlType&&(o.timepicker=!1,o.format="Y-m-d",null!=e.ngModel&&""!==e.ngModel&&e.ngModel.length>18&&(e.ngModel=e.ngModel.substring(0,10))),"no"!==e.step&&void 0!==e.step&&(o.step=e.step),3!==e.ctrlType&&void 0!=e.controlData)switch(e.controlData.type){case"onlypass":o.maxDate="0";break;case"onlyfuture":o.minDate="0"}t.datetimepicker(o)}}}])},function(e,t){angular.module("mam-ng").directive("mamEntityView",function(){return{restrict:"A",scope:{viewtype:"@",entity:"<"},link:function(e,t,n){var o=mam.entity.getViewEntityUrl(e.entity,e.viewtype);!function(){"A"===t[0].tagName&&(t.attr("href",o),t.attr("target","_blank"))}()}}})},function(e,t){angular.module("mam-ng").directive("mamHref",function(){return{restrict:"A",link:function(e,t,n){var o=mam.path(n.mamHref);t.attr("href",o)}}})},function(e,t){angular.module("mam-ng").directive("imageFileSelector",function(){return{restrict:"E",template:"<input type='file' style='display:none' id='{{inputId}}' accept='image/gif,image/jpeg,image/jpg,image/png' />",scope:{inputId:"@",onChange:"&",onError:"&"},replace:!0,link:function(e,t,n){t.bind("change",function(n){var o=t[0].files[0];if(null!=o){var i=new FileReader;i.onload=function(){e.onChange({file:o,base64:this.result}),t.val("")},i.onerror=function(){e.onError()},i.readAsDataURL(o)}})}}})},function(e,t){angular.module("mam-ng").directive("mamImage",function(){return{restrict:"E",template:"<img />",scope:{url:"<",def:"@",error:"@?"},transclude:!1,replace:!0,link:function(e,t,n){var o=$(t);_.isEmpty(e.error)&&(e.error=e.def),e.$watch("url",function(){var t=_.isEmpty(e.url)?e.def:e.url;t=mam.path(t),o.one("error",function(){o.attr("src",mam.path(e.error))}),o.attr("src",t)})}}})},function(module,exports){angular.module("mam-ng").directive("mamInputLimit",function(){return{restrict:"A",scope:{limitStr:"@?",ngModel:"="},link:function(scope,element,attr){function autoClose(){timer=setTimeout(function(){tip.fadeToggle(),isShow=!1},3e3)}var $e=$(element),chars=["\\","/",'"',":","*","?","<",">","|"],isShow=!1,timer=0;if(scope.limitStr)var chars=scope.limitStr.split("");eval("scope.limitStr = /["+chars.join("\\")+"]/g");var tip=$('<div style="display:none;" class="mam-input-limit"><span>文件名不能包含以下任何字符：<p>'+chars.join(" ")+"</p></span></div>");$e.after(tip),$e.on("keyup",function(e){16!=e.keyCode&&17!=e.keyCode&&(clearTimeout(timer),scope.limitStr.test(scope.ngModel)?(isShow||(isShow=!0,tip.fadeToggle()),scope.ngModel=scope.ngModel.replace(scope.limitStr,""),scope.$apply(),autoClose()):isShow&&(isShow=!1,tip.fadeToggle()))})}}})},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(66);n.n(o);angular.module("mam-ng").provider("$mamKeyframe",function(){function e(e,t){if(_.isArray(e)){var n=_.find(e,{code:t});if(null!=n)return n.keyframe}return""}var t={extKeyframes:[],typeKeyframes:[],other:"",folder:""};this.setDefaults=function(n){t=$.extend({},t,n),_.isEmpty(t.folder)&&(t.folder=e(t.extKeyframes,"folder")),_.isEmpty(t.other)&&(t.other=e(t.typeKeyframes,"other"))},this.$get=function(){return{defaults:t}}}).directive("mamKeyframe",["$mamKeyframe",function(e){var t=e.defaults;return{restrict:"E",template:'<div class="mam-keyframe"><img /></div>',scope:{src:"=",ext:"<?",type:"<?"},transclude:!1,replace:!0,link:function(e,n,o){function i(){if(n.addClass("mam-keyframe-default"),"folder"==e.type)return r(t.folder);if(!_.isEmpty(e.ext)){var o=_.find(t.extKeyframes,function(t){return!!_.isArray(t.extensions)&&-1!=t.extensions.indexOf(e.ext)});if(null!=o)return r(o.keyframe)}if(!_.isEmpty(e.type)){var o=_.find(t.typeKeyframes,{code:e.type});if(null!=o)return r(o.keyframe)}r(t.other)}function r(e){a.removeClass("mam-keyframe-loaded"),a.attr("src",mam.path(e))}var a=n.find("img");a.on("load",function(){n.addClass("mam-keyframe-loaded")}),e.$watchGroup(["src","type","ext"],function(e,t,o){_.isEmpty(o.ext)||(o.ext=o.ext.toLowerCase()),_.isEmpty(o.type)||n.addClass("mam-keyframe-type-"+o.type),_.isEmpty(o.src)?i():(n.removeClass("mam-keyframe-default"),a.one("error",i),r(o.src))})}}}])},function(e,t,n){n(67),angular.module("mam-ng").directive("mamLogo",["$sce",function(e){return{restrict:"E",template:n(54),transclude:!0,replace:!0,scope:{config:"<"},link:function(e,t,n){e.config=e.config||nxt.config}}}])},function(e,t,n){n(68),angular.module("mam-ng").provider("$mamPager",function(){var e=this.defaults={text:{total:l("com.pageTotal","总共"),record:l("com.pageRecord","条"),index:l("com.pageIndex","页码"),first:l("com.pageFirst","首页"),last:l("com.pageLast","尾页"),prev:l("com.pagePrev","上一页"),next:l("com.pageNext","下一页")}};this.$get=function(){return{defaults:e}}}).directive("mamPager",["$mamPager",function(e){var t=e.defaults;return{restrict:"E",template:n(55),replace:!0,scope:{recordTotal:"<?",pageIndex:"<?",pageSize:"<?",pageTotal:"<?",showText:"<?",pageChanged:"&?",text:"<?",maxSize:"@?"},link:function(e,n,o){e.textInfo=e.text||t.text,e.maxSize=e.maxSize||7,void 0==e.showText&&(e.showText=!0),e.$watch("pageTotal",function(t){e.pageTotal>1?n.find(".pagination").show():n.find(".pagination").hide()}),e.$watch("text",function(){e.text&&(e.textInfo=$.extend({},t.text,e.text))}),e.change=function(t){e.pageChanged({page:t})},e.$watch("recordTotal",function(t){e.pageTotal=Math.ceil(e.recordTotal/e.pageSize),e.pageIndex>1&&e.pageIndex>e.pageTotal&&e.change(e.pageTotal)})}}}])},function(e,t,n){n(69),angular.module("mam-ng").provider("$mamRadio",function(){var e=this.defaults={className:"mam-radio",icon:n(80)};this.$get=function(){return{defaults:e}}}).directive("mamRadioGroup",function(){return{restrict:"A",require:"ngModel",compile:function(e,t){e.removeAttr("ng-model");var n=e.find('input[type="radio"]');angular.forEach(n,function(e){angular.element(e).attr("ng-model",t.ngModel)})}}}).directive("mamRadio",["$mamRadio",function(e){var t=e.defaults;return{restrict:"A",link:function(e,n,o,i){function r(t){(null==o.ngValue?n.attr("value"):e.$eval(o.ngValue))==t?a.addClass("checked").removeClass("unchecked"):a.addClass("unchecked").removeClass("checked")}var a=n.parent("label");a.hasClass(t.className)||a.addClass(t.className),a.append(t.icon),null!=o.ngModel&&""!=o.ngModel&&(e.$watch(o.ngModel,function(e){r(e)},!1),null!=o.ngValue&&""!=o.ngValue&&e.$watch(o.ngValue,function(t){r(e.$eval(o.ngModel))},!1),r(e.$eval(o.ngModel))),n.on("focus",function(){a.addClass("focus")}).on("blur",function(){a.removeClass("focus")})}}}])},function(e,t){angular.module("mam-ng").directive("mamResizeTable",function(e){return{restrict:"A",scope:{scrollX:"="},link:function(t,n,o){e(function(){var e=n.parent().parent(),o=e.find(".flex-item,.flex-head"),i=o.children(),r=e.find(".flex-head"),a=r.children();o.css("width",e.width()-2+"px"),i.css("position","relative"),e.css("overflow-x",t.scrollX?"scroll":"hidden"),e.css("overflow-y","hidden");var s=$('<div class="resize-line"></div>');s.attr("draggable",!0),s.css({width:"0",height:"100%",position:"absolute","z-index":"9999",right:"-2px",top:"0","border-right":"5px solid transparent",cursor:"e-resize"});var l=$('<div class="dash-line"></div>');l.css({width:"0",height:e.height()+"px",position:"absolute","z-index":"-9999",right:"0",top:"0","border-right":"2px dashed transparent"});var c=0,d=0,u=0,m=[],f="",p=function(e,t){e.originalEvent.dataTransfer.effectAllowed="move",c=e.clientX,d=$(t).parent().width();for(var n=0;n<$(t).parent().parent().children().length;n++)if($(t).parent().get(0)==$(t).parent().parent().children().get(n)){u=n,f=a.find(".dash-line").get(n);break}m=v[u]},g=function(e){$(f).css("right",-(e.clientX-c)+"px"),$(f).css("border-right","2px dashed gray")},h=function(e){$(f).css("right","0"),$(f).css("border-right","2px dashed transparent"),angular.forEach(m,function(n){$(n).css("flex","0 1 auto"),t.scrollX?$(n).css("min-width",d+e.clientX-c+"px"):$(n).css("width",d+e.clientX-c+"px")})};s.on("dragstart",function(e){p(e,this)}),s.on("drag",function(e){g(e)}),s.on("dragend",function(e){h(e)}),i.on("dragover",function(e){return e.preventDefault(),e.originalEvent.dataTransfer.dropEffect="move",!0}),i.find(".resize-line").remove(),i.append(s),a.find(".dash-line").remove(),a.append(l);for(var v=new Array(o.eq(0).find(".resize-line").length),x=0;x<v.length;x++)v[x]=[];angular.forEach(o,function(e,t){angular.forEach($(e).find(".resize-line"),function(e,t){v[t].push($(e).parent())})})})}}})},function(e,t){angular.module("mam-ng").directive("mamResizer",function(){return{restrict:"E",scope:{direction:"@?",width:"@?",height:"@?",offset:"@?",max:"@?",left:"@?",right:"@?",top:"@?",bottom:"@?"},link:function(e,t,n){function o(n){if("h"==e.direction){var o=n.pageX;e.offset&&(o+=parseInt(e.offset)),e.max&&o>e.max&&(o=parseInt(e.max)),t.css({left:o+"px"}),$(e.left).css({width:o+"px"}),$(e.right).css({left:o+parseInt(e.width)+"px"})}else{var i=n.pageY;e.offset&&(i+=parseInt(e.offset)),e.max&&i>e.max&&(i=parseInt(e.max)),t.css({top:i+"px"}),$(e.top).css({height:i+"px"}),$(e.bottom).css({top:i+parseInt(e.height)+"px"})}}function i(){t.removeClass("active"),$(document).unbind("mousemove",o),$(document).unbind("mouseup",i)}e.direction=e.direction||"h","h"==e.direction?t.css("cursor","ew-resize"):t.css("cursor","ns-resize"),t.on("mousedown",function(e){e.preventDefault(),t.addClass("active"),$(document).on("mousemove",o),$(document).on("mouseup",i)})}}})},function(e,t,n){n(70),angular.module("mam-ng").directive("mamSearchInput",["$http",function(e){return{restrict:"E",template:n(56),replace:!0,scope:{search:"&",keywords:"=",useHistory:"<?",placeholder:"@?"},link:function(t,n,o){function i(){function n(){s.not(s).stop().slideUp(100).parent().find(a),s.stop().slideToggle(100)}var o=this;this.items=[],this.showBox=function(e,i){function r(){!c&&o.items.length>0&&(t.selected=void 0,l=-1,c=!0,n())}t.useHistory&&(void 0!=e&&e.stopPropagation(),setTimeout(function(){var e=o.get(i?t.keywords:void 0);e?e.then(function(){r()}):r()},100))},this.close=function(){c&&(c=!1,n())},this.delete=function(n,i){t.useHistory&&(i.stopPropagation(),e.delete("~/search/history/"+n,{errorHandle:!1}).then(function(e){e.data&&o.get()},function(e){console.error(e)}))},this.getBy=function(){o.get(t.keywords)},this.get=function(n){if(t.useHistory){var i="~/search/tip-search?keyword=";return n&&(i+=n),e.get(i,{errorHandle:!1,showLoader:!1}).then(function(e){n!=t.keywords&&void 0!=n||(o.items=e.data)},function(e){404==e.status&&(o.items=[],console.info("需要/search/tip-search接口")),console.error(e)})}},function(){s?(s.hide(),setTimeout(o.get,5e3)):t.useHistory=!1}()}var r=$(n),a="#mam-history-box",s=r.find(a),l=-1,c=!1;t.text={placeholder:t.placeholder||"keyword"},t.selected,t.submit=function(e){e&&(t.keywords=e),setTimeout(function(){t.$apply(function(){t.search()})},50),t.sh.close()},t.changeModel=function(e){t.selected=e||void 0},function(){void 0==t.keywords&&console.error("检索输入框插件：检索keywords初始值不能为undefined！"),t.sh=new i(s),t.useHistory&&r.on("keydown",function(e){var o=t.sh.items.length;38==e.keyCode?(l--,l<=-2&&(l=0),l<0&&(l=o-1),t.selected=t.keywords=t.sh.items[l].keyword):40==e.keyCode?(l++,l>=o&&(l=0),t.selected=t.keywords=t.sh.items[l].keyword):27==e.keyCode?(t.selected=void 0,t.sh.close()):13==e.keyCode&&(n.find("input").blur(),t.submit()),t.$apply()}),$(document).on("click",t.sh.close)}()}}}])},function(e,t){angular.module("mam-ng").directive("mamSelectable",function(){return{restrict:"A",link:function(e,t,n){function o(e,t,n,o){var i=(e.x-n.x)*(t.y-n.y)-(e.y-n.y)*(t.x-n.x),r=(e.x-o.x)*(t.y-o.y)-(e.y-o.y)*(t.x-o.x);if(i*r>=0)return!1;var a=(n.x-e.x)*(o.y-e.y)-(n.y-e.y)*(o.x-e.x);return!(a*(a+i-r)>=0)}function i(){var e=(new Date).getTime(),t=event.clientX-parseInt(s.offset().left),n=event.clientY-parseInt(s.offset().top)+document.body.scrollTop;$(document).on("mouseup",{firstTime:e},a),s.append('<div class="mam-selectable-box"></div>').on("mousemove",{x:event.clientX,y:event.clientY+document.body.scrollTop},r),s.css("position","relative"),$(".mam-selectable-box").css({position:"absolute",top:n,left:t,backgroundColor:"rgba(0, 196, 244, 0.3)"}),$("body").css("userSelect","none").attr("ondragstart","return false;"),$("head").append("<style type='text/css' key='select'>::selection{ background: none; }</style>")}function r(e){var t=event.clientX-e.data.x,i=event.clientY-e.data.y+document.body.scrollTop,r=$(n.itemname),a=$(".mam-selectable-box"),c=a.offset().top,d=a.get(0).offsetHeight+c,u=a.offset().left,m=a.get(0).offsetWidth+u;t>0&&i>0?a.css({width:t,height:i}):t>0&&i<0?a.css({width:t,height:-i,top:event.clientY-parseInt(s.offset().top)+document.body.scrollTop}):t<0&&i>0?a.css({width:-t,height:i,left:event.clientX-parseInt(s.offset().left)}):t<0&&i<0&&a.css({width:-t,height:-i,top:event.clientY-parseInt(s.offset().top)+document.body.scrollTop,left:event.clientX-parseInt(s.offset().left)});for(var f=0;f<r.length;f++){var p=r.eq(f).offset().left,g=p+r.eq(f).width(),h=r.eq(f).offset().top,v=h+r.eq(f).height();"single"===l?c>h&&c<v||c===h?r.eq(f).css("backgroundColor","rgba(0, 196, 244, 0.3)"):h>c&&v<d?r.eq(f).css("backgroundColor","rgba(0, 196, 244, 0.3)"):d>h&&d<v?r.eq(f).css("backgroundColor","rgba(0, 196, 244, 0.3)"):r.eq(f).css("backgroundColor",""):"multi"===l&&(o({x:u,y:c},{x:m,y:c},{x:p,y:h},{x:p,y:v})||o({x:u,y:c},{x:m,y:c},{x:g,y:h},{x:g,y:v})||o({x:u,y:d},{x:m,y:d},{x:p,y:h},{x:p,y:v})||o({x:u,y:d},{x:m,y:d},{x:g,y:h},{x:g,y:v})||o({x:u,y:c},{x:u,y:d},{x:p,y:h},{x:g,y:h})||o({x:u,y:c},{x:u,y:d},{x:p,y:v},{x:g,y:v})||o({x:m,y:c},{x:m,y:d},{x:p,y:h},{x:g,y:h})||o({x:m,y:c},{x:m,y:d},{x:p,y:v},{x:g,y:v})?r.eq(f).css("backgroundColor","rgba(0, 196, 244, 0.3)"):h>c&&v<d&&p>u&&g<m||h<c&&v>d&&p<u&&g>m?r.eq(f).css("backgroundColor","rgba(0, 196, 244, 0.3)"):r.eq(f).css("backgroundColor",""))}}function a(i){var r=(new Date).getTime(),a=$(n.itemname),s=$(".mam-selectable-box"),c=s.offset().top,d=s.get(0).offsetHeight+c,u=s.offset().left,m=s.get(0).offsetWidth+u,f=e.$eval(n.mamSelectable);if(r-i.data.firstTime>200)for(var p=0;p<a.length;p++){var g=a.eq(p).offset().left,h=g+a.eq(p).width(),v=a.eq(p).offset().top,x=v+a.eq(p).height();"single"===l?(a.eq(p).css("backgroundColor",""),c>v&&c<x||c===v?void 0===f[p].selected?f[p].selected=!0:f[p].selected=!f[p].selected:v>c&&x<d?void 0===f[p].selected?f[p].selected=!0:f[p].selected=!f[p].selected:d>v&&d<x&&(void 0===f[p].selected?f[p].selected=!0:f[p].selected=!f[p].selected)):"multi"===l&&(a.eq(p).css("backgroundColor",""),o({x:u,y:c},{x:m,y:c},{x:g,y:v},{x:g,y:x})||o({x:u,y:c},{x:m,y:c},{x:h,y:v},{x:h,y:x})||o({x:u,y:d},{x:m,y:d},{x:g,y:v},{x:g,y:x})||o({x:u,y:d},{x:m,y:d},{x:h,y:v},{x:h,y:x})||o({x:u,y:c},{x:u,y:d},{x:g,y:v},{x:h,y:v})||o({x:u,y:c},{x:u,y:d},{x:g,y:x},{x:h,y:x})||o({x:m,y:c},{x:m,y:d},{x:g,y:v},{x:h,y:v})||o({x:m,y:c},{x:m,y:d},{x:g,y:x},{x:h,y:x})?void 0===f[p].selected?f[p].selected=!0:f[p].selected=!f[p].selected:(v>c&&x<d&&g>u&&h<m||v<c&&x>d&&g<u&&h>m)&&(void 0===f[p].selected?f[p].selected=!0:f[p].selected=!f[p].selected))}else for(var b=0;b<a.length;b++)a.eq(b).css("backgroundColor","");t.off("mousemove").children(".mam-selectable-box").remove(),$(document).off(),$("body").css("userSelect","").attr("ondragstart",""),$("head").children('style[key="select"]').remove(),e.$applyAsync()}t.on("mousedown",i);var s=t,l=n.mode}}})},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(71);n.n(o);angular.module("mam-ng").directive("mamSortGroup",function(){return{restrict:"E",template:n(57),replace:!0,scope:{ngModel:"=",onChange:"&?"},compile:function(e,t){var o=n(3),i=$(o);i.attr("ng-if","ngModel.current.hideDirection!==true").attr("ng-class","{active:ngModel.current.desc=='desc'}"),e.find("button").append(i);var r=$(o);return r.attr("ng-if","item.hideDirection!==true").attr("ng-class","{active:item.desc=='desc'}"),e.find("li a").append(r),function(e,t,o){e.icon=n(3),e.change=function(t){t!=e.ngModel.current&&(_.isEmpty(e.ngModel.storageKey)||localStorage.setItem(e.ngModel.storageKey,JSON.stringify(t)),e.ngModel.current=t,e.onChange({item:t}))},function(){if(null==e.ngModel.current){if(null!=e.ngModel.storageKey){var t=localStorage.getItem(e.ngModel.storageKey);if(!_.isEmpty(t))return void(e.ngModel.current=JSON.parse(t))}e.ngModel.current=_.find(e.ngModel.items,{default:!0}),null==e.ngModel.current&&(e.ngModel.current=e.ngModel.items[0])}}()}}}})},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(72);n.n(o);angular.module("mam-ng").directive("mamSpin",function(){return{restrict:"E",template:n(58),replace:!0,scope:{text:"@?",show:"<"},link:function(e,t,n){var o=t.parent();"static"!=o.css("position")&&"initial"!=o.css("position")||o.css("position","relative")}}})},function(e,t,n){n(73),angular.module("mam-ng").directive("mamSwitchButton",[function(){return{restrict:"EA",template:n(59),transclude:!0,replace:!0,require:"ngModel",scope:{onChange:"="},link:function(e,t,n,o){e.changeEnable=function(){e.val=!o.$viewValue,o.$setViewValue(e.val),e.onChange&&"function"==typeof e.onChange&&e.onChange(e.val)},o.$render=function(){e.val=o.$viewValue}}}}])},function(e,t,n){n(74),angular.module("mam-ng").directive("mamTopNav",["$sce","$timeout","$http",function(e,t,o){return{restrict:"E",template:n(60),transclude:!0,replace:!0,scope:{ngModel:"<?",currentUser:"<?"},link:function(n,o,i){function r(){for(var o=[],i=0;i<n.ngModel.length;i++){var r=n.ngModel[i];if(r.enable)if("|"!=r.content){if(_.isString(r.module)){var l=-1==r.module.indexOf("Enable")?r.module+"Enable":r.module;if(!0!==nxt.config[l])continue}_.isString(r.appPermission)&&r.appPermission.length>0&&!nxt.permission.judge(r.appPermission)||(_.isString(r.content)&&(r.content=e.trustAsHtml(r.content)),r.target&&"auto"!==r.target||(r.target="_self"),r.href=mam.utils.eval(mam.path(r.href)),o.push(r))}else o.push(r)}for(var i=0;i<o.length;i++)if("|"!=o[i].content||0!=i&&"|"!=o[i-1].content){var d=s(o[i]);p.push(d),u.append(d)}_.forEach(p,function(e,t){n.ngModel[t].width=e.outerWidth()}),c=m.outerWidth(),p.length>0?m.hover(function(){f.stop().css("height","auto").slideDown(300)},function(){f.stop().slideUp(300)}).show():m.hide(),t(function(){a()},500)}function a(){u.html(""),f.html("");var e=d.outerWidth(),t=c,o=-1;if(_.forEach(p,function(i,r){if(!((t+=n.ngModel[r].width)<=e))return o=r,!1;u.append(i.removeClass("more-item").addClass("item"))}),o>-1){for(var i=0,r=o;r<p.length;r++)i+=n.ngModel[r].width;if(i>c){for(var r=o;r<p.length;r++)f.append(p[r].removeClass("item").addClass("more-item"));f.css({top:m.height()}),m.show()}else{for(var r=o;r<p.length;r++)u.append(p[r].removeClass("more-item").addClass("item"));m.hide()}}else m.hide();l()}function s(e){var t=$('<div class="item" title="'+e.tooltip+'"><a href="'+e.href+'">'+e.content+"</a></div>");return e.class&&t.addClass(e.class),t}function l(){u.find(".item").each(function(e,t){var n=$(t).find("a").attr("href");n.indexOf("#!")>-1&&(n=n.substring(0,n.indexOf("#!"))),""!=n&&"#"!=n&&-1!=location.href.indexOf(n)?$(t).addClass("current"):$(t).removeClass("current")})}var c,d=$(o),u=d.find(".navs"),m=d.find(".more").hide(),f=m.find(".subs"),p=[];n.logged=!1,function(){n.currentUser=n.currentUser||_.get(window,"nxt.user.current"),n.logged=null!=n.currentUser&&"guest"!=n.currentUser.loginName,null==n.ngModel||""==n.ngModel?(n.ngModel=_.get(window,"nxt.config.topNav",[]),r()):n.$watch("ngModel",function(e,t){_.isArray(e)&&r()}),n.$on("refreshCount",function(e,t){_.get(window,"nxt.config.deskEnable.unreadTip")&&(void 0==t?(n.badge.message=0,n.badge.share=0):(void 0!=t.message&&(n.badge.message=t.message),void 0!=t.share&&(n.badge.share=t.share)))}),$(window).on("resize",a),$(window).bind("hashchange",l)}()}}}])},function(e,t,n){n(75),angular.module("mam-ng").provider("$mamUserAvatar",function(){var e=this.defaults={errorSrc:mam.path("~/test/img/error.jpg")};this.$get=function(){return{defaults:e}}}).directive("mamUserAvatar",["$timeout","$mamUserAvatar",function(e,t){var n=t.defaults;return{restrict:"E",template:'<div class="mam-user-avatar"><img /></div>',scope:{src:"<"},transclude:!1,replace:!0,link:function(t,o,i){function r(){l=void 0!==t.src?t.src:c;var e=new Image;$(e).on("load",a),$(e).on("error",s),e.src=l}function a(){$(o).find("img").attr("src",l)}function s(){$(o).find("img").attr("src",c)}var l,c=n.errorSrc;e(function(){r()}),t.$watch("src",function(e,t){t!==e&&r()})}}}])},function(e,t,n){n(76),angular.module("mam-ng").directive("mamUserInfo",["$sce",function(e){return{restrict:"E",template:n(61),transclude:!0,scope:{currentUser:"<"},link:function(e,t,n){var o=$(t);!function(){if(e.logged=!0,e.currentUser=e.currentUser||nxt.user.current,e.currentUser&&"guest"!==e.currentUser.loginName){var n=o.find(".sub-nav");n.css({top:o.height()}),t.hover(function(){n.stop().css("height","auto").slideDown(300)},function(){n.stop().slideUp(300)})}else e.logged=!1}(),e.isCurrent=function(e){return-1!==window.location.href.indexOf(e.replace(/\~/g,""))}}}}])},function(e,t){angular.module("mam-ng").service("mamValidationService",function(){this.nullValidate=function(e){return void 0!=e&&0!=e.toString().replace(/(^\s*)|(\s*$)/g,"").length},this.blankValidate=function(e){return void 0==e||0==e.toString().length||!/(\s+)/g.test(e)},this.lengthValidate=function(e,t,n){return void 0==e||0==e.toString().length||e.toString().length>=t&&e.toString().length<=n},this.numValueValidate=function(e,t,n){return void 0==e||0==e.toString().length||!isNaN(e)&&parseInt(e,10)>=t&&parseInt(e,10)<=n},this.pswValidate=function(e,t,n,o){return!o||!o[t]||(!e&&!o[t][n]||o[t][n]==e)},this.numValidate=function(e){return void 0==e||0==e.toString().length||!isNaN(e)},this.integerValidate=function(e){return void 0==e||0==e.toString().length||!!/^-?\d+$/.test(e)},this.positiveNumberValidate=function(e){return void 0==e||0==e.toString().length||!(isNaN(e)||e<0)},this.dateValidate=function(e){if(void 0==e||0==e.toString().length)return!0;var t=/^\d{4}-(0?[1-9]|1[0-2])-(0?[1-9]|[1-2]\d|3[0-1]) ([0-1]\d|2[0-3]):[0-5]\d:[0-5]\d$/,n=/^\d{4}-(0?[1-9]|1[0-2])-(0?[1-9]|[1-2]\d|3[0-1])$/,o=/^\d{4}-(0?[1-9]|1[0-2])$/,i=/^\d{4}$/,r=/^([0-1]\d|2[0-3]):[0-5]\d$/,a=/^([0-1]\d|2[0-3]):[0-5]\d:[0-5]\d$/;return!!(t.test(e)||n.test(e)||o.test(e)||i.test(e)||r.test(e)||a.test(e))},this.specialCharValidate=function(e){return void 0==e||0==e.toString().length||!new RegExp("[`~!@%#$^&*()=|{}':;',\\[\\]<>/?\\.；：%……+￥（）【】‘”“'。，、？]").test(e)},this.scriptCharValidate=function(e){return void 0==e||0==e.toString().length||!new RegExp("<script(\\s.*)?>.*<\/script>").test(e)},this.noChineseValidate=function(e){return void 0==e||0==e.toString().length||!e.match(/[^\x00-\xff]/gi)},this.phoneValidate=function(e){return void 0==e||0==e.toString().length||!!/(^((([0-9]){3,4}[\\-])|([\\(]([0-9]){3,4}[\\)]))?[0-9]{4,8}$)|(^[0-9]{11}$)/.test(e)},this.emailValidate=function(e){return void 0==e||0==e.toString().length||!!/^([a-zA-Z0-9_\\.]{1,127}){1}[@]{1}([a-zA-Z0-9_\\.]{1,127}){1}$/.test(e)},this.postcodesValidate=function(e){return void 0==e||0==e.toString().length||!!/^[1-9][0-9]{5}$/.test(e)},this.ipValidate=function(e){return void 0==e||0==e.toString().length||!!/^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/.test(e)},this.mobileValidate=function(e){return void 0==e||0==e.toString().length||!!/^[0-9]{11}$/.test(e)},this.uncAddressValidate=function(e){return void 0==e||0==e.toString().length||!!/^\\\\.*\\.*$/.test(e)},this.ftpAddressValidate=function(e){return void 0==e||0==e.toString().length||!!/^ftp:\/\//.test(e)},this.hddAddressValidate=function(e){return void 0==e||0==e.toString().length||!!/^[A-Z,a-z]:\\/.test(e)},this.ossAddressValidate=function(e){return void 0==e||0==e.toString().length||!!/^oss:\/\//.test(e)}}),angular.module("mam-ng").directive("mamValidationList",["$rootScope","$compile","$timeout","$q","mamValidationService",function(e,t,n,o,i){function r(e){if(e.parent()[0]&&"HTML"!=e.parent()[0].tagName)return"FORM"==e.parent()[0].tagName?e.parent():r(e.parent())}function a(e,t){var n=o.defer();if(e&&e.$mamValidationInfo){e.$mamValidationInfo.$hasValidateAll=!0;var i,r,a=e.$mamValidationInfo.forms[t];for(var s in a)i=a[s],r=i.ngModel,angular.forEach(r.$formatters,function(e){e(r.$viewValue)});return n.resolve(),n.promise}}return{restrict:"A",require:"ngModel",compile:function(){return{pre:function(e,t,n,o){},post:function(e,t,n,o){var s,l=!1,c=r(t);if(c){s=c.attr("name");var d,u;if(e.$mamValidationInfo||(e.$mamValidationInfo={}),e.$mamValidationInfo.forms||(e.$mamValidationInfo.forms={}),e.$mamValidationInfo.forms[s]||(e.$mamValidationInfo.forms[s]={}),e.$mamValidationInfo.validateAll||(e.$mamValidationInfo.validateAll=function(t){return a(e,t)},i.validateAll=function(t){return a(e,t)}),d=e.$mamValidationInfo.forms[s],d[t.attr("name")]||(d[t.attr("name")]={}),u=d[t.attr("name")],u.element=t,u.ngModel=o,n.mamValidationList){var m=JSON.parse(n.mamValidationList.replace(/'/g,'"'));u.validateList=m}var f=u.validateList,p=function(t){var n=!0;if(f&&"object"==typeof f&&f instanceof Array)for(var r,a,s,c=0,d=f.length;c<d;c++)if(r=f[c],l||e.$mamValidationInfo.$hasValidateAll){a=i[r.name];var u=[];if(u.push(t),r.param&&"object"==typeof r.param&&r.param instanceof Array)for(var m=0,p=r.param.length;m<p;m++)u.push(r.param[m]);u.push(e),s=a.apply(this,u),"boolean"==typeof s?s?o.$setValidity(r.validity,!0):(n=!1,o.$setValidity(r.validity,!1)):"object"==typeof s&&s.then(function(){o.$setValidity(r.validity,!0)},function(){n=!1,o.$setValidity(r.validity,!1)})}return n?t:void 0};o.$parsers.push(p),o.$formatters.push(p)}t.on("blur keydown change",function(){l=!0})}}}}}])},function(e,t){var n=angular.module("mam-ng");n.filter("trusted",["$sce",function(e){return function(t){return _.isString(t)?e.trustAsHtml(t):t}}]),n.filter("entityTypeName",function(){return function(e){if(_.isEmpty(e))return"";var t=_.find(mam.entity.types,{code:e});return null==t?"hypermedia"==e?l("com."+e,"图文"):"folder"==e?l("com."+e,"文件夹"):e:l("com."+e,t.name)}}),n.filter("entityTypeShortName",function(){return function(e,t){if(nxt.config.extTagEnable){if(null!=t&&""!=t){var n=t.replace(".","").toUpperCase();return n.indexOf("?")>-1&&(n=n.substring(0,n.indexOf("?"))),n}if(!nxt.config.typeTagEnable)return""}if(null==e||""==e||!nxt.config.typeTagEnable)return"";var o=_.find(mam.entity.types,{code:e});if(null==o&&(o=_.find(mam.entity.types,{namespace:e})),null!=o&&null!=o.shortCode&&o.shortCode.length>0)return o.shortCode;switch(e.toLowerCase()){case"video":case"biz_sobey_video":return"V";case"audio":case"biz_sobey_audio":return"A";case"picture":case"biz_sobey_picture":return"P";case"document":case"biz_sobey_document":return"D";case"hypermedia":case"biz_sobey_hypermedia":return"H";case"dataset":return"G";case"program":case"sequence":case"scene":case"short":return"C";case"rundown":case"biz_sobey_rundown":return"R";case"script":case"biz_sobey_script":return"S";case"other":case"biz_sobey_other":return"O";default:return e}}}),n.filter("entityTypeIcon",function(){return function(e){var t=_.find(mam.entity.types,{code:e});if(null!=t&&null!=t.icon)return t.icon;switch(e){case"video":return"fa fa-film";case"audio":return"fa fa-music";case"picture":return"fa fa-picture-o";case"document":return"fa fa-file-word-o";default:return"fa fa-file-o"}}}),n.filter("removeHtml",function(){return function(e){return mam.utils.removeHtmlTag(e)}}),n.filter("videoQualityFlag",function(){return function(e){switch(e){case 1:return"flag-SD";case 2:case 3:return"flag-HD";case 4:return"flag-4K";default:return""}}}),n.filter("cutString",["$sce",function(e){return function(e,t){var n=0,o="";if("string"!=typeof e)return null;if(!/^[0-9]*[1-9][0-9]*$/.test(t)||0==t)return e;for(var i=e.split(/(<font\s\S*>(\S+)<\/font>)/gim),r=1;r<i.length;r+=3)e=e.replace(i[r],i[r+1]);for(var r=0;r<e.length;r++){if(!((n+=e.charCodeAt(r)>255?2:1)<=t-2||r==e.length-1&&n>t-2)){o+="...";break}o+=e.charAt(r)}for(var r=1;r<i.length;r+=3)o=o.replace(i[r+1],i[r]);return o}}]),n.filter("formatExt",function(){return function(e){if(!e)return"";var t=e.replace(".","");return t=t.toUpperCase()}}),n.filter("formatSize",function(){return function(e,t){if(null==e)return"-";return t?-1==e?"无限制":(e=e/1024/1024/1024,e<=.01&&e>0?"0.01 GB":e.toFixed(2)+" GB"):mam.utils.formatSize(e)}}),n.filter("formatDate",function(){return function(e,t){if(null==e||""==e)return"";var n=new Date;return n=angular.isDate(e)?e:new Date(e.replace(/-/g,"/")),null==t||""==t?n.format("yyyy-MM-dd hh:mm:ss"):n.format(t)}}),n.filter("comingDateTime",function(){return function(e){var t="",n=e.replace(/\s|\-|\/|\:/g,","),o=n.split(","),i={y:o[0],m:o[1]-1,d:o[2],h:o[3],mi:o[4],s:o[5]},r=new Date(i.y,i.m,i.d,i.h,i.mi,i.s),a=new Date,s=r.getTime()-a.getTime(),l=parseInt(s/1e3);return l<0?t="0 秒后":l>=0&&l<60?t=l+" 秒后":l>=60&&l<3600?t=parseInt(l/60)+" 分钟后":l>=3600&&l<86400?t=parseInt(l/3600)+" 小时后":l>=86400&&l<2592e3?t=parseInt(l/3600/24)+" 天后":l>=2592e3&&l<31104e3?t=parseInt(l/3600/24/30)+" 个月后":l>=31104e3&&(t=parseInt(l/3600/24/30/12)+" 年后"),t}})},function(e,t){window.nxt=window.nxt||{},nxt.user=nxt.user||{},nxt.permission=nxt.permission||{},nxt.permission.judge=function(e){if(null==e||""==e)return!0;if(null==_.get(nxt,"user.current"))throw"判断前，必须给user.current赋值";var t=nxt.user.current;if(!_.isArray(t.appPermission)||0==t.appPermission.length)return!1;var n=nxt.config.systemCode?nxt.config.systemCode.toLowerCase()+"_":"";if(-1==e.indexOf(","))return null!=_.find(t.appPermission,function(t){return t.toLowerCase()===n+e.toLowerCase()});for(var o=e.split(","),i=0,r=0;r<o.length;r++)null==_.find(t.appPermission,function(e){return e.toLowerCase()===n+o[r].toLowerCase()})&&i++;return i!=o.length}},function(e,t){window.mam.utils=window.mam.utils||{},mam.utils.comingDateTime=function(e,t,n,o){if(!e)return"";var i="",r=e.replace(/\s|\-|\/|\:/g,","),a=r.split(","),s={y:a[0],m:a[1]-1,d:a[2],h:a[3],mi:a[4],s:a[5]},l=new Date(s.y,s.m,s.d,s.h,s.mi,s.s),c=new Date,d=l.getTime()-c.getTime(),u=parseInt(d/1e3);return n&&u>n?"":(u<0?i="":u>=0&&u<60?i=u+" 秒后":u>=60&&u<3600?i=parseInt(u/60)+"分钟后":u>=3600&&u<86400||"hour"==t?(i=parseInt(u/3600)+"小时","min"==o&&(i+=parseInt(u%3600/60)+"分钟"),i+="后"):u>=86400&&u<2592e3?i=parseInt(u/3600/24)+"天后":u>=2592e3&&u<31104e3?i=parseInt(u/3600/24/30)+"个月后":u>=31104e3&&(i=parseInt(u/3600/24/30/12)+"年后"),i)}},function(e,t,n){t=e.exports=n(0)(void 0),t.push([e.i,".mam-back-top{position:fixed;display:none;background-color:#fbfbfb;color:#7b7b7b;font-size:46px;bottom:20px;right:20px;overflow:hidden;width:46px;height:46px;text-align:center;line-height:46px;border:1px solid #e7e7e7;border-radius:3px;box-shadow:0 2px 3px rgba(0,0,0,.1);cursor:pointer;z-index:11;-webkit-transition:all .3s;transition:all .3s}.mam-back-top svg{width:24px;height:24px;fill:#b7b7b7;-webkit-transform:translateY(-5px);transform:translateY(-5px)}.mam-back-top:hover{background-color:#e98b11}.mam-back-top:hover svg{fill:#fff}",""])},function(e,t,n){t=e.exports=n(0)(void 0),t.push([e.i,".mam-badge{background:red;color:#fff;border-radius:50%;text-align:center}",""])},function(e,t,n){t=e.exports=n(0)(void 0),t.push([e.i,".mam-captcha{cursor:pointer}",""])},function(e,t,n){t=e.exports=n(0)(void 0),t.push([e.i,'.mam-checkbox{position:relative;display:inline-block;min-width:22px;height:22px;line-height:21px;cursor:pointer;margin-bottom:0;font-weight:400}.mam-checkbox svg{left:3px;top:3px;width:14px;height:14px;fill:#e98b11;position:absolute;font-size:12px;-webkit-transition:opacity .2s;transition:opacity .2s}.mam-checkbox:before{content:"";left:0;top:0;position:absolute;border-radius:4px;border:1px solid #ccc;width:20px;height:20px;-webkit-transition:all .3s;transition:all .3s;background:#fff;-moz-box-sizing:border-box;box-sizing:border-box}.mam-checkbox.checked:before{border-color:#e98b11}.mam-checkbox.checked svg{opacity:1}.mam-checkbox.unchecked svg{opacity:0}.mam-checkbox.focus:before,.mam-checkbox:hover:before{border-color:#e98b11;box-shadow:0 0 6px rgba(233,139,17,.5)}.mam-checkbox.checked[disabled],.mam-checkbox[disabled]{cursor:not-allowed}.mam-checkbox.checked[disabled]:before,.mam-checkbox[disabled]:before{background:#eaeaea;border-color:#dcdcdc}.mam-checkbox.checked[disabled]:hover:before,.mam-checkbox[disabled]:hover:before{border-color:#dcdcdc;box-shadow:none}.mam-checkbox.checked[disabled].checked svg,.mam-checkbox[disabled].checked svg{fill:#666}.mam-checkbox span{margin-left:26px}.mam-checkbox input[type=checkbox]{position:absolute;clip:rect(0,0,0,0);pointer-events:none}.mam-checkbox.sm{min-width:18px;height:18px;line-height:17px}.mam-checkbox.sm:before{width:18px;height:18px}.mam-checkbox.sm svg{width:12px;height:12px}',""])},function(e,t,n){t=e.exports=n(0)(void 0),t.push([e.i,".mam-keyframe{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center}.mam-keyframe img{max-width:100%;max-height:100%}.mam-keyframe-loaded.mam-keyframe-type-video{background-color:#000}.mam-keyframe-default.mam-keyframe-loaded.mam-keyframe-type-video{background-color:transparent}",""])},function(e,t,n){t=e.exports=n(0)(void 0),t.push([e.i,".app-logo{font-family:Microsoft YaHei,Hiragino Sans GB;margin-right:0;margin-left:0;float:left;min-width:230px;-ms-flex-align:center}.app-logo,.app-logo .app-logo-img{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.app-logo .app-logo-img{-webkit-box-flex:1;-webkit-flex:1 0 auto;-ms-flex:1 0 auto;flex:1 0 auto;-ms-flex-align:center;height:100%}.app-logo .app-logo-img img{margin:0 auto;max-height:100%;max-width:100%}.app-logo .app-logo-txt .logo-title{min-width:125px;font-size:23px}.app-logo .app-logo-txt .logo-subtitle{min-width:125px;font-size:12px}",""])},function(e,t,n){t=e.exports=n(0)(void 0),t.push([e.i,".mam-pager{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center}.mam-pager .page-total{margin-right:5px}.mam-pager .page-total span{margin:0 4px;color:#e98b11}.mam-pager .pagination{margin-left:8px}.mam-pager .pagination li.active a{color:#e98b11;font-weight:700}.mam-pager .pagination li.active a:hover{background-color:transparent}.mam-pager .pagination li.disabled a:hover{color:#484848}.mam-pager .pagination a{background-color:transparent;color:#484848;border:none}.mam-pager .pagination a:hover{color:#e98b11;background-color:transparent}",""])},function(e,t,n){t=e.exports=n(0)(void 0),t.push([e.i,'.mam-radio{position:relative;display:inline-block;min-width:22px;height:22px;line-height:21px;cursor:pointer;margin-bottom:0}.mam-radio svg{left:6px;top:7px;width:6px;height:6px;fill:#e98b11;position:absolute;opacity:0;-webkit-transition:opacity .2s;transition:opacity .2s}.mam-radio:before{content:"";left:0;top:1px;position:absolute;border:1px solid #ccc;width:18px;height:18px;border-radius:9px;-webkit-transition:all .3s;transition:all .3s;-moz-box-sizing:border-box;box-sizing:border-box}.mam-radio.checked:before{border-color:#e98b11}.mam-radio.checked svg{opacity:1}.mam-radio.unchecked svg{opacity:0}.mam-radio.focus:before,.mam-radio:hover:before{border-color:#e98b11;box-shadow:0 0 6px rgba(233,139,17,.5)}.mam-radio.checked[disabled],.mam-radio[disabled]{cursor:not-allowed}.mam-radio.checked[disabled]:before,.mam-radio[disabled]:before{background:#eaeaea;border-color:#dcdcdc}.mam-radio.checked[disabled]:hover:before,.mam-radio[disabled]:hover:before{border-color:#dcdcdc;box-shadow:none}.mam-radio.checked[disabled].checked svg,.mam-radio[disabled].checked svg{fill:#acacac}.mam-radio span{margin-left:26px}.mam-radio input[type=radio]{position:absolute;clip:rect(0,0,0,0);pointer-events:none}',""])},function(e,t,n){t=e.exports=n(0)(void 0),t.push([e.i,".mam-search-input{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-webkit-flex:1 0 auto;-ms-flex:1 0 auto;flex:1 0 auto;max-width:800px;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;position:relative}.mam-search-input input{-webkit-box-flex:1;-webkit-flex:1 0 auto;-ms-flex:1 0 auto;flex:1 0 auto;border:1px solid #e1e3e2;height:38px;line-height:38px;padding:0 12px;color:#8f8f8f;font-size:14px;outline:none;-webkit-transition:all .3s;transition:all .3s;z-index:101}.mam-search-input input:focus{border-color:#e98b11}.mam-search-input .mam-history-box{width:100%;height:200px;position:absolute;left:0;top:38px;z-index:100;background-color:#fff;border-radius:0 0 5px 5px;box-shadow:0 0 1px 1px rgba(0,0,0,.12)}.mam-search-input .mam-history-box .history-box{height:100%;overflow-y:scroll;border-bottom:1px solid #dcdcdc}.mam-search-input .mam-history-box .history-items{height:100%;margin:0;padding-left:0}.mam-search-input .mam-history-box .history-items li{padding:10px 15px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between;cursor:pointer}.mam-search-input .mam-history-box .history-items li .item-delete{line-height:20px}.mam-search-input .mam-history-box .history-items .list-item-active,.mam-search-input .mam-history-box .history-items .list-item:hover{background-color:#e98b11}.mam-search-input .mam-history-box .history-items .list-item-active span,.mam-search-input .mam-history-box .history-items .list-item:hover span{color:#fff}",""])},function(e,t,n){t=e.exports=n(0)(void 0),t.push([e.i,".mam-sort-dropdown button{border-style:none;background-color:transparent}.mam-sort-dropdown button:hover{background-color:#f7f7f7}.mam-sort-dropdown button svg{fill:#e98b11}.mam-sort-dropdown svg{width:12px;height:13px;-webkit-transform:translateY(2px);transform:translateY(2px)}.mam-sort-dropdown svg.active{-webkit-transform:rotate(180deg);transform:rotate(180deg)}",""])},function(e,t,n){t=e.exports=n(0)(void 0),t.push([e.i,".mam-spin{position:absolute;left:0;top:0;width:100%;height:100%;background:#fff;opacity:.7;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center}.mam-spin .mam-spin-animation>div{width:12px;height:12px;margin:0 1px;background-color:#5da2f0;border-radius:100%;display:inline-block;-webkit-animation:bouncedelay 1.4s infinite ease-in-out;animation:bouncedelay 1.4s infinite ease-in-out;-webkit-animation-fill-mode:both;animation-fill-mode:both}.mam-spin .mam-spin-animation .bounce1{-webkit-animation-delay:-.32s;animation-delay:-.32s}.mam-spin .mam-spin-animation .bounce2{-webkit-animation-delay:-.16s;animation-delay:-.16s}@-webkit-keyframes bouncedelay{0%,80%,to{-webkit-transform:scale(.4);transform:scale(.4)}40%{-webkit-transform:scale(1);transform:scale(1)}}@keyframes bouncedelay{0%,80%,to{-webkit-transform:scale(.4);transform:scale(.4)}40%{-webkit-transform:scale(1);transform:scale(1)}}.mam-spin .mam-spin-text{font-size:12px}",""])},function(e,t,n){t=e.exports=n(0)(void 0),t.push([e.i,'.mam-switch-button{display:inline-block}.mam-switch-button[disabled=disabled] .mam-switch-button-normal{opacity:.5;cursor:default}.mam-switch-button-normal{width:52px;height:32px;background:#f0f0f0;border-radius:16px;border:1px solid #d4d4d4;position:relative;cursor:pointer;-webkit-transition:background .3s;transition:background .3s}.mam-switch-button-normal:before{content:"";display:block;width:30px;height:30px;border-radius:50%;background:#ddd;border:1px solid #c2c2c2;position:absolute;-webkit-transition:left .3s;transition:left .3s;left:0}.mam-switch-button-on{background:#337ab7}.mam-switch-button-on:before{left:20px}',""])},function(e,t,n){t=e.exports=n(0)(void 0),t.push([e.i,".mam-top-nav{-ms-flex-align:center;-ms-flex-pack:end;-webkit-flex:1 1 auto;-ms-flex:1 1 auto;flex:1 1 auto}.mam-top-nav,.mam-top-nav .navs{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:end;-webkit-justify-content:flex-end;justify-content:flex-end;-webkit-box-flex:1}.mam-top-nav .navs{-ms-flex-pack:end;-ms-flex-align:center;-webkit-flex:1 0 auto;-ms-flex:1 0 auto;flex:1 0 auto;height:100%}.mam-top-nav .more{position:relative;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:end;-webkit-justify-content:flex-end;-ms-flex-pack:end;justify-content:flex-end;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;height:100%}.mam-top-nav .more .subs{position:absolute;display:none;height:auto;right:0;z-index:1000;background:#fff;border:1px solid #ccc;box-shadow:0 0 4px rgba(0,0,0,.2)}.mam-top-nav .item,.mam-top-nav .item a{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;height:100%}.mam-top-nav .item a{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center}.mam-top-nav .more-item{width:110px;border-bottom:1px solid #ccc;height:auto;background:#fff}.mam-top-nav .more-item:last-child{border-bottom:none}.mam-top-nav .more-item a{display:block;text-align:center;padding:10px 0}.mam-top-nav .more-item i{width:20px;text-align:center;margin-right:4px}.mam-top-nav .more-item.nav-separator{display:none}",""])},function(e,t,n){t=e.exports=n(0)(void 0),t.push([e.i,".mam-user-avatar img{width:40px;height:40px}",""])},function(e,t,n){t=e.exports=n(0)(void 0),t.push([e.i,".mam-top-nav-user{position:relative;cursor:pointer;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;height:100%}.mam-top-nav-user:hover .fa-angle-down{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.mam-top-nav-user a.user-info{padding-left:18px;padding-right:9px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;text-align:center}.mam-top-nav-user a.user-info img{width:40px;height:40px;border-radius:50%}.mam-top-nav-user a.user-info span{margin:0 8px;display:block}.mam-top-nav-user a.user-info .fa-angle-down{-webkit-transition:all .3s;transition:all .3s}.mam-top-nav-user a.user-info:hover{background-color:transparent}.mam-top-nav-user .sub-nav{position:absolute;right:0;width:140px;padding:4px 0;z-index:1000;background:#fff;border:1px solid #ccc;box-shadow:0 0 4px rgba(0,0,0,.2);display:none}.mam-top-nav-user .sub-nav .fa{margin-right:10px}.mam-top-nav-user .sub-nav a{display:block;height:36px;line-height:36px;padding:0 0 0 20px}.mam-top-nav-user-nologin{width:auto;height:100%;padding:0 20px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center}.mam-top-nav-user-nologin a{margin:0 8px}.mam-top-nav-user-nologin a i{display:none}",""])},function(e,t){e.exports="<div class=app-logo> <div class=app-logo-img ng-class=\"{'app-default-logo':(config.customLogo.logo==null||config.customLogo.logo=='')&& (config.globalLogo==''||config.globalLogo==null)}\"> <img ng-if=\"(config.customLogo.logo!=null&& config.customLogo.logo!='') || config.globalLogo!=''\" ng-src=\"{{config.customLogo.logo!=null && config.customLogo.logo!=''?config.customLogo.logo:config.globalLogo}}\"/> </div> <div class=app-logo-txt> <div class=logo-title ng-if=config.customLogo.mainTitleEnable style=\"\" ng-bind=config.customLogo.mainTitle></div> <div class=logo-subtitle ng-if=config.customLogo.subTitleEnable style=\"\" ng-bind=config.customLogo.subTitle></div> </div> </div> "},function(e,t){e.exports='<div class=mam-pager ng-class="simple?\'mam-pager-simple\':\'mam-pager-full\'"> <div class=page-info> <span ng-show="showText || pageTotal==1" class=page-total>{{textInfo.total}}<span>{{recordTotal}}</span>{{textInfo.record}}</span> <span ng-show="showText && pageTotal>1" class=page-code>{{textInfo.index}}<span>{{pageIndex}}</span>/<span>{{pageTotal}}</span></span> </div> <ul uib-pagination total-items=recordTotal ng-model=pageIndex items-per-page=pageSize boundary-links=true rotate=true num-pages=pageTotal previous-text={{textInfo.prev}} next-text={{textInfo.next}} first-text={{textInfo.first}} last-text={{textInfo.last}} ng-change=change(pageIndex) boundary-link-numbers=true max-size=maxSize></ul> </div>'},function(e,t){e.exports='<div class=mam-search-input> <input type=text ng-change=sh.getBy() ng-click="sh.showBox($event,\'click\')" ng-model=keywords placeholder={{text.placeholder}}> <div class=mam-history-box id=mam-history-box style=display:none> <div class=history-box> <ul class=history-items> <li class=list-item ng-class="{\'list-item-active\':item.keyword==selected}" ng-repeat="item in sh.items" ng-mouseover=changeModel(item.keyword) ng-click=submit(item.keyword) ng-mouseleave=changeModel()> <span class=history-word>{{item.keyword}}</span> <span class="item-delete fa fa-trash" ng-if="item.type==0" ng-click=sh.delete(item.id,$event)></span> </li> </ul> </div> </div> </div>'},function(e,t){e.exports='<div class="mam-sort-dropdown btn-group" mam-dropdown> <button type=button class="btn btn-default dropdown-toggle"> <span>{{ngModel.current.text}}</span> </button> <ul class=dropdown-menu> <li ng-repeat="item in ngModel.items"> <a ng-click=change(item)> <span>{{item.text}}</span> </a> </li> </ul> </div>'},function(e,t){e.exports='<div class=mam-spin ng-if=show> <div class=mam-spin-animation> <div class=bounce1></div> <div class=bounce2></div> <div class=bounce3></div> </div> <span class=mam-spin-text ng-if="$parent.text != null && $parent.text.length > 0">加载中</span> </div>'},function(e,t){e.exports="<div class=mam-switch-button> <div class=mam-switch-button-normal ng-class=\"{true : 'mam-switch-button-on',false : ''}[val]\" ng-click=changeEnable()></div> </div>"},function(e,t){e.exports='<div class=mam-top-nav> <div class=navs ng-show=logged></div> <div class=more ng-show=logged> <div class=item> <a> <i class="fa fa-ellipsis-h" aria-hidden=true></i> <span>更多</span> </a> </div> <div class=subs></div> </div> </div>'},function(e,t){e.exports='<div class=mam-top-nav-user ng-show=logged> <a class=user-info> <mam-user-avatar src=currentUser.avatarUrl></mam-user-avatar> <div> <span class=nickName>{{currentUser.nickName}}</span> <span ng-if=currentUser.haveOrganization>({{currentUser.organization.organizationName}})</span> </div> <i class="fa fa-angle-down"></i> </a> <div class=sub-nav> <div ng-if="scoreEnable && currentUser.loginName !== \'guest\'" class=score> <a mam-href=~/user/#/center/score/ ><i class="fa fa-database"></i>积分：{{currentUser.additional.score}}</a> </div> <div ng-show=deskShow.link ng-class="{\'current\':isCurrent(\'/desk/#/main/favorite/\')}" ng-if="currentUser.loginName !== \'guest\'"> <a mam-href=~/desk/#/main/favorite/ ><i class="fa fa-desktop"></i>工作台</a> </div> <div ng-class="{\'current\':isCurrent(\'/desk/#/main/message/\')}" ng-if="currentUser.loginName !== \'guest\'"> <a href="/desk/#/main/message/?page=1"><i class="fa fa-envelope-o"></i>我的消息</a> </div> <div ng-class="{\'current\':isCurrent(\'/user/#/center/info\')}" ng-if="currentUser.loginName !== \'guest\'"> <a mam-href=~/user/#/center/info><i class="fa fa-user"></i>个人中心</a> </div> <div ng-if="currentUser.isAdmin && currentUser.loginName !== \'guest\'" class=top-manage ng-class="{\'current\':iscurrent(\'/manage/\')}"> <a href=/manage/ ><i class="fa fa-cog"></i>管理中心</a> </div> <div ng-if="currentUser.isSystemAdmin && currentUser.loginName !== \'guest\'" class=top-system ng-class="{\'current\':iscurrent(\'/sysmanage/\')}"> <a href=/sysmanage/ target=_blank><i class="fa fa-cog"></i>系统配置</a> </div> <div> <a mam-href=~/user/exit><i class="fa fa-power-off"></i>退出</a> </div> </div> </div> <div class=mam-top-nav-user-nologin ng-show=!logged> <a mam-href=~/signup/ ><i class="fa fa-user-plus"></i>注册</a> <a mam-href=~/login><i class="fa fa-sign-in"></i>登录</a> </div>'},function(e,t,n){var o=n(39);"string"==typeof o&&(o=[[e.i,o,""]]);var i={};i.transform=void 0;n(1)(o,i);o.locals&&(e.exports=o.locals)},function(e,t,n){var o=n(40);"string"==typeof o&&(o=[[e.i,o,""]]);var i={};i.transform=void 0;n(1)(o,i);o.locals&&(e.exports=o.locals)},function(e,t,n){var o=n(41);"string"==typeof o&&(o=[[e.i,o,""]]);var i={};i.transform=void 0;n(1)(o,i);o.locals&&(e.exports=o.locals)},function(e,t,n){var o=n(42);"string"==typeof o&&(o=[[e.i,o,""]]);var i={};i.transform=void 0;n(1)(o,i);o.locals&&(e.exports=o.locals)},function(e,t,n){var o=n(43);"string"==typeof o&&(o=[[e.i,o,""]]);var i={};i.transform=void 0;n(1)(o,i);o.locals&&(e.exports=o.locals)},function(e,t,n){var o=n(44);"string"==typeof o&&(o=[[e.i,o,""]]);var i={};i.transform=void 0;n(1)(o,i);o.locals&&(e.exports=o.locals)},function(e,t,n){var o=n(45);"string"==typeof o&&(o=[[e.i,o,""]]);var i={};i.transform=void 0;n(1)(o,i);o.locals&&(e.exports=o.locals)},function(e,t,n){var o=n(46);"string"==typeof o&&(o=[[e.i,o,""]]);var i={};i.transform=void 0;n(1)(o,i);o.locals&&(e.exports=o.locals)},function(e,t,n){var o=n(47);"string"==typeof o&&(o=[[e.i,o,""]]);var i={};i.transform=void 0;n(1)(o,i);o.locals&&(e.exports=o.locals)},function(e,t,n){var o=n(48);"string"==typeof o&&(o=[[e.i,o,""]]);var i={};i.transform=void 0;n(1)(o,i);o.locals&&(e.exports=o.locals)},function(e,t,n){var o=n(49);"string"==typeof o&&(o=[[e.i,o,""]]);var i={};i.transform=void 0;n(1)(o,i);o.locals&&(e.exports=o.locals)},function(e,t,n){var o=n(50);"string"==typeof o&&(o=[[e.i,o,""]]);var i={};i.transform=void 0;n(1)(o,i);o.locals&&(e.exports=o.locals)},function(e,t,n){var o=n(51);"string"==typeof o&&(o=[[e.i,o,""]]);var i={};i.transform=void 0;n(1)(o,i);o.locals&&(e.exports=o.locals)},function(e,t,n){var o=n(52);"string"==typeof o&&(o=[[e.i,o,""]]);var i={};i.transform=void 0;n(1)(o,i);o.locals&&(e.exports=o.locals)},function(e,t,n){var o=n(53);"string"==typeof o&&(o=[[e.i,o,""]]);var i={};i.transform=void 0;n(1)(o,i);o.locals&&(e.exports=o.locals)},function(e,t){e.exports=function(e){var t="undefined"!=typeof window&&window.location;if(!t)throw new Error("fixUrls requires window.location");if(!e||"string"!=typeof e)return e;var n=t.protocol+"//"+t.host,o=n+t.pathname.replace(/\/[^\/]*$/,"/");return e.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,function(e,t){var i=t.trim().replace(/^"(.*)"$/,function(e,t){return t}).replace(/^'(.*)'$/,function(e,t){return t});if(/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/)/i.test(i))return e;var r;return r=0===i.indexOf("//")?i:0===i.indexOf("/")?n+i:o+i.replace(/^\.\//,""),"url("+JSON.stringify(r)+")"})}},function(e,t){e.exports='<svg t="1498805495948" class="icon" style viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3582" xmlns:xlink="http://www.w3.org/1999/xlink"><defs><style type="text/css"></style></defs><path d="M1009.762845 56.889457 14.237155 56.889457C6.386724 56.889457 0.01536 50.546537 0.01536 42.667662L0.01536 14.224071C0.01536 6.37364 6.386724 0.002276 14.237155 0.002276L1009.762845 0.002276C1017.613276 0.002276 1023.98464 6.37364 1023.98464 14.224071L1023.98464 42.667662C1023.98464 50.546537 1017.613276 56.889457 1009.762845 56.889457ZM473.800257 197.087918C476.67306 194.215116 480.00096 192.167177 483.556409 190.773441L483.556409 184.885617C483.556409 177.035187 489.927773 170.663822 497.778205 170.663822L526.221795 170.663822C534.072227 170.663822 540.443591 177.035187 540.443591 184.885617L540.443591 190.773441C543.99904 192.167177 547.32694 194.215116 550.199743 197.087918L997.560544 644.44872C1008.653544 655.570164 1008.653544 673.574957 997.560544 684.696401 986.4391 695.789402 968.434307 695.789402 957.341307 684.696401L540.443591 267.798686 540.443591 1009.749761C540.443591 1017.600192 534.072227 1023.971557 526.221795 1023.971557L497.778205 1023.971557C489.927773 1023.971557 483.556409 1017.600192 483.556409 1009.749761L483.556409 267.798686 66.658693 684.696401C55.565693 695.789402 37.532456 695.789402 26.439456 684.696401 15.318011 673.574957 15.318011 655.570164 26.439456 644.44872L473.800257 197.087918Z" p-id="3583"></path></svg>'},function(e,t){e.exports='<svg t="1498706378122" class="icon" style viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3878" xmlns:xlink="http://www.w3.org/1999/xlink"><defs><style type="text/css"></style></defs><path d="M505.344 852.928l-434.944-315.392c-17.888-12.96-21.856-37.984-8.896-55.872 12.928-17.888 37.984-21.888 55.872-8.896l367.296 266.368 409.376-619.392c12.192-18.464 36.992-23.456 55.424-11.328 18.432 12.192 23.488 36.992 11.328 55.424L505.344 852.928z" p-id="3879"></path></svg>'},function(e,t){e.exports='<svg t="1498738186413" class="icon" style viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2536" xmlns:xlink="http://www.w3.org/1999/xlink"><defs><style type="text/css"></style></defs><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill p-id="2537"></path></svg>'},function(e,t,n){if(null==window.mam)throw"该组件依赖 mam-base";window.mam.ng=window.mam.ng||{},angular.module("mam-ng",["ui.router"]).run(["$http",function(e){mam.ng.$http=e}]),n(4),n(8),n(6),n(5),n(7),n(37),n(36),n(38),n(9),n(10),n(14),n(2),n(17),n(19),n(18),n(21),n(23),n(24),n(25),n(26),n(28),n(29),n(30),n(32),n(22),n(34),n(33),n(2),n(15),n(35),n(31),n(27),n(20),n(11),n(13),n(12),n(16)}]);
//# sourceMappingURL=mam-ng.min.js.map