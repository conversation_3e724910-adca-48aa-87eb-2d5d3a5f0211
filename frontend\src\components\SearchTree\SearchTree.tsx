import { IPermission } from '@/models/permission';
import perCfg from '@/permission/config';
import globalParams from '@/permission/globalParams';
import copyandmoveApis from '@/service/copyandmoveApis';
import searchTypes from '@/types/searchTypes';
import { copyObject, debounce } from '@/utils';
import { Badge, Tree } from 'antd';
import React, {
  FC,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { useHistory, useSelector } from 'umi';
import { IconFont } from '../iconFont/iconFont';
import './style.less';
const { DirectoryTree } = Tree;

interface ITree {
  childFlag: boolean;
  title: string;
  key: string;
  children?: ITree[];
  isLeaf?: boolean;
  icon?: React.ReactNode;
}
interface ChildrenTree {
  contentId: string;
  name: string;
  path: string;
  showName: string;
}
interface ISearchTreeProps {
  path?: string;
  id: string; // 选择的文件夹id
  onSelect?: (id: string, path: string, node?: any) => void; // 选择文件夹后触发的方法
  basictree: searchTypes.IFolder[];
  recycle?: boolean; // true 去掉回收站 | false 保留
  loading: boolean;
  unreadNums?: number;
}

interface DataNode {
  title: string;
  key: string;
  isLeaf?: boolean;
  children?: DataNode[];
}
interface SearchTreeRef {
  referCurrentFolder: () => void;
  selectnode: (node: any) => void;
  updateFolderName: (id: string, name: string) => void;
}
const SearchTree: FC<ISearchTreeProps> = forwardRef((props: ISearchTreeProps, fromRef) => {
  let history: any = useHistory();
  const treeRef = useRef<any>();
  // 权限
  const [isAssistant, setIsAssistant] = useState<boolean>(false);
  const { rmanGlobalParameter,permissions, parameterConfig, my_myVerify_display } = useSelector<{ permission: any }, IPermission>(
    ({ permission }) => permission,
  );
  const [testTree, setTestTree] = useState<ITree[]>([]);
  const [random, setRandom] = useState<any>(0)
  const [userroles, setUserroles] = useState<any>([]); //用户角色
  let target = history.location?.query?.target || ''
  let showVideoResouce = history.location?.query?.showVideoResouce || ''
  const [height, setHeight] = useState<number>(
    Math.max(document.body.clientHeight - 128, 600),
  );
  useEffect(() => {
    const userInfo = (window as any).login_useInfo
    if(userInfo?.roles && userInfo?.roles.length ){
      setUserroles(userInfo.roles.map((item: any) => item.roleCode));
    }
  }, [(window as any).login_useInfo])
  useImperativeHandle(fromRef, () => ({
    // changeVal 就是暴露给父组件的方法
    referCurrentFolder: () => {
      if (selectednode) {
        loadData(selectednode);
      }
    },
    // 从右侧内容区选择 反向选择到左侧节点
    selectnode: (node: any) => {
      if (node) {
        gettreebyid(node.contentId_ || node.id, testTree);
      }
    },
    // 滚动定位到当前选中的文件夹节点
    scrollToCurrentNode: (key: string) => {
      if (key) {
        scrollToKey(key);
        // treeRef.current.scrollTo({key,offset:550})
      }
    },
    //用于文件夹重命名后更新树形name
    updateFolderName: (id: string, name: string) => {
      if (id) {
        setTestTree((origin: any) => updateTreeName(origin, id, name));
        scrollToKey(id);
        // treeRef.current.scrollTo({key:id,offset:550})
      }
    },
    deleteNode: (id: string, name: string) => {
      if (id) {
        setTestTree((origin: any) => deleteTreeNode(origin, id));
        // scrollToKey(id);
        // treeRef.current.scrollTo({key:id,offset:550})
      }
    },
    addNode: (item: any) => {
      if (item) {
        setTestTree((origin: any) => addTreeNode(origin, item));
      }
    },
  }));
  useEffect(() => {
    if ((window as any).login_useInfo) {
      // console.log('myVideo',props.myVideo)
      // 判断当前用户是否有删除的权限
      const userInfo = (window as any).login_useInfo
      if (userInfo) {
        const roless = userInfo.roles?.map((item: any) => item.roleCode)
        // console.log('juese', roless)
        // 当前用户角色为zhiliao、sys和资源管理员
        if (roless?.includes('r_teacher_assistant')) {
          setIsAssistant(false)
        } else {
          setIsAssistant(false)
        }
      }
    }
  }, [(window as any).login_useInfo])

  const [expandedKeys, setExpandedKeys] = useState<React.ReactText[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState<any>(true);
  const [selectedKeys, setSelectedKeys] = useState<React.ReactText[]>([]);
  const [selectednode, setSelectednode] = useState<any>(null);
  const [loadKeys, setLoadKeys] = useState<any>([]);
  const [folderId, setFolderId] = useState<string>('');
  useEffect(() => {
    window.addEventListener(
      'resize',
      debounce(() => {
        setHeight(document.body.clientHeight - 128);
      }, 200),
    );
    // setLoadKeys(['recyclebin'])
  }, []);
  // useEffect(() => {
  //   if (history.location.query.isToWork) {
  //     setSelectedKeys(['myVideo']);
  //   }
  // }, []);
  useEffect(() => {
    if (props.basictree?.length > 0) {
      setLoadKeys([
        'split_',
        'myVideo',
        'departmentVideo',
        'myCollection',
        'myShared',
        'myVerify',
        'publishManagement',
        'recyclebin',
      ]);
      let list: any = forTree(props.basictree);
      let hidecatalogue =
        JSON.stringify(history.location.query) === '{}'
          ? '0'
          : history.location.query.hidecatalogue;
      hidecatalogue === '1' && removeTreeListItem(list, '录播资源');
      list.forEach((item:any,index:number) => {
        if(item.title === '群组资源'){
          item.title = parameterConfig.target_customer as string ==='cqnu' ?'群组空间' : '群组资源'
        }
      });
      console.log(list,'zzzzzzzzzzzzzzzzzz')
      if (isAssistant) {
        list.forEach((item:any,index:number) => {
          if(item.title === '案例库文件夹创建' || item.title.includes('课程资源区')){
            list.splice(index,1)
          }
        });
      }
      if (!permissions.includes(perCfg.share_folder_permission) || isAssistant|| target=== 'custom' || !rmanGlobalParameter.includes(globalParams.share_folder_display)) {
        list.forEach((item:any,index:number) => {
          if(item.title === '共享资源'){
            list.splice(index,1)
          }
        });
      }
      // 不勾选则不进行展示(针对教师角色，其他角色一直显示)
      if (!permissions.includes(perCfg.vedio_resource_permission)) { // 没有勾选
        if (isAssistant || target=== 'custom'   
        || (!(userroles.includes('r_resource_manager') || userroles.includes('r_sys_manager') || userroles.includes('admin_S1'))) // 是教师
          && showVideoResouce !== 'true') {
            list.forEach((item:any,index:number) => {
              if(item.title === '录播资源'){
                list.splice(index,1)
              }
            });
        }
      }
      if (!permissions.includes(perCfg.folder_group_permission) || isAssistant|| target=== 'custom') {
        list.forEach((item:any,index:number) => {
          if(item.title === '群组资源'){
            list.splice(index,1)
          }
        });
      }
      list.push({
        key: 'split_',
        title: '',
        layer: 1,
        selectable: false, //禁止选中
        icon: <></>,
      });
      if (rmanGlobalParameter.includes(globalParams.department_video_display) && permissions.includes(perCfg.department_video_resource) && !isAssistant) {
        list.push({
          key: 'departmentVideo',
          title: '院系录播',
          layer: 1,
          icon: <IconFont type="iconwodelubo" />,
        });
      }
      
      if (rmanGlobalParameter.includes(globalParams.my_video_display) && target!== 'custom'&& !isAssistant) {
        list.push({
          key: 'myVideo',
          title: '我的录播',
          layer: 1,
          icon: <IconFont type="iconwodelubo" />,
        });
      }
      if (rmanGlobalParameter.includes(globalParams.my_collection_display) && !isAssistant && target!== 'custom') {
        list.push({
          key: 'myCollection',
          title: '我的收藏',
          layer: 1,
          icon: <IconFont type="iconxingzhuang2" />,
        });
      }
      if (rmanGlobalParameter.includes(globalParams.my_shared_display) && !isAssistant&& target!== 'custom') {
        list.push({
          key: 'myShared',
          layer: 1,
          title: <span className="myShared">我的分享</span>,
          icon: <IconFont type="icona-xingzhuangbeifen2" />,
        });
      }

      // 设置为true时或不设置都是默认展示
      if (my_myVerify_display  && !isAssistant && target!== 'custom')
      {
        list.push({
          key: 'myVerify',
          layer:1,
          title: <span className='myVerify'>我的审核</span>,
          icon: <IconFont type='iconshenhe' />
        });
      }
      
      if(permissions.includes(perCfg.publish_resource) && !isAssistant&& target!== 'custom'){
        if(!isAssistant && target!== 'custom'){
          list.push({
            key: 'publishManagement',
            layer:1,
            title: <span className='pubulishManagement'>发布管理</span>,
            icon: <IconFont type='iconshenhe' />
          });
        }
      }
      if (!props.recycle && !isAssistant && target!== 'custom') {
        list.push({
          key: 'recyclebin',
          title: '回收站',
          layer: 1,
          icon: <IconFont type="iconhuishouzhan" />,
        });
      }
      console.log('初始值', list);
      setSelectednode(list[0])
      setTestTree(list);
      if(target === 'custom'){
        setSelectedKeys([list[0].key]);
      }
      else{
        if (props.id != "") {
          setSelectedKeys([props.id]);
        }
      }

      findTreeNode();
    }
  }, [props.basictree]);
  useEffect(() => {
    console.log('height' + new Date(), height);
  }, [height]);
  //虚拟滚动有bug 决定重写滚动事件
  const scrollToKey = (key: any) => {
    const node = document.getElementsByClassName(key)?.[0]?.parentElement
      ?.parentElement?.parentElement;
    const nodeDiv = document.getElementsByClassName(
      'ant-tree-list-holder-inner',
    )?.[0];
    if (!node || !nodeDiv) {
      return;
    }
    nodeDiv.scrollTo({
      top: node.offsetTop - 50,
      behavior: 'smooth',
    });
  };
  //定时器
  const findTreeNode = () => {
    const interval = setInterval(() => {
      const split_node: any = document.getElementsByClassName('split_')?.[0]
        ?.parentElement?.parentElement?.parentElement;
      if (split_node) {
        split_node.classList.add('split_node');
        clearInterval(interval);
      }
    }, 200);
  };
  // 根据右侧选中的内容 反向找到左侧的节点 并选中
  const gettreebyid = (id: string, list: any) => {
    if (list && list.length) {
      list.forEach((node: any) => {
        if (node.key === id) {
          setSelectednode(node);
          setSelectedKeys([node.key]);
          const newExpandedKeys = copyObject(expandedKeys);
          // console.log(newExpandedKeys);
          if (!newExpandedKeys.includes(id)) {
            newExpandedKeys.push(id);
            setExpandedKeys(newExpandedKeys);
          }
        } else {
          gettreebyid(id, node.children);
        }
      });
    }
  };

  const removeTreeListItem = (treeList: any, name: string) => {
    // 根据id属性从数组（树结构）中移除元素
    if (!treeList || !treeList.length) {
      return;
    }
    for (let i = 0; i < treeList.length; i++) {
      if (treeList[i].title === name) {
        treeList.splice(i, 1);
        break;
      }
      removeTreeListItem(treeList[i].children, name);
    }
  };
  useEffect(() => {
    if (props.id != '') {
      setSelectedKeys([props.id]);
    }
  }, [props.id]);

  useEffect(() => {
    if (selectedKeys.length > 0) {
      // treeRef.current.scrollTo({key:selectedKeys[0]})
      scrollToKey(selectedKeys[0]);
    }
  }, [selectedKeys]);

  const forTree = (tree: any) => {
    return tree.map((item: searchTypes.IFolder) => {
      if (!item.children && !item.childCount) {
        if (!loadKeys.includes(item.id)) {
          setLoadKeys((origin: any) => origin.concat([item.id]));
        }
      }
      return {
        key: item.id,
        path: item.path,
        title: item.name,
        childCount: item.childCount,
        layer: item.layer,
        children: item.children ? forTree(item.children) : [],
      };
    });
  };

  const onSelect = (keys: React.ReactText[], node: any) => {
    if(!props.loading){
      sessionStorage.setItem('id', '')
      if (props.onSelect) {
        setSelectednode(node.node);
        props.onSelect(keys[0] + '', node.node.path,node.node);
      }
      //对于有子节点 但未加载过的 提前加载
      if(node.node.childCount>0 && node.node.children.length==0){
        loadData(node.node);
      }
      setSelectedKeys(keys);
    }
  };

  const onExpand = (keys: React.ReactText[]) => {
    setExpandedKeys(keys);
    setAutoExpandParent(false); //在手动点击展开时需要 关闭自动展开父级 不然功能会失效
  };

  const updateTreeData = (
    list: ITree[],
    key: React.Key,
    children: ITree[],
  ): ITree[] => {
    return list.map((node: any) => {
      if (node.key === key) {
        return {
          // isLeaf:false,
          ...node,
          children,
        };
      }
      if (node.children) {
        return {
          // isLeaf:false,
          ...node,
          children: updateTreeData(
            JSON.parse(JSON.stringify(node.children)),
            key,
            children,
          ),
        };
      }
      return node;
    });
  };
  const updateTreeName = (
    list: ITree[],
    key: React.Key,
    newName: string,
  ): ITree[] => {
    return list.map((node: any) => {
      if (node.key === key) {
        const arr = node.path.split('/');
        arr[arr.length - 1] = newName;
        return {
          // isLeaf:false,
          ...node,
          title: newName,
          path: arr.join('/'),
        };
      }
      if (node.children) {
        return {
          // isLeaf:false,
          ...node,
          children: updateTreeName(
            JSON.parse(JSON.stringify(node.children)),
            key,
            newName,
          ),
        };
      }
      return node;
    });
  };
  const addTreeNode: any = (treeData: any, item: any) => {
    
    let data =  treeData.map((node: any) => {
      if (node.key === selectednode.key) {
        return {
          ...node,
          children: [
            ...node.children,
            {
              key: item.contentId,
              path: item.path,
              title: item.showName,
              childCount: item.childCount,
              layer: selectednode.layer + 1,
              children: [],
            }]
        }
      }else if (node.children) {
        return {
          ...node,
          children: addTreeNode(node.children, item),
        };
      }
      return node;
    })
    
    return data
  };
  const deleteTreeNode: any = (treeData: any, key: string) => {
    return treeData.map((node:any) => {
      if (node.key === key) {
        return undefined;
      } else if (node.children) {
        // 如果节点有子节点，则递归处理子节点
        return {
          ...node,
          children: deleteTreeNode(node.children, key),
        };
      }
      return node; 
    }).filter(Boolean); 
  };
  // 异步加载节点  只加载5层节点  多的不显示
  const loadData = ({ path, key, layer, children }: any): any => {
    return new Promise<void>(resolve => {
      if (key == 'recyclebin' || layer >= 5) {
        resolve();
        // 加载完了没有数组  要把key添加到loadkey里面 防止无限循环加载
        let arrloadlkey = [key];
        loadKeys?.forEach((item: any) => {
          arrloadlkey.push(item);
        });
        setLoadKeys(arrloadlkey);
      } else {
        if (children?.length > 0) {
          resolve();
          return;
        }
        const time1 = new Date().getTime();
        copyandmoveApis.movetreeChildern(path).then((res: any) => {
          const time2 = new Date().getTime();
          console.log('经过了', time2 - time1);
          //加延时是为了解决异步文件目录不及时更新bug 初测300ms比较合适
          setTimeout(
            () => {
              if (res && res.data.length > 0) {
                let arr: any = [];
                res.data.forEach((item: any) => {
                  arr.push({
                    key: item.contentId,
                    path: item.path,
                    title: item.showName,
                    childCount: item.childCount,
                    layer: layer + 1,
                    children: [],
                  });
                  // 对无子节点设置已加载标识实现去掉展开符并保留文件夹的图标(ps:不能使用叶子节点isLeaf标识,不然图标就是文件图标)
                  if (
                    item.childCount === 0 &&
                    !loadKeys.includes(item.contentId)
                  ) {
                    setLoadKeys((origin: any) =>
                      origin.concat([item.contentId]),
                    );
                  }
                });
                setTestTree((origin: any) => updateTreeData(origin, key, arr));
                console.log('刚更新后的testTree', testTree);
                let arrloadlkey = loadKeys;
                if (!loadKeys.includes(key)) {
                  arrloadlkey = arrloadlkey.concat([key]);
                  setLoadKeys((origin: any) => origin.concat([key]));
                }
              } else {
                setTestTree((origin: any) => updateTreeData(origin, key, []));
                // 加载完了没有数组  要把key添加到loadkey里面 防止无限循环加载
                let arrloadlkey: any = [key];
                // console.log('原来', loadKeys)
                loadKeys?.forEach((item: any) => {
                  arrloadlkey.push(item);
                });
                setLoadKeys(arrloadlkey);
              }
              // console.log(treeRef,treeRef.current)
              // if (selectednode
              // ) { //防止展开其他节点而导致滚动定位过去
              //   // treeRef.current.scrollTo({ key: selectednode.key,offset:550 }); // 滚动定位到新建的文件目录
              //   scrollToKey(selectednode.key);
              // }
              resolve();
            },
            time2 - time1 > 300 ? 0 : 300,
          );
        });
      }
    });
  };

  useEffect(() => {
    if(sessionStorage.publishResource){
      setSelectedKeys(['myVerify'])
    }
  }, [sessionStorage.publishResource])

  useEffect(() => {
    if (!selectedKeys .includes('myVerify')) {
      sessionStorage.removeItem('publishResource')
    }
  }, [selectedKeys]);

  return (
    <div className="search-tree">
      <DirectoryTree
        multiple
        ref={treeRef}
        showIcon={true}
        expandedKeys={expandedKeys}
        selectedKeys={selectedKeys}
        autoExpandParent={autoExpandParent}
        titleRender={(e: any) => {
          const node = (
            <span
              className={`${e.key}${
                e.layer == 1
                  ? ` root_node_title${e.key == 'split_' ? ' split_' : ''}`
                  : ''
              }`}
            >
              {e.title}
            </span>
          );
          if (e.key === 'myShared') {
            return (
              <>
                {Number(props.unreadNums) > 0 ? (
                  <Badge
                    count={props.unreadNums}
                    overflowCount={99}
                    offset={[105, 6]}
                  >
                    {node}
                  </Badge>
                ) : (
                  node
                )}
              </>
            );
          } else {
            return node;
          }
        }}
        key={testTree as any}
        loadedKeys={loadKeys}
        onSelect={onSelect}
        onExpand={onExpand}
        treeData={testTree}
        loadData={loadData}
        expandAction={'doubleClick'}
        // height={height}
      />
    </div>
  );
});

export default SearchTree; 
