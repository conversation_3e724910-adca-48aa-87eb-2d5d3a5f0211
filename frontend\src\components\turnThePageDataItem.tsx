import React, { FC, useEffect, useState } from 'react'
import { Form, Select } from 'antd'
import contentListApis from '@/service/contentListApis';

const { Option } = Select
interface IBasicItemProps {
  required: boolean
  message: string
  label: string
  name: string
  multiple: boolean
  type: number
}
const TurnThePageDataItem: FC<IBasicItemProps> = (props) => {
  const { required, message, label, name, multiple, type } = props
  const [teacherList, setTeacherList] = useState<any>([])
  const [keyword, setKeyword] = useState<string>('')
  const [page, setPage] = useState<number>(1)
  const [totalPage, setTotalPage] = useState<number>(1)
  useEffect(() => {
    getTeacherList()
  }, [keyword, page])
  const getTeacherList = () => {
    // &school=edu
    contentListApis.getPagingData(
      `sourceTpye=${type}&pageIndex=${page}&pageSize=30${
        keyword ? '&keyWord=' + keyword : ''
      }`
    ).then((res) => {
      if (res && (res.errorCode === 'success' || res.errorCode === 'success')) {
        setTotalPage(res.extendMessage.pageTotal)
        if (page === 1) {
          setTeacherList(res.extendMessage.results)
        } else {
          setTeacherList([...teacherList, ...res.extendMessage.results])
        }
      }
    })
  }
  const handleSearch = (value: string) => {
    setPage(1)
    setKeyword(value)
  }
  const handleChange = (value: string) => {
    if (!value || value.length) {
      setPage(1)
      setKeyword('')
    }
  }
  const handleScroll = (e: any) => {
    const { target } = e
    const total = target.scrollTop + target.offsetHeight
    const scrollHeight = target.scrollHeight
    if (
      total >= scrollHeight - 1 &&
      total < scrollHeight + 1 &&
      page < totalPage
    ) {
      setPage(page + 1)
    }
  }
  return (
    <Form.Item
      label={label}
      name={name}
      rules={[{ required: required, message: message }]}
    >
      {multiple ? (
        <Select
          mode="multiple"
          showSearch
          style={required ? { width: 200 } : { width: 200 }}
          placeholder={message}
          allowClear={true}
          labelInValue
          filterOption={false}
          onSearch={handleSearch}
          onChange={handleChange}
          onPopupScroll={handleScroll}
        >
          {teacherList.map((item: any) => (
            <Option value={item.fieldCode} key={'teacher' + item.fieldCode}>
              {item.fieldValue}
            </Option>
          ))}
        </Select>
      ) : (
        <Select
          showSearch
          style={required ? { width: 130 } : { width: 130 }}
          placeholder={message}
          allowClear={true}
          filterOption={false}
          onSearch={handleSearch}
          onChange={handleChange}
          onPopupScroll={handleScroll}
        >
          {teacherList.map((item: any) => (
            <Option
              value={item.fieldCode}
              key={'teacher' + item.fieldCode}
            >
              {item.fieldValue}
            </Option>
          ))}
        </Select>
      )}
    </Form.Item>
  )
}

export default TurnThePageDataItem
