!function(e){function t(n){if(a[n])return a[n].exports;var i=a[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,t),i.l=!0,i.exports}var a={};t.m=e,t.c=a,t.i=function(e){return e},t.d=function(e,a,n){t.o(e,a)||Object.defineProperty(e,a,{configurable:!1,enumerable:!0,get:n})},t.n=function(e){var a=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(a,"a",a),a},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="/dist/",t(t.s=52)}([function(e,t){function a(e,t){var a=e[1]||"",i=e[3];if(!i)return a;if(t&&"function"==typeof btoa){var m=n(i);return[a].concat(i.sources.map(function(e){return"/*# sourceURL="+i.sourceRoot+e+" */"})).concat([m]).join("\n")}return[a].join("\n")}function n(e){return"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(e))))+" */"}e.exports=function(e){var t=[];return t.toString=function(){return this.map(function(t){var n=a(t,e);return t[2]?"@media "+t[2]+"{"+n+"}":n}).join("")},t.i=function(e,a){"string"==typeof e&&(e=[[null,e,""]]);for(var n={},i=0;i<this.length;i++){var m=this[i][0];"number"==typeof m&&(n[m]=!0)}for(i=0;i<e.length;i++){var o=e[i];"number"==typeof o[0]&&n[o[0]]||(a&&!o[2]?o[2]=a:a&&(o[2]="("+o[2]+") and ("+a+")"),t.push(o))}},t}},function(e,t,a){function n(e,t){for(var a=0;a<e.length;a++){var n=e[a],i=p[n.id];if(i){i.refs++;for(var m=0;m<i.parts.length;m++)i.parts[m](n.parts[m]);for(;m<n.parts.length;m++)i.parts.push(d(n.parts[m],t))}else{for(var o=[],m=0;m<n.parts.length;m++)o.push(d(n.parts[m],t));p[n.id]={id:n.id,refs:1,parts:o}}}}function i(e,t){for(var a=[],n={},i=0;i<e.length;i++){var m=e[i],o=t.base?m[0]+t.base:m[0],r=m[1],l=m[2],c=m[3],d={css:r,media:l,sourceMap:c};n[o]?n[o].parts.push(d):a.push(n[o]={id:o,parts:[d]})}return a}function m(e,t){var a=h(e.insertInto);if(!a)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var n=b[b.length-1];if("top"===e.insertAt)n?n.nextSibling?a.insertBefore(t,n.nextSibling):a.appendChild(t):a.insertBefore(t,a.firstChild),b.push(t);else{if("bottom"!==e.insertAt)throw new Error("Invalid value for parameter 'insertAt'. Must be 'top' or 'bottom'.");a.appendChild(t)}}function o(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e);var t=b.indexOf(e);t>=0&&b.splice(t,1)}function r(e){var t=document.createElement("style");return e.attrs.type="text/css",c(t,e.attrs),m(e,t),t}function l(e){var t=document.createElement("link");return e.attrs.type="text/css",e.attrs.rel="stylesheet",c(t,e.attrs),m(e,t),t}function c(e,t){Object.keys(t).forEach(function(a){e.setAttribute(a,t[a])})}function d(e,t){var a,n,i,m;if(t.transform&&e.css){if(!(m=t.transform(e.css)))return function(){};e.css=m}if(t.singleton){var c=y++;a=g||(g=r(t)),n=s.bind(null,a,c,!1),i=s.bind(null,a,c,!0)}else e.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(a=l(t),n=u.bind(null,a,t),i=function(){o(a),a.href&&URL.revokeObjectURL(a.href)}):(a=r(t),n=f.bind(null,a),i=function(){o(a)});return n(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;n(e=t)}else i()}}function s(e,t,a,n){var i=a?"":n.css;if(e.styleSheet)e.styleSheet.cssText=w(t,i);else{var m=document.createTextNode(i),o=e.childNodes;o[t]&&e.removeChild(o[t]),o.length?e.insertBefore(m,o[t]):e.appendChild(m)}}function f(e,t){var a=t.css,n=t.media;if(n&&e.setAttribute("media",n),e.styleSheet)e.styleSheet.cssText=a;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(a))}}function u(e,t,a){var n=a.css,i=a.sourceMap,m=void 0===t.convertToAbsoluteUrls&&i;(t.convertToAbsoluteUrls||m)&&(n=x(n)),i&&(n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(i))))+" */");var o=new Blob([n],{type:"text/css"}),r=e.href;e.href=URL.createObjectURL(o),r&&URL.revokeObjectURL(r)}var p={},v=function(e){var t;return function(){return void 0===t&&(t=e.apply(this,arguments)),t}}(function(){return window&&document&&document.all&&!window.atob}),h=function(e){var t={};return function(a){return void 0===t[a]&&(t[a]=e.call(this,a)),t[a]}}(function(e){return document.querySelector(e)}),g=null,y=0,b=[],x=a(49);e.exports=function(e,t){if("undefined"!=typeof DEBUG&&DEBUG&&"object"!=typeof document)throw new Error("The style-loader cannot be used in a non-browser environment");t=t||{},t.attrs="object"==typeof t.attrs?t.attrs:{},t.singleton||(t.singleton=v()),t.insertInto||(t.insertInto="head"),t.insertAt||(t.insertAt="bottom");var a=i(e,t);return n(a,t),function(e){for(var m=[],o=0;o<a.length;o++){var r=a[o],l=p[r.id];l.refs--,m.push(l)}if(e){n(i(e,t),t)}for(var o=0;o<m.length;o++){var l=m[o];if(0===l.refs){for(var c=0;c<l.parts.length;c++)l.parts[c]();delete p[l.id]}}}};var w=function(){var e=[];return function(t,a){return e[t]=a,e.filter(Boolean).join("\n")}}()},function(e,t,a){angular.module("mam-metadata").directive("mam2MfcBool",["mam2MetadataService",function(e){return{restrict:"E",template:a(25),replace:!0,scope:{item:"=",type:"@"},link:function(e,t,a,n){}}}])},function(e,t,a){angular.module("mam-metadata").directive("mam2MfcDate",["mam2MetadataService",function(e){return{restrict:"E",template:a(26),replace:!0,scope:{item:"=",type:"@"},link:function(t,a,n,i){function m(){e.validate(t.item)}function o(){var e;try{e=JSON.parse(t.item.controlData)}catch(t){e=null}var a={showSecond:!0,format:"Y-m-d",timepicker:!1,onClose:function(){""!=r.val()&&(t.item.value=r.val(),m(),t.$applyAsync())}};if(null!=e)switch(e.type){case"onlypass":a.maxDate="0";break;case"onlyfuture":a.minDate="0"}t.item.isReadOnly||r.datetimepicker(a)}var r=$(a);t.$watch("type",function(){"edit"==t.type&&(o(),a.find("input[type=text]").on("blur",function(){t.$apply(function(){m()})}))})}}}])},function(e,t,a){angular.module("mam-metadata").directive("mam2MfcDatetime",["mam2MetadataService",function(e){return{restrict:"E",template:a(27),replace:!0,scope:{item:"=",type:"@"},link:function(t,a,n,i){function m(){e.validate(t.item)}function o(){var e;try{e=JSON.parse(t.item.controlData)}catch(t){e=null}var a={showSecond:!0,formatTime:"H:m:s",format:"Y-m-d H:m:s",onClose:function(){""!=r.val()&&(t.item.value=r.val(),m(),t.$applyAsync())}};if(null!=e)switch(e.type){case"onlypass":a.maxDate="0";break;case"onlyfuture":a.minDate="0"}t.item.isReadOnly||r.datetimepicker(a)}var r=$(a);t.$watch("type",function(){"edit"==t.type&&(o(),a.find("input[type=text]").on("blur",function(){t.$apply(function(){m()})}))})}}}])},function(e,t,a){angular.module("mam-metadata").directive("mam2MfcFrameToTimecode",function(){return{restrict:"E",template:a(28),replace:!0,scope:{item:"=",type:"@",entity:"<"},link:function(e,t,a,n){e.model=e.item.value,null!=e.model&&""!=e.model||(e.model=0),e.model=timecodeconvert.frame2Tc(e.model,e.entity.frameRate)}}})},function(e,t,a){angular.module("mam-metadata").directive("mam2MfcNanosecondToTimecode",function(){return{restrict:"E",template:a(29),replace:!0,scope:{item:"=",type:"@",entity:"<"},link:function(e,t,a,n){e.model=e.item.value,null!=e.model&&""!=e.model||(e.model=0),"audio"==e.entity.type?e.model=timecodeconvert.SecondToTimeString_audio(parseInt(e.model)/1e7):e.model=timecodeconvert.frame2Tc(timecodeconvert.second2Frame(parseInt(e.model)/1e7,e.frameRate),e.frameRate)}}})},function(e,t,a){angular.module("mam-metadata").directive("mam2MfcNumber",["mam2MetadataService",function(e){return{restrict:"E",template:a(30),replace:!0,scope:{item:"=",type:"@"},link:function(t,a,n,i){var m=$(a);t.$watch("type",function(){"browse"!=t.type&&m.find("input").on("blur",function(){e.validate(t.item),t.$applyAsync()})})}}}])},function(e,t,a){angular.module("mam-metadata").directive("mam2MfcSelect",["mam2MetadataService",function(e){return{restrict:"E",template:a(31),replace:!0,scope:{item:"=",type:"@",onChange:"&?"},link:function(t,a,n,i){function m(){var e=t.item.value;null!=e&&""!=e||(e="[]");try{e=JSON.parse(e)}catch(t){e=[e]}var a=_.map(e,function(e){return{key:e,value:t.items[e]}});t.item.isMultiSelect?t.model=a:t.model=0==a.length?{}:a[0]}function o(){var e;e=t.item.isMultiSelect?_.map(t.model,"key"):[t.model.key],t.item.value=JSON.stringify(e)}var r=null;t.items=[],t.model,t.onSelect=function(a,n){if(t.model=n,o(),e.validate(t.item),!t.item.isMultiSelect&&r.key!=t.model.key){var i=r.key||"";t.onChange({value:t.model.key,oldValue:i,item:t.item}),r=t.model}},t.onRemove=t.onSelect,function(){_.isString(t.item.controlData)&&t.item.controlData.length>0&&(t.items=JSON.parse(t.item.controlData)),m(),t.item.isMultiSelect||(r=t.model)}()}}}])},function(e,t,a){angular.module("mam-metadata").directive("mam2MfcSize",[function(){return{restrict:"E",template:a(32),replace:!0,scope:{item:"=",type:"@"},link:function(e,t,a,n){}}}])},function(e,t,a){a(50),a(33);angular.module("mam-metadata").directive("mam2MfcTable",["$timeout","mam2MetadataService","$uibModal","$interval",function(e,t,n,i){return{restrict:"E",template:a(34),replace:!0,scope:{item:"=",type:"@",onChange:"&?"},link:function(t,a,n,m){function o(){"browse"==t.type?a.find(".mam-metadata-table").width(_.filter(t.configDataJson,function(e){return void 0===e.isShow||!0===e.isShow}).length*u):"edit"==t.type&&a.find(".mam-metadata-table").width(t.field.length*u)}function r(){t.type||(t.type="browse"),t.selectIndex=-1,t.configData=JSON.parse(t.item.controlData),t.configDataJson=angular.copy(t.configData);try{t.model=angular.fromJson(t.item.value),t.model||(t.model=[])}catch(e){t.model=[]}f(),t.configDataJson&&e(function(){l()}),t.edit(),o()}function l(){if(t.extraRows=[],t.model.length<3)for(var e=t.model.length+1;e<=3;e++)t.extraRows.push(e);else t.extraRows.push(1)}function c(){if(t.editExtraRows=[],t.fieldData.length<3)for(var e=t.fieldData.length+1;e<=3;e++)t.editExtraRows.push(e);else t.editExtraRows.push(1)}function d(){if(t.model.length>0){var e=angular.copy(t.model);_.forEach(e,function(e){e.hasOwnProperty("$$hashKey"),delete e.$$hashKey}),t.item.value=JSON.stringify(e)}else t.item.value="[]"}function s(e,t){var a="";return _.forEach(t,function(t){var n=!1;if(e.indexOf(t.categoryCode)>-1&&(a+=t.categoryName+"/",t.categoryCode!==e?a+=s(e,t.children):n=!0),n)return a}),a}function f(){_.forEach(t.configDataJson,function(e){null!=e.data&&"string"==typeof e.data&&""!=e.data&&(e.data=JSON.parse(e.data)),null!=e.controlData&&"string"==typeof e.controlData&&""!=e.controlData&&(e.controlData=JSON.parse(e.controlData))})}var u=200;t.addBlankRow=function(){var e=[];t.lastIndex++,_.forEach(t.field,function(a){var n=angular.copy(a);n.value="",n.index=t.lastIndex,Object.defineProperty(n,"value",{get:function(){return this._value},set:function(e){this._value=e,e&&n.index===t.lastIndex&&t.addBlankRow()}}),e.push(n)}),t.fieldData.push(e),c()},t.edit=function(){var e={field:t.configData,data:t.model};t.field=e.field,t.fieldData=_.map(e.data,function(t){return _.map(e.field,function(e){var a=angular.copy(e);return a.value=t[e.fieldName],a})}),t.lastIndex=t.fieldData.length-1,t.addBlankRow()};var p=function(e){var t=!0;return _.forEach(e,function(e){e.value&&(t=!1)}),t},v=function(){var e=[];_.forEach(t.fieldData,function(t){if(!p(t)){var a={};_.forEach(t,function(e){a[e.fieldName]=e.value}),e.push(a)}}),t.model=e};t.reduce=function(a,n){mam.confirm("确定删除此项吗？").then(function(a){e(function(){t.selectIndex=n,t.selectIndex<0||(t.fieldData.splice(t.selectIndex,1),t.selectIndex=-1,v(),d(),c())})},function(e){})},t.sortableOptions={update:function(e,t){},stop:function(e,t){d()}},t.getName=function(e,t){if(null!=e&&e.indexOf("[")>-1){e=JSON.parse(e);var a="";return _.forEach(e,function(e){a=a+=t[e]}),a}},t.getPath=function(e,t){if(null!=e&&""!=e&&"[]"!=e){var a=e.split(","),n=[];_.forEach(a,function(e,a){var i=s(e,t);""!==i&&(i=i.substring(0,i.length-1)),n[a]=i});var i=[];return _.forEach(n,function(e,t){""!==e&&(i[i.length]=e)}),i.join()}},t.getBool=function(e){return 1==e||"True"==e||"true"==e?"是":"否"},t.$watch("type",function(e,t){e&&r()}),i(function(){v(),d()},1e3),window.onunload=function(e){i.cancel(interval)},r()}}}])},function(e,t,a){angular.module("mam-metadata").directive("mam2MfcTag",["mam2MetadataService",function(e){return{restrict:"E",template:a(35),replace:!0,scope:{item:"=",type:"@"},link:function(t,a,n,i){function m(){null!=t.item.value&&t.item.value.length>0?t.tags=_.map(t.item.value.split(","),function(e){return{text:e}}):t.tags=[]}function o(){t.item.value=_.map(t.tags,"text").join(",")}function r(e){return null!=t.options.tagMinLen&&t.options.tagMinLen>0&&e.text.length<t.options.tagMinLen?(t.item.error="custom:单个标签的最小长度为"+t.options.tagMinLen,!1):null!=t.options.tagMaxLen&&t.options.tagMaxLen>0&&e.text.length>t.options.tagMaxLen?(t.item.error="custom:单个标签的最大长度为"+t.options.tagMaxLen,!1):null==_.find(t.tags,{text:e.text})||(t.item.error="custom:已存在该标签",!1)}t.tags=[],t.options={},t.adding=function(e){if(!r(e))return!1},t.added=function(a){o(),e.validate(t.item)},t.remove=function(a){o(),e.validate(t.item)},t.invalid=function(e){r(e)},t.$watch("item.value",function(e){void 0!=e&&m()}),function(){m(),_.isString(t.item.controlData)&&t.item.controlData.length>0&&(t.options=JSON.parse(t.item.controlData));var n=$(a);$(document).ready(function(){"browse"!=t.type&&n.find("input[type=text]").on("blur",function(){e.validate(t.item),t.$applyAsync()})})}()}}}])},function(e,t,a){angular.module("mam-metadata").directive("mam2MfcText",["mam2MetadataService",function(e){return{restrict:"E",template:a(36),replace:!0,scope:{item:"=",type:"@"},link:function(t,a,n,i){var m=$(a);t.$watch("type",function(){"browse"!=t.type&&m.find("input[type=text]").on("blur",function(){e.validate(t.item),t.$applyAsync()})})}}}])},function(e,t,a){angular.module("mam-metadata").directive("mam2MfcTextarea",["mam2MetadataService","$sce",function(e,t){return{restrict:"E",template:a(37),replace:!0,scope:{item:"=",type:"@"},link:function(a,n,i,m){var o=$(n);a.trustAsHtml=function(e){return _.isString(e)?(e=e.replace(new RegExp("\r\n","g"),"<br>"),e=e.replace(new RegExp("\n","g"),"<br>"),e=e.replace(new RegExp("\r","g"),"<br>"),t.trustAsHtml(e)):e},a.$watch("type",function(){"browse"!=a.type&&o.find("textarea").on("blur",function(){e.validate(a.item),a.$applyAsync()})})}}}])},function(e,t,a){angular.module("mam-metadata").directive("mam2MfcTimearea",["$timeout","mam2MetadataService",function(e,t){return{restrict:"E",template:a(38),replace:!0,scope:{item:"=",type:"@"},link:function(a,n,i,m){function o(){t.validate(a.item)}function r(){var t;try{t=JSON.parse(a.item.controlData)}catch(e){t=null}var n={showSecond:!0,format:"Y-m-d",timepicker:!1,onClose:function(){e(function(){l()})}},i=angular.copy(n);if(null!=t)switch(t.type){case"onlypass":n.maxDate="0",i.maxDate="0";break;case"onlyfuture":n.minDate="0",i.minDate="0"}a.item.isReadOnly||(c.find(".start-time>input").datetimepicker(n),c.find(".end-time>input").datetimepicker(i))}function l(){a.item.value=a.model.startModel+","+a.model.endModel,o()}var c=$(n);if(a.model={startModel:"",endModel:""},null!=a.item.value&&a.item.value.length>0){var d=a.item.value.split(",");a.model.startModel=d[0],a.model.endModel=d[1]}a.getErrorInfo=function(e,a){return t.getErrorInfo(e,a)},a.$watch("type",function(){"edit"==a.type&&(r(),n.find("input[type=text]").each(function(e){$(this).on("blur",function(){a.$apply(function(){o()})})}))})}}}])},function(e,t,a){var n=(a(51),a(39));angular.module("mam-metadata").directive("mam2MfcTree",["mam2MetadataService","$uibModal",function(e,t){return{restrict:"E",template:a(40),replace:!0,scope:{item:"=",type:"@"},link:function(a,i,m,o){function r(e,t){for(var a=0;a<t.length;a++){if(t[a].path=t[a].categoryName,t[a].categoryCode==e)return t[a];if(_.isArray(t[a].children)&&t[a].children.length>0){var n=r(e,t[a].children);if(null!=n)return n.path=t[a].categoryName+"/"+n.path,n}}}function l(){null!=a.item.value&&a.item.value.length>0&&d.length>0?(a.model=[],_.forEach(a.item.value.split(","),function(e){var t=r(e,d);null!=t&&a.model.push(angular.copy(t))})):a.model=[]}function c(){var e=a.item.controlData;if(null==e||0==e.length)d=[];else try{d=JSON.parse(e)}catch(e){d=[]}}var d=($(i),[]);a.model=[],a.getErrorInfo=function(t,a){return e.getErrorInfo(t,a)},a.open=function(){t.open({template:n,controller:"mamMetadataTreeSelectorCtrl",windowClass:"mam-metadata-tree-selector",resolve:{params:function(){return{field:a.item}}}}).result.then(function(e){_.isString(e)&&(a.item.value=e,l())})},function(){c(),l()}()}}}])},function(e,t){},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=a(46);a.n(n);angular.module("mam-metadata").directive("mam2MetadataForm",["mam2MetadataService",function(e){return{restrict:"E",template:a(41),replace:!0,transclude:{"mmf-right":"?mmfRight"},scope:{items:"=",type:"@",entity:"<?",twoway:"<?",getFunc:"&",className:"@?",onProgramformChange:"&?"},compile:function(t,a){var n=t.find(".mmf-content").children();return _.forEach(n,function(e){var t=e.tagName.toLowerCase();if(0==t.indexOf("mam2-mfc-")){var a=$(e);a.attr("item","item"),a.attr("type","{{type}}"),a.attr("class",t+" mmf-status-{{type}}")}}),function(t,a,n,i){function m(){var a=t.models;"optional-edit"==t.type&&(a=_.filter(a,{selected:!0})),a=_.filter(a,function(e){return void 0===e.isShow||!0===e.isShow});var n=e.validate(a);return n.newItems=a,n.oldItems=d,n}function o(){_.forEach(t.models,function(e){e.error=null})}function r(){t.models=angular.copy(d)}function l(e){return"clearErros"==e?o():"reset"==e?r():m()}function c(){d=angular.copy(t.items),t.twoway?t.models=t.items:t.models=angular.copy(t.items),_.isFunction(t.getFunc)&&t.getFunc({func:l}),null==t.entity&&(t.entity={}),null==t.entity.type&&(t.entity.type="video"),null==t.entity.frameRate&&(t.entity.frameRate=25)}t.className=t.className||"mam-metadata-form";var d;t.models,t.onSelectChange=function(e,a,n){"programform"==n.fieldName&&t.onProgramformChange({value:e,oldValue:a})},t.changeSelect=function(e){e.selected||(e.error=null)},t.getErrorInfo=function(t,a){return e.getErrorInfo(t,a)},t.$watch("items",function(){c()}),t.getCtrlByType=function(e){var t={1:"datetime",2:"date",3:"",4:"number",5:"text",6:"textarea",7:"bool",8:"select",9:"frame-to-timecode",10:"size",11:"nanosecond-to-timecode",12:"tag",13:"",14:"tree",15:"table",16:"timearea"};return e&&t[e]?t[e]:""}}}}}])},function(e,t,a){var n=(a(53),a(42));angular.module("mam-metadata").service("mam2MetadataService",["$rootScope","$http","$q","$uibModal","mamValidationService",function(e,t,a,i,m){this.openFieldSelector=function(e,t){var m=a.defer();return i.open({template:n,controller:"mamFieldSelectorController",windowClass:"mam-field-selector",backdrop:"static",resolve:{selectedItem:function(){return e||[]},qTreeUrl:function(){return t}}}).result.then(function(e){m.resolve(e)}),m.promise};var o=function(e,t,a){if(e.isMustInput&&(null==a||""===a))return"must";if(!m.dateValidate(a))return"date";if(null!=t.type&&"no"!=t.type){var n=(new Date).format("yyyy-MM-dd"),i=new Date(a).format("yyyy-MM-dd");if("onlypass"==t.type&&Date.parse(i)>Date.parse(n))return"onlypass";if("onlyfuture"==t.type&&Date.parse(i)<Date.parse(n))return"onlyfuture"}},r={1:function(e){var t={type:"no"};return void 0!=e.controlData&&(t=_.isString(e.controlData)?JSON.parse(e.controlData):e.controlData),o(e,t,e.value)},2:function(e){var t={type:"no"};return void 0!=e.controlData&&(t=_.isString(e.controlData)?JSON.parse(e.controlData):e.controlData),o(e,t,e.value)},4:function(e){if(e.isMustInput||null!=e.value&&""!=e.value){if(e.isMustInput&&(null==e.value||""==e.value))return"must";if(isNaN(e.value))return"nubmer";var t=parseFloat(e.value);return 0!==e.minLength&&t<e.minLength?"nubmerMin":0!==e.maxLength&&t>e.maxLength?"nubmerMax":void 0}},5:function(e){if(e.isMustInput||null!=e.value){var t=e.value?e.value.length:0;return e.isMustInput&&0===t?"must":0!==e.minLength&&t<e.minLength?"lengthMin":0!==e.maxLength&&t>e.maxLength?"lengthMax":void 0}},6:function(e){if(e.isMustInput||null!=e.value){var t=e.value?e.value.length:0;return e.isMustInput&&0===t?"must":0!==e.minLength&&t<e.minLength?"lengthMin":0!==e.maxLength&&t>e.maxLength?"lengthMax":void 0}},8:function(e){if(e.isMustInput){var t=e.value;null!=t&&""!=t||(t="[]");try{t=angular.fromJson(t)}catch(e){t=[t]}var a={};try{a=angular.fromJson(e.controlData)}catch(e){console.error(e)}return _.remove(t,function(e){return void 0==a[e]}),0==t.length?"must":void 0}},12:function(e){if(e.isMustInput||null!=e.value){if(e.isMustInput&&(null==e.value||0==e.value.length))return"must";var t=e.value.replace(/,/g,"").length;return 0!==e.minLength&&t<e.minLength?"lengthMin":0!==e.maxLength&&t>e.maxLength?"lengthMax":void 0}},14:function(e){if(e.isMustInput&&(null==e.value||""===e.value||"[]"===e.value||0===e.value.length))return"must"},15:function(e){if(e.isMustInput&&(null==e.value||""===e.value||"[]"===e.value||0===e.value.length))return"must"},16:function(e){if(e.isMustInput||e.value){if(!e.value)return"mustStartAndEndTime";var t=e.value.split(","),a=t[0],n=t[1],i={type:"no"};void 0!=e.controlData&&(i=_.isString(e.controlData)?JSON.parse(e.controlData):e.controlData);var m=[],r=o(e,i,a),l=o(e,i,n);return!r&&!l&&new Date(a)-new Date(n)>0?(m.push("startTimeBiggerThanEnd"),m.push("startTimeBiggerThanEnd")):(m.push(r),m.push(l)),m[0]||m[1]?m:null}}};this.validate=function(e){var t={success:!0,errors:[]};return _.isArray(e)||(e=[e]),_.forEach(e,function(e){var a=r[e.controlType];if(null!=a){var n=a(e);!1===e.isShow||null==n?(e.error=null,e.errors=null):(_.isArray(n)?e.errors=n:e.error=n,t.errors.push(e))}}),t.success=0==t.errors.length,t},this.getErrorInfo=function(e,t){var a=t||e.error;return"must"==a?"该字段为必填！":"mustStartAndEndTime"==a?"请填写开始日期和结束日期":"format"==a?"格式错误":"lengthMin"==a?"长度不能小于"+e.minLength:"lengthMax"==a?"长度不能大于"+e.maxLength:"nubmer"==a?"必须为数字":"nubmerMin"==a?"不能小于"+e.minLength:"nubmerMax"==a?"不能大于"+e.maxLength:"onlypass"==a?"日期不能大于今天":"onlyfuture"==a?"日期不能小于今天":"date"==a?"日期格式不正确":"startTimeBiggerThanEnd"==a?"开始时间不能大于结束时间":0==a.indexOf("custom")?a.split(":")[1]:"未知错误"+a}}])},function(e,t,a){a(48),angular.module("mam-metadata").directive("mamMetadataSelector",["$rootScope","$http","$q",function(e,t,n){return{restrict:"EA",template:a(43),replace:!0,scope:{qTreeUrl:"=",selectedItem:"="},link:function(e,a,n,i){function m(t){_.forEach(t.children,function(t,a){var n=_.findIndex(e.selectedItem,{fieldName:t.fieldName,dataType:t.dataType,fieldPath:t.fieldPath});n>-1&&(t.selected=!1,e.selectedItem.splice(n,1))}),t.expand=!1}function o(t){var a=e.config.dataType[t.dataType][0];return"createUser_"===t.fieldName||"creator"===t.fieldName||"modifier"===t.fieldName||"member"===t.fieldName||"deletor"===t.fieldName||"journallist"===t.fieldName?a=e.config.dataType[t.dataType][4]:"department"!==t.fieldName&&"program_channel_department"!==t.fieldName||(a=e.config.dataType[t.dataType][5]),a}function r(t){var a=[];return _.forEach(t,function(t,n){var i={controlType:o(t),isReadOnly:!1,hiveMaxLength:t.maxLen,hiveMinLength:t.minLen,hiveMustInput:1===t.mustInput,order:n,metadataType:e.config.type,alias:t.alias,showName:t.alias,fixItemId:t.fixItemId,dataType:t.dataType,isEnable:!0,fieldName:t.fieldName};a.push(i)}),JSON.stringify(a)}function l(a,n){var i=e.qTreeUrl.replace(/\{0\}/g,a.refResourceTypeName);t.get(i).then(function(t){var i=t.data;_.forEach(i,function(t,n){a.fieldPath?t.fieldPath=a.fieldPath+","+t.fieldName:t.fieldPath=a.fieldName+","+t.fieldName,a.showNamePath?t.showNamePath=a.showNamePath+","+(t.alias||t.showName):t.showNamePath=(a.alias||a.showName)+","+(t.alias||t.showName);var i=_.findIndex(e.selectedItem,{fieldName:t.fieldName,fieldPath:t.fieldPath});i>-1&&e.selectedItem.splice(i,1)}),n.controlData=r(i),e.selectedItem.push(n)})}function c(e){return _.forEach(e,function(e){e.hasOwnProperty("fieldPath")||(e.fieldPath=""),e.hasOwnProperty("showNamePath")||(e.showNamePath="")}),e}function d(e,t){return t=_.filter(t,{fieldPath:""}),_.forEach(t,function(t){var a=_.findIndex(e,{fieldName:t.fieldName});a>-1&&(e[a].selected=!0)}),e}e.qTreeUrl=e.qTreeUrl||"~/business/metadata/resource/fields?typeName={0}";var s={date:[16],long:[4,9,11,10],string:[5,6,12,14,17,18],boolean:[7],enum:[8],object:[15]};e.config={checkParent:!0,typeName:"model_sobey_object_entity",dataType:s};e.selectItem=function(t){if(t.selected){var a={controlType:o(t),fieldPath:t.fieldPath?t.fieldPath:"",showNamePath:t.showNamePath?t.showNamePath:"",isReadOnly:!1,hiveMaxLength:t.maxLen,hiveMinLength:t.minLen,hiveMustInput:1===t.mustInput,isArray:1===t.isArray,order:e.selectedItem.length,metadataType:e.config.type,alias:t.alias,showName:t.alias,fixItemId:t.fixItemId,dataType:t.dataType,fieldName:t.fieldName};"model_sobey_object_entity"===e.config.typeName&&"tag"!==e.config.type&&(a.isUploadNeed=!1),"object"===t.dataType?(a.refResourceField=t.refResourceTypeName,a.isMultiSelect=1===t.isArray,t.hasOwnProperty("children")&&t.children.length>0?(m(t),a.controlData=r(t.children),e.selectedItem.push(a)):l(t,a)):e.selectedItem.push(a)}else{var n=-1;t.fieldPath!==t.fieldName?n=_.findIndex(e.selectedItem,{fieldName:t.fieldName,dataType:t.dataType,fieldPath:t.fieldPath}):_.forEach(e.selectedItem,function(e,a){if(e.fieldName===t.fieldName&&e.dataType===t.dataType&&e.fieldPath===t.fieldPath)return n=a,!1}),e.selectedItem.splice(n,1)}},e.getChildren=function(e){e.expand?e.expand=!1:e.hasOwnProperty("children")?e.expand=!0:f(e)};var f=function(a){var n="";n=e.qTreeUrl.replace(/\{0\}/g,a.refResourceTypeName),t.get(n).then(function(t){var n=t.data;a.hasOwnProperty("dataType")?(_.forEach(n,function(t){a.fieldPath?t.fieldPath=a.fieldPath+","+t.fieldName:t.fieldPath=a.fieldName+","+t.fieldName,a.showNamePath?t.showNamePath=a.showNamePath+","+(t.alias||t.showName):t.showNamePath=(a.alias||a.showName)+","+(t.alias||t.showName),_.findIndex(e.selectedItem,{fieldName:t.fieldName,fieldPath:t.fieldPath})>-1&&(t.selected=!0)}),a.children=n,a.expand=!0):(n=c(t.data),n=d(n,e.selectedItem),e.folders=n)})},u=function(t){return!!(!e.keyword||t.alias&&t.alias.indexOf(e.keyword)>-1||t.showName&&t.showName.indexOf(e.keyword)>-1)},p=function(e){var t=!1;return _.forEach(e,function(e){t=!1,e.children&&(t=p(e.children)),t||u(e)?(e.show=!0,t=!0):e.show=!1}),t};e.onKeywordChanged=function(){p(e.folders)},function(){_.forEach(e.selectedItem,function(e){e.hasOwnProperty("fieldPath")||(e.fieldPath="",e.showNamePath="")}),f({refResourceTypeName:e.config.typeName})}()}}}])},function(e,t,a){t=e.exports=a(0)(!1),t.push([e.i,".mam-metadata-table-selector{z-index:999}.mam-metadata-table-selector .modal-dialog{width:800px}.mam-metadata-table-selector .modal-body{padding-bottom:3px;height:500px}.mam-metadata-table-selector .mam-metadata-form .mam-mfc-table .mam-metadata-tr .mam-metadata-td{width:200px}.mam-metadata-table-selector .mam-metadata-form .mam-mfc-table .mam-metadata-tr .mam-metadata-td div{width:100%}",""])},function(e,t,a){t=e.exports=a(0)(!1),t.push([e.i,".mam-metadata-tree-selector .modal-body{padding-bottom:3px;height:560px;overflow-y:auto}.mam-metadata-tree-selector ul{list-style:none;margin-left:20px}.mam-metadata-tree-selector .tree-node{display:flex;align-items:center;margin-bottom:12px}.mam-metadata-tree-selector .tree-node .icon-expand{width:22px;text-align:center}.mam-metadata-tree-selector .tree-node .mam-checkbox{margin:0 6px}.mam-metadata-tree-selector .no-children .icon-expand{visibility:hidden}",""])},function(e,t,a){t=e.exports=a(0)(!1),t.push([e.i,'.mam-metadata-form{flex:1}.mam-metadata-form .mmf-group{display:flex;margin-bottom:15px}.mam-metadata-form .mmf-group .mmf-head{min-width:130px}.mam-metadata-form .mmf-group .mmf-head label{padding-top:6px}.mam-metadata-form .mmf-group .mmf-head sup{color:#e30000}.mam-metadata-form .mmf-group .mmf-content{width:1px;flex:1 0 auto;line-height:27px}.mam-metadata-form .mmf-group .mmf-content.disabled{pointer-events:none}.mam-metadata-form .mmf-group .mmf-content .mmf-error{position:relative}.mam-metadata-form .mmf-group .mmf-content .mmf-error-text{position:absolute;background:#e30000;padding:4px 10px;border-radius:5px;color:#fff;white-space:nowrap;margin-top:8px;z-index:8}.mam-metadata-form .mmf-group .mmf-content .mmf-error-text:before{position:absolute;z-index:8;content:" ";top:-6px;width:15px;height:8px;border-left:8px solid transparent;border-right:8px solid transparent;border-bottom:8px solid #e30000}.mam-metadata-form .mam2-mfc-text.mmf-status-browse{word-break:break-all}.mam-metadata-form .mam-mfc-select.mmf-status-browse div{display:flex;flex-wrap:wrap}.mam-metadata-form .mam-mfc-select.mmf-status-browse span{background:#337ab7;color:#fff;margin:5px;padding:4px;border-radius:4px}.mam-metadata-form .mam2-mfc-tag.mmf-status-browse .browse-box,.mam-metadata-form .mam-mfc-tag.mmf-status-browse .browse-box{display:flex;flex-wrap:wrap}.mam-metadata-form .mam2-mfc-tag.mmf-status-browse span,.mam-metadata-form .mam-mfc-tag.mmf-status-browse span{background:#337ab7;color:#fff;margin:0 5px 5px 0;padding:4px;border-radius:4px}.mam-metadata-form .mam-mfc-tree{display:flex}.mam-metadata-form .mam-mfc-tree .items{flex:1 0 auto;padding:6px 12px;font-size:14px;color:#555;background-color:#fff;background-image:none;border:1px solid #ccc;border-radius:4px;box-shadow:inset 0 1px 1px rgba(0,0,0,.075)}.mam-metadata-form .mam-mfc-tree .operate{min-width:60px;margin-left:10px}.mam-metadata-form .mam-mfc-timearea .time-area{display:flex}.mam-metadata-form .mam-mfc-timearea .time-area .end-time,.mam-metadata-form .mam-mfc-timearea .time-area .start-time{flex:1}.mam-metadata-form .mam-mfc-timearea .time-area-browse{display:flex}.mam-metadata-form .mam-mfc-timearea .time-area-browse .time-divide{width:30px}.mam-metadata-form .mam-mfc-timearea .time-divide{width:80px;display:flex;align-items:center;justify-content:center}.mam-metadata-form .mam-mfc-table{flex:1}.mam-metadata-form .mam-mfc-table .mam-matadata-op{padding:6px 0}.mam-metadata-form .mam-mfc-table .mam-matadata-op .isactive{color:#fff;background:#337ab7}.mam-metadata-form .mam-mfc-table .mam-matadata-op .confirm-tip{display:inline-block;margin-left:10px;vertical-align:bottom;line-height:16px}.mam-metadata-form .mam-mfc-table .mam-matadata-op .confirm-tip .label{padding:.1em .3em}.mam-metadata-form .mam-mfc-table .mam-matadata-op .confirm-tip .label-danger{cursor:pointer}.mam-metadata-form .mam-mfc-table .mam-matadata-op .confirm-tip .label-default{background-color:#fff;color:#000;border:1px solid #ccc}.mam-metadata-form .mam-mfc-table .mam-metadata-content{overflow-x:auto}.mam-metadata-form .mam-mfc-table .mam-metadata-content .mam-metadata-table{width:auto}.mam-metadata-form .mam-mfc-table .mam-metadata-content .mam-metadata-table .flex-item.select-item{background:#337ab7}.mam-metadata-form .mam-mfc-table .mam-metadata-content .mam-metadata-table .flex-item.select-item .mam-metadata-table-item{color:#fff}.mam-metadata-form .mam-mfc-table .mam-metadata-content .mam-metadata-table .mam-metadata-table-item{width:200px;display:flex;align-items:center;justify-content:center}.mam-metadata-form .mam-mfc-table .mam-metadata-content .mam-metadata-table .mam-metadata-table-item .mam-metadata-table-item-com{width:100%;padding:0 5px}.mam-metadata-form .mam-mfc-table .mam-metadata-content .mam-metadata-table .mam-metadata-table-operate{width:20px;display:flex;align-items:center;justify-content:center;border:none}.mam-metadata-form .mam-mfc-table .mam-metadata-content .mam-metadata-table .mam-metadata-table-operate i{font-size:12px;cursor:pointer;color:gray;padding:2px 2px 2px 10px}.mam-metadata-table-selector .modal-dialog .modal-content{width:1000px;height:800px}.mam-metadata-table-selector .modal-dialog .modal-content .modal-body{height:682px}',""])},function(e,t,a){t=e.exports=a(0)(!1),t.push([e.i,".mam-field-selector .modal-content{width:600px!important;height:auto!important}.mam-field-selector .modal-content .modal-body{height:500px;overflow-y:auto}",""])},function(e,t,a){t=e.exports=a(0)(!1),t.push([e.i,".mam-search-label{height:34px;line-height:34px}.mam-search-input,.mam-search-label{margin-bottom:10px}",""])},function(e,t){e.exports="<div> <label class=mam-checkbox> <input type=checkbox mam-checkbox ng-model=item.value ng-disabled=\"item.isReadOnly || type=='browse'\"> </label> </div>"},function(e,t){e.exports="<div> <div ng-if=\"type=='browse'\">{{item.value}}</div> <input ng-if=\"type!='browse'\" type=text class=form-control ng-model=item.value ng-readonly=item.isReadOnly /> </div>"},function(e,t){e.exports="<div> <div ng-if=\"type=='browse'\">{{item.value}}</div> <input ng-if=\"type!='browse'\" type=text class=form-control ng-model=item.value ng-readonly=item.isReadOnly /> </div>"},function(e,t){e.exports="<div> <div ng-if=\"type=='browse'\">{{model}}</div> <input ng-if=\"type!='browse'\" type=text class=form-control value={{model}} ng-readonly=item.isReadOnly /> </div>"},function(e,t){e.exports="<div> <div ng-if=\"type=='browse'\">{{model}}</div> <input ng-if=\"type!='browse'\" type=text class=form-control value={{model}} ng-readonly=item.isReadOnly /> </div>"},function(e,t){e.exports="<div> <div ng-if=\"type=='browse'\">{{item.value}}</div> <input ng-if=\"type!='browse'\" type=text class=form-control ng-model=item.value ng-readonly=item.isReadOnly> </div>"},function(e,t){e.exports='<div> <div ng-if="type==\'browse\'"> <div ng-if=item.isMultiSelect> <span ng-repeat="n in model">{{n.value}}</span> </div> <div ng-if=!item.isMultiSelect> <span ng-if="model!=null && model.value!=null">{{model.value}}</span> </div> </div> <div ng-if="type!=\'browse\' && !item.isMultiSelect"> <ui-select ng-model=model theme=bootstrap sortable=true ng-disabled=item.isReadOnly on-select="onSelect($item, $select.selected)" on-remove="onRemove($item, $select.selected)" append-to-body=true> <ui-select-match placeholder="">{{$select.selected.value}}</ui-select-match> <ui-select-choices repeat="(key,value) in items | filter:$select.search"> {{value.value}} </ui-select-choices> </ui-select> </div> <div ng-if="type!=\'browse\' && item.isMultiSelect"> <ui-select ng-model=model multiple=multiple theme=bootstrap sortable=true ng-disabled=item.isReadOnly on-select="onSelect($item, $select.selected)" on-remove="onRemove($item, $select.selected)" append-to-body=true> <ui-select-match placeholder="">{{$item.value}}</ui-select-match> <ui-select-choices repeat="(key,value) in items | filter:$select.search"> {{value.value}} </ui-select-choices> </ui-select> </div> </div>'},function(e,t){e.exports='<div> <div ng-if="type==\'browse\'">{{ item.value | formatSize }}</div> <input ng-if="type!=\'browse\'" type=text class=form-control value="{{item.value | formatSize}}" ng-readonly=item.isReadOnly> </div>'},function(e,t){e.exports='<div class=modal-header> <button type=button class=close ng-click=close()> <i class="fa fa-times"></i> </button> <h4 class=modal-title ng-hide=!title>{{title}}</h4> </div> <div class="modal-body object-con"> <div class=mam-metadata-form> <div class=mam-mfc-table> <div class=mam-metadata-content> <div class="mam-flex-table mam-metadata-table"> <div class=flex-head> <div class=mam-metadata-table-item ng-repeat="item in field"> <div>{{ !item.alias ? item.fieldName : item.alias}}</div> </div> </div> <div class=flex-body> <div class=flex-item ng-if="fieldData.length>0" ng-repeat="fd in fieldData track by $index"> <div class=mam-metadata-table-item ng-repeat="item in fd" ng-switch=item.controlType> <div class=mam-metadata-table-item-com ng-switch-when=1> <mam2-mfc-datetime item=item type=edit></mam2-mfc-datetime> </div> <div class=mam-metadata-table-item-com ng-switch-when=2> <mam2-mfc-date item=item type=edit></mam2-mfc-date> </div> <div class=mam-metadata-table-item-com ng-switch-when=4> <mam2-mfc-number item=item type=edit></mam2-mfc-number> </div> <div class=mam-metadata-table-item-com ng-switch-when=5> <mam2-mfc-text item=item type=edit></mam2-mfc-text> </div> <div class=mam-metadata-table-item-com ng-switch-when=6> <mam2-mfc-textarea item=item type=edit></mam2-mfc-textarea> </div> <div class=mam-metadata-table-item-com ng-switch-when=7> <mam2-mfc-bool item=item type=edit></mam2-mfc-bool> </div> <div class=mam-metadata-table-item-com ng-switch-when=8> <mam2-mfc-select item=item type=edit></mam2-mfc-select> </div> <div class=mam-metadata-table-item-com ng-switch-when=9> <mam2-mfc-frame-to-timecode item=item type=edit entity=entity></mam2-mfc-frame-to-timecode> </div> <div class=mam-metadata-table-item-com ng-switch-when=10> <mam2-mfc-size item=item type=edit></mam2-mfc-size> </div> <div class=mam-metadata-table-item-com ng-switch-when=11> <mam2-mfc-nanosecond-to-timecode item=item type=edit entity=entity></mam2-mfc-nanosecond-to-timecode> </div> <div class=mam-metadata-table-item-com ng-switch-when=12> <mam2-mfc-tag item=item type=edit></mam2-mfc-tag> </div> <div class=mam-metadata-table-item-com ng-switch-when=14> <mam2-mfc-tree item=item type=edit></mam2-mfc-tree> </div> <div class=mam-metadata-table-item-com ng-switch-when=16> <mam2-mfc-timearea item=item type=edit></mam2-mfc-timearea> </div> </div> </div> </div> </div> </div> </div> </div> </div> <div class=modal-footer> <button class="btn btn-primary" ng-click=ok()>确定</button> <button class="btn btn-default" ng-click=close()>取消</button> </div>'},function(e,t){e.exports='<div class=mam-mfc-table> <div class=mam-metadata-content> <div class="mam-flex-table mam-metadata-table" ng-if="type == \'browse\'"> <div class=flex-head> <div class=mam-metadata-table-item ng-repeat="item in configData" ng-if="item.isShow === undefined || item.isShow === true"> <div>{{ !item.alias ? item.fieldName : item.alias}}</div> </div> </div> <div class=flex-body> <div class=flex-item ng-repeat="modelItem in model track by $index" mam-resize-table> <div class=mam-metadata-table-item ng-repeat="control in configDataJson" ng-switch=control.controlType ng-if="control.isShow === undefined || control.isShow === true"> <span ng-switch-when=8 title={{getName(modelItem[control.fieldName],control.controlData)}}>{{getName(modelItem[control.fieldName],control.controlData)}}</span> <span ng-switch-when=14 title={{getPath(modelItem[control.fieldName],control.controlData)}}>{{getPath(modelItem[control.fieldName],control.controlData)}}</span> <span ng-switch-when=7 title={{getBool(modelItem[control.fieldName])}}>{{getBool(modelItem[control.fieldName])}}</span> <span ng-switch-default title={{modelItem[control.fieldName]}}>{{modelItem[control.fieldName]}}</span> </div> </div> <div class=flex-item ng-repeat="extraRow in extraRows track by $index" mam-resize-table> <div class=mam-metadata-table-item ng-repeat="control in configDataJson" ng-switch=control.controlType ng-if="control.isShow === undefined || control.isShow === true"> </div> </div> </div> </div> <div class="mam-flex-table mam-metadata-table" ng-if="type == \'edit\' && !item.isReadOnly"> <div class=flex-head> <div class=mam-metadata-table-operate></div> <div class=mam-metadata-table-item ng-repeat="item in field"> <div>{{ !item.alias ? item.fieldName : item.alias}}</div> </div> </div> <div class=flex-body> <div class=flex-item ng-if="fieldData.length>0" ng-repeat="fd in fieldData track by $index" mam-resize-table> <div class=mam-metadata-table-operate> <i class="fa fa-times" ng-show="fieldData.length-1 !== $index" ng-click=reduce(fd,$index) title=删除></i> </div> <div class=mam-metadata-table-item ng-repeat="item in fd" ng-switch=item.controlType> <div class=mam-metadata-table-item-com ng-switch-when=1> <mam2-mfc-datetime item=item type=edit></mam2-mfc-datetime> </div> <div class=mam-metadata-table-item-com ng-switch-when=2> <mam2-mfc-date item=item type=edit></mam2-mfc-date> </div> <div class=mam-metadata-table-item-com ng-switch-when=4> <mam2-mfc-number item=item type=edit></mam2-mfc-number> </div> <div class=mam-metadata-table-item-com ng-switch-when=5> <mam2-mfc-text item=item type=edit></mam2-mfc-text> </div> <div class=mam-metadata-table-item-com ng-switch-when=6> <mam2-mfc-textarea item=item type=edit></mam2-mfc-textarea> </div> <div class=mam-metadata-table-item-com ng-switch-when=7> <mam2-mfc-bool item=item type=edit></mam2-mfc-bool> </div> <div class=mam-metadata-table-item-com ng-switch-when=8> <mam2-mfc-select item=item type=edit></mam2-mfc-select> </div> <div class=mam-metadata-table-item-com ng-switch-when=9> <mam2-mfc-frame-to-timecode item=item type=edit entity=entity></mam2-mfc-frame-to-timecode> </div> <div class=mam-metadata-table-item-com ng-switch-when=10> <mam2-mfc-size item=item type=edit></mam2-mfc-size> </div> <div class=mam-metadata-table-item-com ng-switch-when=11> <mam2-mfc-nanosecond-to-timecode item=item type=edit entity=entity></mam2-mfc-nanosecond-to-timecode> </div> <div class=mam-metadata-table-item-com ng-switch-when=12> <mam2-mfc-tag item=item type=edit></mam2-mfc-tag> </div> <div class=mam-metadata-table-item-com ng-switch-when=14> <mam2-mfc-tree item=item type=edit></mam2-mfc-tree> </div> <div class=mam-metadata-table-item-com ng-switch-when=16> <mam2-mfc-timearea item=item type=edit></mam2-mfc-timearea> </div> </div> </div> <div class=flex-item ng-repeat="editExtraRow in editExtraRows track by $index"> <div class=mam-metadata-table-operate> </div> <div class=mam-metadata-table-item ng-repeat="control in configDataJson"> </div> </div> </div> </div> </div> </div>'},function(e,t){e.exports='<div> <div class=browse-box ng-if="type==\'browse\'"> <span ng-if="item.value!=null && item.value.length>0" ng-repeat="i in item.value.split(\',\')">{{i}}</span> </div> <tags-input ng-if="type!=\'browse\'" ng-model=tags min-length=0 ng-disabled=item.isReadOnly placeholder=添加标签 on-tag-adding=adding($tag) on-tag-added=added($tag) on-tag-removed=remove($tag) class=tags-input on-invalid-tag=invalid($tag)></tags-input> </div>'},function(e,t){e.exports="<div> <div ng-if=\"type=='browse'\">{{item.value}}</div> <input ng-if=\"type!='browse'\" type=text class=form-control ng-model=item.value ng-readonly=item.isReadOnly /> </div>"},function(e,t){e.exports="<div> <div ng-if=\"type=='browse'\" ng-bind-html=trustAsHtml(item.value)></div> <textarea ng-if=\"type!='browse'\" class=form-control ng-model=item.value ng-readonly=item.isReadOnly></textarea> </div>"},function(e,t){e.exports='<div class=mam-mfc-timearea> <div ng-if="type==\'browse\'" class=time-area-browse>{{model.startModel}}<span class=time-divide>-</span>{{model.endModel}}</div> <div ng-if="type!=\'browse\'" class=time-area> <div class=start-time> <input id="" type=text class="start-time form-control" ng-model=model.startModel placeholder=开始时间 ng-readonly=item.isReadOnly /> <div class=mmf-error ng-if="(type==\'edit\' || (type==\'optional-edit\' && item.selected)) && item.errors[0]!==undefined"> <div class=mmf-error-text>{{ getErrorInfo(item, item.errors[0]) }}</div> </div> </div> <span class=time-divide>-</span> <div class=end-time> <input type=text class="end-time form-control" ng-model=model.endModel placeholder=结束时间 ng-readonly=item.isReadOnly /> <div class=mmf-error ng-if="(type==\'edit\' || (type==\'optional-edit\' && item.selected)) && item.errors[1]!==undefined"> <div class=mmf-error-text>{{ getErrorInfo(item, item.errors[1]) }}</div> </div> </div> </div> </div>'},function(e,t){e.exports='<div class=modal-header> <button type=button class=close ng-click=close()><i class="fa fa-times"></i></button> <h4 class=modal-title>分类选择</h4> </div> <div class=modal-body> <script type=text/ng-template id=mam-metadata-tree-selector-items> <div class="tree-node" ng-class="{\'no-children\':item.children==null||item.children.length==0}">\r\n            <i class="icon-expand fa" ng-click="item.expand=!item.expand" ng-class="item.expand?\'fa-minus-square\':\'fa-plus-square\'"></i>\r\n            <label class="mam-checkbox">\r\n                <input type="checkbox" ng-model="item.selected" mam-checkbox ng-click="selectItem(item)" />\r\n                <span>{{item.categoryName}}</span>\r\n            </label>\r\n        </div>\r\n\r\n        <ul ng-if="item.children && item.expand">\r\n            <li ng-repeat="item in item.children" ng-include="\'mam-metadata-tree-selector-items\'"></li>\r\n        </ul> <\/script> <ul class=mam-category-tree> <li ng-repeat="item in tree.children" ng-include="\'mam-metadata-tree-selector-items\'"></li> </ul> </div> <div class=modal-footer> <button class="btn btn-primary" ng-click=ok()>确定</button> <button class="btn btn-default" ng-click=close()>取消</button> </div>'},function(e,t){e.exports='<div> <div class=items> <div class=item ng-repeat="item in model">{{item.path}}</div> </div> <div class=operate> <button class="btn btn-default" ng-click=open() ng-if="type==\'edit\'">选择</button> </div> </div>'},function(e,t){e.exports='<div class="{{ className }}"> <div class="mmf-group mmf-group-{{item.fieldName}} mmf-control-{{getCtrlByType(item.controlType)}}" ng-repeat="item in models" ng-if="item.isShow === undefined || item.isShow"> <div class=mmf-head> <label> {{item.alias}} <sup ng-if="type!=\'browse\' && item.isMustInput">*</sup> </label> <label class=mam-checkbox ng-show="type==\'optional-edit\' && !item.isReadOnly" ng-click=changeSelect(item)> <input type=checkbox ng-model=item.selected mam-checkbox/> </label> </div> <div class=mmf-content ng-switch=item.controlType ng-class="{\'disabled\':type==\'optional-edit\' && !item.selected}"> <mam2-mfc-datetime ng-switch-when=1></mam2-mfc-datetime> <mam2-mfc-date ng-switch-when=2></mam2-mfc-date> <mam2-mfc-number ng-switch-when=4></mam2-mfc-number> <mam2-mfc-text ng-switch-when=5></mam2-mfc-text> <mam2-mfc-textarea ng-switch-when=6></mam2-mfc-textarea> <mam2-mfc-bool ng-switch-when=7></mam2-mfc-bool> <mam2-mfc-select ng-switch-when=8 on-change=onSelectChange(value,oldValue,item)></mam2-mfc-select> <mam2-mfc-frame-to-timecode ng-switch-when=9 entity=entity></mam2-mfc-frame-to-timecode> <mam2-mfc-size ng-switch-when=10></mam2-mfc-size> <mam2-mfc-nanosecond-to-timecode ng-switch-when=11 entity=entity></mam2-mfc-nanosecond-to-timecode> <mam2-mfc-tag ng-switch-when=12></mam2-mfc-tag> <mam2-mfc-tree ng-switch-when=14></mam2-mfc-tree> <mam2-mfc-table ng-switch-when=15></mam2-mfc-table> <mam2-mfc-timearea ng-switch-when=16></mam2-mfc-timearea> <div ng-switch-default> <mam2-mfc-text></mam2-mfc-text> </div> <div class=mmf-error ng-if="(type==\'edit\' || (type==\'optional-edit\' && item.selected)) && item.error!=null"> <div class=mmf-error-text>{{ getErrorInfo(item) }}</div> </div> </div> <div class=mmf-right ng-transclude=mmf-right item=item></div> </div> </div>'},function(e,t){e.exports='<div class=mam-field-selector-modal> <div class=modal-header> <button type=button class=close ng-click=close()><i class="fa fa-times"></i></button> <h4 class=modal-title ng-bind=title></h4> </div> <div class=modal-body> <mam-metadata-selector selected-item=selectedItem q-tree-url=qTreeUrl></mam-metadata-selector> </div> <div class=modal-footer> <button class="btn btn-primary" ng-click=ok()>确定</button> <button class="btn btn-default" ng-click=close()>取消</button> </div> </div>'},function(e,t){e.exports='<div> <div class=form-group> <div class="col-lg-2 mam-search-label"> <label>搜索：</label> </div> <div class="col-lg-10 mam-search-input"> <input class=form-control type=text ng-model=keyword ng-change=onKeywordChanged() /> </div> </div> <script type=text/ng-template id=tree> <div ng-if="!item.editMode" class="nav" ng-class="item.selected?\'checked\':\'unchecked\'" title="{{item.description}}">\r\n            <label class="mam-checkbox" ng-if="item.dataType!=\'object\'">\r\n                <input type="checkbox" ng-model="item.selected" mam-checkbox ng-click="selectItem(item)" />\r\n            </label>\r\n            <label class="mam-checkbox" ng-if="config.checkParent == \'true\' && item.dataType==\'object\'">\r\n                <input type="checkbox" ng-model="item.selected" mam-checkbox ng-click="selectItem(item)" />\r\n            </label>\r\n\r\n            <i class="fa " ng-click="getChildren(item)" ng-if="item.dataType==\'object\'&& !item.selected" ng-class="item.expand?\'fa-minus-square\':\'fa-plus-square\'"></i>\r\n            <a ng-click="setModel(item)">{{item.alias}}</a>\r\n        </div>\r\n\r\n        <ul ng-if="item.children" ng-show="item.expand">\r\n            <li ng-repeat="item in item.children" ng-include="\'tree\'" ng-if="item.show===undefined || item.show"></li>\r\n        </ul> <\/script> <ul class=mam-tree> <li ng-repeat="item in folders" ng-include="\'tree\'" ng-if="item.show===undefined || item.show"></li> </ul> </div>'},function(e,t,a){var n=a(20);"string"==typeof n&&(n=[[e.i,n,""]]);var i={};i.transform=void 0;a(1)(n,i);n.locals&&(e.exports=n.locals)},function(e,t,a){var n=a(21);"string"==typeof n&&(n=[[e.i,n,""]]);var i={};i.transform=void 0;a(1)(n,i);n.locals&&(e.exports=n.locals)},function(e,t,a){var n=a(22);"string"==typeof n&&(n=[[e.i,n,""]]);var i={};i.transform=void 0;a(1)(n,i);n.locals&&(e.exports=n.locals)},function(e,t,a){var n=a(23);"string"==typeof n&&(n=[[e.i,n,""]]);var i={};i.transform=void 0;a(1)(n,i);n.locals&&(e.exports=n.locals)},function(e,t,a){var n=a(24);"string"==typeof n&&(n=[[e.i,n,""]]);var i={};i.transform=void 0;a(1)(n,i);n.locals&&(e.exports=n.locals)},function(e,t){e.exports=function(e){var t="undefined"!=typeof window&&window.location;if(!t)throw new Error("fixUrls requires window.location");if(!e||"string"!=typeof e)return e;var a=t.protocol+"//"+t.host,n=a+t.pathname.replace(/\/[^\/]*$/,"/");return e.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,function(e,t){var i=t.trim().replace(/^"(.*)"$/,function(e,t){return t}).replace(/^'(.*)'$/,function(e,t){return t});if(/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/)/i.test(i))return e;var m;return m=0===i.indexOf("//")?i:0===i.indexOf("/")?a+i:n+i.replace(/^\.\//,""),"url("+JSON.stringify(m)+")"})}},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=a(44),i=(a.n(n),function(e,t,a,n,i,m,o,r,l){t.field=l.field,t.data=l.data;var c=200;t.addBlankRow=function(){var e=[];t.lastIndex++,_.forEach(t.field,function(a){var n=angular.copy(a);n.value="",n.index=t.lastIndex,Object.defineProperty(n,"value",{get:function(){return this._value},set:function(e){this._value=e,e&&n.index===t.lastIndex&&t.addBlankRow()}}),e.push(n)}),t.fieldData.push(e)};var d=function(e){var t=!0;return _.forEach(e,function(e){e.value&&(t=!1)}),t};t.ok=function(){var e=[];_.forEach(t.fieldData,function(t){if(!d(t)){var a={};_.forEach(t,function(e){a[e.fieldName]=e.value}),e.push(a)}}),m.close(e)},t.close=function(){m.close(!1)},function(){t.fieldData=_.map(t.data,function(e){return _.map(t.field,function(t){var a=angular.copy(t);return a.value=e[t.fieldName],a})}),t.lastIndex=t.fieldData.length-1,t.addBlankRow(),t.field&&n(function(){$(".mam-metadata-table").width(t.field.length*c)})}()});i.$inject=["$rootScope","$scope","$q","$timeout","$parse","$uibModalInstance","$state","$http","params"],angular.module("mam-metadata").controller("mamMetadataTableSelectorCtrl",i)},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=a(45),i=(a.n(n),function(e,t,a,n,i,m,o,r,l){function c(e){_.forEach(e,function(e){p[e.categoryCode]=e,null!=e.children&&e.children.length>0&&c(e.children)})}function d(e,t){if(e&&null!=p[e]){if(!t&&_.some(p[e].children,"selected"))return;p[e].selected=t,t&&!p[e].expand&&(p[e].expand=!0),d(p[e].categoryParent,t)}}function s(e,t){_.forEach(e,function(e){e.selected=t,null!=e.children&&e.children.length>0&&s(e.children,t)})}var f=l.field,u=[];t.tree={};var p={};t.selectItem=function(e){e.selected?f.isMultiSelect||(_.forEach(u,function(e){s(p[e].children,!1),d(e,!1)}),u=[e.categoryCode]):s(e.children,e.selected),d(e.categoryParent,e.selected)},function(){function e(){if(void 0!=f.value&&f.value.length>0){var e=f.value.split(",");_.forEach(e,function(e){!f.isMultiSelect&&u.length>0||void 0!=p[e]&&(p[e].selected=!0,t.selectItem(p[e]))})}}var a=f.controlData;null==a||0==a.length?r.get("~/business/tree/category/"+f.refResourceField).then(function(a){t.tree={children:a.data},c(t.tree.children),e()}):(a=JSON.parse(a),t.tree={children:a},c(t.tree.children),e())}(),t.ok=function(){if(0===u.length){var e;for(var t in p)p[t].selected&&(e=!1,_.forEach(u,function(a,n){p[t].categoryCode.indexOf(a)>-1&&(u[n]=p[t].categoryCode,e=!0)}),e||u.push(p[t].categoryCode))}m.close(u.join())},t.close=function(){m.close(!1)}});i.$inject=["$rootScope","$scope","$q","$timeout","$parse","$uibModalInstance","$state","$http","params"],angular.module("mam-metadata").controller("mamMetadataTreeSelectorCtrl",i)},function(e,t,a){window.mam||(window.mam={}),window.mam.metadata={},angular.module("mam-metadata",["mam-ng","ui.bootstrap"]),a(2),a(3),a(4),a(14),a(5),a(6),a(7),a(8),a(9),a(11),a(12),a(13),a(15),a(10),a(16),a(18),a(17),a(19)},function(e,t,a){a(47);var n=function(e,t,a,n,i,m,o,r,l,c){t.title="选择字段",t.selectedItem=l,t.qTreeUrl=c,t.ok=function(){m.close(t.selectedItem)},t.close=function(){m.close()}};n.$inject=["$rootScope","$scope","$q","$timeout","$parse","$uibModalInstance","$state","$http","selectedItem","qTreeUrl"],angular.module("mam-metadata").controller("mamFieldSelectorController",n)}]);
//# sourceMappingURL=mam-metadata.min.js.map