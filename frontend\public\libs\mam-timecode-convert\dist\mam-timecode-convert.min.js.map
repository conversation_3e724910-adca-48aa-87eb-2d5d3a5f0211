{"version": 3, "sources": ["webpack:///mam-timecode-convert.min.js", "webpack:///webpack/bootstrap 768fc95fb880478ca43a", "webpack:///./src/index.js"], "names": ["modules", "__webpack_require__", "moduleId", "installedModules", "exports", "module", "i", "l", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "configurable", "enumerable", "get", "n", "__esModule", "object", "property", "prototype", "hasOwnProperty", "p", "s", "Array", "S", "String", "fromCharCode", "contains", "e", "RegExp", "this", "test", "join", "replaceAll", "s1", "s2", "replace", "TimeCodeConvert", "timeCodeConvertHelper", "MpcVideoStandard", "mpcVideostandardUnknow", "mpcVideostandardPal", "mpcVideostandardNtsc2997", "mpcVideostandardNtsc30", "mpcVideostandardSecam", "mpcVideostandard1920108050I", "mpcVideostandard192010805994I", "mpcVideostandard1920108060I", "mpcVideostandard192010802398P", "mpcVideostandard1920108024P", "mpcVideostandard1920108025P", "mpcVideostandard192010802997P", "mpcVideostandard1920108030P", "mpcVideostandard12807202398P", "mpcVideostandard128072024P", "mpcVideostandard128072050P", "mpcVideostandard12807205994P", "mpcVideostandard1440108050I", "mpcVideostandard144010805994I", "mpcVideostandard1440108060I", "m_mpcStRate25", "MpcStFrameRate25", "MpcStScale25", "m_mpcStRate2997", "MpcStFrameRate2997", "MpcStScale2997", "m_mpcStRate30", "MpcStFrameRate30", "MpcStScale30", "m_mpcStRate24", "MpcStFrameRate24", "MpcStScale24", "m_mpcStRate2398", "MpcStFrameRate2398", "MpcStScale2398", "m_mpcStRate50", "MpcStFrameRate50", "MpcStScale50", "m_mpcStRate5994", "MpcStFrameRate5994", "MpcStScale5994", "m_mpcStRate60", "MpcStFrameRate60", "MpcStScale60", "MpcFramesSecond25", "MpcFramesMinute25", "MpcFramesHour25", "MpcFramesMinute24Drop", "MpcFrames10Minutes24Drop", "MpcFramesHour24Drop", "MpcFramesSecond24", "MpcFramesMinute24", "MpcFramesHour24", "MpcFramesSecondNodrop30", "MpcFramesMinuteNodrop30", "MpcFramesHourNodrop30", "MpcFramesMinute30Drop", "MpcFrames10Minutes30Drop", "MpcFramesHour30Drop", "MpcFramesSecond50", "MpcFramesMinute50", "MpcFramesHour50", "MpcFramesMinute60Drop", "MpcFrames10Minutes60Drop", "MpcFramesHour60Drop", "MpcFramesSecond60", "MpcFramesMinute60", "MpcFramesHour60", "frame2L100Ns$1", "lFrame", "dRate", "dScale", "dFrameRate", "v", "dFrameScale", "rate2ScaleFrameRateAndFrameScale", "parseInt", "Math", "floor", "pow", "frame2L100Ns", "dFrame", "frameRate2RateAndScale", "frame2Second$1", "frame2Second", "frame2Tc$1", "dropFrame", "getRateDropFrame", "strTc", "dHour", "dResidue", "dMin", "dSec", "dFra", "formatTimeCodeString", "dHour1", "dResidue1", "dMin1", "div", "dSec1", "dFra1", "dHour11", "dResidue11", "dMin11", "dSec11", "dFra11", "frame2Tc", "timeCode2Frame$1", "sTimeCode", "frameRate", "ftcFrames", "timeCode2NdfFrame", "lHour", "lMinute", "lSecond", "timeCode2Format", "lwReste", "lHour1", "lMinute1", "lSecond1", "lFrame1", "lwReste1", "lHour11", "lMinute11", "lSecond11", "lFrame11", "lwReste11", "timeCode2Frame", "formatTimeCodeString$1", "newTc", "getFrameByTimeCode", "abs", "l100Ns2Frame$1", "l100Ns", "l100Ns2Frame", "l100Ns2FrameNoCorrecte", "l100Ns2Second", "l100Ns2Tc", "llResidue", "dFraction", "l100Ns2Tc$1", "second2Frame$1", "dbSec", "toFixed", "second2Frame", "second2FrameNoCorrecte", "hours", "minutes", "seconds", "frames", "wrap", "framesSeparator", "timeCode", "ftcs", "split", "length", "showframeRate", "ceil", "toString", "dropM", "drop5994F", "dropF", "ftcssf", "showframeRate1", "dropM1", "drop5994F1", "dropF1", "isAdded", "corr<PERSON><PERSON><PERSON>", "ftcNewFrames", "rate", "ftcSeconds", "ftcCodes", "SecondToTimeString_audio", "opts", "iseconds", "i10Milliseconds", "hoursStr", "minutesStr", "isecondsStr", "i10MillisecondsStr", "timeCode2L100Ns_audio", "I10Milliseconds", "undefined", "l100ns", "str", "a", "b", "hasValue", "obj", "second2L100Ns", "window", "timecodeconvert"], "mappings": "CAAS,SAAUA,GCIjB,QAASC,GAAoBC,GAG5B,GAAGC,EAAiBD,GACnB,MAAOC,GAAiBD,GAAUE,OAGnC,IAAIC,GAASF,EAAiBD,IAC7BI,EAAGJ,EACHK,GAAG,EACHH,WAUD,OANAJ,GAAQE,GAAUM,KAAKH,EAAOD,QAASC,EAAQA,EAAOD,QAASH,GAG/DI,EAAOE,GAAI,EAGJF,EAAOD,QAvBf,GAAID,KA4BJF,GAAoBQ,EAAIT,EAGxBC,EAAoBS,EAAIP,EAGxBF,EAAoBU,EAAI,SAASP,EAASQ,EAAMC,GAC3CZ,EAAoBa,EAAEV,EAASQ,IAClCG,OAAOC,eAAeZ,EAASQ,GAC9BK,cAAc,EACdC,YAAY,EACZC,IAAKN,KAMRZ,EAAoBmB,EAAI,SAASf,GAChC,GAAIQ,GAASR,GAAUA,EAAOgB,WAC7B,WAAwB,MAAOhB,GAAgB,SAC/C,WAA8B,MAAOA,GAEtC,OADAJ,GAAoBU,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRZ,EAAoBa,EAAI,SAASQ,EAAQC,GAAY,MAAOR,QAAOS,UAAUC,eAAejB,KAAKc,EAAQC,IAGzGtB,EAAoByB,EAAI,GAGjBzB,EAAoBA,EAAoB0B,EAAI,KDM/C,SAAUtB,EAAQD;;;;;;;AEvDxBwB,MAAMJ,UAAUK,EAAIC,OAAOC,aAAa,GACxCH,MAAMJ,UAAUQ,SAAW,SAAUC,GAEjC,MADQ,IAAIC,QAAOC,KAAKN,EAAII,EAAIE,KAAKN,GAC3BO,KAAKD,KAAKN,EAAIM,KAAKE,KAAKF,KAAKN,GAAKM,KAAKN,IAMrDC,OAAON,UAAUc,WAAa,SAAUC,EAAIC,GACxC,MAAOL,MAAKM,QAAQ,GAAIP,QAAOK,EAAI,MAAOC,GAG9C,IAAIE,GAAkB,WAQlB,GAAIC,GAAwB,YAo9C5B,OAj9CAA,GAAsBnB,WAMlBoB,kBACIC,uBAAwB,EACxBC,oBAAqB,EACrBC,yBAA0B,EAC1BC,uBAAwB,EACxBC,sBAAuB,EACvBC,4BAA6B,GAC7BC,8BAA+B,GAC/BC,4BAA6B,GAC7BC,8BAA+B,IAC/BC,4BAA6B,IAC7BC,4BAA6B,IAC7BC,8BAA+B,KAC/BC,4BAA6B,KAC7BC,6BAA8B,KAC9BC,2BAA4B,KAC5BC,2BAA4B,MAC5BC,6BAA8B,MAC9BC,4BAA6B,MAC7BC,8BAA+B,OAC/BC,4BAA6B,QAYjCC,cAAe,GAWfC,iBAAkB,GAWlBC,aAAc,EAUdC,gBAAiB,IAWjBC,mBAAoB,IAWpBC,eAAgB,KAUhBC,cAAe,GAWfC,iBAAkB,GAWlBC,aAAc,EAUdC,cAAe,GAWfC,iBAAkB,GAWlBC,aAAc,EAUdC,gBAAiB,KAWjBC,mBAAoB,KAWpBC,eAAgB,KAUhBC,cAAe,GAWfC,iBAAkB,GAWlBC,aAAc,EAUdC,gBAAiB,IAWjBC,mBAAoB,IAWpBC,eAAgB,KAUhBC,cAAe,GAWfC,iBAAkB,GAWlBC,aAAc,EAWdC,kBAAmB,GAWnBC,kBAAmB,KAWnBC,gBAAiB,IAWjBC,sBAAuB,KAWvBC,yBAA0B,MAW1BC,oBAAqB,MAWrBC,kBAAmB,GAWnBC,kBAAmB,KAWnBC,gBAAiB,MAWjBC,wBAAyB,GAWzBC,wBAAyB,KAWzBC,sBAAuB,MAWvBC,sBAAuB,KAWvBC,yBAA0B,MAW1BC,oBAAqB,OAWrBC,kBAAmB,GAWnBC,kBAAmB,IAWnBC,gBAAiB,KAWjBC,sBAAuB,KAWvBC,yBAA0B,MAW1BC,oBAAqB,OAWrBC,kBAAmB,GAWnBC,kBAAmB,KAWnBC,gBAAiB,MAajBC,eAAgB,SAAUC,EAAQC,EAAOC,GACrC,GAAIC,IAAeC,EAAGnF,KAAK+B,kBACvBqD,GAAgBD,EAAGnF,KAAKgC,aAI5B,OAFAhC,MAAKqF,iCAAiCL,EAAOC,EAAQC,EAAYE,GAE1DE,SAAUC,KAAKC,MAAMT,EAASQ,KAAKE,IAAI,GAAM,GAAKP,EAAWC,EAAIC,EAAYD,KAaxFO,aAAc,SAAUC,EAAQT,GAC5B,GAAIF,IAAUG,EAAGnF,KAAK+B,kBAClBkD,GAAWE,EAAGnF,KAAKgC,aAGvB,OAFAhC,MAAK4F,uBAAuBV,EAAYF,EAAOC,GAExCK,SAAUC,KAAKC,MAAMG,EAASV,EAAOE,EAAII,KAAKE,IAAI,GAAM,GAAKT,EAAMG,KAc9EU,eAAgB,SAAUd,EAAQC,EAAOC,GACrC,GAAIC,IAAeC,EAAGnF,KAAK+B,kBACvBqD,GAAgBD,EAAGnF,KAAKgC,aAG5B,OAFAhC,MAAKqF,iCAAiCL,EAAOC,EAAQC,EAAYE,GAEzDL,EAASK,EAAYD,EAAID,EAAWC,GAahDW,aAAc,SAAUH,EAAQT,GAC5B,GAAIF,IAAUG,EAAGnF,KAAK+B,kBAClBkD,GAAWE,EAAGnF,KAAKgC,aAGvB,OAFAhC,MAAK4F,uBAAuBV,EAAYF,EAAOC,GAEvCU,EAASV,EAAOE,EAAIH,EAAMG,GAetCY,WAAY,SAAUJ,EAAQX,EAAOC,EAAQe,GACpCA,IACDA,EAAYhG,KAAKiG,iBAAiBjB,GAEtC,IAAIkB,GAAQ,EACZ,IAAKlB,IAAUhF,KAAK+B,kBAAoBkD,IAAWjF,KAAKgC,cAAkBgD,EAAQhF,KAAKgC,cAAiBiD,EAASjF,KAAK+B,iBAAmB,CACrI,GAAIoE,GAAQb,SAAUC,KAAKC,MAAMG,EAAS3F,KAAKwD,kBAC3C4C,EAAWd,SAAUC,KAAKC,MAAMG,EAAS3F,KAAKwD,kBAC9C6C,EAAOf,SAAUC,KAAKC,MAAMY,EAAWpG,KAAKuD,mBAChD6C,IAAsBpG,KAAKuD,iBAC3B,IAAI+C,GAAOhB,SAAUC,KAAKC,MAAMY,EAAWpG,KAAKsD,oBAC5CiD,EAAOjB,SAAUC,KAAKC,MAAMY,EAAWpG,KAAKsD,mBAChD4C,GAAQlG,KAAKwG,qBAAqBL,EAAOE,EAAMC,EAAMC,GAAM,OACxD,IAAKvB,IAAUhF,KAAKkC,oBAAsB+C,IAAWjF,KAAKmC,gBAAoB6C,EAAQhF,KAAKmC,gBAAmB8C,EAASjF,KAAKkC,mBAC/H,GAAI8D,EAAW,CACX,GAAIS,GAASnB,SAAUC,KAAKC,MAAMG,EAAS3F,KAAKoE,sBAC5CsC,EAAYpB,SAAUC,KAAKC,MAAMG,EAAS3F,KAAKoE,sBAC/CuC,EAAQrB,SAAUC,KAAKC,MAAM,GAAKD,KAAKC,MAAMxF,KAAK4G,IAAIF,EAAW1G,KAAKmE,4BAC1EuC,IAAwB1G,KAAKmE,yBACzBuC,GAAa1G,KAAKgE,0BAClB0C,GAAa1G,KAAKgE,wBAClB2C,GAAS,EAAIrB,SAASC,KAAKC,MAAMxF,KAAK4G,IAAIF,EAAW1G,KAAKkE,yBAC1DwC,GAAa1G,KAAKkE,sBAClBwC,GAAa,EAEjB,IAAIG,GAAQvB,SAAUC,KAAKC,MAAMkB,EAAY1G,KAAK+D,0BAC9C+C,EAAQxB,SAAUC,KAAKC,MAAMkB,EAAY1G,KAAK+D,yBAClDmC,GAAQlG,KAAKwG,qBAAqBC,EAAQE,EAAOE,EAAOC,GAAO,OAC5D,CACH,GAAIC,GAAUzB,SAAUC,KAAKC,MAAMG,EAAS3F,KAAKiE,wBAC7C+C,EAAa1B,SAAUC,KAAKC,MAAMG,EAAS3F,KAAKiE,wBAChDgD,EAAS3B,SAAUC,KAAKC,MAAMwB,EAAahH,KAAKgE,yBACpDgD,IAA0BhH,KAAKgE,uBAC/B,IAAIkD,GAAS5B,SAAUC,KAAKC,MAAMwB,EAAahH,KAAK+D,0BAChDoD,EAAS7B,SAAUC,KAAKC,MAAMwB,EAAahH,KAAK+D,yBACpDmC,GAAQlG,KAAKwG,qBAAqBO,EAASE,EAAQC,EAAQC,GAAQ,OAEpE,IAAKnC,IAAUhF,KAAKqC,kBAAoB4C,IAAWjF,KAAKsC,cAAkB0C,EAAQhF,KAAKsC,cAAiB2C,EAASjF,KAAKqC,iBAAmB,CAC5I,GAAIoE,GAASnB,SAAUC,KAAKC,MAAMG,EAAS3F,KAAKiE,wBAC5CyC,EAAYpB,SAAUC,KAAKC,MAAMG,EAAS3F,KAAKiE,wBAC/C0C,EAAQrB,SAAUC,KAAKC,MAAMkB,EAAY1G,KAAKgE,yBAClD0C,IAAwB1G,KAAKgE,uBAC7B,IAAI6C,GAAQvB,SAAUC,KAAKC,MAAMkB,EAAY1G,KAAK+D,0BAC9C+C,EAAQxB,SAAUC,KAAKC,MAAMkB,EAAY1G,KAAK+D,yBAClDmC,GAAQlG,KAAKwG,qBAAqBC,EAAQE,EAAOE,EAAOC,GAAO,OAC5D,IAAK9B,IAAUhF,KAAKwC,kBAAoByC,IAAWjF,KAAKyC,cAAkBuC,EAAQhF,KAAKyC,cAAiBwC,EAASjF,KAAKwC,iBAAmB,CAC5I,GAAIuE,GAAUzB,SAAUC,KAAKC,MAAMG,EAAS3F,KAAK8D,kBAC7CkD,EAAa1B,SAAUC,KAAKC,MAAMG,EAAS3F,KAAK8D,kBAChDmD,EAAS3B,SAAUC,KAAKC,MAAMwB,EAAahH,KAAK6D,mBACpDmD,IAA0BhH,KAAK6D,iBAC/B,IAAIqD,GAAS5B,SAAUC,KAAKC,MAAMwB,EAAahH,KAAK4D,oBAChDuD,EAAS7B,SAAUC,KAAKC,MAAMwB,EAAahH,KAAK4D,mBACpDsC,GAAQlG,KAAKwG,qBAAqBO,EAASE,EAAQC,EAAQC,GAAQ,OAChE,IAAKnC,IAAUhF,KAAK2C,oBAAsBsC,IAAWjF,KAAK4C,gBAAoBoC,EAAQhF,KAAK4C,gBAAmBqC,EAASjF,KAAK2C,mBAC/H,GAAIqD,EAAW,CACX,GAAIS,GAASnB,SAAUC,KAAKC,MAAMG,EAAS3F,KAAK2D,sBAC5C+C,EAAYpB,SAAUC,KAAKC,MAAMG,EAAS3F,KAAK2D,sBAC/CgD,EAAQrB,SAAUC,KAAKC,MAAM,GAAMxF,KAAK4G,IAAIF,EAAW1G,KAAK0D,2BAChEgD,IAAwB1G,KAAK0D,yBACzBgD,GAAa1G,KAAK6D,oBAClB6C,GAAa1G,KAAK6D,kBAClB8C,GAAS,EAAIrB,SAASC,KAAKC,MAAMxF,KAAK4G,IAAIF,EAAW1G,KAAKyD,yBAC1DiD,GAAa1G,KAAKyD,sBAClBiD,GAAa,EAEjB,IAAIG,GAAQvB,SAAUC,KAAKC,MAAMkB,EAAY1G,KAAK4D,oBAC9CkD,EAAQxB,SAAUC,KAAKC,MAAMkB,EAAY1G,KAAK4D,mBAClDsC,GAAQlG,KAAKwG,qBAAqBC,EAAQE,EAAOE,EAAOC,GAAO,OAC5D,CACH,GAAIC,GAAUzB,SAAUC,KAAKC,MAAMG,EAAS3F,KAAK8D,kBAC7CkD,EAAa1B,SAAUC,KAAKC,MAAMG,EAAS3F,KAAK8D,kBAChDmD,EAAS3B,SAAUC,KAAKC,MAAMwB,EAAahH,KAAK6D,mBACpDmD,IAA0BhH,KAAK6D,iBAC/B,IAAIqD,GAAS5B,SAAUC,KAAKC,MAAMwB,EAAahH,KAAK4D,oBAChDuD,EAAS7B,SAAUC,KAAKC,MAAMwB,EAAahH,KAAK4D,mBACpDsC,GAAQlG,KAAKwG,qBAAqBO,EAASE,EAAQC,EAAQC,GAAQ,OAEpE,IAAKnC,IAAUhF,KAAK8C,kBAAoBmC,IAAWjF,KAAK+C,cAAkBiC,EAAQhF,KAAK+C,cAAiBkC,EAASjF,KAAK8C,iBAAmB,CAC5I,GAAI2D,GAASnB,SAAUC,KAAKC,MAAMG,EAAS3F,KAAKuE,kBAC5CmC,EAAYpB,SAAUC,KAAKC,MAAMG,EAAS3F,KAAKuE,kBAC/CoC,EAAQrB,SAAUC,KAAKC,MAAMkB,EAAY1G,KAAKsE,mBAClDoC,IAAwB1G,KAAKsE,iBAC7B,IAAIuC,GAAQvB,SAAUC,KAAKC,MAAMkB,EAAY1G,KAAKqE,oBAC9CyC,EAAQxB,SAAUC,KAAKC,MAAMkB,EAAY1G,KAAKqE,mBAClD6B,GAAQlG,KAAKwG,qBAAqBC,EAAQE,EAAOE,EAAOC,GAAO,OAC5D,IAAK9B,IAAUhF,KAAKiD,oBAAsBgC,IAAWjF,KAAKkD,gBAAoB8B,EAAQhF,KAAKkD,gBAAmB+B,EAASjF,KAAKiD,mBAC/H,GAAI+C,EAAW,CACX,GAAIe,GAAUzB,SAAUC,KAAKC,MAAMG,EAAS3F,KAAK0E,sBAC7CsC,EAAa1B,SAAUC,KAAKC,MAAMG,EAAS3F,KAAK0E,sBAChDuC,EAAS3B,SAAUC,KAAKC,MAAM,GAAMxF,KAAK4G,IAAII,EAAYhH,KAAKyE,2BAClEuC,IAA0BhH,KAAKyE,yBAC3BuC,GAAchH,KAAK4E,oBACnBoC,GAAchH,KAAK4E,kBACnBqC,GAAU,EAAI3B,SAASC,KAAKC,MAAMxF,KAAK4G,IAAII,EAAYhH,KAAKwE,yBAC5DwC,GAAchH,KAAKwE,sBACnBwC,GAAc,EAElB,IAAIE,GAAS5B,SAAUC,KAAKC,MAAMwB,EAAahH,KAAK2E,oBAChDwC,EAAS7B,SAAUC,KAAKC,MAAMwB,EAAahH,KAAK2E,mBACpDuB,GAAQlG,KAAKwG,qBAAqBO,EAASE,EAAQC,EAAQC,GAAQ,OAChE,CACH,GAAIV,GAASnB,SAAUC,KAAKC,MAAMG,EAAS3F,KAAK6E,kBAC5C6B,EAAYpB,SAAUC,KAAKC,MAAMG,EAAS3F,KAAK6E,kBAC/C8B,EAAQrB,SAAUC,KAAKC,MAAMkB,EAAY1G,KAAK4E,mBAClD8B,IAAwB1G,KAAK4E,iBAC7B,IAAIiC,GAAQvB,SAAUC,KAAKC,MAAMkB,EAAY1G,KAAK2E,oBAC9CmC,EAAQxB,SAAUC,KAAKC,MAAMkB,EAAY1G,KAAK2E,mBAClDuB,GAAQlG,KAAKwG,qBAAqBC,EAAQE,EAAOE,EAAOC,GAAO,OAEhE,IAAK9B,IAAUhF,KAAKoD,kBAAoB6B,IAAWjF,KAAKqD,cAAkB2B,EAAQhF,KAAKqD,cAAiB4B,EAASjF,KAAKoD,iBAAmB,CAC5I,GAAI2D,GAAUzB,SAAUC,KAAKC,MAAMG,EAAS3F,KAAK6E,kBAC7CmC,EAAa1B,SAAUC,KAAKC,MAAMG,EAAS3F,KAAK6E,kBAChDoC,EAAS3B,SAAUC,KAAKC,MAAMwB,EAAahH,KAAK4E,mBACpDoC,IAA0BhH,KAAK4E,iBAC/B,IAAIsC,GAAS5B,SAAUC,KAAKC,MAAMwB,EAAahH,KAAK2E,oBAChDwC,EAAS7B,SAAUC,KAAKC,MAAMwB,EAAahH,KAAK2E,mBACpDuB,GAAQlG,KAAKwG,qBAAqBO,EAASE,EAAQC,EAAQC,GAAQ,GAEvE,MAAOjB,IAcXkB,SAAU,SAAUzB,EAAQT,EAAYc,GAC/BA,IACDA,EAAYhG,KAAKiG,iBAAiBf,GAEtC,IAAIF,IAAUG,EAAGnF,KAAK+B,kBAClBkD,GAAWE,EAAGnF,KAAKgC,aAIvB,OAHAhC,MAAK4F,uBAAuBV,EAAYF,EAAOC,GAEnCjF,KAAK+F,WAAWJ,EAAQX,EAAMG,EAAGF,EAAOE,EAAGa,IAiB3DqB,iBAAkB,SAAUC,EAAWC,EAAWvC,EAAOC,EAAQe,GACxDA,IACDA,EAAYhG,KAAKiG,iBAAiBsB,GAEtC,IAAIC,GAAY,CAChB,IAAKxC,IAAUhF,KAAK+B,kBAAoBkD,IAAWjF,KAAKgC,cAAkBgD,EAAQhF,KAAKgC,cAAiBiD,EAASjF,KAAK+B,iBAClHyF,EAAYxH,KAAKyH,kBAAkBH,EAAWC,OAC3C,IAAKvC,IAAUhF,KAAKkC,oBAAsB+C,IAAWjF,KAAKmC,gBAAoB6C,EAAQhF,KAAKmC,gBAAmB8C,EAASjF,KAAKkC,mBAAqB,CACpJ,GAAIwF,IAAUvC,EAAG,GACbwC,GAAYxC,EAAG,GACfyC,GAAYzC,EAAG,GACfJ,GAAWI,EAAG,EAIlB,IAFAnF,KAAK6H,gBAAgBP,EAAWI,EAAOC,EAASC,EAAS7C,EAAQwC,GAE7DvB,EAAW,CACXwB,GAAaE,EAAMvC,EAAInF,KAAKoE,mBAE5B,IAAI0D,GAAU9H,KAAK4G,IAAIe,EAAQxC,EAAG,GAClCqC,IAAaM,EAAU9H,KAAKmE,yBAC5B2D,EAAUH,EAAQxC,EAAI,GAElB2C,EAAU,IACVN,GAAaxH,KAAKgE,wBAClBwD,IAAcM,EAAU,GAAK9H,KAAKkE,sBAClCsD,GAAa,GAGjBA,GAAaI,EAAQzC,EAAInF,KAAK+D,wBAC9ByD,GAAazC,EAAOI,MAEpBqC,GAAYxH,KAAKyH,kBAAkBH,EAAW,QAE/C,IAAKtC,IAAUhF,KAAKqC,kBAAoB4C,IAAWjF,KAAKsC,cAAkB0C,EAAQhF,KAAKsC,cAAiB2C,EAASjF,KAAKqC,iBACzHmF,EAAYxH,KAAKyH,kBAAkBH,EAAWC,OAC3C,IAAKvC,IAAUhF,KAAKwC,kBAAoByC,IAAWjF,KAAKyC,cAAkBuC,EAAQhF,KAAKyC,cAAiBwC,EAASjF,KAAKwC,iBACzHgF,EAAYxH,KAAKyH,kBAAkBH,EAAWC,OAC3C,IAAKvC,IAAUhF,KAAK2C,oBAAsBsC,IAAWjF,KAAK4C,gBAAoBoC,EAAQhF,KAAK4C,gBAAmBqC,EAASjF,KAAK2C,mBAAqB,CACpJ,GAAIoF,IAAW5C,EAAG,GACd6C,GAAa7C,EAAG,GAChB8C,GAAa9C,EAAG,GAChB+C,GAAY/C,EAAG,EAInB,IAFAnF,KAAK6H,gBAAgBP,EAAWS,EAAQC,EAAUC,EAAUC,EAASX,GAEjEvB,EAAW,CACXwB,GAAaE,EAAMvC,EAAInF,KAAK8D,eAE5B,IAAIqE,GAAWnI,KAAK4G,IAAIe,EAAQxC,EAAG,GACnCqC,IAAaW,EAAWnI,KAAK0D,yBAC7ByE,EAAWR,EAAQxC,EAAI,GACnBgD,EAAW,IACXX,GAAaxH,KAAK4E,kBAClB4C,IAAcW,EAAW,GAAKnI,KAAK6D,kBACnC2D,GAAa,GAGjBA,GAAaI,EAAQzC,EAAInF,KAAK4D,kBAC9B4D,GAAazC,EAAOI,MAEpBqC,GAAYxH,KAAKyH,kBAAkBH,EAAW,QAE/C,IAAKtC,IAAUhF,KAAK8C,kBAAoBmC,IAAWjF,KAAK+C,cAAkBiC,EAAQhF,KAAK+C,cAAiBkC,EAASjF,KAAK8C,iBACzH0E,EAAYxH,KAAKyH,kBAAkBH,EAAWC,OAC3C,IAAKvC,IAAUhF,KAAKiD,oBAAsBgC,IAAWjF,KAAKkD,gBAAoB8B,EAAQhF,KAAKkD,gBAAmB+B,EAASjF,KAAKiD,mBAAqB,CACpJ,GAAImF,IAAYjD,EAAG,GACfkD,GAAclD,EAAG,GACjBmD,GAAcnD,EAAG,GACjBoD,GAAapD,EAAG,EAGpB,IADAnF,KAAK6H,gBAAgBP,EAAWc,EAASC,EAAWC,EAAWC,EAAUhB,GACrEvB,EAAW,CACXwB,GAAaE,EAAMvC,EAAIT,mBAEvB,IAAI8D,GAAYxI,KAAK4G,IAAIe,EAAQxC,EAAG,GACpCqC,IAAagB,EAAYxI,KAAKyE,yBAC9B+D,EAAYb,EAAQxC,EAAI,GACpBqD,EAAY,IACZhB,GAAaxH,KAAK4E,kBAClB4C,IAAcgB,EAAY,GAAKxI,KAAKwE,sBACpCgD,GAAa,GAGjBA,GAAaI,EAAQzC,EAAInF,KAAK2E,kBAC9B6C,GAAazC,EAAOI,MAEpBqC,GAAYxH,KAAKyH,kBAAkBH,EAAW,SAE1CtC,IAAUhF,KAAKoD,kBAAoB6B,IAAWjF,KAAKqD,cAAkB2B,EAAQhF,KAAKqD,cAAiB4B,EAASjF,KAAKoD,oBACzHoE,EAAYxH,KAAKyH,kBAAkBH,EAAWC,GAElD,OAAOC,IAcXiB,eAAgB,SAAUnB,EAAWpC,EAAYc,GACxCA,IACDA,EAAYhG,KAAKiG,iBAAiBf,IAGtCoC,EAAYtH,KAAK0I,uBAAuBpB,EAAWpC,EAAYc,EAG/D,IAAIhB,IAAUG,EAAGnF,KAAK+B,kBAClBkD,GAAWE,EAAGnF,KAAKgC,aACvBhC,MAAK4F,uBAAuBV,EAAYF,EAAOC,EAE/C,IAAIuC,GAAYxH,KAAKqH,iBAAiBC,EAAWpC,EAAYF,EAAMG,EAAGF,EAAOE,EAAGa,GAE5E2C,EAAQ3I,KAAKoH,SAASI,EAAWtC,EAAYc,EAWjD,OARA2C,GAAQ3I,KAAKG,WAAWwI,EAAO,MAAO,KACtCrB,EAAYtH,KAAKG,WAAWmH,EAAW,MAAO,KAG1CqB,IAAUrB,IACVE,EAAYxH,KAAK4I,mBAAmBtB,EAAWE,GAAW,EAAM,EAAGtC,EAAYc,IAG5EwB,GAcX5B,uBAAwB,SAAUV,EAAYF,EAAOC,GAC7CM,KAAKsD,IAAI3D,EAAa,IAAQ,KAC9BF,EAAMG,EAAInF,KAAK+B,iBACfkD,EAAOE,EAAInF,KAAKgC,cACTuD,KAAKsD,IAAI3D,EAAa,mBAAsB,KACnDF,EAAMG,EAAInF,KAAKkC,mBACf+C,EAAOE,EAAInF,KAAKmC,gBACToD,KAAKsD,IAAI3D,EAAa,IAAQ,KACrCF,EAAMG,EAAInF,KAAKqC,iBACf4C,EAAOE,EAAInF,KAAKsC,cACTiD,KAAKsD,IAAI3D,EAAa,IAAQ,KACrCF,EAAMG,EAAInF,KAAKwC,iBACfyC,EAAOE,EAAInF,KAAKyC,cACT8C,KAAKsD,IAAI3D,EAAa,oBAAsB,KACnDF,EAAMG,EAAInF,KAAK2C,mBACfsC,EAAOE,EAAInF,KAAK4C,gBACT2C,KAAKsD,IAAI3D,EAAa,IAAQ,KACrCF,EAAMG,EAAInF,KAAK8C,iBACfmC,EAAOE,EAAInF,KAAK+C,cACTwC,KAAKsD,IAAI3D,EAAa,mBAAsB,KACnDF,EAAMG,EAAInF,KAAKiD,mBACfgC,EAAOE,EAAInF,KAAKkD,gBACTqC,KAAKsD,IAAI3D,EAAa,IAAQ,MACrCF,EAAMG,EAAInF,KAAKoD,iBACf6B,EAAOE,EAAInF,KAAKqD,eAexByF,eAAgB,SAAUC,EAAQ/D,EAAOC,GACrC,GAAIC,IAAeC,EAAGnF,KAAK+B,kBACvBqD,GAAgBD,EAAGnF,KAAKgC,aAG5B,OAFAhC,MAAKqF,iCAAiCL,EAAOC,EAAQC,EAAYE,GAE1DG,KAAKC,MAAMuD,EAASxD,KAAKE,IAAI,GAAM,GAAKP,EAAWC,EAAIC,EAAYD,EAAI,KAalF6D,aAAc,SAAUD,EAAQ7D,GAC5B,GAAIF,IAAUG,EAAGnF,KAAK+B,kBAClBkD,GAAWE,EAAGnF,KAAKgC,aAGvB,OAFAhC,MAAK4F,uBAAuBV,EAAYF,EAAOC,GAExCM,KAAKC,MAAMuD,EAAS/D,EAAMG,EAAIF,EAAOE,EAAII,KAAKE,IAAI,GAAM,GAAK,KAaxEwD,uBAAwB,SAAUF,EAAQ7D,GACtC,GAAIF,IAAUG,EAAGnF,KAAK+B,kBAClBkD,GAAWE,EAAGnF,KAAKgC,aAGvB,OAFAhC,MAAK4F,uBAAuBV,EAAYF,EAAOC,GAExCM,KAAKC,MAAMuD,EAAS/D,EAAMG,EAAIF,EAAOE,EAAII,KAAKE,IAAI,GAAM,KAYnEyD,cAAe,SAAUH,GACrB,MAAOA,GAAS,KAapBI,UAAW,SAAUJ,EAAQ/C,GACzB,GAAIG,GAASZ,KAAKC,MAAMuD,GAAU,KAAOxD,KAAKE,IAAI,GAAM,KACpD2D,EAAaL,GAAU,KAAOxD,KAAKE,IAAI,GAAM,IAC7CY,EAAQd,KAAKC,MAAM4D,GAAa,GAAK7D,KAAKE,IAAI,GAAM,IACxD2D,IAAwB9D,SAAUC,KAAKC,MAAM,GAAKD,KAAKE,IAAI,GAAM,IACjE,IAAIa,GAAQf,KAAKC,MAAM4D,EAAa7D,KAAKE,IAAI,GAAM,GACnD2D,IAAwB9D,SAAUC,KAAKE,IAAI,GAAM,GACjD,IAAI4D,GAAa9D,KAAKC,MAAM4D,EAAY,IACxC,OAAOpJ,MAAKwG,qBAAqBL,EAAOE,EAAMC,EAAM+C,EAAWrD,IAcnEsD,YAAa,SAAUP,EAAQ7D,EAAYc,GAIvC,MAHKA,KACDA,EAAYhG,KAAKiG,iBAAiBf,IAE/BlF,KAAKoH,SAASpH,KAAKgJ,aAAaD,EAAQ7D,GAAaA,EAAYc,IAe5EX,iCAAkC,SAAUL,EAAOC,EAAQC,EAAYE,GAC9DJ,IAAUhF,KAAK+B,kBAAoBkD,IAAWjF,KAAKgC,cAAkBgD,EAAQhF,KAAKgC,cAAiBiD,EAASjF,KAAK+B,kBAClHmD,EAAWC,EAAInF,KAAK+B,iBACpBqD,EAAYD,EAAInD,cACRgD,IAAUhF,KAAKkC,oBAAsB+C,IAAWjF,KAAKmC,gBAAoB6C,EAAQhF,KAAKmC,gBAAmB8C,EAASjF,KAAKkC,oBAC/HgD,EAAWC,EAAInF,KAAKkC,mBACpBkD,EAAYD,EAAInF,KAAKmC,gBACb6C,IAAUhF,KAAKqC,kBAAoB4C,IAAWjF,KAAKsC,cAAkB0C,EAAQhF,KAAKsC,cAAiB2C,EAASjF,KAAKqC,kBACzH6C,EAAWC,EAAInF,KAAKqC,iBACpB+C,EAAYD,EAAInF,KAAKsC,cACb0C,IAAUhF,KAAKwC,kBAAoByC,IAAWjF,KAAKyC,cAAkBuC,EAAQhF,KAAKyC,cAAiBwC,EAASjF,KAAKwC,kBACzH0C,EAAWC,EAAInF,KAAKwC,iBACpB4C,EAAYD,EAAInF,KAAKyC,cACbuC,IAAUhF,KAAK2C,oBAAsBsC,IAAWjF,KAAK4C,gBAAoBoC,EAAQhF,KAAK4C,gBAAmBqC,EAASjF,KAAK2C,oBAC/HuC,EAAWC,EAAInF,KAAK2C,mBACpByC,EAAYD,EAAInF,KAAK4C,gBACboC,IAAUhF,KAAK8C,kBAAoBmC,IAAWjF,KAAK+C,cAAkBiC,EAAQhF,KAAK+C,cAAiBkC,EAASjF,KAAK8C,kBACzHoC,EAAWC,EAAInF,KAAK8C,iBACpBsC,EAAYD,EAAInF,KAAK+C,cACbiC,IAAUhF,KAAKiD,oBAAsBgC,IAAWjF,KAAKkD,gBAAoB8B,EAAQhF,KAAKkD,gBAAmB+B,EAASjF,KAAKiD,oBAC/HiC,EAAWC,EAAInF,KAAKiD,mBACpBmC,EAAYD,EAAInF,KAAKkD,iBACb8B,IAAUhF,KAAKoD,kBAAoB6B,IAAWjF,KAAKqD,cAAkB2B,EAAQhF,KAAKqD,cAAiB4B,EAASjF,KAAKoD,oBACzH8B,EAAWC,EAAInF,KAAKoD,iBACpBgC,EAAYD,EAAInF,KAAKqD,eAe7BkG,eAAgB,SAAUC,EAAOxE,EAAOC,GAEpCuE,EAAQlE,SAASkE,EAAMC,QAAQ,GAAGnJ,QAAQ,IAAK,IAC/C,IAAI4E,IAAeC,EAAGnF,KAAK+B,kBACvBqD,GAAgBD,EAAGnF,KAAKgC,aAI5B,OAHAhC,MAAKqF,iCAAiCL,EAAOC,EAAQC,EAAYE,GAG1DG,KAAKC,MAAMgE,EAAQtE,EAAWC,EAAIC,EAAYD,EAAII,KAAKE,IAAI,GAAM,GAAK,KAajFiE,aAAc,SAAUF,EAAOtE,GAE3B,GAAIsE,GAAS,MAGT,MAFAA,IAAgB,MAETxJ,KAAK0J,aAAaF,EAAOtE,EAIpCsE,GAAQlE,SAASkE,EAAMC,QAAQ,GAAGnJ,QAAQ,IAAK,IAC/C,IAAI0E,IAAUG,EAAGnF,KAAK+B,kBAClBkD,GAAWE,EAAGnF,KAAKgC,aAKvB,OAJAhC,MAAK4F,uBAAuBV,EAAYF,EAAOC,GAIxCM,KAAKC,MAAMgE,EAAQxE,EAAMG,EAAIF,EAAOE,EAAII,KAAKE,IAAI,GAAM,GAAK,KAavEkE,uBAAwB,SAAUH,EAAOtE,GAErC,GAAIsE,GAAS,MAGT,MAFAA,IAAgB,MAETxJ,KAAK2J,uBAAuBH,EAAOtE,EAI9CsE,GAAQlE,SAASkE,EAAMC,QAAQ,GAAGnJ,QAAQ,IAAK,IAC/C,IAAI0E,IAAUG,EAAGnF,KAAK+B,kBAClBkD,GAAWE,EAAGnF,KAAKgC,aAKvB,OAJAhC,MAAK4F,uBAAuBV,EAAYF,EAAOC,GAIxCM,KAAKC,MAAMgE,EAAQxE,EAAMG,EAAIF,EAAOE,EAAII,KAAKE,IAAI,GAAM,KAgBlEe,qBAAsB,SAAUoD,EAAOC,EAASC,EAASC,EAAQ/D,GAW7D,QAASgE,GAAK/K,GACV,MAASA,GAAI,GAAM,IAAMA,EAAIA,EAXjC2K,EAAQA,GAAS,GAAKA,EAAQ,GAAKA,EACnCC,EAAUA,GAAW,GAAKA,EAAU,GAAKA,EACzCC,EAAUA,GAAW,GAAKA,EAAU,GAAKA,CAEzC,IAAIG,GAAkB,GAWtB,OAVKjE,KACDiE,EAAkB,KAGtBL,EAAQtE,SAAUC,KAAKC,MAAMoE,EAAQ,KAKzBI,EAAKJ,GAAS,IAAMI,EAAKH,GAAWI,EAAkBD,EAAKF,GAAW,IAAME,EAAKD,IAGjGrB,uBAAwB,SAAUwB,EAAUhF,EAAYc,GACpD,GAAIkE,EAAU,CACV,GAAIN,GAAQ,EACRC,EAAU,EACVC,EAAU,EACVC,EAAS,EACTI,EAAOD,EAASE,MAAM,IAE1B,IADAR,EAAQtE,SAAS6E,EAAK,IAClBA,EAAKE,QAAU,EAAG,CAIlB,GAHAR,EAAUvE,SAAS6E,EAAK,IACxBL,EAAUxE,SAAS6E,EAAK,IAEpB7E,SAAS6E,EAAK,KAAOjF,EAAY,CAEjC,GAAIoF,GAAgB/E,KAAKgF,KAAKrF,GAAc,CAC5CiF,GAAK,GAAKG,EAAcE,eAIxB,IAAIxE,EAAW,CAEX,GAAIyE,IAAS,KAAM,KAAM,KAAM,KAAM,KAAM,MACvCC,GAAa,KAAM,KAAM,KAAM,MAC/BC,GAAS,KAAM,KAEH,QAAZR,EAAK,KAAgBM,EAAM5K,SAASsK,EAAK,KAAOO,EAAU7K,SAASsK,EAAK,MACpE,GAAOjF,EAAa,GACpBiF,EAAK,GAAK,KAENQ,EAAM9K,SAASsK,EAAK,MACpBA,EAAK,GAAK,OAM9BJ,EAASzE,SAAS6E,EAAK,QACpB,CACH,GAAIS,GAAST,EAAK,GAAGC,MAAM,IAI3B,IAHAP,EAAUvE,SAAS6E,EAAK,IACxBL,EAAUxE,SAASsF,EAAO,IAEtBtF,SAASsF,EAAO,KAAO1F,EAAY,CACnC,GAAI2F,GAAiBtF,KAAKgF,KAAKrF,GAAc,CAC7C0F,GAAO,GAAKC,EAAeL,eAI3B,IAAIxE,EAAW,CAEX,GAAI8E,IAAU,KAAM,KAAM,KAAM,KAAM,KAAM,MACxCC,GAAc,KAAM,KAAM,KAAM,MAChCC,GAAU,KAAM,KAEF,QAAdJ,EAAO,KAAgBE,EAAOjL,SAASsK,EAAK,KAAOY,EAAWlL,SAAS+K,EAAO,MAC1E,GAAO1F,EAAa,GACpB0F,EAAO,GAAK,KAERI,EAAOnL,SAAS+K,EAAO,MACvBA,EAAO,GAAK,OAOhCb,EAASzE,SAASsF,EAAO,IAG7B,MAAO5K,MAAKwG,qBAAqBoD,EAAOC,EAASC,EAASC,EAAQ/D,GAEtE,MAAO,eAiBX4C,mBAAoB,SAAUtB,EAAWE,EAAWyD,EAASC,EAAWhG,EAAYc,GAChF,GAAImF,GAAe,CACfF,GACAE,EAAe3D,EAAY0D,GAE3BC,EAAe3D,EAAY0D,EAC3BA,IAEJ,IAAIvC,GAAQ3I,KAAKoH,SAAS+D,EAAcjG,EAAYc,EAOpD,OAJA2C,GAAQ3I,KAAKG,WAAWwI,EAAO,IAAK,KACpCrB,EAAYtH,KAAKG,WAAWmH,EAAW,IAAK,KAGxCqB,IAAUrB,EACHtH,KAAK4I,mBAAmBtB,EAAWE,GAAYyD,EAASC,EAAWhG,EAAYc,GAGnFmF,GAYXlF,iBAAkB,SAAUmF,GACxB,MAAa,SAATA,GAAmBA,EAAO,IAAMA,EAAO,IAEvB,KAATA,IAES,KAATA,IAES,QAATA,GAAmBA,EAAO,IAAMA,EAAO,IAE9B,KAATA,IAES,KAATA,IAES,QAATA,GAAmBA,EAAO,IAAMA,EAAO,QAkBtD3D,kBAAmB,SAAUH,EAAWpC,GACpC,GAAImG,GAAa,EACb7D,EAAY,EAEZE,GAAUvC,EAAG,GACbwC,GAAYxC,EAAG,GACfyC,GAAYzC,EAAG,GACfJ,GAAWI,EAAG,EAWlB,OATAnF,MAAK6H,gBAAgBP,EAAWI,EAAOC,EAASC,EAAS7C,EAAQG,GAEjEmG,GAAwB,GAAV3D,EAAMvC,EAAS,GAC7BkG,GAA0B,GAAZ1D,EAAQxC,EACtBkG,GAAczD,EAAQzC,EACtBqC,GAAazC,EAAOI,EAEpBqC,GAAalC,SAAStF,KAAK0J,aAAa2B,EAAYnG,KAmBxD2C,gBAAiB,SAAUP,EAAWI,EAAOC,EAASC,EAAS7C,EAAQG,GACnE,GAAIoG,GAAWhE,EAAU8C,MAAM,IAE/B,IAAIkB,EAASjB,QAAU,EACnB3C,EAAMvC,EAAIG,SAASgG,EAAS,IAC5B3D,EAAQxC,EAAIG,SAASgG,EAAS,IAC9B1D,EAAQzC,EAAIG,SAASgG,EAAS,IAC9BvG,EAAOI,EAAIG,SAASgG,EAAS,QAC1B,CACH,GAAIV,GAASU,EAAS,GAAGlB,MAAM,IAC/B1C,GAAMvC,EAAIG,SAASgG,EAAS,IAC5B3D,EAAQxC,EAAIG,SAASsF,EAAO,IAC5BhD,EAAQzC,EAAIG,SAASsF,EAAO,IAC5B7F,EAAOI,EAAIG,SAASgG,EAAS,IAGjC5D,EAAMvC,EAAIuC,EAAMvC,GAAK,GAAKuC,EAAMvC,EAAI,GAAKuC,EAAMvC,EAC/CwC,EAAQxC,EAAIwC,EAAQxC,GAAK,GAAKwC,EAAQxC,EAAI,GAAKwC,EAAQxC,EACvDyC,EAAQzC,EAAIyC,EAAQzC,GAAK,GAAKyC,EAAQzC,EAAI,GAAKyC,EAAQzC,EACvDJ,EAAOI,EAAIJ,EAAOI,GAAKI,KAAKgF,KAAKrF,GAAcH,EAAOI,EAAIG,SAASC,KAAKgF,KAAKrF,IAAeH,EAAOI,GAIvGoG,yBAA0B,SAAUzB,EAAS0B,GACzC,GAAI5B,GAAQrE,KAAKC,MAAMsE,EAAU,MAC7BD,EAAUtE,KAAKC,OAAOsE,EAAU,KAAOF,GAAS,IAChD6B,EAAWlG,KAAKC,MAAMsE,EAAW,GAAKD,EAAY,KAAOD,GACzD8B,EAAkBnG,KAAKC,MAAiB,IAAVsE,EAAsC,IAAtBvE,KAAKC,MAAMsE,GAEzD4B,IAAmB,MACnBD,IACAC,GAAoC,IAGxC,IAAIC,GAAW/B,EAAQ,GAAK,IAAMA,EAAQA,EAAMY,WAC5CoB,EAAa/B,EAAU,GAAK,IAAMA,EAAUA,EAAQW,WACpDqB,EAAcJ,EAAW,GAAK,IAAMA,EAAWA,EAASjB,WACxDsB,EAAqBJ,EAAkB,GAAK,IAAMA,EAAkBA,EAAgBlB,UACxF,OAAOmB,GAAW,IAAMC,EAAa,IAAMC,GAAwB,cAATL,EAAuB,GAAM,IAAMM,IAGjGC,sBAAuB,SAAUzE,GAC7B,GAAIgE,GAAWhE,EAAU8C,MAAM,KAC3B1C,GAAUvC,EAAG,GACbwC,GAAYxC,EAAG,GACfyC,GAAYzC,EAAG,GACf6G,GAAoB7G,EAAG,EAE3BuC,GAAMvC,EAAIG,SAASgG,EAAS,IAC5B3D,EAAQxC,EAAIG,SAASgG,EAAS,IAC9B1D,EAAQzC,EAAIG,SAASgG,EAAS,QACXW,IAAfX,EAAS,KACTU,EAAgB7G,EAAIG,SAASgG,EAAS,IAE1C,IAAIY,GAAS,CAOb,OANAA,IAAoB,GAAVxE,EAAMvC,EAAS,GACzB+G,GAAsB,GAAZvE,EAAQxC,EAClB+G,GAAUtE,EAAQzC,EAClB+G,GAAUF,EAAgB7G,EAAI,IAC9B+G,EAAS5G,UAAU4G,EAAS3G,KAAKE,IAAI,GAAM,IAAIgE,QAAQ,KAK3DtJ,WAAY,SAAUgM,EAAK/L,EAAIC,GAC3B,MAAO8L,GAAIhM,WAAWC,EAAIC,IAE9BuG,IAAK,SAAUwF,EAAGC,GACd,MAAOrM,MAAKsM,SAASF,IAAMpM,KAAKsM,SAASD,GAAK/G,SAAS8G,EAAIC,GAAK,MAEpEC,SAAU,SAAUC,GAChB,MAAgB,QAARA,OAA0BN,KAARM,GAG9BC,cAAe,SAAU1C,GAErB,MAAOxE,UAAS,EAAUmE,QAAQ,GAAGnJ,QAAQ,IAAK,IAAK,MAG/DmM,OAAOC,gBAAkB,GAAIlM,GACtB,GAAIA,KAEfiM,QAAOlM,gBAAkBA", "file": "mam-timecode-convert.min.js", "sourcesContent": ["/******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, {\n/******/ \t\t\t\tconfigurable: false,\n/******/ \t\t\t\tenumerable: true,\n/******/ \t\t\t\tget: getter\n/******/ \t\t\t});\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 0);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports) {\n\n/*!\n * TimeCode Convert v2.0.1 (http://www.sobey.com)\n * Copyright 2010-2016 Sobey, Inc. CDC\n * Licensed under the MIT license\n * Author: Liujianming (<EMAIL>)\n * Convert From C++(Sobey MPC) ->C#(Sobey MAM) -> Javascript\n */\n\n/**\n * Array Extended contains\n *\n */\nArray.prototype.S = String.fromCharCode(2);\nArray.prototype.contains = function (e) {\n    var r = new RegExp(this.S + e + this.S);\n    return (r.test(this.S + this.join(this.S) + this.S));\n};\n/**\n * String  Extended replaceAll\n *\n */\nString.prototype.replaceAll = function (s1, s2) {\n    return this.replace(new RegExp(s1, \"gm\"), s2);\n};\n\nvar TimeCodeConvert = (function () {\n    /** @namespace TimeCodeConvert */\n    /**\n     * 时码转换帮助类\n     *\n     * @public\n     * @class TimeCodeConvert.TimeCodeConvertHelper\n     */\n    var timeCodeConvertHelper = function () {\n    };\n\n    timeCodeConvertHelper.prototype = {\n        /**\n        * 桌面视频制式标准枚举定义\n        *\n        * @class TimeCodeConvert.MpcVideoStandard\n        */\n        MpcVideoStandard: {\n            mpcVideostandardUnknow: 0,\n            mpcVideostandardPal: 1,\n            mpcVideostandardNtsc2997: 2,\n            mpcVideostandardNtsc30: 4,\n            mpcVideostandardSecam: 8,\n            mpcVideostandard1920108050I: 16,\n            mpcVideostandard192010805994I: 32,\n            mpcVideostandard1920108060I: 64,\n            mpcVideostandard192010802398P: 128,\n            mpcVideostandard1920108024P: 256,\n            mpcVideostandard1920108025P: 512,\n            mpcVideostandard192010802997P: 1024,\n            mpcVideostandard1920108030P: 2048,\n            mpcVideostandard12807202398P: 4096,\n            mpcVideostandard128072024P: 8192,\n            mpcVideostandard128072050P: 16384,\n            mpcVideostandard12807205994P: 32768,\n            mpcVideostandard1440108050I: 65536,\n            mpcVideostandard144010805994I: 131072,\n            mpcVideostandard1440108060I: 262144\n        },\n\n        /**\n         * PAL field frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @default 50\n         * @type number\n         */\n        m_mpcStRate25: 50,\n        /**\n         * PAL frame  frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 25\n         * @type number\n         */\n        MpcStFrameRate25: 25,\n        /**\n         * PAL scale\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1\n         * @type number\n         */\n        MpcStScale25: 1,\n        /**\n         * NTSC field frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @default 60000\n         * @type number\n         */\n        m_mpcStRate2997: 60000,\n        /**\n         * NTSC frame  frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 30000\n         * @type number\n         */\n        MpcStFrameRate2997: 30000,\n        /**\n         * NTSC scale\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1001\n         * @type number\n         */\n        MpcStScale2997: 1001,\n        /**\n         * 30-F field frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @default 60\n         * @type number\n         */\n        m_mpcStRate30: 60,\n        /**\n         * 30-F frame frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 30\n         * @type number\n         */\n        MpcStFrameRate30: 30,\n        /**\n         * 30-F scale\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1\n         * @type number\n         */\n        MpcStScale30: 1,\n        /**\n         * 24-F field frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @default 48\n         * @type number\n         */\n        m_mpcStRate24: 48,\n        /**\n         * 24-F field frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 24\n         * @type number\n         */\n        MpcStFrameRate24: 24,\n        /**\n         * 24-F scale\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1\n         * @type number\n         */\n        MpcStScale24: 1,\n        /**\n         * 2398-F field frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @default 48000\n         * @type number\n         */\n        m_mpcStRate2398: 48000,\n        /**\n         * 2398-F frame frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 24000\n         * @type number\n         */\n        MpcStFrameRate2398: 24000,\n        /**\n         * 2398-F scale\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1001\n         * @type number\n         */\n        MpcStScale2398: 1001,\n        /**\n         * PAL field frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @default 50\n         * @type number\n         */\n        m_mpcStRate50: 50,\n        /**\n         * PAL frame  frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 50\n         * @type number\n         */\n        MpcStFrameRate50: 50,\n        /**\n         * PAL scale\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1\n         * @type number\n         */\n        MpcStScale50: 1,\n        /**\n         * NTSC field frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @default 60000\n         * @type number\n         */\n        m_mpcStRate5994: 60000,\n        /**\n         * NTSC frame  frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 60000\n         * @type number\n         */\n        MpcStFrameRate5994: 60000,\n        /**\n         * NTSC scale\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1001\n         * @type number\n         */\n        MpcStScale5994: 1001,\n        /**\n         * 60-F field frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @default 60\n         * @type number\n         */\n        m_mpcStRate60: 60,\n        /**\n         * 60-F frame frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 60\n         * @type number\n         */\n        MpcStFrameRate60: 60,\n        /**\n         * 60-F scale\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1\n         * @type number\n         */\n        MpcStScale60: 1,\n        /**\n         * 25 Frame: frames per second\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 25\n         * @type number\n         */\n        MpcFramesSecond25: 25,\n        /**\n         * 25 Frame: frames per minute\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1500\n         * @type number\n         */\n        MpcFramesMinute25: 1500,\n        /**\n         * 25 Frame: frames per hour\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 90000\n         * @type number\n         */\n        MpcFramesHour25: 90000,\n        /**\n         * 30 DROP Frame: frames per minute\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1438\n         * @type number\n         */\n        MpcFramesMinute24Drop: 1438,\n        /**\n         * 30 DROP Frame: frames per 10 minutes\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 14382\n         * @type number\n         */\n        MpcFrames10Minutes24Drop: 14382,\n        /**\n         * 30 DROP Frame: frames per hour\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 86292\n         * @type number\n         */\n        MpcFramesHour24Drop: 86292,\n        /**\n         * 24 Frame: frames per second\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 24\n         * @type number\n         */\n        MpcFramesSecond24: 24,\n        /**\n         * 24 Frame: frames per minute\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1440\n         * @type number\n         */\n        MpcFramesMinute24: 1440,\n        /**\n         * 24 Frame: frames per hour\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 86400\n         * @type number\n         */\n        MpcFramesHour24: 86400,\n        /**\n         * 30 NO_DROP Frame: frames per second\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 30\n         * @type number\n         */\n        MpcFramesSecondNodrop30: 30,\n        /**\n         * 30 NO_DROP Frame: frames per minute\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1800\n         * @type number\n         */\n        MpcFramesMinuteNodrop30: 1800,\n        /**\n         * 30 NO_DROP Frame: frames per hour\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 108000\n         * @type number\n         */\n        MpcFramesHourNodrop30: 108000,\n        /**\n         * 30 DROP Frame: frames per minute\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1798\n         * @type number\n         */\n        MpcFramesMinute30Drop: 1798,\n        /**\n         * 30 DROP Frame: frames per 10 minutes\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 17982\n         * @type number\n         */\n        MpcFrames10Minutes30Drop: 17982,\n        /**\n         * 30 DROP Frame: frames per hour\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 107892\n         * @type number\n         */\n        MpcFramesHour30Drop: 107892,\n        /**\n         * 50 Frame: frames per second\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 50\n         * @type number\n         */\n        MpcFramesSecond50: 50,\n        /**\n         * 50 Frame: frames per minute\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 3000\n         * @type number\n         */\n        MpcFramesMinute50: 3000,\n        /**\n         * 50 Frame: frames per hour\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 180000\n         * @type number\n         */\n        MpcFramesHour50: 180000,\n        /**\n         * 60 DROP Frame: frames per minute\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 3596\n         * @type number\n         */\n        MpcFramesMinute60Drop: 3596,\n        /**\n         * 60 DROP Frame: frames per 10 minutes\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 35964\n         * @type number\n         */\n        MpcFrames10Minutes60Drop: 35964,\n        /**\n         * 60 DROP Frame: frames per hour\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 215784\n         * @type number\n         */\n        MpcFramesHour60Drop: 215784,\n        /**\n         * 60 Frame: frames per second\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 60\n         * @type number\n         */\n        MpcFramesSecond60: 60,\n        /**\n         * 60 Frame: frames per minute\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 3600\n         * @type number\n         */\n        MpcFramesMinute60: 3600,\n        /**\n         * 60 Frame: frames per hour\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 216000\n         * @type number\n         */\n        MpcFramesHour60: 216000,\n        /**\n         * 帧转百纳秒\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}    lFrame    帧\n         * @param   {number}    dRate     帧率\n         * @param   {number}    dScale    \n         * @return  {number}              百纳秒\n         */\n        frame2L100Ns$1: function (lFrame, dRate, dScale) {\n            var dFrameRate = { v: this.MpcStFrameRate25 };\n            var dFrameScale = { v: this.MpcStScale25 };\n\n            this.rate2ScaleFrameRateAndFrameScale(dRate, dScale, dFrameRate, dFrameScale);\n\n            return parseInt((Math.floor(lFrame * Math.pow(10.0, 7) * dFrameRate.v / dFrameScale.v)));\n        },\n        /**\n         * 帧转百纳秒\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}    dFrame        帧\n         * @param   {number}    dFrameRate    帧率\n         * @return  {number}                  百纳秒\n         */\n        frame2L100Ns: function (dFrame, dFrameRate) {\n            var dRate = { v: this.MpcStFrameRate25 };\n            var dScale = { v: this.MpcStScale25 };\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\n\n            return parseInt((Math.floor(dFrame * dScale.v * Math.pow(10.0, 7) / dRate.v)));\n        },\n        /**\n         * 帧转秒\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}    lFrame    帧\n         * @param   {number}    dRate     帧率\n         * @param   {number}    dScale    \n         * @return  {number}              秒\n         */\n        frame2Second$1: function (lFrame, dRate, dScale) {\n            var dFrameRate = { v: this.MpcStFrameRate25 };\n            var dFrameScale = { v: this.MpcStScale25 };\n            this.rate2ScaleFrameRateAndFrameScale(dRate, dScale, dFrameRate, dFrameScale);\n\n            return (lFrame * dFrameScale.v / dFrameRate.v);\n        },\n        /**\n         * 帧转秒\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}    dFrame        帧\n         * @param   {number}    dFrameRate    帧率\n         * @return  {number}                  秒\n         */\n        frame2Second: function (dFrame, dFrameRate) {\n            var dRate = { v: this.MpcStFrameRate25 };\n            var dScale = { v: this.MpcStScale25 };\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\n\n            return (dFrame * dScale.v / dRate.v);\n        },\n        /**\n         * 帧转时码\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}     dFrame       帧\n         * @param   {number}     dRate        帧率\n         * @param   {number}     dScale       \n         * @param   {boolean}    dropFrame    \n         * @return  {string}                  时码字符串\n         */\n        frame2Tc$1: function (dFrame, dRate, dScale, dropFrame) {\n            if (!dropFrame) {\n                dropFrame = this.getRateDropFrame(dRate);\n            }\n            var strTc = \"\";\n            if ((dRate === this.MpcStFrameRate25 && dScale === this.MpcStScale25) || (dRate * this.MpcStScale25 === dScale * this.MpcStFrameRate25)) {\n                var dHour = parseInt((Math.floor(dFrame / this.MpcFramesHour25)));\n                var dResidue = parseInt((Math.floor(dFrame % this.MpcFramesHour25)));\n                var dMin = parseInt((Math.floor(dResidue / this.MpcFramesMinute25)));\n                dResidue = dResidue % this.MpcFramesMinute25;\n                var dSec = parseInt((Math.floor(dResidue / this.MpcFramesSecond25)));\n                var dFra = parseInt((Math.floor(dResidue % this.MpcFramesSecond25)));\n                strTc = this.formatTimeCodeString(dHour, dMin, dSec, dFra, false);\n            } else if ((dRate === this.MpcStFrameRate2997 && dScale === this.MpcStScale2997) || (dRate * this.MpcStScale2997 === dScale * this.MpcStFrameRate2997)) {\n                if (dropFrame) {\n                    var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHour30Drop)));\n                    var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHour30Drop)));\n                    var dMin1 = parseInt((Math.floor(10 * Math.floor(this.div(dResidue1, this.MpcFrames10Minutes30Drop)))));\n                    dResidue1 = dResidue1 % this.MpcFrames10Minutes30Drop;\n                    if (dResidue1 >= this.MpcFramesMinuteNodrop30) {\n                        dResidue1 -= this.MpcFramesMinuteNodrop30;\n                        dMin1 += 1 + parseInt(Math.floor(this.div(dResidue1, this.MpcFramesMinute30Drop)));\n                        dResidue1 %= this.MpcFramesMinute30Drop;\n                        dResidue1 += 2;\n                    }\n                    var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecondNodrop30)));\n                    var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecondNodrop30)));\n                    strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, true);\n                } else {\n                    var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHourNodrop30)));\n                    var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHourNodrop30)));\n                    var dMin11 = parseInt((Math.floor(dResidue11 / this.MpcFramesMinuteNodrop30)));\n                    dResidue11 = dResidue11 % this.MpcFramesMinuteNodrop30;\n                    var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecondNodrop30)));\n                    var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecondNodrop30)));\n                    strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, false);\n                }\n            } else if ((dRate === this.MpcStFrameRate30 && dScale === this.MpcStScale30) || (dRate * this.MpcStScale30 === dScale * this.MpcStFrameRate30)) {\n                var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHourNodrop30)));\n                var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHourNodrop30)));\n                var dMin1 = parseInt((Math.floor(dResidue1 / this.MpcFramesMinuteNodrop30)));\n                dResidue1 = dResidue1 % this.MpcFramesMinuteNodrop30;\n                var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecondNodrop30)));\n                var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecondNodrop30)));\n                strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, false);\n            } else if ((dRate === this.MpcStFrameRate24 && dScale === this.MpcStScale24) || (dRate * this.MpcStScale24 === dScale * this.MpcStFrameRate24)) {\n                var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHour24)));\n                var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHour24)));\n                var dMin11 = parseInt((Math.floor(dResidue11 / this.MpcFramesMinute24)));\n                dResidue11 = dResidue11 % this.MpcFramesMinute24;\n                var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecond24)));\n                var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecond24)));\n                strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, false);\n            } else if ((dRate === this.MpcStFrameRate2398 && dScale === this.MpcStScale2398) || (dRate * this.MpcStScale2398 === dScale * this.MpcStFrameRate2398)) {\n                if (dropFrame) {\n                    var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHour24Drop)));\n                    var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHour24Drop)));\n                    var dMin1 = parseInt((Math.floor(10 * (this.div(dResidue1, this.MpcFrames10Minutes24Drop)))));\n                    dResidue1 = dResidue1 % this.MpcFrames10Minutes24Drop;\n                    if (dResidue1 >= this.MpcFramesMinute24) {\n                        dResidue1 -= this.MpcFramesMinute24;\n                        dMin1 += 1 + parseInt(Math.floor(this.div(dResidue1, this.MpcFramesMinute24Drop)));\n                        dResidue1 %= this.MpcFramesMinute24Drop;\n                        dResidue1 += 2;\n                    }\n                    var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecond24)));\n                    var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecond24)));\n                    strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, true);\n                } else {\n                    var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHour24)));\n                    var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHour24)));\n                    var dMin11 = parseInt((Math.floor(dResidue11 / this.MpcFramesMinute24)));\n                    dResidue11 = dResidue11 % this.MpcFramesMinute24;\n                    var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecond24)));\n                    var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecond24)));\n                    strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, false);\n                }\n            } else if ((dRate === this.MpcStFrameRate50 && dScale === this.MpcStScale50) || (dRate * this.MpcStScale50 === dScale * this.MpcStFrameRate50)) {\n                var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHour50)));\n                var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHour50)));\n                var dMin1 = parseInt((Math.floor(dResidue1 / this.MpcFramesMinute50)));\n                dResidue1 = dResidue1 % this.MpcFramesMinute50;\n                var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecond50)));\n                var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecond50)));\n                strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, false);\n            } else if ((dRate === this.MpcStFrameRate5994 && dScale === this.MpcStScale5994) || (dRate * this.MpcStScale5994 === dScale * this.MpcStFrameRate5994)) {\n                if (dropFrame) {\n                    var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHour60Drop)));\n                    var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHour60Drop)));\n                    var dMin11 = parseInt((Math.floor(10 * (this.div(dResidue11, this.MpcFrames10Minutes60Drop)))));\n                    dResidue11 = dResidue11 % this.MpcFrames10Minutes60Drop;\n                    if (dResidue11 >= this.MpcFramesMinute60) {\n                        dResidue11 -= this.MpcFramesMinute60;\n                        dMin11 += 1 + parseInt(Math.floor(this.div(dResidue11, this.MpcFramesMinute60Drop)));\n                        dResidue11 %= this.MpcFramesMinute60Drop;\n                        dResidue11 += 4;\n                    }\n                    var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecond60)));\n                    var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecond60)));\n                    strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, true);\n                } else {\n                    var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHour60)));\n                    var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHour60)));\n                    var dMin1 = parseInt((Math.floor(dResidue1 / this.MpcFramesMinute60)));\n                    dResidue1 = dResidue1 % this.MpcFramesMinute60;\n                    var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecond60)));\n                    var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecond60)));\n                    strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, false);\n                }\n            } else if ((dRate === this.MpcStFrameRate60 && dScale === this.MpcStScale60) || (dRate * this.MpcStScale60 === dScale * this.MpcStFrameRate60)) {\n                var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHour60)));\n                var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHour60)));\n                var dMin11 = parseInt((Math.floor(dResidue11 / this.MpcFramesMinute60)));\n                dResidue11 = dResidue11 % this.MpcFramesMinute60;\n                var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecond60)));\n                var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecond60)));\n                strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, false);\n            }\n            return strTc;\n        },\n        /**\n         * 帧转时码\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}     dFrame        帧\n         * @param   {number}     dFrameRate    帧率\n         * @param   {boolean}    dropFrame     是否丢帧\n         * @return  {string}                   时码字符串\n         */\n        frame2Tc: function (dFrame, dFrameRate, dropFrame) {\n            if (!dropFrame) {\n                dropFrame = this.getRateDropFrame(dFrameRate);\n            }\n            var dRate = { v: this.MpcStFrameRate25 };\n            var dScale = { v: this.MpcStScale25 };\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\n\n            var strTc = this.frame2Tc$1(dFrame, dRate.v, dScale.v, dropFrame);\n            return strTc;\n        },\n        /**\n         * 时码字符串转帧\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {string}     sTimeCode    时码\n         * @param   {number}     frameRate    帧率\n         * @param   {number}     dRate        \n         * @param   {number}     dScale       \n         * @param   {boolean}    dropFrame    是否是丢帧\n         * @return  {number}                  帧\n         */\n        timeCode2Frame$1: function (sTimeCode, frameRate, dRate, dScale, dropFrame) {\n            if (!dropFrame) {\n                dropFrame = this.getRateDropFrame(frameRate);\n            }\n            var ftcFrames = 0;\n            if ((dRate === this.MpcStFrameRate25 && dScale === this.MpcStScale25) || (dRate * this.MpcStScale25 === dScale * this.MpcStFrameRate25)) {\n                ftcFrames = this.timeCode2NdfFrame(sTimeCode, frameRate);\n            } else if ((dRate === this.MpcStFrameRate2997 && dScale === this.MpcStScale2997) || (dRate * this.MpcStScale2997 === dScale * this.MpcStFrameRate2997)) {\n                var lHour = { v: 0 };\n                var lMinute = { v: 0 };\n                var lSecond = { v: 0 };\n                var lFrame = { v: 0 };\n\n                this.timeCode2Format(sTimeCode, lHour, lMinute, lSecond, lFrame, frameRate);\n\n                if (dropFrame) {\n                    ftcFrames += lHour.v * this.MpcFramesHour30Drop;\n\n                    var lwReste = this.div(lMinute.v, 10);\n                    ftcFrames += lwReste * this.MpcFrames10Minutes30Drop;\n                    lwReste = lMinute.v % 10;\n\n                    if (lwReste > 0) {\n                        ftcFrames += this.MpcFramesMinuteNodrop30;\n                        ftcFrames += (lwReste - 1) * this.MpcFramesMinute30Drop;\n                        ftcFrames -= 2;\n                    }\n\n                    ftcFrames += lSecond.v * this.MpcFramesSecondNodrop30;\n                    ftcFrames += lFrame.v;\n                } else {\n                    ftcFrames = this.timeCode2NdfFrame(sTimeCode, 30);\n                }\n            } else if ((dRate === this.MpcStFrameRate30 && dScale === this.MpcStScale30) || (dRate * this.MpcStScale30 === dScale * this.MpcStFrameRate30)) {\n                ftcFrames = this.timeCode2NdfFrame(sTimeCode, frameRate);\n            } else if ((dRate === this.MpcStFrameRate24 && dScale === this.MpcStScale24) || (dRate * this.MpcStScale24 === dScale * this.MpcStFrameRate24)) {\n                ftcFrames = this.timeCode2NdfFrame(sTimeCode, frameRate);\n            } else if ((dRate === this.MpcStFrameRate2398 && dScale === this.MpcStScale2398) || (dRate * this.MpcStScale2398 === dScale * this.MpcStFrameRate2398)) {\n                var lHour1 = { v: 0 };\n                var lMinute1 = { v: 0 };\n                var lSecond1 = { v: 0 };\n                var lFrame1 = { v: 0 };\n\n                this.timeCode2Format(sTimeCode, lHour1, lMinute1, lSecond1, lFrame1, frameRate);\n\n                if (dropFrame) {\n                    ftcFrames += lHour.v * this.MpcFramesHour24;\n\n                    var lwReste1 = this.div(lMinute.v, 10);\n                    ftcFrames += lwReste1 * this.MpcFrames10Minutes24Drop;\n                    lwReste1 = lMinute.v % 10;\n                    if (lwReste1 > 0) {\n                        ftcFrames += this.MpcFramesMinute60;\n                        ftcFrames += (lwReste1 - 1) * this.MpcFramesMinute24;\n                        ftcFrames -= 2;\n                    }\n\n                    ftcFrames += lSecond.v * this.MpcFramesSecond24;\n                    ftcFrames += lFrame.v;\n                } else {\n                    ftcFrames = this.timeCode2NdfFrame(sTimeCode, 24);\n                }\n            } else if ((dRate === this.MpcStFrameRate50 && dScale === this.MpcStScale50) || (dRate * this.MpcStScale50 === dScale * this.MpcStFrameRate50)) {\n                ftcFrames = this.timeCode2NdfFrame(sTimeCode, frameRate);\n            } else if ((dRate === this.MpcStFrameRate5994 && dScale === this.MpcStScale5994) || (dRate * this.MpcStScale5994 === dScale * this.MpcStFrameRate5994)) {\n                var lHour11 = { v: 0 };\n                var lMinute11 = { v: 0 };\n                var lSecond11 = { v: 0 };\n                var lFrame11 = { v: 0 };\n\n                this.timeCode2Format(sTimeCode, lHour11, lMinute11, lSecond11, lFrame11, frameRate);\n                if (dropFrame) {\n                    ftcFrames += lHour.v * MpcFramesHour60Drop;\n\n                    var lwReste11 = this.div(lMinute.v, 10);\n                    ftcFrames += lwReste11 * this.MpcFrames10Minutes60Drop;\n                    lwReste11 = lMinute.v % 10;\n                    if (lwReste11 > 0) {\n                        ftcFrames += this.MpcFramesMinute60;\n                        ftcFrames += (lwReste11 - 1) * this.MpcFramesMinute60Drop;\n                        ftcFrames -= 4;\n                    }\n\n                    ftcFrames += lSecond.v * this.MpcFramesSecond60;\n                    ftcFrames += lFrame.v;\n                } else {\n                    ftcFrames = this.timeCode2NdfFrame(sTimeCode, 60);\n                }\n            } else if ((dRate === this.MpcStFrameRate60 && dScale === this.MpcStScale60) || (dRate * this.MpcStScale60 === dScale * this.MpcStFrameRate60)) {\n                ftcFrames = this.timeCode2NdfFrame(sTimeCode, frameRate);\n            }\n            return ftcFrames;\n        },\n        /**\n         * 时间字符串转帧\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {string}     sTimeCode     时码\n         * @param   {number}     dFrameRate    帧率\n         * @param   {boolean}    dropFrame     是否是丢帧\n         * @return  {number}                   帧\n         */\n        timeCode2Frame: function (sTimeCode, dFrameRate, dropFrame) {\n            if (!dropFrame) {\n                dropFrame = this.getRateDropFrame(dFrameRate);\n            }\n            //sTimeCode = FormatTimeCodeString(sTimeCode, dFrameRate, GetRateDropFrame(dFrameRate));\n            sTimeCode = this.formatTimeCodeString$1(sTimeCode, dFrameRate, dropFrame);\n\n\n            var dRate = { v: this.MpcStFrameRate25 };\n            var dScale = { v: this.MpcStScale25 };\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\n\n            var ftcFrames = this.timeCode2Frame$1(sTimeCode, dFrameRate, dRate.v, dScale.v, dropFrame);\n\n            var newTc = this.frame2Tc(ftcFrames, dFrameRate, dropFrame);\n\n\n            newTc = this.replaceAll(newTc, \"\\\\.\", \":\");\n            sTimeCode = this.replaceAll(sTimeCode, \"\\\\.\", \":\");\n\n\n            if (newTc !== sTimeCode) {\n                ftcFrames = this.getFrameByTimeCode(sTimeCode, ftcFrames, true, 1, dFrameRate, dropFrame);\n            }\n\n            return ftcFrames;\n        },\n        /**\n         * 获取帧率和修正值\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}            dFrameRate    输入帧率\n         * @param   {System.Double&}    dRate         修正帧率\n         * @param   {System.Double&}    dScale        修正值\n         * @return  {void}                            \n         */\n        frameRate2RateAndScale: function (dFrameRate, dRate, dScale) {\n            if (Math.abs(dFrameRate - 25.0) < 0.01) {\n                dRate.v = this.MpcStFrameRate25;\n                dScale.v = this.MpcStScale25;\n            } else if (Math.abs(dFrameRate - 29.970029970029969) < 0.01) {\n                dRate.v = this.MpcStFrameRate2997;\n                dScale.v = this.MpcStScale2997;\n            } else if (Math.abs(dFrameRate - 30.0) < 0.01) {\n                dRate.v = this.MpcStFrameRate30;\n                dScale.v = this.MpcStScale30;\n            } else if (Math.abs(dFrameRate - 24.0) < 0.01) {\n                dRate.v = this.MpcStFrameRate24;\n                dScale.v = this.MpcStScale24;\n            } else if (Math.abs(dFrameRate - 23.976023976023978) < 0.01) {\n                dRate.v = this.MpcStFrameRate2398;\n                dScale.v = this.MpcStScale2398;\n            } else if (Math.abs(dFrameRate - 50.0) < 0.01) {\n                dRate.v = this.MpcStFrameRate50;\n                dScale.v = this.MpcStScale50;\n            } else if (Math.abs(dFrameRate - 59.940059940059939) < 0.01) {\n                dRate.v = this.MpcStFrameRate5994;\n                dScale.v = this.MpcStScale5994;\n            } else if (Math.abs(dFrameRate - 60.0) < 0.01) {\n                dRate.v = this.MpcStFrameRate60;\n                dScale.v = this.MpcStScale60;\n            }\n        },\n        /**\n         * 百纳秒转帧\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}    l100Ns    百纳秒\n         * @param   {number}    dRate     帧率\n         * @param   {number}    dScale    修正值\n         * @return  {number}              帧\n         */\n        l100Ns2Frame$1: function (l100Ns, dRate, dScale) {\n            var dFrameRate = { v: this.MpcStFrameRate25 };\n            var dFrameScale = { v: this.MpcStScale25 };\n            this.rate2ScaleFrameRateAndFrameScale(dRate, dScale, dFrameRate, dFrameScale);\n\n            return Math.floor(l100Ns / Math.pow(10.0, 7) * dFrameRate.v / dFrameScale.v + 0.5);\n        },\n        /**\n         * 百纳秒转帧\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}    l100Ns        百纳秒\n         * @param   {number}    dFrameRate    帧率\n         * @return  {number}                  帧\n         */\n        l100Ns2Frame: function (l100Ns, dFrameRate) {\n            var dRate = { v: this.MpcStFrameRate25 };\n            var dScale = { v: this.MpcStScale25 };\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\n\n            return Math.floor(l100Ns * dRate.v / dScale.v / Math.pow(10.0, 7) + 0.5);\n        },\n        /**\n         * 百纳秒转帧\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}    l100Ns        百纳秒\n         * @param   {number}    dFrameRate    帧率\n         * @return  {number}                  帧\n         */\n        l100Ns2FrameNoCorrecte: function (l100Ns, dFrameRate) {\n            var dRate = { v: this.MpcStFrameRate25 };\n            var dScale = { v: this.MpcStScale25 };\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\n\n            return Math.floor(l100Ns * dRate.v / dScale.v / Math.pow(10.0, 7));\n        },\n        /**\n         * 百纳秒转秒\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}    l100Ns        百纳秒\n         * @return  {number}                  秒\n         */\n        l100Ns2Second: function (l100Ns) {\n            return l100Ns / 10000000;\n        },\n        /**\n         * 百纳秒转时码\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}     l100Ns       百纳秒\n         * @param   {boolean}    dropFrame    是否是丢帧\n         * @return  {string}                  时码字符串\n         */\n        l100Ns2Tc: function (l100Ns, dropFrame) {\n            var dHour = (Math.floor(l100Ns / (3600 * Math.pow(10.0, 7))));\n            var llResidue = (l100Ns % (3600 * Math.pow(10.0, 7)));\n            var dMin = (Math.floor(llResidue / (60 * Math.pow(10.0, 7))));\n            llResidue = llResidue % parseInt((Math.floor(60 * Math.pow(10.0, 7))));\n            var dSec = (Math.floor(llResidue / (Math.pow(10.0, 7))));\n            llResidue = llResidue % parseInt((Math.pow(10.0, 7)));\n            var dFraction = (Math.floor(llResidue / (100000)));\n            return this.formatTimeCodeString(dHour, dMin, dSec, dFraction, dropFrame);\n        },\n        /**\n         * 百纳秒转时码\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}     l100Ns        百纳秒\n         * @param   {number}     dFrameRate    帧率\n         * @param   {boolean}    dropFrame     \n         * @return  {string}                   时码字符串\n         */\n        l100Ns2Tc$1: function (l100Ns, dFrameRate, dropFrame) {\n            if (!dropFrame) {\n                dropFrame = this.getRateDropFrame(dFrameRate);\n            }\n            return this.frame2Tc(this.l100Ns2Frame(l100Ns, dFrameRate), dFrameRate, dropFrame);\n        },\n        /**\n         * 帧率修正\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}            dRate          帧率\n         * @param   {number}            dScale         修正值\n         * @param   {System.Double&}    dFrameRate     输出帧率\n         * @param   {System.Double&}    dFrameScale    输出修正值\n         * @return  {void}                             \n         */\n        rate2ScaleFrameRateAndFrameScale: function (dRate, dScale, dFrameRate, dFrameScale) {\n            if ((dRate === this.MpcStFrameRate25 && dScale === this.MpcStScale25) || (dRate * this.MpcStScale25 === dScale * this.MpcStFrameRate25)) {\n                dFrameRate.v = this.MpcStFrameRate25;\n                dFrameScale.v = MpcStScale25;\n            } else if ((dRate === this.MpcStFrameRate2997 && dScale === this.MpcStScale2997) || (dRate * this.MpcStScale2997 === dScale * this.MpcStFrameRate2997)) {\n                dFrameRate.v = this.MpcStFrameRate2997;\n                dFrameScale.v = this.MpcStScale2997;\n            } else if ((dRate === this.MpcStFrameRate30 && dScale === this.MpcStScale30) || (dRate * this.MpcStScale30 === dScale * this.MpcStFrameRate30)) {\n                dFrameRate.v = this.MpcStFrameRate30;\n                dFrameScale.v = this.MpcStScale30;\n            } else if ((dRate === this.MpcStFrameRate24 && dScale === this.MpcStScale24) || (dRate * this.MpcStScale24 === dScale * this.MpcStFrameRate24)) {\n                dFrameRate.v = this.MpcStFrameRate24;\n                dFrameScale.v = this.MpcStScale24;\n            } else if ((dRate === this.MpcStFrameRate2398 && dScale === this.MpcStScale2398) || (dRate * this.MpcStScale2398 === dScale * this.MpcStFrameRate2398)) {\n                dFrameRate.v = this.MpcStFrameRate2398;\n                dFrameScale.v = this.MpcStScale2398;\n            } else if ((dRate === this.MpcStFrameRate50 && dScale === this.MpcStScale50) || (dRate * this.MpcStScale50 === dScale * this.MpcStFrameRate50)) {\n                dFrameRate.v = this.MpcStFrameRate50;\n                dFrameScale.v = this.MpcStScale50;\n            } else if ((dRate === this.MpcStFrameRate5994 && dScale === this.MpcStScale5994) || (dRate * this.MpcStScale5994 === dScale * this.MpcStFrameRate5994)) {\n                dFrameRate.v = this.MpcStFrameRate5994;\n                dFrameScale.v = this.MpcStScale5994;\n            } else if ((dRate === this.MpcStFrameRate60 && dScale === this.MpcStScale60) || (dRate * this.MpcStScale60 === dScale * this.MpcStFrameRate60)) {\n                dFrameRate.v = this.MpcStFrameRate60;\n                dFrameScale.v = this.MpcStScale60;\n            }\n        },\n        /**\n         * 秒转帧\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}    dbSec     秒数\n         * @param   {number}    dRate     帧率\n         * @param   {number}    dScale    修正值\n         * @return  {number}              帧数\n         */\n        second2Frame$1: function (dbSec, dRate, dScale) {\n            //dbSec = dbSec * Math.pow(10.0, 7);\n            dbSec = parseInt(dbSec.toFixed(7).replace('.', ''));\n            var dFrameRate = { v: this.MpcStFrameRate25 };\n            var dFrameScale = { v: this.MpcStScale25 };\n            this.rate2ScaleFrameRateAndFrameScale(dRate, dScale, dFrameRate, dFrameScale);\n\n            //return (long)( dbSec * dRate / dScale );\n            return Math.floor(dbSec * dFrameRate.v / dFrameScale.v / Math.pow(10.0, 7) + 0.5);\n        },\n        /**\n         * 秒转帧\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}    dbSec         秒数\n         * @param   {number}    dFrameRate    帧率\n         * @return  {number}                  帧数\n         */\n        second2Frame: function (dbSec, dFrameRate) {\n            //超过24的归零\n            if (dbSec >= 86400) {\n                dbSec = dbSec - 86400;\n\n                return this.second2Frame(dbSec, dFrameRate);\n            }\n\n            //dbSec = dbSec * Math.pow(10.0, 7);\n            dbSec = parseInt(dbSec.toFixed(7).replace('.', ''));\n            var dRate = { v: this.MpcStFrameRate25 };\n            var dScale = { v: this.MpcStScale25 };\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\n\n            //return (long)( dbSec * dRate / dScale );\n            //return parseInt((Math.floor(dbSec * dRate.v / dScale.v / Math.pow(10.0, 7))));\n            return Math.floor(dbSec * dRate.v / dScale.v / Math.pow(10.0, 7) + 0.5);\n        },\n        /**\n         * 秒转帧不加0.5修正,适用于真实的点\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}    dbSec         秒数\n         * @param   {number}    dFrameRate    帧率\n         * @return  {number}                  帧数\n         */\n        second2FrameNoCorrecte: function (dbSec, dFrameRate) {\n            //超过24的归零\n            if (dbSec >= 86400) {\n                dbSec = dbSec - 86400;\n\n                return this.second2FrameNoCorrecte(dbSec, dFrameRate);\n            }\n\n            //dbSec = dbSec * Math.pow(10.0, 7);\n            dbSec = parseInt(dbSec.toFixed(7).replace('.', ''));\n            var dRate = { v: this.MpcStFrameRate25 };\n            var dScale = { v: this.MpcStScale25 };\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\n\n            //return (long)( dbSec * dRate / dScale );\n            //return parseInt((Math.floor(dbSec * dRate.v / dScale.v / Math.pow(10.0, 7))));\n            return Math.floor(dbSec * dRate.v / dScale.v / Math.pow(10.0, 7));\n        },\n        /**\n         * 格式化时码字符串\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}     hours        小时数\n         * @param   {number}     minutes      分指数\n         * @param   {number}     seconds      秒数\n         * @param   {number}     frames       帧数\n         * @param   {boolean}    dropFrame    是否是丢帧\n         * @return  {string}                  格式化后的时码字符串\n         */\n        formatTimeCodeString: function (hours, minutes, seconds, frames, dropFrame) {\n            hours = hours >= 24 ? hours - 24 : hours;\n            minutes = minutes >= 60 ? minutes - 60 : minutes;\n            seconds = seconds >= 60 ? seconds - 60 : seconds;\n\n            var framesSeparator = \".\";\n            if (!dropFrame) {\n                framesSeparator = \":\";\n            }\n\n            hours = parseInt((Math.floor(hours % 24.0)));\n            function wrap(n) {\n                return ((n < 10) ? '0' + n : n);\n            }\n            //return string.format(\"{0:D2}:{1:D2}{4}{2:D2}:{3:D2}\", hours, minutes, seconds, frames, framesSeparator);\n            var smtp = (wrap(hours) + ':' + wrap(minutes) + framesSeparator + wrap(seconds) + ':' + wrap(frames));\n            return smtp;\n        },\n        formatTimeCodeString$1: function (timeCode, dFrameRate, dropFrame) {\n            if (timeCode) {\n                var hours = 0;\n                var minutes = 0;\n                var seconds = 0;\n                var frames = 0;\n                var ftcs = timeCode.split(\":\");\n                hours = parseInt(ftcs[0]);\n                if (ftcs.length >= 4) {\n                    minutes = parseInt(ftcs[1]);\n                    seconds = parseInt(ftcs[2]);\n\n                    if (parseInt(ftcs[3]) >= dFrameRate) {\n\n                        var showframeRate = Math.ceil(dFrameRate) - 1;\n                        ftcs[3] = showframeRate.toString();\n                    } else {\n\n                        //验证是否是丢帧时码 \n                        if (dropFrame) {\n                            //如果是丢帧帧率 分钟 除开 00 10 20 30 40 50 外所有的 00 01帧不存在 强制设置为02帧 5994强制设置为04帧\n                            var dropM = [\"00\", \"10\", \"20\", \"30\", \"40\", \"50\"];\n                            var drop5994F = [\"00\", \"01\", \"02\", \"03\"];\n                            var dropF = [\"00\", \"01\"];\n\n                            if (ftcs[2] === \"00\" && !dropM.contains(ftcs[1]) && drop5994F.contains(ftcs[3])) {\n                                if (60.0 - dFrameRate < 0.1) {\n                                    ftcs[3] = \"04\";\n                                } else {\n                                    if (dropF.contains(ftcs[3])) {\n                                        ftcs[3] = \"02\";\n                                    }\n                                }\n                            }\n                        }\n                    }\n                    frames = parseInt(ftcs[3]);\n                } else {\n                    var ftcssf = ftcs[2].split(\".\");\n                    minutes = parseInt(ftcs[1]);\n                    seconds = parseInt(ftcssf[0]);\n\n                    if (parseInt(ftcssf[1]) >= dFrameRate) {\n                        var showframeRate1 = Math.ceil(dFrameRate) - 1;\n                        ftcssf[1] = showframeRate1.toString();\n                    } else {\n\n                        //验证是否是丢帧时码 \n                        if (dropFrame) {\n                            //如果是丢帧帧率 分钟 除开 00 10 20 30 40 50 外所有的 00 01帧不存在 强制设置为02帧\n                            var dropM1 = [\"00\", \"10\", \"20\", \"30\", \"40\", \"50\"];\n                            var drop5994F1 = [\"00\", \"01\", \"02\", \"03\"];\n                            var dropF1 = [\"00\", \"01\"];\n\n                            if (ftcssf[0] === \"00\" && !dropM1.contains(ftcs[1]) && drop5994F1.contains(ftcssf[1])) {\n                                if (60.0 - dFrameRate < 0.1) {\n                                    ftcssf[1] = \"04\";\n                                } else {\n                                    if (dropF1.contains(ftcssf[1])) {\n                                        ftcssf[1] = \"02\";\n                                    }\n                                }\n                            }\n                        }\n\n                    }\n                    frames = parseInt(ftcssf[1]);\n                }\n\n                return this.formatTimeCodeString(hours, minutes, seconds, frames, dropFrame);\n            }\n            return \"--:--:--:--\";\n        },\n        /**\n         * 递归解决时码丢帧的问题\n         *\n         * @static\n         * @private\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {string}     sTimeCode     \n         * @param   {number}     ftcFrames     \n         * @param   {boolean}    isAdded       是否加修正值 为false的时候 为减了修正值\n         * @param   {number}     corrValue     修正值\n         * @param   {number}     dFrameRate    \n         * @param   {boolean}    dropFrame     \n         * @return  {number}                   \n         */\n        getFrameByTimeCode: function (sTimeCode, ftcFrames, isAdded, corrValue, dFrameRate, dropFrame) {\n            var ftcNewFrames = 0;\n            if (isAdded) {\n                ftcNewFrames = ftcFrames + corrValue;\n            } else {\n                ftcNewFrames = ftcFrames - corrValue;\n                corrValue++;\n            }\n            var newTc = this.frame2Tc(ftcNewFrames, dFrameRate, dropFrame);\n\n\n            newTc = this.replaceAll(newTc, \".\", \":\");\n            sTimeCode = this.replaceAll(sTimeCode, \".\", \":\");\n\n\n            if (newTc !== sTimeCode) {\n                return this.getFrameByTimeCode(sTimeCode, ftcFrames, !isAdded, corrValue, dFrameRate, dropFrame);\n            }\n\n            return ftcNewFrames;\n        },\n        /**\n         * 获取此帧率是否丢帧\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}     rate    帧率\n         * @return  {boolean}            \n         */\n        getRateDropFrame: function (rate) {\n            if (rate === 23.98 || (rate < 24 && rate > 23)) {\n                return true;\n            } else if (rate === 24.0) {\n                return false;\n            } else if (rate === 25.0) {\n                return false;\n            } else if (rate === 29.97 || (rate < 30 && rate > 29)) {\n                return true;\n            } else if (rate === 30.0) {\n                return false;\n            } else if (rate === 50.0) {\n                return false;\n            } else if (rate === 59.94 || (rate < 60 && rate > 59)) {\n                return true;\n            } else if (rate === 60.0) {\n                return false;\n            }\n            return false;\n        },\n        /**\n         * 时间字符串转秒(未考虑丢帧的情况)\n         *\n         * @static\n         * @private\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {string}    sTimeCode     \n         * @param   {number}    dFrameRate    \n         * @return  {number}                  帧数\n         */\n        timeCode2NdfFrame: function (sTimeCode, dFrameRate) {\n            var ftcSeconds = 0;\n            var ftcFrames = 0;\n\n            var lHour = { v: 0 };\n            var lMinute = { v: 0 };\n            var lSecond = { v: 0 };\n            var lFrame = { v: 0 };\n\n            this.timeCode2Format(sTimeCode, lHour, lMinute, lSecond, lFrame, dFrameRate);\n\n            ftcSeconds += lHour.v * 60 * 60;\n            ftcSeconds += lMinute.v * 60;\n            ftcSeconds += lSecond.v;\n            ftcFrames += lFrame.v;\n            //这里以前有bug，因为second2Frame返回的是字符串，不转成int会变成字符串拼接了。。\n            ftcFrames += parseInt(this.second2Frame(ftcSeconds, dFrameRate));\n\n            return ftcFrames;\n        },\n        /**\n         * 时间字符串格式化\n         *\n         * @static\n         * @private\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {string}           sTimeCode     \n         * @param   {System.Int64&}    lHour         \n         * @param   {System.Int64&}    lMinute       \n         * @param   {System.Int64&}    lSecond       \n         * @param   {System.Int64&}    lFrame        \n         * @param   {number}           dFrameRate    \n         * @return  {void}                           帧数\n         */\n        timeCode2Format: function (sTimeCode, lHour, lMinute, lSecond, lFrame, dFrameRate) {\n            var ftcCodes = sTimeCode.split(\":\");\n\n            if (ftcCodes.length >= 4) {\n                lHour.v = parseInt(ftcCodes[0]);\n                lMinute.v = parseInt(ftcCodes[1]);\n                lSecond.v = parseInt(ftcCodes[2]);\n                lFrame.v = parseInt(ftcCodes[3]);\n            } else {\n                var ftcssf = ftcCodes[1].split(\".\");\n                lHour.v = parseInt(ftcCodes[0]);\n                lMinute.v = parseInt(ftcssf[0]);\n                lSecond.v = parseInt(ftcssf[1]);\n                lFrame.v = parseInt(ftcCodes[2]);\n            }\n\n            lHour.v = lHour.v >= 24 ? lHour.v - 24 : lHour.v;\n            lMinute.v = lMinute.v >= 60 ? lMinute.v - 60 : lMinute.v;\n            lSecond.v = lSecond.v >= 60 ? lSecond.v - 60 : lSecond.v;\n            lFrame.v = lFrame.v >= Math.ceil(dFrameRate) ? lFrame.v - parseInt(Math.ceil(dFrameRate)) : lFrame.v;\n        },\n\n        //音频时间格式转换  2016-04-10 created by wangyu\n        SecondToTimeString_audio: function (seconds, opts) {\n            var hours = Math.floor(seconds / 3600);\n            var minutes = Math.floor((seconds - 3600 * hours) / 60);\n            var iseconds = Math.floor(seconds - (60 * minutes) - (3600 * hours));\n            var i10Milliseconds = Math.floor((seconds * 100 - Math.floor(seconds) * 100));\n\n            if (i10Milliseconds >= 100) {\n                iseconds++;\n                i10Milliseconds = i10Milliseconds - 100;\n            }\n\n            var hoursStr = hours < 10 ? \"0\" + hours : hours.toString();\n            var minutesStr = minutes < 10 ? \"0\" + minutes : minutes.toString();\n            var isecondsStr = iseconds < 10 ? \"0\" + iseconds : iseconds.toString();\n            var i10MillisecondsStr = i10Milliseconds < 10 ? \"0\" + i10Milliseconds : i10Milliseconds.toString();\n            return hoursStr + \":\" + minutesStr + \":\" + isecondsStr + (opts === 'exactTo_s' ? \"\" : (\":\" + i10MillisecondsStr));\n        },\n        //音频时码转百纳秒\n        timeCode2L100Ns_audio: function (sTimeCode) {\n            var ftcCodes = sTimeCode.split(\":\");\n            var lHour = { v: 0 };\n            var lMinute = { v: 0 };\n            var lSecond = { v: 0 };\n            var I10Milliseconds = { v: 0 };\n\n            lHour.v = parseInt(ftcCodes[0]);\n            lMinute.v = parseInt(ftcCodes[1]);\n            lSecond.v = parseInt(ftcCodes[2]);\n            if (ftcCodes[3] != undefined)\n                I10Milliseconds.v = parseInt(ftcCodes[3]);\n\n            var l100ns = 0;\n            l100ns += lHour.v * 60 * 60;\n            l100ns += lMinute.v * 60;\n            l100ns += lSecond.v\n            l100ns += I10Milliseconds.v / 100;\n            l100ns = parseInt((l100ns * Math.pow(10.0, 7)).toFixed(0));\n\n            return l100ns;\n        },\n\n        replaceAll: function (str, s1, s2) {\n            return str.replaceAll(s1, s2);\n        },\n        div: function (a, b) {\n            return this.hasValue(a) && this.hasValue(b) ? parseInt(a / b) : null;\n        },\n        hasValue: function (obj) {\n            return (obj !== null) && (obj !== undefined);\n        },\n\n        second2L100Ns: function (seconds) {\n            // 直接 *Math.pow(10,7)有精度问题，例如：314.28*10\n            return parseInt((seconds).toFixed(7).replace('.', ''), 10)\n        }\n    };\n    window.timecodeconvert = new timeCodeConvertHelper();\n    return new timeCodeConvertHelper();\n})();\nwindow.TimeCodeConvert = TimeCodeConvert;\n\n/***/ })\n/******/ ]);\n\n\n// WEBPACK FOOTER //\n// mam-timecode-convert.min.js", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, {\n \t\t\t\tconfigurable: false,\n \t\t\t\tenumerable: true,\n \t\t\t\tget: getter\n \t\t\t});\n \t\t}\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 0);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap 768fc95fb880478ca43a", "/*!\n * TimeCode Convert v2.0.1 (http://www.sobey.com)\n * Copyright 2010-2016 Sobey, Inc. CDC\n * Licensed under the MIT license\n * Author: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)\n * Convert From C++(Sobey MPC) ->C#(Sobey MAM) -> Javascript\n */\n\n/**\n * Array Extended contains\n *\n */\nArray.prototype.S = String.fromCharCode(2);\nArray.prototype.contains = function (e) {\n    var r = new RegExp(this.S + e + this.S);\n    return (r.test(this.S + this.join(this.S) + this.S));\n};\n/**\n * String  Extended replaceAll\n *\n */\nString.prototype.replaceAll = function (s1, s2) {\n    return this.replace(new RegExp(s1, \"gm\"), s2);\n};\n\nvar TimeCodeConvert = (function () {\n    /** @namespace TimeCodeConvert */\n    /**\n     * 时码转换帮助类\n     *\n     * @public\n     * @class TimeCodeConvert.TimeCodeConvertHelper\n     */\n    var timeCodeConvertHelper = function () {\n    };\n\n    timeCodeConvertHelper.prototype = {\n        /**\n        * 桌面视频制式标准枚举定义\n        *\n        * @class TimeCodeConvert.MpcVideoStandard\n        */\n        MpcVideoStandard: {\n            mpcVideostandardUnknow: 0,\n            mpcVideostandardPal: 1,\n            mpcVideostandardNtsc2997: 2,\n            mpcVideostandardNtsc30: 4,\n            mpcVideostandardSecam: 8,\n            mpcVideostandard1920108050I: 16,\n            mpcVideostandard192010805994I: 32,\n            mpcVideostandard1920108060I: 64,\n            mpcVideostandard192010802398P: 128,\n            mpcVideostandard1920108024P: 256,\n            mpcVideostandard1920108025P: 512,\n            mpcVideostandard192010802997P: 1024,\n            mpcVideostandard1920108030P: 2048,\n            mpcVideostandard12807202398P: 4096,\n            mpcVideostandard128072024P: 8192,\n            mpcVideostandard128072050P: 16384,\n            mpcVideostandard12807205994P: 32768,\n            mpcVideostandard1440108050I: 65536,\n            mpcVideostandard144010805994I: 131072,\n            mpcVideostandard1440108060I: 262144\n        },\n\n        /**\n         * PAL field frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @default 50\n         * @type number\n         */\n        m_mpcStRate25: 50,\n        /**\n         * PAL frame  frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 25\n         * @type number\n         */\n        MpcStFrameRate25: 25,\n        /**\n         * PAL scale\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1\n         * @type number\n         */\n        MpcStScale25: 1,\n        /**\n         * NTSC field frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @default 60000\n         * @type number\n         */\n        m_mpcStRate2997: 60000,\n        /**\n         * NTSC frame  frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 30000\n         * @type number\n         */\n        MpcStFrameRate2997: 30000,\n        /**\n         * NTSC scale\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1001\n         * @type number\n         */\n        MpcStScale2997: 1001,\n        /**\n         * 30-F field frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @default 60\n         * @type number\n         */\n        m_mpcStRate30: 60,\n        /**\n         * 30-F frame frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 30\n         * @type number\n         */\n        MpcStFrameRate30: 30,\n        /**\n         * 30-F scale\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1\n         * @type number\n         */\n        MpcStScale30: 1,\n        /**\n         * 24-F field frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @default 48\n         * @type number\n         */\n        m_mpcStRate24: 48,\n        /**\n         * 24-F field frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 24\n         * @type number\n         */\n        MpcStFrameRate24: 24,\n        /**\n         * 24-F scale\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1\n         * @type number\n         */\n        MpcStScale24: 1,\n        /**\n         * 2398-F field frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @default 48000\n         * @type number\n         */\n        m_mpcStRate2398: 48000,\n        /**\n         * 2398-F frame frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 24000\n         * @type number\n         */\n        MpcStFrameRate2398: 24000,\n        /**\n         * 2398-F scale\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1001\n         * @type number\n         */\n        MpcStScale2398: 1001,\n        /**\n         * PAL field frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @default 50\n         * @type number\n         */\n        m_mpcStRate50: 50,\n        /**\n         * PAL frame  frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 50\n         * @type number\n         */\n        MpcStFrameRate50: 50,\n        /**\n         * PAL scale\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1\n         * @type number\n         */\n        MpcStScale50: 1,\n        /**\n         * NTSC field frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @default 60000\n         * @type number\n         */\n        m_mpcStRate5994: 60000,\n        /**\n         * NTSC frame  frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 60000\n         * @type number\n         */\n        MpcStFrameRate5994: 60000,\n        /**\n         * NTSC scale\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1001\n         * @type number\n         */\n        MpcStScale5994: 1001,\n        /**\n         * 60-F field frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @default 60\n         * @type number\n         */\n        m_mpcStRate60: 60,\n        /**\n         * 60-F frame frequency\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 60\n         * @type number\n         */\n        MpcStFrameRate60: 60,\n        /**\n         * 60-F scale\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1\n         * @type number\n         */\n        MpcStScale60: 1,\n        /**\n         * 25 Frame: frames per second\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 25\n         * @type number\n         */\n        MpcFramesSecond25: 25,\n        /**\n         * 25 Frame: frames per minute\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1500\n         * @type number\n         */\n        MpcFramesMinute25: 1500,\n        /**\n         * 25 Frame: frames per hour\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 90000\n         * @type number\n         */\n        MpcFramesHour25: 90000,\n        /**\n         * 30 DROP Frame: frames per minute\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1438\n         * @type number\n         */\n        MpcFramesMinute24Drop: 1438,\n        /**\n         * 30 DROP Frame: frames per 10 minutes\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 14382\n         * @type number\n         */\n        MpcFrames10Minutes24Drop: 14382,\n        /**\n         * 30 DROP Frame: frames per hour\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 86292\n         * @type number\n         */\n        MpcFramesHour24Drop: 86292,\n        /**\n         * 24 Frame: frames per second\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 24\n         * @type number\n         */\n        MpcFramesSecond24: 24,\n        /**\n         * 24 Frame: frames per minute\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1440\n         * @type number\n         */\n        MpcFramesMinute24: 1440,\n        /**\n         * 24 Frame: frames per hour\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 86400\n         * @type number\n         */\n        MpcFramesHour24: 86400,\n        /**\n         * 30 NO_DROP Frame: frames per second\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 30\n         * @type number\n         */\n        MpcFramesSecondNodrop30: 30,\n        /**\n         * 30 NO_DROP Frame: frames per minute\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1800\n         * @type number\n         */\n        MpcFramesMinuteNodrop30: 1800,\n        /**\n         * 30 NO_DROP Frame: frames per hour\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 108000\n         * @type number\n         */\n        MpcFramesHourNodrop30: 108000,\n        /**\n         * 30 DROP Frame: frames per minute\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 1798\n         * @type number\n         */\n        MpcFramesMinute30Drop: 1798,\n        /**\n         * 30 DROP Frame: frames per 10 minutes\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 17982\n         * @type number\n         */\n        MpcFrames10Minutes30Drop: 17982,\n        /**\n         * 30 DROP Frame: frames per hour\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 107892\n         * @type number\n         */\n        MpcFramesHour30Drop: 107892,\n        /**\n         * 50 Frame: frames per second\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 50\n         * @type number\n         */\n        MpcFramesSecond50: 50,\n        /**\n         * 50 Frame: frames per minute\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 3000\n         * @type number\n         */\n        MpcFramesMinute50: 3000,\n        /**\n         * 50 Frame: frames per hour\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 180000\n         * @type number\n         */\n        MpcFramesHour50: 180000,\n        /**\n         * 60 DROP Frame: frames per minute\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 3596\n         * @type number\n         */\n        MpcFramesMinute60Drop: 3596,\n        /**\n         * 60 DROP Frame: frames per 10 minutes\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 35964\n         * @type number\n         */\n        MpcFrames10Minutes60Drop: 35964,\n        /**\n         * 60 DROP Frame: frames per hour\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 215784\n         * @type number\n         */\n        MpcFramesHour60Drop: 215784,\n        /**\n         * 60 Frame: frames per second\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 60\n         * @type number\n         */\n        MpcFramesSecond60: 60,\n        /**\n         * 60 Frame: frames per minute\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 3600\n         * @type number\n         */\n        MpcFramesMinute60: 3600,\n        /**\n         * 60 Frame: frames per hour\n         *\n         * @static\n         * @private\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @constant\n         * @default 216000\n         * @type number\n         */\n        MpcFramesHour60: 216000,\n        /**\n         * 帧转百纳秒\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}    lFrame    帧\n         * @param   {number}    dRate     帧率\n         * @param   {number}    dScale    \n         * @return  {number}              百纳秒\n         */\n        frame2L100Ns$1: function (lFrame, dRate, dScale) {\n            var dFrameRate = { v: this.MpcStFrameRate25 };\n            var dFrameScale = { v: this.MpcStScale25 };\n\n            this.rate2ScaleFrameRateAndFrameScale(dRate, dScale, dFrameRate, dFrameScale);\n\n            return parseInt((Math.floor(lFrame * Math.pow(10.0, 7) * dFrameRate.v / dFrameScale.v)));\n        },\n        /**\n         * 帧转百纳秒\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}    dFrame        帧\n         * @param   {number}    dFrameRate    帧率\n         * @return  {number}                  百纳秒\n         */\n        frame2L100Ns: function (dFrame, dFrameRate) {\n            var dRate = { v: this.MpcStFrameRate25 };\n            var dScale = { v: this.MpcStScale25 };\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\n\n            return parseInt((Math.floor(dFrame * dScale.v * Math.pow(10.0, 7) / dRate.v)));\n        },\n        /**\n         * 帧转秒\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}    lFrame    帧\n         * @param   {number}    dRate     帧率\n         * @param   {number}    dScale    \n         * @return  {number}              秒\n         */\n        frame2Second$1: function (lFrame, dRate, dScale) {\n            var dFrameRate = { v: this.MpcStFrameRate25 };\n            var dFrameScale = { v: this.MpcStScale25 };\n            this.rate2ScaleFrameRateAndFrameScale(dRate, dScale, dFrameRate, dFrameScale);\n\n            return (lFrame * dFrameScale.v / dFrameRate.v);\n        },\n        /**\n         * 帧转秒\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}    dFrame        帧\n         * @param   {number}    dFrameRate    帧率\n         * @return  {number}                  秒\n         */\n        frame2Second: function (dFrame, dFrameRate) {\n            var dRate = { v: this.MpcStFrameRate25 };\n            var dScale = { v: this.MpcStScale25 };\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\n\n            return (dFrame * dScale.v / dRate.v);\n        },\n        /**\n         * 帧转时码\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}     dFrame       帧\n         * @param   {number}     dRate        帧率\n         * @param   {number}     dScale       \n         * @param   {boolean}    dropFrame    \n         * @return  {string}                  时码字符串\n         */\n        frame2Tc$1: function (dFrame, dRate, dScale, dropFrame) {\n            if (!dropFrame) {\n                dropFrame = this.getRateDropFrame(dRate);\n            }\n            var strTc = \"\";\n            if ((dRate === this.MpcStFrameRate25 && dScale === this.MpcStScale25) || (dRate * this.MpcStScale25 === dScale * this.MpcStFrameRate25)) {\n                var dHour = parseInt((Math.floor(dFrame / this.MpcFramesHour25)));\n                var dResidue = parseInt((Math.floor(dFrame % this.MpcFramesHour25)));\n                var dMin = parseInt((Math.floor(dResidue / this.MpcFramesMinute25)));\n                dResidue = dResidue % this.MpcFramesMinute25;\n                var dSec = parseInt((Math.floor(dResidue / this.MpcFramesSecond25)));\n                var dFra = parseInt((Math.floor(dResidue % this.MpcFramesSecond25)));\n                strTc = this.formatTimeCodeString(dHour, dMin, dSec, dFra, false);\n            } else if ((dRate === this.MpcStFrameRate2997 && dScale === this.MpcStScale2997) || (dRate * this.MpcStScale2997 === dScale * this.MpcStFrameRate2997)) {\n                if (dropFrame) {\n                    var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHour30Drop)));\n                    var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHour30Drop)));\n                    var dMin1 = parseInt((Math.floor(10 * Math.floor(this.div(dResidue1, this.MpcFrames10Minutes30Drop)))));\n                    dResidue1 = dResidue1 % this.MpcFrames10Minutes30Drop;\n                    if (dResidue1 >= this.MpcFramesMinuteNodrop30) {\n                        dResidue1 -= this.MpcFramesMinuteNodrop30;\n                        dMin1 += 1 + parseInt(Math.floor(this.div(dResidue1, this.MpcFramesMinute30Drop)));\n                        dResidue1 %= this.MpcFramesMinute30Drop;\n                        dResidue1 += 2;\n                    }\n                    var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecondNodrop30)));\n                    var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecondNodrop30)));\n                    strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, true);\n                } else {\n                    var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHourNodrop30)));\n                    var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHourNodrop30)));\n                    var dMin11 = parseInt((Math.floor(dResidue11 / this.MpcFramesMinuteNodrop30)));\n                    dResidue11 = dResidue11 % this.MpcFramesMinuteNodrop30;\n                    var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecondNodrop30)));\n                    var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecondNodrop30)));\n                    strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, false);\n                }\n            } else if ((dRate === this.MpcStFrameRate30 && dScale === this.MpcStScale30) || (dRate * this.MpcStScale30 === dScale * this.MpcStFrameRate30)) {\n                var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHourNodrop30)));\n                var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHourNodrop30)));\n                var dMin1 = parseInt((Math.floor(dResidue1 / this.MpcFramesMinuteNodrop30)));\n                dResidue1 = dResidue1 % this.MpcFramesMinuteNodrop30;\n                var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecondNodrop30)));\n                var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecondNodrop30)));\n                strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, false);\n            } else if ((dRate === this.MpcStFrameRate24 && dScale === this.MpcStScale24) || (dRate * this.MpcStScale24 === dScale * this.MpcStFrameRate24)) {\n                var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHour24)));\n                var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHour24)));\n                var dMin11 = parseInt((Math.floor(dResidue11 / this.MpcFramesMinute24)));\n                dResidue11 = dResidue11 % this.MpcFramesMinute24;\n                var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecond24)));\n                var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecond24)));\n                strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, false);\n            } else if ((dRate === this.MpcStFrameRate2398 && dScale === this.MpcStScale2398) || (dRate * this.MpcStScale2398 === dScale * this.MpcStFrameRate2398)) {\n                if (dropFrame) {\n                    var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHour24Drop)));\n                    var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHour24Drop)));\n                    var dMin1 = parseInt((Math.floor(10 * (this.div(dResidue1, this.MpcFrames10Minutes24Drop)))));\n                    dResidue1 = dResidue1 % this.MpcFrames10Minutes24Drop;\n                    if (dResidue1 >= this.MpcFramesMinute24) {\n                        dResidue1 -= this.MpcFramesMinute24;\n                        dMin1 += 1 + parseInt(Math.floor(this.div(dResidue1, this.MpcFramesMinute24Drop)));\n                        dResidue1 %= this.MpcFramesMinute24Drop;\n                        dResidue1 += 2;\n                    }\n                    var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecond24)));\n                    var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecond24)));\n                    strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, true);\n                } else {\n                    var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHour24)));\n                    var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHour24)));\n                    var dMin11 = parseInt((Math.floor(dResidue11 / this.MpcFramesMinute24)));\n                    dResidue11 = dResidue11 % this.MpcFramesMinute24;\n                    var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecond24)));\n                    var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecond24)));\n                    strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, false);\n                }\n            } else if ((dRate === this.MpcStFrameRate50 && dScale === this.MpcStScale50) || (dRate * this.MpcStScale50 === dScale * this.MpcStFrameRate50)) {\n                var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHour50)));\n                var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHour50)));\n                var dMin1 = parseInt((Math.floor(dResidue1 / this.MpcFramesMinute50)));\n                dResidue1 = dResidue1 % this.MpcFramesMinute50;\n                var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecond50)));\n                var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecond50)));\n                strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, false);\n            } else if ((dRate === this.MpcStFrameRate5994 && dScale === this.MpcStScale5994) || (dRate * this.MpcStScale5994 === dScale * this.MpcStFrameRate5994)) {\n                if (dropFrame) {\n                    var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHour60Drop)));\n                    var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHour60Drop)));\n                    var dMin11 = parseInt((Math.floor(10 * (this.div(dResidue11, this.MpcFrames10Minutes60Drop)))));\n                    dResidue11 = dResidue11 % this.MpcFrames10Minutes60Drop;\n                    if (dResidue11 >= this.MpcFramesMinute60) {\n                        dResidue11 -= this.MpcFramesMinute60;\n                        dMin11 += 1 + parseInt(Math.floor(this.div(dResidue11, this.MpcFramesMinute60Drop)));\n                        dResidue11 %= this.MpcFramesMinute60Drop;\n                        dResidue11 += 4;\n                    }\n                    var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecond60)));\n                    var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecond60)));\n                    strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, true);\n                } else {\n                    var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHour60)));\n                    var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHour60)));\n                    var dMin1 = parseInt((Math.floor(dResidue1 / this.MpcFramesMinute60)));\n                    dResidue1 = dResidue1 % this.MpcFramesMinute60;\n                    var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecond60)));\n                    var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecond60)));\n                    strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, false);\n                }\n            } else if ((dRate === this.MpcStFrameRate60 && dScale === this.MpcStScale60) || (dRate * this.MpcStScale60 === dScale * this.MpcStFrameRate60)) {\n                var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHour60)));\n                var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHour60)));\n                var dMin11 = parseInt((Math.floor(dResidue11 / this.MpcFramesMinute60)));\n                dResidue11 = dResidue11 % this.MpcFramesMinute60;\n                var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecond60)));\n                var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecond60)));\n                strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, false);\n            }\n            return strTc;\n        },\n        /**\n         * 帧转时码\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}     dFrame        帧\n         * @param   {number}     dFrameRate    帧率\n         * @param   {boolean}    dropFrame     是否丢帧\n         * @return  {string}                   时码字符串\n         */\n        frame2Tc: function (dFrame, dFrameRate, dropFrame) {\n            if (!dropFrame) {\n                dropFrame = this.getRateDropFrame(dFrameRate);\n            }\n            var dRate = { v: this.MpcStFrameRate25 };\n            var dScale = { v: this.MpcStScale25 };\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\n\n            var strTc = this.frame2Tc$1(dFrame, dRate.v, dScale.v, dropFrame);\n            return strTc;\n        },\n        /**\n         * 时码字符串转帧\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {string}     sTimeCode    时码\n         * @param   {number}     frameRate    帧率\n         * @param   {number}     dRate        \n         * @param   {number}     dScale       \n         * @param   {boolean}    dropFrame    是否是丢帧\n         * @return  {number}                  帧\n         */\n        timeCode2Frame$1: function (sTimeCode, frameRate, dRate, dScale, dropFrame) {\n            if (!dropFrame) {\n                dropFrame = this.getRateDropFrame(frameRate);\n            }\n            var ftcFrames = 0;\n            if ((dRate === this.MpcStFrameRate25 && dScale === this.MpcStScale25) || (dRate * this.MpcStScale25 === dScale * this.MpcStFrameRate25)) {\n                ftcFrames = this.timeCode2NdfFrame(sTimeCode, frameRate);\n            } else if ((dRate === this.MpcStFrameRate2997 && dScale === this.MpcStScale2997) || (dRate * this.MpcStScale2997 === dScale * this.MpcStFrameRate2997)) {\n                var lHour = { v: 0 };\n                var lMinute = { v: 0 };\n                var lSecond = { v: 0 };\n                var lFrame = { v: 0 };\n\n                this.timeCode2Format(sTimeCode, lHour, lMinute, lSecond, lFrame, frameRate);\n\n                if (dropFrame) {\n                    ftcFrames += lHour.v * this.MpcFramesHour30Drop;\n\n                    var lwReste = this.div(lMinute.v, 10);\n                    ftcFrames += lwReste * this.MpcFrames10Minutes30Drop;\n                    lwReste = lMinute.v % 10;\n\n                    if (lwReste > 0) {\n                        ftcFrames += this.MpcFramesMinuteNodrop30;\n                        ftcFrames += (lwReste - 1) * this.MpcFramesMinute30Drop;\n                        ftcFrames -= 2;\n                    }\n\n                    ftcFrames += lSecond.v * this.MpcFramesSecondNodrop30;\n                    ftcFrames += lFrame.v;\n                } else {\n                    ftcFrames = this.timeCode2NdfFrame(sTimeCode, 30);\n                }\n            } else if ((dRate === this.MpcStFrameRate30 && dScale === this.MpcStScale30) || (dRate * this.MpcStScale30 === dScale * this.MpcStFrameRate30)) {\n                ftcFrames = this.timeCode2NdfFrame(sTimeCode, frameRate);\n            } else if ((dRate === this.MpcStFrameRate24 && dScale === this.MpcStScale24) || (dRate * this.MpcStScale24 === dScale * this.MpcStFrameRate24)) {\n                ftcFrames = this.timeCode2NdfFrame(sTimeCode, frameRate);\n            } else if ((dRate === this.MpcStFrameRate2398 && dScale === this.MpcStScale2398) || (dRate * this.MpcStScale2398 === dScale * this.MpcStFrameRate2398)) {\n                var lHour1 = { v: 0 };\n                var lMinute1 = { v: 0 };\n                var lSecond1 = { v: 0 };\n                var lFrame1 = { v: 0 };\n\n                this.timeCode2Format(sTimeCode, lHour1, lMinute1, lSecond1, lFrame1, frameRate);\n\n                if (dropFrame) {\n                    ftcFrames += lHour.v * this.MpcFramesHour24;\n\n                    var lwReste1 = this.div(lMinute.v, 10);\n                    ftcFrames += lwReste1 * this.MpcFrames10Minutes24Drop;\n                    lwReste1 = lMinute.v % 10;\n                    if (lwReste1 > 0) {\n                        ftcFrames += this.MpcFramesMinute60;\n                        ftcFrames += (lwReste1 - 1) * this.MpcFramesMinute24;\n                        ftcFrames -= 2;\n                    }\n\n                    ftcFrames += lSecond.v * this.MpcFramesSecond24;\n                    ftcFrames += lFrame.v;\n                } else {\n                    ftcFrames = this.timeCode2NdfFrame(sTimeCode, 24);\n                }\n            } else if ((dRate === this.MpcStFrameRate50 && dScale === this.MpcStScale50) || (dRate * this.MpcStScale50 === dScale * this.MpcStFrameRate50)) {\n                ftcFrames = this.timeCode2NdfFrame(sTimeCode, frameRate);\n            } else if ((dRate === this.MpcStFrameRate5994 && dScale === this.MpcStScale5994) || (dRate * this.MpcStScale5994 === dScale * this.MpcStFrameRate5994)) {\n                var lHour11 = { v: 0 };\n                var lMinute11 = { v: 0 };\n                var lSecond11 = { v: 0 };\n                var lFrame11 = { v: 0 };\n\n                this.timeCode2Format(sTimeCode, lHour11, lMinute11, lSecond11, lFrame11, frameRate);\n                if (dropFrame) {\n                    ftcFrames += lHour.v * MpcFramesHour60Drop;\n\n                    var lwReste11 = this.div(lMinute.v, 10);\n                    ftcFrames += lwReste11 * this.MpcFrames10Minutes60Drop;\n                    lwReste11 = lMinute.v % 10;\n                    if (lwReste11 > 0) {\n                        ftcFrames += this.MpcFramesMinute60;\n                        ftcFrames += (lwReste11 - 1) * this.MpcFramesMinute60Drop;\n                        ftcFrames -= 4;\n                    }\n\n                    ftcFrames += lSecond.v * this.MpcFramesSecond60;\n                    ftcFrames += lFrame.v;\n                } else {\n                    ftcFrames = this.timeCode2NdfFrame(sTimeCode, 60);\n                }\n            } else if ((dRate === this.MpcStFrameRate60 && dScale === this.MpcStScale60) || (dRate * this.MpcStScale60 === dScale * this.MpcStFrameRate60)) {\n                ftcFrames = this.timeCode2NdfFrame(sTimeCode, frameRate);\n            }\n            return ftcFrames;\n        },\n        /**\n         * 时间字符串转帧\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {string}     sTimeCode     时码\n         * @param   {number}     dFrameRate    帧率\n         * @param   {boolean}    dropFrame     是否是丢帧\n         * @return  {number}                   帧\n         */\n        timeCode2Frame: function (sTimeCode, dFrameRate, dropFrame) {\n            if (!dropFrame) {\n                dropFrame = this.getRateDropFrame(dFrameRate);\n            }\n            //sTimeCode = FormatTimeCodeString(sTimeCode, dFrameRate, GetRateDropFrame(dFrameRate));\n            sTimeCode = this.formatTimeCodeString$1(sTimeCode, dFrameRate, dropFrame);\n\n\n            var dRate = { v: this.MpcStFrameRate25 };\n            var dScale = { v: this.MpcStScale25 };\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\n\n            var ftcFrames = this.timeCode2Frame$1(sTimeCode, dFrameRate, dRate.v, dScale.v, dropFrame);\n\n            var newTc = this.frame2Tc(ftcFrames, dFrameRate, dropFrame);\n\n\n            newTc = this.replaceAll(newTc, \"\\\\.\", \":\");\n            sTimeCode = this.replaceAll(sTimeCode, \"\\\\.\", \":\");\n\n\n            if (newTc !== sTimeCode) {\n                ftcFrames = this.getFrameByTimeCode(sTimeCode, ftcFrames, true, 1, dFrameRate, dropFrame);\n            }\n\n            return ftcFrames;\n        },\n        /**\n         * 获取帧率和修正值\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}            dFrameRate    输入帧率\n         * @param   {System.Double&}    dRate         修正帧率\n         * @param   {System.Double&}    dScale        修正值\n         * @return  {void}                            \n         */\n        frameRate2RateAndScale: function (dFrameRate, dRate, dScale) {\n            if (Math.abs(dFrameRate - 25.0) < 0.01) {\n                dRate.v = this.MpcStFrameRate25;\n                dScale.v = this.MpcStScale25;\n            } else if (Math.abs(dFrameRate - 29.970029970029969) < 0.01) {\n                dRate.v = this.MpcStFrameRate2997;\n                dScale.v = this.MpcStScale2997;\n            } else if (Math.abs(dFrameRate - 30.0) < 0.01) {\n                dRate.v = this.MpcStFrameRate30;\n                dScale.v = this.MpcStScale30;\n            } else if (Math.abs(dFrameRate - 24.0) < 0.01) {\n                dRate.v = this.MpcStFrameRate24;\n                dScale.v = this.MpcStScale24;\n            } else if (Math.abs(dFrameRate - 23.976023976023978) < 0.01) {\n                dRate.v = this.MpcStFrameRate2398;\n                dScale.v = this.MpcStScale2398;\n            } else if (Math.abs(dFrameRate - 50.0) < 0.01) {\n                dRate.v = this.MpcStFrameRate50;\n                dScale.v = this.MpcStScale50;\n            } else if (Math.abs(dFrameRate - 59.940059940059939) < 0.01) {\n                dRate.v = this.MpcStFrameRate5994;\n                dScale.v = this.MpcStScale5994;\n            } else if (Math.abs(dFrameRate - 60.0) < 0.01) {\n                dRate.v = this.MpcStFrameRate60;\n                dScale.v = this.MpcStScale60;\n            }\n        },\n        /**\n         * 百纳秒转帧\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}    l100Ns    百纳秒\n         * @param   {number}    dRate     帧率\n         * @param   {number}    dScale    修正值\n         * @return  {number}              帧\n         */\n        l100Ns2Frame$1: function (l100Ns, dRate, dScale) {\n            var dFrameRate = { v: this.MpcStFrameRate25 };\n            var dFrameScale = { v: this.MpcStScale25 };\n            this.rate2ScaleFrameRateAndFrameScale(dRate, dScale, dFrameRate, dFrameScale);\n\n            return Math.floor(l100Ns / Math.pow(10.0, 7) * dFrameRate.v / dFrameScale.v + 0.5);\n        },\n        /**\n         * 百纳秒转帧\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}    l100Ns        百纳秒\n         * @param   {number}    dFrameRate    帧率\n         * @return  {number}                  帧\n         */\n        l100Ns2Frame: function (l100Ns, dFrameRate) {\n            var dRate = { v: this.MpcStFrameRate25 };\n            var dScale = { v: this.MpcStScale25 };\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\n\n            return Math.floor(l100Ns * dRate.v / dScale.v / Math.pow(10.0, 7) + 0.5);\n        },\n        /**\n         * 百纳秒转帧\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}    l100Ns        百纳秒\n         * @param   {number}    dFrameRate    帧率\n         * @return  {number}                  帧\n         */\n        l100Ns2FrameNoCorrecte: function (l100Ns, dFrameRate) {\n            var dRate = { v: this.MpcStFrameRate25 };\n            var dScale = { v: this.MpcStScale25 };\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\n\n            return Math.floor(l100Ns * dRate.v / dScale.v / Math.pow(10.0, 7));\n        },\n        /**\n         * 百纳秒转秒\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}    l100Ns        百纳秒\n         * @return  {number}                  秒\n         */\n        l100Ns2Second: function (l100Ns) {\n            return l100Ns / 10000000;\n        },\n        /**\n         * 百纳秒转时码\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}     l100Ns       百纳秒\n         * @param   {boolean}    dropFrame    是否是丢帧\n         * @return  {string}                  时码字符串\n         */\n        l100Ns2Tc: function (l100Ns, dropFrame) {\n            var dHour = (Math.floor(l100Ns / (3600 * Math.pow(10.0, 7))));\n            var llResidue = (l100Ns % (3600 * Math.pow(10.0, 7)));\n            var dMin = (Math.floor(llResidue / (60 * Math.pow(10.0, 7))));\n            llResidue = llResidue % parseInt((Math.floor(60 * Math.pow(10.0, 7))));\n            var dSec = (Math.floor(llResidue / (Math.pow(10.0, 7))));\n            llResidue = llResidue % parseInt((Math.pow(10.0, 7)));\n            var dFraction = (Math.floor(llResidue / (100000)));\n            return this.formatTimeCodeString(dHour, dMin, dSec, dFraction, dropFrame);\n        },\n        /**\n         * 百纳秒转时码\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}     l100Ns        百纳秒\n         * @param   {number}     dFrameRate    帧率\n         * @param   {boolean}    dropFrame     \n         * @return  {string}                   时码字符串\n         */\n        l100Ns2Tc$1: function (l100Ns, dFrameRate, dropFrame) {\n            if (!dropFrame) {\n                dropFrame = this.getRateDropFrame(dFrameRate);\n            }\n            return this.frame2Tc(this.l100Ns2Frame(l100Ns, dFrameRate), dFrameRate, dropFrame);\n        },\n        /**\n         * 帧率修正\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}            dRate          帧率\n         * @param   {number}            dScale         修正值\n         * @param   {System.Double&}    dFrameRate     输出帧率\n         * @param   {System.Double&}    dFrameScale    输出修正值\n         * @return  {void}                             \n         */\n        rate2ScaleFrameRateAndFrameScale: function (dRate, dScale, dFrameRate, dFrameScale) {\n            if ((dRate === this.MpcStFrameRate25 && dScale === this.MpcStScale25) || (dRate * this.MpcStScale25 === dScale * this.MpcStFrameRate25)) {\n                dFrameRate.v = this.MpcStFrameRate25;\n                dFrameScale.v = MpcStScale25;\n            } else if ((dRate === this.MpcStFrameRate2997 && dScale === this.MpcStScale2997) || (dRate * this.MpcStScale2997 === dScale * this.MpcStFrameRate2997)) {\n                dFrameRate.v = this.MpcStFrameRate2997;\n                dFrameScale.v = this.MpcStScale2997;\n            } else if ((dRate === this.MpcStFrameRate30 && dScale === this.MpcStScale30) || (dRate * this.MpcStScale30 === dScale * this.MpcStFrameRate30)) {\n                dFrameRate.v = this.MpcStFrameRate30;\n                dFrameScale.v = this.MpcStScale30;\n            } else if ((dRate === this.MpcStFrameRate24 && dScale === this.MpcStScale24) || (dRate * this.MpcStScale24 === dScale * this.MpcStFrameRate24)) {\n                dFrameRate.v = this.MpcStFrameRate24;\n                dFrameScale.v = this.MpcStScale24;\n            } else if ((dRate === this.MpcStFrameRate2398 && dScale === this.MpcStScale2398) || (dRate * this.MpcStScale2398 === dScale * this.MpcStFrameRate2398)) {\n                dFrameRate.v = this.MpcStFrameRate2398;\n                dFrameScale.v = this.MpcStScale2398;\n            } else if ((dRate === this.MpcStFrameRate50 && dScale === this.MpcStScale50) || (dRate * this.MpcStScale50 === dScale * this.MpcStFrameRate50)) {\n                dFrameRate.v = this.MpcStFrameRate50;\n                dFrameScale.v = this.MpcStScale50;\n            } else if ((dRate === this.MpcStFrameRate5994 && dScale === this.MpcStScale5994) || (dRate * this.MpcStScale5994 === dScale * this.MpcStFrameRate5994)) {\n                dFrameRate.v = this.MpcStFrameRate5994;\n                dFrameScale.v = this.MpcStScale5994;\n            } else if ((dRate === this.MpcStFrameRate60 && dScale === this.MpcStScale60) || (dRate * this.MpcStScale60 === dScale * this.MpcStFrameRate60)) {\n                dFrameRate.v = this.MpcStFrameRate60;\n                dFrameScale.v = this.MpcStScale60;\n            }\n        },\n        /**\n         * 秒转帧\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}    dbSec     秒数\n         * @param   {number}    dRate     帧率\n         * @param   {number}    dScale    修正值\n         * @return  {number}              帧数\n         */\n        second2Frame$1: function (dbSec, dRate, dScale) {\n            //dbSec = dbSec * Math.pow(10.0, 7);\n            dbSec = parseInt(dbSec.toFixed(7).replace('.', ''));\n            var dFrameRate = { v: this.MpcStFrameRate25 };\n            var dFrameScale = { v: this.MpcStScale25 };\n            this.rate2ScaleFrameRateAndFrameScale(dRate, dScale, dFrameRate, dFrameScale);\n\n            //return (long)( dbSec * dRate / dScale );\n            return Math.floor(dbSec * dFrameRate.v / dFrameScale.v / Math.pow(10.0, 7) + 0.5);\n        },\n        /**\n         * 秒转帧\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}    dbSec         秒数\n         * @param   {number}    dFrameRate    帧率\n         * @return  {number}                  帧数\n         */\n        second2Frame: function (dbSec, dFrameRate) {\n            //超过24的归零\n            if (dbSec >= 86400) {\n                dbSec = dbSec - 86400;\n\n                return this.second2Frame(dbSec, dFrameRate);\n            }\n\n            //dbSec = dbSec * Math.pow(10.0, 7);\n            dbSec = parseInt(dbSec.toFixed(7).replace('.', ''));\n            var dRate = { v: this.MpcStFrameRate25 };\n            var dScale = { v: this.MpcStScale25 };\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\n\n            //return (long)( dbSec * dRate / dScale );\n            //return parseInt((Math.floor(dbSec * dRate.v / dScale.v / Math.pow(10.0, 7))));\n            return Math.floor(dbSec * dRate.v / dScale.v / Math.pow(10.0, 7) + 0.5);\n        },\n        /**\n         * 秒转帧不加0.5修正,适用于真实的点\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}    dbSec         秒数\n         * @param   {number}    dFrameRate    帧率\n         * @return  {number}                  帧数\n         */\n        second2FrameNoCorrecte: function (dbSec, dFrameRate) {\n            //超过24的归零\n            if (dbSec >= 86400) {\n                dbSec = dbSec - 86400;\n\n                return this.second2FrameNoCorrecte(dbSec, dFrameRate);\n            }\n\n            //dbSec = dbSec * Math.pow(10.0, 7);\n            dbSec = parseInt(dbSec.toFixed(7).replace('.', ''));\n            var dRate = { v: this.MpcStFrameRate25 };\n            var dScale = { v: this.MpcStScale25 };\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\n\n            //return (long)( dbSec * dRate / dScale );\n            //return parseInt((Math.floor(dbSec * dRate.v / dScale.v / Math.pow(10.0, 7))));\n            return Math.floor(dbSec * dRate.v / dScale.v / Math.pow(10.0, 7));\n        },\n        /**\n         * 格式化时码字符串\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}     hours        小时数\n         * @param   {number}     minutes      分指数\n         * @param   {number}     seconds      秒数\n         * @param   {number}     frames       帧数\n         * @param   {boolean}    dropFrame    是否是丢帧\n         * @return  {string}                  格式化后的时码字符串\n         */\n        formatTimeCodeString: function (hours, minutes, seconds, frames, dropFrame) {\n            hours = hours >= 24 ? hours - 24 : hours;\n            minutes = minutes >= 60 ? minutes - 60 : minutes;\n            seconds = seconds >= 60 ? seconds - 60 : seconds;\n\n            var framesSeparator = \".\";\n            if (!dropFrame) {\n                framesSeparator = \":\";\n            }\n\n            hours = parseInt((Math.floor(hours % 24.0)));\n            function wrap(n) {\n                return ((n < 10) ? '0' + n : n);\n            }\n            //return string.format(\"{0:D2}:{1:D2}{4}{2:D2}:{3:D2}\", hours, minutes, seconds, frames, framesSeparator);\n            var smtp = (wrap(hours) + ':' + wrap(minutes) + framesSeparator + wrap(seconds) + ':' + wrap(frames));\n            return smtp;\n        },\n        formatTimeCodeString$1: function (timeCode, dFrameRate, dropFrame) {\n            if (timeCode) {\n                var hours = 0;\n                var minutes = 0;\n                var seconds = 0;\n                var frames = 0;\n                var ftcs = timeCode.split(\":\");\n                hours = parseInt(ftcs[0]);\n                if (ftcs.length >= 4) {\n                    minutes = parseInt(ftcs[1]);\n                    seconds = parseInt(ftcs[2]);\n\n                    if (parseInt(ftcs[3]) >= dFrameRate) {\n\n                        var showframeRate = Math.ceil(dFrameRate) - 1;\n                        ftcs[3] = showframeRate.toString();\n                    } else {\n\n                        //验证是否是丢帧时码 \n                        if (dropFrame) {\n                            //如果是丢帧帧率 分钟 除开 00 10 20 30 40 50 外所有的 00 01帧不存在 强制设置为02帧 5994强制设置为04帧\n                            var dropM = [\"00\", \"10\", \"20\", \"30\", \"40\", \"50\"];\n                            var drop5994F = [\"00\", \"01\", \"02\", \"03\"];\n                            var dropF = [\"00\", \"01\"];\n\n                            if (ftcs[2] === \"00\" && !dropM.contains(ftcs[1]) && drop5994F.contains(ftcs[3])) {\n                                if (60.0 - dFrameRate < 0.1) {\n                                    ftcs[3] = \"04\";\n                                } else {\n                                    if (dropF.contains(ftcs[3])) {\n                                        ftcs[3] = \"02\";\n                                    }\n                                }\n                            }\n                        }\n                    }\n                    frames = parseInt(ftcs[3]);\n                } else {\n                    var ftcssf = ftcs[2].split(\".\");\n                    minutes = parseInt(ftcs[1]);\n                    seconds = parseInt(ftcssf[0]);\n\n                    if (parseInt(ftcssf[1]) >= dFrameRate) {\n                        var showframeRate1 = Math.ceil(dFrameRate) - 1;\n                        ftcssf[1] = showframeRate1.toString();\n                    } else {\n\n                        //验证是否是丢帧时码 \n                        if (dropFrame) {\n                            //如果是丢帧帧率 分钟 除开 00 10 20 30 40 50 外所有的 00 01帧不存在 强制设置为02帧\n                            var dropM1 = [\"00\", \"10\", \"20\", \"30\", \"40\", \"50\"];\n                            var drop5994F1 = [\"00\", \"01\", \"02\", \"03\"];\n                            var dropF1 = [\"00\", \"01\"];\n\n                            if (ftcssf[0] === \"00\" && !dropM1.contains(ftcs[1]) && drop5994F1.contains(ftcssf[1])) {\n                                if (60.0 - dFrameRate < 0.1) {\n                                    ftcssf[1] = \"04\";\n                                } else {\n                                    if (dropF1.contains(ftcssf[1])) {\n                                        ftcssf[1] = \"02\";\n                                    }\n                                }\n                            }\n                        }\n\n                    }\n                    frames = parseInt(ftcssf[1]);\n                }\n\n                return this.formatTimeCodeString(hours, minutes, seconds, frames, dropFrame);\n            }\n            return \"--:--:--:--\";\n        },\n        /**\n         * 递归解决时码丢帧的问题\n         *\n         * @static\n         * @private\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {string}     sTimeCode     \n         * @param   {number}     ftcFrames     \n         * @param   {boolean}    isAdded       是否加修正值 为false的时候 为减了修正值\n         * @param   {number}     corrValue     修正值\n         * @param   {number}     dFrameRate    \n         * @param   {boolean}    dropFrame     \n         * @return  {number}                   \n         */\n        getFrameByTimeCode: function (sTimeCode, ftcFrames, isAdded, corrValue, dFrameRate, dropFrame) {\n            var ftcNewFrames = 0;\n            if (isAdded) {\n                ftcNewFrames = ftcFrames + corrValue;\n            } else {\n                ftcNewFrames = ftcFrames - corrValue;\n                corrValue++;\n            }\n            var newTc = this.frame2Tc(ftcNewFrames, dFrameRate, dropFrame);\n\n\n            newTc = this.replaceAll(newTc, \".\", \":\");\n            sTimeCode = this.replaceAll(sTimeCode, \".\", \":\");\n\n\n            if (newTc !== sTimeCode) {\n                return this.getFrameByTimeCode(sTimeCode, ftcFrames, !isAdded, corrValue, dFrameRate, dropFrame);\n            }\n\n            return ftcNewFrames;\n        },\n        /**\n         * 获取此帧率是否丢帧\n         *\n         * @static\n         * @public\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {number}     rate    帧率\n         * @return  {boolean}            \n         */\n        getRateDropFrame: function (rate) {\n            if (rate === 23.98 || (rate < 24 && rate > 23)) {\n                return true;\n            } else if (rate === 24.0) {\n                return false;\n            } else if (rate === 25.0) {\n                return false;\n            } else if (rate === 29.97 || (rate < 30 && rate > 29)) {\n                return true;\n            } else if (rate === 30.0) {\n                return false;\n            } else if (rate === 50.0) {\n                return false;\n            } else if (rate === 59.94 || (rate < 60 && rate > 59)) {\n                return true;\n            } else if (rate === 60.0) {\n                return false;\n            }\n            return false;\n        },\n        /**\n         * 时间字符串转秒(未考虑丢帧的情况)\n         *\n         * @static\n         * @private\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {string}    sTimeCode     \n         * @param   {number}    dFrameRate    \n         * @return  {number}                  帧数\n         */\n        timeCode2NdfFrame: function (sTimeCode, dFrameRate) {\n            var ftcSeconds = 0;\n            var ftcFrames = 0;\n\n            var lHour = { v: 0 };\n            var lMinute = { v: 0 };\n            var lSecond = { v: 0 };\n            var lFrame = { v: 0 };\n\n            this.timeCode2Format(sTimeCode, lHour, lMinute, lSecond, lFrame, dFrameRate);\n\n            ftcSeconds += lHour.v * 60 * 60;\n            ftcSeconds += lMinute.v * 60;\n            ftcSeconds += lSecond.v;\n            ftcFrames += lFrame.v;\n            //这里以前有bug，因为second2Frame返回的是字符串，不转成int会变成字符串拼接了。。\n            ftcFrames += parseInt(this.second2Frame(ftcSeconds, dFrameRate));\n\n            return ftcFrames;\n        },\n        /**\n         * 时间字符串格式化\n         *\n         * @static\n         * @private\n         * @this TimeCodeConvert.TimeCodeConvertHelper\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\n         * @param   {string}           sTimeCode     \n         * @param   {System.Int64&}    lHour         \n         * @param   {System.Int64&}    lMinute       \n         * @param   {System.Int64&}    lSecond       \n         * @param   {System.Int64&}    lFrame        \n         * @param   {number}           dFrameRate    \n         * @return  {void}                           帧数\n         */\n        timeCode2Format: function (sTimeCode, lHour, lMinute, lSecond, lFrame, dFrameRate) {\n            var ftcCodes = sTimeCode.split(\":\");\n\n            if (ftcCodes.length >= 4) {\n                lHour.v = parseInt(ftcCodes[0]);\n                lMinute.v = parseInt(ftcCodes[1]);\n                lSecond.v = parseInt(ftcCodes[2]);\n                lFrame.v = parseInt(ftcCodes[3]);\n            } else {\n                var ftcssf = ftcCodes[1].split(\".\");\n                lHour.v = parseInt(ftcCodes[0]);\n                lMinute.v = parseInt(ftcssf[0]);\n                lSecond.v = parseInt(ftcssf[1]);\n                lFrame.v = parseInt(ftcCodes[2]);\n            }\n\n            lHour.v = lHour.v >= 24 ? lHour.v - 24 : lHour.v;\n            lMinute.v = lMinute.v >= 60 ? lMinute.v - 60 : lMinute.v;\n            lSecond.v = lSecond.v >= 60 ? lSecond.v - 60 : lSecond.v;\n            lFrame.v = lFrame.v >= Math.ceil(dFrameRate) ? lFrame.v - parseInt(Math.ceil(dFrameRate)) : lFrame.v;\n        },\n\n        //音频时间格式转换  2016-04-10 created by wangyu\n        SecondToTimeString_audio: function (seconds, opts) {\n            var hours = Math.floor(seconds / 3600);\n            var minutes = Math.floor((seconds - 3600 * hours) / 60);\n            var iseconds = Math.floor(seconds - (60 * minutes) - (3600 * hours));\n            var i10Milliseconds = Math.floor((seconds * 100 - Math.floor(seconds) * 100));\n\n            if (i10Milliseconds >= 100) {\n                iseconds++;\n                i10Milliseconds = i10Milliseconds - 100;\n            }\n\n            var hoursStr = hours < 10 ? \"0\" + hours : hours.toString();\n            var minutesStr = minutes < 10 ? \"0\" + minutes : minutes.toString();\n            var isecondsStr = iseconds < 10 ? \"0\" + iseconds : iseconds.toString();\n            var i10MillisecondsStr = i10Milliseconds < 10 ? \"0\" + i10Milliseconds : i10Milliseconds.toString();\n            return hoursStr + \":\" + minutesStr + \":\" + isecondsStr + (opts === 'exactTo_s' ? \"\" : (\":\" + i10MillisecondsStr));\n        },\n        //音频时码转百纳秒\n        timeCode2L100Ns_audio: function (sTimeCode) {\n            var ftcCodes = sTimeCode.split(\":\");\n            var lHour = { v: 0 };\n            var lMinute = { v: 0 };\n            var lSecond = { v: 0 };\n            var I10Milliseconds = { v: 0 };\n\n            lHour.v = parseInt(ftcCodes[0]);\n            lMinute.v = parseInt(ftcCodes[1]);\n            lSecond.v = parseInt(ftcCodes[2]);\n            if (ftcCodes[3] != undefined)\n                I10Milliseconds.v = parseInt(ftcCodes[3]);\n\n            var l100ns = 0;\n            l100ns += lHour.v * 60 * 60;\n            l100ns += lMinute.v * 60;\n            l100ns += lSecond.v\n            l100ns += I10Milliseconds.v / 100;\n            l100ns = parseInt((l100ns * Math.pow(10.0, 7)).toFixed(0));\n\n            return l100ns;\n        },\n\n        replaceAll: function (str, s1, s2) {\n            return str.replaceAll(s1, s2);\n        },\n        div: function (a, b) {\n            return this.hasValue(a) && this.hasValue(b) ? parseInt(a / b) : null;\n        },\n        hasValue: function (obj) {\n            return (obj !== null) && (obj !== undefined);\n        },\n\n        second2L100Ns: function (seconds) {\n            // 直接 *Math.pow(10,7)有精度问题，例如：314.28*10\n            return parseInt((seconds).toFixed(7).replace('.', ''), 10)\n        }\n    };\n    window.timecodeconvert = new timeCodeConvertHelper();\n    return new timeCodeConvertHelper();\n})();\nwindow.TimeCodeConvert = TimeCodeConvert;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/index.js\n// module id = 0\n// module chunks = 0"], "sourceRoot": ""}