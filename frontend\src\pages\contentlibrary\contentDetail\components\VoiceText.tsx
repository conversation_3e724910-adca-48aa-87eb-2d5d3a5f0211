import React, {
    FC,
    useEffect,
    useRef,
    useState,
    KeyboardEvent,
    FocusEvent,
} from 'react';
import { IconFont } from '@/components';
import { Input, Button, message, Timeline, Anchor } from 'antd';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import _ from 'lodash';
import '../index.less';
import SmartService from '@/service/smartService';
import { useParams, useDispatch, useSelector } from 'umi';
import { IPermission } from '@/models/permission';
import perCfg from '@/permission/config';
import { searchKeywords } from '@/utils';

interface IData {
    item: any;
    index: any;
    sequencemeta: any;
    fontSize: any;
    voice: any;
    currentFrame: any;
    searchVoiceWord: any;
    currentLanguage: any;
    getQueryVoice: any;
    Timelineindex: number;
    player: any;
    cur: number;
    setCur: (value) => void;
    setBtnStyle: (value) => void;
    setStartSpan: (value) => void;
    doPop: (value) => void;
    setEndSpan: (value) => void;
    setCurrentTime: (value) => void;
}

const VoiceText: FC<IData> = props => {
    const params = useParams<{ contentId: string }>();
    const {
        item,
        sequencemeta,
        searchVoiceWord,
        currentLanguage,
        currentFrame,
        setCurrentTime,
        index,
        cur,
        setCur,
        getQueryVoice
    } = props;
    const win = window as any;
    const sequencemetaName = useRef<any>();
    const sequencemetaTransName = useRef<any>();
    const [sequencemetaEdit, setSequencemetaEdit] = useState<boolean>(true);
    const [sequencemetaTransEdit, setSequencemetaTransEdit] = useState<boolean>(true); //翻译语言
    const [voiceTitle, setVoiceTitle] = useState<any>();
    const { permissions } = useSelector<{ permission: any }, IPermission>(
        ({ permission }) => permission,
    );

    // useEffect(() => {
    //     if (!sequencemetaEdit) {
    //         sequencemetaName.current?.focus();
    //     }
    //     if (!sequencemetaTransEdit) {
    //         sequencemetaTransName.current?.focus();
    //     }
    //     if (sequencemetaEdit || sequencemetaTransEdit) {
    //         // setCurrentTime()
    //     }
    // }, [sequencemetaEdit, sequencemetaTransEdit]);
    // useEffect(() => {
    // }, [searchVoiceWord, detail]);
    const sequencemetaTransChangeName = async (
        e: KeyboardEvent<HTMLInputElement> | FocusEvent<HTMLInputElement> | any,
        item: any,
    ) => {
        e.preventDefault();
        let name = sequencemetaTransName.current.input.value || e.target.value;
        if (name === '') {
            message.error('标题不能为空');
            return;
        }
        const res = await SmartService.updatepartVoice(params.contentId, [
            {
                guid_: item.guid_,
                textLanguage:{
                    [`text_${currentLanguage}`]: name,
                },
                _in: item._in,
                _out: item._out,
            },
        ]);
        if (res?.success) {
            message.success('编辑成功');
            setSequencemetaTransEdit(true);
        } else {
            message.error('编辑失败');
        }
        getQueryVoice();
        setSequencemetaEdit(true);
    };
    const searchVoice = (tit: string) => {
        const temp = searchKeywords(tit, 'voice');
        if (!searchVoiceWord && temp.index > -1) {
            return <span className='text'>
                {temp.beforeStr}
                <span className="key-search-value">{temp.word}</span>
                {temp.afterStr}
            </span>
        }
        if (searchVoiceWord) { //在检索时 需要置空
            window.localStorage.setItem('searchKeyWord', JSON.stringify({}));
        }
        const index = tit?.indexOf(searchVoiceWord);
        const beforeStr = tit?.substr(0, index);
        const afterStr = tit?.substr(index + searchVoiceWord.length);
        const title =
            index > -1 ? (
                <span className='text'>
                    {beforeStr}
                    <span className="voice-search-value">{searchVoiceWord}</span>
                    {afterStr}
                </span>
            ) : (
                <span className='text'>{tit}</span>
            );
        return title;
    };
    
    return (
        <span className='voiceText'>
            <span
                key={index}
                data-id={index}
                onClick={() => {
                    setCurrentTime(item._in);
                    setCur(item._in)
                    
                }}
                className={(() => {
                    let temp: any = `voicetext mao_${index}`;
                    const flag = sequencemeta.some(
                        s =>
                            s.inpoint <= item._in &&
                            s.outpoint >= item._out,
                    )
                    if (flag) {
                        temp += ' selected';
                    }
                    if (currentFrame >= item._in && currentFrame < item._out) {
                        temp += ' currentPlay';
                    }
                    return temp;
                })()
                }
            >
                {sequencemetaEdit && <span>{searchVoice(item.textLanguage[`text_${currentLanguage}`])}</span>}
                <IconFont style={{display: cur=== item._in? 'inline-block': 'none'}} className="icon" onClick={
                        (e) => {
                            setSequencemetaEdit(false)
                            e.stopPropagation()
                            setCur(-1)
                        }
                    } type='icona-bianzu121'/>
            </span>
            {!sequencemetaEdit && <span className='editDiv'>
                <Input
                    defaultValue={item.textLanguage[`text_${currentLanguage}`]}
                    className={'editInput'}
                    ref={sequencemetaTransName}
                    onPressEnter={e => sequencemetaTransChangeName(e, item)}
                />
                <CheckOutlined onClick={(e: any) => sequencemetaTransChangeName(e, item)} />
                <CloseOutlined onClick={(e: any) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setSequencemetaEdit(true);
                }} />
            </span>}
        </span>
    );
};
export default VoiceText;

