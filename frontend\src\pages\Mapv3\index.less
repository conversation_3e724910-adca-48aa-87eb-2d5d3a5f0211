.text_overflow_multi(@line) {
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: @line;
    -webkit-box-orient: vertical;
}

.mapv3_view {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    background-image: url('/rman/static/images/coursemap/v3/bg.png');
    .ant-drawer-content{
        background-color: rgba(0, 0, 0, 0.05);
    }
    .map_content_view {
        position: relative;
        width: 100%;
        height: calc(100% - 90px);
        margin-top: 100px;
        .mejs__trimin-button, .mejs__trimout-button, .mejs__cleantrim-button, .mejs__captions-button{
            display: none;
        }
        .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item-selected::after{
            height: 4px;
            background: linear-gradient(270deg, rgba(84,156,255,0) 0%, #549CFF 100%);
        }
        .ant-menu-horizontal > .ant-menu-item::after{
            border: none;
        }
        .ant-menu-item, .ant-menu-overflow-item-rest{
            font-weight: 600;
        }
        .ant-drawer-header {
            padding: 0;
            margin: 0;
            border-bottom: 1px solid #1E2734;
        }

        .ant-drawer-body {
            background-color: #fff;
        }

        .course_info_item {
            background-color: #202D3E;
            margin-bottom: 10px;
            justify-content: space-between;
        }

        .course_info_item_label {
            color: #ABAEB3;
            border-left: 4px solid #5BCDFF;
            padding-left: 10px;
            margin-left: 10px;
            font-weight: 600;
        }

        .course_info_item_value {
            color: #ABAEB3;
            margin-right: 10px;
        }
    }


    .node_detail_view {

        .span1,
        .other_title,
        .rate_title,
        .title_span,
        .content_name,
        .ant-list-empty-text,
        .ant-page-header-heading-title,
        .ant-table-cell {
            color: #ABAEB3 !important;
        }

        .ant-input {
            background-color: #1E2734;
            color: #ABAEB3;
        }

        .ant-tabs-tab,
        .ant-empty-description {
            color: #ABAEB3;
        }

        .ant-input-search-button,
        .ant-table-container {
            background-color: #1E2734;
        }

        .anticon-search {
            color: #ABAEB3 !important;
        }

        .ant-table-tbody>tr.ant-table-placeholder:hover>td {
            background-color: #1E2734 !important;
        }

        .ant-table-thead>tr>th {
            background-color: #2B3546;
        }

        .ant-table-thead>tr {
            background-color: #2B3546;
        }

        .ant-table-thead>tr>th {
            border-color: #5D5D5D;
        }

        .ant-table-tbody>tr>td {
            border-color: #5D5D5D;
        }

        .divider_dashed {
            border-bottom: 1px solid #1E2734;
        }

        .ant-tabs-top>.ant-tabs-nav::before {
            border-bottom: 1px solid #404E62;
        }

        .entity-preview .video-wrap {
            border: 0;
        }

        .ant-list-bordered {
            border: 1px solid #404E62;
        }

        .ant-drawer-body {

            // 滚动条颜色变成暗黑系
            &::-webkit-scrollbar {
                width: 6px;
                height: 6px;
            }

            &::-webkit-scrollbar-thumb {
                border-radius: 3px;
                background-color: #404F65;
            }

            &::-webkit-scrollbar-track {
                border-radius: 3px;
                background-color: #1E2734;
            }

        }

        .serial-number,
        .homework-sub-item .title-container,
        .answer-item,
        .ant-radio-wrapper,
        .ant-checkbox-wrapper {
            color: #ABAEB3;
        }

        .ant-input {
            border-color: #797979;
        }

        .homework-sub-item {
            border-bottom: 1px dashed #5D5D5D;
        }

        .ant-list-split .ant-list-item {
            border-bottom: 1px solid #404E62;

            &:last-child {
                border-bottom: 0;
            }
        }
    }

    .courseware_list {
        width: 60%;

        .pagination {
            width: 380px;
            text-align: center;
        }

        .ant-timeline {
            .ant-timeline-item {
                display: flex;
                flex-direction: row;
                cursor: pointer;

                .ant-timeline-item-label {
                    top: 0;
                }

                .ant-timeline-item-tail {
                    display: block;
                }

                .ant-timeline-item-head {
                    top: 5px;
                }

                .ant-timeline-item-content {
                    position: relative;
                    top: 0px;
                    width: 224px;
                    height: 126px;

                    img {
                        width: 224px;
                        height: 126px;
                        object-fit: contain;
                        background: #000000;
                    }

                    .number {
                        position: absolute;
                        top: 0;
                        width: 100px;
                        height: 20px;
                        background: #00000050;
                        color: white;
                        // justify-content: center;
                    }

                    .currentCover {
                        cursor: pointer;
                        height: 18px;
                        display: flex;
                        position: absolute;
                        right: 5px;
                        bottom: 5px;
                        padding: 0 3px;
                        background: #00000050;
                        color: #ffffff;
                        border-radius: 5px;
                        font-size: 14px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }

                    .setCoverBtn {
                        visibility: hidden;
                        cursor: pointer;
                        height: 18px;
                        display: flex;
                        position: absolute;
                        right: 5px;
                        bottom: 5px;
                        padding: 0 3px;
                        background: #00000050;
                        color: #ffffff;
                        border-radius: 5px;
                        font-size: 14px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }

                    .deleteBtnDiv {
                        visibility: hidden;
                        cursor: pointer;
                        width: 18px;
                        height: 18px;
                        display: flex;
                        position: absolute;
                        right: 5px;
                        top: 5px;
                        background: #00000050;
                        color: #ffffff;
                        border-radius: 50%;
                        font-size: 14px;
                        flex-direction: row;
                        align-items: center;
                        justify-content: center;
                    }

                    &:hover {

                        .deleteBtnDiv,
                        .setCoverBtn {
                            visibility: visible !important;
                        }
                    }
                }
            }

            .lastItem {
                .ant-timeline-item-tail {
                    display: none;
                }
            }
        }

        .courseware_item_wrap.active {
            .ant-timeline-item-label {
                color: var(--primary-color);
            }

            .ant-timeline-item-content {
                img {
                    border: 3px solid var(--primary-color);
                }
            }
        }
    }

    .ant-empty {
        padding-top: 100px;
    }

    .entity_info_item,
    .voice_div {
        height: 350px;
        overflow: auto;
    }

    .paragraph-wrapper {
        position: relative;
        text-align: justify;
        height: 100%;
        max-height: calc(100% - 45px);

        .right_ {
            position: absolute;
            top: 0;
            right: 0;
        }

        .paragraph-content {
            user-select: text;
            padding: 5px 10px;
            height: calc(100% - 50px);
            overflow-y: auto;

            .voice-search-value {
                color: red;
            }

            .selected {
                text-decoration: underline;
            }

            .currentPlay {
                color: var(--primary-color);
            }
        }

        .text_pop {
            display: none;
            position: absolute;
            min-width: 72px;
            top: 0;
            left: 3px;
            padding: 8px;
            background-color: #fff;
            color: var(--primary-color);
            border-radius: 3px;
            cursor: pointer;
            box-shadow: 0 0 8px rgba(0, 0, 0, 0.15);

            &::after {
                content: ' ';
                width: 0;
                height: 0;
                position: absolute;
                bottom: -6px;
                left: 50%;
                transform: translateX(-50%);
                border-top: 6px solid #fff;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
            }
        }

        .tips {
            width: 80%;
            background: var(--active-bg-color);
            color: #a6a6a6;
            font-size: 14px;
            font-weight: bold;
            margin: 5px 10px;

            .anticon-exclamation-circle {
                margin-right: 5px;
            }
        }

        span:hover {
            background-color: var(--primary-color);
            color: #fff;
            padding: 2px 0px;
        }
    }

    .sequence_list {
        position: relative;
        width: 100%;
        height: 100%;

        .ant-checkbox-group {
            display: block;
            height: calc(100% - 45px);
            overflow-y: auto;
        }

        .sequence_operator {
            margin-left: 12px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 30%;

            .vd_ghost_btn {
                color: var(--primary-color);
                border-color: var(--primary-color);
            }

            .create {
                margin-right: 16px !important;
            }
        }

        .sequence_item_wrap {
            padding: 0 12px;
            display: flex;
            // width: 364px;
            align-items: center;

            // .sequence_item_checkbox {
            //   // margin-right: 4%;
            // }
            &.active {
                background: var(--active-bg-color);
            }

            .sequence_item {
                position: relative;
                height: 72px;
                display: flex;
                cursor: pointer;
                margin: 12px 0;

                .sequence_item_content {
                    width: 130px;
                    position: relative;
                    text-align: center;

                    .imgContainer {
                        width: 130px;
                        height: 100%;
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        justify-content: center;

                        >img {
                            // width: 130px;
                            // height: 100%;
                            height: 72px;
                            width: 128px;
                            object-fit: contain;
                            background: #0a2e4b;
                        }
                    }

                    >img {
                        // width: 130px;
                        // height: 100%;
                        height: 72px;
                        width: 128px;
                        object-fit: contain;
                        background: #0a2e4b;
                    }

                    .ant-spin {
                        position: absolute;
                        top: 44%;
                        left: 45%;
                    }

                    .timeSpan {
                        // color: #747477;
                        position: absolute;
                        font-size: 12px;
                        bottom: 0;
                        color: white;
                        background: black;
                        width: 100;
                        width: 100%;
                        left: 0px;
                        opacity: 0.5;
                        display: flex;
                        flex-direction: row;
                        justify-content: flex-end;
                        flex-wrap: nowrap;

                        span {
                            transform: scale(0.9);
                            display: inline-block;
                            margin-right: 4px;
                        }
                    }
                }

                .opacityDom {
                    position: relative;
                    opacity: 0.35;
                }

                .mixup_container_doing {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    position: absolute;
                    left: 44px;
                    top: 20%;

                    img {
                        width: 20px;
                    }

                    span:last-child {
                        color: #d5d8d8;
                    }
                }

                .backgroundDom {
                    background: black !important;
                }

                .mixup_container_failed {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    position: absolute;
                    left: 37px;
                    top: 20%;

                    img {
                        width: 20px;
                    }

                    span:last-child {
                        color: #d5d8d8;
                    }
                }

                .reStartMixup {
                    border: 1px solid;
                    border-radius: 5px;
                }

                .sequence_content {
                    // margin-right: 48px;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    margin-left: 12px;
                    justify-content: space-between;
                    width: 100%;

                    .sequence_content_top {
                        display: flex;
                        height: 72px;
                        flex-direction: column;
                        justify-content: space-between;
                    }

                    .sequence_title {
                        .text_overflow_multi(2);
                    }

                    span {
                        font-size: 12px;
                    }
                }

                .del_icon_product {
                    z-index: 10;

                    .ant-btn-link {
                        width: 24px;
                        height: 24px;

                        span {
                            font-size: 12px;
                        }
                    }
                }
            }
        }
    }
}