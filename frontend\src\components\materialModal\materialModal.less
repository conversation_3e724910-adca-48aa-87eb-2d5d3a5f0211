.material-modal-content {
  position: relative;
  display: flex;

  .directory {
    width: 200px;
    border: 1px solid #f0f0f0;
    height: 680px;
    overflow-y: auto;
    font-size: 14px !important;
  }

  .content {
    flex: 1;
    margin-left: 24px;
    position: relative;
  }

  .type-item {
    width: 85px;
  }

  .keyword-item {
    width: calc(~"100% - 85px");
  }

  .folder-list {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin-top: 24px;

    > .content_item {
      width: 240px !important;
      margin: 0 24px 24px 0 !important;
    }
  }

  .pagination-wrapper {
    position: absolute;
    width: 100%;
    bottom: 0;
    left: 0;
    text-align: center;
  }
}
