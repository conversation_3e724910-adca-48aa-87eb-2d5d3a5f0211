import React, { FC, useState, useEffect } from 'react';
import { Button, Checkbox, Popover, Tooltip } from 'antd';
import { SettingOutlined } from '@ant-design/icons';
import { IPermission, useHistory, useSelector } from 'umi';
import './index.less';
import globalParams from '@/permission/globalParams';
import perCfg from '@/permission/config';
const columnList = [
  {
    key: 'extension',
    value: '扩展名',
  },
  {
    key: 'size',
    value: '大小',
  },
  {
    key: 'teacher',
    value: '授课教师',
  },
  {
    key: 'directory',
    value: '所在目录',
  },
  {
    key: 'deleteUser_',
    value: '删除人',
  },
  {
    key: 'deleteTime_',
    value: '删除时间',
  },
  {
    key: 'source',
    value: '来源',
  },
  {
    key: 'person',
    value: '上传人',
  },
  {
    key: 'semester',
    value: '学期',
  },
  {
    key: 'time',
    value: '上传时间',
  },
  // {
  //   key: 'smarttag',
  //   value: '智能标签',
  // },
  // {
  //   key: 'voice',
  //   value: '语音分析',
  // },
  // {
  //   // key: 'knowledge',
  //   key: 'intelliState',
  //   value: '是否有知识点',
  // },
  {
    key: 'duration',
    value: '播放时长'
  },
  {
    key: 'deleteUserName_',
    value: '删除用户'
  },
  {
    key: 'cataloging_status',
    value: '编目状态',
  },
  {
    key: 'status',
    value: '使用状态',
  },
  {
    key: 'shared_state',
    value: '共享状态',
  },
  {
    key: 'type',
    value: '类型',
  },
];
const defaultChecked = ['size', 'person', 'time'];
const columnList1 = [
  {
    key: 'extension',
    value: '扩展名',
  },
  {
    key: 'size',
    value: '大小',
  },
  {
    key: 'directory',
    value: '所在目录',
  },
  {
    key: 'person',
    value: '上传人',
  },
  {
    key: 'time',
    value: '上传时间',
  },
  {
    key: 'type',
    value: '类型',
  },
];
// 我的分享专属列表
const columnList2 = [
  {
    key: 'userName',
    value: '分享人',
  },
  {
    key: 'validity',
    value: '有效期',
  },
  {
    key: 'shareTime',
    value: '分享时间',
  }
];
// 我的分享专属列表
const columnList3 = [
  {
    key: 'shareUserName',
    value: '分享给',
  },
  {
    key: 'readPeople',
    value: '查看次数',
  },
  {
    key: 'validity',
    value: '有效期',
  },
  {
    key: 'shareTime',
    value: '分享时间',
  },
];

// 不按课表分类
const columnListByschedule = [
  {
    key: 'name',
    value: '素材名',
  },
  {
    key: 'fileext',
    value: '扩展名',
  },
  {
    key: 'filesize',
    value: '大小',
  },
  {
    key: 'importuser',
    value: '上传人',
  },
  {
    key: 'createDate',
    value: '上传时间',
  },
  {
    key: 'source',
    value: '来源',
  },
  // {
  //   key: 'intelliState',
  //   value: '是否有知识点',
  // },
  // {
  //   key: 'usestatus',
  //   value: '使用状态',
  // },
  {
    key: 'duration',
    value: '播放时长'
  },
  {
    key: 'seat',
    value: '机位',
  },
  {
    key: 'semester',
    value: '学期',
  },
]
const default_share_Checked = ['shareUserName', 'extension', 'size', 'teacher', 'directory'];
export const allColumns: string[] = columnList.map(item => item.key);
export const allColumns2: string[] = columnList2.map(item => item.key);
export const allColumns3: string[] = columnList3.map(item => item.key);

interface ColumnSelectProps {
  issearch?: boolean;
  onChange: (checked: string[]) => void;
  myShare?: boolean;
  columns: string[];
  pubOrPri: boolean;
  noteClassifiedByschedule: boolean
  breadCrumb:any
}

const ColumnSelect: FC<ColumnSelectProps> = ({
  issearch,
  onChange,
  myShare,
  columns,
  pubOrPri,
  noteClassifiedByschedule,
  breadCrumb
}) => {
  let history: any = useHistory();
  // 权限
  const { permissions, rmanGlobalParameter } = useSelector<{ permission: any },IPermission>(({ permission }) => permission);
  const [checkList, setCheckList] = useState<any>([]);
  const hidecatalogue = JSON.stringify(history.location.query) === '{}' ? '0' : history.location.query.hidecatalogue;

  // 根据权限，筛选显示的列【有 knowledge_analysis_display 的显示 analysis 列】
  const getColumns = (columnList: any) => {
    if (
      rmanGlobalParameter.includes(globalParams.speech_analysis_display) &&
      rmanGlobalParameter.includes(globalParams.knowledge_analysis_display)
    ) {
      //   if (!pubOrPri) {
      //     // 私有
      return columnList;
      //   } else {
      //     // 公共的
      //     if (permissions.includes(perCfg.resource_analysis)) {
      //       return columnList;
      //     } else {
      //       return columnList.filter((item: any) =>  item.key !== 'voice' && item.key !== 'knowledge');
      //     }
      //   }
    } else {
      if (
        !rmanGlobalParameter.includes(globalParams.speech_analysis_display) &&
        !rmanGlobalParameter.includes(globalParams.knowledge_analysis_display)
      ) {
        return columnList.filter(
          (item: any) => item.key !== 'voice' && item.key !== 'knowledge',
        );
      }
      if (!rmanGlobalParameter.includes(globalParams.speech_analysis_display)) {
        return columnList.filter((item: any) => item.key !== 'voice');
      } else if (
        !rmanGlobalParameter.includes(globalParams.knowledge_analysis_display)
      ) {
        return columnList.filter((item: any) => item.key !== 'knowledge');
      }
    }
  };

  let list: any = [];
  hidecatalogue === '1' ? (list = columnList1) : (list = getColumns(columnList));
  noteClassifiedByschedule && (list = columnListByschedule);
  myShare && (list = columnList3);

  const changeColumns = () => {
    return myShare ? (list.map((item: any) => {
      if (default_share_Checked.includes(item.key))
        return item.key
    }).filter(Boolean)) :
      (list.map((item: any) => {
        if ((columns.length ? columns : defaultChecked).includes(item.key))
          return item.key
      }).filter(Boolean))
  }

  useEffect(() => {
    let temp = changeColumns()
    setCheckList(temp.filter((item: any) => {
      if (item == 'directory') {
        return false;
      } else {
        return true;
      }
    }));
  }, [breadCrumb, myShare]);

  const onColumnChange = (checkList: any[]) => {
    setCheckList(checkList.filter((item: any) => {
      if (item == 'directory') {
        return false;
      } else {
        return true;
      }
    }));
  };

  useEffect(() => {
    onChange(checkList);
  }, [checkList]);

  // 产品要求只有查询的时候展示所在目录  点击重置 去除展示所在目录
  // useEffect(() => {
  //   if (issearch) {
  //     setCheckList([...checkList, 'directory']);
  //   } else {
  //     setCheckList(checkList.filter((item: any) => {
  //       if (item == 'directory') {
  //         return false;
  //       } else {
  //         return true;
  //       }
  //     }));
  //   }
  // }, [issearch]);

  const content = (
    <div>
      <Checkbox.Group value={checkList} onChange={onColumnChange}>
        {list.map((item: any) => (
          <div key={item.key}>
            <Checkbox value={item.key}>{item.value}</Checkbox>
          </div>
        ))}
      </Checkbox.Group>
    </div>
  );

  const title = (
    <div className="column-select-title">
      列展示{' '}
      {/* <Button type="link" onClick={onReset}>
        重置
      </Button> */}
    </div>
  );
  return (
    <Popover
      overlayClassName="column-select-wrapper"
      title={title}
      content={content}
      trigger="click"
      placement="bottomRight"
    >
      <Tooltip title={'列设置'}>
        <div className="column-select-icon">
          <SettingOutlined />
        </div>
      </Tooltip>
    </Popover>
  );
};

export default ColumnSelect;
