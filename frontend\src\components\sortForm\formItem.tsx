import React, { <PERSON> } from 'react';
import {
  Input,
  Radio,
  Select,
  Button,
  Form,
  InputNumber,
  Checkbox,
  Switch,
  DatePicker,
  TimePicker,
  TreeSelect,
  Slider,
  Tag,
} from 'antd';

const { Item } = Form;

import { IFormItem } from './index';

const FormItem: FC<IFormItem> = ({ type, name, id }) => {
  switch (type) {
    case 'input':
      return (
        <Item label={name}>
          <Input />
        </Item>
      );
    case 'input.number':
      return (
        <Item label={name}>
          <InputNumber />
        </Item>
      );
    case 'input.textarea':
      return (
        <Item label={name}>
          <Input.TextArea />
        </Item>
      );
    case 'checkbox':
      return (
        <Item label={name}>
          <Checkbox>{name}</Checkbox>
        </Item>
      );
    case 'radio':
      return (
        <Item label={name}>
          <Radio.Group>
            <Radio value="a">item 1</Radio>
            <Radio value="b">item 2</Radio>
            <Radio value="c">item 3</Radio>
          </Radio.Group>
        </Item>
      );
    case 'switch':
      return (
        <Item label={name}>
          <Switch />
        </Item>
      );
    case 'select':
      return (
        <Item label={name}>
          <Select>
            <Select.Option value="red">Red</Select.Option>
            <Select.Option value="green">Green</Select.Option>
            <Select.Option value="blue">Blue</Select.Option>
          </Select>
        </Item>
      );
    case 'date':
      return (
        <Item label={name}>
          <DatePicker />
        </Item>
      );
    case 'date.time':
      return (
        <Item label={name}>
          <DatePicker showTime />
        </Item>
      );
    case 'date.range':
      return (
        <Item label={name}>
          <DatePicker.RangePicker showTime />
        </Item>
      );
    case 'time':
      return (
        <Item label={name}>
          <TimePicker />
        </Item>
      );
    case 'select.tree':
      return (
        <Item label={name}>
          <TreeSelect />
        </Item>
      );
    case 'slider':
      return (
        <Item label={name}>
          <Slider />
        </Item>
      );
    case 'tag':
      return (
        <Item label={name}>
          <Tag>{name}</Tag>
        </Item>
      );
    default:
      return (
        <Item label={name}>
          <Input />
        </Item>
      );
  }
};
export default FormItem;
