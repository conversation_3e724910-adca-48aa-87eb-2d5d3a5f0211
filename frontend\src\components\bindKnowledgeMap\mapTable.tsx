import React, { useEffect, useState } from "react";
import { Modal, Checkbox, Button, Form, message, Input, List, Tree, Radio,Tabs,Empty,Table } from 'antd';

interface CreateTableProps {
  columns: any;
  dataSource:any;
  videoId:any;
  callback:(videoId:any,selectedRowKeys:any)=> void;
  visible:boolean;
}
const MapTable:React.FC<CreateTableProps> = props =>{
  const { columns, dataSource,callback,videoId,visible } = props;
  const [selectedRowKeys, setSelectedRowKeys] = useState<Array<any>>([]);
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const rowSelection = {
    type:'checkbox',
    onChange: (newSelectedRowKeys: Array<any>, newSelectedRows: any) => {
      setSelectedRowKeys(newSelectedRowKeys);
      setSelectedRows(newSelectedRows);
      callback(videoId,newSelectedRows);
    },
    preserveSelectedRowKeys: true,
    selectedRowKeys,
  };
  useEffect(() => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
  }, [visible]);
  return (
    <Table
      key={selectedRows}
      columns={columns}
      rowKey="id"
      dataSource={dataSource}
      pagination={false}
      rowSelection={rowSelection as any}
    />
  )
}
export default MapTable;