import loginApis from '@/service/loginApis';
import loginTypes from '@/types/loginTypes';
import { getLightColor, loadFavicon, getActiveBgColorFade } from '@/utils';
import config from '@/config';

interface ITheme {
  title: string;
  themeColor: string;
  logoUrl: string;
  isShow: number;
  faviconUrl: string;
}

const themeStr = localStorage.getItem(config.themeStorage);
const themeConfig: ITheme = themeStr
  ? JSON.parse(themeStr)
  : {
      title: '',
      themeColor: '',
      logoUrl: '',
      isShow: 1,
      faviconUrl: '',
    };

function modifyTheme(themeColor: string) {
  console.time('theme');
  // 切换主题颜色（antd）
  return (window as any).less
    .modifyVars({
      '@primary-color': themeColor,
      '@second-color': getLightColor(themeColor, 0.2),
      '@primary-bg-color': getActiveBgColorFade(themeColor, 0.2), // 添加20%透明度的背景色变量 (33 = 20% 的十六进制)
    })
    .then(() => {
      console.timeEnd('theme');
      console.log(`${themeColor} 主题切换成功`);
    })
    .catch(() => console.error(`${themeColor} 主题切换失败`));
}

export default {
  namespace: 'themes',
  subscriptions: {
    setup({ dispatch }: any) {
      dispatch({
        type: 'initTheme',
        payload: themeConfig,
      });
      if (themeConfig.themeColor) {
        dispatch({
          type: 'generateTheme',
          payload: themeConfig,
        });
      }
    },
  },
  state: themeConfig,
  effects: {
    *initTheme({ payload }: { payload: ITheme }, { call, put }: any) {
      //先初始化配置文件
      const temp = JSON.parse((window as any).sessionStorage.getItem('configSafe'));
      if(!temp){
        const result:API.OsResponse<loginTypes.ISetting> = yield call(loginApis.fetchSafeConfig);
        (window as any).sessionStorage.setItem('configSafe',JSON.stringify(result));
      }
      const {
        errorCode,
        extendMessage,
      }: API.OsResponse<loginTypes.ISetting> = yield call(
        loginApis.fetchSysSetting,
      );
      if (errorCode === 'success') {
        localStorage.setItem(
          config.themeStorage,
          JSON.stringify(extendMessage),
        );
        if (extendMessage.themeColor !== payload.themeColor) {
          yield put({
            type: 'setTheme',
            payload: extendMessage,
          });
          yield put({
            type: 'generateTheme',
            payload: extendMessage,
          });
        }
      }
    },
    *generateTheme(
      { payload: { faviconUrl, themeColor } }: { payload: ITheme },
      { call, put }: any,
    ) {
      // const config = themeCfg[theme];
      // 下载icon
      loadFavicon(faviconUrl || '');
      yield put({
        type: 'config/changeShowLoading',
        payload: {
          value: true,
        },
      });
      try {
        yield call(modifyTheme, themeColor);
      } catch (e) {
        yield put({
          type: 'config/changeShowLoading',
          payload: {
            value: false,
          },
        });
      }
      yield put({
        type: 'config/changeShowLoading',
        payload: {
          value: false,
        },
      });
    },
  },
  reducers: {
    setTheme(state: any, { payload }: any) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
