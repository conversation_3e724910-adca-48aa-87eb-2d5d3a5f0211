import React, { FC, useEffect, useState } from 'react';
import { useIntl } from 'umi';
import { Empty, Modal, message, Button, Input, Image, Pagination, Popconfirm, Rate } from "antd";
import { Divider } from 'antd';
import contentListApis from '@/service/contentListApis';
const { TextArea } = Input;
import { getSensitiveWord } from '@/utils';

import "./index.less";

interface textProps {
  commentVisible: boolean;
  contentDetail: any,
  onClose: () => void;
}
const TextclipModal: FC<textProps> = props => {
  const {
    commentVisible,
    contentDetail,
    onClose,
  } = props;
  const userInfo = JSON.parse(localStorage.getItem('userinfo') || '{}');
  const [comment, setContent] = useState<string>('')
  const [list, setList] = useState<any[]>([])
  const intl = useIntl();
  const [total, setTotal] = useState<number>(0)
  const [page, setPage] = useState<number>(1)
  const [commentScore, setCommentScore] = useState<number>(0)
  useEffect(() => {
    commentVisible && getComment()
  }, [commentVisible, page])
  const getComment = () => {
    contentListApis.getComment({
      contentId: contentDetail.contentId_,
      pageIndex: page,
      pageSize: 10
    }).then(res => {
      if (res?.data) {
        setList(res.data.data);
        setPage(res.data.pageIndex)
        setTotal(res.data.recordTotal)
        if (res.data.data.length === 0 && page > 1) {
          setPage(page - 1)
        }
      }
    })
  }
  const release = () => {
    getSensitiveWord(comment, intl.formatMessage({ id: '评论' }), () => {
      contentListApis.addComment({
        commentContent: comment,
        commentScore: commentScore,
        parentCommentId: '',
        resourceName: contentDetail.entityName,
        resourceCreateUserCode: contentDetail.createUser_,
        contentId: contentDetail.contentId_
      }).then(_res => {
        setContent('')
        getComment()
      })
    })
  }
  const deleteComment = (commentId: string) => {
    contentListApis.deleteComment([commentId]).then(_res => {
      getComment()
    })
  }
  return (
    <Modal
      className='commentModal'
      title={intl.formatMessage({ id: '资源评论' })}
      visible={commentVisible}
      onCancel={onClose}
      width={500}
      footer={null}
    >
      <div className='title'>{intl.formatMessage({ id: '全部评论' })}</div>
      <div className="comment">
        <div className="header">
          <Image
            src={userInfo?.avatar || require('@/images/login/default-avatar.png')}
            fallback={require('@/images/login/default-avatar.png')}
            preview={false}
          />
          <div>
            <span>
              <Rate
                allowHalf
                value={commentScore}
                onChange={(star: any) => {
                  setCommentScore(star)
                }} />
              <span style={{color: '#fadb15', marginLeft: '5px'}}>{commentScore}</span>
            </span>
            <TextArea
              value={comment}
              showCount
              maxLength={100}
              style={{ width: 300, marginTop: '10px', height: 60, resize: 'none' }}
              onChange={e => setContent(e.target.value)}
              placeholder={intl.formatMessage({ id: '请写下对资源的评价吧' })}
            />
          </div>
          <Button type="primary" disabled={comment.length === 0} style={{marginTop: '19px'}} onClick={release}>{intl.formatMessage({ id: '发布' })}</Button>
        </div>
        {list.map(item => (
          <div className="list" key={item.commentId}>
            <Image
              src={item.commentUserSculptureUrl || require('@/images/login/default-avatar.png')}
              fallback={require('@/images/login/default-avatar.png')}
              preview={false}
            />
            <div className="list-content">
              <div className="top">
                <span className="nickname">{item.commentNickName}</span>
                <span className="time">{item.commentTime}</span>
              </div>
              <span>
                <Rate
                  allowHalf
                  disabled
                  defaultValue={item.commentScore} />
                <span style={{color: '#fadb15', marginLeft: '5px'}}>{item.commentScore}</span>
              </span>
              <div className="text">{item.commentContent}</div>
              <Divider />
            </div>
            {item.commentUserCode === userInfo.userCode || userInfo.userCode === contentDetail.createUser_ ?
              <Popconfirm
              placement="topRight"
              title={intl.formatMessage({ id: '确定删除该评论吗' })}
              onConfirm={() => deleteComment((item.commentId))}
              okText={intl.formatMessage({ id: '确定' })}
              cancelText={intl.formatMessage({ id: '取消' })}
            >
              <span className='delete'>{intl.formatMessage({ id: '删除' })}</span>
            </Popconfirm>
              : ''}
          </div>
        ))}
        {total > 0 && <div style={{ textAlign: 'right' }}>
          <Pagination current={page} total={total} onChange={page => setPage(page)} />
        </div>}
      </div>
    </Modal>
  )
};

export default TextclipModal;