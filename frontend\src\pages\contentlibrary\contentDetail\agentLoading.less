/* 容器 */
.circle-loader-container {
  display: flex;
  align-items: center;
  flex-direction: column;
  padding-top: 35%;
  height: 100vh;
  margin-top: -20px;
  width: calc(100% + 40px);
  margin-left: -20px;
  background: var(--active-bg-color);
  font-family: 'Arial', sans-serif;

  /* 外层加载器 */
  .circle-loader {
    position: relative;
    width: 110px;
    height: 110px;
    border-radius: 50%;
    animation: loader-scale 1.5s infinite ease-in-out;
  }

  /* 加载进度条 */
  .circle-progress {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: conic-gradient(var(--primary-color) 0deg, var(--el-color-primary-light-8) 0deg);
    /* 固定蓝色 */
    // box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  }

  /* 内层圆 */
  .inner-circle {
    position: absolute;
    top: 8%;
    left: 8%;
    width: 84%;
    height: 84%;
    background: var(--active-bg-color);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    // box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  /* 百分比文字 */
  .percentage {
    font-size: 18px;
    font-family: 'Roboto', sans-serif;
    color: #000;
    font-weight: normal;
    // background: linear-gradient(135deg, #6a11cb, #2575fc);
    -webkit-background-clip: text;
    // -webkit-text-fill-color: transparent;
    animation: text-fade 1s infinite;
  }

  .text-loading {
    color: var(--el-color-primary-dark-2);
    font-size: 14px;
    margin-top: 15px;
    margin-left: 30px;
    display: flex;
  }

  /* 点点容器样式 */
.dots {
    display: inline-block;
    width: 24px; /* 固定宽度，确保文本对齐 */
    font-weight: bold;
    text-align: left;
  }
  
  /* 单个点点样式 */
  .dots span {
    opacity: 0; /* 初始不可见 */
    display: inline-block;
    animation: fade-in-sequence 2s infinite;
  }
  
  /* 为每个点点添加延迟，使其依次显示 */
  .dots span:nth-child(1) {
    animation-delay: 0s;
  }
  .dots span:nth-child(2) {
    animation-delay: 0.2s;
  }
  .dots span:nth-child(3) {
    animation-delay: 0.4s;
  }
  .dots span:nth-child(4) {
    animation-delay: 0.6s;
  }
  .dots span:nth-child(5) {
    animation-delay: 0.8s;
  }
  .dots span:nth-child(6) {
    animation-delay: 1s;
  }
  
  /* 动画效果 */
  @keyframes fade-in-sequence {
    0%, 60% {
      opacity: 0; /* 隐藏 */
    }
    80%, 100% {
      opacity: 1; /* 显示 */
    }
  }

}
