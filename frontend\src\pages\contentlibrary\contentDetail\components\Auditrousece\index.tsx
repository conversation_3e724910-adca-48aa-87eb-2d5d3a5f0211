import React, {
  FC,
  useEffect,
  useRef,
  useState,
  KeyboardEvent,
  FocusEvent,
  CSSProperties,
} from 'react';
import { Button, Input, message, Modal, Table } from 'antd';
import AuditService from '@/service/audit';
import { CheckCircleOutlined, CloseCircleOutlined, FileTextOutlined } from '@ant-design/icons';
import { getSensitiveWord } from '@/utils';
import './index.less';
import { useHistory, useIntl } from 'umi';
import contentListApis from '@/service/contentListApis';
const { TextArea } = Input;
const { confirm } = Modal;

const Auditrousece: FC<any> = ({ instanceId, contentId_, looklog, showBtn }) => {
  let history: any = useHistory();
  let isPublish = history.location?.query?.isPublish || false;
  let flowId = history.location?.query?.flowId || '';
  let myVerify = history.location?.query?.myVerify || false;

  // 审核的流程信息
  const [auditInfo, setAuditInfo] = useState<any>(null);
  // 驳回审核弹窗
  const [rejectisModalOpen, setRejectisModalOpen] = useState<boolean>(false);
  // 驳回理由
  const [rejectisReason, setRejectisReason] = useState<string>('');
  const intl = useIntl();

  // 查询审核流程进度
  useEffect(() => {
    getAuditProcess();
  }, []);

  // 查询审核流程进度
  const getAuditProcess = () => {
    if(isPublish){
      contentListApis.getAuditinfoById({id:instanceId}).then((res: any) => {
        if (res.errorCode == 'success') {
          res.extendMessage.auditState = res.extendMessage.auditStateName
          setAuditInfo(res.extendMessage);
        }
      });
    } else if (myVerify) {
      contentListApis.getFlowInfo({id:instanceId}).then((res: any) => {
        console.log(res, '审核流程进度');
        if (res.success ) {
          let auditStateName = ''
          switch (res.data.auditState) {
            case 0:
              auditStateName = '待审核'
              break;
            case 1:
              auditStateName = '审核中'
              break;
            case 2:
              auditStateName = '已通过'
              break;
            case 3:
              auditStateName = '已驳回'
              break;
            case 4:
              auditStateName = '撤销'
              break;
            case 5:
              auditStateName = '已发布'
              break;
            case 6:
              auditStateName = '已下架'
              break;
            default:
              break;
          }
          res.data.auditState = auditStateName
          setAuditInfo(res.data);
        }
      });
    } else {
      AuditService.getResourceAuditInfo(instanceId).then((res: any) => {
        if (res.errorCode == 'success') {
          let info = res.extendMessage.results.find((e: any) => e.resourceId == contentId_);
          info.extendedAttribute = info.extendedAttribute.split('/');
          // 截取下标2到最后一个
          info.extendedAttribute = info.extendedAttribute.slice(2).join('>');
          setAuditInfo(info);
        }
      });
    }
  }

  // 通过
  const pass = () => {
    confirm({
      content: intl.formatMessage({ id: '确认通过审核？' }),
      onOk() {
        if(isPublish){
          contentListApis.batchAuditPbulishResource(0, {[auditInfo.id]: flowId}).then((res: any) => {
            if (res.errorCode == 'success') {
              message.success(intl.formatMessage({ id: '审核通过' }));
              getAuditProcess();
            }
          })
        } else if (myVerify) {
          let params = {
            processNodes: [{id: auditInfo.id, nodeId: flowId || ''}],
            "operateType": 0,
            "auditComment": ''
          }
          contentListApis.flowProcess(params).then((res: any) => {
            if (res?.success) {
              message.success(intl.formatMessage({ id: '审核通过' }));
              getAuditProcess();
            }
          });
        }
        else{
          AuditService.auditprocess({
            instanceId: instanceId,
            resourceId: contentId_,
            status: intl.formatMessage({ id: '已审核' }),
            auditComment: intl.formatMessage({ id: '同意' })
          }).then((res: any) => {
            if (res.errorCode == 'success') {
              message.success(intl.formatMessage({ id: '审核通过' }));
            }
            getAuditProcess();
          })
        }
        
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  }

  // 驳回
  const biohui = () => {
    if (rejectisReason == '') {
      message.error(intl.formatMessage({ id: intl.formatMessage({ id: '请输入驳回理由' }) }));
      return;
    }
    getSensitiveWord(rejectisReason, intl.formatMessage({ id: '驳回理由' }), () => {
      if(isPublish){
        contentListApis.batchAuditPbulishResource(1, {[auditInfo.id]: flowId}, rejectisReason).then((res: any) => {
          if (res.errorCode == 'success') {
            message.success(intl.formatMessage({ id: '驳回成功' }));
            setRejectisModalOpen(false);
            setRejectisReason('');
            getAuditProcess();
          }
        })
      }else if (myVerify) {
        let params = {
          processNodes: [{id: auditInfo.id, nodeId: flowId || ''}],
          "operateType": 1,
          "auditComment": rejectisReason
        }
        contentListApis.flowProcess(params).then((res: any) => {
          console.log(res, '驳回');
          if (res?.success) {
            message.success(intl.formatMessage({ id: '驳回成功' }));
            setRejectisModalOpen(false);
            getAuditProcess();
            setRejectisReason('');
          }
        });
      }
      else{
        AuditService.auditprocess({
          instanceId: instanceId,
          resourceId: contentId_,
          status: intl.formatMessage({ id: '已驳回' }),
          auditComment: rejectisReason
        }).then((res: any) => {
          if (res.errorCode == 'success') {
            message.success(intl.formatMessage({ id: '驳回成功' }));
            setRejectisModalOpen(false);
            setRejectisReason('');
          }
          getAuditProcess();
        })
      }
      
    })
  }
  const publish =() => {
    contentListApis.batchProcessPbulishResource(2, [auditInfo.id]).then(res => {
      if (res.errorCode == 'success') {
        message.success(intl.formatMessage({ id: '发布成功' }));
        getAuditProcess();
      }
      }
    )
  }
  const remove =() => {
    if (myVerify) {
      let params = {
        processNodes: [{id: auditInfo.id, nodeId: flowId || ''}],
        "operateType": 4,
        "auditComment": ''
      }
      contentListApis.flowProcess(params).then((res: any) => {
        if (res?.success) {
          message.success(intl.formatMessage({ id: '下架成功' }));
          getAuditProcess();
        }
      });
      return
    }
    contentListApis.batchProcessPbulishResource(3, [auditInfo.id]).then(res => {
      if (res.errorCode == 'success') {
        message.success(intl.formatMessage({ id: '下架成功' }));
        getAuditProcess();
        }
      }
    )
  }

  return (
    <div>
      {
        auditInfo &&
        <div className='video_btn'>
          <div className='left_'>
            {
              auditInfo.remarks && <div className='sepicalTopic'>
                { auditInfo.auditState == intl.formatMessage({ id: '已驳回' }) && <CloseCircleOutlined style={{ color: 'red', fontSize: 25, marginTop: '2px', marginRight: 20 }}/>}
                <div>申请发布到：</div>
                <div className="topic_text" title={auditInfo.remarks ? JSON.parse(auditInfo.remarks)?.map((item:any) => item.value).join(',') : '-'}>
                  { auditInfo.remarks ? JSON.parse(auditInfo.remarks)?.map((item:any) => item.value).join(',') : '-'}
                  </div>
              </div>
            }
            {
              auditInfo.auditState == intl.formatMessage({ id: '已审核' }) &&
              <div className='audit_pass'>
                <CheckCircleOutlined style={{ color: 'red', fontSize: 25, marginTop: '3px', marginLeft: 20 }} />
                <span style={{ lineHeight: '32px', marginLeft: 5 }}>{intl.formatMessage({ id: '已通过' })}</span>
              </div>
            }
            {
              auditInfo.auditState == intl.formatMessage({ id: '已驳回' }) &&  !auditInfo.remarks && <CloseCircleOutlined style={{ color: 'red', fontSize: 25, marginTop: '2px', marginLeft: 20 }} />
            }
            {!isPublish && !myVerify && <><span className='audit_title'>{intl.formatMessage({ id: '申请共享到' })}</span>
            <span className='audit_folder'>{auditInfo.extendedAttribute}</span></>}
            {
              auditInfo.auditState == intl.formatMessage({ id: '已驳回' }) &&
              <div className='audit_reject'>
                <span className='audit_reason'>{intl.formatMessage({ id: '已驳回，理由' })}{auditInfo.auditComment || '无'}</span>
              </div>
            }
            {
              showBtn && 
              <div className='audit_option'>
                {
                  !myVerify && auditInfo.auditState == intl.formatMessage({ id: '待审核' }) &&<>  
                  <Button type="primary" icon={<CheckCircleOutlined />} onClick={pass}>{intl.formatMessage({ id: '通过' })}</Button>
                  <Button type="primary" danger icon={<CloseCircleOutlined />} style={{ marginLeft: '30px' }} onClick={() => {
                    setRejectisModalOpen(true);
                  }}>{intl.formatMessage({ id: '驳回' })}</Button></>
                }
                {
                  myVerify && (auditInfo.auditState == intl.formatMessage({ id: '待审核' }) || auditInfo.auditState == intl.formatMessage({ id: '审核中' })) &&<>  
                  <Button type="primary" icon={<CheckCircleOutlined />} onClick={pass}>{intl.formatMessage({ id: '通过' })}</Button>
                    <Button type="primary" danger icon={<CloseCircleOutlined />} style={{ marginLeft: '30px' }} onClick={() => {
                      setRejectisModalOpen(true);
                    }}>{intl.formatMessage({ id: '驳回' })}</Button></>
                }
                {
                isPublish &&<>
                { ['已驳回', '已下架'].includes(auditInfo.auditState) && <Button type="primary" danger icon={<CheckCircleOutlined />} style={{ marginLeft: '30px' }} onClick={() => {
                  publish();
                }}>{intl.formatMessage({ id: '发布' })}</Button>}
                { ['已通过', '已发布'].includes(auditInfo.auditState) && <Button type="primary" danger icon={<CheckCircleOutlined />} style={{ marginLeft: '30px' }} onClick={() => {
                  remove();
                }}>{intl.formatMessage({ id: '下架' })}</Button>}
                </>}
                {
                  myVerify && ['已通过'].includes(auditInfo.auditState) && <Button type="primary" danger icon={<CheckCircleOutlined />} style={{ marginLeft: '30px' }} onClick={() => {
                    remove();
                  }}>{intl.formatMessage({ id: '下架' })}</Button>
                }
              </div>
            }
            
          </div>
          <div className='right_'>
          {!myVerify && <Button type="primary" className='look_log' ghost icon={<FileTextOutlined />} onClick={looklog}>{intl.formatMessage({ id: '查看审核日志' })}</Button>}
          </div>
        </div>
      }
      <div className='rejectis_box'>
        <Modal title={intl.formatMessage({ id: '驳回理由' })} centered footer={false} open={rejectisModalOpen} getContainer={false} onOk={() => {

        }} onCancel={() => {
          setRejectisModalOpen(false);
        }}>
          {/* <span className='tip_span'>请输入驳回理由,最多30字：</span> */}
          <TextArea rows={6} placeholder={intl.formatMessage({ id: '请输入驳回理由,最多30字：' })} maxLength={30} onChange={(e) => setRejectisReason(e.target.value)} />
          <div className='confim_box'>
            <Button type="primary" ghost style={{ marginTop: '20px', width: 100 }} onClick={biohui}>{intl.formatMessage({ id: '确认' })}</Button>
          </div>
        </Modal>
      </div>
    </div>
  );

}

export default Auditrousece;
