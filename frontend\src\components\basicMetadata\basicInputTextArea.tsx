import React, { FC } from 'react'
import { IBasicItemProps } from './basicMetadata'
import { Input, Col, Row, Form } from 'antd'
import { searchKeywords } from '@/utils';


const BasicInputTextArea: FC<IBasicItemProps> = (props) => {
    let newValue:any = props.item.value;
    let flag:boolean=false;
    if(props.item.fieldName === 'describe'){
        const temp = searchKeywords(newValue,'basic');
        flag = temp.index>-1;
        newValue = temp.index>-1?
        <span>
            {temp.beforeStr}
            <span className="key-search-value">{temp.word}</span>
            {temp.afterStr}
        </span>
        :newValue;
    }
    return (
        <Form.Item
            label={props.item.alias}
            name={props.item.fieldName}
            rules={[{ required: props.item.isReadOnly || !props.edit ? false : props.item.isMustInput }]}
            className={flag?'describe_highlight':''}
        // hasFeedback={props.item.canEdit}
        >
            {
                props.item.isReadOnly || !props.edit ? <div style={{ lineHeight: '32px', wordBreak: 'break-all', whiteSpace: 'normal', textAlign: 'justify' }}>{newValue}</div> : <Input.TextArea rows={4} />
            }
        </Form.Item>
    )
}

export default BasicInputTextArea