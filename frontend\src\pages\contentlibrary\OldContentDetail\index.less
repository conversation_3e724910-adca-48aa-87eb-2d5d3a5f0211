* {
  box-sizing: border-box;
}

#root, body, html {
  height: 100%;
}


.contentdetail_container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #F2F2F2;
  .contentdetail_top{
    // padding:13px 0 0 30px;
    padding: 0 15px;
    height: 70px;
    width: 100%;
    background-color: #fff;
    .top_title{
      font-size: 18px;
      font-weight: 800;
      line-height: 70px;
    }
    .score{
      .score_people{
        margin-left: 8px;
      }
    }
  }
  .contentdetail_bottom{
    flex: 1;
    width: 100%;
    padding: 20px 15px;
    display: flex;
    .exhibition{
      width: 1390px;
      background-color: #fff;
      margin-right: 27px;
      height: ~"calc(100vh - 110px)";
    }
    .essential_information{
      width:470px;
      height: ~"calc(100vh - 110px)";
      overflow-y: auto;
      background-color: #fff;
      position: relative;
      .essential_title{
        padding-left: 46px;
        width: 100%;
        height: 88px;
        line-height: 88px;
        font-size: 20px;
        font-weight: 800;
      }
      .blue{
        width:35px;
        height:5px;
        background:rgba(76,152,239,1);
        margin-left: 46px;
        margin-bottom: 25px;
      }
      .edit{
          position: absolute;
          right: 29px;
          top: 35px;
          .editor_selection{
            font-size: 17px;
            display: flex;
            flex-direction: column;
            height: 50px;
            justify-content: space-between;
          }
          .icon:hover{
            color: var(--primary-color);
          }
          img{
            width: 50px;
          }
      }
    }
  }
  .vjs-paused{
  .vjs-big-play-button{
    display: block;
  }
}
}

