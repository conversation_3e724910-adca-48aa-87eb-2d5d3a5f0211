import React from 'react'
import { Modal } from 'antd'
import { useSelector, useDispatch, useIntl } from 'umi'
import { IUpload } from '@/models/upload'
import UploadModalBody from './uploadModalBody'
import "./style.less"
import uploader from '../core/uploader'
const intl = useIntl();
const UploadModal = () => {
    const { showModal, tasks } = useSelector<{ upload: IUpload }, IUpload>(({ upload }) => {
        return upload
    })
    const dispatch = useDispatch()
    const onCancel = () => {
        dispatch({
            type: 'upload/changeModal',
            payload: {
                value: false
            }
        })
    }
    const onOk = () => {
        uploader.createTask(tasks)
    }
    return (
        <Modal
            visible={showModal}
            title={intl.formatMessage({ id: '文件上传' })}
            onCancel={onCancel}
            onOk={onOk}
            width={1000}
        >
            <UploadModalBody tasks={tasks}></UploadModalBody>
        </Modal>
    )
}

export default UploadModal