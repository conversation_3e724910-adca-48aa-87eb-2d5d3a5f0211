import React, { FC, useState, useEffect } from 'react';
import './index.less';
import { useDispatch, useSelector } from '@@/plugin-dva/exports';
import { Dropdown, Image, Menu, message, Divider, Popover, Badge } from 'antd';
import { useHistory, useParams } from 'umi';
import { IconFont } from '@/components/iconFont/iconFont';
import globalParams, { ModuleCfg } from '@/permission/globalParams';
import perCfg from '@/permission/config';
import { IPermission } from '@/models/permission';
import Icon from '@ant-design/icons';
import { ReactComponent as clip_icon } from '@/images/icons/clip_icon.svg';
import { ReactComponent as literature_icon } from '@/images/icons/literature_icon.svg';
import loginApis from "@/service/loginApis"
import MessageBox from "@/components/Message"
import { IConfig } from '@/models/config';
import { HeaderComponent } from "./header.js";
import "./style.css";
interface LinkItem {
  key: string;
  name: string;
  href?: string;
  target?: string;
  disabled?: boolean;
}

interface HeaderProps {
  subtitle?: string;
  navList?: LinkItem[];
  showNav: boolean;
  user_Info:any;
  navActive?: string;
  ifBack?: boolean;
  showOther?: boolean;
}

interface NavProps {
  list: LinkItem[];
  active?: string;
}

interface UserAvatarProps {
  username: string;
  avatar: string;
  work: boolean;
  // teacher: boolean;
  student: boolean;
  admin: boolean;
  // rman: boolean;
  // joveone: boolean;
  workUrl: string;
  personal: boolean;
  onLogout: () => void;
}

const Nav: FC<NavProps> = ({ list, active }) => {
  const { title } = useSelector<any, any>(
    state => state.themes,
  );

  let history: any = useHistory();
  const [navList, setNavList] = useState<any>([]);
  const [dropdownList, setDropdownList] = useState<any>([]);


  useEffect(() => {
    if (list.length > 0) {
      handleChange();
      window.addEventListener('resize', handleChange);
      return () => {
        window.removeEventListener('resize', handleChange);
      };
    }
  }, [list]);
  const handleChange = () => {
    if (list.length > 0) {
      const width = window.innerWidth > 1300 ? window.innerWidth : 1300;
      const logoWidth = title.length * 20; // 一个字20px
      const otherWidth = 470; // logo + 工具栏 + 消息 + 用户 + 间距
      const navWidth = width - logoWidth - otherWidth;
      let curWidth = 0;
      const i = list.findIndex((item: any) => {
        curWidth += item.name.length * 14 + 40;
        console.info(curWidth, navWidth)
        return curWidth > navWidth;
      });
      const index = i === -1 ? list.length : i - 1;
      const nav = list.slice(0, index);
      const other = list.slice(index);
      setNavList(nav);
      setDropdownList(other);
    }
  };
  return (
    <div className="nav-menu-wrapper">
      {navList
        .filter(l => !l.disabled)
        .map((link: any, index: number) =>
          link.href ? (
            <a
              key={index}
              className={active?.includes(link.key) ? 'active' : ''}
              href={link.href}
              target={link.target}
            >
              <div className="nav-item">{link.name}</div>
            </a>
          ) : (
            <span key={index}>{link.name}</span>
          ),
        )}
        {navList.length < list.length && <Dropdown overlay={<div className='drop-container'>{dropdownList.map((item: any) => <div className='drop-item' onClick={() => {
            if (item.href) {
              window.open(item.href, item.target || '_self');
            }
          }}>{item.name}</div>)}</div>} placement="bottomCenter" getPopupContainer={(e: any) => e.parentNode} >
          <span key="more" style={{ cursor: "pointer", color: "#666", marginBottom: "4px" }}>更多</span>
        </Dropdown>}
    </div>
  );
};

const UserAvatar: FC<UserAvatarProps> = ({
  username,
  avatar,
  work,
  admin,
  workUrl,
  student,
  personal,
  onLogout,
}) => {
  // let workUrl = ""
  // let target = ""
  // if (teacher) {
  //   workUrl = "/learn/workbench/#/course"
  //   target = "my_teaching"
  // } else if (rman) {
  //   workUrl = "#/basic/rmanCenterList"
  //   target = "source_manage"
  // } else if (admin) {
  //   workUrl = "/unifiedplatform/#/basic"
  //   target = "sys_manage"
  // } else if (joveone) {
  //   workUrl = "/joveone"
  //   target = "joveone"
  // }
  const menu = (
    <Menu>
      {work && (
        <Menu.Item>
          <a href={workUrl} target='work'>工作台</a>
        </Menu.Item>
      )}
      {student && (
        <Menu.Item>
          <a href="/unifiedplatform/#/learn/mycourse" target="my_study">我的学习</a>
        </Menu.Item>
      )}
      {personal && (
        <Menu.Item>
          {/* <a href="/unifiedplatform/#/personal/info" target="personal_center">账号管理</a> */}
          <a href="/unifiedplatform/#/personal/home" target="personal_center">个人中心</a>
        </Menu.Item>
      )}
      {admin && (
        <Menu.Item>
          {/* <a href="/unifiedplatform/#/personal/info" target="personal_center">账号管理</a> */}
          <a href="/unifiedplatform/#/management" target="sys_manage">管理中心</a>
        </Menu.Item>
      )}
      <Menu.Item onClick={onLogout}>退出登录</Menu.Item>
    </Menu>
  );

  const microMajorMenu = <Menu><Menu.Item onClick={onLogout}>退出登录</Menu.Item></Menu>

  const getMenu = () => {
    if (location.hash.includes('micromajor')) {
      return microMajorMenu
    }
    return menu
  }

  return (
    <Dropdown overlay={()=>getMenu()} className="user-avatar-wrapper">
      <div>
        <Image
          src={avatar || require('@/images/login/default-avatar.png')}
          fallback={require('@/images/login/default-avatar.png')}
          preview={false}
        />
        <p className="user-name" title={username}>{username}</p>
      </div>
    </Dropdown>
  );
};

const OtherNav: FC<NavProps> = ({ list }) => {
  const getIcon = (iconName: string) => {
    const ClipIcon = (props: any) => <Icon component={clip_icon} {...props} />;
    const LiteratureIcon = (props: any) => <Icon component={literature_icon} {...props} />;
    switch(iconName) {
      case "joveone":
        return <IconFont type='iconzaixianbianji' />;
      case "literature":
        return <LiteratureIcon />;
      case "textclip":
        return <IconFont type='iconyuyinbianji' />;
      default:
        return <span></span>;
    }
  }
  //antd 版本不兼容
  const menu = (
    <Menu>
      {list
        .filter(l => !l.disabled)
        .map((item:any,index:number) =>{
          return <Menu.Item key={item.key}>
                  <a className='other' href={item.href} target={item.target}>{getIcon(item.key)}{item.name}</a>
                </Menu.Item>
            }
        )}
    </Menu>
  )
  return (
    <div className='other-nav-wrapper'>
      {
       list.filter(l => !l.disabled).length>0 &&
        <Dropdown overlay={menu}>
          <div>
            <IconFont type='icongongjuxiang'/>
            工具箱
          </div>
        </Dropdown>
      }
    </div>
  );
};
const userInfo = JSON.parse(localStorage.getItem('userinfo') || '{}');
const Header: FC<HeaderProps> = ({
  subtitle,
  // navList,
  user_Info,
  navActive,
  showNav,
  ifBack = true,
  showOther = true
}) => {
  const { title, logoUrl, isShow } = useSelector<any, any>(
    state => state.themes,
  );
  let history: any = useHistory();
  let hideHead = history.location?.query?.hideHead || false;
  let hideBanner = history.location?.query?.hidebanner || false; // 是否显示导航栏
  let target = history.location?.query?.target || ''
  const [headerList, setHeaderList] = useState<any[]>([]);
  const [unReadCount, setUnreadCount] = useState<number>(0);
  const dispatch = useDispatch();
  const params = window.location.href.split('/')[window.location.href.split('/')?.length - 1] || ''
  const shareFlag_ = params?.split('_')[1];
  const  configs:any  = useSelector<{ config: any }, IConfig>(
    ({config})=>config
 );
 //  两个contentId_为...的资源详情页面不显示头部
 const urlSon = window.location.href.split('/')[window.location.href.split('/')?.length - 2]
 const url = window.location.href.split('/')[window.location.href.split('/')?.length - 3]
 const standardUrl = url + '/' + urlSon
  // const { modules, permissions } = useSelector<
  //   { global: any },
  //   { modules: string[]; permissions: string[] }
  // >(state => state.global);
  const { modules, parameterConfig, permissions,rmanGlobalParameter } = useSelector<{ permission: any }, IPermission>(
    ({ permission }) => permission,
  );
 let notShowHeader = standardUrl === 'basic/rmanDetail' && configs.mobileFlag && parameterConfig.target_customer as string =='npu'
  const handleLogout = async () => {
    window.location.href = `/unifiedlogin/v1/loginmanage/loginout/go`;
    // let res = await api.user.logout();
    // if (res && res.errorCode === 'success') {
    //   message.success('注销成功');
    //   // history.push('/basic?action=login');
    //   // window.location.replace(`/cvodweb`);
    // }
  };

  useEffect(() => {
    // loginApis.fetchHeaderList().then((res: any) => {
    //   if (res.errorCode === 'success') {
    //     const list = res.extendMessage.filter((item: any) => item.name !== '在线剪辑').map((item: any) => ({
    //       key: item.isSystem ? (item.link.includes('rman') ? 'rman' : item.link.split('#/')?.[1]?.split("/")?.[0]) : "key",
    //       name: item.name,
    //       href: item.link,
    //       target: item.openWay ? item.link.split('/')?.[1] || item.name : null,
    //       disabled: item.disabled
    //     }))
    //     setHeaderList(list)
    //   }
    // })
    getUnreadCount()
    const timer = setInterval(getUnreadCount, 10000)
    return () => {
      clearInterval(timer)
    }
  }, [])

  const getUnreadCount = () => {
    loginApis.reqUnreadMessageCount().then(res => {
      if (res?.errorCode === 'success') {
        setUnreadCount(res.extendMessage);
      }
    });
  };
  const leftMenuChange = ()=>{
    dispatch({
      type:'config/updateState',
      payload:{
        leftMenuExpand:!configs.leftMenuExpand
      }
    })
  }

  const getLogHef = () => {
    let href = "/learn/resource/new?tab=2"
    // if (location.hash.includes('micromajor')) {
    //   href = '/learn/search/microMajor'
    // } else {
    //   href =(process.env.NODE_ENV === 'development' ? 'http://172.16.151.202' : '') + '/unifiedplatform/v1/app/home/<USER>'
    // }
    return href
  }

  const getLogTitle = () => {
    let name: any = '知了智慧教育资源管理平台'
    const title = localStorage.getItem('theme_config')
    if (title) {
      const config = JSON.parse(title)
      name = config?.title
    }
    // if (location.hash.includes('micromajor')) {
    //   name = '微专业平台'
    // }
    // else {
    //   name = isShow ? (parameterConfig?.target_customer == 'shangHaiTech' ? '工作空间' : "工作台") : null
    // }
    return name? <h2>{name}</h2>: null
  }
  return (
    <>
      {!hideBanner && !hideHead && parameterConfig.target_customer != null && parameterConfig.target_customer as string !=='npu' && target!=='custom' && <div className={`uf-header-wrapper${configs.mobileFlag?' mobileContainer':''}`}>
      <div className="uf-header-left-part">
      {configs.mobileFlag && <IconFont onClick={leftMenuChange} type='iconwenzhangpailie2-221'/>}
        <a
          href={getLogHef()}
          className="home-part"
        >
          {/* {ifBack && (
            <div className="go-back-btn">
              <IconFont type="iconjiantouda" />
            </div>
          )} */}
          {configs.mobileFlag &&<img src={require('@/images/login/home.png')} className="uf-header-icon" alt="" />}
          <div className='icon_box'>
            <img
              src={logoUrl || require('@/images/login/default_logo.png')}
              className="uf-header-icon"
            />
          </div>
          {getLogTitle()}
        </a>
        {/* {subtitle && (
          <>
            <h4 style={{marginLeft:'26px'}}>|</h4>
            <h4>{subtitle}</h4>
          </>
        )} */}
      </div>
      <div className="uf-header-right-part">
        {/* {showNav && <Nav list={headerList} active={navActive} />} */}
        {/* {showOther ? <OtherNav list={[
          {
            key: 'joveone',
            name: '在线剪辑',
            href: '/joveone',
            target: "joveone",
            disabled:
              !modules.includes(ModuleCfg.jove) ||
              !permissions.includes(perCfg.jove_use),
          },
          {
            key: 'textclip',
            name: '语音剪辑',
            href: '/textclip/#/clip/myTextClip',
            target: "textclip",
            disabled:!modules.includes(ModuleCfg.textclip),
          }
        ]} /> : ''} */}
        {shareFlag_ !== 'share' && <Popover overlayClassName='message-popover' destroyTooltipOnHide={true} content={<MessageBox readChange={getUnreadCount} />}>
          <Badge count={unReadCount} offset={[5, 0]}>
            <div className='message-container'><IconFont type="iconxiaoxitongzhi" /></div>
          </Badge>
        </Popover>}

        {
          user_Info
          ?
          <UserAvatar
            username={user_Info?.nickName}
            avatar={user_Info?.avatar}
            workUrl={"/learn/workbench/#/home"}
            work={modules.includes(ModuleCfg.work)}
            // joveone={modules.includes(ModuleCfg.jove) && permissions.includes(perCfg.jove_use)}
            // teacher={modules.includes(ModuleCfg.teacher)}
            student={modules.includes(ModuleCfg.student)}
            admin={modules.includes(ModuleCfg.manager)}
            // rman={
            //   modules.includes(ModuleCfg.rman) &&
            //   permissions.includes(perCfg.show_resource_management)
            // }
            personal={modules.includes(ModuleCfg.personal)}
            onLogout={handleLogout}
          />:
          <a href={`/unifiedlogin/v1/loginmanage/login/direction?redirect_url=${encodeURIComponent(window.location.href.replace('javascript:',''))}`}>登录</a>
        }
      </div>
    </div>}
    {parameterConfig.target_customer as string === 'npu' && !hideHead && target!=='custom' && !notShowHeader && <HeaderComponent token={userInfo?.extend?.jwt} />}
    </>
  );
};
export default Header;
