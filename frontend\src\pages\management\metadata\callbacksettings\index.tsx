import React, { FC, useEffect, useState } from 'react';
import MetadataService from '@/service/metadataService';
import { Button, Input, Switch, Space, Modal, Form, message, Spin, Divider, Checkbox, Row, Col, Radio } from 'antd';
import './index.less';
import { EditOutlined } from '@ant-design/icons';
const CallbackSettings: FC = () => {
    const [dataLoading, setDataLoading] = useState(false);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [form] = Form.useForm();
    const [callbackData, setCallbackData] = useState<any>({});
    const [isAuthSetting, setModalType] = useState(false); // 默认是回调设置
    const [updateCallbackData, setupdateCallbackData] = useState();
    useEffect(() => {
        fetchCallbackSettings();
    }, []);

    useEffect(() => {
        form.setFieldsValue({
            url: callbackData?.returnsettings?.returnurl || '', // 避免 undefined
        });
    }, [callbackData, form]);
    useEffect(() => {
        if (updateCallbackData) {
            console.log(updateCallbackData, 'updateCallbackData');
            fetchCallbacksavesettings(updateCallbackData)
        }
    }, [updateCallbackData]);


    const fetchCallbackSettings = async () => {
        setDataLoading(true);
        try {
            const res = await MetadataService.returnsettings();
            setCallbackData(res.data || {});
        } catch (error) {
            message.error('获取回调设置失败');
        } finally {
            setDataLoading(false);
        }
    };


    const callbackSettings = () => {
        setIsModalVisible(true)
        setModalType(false)
    }

    const callbackSettingsfalse = () => {
        setIsModalVisible(true)
        setModalType(true)
    }


    const fetchCallbacksavesettings = async (updatedData: any) => {
        try {
            const res = await MetadataService.savesettings(updatedData);
            if (res.success) {
                fetchCallbackSettings();
                message.success('修改成功');
            } else {
                message.success('修改失败');
            }
        } catch (error) {
            message.error('获取回调设置失败');
        } finally {

        }
    };

    const handleSave = () => {
        form.validateFields().then(values => {
            const updatedData = {
                returnsettings: {
                    returnurl: values.url,
                    returntype: values.returntype,
                    returnevents: {
                        fileuploadcomplete: values.returnevents?.includes("fileuploadcomplete") ? 1 : 0,
                        imageuploadcomplete: values.returnevents?.includes("imageuploadcomplete") ? 1 : 0,
                        createauditcomplete: values.returnevents?.includes("createauditcomplete") ? 1 : 0,
                        deletemediacomplete: values.returnevents?.includes("deletemediacomplete") ? 1 : 0,
                    }
                },
                returnauthentication: {
                    ...callbackData?.returnauthentication,
                }
            }
            const onselectstart = {
                ...callbackData,
                returnauthentication: {
                    enable: callbackData?.returnauthentication?.enable,
                    key: values.authToken  // 修改 key 的值
                }
            };
            if (isAuthSetting) {
                // fetchCallbacksavesettings(onselectstart)
                setupdateCallbackData(onselectstart)
            } else {
                setupdateCallbackData(updatedData)
            }
            setIsModalVisible(false);
        }).catch(errorInfo => {
            console.log("表单验证失败：", errorInfo);
        });
    };

    return (
        <div className="metadata_basic_container">
            <h4 className='container' >回调设置</h4>

            <div className='setting' >
                <div className="setting-item">
                    <div className="labels">回调设置</div>
                    <Button onClick={() =>
                        callbackSettings()
                    } icon={<EditOutlined />} type="link">修改设置</Button>
                </div>
                <div className="setting-item">
                    <div className="label">回调方式</div>
                    <div>{callbackData?.returnsettings?.returntype}</div>
                </div>
                <div className="setting-item">
                    <div className="label">回调 URL</div>
                    <div>
                        {callbackData?.returnsettings?.returnurl || '未设置'}
                        <div className='subheading' >当产生回调事件时，点播服务端会向该URL发起HTTP POST请求</div>
                    </div>
                </div>
                <div className="setting-item">
                    <div className="label">回调事件</div>
                    <div>
                        <Row>
                            <Col span={12}>
                                <Checkbox disabled checked={callbackData?.returnsettings?.returnevents?.fileuploadcomplete === 1}>
                                    视频上传完成
                                </Checkbox>
                            </Col>
                            <Col span={12}>
                                <Checkbox disabled checked={callbackData?.returnsettings?.returnevents?.imageuploadcomplete === 1}>
                                    图片上传完成
                                </Checkbox>
                            </Col>
                            <Col span={12}>
                                <Checkbox disabled checked={callbackData?.returnsettings?.returnevents?.createauditcomplete === 1}>
                                    人工审核完成
                                </Checkbox>
                            </Col>
                            <Col span={12}>
                                <Checkbox disabled checked={callbackData?.returnsettings?.returnevents?.deletemediacomplete === 1}>
                                    媒体删除完成
                                </Checkbox>
                            </Col>
                        </Row>
                        <div className='subheading'>
                            您选择的回调事件处理完成后，点播服务会进行事件通知，事件通知的实现方式
                        </div>
                    </div>
                </div>
                <Divider />
                <div className="setting-item">
                    <div className="labels">回调鉴权</div>
                    <Button onClick={() =>
                        callbackSettingsfalse()
                    } icon={<EditOutlined />} type="link">修改设置</Button>

                </div>
                <div className="setting-item">
                    <div className="label">回调鉴权</div>
                    <div>
                        <Switch onChange={(checked) => {
                            console.log('开关状态变更：', checked);  // 添加打印日志
                            // 这里需要添加状态更新逻辑，例如：
                            setupdateCallbackData({
                                ...callbackData,
                                returnauthentication: {
                                    ...callbackData?.returnauthentication,
                                    enable: checked ? 1 : 0
                                }
                            });
                        }} checked={callbackData?.returnauthentication?.enable === 1} />
                        <div className='subheading' >在HTTP回调时增加特定签名头，供回调消息接收服务端进行签名认证，防止非法或无效请求，</div>
                    </div>
                </div>
                <div className="setting-item">
                    <div className="label">鉴权密钥</div>
                    <div>{callbackData?.returnauthentication?.key || '未生成'}</div>
                </div>
            </div>

            <Modal
                title={isAuthSetting ? "设置回调鉴权" : "修改回调设置"}
                open={isModalVisible}
                onCancel={() => setIsModalVisible(false)}
                onOk={handleSave}
            >
                <Form
                    form={form}
                    initialValues={{
                        url: callbackData?.returnsettings?.returnurl || '',
                        returntype: callbackData?.returnsettings?.returntype,
                        returnevents: [
                            callbackData?.returnsettings?.returnevents?.fileuploadcomplete === 1 ? "fileuploadcomplete" : null,
                            callbackData?.returnsettings?.returnevents?.imageuploadcomplete === 1 ? "imageuploadcomplete" : null,
                            callbackData?.returnsettings?.returnevents?.createauditcomplete === 1 ? "createauditcomplete" : null,
                            callbackData?.returnsettings?.returnevents?.deletemediacomplete === 1 ? "deletemediacomplete" : null,
                        ].filter(Boolean),
                        authToken: callbackData?.returnauthentication?.key || '',
                    }}
                >
                    {isAuthSetting ? (
                        <Form.Item extra="最长32位，必须同时包含大写字母、小写字母和数字" label="鉴权 Token" name="authToken" rules={[{ required: true, message: '请输入鉴权 Token' }]}>
                            <Input />
                        </Form.Item>
                    ) : (
                        <>
                            <Form.Item label="回调方式" name="returntype" rules={[{ required: true, message: '请选择回调方式' }]}>
                                <Radio.Group>
                                    <Radio value="HTTP 请求">HTTP 请求</Radio>
                                    <Radio value="HTTPS 请求">HTTPS 请求</Radio>
                                </Radio.Group>
                            </Form.Item>
                            <Form.Item extra="当产生回调事件时，点播服务端会向该URL发起HTTP POST请求" label="回调 URL" name="url" rules={[{ required: true, message: '请输入回调 URL' }]} >
                                <Input />
                            </Form.Item>
                            <Form.Item extra="您选择的回调事件处理完成后，点播服务会进行事件通知，事件通知的实现方式" name="returnevents" label="回调事件">
                                <Checkbox.Group>
                                    <Row>
                                        <Col span={12}>
                                            <Checkbox value="fileuploadcomplete" style={{ lineHeight: '32px' }}>
                                                视频上传完成
                                            </Checkbox>
                                        </Col>
                                        <Col span={12}>
                                            <Checkbox value="imageuploadcomplete" style={{ lineHeight: '32px' }}>
                                                图片上传完成
                                            </Checkbox>
                                        </Col>
                                        <Col span={12}>
                                            <Checkbox value="createauditcomplete" style={{ lineHeight: '32px' }}>
                                                人工审核完成
                                            </Checkbox>
                                        </Col>
                                        <Col span={12}>
                                            <Checkbox value="deletemediacomplete" style={{ lineHeight: '32px' }}>
                                                媒体删除完成
                                            </Checkbox>
                                        </Col>
                                    </Row>
                                </Checkbox.Group>
                            </Form.Item>
                        </>
                    )}
                </Form>
            </Modal>

        </div >
    );
};

export default CallbackSettings;
