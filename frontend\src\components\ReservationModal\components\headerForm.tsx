import React, { useState, useEffect } from 'react';
import { Button, Form, Input, DatePicker, TreeSelect} from 'antd';
import contentListApis from '@/service/contentListApis';
import TurnThePageDataItem from '../../turnThePageDataItem';

const { RangePicker } = DatePicker
interface CreateModalProps {
  modalVisible: boolean;
  modalClose: () => void;
  setSearchData: (form) => void;
  dataSource: any;
  userroles: any;
  tabKey: any;
  className_?: string;
  refresh?: (tag?: boolean, voice?: boolean, point?: boolean) => any;
}

const HeaderForm: React.FC<CreateModalProps> = props => {
    const [form] = Form.useForm()
    const { userroles, setSearchData, tabKey } = props;
    // 语音
    const [testTree, setTestTree] = useState<any>([])
    const [building, setBuilding] = useState<any>([])
    const [classroom, setClassroom] = useState<any>([])
    useEffect(() => {
      gettree()
    }, [])
    const gettree = () => {
      contentListApis.searchtree().then(res => {
        if (res.error_msg === 'Success' && res.extend_message.length) {
          setTestTree(res.extend_message)
        }
      })
    }
    return (
      <Form
        layout='inline'
        name="basic"
        form={form}
      >
        <Form.Item
          label={'课程名称'}
          name={'courseName'}
        >
          <Input style={{ width: 140 }} autoComplete="off" />
        </Form.Item>
        <Form.Item name="classroom">
          <TreeSelect
            treeData={testTree}
            placeholder="选择校区/教学楼/教室"
            fieldNames={{ label: 'name', value: 'id', children: 'children' }}
            style={{ width: 230 }}
            allowClear />
        </Form.Item>
        {
          (userroles.includes('r_resource_manager') || userroles.includes('r_sys_manager') || userroles.includes('admin_S1')) && <TurnThePageDataItem
          multiple={false}
          required={false}
          message='请选择教师'
          name='teacher'
          key="teacher"
          type={0}
        />
        }
        {tabKey === 'item-2' &&
          <Form.Item name='date'>
            <RangePicker format="YYYY-MM-DD" />
        </Form.Item>
        }
        <Form.Item >
          <Button onClick={() => {
            let value = form.getFieldsValue()
            setSearchData(value)
          }}>搜索</Button>
        </Form.Item>
      </Form>
    );
}

export default HeaderForm;

