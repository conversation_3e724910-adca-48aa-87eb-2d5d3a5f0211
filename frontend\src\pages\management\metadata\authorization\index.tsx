import React, { FC, useEffect, useRef, useState } from 'react';
import MetadataService from '@/service/metadataService';
import { SettingOutlined } from '@ant-design/icons';
import {
    Table,
    Button,
    Input,
    Space,
    Modal,
    Form,
    message,
    Collapse,
} from 'antd';
import './index.less';
import TextArea from 'antd/lib/input/TextArea';

const { Panel } = Collapse;
/**
 * hive基础数据
 * @constructor
 */
const authorization: FC = () => {
    const [dataLoading, setDataLoading] = useState(false);
    const [typeList, setTypeList] = useState<MetadataTypes.HiveMetadataType[]>(
        [],
    );
    const [modalVisible, setModalVisible] = useState(false);
    const [isAdd, setIsAdd] = useState(false);
    const [editMetadataForm] = Form.useForm();

    const [uuid, setUid] = useState(false);
    const [Keyshowd, setKeyshowd] = useState([]);
    const [detailsshow, setdetailsshow] = useState(false);
    useEffect(() => {
        setDataLoading(true);
        fetchaksklist()
    }, []);

    useEffect(() => {
        console.log(Keyshowd, 'Keyshowd');
    }, [Keyshowd]);

    const fetchaksklist = () => {
        MetadataService.fetchaksklist()
            .then(res => {
                setTypeList(res?.extendMessage);
                setDataLoading(false);
            })
            .catch(err => {
                setDataLoading(false);
            });
    }

    const columns = [
        {
            title: 'AccessKey ID',
            dataIndex: 'ak',
        },
        {
            title: 'Access Key Secret',
            dataIndex: 'sk',
            render: (code: string, record: any) => (
                <span onClick={() => {
                    setKeyshowd(record)
                    setdetailsshow(true)
                }} style={{ color: 'blue' }}>
                    显示
                </span>
            ),
        },
        {
            title: '状态',
            dataIndex: 'disabled',
            render: (disabled: boolean) => (
                <span style={{ color: disabled ? 'red' : 'green' }}>
                    {disabled ? '禁用' : '启用'}
                </span>
            ),
        },
        {
            title: '时间',
            dataIndex: 'createTime',
            render: (createTime: string) => {
                const timestamp = parseInt(createTime, 10);
                return new Date(timestamp * 1000).toLocaleString();
            },
        },
        {
            title: '备注',
            dataIndex: 'desc',
        },
        {
            title: '操作',
            dataIndex: 'code',
            render: (code: string, record: any) => (
                <Space>
                    <Button
                        size="small"
                        type="primary"
                        onClick={() => {
                            AccessKeydisabled(record)
                        }}
                    >
                        {record.disabled ? '启用' : '禁用'}
                    </Button>
                    <Button type="primary" size="small" onClick={() => {
                        Modal.confirm({
                            title: '确认删除',
                            content: '您确定要删除此项吗？',
                            onOk: () => deletebtn(record),
                        });
                    }}>
                        删除
                    </Button>
                </Space>
            ),
        },
    ];

    const AccessKeydisabled = (record: boolean) => {
        var data = {
            id: record?.id,
            disabled: !record?.disabled
        }
        console.log(data);
        MetadataService.disabled(data)
            .then(res => {
                message.success(res?.errorMsg); // 提示成功
                if (res?.errorMsg == 'Success') {
                    fetchaksklist()
                }
            })
            .catch(err => {
                setDataLoading(false);
            });
    }

    const AccessKeybtn = () => {
        const values = editMetadataForm.getFieldValue('code');
        var data = {
            appId: uuid,
            desc: values
        }
        MetadataService.Saveaksk(data)
            .then(res => {
                console.log(res, '成功');
                if (res?.errorMsg == 'Success') {
                    message.success('创建成功');
                    fetchaksklist();
                    setIsAdd(false);
                    editMetadataForm.resetFields();
                } else {
                    message.success(res?.errorMsg);
                }
            })
            .catch(err => {
                message.success(res?.errorMsg);
            });
    }
    const generateUUID = () => {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
    const deletebtn = (record: any) => {
        var data = {
            id: record?.id,
        }
        MetadataService.fetchHivedelete(data)
            .then(res => {
                if (res?.errorMsg == 'Success') {
                    message.success('删除成功'); // 提示成功
                    fetchaksklist()
                } else {
                    message.success(res?.errorMsg);
                }
            })
            .catch(err => {
            });
    }

    return (
        <div className="metadata_basic_container">
            <div className="metadata_basic_header">
                <div>
                    <Button
                        type="primary"
                        onClick={() => {
                            const Uuid = generateUUID().replace(/-/g, ''); // 生成UUID并去掉-
                            setUid(Uuid)
                            setIsAdd(true);
                        }}
                    >
                        新增AccessKey
                    </Button>
                </div>
                {/* <div>
                    <Input.Search
                        placeholder="请输入code"
                        enterButton="搜索"
                        onSearch={value => setQuery({ ...query, code: value })}
                    />
                </div> */}
            </div>
            <Table
                columns={columns}
                dataSource={typeList}
                rowKey="id"
                loading={dataLoading}
                pagination={false} // 不需要显示分页
            />
            <Modal
                title={'新增AccessKey'}
                visible={isAdd}
                onCancel={() => setIsAdd(false)}
                onOk={() => {
                    AccessKeybtn()
                }}
            >
                <div>
                    AccessKey: {uuid}
                </div>


                <div style={{ marginTop: '20px' }}  >
                    <Form
                        name="editMetadata"
                        initialValues={{}}
                        form={editMetadataForm}
                    >
                        <Form.Item
                            label="备注(可选):"
                            name="code"
                        >
                            <TextArea rows={4}
                                placeholder="请输入备注"
                                style={{ width: '100%' }} />
                        </Form.Item>
                    </Form>
                </div>
            </Modal>


            <Modal
                title={'Access Key Secret详情'}
                visible={detailsshow}
                onCancel={() => setdetailsshow(false)}
                onOk={() => {
                    setdetailsshow(false)
                }}
            >
                <Collapse
                    defaultActiveKey={['1']}
                    expandIconPosition={'end'}
                >
                    <Panel header="AccessKey ID详情" key="1" >
                        <div >
                            <div style={{ marginBottom: '10px' }} >AccessKeyID: {Keyshowd?.ak}</div>
                            <div>AccessKeySecret: {Keyshowd?.sk}</div>
                        </div>
                    </Panel>
                </Collapse>
                <div style={{ marginTop: '20px' }}  >
                    <Form
                        name="viewMetadata"
                        initialValues={{}}
                        form={editMetadataForm}
                    >
                        <Form.Item
                            label="备注"
                        >
                            <TextArea
                                value={Keyshowd?.desc}
                                readOnly
                                style={{ width: '100%' }}
                            />
                        </Form.Item>
                    </Form>
                </div>
            </Modal>
        </div>
    );
};
export default authorization;
