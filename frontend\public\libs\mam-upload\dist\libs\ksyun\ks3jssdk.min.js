/*! ks3jssdk 14-12-2017 */
function getExpires(a){return Math.round((new Date).getTime()/1e3)+a}!function(a){var b=function(a,b){this.defaultKS3Options={KSSAccessKeyId:"",policy:"",signature:"",bucket_name:"",key:"",acl:"private",uploadDomain:"",autoStart:!1,onInitCallBack:function(){},onErrorCallBack:function(){},onFilesAddedCallBack:function(){},onBeforeUploadCallBack:function(){},onStartUploadFileCallBack:function(){},onUploadProgressCallBack:function(){},onFileUploadedCallBack:function(){},onUploadCompleteCallBack:function(){}},a&&plupload.extend(this.defaultKS3Options,a);var c={};c=this.defaultKS3Options.signature&&this.defaultKS3Options.policy?{key:this.defaultKS3Options.key,acl:this.defaultKS3Options.acl,signature:this.defaultKS3Options.signature,KSSAccessKeyId:this.defaultKS3Options.KSSAccessKeyId,policy:this.defaultKS3Options.policy,"Cache-Control":this.defaultKS3Options["Cache-Control"],Expires:this.defaultKS3Options.Expires,"Content-Disposition":this.defaultKS3Options["Content-Disposition"],"Content-Encoding":this.defaultKS3Options["Content-Encoding"],"Content-Type":this.defaultKS3Options["Content-Type"],"Content-Encoding":this.defaultKS3Options["Content-Encoding"]}:{key:this.defaultKS3Options.key,acl:this.defaultKS3Options.acl,KSSAccessKeyId:this.defaultKS3Options.KSSAccessKeyId};for(var d in this.defaultKS3Options)("string"==typeof this.defaultKS3Options[d]&&-1!==d.indexOf("x-kss-meta-")||"x-kss-newfilename-in-body"==d)&&(c[d]=this.defaultKS3Options[d]);this.defaultPluploadOptions={runtimes:"html5,flash,silverlight,html4",url:this.defaultKS3Options.uploadDomain,browse_button:"browse",flash_swf_url:"js/Moxie.swf",silverlight_xap_url:"js/Moxie.xap",drop_element:void 0,multipart:!0,multipart_params:c},b&&plupload.extend(this.defaultPluploadOptions,b),this.uploader=new plupload.Uploader(this.defaultPluploadOptions),this.uploader.bind("Init",this.onInit,this),this.uploader.bind("Error",this.onUploadError,this),this.uploader.init(),this.uploader.bind("FilesAdded",this.onFilesAdded,this),this.uploader.bind("BeforeUpload",this.onBeforeUpload,this),this.uploader.bind("UploadFile",this.onStartUploadFile,this),this.uploader.bind("UploadProgress",this.onUploadProgress,this),this.uploader.bind("FileUploaded",this.onFileUploaded,this)};return b.prototype.onInit=function(a,b){this.defaultKS3Options.onInitCallBack&&this.defaultKS3Options.onInitCallBack.apply(this,[a,b])},b.prototype.onUploadError=function(a,b){this.defaultKS3Options.onErrorCallBack&&this.defaultKS3Options.onErrorCallBack.apply(this,[a,b])},b.prototype.onFilesAdded=function(a,b){this.defaultKS3Options.autoStart&&this.uploader.start(),this.defaultKS3Options.onFilesAddedCallBack&&this.defaultKS3Options.onFilesAddedCallBack.apply(this,[a,b])},b.prototype.onBeforeUpload=function(a,b){this.defaultKS3Options.onBeforeUploadCallBack&&this.defaultKS3Options.onBeforeUploadCallBack.apply(this,[a,b])},b.prototype.onStartUploadFile=function(a,b){this.defaultKS3Options.onStartUploadFileCallBack&&this.defaultKS3Options.onStartUploadFileCallBack.apply(this,[a,b])},b.prototype.onUploadProgress=function(a,b){this.defaultKS3Options.onUploadProgressCallBack&&this.defaultKS3Options.onUploadProgressCallBack.apply(this,[a,b])},b.prototype.onFileUploaded=function(a,b,c){this.defaultKS3Options.onFileUploadedCallBack&&this.defaultKS3Options.onFileUploadedCallBack.apply(this,[a,b,c])},b.prototype.onUploadComplete=function(a,b){this.defaultKS3Options.onUploadCompleteCallBack&&this.defaultKS3Options.onUploadCompleteCallBack.apply(this,[a,b])},a.ks3FileUploader=b}(window);var Ks3={};Ks3.addURLParam=function(a,b){a+=-1==a.indexOf("?")?"?":"";var c=[];for(var d in b){d=encodeURIComponent(d);var e=b[d];e&&"[object String]"==Object.prototype.toString.call(e)&&c.push(d+"="+encodeURIComponent(e))}return a+c.join("&")},Ks3.xmlToJson=function(a){var b={};if(a.nodeType==Node.TEXT_NODE&&(b=a.nodeValue),a.hasChildNodes())for(var c=0;c<a.childNodes.length;c++){var d=a.childNodes.item(c),e=d.nodeName;if("undefined"==typeof b[e])"#text"===e?b=d.nodeValue:b[e]=Ks3.xmlToJson(d);else{if("undefined"==typeof b[e].length){var f=b[e];b[e]=[],b[e].push(f)}b[e].push(Ks3.xmlToJson(d))}}return b},Ks3.Base64={encTable:["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","0","1","2","3","4","5","6","7","8","9","+","/"],decTable:[-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,62,-1,-1,-1,63,52,53,54,55,56,57,58,59,60,61,-1,-1,-1,-1,-1,-1,-1,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,-1,-1,-1,-1,-1,-1,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,-1,-1,-1,-1,-1],encUTF8:function(a){for(var b,c,d,e,f,g,h,i=[],j=a.length,k=0;j>k;k++)b=a.charCodeAt(k),b>0&&127>=b?i.push(b):b>=128&&2047>=b?(c=192|b>>6&31,d=128|63&b,i.push(c,d)):b>=2048&&65535>=b?(c=224|b>>12&15,d=128|b>>6&63,e=128|63&b,i.push(c,d,e)):b>=65536&&2097151>=b?(c=240|b>>18&7,d=128|b>>12&63,e=128|b>>6&63,f=128|63&b,i.push(c,d,e,f)):b>=2097152&&67108863>=b?(c=240|b>>24&3,d=240|b>>18&63,e=128|b>>12&63,f=128|b>>6&63,g=128|63&b,i.push(c,d,e,f,g)):b>=67108864&&2147483647>=b&&(c=240|b>>30&1,d=240|b>>24&63,e=240|b>>18&63,f=128|b>>12&63,g=128|b>>6&63,h=128|63&b,i.push(c,d,e,f,g,h));return i},encode:function(a){if(!a)return"";for(var b,c,d,e=this.encUTF8(a),f=0,g=e.length,h=[];g>f;){if(b=255&e[f++],h.push(this.encTable[b>>2]),f==g){h.push(this.encTable[(3&b)<<4],"==");break}if(c=e[f++],f==g){h.push(this.encTable[(3&b)<<4|c>>4&15]),h.push(this.encTable[(15&c)<<2],"=");break}d=e[f++],h.push(this.encTable[(3&b)<<4|c>>4&15]),h.push(this.encTable[(15&c)<<2|(192&d)>>6]),h.push(this.encTable[63&d])}return h.join("")}},Ks3.chrsz=8,Ks3.b64pad="=",Ks3.b64_hmac_sha1=function(a,b){return Ks3.binb2b64(Ks3.core_hmac_sha1(a,b))},Ks3.core_hmac_sha1=function(a,b){var c=Ks3.str2binb(a);c.length>16&&(c=Ks3.core_sha1(c,a.length*Ks3.chrsz));for(var d=Array(16),e=Array(16),f=0;16>f;f++)d[f]=909522486^c[f],e[f]=1549556828^c[f];var g=Ks3.core_sha1(d.concat(Ks3.str2binb(b)),512+b.length*Ks3.chrsz);return Ks3.core_sha1(e.concat(g),672)},Ks3.binb2b64=function(a){for(var b="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",c="",d=0;d<4*a.length;d+=3)for(var e=(a[d>>2]>>8*(3-d%4)&255)<<16|(a[d+1>>2]>>8*(3-(d+1)%4)&255)<<8|a[d+2>>2]>>8*(3-(d+2)%4)&255,f=0;4>f;f++)c+=8*d+6*f>32*a.length?Ks3.b64pad:b.charAt(e>>6*(3-f)&63);return c},Ks3.str2binb=function(a){for(var b=Array(),c=(1<<Ks3.chrsz)-1,d=0;d<a.length*Ks3.chrsz;d+=Ks3.chrsz)b[d>>5]|=(a.charCodeAt(d/Ks3.chrsz)&c)<<32-Ks3.chrsz-d%32;return b},Ks3.core_sha1=function(a,b){a[b>>5]|=128<<24-b%32,a[(b+64>>9<<4)+15]=b;for(var c=Array(80),d=1732584193,e=-271733879,f=-1732584194,g=271733878,h=-1009589776,i=0;i<a.length;i+=16){for(var j=d,k=e,l=f,m=g,n=h,o=0;80>o;o++){16>o?c[o]=a[i+o]:c[o]=Ks3.rol(c[o-3]^c[o-8]^c[o-14]^c[o-16],1);var p=Ks3.safe_add(Ks3.safe_add(Ks3.rol(d,5),Ks3.sha1_ft(o,e,f,g)),Ks3.safe_add(Ks3.safe_add(h,c[o]),Ks3.sha1_kt(o)));h=g,g=f,f=Ks3.rol(e,30),e=d,d=p}d=Ks3.safe_add(d,j),e=Ks3.safe_add(e,k),f=Ks3.safe_add(f,l),g=Ks3.safe_add(g,m),h=Ks3.safe_add(h,n)}return Array(d,e,f,g,h)},Ks3.safe_add=function(a,b){var c=(65535&a)+(65535&b),d=(a>>16)+(b>>16)+(c>>16);return d<<16|65535&c},Ks3.rol=function(a,b){return a<<b|a>>>32-b},Ks3.sha1_ft=function(a,b,c,d){return 20>a?b&c|~b&d:40>a?b^c^d:60>a?b&c|b&d|c&d:b^c^d},Ks3.sha1_kt=function(a){return 20>a?1518500249:40>a?1859775393:60>a?-1894007588:-899497514},Ks3.generateHeaders=function(a){var b="",c=[];if(a){var d="x-kss";for(var e in a)0==e.indexOf(d)&&c.push(e.toLowerCase()+":"+a[e]);c.sort(),b=c.join("\n")}return b},Ks3.generateToken=function(a,b,c,d,e,f,g){var h=Ks3.generateHeaders(f),i="/"+b+"/"+c;if(""!==h)var j=d+"\n\n"+e+"\n"+g+"\n"+h+"\n"+i;else var j=d+"\n\n"+e+"\n"+g+"\n"+i;var k=Ks3.b64_hmac_sha1(a,j);return k},Ks3.ENDPOINT={HANGZHOU:"kss.ksyun.com",AMERICA:"ks3-us-west-1.ksyun.com",BEIJING:"ks3-cn-beijing.ksyun.com",HONGKONG:"ks3-cn-hk-1.ksyun.com",SHANGHAI:"ks3-cn-shanghai.ksyun.com"},Ks3.config={AK:"",SK:"",protocol:"http",baseUrl:"",region:"",bucket:"",prefix:"kss",chunkSize:5242880,retries:20,currentUploadId:"",stopFlag:!1},Ks3.listObject=function(a,b){var c=new XMLHttpRequest,d={delimiter:a.delimiter,"encoding-type":a["encoding-type"],marker:a.marker,"max-keys":a["max-keys"],prefix:a.prefix},e=a.Bucket||Ks3.config.bucket,f=a.region||Ks3.config.region;f&&(Ks3.config.baseUrl=Ks3.ENDPOINT[f]);var g=Ks3.config.protocol+"://"+Ks3.config.baseUrl+"/"+e;g=Ks3.addURLParam(g,d),c.overrideMimeType("text/xml"),c.onreadystatechange=function(){4==c.readyState&&(c.status>=200&&c.status<300||304==c.status?b(Ks3.xmlToJson(c.responseXML)):(alert("Request was unsuccessful: "+c.status),console.log("status: "+c.status)))},c.open("GET",g,!0);var h=a.Signature||Ks3.generateToken(Ks3.config.SK,e,"","GET","","","");c.setRequestHeader("Authorization","KSS "+Ks3.config.AK+":"+h),c.send(null)},Ks3.delObject=function(a,b){var c=a.Bucket||Ks3.config.bucket,d=Ks3.encodeKey(a.Key),e=a.region||Ks3.config.region;e&&(Ks3.config.baseUrl=Ks3.ENDPOINT[e]);var f=Ks3.config.protocol+"://"+Ks3.config.baseUrl+"/"+c+"/"+d,g=a.Signature||Ks3.generateToken(Ks3.config.SK,c,d,"DELETE","","",""),h=new XMLHttpRequest;h.onreadystatechange=function(){4==h.readyState&&(h.status>=200&&h.status<300||304==h.status?b(h.status):(alert("Request was unsuccessful: "+h.status),console.log("status: "+h.status)))},h.open("DELETE",f,!0),h.setRequestHeader("Authorization","KSS "+Ks3.config.AK+":"+g),h.send(null)},Ks3.encodeKey=function(a){if(null==a)return"";var b=encodeURIComponent(a);return b=b.replace(/\+/g,"%20").replace(/\*/g,"%2A").replace(/%7E/g,"~").replace(/%2F/g,"/")},Ks3.headObject=function(a,b){(null===a.Key||void 0===a.Key)&&alert("require the Key");var c=Ks3.encodeKey(a.Key),d=a.region||Ks3.config.region;d&&(Ks3.config.baseUrl=Ks3.ENDPOINT[d]);var e=a.Bucket||Ks3.config.bucket||"";e||alert("require the bucket name");var f=Ks3.config.protocol+"://"+Ks3.config.baseUrl+"/"+e+"/"+c,g="HEAD",h=a.Signature||Ks3.generateToken(Ks3.config.SK,e,c,g,"","",""),i=new XMLHttpRequest;i.onreadystatechange=function(){4==i.readyState&&(i.status>=200&&i.status<300||304==i.status?b(null,i):(console.log("status: "+i.status),b({msg:"request failed"},i)))},i.open(g,f,!0),i.setRequestHeader("Authorization","KSS "+Ks3.config.AK+":"+h),i.send(null)},Ks3.getObject=function(a,b){(null===a.Key||void 0===a.Key)&&alert("require the Key");var c=Ks3.encodeKey(a.Key),d=a.region||Ks3.config.region;d&&(Ks3.config.baseUrl=Ks3.ENDPOINT[d]);var e=a.Bucket||Ks3.config.bucket||"";e||alert("require the bucket name");var f=a.range||"",g=Ks3.config.protocol+"://"+Ks3.config.baseUrl+"/"+e+"/"+c,h="GET",i=a.Signature||Ks3.generateToken(Ks3.config.SK,e,c,h,"","",""),j=new XMLHttpRequest;j.onreadystatechange=function(){if(4==j.readyState)if(j.status>=200&&j.status<300||304==j.status){var a=new Blob([this.response],{type:this.getResponseHeader("Content-Type")});b(null,a,j)}else console.log("status: "+j.status),b({msg:"request failed"},a,j)},j.open(h,g,!0),j.responseType="arraybuffer";var k=/^bytes=(\d+)-(\d+)$/i;""!==f&&k.test(f)&&j.setRequestHeader("Range",f),j.setRequestHeader("Authorization","KSS "+Ks3.config.AK+":"+i),j.send(null)},Ks3.putObject=function(a,b){(null===a.Key||void 0===a.Key)&&alert("require the Key");var c=Ks3.encodeKey(a.Key),d=a.region||Ks3.config.region;d&&(Ks3.config.baseUrl=Ks3.ENDPOINT[d]);var e=a.Bucket||Ks3.config.bucket||"";e||alert("require the bucket name");var f=Ks3.config.protocol+"://"+Ks3.config.baseUrl+"/"+e+"/"+c,g="PUT",h=new XMLHttpRequest;h.open(g,f,!0);var i={},j=a.ACL;if("private"==j||"public-read"==j){var k="x-"+Ks3.config.prefix+"-acl";h.setRequestHeader(k,j),i[k]=j}var l=a.Signature||Ks3.generateToken(Ks3.config.SK,e,c,g,a.File.type,i,"");h.onreadystatechange=function(){if(4==h.readyState)if(h.status>=200&&h.status<300||304==h.status)b(null);else if(413===h.status||415===h.status){var a=Ks3.xmlToJson(h.responseXML).Error.Message;b({msg:a})}else console.log("status: "+h.status),b({msg:"request failed"})},h.upload.addEventListener("progress",a.ProgressListener,!1),h.setRequestHeader("Authorization","KSS "+Ks3.config.AK+":"+l),h.send(a.File)},Ks3.multitpart_upload_init=function(a,b){var c=a.Bucket||Ks3.config.bucket||"",d=Ks3.encodeKey(a.Key)||null;if(!c)throw new Error("require the bucketName");if(!d)throw new Error("require the object Key");var e=a.region||Ks3.config.region;e&&(Ks3.config.baseUrl=Ks3.ENDPOINT[e]);var f=d+"?uploads";f=f.replace(/\/\//g,"/%2F");var g=a.ContentType||"",h="POST",i=Ks3.config.protocol+"://"+Ks3.config.baseUrl+"/"+c+"/"+f,j=new XMLHttpRequest;j.open(h,i,!0);var k={},l=a.ACL;if("private"==l||"public-read"==l){var m="x-"+Ks3.config.prefix+"-acl";j.setRequestHeader(m,l),k[m]=l}var n=a.TotalSize;if(n){var o="x-"+Ks3.config.prefix+"-meta-content-length";j.setRequestHeader(o,n),k[o]=n}var p=a.Signature||Ks3.generateToken(Ks3.config.SK,c,f,h,g,k,"");j.overrideMimeType("text/xml"),j.onreadystatechange=function(){if(4==j.readyState)if(j.status>=200&&j.status<300||304==j.status){var a=Ks3.xmlToJson(j.responseXML).InitiateMultipartUploadResult.UploadId;b(null,a)}else 413===j.status||415===j.status?b({status:j.status,msg:Ks3.xmlToJson(j.responseXML).Error.Message},null):(console.log("status: "+j.status),b({msg:"request failed"},null))},j.setRequestHeader("Authorization","KSS "+Ks3.config.AK+":"+p),g&&j.setRequestHeader("Content-Type",g),j.send(null)},Ks3.upload_part=function(a,b){var c=a.Bucket||Ks3.config.bucket||"",d=Ks3.encodeKey(a.Key)||null,e=a.ContentType||"",f="undefined"!=typeof a.PartNumber?a.PartNumber:"",g=a.UploadId||"";if(!c||!d)throw new Error("require the bucketName and object key");if(""===f||!g)throw new Error("require the partNumber and uploadId");var h=a.body||"",i=d+"?partNumber="+f+"&uploadId="+g;i=i.replace(/\/\//g,"/%2F");var j=Ks3.config.protocol+"://"+Ks3.config.baseUrl+"/"+c+"/"+i,k="PUT",l=a.Signature||Ks3.generateToken(Ks3.config.SK,c,i,k,e,"",""),m=new XMLHttpRequest;m.onreadystatechange=function(){if(4==m.readyState)if(m.status>=200&&m.status<300||304==m.status){var a=m.getResponseHeader("Etag");b(null,f,a)}else 413===m.status||415===m.status?b({status:m.status,msg:Ks3.xmlToJson(m.responseXML).Error.Message},null):(console.log("status: "+m.status),b({msg:"request failed"},null))},m.open(k,j,!0),m.setRequestHeader("Authorization","KSS "+Ks3.config.AK+":"+l),e&&m.setRequestHeader("Content-Type",e),h&&m.send(h)},Ks3.upload_complete=function(a,b){var c=a.Bucket||Ks3.config.bucket||"",d=Ks3.encodeKey(a.Key)||null,e=a.UploadId||"",f=a.callbackurl||"",g=a.callbackbody||"";if(!c||!d)throw new Error("require the bucketName and object key");if(!e)throw new Error("require the uploadId");var h=a.body||"",i=d+"?uploadId="+e;i=i.replace(/\/\//g,"/%2F");var j="text/plain;charset=UTF-8",k={};if(f){var l="x-"+Ks3.config.prefix+"-callbackurl";k[l]=f}if(g){var m="x-"+Ks3.config.prefix+"-callbackbody";k[m]=g}var n=Ks3.config.protocol+"://"+Ks3.config.baseUrl+"/"+c+"/"+i,o="POST",p=a.Signature||Ks3.generateToken(Ks3.config.SK,c,i,o,j,"","");k&&(p=a.Signature||Ks3.generateToken(Ks3.config.SK,c,i,o,j,k,""));var q=new XMLHttpRequest;q.overrideMimeType("text/xml"),q.onreadystatechange=function(){if(4==q.readyState)if(q.status>=200&&q.status<300||304==q.status){var a=Ks3.xmlToJson(q.responseXML);b(null,a)}else console.log("status: "+q.status),b({msg:"request failed",status:q.status},a)},q.open(o,n,!0),q.setRequestHeader("Authorization","KSS "+Ks3.config.AK+":"+p),f&&q.setRequestHeader("x-kss-callbackurl",f),g&&q.setRequestHeader("x-kss-callbackbody",g),h&&q.send(h)},Ks3.abort_multipart_upload=function(a,b){var c=a.Bucket||Ks3.config.bucket||"",d=Ks3.encodeKey(a.Key)||null,e=a.UploadId||"";if(!c||!d)throw new Error("require the bucketName and object key");if(!e)throw new Error("require the uploadId");var f=d+"?uploadId="+e;f=f.replace(/\/\//g,"/%2F");var g=Ks3.config.protocol+"://"+Ks3.config.baseUrl+"/"+c+"/"+f,h="DELETE",i=a.Signature||Ks3.generateToken(Ks3.config.SK,c,f,h,"","",""),j=new XMLHttpRequest;j.onreadystatechange=function(){4==j.readyState&&(204==j.status?b(null,{status:j.status}):(console.log("status: "+j.status),b({msg:"request failed",status:j.status})))},j.open(h,g,!0),j.setRequestHeader("Authorization","KSS "+Ks3.config.AK+":"+i),j.send(null)},Ks3.upload_list_part=function(a,b){var c=a.Bucket||Ks3.config.bucket||"",d=Ks3.encodeKey(a.Key)||null,e=a.UploadId||"";if(!c||!d)throw new Error("require the bucketName and object key");if(!e)throw new Error("require the uploadId");var f=d+"?uploadId="+e;f=f.replace(/\/\//g,"/%2F");var g=Ks3.config.protocol+"://"+Ks3.config.baseUrl+"/"+c+"/"+f,h="GET",i=a.Signature||Ks3.generateToken(Ks3.config.SK,c,f,h,"","",""),j=new XMLHttpRequest;j.overrideMimeType("text/xml"),j.onreadystatechange=function(){if(4==j.readyState)if(j.status>=200&&j.status<300||304==j.status){var a=Ks3.xmlToJson(j.responseXML);b(null,a)}else console.log("status: "+j.status),b({msg:"request failed",status:j.status},a)},j.open(h,g,!0),j.setRequestHeader("Authorization","KSS "+Ks3.config.AK+":"+i),j.send(null)},String.prototype.endWith=function(a){var b=new RegExp(a+"$");return b.test(this)},Ks3.parseStringToXML=function(a){if(document.implementation&&document.implementation.createDocument)var b=(new DOMParser).parseFromString(a,"text/xml");else{if(!window.ActiveXObject)return alert("浏览器不支持xml解析，请升级浏览器"),null;var b=new ActiveXObject("Microsoft.XMLDOM");b.loadXML(a)}return b};