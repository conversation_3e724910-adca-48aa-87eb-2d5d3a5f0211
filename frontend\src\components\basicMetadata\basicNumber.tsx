import React, { <PERSON> } from 'react'
import { IBasicItemProps } from './basicMetadata'
import { Input, Col, Row, Form } from 'antd'
import { changesize } from "@/utils"
import timecodeconvert from '@/components/time-code/timeCode'

interface IBasicNumberProps extends IBasicItemProps {
    frameRate: number
}

const BasicNumber: FC<IBasicNumberProps> = (props) => {
    const showValue = (value: string, type: string) => {
        if(!value){
            return ''
        }
        switch (type) {
            case 'filesize':
                return changesize(value)
            case 'duration':
                return timecodeconvert.frame2Tc(timecodeconvert.second2Frame((parseInt(value, 10) / 10000000), props.frameRate), props.frameRate)
            default:
                return value
        }
    }
    return (
        <Form.Item
            label={props.item.alias}
            name={props.item.fieldName}
            rules={[{ required: props.item.isReadOnly || !props.edit ? false : props.item.isMustInput }]}
        >
            {
                props.item.isReadOnly || !props.edit ? showValue(props.item.value as string, props.item.fieldName) : <Input autoComplete={'off'}/>
            }
        </Form.Item>
    )
}

export default BasicNumber