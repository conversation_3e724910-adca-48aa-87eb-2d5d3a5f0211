import React, { useState, useEffect } from 'react';
import { Modal, message, Pagination, Spin, Empty } from 'antd';
import contentListApis from '@/service/contentListApis';


interface props {
    visible: boolean,
    contentId: string,
    setRelatedModalVisible: (value: boolean) => void,
}

const RelatedModal: React.FC<props> = ({ visible, contentId, setRelatedModalVisible }) => {
    const [resourceList, setResourceList] = useState<any[]>([]);
    const [relatedSourcePage, setRelatedSourcePage] = useState<number>(1);//
    const [relatedTotal, setRelatedTotal] = useState<number>(0);//
    const [loading, setLoading] = useState<boolean>(false);
    const getAssociationData = () => {
        setLoading(true)
        contentListApis.getAssociationData({ sourceContentId: contentId, pageIndex: relatedSourcePage, pageSize: 10 }).then(res => {
            if (res?.success) {
                setResourceList(res.data.data)
                setRelatedTotal(res.data.recordTotal)
                setLoading(false)
            }
        })
    }
    const updateAssociationStatus = (item) => {
        contentListApis.updateAssociationStatus({
          sourceContentId: contentId,
          associatedContentId: item.associatedContentId,
          isDelete: item.isDelete === 0 ? 1 : 0
        }).then((res: any) => {
          if (res?.success) {
            message.success('操作成功')
            getAssociationData()
          }
        })
      }
    useEffect(() => {
          getAssociationData()
      }, [relatedSourcePage])
    return (
        <Modal
            open={visible}
            className='relatedModal'
            title='关联视频'
            onOk={() => {
                setRelatedModalVisible(false);
            }}
            onCancel={() => setRelatedModalVisible(false)}
        >
            <Spin spinning={loading} >
            {resourceList.length > 0 ?resourceList.map(item => (
                <div className='related_item' key={item.id}><span className='associated_name' title={item.associatedName}>{item.associatedName} </span><span style={{ color: 'var(--primary-color)', cursor: 'pointer' }} onClick={() => updateAssociationStatus(item)}>{item.isDelete === 0 ? '解绑' : '恢复绑定'}</span></div>
            )):<Empty description='暂无数据' />}
            {relatedTotal > 0 && <div className="pagination">
                <Pagination
                    current={relatedSourcePage}
                    pageSize={10}
                    total={relatedTotal}
                    size="small"
                    showQuickJumper
                    onChange={(page, pageSize) => {
                        setRelatedSourcePage(page)
                    }}
                    showTotal={total => `共${total}条`}
                    pageSizeOptions={['30', '40', '50', '100']}
                />
            </div>}
            </Spin>
        </Modal>
    );
};

export default RelatedModal;
