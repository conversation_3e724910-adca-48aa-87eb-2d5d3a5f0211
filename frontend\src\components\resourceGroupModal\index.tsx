import React, { useState, useEffect } from 'react';
import { Modal, Progress, Button, Form, message, Table, Select, Radio, Input } from 'antd';
import { useIntl } from 'umi';
import './index.less';
import TeacherItem from '../formItemBox/teacherItem';
import rmanApis from '@/service/rman';
import { AddTeacherModal, IconFont } from '..';
import {
  PlusCircleOutlined
} from '@ant-design/icons';
import contentListApis from '@/service/contentListApis';
import { initGroupUsers } from '@/service/choicepeople';
import SpaceManageModal from '@/components/spaceManageModal';
const { Option } = Select;
interface CreateModalProps {
  modalVisible: boolean;
  modalClose: () => void;
  resourceGroup: any;
}

const ResourceGroupModal: React.FC<CreateModalProps> = props => {
  const {
    modalClose,
    modalVisible,
    resourceGroup,
  } = props;
  const [userList, setUserlist] = useState<any>([]);
  const [batchSettingVisible, setBatchSettingVisible] = useState<boolean>(false);
  const [selectRows, setSelectedRows] = useState<any>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  const [defaultValue, setDefaultValue] = useState<any>(1);
  const [teacherModalVisible, setteacherModalVisible] = useState<boolean>(false);
  const [permission, setPermission] = useState<boolean>(false); // 登录人员权限
  const [isManager, setIsManager] = useState<boolean>(false); // 当前群是否是管理员
  const [spaceAllocate, setSpaceAllocate] = useState<boolean>(false); // 打开空间分配弹窗
  const [loading, setLoading] = useState<boolean>(false); // 加载中
  const intl = useIntl();
  // const [selectedList, setTelectedList] = useState<any>([]);
  const [form] = Form.useForm();
  useEffect(() => {
    if (modalVisible) {
      //初始化数据
      fetchGroupUser()
    }
  }, [modalVisible]);

  useEffect(() => {
    if (modalVisible && resourceGroup?.contentId_) {
      rmanApis.administrator(resourceGroup?.contentId_).then((res: any) => {
        if (res.success) {
          setIsManager(res.data)
        }
      })
    }
  }, [modalVisible, resourceGroup])

  useEffect(() => {
    if ((window as any).login_useInfo) {
      const userInfo = (window as any).login_useInfo
      if (userInfo) {
        const roless = userInfo.roles?.map((item: any) => item.roleCode)
        // 当前用户角色为zhiliao、sys和资源管理员
        if (roless?.includes('admin_S1') || roless?.includes('r_sys_manager') || roless?.includes('r_resource_manager')) {
          setPermission(true)
        } else {
          setPermission(false)
        }
      }
    }
  }, [(window as any).login_useInfo])

  
  const columns: any = [
    {
      title: intl.formatMessage({ id: '学/工号' }),
      dataIndex: 'accountshow',
      align: 'center',
      width: '20%',
      key: 'accountshow',
      render: (value: string, row: any, index: number) => {
        return <div title={value}>{value}</div>;
      }
    },
    {
      title: intl.formatMessage({ id: '姓名' }),
      dataIndex: 'nick_name',
      align: 'center',
      width: '20%',
      key: 'nick_name',
      render: (value: string, row: any, index: number) => {
        return <div title={value}>{value}</div>;
      },
    },
    {
      title: intl.formatMessage({ id: '所属组织' }),
      dataIndex: 'orgname',
      align: 'center',
      width: '30%',
      key: 'orgname',
      render: (value: string, row: any, index: number) => {
        return <div title={value}>{value}</div>;
      },
    },
    {
      // 只有管理员使用
      title: intl.formatMessage({ id: '权限' }),
      dataIndex: 'jurisdiction',
      align: 'center',
      width: '30%',
      key: 'jurisdiction',
      render: (value: string, row: any, index: number) => {
        return <Select style={{ width: '100%' }} defaultValue={value} onChange={(e: any) => changeOption(e, row)}>
          <Option value={1}>{intl.formatMessage({ id: '仅使用' })}</Option>
          <Option value={2}>{intl.formatMessage({ id: '编辑者' })}</Option>
          <Option value={3}>{intl.formatMessage({ id: '管理员' })}</Option>
        </Select>
      },
    }
  ]
  const rowSelection = {
    type: 'checkbox',
    onChange: (newSelectedRowKeys: Array<any>, newSelectedRows: any) => {
      setSelectedRowKeys(newSelectedRowKeys.filter(Boolean));
      setSelectedRows(newSelectedRows.filter(Boolean)); //得处理勾选移除后的残余空值对象
    },
    preserveSelectedRowKeys: true,
    selectedRowKeys,
  };
  const fetchGroupUser = () => {
    initGroupUsers({ contentId: resourceGroup.contentId_ }).then((res: any) => {
      const temp = res?.data?.map((item: any, index: number) => {
        return {
          accountshow: item.userCode,
          user_code: item.userCode,
          nick_name: item.userName,
          orgname: item.userOrganizeName,
          jurisdiction: item.all ? 3 : (item.canWrite ? 2 : 1)
        }
      })
      console.log('初始值', temp);
      setUserlist(temp)
    })
  };
  const handleCancel = () => {
    modalClose();
  };
  const addUser = () => {
    // console.log(resourceGroup)
    setteacherModalVisible(true)
  };
  const selectList = (lists: any) => {
    console.log('selectList', lists)
    setUserlist(lists)
  };
  const closeTeacherModal = () => {
    setteacherModalVisible(false)
  };
  const removeUser = (e: any) => {
    console.log(selectRows);
    contentListApis.deletegroupusers({
      contentIds: [resourceGroup.contentId_],
      userCodes: selectRows.map((item: any) => item.accountshow)
    }).then((res: any) => {
      if (res?.success) {
        const temp = JSON.parse(JSON.stringify(userList))
        selectRows.forEach((item: any) => {
          temp.forEach((item_: any, index: number) => {
            if (item.user_code === item_.user_code) {
              temp.splice(index, 1);
            }
          })
        })
        console.log('变更hou', temp)
        setUserlist(temp)
        setSelectedRows([]) //需要置空 防止立即批量操作权限遗留数据
        setSelectedRowKeys([])
        message.success(intl.formatMessage({ id: '移除成功' }))
      }
    })
  };
  const batchSettingModal = (e: any) => {
    setBatchSettingVisible(true)
  };
  const changeOption = (e: any, row: any) => {
    e === 3 && message.info('管理员可修改群组人员及权限')
    userList.forEach((item: any) => {
      if (item.user_code === row.user_code) {
        item.jurisdiction = e;
      }
    })
    // console.log('变更hou',userList)
    setUserlist(userList)
  };
  const changeOptions = (e: any) => {
    console.log(e)
    setDefaultValue(e)
  };
  const onFinish = () => {
    console.log('Success:', userList);
    if (!(permission || isManager)) {
      modalClose()
      return false
    }
    let userAuthorities: any = [];
    userList.forEach((item: any) => {
      userAuthorities.push({
        userCode: item.user_code,
        userName: item.nick_name,
        // userOrganizeCode:item.extend?.orgcodepath,
        userOrganizeName: item.orgname ? item.orgname : item.extend?.school + (item.extend?.college ? '/' + item.extend?.college : ''),
        canRead: item.jurisdiction >= 1 ? true : false,
        canWrite: item.jurisdiction >= 2 ? true : false,
        canDelete: false,  // 暂时未用到 
        all: item.jurisdiction === 3 ? true : false
      })
    });
    setLoading(true)
    contentListApis.addgroupusers({
      contentIds: [resourceGroup.contentId_],
      userAuthorities
    }).then((res: any) => {
      if (res?.success) {
        setLoading(false)
        message.success(intl.formatMessage({ id: '添加成功' }))
      } else {
        message.error(intl.formatMessage({ id: '添加失败' }))
      }
      modalClose()
    })
  };
  const batchSettingFinish = () => {
    console.log('defaultValue', defaultValue)
    selectRows.forEach((item: any) => {
      userList.forEach((item_: any) => {
        if (item.user_code === item_.user_code) {
          item_.jurisdiction = defaultValue
        }
      })
    })
    console.log('变更完后', userList)
    setUserlist(userList)
    setBatchSettingVisible(false)
  };

  return (
    <>
      <Modal
        destroyOnClose={true}
        className='resourceGroupModal'
        title={intl.formatMessage({ id: '查看群成员' })}
        visible={modalVisible}
        onCancel={handleCancel}
        footer={[
          <Button
            type='primary'
            onClick={onFinish}
            loading={loading}
          >
            {intl.formatMessage({ id: (permission || isManager) ? '保存' : '关闭' })}
          </Button>
        ]}
        width={680}
      >
        <div className='content'>
          <div className='opt_btn'>
            <Button
              type='primary'
              onClick={addUser}
            >
              <PlusCircleOutlined />
              {intl.formatMessage({ id: '添加人员' })}
            </Button>
            <Button
              disabled={selectRows.length === 0}
              onClick={removeUser}
            >
              {intl.formatMessage({ id: '移除人员' })}
            </Button>
            <Button
              disabled={selectRows.length === 0}
              onClick={batchSettingModal}
            >
              {intl.formatMessage({ id: '批量配置权限' })}
            </Button>
            <Button type='primary' onClick={() => setSpaceAllocate(true)}>
              {intl.formatMessage({ id: '群组空间分配' })}
            </Button>

          </div>
          <div>
            <Table
              dataSource={userList}
              columns={(permission || isManager) ? columns : columns.slice(0, -1)}
              rowKey={'accountshow'}
              key={JSON.stringify(userList)} // 作为table更新的凭证
              rowSelection={(permission || isManager) ? rowSelection as any : false}
              pagination={{
                position: ['bottomCenter'],
                pageSizeOptions: ['10', '20', '30', '50'],
                showSizeChanger: true,
                total: userList.length,
                showQuickJumper: true,
                // onChange: (page: number) =>
                //   setQuery({
                //     ...query,
                //     page,
                //   }),
                showTotal: total => `共 ${total} 条`,
              }}
              scroll={{ y: '400px' }}
            />
          </div>
        </div>
        <Modal
          className='batchSetting'
          title={intl.formatMessage({ id: '批量配置权限' })}
          visible={batchSettingVisible}
          onCancel={() => setBatchSettingVisible(false)}
          footer={[
            <Button
              type='primary'
              onClick={batchSettingFinish}
            >
              {intl.formatMessage({ id: '确认' })}
            </Button>
          ]}
          width={400}
        >
          <div>
            <span>已选{selectRows.length}人</span>
          </div>
          <div>
            <Select style={{ width: '100%' }} defaultValue={defaultValue} onChange={changeOptions}>
              <Option value={1}>{intl.formatMessage({ id: '仅使用' })}</Option>
              <Option value={2}>{intl.formatMessage({ id: '编辑者' })}</Option>
              <Option value={3}>{intl.formatMessage({ id: '管理员' })}</Option>
            </Select>
          </div>
        </Modal>
        {spaceAllocate && <SpaceManageModal
          modalVisible={spaceAllocate}
          title={intl.formatMessage({ id: '群组空间分配' })}
          spaceManageOk={()=>{}}
          modalClose={() => setSpaceAllocate(false)}
          resourceGroup={resourceGroup}
        />}
        <AddTeacherModal
          modalVisible={teacherModalVisible}
          modalClose={closeTeacherModal}
          chooseedTeacher={userList}
          callback={selectList}
        />
      </Modal>
    </>
  );
};

export default ResourceGroupModal;