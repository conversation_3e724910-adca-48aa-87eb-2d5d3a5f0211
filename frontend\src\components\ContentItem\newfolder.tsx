import React, { FC, useState, useRef, useEffect, KeyboardEvent, FocusEvent } from 'react';
import { message, Input, Checkbox, Form } from 'antd';
import './index.less';
import contentListApis from '@/service/contentListApis';
import Keyframe from '../keyframe/keyframe';
import { getSensitiveWord } from '@/utils';
interface Idetail {
    type_: string
    name_: string
    fatherTreeId: string
    fatherTreePath?: string
    keyframe: string
}
interface ContentData {
    modal: boolean;
    detail: Idetail;
    refresh?: () => Promise<any>;
    setallrename: (item: boolean) => void;
    privateorpublic: boolean;
    resourceGroup?: boolean;
}
const NewFolder: React.FC<ContentData> = (props) => {
    const { modal, detail, refresh = () => Promise.resolve(), setallrename, privateorpublic, resourceGroup } = props;
    const inputRef = useRef<Input | null>(null);
    const lockRef = useRef<any>(null);
    const contentitemBox: HTMLDivElement | null = document.querySelector('.contentitem_box')
    useEffect(() => {
        inputRef.current?.focus();
        console.log(detail, '参数')
        setallrename(true)
    }, []);
    // 文件夹重命名
    const changeName = async (e: KeyboardEvent<HTMLInputElement> | FocusEvent<HTMLInputElement> | any,flag?:number) => {
        if(flag){ // 手动防抖
            inputRef.current?.blur();
            return
        }
        console.log('zzzzzzzzzzzzzzz')
        e.preventDefault();
        let name = e.target.value.replace(/(^\s*)|(\s*$)/g, "")
        if (name === '') {
            message.error('文件或文件夹姓名不能为空');
            return
        }
        let re = /^[^#\x22]*$/
        if (!re.test(name)) {
            message.error('文件夹姓名中不能包含#');
            return
        }
        getSensitiveWord(name, '文件夹名', ()=> {
            if (resourceGroup) { // 群组资源
                contentListApis.newgroupfolder({
                   "name": name,
                   "parentId": detail.fatherTreeId,
                   "parentPath": detail.fatherTreePath,
                   "privilege": "private"
               }).then(async res => {
                   if (res && res.success && res.data) {
                       message.success('新建群组文件夹成功');
                       refresh('', {
                        name: '新建',
                        item: res.data,
                      })
                   } else {
                       message.error('新建群组文件夹失败');
                       await refresh();
                   }
               })
           } else {
               if (privateorpublic) {
                //    if(share){
                       contentListApis.newsharefolder({
                           "users": [
                           ],
                           "name": name,
                           "parentId": detail.fatherTreeId,
                           "parentPath": detail.fatherTreePath,
                           "privilege": "public"
                       }).then(async res => {
                           if (res && res.success && res.data) {
                               message.success('新建文件夹成功');
                               await refresh('', {
                                name: '新建',
                                item: res.data,
                              })
                           } else {
                               message.error('新建文件夹失败');
                               await refresh();
                           }
                       })
                //    }
                //    else{
                //        contentListApis.newpublicfolder({
                //            "users": [
                //            ],
                //            "name": name,
                //            "parentId": detail.fatherTreeId,
                //            "parentPath": detail.fatherTreePath,
                //            "privilege": "public"
                //        }).then(async res => {
                //            if (res && res.success && res.data) {
                //                message.success('新建文件夹成功');
                //                await refresh();
                //            } else {
                //                message.error('新建文件夹失败');
                //                await refresh();
                //            }
                //        })
                //    }
                    
               } else {
                    contentListApis.newprivatefolder({
                       "users": [
                       ],
                       "name": name,
                       "parentId": detail.fatherTreeId,
                       "parentPath": detail.fatherTreePath,
                       "privilege": "private"
                   }).then(async res => {
                       if (res && res.success && res.data) {
                           message.success('新建文件夹成功');
                           await refresh('', {
                            name: '新建',
                            item: res.data,
                          })
                       } else {
                           message.error('新建文件夹失败');
                           await refresh();
                       }
                   })
               }
           }
           setallrename(false);
        })
    }
    if (modal) {
        return (
            <div className='content_item'>
                <div className='imgbox'>
                    <div>
                        <Keyframe type={detail.type_} root={contentitemBox} src={detail.keyframe}></Keyframe>
                    </div>
                </div>
                <div className='content_title'>
                    <Checkbox disabled={true}></Checkbox>
                    <Input
                        className='content_word'
                        size="small"
                        onPressEnter={(e:any)=>changeName(e,1)}
                        onBlur={changeName}
                        defaultValue={detail.name_}
                        ref={inputRef}
                        autoComplete={'off'}
                    />
                </div>
            </div>
        );
    } else {
        return (
            <div className='list_content'>
                <div className='item checkbox'><Checkbox disabled={true}></Checkbox></div>
                <div className='item checkbox'>
                    <Keyframe type={detail.type_} root={contentitemBox} src={detail.keyframe}></Keyframe>
                </div>
                <div className='contenttitle item'>
                    <Input
                        className='content_word'
                        size="small"
                        onPressEnter={(e:any)=>changeName(e,1)}
                        onBlur={changeName}
                        defaultValue={detail.name_}
                        ref={inputRef}
                        autoComplete={'off'}
                    />
                </div>
                <div className='extension item width1'></div>
                <div className='size item width1'></div>
                <div className='people item width1'></div>
                <div className='time item '></div>
                <div className='type item width1'>文件夹</div>
            </div>
        );
    }
};

export default NewFolder;
