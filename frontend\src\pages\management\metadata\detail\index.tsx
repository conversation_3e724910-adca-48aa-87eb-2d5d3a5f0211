import React, { FC, useEffect, useState } from 'react';
import { SortForm } from '@/components';
import { useIntl, useParams } from 'umi';
import {
  Table,
  Checkbox,
  Select,
  Space,
  Button,
  message,
  Form,
  Modal,
  Input,
  InputNumber,
  Popconfirm,
  List,
} from 'antd';
import { ColumnsType } from 'antd/lib/table';
import {
  SortableContainer,
  SortableElement,
  SortableHandle,
} from 'react-sortable-hoc';
import arrayMove from 'array-move';
import { IResponse } from '@/types/requsetTypes';

import './index.less';
import MetadataService from '@/service/metadataService';
import {
  EditOutlined,
  DeleteOutlined,
  AppstoreOutlined,
  BarsOutlined,
  PlusOutlined,
} from '@ant-design/icons';

const formLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 12 },
};

const SortableWrapper = SortableContainer((props: any) => <tbody {...props} />);
const SortableItem = SortableElement((props: any) => <tr {...props} />);

const typeMap: { [propName: string]: string } = {
  '1': '日期+时间',
  '2': '日期',
  '3': '时间',
  '4': '数字',
  '5': '单行文本',
  '6': '多行文本',
  '7': '单选按钮',
  '8': '下拉列表框',
  '9': '帧显示时码',
  '10': '存储容量',
  '11': '百纳秒显示时码',
  '12': '标签输入框',
  '13': '经纬度信息',
  '14': '树形数据',
  '15': '表格',
  '16': '开始-结束时间',
  '17': '用户选择框',
  '18': '部门选择框',
  '19': '小数',
  '20': 'Uri控件',
  '21': '富文本框',
};

const _localModalKey = 'entity_modal';
const _hiveTypes = ['string', 'long', 'date', 'float', 'integer', 'boolean'];

const MetadataDetail: FC = () => {
  const [editMetadataForm] = Form.useForm();
  const params = useParams<{ code: string; moduleType: string }>();
  const [dataSource, setDataSource] = useState<MetadataTypes.Field[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [viewModel, setViewModal] = useState<string>(
    localStorage.getItem(_localModalKey) || 'column',
  );
  const [fieldList, setFieldList] = useState<MetadataTypes.IHiveEntity2[]>([]);
  const [checkedFields, setCheckedFields] = useState<
    MetadataTypes.IHiveEntity2[]
  >([]);
  const [fieldModalVisible, setFieldModalVisible] = useState(false);
  const [ableType, setAbleType] = useState(_hiveTypes);

  useEffect(() => {
    if (params && params.code) {
      fetchDataSource();
    }
  }, [params]);
  useEffect(() => {
    handleFieldListChange();
  }, [dataSource]);

  const columns: ColumnsType<MetadataTypes.Field> = [
    {
      key: 'order',
      title: '顺序',
      dataIndex: 'order',
    },
    {
      title: 'fieldName',
      dataIndex: 'fieldName',
    },
    {
      key: 'showName',
      title: '显示名称',
      dataIndex: 'showName',
    },
    {
      key: 'controlType',
      title: '类型',
      dataIndex: 'controlType',
      render: (type: string) => typeMap[type],
      // render: (type: string) => (
      //   <Select defaultValue={String(type)} style={{ width: 120 }}>
      //     {Object.keys(typeMap).map((key: string) => (
      //       <Select.Option value={key} key={key}>
      //         {typeMap[key]}
      //       </Select.Option>
      //     ))}
      //   </Select>
      // ),
    },
    {
      key: 'minLength',
      title: '长度限制',
      dataIndex: 'minLength',
      render: (text: number, record: any) =>
        text || text === 0 ? `${text} - ${record.maxLength}` : '',
    },
    {
      key: 'width',
      title: '宽度',
      dataIndex: 'width',
    },
    {
      title: '显示设置',
      dataIndex: 'isEnable',
      render: (text: boolean, record: any) => (
        <Checkbox
          defaultChecked={text}
          onChange={async e => {
            const res = await MetadataService.updateMetadataDisplay({
              id: record.id,
              isShow: e.target.checked ? 1 : 0,
            });
            if (res?.success) {
              const newData = [...dataSource];
              const index = newData.findIndex(item => item.id === record.id);
              if (index > -1) {
                newData.splice(index, 1, {
                  ...newData[index],
                  isEnable: e.target.checked,
                });
                setDataSource(newData);
              }
              message.success('更新成功');
            }
          }}
        >
          显示
        </Checkbox>
      ),
    },
    {
      title: '编辑设置',
      dataIndex: 'isReadOnly',
      render: (text: boolean, record: any) => (
        <Checkbox
          checked={text}
          disabled={record.isMustInput}
          onChange={async e => {
            const res = await MetadataService.updateMetadataReadOnly({
              id: record.id,
              isReadOnly: e.target.checked ? 1 : 0,
            });
            if (res?.success) {
              const newData = [...dataSource];
              const index = newData.findIndex(item => item.id === record.id);
              if (index > -1) {
                newData.splice(index, 1, {
                  ...newData[index],
                  isReadOnly: e.target.checked,
                });
                setDataSource(newData);
              }
              message.success('更新成功');
            }
          }}
        >
          只读
        </Checkbox>
      ),
    },
    {
      key: 'isMustInput',
      title: '是否必填',
      dataIndex: 'isMustInput',
      render: (text: boolean, record: any) => (
        <Checkbox
          checked={text}
          disabled={record.isReadOnly}
          onChange={async e => {
            const res = await MetadataService.updateMetadataRequired({
              id: record.id,
              isRequired: e.target.checked ? 1 : 0,
            });
            if (res?.success) {
              const newData = [...dataSource];
              const index = newData.findIndex(item => item.id === record.id);
              if (index > -1) {
                newData.splice(index, 1, {
                  ...newData[index],
                  isMustInput: e.target.checked,
                });
                setDataSource(newData);
              }
              message.success('更新成功');
            }
          }}
        >
          必填
        </Checkbox>
      ),
    },
    {
      title: '是否多选',
      dataIndex: 'isMultiSelect',
      render: (text: boolean, record: any) => (
        <Checkbox
          checked={text}
          disabled={![8, 14, 17, 18].includes(record.controlType)}
          onChange={async e => {
            const res = await MetadataService.updateMetadataMultiSelect({
              id: record.id,
              isChoice: e.target.checked ? 1 : 0,
            });
            if (res?.success) {
              const newData = [...dataSource];
              const index = newData.findIndex(item => item.id === record.id);
              if (index > -1) {
                newData.splice(index, 1, {
                  ...newData[index],
                  isMultiSelect: e.target.checked,
                });
                setDataSource(newData);
              }
              message.success('更新成功');
            }
          }}
        >
          多选
        </Checkbox>
      ),
    },
    {
      title: '操作',
      dataIndex: 'opt',
      align: 'center',
      render: (_t: any, record) => (
        <Space>
          <Button
            type="primary"
            size="small"
            onClick={() => {
              // @ts-ignore
              record.controlType = String(record.controlType);
              editMetadataForm.setFieldsValue(record);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除该元数据字段？"
            onConfirm={() => handleDelete(Number(record.id))}
          >
            <Button danger={true} size="small">
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const onSortEnd = ({
    oldIndex,
    newIndex,
  }: {
    oldIndex: number;
    newIndex: number;
  }) => {
    console.log(oldIndex, newIndex);
    if (oldIndex !== newIndex) {
      const newData = arrayMove([...dataSource], oldIndex, newIndex).filter(
        el => !!el,
      );
      setDataSource(newData);
    }
  };

  const DraggableWrapper = (props: any) => (
    <SortableWrapper
      className="row-dragging"
      onSortEnd={onSortEnd}
      {...props}
    />
  );

  const DraggableRow = ({ className, style, ...restProps }: any) => {
    // function findIndex base on Table rowKey props and should always be a right array index
    const id = dataSource.findIndex(x => x.id === restProps['data-row-key']);
    return <SortableItem index={id} {...restProps} />;
  };

  const fetchDataSource = async () => {
    setLoading(true);
    const res = await MetadataService.fetchMetadataFields({
      Type: 'basic',
      ResourceType: params.code,
      FieldType: params.moduleType === 'basic' ? '' : params.moduleType,
    });
    if (res?.success && res.data) {
      setLoading(false);
      setDataSource(res.data.sort((a, b) => a.order - b.order));
    }
  };
  /**
   * 修改元数据
   * @param values
   */
  const handleFormFinish = async (values: any) => {
    const res = await MetadataService.updateMetadataField({
      ...values,
      isUploadField: params.moduleType === 'basic' ? 0 : 1,
    });
    if (res?.success) {
      message.success('修改元数据成功');
      fetchDataSource();
      setModalVisible(false);
    }
  };
  // 获取字段列表
  const handleFieldListChange = async () => {
    const { code } = params;
    const res = await MetadataService.fetchHiveMetadataFieldList({ code });
    if (res?.success && res.data) {
      let fList = res.data.data.entity_data_defines;
      const checkedFields = dataSource.map(item => item.fieldName);
      fList = fList.filter(item => !checkedFields.includes(item.code));
      setFieldList(fList);
    }
  };
  const handleCheckedFieldChange = (
    checked: boolean,
    entity: MetadataTypes.IHiveEntity2,
  ) => {
    const newCheckedFields = [...checkedFields];
    if (checked) {
      newCheckedFields.push(entity);
      setCheckedFields(newCheckedFields);
    } else {
      const index = newCheckedFields.findIndex(
        item => item.code === entity.code,
      );
      if (index > -1) {
        newCheckedFields.splice(index, 1);
        setCheckedFields(newCheckedFields);
      }
    }
  };
  /**
   * 添加字段
   */
  const handleAddFieldFinish = () => {
    if (checkedFields.length === 0) {
      message.error('请选择字段');
      return;
    }
    const loading = message.loading('请求中...');
    const promiseList: Promise<IResponse<any> | undefined>[] = [];
    const { code } = params;
    checkedFields.forEach(field => {
      const params: MetadataTypes.Field = {
        dataType: field.type,
        fieldName: field.code,
        showName: field.name,
        fixItemId: field._fixed,
        isReadOnly: !field.editable,
        isMustInput: !!field.required,
        minLength: field.min_length,
        maxLength: field.max_length,
        controlType: 5, // 初始化单行文本
        metadataType: code,
        fieldPath: '',
        defaultValue: '',
        order: field.order,
        isEnable: true,
        type: 'basic',
      };
      promiseList.push(MetadataService.addMetadataField(params));
    });
    Promise.all(promiseList).then(res => {
      loading();
      fetchDataSource();
      setCheckedFields([]);
      message.success('创建成功');
      setFieldModalVisible(false);
    });
  };
  const handleDelete = async (id: number) => {
    // todo
    if (params.moduleType === 'upload') {
      const field = dataSource.find(item => item.id === id);
      if (field) {
        const res = await MetadataService.updateMetadataField({
          ...field,
          isUploadField: 0,
        });
        if (res?.success) {
          fetchDataSource();
          message.success('删除字段成功');
        }
      }
    } else {
      const res = await MetadataService.deleteMetadataField({ id });
      if (res?.success) {
        fetchDataSource();
        message.success('删除字段成功');
      }
    }
  };
  /**
   * 同步上传元数据
   */
  const syncUpload = async () => {
    setLoading(true);
    const res = await MetadataService.fetchMetadataFields({
      Type: 'basic',
      ResourceType: params.code,
    });
    const promises: Promise<IResponse<any> | undefined>[] = [];
    if (res?.success && res.data) {
      res.data
        .filter(item => !item.isUploadField)
        .forEach(item => {
          promises.push(
            MetadataService.syncMetadataUploadField({
              id: item.id || 0,
              isUpload: 1,
            }),
          );
        });
      if (promises.length) {
        Promise.all(promises).then(res => {
          setLoading(false);
          message.success('同步成功');
          fetchDataSource();
        });
      } else {
        setLoading(false);
        message.success('同步成功');
      }
    }
  };

  return (
    <div className="metadata_detail_wrapper">
      <div className="metadata_detail_header">
        <Space className="opt_wrapper">
          <Button onClick={() => history.back()}>返回</Button>
          {params.moduleType === 'basic' ? (
            <Button
              type="primary"
              onClick={() => {
                setAbleType(_hiveTypes);
                setFieldModalVisible(true);
              }}
            >
              字段配置
            </Button>
          ) : (
            <Button type="primary" onClick={syncUpload}>
              同步预览元数据
            </Button>
          )}
        </Space>
        <Space className="view_switch_wrapper">
          <span
            className={
              viewModel === 'column'
                ? 'view_switch_item active'
                : 'view_switch_item'
            }
            onClick={() => {
              setViewModal('column');
              localStorage.setItem(_localModalKey, 'column');
            }}
          >
            <BarsOutlined />
          </span>
          <span
            className={
              viewModel === 'block'
                ? 'view_switch_item active'
                : 'view_switch_item'
            }
            onClick={() => {
              setViewModal('block');
              localStorage.setItem(_localModalKey, 'block');
            }}
          >
            <AppstoreOutlined />
          </span>
        </Space>
      </div>
      <div className="metadata_detail_container">
        {viewModel === 'column' ? (
          <Table
            pagination={false}
            dataSource={dataSource}
            columns={columns}
            loading={loading}
            rowKey="id"
            // components={{
            //   body: {
            //     wrapper: DraggableWrapper,
            //     row: DraggableRow,
            //   },
            // }}
          />
        ) : (
          <SortForm
            dataSource={dataSource}
            onBindField={types => {
              setAbleType(types);
              setFieldModalVisible(true);
            }}
            onSaveSetting={handleFormFinish}
            onDelete={handleDelete}
          />
        )}
      </div>
      <Modal
        title={'编辑元数据字段'}
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => editMetadataForm.submit()}
      >
        <Form
          name="editMetadata"
          {...formLayout}
          initialValues={{}}
          onFinish={handleFormFinish}
          form={editMetadataForm}
        >
          <Form.Item name="id" noStyle={true} hidden={true} />
          <Form.Item name="type" noStyle={true} hidden={true} />
          <Form.Item name="metadataType" noStyle={true} hidden={true} />
          <Form.Item
            label="fieldName"
            name="fieldName"
            rules={[{ required: true, message: '请填写fieldName' }]}
          >
            <Input disabled={true} />
          </Form.Item>
          <Form.Item
            label="hive字段名称"
            name="alias"
            rules={[{ required: true, message: '请填写hive字段名称' }]}
          >
            <Input disabled={true} />
          </Form.Item>
          <Form.Item
            label="显示名称"
            name="showName"
            rules={[{ required: true, message: '请填写显示名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="顺序"
            name="order"
            rules={[{ required: true, message: '请填写顺序' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item label="类型设置" name="controlType">
            <Select style={{ width: 120 }}>
              {Object.keys(typeMap).map((key: string) => (
                <Select.Option value={key} key={key}>
                  {typeMap[key]}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item label="最小长度" name="minLength">
            <InputNumber />
          </Form.Item>
          <Form.Item label="最大长度" name="maxLength">
            <InputNumber />
          </Form.Item>
          <Form.Item label="显示设置" name="isEnable" valuePropName="checked">
            <Checkbox>显示</Checkbox>
          </Form.Item>
          <Form.Item
            noStyle={true}
            shouldUpdate={(pre, cur) => pre.isMustInput !== cur.isMustInput}
          >
            {() => (
              <Form.Item
                label="编辑设置"
                name="isReadOnly"
                valuePropName="checked"
              >
                <Checkbox
                  disabled={(() =>
                    editMetadataForm.getFieldValue('isMustInput'))()}
                >
                  只读
                </Checkbox>
              </Form.Item>
            )}
          </Form.Item>
          <Form.Item
            noStyle={true}
            shouldUpdate={(pre, cur) => pre.isReadOnly !== cur.isReadOnly}
          >
            {() => (
              <Form.Item
                label="必填设置"
                name="isMustInput"
                valuePropName="checked"
              >
                <Checkbox
                  disabled={(() =>
                    editMetadataForm.getFieldValue('isReadOnly'))()}
                >
                  必填
                </Checkbox>
              </Form.Item>
            )}
          </Form.Item>
          <Form.Item
            noStyle={true}
            shouldUpdate={(pre, cur) => pre.controlType !== cur.controlType}
          >
            {() =>
              ['8', '14', '17', '18'].includes(
                editMetadataForm.getFieldValue('controlType'),
              ) && (
                <Form.Item
                  label="是否多选"
                  name="isMultiSelect"
                  valuePropName="checked"
                  shouldUpdate
                >
                  <Checkbox>多选</Checkbox>
                </Form.Item>
              )
            }
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        title="字段配置"
        visible={fieldModalVisible}
        onCancel={() => setFieldModalVisible(false)}
        onOk={handleAddFieldFinish}
      >
        <List
          style={{ height: 420, overflowY: 'auto' }}
          size="small"
          itemLayout="horizontal"
          dataSource={fieldList}
          renderItem={item => (
            <List.Item>
              {fieldModalVisible && (
                <Checkbox
                  defaultChecked={item.checked}
                  disabled={item.checked || !ableType.includes(item.type)}
                  onChange={e =>
                    handleCheckedFieldChange(e.target.checked, item)
                  }
                >
                  {`${item.name} (${item.code}) `}
                </Checkbox>
              )}
            </List.Item>
          )}
        />
      </Modal>
    </div>
  );
};
export default MetadataDetail;
