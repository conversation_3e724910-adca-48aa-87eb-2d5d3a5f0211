import React from 'react';
import { Modal, Input, Table, Button, Select, Form } from 'antd';
import "./TopicSelectModal.less";
import { useEffect } from 'react';
import { useState } from 'react';
import { fetchTopicList } from '@/service/homework';
import { optionType_ } from '@/utils';
import $ from "jquery";
import timecodeconvert from '@/components/time-code/timeCode';
import {  useIntl } from 'umi';
const {Option} = Select;
interface ITopicSelectModal {
  selectKeys?: any,
  type?: string,// 单选还是多选
  visible: boolean,
  currentPlayTime: number,
  questionVersion: any,
  versionCode:string,
  onConfirm: (data: any) => void,
  setCurrentFrame: (data: any) => void,
  onAdd: () => void,
  setVersionCode: (e: any)=> void,
  onclose: () => void,
  disabled?: any,
  currentname?: string,
  iscoursemap?: boolean
}

const TopicSelectModal: React.FC<ITopicSelectModal> = ({ selectKeys, type, questionVersion, versionCode, visible, currentPlayTime, onAdd, setCurrentFrame, setVersionCode, onConfirm, onclose, disabled = [], currentname, iscoursemap }) => {
  const [query, setQuery] = useState<any>({
    questions_content: '',
    questions_type: undefined,
    page: 1,
    size: 10
  });
  const [data, setData] = useState<any>([]);
  const [total, setTotal] = useState<any>([]);
  const intl = useIntl();
  const [selectRows, setSelectedRows] = useState<any>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  const [curImage, setCurImage] = useState<string>("");
  const [imageVisible, setImageVisible] = useState<boolean>(false);
  const type_enum = optionType_; //题目类型
  const [questions_content, setQuestions_content] = useState<any>(''); //题目内容
  const [form] = Form.useForm();
  useEffect(() => {
    if (selectKeys?.length > 0) {
      console.log('selectKeys', selectKeys);
      setSelectedRowKeys(selectKeys?.map((item: any) => item.parent_id || item.topicId || item.id));
    }
  }, [selectKeys]);
  useEffect(() => {
    $(document).on("click", ".topic_modal .special-dom img", (e: any) => {
      setCurImage(e.target.src);
      setImageVisible(true);
    });
  }, []);
  const nameChange = (e: any) => {
    setQuestions_content(e.target.value);
    setQuery({
      ...query,
      page: 1,
      questions_content: e.target.value
    });
  };
  const typeChange = (e: any) => {
    console.log(e);
    setQuery({
      ...query,
      page: 1,
      questions_type: e
    });
  };
  const fetchDataList = async () => {
    const res: any = await fetchTopicList({
      ...query,
      excludeQuestionTypes: disabled?.toString()
    });
    setData(res?.data);
    setTotal(res?.totalCount);
  };
  useEffect(() => {
    fetchDataList();
  }, [query]);
  const columns: any = [
    {
      title: intl.formatMessage({ id: '类型' }),
      dataIndex: 'questions_type',
      key: 'questions_type',
      ellipsis: true,
      render: (item: any, record: any) => (
        <div>{optionType_[Number(item)]}</div>
      )
    },
    {
      title: intl.formatMessage({ id: '难度' }),
      dataIndex: 'questions_difficulty',
      key: 'questions_difficulty',
      ellipsis: true
    },
    {
      title: intl.formatMessage({ id: '题目' }),
      dataIndex: 'questions_content',
      key: 'questions_content',
      ellipsis: true,
      render: (value: any) => (
        <div dangerouslySetInnerHTML={{ __html: value }} className='special-dom'></div>
      ),
    },
    {
      title: intl.formatMessage({ id: '答案' }),
      dataIndex: 'questions_answers',
      key: 'questions_answers',
      ellipsis: true,
      render: (value: any, record: any) => (
        <div>{value?.join(',')}</div>
      )
    },
    {
      title: intl.formatMessage({ id: '创建人' }),
      // width: '8%',
      dataIndex: 'add_username',
      key: 'add_username',
      ellipsis: true
    },
  ];
  const rowSelection = {
    type: type,
    onChange: (newSelectedRowKeys: Array<any>, newSelectedRows: any) => {
      setSelectedRowKeys(newSelectedRowKeys.filter(Boolean));
      setSelectedRows(newSelectedRows.filter(Boolean)); //得处理勾选移除后的残余空值对象
      console.log('query', query);
    },
    preserveSelectedRowKeys: true,
    selectedRowKeys,
  };
  const handleChange = (e: any) => {
    setVersionCode(e)
  }
  const confirm = () => {
    onConfirm(selectRows);
    close();
  };
  const reset = () => {
    setQuery({
      ...query,
      page: 1,
      questions_content: '',
      questions_type: undefined
    });
  };
  const close = () => {
    // setSelectedRowKeys(selectKeys?.map((item: any) => item.parent_id ? item.parent_id : item.id));
    setSelectedRowKeys([]);
    setSelectedRows([])
    onclose();
  };
  const setTime = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCurrentFrame(timecodeconvert.timeCode2L100Ns_audio(e.target.value))
  }
  return (
    <Modal
      title={intl.formatMessage({ id: '选择题目' })}
      visible={visible}
      onCancel={close}
      width={944}
      destroyOnClose
      className='topic_modal'
      footer={[
        <Button
          type='primary'
          key='1'
          onClick={confirm}
          disabled={selectRows.length === 0}
        >
          {intl.formatMessage({ id: '确定' })}
        </Button>,
        <Button
          key='2'
          onClick={close}
        >
          {intl.formatMessage({ id: '取消' })}
        </Button>,
      ]}
    >
      <div className="header">
        <span>{intl.formatMessage({ id: '在当前视频' })}</span>
        <Input style={{ width: '150px' }} defaultValue={currentPlayTime} onChange={(e) => {setTime(e)}} />
        <span>{intl.formatMessage({ id: '位置添加题目，题目所属互动试题版本' })}：</span>
        <Select
          style={{ width: 190 }}
          value={versionCode}
          onChange={handleChange}
        >
          {
            questionVersion?.length&&questionVersion.map((item: any) => <Option value={item.code}>{item.name}</Option>)
          }
        </Select>
        {/* <Form
          layout='inline'
          form={form}
        >
          <Form.Item label="在当前视频" name="time">
            <Input/>
            <span>fsafasfsdfaf</span>
          </Form.Item>
          <Form.Item label="">
            <Input placeholder="input placeholder" />
          </Form.Item>
        </Form> */}
      </div>
      <div className='search-box'>
        <div className="btn-box">
          <Input placeholder={intl.formatMessage({ id: '请输入名称' })} onChange={nameChange} value={questions_content} onClick={() => {
            if (questions_content == '' || questions_content == undefined || questions_content == null) {
              setQuestions_content(currentname);
              nameChange({
                target: {
                  value: currentname
                }
              })
            }
          }} style={{ width: '200px', marginRight: '20px' }} allowClear />
          <Select placeholder={intl.formatMessage({ id: '题目类型' })} onChange={typeChange} value={query.questions_type}>
            {
              type_enum.map((item: any, index: number) => (
                <Select.Option
                  value={index}
                  key={item + index}
                  disabled={disabled?.includes(index)}
                >
                  {item}
                </Select.Option>
              ))
            }
          </Select>
          <Button
            type='primary'
            onClick={reset}
          >
            {intl.formatMessage({ id: '重置' })}
          </Button>
        </div>
        <div>{intl.formatMessage({ id: '没有需要的试题/试卷' })}？<a onClick={onAdd}>{iscoursemap ? intl.formatMessage({ id: '去题库' }) : intl.formatMessage({ id: '立即添加' })}</a></div>
      </div>
      <div>
        <Table
          dataSource={data}
          rowKey={"id"}
          columns={columns}
          // key={selectedRowKeys}
          rowSelection={rowSelection as any}
          pagination={{
            position: ['bottomCenter'],
            showSizeChanger: true,
            total: total,
            showQuickJumper: true,
            onChange: (page: number, size: any) =>
              setQuery({
                ...query,
                page,
                size
              }),
            showTotal: total => intl.formatMessage({ id: '共个' },{total}),
          }}
          scroll={{ y: '420px' }}
        />
      </div>
      {/* <ImageModal image={curImage} visible={imageVisible} onClose={() => setImageVisible(false)} /> */}
    </Modal >
  );
};

export default TopicSelectModal;
