.content_item {
  width: 205px;
  background: #F7F9FA;
  border: 1px solid #f4f4f4;
  box-sizing: border-box;
  position: relative;
  margin-right: calc((100% - 252px * 6 - 10px) / 6);
  margin-bottom: 10px;
  margin-top: 10px;
  margin-left: 10px;
  // border-radius: 6px 6px 0px 0px;
  border-radius: 10px;
  // float: left;
  .imgbox {
    // display: flex;
    // justify-content: center;
    position: relative;
    // height: 126px;
    aspect-ratio: 16/9;
    cursor: pointer;
    overflow: hidden;
    
    // position: relative;
    color: rgba(255, 255, 255, 1);
    background: #F7F8FA;
    border-radius: 10px 10px 0 0;
    div:nth-of-type(1) {
      width: 100%;
      // height: 126px;
      aspect-ratio: 16/9;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 10px;
      img {
        // width:220px;
      }

      // .folder {
      //   width: 100px;
      // }
    }

    .shared{
      position: absolute;
      left: 6px;
      bottom: 6px;
      z-index: 2;
      width: 48px;
      height: 18px;
      background: rgba(0, 0, 0, .7);
      border-radius: 11px;
      text-align: center;
      line-height: 18px;
      color: #fff;
      font-size: 11px;
    }
    video{
      width: 100%;
      height: 100%;
      position: absolute;
      right:0;
      bottom: 0;
    }
    .total_hits{
      position: absolute;
      left: 8px;
      bottom: 0;
      z-index: 2;
      display: flex;
      align-items: center;
    }
    .time1 {
      height: 26px;
      // width: 75%;
      // background-color: rgba(0, 0, 0, 0.4);
      font-size: 14px;
      line-height: 26px;
      padding-left: 10px;
      position: absolute;
      padding-right: 5px;
      right:0;
      bottom: 0;
      z-index: 2;
    }
    .time2 {
      font-size: 12px;
      // height: 26px;
      height: 22px;
      // width: 25%;
      width: 50px;
      background-color: var(--second-color);
      line-height: 22px;
      text-align: center;
      position: absolute;
      left: 0;
      top: 0;
      border-radius: 9px 1px 13px 1px;
      >span{
        width: 30px;
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 20px;
      }
    }
    .biz_sobey_video{
      background: #8183F0;
    }
    .biz_sobey_audio{
      background: #25AF20;
    }
    .biz_sobey_document{
      background: #59B6FD;
    }
    .biz_sobey_picture{
      background: #FE9A41;
    }
    .biz_sobey_other{
      background: #62738A;
    }
    .isDeleteDiv{
      background: black;
      position: relative;
      .isDeleteFlag{
        opacity: 0.35;
      }
      .isDeleteSpan{
        position: absolute;
        font-size: 12px;
        color: white;
      }
    }
  }
  .img_mask{
    position: absolute;
    width: 100%;
    height: 55px;
    bottom: 0;
    background-image: linear-gradient( 180deg,rgba(0,0,0,0) 0%,rgba(0,0,0,.3) 100%);
    border-radius: 0 0 10px 10px;
  }
  .content_title {
    padding-left: 16px;
    display: flex;
    color: rgba(28, 28, 28, 1);
    font-size: 15px;
    margin: 10px 0;
    .unread{
      display: inline-block;
      width: 10px;
      height: 10px;
      background: #FF5252;
      box-shadow: 0px 2px 4px 0px rgb(253 118 118 / 10%);
      border: 1px solid #FFFFFF;
      border-radius: 50%;
    }
    .content_word {
      margin-left: 5px;
      cursor: pointer;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      word-break: break-all;
      text-overflow: ellipsis;
      transition: all .2s ease 0s;
      &.search_word{
        -webkit-line-clamp: 1;
      }
      &:hover {
        color: var(--primary-color);
      }
      .resource-search-value{
        color: var(--primary-color);
      }
    }
    .mobile_word{
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      >span.title_{
        width: 80%;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .mobile_single_btns{
        margin-right: 5px;
        color: var(--primary-color);
        font-size: 18px;
      }
    }
  }
  .resource {
    width: 100%;
    padding-left: 16px;
    overflow: hidden;
    max-height: 26px;
    
    .resource-search-value{
      font-size: 12px;
      color: var(--primary-color);
      margin-left: 5px;
    }
    .resource {
      text-align: right;
    }
    .type_knowladge,
    .type_voice,
    .type_teacher {
      font-size: 12px;
      height: 22px;
      line-height: 22px;
      text-align: center;
      display: inline-block;
      border-radius: 99px;
      padding: 0 8px;
    }
    .type_knowladge {
      background-color: rgba(255, 105, 81, 0.1);
      color: rgba(255, 105, 81, 1);
    }
    .type_voice {
      background-color: rgba(37, 175, 32, 0.1);
      color: rgba(37, 175, 32, 1);
    }
    .type_teacher {
      background-color: #f6c31d23;
      color: #f6c21d;
    }
  }

  .content_links {
    z-index: 1000;
    opacity: 0;
    // background-color: rgba(0, 0, 0, 0.8);
    position: absolute;
    bottom: 100px;
    // bottom: 35px;
    display: flex;
    // justify-content: space-between;
    // align-items: center;
    // padding: 0 20px;
    transition: all 0.5s;
    right: 6px;
    top: 6px;
    justify-content: space-evenly;
    &.empty{
      display: none !important;
    }
    .icon {
      color: #fff;
      cursor: pointer;
      transition: all .2s ease 0s;
    }
    .icon_style{
      color: #ffffff;
      cursor: pointer;
      transition: all .2s ease 0s;
      width: 34px;
      height: 34px;
      background: var(--primary-color);
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 16px;
    }
  }

  .labelbox {
    position: absolute;
    top: 14px;
    display: flex;
    right: 14px;

    .item {
      width: 22px;
      height: 22px;
      background: #000000;
      border-radius: 2px;
      opacity: 0.4;
      color: #FFFFFF;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 18px;
      margin-left: 6px;
    }
  }

  // .usebox{
  //   position: absolute;
  //   right: -4px;
  //   width: 60px;
  //   top: -2px;
  //   img{
  //     width: 60px;
  //   }
  // }
}

.content_item:nth-child(6n) {
  margin-right: 10px;
}
@media screen and (min-width:1921px) and (max-width: 2560px) {

  .content_item, 
  .content_item:nth-child(6n) {
    margin-right: calc((100% - 218px * 9 - 10px) / 8);
  }
}
@media screen and (min-width:1921px) and (max-width: 2299px) {

  .content_item, 
  .content_item:nth-child(6n) {
    margin-right: calc((100% - 218px * 8 - 10px) / 7);
  }
}
@media screen and (max-width: 1919px) {

  .content_item,
  .content_item:nth-child(6n) {
    margin-right: calc((100% - 218px * 5 - 10px) / 4);
  }

  .content_item:nth-child(5n) {
    margin-right: 10px;
  }
}

@media screen and (max-width: 1599px) {

  .content_item,
  .content_item:nth-child(6n),
  .content_item:nth-child(5n) {
    margin-right: calc((100% - 218px * 4 - 10px) / 3);
  }

  .content_item:nth-child(4n) {
    margin-right: 10px;
  }
}

@media screen and (max-width: 1365px) {

  .content_item,
  .content_item:nth-child(6n),
  .content_item:nth-child(5n),
  .content_item:nth-child(4n) {
    margin-right: calc((100% - 218px * 3 - 10px) / 2);
  }

  .content_item:nth-child(3n) {
    margin-right: 10px;
  }
}
//移动端
@media screen and (max-width: 768px) {

  .content_item,
  .content_item:nth-child(6n),
  .content_item:nth-child(5n),
  .content_item:nth-child(4n),
  .content_item:nth-child(3n) {
    &:hover{
      transform:scale(1) !important;
      .item_hover_mask{
        height: 100px !important;
      }
      .content_links{
        bottom: 70px !important;
      }
    }
    width: 46%;
    .imgbox{
      // height: 100px ;
      div:nth-of-type(1){
        height: 100%;
      }
    }
    .content_links{
      bottom: 50px !important;
    }
    .img_mask{
      top: 46px!important;
    }
    margin-right: 1%;
  }

  .content_item:nth-child(2n) {
    margin-right: 0;
  }
}
.content_item:hover{
  transform: scale(1.08);
  box-shadow: 0 2px 8px 2px rgba(129, 129, 129, 0.1);
  transition: all .3s;
  z-index: 999;
  // .item_hover_mask{
  //   cursor: pointer;
  //   transition: all .5s;
  //   width: 100%;
  //   aspect-ratio: 16/9;
  //   border-radius: 10px 10px 0 0;
  //   opacity: 0.4;
  //   position: absolute;
  //   top: 0;
  //   background: #000000;
  // }
}
.content_item .content_links_focus{
  opacity: 1 ;
  bottom: 100px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  >div{
    width: 20px;
    height: 20px;
    // background: var(--primary-color);
    background: var(--primary-color);
    font-size: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    >.icon{
      width: auto;
    }
  }
}
.content_item:hover .content_links {
  opacity: 1;
  bottom: 100px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  &.empty{
    display: none !important;
  }
  >div{
    width: 20px;
    height: 20px;
    // background: var(--primary-color);
    background: var(--primary-color);
    font-size: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    >.icon{
      width: auto;
    }
  }
}

.list_content {
  // height: 38px;
  padding: 10px 0;
  display: flex;
  width: 100%;
  align-items: center;

  .item {
    display: flex;
    align-items: center;
    // border-left: 1px solid #999999;
    justify-content: center;
    a{
      text-align: center;
    }
  }

  .item:nth-of-type(1) {
    border: none
  }

  .checkbox {
    // flex: .4;
    width: 48px;
    height: 27px;
    overflow: hidden;
    position: relative;
    .unread{
      position: absolute;
      right: 0;
      width: 10px;
      height: 10px;
      background: #FF5252;
      box-shadow: 0px 2px 4px 0px rgb(253 118 118 / 10%);
      border: 1px solid #FFFFFF;
      border-radius: 50%;
    }
    .keyframe {
      // width: 45px;
    }

    .folder {
      width: 27px;
    }
  }
  .teacher_name{
    width: 120px;
    span{
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .width1 {
    width: 120px;
    text-align: center;
  }
  .seat{
    cursor: pointer;
    width: 150px;
    .ant-checkbox + span {
      color: var(--primary-color);
    }
    .video_seat{
      display: flex;
      justify-content: center;
      .content_links {
        opacity: 0;
        //width: 100px;
        // justify-content: space-between;
        transition: all 0.2s;
        .moreBtn{
          font-size: 18px;
        }
        .icon {
          // margin-right: 12px;
          cursor: pointer;
          transition: all .2s ease 0s;
  
          &:hover {
            color: var(--primary-color);
          }
        }
        .linkIcon{
          font-size: 18px;
        }
      }
      &:hover{
        .content_links{
          opacity: 1;
        }
      }
      
    }
  }
  .a_time, .c_time {
    width: 180px;
  }
  .contenttitle {
    padding-left: 5px;
    flex: 1;
    justify-content: space-between;
    .content_word {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
      flex: 1 0 auto;
      width: 1px;
      transition: all .2s ease 0s;
      padding-left: 5px;
      .anticon-arrow-down{
        display: none;
        margin-left: 8px;
      }
      &:hover {
        color: var(--primary-color);
        .anticon-arrow-down{
          display: inline-block;
        }
      }
      .resource-search-value{
        color: var(--primary-color);
      }
      &.r_content_word{
        cursor: default;
        &:hover {
          color: rgba(0, 0, 0, 0.85);
        }
      }
    }

    .content_links {
      opacity: 0;
      //width: 100px;
      display: flex;
      // justify-content: space-between;
      transition: all 0.2s;
      .moreBtn{
        font-size: 18px;
      }
      .icon {
        // margin-right: 12px;
        cursor: pointer;
        transition: all .2s ease 0s;

        &:hover {
          color: var(--primary-color);
        }
      }
      .linkIcon{
        font-size: 18px;
      }
    }
    .isDeleteDiv{
      background: black;
      position: relative;
      .isDeleteFlag{
        opacity: 0.35;
      }
      .isDeleteSpan{
        position: absolute;
        font-size: 12px;
        color: white;
        transform: scale(0.7);
      }
    }
  }
  .overFlowStyle {
    padding-left: 5px;
    // flex: 1;
    width: 350px;
    justify-content: space-between;

    .content_word {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex: 1 0 auto;
      width: 1px;
      cursor: pointer;
      transition: all .2s ease 0s;
      padding-left: 5px;
      &:hover {
        color: var(--primary-color);
      }
    }

    .content_links {
      opacity: 0;
      //width: 100px;
      display: flex;
      // justify-content: space-between;
      transition: all 0.2s;
      .moreBtn{
        font-size: 18px;
      }
      .icon {
        // margin-right: 12px;
        cursor: pointer;
        transition: all .2s ease 0s;

        &:hover {
          color: var(--primary-color);
        }
      }
    }
    .isDeleteDiv{
      background: black;
      position: relative;
      .isDeleteFlag{
        opacity: 0.35;
      }
      .isDeleteSpan{
        position: absolute;
        font-size: 12px;
        color: white;
        transform: scale(0.7);
      }
    }
  }
  .week{
    width: 100px;
  }
  .time, .semester, .area_name {
    width: 180px;
    span{
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .linkshare{
    opacity: 0.5;
  }
}

.list_content:nth-of-type(2n) {
  background: #F2F2F2;
}

.list_content:hover .content_links {
  opacity: 1;
}

.list_content:hover {
  background: rgba(0, 0, 0, .03);
}

.list_contenttop {
  position: relative;
  font-size: 15px;
  background: #d8d8d8;

  .contenttitle {
    // text-align: center;
    padding: 0 20px 0 0px;
  }
}

.list_contenttop:hover {
  background: #d8d8d8;
}
.ant-popover-content{
  .ant-popover-inner{
    .ant-popover-inner-content{
      .moreOpt{
        >div{
          display: flex;
          cursor: pointer;
          align-items: center;
          margin-bottom: 14px;
          white-space: nowrap;
          &:hover{
            color: var(--primary-color);
          }
          >div{
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-right: 8px;
            font-size: 18px;
          }
        }
      }
    }
  }
}