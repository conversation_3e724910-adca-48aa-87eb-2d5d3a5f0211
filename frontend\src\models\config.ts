// import { Effect, ImmerReducer, Reducer, Subscription } from 'umi';

import loginApis from '@/service/loginApis';
import { IReducers } from '@/types/modelsTypes';

export interface IConfig {
  showLoading: boolean;
  mobileFlag: boolean;//移动端标识
  leftMenuExpand: boolean;//移动端左侧菜单显示
  publicDomain:any; //公用网络域名
}

export default {
  namespace: 'config',
  subscriptions:{
    setup({ dispatch, history }: any) {
      dispatch({
        type:'fetchPublicDomain'
      })
      // return history.listen(({ pathname }: any) => {
      //   dispatch({
      //     type:'fetchPublicDomain'
      //   })
      // });
    },
  },
  state: {
    showLoading: false,
    leftMenuExpand: false,
    mobileFlag:false,
    publicDomain:undefined,
  },
  effects:{
    *fetchPublicDomain(_: any, { call, put }: any){
      const {errorCode, extendMessage}:API.OsResponse<any> = yield call(loginApis.fetchPlatformDomain);
      console.log(extendMessage);
      if(errorCode=="success"){ 
        const domain = extendMessage.find((item:any)=>item.code=='website_access_address');
        if(domain.value?.trim()){
          console.log(domain.value);
          yield put({
            type: 'updateState',
            payload: {
              publicDomain:domain.value
            },
          })
        }
      }
    }
  },
  reducers: {
    updateState: (
      state: IConfig,
      { payload }: IReducers<IConfig>,
    ) => {
      return {
        ...state,
        ...payload
      };
    },
  },
};
