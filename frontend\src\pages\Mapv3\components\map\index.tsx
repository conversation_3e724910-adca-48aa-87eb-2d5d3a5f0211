import React,{ useState , useEffect, FC, useRef  } from "react";
import ReactDOM from 'react-dom';
import './index.less'
import G6 from '@antv/g6';
import {
  Rect,
  Text,
  Circle,
  Image,
  Group,
  createNodeFromReact,
  appenAutoShapeListener
} from '@antv/g6-react-node';
import { MapConfig } from '../util';

const Map:FC<any> =()=> {
    const container = useRef<any>(null);
    const graph = useRef<any>(null);  //全局方法

    useEffect(()=>{
        initmap();
    },[])

    // 
    const marjorNode = ({ cfg = {} }:any) => {
      const { id,data} = cfg;
      let config:any = null;
      if(data.type == 0){
        config = MapConfig.marjor;
      }else if (data.type == 1){
        config = MapConfig.course;
      }else if (data.type == 2){
        config = MapConfig.fenlei;
      }else if (data.type == 3){
        config = MapConfig.knowledge;
      }
      let marginLeft = config.size[0]/2 * -1;
      let marginTop = config.size[1]/2 * -1;
      
      return(
        <Group draggable style={{width: config.size[0],height: config.size[1], margin:[marginTop,0,0,marginLeft]}}>
          <Rect style={{alignContent:'center'}} draggable>
            <Image style={{
              width: config.iconsize[0],
              height: config.iconsize[1],
              img: config.icon,
            }} draggable />
          </Rect>
          <Rect style={{ width: config.size[0],height:30,alignItems:'center',justifyContent:'center'}}>
            <Text style={{
              fill:'#fff',
              fontSize: config.fontSize,
            }} draggable>{id}</Text>
          </Rect>          
        </Group>
      )
    }

    
    // 注册自定义节点
    const registerstyle = () =>{
      G6.registerNode('marjorNode', createNodeFromReact(marjorNode));      
    }

    const initmap = () =>{
        if (graph.current) {
            try {
              graph.current.dispose();
             
            } catch (error) {
              console.log(error);
            }
          }
        const containerdom: any = ReactDOM.findDOMNode(container.current);
        const width = containerdom.scrollWidth;
        const height = containerdom.scrollHeight || 1000;
        // g6 注册react 组件
        registerstyle();
        // 初始化图        
        graph.current = new G6.Graph({
            container: containerdom,
            width,
            height,
            fitCenter: true,
            animate: true,
            // layout: {
            //   type: 'forceAtlas2',             
            //   maxSpeed: 100,
            //   nodeClusterBy: 'cluster',
            //   clusterNodeStrength: 300, 
            //   workerEnabled: true,      // web-worker 启用，若浏览器不支持，会自动关闭
            //   preventOverlap: true,        
            //   animate: true,            // 动画       
            //   nodeSpacing:40,           // 防碰撞节点间距     
            //   clustering: true,         // 聚类 
            //   onTick: () => {           // 可选
            //     console.log('ticking');
            //   },
            //   onLayoutEnd: () => {      // 可选
            //     console.log('force layout done');
            //   },              
            //   linkDistance: (d) => {
            //     if (d.source.id === 'node0') {
            //       return 100;
            //     }
            //     return 30;
            //   },
            //   nodeStrength: (d) => {
            //     if (d.isLeaf) {
            //       return -50;
            //     }
            //     return -10;
            //   },
            //   edgeStrength: (d) => {
            //     if (d.source.id === 'node1' || d.source.id === 'node2' || d.source.id === 'node3') {
            //       return 0.7;
            //     }
            //     return 0.1;
            //   },
            // },
            layout: {
              type: 'force',
              preventOverlap: true,              
              linkDistance: (d) => {
                if (d.source.id === 'node0') {
                  return 200;
                }
                return 130;
              },
              nodeStrength: (d) => {
                if (d.isLeaf) {
                  return -150;
                }
                return -110;
              },
              edgeStrength: (d) => {
                if (d.source.id === 'node1' || d.source.id === 'node2' || d.source.id === 'node3') {
                  return 0.7;
                }
                return 0.1;
              },
            },
            defaultNode: {
                type: 'marjorNode',
                linkPoints: {
                  top: true,
                  right: true,
                  bottom: true,
                  left: true,
                }
            },
            defaultEdge: {
              type: 'line',
              style: {
                endArrow: true,
                startArrow: false,
              }
            },
            modes: {
                // 'drag-node'
                default: ['zoom-canvas','drag-canvas'],
            },
        });
        const data = {
            nodes: [
              { id: 'node0', size: MapConfig.marjor.size ,data:{type:0}},
              { id: 'node1', size: MapConfig.course.size ,data:{type:1}},
              { id: 'node2', size: MapConfig.course.size ,data:{type:1}},
              { id: 'node3', size: MapConfig.course.size ,data:{type:1}},
              { id: 'node4', size: MapConfig.fenlei.size , data:{type:2}},
              { id: 'node5', size: MapConfig.fenlei.size, data:{type:2} },
              { id: 'node6', size: MapConfig.fenlei.size, data:{type:2} },
              { id: 'node7', size: MapConfig.fenlei.size, data:{type:2} },
              { id: 'node8', size: MapConfig.fenlei.size, data:{type:2} },
              { id: 'node9', size: MapConfig.fenlei.size, data:{type:2} },
              { id: 'node10', size: MapConfig.knowledge.size, data:{type:3} },
              { id: 'node11', size: MapConfig.knowledge.size, data:{type:3} },
              { id: 'node12', size: MapConfig.knowledge.size, data:{type:3} },
              { id: 'node13', size: MapConfig.knowledge.size, data:{type:3} },
              { id: 'node14', size: MapConfig.knowledge.size, data:{type:3} },
              { id: 'node15', size: MapConfig.knowledge.size, data:{type:3} },
              { id: 'node16', size: MapConfig.knowledge.size, data:{type:3} },
            ],
            edges: [
              { source: 'node0', target: 'node1' },
              { source: 'node0', target: 'node2' },
              { source: 'node0', target: 'node3' },
              { source: 'node0', target: 'node4' },
              { source: 'node0', target: 'node5' },
              { source: 'node1', target: 'node6' },
              { source: 'node1', target: 'node7' },
              { source: 'node2', target: 'node8' },
              { source: 'node2', target: 'node9' },
              { source: 'node2', target: 'node10' },
              { source: 'node2', target: 'node11' },
              { source: 'node2', target: 'node12' },
              { source: 'node2', target: 'node13' },
              { source: 'node3', target: 'node14' },
              { source: 'node3', target: 'node15' },
              { source: 'node3', target: 'node16' },
            ],
          };
          const nodes = data.nodes;
          graph.current.data({
            nodes,
            edges: data.edges.map(function (edge, i) {
              edge.id = 'edge' + i;
              return Object.assign({}, edge);
            }),
          });
          graph.current.render();
          
          graph.current.on('node:dragstart', function (e) {
            graph.current.layout();
            refreshDragedNodePosition(e);
          });
          graph.current.on('node:drag', function (e) {
            refreshDragedNodePosition(e);
          });
          graph.current.on('node:dragend', function (e) {
            e.item.get('model').fx = null;
            e.item.get('model').fy = null;
          });
          
          if (typeof window !== 'undefined'){
            window.onresize = () => {
                if (!graph.current || graph.current.get('destroyed')) return;
                if (!containerdom || !containerdom.scrollWidth || !containerdom.scrollHeight) return;
                graph.current.changeSize(containerdom.scrollWidth, containerdom.scrollHeight);
            };
          };
    }

    const refreshDragedNodePosition =(e) => {
        const model = e.item.get('model');
        model.fx = e.x;
        model.fy = e.y;
        model.x = e.x;
        model.y = e.y;
    }

    return (
        <div className="mapv3_view">
            <div className="map_canvas" ref={container}></div>
        </div>
    )
}

export default Map;