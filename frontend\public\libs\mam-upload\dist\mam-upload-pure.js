/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, {
/******/ 				configurable: false,
/******/ 				enumerable: true,
/******/ 				get: getter
/******/ 			});
/******/ 		}
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "/dist/";
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = 119);
/******/ })
/************************************************************************/
/******/ ([
/* 0 */
/***/ (function(module, exports) {

// https://github.com/zloirock/core-js/issues/86#issuecomment-115759028
var global = module.exports = typeof window != 'undefined' && window.Math == Math
  ? window : typeof self != 'undefined' && self.Math == Math ? self
  // eslint-disable-next-line no-new-func
  : Function('return this')();
if (typeof __g == 'number') __g = global; // eslint-disable-line no-undef


/***/ }),
/* 1 */
/***/ (function(module, exports, __webpack_require__) {

var store = __webpack_require__(29)('wks');
var uid = __webpack_require__(18);
var Symbol = __webpack_require__(0).Symbol;
var USE_SYMBOL = typeof Symbol == 'function';

var $exports = module.exports = function (name) {
  return store[name] || (store[name] =
    USE_SYMBOL && Symbol[name] || (USE_SYMBOL ? Symbol : uid)('Symbol.' + name));
};

$exports.store = store;


/***/ }),
/* 2 */
/***/ (function(module, exports) {

var core = module.exports = { version: '2.6.12' };
if (typeof __e == 'number') __e = core; // eslint-disable-line no-undef


/***/ }),
/* 3 */
/***/ (function(module, exports, __webpack_require__) {

var isObject = __webpack_require__(6);
module.exports = function (it) {
  if (!isObject(it)) throw TypeError(it + ' is not an object!');
  return it;
};


/***/ }),
/* 4 */
/***/ (function(module, exports, __webpack_require__) {

var dP = __webpack_require__(5);
var createDesc = __webpack_require__(17);
module.exports = __webpack_require__(7) ? function (object, key, value) {
  return dP.f(object, key, createDesc(1, value));
} : function (object, key, value) {
  object[key] = value;
  return object;
};


/***/ }),
/* 5 */
/***/ (function(module, exports, __webpack_require__) {

var anObject = __webpack_require__(3);
var IE8_DOM_DEFINE = __webpack_require__(39);
var toPrimitive = __webpack_require__(26);
var dP = Object.defineProperty;

exports.f = __webpack_require__(7) ? Object.defineProperty : function defineProperty(O, P, Attributes) {
  anObject(O);
  P = toPrimitive(P, true);
  anObject(Attributes);
  if (IE8_DOM_DEFINE) try {
    return dP(O, P, Attributes);
  } catch (e) { /* empty */ }
  if ('get' in Attributes || 'set' in Attributes) throw TypeError('Accessors not supported!');
  if ('value' in Attributes) O[P] = Attributes.value;
  return O;
};


/***/ }),
/* 6 */
/***/ (function(module, exports) {

module.exports = function (it) {
  return typeof it === 'object' ? it !== null : typeof it === 'function';
};


/***/ }),
/* 7 */
/***/ (function(module, exports, __webpack_require__) {

// Thank's IE8 for his funny defineProperty
module.exports = !__webpack_require__(16)(function () {
  return Object.defineProperty({}, 'a', { get: function () { return 7; } }).a != 7;
});


/***/ }),
/* 8 */
/***/ (function(module, exports) {

var hasOwnProperty = {}.hasOwnProperty;
module.exports = function (it, key) {
  return hasOwnProperty.call(it, key);
};


/***/ }),
/* 9 */
/***/ (function(module, exports, __webpack_require__) {

// to indexed object, toObject with fallback for non-array-like ES3 strings
var IObject = __webpack_require__(59);
var defined = __webpack_require__(24);
module.exports = function (it) {
  return IObject(defined(it));
};


/***/ }),
/* 10 */
/***/ (function(module, exports) {

module.exports = true;


/***/ }),
/* 11 */
/***/ (function(module, exports, __webpack_require__) {

var global = __webpack_require__(0);
var core = __webpack_require__(2);
var ctx = __webpack_require__(14);
var hide = __webpack_require__(4);
var has = __webpack_require__(8);
var PROTOTYPE = 'prototype';

var $export = function (type, name, source) {
  var IS_FORCED = type & $export.F;
  var IS_GLOBAL = type & $export.G;
  var IS_STATIC = type & $export.S;
  var IS_PROTO = type & $export.P;
  var IS_BIND = type & $export.B;
  var IS_WRAP = type & $export.W;
  var exports = IS_GLOBAL ? core : core[name] || (core[name] = {});
  var expProto = exports[PROTOTYPE];
  var target = IS_GLOBAL ? global : IS_STATIC ? global[name] : (global[name] || {})[PROTOTYPE];
  var key, own, out;
  if (IS_GLOBAL) source = name;
  for (key in source) {
    // contains in native
    own = !IS_FORCED && target && target[key] !== undefined;
    if (own && has(exports, key)) continue;
    // export native or passed
    out = own ? target[key] : source[key];
    // prevent global pollution for namespaces
    exports[key] = IS_GLOBAL && typeof target[key] != 'function' ? source[key]
    // bind timers to global for call from export context
    : IS_BIND && own ? ctx(out, global)
    // wrap global constructors for prevent change them in library
    : IS_WRAP && target[key] == out ? (function (C) {
      var F = function (a, b, c) {
        if (this instanceof C) {
          switch (arguments.length) {
            case 0: return new C();
            case 1: return new C(a);
            case 2: return new C(a, b);
          } return new C(a, b, c);
        } return C.apply(this, arguments);
      };
      F[PROTOTYPE] = C[PROTOTYPE];
      return F;
    // make static versions for prototype methods
    })(out) : IS_PROTO && typeof out == 'function' ? ctx(Function.call, out) : out;
    // export proto methods to core.%CONSTRUCTOR%.methods.%NAME%
    if (IS_PROTO) {
      (exports.virtual || (exports.virtual = {}))[key] = out;
      // export proto methods to core.%CONSTRUCTOR%.prototype.%NAME%
      if (type & $export.R && expProto && !expProto[key]) hide(expProto, key, out);
    }
  }
};
// type bitmap
$export.F = 1;   // forced
$export.G = 2;   // global
$export.S = 4;   // static
$export.P = 8;   // proto
$export.B = 16;  // bind
$export.W = 32;  // wrap
$export.U = 64;  // safe
$export.R = 128; // real proto method for `library`
module.exports = $export;


/***/ }),
/* 12 */
/***/ (function(module, exports) {

module.exports = {};


/***/ }),
/* 13 */
/***/ (function(module, exports) {

var toString = {}.toString;

module.exports = function (it) {
  return toString.call(it).slice(8, -1);
};


/***/ }),
/* 14 */
/***/ (function(module, exports, __webpack_require__) {

// optional / simple context binding
var aFunction = __webpack_require__(15);
module.exports = function (fn, that, length) {
  aFunction(fn);
  if (that === undefined) return fn;
  switch (length) {
    case 1: return function (a) {
      return fn.call(that, a);
    };
    case 2: return function (a, b) {
      return fn.call(that, a, b);
    };
    case 3: return function (a, b, c) {
      return fn.call(that, a, b, c);
    };
  }
  return function (/* ...args */) {
    return fn.apply(that, arguments);
  };
};


/***/ }),
/* 15 */
/***/ (function(module, exports) {

module.exports = function (it) {
  if (typeof it != 'function') throw TypeError(it + ' is not a function!');
  return it;
};


/***/ }),
/* 16 */
/***/ (function(module, exports) {

module.exports = function (exec) {
  try {
    return !!exec();
  } catch (e) {
    return true;
  }
};


/***/ }),
/* 17 */
/***/ (function(module, exports) {

module.exports = function (bitmap, value) {
  return {
    enumerable: !(bitmap & 1),
    configurable: !(bitmap & 2),
    writable: !(bitmap & 4),
    value: value
  };
};


/***/ }),
/* 18 */
/***/ (function(module, exports) {

var id = 0;
var px = Math.random();
module.exports = function (key) {
  return 'Symbol('.concat(key === undefined ? '' : key, ')_', (++id + px).toString(36));
};


/***/ }),
/* 19 */
/***/ (function(module, exports, __webpack_require__) {

var def = __webpack_require__(5).f;
var has = __webpack_require__(8);
var TAG = __webpack_require__(1)('toStringTag');

module.exports = function (it, tag, stat) {
  if (it && !has(it = stat ? it : it.prototype, TAG)) def(it, TAG, { configurable: true, value: tag });
};


/***/ }),
/* 20 */
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
    value: true
});

var _promise = __webpack_require__(54);

var _promise2 = _interopRequireDefault(_promise);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

var commonUtil = {
    asyncLoadedScripts: {},
    asyncLoadedScriptsCallbackQueue: {},
    getScriptDomFromUrl: function getScriptDomFromUrl(url) {
        var dom;
        if (/.+\.js$/.test(url)) {
            dom = document.createElement("SCRIPT");
            dom.setAttribute("type", "text/javascript");
            dom.setAttribute("src", url);
        } else if (/.+\.css$/.test(url)) {
            dom = document.createElement('link');
            dom.href = url;
            dom.type = "text/css";
            dom.rel = "stylesheet";
        }
        return dom;
    },
    /**
     * 异步加载script或css
     */
    asyncLoadScript: function asyncLoadScript(url, callback) {
        var $this = commonUtil;
        if ($this.asyncLoadedScripts[url] != undefined) //已加载script标签
            {
                if (callback && typeof callback == "function") {
                    if ($this.asyncLoadedScripts[url] == 0) //未执行首个script标签的回调
                        {
                            if (!$this.asyncLoadedScriptsCallbackQueue[url]) {
                                $this.asyncLoadedScriptsCallbackQueue[url] = [];
                            }
                            $this.asyncLoadedScriptsCallbackQueue[url].push(callback);
                        } else {
                        callback.apply($this, []);
                    }
                }
                return;
            }
        $this.asyncLoadedScripts[url] = 0;
        var scriptDom = $this.getScriptDomFromUrl(url);
        if (scriptDom.readyState) {
            scriptDom.onreadystatechange = function () {
                if (scriptDom.readyState == "loaded" || scriptDom.readyState == "complete") {
                    scriptDom.onreadystatechange = null;
                    $this.asyncLoadedScripts[url] = 1;
                    if (callback && typeof callback == "function") {
                        callback.apply($this, []);
                    }
                    if ($this.asyncLoadedScriptsCallbackQueue[url]) {
                        for (var i = 0, j = $this.asyncLoadedScriptsCallbackQueue[url].length; i < j; i++) {
                            $this.asyncLoadedScriptsCallbackQueue[url][i].apply($this, []);
                        }
                        $this.asyncLoadedScriptsCallbackQueue[url] = undefined;
                    }
                }
            };
        } else {
            scriptDom.onload = function () {
                $this.asyncLoadedScripts[url] = 1;
                if (callback && typeof callback == "function") {
                    callback.apply($this, []);
                }
                if ($this.asyncLoadedScriptsCallbackQueue[url]) {
                    for (var i = 0, j = $this.asyncLoadedScriptsCallbackQueue[url].length; i < j; i++) {
                        $this.asyncLoadedScriptsCallbackQueue[url][i].apply($this, []);
                    }
                    $this.asyncLoadedScriptsCallbackQueue[url] = undefined;
                }
            };
        }
        document.getElementsByTagName('head')[0].appendChild(scriptDom);
    },
    getFileNameFromUrl: function getFileNameFromUrl(url) {
        return url.substring(url.lastIndexOf("/") + 1, url.length);
    },
    isIncludeScript: function isIncludeScript(name) {
        var js = /js$/i.test(name);
        var es = document.getElementsByTagName(js ? 'script' : 'link');
        for (var i = 0; i < es.length; i++) {
            if (es[i][js ? 'src' : 'href'].indexOf(name) != -1) return true;
        }return false;
    },
    loadScripts: function loadScripts(scriptArr) {
        if (scriptArr instanceof Array) {
            var promises = [];
            for (var i = 0; i < scriptArr.length; i++) {
                promises.push(new _promise2.default(function (resolve, reject) {
                    if (commonUtil.isIncludeScript(commonUtil.getFileNameFromUrl(scriptArr[i]))) {
                        resolve();
                    } else {
                        commonUtil.asyncLoadScript(scriptArr[i], function () {
                            resolve();
                        });
                    }
                }));
            }
            return _promise2.default.all(promises);
        } else {
            return new _promise2.default(function (resolve, reject) {
                resolve();
            });
        }
    },
    getExtensions: function getExtensions(s) {
        if (!s) {
            return '';
        }
        for (var i = s.length; i != 0; i--) {
            if (s[i] == '.') {
                return s.substring(i, s.length);
            }
        }
        return '';
    },
    getExtension: function getExtension(s) {
        var e = commonUtil.getExtensions(s);
        if (e.length > 0) {
            return e.substring(1, e.length).toLowerCase();
        }
        return '';
    },
    getFileName: function getFileName(s) {
        var e = s.length;
        for (var i = s.length; i != 0; i--) {
            if (s[i] == '.' && e == s.length) {
                e = i;
                continue;
            }
            if (s[i] == '\\') {
                // 此处需要考虑路径的多种分隔符，暂时就写一种
                return s.substring(i + 1, e);
            }
        }
        return s.substring(0, e);
    },
    getFullFileName: function getFullFileName(s) {
        for (var i = s.length; i != 0; i--) {
            if (s[i] == '\\') {
                // 此处需要考虑路径的多种分隔符，暂时就写一种
                return s.substring(i + 1, s.length);
            }
        }
        return s;
    },
    getTypeByExt: function getTypeByExt(ext, config) {
        ext = ext.toLowerCase();
        if (ext.indexOf(".") !== 0) {
            ext = "." + ext;
        }
        var types = _.get(window, 'nxt.config.entityTypes', []);
        if (config && types.length === 0) {
            types = _.get(config, 'entityTypes', []);
        }
        for (var i = 0; i < types.length; i++) {
            if (types[i].extensions && types[i].extensions.indexOf(ext) != -1) {
                return types[i];
            }
        }
        return _.find(types, { 'code': 'other' });
    },
    formatSize: function formatSize(size, pointLength, units) {
        var unit;
        units = units || ['B', 'KB', 'MB', 'GB', 'TB'];
        while ((unit = units.shift()) && size > 1024) {
            size = size / 1024;
        }
        return (unit === 'B' ? size : size.toFixed(pointLength || 2)) + ' ' + unit;
    },
    prompt: function prompt(msg) {
        if (mam && mam.prompt) {
            mam.prompt(msg);
        }
    },
    msgOk: function msgOk(msg) {
        if (mam && mam.message && mam.message.ok) {
            mam.message.ok(msg);
        }
    },
    addUrlParam: function addUrlParam(url, param) {
        url += (url.indexOf('?') !== -1 ? '&' : '?') + param;
        return url;
    },
    getCookie: function getCookie(cname) {
        var name = cname + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i].trim();
            if (c.indexOf(name) == 0) return c.substring(name.length, c.length);
        }
        return "";
    }
};
exports.default = commonUtil;

/***/ }),
/* 21 */
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var globalUtil = {};
exports.default = globalUtil;

/***/ }),
/* 22 */
/***/ (function(module, exports, __webpack_require__) {

module.exports = { "default": __webpack_require__(80), __esModule: true };

/***/ }),
/* 23 */
/***/ (function(module, exports) {

// 7.1.4 ToInteger
var ceil = Math.ceil;
var floor = Math.floor;
module.exports = function (it) {
  return isNaN(it = +it) ? 0 : (it > 0 ? floor : ceil)(it);
};


/***/ }),
/* 24 */
/***/ (function(module, exports) {

// 7.2.1 RequireObjectCoercible(argument)
module.exports = function (it) {
  if (it == undefined) throw TypeError("Can't call method on  " + it);
  return it;
};


/***/ }),
/* 25 */
/***/ (function(module, exports, __webpack_require__) {

var isObject = __webpack_require__(6);
var document = __webpack_require__(0).document;
// typeof document.createElement is 'object' in old IE
var is = isObject(document) && isObject(document.createElement);
module.exports = function (it) {
  return is ? document.createElement(it) : {};
};


/***/ }),
/* 26 */
/***/ (function(module, exports, __webpack_require__) {

// 7.1.1 ToPrimitive(input [, PreferredType])
var isObject = __webpack_require__(6);
// instead of the ES6 spec version, we didn't implement @@toPrimitive case
// and the second argument - flag - preferred type is a string
module.exports = function (it, S) {
  if (!isObject(it)) return it;
  var fn, val;
  if (S && typeof (fn = it.toString) == 'function' && !isObject(val = fn.call(it))) return val;
  if (typeof (fn = it.valueOf) == 'function' && !isObject(val = fn.call(it))) return val;
  if (!S && typeof (fn = it.toString) == 'function' && !isObject(val = fn.call(it))) return val;
  throw TypeError("Can't convert object to primitive value");
};


/***/ }),
/* 27 */
/***/ (function(module, exports, __webpack_require__) {

// 19.1.2.14 / 15.2.3.14 Object.keys(O)
var $keys = __webpack_require__(42);
var enumBugKeys = __webpack_require__(30);

module.exports = Object.keys || function keys(O) {
  return $keys(O, enumBugKeys);
};


/***/ }),
/* 28 */
/***/ (function(module, exports, __webpack_require__) {

var shared = __webpack_require__(29)('keys');
var uid = __webpack_require__(18);
module.exports = function (key) {
  return shared[key] || (shared[key] = uid(key));
};


/***/ }),
/* 29 */
/***/ (function(module, exports, __webpack_require__) {

var core = __webpack_require__(2);
var global = __webpack_require__(0);
var SHARED = '__core-js_shared__';
var store = global[SHARED] || (global[SHARED] = {});

(module.exports = function (key, value) {
  return store[key] || (store[key] = value !== undefined ? value : {});
})('versions', []).push({
  version: core.version,
  mode: __webpack_require__(10) ? 'pure' : 'global',
  copyright: '© 2020 Denis Pushkarev (zloirock.ru)'
});


/***/ }),
/* 30 */
/***/ (function(module, exports) {

// IE 8- don't enum bug keys
module.exports = (
  'constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf'
).split(',');


/***/ }),
/* 31 */
/***/ (function(module, exports, __webpack_require__) {

"use strict";

// 25.4.1.5 NewPromiseCapability(C)
var aFunction = __webpack_require__(15);

function PromiseCapability(C) {
  var resolve, reject;
  this.promise = new C(function ($$resolve, $$reject) {
    if (resolve !== undefined || reject !== undefined) throw TypeError('Bad Promise constructor');
    resolve = $$resolve;
    reject = $$reject;
  });
  this.resolve = aFunction(resolve);
  this.reject = aFunction(reject);
}

module.exports.f = function (C) {
  return new PromiseCapability(C);
};


/***/ }),
/* 32 */
/***/ (function(module, exports, __webpack_require__) {

exports.f = __webpack_require__(1);


/***/ }),
/* 33 */
/***/ (function(module, exports, __webpack_require__) {

var global = __webpack_require__(0);
var core = __webpack_require__(2);
var LIBRARY = __webpack_require__(10);
var wksExt = __webpack_require__(32);
var defineProperty = __webpack_require__(5).f;
module.exports = function (name) {
  var $Symbol = core.Symbol || (core.Symbol = LIBRARY ? {} : global.Symbol || {});
  if (name.charAt(0) != '_' && !(name in $Symbol)) defineProperty($Symbol, name, { value: wksExt.f(name) });
};


/***/ }),
/* 34 */
/***/ (function(module, exports) {

exports.f = {}.propertyIsEnumerable;


/***/ }),
/* 35 */
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
    value: true
});

var _stringify = __webpack_require__(22);

var _stringify2 = _interopRequireDefault(_stringify);

var _commonUtil = __webpack_require__(20);

var _commonUtil2 = _interopRequireDefault(_commonUtil);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

var getQueryParam = function getQueryParam(url, key) {
    key = key.toLowerCase().replace(/[\[\]]/g, '\\$&');
    var regex = new RegExp('[?&]' + key + '(=([^&#]*)|&|#|$)'),
        results = regex.exec(url.toLowerCase());
    if (!results) return null;
    if (!results[2]) return '';
    return decodeURIComponent(results[2].replace(/\+/g, ' '));
}; /**
    * Created by heju on 2017/5/31.
    */

var getUrlQueryParam = function getUrlQueryParam(key) {
    return getQueryParam(window.location.href, key);
};
var addUrlParam = function addUrlParam(url, param) {
    url += (url.indexOf('?') !== -1 ? '&' : '?') + param;
    return url;
};

var httpUtil = {
    getGetParamStr: function getGetParamStr(param, restful) {
        var str = "";
        if (param != undefined && !$.isEmptyObject(param)) {
            for (var key in param) {
                if (!restful) {
                    str += key + "=" + param[key] + "&";
                } else {
                    str += "/" + param[key];
                }
            }
            if (!restful) {
                str = "?" + str.substring(0, str.length - 1);
            }
        }
        return str;
    },
    get: function get(url, param) {
        var defer = $.Deferred();
        var purl = url;
        if (!param) {
            param = {};
        }
        purl = url + httpUtil.getGetParamStr(param);
        var token = getUrlQueryParam('token');
        if (token) {
            purl = addUrlParam(purl, 'token=' + token);
        }
        $.ajax({
            type: "get",
            xhrFields: {
                withCredentials: url.indexOf('token=') > -1 ? false : true //如果传了token，就不用传cookie了
            },
            url: purl
        }).then(function (res) {
            if (!res.success) {
                console.error('response', res);
                _commonUtil2.default.prompt(l('system.' + res.data.code, res.data.title));
                defer.reject(res);
            } else {
                defer.resolve(res);
            }
        }, function (res) {
            defer.reject(res);
        });
        return defer;
    },
    post: function post(url, param, opts) {
        var defer = $.Deferred();
        if (!param) {
            param = {};
        }
        var token = getUrlQueryParam('token');
        if (token) {
            url = addUrlParam(url, 'token=' + token);
        }
        var postConfig = {
            type: "post",
            data: param,
            contentType: 'application/json',
            processData: false,
            xhrFields: {
                withCredentials: url.indexOf('token=') > -1 ? false : true //如果传了token，就不用传cookie了
            },
            url: url
        };
        if (opts && opts.contentType !== undefined) {
            postConfig.contentType = opts.contentType;
        }
        if (opts && opts.processData !== undefined) {
            postConfig.processData = opts.processData;
        }
        if (postConfig.contentType === 'application/json') {
            postConfig.data = (0, _stringify2.default)(param);
        }
        $.ajax(postConfig).then(function (res) {
            if (!res.success) {
                console.error('response', res);
                _commonUtil2.default.prompt(l('system.' + res.error.code, res.error.title));
                defer.reject(res);
            } else {
                defer.resolve(res);
            }
        }, function (res) {
            defer.reject(res);
        });
        return defer;
    },
    delete: function _delete(url, param, opts) {
        var defer = $.Deferred();
        if (!param) {
            param = {};
        }
        var token = getUrlQueryParam('token');
        if (token) {
            url = addUrlParam(url, 'token=' + token);
        }
        var postConfig = {
            type: "delete",
            data: param,
            contentType: 'application/json',
            processData: false,
            xhrFields: {
                withCredentials: url.indexOf('token=') > -1 ? false : true //如果传了token，就不用传cookie了
            },
            url: url
        };
        if (opts && opts.contentType !== undefined) {
            postConfig.contentType = opts.contentType;
        }
        if (opts && opts.processData !== undefined) {
            postConfig.processData = opts.processData;
        }
        if (postConfig.contentType === 'application/json') {
            postConfig.data = (0, _stringify2.default)(param);
        }
        $.ajax(postConfig).then(function (res) {
            if (!res.success) {
                console.error('response', res);
                _commonUtil2.default.prompt(l('system.' + res.error.code, res.error.title));
                defer.reject(res);
            } else {
                defer.resolve(res);
            }
        }, function (res) {
            defer.reject(res);
        });
        return defer;
    }
};
exports.default = httpUtil;

/***/ }),
/* 36 */
/***/ (function(module, exports) {



/***/ }),
/* 37 */
/***/ (function(module, exports, __webpack_require__) {

"use strict";

var $at = __webpack_require__(56)(true);

// 21.1.3.27 String.prototype[@@iterator]()
__webpack_require__(38)(String, 'String', function (iterated) {
  this._t = String(iterated); // target
  this._i = 0;                // next index
// 21.1.5.2.1 %StringIteratorPrototype%.next()
}, function () {
  var O = this._t;
  var index = this._i;
  var point;
  if (index >= O.length) return { value: undefined, done: true };
  point = $at(O, index);
  this._i += point.length;
  return { value: point, done: false };
});


/***/ }),
/* 38 */
/***/ (function(module, exports, __webpack_require__) {

"use strict";

var LIBRARY = __webpack_require__(10);
var $export = __webpack_require__(11);
var redefine = __webpack_require__(40);
var hide = __webpack_require__(4);
var Iterators = __webpack_require__(12);
var $iterCreate = __webpack_require__(57);
var setToStringTag = __webpack_require__(19);
var getPrototypeOf = __webpack_require__(62);
var ITERATOR = __webpack_require__(1)('iterator');
var BUGGY = !([].keys && 'next' in [].keys()); // Safari has buggy iterators w/o `next`
var FF_ITERATOR = '@@iterator';
var KEYS = 'keys';
var VALUES = 'values';

var returnThis = function () { return this; };

module.exports = function (Base, NAME, Constructor, next, DEFAULT, IS_SET, FORCED) {
  $iterCreate(Constructor, NAME, next);
  var getMethod = function (kind) {
    if (!BUGGY && kind in proto) return proto[kind];
    switch (kind) {
      case KEYS: return function keys() { return new Constructor(this, kind); };
      case VALUES: return function values() { return new Constructor(this, kind); };
    } return function entries() { return new Constructor(this, kind); };
  };
  var TAG = NAME + ' Iterator';
  var DEF_VALUES = DEFAULT == VALUES;
  var VALUES_BUG = false;
  var proto = Base.prototype;
  var $native = proto[ITERATOR] || proto[FF_ITERATOR] || DEFAULT && proto[DEFAULT];
  var $default = $native || getMethod(DEFAULT);
  var $entries = DEFAULT ? !DEF_VALUES ? $default : getMethod('entries') : undefined;
  var $anyNative = NAME == 'Array' ? proto.entries || $native : $native;
  var methods, key, IteratorPrototype;
  // Fix native
  if ($anyNative) {
    IteratorPrototype = getPrototypeOf($anyNative.call(new Base()));
    if (IteratorPrototype !== Object.prototype && IteratorPrototype.next) {
      // Set @@toStringTag to native iterators
      setToStringTag(IteratorPrototype, TAG, true);
      // fix for some old engines
      if (!LIBRARY && typeof IteratorPrototype[ITERATOR] != 'function') hide(IteratorPrototype, ITERATOR, returnThis);
    }
  }
  // fix Array#{values, @@iterator}.name in V8 / FF
  if (DEF_VALUES && $native && $native.name !== VALUES) {
    VALUES_BUG = true;
    $default = function values() { return $native.call(this); };
  }
  // Define iterator
  if ((!LIBRARY || FORCED) && (BUGGY || VALUES_BUG || !proto[ITERATOR])) {
    hide(proto, ITERATOR, $default);
  }
  // Plug for library
  Iterators[NAME] = $default;
  Iterators[TAG] = returnThis;
  if (DEFAULT) {
    methods = {
      values: DEF_VALUES ? $default : getMethod(VALUES),
      keys: IS_SET ? $default : getMethod(KEYS),
      entries: $entries
    };
    if (FORCED) for (key in methods) {
      if (!(key in proto)) redefine(proto, key, methods[key]);
    } else $export($export.P + $export.F * (BUGGY || VALUES_BUG), NAME, methods);
  }
  return methods;
};


/***/ }),
/* 39 */
/***/ (function(module, exports, __webpack_require__) {

module.exports = !__webpack_require__(7) && !__webpack_require__(16)(function () {
  return Object.defineProperty(__webpack_require__(25)('div'), 'a', { get: function () { return 7; } }).a != 7;
});


/***/ }),
/* 40 */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__(4);


/***/ }),
/* 41 */
/***/ (function(module, exports, __webpack_require__) {

// ******** / 15.2.3.5 Object.create(O [, Properties])
var anObject = __webpack_require__(3);
var dPs = __webpack_require__(58);
var enumBugKeys = __webpack_require__(30);
var IE_PROTO = __webpack_require__(28)('IE_PROTO');
var Empty = function () { /* empty */ };
var PROTOTYPE = 'prototype';

// Create object with fake `null` prototype: use iframe Object with cleared prototype
var createDict = function () {
  // Thrash, waste and sodomy: IE GC bug
  var iframe = __webpack_require__(25)('iframe');
  var i = enumBugKeys.length;
  var lt = '<';
  var gt = '>';
  var iframeDocument;
  iframe.style.display = 'none';
  __webpack_require__(44).appendChild(iframe);
  iframe.src = 'javascript:'; // eslint-disable-line no-script-url
  // createDict = iframe.contentWindow.Object;
  // html.removeChild(iframe);
  iframeDocument = iframe.contentWindow.document;
  iframeDocument.open();
  iframeDocument.write(lt + 'script' + gt + 'document.F=Object' + lt + '/script' + gt);
  iframeDocument.close();
  createDict = iframeDocument.F;
  while (i--) delete createDict[PROTOTYPE][enumBugKeys[i]];
  return createDict();
};

module.exports = Object.create || function create(O, Properties) {
  var result;
  if (O !== null) {
    Empty[PROTOTYPE] = anObject(O);
    result = new Empty();
    Empty[PROTOTYPE] = null;
    // add "__proto__" for Object.getPrototypeOf polyfill
    result[IE_PROTO] = O;
  } else result = createDict();
  return Properties === undefined ? result : dPs(result, Properties);
};


/***/ }),
/* 42 */
/***/ (function(module, exports, __webpack_require__) {

var has = __webpack_require__(8);
var toIObject = __webpack_require__(9);
var arrayIndexOf = __webpack_require__(60)(false);
var IE_PROTO = __webpack_require__(28)('IE_PROTO');

module.exports = function (object, names) {
  var O = toIObject(object);
  var i = 0;
  var result = [];
  var key;
  for (key in O) if (key != IE_PROTO) has(O, key) && result.push(key);
  // Don't enum bug & hidden keys
  while (names.length > i) if (has(O, key = names[i++])) {
    ~arrayIndexOf(result, key) || result.push(key);
  }
  return result;
};


/***/ }),
/* 43 */
/***/ (function(module, exports, __webpack_require__) {

// 7.1.15 ToLength
var toInteger = __webpack_require__(23);
var min = Math.min;
module.exports = function (it) {
  return it > 0 ? min(toInteger(it), 0x1fffffffffffff) : 0; // pow(2, 53) - 1 == 9007199254740991
};


/***/ }),
/* 44 */
/***/ (function(module, exports, __webpack_require__) {

var document = __webpack_require__(0).document;
module.exports = document && document.documentElement;


/***/ }),
/* 45 */
/***/ (function(module, exports, __webpack_require__) {

// 7.1.13 ToObject(argument)
var defined = __webpack_require__(24);
module.exports = function (it) {
  return Object(defined(it));
};


/***/ }),
/* 46 */
/***/ (function(module, exports, __webpack_require__) {

__webpack_require__(63);
var global = __webpack_require__(0);
var hide = __webpack_require__(4);
var Iterators = __webpack_require__(12);
var TO_STRING_TAG = __webpack_require__(1)('toStringTag');

var DOMIterables = ('CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,' +
  'DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,' +
  'MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,' +
  'SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,' +
  'TextTrackList,TouchList').split(',');

for (var i = 0; i < DOMIterables.length; i++) {
  var NAME = DOMIterables[i];
  var Collection = global[NAME];
  var proto = Collection && Collection.prototype;
  if (proto && !proto[TO_STRING_TAG]) hide(proto, TO_STRING_TAG, NAME);
  Iterators[NAME] = Iterators.Array;
}


/***/ }),
/* 47 */
/***/ (function(module, exports, __webpack_require__) {

// getting tag from ******** Object.prototype.toString()
var cof = __webpack_require__(13);
var TAG = __webpack_require__(1)('toStringTag');
// ES3 wrong here
var ARG = cof(function () { return arguments; }()) == 'Arguments';

// fallback for IE11 Script Access Denied error
var tryGet = function (it, key) {
  try {
    return it[key];
  } catch (e) { /* empty */ }
};

module.exports = function (it) {
  var O, T, B;
  return it === undefined ? 'Undefined' : it === null ? 'Null'
    // @@toStringTag case
    : typeof (T = tryGet(O = Object(it), TAG)) == 'string' ? T
    // builtinTag case
    : ARG ? cof(O)
    // ES3 arguments fallback
    : (B = cof(O)) == 'Object' && typeof O.callee == 'function' ? 'Arguments' : B;
};


/***/ }),
/* 48 */
/***/ (function(module, exports, __webpack_require__) {

// 7.3.20 SpeciesConstructor(O, defaultConstructor)
var anObject = __webpack_require__(3);
var aFunction = __webpack_require__(15);
var SPECIES = __webpack_require__(1)('species');
module.exports = function (O, D) {
  var C = anObject(O).constructor;
  var S;
  return C === undefined || (S = anObject(C)[SPECIES]) == undefined ? D : aFunction(S);
};


/***/ }),
/* 49 */
/***/ (function(module, exports, __webpack_require__) {

var ctx = __webpack_require__(14);
var invoke = __webpack_require__(72);
var html = __webpack_require__(44);
var cel = __webpack_require__(25);
var global = __webpack_require__(0);
var process = global.process;
var setTask = global.setImmediate;
var clearTask = global.clearImmediate;
var MessageChannel = global.MessageChannel;
var Dispatch = global.Dispatch;
var counter = 0;
var queue = {};
var ONREADYSTATECHANGE = 'onreadystatechange';
var defer, channel, port;
var run = function () {
  var id = +this;
  // eslint-disable-next-line no-prototype-builtins
  if (queue.hasOwnProperty(id)) {
    var fn = queue[id];
    delete queue[id];
    fn();
  }
};
var listener = function (event) {
  run.call(event.data);
};
// Node.js 0.9+ & IE10+ has setImmediate, otherwise:
if (!setTask || !clearTask) {
  setTask = function setImmediate(fn) {
    var args = [];
    var i = 1;
    while (arguments.length > i) args.push(arguments[i++]);
    queue[++counter] = function () {
      // eslint-disable-next-line no-new-func
      invoke(typeof fn == 'function' ? fn : Function(fn), args);
    };
    defer(counter);
    return counter;
  };
  clearTask = function clearImmediate(id) {
    delete queue[id];
  };
  // Node.js 0.8-
  if (__webpack_require__(13)(process) == 'process') {
    defer = function (id) {
      process.nextTick(ctx(run, id, 1));
    };
  // Sphere (JS game engine) Dispatch API
  } else if (Dispatch && Dispatch.now) {
    defer = function (id) {
      Dispatch.now(ctx(run, id, 1));
    };
  // Browsers with MessageChannel, includes WebWorkers
  } else if (MessageChannel) {
    channel = new MessageChannel();
    port = channel.port2;
    channel.port1.onmessage = listener;
    defer = ctx(port.postMessage, port, 1);
  // Browsers with postMessage, skip WebWorkers
  // IE8 has postMessage, but it's sync & typeof its postMessage is 'object'
  } else if (global.addEventListener && typeof postMessage == 'function' && !global.importScripts) {
    defer = function (id) {
      global.postMessage(id + '', '*');
    };
    global.addEventListener('message', listener, false);
  // IE8-
  } else if (ONREADYSTATECHANGE in cel('script')) {
    defer = function (id) {
      html.appendChild(cel('script'))[ONREADYSTATECHANGE] = function () {
        html.removeChild(this);
        run.call(id);
      };
    };
  // Rest old browsers
  } else {
    defer = function (id) {
      setTimeout(ctx(run, id, 1), 0);
    };
  }
}
module.exports = {
  set: setTask,
  clear: clearTask
};


/***/ }),
/* 50 */
/***/ (function(module, exports) {

module.exports = function (exec) {
  try {
    return { e: false, v: exec() };
  } catch (e) {
    return { e: true, v: e };
  }
};


/***/ }),
/* 51 */
/***/ (function(module, exports, __webpack_require__) {

var anObject = __webpack_require__(3);
var isObject = __webpack_require__(6);
var newPromiseCapability = __webpack_require__(31);

module.exports = function (C, x) {
  anObject(C);
  if (isObject(x) && x.constructor === C) return x;
  var promiseCapability = newPromiseCapability.f(C);
  var resolve = promiseCapability.resolve;
  resolve(x);
  return promiseCapability.promise;
};


/***/ }),
/* 52 */
/***/ (function(module, exports) {

exports.f = Object.getOwnPropertySymbols;


/***/ }),
/* 53 */
/***/ (function(module, exports, __webpack_require__) {

// ******** / 15.2.3.4 Object.getOwnPropertyNames(O)
var $keys = __webpack_require__(42);
var hiddenKeys = __webpack_require__(30).concat('length', 'prototype');

exports.f = Object.getOwnPropertyNames || function getOwnPropertyNames(O) {
  return $keys(O, hiddenKeys);
};


/***/ }),
/* 54 */
/***/ (function(module, exports, __webpack_require__) {

module.exports = { "default": __webpack_require__(55), __esModule: true };

/***/ }),
/* 55 */
/***/ (function(module, exports, __webpack_require__) {

__webpack_require__(36);
__webpack_require__(37);
__webpack_require__(46);
__webpack_require__(66);
__webpack_require__(78);
__webpack_require__(79);
module.exports = __webpack_require__(2).Promise;


/***/ }),
/* 56 */
/***/ (function(module, exports, __webpack_require__) {

var toInteger = __webpack_require__(23);
var defined = __webpack_require__(24);
// true  -> String#at
// false -> String#codePointAt
module.exports = function (TO_STRING) {
  return function (that, pos) {
    var s = String(defined(that));
    var i = toInteger(pos);
    var l = s.length;
    var a, b;
    if (i < 0 || i >= l) return TO_STRING ? '' : undefined;
    a = s.charCodeAt(i);
    return a < 0xd800 || a > 0xdbff || i + 1 === l || (b = s.charCodeAt(i + 1)) < 0xdc00 || b > 0xdfff
      ? TO_STRING ? s.charAt(i) : a
      : TO_STRING ? s.slice(i, i + 2) : (a - 0xd800 << 10) + (b - 0xdc00) + 0x10000;
  };
};


/***/ }),
/* 57 */
/***/ (function(module, exports, __webpack_require__) {

"use strict";

var create = __webpack_require__(41);
var descriptor = __webpack_require__(17);
var setToStringTag = __webpack_require__(19);
var IteratorPrototype = {};

// ********.1 %IteratorPrototype%[@@iterator]()
__webpack_require__(4)(IteratorPrototype, __webpack_require__(1)('iterator'), function () { return this; });

module.exports = function (Constructor, NAME, next) {
  Constructor.prototype = create(IteratorPrototype, { next: descriptor(1, next) });
  setToStringTag(Constructor, NAME + ' Iterator');
};


/***/ }),
/* 58 */
/***/ (function(module, exports, __webpack_require__) {

var dP = __webpack_require__(5);
var anObject = __webpack_require__(3);
var getKeys = __webpack_require__(27);

module.exports = __webpack_require__(7) ? Object.defineProperties : function defineProperties(O, Properties) {
  anObject(O);
  var keys = getKeys(Properties);
  var length = keys.length;
  var i = 0;
  var P;
  while (length > i) dP.f(O, P = keys[i++], Properties[P]);
  return O;
};


/***/ }),
/* 59 */
/***/ (function(module, exports, __webpack_require__) {

// fallback for non-array-like ES3 and non-enumerable old V8 strings
var cof = __webpack_require__(13);
// eslint-disable-next-line no-prototype-builtins
module.exports = Object('z').propertyIsEnumerable(0) ? Object : function (it) {
  return cof(it) == 'String' ? it.split('') : Object(it);
};


/***/ }),
/* 60 */
/***/ (function(module, exports, __webpack_require__) {

// false -> Array#indexOf
// true  -> Array#includes
var toIObject = __webpack_require__(9);
var toLength = __webpack_require__(43);
var toAbsoluteIndex = __webpack_require__(61);
module.exports = function (IS_INCLUDES) {
  return function ($this, el, fromIndex) {
    var O = toIObject($this);
    var length = toLength(O.length);
    var index = toAbsoluteIndex(fromIndex, length);
    var value;
    // Array#includes uses SameValueZero equality algorithm
    // eslint-disable-next-line no-self-compare
    if (IS_INCLUDES && el != el) while (length > index) {
      value = O[index++];
      // eslint-disable-next-line no-self-compare
      if (value != value) return true;
    // Array#indexOf ignores holes, Array#includes - not
    } else for (;length > index; index++) if (IS_INCLUDES || index in O) {
      if (O[index] === el) return IS_INCLUDES || index || 0;
    } return !IS_INCLUDES && -1;
  };
};


/***/ }),
/* 61 */
/***/ (function(module, exports, __webpack_require__) {

var toInteger = __webpack_require__(23);
var max = Math.max;
var min = Math.min;
module.exports = function (index, length) {
  index = toInteger(index);
  return index < 0 ? max(index + length, 0) : min(index, length);
};


/***/ }),
/* 62 */
/***/ (function(module, exports, __webpack_require__) {

// 19.1.2.9 / 15.2.3.2 Object.getPrototypeOf(O)
var has = __webpack_require__(8);
var toObject = __webpack_require__(45);
var IE_PROTO = __webpack_require__(28)('IE_PROTO');
var ObjectProto = Object.prototype;

module.exports = Object.getPrototypeOf || function (O) {
  O = toObject(O);
  if (has(O, IE_PROTO)) return O[IE_PROTO];
  if (typeof O.constructor == 'function' && O instanceof O.constructor) {
    return O.constructor.prototype;
  } return O instanceof Object ? ObjectProto : null;
};


/***/ }),
/* 63 */
/***/ (function(module, exports, __webpack_require__) {

"use strict";

var addToUnscopables = __webpack_require__(64);
var step = __webpack_require__(65);
var Iterators = __webpack_require__(12);
var toIObject = __webpack_require__(9);

// 22.1.3.4 Array.prototype.entries()
// 22.1.3.13 Array.prototype.keys()
// 22.1.3.29 Array.prototype.values()
// 22.1.3.30 Array.prototype[@@iterator]()
module.exports = __webpack_require__(38)(Array, 'Array', function (iterated, kind) {
  this._t = toIObject(iterated); // target
  this._i = 0;                   // next index
  this._k = kind;                // kind
// 22.1.5.2.1 %ArrayIteratorPrototype%.next()
}, function () {
  var O = this._t;
  var kind = this._k;
  var index = this._i++;
  if (!O || index >= O.length) {
    this._t = undefined;
    return step(1);
  }
  if (kind == 'keys') return step(0, index);
  if (kind == 'values') return step(0, O[index]);
  return step(0, [index, O[index]]);
}, 'values');

// argumentsList[@@iterator] is %ArrayProto_values% (9.4.4.6, 9.4.4.7)
Iterators.Arguments = Iterators.Array;

addToUnscopables('keys');
addToUnscopables('values');
addToUnscopables('entries');


/***/ }),
/* 64 */
/***/ (function(module, exports) {

module.exports = function () { /* empty */ };


/***/ }),
/* 65 */
/***/ (function(module, exports) {

module.exports = function (done, value) {
  return { value: value, done: !!done };
};


/***/ }),
/* 66 */
/***/ (function(module, exports, __webpack_require__) {

"use strict";

var LIBRARY = __webpack_require__(10);
var global = __webpack_require__(0);
var ctx = __webpack_require__(14);
var classof = __webpack_require__(47);
var $export = __webpack_require__(11);
var isObject = __webpack_require__(6);
var aFunction = __webpack_require__(15);
var anInstance = __webpack_require__(67);
var forOf = __webpack_require__(68);
var speciesConstructor = __webpack_require__(48);
var task = __webpack_require__(49).set;
var microtask = __webpack_require__(73)();
var newPromiseCapabilityModule = __webpack_require__(31);
var perform = __webpack_require__(50);
var userAgent = __webpack_require__(74);
var promiseResolve = __webpack_require__(51);
var PROMISE = 'Promise';
var TypeError = global.TypeError;
var process = global.process;
var versions = process && process.versions;
var v8 = versions && versions.v8 || '';
var $Promise = global[PROMISE];
var isNode = classof(process) == 'process';
var empty = function () { /* empty */ };
var Internal, newGenericPromiseCapability, OwnPromiseCapability, Wrapper;
var newPromiseCapability = newGenericPromiseCapability = newPromiseCapabilityModule.f;

var USE_NATIVE = !!function () {
  try {
    // correct subclassing with @@species support
    var promise = $Promise.resolve(1);
    var FakePromise = (promise.constructor = {})[__webpack_require__(1)('species')] = function (exec) {
      exec(empty, empty);
    };
    // unhandled rejections tracking support, NodeJS Promise without it fails @@species test
    return (isNode || typeof PromiseRejectionEvent == 'function')
      && promise.then(empty) instanceof FakePromise
      // v8 6.6 (Node 10 and Chrome 66) have a bug with resolving custom thenables
      // https://bugs.chromium.org/p/chromium/issues/detail?id=830565
      // we can't detect it synchronously, so just check versions
      && v8.indexOf('6.6') !== 0
      && userAgent.indexOf('Chrome/66') === -1;
  } catch (e) { /* empty */ }
}();

// helpers
var isThenable = function (it) {
  var then;
  return isObject(it) && typeof (then = it.then) == 'function' ? then : false;
};
var notify = function (promise, isReject) {
  if (promise._n) return;
  promise._n = true;
  var chain = promise._c;
  microtask(function () {
    var value = promise._v;
    var ok = promise._s == 1;
    var i = 0;
    var run = function (reaction) {
      var handler = ok ? reaction.ok : reaction.fail;
      var resolve = reaction.resolve;
      var reject = reaction.reject;
      var domain = reaction.domain;
      var result, then, exited;
      try {
        if (handler) {
          if (!ok) {
            if (promise._h == 2) onHandleUnhandled(promise);
            promise._h = 1;
          }
          if (handler === true) result = value;
          else {
            if (domain) domain.enter();
            result = handler(value); // may throw
            if (domain) {
              domain.exit();
              exited = true;
            }
          }
          if (result === reaction.promise) {
            reject(TypeError('Promise-chain cycle'));
          } else if (then = isThenable(result)) {
            then.call(result, resolve, reject);
          } else resolve(result);
        } else reject(value);
      } catch (e) {
        if (domain && !exited) domain.exit();
        reject(e);
      }
    };
    while (chain.length > i) run(chain[i++]); // variable length - can't use forEach
    promise._c = [];
    promise._n = false;
    if (isReject && !promise._h) onUnhandled(promise);
  });
};
var onUnhandled = function (promise) {
  task.call(global, function () {
    var value = promise._v;
    var unhandled = isUnhandled(promise);
    var result, handler, console;
    if (unhandled) {
      result = perform(function () {
        if (isNode) {
          process.emit('unhandledRejection', value, promise);
        } else if (handler = global.onunhandledrejection) {
          handler({ promise: promise, reason: value });
        } else if ((console = global.console) && console.error) {
          console.error('Unhandled promise rejection', value);
        }
      });
      // Browsers should not trigger `rejectionHandled` event if it was handled here, NodeJS - should
      promise._h = isNode || isUnhandled(promise) ? 2 : 1;
    } promise._a = undefined;
    if (unhandled && result.e) throw result.v;
  });
};
var isUnhandled = function (promise) {
  return promise._h !== 1 && (promise._a || promise._c).length === 0;
};
var onHandleUnhandled = function (promise) {
  task.call(global, function () {
    var handler;
    if (isNode) {
      process.emit('rejectionHandled', promise);
    } else if (handler = global.onrejectionhandled) {
      handler({ promise: promise, reason: promise._v });
    }
  });
};
var $reject = function (value) {
  var promise = this;
  if (promise._d) return;
  promise._d = true;
  promise = promise._w || promise; // unwrap
  promise._v = value;
  promise._s = 2;
  if (!promise._a) promise._a = promise._c.slice();
  notify(promise, true);
};
var $resolve = function (value) {
  var promise = this;
  var then;
  if (promise._d) return;
  promise._d = true;
  promise = promise._w || promise; // unwrap
  try {
    if (promise === value) throw TypeError("Promise can't be resolved itself");
    if (then = isThenable(value)) {
      microtask(function () {
        var wrapper = { _w: promise, _d: false }; // wrap
        try {
          then.call(value, ctx($resolve, wrapper, 1), ctx($reject, wrapper, 1));
        } catch (e) {
          $reject.call(wrapper, e);
        }
      });
    } else {
      promise._v = value;
      promise._s = 1;
      notify(promise, false);
    }
  } catch (e) {
    $reject.call({ _w: promise, _d: false }, e); // wrap
  }
};

// constructor polyfill
if (!USE_NATIVE) {
  // 25.4.3.1 Promise(executor)
  $Promise = function Promise(executor) {
    anInstance(this, $Promise, PROMISE, '_h');
    aFunction(executor);
    Internal.call(this);
    try {
      executor(ctx($resolve, this, 1), ctx($reject, this, 1));
    } catch (err) {
      $reject.call(this, err);
    }
  };
  // eslint-disable-next-line no-unused-vars
  Internal = function Promise(executor) {
    this._c = [];             // <- awaiting reactions
    this._a = undefined;      // <- checked in isUnhandled reactions
    this._s = 0;              // <- state
    this._d = false;          // <- done
    this._v = undefined;      // <- value
    this._h = 0;              // <- rejection state, 0 - default, 1 - handled, 2 - unhandled
    this._n = false;          // <- notify
  };
  Internal.prototype = __webpack_require__(75)($Promise.prototype, {
    // 25.4.5.3 Promise.prototype.then(onFulfilled, onRejected)
    then: function then(onFulfilled, onRejected) {
      var reaction = newPromiseCapability(speciesConstructor(this, $Promise));
      reaction.ok = typeof onFulfilled == 'function' ? onFulfilled : true;
      reaction.fail = typeof onRejected == 'function' && onRejected;
      reaction.domain = isNode ? process.domain : undefined;
      this._c.push(reaction);
      if (this._a) this._a.push(reaction);
      if (this._s) notify(this, false);
      return reaction.promise;
    },
    // 25.4.5.1 Promise.prototype.catch(onRejected)
    'catch': function (onRejected) {
      return this.then(undefined, onRejected);
    }
  });
  OwnPromiseCapability = function () {
    var promise = new Internal();
    this.promise = promise;
    this.resolve = ctx($resolve, promise, 1);
    this.reject = ctx($reject, promise, 1);
  };
  newPromiseCapabilityModule.f = newPromiseCapability = function (C) {
    return C === $Promise || C === Wrapper
      ? new OwnPromiseCapability(C)
      : newGenericPromiseCapability(C);
  };
}

$export($export.G + $export.W + $export.F * !USE_NATIVE, { Promise: $Promise });
__webpack_require__(19)($Promise, PROMISE);
__webpack_require__(76)(PROMISE);
Wrapper = __webpack_require__(2)[PROMISE];

// statics
$export($export.S + $export.F * !USE_NATIVE, PROMISE, {
  // 25.4.4.5 Promise.reject(r)
  reject: function reject(r) {
    var capability = newPromiseCapability(this);
    var $$reject = capability.reject;
    $$reject(r);
    return capability.promise;
  }
});
$export($export.S + $export.F * (LIBRARY || !USE_NATIVE), PROMISE, {
  // 25.4.4.6 Promise.resolve(x)
  resolve: function resolve(x) {
    return promiseResolve(LIBRARY && this === Wrapper ? $Promise : this, x);
  }
});
$export($export.S + $export.F * !(USE_NATIVE && __webpack_require__(77)(function (iter) {
  $Promise.all(iter)['catch'](empty);
})), PROMISE, {
  // 25.4.4.1 Promise.all(iterable)
  all: function all(iterable) {
    var C = this;
    var capability = newPromiseCapability(C);
    var resolve = capability.resolve;
    var reject = capability.reject;
    var result = perform(function () {
      var values = [];
      var index = 0;
      var remaining = 1;
      forOf(iterable, false, function (promise) {
        var $index = index++;
        var alreadyCalled = false;
        values.push(undefined);
        remaining++;
        C.resolve(promise).then(function (value) {
          if (alreadyCalled) return;
          alreadyCalled = true;
          values[$index] = value;
          --remaining || resolve(values);
        }, reject);
      });
      --remaining || resolve(values);
    });
    if (result.e) reject(result.v);
    return capability.promise;
  },
  // 25.4.4.4 Promise.race(iterable)
  race: function race(iterable) {
    var C = this;
    var capability = newPromiseCapability(C);
    var reject = capability.reject;
    var result = perform(function () {
      forOf(iterable, false, function (promise) {
        C.resolve(promise).then(capability.resolve, reject);
      });
    });
    if (result.e) reject(result.v);
    return capability.promise;
  }
});


/***/ }),
/* 67 */
/***/ (function(module, exports) {

module.exports = function (it, Constructor, name, forbiddenField) {
  if (!(it instanceof Constructor) || (forbiddenField !== undefined && forbiddenField in it)) {
    throw TypeError(name + ': incorrect invocation!');
  } return it;
};


/***/ }),
/* 68 */
/***/ (function(module, exports, __webpack_require__) {

var ctx = __webpack_require__(14);
var call = __webpack_require__(69);
var isArrayIter = __webpack_require__(70);
var anObject = __webpack_require__(3);
var toLength = __webpack_require__(43);
var getIterFn = __webpack_require__(71);
var BREAK = {};
var RETURN = {};
var exports = module.exports = function (iterable, entries, fn, that, ITERATOR) {
  var iterFn = ITERATOR ? function () { return iterable; } : getIterFn(iterable);
  var f = ctx(fn, that, entries ? 2 : 1);
  var index = 0;
  var length, step, iterator, result;
  if (typeof iterFn != 'function') throw TypeError(iterable + ' is not iterable!');
  // fast case for arrays with default iterator
  if (isArrayIter(iterFn)) for (length = toLength(iterable.length); length > index; index++) {
    result = entries ? f(anObject(step = iterable[index])[0], step[1]) : f(iterable[index]);
    if (result === BREAK || result === RETURN) return result;
  } else for (iterator = iterFn.call(iterable); !(step = iterator.next()).done;) {
    result = call(iterator, f, step.value, entries);
    if (result === BREAK || result === RETURN) return result;
  }
};
exports.BREAK = BREAK;
exports.RETURN = RETURN;


/***/ }),
/* 69 */
/***/ (function(module, exports, __webpack_require__) {

// call something on iterator step with safe closing on error
var anObject = __webpack_require__(3);
module.exports = function (iterator, fn, value, entries) {
  try {
    return entries ? fn(anObject(value)[0], value[1]) : fn(value);
  // 7.4.6 IteratorClose(iterator, completion)
  } catch (e) {
    var ret = iterator['return'];
    if (ret !== undefined) anObject(ret.call(iterator));
    throw e;
  }
};


/***/ }),
/* 70 */
/***/ (function(module, exports, __webpack_require__) {

// check on default Array iterator
var Iterators = __webpack_require__(12);
var ITERATOR = __webpack_require__(1)('iterator');
var ArrayProto = Array.prototype;

module.exports = function (it) {
  return it !== undefined && (Iterators.Array === it || ArrayProto[ITERATOR] === it);
};


/***/ }),
/* 71 */
/***/ (function(module, exports, __webpack_require__) {

var classof = __webpack_require__(47);
var ITERATOR = __webpack_require__(1)('iterator');
var Iterators = __webpack_require__(12);
module.exports = __webpack_require__(2).getIteratorMethod = function (it) {
  if (it != undefined) return it[ITERATOR]
    || it['@@iterator']
    || Iterators[classof(it)];
};


/***/ }),
/* 72 */
/***/ (function(module, exports) {

// fast apply, http://jsperf.lnkit.com/fast-apply/5
module.exports = function (fn, args, that) {
  var un = that === undefined;
  switch (args.length) {
    case 0: return un ? fn()
                      : fn.call(that);
    case 1: return un ? fn(args[0])
                      : fn.call(that, args[0]);
    case 2: return un ? fn(args[0], args[1])
                      : fn.call(that, args[0], args[1]);
    case 3: return un ? fn(args[0], args[1], args[2])
                      : fn.call(that, args[0], args[1], args[2]);
    case 4: return un ? fn(args[0], args[1], args[2], args[3])
                      : fn.call(that, args[0], args[1], args[2], args[3]);
  } return fn.apply(that, args);
};


/***/ }),
/* 73 */
/***/ (function(module, exports, __webpack_require__) {

var global = __webpack_require__(0);
var macrotask = __webpack_require__(49).set;
var Observer = global.MutationObserver || global.WebKitMutationObserver;
var process = global.process;
var Promise = global.Promise;
var isNode = __webpack_require__(13)(process) == 'process';

module.exports = function () {
  var head, last, notify;

  var flush = function () {
    var parent, fn;
    if (isNode && (parent = process.domain)) parent.exit();
    while (head) {
      fn = head.fn;
      head = head.next;
      try {
        fn();
      } catch (e) {
        if (head) notify();
        else last = undefined;
        throw e;
      }
    } last = undefined;
    if (parent) parent.enter();
  };

  // Node.js
  if (isNode) {
    notify = function () {
      process.nextTick(flush);
    };
  // browsers with MutationObserver, except iOS Safari - https://github.com/zloirock/core-js/issues/339
  } else if (Observer && !(global.navigator && global.navigator.standalone)) {
    var toggle = true;
    var node = document.createTextNode('');
    new Observer(flush).observe(node, { characterData: true }); // eslint-disable-line no-new
    notify = function () {
      node.data = toggle = !toggle;
    };
  // environments with maybe non-completely correct, but existent Promise
  } else if (Promise && Promise.resolve) {
    // Promise.resolve without an argument throws an error in LG WebOS 2
    var promise = Promise.resolve(undefined);
    notify = function () {
      promise.then(flush);
    };
  // for other environments - macrotask based on:
  // - setImmediate
  // - MessageChannel
  // - window.postMessag
  // - onreadystatechange
  // - setTimeout
  } else {
    notify = function () {
      // strange IE + webpack dev server bug - use .call(global)
      macrotask.call(global, flush);
    };
  }

  return function (fn) {
    var task = { fn: fn, next: undefined };
    if (last) last.next = task;
    if (!head) {
      head = task;
      notify();
    } last = task;
  };
};


/***/ }),
/* 74 */
/***/ (function(module, exports, __webpack_require__) {

var global = __webpack_require__(0);
var navigator = global.navigator;

module.exports = navigator && navigator.userAgent || '';


/***/ }),
/* 75 */
/***/ (function(module, exports, __webpack_require__) {

var hide = __webpack_require__(4);
module.exports = function (target, src, safe) {
  for (var key in src) {
    if (safe && target[key]) target[key] = src[key];
    else hide(target, key, src[key]);
  } return target;
};


/***/ }),
/* 76 */
/***/ (function(module, exports, __webpack_require__) {

"use strict";

var global = __webpack_require__(0);
var core = __webpack_require__(2);
var dP = __webpack_require__(5);
var DESCRIPTORS = __webpack_require__(7);
var SPECIES = __webpack_require__(1)('species');

module.exports = function (KEY) {
  var C = typeof core[KEY] == 'function' ? core[KEY] : global[KEY];
  if (DESCRIPTORS && C && !C[SPECIES]) dP.f(C, SPECIES, {
    configurable: true,
    get: function () { return this; }
  });
};


/***/ }),
/* 77 */
/***/ (function(module, exports, __webpack_require__) {

var ITERATOR = __webpack_require__(1)('iterator');
var SAFE_CLOSING = false;

try {
  var riter = [7][ITERATOR]();
  riter['return'] = function () { SAFE_CLOSING = true; };
  // eslint-disable-next-line no-throw-literal
  Array.from(riter, function () { throw 2; });
} catch (e) { /* empty */ }

module.exports = function (exec, skipClosing) {
  if (!skipClosing && !SAFE_CLOSING) return false;
  var safe = false;
  try {
    var arr = [7];
    var iter = arr[ITERATOR]();
    iter.next = function () { return { done: safe = true }; };
    arr[ITERATOR] = function () { return iter; };
    exec(arr);
  } catch (e) { /* empty */ }
  return safe;
};


/***/ }),
/* 78 */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
// https://github.com/tc39/proposal-promise-finally

var $export = __webpack_require__(11);
var core = __webpack_require__(2);
var global = __webpack_require__(0);
var speciesConstructor = __webpack_require__(48);
var promiseResolve = __webpack_require__(51);

$export($export.P + $export.R, 'Promise', { 'finally': function (onFinally) {
  var C = speciesConstructor(this, core.Promise || global.Promise);
  var isFunction = typeof onFinally == 'function';
  return this.then(
    isFunction ? function (x) {
      return promiseResolve(C, onFinally()).then(function () { return x; });
    } : onFinally,
    isFunction ? function (e) {
      return promiseResolve(C, onFinally()).then(function () { throw e; });
    } : onFinally
  );
} });


/***/ }),
/* 79 */
/***/ (function(module, exports, __webpack_require__) {

"use strict";

// https://github.com/tc39/proposal-promise-try
var $export = __webpack_require__(11);
var newPromiseCapability = __webpack_require__(31);
var perform = __webpack_require__(50);

$export($export.S, 'Promise', { 'try': function (callbackfn) {
  var promiseCapability = newPromiseCapability.f(this);
  var result = perform(callbackfn);
  (result.e ? promiseCapability.reject : promiseCapability.resolve)(result.v);
  return promiseCapability.promise;
} });


/***/ }),
/* 80 */
/***/ (function(module, exports, __webpack_require__) {

var core = __webpack_require__(2);
var $JSON = core.JSON || (core.JSON = { stringify: JSON.stringify });
module.exports = function stringify(it) { // eslint-disable-line no-unused-vars
  return $JSON.stringify.apply($JSON, arguments);
};


/***/ }),
/* 81 */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(Buffer) {

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _stringify = __webpack_require__(22);

var _stringify2 = _interopRequireDefault(_stringify);

var _commonUtil = __webpack_require__(20);

var _commonUtil2 = _interopRequireDefault(_commonUtil);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

var globalUtil = __webpack_require__(21).default;
var httpUtil = __webpack_require__(35).default;
var sparkmd5 = __webpack_require__(87);

var web = function web(mainOpts) {
    var self = this,
        $control,
        controlId,
        uploadRetryTimer = {},
        //文件上传重试计时器
    s3;
    this.tasks = []; //任务列表

    function getConfig() {
        if (window.nxt && window.nxt.config) {
            return window.nxt.config;
        } else if (mainOpts.configInst) {
            return mainOpts.configInst;
        }
    }

    function windowClose() {
        if (self.getFilesByStatus('progress', 'deleteing').length > 0) {
            return l('upload.closePageTip', '当前页面存在未完成的上传任务，关闭页面会导致上传失败，你确定要关闭吗？');
        }
    }

    function init() {
        $(window).on('beforeunload', windowClose);
    }

    //获取文件最后修改时间
    function getFileTime(file) {
        //兼容不同浏览器
        if (_.isUndefined(file.lastModifiedDate)) {
            return file.lastModified;
        }
        return file.lastModifiedDate.getTime();
    }

    //获取默认关键帧
    function getDefaultKeyframe(task) {
        var type = _.find(getConfig().entityTypes, { code: task.entityType });
        if (type == null) {
            type = _.find(getConfig().entityTypes, { isOther: true });
        }
        if (type == null) {
            return '';
        }
        return type.keyframe.replace('~', '');
    }

    //未授权提示
    function unauthorizedTip() {
        _.forEach(self.tasks, function (item) {
            if (item.status !== 'added' && item.status !== 'success') item.status = 'error';
        });
        $(window).off('beforeunload', windowClose);
        _commonUtil2.default.prompt(l('upload.unauthorized', '你未登录或已超时，请重新登录。'));
        location.href = getConfig().loginUrl + '?login_backUrl=' + location.href;
    }

    function initControl(accept) {
        var accept = accept || '';
        if ($control == null) {
            controlId = _.uniqueId('mam-web-transfer-');
            if (mainOpts.isMultiple === undefined || mainOpts.isMultiple) {
                //$control = $('<input id="' + controlId + '" type="file" multiple style="display:none"/>');
                $control = $('<input id="' + controlId + '" accept="' + accept + '" type="file" multiple style="display:none"/>');
            } else {
                //$control = $('<input id="' + controlId + '" type="file" style="display:none"/>');
                $control = $('<input id="' + controlId + '" accept="' + accept + '" type="file" style="display:none"/>');
            }
            $('body').append($control);
        }
    }

    function destroyControl() {
        if ($control != null) {
            $control.unbind('change');
            $control.remove(); //因为IE10 重置文件框的值比较麻烦，如果不重置，无法重复选择。所以每次初始化来兼容IE10
            $control = null;
        }
    }

    //触发事件
    function trigger(event, data) {
        $(self).trigger(event, data);
    }

    //订阅事件
    this.on = function (name, call) {
        //利用jquery的事件，因为HTML5的自定义事件不兼容IE10。
        $(self).on(name, call);
    };
    this.off = function (name, call) {
        $(self).off(name, call);
    };

    /** 为图片包和成组上传获取任务，单个任务里包含多个文件这种方式 */
    this.getGroupTask = function (tasks, code, targetFolder, taskType, transferType) {
        var type = _.find(getConfig().entityTypes, { 'code': code });

        var rettask = {
            entityType: type ? type.code : '',
            files: tasks.map(function (task) {
                return {
                    fileName: task.fileName,
                    file: task.file,
                    metadata: task.metadata
                };
            }),
            'metadata': {
                name: ''
            },
            targetFolder: targetFolder,
            taskType: taskType,
            transferType: transferType,
            status: 'added',
            progress: 0
        };
        return rettask;
    };

    this.getTasksByFiles = function (files) {
        var result = [];
        _.forEach(files, function (item) {
            var ext = '.' + _commonUtil2.default.getExtension(item.name);
            result.push({
                entityType: _commonUtil2.default.getTypeByExt(ext, getConfig()).code,
                fileName: item.name,
                metadata: {
                    name: _commonUtil2.default.getFileName(item.name),
                    ext: ext
                },
                status: 'added',
                progress: 0,
                file: item
            });
        });
        return result;
    };

    //打开文件选取框
    this.openFileSelector = function (callback, single, accept) {
        var self = this;
        // console.log(AWS)
        // console.log(this.OSS)
        destroyControl();
        initControl(accept);
        if (single === true) {
            $control.removeAttr('multiple');
        } else {
            $control.attr('multiple', 'multiple');
        }
        $control.on('change', function () {
            if ($control.val() === '') {
                return;
            }
            var files = $control[0].files;
            if (single === true) {
                files = files[0];
            }
            callback(self.getTasksByFiles(files));
            initControl(accept);
        });
        $control.click(function (e) {
            e.stopPropagation();
        });
        $control.trigger('click');
    };

    //创建任务
    this.createTask = function (dto, params) {
        var list = [];
        switch (params.taskType) {
            case 1:
                //普通类型 //另外，素材包类型进行多个素材上传并分别创建任务，这些个任务在上一步已重置为了普通类型上传
                if (!_.isArray(dto)) {
                    dto = [dto];
                }
                _.forEach(dto, function (task) {
                    if (task.files && task.files.length > 0) {
                        task.files = _.map(task.files, function (file) {
                            return {
                                file: file.file,
                                fileName: file.file.name,
                                fileSize: file.file.size,
                                fileLastModifiedDate: getFileTime(file.file),
                                type: Number(file.type) ? Number(file.type) : 0
                            };
                        });
                        if (task.file) {
                            task.files.unshift({
                                file: task.file,
                                fileName: task.file.name,
                                fileSize: task.file.size,
                                fileLastModifiedDate: getFileTime(task.file),
                                type: task.type !== undefined ? Number(task.type) : 0
                            });
                        }
                    } else {
                        task.files = [{
                            file: task.file,
                            fileName: task.file.name,
                            fileSize: task.file.size,
                            fileLastModifiedDate: getFileTime(task.file),
                            type: task.type !== undefined ? Number(task.type) : 0
                        }];
                    }
                    delete task.file;
                    list.push(task);
                });
                break;
            case 2:
            case 3:
                _.forEach(dto.files, function (file) {
                    file.fileName = file.file.name;
                    file.fileSize = file.file.size;
                    file.fileLastModifiedDate = getFileTime(file.file);
                });
                list.push(dto);
                break;
        }
        _.forEach(list, function (task) {
            task.taskType = params.taskType;
            task.transferType = params.transferType;
            task.targetFolder = params.targetFolder;
            task.relationContentType = params.relationContentType;
            task.relationContentId = params.relationContentId;
            self.addTask(task);
        });
    };

    //亚马逊OSS初始化 + Vida Grid（类型是5）
    function initS3MultipartUpload(ossClientInfo, file) {
        if (!ossClientInfo) return;
        if (ossClientInfo.manageType === 'TYY_S3') return initOssMultipartUpload(ossClientInfo, file);
        if (ossClientInfo.manageType === 'ALY_OSS') return;
        var params = {
            Bucket: ossClientInfo.bucketName,
            Key: file.relativePath.replace(/\\/g, '/')
        };
        getS3Client(ossClientInfo);
        if (file.fileSize <= 5 * 1024 * 1024) return;
        var createUploadPromise = s3.createMultipartUpload(params).promise();
        file.createUploadPromise = createUploadPromise;
        var defer = $.Deferred();
        file.createUploadPromise.then(function (initData) {
            file.uploadId = initData.UploadId;
            /* initData = {
                Bucket: "examplebucket", 
                Key: "largeobject", 
                UploadId: "ibZBv_75gd9r8lH_gqXatLdxMVpAlj6ZQjEs.OwyF3953YdwbcQnMA2BLGn8Lx12fQNICtMw5KyteFeHw.Sjng--"
            } */
            defer.resolve();
        }).catch(function (err) {
            defer.reject();
            console.info(err); // an error occurred
        });
        return defer.promise();
    }

    function getS3Client(ossClientInfo) {
        var credentials = {
            accessKeyId: ossClientInfo.accessKeyId,
            secretAccessKey: ossClientInfo.accessKeySecret,
            endpoint: ossClientInfo.endpoint,
            sslEnabled: !ossClientInfo.useHttp,
            s3ForcePathStyle: true,
            signatureVersion: 'v' + ossClientInfo.version,
            apiVersion: '2006-03-01'
        }; // 秘钥形式的登录上传
        if (ossClientInfo.region) AWS.config.region = ossClientInfo.region; // 设置0000000区域
        if (getConfig().isHandleHttpPath && credentials.endpoint) {
            var href = window.location.protocol + '//' + window.location.host;
            credentials.endpoint = href;
        }
        if (ossClientInfo.manageType !== 'TYY_S3') s3 = new AWS.S3(credentials);else s3 = new OOS.S3(credentials);
    }

    //天翼云OSS初始化
    function initOssMultipartUpload(ossClientInfo, file) {
        if (!ossClientInfo || ossClientInfo.manageType !== 'TYY_S3') return;
        getS3Client(ossClientInfo);
        if (file.fileSize <= 5 * 1024 * 1024) return;
        var params = {
            Bucket: ossClientInfo.bucketName,
            Key: file.relativePath.replace(/\\/g, '/')
        };
        var createUploadPromise = s3.createMultipartUpload(params).promise();
        file.createUploadPromise = createUploadPromise;
        var defer = $.Deferred();
        file.createUploadPromise.then(function (initData) {
            file.uploadId = initData.UploadId;
            /* initData = {
                Bucket: "examplebucket", 
                Key: "largeobject", 
                UploadId: "ibZBv_75gd9r8lH_gqXatLdxMVpAlj6ZQjEs.OwyF3953YdwbcQnMA2BLGn8Lx12fQNICtMw5KyteFeHw.Sjng--"
            } */
            defer.resolve();
        }).catch(function (err) {
            defer.reject();
            console.info(err); // an error occurred
        });
        return defer.promise();
    }

    //初始化任务
    function initTask(task) {
        trigger('task-init-before', task);
        var apiVersion = _commonUtil2.default.getCookie('apiVersion');
        var url = getConfig().server + '/upload/multipart/init';
        if (apiVersion && apiVersion === 'mamcore2.3') {
            url = getConfig().server + '/sflud/v1/upload/multipart/init';
        }
        if (mainOpts.loginToken) {
            url += '?token=' + mainOpts.loginToken;
        }
        if (task.metadata && task.metadata.field) {
            // 因后台升级为.net core3.0 需要数据类型一致
            var fileSize = task.metadata.field.find(function (s) {
                return s.fieldName === 'filesize';
            });
            if (fileSize) {
                fileSize.value = String(fileSize.value);
            }
        }
        httpUtil.post(url, task).then(function (res) {
            res = res.data;
            for (var i = 0; i < res.files.length; i++) {
                var file = task.files[i].file;
                task.files[i] = res.files[i];
                task.files[i].file = file;
                task.files[i].status = 'prepared';
                task.files[i].fileSizeString = _commonUtil2.default.formatSize(res.files[i].fileSize);
                task.files[i].progress = 0;
                task.sizeTotal += res.files[i].fileSize;
                task.chunkTotal += res.files[i].chunkTotal;
            }
            task.taskId = res.taskId;
            task.entityType = res.entityType;
            task.fileTotal = res.fileTotal;
            task.targetFolder = res.targetFolder;
            task.targetFolderName = res.targetFolderName;
            task.targetType = res.targetType;
            task.keyframe = res.keyframe;
            task.status = 'prepared';
            task.inited = true;
            task.sizeTotalString = _commonUtil2.default.formatSize(task.sizeTotal);
            task.isJsUpload = res.isJsUpload;
            task.ossClientInfo = res.ossClientInfo;
            trigger('task-init-success', task);
            self.prepareUpload();
        }, function (res) {
            trigger('task-init-error', [task, res]);
            if (res.status === 401) {
                unauthorizedTip(self.tasks);
                return;
            } else {
                task.status = 'error';
                _.forEach(task.files, function (file) {
                    file.status = 'error';
                });
                _commonUtil2.default.prompt(l('upload.', '上传失败：${text}', { text: res.error.desc || res.error.title }));
                self.prepareUpload();
            }
        });
    }

    //添加任务
    this.addTask = function (task) {
        task.status = 'init';
        task.progress = task.sizeTotal = task.chunkFinished = task.chunkTotal = 0;
        task.keyframe = getDefaultKeyframe(task);
        self.tasks.push(task);
        initTask(task);
    };

    //准备上传
    this.prepareUpload = function () {
        var progress = self.getFilesByStatus('progress');
        var len = getConfig().webUploadThreads - progress.length;
        if (len <= 0) return;
        var prepared = self.getFilesByStatus('prepared');
        if (len > prepared.length) len = prepared.length;
        if (len === 0) //判断是否存在需要上传的任务
            return;
        for (var i = 0; i < len; i++) {
            prepared[i].task.status = prepared[i].file.status = 'progress';
            self.upload(prepared[i]);
        }
    };

    //上传
    this.upload = function (item) {
        if (item.task.ossClientInfo) {
            if (item.task.ossClientInfo.manageType === 'ALY_OSS') this.uploadByAliOss(item);else if (item.task.ossClientInfo.manageType === 'TYY_S3') this.uploadByOos(item);else if (item.task.ossClientInfo.manageType === 'KSYUN') this.uploadByKsyun(item);else this.uploadByS3(item);
        } else {
            this.uploadByBackend(item);
        }
    };

    //计算分片数据的MD5
    function calcChunkMd5(item, data, callback) {
        if (getConfig().webUploadMd5Enable) {
            if (item.file.fileReader == null) item.file.fileReader = new FileReader();
            item.file.fileReader.onload = function (e) {
                var spark = new sparkmd5();
                spark.appendBinary(e.target.result);
                var md5 = spark.end();
                spark.destroy();
                callback(md5);
            };
            item.file.fileReader.onerror = function (e) {
                uploadErrorRetry(item);
            };
            item.file.fileReader.readAsBinaryString(data);
        } else {
            callback('');
        }
    }

    //计算剩余时间
    function calcSurplusTime(task, file) {
        if (file.startTime != null) {
            if (!_.isNumber(file.surplusTime)) file.surplusTime = 0;
            file.surplusTime = (new Date() - file.startTime) * (file.chunkTotal - file.chunkIndex);

            if (!_.isNumber(task.surplusTime)) task.surplusTime = 0;
            task.surplusTime = (new Date() - file.startTime) * (task.chunkTotal - task.chunkFinished);
        }
        file.startTime = new Date();
    }

    //上传失败重试
    function uploadErrorRetry(item) {
        if (!item.file.hasOwnProperty('errorCount')) item.file.errorCount = 0;
        if (item.file.errorCount < 4) {
            item.file.errorCount++;
            uploadRetryTimer[item.file.fileId] = setTimeout(function () {
                self.upload(item);
            }, 3000);
        } else {
            item.task.status = item.file.status = 'error';
            trigger('task-upload-error', item.task);
            self.prepareUpload();
        }
    }

    this.uploadByBackend = function (item) {
        var file = item.file;
        var task = item.task;
        if (file.chunkIndex < file.chunkTotal) {
            calcSurplusTime(task, file);
            var start = file.chunkIndex * file.chunkSize,
                end = Math.min(file.fileSize, start + file.chunkSize),
                form = new FormData(),
                data = file.file.slice(start, end);
            if (data == null || data.size == 0) {
                item.task.status = item.file.status = 'error';
                trigger('task-upload-error', item.task);
                item.file.file = null;
                self.prepareUpload();
                return;
            }
            calcChunkMd5(item, data, function (md5) {
                form.append('fileData', data);
                form.append('taskId', file.taskId);
                form.append('fileId', file.fileId);
                form.append('chunkIndex', file.chunkIndex);
                form.append('md5', md5);
                //form.append('uploadId', file.tag);

                //下面还写一次是因为有2种情况，必须在这里也要删除才合理，1,初始化成功准备上传，2，上传一片后上传下一片之前
                //之所有放在构造formData之后，是因为构造formData是需要一点点时间的。避免时间差带来的偶现bug
                if (task.status === 'deleteing') {
                    self.clearTask(task);
                    return;
                }
                var apiVersion = _commonUtil2.default.getCookie('apiVersion');
                var url = '/upload/multipart';
                if (apiVersion && apiVersion === 'mamcore2.3') {
                    url = '/sflud/v1/upload/multipart';
                }
                if (mainOpts.loginToken) {
                    url += '?token=' + mainOpts.loginToken;
                }
                httpUtil.post(getConfig().server + url, form, {
                    contentType: false,
                    processData: false
                }).then(function (res) {
                    if (task.status === 'deleteing') {
                        self.clearTask(task);
                        return;
                    }
                    res = res.data;
                    file = _.find(task.files, { fileId: res.fileId });
                    file.chunkIndex = res.chunkIndex;
                    if (res.taskStatus === 3) {
                        // 3：任务完成
                        file.progress = task.progress = 100;
                        file.surplusTime = task.surplusTime = null;
                        delete file.fileReader;
                        file.status = task.status = 'success';
                        self.prepareUpload();
                        callComplete(task).then(function () {
                            trigger('task-upload-success', task);
                        });
                    } else {
                        task.progress = calcTaskProgress(task);
                        file.progress = calcFileProgress(file);
                        file.status = task.status = 'progress';
                        trigger('task-upload-progress', task);
                        file.errorCount = 0;
                        self.upload(item);
                    }
                }, function (res) {
                    if (res.status === 401) {
                        unauthorizedTip();
                        return;
                    }
                    uploadErrorRetry(item);
                });
            });
        } else {
            if (file.hasOwnProperty('errorCount')) {
                delete uploadRetryTimer[file.fileId];
                delete file.errorCount;
            }
            delete file.fileReader;
            file.surplusTime = null;
            file.progress = 100;
            file.status = 'success';
            task.progress = calcTaskProgress(task);
            trigger('task-upload-success', task);
            self.prepareUpload();
        }
    };

    var callUpload = function callUpload(file) {
        var form = new FormData();
        form.append('taskId', file.taskId);
        form.append('fileId', file.fileId);
        form.append('chunkIndex', file.chunkIndex);
        form.append('partInfo', (0, _stringify2.default)(file.partInfo));
        form.append('uploadId', file.uploadId);
        form.append('checkPoint', file.checkPoint);
        var apiVersion = _commonUtil2.default.getCookie('apiVersion');
        var url = '/upload/multipart';
        if (apiVersion && apiVersion === 'mamcore2.3') {
            url = '/sflud/v1/upload/multipart';
        }
        if (mainOpts.loginToken) {
            url += '?token=' + mainOpts.loginToken;
        }
        return httpUtil.post(getConfig().server + url, form, {
            contentType: false,
            processData: false
        });
    };

    var callComplete = function callComplete(task) {
        var serverUrl = getConfig().server;
        var apiVersion = _commonUtil2.default.getCookie('apiVersion');
        var url = '/upload/multipart/complete?taskId=' + task.taskId;
        if (apiVersion && apiVersion === 'mamcore2.3') {
            url = '/sflud/v1/upload/multipart/complete?taskId=' + task.taskId;
        }
        return httpUtil.post(serverUrl + url);
    };

    this.uploadByAliOssWithServerCallback = function (item) {
        var file = item.file;
        var task = item.task;

        var clientOpt = {
            region: task.ossClientInfo.region,
            accessKeyId: task.ossClientInfo.accessKeyId,
            accessKeySecret: task.ossClientInfo.accessKeySecret,
            bucket: task.ossClientInfo.bucketName
        };
        if (task.ossClientInfo.endpoint) {
            clientOpt.endpoint = task.ossClientInfo.endpoint;
        }
        var client = new this.OSS.Wrapper(clientOpt);
        // var client = new OSS.Wrapper({
        //     region: 'oss-cn-beijing',
        //     accessKeyId: 'LTAIJNTTrKH8Aoat',
        //     accessKeySecret: 'HjYCH0K4bYXZQ3I0JIoJ8Rbp2zYK0J',
        //     bucket: 'bucket-pan-test'
        // });

        if (file.fileSize <= 102400) // 小文件不能分片上传
            {
                item.fileReader = new FileReader();
                item.fileReader.onload = function (e) {
                    client.put(file.relativePath, new Buffer(this.result)).then(function (res) {
                        callUpload(file).then(function (res) {
                            res = res.data;
                            if (res.taskStatus === 3) {
                                // 3：任务完成
                                file.progress = task.progress = 100;
                                file.surplusTime = task.surplusTime = null;
                                delete file.fileReader;
                                file.status = task.status = 'success';
                                callComplete(task).then(function () {
                                    trigger('task-upload-success', task);
                                });
                            } else {
                                file.status = 'success';
                                file.progress = 100;
                            }
                            self.prepareUpload();
                        });
                    }, function (res) {
                        console.error(res);
                    });
                };
                item.fileReader.readAsArrayBuffer(file.file);
            } else {
            var callbackUrl = _commonUtil2.default.addUrlParam(getConfig().ossUpCallbackUrl, 'taskId=' + task.taskId);
            if (mainOpts.loginToken) {
                callbackUrl = _commonUtil2.default.addUrlParam(callbackUrl, 'token=' + mainOpts.loginToken);
            }
            var opts = {
                partSize: file.chunkSize,
                progress: function progress(p, checkpoint, res) {
                    return function (done) {
                        if (task.status === 'deleteing') {
                            self.clearTask(task);
                        } else if (task.status === 'success') {
                            done();
                        } else {
                            if (checkpoint.doneParts.length === 0) //第一次回调表示初始化，oss还未传文件上去，可忽略
                                {
                                    done();
                                    return;
                                }
                            file.partInfo = checkpoint.doneParts[checkpoint.doneParts.length - 1];
                            file.uploadId = checkpoint.uploadId;
                            file.checkPoint = (0, _stringify2.default)(checkpoint, function (key, value) {
                                if (key === 'file') {
                                    return undefined;
                                }
                                return value;
                            });
                            file.chunkIndex = checkpoint.doneParts.length - 1; //以oss的完成片数为准，避免调用done以前连续收到两次回调
                            callUpload(file).then(function (res) {
                                if (task.status === 'deleteing') {
                                    self.clearTask(task);
                                    return;
                                }
                                res = res.data;
                                file.chunkIndex = res.chunkIndex;
                                if (res.taskStatus === 3) {
                                    // 3：任务完成
                                    file.progress = task.progress = 100;
                                    file.surplusTime = task.surplusTime = null;
                                    delete file.fileReader;
                                    file.status = task.status = 'success';
                                    trigger('task-upload-success', task);
                                    self.prepareUpload();
                                    //callComplete(task);
                                } else {
                                    if (file.chunkIndex < file.chunkTotal) {
                                        task.progress = calcTaskProgress(task);
                                        file.progress = calcFileProgress(file);
                                        file.status = task.status = 'progress';
                                        trigger('task-upload-progress', task);
                                        file.errorCount = 0;
                                    } else {
                                        if (file.hasOwnProperty('errorCount')) {
                                            delete uploadRetryTimer[file.fileId];
                                            delete file.errorCount;
                                        }
                                        file.surplusTime = null;
                                        file.progress = 100;
                                        file.status = 'success';
                                        task.progress = calcTaskProgress(task);
                                        trigger('task-upload-success', task);
                                        self.prepareUpload();
                                    }
                                }
                                done();
                            }, function (res) {
                                if (res.status === 401) {
                                    unauthorizedTip();
                                    return;
                                }
                                uploadErrorRetry(item);
                            });
                        }
                    };
                },
                callback: {
                    callbackUrl: callbackUrl,
                    callbackBody: 'bucket=${bucket}&object=${object}&etag=${etag}&size=${size}&mimeType=${mimeType}&imageInfo.height=${imageInfo.height}&imageInfo.width=${imageInfo.width}&imageInfo.format=${imageInfo.format}'
                }
            };
            if (file.checkPoint) //断点续传
                {
                    opts.checkpoint = JSON.parse(file.checkPoint);
                    opts.checkpoint.file = file.file;
                }
            client.multipartUpload(file.relativePath, file.file, opts).then(function (res) {
                console.log('upload success', res);
            }, function (res) {
                console.log(res);
            });
        }
    };

    this.uploadByAliOss = function (item) {
        var file = item.file;
        var task = item.task;

        var clientOpt = {
            region: task.ossClientInfo.region,
            accessKeyId: task.ossClientInfo.accessKeyId,
            accessKeySecret: task.ossClientInfo.accessKeySecret,
            bucket: task.ossClientInfo.bucketName
        };
        if (task.ossClientInfo.endpoint) {
            clientOpt.endpoint = task.ossClientInfo.endpoint;
        }
        var client = new this.OSS.Wrapper(clientOpt);
        // var client = new OSS.Wrapper({
        //     region: 'oss-cn-beijing',
        //     accessKeyId: 'LTAIJNTTrKH8Aoat',
        //     accessKeySecret: 'HjYCH0K4bYXZQ3I0JIoJ8Rbp2zYK0J',
        //     bucket: 'bucket-pan-test'
        // });

        if (file.fileSize <= 102400) // 小文件不能分片上传
            {
                item.fileReader = new FileReader();
                item.fileReader.onload = function (e) {
                    client.put(file.relativePath, new Buffer(this.result)).then(function (res) {
                        callUpload(file).then(function (res) {
                            res = res.data;
                            if (res.taskStatus === 3) {
                                // 3：任务完成
                                file.progress = task.progress = 100;
                                file.surplusTime = task.surplusTime = null;
                                delete file.fileReader;
                                file.status = task.status = 'success';
                                callComplete(task).then(function () {
                                    trigger('task-upload-success', task);
                                });
                            } else {
                                file.status = 'success';
                                file.progress = 100;
                            }
                            self.prepareUpload();
                        });
                    }, function (res) {
                        console.error(res);
                    });
                };
                item.fileReader.readAsArrayBuffer(file.file);
            } else {
            var opts = {
                partSize: file.chunkSize,
                progress: function progress(p, checkpoint, res) {
                    return function (done) {
                        if (task.status === 'deleteing') {
                            self.clearTask(task);
                        } else if (task.status === 'success') {
                            done();
                        } else {
                            if (checkpoint.doneParts.length === 0) //第一次回调表示初始化，oss还未传文件上去，可忽略
                                {
                                    done();
                                    return;
                                }
                            file.partInfo = checkpoint.doneParts[checkpoint.doneParts.length - 1];
                            file.uploadId = checkpoint.uploadId;
                            file.checkPoint = (0, _stringify2.default)(checkpoint, function (key, value) {
                                if (key === 'file') {
                                    return undefined;
                                }
                                return value;
                            });
                            file.chunkIndex = checkpoint.doneParts.length - 1; //以oss的完成片数为准，避免调用done以前连续收到两次回调
                            callUpload(file).then(function (res) {
                                if (task.status === 'deleteing') {
                                    self.clearTask(task);
                                    return;
                                }
                                res = res.data;
                                file.chunkIndex = res.chunkIndex;
                                if (res.taskStatus === 3) {
                                    // 3：任务完成
                                    file.progress = task.progress = 100;
                                    file.surplusTime = task.surplusTime = null;
                                    delete file.fileReader;
                                    file.status = task.status = 'success';
                                    self.prepareUpload();
                                    callComplete(task).then(function () {
                                        trigger('task-upload-success', task);
                                    });
                                } else {
                                    if (file.chunkIndex < file.chunkTotal) {
                                        task.progress = calcTaskProgress(task);
                                        file.progress = calcFileProgress(file);
                                        file.status = task.status = 'progress';
                                        trigger('task-upload-progress', task);
                                        file.errorCount = 0;
                                    } else {
                                        if (file.hasOwnProperty('errorCount')) {
                                            delete uploadRetryTimer[file.fileId];
                                            delete file.errorCount;
                                        }
                                        file.surplusTime = null;
                                        file.progress = 100;
                                        file.status = 'success';
                                        task.progress = calcTaskProgress(task);
                                        trigger('task-upload-success', task);
                                        self.prepareUpload();
                                    }
                                }
                                done();
                            }, function (res) {
                                if (res.status === 401) {
                                    unauthorizedTip();
                                    return;
                                }
                                uploadErrorRetry(item);
                            });
                        }
                    };
                }
            };
            if (file.checkPoint) //断点续传
                {
                    opts.checkpoint = JSON.parse(file.checkPoint);
                    opts.checkpoint.file = file.file;
                }
            client.multipartUpload(file.relativePath, file.file, opts).then(function (res) {
                console.log('upload success', res);
            }, function (res) {
                console.log(res);
            });
        }
    };

    this.doUploadByKsyun = function (item) {
        var file = item.file;
        var task = item.task;

        var start = file.chunkIndex * file.chunkSize,
            end = Math.min(file.fileSize, start + file.chunkSize),
            filedata = file.file.slice(start, end);
        var upParams = {
            Bucket: task.ossClientInfo.bucketName,
            Key: file.relativePath.replace(/\\/g, '/'),
            PartNumber: file.chunkIndex + 1,
            UploadId: file.uploadId,
            body: filedata
        };
        Ks3.upload_part(upParams, function (err, partNumber, etag) {
            if (err) {
                console.error(err);
                return;
            }
            if (task.status === 'deleteing') {
                self.clearTask(task);
            } else {
                callUpload(file).then(function (res) {
                    if (task.status === 'deleteing') {
                        self.clearTask(task);
                        return;
                    }
                    res = res.data;
                    file.chunkIndex = res.chunkIndex;
                    if (res.taskStatus === 3) {
                        // 3：任务完成
                        file.progress = task.progress = 100;
                        file.surplusTime = task.surplusTime = null;
                        delete file.fileReader;
                        file.status = task.status = 'success';
                        self.prepareUpload();
                        callComplete(task).then(function () {
                            trigger('task-upload-success', task);
                        });
                    } else {
                        if (file.chunkIndex < file.chunkTotal) {
                            task.progress = calcTaskProgress(task);
                            file.progress = calcFileProgress(file);
                            file.status = task.status = 'progress';
                            trigger('task-upload-progress', task);
                            file.errorCount = 0;
                            self.upload(item);
                        } else {
                            if (file.hasOwnProperty('errorCount')) {
                                delete uploadRetryTimer[file.fileId];
                                delete file.errorCount;
                            }
                            file.surplusTime = null;
                            file.progress = 100;
                            file.status = 'success';
                            task.progress = calcTaskProgress(task);
                            trigger('task-upload-success', task);
                            self.prepareUpload();
                        }
                    }
                }, function (res) {
                    if (res.status === 401) {
                        unauthorizedTip();
                        return;
                    }
                    uploadErrorRetry(item);
                });
            }
        });
    };

    this.uploadByKsyun = function (item) {
        var file = item.file;
        var task = item.task;

        if (task.ossClientInfo.accessKeyId && task.ossClientInfo.accessKeySecret) {
            Ks3.config.AK = task.ossClientInfo.accessKeyId; //TODO： 请替换为您的AK
            Ks3.config.SK = task.ossClientInfo.accessKeySecret; //TODO: 测试时请填写您的secret key  注意：前端计算signature不安全
        }

        Ks3.config.region = task.ossClientInfo.region;
        Ks3['ENDPOINT']['' + task.ossClientInfo.region] = task.ossClientInfo.serviceUrl;

        //必须调用，不然没法用。。。
        var ks3Options = {
            KSSAccessKeyId: task.ossClientInfo.accessKeyId,
            signature: task.ossClientInfo.accessKeySecret,
            bucket_name: task.ossClientInfo.bucketName,
            key: file.relativePath.replace(/\\/g, '/'),
            acl: "public-read", //此处需要与policy中的acl值保持一致，默认值：private。
            uploadDomain: task.ossClientInfo.serviceUrl + '/' + task.ossClientInfo.bucketName, //杭州region
            autoStart: false
        };
        var pluploadOptions = {
            drop_element: document.body,
            url: task.ossClientInfo.serviceUrl
        };
        var tempUpload = new ks3FileUploader(ks3Options, pluploadOptions);
        //必须调用，不然没法用。。。

        if (file.fileSize <= 100 * 1024 * 1024) {
            // 小于100M文件不用分片上传
            var params = {
                Bucket: task.ossClientInfo.bucketName,
                Key: file.relativePath.replace(/\\/g, '/'),
                File: file.file,
                ACL: 'public-read'
            };
            Ks3.putObject(params, function (rerr) {
                if (rerr) {
                    console.info(rerr, rerr.error); // an error occurred
                    uploadErrorRetry(item);
                } else {
                    callUpload(file).then(function (res) {
                        res = res.data;
                        if (res.taskStatus === 3) {
                            // 3：任务完成
                            file.progress = task.progress = 100;
                            file.surplusTime = task.surplusTime = null;
                            delete file.fileReader;
                            file.status = task.status = 'success';
                            callComplete(task).then(function () {
                                trigger('task-upload-success', task);
                            });
                        } else {
                            file.status = 'success';
                            file.progress = 100;
                        }
                        self.prepareUpload();
                    });
                }
            });
        } else {
            var params = {
                Bucket: task.ossClientInfo.bucketName,
                Key: file.relativePath.replace(/\\/g, '/'),
                ACL: 'public-read'
            };
            if (!file.uploadId) {
                Ks3.multitpart_upload_init(params, function (err, uploadId) {
                    if (err) {
                        console.error(err);
                    } else {
                        file.uploadId = uploadId;
                        this.doUploadByKsyun(item);
                    }
                });
            } else {
                this.doUploadByKsyun(item);
            }
        }
    };

    this.uploadByS3 = function (item) {
        var file = item.file;
        var task = item.task;
        if (!s3) getS3Client(item.task.ossClientInfo);
        if (file.chunkIndex < file.chunkTotal) {
            var start = file.chunkIndex * file.chunkSize,
                end = Math.min(file.fileSize, start + file.chunkSize),
                form = new FormData(),
                data = file.file.slice(start, end);
            if (data == null || data.size == 0) {
                item.task.status = item.file.status = 'error';
                trigger('task-upload-error', item.task);
                item.file.file = null;
                self.prepareUpload();
                return;
            }

            var uploadAfter = function uploadAfter(task, file) {
                form.append('taskId', file.taskId);
                form.append('fileId', file.fileId);
                form.append('chunkIndex', file.chunkIndex);
                form.append('partInfo', file.partInfo);
                form.append('uploadId', file.uploadId);

                // 下面还写一次是因为有2种情况，必须在这里也要删除才合理，1,初始化成功准备上传，2，上传一片后上传下一片之前
                // 之所有放在构造formData之后，是因为构造formData是需要一点点时间的。避免时间差带来的偶现bug
                if (task.status === 'deleteing') {
                    self.clearTask(task);
                    return;
                }

                var apiVersion = _commonUtil2.default.getCookie('apiVersion');
                var url = '/upload/multipart';
                if (apiVersion && apiVersion === 'mamcore2.3') {
                    url = '/sflud/v1/upload/multipart';
                }
                if (mainOpts.loginToken) {
                    url += '?token=' + mainOpts.loginToken;
                }
                httpUtil.post(getConfig().server + url, form, {
                    contentType: false,
                    processData: false
                }).then(function (res) {
                    if (task.status === 'deleteing') {
                        self.clearTask(task);
                        return;
                    }
                    res = res.data;
                    file = _.find(task.files, { fileId: res.fileId });
                    file.chunkIndex = res.chunkIndex;
                    if (res.taskStatus === 3) {
                        // 3：任务完成
                        file.progress = task.progress = 100;
                        file.surplusTime = task.surplusTime = null;
                        delete file.fileReader;
                        file.status = task.status = 'success';
                        self.prepareUpload();
                        callComplete(task).then(function () {
                            trigger('task-upload-success', task);
                        });
                    } else {
                        task.progress = calcTaskProgress(task);
                        file.progress = calcFileProgress(file);
                        file.status = task.status = 'progress';
                        trigger('task-upload-progress', task);
                        file.errorCount = 0;
                        self.upload(item);
                    }
                }, function (res) {
                    if (res.status === 401) {
                        unauthorizedTip();
                        return;
                    }
                    uploadErrorRetry(item);
                });
            };

            if (file.fileSize <= 5 * 1024 * 1024) // 小于5M文件不用分片上传
                {
                    s3.putObject({
                        Body: file.file,
                        Bucket: task.ossClientInfo.bucketName,
                        Key: file.relativePath.replace(/\\/g, '/')
                    }, function (err, successData) {
                        if (err) {
                            console.info(err, err.stack); // an error occurred
                            uploadErrorRetry(item);
                        } else {
                            if (successData.ETag) file.partInfo = successData.ETag.replace(/"/g, '');
                            uploadAfter(task, file);
                            console.info(successData); // successful response
                        }
                        /*
                        data = {
                            ETag: "\"6805f2cfc46c0f04559748bb039d69ae\"",
                            VersionId: "Kirh.unyZwjQ69YxcQLA8z4F5j3kJJKr"
                        }
                        */
                    });
                } else {
                if (!file.uploadId) {
                    var getUploadIdTask = initS3MultipartUpload(task.ossClientInfo, file);
                    if (getUploadIdTask) {
                        getUploadIdTask.then(function () {
                            self.upload(item);
                        });
                    }
                    return;
                }
                var params = {
                    Body: data,
                    Bucket: task.ossClientInfo.bucketName,
                    Key: file.relativePath.replace(/\\/g, '/'),
                    PartNumber: file.chunkIndex + 1,
                    UploadId: file.uploadId
                };
                s3.uploadPart(params, function (err, successData) {
                    if (err) {
                        console.info(err, err.stack); // an error occurred
                        uploadErrorRetry(item);
                    } else {
                        if (successData.ETag) file.partInfo = successData.ETag.replace(/"/g, '');
                        uploadAfter(task, file);
                        console.info(successData); // successful response
                        /*
                        successData = {
                            ETag: "\"d8c2eafd90c266e19ab9dcacc479f8af\""
                        }
                        */
                    }
                });
            }
        } else {
            if (file.hasOwnProperty('errorCount')) {
                delete uploadRetryTimer[file.fileId];
                delete file.errorCount;
            }
            delete file.fileReader;
            file.surplusTime = null;
            file.progress = 100;
            file.status = 'success';
            task.progress = calcTaskProgress(task);
            trigger('task-upload-success', task);
            self.prepareUpload();
        }
    };

    this.uploadByOos = function (item) {
        var file = item.file;
        var task = item.task;

        if (file.chunkIndex < file.chunkTotal) {
            var start = file.chunkIndex * file.chunkSize,
                end = Math.min(file.fileSize, start + file.chunkSize),
                form = new FormData(),
                data = file.file.slice(start, end);
            if (data == null || data.size == 0) {
                item.task.status = item.file.status = 'error';
                trigger('task-upload-error', item.task);
                item.file.file = null;
                self.prepareUpload();
                return;
            }
            var uploadAfter = function uploadAfter(task, file) {
                form.append('taskId', file.taskId);
                form.append('fileId', file.fileId);
                form.append('chunkIndex', file.chunkIndex);
                form.append('partInfo', file.partInfo);
                form.append('uploadId', file.uploadId);

                // 下面还写一次是因为有2种情况，必须在这里也要删除才合理，1,初始化成功准备上传，2，上传一片后上传下一片之前
                // 之所有放在构造formData之后，是因为构造formData是需要一点点时间的。避免时间差带来的偶现bug
                if (task.status === 'deleteing') {
                    self.clearTask(task);
                    return;
                }

                var apiVersion = _commonUtil2.default.getCookie('apiVersion');
                var url = '/upload/multipart';
                if (apiVersion && apiVersion === 'mamcore2.3') {
                    url = '/sflud/v1/upload/multipart';
                }
                if (mainOpts.loginToken) {
                    url += '?token=' + mainOpts.loginToken;
                }
                httpUtil.post(url, form, {
                    contentType: false,
                    processData: false
                }).then(function (res) {
                    if (task.status === 'deleteing') {
                        self.clearTask(task);
                        return;
                    }
                    res = res.data;
                    file = _.find(task.files, { fileId: res.fileId });
                    file.chunkIndex = res.chunkIndex;
                    if (res.taskStatus === 3) {
                        // 3：任务完成
                        file.progress = task.progress = 100;
                        file.surplusTime = task.surplusTime = null;
                        delete file.fileReader;
                        file.status = task.status = 'success';
                        self.prepareUpload();
                        callComplete(task).then(function () {
                            trigger('task-upload-success', task);
                        });
                    } else {
                        task.progress = calcTaskProgress(task);
                        file.progress = calcFileProgress(file);
                        file.status = task.status = 'progress';
                        trigger('task-upload-progress', task);
                        file.errorCount = 0;
                        self.upload(item);
                    }
                }, function (res) {
                    if (res.status === 401) {
                        unauthorizedTip();
                        return;
                    }
                    uploadErrorRetry(item);
                });
            };
            if (!s3) {
                getS3Client(task.ossClientInfo);
            }
            if (file.fileSize <= 5 * 1024 * 1024) // 小于5M文件不用分片上传
                {
                    s3.putObject({
                        Body: file.file,
                        Bucket: task.ossClientInfo.bucketName,
                        Key: file.relativePath.replace(/\\/g, '/')
                    }, function (err, successData) {
                        if (err) {
                            console.info(err, err.stack); // an error occurred
                            uploadErrorRetry(item);
                        } else {
                            if (successData.ETag) file.partInfo = successData.ETag.replace(/"/g, '');
                            uploadAfter(task, file);
                            console.info(successData); // successful response
                        }
                        /*
                        data = {
                            ETag: "\"6805f2cfc46c0f04559748bb039d69ae\"",
                            VersionId: "Kirh.unyZwjQ69YxcQLA8z4F5j3kJJKr"
                        }
                        */
                    });
                } else {
                if (!file.uploadId) {
                    var getUploadIdTask = initS3MultipartUpload(task.ossClientInfo, file);
                    if (getUploadIdTask) {
                        getUploadIdTask.then(function () {
                            self.upload(item);
                        });
                    }
                    return;
                }
                var params = {
                    Body: data,
                    Bucket: task.ossClientInfo.bucketName,
                    Key: file.relativePath.replace(/\\/g, '/'),
                    PartNumber: file.chunkIndex + 1,
                    UploadId: file.uploadId
                };
                s3.uploadPart(params, function (err, successData) {
                    if (err) {
                        console.info(err, err.stack); // an error occurred
                        uploadErrorRetry(item);
                    } else {
                        // if (params.PartNumber == file.chunkTotal) {
                        //     s3.completeMultipartUpload({ Bucket: params.Bucket, Key: params.Key, UploadId: params.UploadId }, function (err, data) {
                        //         if (err) {
                        //             console.info(err, err.stack); // an error occurred
                        //             uploadErrorRetry(item);
                        //         } else {
                        //             console.log('upload complete', data);
                        //             uploadAfter(task, file);
                        //         }
                        //     });
                        // } else {
                        if (successData.ETag) file.partInfo = successData.ETag.replace(/"/g, '');
                        uploadAfter(task, file);
                        console.info(successData); // successful response
                        // }
                    }
                });
            }
        } else {
            if (file.hasOwnProperty('errorCount')) {
                delete uploadRetryTimer[file.fileId];
                delete file.errorCount;
            }
            delete file.fileReader;
            file.surplusTime = null;
            file.progress = 100;
            file.status = 'success';
            task.progress = calcTaskProgress(task);
            trigger('task-upload-success', task);
            self.prepareUpload();
        }
    };

    //继续上传
    this.continueUpload = function (task, file, errCallback) {
        if (file.file == null) self.openFileSelector(function (result) {
            var md5 = self.calcFileMd5(result[0].file);
            if (file.fileMd5 !== md5) {
                _commonUtil2.default.prompt(l('upload.fileDiffer', '选择的文件不一致，请重新上传'));
                if (errCallback) {
                    errCallback.apply(window, [l('upload.fileDiffer', '选择的文件不一致，请重新上传')]);
                }
                return;
            }
            file.file = result[0].file;
            task.status = file.status = 'prepared';
            self.prepareUpload();
        }, false);else {
            if (task.inited) {
                task.status = file.status = 'prepared';
                self.prepareUpload();
            } else {
                initTask(task);
            }
        }
    };

    //获取未完成需要断点续传的任务
    this.getUnfinishedTask = function (relationId, relationContentType, targetType) {
        var deferred = $.Deferred();
        if (relationId == null) relationId = '';
        if (!_.isNumber(relationContentType)) relationContentType = 0;
        if (!_.isNumber(relationContentType)) relationContentType = 1;
        var apiVersion = _commonUtil2.default.getCookie('apiVersion');
        var url = '/upload/get-unfinished-task?relationId=' + relationId + '&relationContentType=' + relationContentType + '&targetType=' + targetType + (mainOpts.loginToken ? '&token=' + mainOpts.loginToken : '');
        if (apiVersion && apiVersion === 'mamcore2.3') {
            url = '/sflud/v1/upload/unfinished-task?relationId=' + relationId + '&relationContentType=' + relationContentType + '&targetType=' + targetType + (mainOpts.loginToken ? '&token=' + mainOpts.loginToken : '');
        }
        httpUtil.get(getConfig().server + url).then(function (res) {
            var result = [];
            _.forEach(res.data, function (task) {
                task.status = 'error';
                task.inited = true;
                task.sizeTotal = task.chunkFinished = task.chunkTotal = 0;
                _.forEach(task.files, function (file) {
                    file.fileSizeString = _commonUtil2.default.formatSize(file.fileSize);
                    file.progress = calcFileProgress(file);
                    file.status = file.progress === 100 ? 'success' : 'error';
                    task.sizeTotal += file.fileSize;
                    task.chunkTotal += file.chunkTotal;
                });
                task.progress = calcTaskProgress(task);
                task.sizeTotalString = _commonUtil2.default.formatSize(task.sizeTotal);
                self.tasks.push(task);
                result.push(task);
            });
            deferred.resolve(result);
        }, function (res) {
            deferred.reject(res);
        });
        return deferred;
    };

    //是否能删除任务
    this.canDeleteTask = function (task) {
        if (task == null) return false;
        if (task.status == 'init') return false;
        if (task.status == 'deleteing') return false;
        if (task.status == 'progress' && task.chunkFinished == task.chunkTotal - 1) return false;
        return true;
    };

    this.removeTask = function (task) {
        _.remove(self.tasks, function (ta) {
            return ta.taskId === task.taskId;
        });
    };

    //删除任务
    this.deleteTask = function (task) {
        if (!this.canDeleteTask(task)) return;
        if (task.inited === true) {
            _.forEach(task.files, function (file) {
                if (uploadRetryTimer[file.fileId] != null) {
                    timeout.cancel(uploadRetryTimer[file.fileId]);
                    delete uploadRetryTimer[file.fileId];
                }
            });
            switch (task.status) {
                case 'progress':
                    task.status = 'deleteing';
                    return;
                case 'prepared':
                case 'error':
                    self.clearTask(task);
                    return;
                default:
                    self.removeTask(task);
                    trigger('task-delete-success', task);
            }
        } else {
            self.removeTask(task);
            trigger('task-delete-success', task);
        }
    };

    //清理任务
    this.clearTask = function (task) {
        var fileAndTag = {};
        if (task.files && task.files.length > 0) {
            _.forEach(task.files, function (file) {
                fileAndTag[file.fileId] = file.uploadId;
            });
        }
        var apiVersion = _commonUtil2.default.getCookie('apiVersion');
        var url = '/upload/delete-task';
        if (apiVersion && apiVersion === 'mamcore2.3') {
            url = '/sflud/v1/upload/task';
        }
        if (mainOpts.loginToken) {
            url += '?token=' + mainOpts.loginToken;
        }
        if (!apiVersion || apiVersion === 'mamcore2.1') {
            httpUtil.post(getConfig().server + url, { taskId: task.taskId, fileAndTag: fileAndTag }).then(function (res) {
                self.removeTask(task);
                trigger('task-delete-success', task);
                self.prepareUpload();
            }, function (res) {
                trigger('task-delete-error', [task, res]);
            });
        } else {
            httpUtil.delete(getConfig().server + url, { taskId: task.taskId, fileAndTag: fileAndTag }).then(function (res) {
                self.removeTask(task);
                trigger('task-delete-success', task);
                self.prepareUpload();
            }, function (res) {
                trigger('task-delete-error', [task, res]);
            });
        }
    };

    //计算文件MD5
    this.calcFileMd5 = function (file) {
        //只是简单计算一下，没办法整个文件进行MD5计算。现在这种方式肯定是会有bug的。只能应对大部分情况。
        return sparkmd5.hash(file.name + file.size + getFileTime(file));
    };

    //获取一个任务
    //this.getTask = function (taskId) {
    //    return _.find(tasks, function (n) { return n.taskId === taskId });
    //}

    //根据状态获取文件，参数可传入多个状态code
    this.getFilesByStatus = function () {
        var status = [].slice.call(arguments, 0);
        var result = [];
        for (var x = 0; x < self.tasks.length; x++) {
            for (var y = 0; y < self.tasks[x].files.length; y++) {
                for (var z = 0; z < status.length; z++) {
                    if (self.tasks[x].files[y].status === status[z]) {
                        result.push({ task: self.tasks[x], file: self.tasks[x].files[y] });
                        break;
                    }
                }
            }
        }
        return result;
    };

    //计算进度
    function calcProgress(current, total) {
        if (current == total) return 100;
        var result = current / total * 100;
        if (result.toString().indexOf('.') == -1) return result;
        return result.toFixed(2);
    }

    //计算文件进度
    function calcFileProgress(file) {
        return calcProgress(file.chunkIndex, file.chunkTotal);
    }

    //计算任务进度
    function calcTaskProgress(task) {
        var count = 0;
        for (var i = 0; i < task.files.length; i++) {
            count += task.files[i].chunkIndex;
        }task.chunkFinished = count;
        return calcProgress(task.chunkFinished, task.chunkTotal);
    }

    init();
};

exports.default = web;
/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(82).Buffer))

/***/ }),
/* 82 */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(global) {/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <http://feross.org>
 * @license  MIT
 */
/* eslint-disable no-proto */



var base64 = __webpack_require__(84)
var ieee754 = __webpack_require__(85)
var isArray = __webpack_require__(86)

exports.Buffer = Buffer
exports.SlowBuffer = SlowBuffer
exports.INSPECT_MAX_BYTES = 50

/**
 * If `Buffer.TYPED_ARRAY_SUPPORT`:
 *   === true    Use Uint8Array implementation (fastest)
 *   === false   Use Object implementation (most compatible, even IE6)
 *
 * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,
 * Opera 11.6+, iOS 4.2+.
 *
 * Due to various browser bugs, sometimes the Object implementation will be used even
 * when the browser supports typed arrays.
 *
 * Note:
 *
 *   - Firefox 4-29 lacks support for adding new properties to `Uint8Array` instances,
 *     See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438.
 *
 *   - Chrome 9-10 is missing the `TypedArray.prototype.subarray` function.
 *
 *   - IE10 has a broken `TypedArray.prototype.subarray` function which returns arrays of
 *     incorrect length in some situations.

 * We detect these buggy browsers and set `Buffer.TYPED_ARRAY_SUPPORT` to `false` so they
 * get the Object implementation, which is slower but behaves correctly.
 */
Buffer.TYPED_ARRAY_SUPPORT = global.TYPED_ARRAY_SUPPORT !== undefined
  ? global.TYPED_ARRAY_SUPPORT
  : typedArraySupport()

/*
 * Export kMaxLength after typed array support is determined.
 */
exports.kMaxLength = kMaxLength()

function typedArraySupport () {
  try {
    var arr = new Uint8Array(1)
    arr.__proto__ = {__proto__: Uint8Array.prototype, foo: function () { return 42 }}
    return arr.foo() === 42 && // typed array instances can be augmented
        typeof arr.subarray === 'function' && // chrome 9-10 lack `subarray`
        arr.subarray(1, 1).byteLength === 0 // ie10 has broken `subarray`
  } catch (e) {
    return false
  }
}

function kMaxLength () {
  return Buffer.TYPED_ARRAY_SUPPORT
    ? 0x7fffffff
    : 0x3fffffff
}

function createBuffer (that, length) {
  if (kMaxLength() < length) {
    throw new RangeError('Invalid typed array length')
  }
  if (Buffer.TYPED_ARRAY_SUPPORT) {
    // Return an augmented `Uint8Array` instance, for best performance
    that = new Uint8Array(length)
    that.__proto__ = Buffer.prototype
  } else {
    // Fallback: Return an object instance of the Buffer class
    if (that === null) {
      that = new Buffer(length)
    }
    that.length = length
  }

  return that
}

/**
 * The Buffer constructor returns instances of `Uint8Array` that have their
 * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of
 * `Uint8Array`, so the returned instances will have all the node `Buffer` methods
 * and the `Uint8Array` methods. Square bracket notation works as expected -- it
 * returns a single octet.
 *
 * The `Uint8Array` prototype remains unmodified.
 */

function Buffer (arg, encodingOrOffset, length) {
  if (!Buffer.TYPED_ARRAY_SUPPORT && !(this instanceof Buffer)) {
    return new Buffer(arg, encodingOrOffset, length)
  }

  // Common case.
  if (typeof arg === 'number') {
    if (typeof encodingOrOffset === 'string') {
      throw new Error(
        'If encoding is specified then the first argument must be a string'
      )
    }
    return allocUnsafe(this, arg)
  }
  return from(this, arg, encodingOrOffset, length)
}

Buffer.poolSize = 8192 // not used by this implementation

// TODO: Legacy, not needed anymore. Remove in next major version.
Buffer._augment = function (arr) {
  arr.__proto__ = Buffer.prototype
  return arr
}

function from (that, value, encodingOrOffset, length) {
  if (typeof value === 'number') {
    throw new TypeError('"value" argument must not be a number')
  }

  if (typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer) {
    return fromArrayBuffer(that, value, encodingOrOffset, length)
  }

  if (typeof value === 'string') {
    return fromString(that, value, encodingOrOffset)
  }

  return fromObject(that, value)
}

/**
 * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError
 * if value is a number.
 * Buffer.from(str[, encoding])
 * Buffer.from(array)
 * Buffer.from(buffer)
 * Buffer.from(arrayBuffer[, byteOffset[, length]])
 **/
Buffer.from = function (value, encodingOrOffset, length) {
  return from(null, value, encodingOrOffset, length)
}

if (Buffer.TYPED_ARRAY_SUPPORT) {
  Buffer.prototype.__proto__ = Uint8Array.prototype
  Buffer.__proto__ = Uint8Array
  if (typeof Symbol !== 'undefined' && Symbol.species &&
      Buffer[Symbol.species] === Buffer) {
    // Fix subarray() in ES2016. See: https://github.com/feross/buffer/pull/97
    Object.defineProperty(Buffer, Symbol.species, {
      value: null,
      configurable: true
    })
  }
}

function assertSize (size) {
  if (typeof size !== 'number') {
    throw new TypeError('"size" argument must be a number')
  } else if (size < 0) {
    throw new RangeError('"size" argument must not be negative')
  }
}

function alloc (that, size, fill, encoding) {
  assertSize(size)
  if (size <= 0) {
    return createBuffer(that, size)
  }
  if (fill !== undefined) {
    // Only pay attention to encoding if it's a string. This
    // prevents accidentally sending in a number that would
    // be interpretted as a start offset.
    return typeof encoding === 'string'
      ? createBuffer(that, size).fill(fill, encoding)
      : createBuffer(that, size).fill(fill)
  }
  return createBuffer(that, size)
}

/**
 * Creates a new filled Buffer instance.
 * alloc(size[, fill[, encoding]])
 **/
Buffer.alloc = function (size, fill, encoding) {
  return alloc(null, size, fill, encoding)
}

function allocUnsafe (that, size) {
  assertSize(size)
  that = createBuffer(that, size < 0 ? 0 : checked(size) | 0)
  if (!Buffer.TYPED_ARRAY_SUPPORT) {
    for (var i = 0; i < size; ++i) {
      that[i] = 0
    }
  }
  return that
}

/**
 * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.
 * */
Buffer.allocUnsafe = function (size) {
  return allocUnsafe(null, size)
}
/**
 * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.
 */
Buffer.allocUnsafeSlow = function (size) {
  return allocUnsafe(null, size)
}

function fromString (that, string, encoding) {
  if (typeof encoding !== 'string' || encoding === '') {
    encoding = 'utf8'
  }

  if (!Buffer.isEncoding(encoding)) {
    throw new TypeError('"encoding" must be a valid string encoding')
  }

  var length = byteLength(string, encoding) | 0
  that = createBuffer(that, length)

  var actual = that.write(string, encoding)

  if (actual !== length) {
    // Writing a hex string, for example, that contains invalid characters will
    // cause everything after the first invalid character to be ignored. (e.g.
    // 'abxxcd' will be treated as 'ab')
    that = that.slice(0, actual)
  }

  return that
}

function fromArrayLike (that, array) {
  var length = array.length < 0 ? 0 : checked(array.length) | 0
  that = createBuffer(that, length)
  for (var i = 0; i < length; i += 1) {
    that[i] = array[i] & 255
  }
  return that
}

function fromArrayBuffer (that, array, byteOffset, length) {
  array.byteLength // this throws if `array` is not a valid ArrayBuffer

  if (byteOffset < 0 || array.byteLength < byteOffset) {
    throw new RangeError('\'offset\' is out of bounds')
  }

  if (array.byteLength < byteOffset + (length || 0)) {
    throw new RangeError('\'length\' is out of bounds')
  }

  if (byteOffset === undefined && length === undefined) {
    array = new Uint8Array(array)
  } else if (length === undefined) {
    array = new Uint8Array(array, byteOffset)
  } else {
    array = new Uint8Array(array, byteOffset, length)
  }

  if (Buffer.TYPED_ARRAY_SUPPORT) {
    // Return an augmented `Uint8Array` instance, for best performance
    that = array
    that.__proto__ = Buffer.prototype
  } else {
    // Fallback: Return an object instance of the Buffer class
    that = fromArrayLike(that, array)
  }
  return that
}

function fromObject (that, obj) {
  if (Buffer.isBuffer(obj)) {
    var len = checked(obj.length) | 0
    that = createBuffer(that, len)

    if (that.length === 0) {
      return that
    }

    obj.copy(that, 0, 0, len)
    return that
  }

  if (obj) {
    if ((typeof ArrayBuffer !== 'undefined' &&
        obj.buffer instanceof ArrayBuffer) || 'length' in obj) {
      if (typeof obj.length !== 'number' || isnan(obj.length)) {
        return createBuffer(that, 0)
      }
      return fromArrayLike(that, obj)
    }

    if (obj.type === 'Buffer' && isArray(obj.data)) {
      return fromArrayLike(that, obj.data)
    }
  }

  throw new TypeError('First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.')
}

function checked (length) {
  // Note: cannot use `length < kMaxLength()` here because that fails when
  // length is NaN (which is otherwise coerced to zero.)
  if (length >= kMaxLength()) {
    throw new RangeError('Attempt to allocate Buffer larger than maximum ' +
                         'size: 0x' + kMaxLength().toString(16) + ' bytes')
  }
  return length | 0
}

function SlowBuffer (length) {
  if (+length != length) { // eslint-disable-line eqeqeq
    length = 0
  }
  return Buffer.alloc(+length)
}

Buffer.isBuffer = function isBuffer (b) {
  return !!(b != null && b._isBuffer)
}

Buffer.compare = function compare (a, b) {
  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {
    throw new TypeError('Arguments must be Buffers')
  }

  if (a === b) return 0

  var x = a.length
  var y = b.length

  for (var i = 0, len = Math.min(x, y); i < len; ++i) {
    if (a[i] !== b[i]) {
      x = a[i]
      y = b[i]
      break
    }
  }

  if (x < y) return -1
  if (y < x) return 1
  return 0
}

Buffer.isEncoding = function isEncoding (encoding) {
  switch (String(encoding).toLowerCase()) {
    case 'hex':
    case 'utf8':
    case 'utf-8':
    case 'ascii':
    case 'latin1':
    case 'binary':
    case 'base64':
    case 'ucs2':
    case 'ucs-2':
    case 'utf16le':
    case 'utf-16le':
      return true
    default:
      return false
  }
}

Buffer.concat = function concat (list, length) {
  if (!isArray(list)) {
    throw new TypeError('"list" argument must be an Array of Buffers')
  }

  if (list.length === 0) {
    return Buffer.alloc(0)
  }

  var i
  if (length === undefined) {
    length = 0
    for (i = 0; i < list.length; ++i) {
      length += list[i].length
    }
  }

  var buffer = Buffer.allocUnsafe(length)
  var pos = 0
  for (i = 0; i < list.length; ++i) {
    var buf = list[i]
    if (!Buffer.isBuffer(buf)) {
      throw new TypeError('"list" argument must be an Array of Buffers')
    }
    buf.copy(buffer, pos)
    pos += buf.length
  }
  return buffer
}

function byteLength (string, encoding) {
  if (Buffer.isBuffer(string)) {
    return string.length
  }
  if (typeof ArrayBuffer !== 'undefined' && typeof ArrayBuffer.isView === 'function' &&
      (ArrayBuffer.isView(string) || string instanceof ArrayBuffer)) {
    return string.byteLength
  }
  if (typeof string !== 'string') {
    string = '' + string
  }

  var len = string.length
  if (len === 0) return 0

  // Use a for loop to avoid recursion
  var loweredCase = false
  for (;;) {
    switch (encoding) {
      case 'ascii':
      case 'latin1':
      case 'binary':
        return len
      case 'utf8':
      case 'utf-8':
      case undefined:
        return utf8ToBytes(string).length
      case 'ucs2':
      case 'ucs-2':
      case 'utf16le':
      case 'utf-16le':
        return len * 2
      case 'hex':
        return len >>> 1
      case 'base64':
        return base64ToBytes(string).length
      default:
        if (loweredCase) return utf8ToBytes(string).length // assume utf8
        encoding = ('' + encoding).toLowerCase()
        loweredCase = true
    }
  }
}
Buffer.byteLength = byteLength

function slowToString (encoding, start, end) {
  var loweredCase = false

  // No need to verify that "this.length <= MAX_UINT32" since it's a read-only
  // property of a typed array.

  // This behaves neither like String nor Uint8Array in that we set start/end
  // to their upper/lower bounds if the value passed is out of range.
  // undefined is handled specially as per ECMA-262 6th Edition,
  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.
  if (start === undefined || start < 0) {
    start = 0
  }
  // Return early if start > this.length. Done here to prevent potential uint32
  // coercion fail below.
  if (start > this.length) {
    return ''
  }

  if (end === undefined || end > this.length) {
    end = this.length
  }

  if (end <= 0) {
    return ''
  }

  // Force coersion to uint32. This will also coerce falsey/NaN values to 0.
  end >>>= 0
  start >>>= 0

  if (end <= start) {
    return ''
  }

  if (!encoding) encoding = 'utf8'

  while (true) {
    switch (encoding) {
      case 'hex':
        return hexSlice(this, start, end)

      case 'utf8':
      case 'utf-8':
        return utf8Slice(this, start, end)

      case 'ascii':
        return asciiSlice(this, start, end)

      case 'latin1':
      case 'binary':
        return latin1Slice(this, start, end)

      case 'base64':
        return base64Slice(this, start, end)

      case 'ucs2':
      case 'ucs-2':
      case 'utf16le':
      case 'utf-16le':
        return utf16leSlice(this, start, end)

      default:
        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)
        encoding = (encoding + '').toLowerCase()
        loweredCase = true
    }
  }
}

// The property is used by `Buffer.isBuffer` and `is-buffer` (in Safari 5-7) to detect
// Buffer instances.
Buffer.prototype._isBuffer = true

function swap (b, n, m) {
  var i = b[n]
  b[n] = b[m]
  b[m] = i
}

Buffer.prototype.swap16 = function swap16 () {
  var len = this.length
  if (len % 2 !== 0) {
    throw new RangeError('Buffer size must be a multiple of 16-bits')
  }
  for (var i = 0; i < len; i += 2) {
    swap(this, i, i + 1)
  }
  return this
}

Buffer.prototype.swap32 = function swap32 () {
  var len = this.length
  if (len % 4 !== 0) {
    throw new RangeError('Buffer size must be a multiple of 32-bits')
  }
  for (var i = 0; i < len; i += 4) {
    swap(this, i, i + 3)
    swap(this, i + 1, i + 2)
  }
  return this
}

Buffer.prototype.swap64 = function swap64 () {
  var len = this.length
  if (len % 8 !== 0) {
    throw new RangeError('Buffer size must be a multiple of 64-bits')
  }
  for (var i = 0; i < len; i += 8) {
    swap(this, i, i + 7)
    swap(this, i + 1, i + 6)
    swap(this, i + 2, i + 5)
    swap(this, i + 3, i + 4)
  }
  return this
}

Buffer.prototype.toString = function toString () {
  var length = this.length | 0
  if (length === 0) return ''
  if (arguments.length === 0) return utf8Slice(this, 0, length)
  return slowToString.apply(this, arguments)
}

Buffer.prototype.equals = function equals (b) {
  if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer')
  if (this === b) return true
  return Buffer.compare(this, b) === 0
}

Buffer.prototype.inspect = function inspect () {
  var str = ''
  var max = exports.INSPECT_MAX_BYTES
  if (this.length > 0) {
    str = this.toString('hex', 0, max).match(/.{2}/g).join(' ')
    if (this.length > max) str += ' ... '
  }
  return '<Buffer ' + str + '>'
}

Buffer.prototype.compare = function compare (target, start, end, thisStart, thisEnd) {
  if (!Buffer.isBuffer(target)) {
    throw new TypeError('Argument must be a Buffer')
  }

  if (start === undefined) {
    start = 0
  }
  if (end === undefined) {
    end = target ? target.length : 0
  }
  if (thisStart === undefined) {
    thisStart = 0
  }
  if (thisEnd === undefined) {
    thisEnd = this.length
  }

  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {
    throw new RangeError('out of range index')
  }

  if (thisStart >= thisEnd && start >= end) {
    return 0
  }
  if (thisStart >= thisEnd) {
    return -1
  }
  if (start >= end) {
    return 1
  }

  start >>>= 0
  end >>>= 0
  thisStart >>>= 0
  thisEnd >>>= 0

  if (this === target) return 0

  var x = thisEnd - thisStart
  var y = end - start
  var len = Math.min(x, y)

  var thisCopy = this.slice(thisStart, thisEnd)
  var targetCopy = target.slice(start, end)

  for (var i = 0; i < len; ++i) {
    if (thisCopy[i] !== targetCopy[i]) {
      x = thisCopy[i]
      y = targetCopy[i]
      break
    }
  }

  if (x < y) return -1
  if (y < x) return 1
  return 0
}

// Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,
// OR the last index of `val` in `buffer` at offset <= `byteOffset`.
//
// Arguments:
// - buffer - a Buffer to search
// - val - a string, Buffer, or number
// - byteOffset - an index into `buffer`; will be clamped to an int32
// - encoding - an optional encoding, relevant is val is a string
// - dir - true for indexOf, false for lastIndexOf
function bidirectionalIndexOf (buffer, val, byteOffset, encoding, dir) {
  // Empty buffer means no match
  if (buffer.length === 0) return -1

  // Normalize byteOffset
  if (typeof byteOffset === 'string') {
    encoding = byteOffset
    byteOffset = 0
  } else if (byteOffset > 0x7fffffff) {
    byteOffset = 0x7fffffff
  } else if (byteOffset < -0x80000000) {
    byteOffset = -0x80000000
  }
  byteOffset = +byteOffset  // Coerce to Number.
  if (isNaN(byteOffset)) {
    // byteOffset: it it's undefined, null, NaN, "foo", etc, search whole buffer
    byteOffset = dir ? 0 : (buffer.length - 1)
  }

  // Normalize byteOffset: negative offsets start from the end of the buffer
  if (byteOffset < 0) byteOffset = buffer.length + byteOffset
  if (byteOffset >= buffer.length) {
    if (dir) return -1
    else byteOffset = buffer.length - 1
  } else if (byteOffset < 0) {
    if (dir) byteOffset = 0
    else return -1
  }

  // Normalize val
  if (typeof val === 'string') {
    val = Buffer.from(val, encoding)
  }

  // Finally, search either indexOf (if dir is true) or lastIndexOf
  if (Buffer.isBuffer(val)) {
    // Special case: looking for empty string/buffer always fails
    if (val.length === 0) {
      return -1
    }
    return arrayIndexOf(buffer, val, byteOffset, encoding, dir)
  } else if (typeof val === 'number') {
    val = val & 0xFF // Search for a byte value [0-255]
    if (Buffer.TYPED_ARRAY_SUPPORT &&
        typeof Uint8Array.prototype.indexOf === 'function') {
      if (dir) {
        return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset)
      } else {
        return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset)
      }
    }
    return arrayIndexOf(buffer, [ val ], byteOffset, encoding, dir)
  }

  throw new TypeError('val must be string, number or Buffer')
}

function arrayIndexOf (arr, val, byteOffset, encoding, dir) {
  var indexSize = 1
  var arrLength = arr.length
  var valLength = val.length

  if (encoding !== undefined) {
    encoding = String(encoding).toLowerCase()
    if (encoding === 'ucs2' || encoding === 'ucs-2' ||
        encoding === 'utf16le' || encoding === 'utf-16le') {
      if (arr.length < 2 || val.length < 2) {
        return -1
      }
      indexSize = 2
      arrLength /= 2
      valLength /= 2
      byteOffset /= 2
    }
  }

  function read (buf, i) {
    if (indexSize === 1) {
      return buf[i]
    } else {
      return buf.readUInt16BE(i * indexSize)
    }
  }

  var i
  if (dir) {
    var foundIndex = -1
    for (i = byteOffset; i < arrLength; i++) {
      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {
        if (foundIndex === -1) foundIndex = i
        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize
      } else {
        if (foundIndex !== -1) i -= i - foundIndex
        foundIndex = -1
      }
    }
  } else {
    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength
    for (i = byteOffset; i >= 0; i--) {
      var found = true
      for (var j = 0; j < valLength; j++) {
        if (read(arr, i + j) !== read(val, j)) {
          found = false
          break
        }
      }
      if (found) return i
    }
  }

  return -1
}

Buffer.prototype.includes = function includes (val, byteOffset, encoding) {
  return this.indexOf(val, byteOffset, encoding) !== -1
}

Buffer.prototype.indexOf = function indexOf (val, byteOffset, encoding) {
  return bidirectionalIndexOf(this, val, byteOffset, encoding, true)
}

Buffer.prototype.lastIndexOf = function lastIndexOf (val, byteOffset, encoding) {
  return bidirectionalIndexOf(this, val, byteOffset, encoding, false)
}

function hexWrite (buf, string, offset, length) {
  offset = Number(offset) || 0
  var remaining = buf.length - offset
  if (!length) {
    length = remaining
  } else {
    length = Number(length)
    if (length > remaining) {
      length = remaining
    }
  }

  // must be an even number of digits
  var strLen = string.length
  if (strLen % 2 !== 0) throw new TypeError('Invalid hex string')

  if (length > strLen / 2) {
    length = strLen / 2
  }
  for (var i = 0; i < length; ++i) {
    var parsed = parseInt(string.substr(i * 2, 2), 16)
    if (isNaN(parsed)) return i
    buf[offset + i] = parsed
  }
  return i
}

function utf8Write (buf, string, offset, length) {
  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length)
}

function asciiWrite (buf, string, offset, length) {
  return blitBuffer(asciiToBytes(string), buf, offset, length)
}

function latin1Write (buf, string, offset, length) {
  return asciiWrite(buf, string, offset, length)
}

function base64Write (buf, string, offset, length) {
  return blitBuffer(base64ToBytes(string), buf, offset, length)
}

function ucs2Write (buf, string, offset, length) {
  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length)
}

Buffer.prototype.write = function write (string, offset, length, encoding) {
  // Buffer#write(string)
  if (offset === undefined) {
    encoding = 'utf8'
    length = this.length
    offset = 0
  // Buffer#write(string, encoding)
  } else if (length === undefined && typeof offset === 'string') {
    encoding = offset
    length = this.length
    offset = 0
  // Buffer#write(string, offset[, length][, encoding])
  } else if (isFinite(offset)) {
    offset = offset | 0
    if (isFinite(length)) {
      length = length | 0
      if (encoding === undefined) encoding = 'utf8'
    } else {
      encoding = length
      length = undefined
    }
  // legacy write(string, encoding, offset, length) - remove in v0.13
  } else {
    throw new Error(
      'Buffer.write(string, encoding, offset[, length]) is no longer supported'
    )
  }

  var remaining = this.length - offset
  if (length === undefined || length > remaining) length = remaining

  if ((string.length > 0 && (length < 0 || offset < 0)) || offset > this.length) {
    throw new RangeError('Attempt to write outside buffer bounds')
  }

  if (!encoding) encoding = 'utf8'

  var loweredCase = false
  for (;;) {
    switch (encoding) {
      case 'hex':
        return hexWrite(this, string, offset, length)

      case 'utf8':
      case 'utf-8':
        return utf8Write(this, string, offset, length)

      case 'ascii':
        return asciiWrite(this, string, offset, length)

      case 'latin1':
      case 'binary':
        return latin1Write(this, string, offset, length)

      case 'base64':
        // Warning: maxLength not taken into account in base64Write
        return base64Write(this, string, offset, length)

      case 'ucs2':
      case 'ucs-2':
      case 'utf16le':
      case 'utf-16le':
        return ucs2Write(this, string, offset, length)

      default:
        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)
        encoding = ('' + encoding).toLowerCase()
        loweredCase = true
    }
  }
}

Buffer.prototype.toJSON = function toJSON () {
  return {
    type: 'Buffer',
    data: Array.prototype.slice.call(this._arr || this, 0)
  }
}

function base64Slice (buf, start, end) {
  if (start === 0 && end === buf.length) {
    return base64.fromByteArray(buf)
  } else {
    return base64.fromByteArray(buf.slice(start, end))
  }
}

function utf8Slice (buf, start, end) {
  end = Math.min(buf.length, end)
  var res = []

  var i = start
  while (i < end) {
    var firstByte = buf[i]
    var codePoint = null
    var bytesPerSequence = (firstByte > 0xEF) ? 4
      : (firstByte > 0xDF) ? 3
      : (firstByte > 0xBF) ? 2
      : 1

    if (i + bytesPerSequence <= end) {
      var secondByte, thirdByte, fourthByte, tempCodePoint

      switch (bytesPerSequence) {
        case 1:
          if (firstByte < 0x80) {
            codePoint = firstByte
          }
          break
        case 2:
          secondByte = buf[i + 1]
          if ((secondByte & 0xC0) === 0x80) {
            tempCodePoint = (firstByte & 0x1F) << 0x6 | (secondByte & 0x3F)
            if (tempCodePoint > 0x7F) {
              codePoint = tempCodePoint
            }
          }
          break
        case 3:
          secondByte = buf[i + 1]
          thirdByte = buf[i + 2]
          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {
            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | (thirdByte & 0x3F)
            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {
              codePoint = tempCodePoint
            }
          }
          break
        case 4:
          secondByte = buf[i + 1]
          thirdByte = buf[i + 2]
          fourthByte = buf[i + 3]
          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {
            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | (fourthByte & 0x3F)
            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {
              codePoint = tempCodePoint
            }
          }
      }
    }

    if (codePoint === null) {
      // we did not generate a valid codePoint so insert a
      // replacement char (U+FFFD) and advance only 1 byte
      codePoint = 0xFFFD
      bytesPerSequence = 1
    } else if (codePoint > 0xFFFF) {
      // encode to utf16 (surrogate pair dance)
      codePoint -= 0x10000
      res.push(codePoint >>> 10 & 0x3FF | 0xD800)
      codePoint = 0xDC00 | codePoint & 0x3FF
    }

    res.push(codePoint)
    i += bytesPerSequence
  }

  return decodeCodePointsArray(res)
}

// Based on http://stackoverflow.com/a/22747272/680742, the browser with
// the lowest limit is Chrome, with 0x10000 args.
// We go 1 magnitude less, for safety
var MAX_ARGUMENTS_LENGTH = 0x1000

function decodeCodePointsArray (codePoints) {
  var len = codePoints.length
  if (len <= MAX_ARGUMENTS_LENGTH) {
    return String.fromCharCode.apply(String, codePoints) // avoid extra slice()
  }

  // Decode in chunks to avoid "call stack size exceeded".
  var res = ''
  var i = 0
  while (i < len) {
    res += String.fromCharCode.apply(
      String,
      codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH)
    )
  }
  return res
}

function asciiSlice (buf, start, end) {
  var ret = ''
  end = Math.min(buf.length, end)

  for (var i = start; i < end; ++i) {
    ret += String.fromCharCode(buf[i] & 0x7F)
  }
  return ret
}

function latin1Slice (buf, start, end) {
  var ret = ''
  end = Math.min(buf.length, end)

  for (var i = start; i < end; ++i) {
    ret += String.fromCharCode(buf[i])
  }
  return ret
}

function hexSlice (buf, start, end) {
  var len = buf.length

  if (!start || start < 0) start = 0
  if (!end || end < 0 || end > len) end = len

  var out = ''
  for (var i = start; i < end; ++i) {
    out += toHex(buf[i])
  }
  return out
}

function utf16leSlice (buf, start, end) {
  var bytes = buf.slice(start, end)
  var res = ''
  for (var i = 0; i < bytes.length; i += 2) {
    res += String.fromCharCode(bytes[i] + bytes[i + 1] * 256)
  }
  return res
}

Buffer.prototype.slice = function slice (start, end) {
  var len = this.length
  start = ~~start
  end = end === undefined ? len : ~~end

  if (start < 0) {
    start += len
    if (start < 0) start = 0
  } else if (start > len) {
    start = len
  }

  if (end < 0) {
    end += len
    if (end < 0) end = 0
  } else if (end > len) {
    end = len
  }

  if (end < start) end = start

  var newBuf
  if (Buffer.TYPED_ARRAY_SUPPORT) {
    newBuf = this.subarray(start, end)
    newBuf.__proto__ = Buffer.prototype
  } else {
    var sliceLen = end - start
    newBuf = new Buffer(sliceLen, undefined)
    for (var i = 0; i < sliceLen; ++i) {
      newBuf[i] = this[i + start]
    }
  }

  return newBuf
}

/*
 * Need to make sure that buffer isn't trying to write out of bounds.
 */
function checkOffset (offset, ext, length) {
  if ((offset % 1) !== 0 || offset < 0) throw new RangeError('offset is not uint')
  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length')
}

Buffer.prototype.readUIntLE = function readUIntLE (offset, byteLength, noAssert) {
  offset = offset | 0
  byteLength = byteLength | 0
  if (!noAssert) checkOffset(offset, byteLength, this.length)

  var val = this[offset]
  var mul = 1
  var i = 0
  while (++i < byteLength && (mul *= 0x100)) {
    val += this[offset + i] * mul
  }

  return val
}

Buffer.prototype.readUIntBE = function readUIntBE (offset, byteLength, noAssert) {
  offset = offset | 0
  byteLength = byteLength | 0
  if (!noAssert) {
    checkOffset(offset, byteLength, this.length)
  }

  var val = this[offset + --byteLength]
  var mul = 1
  while (byteLength > 0 && (mul *= 0x100)) {
    val += this[offset + --byteLength] * mul
  }

  return val
}

Buffer.prototype.readUInt8 = function readUInt8 (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 1, this.length)
  return this[offset]
}

Buffer.prototype.readUInt16LE = function readUInt16LE (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 2, this.length)
  return this[offset] | (this[offset + 1] << 8)
}

Buffer.prototype.readUInt16BE = function readUInt16BE (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 2, this.length)
  return (this[offset] << 8) | this[offset + 1]
}

Buffer.prototype.readUInt32LE = function readUInt32LE (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 4, this.length)

  return ((this[offset]) |
      (this[offset + 1] << 8) |
      (this[offset + 2] << 16)) +
      (this[offset + 3] * 0x1000000)
}

Buffer.prototype.readUInt32BE = function readUInt32BE (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 4, this.length)

  return (this[offset] * 0x1000000) +
    ((this[offset + 1] << 16) |
    (this[offset + 2] << 8) |
    this[offset + 3])
}

Buffer.prototype.readIntLE = function readIntLE (offset, byteLength, noAssert) {
  offset = offset | 0
  byteLength = byteLength | 0
  if (!noAssert) checkOffset(offset, byteLength, this.length)

  var val = this[offset]
  var mul = 1
  var i = 0
  while (++i < byteLength && (mul *= 0x100)) {
    val += this[offset + i] * mul
  }
  mul *= 0x80

  if (val >= mul) val -= Math.pow(2, 8 * byteLength)

  return val
}

Buffer.prototype.readIntBE = function readIntBE (offset, byteLength, noAssert) {
  offset = offset | 0
  byteLength = byteLength | 0
  if (!noAssert) checkOffset(offset, byteLength, this.length)

  var i = byteLength
  var mul = 1
  var val = this[offset + --i]
  while (i > 0 && (mul *= 0x100)) {
    val += this[offset + --i] * mul
  }
  mul *= 0x80

  if (val >= mul) val -= Math.pow(2, 8 * byteLength)

  return val
}

Buffer.prototype.readInt8 = function readInt8 (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 1, this.length)
  if (!(this[offset] & 0x80)) return (this[offset])
  return ((0xff - this[offset] + 1) * -1)
}

Buffer.prototype.readInt16LE = function readInt16LE (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 2, this.length)
  var val = this[offset] | (this[offset + 1] << 8)
  return (val & 0x8000) ? val | 0xFFFF0000 : val
}

Buffer.prototype.readInt16BE = function readInt16BE (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 2, this.length)
  var val = this[offset + 1] | (this[offset] << 8)
  return (val & 0x8000) ? val | 0xFFFF0000 : val
}

Buffer.prototype.readInt32LE = function readInt32LE (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 4, this.length)

  return (this[offset]) |
    (this[offset + 1] << 8) |
    (this[offset + 2] << 16) |
    (this[offset + 3] << 24)
}

Buffer.prototype.readInt32BE = function readInt32BE (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 4, this.length)

  return (this[offset] << 24) |
    (this[offset + 1] << 16) |
    (this[offset + 2] << 8) |
    (this[offset + 3])
}

Buffer.prototype.readFloatLE = function readFloatLE (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 4, this.length)
  return ieee754.read(this, offset, true, 23, 4)
}

Buffer.prototype.readFloatBE = function readFloatBE (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 4, this.length)
  return ieee754.read(this, offset, false, 23, 4)
}

Buffer.prototype.readDoubleLE = function readDoubleLE (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 8, this.length)
  return ieee754.read(this, offset, true, 52, 8)
}

Buffer.prototype.readDoubleBE = function readDoubleBE (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 8, this.length)
  return ieee754.read(this, offset, false, 52, 8)
}

function checkInt (buf, value, offset, ext, max, min) {
  if (!Buffer.isBuffer(buf)) throw new TypeError('"buffer" argument must be a Buffer instance')
  if (value > max || value < min) throw new RangeError('"value" argument is out of bounds')
  if (offset + ext > buf.length) throw new RangeError('Index out of range')
}

Buffer.prototype.writeUIntLE = function writeUIntLE (value, offset, byteLength, noAssert) {
  value = +value
  offset = offset | 0
  byteLength = byteLength | 0
  if (!noAssert) {
    var maxBytes = Math.pow(2, 8 * byteLength) - 1
    checkInt(this, value, offset, byteLength, maxBytes, 0)
  }

  var mul = 1
  var i = 0
  this[offset] = value & 0xFF
  while (++i < byteLength && (mul *= 0x100)) {
    this[offset + i] = (value / mul) & 0xFF
  }

  return offset + byteLength
}

Buffer.prototype.writeUIntBE = function writeUIntBE (value, offset, byteLength, noAssert) {
  value = +value
  offset = offset | 0
  byteLength = byteLength | 0
  if (!noAssert) {
    var maxBytes = Math.pow(2, 8 * byteLength) - 1
    checkInt(this, value, offset, byteLength, maxBytes, 0)
  }

  var i = byteLength - 1
  var mul = 1
  this[offset + i] = value & 0xFF
  while (--i >= 0 && (mul *= 0x100)) {
    this[offset + i] = (value / mul) & 0xFF
  }

  return offset + byteLength
}

Buffer.prototype.writeUInt8 = function writeUInt8 (value, offset, noAssert) {
  value = +value
  offset = offset | 0
  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0)
  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value)
  this[offset] = (value & 0xff)
  return offset + 1
}

function objectWriteUInt16 (buf, value, offset, littleEndian) {
  if (value < 0) value = 0xffff + value + 1
  for (var i = 0, j = Math.min(buf.length - offset, 2); i < j; ++i) {
    buf[offset + i] = (value & (0xff << (8 * (littleEndian ? i : 1 - i)))) >>>
      (littleEndian ? i : 1 - i) * 8
  }
}

Buffer.prototype.writeUInt16LE = function writeUInt16LE (value, offset, noAssert) {
  value = +value
  offset = offset | 0
  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)
  if (Buffer.TYPED_ARRAY_SUPPORT) {
    this[offset] = (value & 0xff)
    this[offset + 1] = (value >>> 8)
  } else {
    objectWriteUInt16(this, value, offset, true)
  }
  return offset + 2
}

Buffer.prototype.writeUInt16BE = function writeUInt16BE (value, offset, noAssert) {
  value = +value
  offset = offset | 0
  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)
  if (Buffer.TYPED_ARRAY_SUPPORT) {
    this[offset] = (value >>> 8)
    this[offset + 1] = (value & 0xff)
  } else {
    objectWriteUInt16(this, value, offset, false)
  }
  return offset + 2
}

function objectWriteUInt32 (buf, value, offset, littleEndian) {
  if (value < 0) value = 0xffffffff + value + 1
  for (var i = 0, j = Math.min(buf.length - offset, 4); i < j; ++i) {
    buf[offset + i] = (value >>> (littleEndian ? i : 3 - i) * 8) & 0xff
  }
}

Buffer.prototype.writeUInt32LE = function writeUInt32LE (value, offset, noAssert) {
  value = +value
  offset = offset | 0
  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)
  if (Buffer.TYPED_ARRAY_SUPPORT) {
    this[offset + 3] = (value >>> 24)
    this[offset + 2] = (value >>> 16)
    this[offset + 1] = (value >>> 8)
    this[offset] = (value & 0xff)
  } else {
    objectWriteUInt32(this, value, offset, true)
  }
  return offset + 4
}

Buffer.prototype.writeUInt32BE = function writeUInt32BE (value, offset, noAssert) {
  value = +value
  offset = offset | 0
  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)
  if (Buffer.TYPED_ARRAY_SUPPORT) {
    this[offset] = (value >>> 24)
    this[offset + 1] = (value >>> 16)
    this[offset + 2] = (value >>> 8)
    this[offset + 3] = (value & 0xff)
  } else {
    objectWriteUInt32(this, value, offset, false)
  }
  return offset + 4
}

Buffer.prototype.writeIntLE = function writeIntLE (value, offset, byteLength, noAssert) {
  value = +value
  offset = offset | 0
  if (!noAssert) {
    var limit = Math.pow(2, 8 * byteLength - 1)

    checkInt(this, value, offset, byteLength, limit - 1, -limit)
  }

  var i = 0
  var mul = 1
  var sub = 0
  this[offset] = value & 0xFF
  while (++i < byteLength && (mul *= 0x100)) {
    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {
      sub = 1
    }
    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF
  }

  return offset + byteLength
}

Buffer.prototype.writeIntBE = function writeIntBE (value, offset, byteLength, noAssert) {
  value = +value
  offset = offset | 0
  if (!noAssert) {
    var limit = Math.pow(2, 8 * byteLength - 1)

    checkInt(this, value, offset, byteLength, limit - 1, -limit)
  }

  var i = byteLength - 1
  var mul = 1
  var sub = 0
  this[offset + i] = value & 0xFF
  while (--i >= 0 && (mul *= 0x100)) {
    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {
      sub = 1
    }
    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF
  }

  return offset + byteLength
}

Buffer.prototype.writeInt8 = function writeInt8 (value, offset, noAssert) {
  value = +value
  offset = offset | 0
  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80)
  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value)
  if (value < 0) value = 0xff + value + 1
  this[offset] = (value & 0xff)
  return offset + 1
}

Buffer.prototype.writeInt16LE = function writeInt16LE (value, offset, noAssert) {
  value = +value
  offset = offset | 0
  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)
  if (Buffer.TYPED_ARRAY_SUPPORT) {
    this[offset] = (value & 0xff)
    this[offset + 1] = (value >>> 8)
  } else {
    objectWriteUInt16(this, value, offset, true)
  }
  return offset + 2
}

Buffer.prototype.writeInt16BE = function writeInt16BE (value, offset, noAssert) {
  value = +value
  offset = offset | 0
  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)
  if (Buffer.TYPED_ARRAY_SUPPORT) {
    this[offset] = (value >>> 8)
    this[offset + 1] = (value & 0xff)
  } else {
    objectWriteUInt16(this, value, offset, false)
  }
  return offset + 2
}

Buffer.prototype.writeInt32LE = function writeInt32LE (value, offset, noAssert) {
  value = +value
  offset = offset | 0
  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)
  if (Buffer.TYPED_ARRAY_SUPPORT) {
    this[offset] = (value & 0xff)
    this[offset + 1] = (value >>> 8)
    this[offset + 2] = (value >>> 16)
    this[offset + 3] = (value >>> 24)
  } else {
    objectWriteUInt32(this, value, offset, true)
  }
  return offset + 4
}

Buffer.prototype.writeInt32BE = function writeInt32BE (value, offset, noAssert) {
  value = +value
  offset = offset | 0
  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)
  if (value < 0) value = 0xffffffff + value + 1
  if (Buffer.TYPED_ARRAY_SUPPORT) {
    this[offset] = (value >>> 24)
    this[offset + 1] = (value >>> 16)
    this[offset + 2] = (value >>> 8)
    this[offset + 3] = (value & 0xff)
  } else {
    objectWriteUInt32(this, value, offset, false)
  }
  return offset + 4
}

function checkIEEE754 (buf, value, offset, ext, max, min) {
  if (offset + ext > buf.length) throw new RangeError('Index out of range')
  if (offset < 0) throw new RangeError('Index out of range')
}

function writeFloat (buf, value, offset, littleEndian, noAssert) {
  if (!noAssert) {
    checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38)
  }
  ieee754.write(buf, value, offset, littleEndian, 23, 4)
  return offset + 4
}

Buffer.prototype.writeFloatLE = function writeFloatLE (value, offset, noAssert) {
  return writeFloat(this, value, offset, true, noAssert)
}

Buffer.prototype.writeFloatBE = function writeFloatBE (value, offset, noAssert) {
  return writeFloat(this, value, offset, false, noAssert)
}

function writeDouble (buf, value, offset, littleEndian, noAssert) {
  if (!noAssert) {
    checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308)
  }
  ieee754.write(buf, value, offset, littleEndian, 52, 8)
  return offset + 8
}

Buffer.prototype.writeDoubleLE = function writeDoubleLE (value, offset, noAssert) {
  return writeDouble(this, value, offset, true, noAssert)
}

Buffer.prototype.writeDoubleBE = function writeDoubleBE (value, offset, noAssert) {
  return writeDouble(this, value, offset, false, noAssert)
}

// copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)
Buffer.prototype.copy = function copy (target, targetStart, start, end) {
  if (!start) start = 0
  if (!end && end !== 0) end = this.length
  if (targetStart >= target.length) targetStart = target.length
  if (!targetStart) targetStart = 0
  if (end > 0 && end < start) end = start

  // Copy 0 bytes; we're done
  if (end === start) return 0
  if (target.length === 0 || this.length === 0) return 0

  if (targetStart < 0) {
    throw new RangeError('targetStart out of bounds')
  }
  if (start < 0 || start >= this.length) throw new RangeError('sourceStart out of bounds')
  if (end < 0) throw new RangeError('sourceEnd out of bounds')

  // Are we oob?
  if (end > this.length) end = this.length
  if (target.length - targetStart < end - start) {
    end = target.length - targetStart + start
  }

  var len = end - start
  var i

  if (this === target && start < targetStart && targetStart < end) {
    // descending copy from end
    for (i = len - 1; i >= 0; --i) {
      target[i + targetStart] = this[i + start]
    }
  } else if (len < 1000 || !Buffer.TYPED_ARRAY_SUPPORT) {
    // ascending copy from start
    for (i = 0; i < len; ++i) {
      target[i + targetStart] = this[i + start]
    }
  } else {
    Uint8Array.prototype.set.call(
      target,
      this.subarray(start, start + len),
      targetStart
    )
  }

  return len
}

// Usage:
//    buffer.fill(number[, offset[, end]])
//    buffer.fill(buffer[, offset[, end]])
//    buffer.fill(string[, offset[, end]][, encoding])
Buffer.prototype.fill = function fill (val, start, end, encoding) {
  // Handle string cases:
  if (typeof val === 'string') {
    if (typeof start === 'string') {
      encoding = start
      start = 0
      end = this.length
    } else if (typeof end === 'string') {
      encoding = end
      end = this.length
    }
    if (val.length === 1) {
      var code = val.charCodeAt(0)
      if (code < 256) {
        val = code
      }
    }
    if (encoding !== undefined && typeof encoding !== 'string') {
      throw new TypeError('encoding must be a string')
    }
    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {
      throw new TypeError('Unknown encoding: ' + encoding)
    }
  } else if (typeof val === 'number') {
    val = val & 255
  }

  // Invalid ranges are not set to a default, so can range check early.
  if (start < 0 || this.length < start || this.length < end) {
    throw new RangeError('Out of range index')
  }

  if (end <= start) {
    return this
  }

  start = start >>> 0
  end = end === undefined ? this.length : end >>> 0

  if (!val) val = 0

  var i
  if (typeof val === 'number') {
    for (i = start; i < end; ++i) {
      this[i] = val
    }
  } else {
    var bytes = Buffer.isBuffer(val)
      ? val
      : utf8ToBytes(new Buffer(val, encoding).toString())
    var len = bytes.length
    for (i = 0; i < end - start; ++i) {
      this[i + start] = bytes[i % len]
    }
  }

  return this
}

// HELPER FUNCTIONS
// ================

var INVALID_BASE64_RE = /[^+\/0-9A-Za-z-_]/g

function base64clean (str) {
  // Node strips out invalid characters like \n and \t from the string, base64-js does not
  str = stringtrim(str).replace(INVALID_BASE64_RE, '')
  // Node converts strings with length < 2 to ''
  if (str.length < 2) return ''
  // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not
  while (str.length % 4 !== 0) {
    str = str + '='
  }
  return str
}

function stringtrim (str) {
  if (str.trim) return str.trim()
  return str.replace(/^\s+|\s+$/g, '')
}

function toHex (n) {
  if (n < 16) return '0' + n.toString(16)
  return n.toString(16)
}

function utf8ToBytes (string, units) {
  units = units || Infinity
  var codePoint
  var length = string.length
  var leadSurrogate = null
  var bytes = []

  for (var i = 0; i < length; ++i) {
    codePoint = string.charCodeAt(i)

    // is surrogate component
    if (codePoint > 0xD7FF && codePoint < 0xE000) {
      // last char was a lead
      if (!leadSurrogate) {
        // no lead yet
        if (codePoint > 0xDBFF) {
          // unexpected trail
          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)
          continue
        } else if (i + 1 === length) {
          // unpaired lead
          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)
          continue
        }

        // valid lead
        leadSurrogate = codePoint

        continue
      }

      // 2 leads in a row
      if (codePoint < 0xDC00) {
        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)
        leadSurrogate = codePoint
        continue
      }

      // valid surrogate pair
      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000
    } else if (leadSurrogate) {
      // valid bmp char, but last char was a lead
      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)
    }

    leadSurrogate = null

    // encode utf8
    if (codePoint < 0x80) {
      if ((units -= 1) < 0) break
      bytes.push(codePoint)
    } else if (codePoint < 0x800) {
      if ((units -= 2) < 0) break
      bytes.push(
        codePoint >> 0x6 | 0xC0,
        codePoint & 0x3F | 0x80
      )
    } else if (codePoint < 0x10000) {
      if ((units -= 3) < 0) break
      bytes.push(
        codePoint >> 0xC | 0xE0,
        codePoint >> 0x6 & 0x3F | 0x80,
        codePoint & 0x3F | 0x80
      )
    } else if (codePoint < 0x110000) {
      if ((units -= 4) < 0) break
      bytes.push(
        codePoint >> 0x12 | 0xF0,
        codePoint >> 0xC & 0x3F | 0x80,
        codePoint >> 0x6 & 0x3F | 0x80,
        codePoint & 0x3F | 0x80
      )
    } else {
      throw new Error('Invalid code point')
    }
  }

  return bytes
}

function asciiToBytes (str) {
  var byteArray = []
  for (var i = 0; i < str.length; ++i) {
    // Node's code seems to be doing this and not & 0x7F..
    byteArray.push(str.charCodeAt(i) & 0xFF)
  }
  return byteArray
}

function utf16leToBytes (str, units) {
  var c, hi, lo
  var byteArray = []
  for (var i = 0; i < str.length; ++i) {
    if ((units -= 2) < 0) break

    c = str.charCodeAt(i)
    hi = c >> 8
    lo = c % 256
    byteArray.push(lo)
    byteArray.push(hi)
  }

  return byteArray
}

function base64ToBytes (str) {
  return base64.toByteArray(base64clean(str))
}

function blitBuffer (src, dst, offset, length) {
  for (var i = 0; i < length; ++i) {
    if ((i + offset >= dst.length) || (i >= src.length)) break
    dst[i + offset] = src[i]
  }
  return i
}

function isnan (val) {
  return val !== val // eslint-disable-line no-self-compare
}

/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(83)))

/***/ }),
/* 83 */
/***/ (function(module, exports) {

var g;

// This works in non-strict mode
g = (function() {
	return this;
})();

try {
	// This works if eval is allowed (see CSP)
	g = g || Function("return this")() || (1,eval)("this");
} catch(e) {
	// This works if the window reference is available
	if(typeof window === "object")
		g = window;
}

// g can still be undefined, but nothing to do about it...
// We return undefined, instead of nothing here, so it's
// easier to handle this case. if(!global) { ...}

module.exports = g;


/***/ }),
/* 84 */
/***/ (function(module, exports, __webpack_require__) {

"use strict";


exports.byteLength = byteLength
exports.toByteArray = toByteArray
exports.fromByteArray = fromByteArray

var lookup = []
var revLookup = []
var Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array

var code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'
for (var i = 0, len = code.length; i < len; ++i) {
  lookup[i] = code[i]
  revLookup[code.charCodeAt(i)] = i
}

// Support decoding URL-safe base64 strings, as Node.js does.
// See: https://en.wikipedia.org/wiki/Base64#URL_applications
revLookup['-'.charCodeAt(0)] = 62
revLookup['_'.charCodeAt(0)] = 63

function getLens (b64) {
  var len = b64.length

  if (len % 4 > 0) {
    throw new Error('Invalid string. Length must be a multiple of 4')
  }

  // Trim off extra bytes after placeholder bytes are found
  // See: https://github.com/beatgammit/base64-js/issues/42
  var validLen = b64.indexOf('=')
  if (validLen === -1) validLen = len

  var placeHoldersLen = validLen === len
    ? 0
    : 4 - (validLen % 4)

  return [validLen, placeHoldersLen]
}

// base64 is 4/3 + up to two characters of the original data
function byteLength (b64) {
  var lens = getLens(b64)
  var validLen = lens[0]
  var placeHoldersLen = lens[1]
  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen
}

function _byteLength (b64, validLen, placeHoldersLen) {
  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen
}

function toByteArray (b64) {
  var tmp
  var lens = getLens(b64)
  var validLen = lens[0]
  var placeHoldersLen = lens[1]

  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen))

  var curByte = 0

  // if there are placeholders, only get up to the last complete 4 chars
  var len = placeHoldersLen > 0
    ? validLen - 4
    : validLen

  var i
  for (i = 0; i < len; i += 4) {
    tmp =
      (revLookup[b64.charCodeAt(i)] << 18) |
      (revLookup[b64.charCodeAt(i + 1)] << 12) |
      (revLookup[b64.charCodeAt(i + 2)] << 6) |
      revLookup[b64.charCodeAt(i + 3)]
    arr[curByte++] = (tmp >> 16) & 0xFF
    arr[curByte++] = (tmp >> 8) & 0xFF
    arr[curByte++] = tmp & 0xFF
  }

  if (placeHoldersLen === 2) {
    tmp =
      (revLookup[b64.charCodeAt(i)] << 2) |
      (revLookup[b64.charCodeAt(i + 1)] >> 4)
    arr[curByte++] = tmp & 0xFF
  }

  if (placeHoldersLen === 1) {
    tmp =
      (revLookup[b64.charCodeAt(i)] << 10) |
      (revLookup[b64.charCodeAt(i + 1)] << 4) |
      (revLookup[b64.charCodeAt(i + 2)] >> 2)
    arr[curByte++] = (tmp >> 8) & 0xFF
    arr[curByte++] = tmp & 0xFF
  }

  return arr
}

function tripletToBase64 (num) {
  return lookup[num >> 18 & 0x3F] +
    lookup[num >> 12 & 0x3F] +
    lookup[num >> 6 & 0x3F] +
    lookup[num & 0x3F]
}

function encodeChunk (uint8, start, end) {
  var tmp
  var output = []
  for (var i = start; i < end; i += 3) {
    tmp =
      ((uint8[i] << 16) & 0xFF0000) +
      ((uint8[i + 1] << 8) & 0xFF00) +
      (uint8[i + 2] & 0xFF)
    output.push(tripletToBase64(tmp))
  }
  return output.join('')
}

function fromByteArray (uint8) {
  var tmp
  var len = uint8.length
  var extraBytes = len % 3 // if we have 1 byte left, pad 2 bytes
  var parts = []
  var maxChunkLength = 16383 // must be multiple of 3

  // go through the array every three bytes, we'll deal with trailing stuff later
  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {
    parts.push(encodeChunk(uint8, i, (i + maxChunkLength) > len2 ? len2 : (i + maxChunkLength)))
  }

  // pad the end with zeros, but make sure to not forget the extra bytes
  if (extraBytes === 1) {
    tmp = uint8[len - 1]
    parts.push(
      lookup[tmp >> 2] +
      lookup[(tmp << 4) & 0x3F] +
      '=='
    )
  } else if (extraBytes === 2) {
    tmp = (uint8[len - 2] << 8) + uint8[len - 1]
    parts.push(
      lookup[tmp >> 10] +
      lookup[(tmp >> 4) & 0x3F] +
      lookup[(tmp << 2) & 0x3F] +
      '='
    )
  }

  return parts.join('')
}


/***/ }),
/* 85 */
/***/ (function(module, exports) {

/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
exports.read = function (buffer, offset, isLE, mLen, nBytes) {
  var e, m
  var eLen = (nBytes * 8) - mLen - 1
  var eMax = (1 << eLen) - 1
  var eBias = eMax >> 1
  var nBits = -7
  var i = isLE ? (nBytes - 1) : 0
  var d = isLE ? -1 : 1
  var s = buffer[offset + i]

  i += d

  e = s & ((1 << (-nBits)) - 1)
  s >>= (-nBits)
  nBits += eLen
  for (; nBits > 0; e = (e * 256) + buffer[offset + i], i += d, nBits -= 8) {}

  m = e & ((1 << (-nBits)) - 1)
  e >>= (-nBits)
  nBits += mLen
  for (; nBits > 0; m = (m * 256) + buffer[offset + i], i += d, nBits -= 8) {}

  if (e === 0) {
    e = 1 - eBias
  } else if (e === eMax) {
    return m ? NaN : ((s ? -1 : 1) * Infinity)
  } else {
    m = m + Math.pow(2, mLen)
    e = e - eBias
  }
  return (s ? -1 : 1) * m * Math.pow(2, e - mLen)
}

exports.write = function (buffer, value, offset, isLE, mLen, nBytes) {
  var e, m, c
  var eLen = (nBytes * 8) - mLen - 1
  var eMax = (1 << eLen) - 1
  var eBias = eMax >> 1
  var rt = (mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0)
  var i = isLE ? 0 : (nBytes - 1)
  var d = isLE ? 1 : -1
  var s = value < 0 || (value === 0 && 1 / value < 0) ? 1 : 0

  value = Math.abs(value)

  if (isNaN(value) || value === Infinity) {
    m = isNaN(value) ? 1 : 0
    e = eMax
  } else {
    e = Math.floor(Math.log(value) / Math.LN2)
    if (value * (c = Math.pow(2, -e)) < 1) {
      e--
      c *= 2
    }
    if (e + eBias >= 1) {
      value += rt / c
    } else {
      value += rt * Math.pow(2, 1 - eBias)
    }
    if (value * c >= 2) {
      e++
      c /= 2
    }

    if (e + eBias >= eMax) {
      m = 0
      e = eMax
    } else if (e + eBias >= 1) {
      m = ((value * c) - 1) * Math.pow(2, mLen)
      e = e + eBias
    } else {
      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen)
      e = 0
    }
  }

  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}

  e = (e << mLen) | m
  eLen += mLen
  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}

  buffer[offset + i - d] |= s * 128
}


/***/ }),
/* 86 */
/***/ (function(module, exports) {

var toString = {}.toString;

module.exports = Array.isArray || function (arr) {
  return toString.call(arr) == '[object Array]';
};


/***/ }),
/* 87 */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_RESULT__;

var _typeof2 = __webpack_require__(88);

var _typeof3 = _interopRequireDefault(_typeof2);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

(function (factory) {
    if (( false ? 'undefined' : (0, _typeof3.default)(exports)) === 'object') {
        // Node/CommonJS
        module.exports = factory();
    } else if (true) {
        // AMD
        !(__WEBPACK_AMD_DEFINE_FACTORY__ = (factory),
				__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?
				(__WEBPACK_AMD_DEFINE_FACTORY__.call(exports, __webpack_require__, exports, module)) :
				__WEBPACK_AMD_DEFINE_FACTORY__),
				__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));
    } else {
        // Browser globals (with support for web workers)
        var glob;

        try {
            glob = window;
        } catch (e) {
            glob = self;
        }

        glob.SparkMD5 = factory();
    }
})(function (undefined) {

    'use strict';

    ////////////////////////////////////////////////////////////////////////////

    /*
     * Fastest md5 implementation around (JKM md5)
     * Credits: Joseph Myers
     *
     * @see http://www.myersdaily.org/joseph/javascript/md5-text.html
     * @see http://jsperf.com/md5-shootout/7
     */

    /* this function is much faster,
      so if possible we use it. Some IEs
      are the only ones I know of that
      need the idiotic second function,
      generated by an if clause.  */

    var add32 = function add32(a, b) {
        return a + b & 0xFFFFFFFF;
    },
        cmn = function cmn(q, a, b, x, s, t) {
        a = add32(add32(a, q), add32(x, t));
        return add32(a << s | a >>> 32 - s, b);
    },
        ff = function ff(a, b, c, d, x, s, t) {
        return cmn(b & c | ~b & d, a, b, x, s, t);
    },
        gg = function gg(a, b, c, d, x, s, t) {
        return cmn(b & d | c & ~d, a, b, x, s, t);
    },
        hh = function hh(a, b, c, d, x, s, t) {
        return cmn(b ^ c ^ d, a, b, x, s, t);
    },
        ii = function ii(a, b, c, d, x, s, t) {
        return cmn(c ^ (b | ~d), a, b, x, s, t);
    },
        md5cycle = function md5cycle(x, k) {
        var a = x[0],
            b = x[1],
            c = x[2],
            d = x[3];

        a = ff(a, b, c, d, k[0], 7, -680876936);
        d = ff(d, a, b, c, k[1], 12, -389564586);
        c = ff(c, d, a, b, k[2], 17, 606105819);
        b = ff(b, c, d, a, k[3], 22, -1044525330);
        a = ff(a, b, c, d, k[4], 7, -176418897);
        d = ff(d, a, b, c, k[5], 12, 1200080426);
        c = ff(c, d, a, b, k[6], 17, -1473231341);
        b = ff(b, c, d, a, k[7], 22, -45705983);
        a = ff(a, b, c, d, k[8], 7, 1770035416);
        d = ff(d, a, b, c, k[9], 12, -1958414417);
        c = ff(c, d, a, b, k[10], 17, -42063);
        b = ff(b, c, d, a, k[11], 22, -1990404162);
        a = ff(a, b, c, d, k[12], 7, 1804603682);
        d = ff(d, a, b, c, k[13], 12, -40341101);
        c = ff(c, d, a, b, k[14], 17, -1502002290);
        b = ff(b, c, d, a, k[15], 22, 1236535329);

        a = gg(a, b, c, d, k[1], 5, -165796510);
        d = gg(d, a, b, c, k[6], 9, -1069501632);
        c = gg(c, d, a, b, k[11], 14, 643717713);
        b = gg(b, c, d, a, k[0], 20, -373897302);
        a = gg(a, b, c, d, k[5], 5, -701558691);
        d = gg(d, a, b, c, k[10], 9, 38016083);
        c = gg(c, d, a, b, k[15], 14, -660478335);
        b = gg(b, c, d, a, k[4], 20, -405537848);
        a = gg(a, b, c, d, k[9], 5, 568446438);
        d = gg(d, a, b, c, k[14], 9, -1019803690);
        c = gg(c, d, a, b, k[3], 14, -187363961);
        b = gg(b, c, d, a, k[8], 20, 1163531501);
        a = gg(a, b, c, d, k[13], 5, -1444681467);
        d = gg(d, a, b, c, k[2], 9, -51403784);
        c = gg(c, d, a, b, k[7], 14, 1735328473);
        b = gg(b, c, d, a, k[12], 20, -1926607734);

        a = hh(a, b, c, d, k[5], 4, -378558);
        d = hh(d, a, b, c, k[8], 11, -2022574463);
        c = hh(c, d, a, b, k[11], 16, 1839030562);
        b = hh(b, c, d, a, k[14], 23, -35309556);
        a = hh(a, b, c, d, k[1], 4, -1530992060);
        d = hh(d, a, b, c, k[4], 11, 1272893353);
        c = hh(c, d, a, b, k[7], 16, -155497632);
        b = hh(b, c, d, a, k[10], 23, -1094730640);
        a = hh(a, b, c, d, k[13], 4, 681279174);
        d = hh(d, a, b, c, k[0], 11, -358537222);
        c = hh(c, d, a, b, k[3], 16, -722521979);
        b = hh(b, c, d, a, k[6], 23, 76029189);
        a = hh(a, b, c, d, k[9], 4, -640364487);
        d = hh(d, a, b, c, k[12], 11, -421815835);
        c = hh(c, d, a, b, k[15], 16, 530742520);
        b = hh(b, c, d, a, k[2], 23, -995338651);

        a = ii(a, b, c, d, k[0], 6, -198630844);
        d = ii(d, a, b, c, k[7], 10, 1126891415);
        c = ii(c, d, a, b, k[14], 15, -1416354905);
        b = ii(b, c, d, a, k[5], 21, -57434055);
        a = ii(a, b, c, d, k[12], 6, 1700485571);
        d = ii(d, a, b, c, k[3], 10, -1894986606);
        c = ii(c, d, a, b, k[10], 15, -1051523);
        b = ii(b, c, d, a, k[1], 21, -2054922799);
        a = ii(a, b, c, d, k[8], 6, 1873313359);
        d = ii(d, a, b, c, k[15], 10, -30611744);
        c = ii(c, d, a, b, k[6], 15, -1560198380);
        b = ii(b, c, d, a, k[13], 21, 1309151649);
        a = ii(a, b, c, d, k[4], 6, -145523070);
        d = ii(d, a, b, c, k[11], 10, -1120210379);
        c = ii(c, d, a, b, k[2], 15, 718787259);
        b = ii(b, c, d, a, k[9], 21, -343485551);

        x[0] = add32(a, x[0]);
        x[1] = add32(b, x[1]);
        x[2] = add32(c, x[2]);
        x[3] = add32(d, x[3]);
    },


    /* there needs to be support for Unicode here,
       * unless we pretend that we can redefine the MD-5
       * algorithm for multi-byte characters (perhaps
       * by adding every four 16-bit characters and
       * shortening the sum to 32 bits). Otherwise
       * I suggest performing MD-5 as if every character
       * was two bytes--e.g., 0040 0025 = @%--but then
       * how will an ordinary MD-5 sum be matched?
       * There is no way to standardize text to something
       * like UTF-8 before transformation; speed cost is
       * utterly prohibitive. The JavaScript standard
       * itself needs to look at this: it should start
       * providing access to strings as preformed UTF-8
       * 8-bit unsigned value arrays.
       */
    md5blk = function md5blk(s) {
        var md5blks = [],
            i; /* Andy King said do it this way. */

        for (i = 0; i < 64; i += 4) {
            md5blks[i >> 2] = s.charCodeAt(i) + (s.charCodeAt(i + 1) << 8) + (s.charCodeAt(i + 2) << 16) + (s.charCodeAt(i + 3) << 24);
        }
        return md5blks;
    },
        md5blk_array = function md5blk_array(a) {
        var md5blks = [],
            i; /* Andy King said do it this way. */

        for (i = 0; i < 64; i += 4) {
            md5blks[i >> 2] = a[i] + (a[i + 1] << 8) + (a[i + 2] << 16) + (a[i + 3] << 24);
        }
        return md5blks;
    },
        md51 = function md51(s) {
        var n = s.length,
            state = [1732584193, -271733879, -1732584194, 271733878],
            i,
            length,
            tail,
            tmp,
            lo,
            hi;

        for (i = 64; i <= n; i += 64) {
            md5cycle(state, md5blk(s.substring(i - 64, i)));
        }
        s = s.substring(i - 64);
        length = s.length;
        tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
        for (i = 0; i < length; i += 1) {
            tail[i >> 2] |= s.charCodeAt(i) << (i % 4 << 3);
        }
        tail[i >> 2] |= 0x80 << (i % 4 << 3);
        if (i > 55) {
            md5cycle(state, tail);
            for (i = 0; i < 16; i += 1) {
                tail[i] = 0;
            }
        }

        // Beware that the final length might not fit in 32 bits so we take care of that
        tmp = n * 8;
        tmp = tmp.toString(16).match(/(.*?)(.{0,8})$/);
        lo = parseInt(tmp[2], 16);
        hi = parseInt(tmp[1], 16) || 0;

        tail[14] = lo;
        tail[15] = hi;

        md5cycle(state, tail);
        return state;
    },
        md51_array = function md51_array(a) {
        var n = a.length,
            state = [1732584193, -271733879, -1732584194, 271733878],
            i,
            length,
            tail,
            tmp,
            lo,
            hi;

        for (i = 64; i <= n; i += 64) {
            md5cycle(state, md5blk_array(a.subarray(i - 64, i)));
        }

        // Not sure if it is a bug, however IE10 will always produce a sub array of length 1
        // containing the last element of the parent array if the sub array specified starts
        // beyond the length of the parent array - weird.
        // https://connect.microsoft.com/IE/feedback/details/771452/typed-array-subarray-issue
        a = i - 64 < n ? a.subarray(i - 64) : new Uint8Array(0);

        length = a.length;
        tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
        for (i = 0; i < length; i += 1) {
            tail[i >> 2] |= a[i] << (i % 4 << 3);
        }

        tail[i >> 2] |= 0x80 << (i % 4 << 3);
        if (i > 55) {
            md5cycle(state, tail);
            for (i = 0; i < 16; i += 1) {
                tail[i] = 0;
            }
        }

        // Beware that the final length might not fit in 32 bits so we take care of that
        tmp = n * 8;
        tmp = tmp.toString(16).match(/(.*?)(.{0,8})$/);
        lo = parseInt(tmp[2], 16);
        hi = parseInt(tmp[1], 16) || 0;

        tail[14] = lo;
        tail[15] = hi;

        md5cycle(state, tail);

        return state;
    },
        hex_chr = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'],
        rhex = function rhex(n) {
        var s = '',
            j;
        for (j = 0; j < 4; j += 1) {
            s += hex_chr[n >> j * 8 + 4 & 0x0F] + hex_chr[n >> j * 8 & 0x0F];
        }
        return s;
    },
        hex = function hex(x) {
        var i;
        for (i = 0; i < x.length; i += 1) {
            x[i] = rhex(x[i]);
        }
        return x.join('');
    },
        md5 = function md5(s) {
        return hex(md51(s));
    },


    ////////////////////////////////////////////////////////////////////////////

    /**
     * SparkMD5 OOP implementation.
     *
     * Use this class to perform an incremental md5, otherwise use the
     * static methods instead.
     */
    SparkMD5 = function SparkMD5() {
        // call reset to init the instance
        this.reset();
    };

    // In some cases the fast add32 function cannot be used..
    if (md5('hello') !== '5d41402abc4b2a76b9719d911017c592') {
        add32 = function add32(x, y) {
            var lsw = (x & 0xFFFF) + (y & 0xFFFF),
                msw = (x >> 16) + (y >> 16) + (lsw >> 16);
            return msw << 16 | lsw & 0xFFFF;
        };
    }

    /**
     * Appends a string.
     * A conversion will be applied if an utf8 string is detected.
     *
     * @param {String} str The string to be appended
     *
     * @return {SparkMD5} The instance itself
     */
    SparkMD5.prototype.append = function (str) {
        // converts the string to utf8 bytes if necessary
        if (/[\u0080-\uFFFF]/.test(str)) {
            str = unescape(encodeURIComponent(str));
        }

        // then append as binary
        this.appendBinary(str);

        return this;
    };

    /**
     * Appends a binary string.
     *
     * @param {String} contents The binary string to be appended
     *
     * @return {SparkMD5} The instance itself
     */
    SparkMD5.prototype.appendBinary = function (contents) {
        this._buff += contents;
        this._length += contents.length;

        var length = this._buff.length,
            i;

        for (i = 64; i <= length; i += 64) {
            md5cycle(this._state, md5blk(this._buff.substring(i - 64, i)));
        }

        this._buff = this._buff.substr(i - 64);

        return this;
    };

    /**
     * Finishes the incremental computation, reseting the internal state and
     * returning the result.
     * Use the raw parameter to obtain the raw result instead of the hex one.
     *
     * @param {Boolean} raw True to get the raw result, false to get the hex result
     *
     * @return {String|Array} The result
     */
    SparkMD5.prototype.end = function (raw) {
        var buff = this._buff,
            length = buff.length,
            i,
            tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            ret;

        for (i = 0; i < length; i += 1) {
            tail[i >> 2] |= buff.charCodeAt(i) << (i % 4 << 3);
        }

        this._finish(tail, length);
        ret = !!raw ? this._state : hex(this._state);

        this.reset();

        return ret;
    };

    /**
     * Finish the final calculation based on the tail.
     *
     * @param {Array}  tail   The tail (will be modified)
     * @param {Number} length The length of the remaining buffer
     */
    SparkMD5.prototype._finish = function (tail, length) {
        var i = length,
            tmp,
            lo,
            hi;

        tail[i >> 2] |= 0x80 << (i % 4 << 3);
        if (i > 55) {
            md5cycle(this._state, tail);
            for (i = 0; i < 16; i += 1) {
                tail[i] = 0;
            }
        }

        // Do the final computation based on the tail and length
        // Beware that the final length may not fit in 32 bits so we take care of that
        tmp = this._length * 8;
        tmp = tmp.toString(16).match(/(.*?)(.{0,8})$/);
        lo = parseInt(tmp[2], 16);
        hi = parseInt(tmp[1], 16) || 0;

        tail[14] = lo;
        tail[15] = hi;
        md5cycle(this._state, tail);
    };

    /**
     * Resets the internal state of the computation.
     *
     * @return {SparkMD5} The instance itself
     */
    SparkMD5.prototype.reset = function () {
        this._buff = '';
        this._length = 0;
        this._state = [1732584193, -271733879, -1732584194, 271733878];

        return this;
    };

    /**
     * Releases memory used by the incremental buffer and other aditional
     * resources. If you plan to use the instance again, use reset instead.
     */
    SparkMD5.prototype.destroy = function () {
        delete this._state;
        delete this._buff;
        delete this._length;
    };

    /**
     * Performs the md5 hash on a string.
     * A conversion will be applied if utf8 string is detected.
     *
     * @param {String}  str The string
     * @param {Boolean} raw True to get the raw result, false to get the hex result
     *
     * @return {String|Array} The result
     */
    SparkMD5.hash = function (str, raw) {
        // converts the string to utf8 bytes if necessary
        if (/[\u0080-\uFFFF]/.test(str)) {
            str = unescape(encodeURIComponent(str));
        }

        var hash = md51(str);

        return !!raw ? hash : hex(hash);
    };

    /**
     * Performs the md5 hash on a binary string.
     *
     * @param {String}  content The binary string
     * @param {Boolean} raw     True to get the raw result, false to get the hex result
     *
     * @return {String|Array} The result
     */
    SparkMD5.hashBinary = function (content, raw) {
        var hash = md51(content);

        return !!raw ? hash : hex(hash);
    };

    /**
     * SparkMD5 OOP implementation for array buffers.
     *
     * Use this class to perform an incremental md5 ONLY for array buffers.
     */
    SparkMD5.ArrayBuffer = function () {
        // call reset to init the instance
        this.reset();
    };

    ////////////////////////////////////////////////////////////////////////////

    /**
     * Appends an array buffer.
     *
     * @param {ArrayBuffer} arr The array to be appended
     *
     * @return {SparkMD5.ArrayBuffer} The instance itself
     */
    SparkMD5.ArrayBuffer.prototype.append = function (arr) {
        // TODO: we could avoid the concatenation here but the algorithm would be more complex
        //       if you find yourself needing extra performance, please make a PR.
        var buff = this._concatArrayBuffer(this._buff, arr),
            length = buff.length,
            i;

        this._length += arr.byteLength;

        for (i = 64; i <= length; i += 64) {
            md5cycle(this._state, md5blk_array(buff.subarray(i - 64, i)));
        }

        // Avoids IE10 weirdness (documented above)
        this._buff = i - 64 < length ? buff.subarray(i - 64) : new Uint8Array(0);

        return this;
    };

    /**
     * Finishes the incremental computation, reseting the internal state and
     * returning the result.
     * Use the raw parameter to obtain the raw result instead of the hex one.
     *
     * @param {Boolean} raw True to get the raw result, false to get the hex result
     *
     * @return {String|Array} The result
     */
    SparkMD5.ArrayBuffer.prototype.end = function (raw) {
        var buff = this._buff,
            length = buff.length,
            tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            i,
            ret;

        for (i = 0; i < length; i += 1) {
            tail[i >> 2] |= buff[i] << (i % 4 << 3);
        }

        this._finish(tail, length);
        ret = !!raw ? this._state : hex(this._state);

        this.reset();

        return ret;
    };

    SparkMD5.ArrayBuffer.prototype._finish = SparkMD5.prototype._finish;

    /**
     * Resets the internal state of the computation.
     *
     * @return {SparkMD5.ArrayBuffer} The instance itself
     */
    SparkMD5.ArrayBuffer.prototype.reset = function () {
        this._buff = new Uint8Array(0);
        this._length = 0;
        this._state = [1732584193, -271733879, -1732584194, 271733878];

        return this;
    };

    /**
     * Releases memory used by the incremental buffer and other aditional
     * resources. If you plan to use the instance again, use reset instead.
     */
    SparkMD5.ArrayBuffer.prototype.destroy = SparkMD5.prototype.destroy;

    /**
     * Concats two array buffers, returning a new one.
     *
     * @param  {ArrayBuffer} first  The first array buffer
     * @param  {ArrayBuffer} second The second array buffer
     *
     * @return {ArrayBuffer} The new array buffer
     */
    SparkMD5.ArrayBuffer.prototype._concatArrayBuffer = function (first, second) {
        var firstLength = first.length,
            result = new Uint8Array(firstLength + second.byteLength);

        result.set(first);
        result.set(new Uint8Array(second), firstLength);

        return result;
    };

    /**
     * Performs the md5 hash on an array buffer.
     *
     * @param {ArrayBuffer} arr The array buffer
     * @param {Boolean}     raw True to get the raw result, false to get the hex result
     *
     * @return {String|Array} The result
     */
    SparkMD5.ArrayBuffer.hash = function (arr, raw) {
        var hash = md51_array(new Uint8Array(arr));

        return !!raw ? hash : hex(hash);
    };

    return SparkMD5;
});

/***/ }),
/* 88 */
/***/ (function(module, exports, __webpack_require__) {

"use strict";


exports.__esModule = true;

var _iterator = __webpack_require__(89);

var _iterator2 = _interopRequireDefault(_iterator);

var _symbol = __webpack_require__(91);

var _symbol2 = _interopRequireDefault(_symbol);

var _typeof = typeof _symbol2.default === "function" && typeof _iterator2.default === "symbol" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof _symbol2.default === "function" && obj.constructor === _symbol2.default && obj !== _symbol2.default.prototype ? "symbol" : typeof obj; };

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

exports.default = typeof _symbol2.default === "function" && _typeof(_iterator2.default) === "symbol" ? function (obj) {
  return typeof obj === "undefined" ? "undefined" : _typeof(obj);
} : function (obj) {
  return obj && typeof _symbol2.default === "function" && obj.constructor === _symbol2.default && obj !== _symbol2.default.prototype ? "symbol" : typeof obj === "undefined" ? "undefined" : _typeof(obj);
};

/***/ }),
/* 89 */
/***/ (function(module, exports, __webpack_require__) {

module.exports = { "default": __webpack_require__(90), __esModule: true };

/***/ }),
/* 90 */
/***/ (function(module, exports, __webpack_require__) {

__webpack_require__(37);
__webpack_require__(46);
module.exports = __webpack_require__(32).f('iterator');


/***/ }),
/* 91 */
/***/ (function(module, exports, __webpack_require__) {

module.exports = { "default": __webpack_require__(92), __esModule: true };

/***/ }),
/* 92 */
/***/ (function(module, exports, __webpack_require__) {

__webpack_require__(93);
__webpack_require__(36);
__webpack_require__(99);
__webpack_require__(100);
module.exports = __webpack_require__(2).Symbol;


/***/ }),
/* 93 */
/***/ (function(module, exports, __webpack_require__) {

"use strict";

// ECMAScript 6 symbols shim
var global = __webpack_require__(0);
var has = __webpack_require__(8);
var DESCRIPTORS = __webpack_require__(7);
var $export = __webpack_require__(11);
var redefine = __webpack_require__(40);
var META = __webpack_require__(94).KEY;
var $fails = __webpack_require__(16);
var shared = __webpack_require__(29);
var setToStringTag = __webpack_require__(19);
var uid = __webpack_require__(18);
var wks = __webpack_require__(1);
var wksExt = __webpack_require__(32);
var wksDefine = __webpack_require__(33);
var enumKeys = __webpack_require__(95);
var isArray = __webpack_require__(96);
var anObject = __webpack_require__(3);
var isObject = __webpack_require__(6);
var toObject = __webpack_require__(45);
var toIObject = __webpack_require__(9);
var toPrimitive = __webpack_require__(26);
var createDesc = __webpack_require__(17);
var _create = __webpack_require__(41);
var gOPNExt = __webpack_require__(97);
var $GOPD = __webpack_require__(98);
var $GOPS = __webpack_require__(52);
var $DP = __webpack_require__(5);
var $keys = __webpack_require__(27);
var gOPD = $GOPD.f;
var dP = $DP.f;
var gOPN = gOPNExt.f;
var $Symbol = global.Symbol;
var $JSON = global.JSON;
var _stringify = $JSON && $JSON.stringify;
var PROTOTYPE = 'prototype';
var HIDDEN = wks('_hidden');
var TO_PRIMITIVE = wks('toPrimitive');
var isEnum = {}.propertyIsEnumerable;
var SymbolRegistry = shared('symbol-registry');
var AllSymbols = shared('symbols');
var OPSymbols = shared('op-symbols');
var ObjectProto = Object[PROTOTYPE];
var USE_NATIVE = typeof $Symbol == 'function' && !!$GOPS.f;
var QObject = global.QObject;
// Don't use setters in Qt Script, https://github.com/zloirock/core-js/issues/173
var setter = !QObject || !QObject[PROTOTYPE] || !QObject[PROTOTYPE].findChild;

// fallback for old Android, https://code.google.com/p/v8/issues/detail?id=687
var setSymbolDesc = DESCRIPTORS && $fails(function () {
  return _create(dP({}, 'a', {
    get: function () { return dP(this, 'a', { value: 7 }).a; }
  })).a != 7;
}) ? function (it, key, D) {
  var protoDesc = gOPD(ObjectProto, key);
  if (protoDesc) delete ObjectProto[key];
  dP(it, key, D);
  if (protoDesc && it !== ObjectProto) dP(ObjectProto, key, protoDesc);
} : dP;

var wrap = function (tag) {
  var sym = AllSymbols[tag] = _create($Symbol[PROTOTYPE]);
  sym._k = tag;
  return sym;
};

var isSymbol = USE_NATIVE && typeof $Symbol.iterator == 'symbol' ? function (it) {
  return typeof it == 'symbol';
} : function (it) {
  return it instanceof $Symbol;
};

var $defineProperty = function defineProperty(it, key, D) {
  if (it === ObjectProto) $defineProperty(OPSymbols, key, D);
  anObject(it);
  key = toPrimitive(key, true);
  anObject(D);
  if (has(AllSymbols, key)) {
    if (!D.enumerable) {
      if (!has(it, HIDDEN)) dP(it, HIDDEN, createDesc(1, {}));
      it[HIDDEN][key] = true;
    } else {
      if (has(it, HIDDEN) && it[HIDDEN][key]) it[HIDDEN][key] = false;
      D = _create(D, { enumerable: createDesc(0, false) });
    } return setSymbolDesc(it, key, D);
  } return dP(it, key, D);
};
var $defineProperties = function defineProperties(it, P) {
  anObject(it);
  var keys = enumKeys(P = toIObject(P));
  var i = 0;
  var l = keys.length;
  var key;
  while (l > i) $defineProperty(it, key = keys[i++], P[key]);
  return it;
};
var $create = function create(it, P) {
  return P === undefined ? _create(it) : $defineProperties(_create(it), P);
};
var $propertyIsEnumerable = function propertyIsEnumerable(key) {
  var E = isEnum.call(this, key = toPrimitive(key, true));
  if (this === ObjectProto && has(AllSymbols, key) && !has(OPSymbols, key)) return false;
  return E || !has(this, key) || !has(AllSymbols, key) || has(this, HIDDEN) && this[HIDDEN][key] ? E : true;
};
var $getOwnPropertyDescriptor = function getOwnPropertyDescriptor(it, key) {
  it = toIObject(it);
  key = toPrimitive(key, true);
  if (it === ObjectProto && has(AllSymbols, key) && !has(OPSymbols, key)) return;
  var D = gOPD(it, key);
  if (D && has(AllSymbols, key) && !(has(it, HIDDEN) && it[HIDDEN][key])) D.enumerable = true;
  return D;
};
var $getOwnPropertyNames = function getOwnPropertyNames(it) {
  var names = gOPN(toIObject(it));
  var result = [];
  var i = 0;
  var key;
  while (names.length > i) {
    if (!has(AllSymbols, key = names[i++]) && key != HIDDEN && key != META) result.push(key);
  } return result;
};
var $getOwnPropertySymbols = function getOwnPropertySymbols(it) {
  var IS_OP = it === ObjectProto;
  var names = gOPN(IS_OP ? OPSymbols : toIObject(it));
  var result = [];
  var i = 0;
  var key;
  while (names.length > i) {
    if (has(AllSymbols, key = names[i++]) && (IS_OP ? has(ObjectProto, key) : true)) result.push(AllSymbols[key]);
  } return result;
};

// ******** Symbol([description])
if (!USE_NATIVE) {
  $Symbol = function Symbol() {
    if (this instanceof $Symbol) throw TypeError('Symbol is not a constructor!');
    var tag = uid(arguments.length > 0 ? arguments[0] : undefined);
    var $set = function (value) {
      if (this === ObjectProto) $set.call(OPSymbols, value);
      if (has(this, HIDDEN) && has(this[HIDDEN], tag)) this[HIDDEN][tag] = false;
      setSymbolDesc(this, tag, createDesc(1, value));
    };
    if (DESCRIPTORS && setter) setSymbolDesc(ObjectProto, tag, { configurable: true, set: $set });
    return wrap(tag);
  };
  redefine($Symbol[PROTOTYPE], 'toString', function toString() {
    return this._k;
  });

  $GOPD.f = $getOwnPropertyDescriptor;
  $DP.f = $defineProperty;
  __webpack_require__(53).f = gOPNExt.f = $getOwnPropertyNames;
  __webpack_require__(34).f = $propertyIsEnumerable;
  $GOPS.f = $getOwnPropertySymbols;

  if (DESCRIPTORS && !__webpack_require__(10)) {
    redefine(ObjectProto, 'propertyIsEnumerable', $propertyIsEnumerable, true);
  }

  wksExt.f = function (name) {
    return wrap(wks(name));
  };
}

$export($export.G + $export.W + $export.F * !USE_NATIVE, { Symbol: $Symbol });

for (var es6Symbols = (
  // ********, ********, ********, ********, ********, ********, *********, *********, *********, *********, *********
  'hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables'
).split(','), j = 0; es6Symbols.length > j;)wks(es6Symbols[j++]);

for (var wellKnownSymbols = $keys(wks.store), k = 0; wellKnownSymbols.length > k;) wksDefine(wellKnownSymbols[k++]);

$export($export.S + $export.F * !USE_NATIVE, 'Symbol', {
  // ******** Symbol.for(key)
  'for': function (key) {
    return has(SymbolRegistry, key += '')
      ? SymbolRegistry[key]
      : SymbolRegistry[key] = $Symbol(key);
  },
  // ******** Symbol.keyFor(sym)
  keyFor: function keyFor(sym) {
    if (!isSymbol(sym)) throw TypeError(sym + ' is not a symbol!');
    for (var key in SymbolRegistry) if (SymbolRegistry[key] === sym) return key;
  },
  useSetter: function () { setter = true; },
  useSimple: function () { setter = false; }
});

$export($export.S + $export.F * !USE_NATIVE, 'Object', {
  // ******** Object.create(O [, Properties])
  create: $create,
  // ******** Object.defineProperty(O, P, Attributes)
  defineProperty: $defineProperty,
  // ******** Object.defineProperties(O, Properties)
  defineProperties: $defineProperties,
  // ******** Object.getOwnPropertyDescriptor(O, P)
  getOwnPropertyDescriptor: $getOwnPropertyDescriptor,
  // ******** Object.getOwnPropertyNames(O)
  getOwnPropertyNames: $getOwnPropertyNames,
  // ******** Object.getOwnPropertySymbols(O)
  getOwnPropertySymbols: $getOwnPropertySymbols
});

// Chrome 38 and 39 `Object.getOwnPropertySymbols` fails on primitives
// https://bugs.chromium.org/p/v8/issues/detail?id=3443
var FAILS_ON_PRIMITIVES = $fails(function () { $GOPS.f(1); });

$export($export.S + $export.F * FAILS_ON_PRIMITIVES, 'Object', {
  getOwnPropertySymbols: function getOwnPropertySymbols(it) {
    return $GOPS.f(toObject(it));
  }
});

// 24.3.2 JSON.stringify(value [, replacer [, space]])
$JSON && $export($export.S + $export.F * (!USE_NATIVE || $fails(function () {
  var S = $Symbol();
  // MS Edge converts symbol values to JSON as {}
  // WebKit converts symbol values to JSON as null
  // V8 throws on boxed symbols
  return _stringify([S]) != '[null]' || _stringify({ a: S }) != '{}' || _stringify(Object(S)) != '{}';
})), 'JSON', {
  stringify: function stringify(it) {
    var args = [it];
    var i = 1;
    var replacer, $replacer;
    while (arguments.length > i) args.push(arguments[i++]);
    $replacer = replacer = args[1];
    if (!isObject(replacer) && it === undefined || isSymbol(it)) return; // IE8 returns string on undefined
    if (!isArray(replacer)) replacer = function (key, value) {
      if (typeof $replacer == 'function') value = $replacer.call(this, key, value);
      if (!isSymbol(value)) return value;
    };
    args[1] = replacer;
    return _stringify.apply($JSON, args);
  }
});

// 19.4.3.4 Symbol.prototype[@@toPrimitive](hint)
$Symbol[PROTOTYPE][TO_PRIMITIVE] || __webpack_require__(4)($Symbol[PROTOTYPE], TO_PRIMITIVE, $Symbol[PROTOTYPE].valueOf);
// 19.4.3.5 Symbol.prototype[@@toStringTag]
setToStringTag($Symbol, 'Symbol');
// 20.2.1.9 Math[@@toStringTag]
setToStringTag(Math, 'Math', true);
// 24.3.3 JSON[@@toStringTag]
setToStringTag(global.JSON, 'JSON', true);


/***/ }),
/* 94 */
/***/ (function(module, exports, __webpack_require__) {

var META = __webpack_require__(18)('meta');
var isObject = __webpack_require__(6);
var has = __webpack_require__(8);
var setDesc = __webpack_require__(5).f;
var id = 0;
var isExtensible = Object.isExtensible || function () {
  return true;
};
var FREEZE = !__webpack_require__(16)(function () {
  return isExtensible(Object.preventExtensions({}));
});
var setMeta = function (it) {
  setDesc(it, META, { value: {
    i: 'O' + ++id, // object ID
    w: {}          // weak collections IDs
  } });
};
var fastKey = function (it, create) {
  // return primitive with prefix
  if (!isObject(it)) return typeof it == 'symbol' ? it : (typeof it == 'string' ? 'S' : 'P') + it;
  if (!has(it, META)) {
    // can't set metadata to uncaught frozen object
    if (!isExtensible(it)) return 'F';
    // not necessary to add metadata
    if (!create) return 'E';
    // add missing metadata
    setMeta(it);
  // return object ID
  } return it[META].i;
};
var getWeak = function (it, create) {
  if (!has(it, META)) {
    // can't set metadata to uncaught frozen object
    if (!isExtensible(it)) return true;
    // not necessary to add metadata
    if (!create) return false;
    // add missing metadata
    setMeta(it);
  // return hash weak collections IDs
  } return it[META].w;
};
// add metadata on freeze-family methods calling
var onFreeze = function (it) {
  if (FREEZE && meta.NEED && isExtensible(it) && !has(it, META)) setMeta(it);
  return it;
};
var meta = module.exports = {
  KEY: META,
  NEED: false,
  fastKey: fastKey,
  getWeak: getWeak,
  onFreeze: onFreeze
};


/***/ }),
/* 95 */
/***/ (function(module, exports, __webpack_require__) {

// all enumerable object keys, includes symbols
var getKeys = __webpack_require__(27);
var gOPS = __webpack_require__(52);
var pIE = __webpack_require__(34);
module.exports = function (it) {
  var result = getKeys(it);
  var getSymbols = gOPS.f;
  if (getSymbols) {
    var symbols = getSymbols(it);
    var isEnum = pIE.f;
    var i = 0;
    var key;
    while (symbols.length > i) if (isEnum.call(it, key = symbols[i++])) result.push(key);
  } return result;
};


/***/ }),
/* 96 */
/***/ (function(module, exports, __webpack_require__) {

// 7.2.2 IsArray(argument)
var cof = __webpack_require__(13);
module.exports = Array.isArray || function isArray(arg) {
  return cof(arg) == 'Array';
};


/***/ }),
/* 97 */
/***/ (function(module, exports, __webpack_require__) {

// fallback for IE11 buggy Object.getOwnPropertyNames with iframe and window
var toIObject = __webpack_require__(9);
var gOPN = __webpack_require__(53).f;
var toString = {}.toString;

var windowNames = typeof window == 'object' && window && Object.getOwnPropertyNames
  ? Object.getOwnPropertyNames(window) : [];

var getWindowNames = function (it) {
  try {
    return gOPN(it);
  } catch (e) {
    return windowNames.slice();
  }
};

module.exports.f = function getOwnPropertyNames(it) {
  return windowNames && toString.call(it) == '[object Window]' ? getWindowNames(it) : gOPN(toIObject(it));
};


/***/ }),
/* 98 */
/***/ (function(module, exports, __webpack_require__) {

var pIE = __webpack_require__(34);
var createDesc = __webpack_require__(17);
var toIObject = __webpack_require__(9);
var toPrimitive = __webpack_require__(26);
var has = __webpack_require__(8);
var IE8_DOM_DEFINE = __webpack_require__(39);
var gOPD = Object.getOwnPropertyDescriptor;

exports.f = __webpack_require__(7) ? gOPD : function getOwnPropertyDescriptor(O, P) {
  O = toIObject(O);
  P = toPrimitive(P, true);
  if (IE8_DOM_DEFINE) try {
    return gOPD(O, P);
  } catch (e) { /* empty */ }
  if (has(O, P)) return createDesc(!pIE.f.call(O, P), O[P]);
};


/***/ }),
/* 99 */
/***/ (function(module, exports, __webpack_require__) {

__webpack_require__(33)('asyncIterator');


/***/ }),
/* 100 */
/***/ (function(module, exports, __webpack_require__) {

__webpack_require__(33)('observable');


/***/ }),
/* 101 */
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
    value: true
});

var _stringify = __webpack_require__(22);

var _stringify2 = _interopRequireDefault(_stringify);

var _commonUtil = __webpack_require__(20);

var _commonUtil2 = _interopRequireDefault(_commonUtil);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

var globalUtil = __webpack_require__(21).default;
var httpUtil = __webpack_require__(35).default;

var vtube = function vtube(mainOpts) {
    var self = this;
    var baseUrl = 'http://127.0.0.1:8084/';

    function getConfig() {
        if (window.nxt && window.nxt.config) {
            return window.nxt.config;
        } else if (mainOpts.configInst) {
            return mainOpts.configInst;
        }
    }

    function getCompleteUrl() {
        var callUrl = 'http://' + location.host;
        // if (nxt.config.server != '') {
        //     callUrl = callUrl + nxt.config.server + '/';
        // }
        var apiVersion = _commonUtil2.default.getCookie('apiVersion');
        var url = '/upload/complete';
        if (apiVersion && apiVersion === 'mamcore2.3') {
            url = '/sflud/v1/upload/complete';
        }
        return callUrl + url;
    }

    function getRequestParams(task) {
        var res = {
            TaskName: task.metadata.name,
            TaskGuid: task.taskId,
            TransType: 'Upload',
            TransFile: [],
            UserInfo: {
                UserId: nxt.user.current.id.toString(),
                UserName: nxt.user.current.nickName || nxt.user.current.loginName,
                UserCode: nxt.user.current.userCode
            },
            ExtendeAttr: []
        };
        if (getConfig().vtubeInfo.importType == 1) {
            res.CallBackUrl = getCompleteUrl();
        }
        if (task.serverInfo) {
            res.ServerInfo = {
                HostName: task.serverInfo.hostName,
                Port: task.serverInfo.port,
                Scheme: task.serverInfo.scheme,
                UserName: task.serverInfo.userName,
                Password: task.serverInfo.password,
                PathRoot: task.serverInfo.pathRoot
            };
        }
        res.ExtendeAttr = _.map(task.metadata.field, function (item) {
            var n = { ItemCode: item.fieldName, ItemName: item.alias };
            if (item.controlType == 8) {
                if (item.value != null && item.value != '' && item.value != '[]') {
                    try {
                        n.Value = JSON.parse(item.value)[0];
                    } catch (e) {
                        n.Value = '';
                    }
                } else {
                    n.Value = '';
                }
            } else {
                n.Value = item.value || '';
            }
            return n;
        });
        res.ExtendeAttr.push({
            ItemCode: 'tree',
            ItemName: '目录树',
            Value: task.tree
        });
        _.remove(res.ExtendeAttr, { ItemCode: 'cliptype' });
        res.ExtendeAttr.push({
            ItemCode: 'cliptype',
            ItemName: '素材类型',
            Value: task.entityType
        });
        return res;
    }

    this.openFileSelector = function (callback, errorCallback) {
        $.ajax({
            url: baseUrl + 'request/getuploadfiles?user_token=&filetype=all',
            success: function success(res) {
                var data = _.isString(res) ? JSON.parse(res) : res;
                if (data.FileCount === 0) return;
                var result = [];
                _.forEach(data.FileList, function (item) {
                    var ext = '.' + _commonUtil2.default.getExtension(item.FilePath);
                    result.push({
                        entityType: _commonUtil2.default.getTypeByExt(ext, getConfig()).code,
                        fileName: _commonUtil2.default.getFullFileName(item.FilePath),
                        metadata: {
                            name: _commonUtil2.default.getFileName(item.FilePath),
                            ext: ext
                        },
                        status: 'added',
                        progress: 0,
                        file: item
                    });
                });
                callback(result);
            },
            error: errorCallback || function (res) {
                console.error(res);
                var vtubeDownloadPath = getConfig().vtubeDownloadPath || getConfig().server + '/assets/Sobey_vRocket_v2.0_Setup.exe';
                _commonUtil2.default.prompt(l('upload.clientInstallTip', '<p>打开客户端失败</p><p>请确定已经正常开启客户端或重启客户端再重试。</p><p>如没有安装，请点击下面地址。</p><p><a href="${path}"><i class="icon iconfont icon-iconfontxiazai" style="margin-right:5px"></i>点击客户端下载</a></p>', { path: vtubeDownloadPath }));
            }
        });
    };

    this.createTask = function (dto, params) {
        var list = [];
        switch (params.taskType) {
            case 1:
                //普通类型
                if (!_.isArray(dto)) {
                    dto = [dto];
                }
                _.forEach(dto, function (task) {
                    if (task.file) {
                        task.files = [{
                            file: task.file,
                            fileName: task.fileName,
                            fileSize: task.file.FileSize
                        }];
                        delete task.file;
                        list.push(task);
                    } else if (task.files && task.files.length > 0) {
                        task.files = _.map(task.files, function (file) {
                            return {
                                file: file.file,
                                fileName: file.fileName,
                                fileSize: file.fileSize
                            };
                        });
                        list.push(task);
                    }
                });
                break;
            case 2:
            case 3:
                //普通类型
                list.push(dto);
                break;
        }
        _.forEach(list, function (task) {
            task.taskType = params.taskType;
            task.transferType = params.transferType;
            task.targetFolder = params.targetFolder;
            task.relationContentType = params.relationContentType;
            task.relationContentId = params.relationContentId;
            self.addTask(task);
        });
    };

    this.addTask = function (task) {
        var apiVersion = _commonUtil2.default.getCookie('apiVersion');
        var url = '/upload/multipart/init?token=' + mainOpts.loginToken;
        if (apiVersion && apiVersion === 'mamcore2.3') {
            url = '/sflud/v1/upload/multipart/init?token=' + mainOpts.loginToken;
        }
        httpUtil.post(getConfig().server + url, task).then(function (res) {
            res = res.data;
            var parms = getRequestParams(res);
            parms.ExtendData = res.userToken;
            function getPath(file) {
                if (getConfig().isHandleHttpPath && res && res.ossClientInfo) {
                    var hostname = window.location.hostname;
                    var absolutePath = file.absolutePath.split('@');
                    if (absolutePath.length > 1) {
                        var startChar = absolutePath[1].indexOf(':') > -1 ? ':' : '/';
                        file.absolutePath = absolutePath[0] + '@' + hostname + absolutePath[1].substr(absolutePath[1].indexOf(startChar));
                    }
                }
                return res && res.ossClientInfo ? file.absolutePath : file.relativePath;
            }

            switch (task.taskType) {
                case 1:
                    parms.TransFile.push({
                        SourceFile: task.files[0].file.FilePath,
                        DestFile: getPath(res.files[0])
                    });
                    break;
                case 2:
                case 3:
                    _.forEach(res.files, function (file) {
                        parms.TransFile.push({
                            SourceFile: file.fileName,
                            DestFile: getPath(file)
                        });
                    });
                    break;
                default:
            }
            $.ajax({
                url: baseUrl + 'request/addtask?user_token=',
                type: 'POST',
                contentType: 'application/json',
                data: (0, _stringify2.default)(parms),
                success: function success(result) {
                    if (typeof result === 'string') {
                        result = JSON.parse(result);
                    }
                    if (result.Result == 1) {
                        _commonUtil2.default.msgOk(l('upload.addTaskOk', '添加任务成功'));
                    } else {
                        _commonUtil2.default.prompt(l('upload.addTaskError', '添加任务失败：') + result.Msg);
                    }
                    console.info(result);
                },
                error: function error(result) {
                    console.info(result);
                    _commonUtil2.default.prompt(l('upload.addTaskError', '添加任务失败'));
                }
            });
        }, function (res) {
            _commonUtil2.default.prompt(l('upload.uploadError', '上传失败：') + res.data.desc);
        });
    };
};

exports.default = vtube;

/***/ }),
/* 102 */,
/* 103 */,
/* 104 */,
/* 105 */,
/* 106 */,
/* 107 */,
/* 108 */,
/* 109 */,
/* 110 */,
/* 111 */,
/* 112 */,
/* 113 */,
/* 114 */,
/* 115 */,
/* 116 */,
/* 117 */,
/* 118 */,
/* 119 */
/***/ (function(module, exports, __webpack_require__) {

"use strict";


var web = __webpack_require__(81);
var vtube = __webpack_require__(101);
var thirdpartSupport = __webpack_require__(120);

var mamUpload = {
    init: function init(options) {
        mamUpload.web = new web.default(options);
        mamUpload.vtube = new vtube.default(options);
        mamUpload.thirdpartSupport = new thirdpartSupport.default(options);

        if (window.OSS) {
            mamUpload.web.OSS = window.OSS;
        }
    }
};

window.mamUpload = mamUpload;

/***/ }),
/* 120 */
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
    value: true
});
var thirdpartSupport = function thirdpartSupport(mainOpts) {
    this.handlePicPkgTaskMetadataName = function (tasks) {
        _.forEach(tasks.files, function (file, index) {
            if (index === 0 && (tasks.metadata.name == null || tasks.metadata.name === '')) {
                tasks.metadata.name = file.metadata.name;
            }
        });
    };
};
exports.default = thirdpartSupport;

/***/ })
/******/ ]);
//# sourceMappingURL=mam-upload-pure.js.map