import React, { FC, useState, useRef, useEffect } from 'react';
import {
  Form,
  Input,
  Button,
  DatePicker,
  TreeSelect,
  Row,
  Col,
  Select,
} from 'antd';
import { CaretDownOutlined, CaretUpOutlined, SearchOutlined, UndoOutlined, CloseCircleOutlined } from '@ant-design/icons';
import contentListApis from '@/service/contentListApis';
import TeacherItem from '@/components/formItemBox/teacherItem';
import './index.less';
import {
  formatMessage,
  useIntl,
  useHistory,
  useSelector,
  IPermission,
} from 'umi';
import { MenuItem } from './type';
import globalParams from '@/permission/globalParams';
import { IconFont } from '../iconFont/iconFont';
import { fullOptions, options, positions } from '.';
const { RangePicker } = DatePicker;
const { Option } = Select;

interface ContentData {
  // optionsChange?: (value: string) => void;
  resourceSearch: (data: any) => void;
  form: any;
  selected: any;
  expand: boolean;
  reset: () => void;
  filterOptions: boolean;
}

interface ITree {
  title: string;
  key: string;
  children?: ITree[];
}

const MobileSearch: React.FC<ContentData> = props => {
  let history: any = useHistory();
  const hidecatalogue =
    JSON.stringify(history.location.query) === '{}'
      ? '0'
      : history.location.query.hidecatalogue;
  // console.log('history.location.query',history.location.query);
  const initData: any = {
    format: undefined,
    name_: undefined,
    data: undefined,
    college: undefined,
    major: undefined,
    guideScreen: undefined,
    teacher: undefined,
    creator: undefined,
    seat: undefined,
    semester: undefined,
    source: undefined,
    usestatus: undefined,
    smart_status: undefined,
    asr_status: undefined,
    ocr_status: undefined,
  }
  const { resourceSearch, form, reset, expand, selected, filterOptions } = props;
  const [majorList, setMajorList] = useState<ITree[]>([]);
  const [analysis, setAnalysis] = useState<any>([]);
  const [tempData, setTempData] = useState<any>(initData);
  const [outTempDate, setOutTempData] = useState<any>({})
  const [formData, setFormData] = useState<any>(initData);
  const [semester, setSemester] = useState<any>([]);//学期
  const [source, setSource] = useState<any>([]);//来源
  const [moreSearch, setMoreSearch] = useState<boolean>(false);
  // 权限
  const { rmanGlobalParameter } = useSelector<{ permission: any }, IPermission>(
    ({ permission }) => permission,
  );
  const intl = useIntl();
  useEffect(() => {
    contentListApis.getSemesterData().then((res: any) => {
      if (res && res.errorCode === 'success') {
        setSemester(res.extendMessage);
      }
    })
    contentListApis.getVideoSource().then((res: any) => {
      if (res?.success) {
        setSource(res.data);
      }
    })
  }, []);
  const setTreeData = (t: any[]): any => {
    return t.map(item => {
      if (item.children && item.children.length > 0) {
        return {
          title: item.categoryName,
          key: item.categoryCode,
          children: setTreeData(item.children),
        };
      } else {
        return {
          title: item.categoryName,
          key: item.categoryCode,
        };
      }
    });
  };
  useEffect(() => {
    let param: any = {
      labels: undefined,
      ocr_status: undefined,
      asr_status: undefined
    };
    analysis.map((item: any) => {
      param[item.split('~')[0]] = item.split('~')?.[1]
    })
    setTempData({
      ...tempData,
      ...param
    })
  }, [analysis])
  const formChange = (name: any, value: any) => {
    setTempData({
      ...tempData,
      [name]: value || undefined
    })
  };
  const oldFormChange = () => {
    console.log(form.getFieldsValue());
    const temp = form.getFieldsValue();
    setOutTempData({
      ...temp,
      name_: temp.search.name_,
      format: temp.search.format
    })
    setFormData({
      ...formData,
      ...temp,
      name_: temp.search.name_,
      format: temp.search.format
    })
  };
  useEffect(() => {
    //目的是为了不让第一次调用resourceSearch方法导致父组件方法会调用两次
    // if(JSON.stringify(formData)!==JSON.stringify(initData)){
    resourceSearch(formData);
    // }
  }, [formData])
  useEffect(() => {
    //一旦切换其他目录 重置搜索词
    if (selected !== '共享资源') {
      window.localStorage.setItem('searchKeyWord', '{}');
      console.log('重置搜索词了');
    }
  }, [selected])
  const searchReset = () => {
    setTempData(initData);
    setAnalysis([])
    // resourceSearch(initData);
  };
  const searchConfirm = () => {
    setFormData({...tempData, ...outTempDate})
    setMoreSearch(false);
  };
  return (
    <div className={`search_mobile`}>
      <div className='catalogue'>
        <Form
          name="complex-form"
          layout="inline"
          form={form}
          initialValues={{
            search: {
              format: '',
            },
          }}
          style={{ width: '100%', height: '100%' }}
        >
          <Input.Group compact>
            <Form.Item name={['search', 'format']} noStyle>
              <Select className="type-width" onChange={oldFormChange}>
              {(filterOptions ? options : fullOptions).map((item, index) => (
                  <Option value={item.value || ''} key={'type' + index}>
                    {item.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name={['search', 'name_']} noStyle>
              <Input
                placeholder={intl.formatMessage({ id: '输入关键词' })}
                className="keyword-width"
                allowClear
                autoComplete={'off'}
                addonAfter={<IconFont onClick={oldFormChange} type='iconsousuo2' />}
              />
            </Form.Item>
          </Input.Group>
          <Button
            onClick={() => setMoreSearch(true)}
            icon={<IconFont type='iconshaixuan' />}
          >{intl.formatMessage({ id: '筛选' })}</Button>
        </Form>
      </div>
      {
        <>
          <div className={moreSearch?'ant-modal-mask':''}></div>
          <div className={`moreSearch${moreSearch ? '' : ' none'}`}>
            <div className='head'>
              <span>{intl.formatMessage({ id: '更多筛选' })}</span>
              <IconFont type='iconguanbi2' onClick={() => setMoreSearch(false)} />
            </div>
            {
              ['个人资源', '群组资源', '共享资源', '录播资源', 'myVideo', 'departmentVideo'].includes(selected) &&
              <div className='public_group'>
                <div className='item'>
                  <Select
                    placeholder={intl.formatMessage({ id: '是否有语音文本、知识点、关键词' })}
                    mode='multiple'
                    onChange={(e) => setAnalysis(e)}
                    value={analysis}
                    allowClear>
                    <Option value="asr_status~2" key="asr_status">
                      {intl.formatMessage({ id: '有语音文本' })}
                    </Option>
                    <Option value="ocr_status~2" key="ocr_status">
                      {intl.formatMessage({ id: '有知识点' })}
                    </Option>
                    <Option value="labels~11" key="labels">
                      {intl.formatMessage({ id: '有标签' })}
                    </Option>
                  </Select>
                </div>
                <div className='item'>
                  <RangePicker
                    showTime
                    format={'YYYY-MM-DD'}
                    value={tempData.data}
                    placeholder={[intl.formatMessage({ id: '起始时间' }), intl.formatMessage({ id: '终止时间' })]}
                    className="textcenter"
                    onChange={(e) => formChange('data', e)}
                    getPopupContainer={(e: any) => e.parentElement.parentElement}
                    style={{ width: '100%' }} />
                </div>
                <div className='item'>
                  <Select
                    allowClear
                    value={tempData.usestatus || null}
                    onChange={(e) => formChange('usestatus', e)}
                    placeholder={intl.formatMessage({ id: '资源使用状态' })}>
                    <Option value="true" key="true">
                      {intl.formatMessage({ id: '已使用' })}
                    </Option>
                    <Option value="false" key="false">
                      {intl.formatMessage({ id: '未使用' })}
                    </Option>
                  </Select>
                </div>
                {
                  ['录播资源', 'myVideo', 'departmentVideo'].includes(selected) &&
                  <>
                    <div className='item'>
                      <Select className="guideScreen"
                        placeholder={intl.formatMessage({ id: '录播机位' })}
                        mode='multiple'
                        value={tempData.seat}
                        onChange={(e) => formChange('seat', e)}
                        maxTagCount={3}
                        allowClear
                      >
                        {positions.map((item, index) => (
                          <Option value={item.value || ''} key={'type' + index}>
                            {item.label}
                          </Option>
                        ))}
                      </Select>
                    </div>
                    <div className='item'>
                      <Select className="source"
                        placeholder={intl.formatMessage({ id: '来源' })}
                        value={tempData.source}
                        mode='multiple'
                        allowClear
                        onChange={(e) => formChange('source', e)}
                        maxTagCount={3}
                      >
                        {source.map((item: any, index: any) => (
                          <Option value={item || ''} key={index}>
                            {item}
                          </Option>
                        ))}
                      </Select>
                    </div>
                    <div className='item'>
                      <Select
                        className="semester"
                        value={tempData.semester}
                        placeholder={intl.formatMessage({ id: '学期' })}
                        allowClear
                        onChange={(e) => formChange('semester', e)}
                      >
                        {
                          semester?.map((item: any, index: number) => {
                            return <Option value={item.name} key={index}>{item.name}</Option>
                          })
                        }
                      </Select>
                    </div>
                  </>
                }
              </div>
            }
            <div className='btns'>
              <Button onClick={searchReset}>{intl.formatMessage({ id: '重置' })}</Button>
              <Button type='primary' onClick={searchConfirm}>{intl.formatMessage({ id: '确认' })}</Button>
            </div>
          </div>
        </>
      }
    </div>
  );
};

export default MobileSearch;
