.links_box {
  display: flex;
  // margin-left: 20px;
  cursor: pointer;

  img {
    width: 14px;
    // height: 17px;
  }
  >div{
    height: 25px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 10px;
    position: relative;
    color: #525252;
    &:hover{
      color:var(--primary-color)
    }
    &[disabled]{
      color: rgba(0, 0, 0, 0.25)
    }
    >span:last-child{
      margin-left: 8px;
    }
    &.disabled{
      color: rgba(0, 0, 0, 0.25)
    }
    &:not(:last-child)::after{
      content: "";
      display: inline-block;
      width: 1px;
      height: 16px;
      background: #D8D8D8;
      right: 0;
      top: 4px;
      position: absolute;
    }
  }
  .ant-btn {
    // width: 36px;
    height: 25px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 10px;
    // color: var(--primary-color);
    // border-color: var(--primary-color);
  }
  .ant-btn-link{
    color: #525252;
    &:hover{
      color:var(--primary-color)
    }
    &[disabled]{
      color: rgba(0, 0, 0, 0.25)
    }
  }
  .ant-btn-link:not(:last-child)::after{
    content: "";
    display: inline-block;
    width: 1px;
    height: 16px;
    background: #D8D8D8;
    right: 0;
    top: 4px;
    position: absolute;
  }
  // .ant-btn:hover{
  //   background: var(--primary-color);
  //   color: white;
  // }
  .links_item {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 36px;
    height: 25px;
    background: rgba(228, 228, 228, 1);
    margin-left: 2px;
    transition: all .5s;

    .icon {
      font-size: 13px;
    }
  }

  .bgcolor {
    background-color: #F29402;
    color: #FDF0DB;
  }

  .download-box {
    display: none;
    // opacity: 0;
  }

  .ant-btn-primary[disabled], .ant-btn-primary[disabled]:hover, .ant-btn-primary[disabled]:focus, .ant-btn-primary[disabled]:active {
    color: #4C4C4C;
    background-color: #F1F4F7;
    border-color: #F1F4F7;
  }
}

.mobile_btns{
  >div{
    margin-bottom: 10px;
    &.disabled{
      color: rgba(0, 0, 0, 0.25)
    }
    >span.anticon{
      margin-right: 5px;
    }
  }
}