import React from 'react';
import { Mo<PERSON>, Button } from 'antd';
import './index.less';
import InputItem from './inputitem';
interface IBasicProps {
  title: string;
  detail: string;
  // modalVisible: boolean;
  // title: string;
  // footers:Array<any>
}

const BasicInformationItem: React.FC<IBasicProps> = (props) => {
  const {title, detail} = props;
  return (
    <div>
      <div className='basicinformation_item'>
        <span>{title}：</span>
        <span>{detail}</span>
      </div>
      <InputItem
      title='12'
      />
    </div>
    
  );
};

export default BasicInformationItem;
