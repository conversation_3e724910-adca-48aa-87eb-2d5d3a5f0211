namespace SmartTypes {
  export interface Metadata<T> {
    data: {
      metadata: T[];
      name: string;
      type: string;
    }[];
    isSuccess: boolean;
  }

  export interface SequenceMeta {
    fragment_description?: string;
    guid_?: string;
    title?: string;
    inpoint: number;
    outpoint: number;
    keyframeno?: number;
    keyframepath?: string;
  }

  export interface SequenceRes extends Metadata<SequenceMeta> {}

  export interface Lyrics {
    guid_: string;
    text: string;
    text_en?: string;
    _in: number;
    _out: number;
  }

  export interface LyricsRes extends Metadata<Lyrics> {}

  export interface FileEntity {
    bitRate: number;
    displayPath: string;
    duration: number;
    fileSize: number;
    frameRate: number;
  }

  export interface test {}
}
export default SmartTypes;
