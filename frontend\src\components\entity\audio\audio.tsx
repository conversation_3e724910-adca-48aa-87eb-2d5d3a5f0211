import React, { <PERSON> } from 'react'
import { IBaseEntityTypes } from '@/types/entityTypes'
import { dynamic } from 'umi'
import Loading from '@/loading'
import { IPlayerProps } from '../player/player'

interface IAudioProps extends IBaseEntityTypes {
    keyframes?: string // 音频播放的图片
}

const AsyncPlayer = (() => dynamic({
    loader: async () => {
        const { default: Player } = await import('../player/player')
        return Player
    },
    loading: Loading
}) as unknown as React.ComponentClass<IPlayerProps, any>)()

const Audio: FC<IAudioProps> = ({ src, keyframes, onError }) => {
    return <AsyncPlayer type="audio" src={src} keyframes={keyframes || ""} onError={onError}></AsyncPlayer>
}

export default Audio