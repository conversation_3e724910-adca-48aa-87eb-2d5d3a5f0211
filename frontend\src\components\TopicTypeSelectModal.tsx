import { Modal, Radio } from "antd";
import React, { FC, useState } from "react";
import { optionType_ } from "@/utils";
import { useIntl} from 'umi';

interface TopicTypeSelectProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (type: number) => void;
}

const TopicTypeSelectModal: FC<TopicTypeSelectProps> = ({ visible, onClose, onConfirm }) => {
  const [value, setValue] = useState<number>(0);
  const intl = useIntl();
  const init = () => {
    setValue(0);
    onClose();
  };
  return <Modal
    title={intl.formatMessage({id: '新建题目类型'})}
    open={visible}
    onCancel={init}
    onOk={() => {
      onConfirm(value);
      init();
    }}
  >
    <Radio.Group value={value} onChange={(e: any) => setValue(e.target.value)}>
      {optionType_.map((item: string, index: number) => item!==intl.formatMessage({id: '主观'})&&<Radio value={index} key={index}>{item}{intl.formatMessage({id: '题'})}</Radio>)}
    </Radio.Group>
  </Modal>;
};

export default TopicTypeSelectModal;