import React, { useState, useEffect, useRef } from 'react';
import { Modal, Checkbox, Button, Table, message, Tooltip } from 'antd';
import SmartService from '@/service/smartService';
import './index.less';
import { l100Ns2Tc$1 } from '@/utils';
import { IPermission } from '@/models/permission';
import globalParams from '@/permission/globalParams';
import LanguageVisible from './LanguageModal';
import perCfg from '@/permission/config';
import { useSelector, useIntl } from 'umi';
interface CreateModalProps {
  modalVisible: boolean;
  modalClose: () => void;
  analysislist: any;
  className_?: string;
  pubOrPri: boolean;
  voiceContent: any;
  refresh?: (tag?: boolean, voice?: boolean, point?: boolean) => any;
}
const IntelligentAnalysisModal: React.FC<CreateModalProps> = props => {
  const intl = useIntl();
  const { modalClose, modalVisible, analysislist, className_, refresh, voiceContent } = props;
  const [downData, setDownData] = useState<any>([]);
  // 语音
  const [analysisCheckList, setAnalysisCheckList] = useState<any>([]);
  const [analysisALLCheck, setAnalysisALLCheck] = useState<boolean>(false);
  // 翻译
  const [translateCheckList, setTranslateCheckList] = useState<any>([]);
  const [translateALLCheck, setTranslateALLCheck] = useState<boolean>(false);
  //智能标签
  const [smartTagCheckList, setSmartTagCheckList] = useState<any>([]);
  const [smartTagAllCheck, setSmartTagAllCheck] = useState<boolean>(false);
  // 知识点
  const [pointsCheckList, setPointsCheckList] = useState<any>([]);
  const [pointsALLCheck, setPointsALLCheck] = useState<boolean>(false);
  // 人物
  const [characterCheckList, setCharacterCheckList] = useState<any>([]);
  const [characterAllCheck, setCharacterAllCheck] = useState<boolean>(false);
  // 敏感词
  const [sensitiveCheckList, setSensitiveCheckList] = useState<any>([]);
  const [sensitiveAllCheck, setSensitiveAllCheck] = useState<boolean>(false);
  // 摘要
  const [summaryCheckList, setSummaryCheckList] = useState<any>([]);
  const [summaryAllCheck, setSummaryAllCheck] = useState<boolean>(false);
  //截图
  const [screenshotCheckList, setScreenshotCheckList] = useState<any>([]);
  const [screenAllCheck, setScreenAllCheck] = useState<boolean>(false);
  // 智能拆解
  const [disassembleCheckList, setDisassembleCheckList] = useState<any>([]);
  const [disassembleAllCheck, setDisassembleAllCheck] = useState<boolean>(false);
  // 视频质量
  const [videoQualityCheckList, setVideoQualityCheckList] = useState<any>([]);
  const [videoQualityAllCheck, setVideoQualityAllCheck] = useState<boolean>(false);
  // 人数
  const [peopleNumCheckList, setPeopleNumCheckList] = useState<any>([]);
  const [peopleNumAllCheck, setPeopleNumAllCheck] = useState<boolean>(false);
  // 物体
  const [objectCheckList, setObjectCheckList] = useState<any>([]);
  const [objectAllCheck, setObjectAllCheck] = useState<boolean>(false);
  // 美声
  const [belcantoCheckList, setbelcantoCheckList] = useState<any>([]);
  const [belcantoAllCheck, setbelcantoAllCheck] = useState<boolean>(false);
    // 人脸
  const [faceCheckList, setFaceCheckList] = useState<any>([]);
  const [faceAllCheck, setFaceAllCheck] = useState<boolean>(false);
  // 知识要点
  const [knowledgeCheckList, setKnowledgeCheckList] = useState<any>([]);
  const [knowledgeAllCheck, setKnowledgeAllCheck] = useState<boolean>(false);
  // 内容审核
  const [contentCheckList, setContentCheckList] = useState<any>([]);
  const [contentAllCheck, setContentAllCheck] = useState<boolean>(false);
  // 权限
  const [languageVisible, setLanguageModalVisible] = useState<boolean>(false);
  const [languageId, setLanguageId] = useState<string>('');
  const [targetIds, setTargetIds] = useState<any[]>([]);
  const [sourceId, setSourceId] = useState<any>('')
  const { rmanGlobalParameter, permissions } = useSelector<
    { permission: any },
    IPermission
  >(({ permission }) => permission);
  const { mobileFlag } = useSelector<{ config: any }, any>(
    ({ config }) => config,
  );
  useEffect(() => {
    if (modalVisible) {
      console.log(analysislist);
      setDownData(analysislist);
    }
  }, [modalVisible]);
  // 语音
  const changeAnalysisCheckList = (id: string) => {
    const list: any = [...analysisCheckList];
    let index = list.indexOf(id);
    if (index === -1) {
      list.push(id);
    } else {
      list.splice(index, 1);
    }
    setAnalysisALLCheck(list.length === downData.length);
    setAnalysisCheckList(list);
  };
  const changeAnalysisCheckALLList = (e: any) => {
    setAnalysisALLCheck(e.target.checked);
    if (e.target.checked) {
      setAnalysisCheckList(
        downData.map((item: any) => {
          return item.contentId_;
        }),
      );
    } else {
      setAnalysisCheckList([]);
    }
  };
  // 翻译
  const changeTranslateCheckList = (id: string) => {
    const list: any = [...translateCheckList];
    let index = list.indexOf(id);
    if (index === -1) {
      list.push(id);
    } else {
      list.splice(index, 1);
    }
    setTranslateALLCheck(list.length === downData.length);
    setTranslateCheckList(list);
    //翻译依赖语音分析 考虑到已经完成语音的情况 便只在传参处理即可
    // let index_ = analysisCheckList.indexOf(id);
    // if (index_ === -1) {
    //   analysisCheckList.push(id);
    // }
    // setAnalysisCheckList([...analysisCheckList]);
    // setAnalysisALLCheck(analysisCheckList.length === downData.length);
  };
  useEffect(() => {
    if(translateCheckList.length){
      setLanguageModalVisible(true)
    }
  }, [translateCheckList])
  const changeTranslateCheckALLList = (e: any) => {
    setTranslateALLCheck(e.target.checked);
    if (e.target.checked) {
      // setAnalysisALLCheck(true)
      setTranslateCheckList(
        downData.map((item: any) => {
          return item.contentId_;
        }),
      );
      //翻译依赖语音分析 考虑到已经完成语音的情况 便只在传参处理即可
      // setAnalysisCheckList(
      //   downData.map((item: any) => {
      //     return item.contentId_;
      //   }),
      // );
    } else {
      setTranslateCheckList([]);
    }
  };
  // 智能标签
  const changeSmartTagCheckList = (id: string) => {
    const list: any = [...smartTagCheckList];
    let index = list.indexOf(id);
    if (index === -1) {
      list.push(id);
    } else {
      list.splice(index, 1);
    }
    setSmartTagAllCheck(list.length === downData.length);
    setSmartTagCheckList(list);
  };
  const changeSmartTagCheckAllList = (e: any) => {
    setSmartTagAllCheck(e.target.checked);
    if (e.target.checked) {
      setSmartTagCheckList(
        downData.map((item: any) => {
          return item.contentId_;
        }),
      );
    } else {
      setSmartTagCheckList([]);
    }
  };
  const changeCharacterTagCheckList = (id: string) => {
    const list: any = [...characterCheckList];
    let index = list.indexOf(id);
    if (index === -1) {
      list.push(id);
    } else {
      list.splice(index, 1);
    }
    setCharacterAllCheck(list.length === downData.length);
    setCharacterCheckList(list);
  };
  const changeCharacterTagCheckAllList = (e: any) => {
    setCharacterAllCheck(e.target.checked);
    if (e.target.checked) {
      setCharacterCheckList(
        downData.map((item: any) => {
          return item.contentId_;
        }),
      );
    } else {
      setCharacterCheckList([]);
    }
  };
  const changeSensitiveTagCheckList = (id: string) => {
    const list: any = [...sensitiveCheckList];
    let index = list.indexOf(id);
    if (index === -1) {
      list.push(id);
    } else {
      list.splice(index, 1);
    }
    setSensitiveAllCheck(list.length === downData.length);
    setSensitiveCheckList(list);
  };
  const changeSensitiveTagCheckAllList = (e: any) => {
    setSensitiveAllCheck(e.target.checked);
    if (e.target.checked) {
      setSensitiveCheckList(
        downData.map((item: any) => {
          return item.contentId_;
        }),
      );
    } else {
      setSensitiveCheckList([]);
    }
  };
  const changeSummaryTagCheckList = (id: string) => {
    const list: any = [...summaryCheckList];
    let index = list.indexOf(id);
    if (index === -1) {
      list.push(id);
    } else {
      list.splice(index, 1);
    }
    setSummaryAllCheck(list.length === downData.length);
    setSummaryCheckList(list);
  };
  const changeSummaryTagCheckAllList = (e: any) => {
    setSummaryAllCheck(e.target.checked);
    if (e.target.checked) {
      setSummaryCheckList(
        downData.map((item: any) => {
          return item.contentId_;
        }),
      );
    } else {
      setSummaryCheckList([]);
    }
  };
  const changeScreenshotTagCheckList = (id: string) => {
    const list: any = [...screenshotCheckList];
    let index = list.indexOf(id);
    if (index === -1) {
      list.push(id);
    } else {
      list.splice(index, 1);
    }
    setScreenAllCheck(list.length === downData.length);
    setScreenshotCheckList(list);
  };
  const changeScreenshotTagCheckAllList = (e: any) => {
    setScreenAllCheck(e.target.checked);
    if (e.target.checked) {
      setScreenshotCheckList(
        downData.map((item: any) => {
          return item.contentId_;
        }),
      );
    } else {
      setScreenshotCheckList([]);
    }
  };
  const changeDisassembleTagCheckList = (id: string) => {
    const list: any = [...disassembleCheckList];
    let index = list.indexOf(id);
    if (index === -1) {
      list.push(id);
    } else {
      list.splice(index, 1);
    }
    setDisassembleAllCheck(list.length === downData.length);
    setDisassembleCheckList(list);
  };

  const changeDisassembleTagCheckAllList = (e: any) => {
    setDisassembleAllCheck(e.target.checked);
    if (e.target.checked) {
      setDisassembleCheckList(
        downData.map((item: any) => {
          return item.contentId_;
        }),
      );
    } else {
      setDisassembleCheckList([]);
    }
  };
  const changeVideoQualityTagCheckList = (id: string) => {
      const list: any = [...videoQualityCheckList];
      let index = list.indexOf(id);
      if (index === -1) {
        list.push(id);
      } else {
        list.splice(index, 1);
      }
      setVideoQualityAllCheck(list.length === downData.length);
      setVideoQualityCheckList(list);
    };
  const changeVideoQualityTagCheckAllList = (e: any) => {
    setVideoQualityAllCheck(e.target.checked);
    if (e.target.checked) {
      setDisassembleCheckList(
        downData.map((item: any) => {
          return item.contentId_;
        }),
      );
    } else {
      setVideoQualityCheckList([]);
    }
  };
  // 知识点
  const changePointsCheckList = (id: string) => {
    const list: any = [...pointsCheckList];
    let index = list.indexOf(id);
    if (index === -1) {
      list.push(id);
    } else {
      list.splice(index, 1);
    }
    setPointsALLCheck(list.length === downData.length);
    setPointsCheckList(list);
  };
  const changePointsCheckALLList = (e: any) => {
    setPointsALLCheck(e.target.checked);
    if (e.target.checked) {
      let idList: string[] = [];
      downData.forEach((item: any) => {
        idList.push(item.contentId_);
        // if (item.type_ !== 'biz_sobey_audio') {

        // }
      });
      setPointsCheckList(idList);
    } else {
      setPointsCheckList([]);
    }
  };
  const changePeopleNumTagCheckList = (id: string) => {
    const list: any = [...peopleNumCheckList];
    let index = list.indexOf(id);
    if (index === -1) {
      list.push(id);
    } else {
      list.splice(index, 1);
    }
    setPeopleNumAllCheck(list.length === downData.length);
    setPeopleNumCheckList(list);
  };
  const changePeopleNumTagCheckAllList = (e: any) => {
    setPeopleNumAllCheck(e.target.checked);
    if (e.target.checked) {
      setPeopleNumCheckList(
        downData.map((item: any) => {
          return item.contentId_;
        }),
      );
    } else {
      setPeopleNumCheckList([]);
    }
  };
  
  const changeObjectTagCheckList = (id: string) => {
    const list: any = [...objectCheckList];
    let index = list.indexOf(id);
    if (index === -1) {
      list.push(id);
    } else {
      list.splice(index, 1);
    }
    setObjectAllCheck(list.length === downData.length);
    setObjectCheckList(list);
  };
  const changeObjectTagCheckAllList = (e: any) => {
    setObjectAllCheck(e.target.checked);
    if (e.target.checked) {
      setObjectCheckList(
        downData.map((item: any) => {
          return item.contentId_;
        }),
      );
    } else {
      setObjectCheckList([]);
    }
  };
  const changebelcantoTagCheckList = (id: string) => {
    const list: any = [...belcantoCheckList];
    let index = list.indexOf(id);
    if (index === -1) {
      list.push(id);
    } else {
      list.splice(index, 1);
    }
    setbelcantoAllCheck(list.length === downData.length);
    setbelcantoCheckList(list);
  };
  const changebelcantoTagCheckAllList = (e: any) => {
    setbelcantoAllCheck(e.target.checked);
    if (e.target.checked) {
      setbelcantoCheckList(
        downData.map((item: any) => {
          return item.contentId_;
        }),
      );
    } else {
      setbelcantoCheckList([]);
    }
  };

  const changeFaceTagCheckList = (id: string) => {
    const list: any = [...faceCheckList];
    let index = list.indexOf(id);
    if (index === -1) {
      list.push(id);
    } else {
      list.splice(index, 1);
    }
    setFaceAllCheck(list.length === downData.length);
    setFaceCheckList(list);
  };

  const changeFaceTagCheckAllList = (e: any) => {
    setFaceAllCheck(e.target.checked);
    if (e.target.checked) {
      setFaceCheckList(
        downData.map((item: any) => {
          return item.contentId_;
        }),
      );
    } else {
      setFaceCheckList([]);
    }
  };
  const changeKnowledgeTagCheckList = (id: string) => {
    const list: any = [...knowledgeCheckList];
    let index = list.indexOf(id);
    if (index === -1) {
      list.push(id);
    } else {
      list.splice(index, 1);
    }
    setKnowledgeAllCheck(list.length === downData.length);
    setKnowledgeCheckList(list);
  };
  const changeKnowledgeTagCheckAllList = (e: any) => {
    setKnowledgeAllCheck(e.target.checked);
    if (e.target.checked) {
      setKnowledgeCheckList(
        downData.map((item: any) => {
          return item.contentId_;
        }),
      );
    } else {
      setKnowledgeCheckList([]);
    }
  };
  const changeContentTagCheckList = (id: string) => {
    const list: any = [...contentCheckList];
    let index = list.indexOf(id);
    if (index === -1) {
      list.push(id);
    } else {
      list.splice(index, 1);
    }
    setContentAllCheck(list.length === downData.length);
    setContentCheckList(list);
  };
  const changeContentTagCheckAllList = (e: any) => {
    setContentAllCheck(e.target.checked);
    if (e.target.checked) {
      setContentCheckList(
        downData.map((item: any) => {
          return item.contentId_;
        }),
      );
    } else {
      setContentCheckList([]);
    }
  };
  let align = 'center';
  const columns: any = [
    {
      title: intl.formatMessage({ id: '标题' }),
      dataIndex: 'name_',
      align: 'left',
      ellipsis: true,
      width: mobileFlag ? '20%' : '10%',
    },
  ];
  if (!mobileFlag) {
    columns.push({
      title: intl.formatMessage({ id: '时长' }),
      width: '8%',
      dataIndex: 'duration',
      render: (value: number, row: any, index: number) => {
        return <span className='time'>{l100Ns2Tc$1(value, row.frameRate)}</span>;
      },
    });
    align = 'left';
  }
  let knowledge: any = {
    title: () => {
      return (
        <div className="analysis">
          <Checkbox
            onChange={changePointsCheckALLList}
            checked={pointsALLCheck}
            disabled={downData.every((item: any) => {
              return !['biz_sobey_video'].includes(item.type_);
            })}
          ></Checkbox>
          {intl.formatMessage({ id: '知识点' })}
        </div>
      );
    },
    dataIndex: 'analysis',
    width: '12%',
    align,
    render: (value: number, row: any, index: number) => {
      return (
        <Checkbox
          checked={pointsCheckList.includes(row.contentId_)}
          onChange={() => changePointsCheckList(row.contentId_)}
          disabled={!['biz_sobey_video'].includes(row.type_)}
        ></Checkbox>
      );
    },
  };
  let voice: any = {
    title: () => {
      return (
        <div className="analysis">
          <Checkbox
            onChange={changeAnalysisCheckALLList}
            checked={analysisALLCheck}
            disabled={downData.every((item: any) => {
              return !['biz_sobey_video', 'biz_sobey_audio'].includes(
                item.type_,
              );
            })}
          ></Checkbox>
          {intl.formatMessage({ id: '语音' })}
        </div>
      );
    },
    dataIndex: 'voice',
    // width:mobileFlag?'70px':'',
    width: '10%',
    align,
    render: (value: number, row: any, index: number) => {
      return (
        <Checkbox
          checked={analysisCheckList.includes(row.contentId_)}
          onChange={() => changeAnalysisCheckList(row.contentId_)}
          disabled={!['biz_sobey_video', 'biz_sobey_audio'].includes(row.type_)}
        ></Checkbox>
      );
    },
  };
  let translate: any = {
    title: () => {
      return (
        <div className="translate">
          <Checkbox
            onChange={changeTranslateCheckALLList}
            checked={translateALLCheck}
            disabled={downData.every((item: any) => {
              return !['biz_sobey_video', 'biz_sobey_audio'].includes(
                item.type_,
              );
            })}
          ></Checkbox>
          {intl.formatMessage({ id: '翻译语音' })}
        </div>
      );
    },
    dataIndex: 'translate',
    width: '10%',
    align,
    render: (value: number, row: any, index: number) => {
      return (
        <Checkbox
          checked={translateCheckList.includes(row.contentId_)}
          onChange={() => changeTranslateCheckList(row.contentId_)}
          disabled={!['biz_sobey_video', 'biz_sobey_audio'].includes(row.type_)}
        ></Checkbox>
      );
    },
  };
  let smartTag: any = {
    title: () => {
      return (
        <div className="smartTag">
          <Checkbox
            onChange={changeSmartTagCheckAllList}
            checked={smartTagAllCheck}
            disabled={downData.every((item: any) => {
              return ![
                'biz_sobey_video',
                'biz_sobey_audio',
                'biz_sobey_document',
              ].includes(item.type_);
            })}
          ></Checkbox>
          {intl.formatMessage({ id: '关键词' })}
        </div>
      );
    },
    dataIndex: 'smart',
    // width:mobileFlag?'70px':'',
    width: '10%',
    align,
    render: (value: number, row: any, index: number) => {
      return (
        <Checkbox
          checked={smartTagCheckList.includes(row.contentId_)}
          onChange={() => changeSmartTagCheckList(row.contentId_)}
          disabled={
            ![
              'biz_sobey_video',
              'biz_sobey_audio',
              'biz_sobey_document',
            ].includes(row.type_)
          }
        ></Checkbox>
      );
    },
  };
  let sensitiveFaceTag: any = {
    title: () => {
      return (
        <div className="sensitiveFaceTag">
          <Checkbox
            onChange={changeCharacterTagCheckAllList}
            checked={characterAllCheck}
            disabled={downData.every((item: any) => {
              return !['biz_sobey_video', 'biz_sobey_picture'].includes(
                item.type_,
              );
            })}
          ></Checkbox>
          {intl.formatMessage({ id: '人物' })}
        </div>
      );
    },
    dataIndex: 'face',
    width: '10%',
    align,
    render: (value: number, row: any, index: number) => {
      return (
        <Checkbox
          checked={characterCheckList.includes(row.contentId_)}
          onChange={() => changeCharacterTagCheckList(row.contentId_)}
          disabled={
            !['biz_sobey_video', 'biz_sobey_picture'].includes(row.type_)
          }
        ></Checkbox>
      );
    },
  };
  let sensitiveWordTag: any = {
    title: () => {
      return (
        <div className="sensitiveWordTag">
          <Checkbox
            onChange={changeSensitiveTagCheckAllList}
            checked={sensitiveAllCheck}
            disabled={downData.every((item: any) => {
              return ![
                'biz_sobey_video',
                'biz_sobey_picture',
                'biz_sobey_audio',
                'biz_sobey_document',
              ].includes(item.type_);
            })}
          ></Checkbox>
          {intl.formatMessage({ id: '敏感内容' })}
        </div>
      );
    },
    dataIndex: 'face',
    width: '10%',
    align,
    render: (value: number, row: any, index: number) => {
      return (
        <Checkbox
          checked={sensitiveCheckList.includes(row.contentId_)}
          onChange={() => changeSensitiveTagCheckList(row.contentId_)}
          disabled={
            ![
              'biz_sobey_video',
              'biz_sobey_picture',
              'biz_sobey_audio',
              'biz_sobey_document',
            ].includes(row.type_)
          }
        ></Checkbox>
      );
    },
  };
  let summaryTag: any = {
    title: () => {
      return (
        <div className="summaryTag">
          <Checkbox
            onChange={changeSummaryTagCheckAllList}
            checked={summaryAllCheck}
            disabled={downData.every((item: any) => {
              return ![
                'biz_sobey_video',
                'biz_sobey_audio',
                'biz_sobey_document',
              ].includes(item.type_);
            })}
          ></Checkbox>
          {intl.formatMessage({ id: '摘要' })}
        </div>
      );
    },
    dataIndex: 'summary',
    width: '10%',
    align,
    render: (value: number, row: any, index: number) => {
      return (
        <Checkbox
          checked={summaryCheckList.includes(row.contentId_)}
          onChange={() => changeSummaryTagCheckList(row.contentId_)}
          disabled={
            ![
              'biz_sobey_video',
              'biz_sobey_audio',
              'biz_sobey_document',
            ].includes(row.type_)
          }
        ></Checkbox>
      );
    },
  };

  let screenshotTag: any = {
    title: () => {
      return (
        <div className="screenshotTag">
          <Checkbox
            onChange={changeScreenshotTagCheckAllList}
            checked={screenAllCheck}
            disabled={downData.every((item: any) => {
              return ![
                'biz_sobey_video',
              ].includes(item.type_);
            })}
          ></Checkbox>
          {intl.formatMessage({ id: 'PPT图片' })}
        </div>
      );
    },
    dataIndex: 'PptScreen',
    width: '12%',
    align,
    render: (value: number, row: any, index: number) => {
      return (
        <Checkbox
          checked={screenshotCheckList.includes(row.contentId_)}
          onChange={() => changeScreenshotTagCheckList(row.contentId_)}
          disabled={
            ![
              'biz_sobey_video'
            ].includes(row.type_)
          }
        ></Checkbox>
      );
    },
  };

  let disassembleTag: any = {
    title: () => {
      return (
        <div className="disassembleTag">
          <Checkbox
            onChange={changeDisassembleTagCheckAllList}
            checked={disassembleAllCheck}
            disabled={downData.every((item: any) => {
              return ![
                'biz_sobey_document',
              ].includes(item.type_);
            })}
          ></Checkbox>
          {intl.formatMessage({ id: '内容大纲' })}
        </div>
      );
    },
    dataIndex: 'pdfAnalysis',
    width: '12%',
    align,
    render: (value: number, row: any, index: number) => {
      return (
        <Checkbox
          checked={disassembleCheckList.includes(row.contentId_)}
          onChange={() => changeDisassembleTagCheckList(row.contentId_)}
          disabled={
            ![
              'biz_sobey_document'
            ].includes(row.type_)
          }
        ></Checkbox>
      );
    },
  };

  let videoQualityTag: any = {
    title: () => {
      return (
        <div className="videoQualityTag">
          <Checkbox
            onChange={changeVideoQualityTagCheckAllList}
            checked={videoQualityAllCheck}
            disabled={downData.every((item: any) => {
              return ![
                'biz_sobey_document',
              ].includes(item.type_);
            })}
          ></Checkbox>
          {intl.formatMessage({ id: '视频质量' })}
        </div>
      );
    },
    dataIndex: 'videoCheck',
    width: '12%',
    align,
    render: (value: number, row: any, index: number) => {
      return (
        <Checkbox
          checked={videoQualityCheckList.includes(row.contentId_)}
          onChange={() => changeVideoQualityTagCheckList(row.contentId_)}
          disabled={
            ![
              'biz_sobey_video'
            ].includes(row.type_)
          }
        ></Checkbox>
      );
    },
  };

  let peopleNumTag: any = {
    title: () => {
      return (
        <div className="peopleNumTag">
          <Checkbox
            onChange={changePeopleNumTagCheckAllList}
            checked={peopleNumAllCheck}
            disabled={downData.every((item: any) => {
              return ![
                'biz_sobey_document',
              ].includes(item.type_);
            })}
          ></Checkbox>
          {intl.formatMessage({ id: '人数' })}
        </div>
      );
    },
    dataIndex: 'peopleNumCheck',
    width: '12%',
    align,
    render: (value: number, row: any, index: number) => {
      return (
        <Checkbox
          checked={peopleNumCheckList.includes(row.contentId_)}
          onChange={() => changePeopleNumTagCheckList(row.contentId_)}
          disabled={
            ![
              'biz_sobey_video'
            ].includes(row.type_)
          }
        ></Checkbox>
      );
    },
  };

  let objectTag: any = {
    title: () => {
      return (
        <div className="objectTag">
          <Checkbox
            onChange={changeObjectTagCheckAllList}
            checked={objectAllCheck}
            disabled={downData.every((item: any) => {
              return ![
                'biz_sobey_document',
              ].includes(item.type_);
            })}
          ></Checkbox>
          {intl.formatMessage({ id: '物体' })}
        </div>
      );
    },
    dataIndex: 'objectCheck',
    width: '12%',
    align,
    render: (value: number, row: any, index: number) => {
      return (
        <Checkbox
          checked={objectCheckList.includes(row.contentId_)}
          onChange={() => changeObjectTagCheckList(row.contentId_)}
          disabled={
            ![
              'biz_sobey_video'
            ].includes(row.type_)
          }
        ></Checkbox>
      );
    },
  };
  let belcantoTag: any = {
    title: () => {
      return (
        <div className="belcantoTag">
          <Checkbox
            onChange={changebelcantoTagCheckAllList}
            checked={belcantoAllCheck}
            disabled={downData.every((item: any) => {
              return ![
                'biz_sobey_document',
              ].includes(item.type_);
            })}
          ></Checkbox>
          {intl.formatMessage({ id: '美声' })}
        </div>
      );
    },
    dataIndex: 'belcantoCheck',
    width: '12%',
    align,
    render: (value: number, row: any, index: number) => {
      return (
        <Checkbox
          checked={belcantoCheckList.includes(row.contentId_)}
          onChange={() => changebelcantoTagCheckList(row.contentId_)}
          disabled={
            ![
              'biz_sobey_video'
            ].includes(row.type_)
          }
        ></Checkbox>
      );
    },
  };

  let faceTag: any = {
    title: () => {
      return (
        <div className="faceTag">
          <Checkbox
            onChange={changeFaceTagCheckAllList}
            checked={faceAllCheck}
            disabled={downData.every((item: any) => {
              return ![
                'biz_sobey_document',
              ].includes(item.type_);
            })}
          ></Checkbox>
          {intl.formatMessage({ id: '人脸' })}
        </div>
      );
    },
    dataIndex: 'faceCheck',
    width: '12%',
    align,
    render: (value: number, row: any, index: number) => {
      return (
        <Checkbox
          checked={faceCheckList.includes(row.contentId_)}
          onChange={() => changeFaceTagCheckList(row.contentId_)}
          disabled={
            ![
              'biz_sobey_video'
            ].includes(row.type_)
          }
        ></Checkbox>
      );
    },
  };
  let knowledgeTag: any = {
    title: () => {
      return (
        <div className="knowledgeTag">
          <Checkbox
            onChange={changeKnowledgeTagCheckAllList}
            checked={knowledgeAllCheck}
            disabled={downData.every((item: any) => {
              return ![
                'biz_sobey_document',
              ].includes(item.type_);
            })}
          ></Checkbox>
          {intl.formatMessage({ id: '人脸' })}
        </div>
      );
    },
    dataIndex: 'knowledgeCheck',
    width: '12%',
    align,
    render: (value: number, row: any, index: number) => {
      return (
        <Checkbox
          checked={knowledgeCheckList.includes(row.contentId_)}
          onChange={() => changeKnowledgeTagCheckList(row.contentId_)}
          disabled={
            ![
              'biz_sobey_video'
            ].includes(row.type_)
          }
        ></Checkbox>
      );
    },
  };
  let contentTag: any = {
    title: () => {
      return (
        <div className="contentTag">
          <Checkbox
            onChange={changeContentTagCheckAllList}
            checked={contentAllCheck}
            disabled={downData.every((item: any) => {
              return ![
                'biz_sobey_document',
              ].includes(item.type_);
            })}
          ></Checkbox>
          {intl.formatMessage({ id: '内容审核' })}
        </div>
      );
    },
    dataIndex: 'contentCheck',
    width: '12%',
    align,
    render: (value: number, row: any, index: number) => {
      return (
        <Checkbox
          checked={contentCheckList.includes(row.contentId_)}
          onChange={() => changeContentTagCheckList(row.contentId_)}
          disabled={
            ![
              'biz_sobey_video'
            ].includes(row.type_)
          }
        ></Checkbox>
      );
    },
  };
  // 根据权限，筛选显示的列【有 knowledge_analysis_display 的显示 analysis 列】
  const getColumns = () => {
    let com: any = columns;
    //智能分析
    if (permissions.includes(perCfg.resource_analysis)) {
      if (rmanGlobalParameter.includes(globalParams.smart_tag_display)) {
        com.push(smartTag);
      }
      if (rmanGlobalParameter.includes(globalParams.speech_analysis_display)) {
        com.push(voice);
        if (
          rmanGlobalParameter.includes(globalParams.speech_translate_display)
        ) {
          com.push(translate);
        }
      }
      if (
        rmanGlobalParameter.includes(globalParams.knowledge_analysis_display)
      ) {
        com.push(knowledge);
      }
      if (
        rmanGlobalParameter.includes(
          globalParams.sensitiveface_analysis_display,
        )
      ) {
        com.push(sensitiveFaceTag);
      }
      if (
        rmanGlobalParameter.includes(
          globalParams.sensitiveword_analysis_display,
        )
      ) {
        com.push(sensitiveWordTag);
      }
      if (rmanGlobalParameter.includes(globalParams.summary_analysis_display)) {
        com.push(summaryTag);
      }
      com.push(screenshotTag)
      com.push(disassembleTag)
      com.push(videoQualityTag)
      if (rmanGlobalParameter.includes(globalParams.number_of_people_display)) {
        com.push(peopleNumTag);
      }
      if (rmanGlobalParameter.includes(globalParams.object_recognition_display)) {
        com.push(objectTag);
      }
      if (rmanGlobalParameter.includes(globalParams.bel_canto_review)) {
        com.push(belcantoTag);
      }
      if (rmanGlobalParameter.includes(globalParams.face_review)) {
        com.push(faceTag);
      }
      com.push(knowledgeTag);
      if (rmanGlobalParameter.includes(globalParams.sensitive_image_enable)) {
        com.push(contentTag)
      }
    }
    return com;
  };
  const getColumns_single = () => {
    let com: any = [];
    const item = downData[0];
    //智能分析
    if (permissions.includes(perCfg.resource_analysis)) {
      if (rmanGlobalParameter.includes(globalParams.speech_analysis_display)) {
        com.push({
          title: intl.formatMessage({ id: '语音文本' }),
          className: 'voice',
          disabled: !['biz_sobey_video', 'biz_sobey_audio'].includes(
            item.type_,
          ),
          descrip: intl.formatMessage({ id: '将资源中语音内容转换为文字，便于快速浏览及查找内容。' }),
          func: () => changeAnalysisCheckList(item.contentId_),
        });
        if (
          rmanGlobalParameter.includes(globalParams.speech_translate_display)
        ) {
          com.push({
            title: intl.formatMessage({ id: '语音翻译' }),
            className: 'translate',
            disabled: !['biz_sobey_video', 'biz_sobey_audio'].includes(
              item.type_,
            ),
            descrip:
              intl.formatMessage({ id: '将资源分析出的语音文本转换为其他语言，便于非中文母语者阅读。' }),
            func: () => {
              changeTranslateCheckList((item.contentId_))
            },
          });
        }
      }
      if (rmanGlobalParameter.includes(globalParams.smart_tag_display)) {
        com.push({
          title: intl.formatMessage({ id: '关键词' }),
          className: 'tag',
          disabled: ![
            'biz_sobey_video',
            'biz_sobey_audio',
            'biz_sobey_document',
          ].includes(item.type_),
          descrip: intl.formatMessage({ id: '通过语音文本提取内容标签，便于资源查询及内容推荐。' }),
          func: () => changeSmartTagCheckList(item.contentId_),
        });
      }
      if (
        rmanGlobalParameter.includes(globalParams.knowledge_analysis_display)
      ) {
        com.push({
          title: intl.formatMessage({ id: '知识点' }),
          className: 'knowledge',
          disabled: !['biz_sobey_video'].includes(item.type_),
          descrip: intl.formatMessage({ id: '基于多模态智能分析，识别知识点讲解片段，便于精准学习。' }),
          func: () => changePointsCheckList(item.contentId_),
        });
      }
      if (
        rmanGlobalParameter.includes(
          globalParams.sensitiveface_analysis_display,
        )
      ) {
        com.push({
          title: intl.formatMessage({ id: '人物' }),
          className: 'person',
          disabled: !['biz_sobey_video', 'biz_sobey_picture'].includes(
            item.type_,
          ),
          descrip: intl.formatMessage({ id: '基于命名实体识别语音中提到的人物名称。' }),
          func: () => changeCharacterTagCheckList(item.contentId_),
        });
      }
      if (
        rmanGlobalParameter.includes(
          globalParams.sensitiveword_analysis_display,
        )
      ) {
        com.push({
          title: intl.formatMessage({ id: '敏感内容' }),
          className: 'sensitiveword',
          disabled: ![
            'biz_sobey_video',
            'biz_sobey_picture',
            'biz_sobey_audio',
            'biz_sobey_document',
          ].includes(item.type_),
          descrip: intl.formatMessage({ id: '通过语音文本进行敏感内容检测，便于快速定位敏感信息出现位置。' }),
          func: () => changeSensitiveTagCheckList(item.contentId_),
        });
      }
      if (rmanGlobalParameter.includes(globalParams.summary_analysis_display)) {
        com.push({
          title: intl.formatMessage({ id: '摘要' }),
          className: 'abstract',
          disabled: ![
            'biz_sobey_video',
            'biz_sobey_audio',
            'biz_sobey_document',
          ].includes(item.type_),
          descrip: '通过语音文本提取内容摘要，便于快速了解主要内容。' ,
          func: () => changeSummaryTagCheckList(item.contentId_),
        });
      }
      if (rmanGlobalParameter.includes(globalParams.courseware_screenshot_tab_display)) {
        com.push({
          title: intl.formatMessage({ id: 'PPT图片' }),
          className: 'screenshot',
          disabled: ![
            'biz_sobey_video'
          ].includes(item.type_),
          descrip: '基于图像识别，将视频中课件画面自动截取，便于快速观看。' ,
          func: () => changeScreenshotTagCheckList(item.contentId_),
        });
      }
      // if (rmanGlobalParameter.includes(globalParams.courseware_screenshot_tab_display)) {

        com.push({
          title: intl.formatMessage({ id: '内容大纲' }),
          className: 'disassemble',
          disabled: ![
            'PDF'
          ].includes(item.fileext),
          descrip: intl.formatMessage({ id: '内容大纲' }),
          func: () => changeDisassembleTagCheckList(item.contentId_),
        });
        if (rmanGlobalParameter.includes(globalParams.video_quality_check_display)) {
          com.push({
            title: intl.formatMessage({ id: '视频质量' }),
            className: 'videoCheck',
            disabled: ![
              'biz_sobey_video'
            ].includes(item.type_),
            descrip: '基于多模态分析，检测视频内容质量，便于管理员清理低质量资源。',
            func: () => changeVideoQualityTagCheckList(item.contentId_),
          });
        }
        if (rmanGlobalParameter.includes(globalParams.number_of_people_display)) {
          com.push({
            title: intl.formatMessage({ id: '人数' }),
            className: 'peopleNum',
            disabled: ![
              'biz_sobey_video'
            ].includes(item.type_),
            descrip:  '基于图像识别自动识别画面中人数。' ,
            func: () => changePeopleNumTagCheckList(item.contentId_),
          });
        }
        if (rmanGlobalParameter.includes(globalParams.object_recognition_display)) {
          com.push({
            title: intl.formatMessage({ id: '物体' }),
            className: 'object',
            disabled: ![
              'biz_sobey_video'
            ].includes(item.type_),
            descrip: '基于物体建模数据库，对画面中的物体进行识别并标记。' ,
            func: () => changeObjectTagCheckList(item.contentId_),
          });
        }
        if (rmanGlobalParameter.includes(globalParams.bel_canto_review)) {
          com.push({
            title: intl.formatMessage({ id: '美声' }),
            className: 'belcanto',
            disabled: ![
              'biz_sobey_video'
            ].includes(item.type_),
            descrip: intl.formatMessage({ id: '美声' }),
            func: () => changebelcantoTagCheckList(item.contentId_),
          });
        }
        if (rmanGlobalParameter.includes(globalParams.face_review)) {
          com.push({
            title: intl.formatMessage({ id: '人脸' }),
            className: 'face',
            disabled: ![
              'biz_sobey_video'
            ].includes(item.type_),
            descrip: intl.formatMessage({ id: '人脸' }),
            func: () => changeFaceTagCheckList(item.contentId_),
          });
        }
        if (rmanGlobalParameter.includes(globalParams.voice_knowledge_display)) {
          com.push({
            title: intl.formatMessage({ id: '知识要点' }),
            className: 'voiceKnowledge',
            disabled: ![
              'biz_sobey_video',
            ].includes(item.type_),
            noClick: voiceContent,
            descrip: intl.formatMessage({ id: '知识要点' }),
            func: () => changeKnowledgeTagCheckList(item.contentId_),
          });
        }
        if (rmanGlobalParameter.includes(globalParams.sensitive_image_enable)) {
          com.push({
            title: intl.formatMessage({ id: '内容审核' }),
            className: 'content',
            disabled: ![
              'biz_sobey_video',
            ].includes(item.type_),
            descrip: intl.formatMessage({ id: '内容审核' }),
            func: () => changeContentTagCheckList(item.contentId_),
          });
        }
      // }
    }
    return com;
  };
  const getClassName = (item: any) => {
    let name: string = item.className + (item.disabled ? ' disabled' : '');
    const id = downData[0].contentId_;
    if (
      (analysisCheckList.includes(id) && item.className == 'voice') ||
      (translateCheckList.includes(id) && item.className == 'translate') ||
      (smartTagCheckList.includes(id) && item.className == 'tag') ||
      (pointsCheckList.includes(id) && item.className == 'knowledge') ||
      (sensitiveCheckList.includes(id) && item.className == 'sensitiveword') ||
      (characterCheckList.includes(id) && item.className == 'person') ||
      (summaryCheckList.includes(id) && item.className == 'abstract')||
      (screenshotCheckList.includes(id) && item.className == 'screenshot') ||
      (disassembleCheckList.includes(id) && item.className == 'disassemble') ||
      (videoQualityCheckList.includes(id) && item.className == 'videoCheck') ||
      (peopleNumCheckList.includes(id) && item.className == 'peopleNum') ||
      (objectCheckList.includes(id) && item.className == 'object') ||
      (belcantoCheckList.includes(id) && item.className == 'belcanto') ||
      (faceCheckList.includes(id) && item.className == 'face') ||
      (knowledgeCheckList.includes(id) && item.className == 'voiceKnowledge') ||
      (contentCheckList.includes(id) && item.className == 'content') 
    ) {
      name += ' selected';
    }
    return name;
  };
  const closemodal = () => {
    modalClose();
  };
  const analysis = async () => {
    let tag = false,
      voice: boolean = false,
      point: boolean = false;
    //智能分析整合
    let params: any = [];
    downData?.map(async (item: any, index: any) => {
      let asr = analysisCheckList.includes(item.contentId_);
      const smartTag = smartTagCheckList.includes(item.contentId_);
      const translate = translateCheckList.includes(item.contentId_);
      //已勾选翻译的需要判断是否需要进行语音分析
      if (translate && !asr) {
        if (item.asr_status === '0') {
          asr = true;
        }
      }
      const knowledge = pointsCheckList.includes(item.contentId_);
      const character = characterCheckList.includes(item.contentId_);
      const word = sensitiveCheckList.includes(item.contentId_);
      const summary = summaryCheckList.includes(item.contentId_);
      const PptScreen = screenshotCheckList.includes(item.contentId_);
      const pdfAnalysis = disassembleCheckList.includes(item.contentId_);
      const videoQualityCheck = videoQualityCheckList.includes(item.contentId_);
      const peopleNumCheck = peopleNumCheckList.includes(item.contentId_);
      const objectCheck = objectCheckList.includes(item.contentId_);
      const audioImprove = belcantoCheckList.includes(item.contentId_);
      const face = faceCheckList.includes(item.contentId_);
      const VoiceKnowledge = knowledgeCheckList.includes(item.contentId_);
      const contentSecurity = contentCheckList.includes(item.contentId_);
      const knowledgeSummary = knowledgeCheckList.includes(item.contentId_);
      if (
        smartTag ||
        knowledge ||
        asr ||
        translate ||
        face ||
        word ||
        summary ||
        PptScreen ||
        pdfAnalysis ||
        videoQualityCheck ||
        peopleNumCheck ||
        objectCheck ||
        audioImprove ||
        character ||
        VoiceKnowledge ||
        contentSecurity ||
        knowledgeSummary
      ) {
        if (asr) {
          voice = true;
        }
        if (smartTag) {
          tag = true;
        }
        if (knowledge) {
          point = true;
        }
        let obj = {
          contentId: item.contentId_,
          asr,
          smartTag,
          translate,
          knowledge,
          face: character || face,
          word,
          summary,
          PptScreen,
          pdfAnalysis,
          videoQualityCheck,
          detectingCheck: peopleNumCheck|| objectCheck,
          audioImprove,
          VoiceKnowledge,
          contentSecurity,
          knowledgeSummary
        } as any
        if(translate){
          obj.targetIds = targetIds
          obj.sourceId = sourceId
        }
        params.push(obj);
      }
    });
    const res: any = await SmartService.sendFlow_new(params);
    // console.log('sendFlow_new',res)
    if (res?.success) {
      if (res.data) {
        closemodal();
        message.success(intl.formatMessage({ id: '智能分析已发起' }));
      } else {
        message.error(intl.formatMessage({ id: '智能分析发起失败' }));
      }
    }
    if (refresh) {
      refresh(tag, voice, point);
    }
  };
  return (
    <Modal
      className={`intelligentanalysismodal ${className_}`}
      destroyOnClose={true}
      title={<div className="intelligentanalysismodal-title">{intl.formatMessage({ id: '智能分析' })}</div>}
      open={modalVisible}
      onCancel={closemodal}
      width={downData.length == 1 ? 625 : 1200}
      footer={[
        <div className="footer-btn">
          <Button
            key="downloadyes"
            type="primary"
            onClick={analysis}
            disabled={
              !(
                smartTagCheckList.length ||
                analysisCheckList.length ||
                pointsCheckList.length ||
                translateCheckList.length ||
                sensitiveCheckList.length ||
                characterCheckList.length ||
                summaryCheckList.length ||
                screenshotCheckList.length ||
                disassembleCheckList.length ||
                videoQualityCheckList.length ||
                peopleNumCheckList.length ||
                objectCheckList.length ||
                belcantoCheckList.length ||
                faceCheckList.length ||
                knowledgeCheckList.length ||
                contentCheckList.length
              )
            }
          >
            {intl.formatMessage({ id: '发起智能分析' })}
          </Button>
        </div>,
      ]}
    >
      {downData.length == 1 ? (
        <div className="singleDisplay">
          <div className="head">{intl.formatMessage({ id: '对该资源发起以下内容智能分析' })}</div>
          <div className="body">
            {getColumns_single()?.length > 0 &&
              getColumns_single()
                .filter((item: any) => !item.disabled)
                .map((item: any, index: number) => {
                  return (
                    <div
                      className={(() => getClassName(item))()}
                      onClick={item.noClick ? null : item.func}
                      key={index}
                    >
                      <Tooltip title={item.className === 'voiceKnowledge'?voiceContent? '需要语音文本内容才能发起知识要点分析': item.descrip :item.descrip}>
                        <div className="item">
                          <div className="tag"></div>
                          <div className="title">{item.title}</div>
                        </div>
                      </Tooltip>

                      <div className="mask"></div>
                      <img
                        className="select"
                        src="/rman/static/images/smart/selected.png"
                      />
                    </div>
                  );
                })}
          </div>
        </div>
      ) : (
        <Table
          columns={getColumns()}
          dataSource={downData}
          rowKey="contentId_"
          scroll={{ y: 300 }}
          size={mobileFlag ? 'small' : 'middle'}
          pagination={false}
          className="download_modal"
        />
      )}
        <LanguageVisible setTargetIds={setTargetIds} setSourceId={setSourceId}  sourceId={sourceId}  targetIds={targetIds} changeTranslateCheckList={() => {changeTranslateCheckList(languageId)}} setLanguageModalVisible={setLanguageModalVisible}  visible={languageVisible}/>
    </Modal>
  );
};

export default IntelligentAnalysisModal;
