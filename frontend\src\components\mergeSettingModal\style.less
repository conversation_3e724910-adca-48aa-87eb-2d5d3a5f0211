
.color-modal {
  position: relative;

  .preview-wrapper {
    padding: 12px;
    border: 1px dashed #aa1f29;
    border-radius: 4px;
    max-height: 70vh;
    min-height: 50vh;
    overflow-y: auto;

    img {
      width: 100%;
      object-fit: cover;
    }
  }

  .color-input-wrapper {
    margin-top: 12px;

    input {
      cursor: pointer;
    }

    span {
      margin-left: 12px;
      color: #aa1f29;
      font-size: 12px;
      font-style: italic;
    }
  }
}

.color-show-wrapper {
  .color-show-box {
    width: 44px;
    height: 23px;
    padding: 3px;
    border-radius: 3px;
    border: 1px solid #d9d9d9;

    div {
      height: 100%;
      border-radius: 3px;
      cursor: pointer;
    }
  }

  .close-btn {
    font-size: 12px;
    color: #ff4d4f;
  }
}

.material-preview-wrapper {
  position: relative;

  .material-preview {
    padding: 6px;
    border: 1px solid #d9d9d9;
    border-radius: 3px;


    img {
      width: 220px;
      height: 124px;
      object-fit: cover;
    }
  }


  span {
    cursor: pointer;
    font-size: 12px;
  }
}
