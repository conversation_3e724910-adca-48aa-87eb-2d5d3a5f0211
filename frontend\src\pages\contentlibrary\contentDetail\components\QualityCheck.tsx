import React, {
    FC,
    useState,
} from 'react';
import contentListApis from '@/service/contentListApis';
import { useIntl } from 'umi';
import { Button, message, Collapse } from 'antd';
import './QualityCheck.less'
import {
    IconFont
  } from '@/components';
const { Panel } = Collapse;
interface props {
    contentId: string,
    qualityCheckList: any,
    setPlayerSec: (inpoint: any, outpoint: any, item: any)=> void;
    qualityCheckMeta: ()=> void,
}
const QualityCheck: FC<props> = ({contentId, qualityCheckList, qualityCheckMeta, setPlayerSec }) => {
    //全选start
    const [current, setCurrent] = useState<any>([]);
    const intl = useIntl();

    const desc = [
        {title: intl.formatMessage({ id: '有画面无声音' }),key: 'Mute' },
        {title: intl.formatMessage({ id: '无画面有声音' }),key: 'BlackScreen' },
        {title: intl.formatMessage({ id: '无画面无声音' }),key: 'Mute,BlackScreen' },
        {title: intl.formatMessage({ id: '无教学活动' }),key: 'NoTeaching' }
    ]
    const chooseCurrent = (item) => {
        let copyCurrent = [...current]
        if(current.includes(item.guid_)){
            let index = copyCurrent.findIndex(item_ => item_ ===item.guid_)
            copyCurrent.splice(index, 1)
        }
        else{
            copyCurrent.push(item.guid_)
        }
        setPlayerSec(item.inpoint, item.outpoint, item)
        setCurrent(copyCurrent)
    }
    const allCheck = () => {
        let guids = qualityCheckList.map(item => item.guid_)
        if(guids.length === current.length) {
            setCurrent([])
        }
        else{
            setCurrent(qualityCheckList.map(item => item.guid_))
        }
    }
    const timeFormat=(start:any,end:any)=>{
        const result = Math.floor((Number(end)-Number(start))/10000000); //百纳秒
        let h = Math.floor(result / 3600);
        let m = Math.floor((result / 60 % 60));
        let s = Math.floor((result % 60));
        let res = '';
        if(h!=0) res += `${h}h`;
        if(m!=0) res += `${m}min`;
        res += `${s}s`;
        return res;
      }
    const deleteData = () => {
        if(current.length === 0) {
            message.warning({ content: intl.formatMessage({ id: '请选择数据' }) });
            return
        }
        let data =  qualityCheckList.map((item: any) => {
            if(current.includes(item.guid_)){
                return {...item, audit_delete: '1'}
            }
            return item
        });
        contentListApis.qualityCheckMetaUpdate(contentId, data).then(res => {
            if(res?.success) {
                message.success({ content: intl.formatMessage({ id: '删除成功' }) });
                qualityCheckMeta()
            }
        })
    }
    const qualityCheckMetaSaveas = () => {
        let data =  current.map((item: any) => qualityCheckList.filter(i => i?.guid_ == item)[0]);
        if(current.length === 0) {
            message.warning({ content: intl.formatMessage({ id: '请选择数据' })});
            return
        }
        contentListApis.qualityCheckMetaSaveas(contentId, data).then( res => {
            if(res?.success) {
                message.success({ content: intl.formatMessage({ id: '合成成功' }) });
                qualityCheckMeta()
            }
        })
    }
    return (
        <div className='qualityCheck'>
            <div className="btns">
                <Button
                    type='primary'
                    onClick={allCheck}
                >
                    {intl.formatMessage({ id: '全选' })}
                </Button>
                <Button
                    style={{margin: '0 10px'}}
                    type='primary'
                    onClick={qualityCheckMetaSaveas}
                >
                    {intl.formatMessage({ id: '剪切后合成' })}
                </Button>
                <Button
                    type='primary'
                    onClick={deleteData}
                >
                    {intl.formatMessage({ id: '删除' })}
                </Button>
            </div>
            <Collapse expandIconPosition='end' defaultActiveKey={[0]} >
                {desc.map( (item,index) => (
                    <Panel header={`${item.title}（${qualityCheckList.filter((item_: any) => item_.type === item.key).length}）`} key={index}>
                    <div className='quality-list'>
                        {
                            qualityCheckList.filter((item_: any) => item_.type === item.key).map((item_: any) => (
                                <div className='list-item' onClick={() => chooseCurrent(item_)}>
                                    {current.includes(item_.guid_) &&<IconFont type='icongou1' className='gou'  />}
                                    <img src={item_.keyframepath} alt="" />
                                    <span className='time'>{timeFormat(item_.inpoint, item_.outpoint)}</span>
                                </div>
                            ))
                        }
                    </div>
                </Panel>
                ))}
            </Collapse>
        </div>
    )
}
export default QualityCheck