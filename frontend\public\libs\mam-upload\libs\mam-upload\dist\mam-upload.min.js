!function(e){function t(o){if(a[o])return a[o].exports;var n=a[o]={i:o,l:!1,exports:{}};return e[o].call(n.exports,n,n.exports,t),n.l=!0,n.exports}var a={};t.m=e,t.c=a,t.d=function(e,a,o){t.o(e,a)||Object.defineProperty(e,a,{configurable:!1,enumerable:!0,get:o})},t.n=function(e){var a=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(a,"a",a),a},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="/dist/",t(t.s=11)}([function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o={asyncLoadedScripts:{},asyncLoadedScriptsCallbackQueue:{},getScriptDomFromUrl:function(e){var t;return/.+\.js$/.test(e)?(t=document.createElement("SCRIPT"),t.setAttribute("type","text/javascript"),t.setAttribute("src",e)):/.+\.css$/.test(e)&&(t=document.createElement("link"),t.href=e,t.type="text/css",t.rel="stylesheet"),t},asyncLoadScript:function(e,t){var a=o;if(void 0!=a.asyncLoadedScripts[e])return void(t&&"function"==typeof t&&(0==a.asyncLoadedScripts[e]?(a.asyncLoadedScriptsCallbackQueue[e]||(a.asyncLoadedScriptsCallbackQueue[e]=[]),a.asyncLoadedScriptsCallbackQueue[e].push(t)):t.apply(a,[])));a.asyncLoadedScripts[e]=0;var n=a.getScriptDomFromUrl(e);n.readyState?n.onreadystatechange=function(){if(("loaded"==n.readyState||"complete"==n.readyState)&&(n.onreadystatechange=null,a.asyncLoadedScripts[e]=1,t&&"function"==typeof t&&t.apply(a,[]),a.asyncLoadedScriptsCallbackQueue[e])){for(var o=0,r=a.asyncLoadedScriptsCallbackQueue[e].length;o<r;o++)a.asyncLoadedScriptsCallbackQueue[e][o].apply(a,[]);a.asyncLoadedScriptsCallbackQueue[e]=void 0}}:n.onload=function(){if(a.asyncLoadedScripts[e]=1,t&&"function"==typeof t&&t.apply(a,[]),a.asyncLoadedScriptsCallbackQueue[e]){for(var o=0,n=a.asyncLoadedScriptsCallbackQueue[e].length;o<n;o++)a.asyncLoadedScriptsCallbackQueue[e][o].apply(a,[]);a.asyncLoadedScriptsCallbackQueue[e]=void 0}},document.getElementsByTagName("head")[0].appendChild(n)},getFileNameFromUrl:function(e){return e.substring(e.lastIndexOf("/")+1,e.length)},isIncludeScript:function(e){for(var t=/js$/i.test(e),a=document.getElementsByTagName(t?"script":"link"),o=0;o<a.length;o++)if(-1!=a[o][t?"src":"href"].indexOf(e))return!0;return!1},loadScripts:function(e){if(e instanceof Array){for(var t=[],a=0;a<e.length;a++)t.push(new Promise(function(t,n){o.isIncludeScript(o.getFileNameFromUrl(e[a]))?t():o.asyncLoadScript(e[a],function(){t()})}));return Promise.all(t)}return new Promise(function(e,t){e()})},getExtensions:function(e){if(!e)return"";for(var t=e.length;0!=t;t--)if("."==e[t])return e.substring(t,e.length);return""},getExtension:function(e){var t=o.getExtensions(e);return t.length>0?t.substring(1,t.length):""},getFileName:function(e){for(var t=e.length,a=e.length;0!=a;a--)if("."!=e[a]||t!=e.length){if("\\"==e[a])return e.substring(a+1,t)}else t=a;return e.substring(0,t)},getFullFileName:function(e){for(var t=e.length;0!=t;t--)if("\\"==e[t])return e.substring(t+1,e.length);return e},getTypeByExt:function(e,t){e=e.toLowerCase(),0!==e.indexOf(".")&&(e="."+e);var a=_.get(window,"nxt.config.entityTypes",[]);t&&0===a.length&&(a=_.get(t,"entityTypes",[]));for(var o=0;o<a.length;o++)if(-1!=a[o].extensions.indexOf(e))return a[o];return _.find(a,{code:"other"})},formatSize:function(e,t,a){var o;for(a=a||["B","KB","MB","GB","TB"];(o=a.shift())&&e>1024;)e/=1024;return("B"===o?e:e.toFixed(t||2))+" "+o},prompt:function(e){mam&&mam.prompt&&mam.prompt(e)},msgOk:function(e){mam&&mam.message&&mam.message.ok&&mam.message.ok(e)}};t.default=o},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o={};t.default=o},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=a(0),n={getGetParamStr:function(e,t){var a="";if(void 0!=e&&!$.isEmptyObject(e)){for(var o in e)a+=t?"/"+e[o]:o+"="+e[o]+"&";t||(a="?"+a.substring(0,a.length-1))}return a},get:function(e,t){var a=$.Deferred(),r=e;return t||(t={}),r=e+n.getGetParamStr(t),$.ajax({type:"get",xhrFields:{withCredentials:!(e.indexOf("token=")>-1)},url:r}).then(function(e){e.success?a.resolve(e):(console.error("response",e),o.default.prompt(l("system."+e.data.code,e.data.title)),a.reject(e))},function(e){a.reject(e)}),a},post:function(e,t,a){var n=$.Deferred();t||(t={});var r={type:"post",data:t,contentType:"application/json",processData:!1,xhrFields:{withCredentials:!(e.indexOf("token=")>-1)},url:e};return a&&void 0!==a.contentType&&(r.contentType=a.contentType),a&&void 0!==a.processData&&(r.processData=a.processData),"application/json"===r.contentType&&(r.data=JSON.stringify(t)),$.ajax(r).then(function(e){e.success?n.resolve(e):(console.error("response",e),o.default.prompt(l("system."+e.error.code,e.error.title)),n.reject(e))},function(e){n.reject(e)}),n}};t.default=n},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){var o=a(0),n=(a(1).default,a(2).default),r=a(9),i=function(t){function a(){return window.nxt&&window.nxt.config?window.nxt.config:t.configInst?t.configInst:void 0}function i(){if(P.getFilesByStatus("progress","deleteing").length>0)return l("upload.closePageTip","当前页面存在未完成的上传任务，关闭页面会导致上传失败，你确定要关闭吗？")}function s(e){return _.isUndefined(e.lastModifiedDate)?e.lastModified:e.lastModifiedDate.getTime()}function u(e){var t=_.find(a().entityTypes,{code:e.entityType});return null==t&&(t=_.find(a().entityTyps,{isOther:!0})),null==t?"":t.keyframe.replace("~","")}function p(){_.forEach(U,function(e){"added"!==e.status&&"success"!==e.status&&(e.status="error")}),$(window).off("beforeunload",i),o.default.prompt(l("upload.unauthorized","你未登录或已超时，请重新登录。")),location.href=a().loginUrl+"?login_backUrl="+location.href}function f(){null==I&&(S=_.uniqueId("mam-web-transfer-"),I=$('<input id="'+S+'" type="file" multiple style="display:none"/>'),$("body").append(I))}function d(){null!=I&&(I.unbind("change"),I.remove(),I=null)}function c(e,t){$(P).trigger(e,t)}function m(e,t){if(6==a().storageType)return g(e,t);if(4==a().storageType){var o={Bucket:e.bucket,Key:t.location.replace(/\\/g,"/")},n={accessKeyId:e.keyId,secretAccessKey:e.secretKey,endpoint:e.serviceURL,sslEnabled:!e.useHttp,s3ForcePathStyle:!0,signatureVersion:e.version};if(AWS.config.update(n),e&&e.region&&(AWS.config.region=e.region),E=new AWS.S3({apiVersion:"2006-03-01"}),!(t.fileSize<=5242880)){var r=E.createMultipartUpload(o).promise();t.createUploadPromise=r;var i=$.Deferred();return t.createUploadPromise.then(function(e){t.uploadId=e.UploadId,i.resolve()}).catch(function(e){i.reject(),console.info(e)}),i.promise()}}}function h(e){var t={accessKeyId:e.keyId,secretAccessKey:e.secretKey,endpoint:e.serviceURL,sslEnabled:!e.useHttp,s3ForcePathStyle:!0,signatureVersion:"v"+e.version,apiVersion:"2006-03-01"};e.region&&(AWS.config.region=e.region),E=new OOS.S3(t)}function g(e,t){if(6==a().storageType&&(h(e),!(t.fileSize<=5242880))){var o={Bucket:e.bucket,Key:t.location.replace(/\\/g,"/")},n=E.createMultipartUpload(o).promise();t.createUploadPromise=n;var r=$.Deferred();return t.createUploadPromise.then(function(e){t.uploadId=e.UploadId,r.resolve()}).catch(function(e){r.reject(),console.info(e)}),r.promise()}}function b(e){c("task-init-before",e);var r=[],i=a().server+"/upload/multipart/init";t.loginToken&&(i+="?token="+t.loginToken),n.post(i,e).then(function(t){t=t.data;for(var n=0;n<t.files.length;n++){var i=e.files[n].file;e.files[n]=t.files[n],e.files[n].file=i,e.files[n].status="prepared",e.files[n].fileSizeString=o.default.formatSize(t.files[n].fileSize),e.files[n].progress=0,e.sizeTotal+=t.files[n].fileSize,e.chunkTotal+=t.files[n].chunkTotal;var s=m(t.ossClientInfo,e.files[n]);s&&r.push(s)}e.taskId=t.taskId,e.entityType=t.entityType,e.fileTotal=t.fileTotal,e.targetFolder=t.targetFolder,e.targetFolderName=t.targetFolderName,e.targetType=t.targetType,e.keyframe=t.keyframe,e.status="prepared",e.inited=!0,e.sizeTotalString=o.default.formatSize(e.sizeTotal),e.isJsUpload=t.isJsUpload,e.ossClientInfo=t.ossClientInfo,c("task-init-success",e),(4==a().storageType||6==a().storageType)&&e.isJsUpload&&r.length>0?$.when.apply($,r).done(function(){P.prepareUpload()}):P.prepareUpload()},function(t){if(c("task-init-error",[e,t]),401===t.status)return void p(U);e.status="error",_.forEach(e.files,function(e){e.status="error"}),o.default.prompt(l("upload.","上传失败：${text}",{text:t.data.desc||t.data.title})),P.prepareUpload()})}function y(e,t,o){a().webUploadMd5Enable?(null==e.file.fileReader&&(e.file.fileReader=new FileReader),e.file.fileReader.onload=function(e){var t=new r;t.appendBinary(e.target.result);var a=t.end();t.destroy(),o(a)},e.file.fileReader.onerror=function(t){k(e)},e.file.fileReader.readAsBinaryString(t)):o("")}function x(e,t){null!=t.startTime&&(_.isNumber(t.surplusTime)||(t.surplusTime=0),t.surplusTime=(new Date-t.startTime)*(t.chunkTotal-t.chunkIndex),_.isNumber(e.surplusTime)||(e.surplusTime=0),e.surplusTime=(new Date-t.startTime)*(e.chunkTotal-e.chunkFinished)),t.startTime=new Date}function k(e){e.file.hasOwnProperty("errorCount")||(e.file.errorCount=0),e.file.errorCount<4?(e.file.errorCount++,A[e.file.fileId]=setTimeout(function(){P.upload(e)},3e3)):(e.task.status=e.file.status="error",c("task-upload-error",e.task),P.prepareUpload())}function v(e,t){if(e==t)return 100;var a=e/t*100;return-1==a.toString().indexOf(".")?a:a.toFixed(2)}function w(e){return v(e.chunkIndex,e.chunkTotal)}function T(e){for(var t=0,a=0;a<e.files.length;a++)t+=e.files[a].chunkIndex;return e.chunkFinished=t,v(e.chunkFinished,e.chunkTotal)}var I,S,E,P=this,U=[],A={};this.on=function(e,t){$(P).on(e,t)},this.off=function(e,t){$(P).off(e,t)},this.openFileSelector=function(e,a){d(),f(),!0===a?I.removeAttr("multiple"):I.attr("multiple","multiple"),I.on("change",function(){if(""!==I.val()){var n=I[0].files;!0===a&&(n=n[0]);var r=[];_.forEach(n,function(e){var a="."+o.default.getExtension(e.name);r.push({entityType:o.default.getTypeByExt(a,t.configInst).code,fileName:e.name,metadata:{name:o.default.getFileName(e.name),ext:a},status:"added",progress:0,file:e})}),e(r),f()}}),I.click(function(e){e.stopPropagation()}),I.trigger("click")},this.createTask=function(e,t){var a=[];switch(t.taskType){case 1:_.isArray(e)||(e=[e]),_.forEach(e,function(e){e.files&&e.files.length>0?(e.files=_.map(e.files,function(e){return{file:e.file,fileName:e.file.name,fileSize:e.file.size,fileLastModifiedDate:s(e.file),type:e.type}}),e.files.unshift({file:e.file,fileName:e.file.name,fileSize:e.file.size,fileLastModifiedDate:s(e.file),type:void 0!==e.type?e.type:"0"})):e.files=[{file:e.file,fileName:e.file.name,fileSize:e.file.size,fileLastModifiedDate:s(e.file),type:void 0!==e.type?e.type:"0"}],delete e.file,a.push(e)});break;case 2:case 3:_.forEach(e.files,function(e){e.fileName=e.file.name,e.fileSize=e.file.size,e.fileLastModifiedDate=s(e.file)}),a.push(e)}_.forEach(a,function(e){e.taskType=t.taskType,e.transferType=t.transferType,e.targetFolder=t.targetFolder,e.relationContentType=t.relationContentType,e.relationContentId=t.relationContentId,P.addTask(e)})},this.addTask=function(e){e.status="init",e.progress=e.sizeTotal=e.chunkFinished=e.chunkTotal=0,e.keyframe=u(e),U.push(e),b(e)},this.prepareUpload=function(){var e=P.getFilesByStatus("progress"),t=a().webUploadThreads-e.length;if(!(t<=0)){var o=P.getFilesByStatus("prepared");if(t>o.length&&(t=o.length),0!==t)for(var n=0;n<t;n++)o[n].task.status=o[n].file.status="progress",P.upload(o[n])}},this.uploadByBackend=function(e){var o=e.file,r=e.task;if(o.chunkIndex<o.chunkTotal){x(r,o);var i=o.chunkIndex*o.chunkSize,s=Math.min(o.fileSize,i+o.chunkSize),l=new FormData,u=o.file.slice(i,s);if(null==u||0==u.size)return e.task.status=e.file.status="error",c("task-upload-error",e.task),e.file.file=null,void P.prepareUpload();y(e,u,function(i){if(l.append("fileData",u),l.append("taskId",o.taskId),l.append("fileId",o.fileId),l.append("chunkIndex",o.chunkIndex),l.append("md5",i),"deleteing"===r.status)return void P.clearTask(r);var s=a().server+"/upload/multipart";t.loginToken&&(s+="?token="+t.loginToken),n.post(s,l,{contentType:!1,processData:!1}).then(function(t){if("deleteing"===r.status)return void P.clearTask(r);t=t.data,o=_.find(r.files,{fileId:t.fileId}),o.chunkIndex=t.chunkIndex,3===t.taskStatus?(o.progress=r.progress=100,o.surplusTime=r.surplusTime=null,delete o.fileReader,o.status=r.status="success",c("task-upload-success",r),P.prepareUpload()):(r.progress=T(r),o.progress=w(o),o.status=r.status="progress",c("task-upload-progress",r),o.errorCount=0,P.upload(e))},function(t){if(401===t.status)return void p();k(e)})})}else o.hasOwnProperty("errorCount")&&(delete A[o.fileId],delete o.errorCount),delete o.fileReader,o.surplusTime=null,o.progress=100,o.status="success",r.progress=T(r),c("task-upload-success",r),P.prepareUpload()};var C=function(e){var o=new FormData;o.append("taskId",e.taskId),o.append("fileId",e.fileId),o.append("chunkIndex",e.chunkIndex),o.append("partInfo",JSON.stringify(e.partInfo)),o.append("uploadId",e.uploadId),o.append("checkPoint",e.checkPoint);var r=a().server+"/upload/multipart";return t.loginToken&&(r+="?token="+t.loginToken),n.post(r,o,{contentType:!1,processData:!1})};this.uploadByWeb=function(t){var a=t.file,o=t.task,n={region:o.ossClientInfo.region,accessKeyId:o.ossClientInfo.accessKeyId,accessKeySecret:o.ossClientInfo.accessKeySecret,bucket:o.ossClientInfo.bucketName};o.ossClientInfo.endpoint&&(n.endpoint=o.ossClientInfo.endpoint);var r=new this.OSS.Wrapper(n);if(a.fileSize<=102400)t.fileReader=new FileReader,t.fileReader.onload=function(t){r.put(a.location,new e(this.result)).then(function(e){C(a).then(function(e){a.progress=o.progress=100,a.surplusTime=o.surplusTime=null,delete a.fileReader,a.status=o.status="success",c("task-upload-success",o),P.prepareUpload()})},function(e){console.error(e)})},t.fileReader.readAsArrayBuffer(a.file);else{var i={partSize:a.chunkSize,progress:function(e,n,r){return function(e){"deleteing"===o.status?P.clearTask(o):(a.partInfo=n.doneParts[n.doneParts.length-1],a.uploadId=n.uploadId,a.checkPoint=JSON.stringify(n,function(e,t){if("file"!==e)return t}),C(a).then(function(t){if("deleteing"===o.status)return void P.clearTask(o);t=t.data,a.chunkIndex=t.chunkIndex,3===t.taskStatus?(a.progress=o.progress=100,a.surplusTime=o.surplusTime=null,delete a.fileReader,a.status=o.status="success",c("task-upload-success",o),P.prepareUpload(),e()):(o.progress=T(o),a.progress=w(a),a.status=o.status="progress",c("task-upload-progress",o),a.errorCount=0,e())},function(e){if(401===e.status)return void p();k(t)}))}}};a.checkPoint&&(i.checkpoint=JSON.parse(a.checkPoint),i.checkpoint.file=a.file),r.multipartUpload(a.location,a.file,i).then(function(e){console.log("upload success",e)},function(e){console.log(e)})}},this.uploadByS3=function(e){var o=e.file,r=e.task;if(o.chunkIndex<o.chunkTotal){var i=o.chunkIndex*o.chunkSize,s=Math.min(o.fileSize,i+o.chunkSize),l=new FormData,u=o.file.slice(i,s);if(null==u||0==u.size)return e.task.status=e.file.status="error",c("task-upload-error",e.task),e.file.file=null,void P.prepareUpload();var f=function(o,r){if(l.append("taskId",r.taskId),l.append("fileId",r.fileId),l.append("chunkIndex",r.chunkIndex),l.append("partInfo",r.partInfo),l.append("uploadId",r.uploadId),"deleteing"===o.status)return void P.clearTask(o);var i=a().server+"/upload/multipart";t.loginToken&&(i+="?token="+t.loginToken),n.post(i,l,{contentType:!1,processData:!1}).then(function(t){if("deleteing"===o.status)return void P.clearTask(o);t=t.data,r=_.find(o.files,{fileId:t.fileId}),r.chunkIndex=t.chunkIndex,3===t.taskStatus?(r.progress=o.progress=100,r.surplusTime=o.surplusTime=null,delete r.fileReader,r.status=o.status="success",c("task-upload-success",o),P.prepareUpload()):(o.progress=T(o),r.progress=w(r),r.status=o.status="progress",c("task-upload-progress",o),r.errorCount=0,P.upload(e))},function(t){if(401===t.status)return void p();k(e)})};if(o.fileSize<=5242880)E.putObject({Body:o.file,Bucket:r.ossClientInfo.bucket,Key:o.location.replace(/\\/g,"/")},function(t,a){t?(console.info(t,t.stack),k(e)):(a.ETag&&(o.partInfo=a.ETag),f(r,o),console.info(a))});else{var d={Body:u,Bucket:r.ossClientInfo.bucket,Key:o.location.replace(/\\/g,"/"),PartNumber:o.chunkIndex+1,UploadId:o.uploadId};E.uploadPart(d,function(t,a){t?(console.info(t,t.stack),k(e)):(a.ETag&&(o.partInfo=a.ETag.replace(/"/g,"")),f(r,o),console.info(a))})}}else o.hasOwnProperty("errorCount")&&(delete A[o.fileId],delete o.errorCount),delete o.fileReader,o.surplusTime=null,o.progress=100,o.status="success",r.progress=T(r),c("task-upload-success",r),P.prepareUpload()},this.uploadByOos=function(e){var o=e.file,r=e.task;if(o.chunkIndex<o.chunkTotal){var i=o.chunkIndex*o.chunkSize,s=Math.min(o.fileSize,i+o.chunkSize),l=new FormData,u=o.file.slice(i,s);if(null==u||0==u.size)return e.task.status=e.file.status="error",c("task-upload-error",e.task),e.file.file=null,void P.prepareUpload();var f=function(o,r){if(l.append("taskId",r.taskId),l.append("fileId",r.fileId),l.append("chunkIndex",r.chunkIndex),l.append("partInfo",r.partInfo),l.append("uploadId",r.uploadId),"deleteing"===o.status)return void P.clearTask(o);var i=a().server+"/upload/multipart";t.loginToken&&(i+="?token="+t.loginToken),n.post(i,l,{contentType:!1,processData:!1}).then(function(t){if("deleteing"===o.status)return void P.clearTask(o);t=t.data,r=_.find(o.files,{fileId:t.fileId}),r.chunkIndex=t.chunkIndex,3===t.taskStatus?(r.progress=o.progress=100,r.surplusTime=o.surplusTime=null,delete r.fileReader,r.status=o.status="success",c("task-upload-success",o),P.prepareUpload()):(o.progress=T(o),r.progress=w(r),r.status=o.status="progress",c("task-upload-progress",o),r.errorCount=0,P.upload(e))},function(t){if(401===t.status)return void p();k(e)})};if(E||h(r.ossClientInfo),o.fileSize<=5242880)E.putObject({Body:o.file,Bucket:r.ossClientInfo.bucket,Key:o.location.replace(/\\/g,"/")},function(t,a){t?(console.info(t,t.stack),k(e)):(a.ETag&&(o.partInfo=a.ETag),f(r,o),console.info(a))});else{var d={Body:u,Bucket:r.ossClientInfo.bucket,Key:o.location.replace(/\\/g,"/"),PartNumber:o.chunkIndex+1,UploadId:o.uploadId};E.uploadPart(d,function(t,a){t?(console.info(t,t.stack),k(e)):(a.ETag&&(o.partInfo=a.ETag.replace(/"/g,"")),f(r,o),console.info(a))})}}else o.hasOwnProperty("errorCount")&&(delete A[o.fileId],delete o.errorCount),delete o.fileReader,o.surplusTime=null,o.progress=100,o.status="success",r.progress=T(r),c("task-upload-success",r),P.prepareUpload()},this.upload=function(e){e.task.isJsUpload?4==a().storageType?this.uploadByS3(e):6==a().storageType?this.uploadByOos(e):this.uploadByWeb(e):this.uploadByBackend(e)},this.continueUpload=function(e,t,a){null==t.file?P.openFileSelector(function(n){var r=P.calcFileMd5(n[0].file);if(t.fileMd5!==r)return o.default.prompt(l("upload.fileDiffer","选择的文件不一致，请重新上传")),void(a&&a.apply(window,[l("upload.fileDiffer","选择的文件不一致，请重新上传")]));t.file=n[0].file,e.status=t.status="prepared",P.prepareUpload()},!1):e.inited?(e.status=t.status="prepared",P.prepareUpload()):b(e)},this.getUnfinishedTask=function(e,r,i){var s=$.Deferred();null==e&&(e=""),_.isNumber(r)||(r=0),_.isNumber(r)||(r=1);var l=a().server+"/upload/get-unfinished-task?relationId="+e+"&relationContentType="+r+"&targetType="+i;return t.loginToken&&(l+="&token="+t.loginToken),n.get(l).then(function(e){var t=[];_.forEach(e.data,function(e){e.status="error",e.inited=!0,e.sizeTotal=e.chunkFinished=e.chunkTotal=0,_.forEach(e.files,function(t){t.fileSizeString=o.default.formatSize(t.fileSize),t.progress=w(t),t.status=100===t.progress?"success":"error",e.sizeTotal+=t.fileSize,e.chunkTotal+=t.chunkTotal}),e.progress=T(e),e.sizeTotalString=o.default.formatSize(e.sizeTotal),U.push(e),t.push(e)}),s.resolve(t)},function(e){s.reject(e)}),s},this.canDeleteTask=function(e){return null!=e&&("init"!=e.status&&("deleteing"!=e.status&&("progress"!=e.status||e.chunkFinished!=e.chunkTotal-1)))},this.removeTask=function(e){_.remove(U,function(t){return t.taskId===e.taskId})},this.deleteTask=function(e){if(this.canDeleteTask(e))if(!0===e.inited)switch(_.forEach(e.files,function(e){null!=A[e.fileId]&&(timeout.cancel(A[e.fileId]),delete A[e.fileId])}),e.status){case"progress":return void(e.status="deleteing");case"prepared":case"error":return void P.clearTask(e);default:P.removeTask(e),c("task-delete-success",e)}else P.removeTask(e),c("task-delete-success",e)},this.clearTask=function(e){var o={};if(e.files&&e.files.length>0){_.forEach(e.files,function(e){o[e.fileId]=e.uploadId});var r=a().server+"/upload/delete-task";t.loginToken&&(r+="?token="+t.loginToken),n.post(r,{taskId:e.taskId,fileAndTag:o}).then(function(t){P.removeTask(e),c("task-delete-success",e),P.prepareUpload()},function(t){c("task-delete-error",[e,t])})}},this.calcFileMd5=function(e){return r.hash(e.name+e.size+s(e))},this.getFilesByStatus=function(){for(var e=[].slice.call(arguments,0),t=[],a=0;a<U.length;a++)for(var o=0;o<U[a].files.length;o++)for(var n=0;n<e.length;n++)if(U[a].files[o].status===e[n]){t.push({task:U[a],file:U[a].files[o]});break}return t},function(){$(window).on("beforeunload",i)}()};t.default=i}.call(t,a(4).Buffer)},function(e,t,a){"use strict";(function(e){function o(){return r.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function n(e,t){if(o()<t)throw new RangeError("Invalid typed array length");return r.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t),e.__proto__=r.prototype):(null===e&&(e=new r(t)),e.length=t),e}function r(e,t,a){if(!(r.TYPED_ARRAY_SUPPORT||this instanceof r))return new r(e,t,a);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return u(this,e)}return i(this,e,t,a)}function i(e,t,a,o){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?d(e,t,a,o):"string"==typeof t?p(e,t,a):c(e,t)}function s(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function l(e,t,a,o){return s(t),t<=0?n(e,t):void 0!==a?"string"==typeof o?n(e,t).fill(a,o):n(e,t).fill(a):n(e,t)}function u(e,t){if(s(t),e=n(e,t<0?0:0|m(t)),!r.TYPED_ARRAY_SUPPORT)for(var a=0;a<t;++a)e[a]=0;return e}function p(e,t,a){if("string"==typeof a&&""!==a||(a="utf8"),!r.isEncoding(a))throw new TypeError('"encoding" must be a valid string encoding');var o=0|g(t,a);e=n(e,o);var i=e.write(t,a);return i!==o&&(e=e.slice(0,i)),e}function f(e,t){var a=t.length<0?0:0|m(t.length);e=n(e,a);for(var o=0;o<a;o+=1)e[o]=255&t[o];return e}function d(e,t,a,o){if(t.byteLength,a<0||t.byteLength<a)throw new RangeError("'offset' is out of bounds");if(t.byteLength<a+(o||0))throw new RangeError("'length' is out of bounds");return t=void 0===a&&void 0===o?new Uint8Array(t):void 0===o?new Uint8Array(t,a):new Uint8Array(t,a,o),r.TYPED_ARRAY_SUPPORT?(e=t,e.__proto__=r.prototype):e=f(e,t),e}function c(e,t){if(r.isBuffer(t)){var a=0|m(t.length);return e=n(e,a),0===e.length?e:(t.copy(e,0,0,a),e)}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||V(t.length)?n(e,0):f(e,t);if("Buffer"===t.type&&X(t.data))return f(e,t.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function m(e){if(e>=o())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+o().toString(16)+" bytes");return 0|e}function h(e){return+e!=e&&(e=0),r.alloc(+e)}function g(e,t){if(r.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var a=e.length;if(0===a)return 0;for(var o=!1;;)switch(t){case"ascii":case"latin1":case"binary":return a;case"utf8":case"utf-8":case void 0:return G(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*a;case"hex":return a>>>1;case"base64":return q(e).length;default:if(o)return G(e).length;t=(""+t).toLowerCase(),o=!0}}function b(e,t,a){var o=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===a||a>this.length)&&(a=this.length),a<=0)return"";if(a>>>=0,t>>>=0,a<=t)return"";for(e||(e="utf8");;)switch(e){case"hex":return B(this,t,a);case"utf8":case"utf-8":return U(this,t,a);case"ascii":return A(this,t,a);case"latin1":case"binary":return C(this,t,a);case"base64":return P(this,t,a);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return R(this,t,a);default:if(o)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),o=!0}}function y(e,t,a){var o=e[t];e[t]=e[a],e[a]=o}function x(e,t,a,o,n){if(0===e.length)return-1;if("string"==typeof a?(o=a,a=0):a>2147483647?a=2147483647:a<-2147483648&&(a=-2147483648),a=+a,isNaN(a)&&(a=n?0:e.length-1),a<0&&(a=e.length+a),a>=e.length){if(n)return-1;a=e.length-1}else if(a<0){if(!n)return-1;a=0}if("string"==typeof t&&(t=r.from(t,o)),r.isBuffer(t))return 0===t.length?-1:k(e,t,a,o,n);if("number"==typeof t)return t&=255,r.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?n?Uint8Array.prototype.indexOf.call(e,t,a):Uint8Array.prototype.lastIndexOf.call(e,t,a):k(e,[t],a,o,n);throw new TypeError("val must be string, number or Buffer")}function k(e,t,a,o,n){function r(e,t){return 1===i?e[t]:e.readUInt16BE(t*i)}var i=1,s=e.length,l=t.length;if(void 0!==o&&("ucs2"===(o=String(o).toLowerCase())||"ucs-2"===o||"utf16le"===o||"utf-16le"===o)){if(e.length<2||t.length<2)return-1;i=2,s/=2,l/=2,a/=2}var u;if(n){var p=-1;for(u=a;u<s;u++)if(r(e,u)===r(t,-1===p?0:u-p)){if(-1===p&&(p=u),u-p+1===l)return p*i}else-1!==p&&(u-=u-p),p=-1}else for(a+l>s&&(a=s-l),u=a;u>=0;u--){for(var f=!0,d=0;d<l;d++)if(r(e,u+d)!==r(t,d)){f=!1;break}if(f)return u}return-1}function v(e,t,a,o){a=Number(a)||0;var n=e.length-a;o?(o=Number(o))>n&&(o=n):o=n;var r=t.length;if(r%2!=0)throw new TypeError("Invalid hex string");o>r/2&&(o=r/2);for(var i=0;i<o;++i){var s=parseInt(t.substr(2*i,2),16);if(isNaN(s))return i;e[a+i]=s}return i}function w(e,t,a,o){return Q(G(t,e.length-a),e,a,o)}function T(e,t,a,o){return Q(K(t),e,a,o)}function I(e,t,a,o){return T(e,t,a,o)}function S(e,t,a,o){return Q(q(t),e,a,o)}function E(e,t,a,o){return Q(J(t,e.length-a),e,a,o)}function P(e,t,a){return 0===t&&a===e.length?W.fromByteArray(e):W.fromByteArray(e.slice(t,a))}function U(e,t,a){a=Math.min(e.length,a);for(var o=[],n=t;n<a;){var r=e[n],i=null,s=r>239?4:r>223?3:r>191?2:1;if(n+s<=a){var l,u,p,f;switch(s){case 1:r<128&&(i=r);break;case 2:l=e[n+1],128==(192&l)&&(f=(31&r)<<6|63&l)>127&&(i=f);break;case 3:l=e[n+1],u=e[n+2],128==(192&l)&&128==(192&u)&&(f=(15&r)<<12|(63&l)<<6|63&u)>2047&&(f<55296||f>57343)&&(i=f);break;case 4:l=e[n+1],u=e[n+2],p=e[n+3],128==(192&l)&&128==(192&u)&&128==(192&p)&&(f=(15&r)<<18|(63&l)<<12|(63&u)<<6|63&p)>65535&&f<1114112&&(i=f)}}null===i?(i=65533,s=1):i>65535&&(i-=65536,o.push(i>>>10&1023|55296),i=56320|1023&i),o.push(i),n+=s}return _(o)}function _(e){var t=e.length;if(t<=Z)return String.fromCharCode.apply(String,e);for(var a="",o=0;o<t;)a+=String.fromCharCode.apply(String,e.slice(o,o+=Z));return a}function A(e,t,a){var o="";a=Math.min(e.length,a);for(var n=t;n<a;++n)o+=String.fromCharCode(127&e[n]);return o}function C(e,t,a){var o="";a=Math.min(e.length,a);for(var n=t;n<a;++n)o+=String.fromCharCode(e[n]);return o}function B(e,t,a){var o=e.length;(!t||t<0)&&(t=0),(!a||a<0||a>o)&&(a=o);for(var n="",r=t;r<a;++r)n+=Y(e[r]);return n}function R(e,t,a){for(var o=e.slice(t,a),n="",r=0;r<o.length;r+=2)n+=String.fromCharCode(o[r]+256*o[r+1]);return n}function F(e,t,a){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>a)throw new RangeError("Trying to access beyond buffer length")}function z(e,t,a,o,n,i){if(!r.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>n||t<i)throw new RangeError('"value" argument is out of bounds');if(a+o>e.length)throw new RangeError("Index out of range")}function O(e,t,a,o){t<0&&(t=65535+t+1);for(var n=0,r=Math.min(e.length-a,2);n<r;++n)e[a+n]=(t&255<<8*(o?n:1-n))>>>8*(o?n:1-n)}function j(e,t,a,o){t<0&&(t=4294967295+t+1);for(var n=0,r=Math.min(e.length-a,4);n<r;++n)e[a+n]=t>>>8*(o?n:3-n)&255}function N(e,t,a,o,n,r){if(a+o>e.length)throw new RangeError("Index out of range");if(a<0)throw new RangeError("Index out of range")}function M(e,t,a,o,n){return n||N(e,t,a,4,3.4028234663852886e38,-3.4028234663852886e38),H.write(e,t,a,o,23,4),a+4}function L(e,t,a,o,n){return n||N(e,t,a,8,1.7976931348623157e308,-1.7976931348623157e308),H.write(e,t,a,o,52,8),a+8}function D(e){if(e=$(e).replace(ee,""),e.length<2)return"";for(;e.length%4!=0;)e+="=";return e}function $(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}function Y(e){return e<16?"0"+e.toString(16):e.toString(16)}function G(e,t){t=t||1/0;for(var a,o=e.length,n=null,r=[],i=0;i<o;++i){if((a=e.charCodeAt(i))>55295&&a<57344){if(!n){if(a>56319){(t-=3)>-1&&r.push(239,191,189);continue}if(i+1===o){(t-=3)>-1&&r.push(239,191,189);continue}n=a;continue}if(a<56320){(t-=3)>-1&&r.push(239,191,189),n=a;continue}a=65536+(n-55296<<10|a-56320)}else n&&(t-=3)>-1&&r.push(239,191,189);if(n=null,a<128){if((t-=1)<0)break;r.push(a)}else if(a<2048){if((t-=2)<0)break;r.push(a>>6|192,63&a|128)}else if(a<65536){if((t-=3)<0)break;r.push(a>>12|224,a>>6&63|128,63&a|128)}else{if(!(a<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;r.push(a>>18|240,a>>12&63|128,a>>6&63|128,63&a|128)}}return r}function K(e){for(var t=[],a=0;a<e.length;++a)t.push(255&e.charCodeAt(a));return t}function J(e,t){for(var a,o,n,r=[],i=0;i<e.length&&!((t-=2)<0);++i)a=e.charCodeAt(i),o=a>>8,n=a%256,r.push(n),r.push(o);return r}function q(e){return W.toByteArray(D(e))}function Q(e,t,a,o){for(var n=0;n<o&&!(n+a>=t.length||n>=e.length);++n)t[n+a]=e[n];return n}function V(e){return e!==e}/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <<EMAIL>> <http://feross.org>
 * @license  MIT
 */
var W=a(6),H=a(7),X=a(8);t.Buffer=r,t.SlowBuffer=h,t.INSPECT_MAX_BYTES=50,r.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}(),t.kMaxLength=o(),r.poolSize=8192,r._augment=function(e){return e.__proto__=r.prototype,e},r.from=function(e,t,a){return i(null,e,t,a)},r.TYPED_ARRAY_SUPPORT&&(r.prototype.__proto__=Uint8Array.prototype,r.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&r[Symbol.species]===r&&Object.defineProperty(r,Symbol.species,{value:null,configurable:!0})),r.alloc=function(e,t,a){return l(null,e,t,a)},r.allocUnsafe=function(e){return u(null,e)},r.allocUnsafeSlow=function(e){return u(null,e)},r.isBuffer=function(e){return!(null==e||!e._isBuffer)},r.compare=function(e,t){if(!r.isBuffer(e)||!r.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var a=e.length,o=t.length,n=0,i=Math.min(a,o);n<i;++n)if(e[n]!==t[n]){a=e[n],o=t[n];break}return a<o?-1:o<a?1:0},r.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},r.concat=function(e,t){if(!X(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return r.alloc(0);var a;if(void 0===t)for(t=0,a=0;a<e.length;++a)t+=e[a].length;var o=r.allocUnsafe(t),n=0;for(a=0;a<e.length;++a){var i=e[a];if(!r.isBuffer(i))throw new TypeError('"list" argument must be an Array of Buffers');i.copy(o,n),n+=i.length}return o},r.byteLength=g,r.prototype._isBuffer=!0,r.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)y(this,t,t+1);return this},r.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)y(this,t,t+3),y(this,t+1,t+2);return this},r.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)y(this,t,t+7),y(this,t+1,t+6),y(this,t+2,t+5),y(this,t+3,t+4);return this},r.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?U(this,0,e):b.apply(this,arguments)},r.prototype.equals=function(e){if(!r.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===r.compare(this,e)},r.prototype.inspect=function(){var e="",a=t.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,a).match(/.{2}/g).join(" "),this.length>a&&(e+=" ... ")),"<Buffer "+e+">"},r.prototype.compare=function(e,t,a,o,n){if(!r.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===a&&(a=e?e.length:0),void 0===o&&(o=0),void 0===n&&(n=this.length),t<0||a>e.length||o<0||n>this.length)throw new RangeError("out of range index");if(o>=n&&t>=a)return 0;if(o>=n)return-1;if(t>=a)return 1;if(t>>>=0,a>>>=0,o>>>=0,n>>>=0,this===e)return 0;for(var i=n-o,s=a-t,l=Math.min(i,s),u=this.slice(o,n),p=e.slice(t,a),f=0;f<l;++f)if(u[f]!==p[f]){i=u[f],s=p[f];break}return i<s?-1:s<i?1:0},r.prototype.includes=function(e,t,a){return-1!==this.indexOf(e,t,a)},r.prototype.indexOf=function(e,t,a){return x(this,e,t,a,!0)},r.prototype.lastIndexOf=function(e,t,a){return x(this,e,t,a,!1)},r.prototype.write=function(e,t,a,o){if(void 0===t)o="utf8",a=this.length,t=0;else if(void 0===a&&"string"==typeof t)o=t,a=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(a)?(a|=0,void 0===o&&(o="utf8")):(o=a,a=void 0)}var n=this.length-t;if((void 0===a||a>n)&&(a=n),e.length>0&&(a<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");o||(o="utf8");for(var r=!1;;)switch(o){case"hex":return v(this,e,t,a);case"utf8":case"utf-8":return w(this,e,t,a);case"ascii":return T(this,e,t,a);case"latin1":case"binary":return I(this,e,t,a);case"base64":return S(this,e,t,a);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return E(this,e,t,a);default:if(r)throw new TypeError("Unknown encoding: "+o);o=(""+o).toLowerCase(),r=!0}},r.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var Z=4096;r.prototype.slice=function(e,t){var a=this.length;e=~~e,t=void 0===t?a:~~t,e<0?(e+=a)<0&&(e=0):e>a&&(e=a),t<0?(t+=a)<0&&(t=0):t>a&&(t=a),t<e&&(t=e);var o;if(r.TYPED_ARRAY_SUPPORT)o=this.subarray(e,t),o.__proto__=r.prototype;else{var n=t-e;o=new r(n,void 0);for(var i=0;i<n;++i)o[i]=this[i+e]}return o},r.prototype.readUIntLE=function(e,t,a){e|=0,t|=0,a||F(e,t,this.length);for(var o=this[e],n=1,r=0;++r<t&&(n*=256);)o+=this[e+r]*n;return o},r.prototype.readUIntBE=function(e,t,a){e|=0,t|=0,a||F(e,t,this.length);for(var o=this[e+--t],n=1;t>0&&(n*=256);)o+=this[e+--t]*n;return o},r.prototype.readUInt8=function(e,t){return t||F(e,1,this.length),this[e]},r.prototype.readUInt16LE=function(e,t){return t||F(e,2,this.length),this[e]|this[e+1]<<8},r.prototype.readUInt16BE=function(e,t){return t||F(e,2,this.length),this[e]<<8|this[e+1]},r.prototype.readUInt32LE=function(e,t){return t||F(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},r.prototype.readUInt32BE=function(e,t){return t||F(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},r.prototype.readIntLE=function(e,t,a){e|=0,t|=0,a||F(e,t,this.length);for(var o=this[e],n=1,r=0;++r<t&&(n*=256);)o+=this[e+r]*n;return n*=128,o>=n&&(o-=Math.pow(2,8*t)),o},r.prototype.readIntBE=function(e,t,a){e|=0,t|=0,a||F(e,t,this.length);for(var o=t,n=1,r=this[e+--o];o>0&&(n*=256);)r+=this[e+--o]*n;return n*=128,r>=n&&(r-=Math.pow(2,8*t)),r},r.prototype.readInt8=function(e,t){return t||F(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},r.prototype.readInt16LE=function(e,t){t||F(e,2,this.length);var a=this[e]|this[e+1]<<8;return 32768&a?4294901760|a:a},r.prototype.readInt16BE=function(e,t){t||F(e,2,this.length);var a=this[e+1]|this[e]<<8;return 32768&a?4294901760|a:a},r.prototype.readInt32LE=function(e,t){return t||F(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},r.prototype.readInt32BE=function(e,t){return t||F(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},r.prototype.readFloatLE=function(e,t){return t||F(e,4,this.length),H.read(this,e,!0,23,4)},r.prototype.readFloatBE=function(e,t){return t||F(e,4,this.length),H.read(this,e,!1,23,4)},r.prototype.readDoubleLE=function(e,t){return t||F(e,8,this.length),H.read(this,e,!0,52,8)},r.prototype.readDoubleBE=function(e,t){return t||F(e,8,this.length),H.read(this,e,!1,52,8)},r.prototype.writeUIntLE=function(e,t,a,o){if(e=+e,t|=0,a|=0,!o){z(this,e,t,a,Math.pow(2,8*a)-1,0)}var n=1,r=0;for(this[t]=255&e;++r<a&&(n*=256);)this[t+r]=e/n&255;return t+a},r.prototype.writeUIntBE=function(e,t,a,o){if(e=+e,t|=0,a|=0,!o){z(this,e,t,a,Math.pow(2,8*a)-1,0)}var n=a-1,r=1;for(this[t+n]=255&e;--n>=0&&(r*=256);)this[t+n]=e/r&255;return t+a},r.prototype.writeUInt8=function(e,t,a){return e=+e,t|=0,a||z(this,e,t,1,255,0),r.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},r.prototype.writeUInt16LE=function(e,t,a){return e=+e,t|=0,a||z(this,e,t,2,65535,0),r.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):O(this,e,t,!0),t+2},r.prototype.writeUInt16BE=function(e,t,a){return e=+e,t|=0,a||z(this,e,t,2,65535,0),r.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):O(this,e,t,!1),t+2},r.prototype.writeUInt32LE=function(e,t,a){return e=+e,t|=0,a||z(this,e,t,4,4294967295,0),r.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):j(this,e,t,!0),t+4},r.prototype.writeUInt32BE=function(e,t,a){return e=+e,t|=0,a||z(this,e,t,4,4294967295,0),r.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):j(this,e,t,!1),t+4},r.prototype.writeIntLE=function(e,t,a,o){if(e=+e,t|=0,!o){var n=Math.pow(2,8*a-1);z(this,e,t,a,n-1,-n)}var r=0,i=1,s=0;for(this[t]=255&e;++r<a&&(i*=256);)e<0&&0===s&&0!==this[t+r-1]&&(s=1),this[t+r]=(e/i>>0)-s&255;return t+a},r.prototype.writeIntBE=function(e,t,a,o){if(e=+e,t|=0,!o){var n=Math.pow(2,8*a-1);z(this,e,t,a,n-1,-n)}var r=a-1,i=1,s=0;for(this[t+r]=255&e;--r>=0&&(i*=256);)e<0&&0===s&&0!==this[t+r+1]&&(s=1),this[t+r]=(e/i>>0)-s&255;return t+a},r.prototype.writeInt8=function(e,t,a){return e=+e,t|=0,a||z(this,e,t,1,127,-128),r.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},r.prototype.writeInt16LE=function(e,t,a){return e=+e,t|=0,a||z(this,e,t,2,32767,-32768),r.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):O(this,e,t,!0),t+2},r.prototype.writeInt16BE=function(e,t,a){return e=+e,t|=0,a||z(this,e,t,2,32767,-32768),r.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):O(this,e,t,!1),t+2},r.prototype.writeInt32LE=function(e,t,a){return e=+e,t|=0,a||z(this,e,t,4,2147483647,-2147483648),r.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):j(this,e,t,!0),t+4},r.prototype.writeInt32BE=function(e,t,a){return e=+e,t|=0,a||z(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),r.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):j(this,e,t,!1),t+4},r.prototype.writeFloatLE=function(e,t,a){return M(this,e,t,!0,a)},r.prototype.writeFloatBE=function(e,t,a){return M(this,e,t,!1,a)},r.prototype.writeDoubleLE=function(e,t,a){return L(this,e,t,!0,a)},r.prototype.writeDoubleBE=function(e,t,a){return L(this,e,t,!1,a)},r.prototype.copy=function(e,t,a,o){if(a||(a=0),o||0===o||(o=this.length),t>=e.length&&(t=e.length),t||(t=0),o>0&&o<a&&(o=a),o===a)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(a<0||a>=this.length)throw new RangeError("sourceStart out of bounds");if(o<0)throw new RangeError("sourceEnd out of bounds");o>this.length&&(o=this.length),e.length-t<o-a&&(o=e.length-t+a);var n,i=o-a;if(this===e&&a<t&&t<o)for(n=i-1;n>=0;--n)e[n+t]=this[n+a];else if(i<1e3||!r.TYPED_ARRAY_SUPPORT)for(n=0;n<i;++n)e[n+t]=this[n+a];else Uint8Array.prototype.set.call(e,this.subarray(a,a+i),t);return i},r.prototype.fill=function(e,t,a,o){if("string"==typeof e){if("string"==typeof t?(o=t,t=0,a=this.length):"string"==typeof a&&(o=a,a=this.length),1===e.length){var n=e.charCodeAt(0);n<256&&(e=n)}if(void 0!==o&&"string"!=typeof o)throw new TypeError("encoding must be a string");if("string"==typeof o&&!r.isEncoding(o))throw new TypeError("Unknown encoding: "+o)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<a)throw new RangeError("Out of range index");if(a<=t)return this;t>>>=0,a=void 0===a?this.length:a>>>0,e||(e=0);var i;if("number"==typeof e)for(i=t;i<a;++i)this[i]=e;else{var s=r.isBuffer(e)?e:G(new r(e,o).toString()),l=s.length;for(i=0;i<a-t;++i)this[i+t]=s[i%l]}return this};var ee=/[^+\/0-9A-Za-z-_]/g}).call(t,a(5))},function(e,t){var a;a=function(){return this}();try{a=a||Function("return this")()||(0,eval)("this")}catch(e){"object"==typeof window&&(a=window)}e.exports=a},function(e,t,a){"use strict";function o(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");return"="===e[t-2]?2:"="===e[t-1]?1:0}function n(e){return 3*e.length/4-o(e)}function r(e){var t,a,n,r,i,s=e.length;r=o(e),i=new f(3*s/4-r),a=r>0?s-4:s;var l=0;for(t=0;t<a;t+=4)n=p[e.charCodeAt(t)]<<18|p[e.charCodeAt(t+1)]<<12|p[e.charCodeAt(t+2)]<<6|p[e.charCodeAt(t+3)],i[l++]=n>>16&255,i[l++]=n>>8&255,i[l++]=255&n;return 2===r?(n=p[e.charCodeAt(t)]<<2|p[e.charCodeAt(t+1)]>>4,i[l++]=255&n):1===r&&(n=p[e.charCodeAt(t)]<<10|p[e.charCodeAt(t+1)]<<4|p[e.charCodeAt(t+2)]>>2,i[l++]=n>>8&255,i[l++]=255&n),i}function i(e){return u[e>>18&63]+u[e>>12&63]+u[e>>6&63]+u[63&e]}function s(e,t,a){for(var o,n=[],r=t;r<a;r+=3)o=(e[r]<<16&16711680)+(e[r+1]<<8&65280)+(255&e[r+2]),n.push(i(o));return n.join("")}function l(e){for(var t,a=e.length,o=a%3,n="",r=[],i=0,l=a-o;i<l;i+=16383)r.push(s(e,i,i+16383>l?l:i+16383));return 1===o?(t=e[a-1],n+=u[t>>2],n+=u[t<<4&63],n+="=="):2===o&&(t=(e[a-2]<<8)+e[a-1],n+=u[t>>10],n+=u[t>>4&63],n+=u[t<<2&63],n+="="),r.push(n),r.join("")}t.byteLength=n,t.toByteArray=r,t.fromByteArray=l;for(var u=[],p=[],f="undefined"!=typeof Uint8Array?Uint8Array:Array,d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",c=0,m=d.length;c<m;++c)u[c]=d[c],p[d.charCodeAt(c)]=c;p["-".charCodeAt(0)]=62,p["_".charCodeAt(0)]=63},function(e,t){t.read=function(e,t,a,o,n){var r,i,s=8*n-o-1,l=(1<<s)-1,u=l>>1,p=-7,f=a?n-1:0,d=a?-1:1,c=e[t+f];for(f+=d,r=c&(1<<-p)-1,c>>=-p,p+=s;p>0;r=256*r+e[t+f],f+=d,p-=8);for(i=r&(1<<-p)-1,r>>=-p,p+=o;p>0;i=256*i+e[t+f],f+=d,p-=8);if(0===r)r=1-u;else{if(r===l)return i?NaN:1/0*(c?-1:1);i+=Math.pow(2,o),r-=u}return(c?-1:1)*i*Math.pow(2,r-o)},t.write=function(e,t,a,o,n,r){var i,s,l,u=8*r-n-1,p=(1<<u)-1,f=p>>1,d=23===n?Math.pow(2,-24)-Math.pow(2,-77):0,c=o?0:r-1,m=o?1:-1,h=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(s=isNaN(t)?1:0,i=p):(i=Math.floor(Math.log(t)/Math.LN2),t*(l=Math.pow(2,-i))<1&&(i--,l*=2),t+=i+f>=1?d/l:d*Math.pow(2,1-f),t*l>=2&&(i++,l/=2),i+f>=p?(s=0,i=p):i+f>=1?(s=(t*l-1)*Math.pow(2,n),i+=f):(s=t*Math.pow(2,f-1)*Math.pow(2,n),i=0));n>=8;e[a+c]=255&s,c+=m,s/=256,n-=8);for(i=i<<n|s,u+=n;u>0;e[a+c]=255&i,c+=m,i/=256,u-=8);e[a+c-m]|=128*h}},function(e,t){var a={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==a.call(e)}},function(e,t,a){!function(t){e.exports=t()}(function(e){"use strict";var t=function(e,t){return e+t&4294967295},a=function(e,a,o,n,r,i){return a=t(t(a,e),t(n,i)),t(a<<r|a>>>32-r,o)},o=function(e,t,o,n,r,i,s){return a(t&o|~t&n,e,t,r,i,s)},n=function(e,t,o,n,r,i,s){return a(t&n|o&~n,e,t,r,i,s)},r=function(e,t,o,n,r,i,s){return a(t^o^n,e,t,r,i,s)},i=function(e,t,o,n,r,i,s){return a(o^(t|~n),e,t,r,i,s)},s=function(e,a){var s=e[0],l=e[1],u=e[2],p=e[3];s=o(s,l,u,p,a[0],7,-680876936),p=o(p,s,l,u,a[1],12,-389564586),u=o(u,p,s,l,a[2],17,606105819),l=o(l,u,p,s,a[3],22,-1044525330),s=o(s,l,u,p,a[4],7,-176418897),p=o(p,s,l,u,a[5],12,1200080426),u=o(u,p,s,l,a[6],17,-1473231341),l=o(l,u,p,s,a[7],22,-45705983),s=o(s,l,u,p,a[8],7,1770035416),p=o(p,s,l,u,a[9],12,-1958414417),u=o(u,p,s,l,a[10],17,-42063),l=o(l,u,p,s,a[11],22,-1990404162),s=o(s,l,u,p,a[12],7,1804603682),p=o(p,s,l,u,a[13],12,-40341101),u=o(u,p,s,l,a[14],17,-1502002290),l=o(l,u,p,s,a[15],22,1236535329),s=n(s,l,u,p,a[1],5,-165796510),p=n(p,s,l,u,a[6],9,-1069501632),u=n(u,p,s,l,a[11],14,643717713),l=n(l,u,p,s,a[0],20,-373897302),s=n(s,l,u,p,a[5],5,-701558691),p=n(p,s,l,u,a[10],9,38016083),u=n(u,p,s,l,a[15],14,-660478335),l=n(l,u,p,s,a[4],20,-405537848),s=n(s,l,u,p,a[9],5,568446438),p=n(p,s,l,u,a[14],9,-1019803690),u=n(u,p,s,l,a[3],14,-187363961),l=n(l,u,p,s,a[8],20,1163531501),s=n(s,l,u,p,a[13],5,-1444681467),p=n(p,s,l,u,a[2],9,-51403784),u=n(u,p,s,l,a[7],14,1735328473),l=n(l,u,p,s,a[12],20,-1926607734),s=r(s,l,u,p,a[5],4,-378558),p=r(p,s,l,u,a[8],11,-2022574463),u=r(u,p,s,l,a[11],16,1839030562),l=r(l,u,p,s,a[14],23,-35309556),s=r(s,l,u,p,a[1],4,-1530992060),p=r(p,s,l,u,a[4],11,1272893353),u=r(u,p,s,l,a[7],16,-155497632),l=r(l,u,p,s,a[10],23,-1094730640),s=r(s,l,u,p,a[13],4,681279174),p=r(p,s,l,u,a[0],11,-358537222),u=r(u,p,s,l,a[3],16,-722521979),l=r(l,u,p,s,a[6],23,76029189),s=r(s,l,u,p,a[9],4,-640364487),p=r(p,s,l,u,a[12],11,-421815835),u=r(u,p,s,l,a[15],16,530742520),l=r(l,u,p,s,a[2],23,-995338651),s=i(s,l,u,p,a[0],6,-198630844),p=i(p,s,l,u,a[7],10,1126891415),u=i(u,p,s,l,a[14],15,-1416354905),l=i(l,u,p,s,a[5],21,-57434055),s=i(s,l,u,p,a[12],6,1700485571),p=i(p,s,l,u,a[3],10,-1894986606),u=i(u,p,s,l,a[10],15,-1051523),l=i(l,u,p,s,a[1],21,-2054922799),s=i(s,l,u,p,a[8],6,1873313359),p=i(p,s,l,u,a[15],10,-30611744),u=i(u,p,s,l,a[6],15,-1560198380),l=i(l,u,p,s,a[13],21,1309151649),s=i(s,l,u,p,a[4],6,-145523070),p=i(p,s,l,u,a[11],10,-1120210379),u=i(u,p,s,l,a[2],15,718787259),l=i(l,u,p,s,a[9],21,-343485551),e[0]=t(s,e[0]),e[1]=t(l,e[1]),e[2]=t(u,e[2]),e[3]=t(p,e[3])},l=function(e){var t,a=[];for(t=0;t<64;t+=4)a[t>>2]=e.charCodeAt(t)+(e.charCodeAt(t+1)<<8)+(e.charCodeAt(t+2)<<16)+(e.charCodeAt(t+3)<<24);return a},u=function(e){var t,a=[];for(t=0;t<64;t+=4)a[t>>2]=e[t]+(e[t+1]<<8)+(e[t+2]<<16)+(e[t+3]<<24);return a},p=function(e){var t,a,o,n,r,i,u=e.length,p=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=u;t+=64)s(p,l(e.substring(t-64,t)));for(e=e.substring(t-64),a=e.length,o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],t=0;t<a;t+=1)o[t>>2]|=e.charCodeAt(t)<<(t%4<<3);if(o[t>>2]|=128<<(t%4<<3),t>55)for(s(p,o),t=0;t<16;t+=1)o[t]=0;return n=8*u,n=n.toString(16).match(/(.*?)(.{0,8})$/),r=parseInt(n[2],16),i=parseInt(n[1],16)||0,o[14]=r,o[15]=i,s(p,o),p},f=function(e){var t,a,o,n,r,i,l=e.length,p=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=l;t+=64)s(p,u(e.subarray(t-64,t)));for(e=t-64<l?e.subarray(t-64):new Uint8Array(0),a=e.length,o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],t=0;t<a;t+=1)o[t>>2]|=e[t]<<(t%4<<3);if(o[t>>2]|=128<<(t%4<<3),t>55)for(s(p,o),t=0;t<16;t+=1)o[t]=0;return n=8*l,n=n.toString(16).match(/(.*?)(.{0,8})$/),r=parseInt(n[2],16),i=parseInt(n[1],16)||0,o[14]=r,o[15]=i,s(p,o),p},d=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"],c=function(e){var t,a="";for(t=0;t<4;t+=1)a+=d[e>>8*t+4&15]+d[e>>8*t&15];return a},m=function(e){var t;for(t=0;t<e.length;t+=1)e[t]=c(e[t]);return e.join("")},h=function(){this.reset()};return"5d41402abc4b2a76b9719d911017c592"!==function(e){return m(p(e))}("hello")&&(t=function(e,t){var a=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(a>>16)<<16|65535&a}),h.prototype.append=function(e){return/[\u0080-\uFFFF]/.test(e)&&(e=unescape(encodeURIComponent(e))),this.appendBinary(e),this},h.prototype.appendBinary=function(e){this._buff+=e,this._length+=e.length;var t,a=this._buff.length;for(t=64;t<=a;t+=64)s(this._state,l(this._buff.substring(t-64,t)));return this._buff=this._buff.substr(t-64),this},h.prototype.end=function(e){var t,a,o=this._buff,n=o.length,r=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<n;t+=1)r[t>>2]|=o.charCodeAt(t)<<(t%4<<3);return this._finish(r,n),a=e?this._state:m(this._state),this.reset(),a},h.prototype._finish=function(e,t){var a,o,n,r=t;if(e[r>>2]|=128<<(r%4<<3),r>55)for(s(this._state,e),r=0;r<16;r+=1)e[r]=0;a=8*this._length,a=a.toString(16).match(/(.*?)(.{0,8})$/),o=parseInt(a[2],16),n=parseInt(a[1],16)||0,e[14]=o,e[15]=n,s(this._state,e)},h.prototype.reset=function(){return this._buff="",this._length=0,this._state=[1732584193,-271733879,-1732584194,271733878],this},h.prototype.destroy=function(){delete this._state,delete this._buff,delete this._length},h.hash=function(e,t){/[\u0080-\uFFFF]/.test(e)&&(e=unescape(encodeURIComponent(e)));var a=p(e);return t?a:m(a)},h.hashBinary=function(e,t){var a=p(e);return t?a:m(a)},h.ArrayBuffer=function(){this.reset()},h.ArrayBuffer.prototype.append=function(e){var t,a=this._concatArrayBuffer(this._buff,e),o=a.length;for(this._length+=e.byteLength,t=64;t<=o;t+=64)s(this._state,u(a.subarray(t-64,t)));return this._buff=t-64<o?a.subarray(t-64):new Uint8Array(0),this},h.ArrayBuffer.prototype.end=function(e){var t,a,o=this._buff,n=o.length,r=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<n;t+=1)r[t>>2]|=o[t]<<(t%4<<3);return this._finish(r,n),a=e?this._state:m(this._state),this.reset(),a},h.ArrayBuffer.prototype._finish=h.prototype._finish,h.ArrayBuffer.prototype.reset=function(){return this._buff=new Uint8Array(0),this._length=0,this._state=[1732584193,-271733879,-1732584194,271733878],this},h.ArrayBuffer.prototype.destroy=h.prototype.destroy,h.ArrayBuffer.prototype._concatArrayBuffer=function(e,t){var a=e.length,o=new Uint8Array(a+t.byteLength);return o.set(e),o.set(new Uint8Array(t),a),o},h.ArrayBuffer.hash=function(e,t){var a=f(new Uint8Array(e));return t?a:m(a)},h})},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=a(0),n=(a(1).default,a(2).default),r=function(e){function t(){return window.nxt&&window.nxt.config?window.nxt.config:e.configInst?e.configInst:void 0}function a(){return"http://"+location.host+"/upload/complete/?user_token="}function r(e){var o={TaskName:e.metadata.name,TaskGuid:e.taskId,TransType:"Upload",TransFile:[],UserInfo:{UserId:nxt.user.current.id.toString(),UserName:nxt.user.current.nickName||nxt.user.current.loginName,UserCode:nxt.user.current.userCode},ExtendeAttr:[]};return 1==t().vtubeInfo.importType&&(o.CallBackUrl=a()),t().vtubeInfo.usedConfig&&(o.ServerInfo={HostName:t().vtubeInfo.address,Port:parseInt(t().vtubeInfo.port),Scheme:"FTP",UserName:t().vtubeInfo.userName,Password:t().vtubeInfo.password,PathRoot:""}),2==t().storageType&&(o.ServerInfo={HostName:"",Port:0,Scheme:"ALIS3",UserName:"",Password:"",PathRoot:""}),o.ExtendeAttr=_.map(e.metadata.field,function(e){var t={ItemCode:e.fieldName,ItemName:e.alias};if(8==e.controlType)if(null!=e.value&&""!=e.value&&"[]"!=e.value)try{t.Value=JSON.parse(e.value)[0]}catch(e){t.Value=""}else t.Value="";else t.Value=e.value||"";return t}),o.ExtendeAttr.push({ItemCode:"tree",ItemName:"目录树",Value:e.tree}),_.remove(o.ExtendeAttr,{ItemCode:"cliptype"}),o.ExtendeAttr.push({ItemCode:"cliptype",ItemName:"素材类型",Value:e.entityType}),o}var i=this,s="http://127.0.0.1:8084/";this.openFileSelector=function(a){$.ajax({url:s+"request/getuploadfiles?user_token=&filetype=all",success:function(t){var n=_.isString(t)?JSON.parse(t):t;if(0!==n.FileCount){var r=[];_.forEach(n.FileList,function(t){var a="."+o.default.getExtension(t.FilePath);r.push({entityType:o.default.getTypeByExt(a,e.configInst).code,fileName:o.default.getFullFileName(t.FilePath),metadata:{name:o.default.getFileName(t.FilePath),ext:a},status:"added",progress:0,file:t})}),a(r)}},error:function(e){console.error(e);var a=t().vtubeDownloadPath||t().server+"/assets/Sobey_vRocket_v2.0_Setup.exe";o.default.prompt(l("upload.clientInstallTip",'<p>打开客户端失败</p><p>请确定已经正常开启客户端或重启客户端再重试。</p><p>如没有安装，请点击下面地址。</p><p><a href="${path}"><i class="icon iconfont icon-iconfontxiazai" style="margin-right:5px"></i>点击客户端下载</a></p>',{path:a}))}})},this.createTask=function(e,t){var a=[];switch(t.taskType){case 1:_.isArray(e)||(e=[e]),_.forEach(e,function(e){e.file?(e.files=[{file:e.file,fileName:e.fileName,fileSize:e.file.FileSize}],delete e.file,a.push(e)):e.files&&e.files.length>0&&(e.files=_.map(e.files,function(e){return{file:e.file,fileName:e.fileName,fileSize:e.fileSize}}),a.push(e))});break;case 2:case 3:a.push(e)}_.forEach(a,function(e){e.taskType=t.taskType,e.transferType=t.transferType,e.targetFolder=t.targetFolder,e.relationContentType=t.relationContentType,e.relationContentId=t.relationContentId,i.addTask(e)})},this.addTask=function(a){var i=t().server+"/upload/multipart/init";e.loginToken&&(i+="?token="+e.loginToken),n.post(i,a).then(function(e){function n(e){return _.isString(e.location)&&-1==e.location.indexOf("oss:")?t().vtubeInfo.path+e.location.replace(/\\/g,"/"):e.location}e=e.data;var i=r(e);switch(i.ExtendData=e.userCode,a.taskType){case 1:i.TransFile.push({SourceFile:a.files[0].file.FilePath,DestFile:n(e.files[0])});break;case 2:case 3:_.forEach(e.files,function(e){i.TransFile.push({SourceFile:e.fileName,DestFile:n(e)})})}$.ajax({url:s+"request/addtask?user_token=",type:"POST",contentType:"application/json",data:JSON.stringify(i),success:function(e){"string"==typeof e&&(e=JSON.parse(e)),1==e.Result?o.default.msgOk(l("upload.addTaskOk","添加任务成功")):o.default.prompt(l("upload.addTaskError","添加任务失败：")+e.Msg),console.info(e)},error:function(e){console.info(e),o.default.prompt(l("upload.addTaskError","添加任务失败"))}})},function(e){o.default.prompt(l("upload.uploadError","上传失败：")+e.data.desc)})}};t.default=r},function(e,t,a){var o=a(12).default,n=a(0).default,r=a(1).default,i=a(2).default,s={loadFrameworkScripts:function(e,t){return new Promise(function(a,o){n.loadScripts([e.basePath+"/libs/jquery.min.js",e.basePath+"/libs/lodash.min.js",e.basePath+"/libs/angular.min.js"]).then(function(){t?n.loadScripts([e.basePath+"/libs/mam-base/dist/mam-base.js",e.basePath+"/libs/oss/aliyun-oss-sdk-4.3.0.min.js",e.basePath+"/libs/oss-s3/aws-sdk.min.js",e.basePath+"/libs/oos/oos-sdk.min.js",e.basePath+"/libs/font-awesome/css/font-awesome.min.css",e.basePath+"/libs/bootstrap/css/bootstrap.min.css",e.basePath+"/libs/iconfont/iconfont.css"]).then(function(){a(e)}):n.loadScripts([e.basePath+"/libs/angular-ui-router.min.js",e.basePath+"/libs/angular-sanitize.min.js",e.basePath+"/libs/angular-ui/ui-bootstrap-tpls-2.5.0.min.js",e.basePath+"/libs/datetimepicker/jquery.datetimepicker.full.min.js",e.basePath+"/libs/ng-tags-input/ng-tags-input.js",e.basePath+"/libs/ui-select/select.min.js",e.basePath+"/libs/mam-base/dist/mam-base.js",e.basePath+"/libs/mam-timecode-convert/dist/mam-timecode-convert.min.js",e.basePath+"/libs/mam-ng/dist/mam-ng.js",e.basePath+"/libs/mam-metadata/dist/mam-metadata.js",e.basePath+"/libs/oss/aliyun-oss-sdk-4.3.0.min.js",e.basePath+"/libs/oss-s3/aws-sdk.min.js",e.basePath+"/libs/oos/oos-sdk.min.js",e.basePath+"/libs/font-awesome/css/font-awesome.min.css",e.basePath+"/libs/bootstrap/css/bootstrap.min.css",e.basePath+"/libs/datetimepicker/jquery.datetimepicker.css",e.basePath+"/libs/ng-tags-input/ng-tags-input.min.css",e.basePath+"/libs/ng-tags-input/ng-tags-input.bootstrap.min.css",e.basePath+"/libs/ui-select/select.min.css",e.basePath+"/libs/iconfont/iconfont.css"]).then(function(){a(e)})})})},loadConfig:function(e){return new Promise(function(t,a){if(window.nxt=window.nxt||{app:{},task:{},config:{},entity:{},currentUser:null},!window.nxt.config||$.isEmptyObject(window.nxt.config)){var o=e.server+"/config/get-base-info";e.loginToken&&(o+="?token="+e.loginToken),i.get(o).then(function(a){!0===a.success&&(window.nxt.config=a.data.config,nxt.config.server||(nxt.config.server=e.server),nxt.user.current=a.data.currentUser,mam.entity.types=a.data.config.entityTypes,t(e))})}else t(e)})},initUploadFramework:function(e){var t=$.Deferred();return s.loadFrameworkScripts(e).then(function(e){s.loadConfig(e).then(function(e){s.initApp(e),t.resolve()})}),t},initNoNg:function(e){var t=$.Deferred();return s.loadFrameworkScripts(e,!0).then(function(e){s.loadConfig(e).then(function(e){s.uploader=new o(e),t.resolve()})}),t},initApp:function(e){s.uploader=new o(e);var t=angular.module("mam-upload",["mam-metadata","mam-ng","ngTagsInput","ui.select","ngSanitize","ui.bootstrap"]);return mam.ng.config(t),e.el&&mam.ng.httpProvider(t,{server:nxt.config.server}),a(18),a(19),a(21),a(23),a(25),a(26),a(27),e.el&&(t.controller("mamUploadCtrl",["$scope","$http","$uibModal",function(e,t,a){s.uploader.init(a)}]),angular.bootstrap($(e.el)[0],["mam-upload"])),e.loginToken,t}};r.mamUpload=s;var l=window.define;"function"==typeof l&&l.amd?l("mam-upload",[],s):window.mamUpload=s},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=a(3),n=a(10),r=a(0).default;a(13);var i=function(e){function t(t,o){var n="general";for(var i in a.taskType)if(a.taskType.hasOwnProperty(i)&&a.taskType[i]===t.uploadParams.taskType){n=i;break}r.asyncLoadScript(e.basePath+"/directives/"+n+"/modal-"+n+".js",function(){$.get(e.basePath+"/directives/"+n+"/modal-"+n+".html").then(function(e){a.$uibModal.open({template:e,controller:"uploadModal"+n+"Ctrl",windowClass:"modal-upload-"+n,resolve:{opts:function(){return t}}}).result.then(function(e){o(e)})})})}var a=this;this.transferType={web:1,vtube:2},this.taskType={general:1,group:2,picturePackage:3},this.relationContentType={none:0,manuscript:1,dataset:2,attachment:3},this.modules={yunpan:"yunpan",webupload:"webupload",necs:"necs"},this.module=this.modules.yunpan,this.web=new o.default(e),this.vtube=new n.default(e),this.transfers={},this.transfers[this.transferType.web]=this.web,this.transfers[this.transferType.vtube]=this.vtube,this.init=function(e,t){window.OSS&&(this.web.OSS=window.OSS),e&&(a.$uibModal=e),t&&(a.web.OSS=t)},this.createTask=function(e){var o=$.extend({},{module:a.module,taskType:a.taskType.general,transferType:a.transferType.web,targetFolder:"",relationContentType:a.relationContentType.none,relationContentId:"",writeMetadataBefore:function(){}},e),n=a.transfers[o.transferType];n.openFileSelector(function(a){if(!1!==o.writeMetadataBefore(a,o)){t({uploadParams:o,files:a,extraField:e.extraField},function(e){null!=e&&n.createTask(e,o)})}})},this.validateExt=function(e){if(3===nxt.config.uploadExtensionLimit.type){if(e===_.find(nxt.config.uploadExtensionLimit.extensions,function(t){return t===e}))return!1}else if(2===nxt.config.uploadExtensionLimit.type&&e!==_.find(nxt.config.uploadExtensionLimit.extensions,function(t){return t===e}))return!1;return!0}};t.default=i},function(e,t,a){var o=a(14);"string"==typeof o&&(o=[[e.i,o,""]]);var n={};n.transform=void 0;a(16)(o,n);o.locals&&(e.exports=o.locals)},function(e,t,a){t=e.exports=a(15)(!1),t.push([e.i,".modal{position:fixed;top:0;right:0;bottom:0;left:0;z-index:1050;display:none;overflow:hidden;-webkit-overflow-scrolling:touch;outline:0}@media (-ms-high-contrast:none),screen and (-ms-high-contrast:active){.modal .modal-dialog{height:1px}}.modal .modal-dialog{display:flex;min-height:100vh;min-width:100vw;align-items:center;justify-content:center;margin:0 auto;padding:30px 0}.modal .modal-dialog .modal-content{display:flex;flex-direction:column}.modal .modal-dialog .modal-header{height:56px;min-height:56px;background:#212121;color:#fff}.modal .modal-dialog .modal-header button{outline:none;text-shadow:unset;transition:all .2s}.modal .modal-dialog .modal-body{overflow:auto;flex:1 0 auto}.modal .modal-dialog .modal-body a:hover{color:#e98b11}.modal .modal-dialog .modal-body .nav-tabs li a:hover{color:#fff}.modal .modal-dialog .modal-footer{min-height:65px;text-align:center}.modal .modal-dialog .modal-footer button{margin:0 5px}.modal .mam-metadata-form .mmf-group .mmf-head{color:#333}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-datetime input,.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-nanosecond-to-timecode input,.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-size input,.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-text input{background:#fff;border-color:#ccc;color:#555}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-datetime input[readonly],.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-nanosecond-to-timecode input[readonly],.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-size input[readonly],.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-text input[readonly]{background:#eee}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-textarea textarea,.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-tree .items{background:#fff;border-color:#ccc;color:#555}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-tag .tags-input .tags{background:#fff;border-color:#ccc}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-tag .tags-input .tags input{background-color:#fff;color:#555}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-tag .tags-input .tags .tag-item{background:#337ab7}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-select .form-control{background-color:#2c2c2c;background-image:none;border:1px solid #444}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-select .ui-select-bootstrap .ui-select-choices-row.active>span{background-color:#e98b11}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-select .ui-select-bootstrap .ui-select-choices-row>span{color:#fff}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-select .ui-select-bootstrap .ui-select-choices-row>span:hover{background-color:#62b0ef}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-select .dropdown-menu{background-color:#5c5c5c}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-select .btn-default{color:#555;background-color:#fff;border-color:#ccc}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-select .btn-default .close{color:#fff}.modal .mam-metadata-form .mmf-group .mmf-content .mam2-mfc-select .btn[disabled]{background-color:#b4b4b4!important;color:#555!important}body>.ui-select-bootstrap.open{z-index:1050!important}.btn-icon{border:none;background:none;outline:none;padding:0}.modal-upload-general .modal-body{width:980px;height:600px;display:flex}.modal-upload-general .box-header-title{font-size:15px;padding:0 14px;height:36px;line-height:36px;display:inline-block;color:#fff;border-radius:3px 3px 0 0;background:#337ab7}.modal-upload-general .task-list-box{display:flex;flex-direction:column;width:430px;min-width:430px;margin-right:10px;position:relative;overflow:hidden}.modal-upload-general .task-list-box .box-header{display:flex;align-items:flex-end}.modal-upload-general .task-list-box .box-header .box-header-btns{display:flex;justify-content:flex-end;flex:1 0 auto}.modal-upload-general .task-list-box .box-header .box-header-btns button{outline:none;border:none;color:#fff;width:42px;height:32px}.modal-upload-general .task-list-box .box-header .box-header-btns button:hover i{transform:scale(1.2)}.modal-upload-general .task-list-box .box-header .box-header-btns button i{transition:all .3s;font-size:18px}.modal-upload-general .task-list-box .box-header .box-header-btns button span{display:none}.modal-upload-general .task-list-box .box-header .box-header-btns .delete{background:#4fcaca}.modal-upload-general .task-list-box .box-header .box-header-btns .add{background:#ccc}.modal-upload-general .task-list-box .box-header .box-header-btns .edit{background:#feb872}.modal-upload-general .task-list-box .box-header .box-header-btns .upload{background:#3c0}.modal-upload-general .task-list-box .box-toolbar{height:47px;min-height:47px;background:#f9f9f9;border:1px solid #e9e9e9;border-bottom:none}.modal-upload-general .task-list-box .box-toolbar .box-toolbar-container{display:flex;align-items:center;padding-left:15px;padding-right:8px;height:42px;min-height:42px;box-shadow:0 2px 1px rgba(0,0,0,.1)}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter{flex:1 0 auto;text-align:right}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox{min-width:inherit}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox:after,.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox:before{display:none}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox.unchecked span{opacity:.6;transform:scale(.8)}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox.unchecked span:hover{opacity:.9;transform:scale(.8)}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox.checked span{transform:scale(1);opacity:1}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox .tooltip{min-width:40px}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox span{margin:0 4px 0 0;display:inline-block;min-width:22px;padding:0 4px;text-align:center;transition:all .3s;border-radius:2px;color:#fff}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox span:hover{opacity:1;transform:scale(1)}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox:first-child span{background-color:#9cf}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox:nth-child(2) span{background-color:#6c9}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox:nth-child(3) span{background-color:#fc6}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox:nth-child(4) span{background-color:#c9f}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox:nth-child(5) span{background-color:#c90}.modal-upload-general .task-list-box .box-toolbar .entity-type-filter .mam-checkbox svg{display:none}.modal-upload-general .task-list-box .list-list{flex:1 0 auto;border:1px solid #e9e9e9;border-top:none}.modal-upload-general .task-list-box .list-list .item{display:flex;height:50px;align-items:center;position:relative;border-bottom:1px solid rgba(0,0,0,.1)}.modal-upload-general .task-list-box .list-list .item:nth-child(2n){background:#f9f9f9}.modal-upload-general .task-list-box .list-list .item:hover .op-btns{opacity:1}.modal-upload-general .task-list-box .list-list .item.active .title a{font-weight:700;color:#22262e}.modal-upload-general .task-list-box .list-list .item.validate-error .title a{color:red}.modal-upload-general .task-list-box .list-list .item .mam-checkbox{margin-left:15px;margin-right:8px}.modal-upload-general .task-list-box .list-list .item .no{margin-right:4px}.modal-upload-general .task-list-box .list-list .item .title{flex:1 0 auto;width:1px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.modal-upload-general .task-list-box .list-list .item .title a{display:block;height:50px;line-height:50px;transition:none}.modal-upload-general .task-list-box .list-list .item .type{display:inline-block;background:#00a8ff;color:#fff;width:18px;height:18px;border-radius:2px;text-align:center;line-height:18px;margin-right:4px}.modal-upload-general .task-list-box .list-list .item .op-btns{position:absolute;right:-1px;top:1px;opacity:0;transition:all .3s}.modal-upload-general .metadata-box{display:flex;flex-direction:column;width:1px;flex:1 0 auto;position:relative}.modal-upload-general .metadata-box .box-content{border:1px solid #e9e9e9;border-bottom:none;display:flex;flex-direction:column;height:1px;flex:1 0 auto}.modal-upload-general .metadata-box .box-content .metadata-form-wrap{flex:1 0 auto;height:1px;display:flex}.modal-upload-general .metadata-box .box-content .mam-metadata-form{flex:1 0 auto;height:1px}.modal-upload-general .metadata-box .box-content .mam-metadata-form .mmf-group{display:flex;flex-wrap:wrap;margin-bottom:5px;padding:10px}.modal-upload-general .metadata-box .box-footer{padding:10px;text-align:center;border:1px solid #e9e9e9;background:#fff;z-index:10}.modal-upload-general .metadata-box .box-footer button{margin:0 5px}.modal-upload-general .mask-layer{background:rgba(0,0,0,.3);position:absolute;left:0;top:0;width:100%;height:100%}.modal-upload-group .modal-body{display:flex;flex-direction:row;width:850px;height:500px}.modal-upload-group .box-header-title{font-size:15px;padding:0 14px;height:36px;line-height:36px;display:inline-block;color:#fff;border-radius:3px 3px 0 0;background:#337ab7}.modal-upload-group .box-content{border:1px solid #e9e9e9;display:flex;flex-direction:column;height:434px}.modal-upload-group .metadata-box{display:flex;flex-direction:column;width:380px;position:relative}.modal-upload-group .metadata-box .box-content .mam-metadata-form{flex:1 0 auto;height:1px}.modal-upload-group .metadata-box .box-content .mam-metadata-form .mmf-group{display:flex;flex-wrap:wrap;margin-bottom:5px;padding:10px}.modal-upload-group .files-box{width:460px;margin-left:10px}.modal-upload-group .files-box .box-header{display:flex;align-items:flex-end}.modal-upload-group .files-box .box-header .box-header-btns{display:flex;justify-content:flex-end;flex:1 0 auto}.modal-upload-group .files-box .box-header .box-header-btns button{outline:none;border:none;color:#fff;display:flex;align-items:center;justify-content:center;width:38px;height:32px}.modal-upload-group .files-box .box-header .box-header-btns button:hover i{transform:scale(1.2)}.modal-upload-group .files-box .box-header .box-header-btns button i{transition:all .3s;font-size:18px}.modal-upload-group .files-box .box-header .box-header-btns button span{display:none}.modal-upload-group .files-box .box-header .box-header-btns .add{background:#feb872}.modal-upload-group .files-box .box-header .box-header-btns .add i{margin-top:3px;margin-left:2px}.modal-upload-group .files-box .file-group .group-title{font-size:16px;border-bottom:1px solid #e5e5e5;padding-bottom:10px;padding-left:10px;margin-bottom:8px}.modal-upload-group .files-box .file-group .item{display:flex;height:30px;line-height:30px;padding:0 10px}.modal-upload-group .files-box .file-group .item .no{min-width:25px}.modal-upload-group .files-box .file-group .item .name{flex:1 0 auto;width:1px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;cursor:move}.modal-upload-group .files-box .file-group .item .btns .delete{font-size:14px}.modal-upload-group .files-box .file-group .ui-sortable-helper{background:hsla(0,0%,100%,.7)}.modal-upload-group .files-box .file-group .no-data{font-size:16px;padding:15px 0;text-align:center}.modal-upload-group .files-box .video-group{margin-top:10px;margin-bottom:10px}.modal-upload-group .files-box .video-group .name{cursor:default!important}.modal-upload-picturePackage .modal-body{display:flex;flex-direction:row;width:850px;height:500px}.modal-upload-picturePackage .box-header-title{font-size:15px;padding:0 14px;height:36px;line-height:36px;display:inline-block;color:#fff;border-radius:3px 3px 0 0;background:#337ab7}.modal-upload-picturePackage .box-content{border:1px solid #e9e9e9;display:flex;flex-direction:column;height:434px}.modal-upload-picturePackage .metadata-box{display:flex;flex-direction:column;width:380px;position:relative}.modal-upload-picturePackage .metadata-box .box-content .mam-metadata-form{flex:1 0 auto;height:1px}.modal-upload-picturePackage .metadata-box .box-content .mam-metadata-form .mmf-group{display:flex;flex-wrap:wrap;margin-bottom:5px;padding:10px}.modal-upload-picturePackage .files-box{width:460px;margin-left:10px}.modal-upload-picturePackage .files-box .box-header{display:flex;align-items:flex-end}.modal-upload-picturePackage .files-box .box-header .box-header-btns{display:flex;justify-content:flex-end;flex:1 0 auto}.modal-upload-picturePackage .files-box .box-header .box-header-btns button{outline:none;border:none;color:#fff;display:flex;align-items:center;justify-content:center;width:38px;height:32px}.modal-upload-picturePackage .files-box .box-header .box-header-btns button:hover i{transform:scale(1.2)}.modal-upload-picturePackage .files-box .box-header .box-header-btns button i{transition:all .3s;font-size:18px}.modal-upload-picturePackage .files-box .box-header .box-header-btns button span{display:none}.modal-upload-picturePackage .files-box .box-header .box-header-btns .add{background:#feb872}.modal-upload-picturePackage .files-box .items-box{display:flex;flex-wrap:wrap}.modal-upload-picturePackage .files-box .item{position:relative;cursor:move;width:130px;min-height:73px;margin:10px 8px 6px;background-color:#eee}.modal-upload-picturePackage .files-box .item .keyframe{width:130px;height:73px;display:flex;justify-content:center;align-items:center}.modal-upload-picturePackage .files-box .item .keyframe img{max-width:130px;max-height:73px}.modal-upload-picturePackage .files-box .item .name{height:30px;line-height:30px;width:130px;padding-left:6px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;background-color:#fff}.modal-upload-picturePackage .files-box .item .btns{cursor:pointer;position:absolute;right:-7px;top:-12px;font-size:16px;color:#5f5f5f}.modal-upload-picturePackage .files-box .item .btns i:hover{color:#000}.modal-upload-picturePackage .files-box .item-add{margin:10px 8px 6px}.modal-upload-picturePackage .files-box .item-add .btn-add{width:130px;height:73px;display:flex;background:#eee;justify-content:center;align-items:center;font-size:38px;font-weight:400;display:block;color:#717171;border:none}.modal-upload-picturePackage .files-box .item-add .btn-add:hover{color:#000}.mam-upload-task-panel{position:fixed;right:-768px;bottom:88px;width:768px;height:373px;background:#fff;box-shadow:0 0 14px rgba(0,0,0,.2);z-index:99;transition:right .3s}.mam-upload-task-panel .panel-control{width:48px;height:48px;background-color:#e98b11;color:#fff;position:absolute;left:-48px;top:0;box-shadow:0 0 10px rgba(0,0,0,.1);z-index:0;cursor:pointer;display:flex;align-items:center;justify-content:center}.mam-upload-task-panel .panel-control .fa{font-size:18px}.mam-upload-task-panel .panel-control.close{transition:opacity .3s;opacity:.4}.mam-upload-task-panel .panel-control.close:hover{opacity:1}.mam-upload-task-panel .box{padding:18px;height:inherit;display:flex;flex-direction:column}.mam-upload-task-panel .header{border-bottom:1px solid #e4e4e4;margin-bottom:18px}.mam-upload-task-panel .box-header-title{font-size:16px;color:#272727;margin-bottom:15px}.mam-upload-task-panel .task-list{flex:1;overflow:auto}.mam-upload-task-panel .task-list .item-th{display:flex;margin-bottom:15px;color:#535353}.mam-upload-task-panel .task-list .item-tr{color:#494949}.mam-upload-task-panel .task-list .item-tr .task{display:flex;height:48px;line-height:48px;border-top:1px solid #eee;position:relative}.mam-upload-task-panel .task-list .item-tr .files{border-top:1px solid #eee}.mam-upload-task-panel .task-list .item-tr .files .file{display:flex;position:relative;height:48px;line-height:48px;margin-left:24px;border-top:1px solid #eee;border-left:1px solid #eee}.mam-upload-task-panel .task-list .item-tr .files .file:first-child{border-top:none}.mam-upload-task-panel .task-list .item-tr .upload-progress-bar{position:absolute;z-index:-1;width:100%;left:0;top:0;height:inherit}.mam-upload-task-panel .task-list .item-tr .upload-progress-bar span{display:block;height:inherit;background-color:#d9f3ff;border-top:2px solid transparent;border-bottom:2px solid transparent;border-right:2px solid #bae9ff;transition:width .6s}.mam-upload-task-panel .task-list .item-tr .surplus-time{position:absolute;left:43px;top:28px;color:#949494;height:20px;line-height:20px;font-size:12px;text-shadow:0 1px 1px #fff}.mam-upload-task-panel .task-list .status{width:44px;text-align:center}.mam-upload-task-panel .task-list .status i{display:inline-block}.mam-upload-task-panel .task-list .status i.icon-gou1{color:green}.mam-upload-task-panel .task-list .name{flex:1;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.mam-upload-task-panel .task-list .name-link{cursor:pointer}.mam-upload-task-panel .task-list .name-link:hover{color:#e98b11}.mam-upload-task-panel .task-list .size{width:88px}.mam-upload-task-panel .task-list .folder{width:86px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.mam-upload-task-panel .task-list .upload-progress{width:48px;text-align:center}.mam-upload-task-panel .task-list .control{width:65px;text-align:center}.mam-upload-task-panel .task-list .control .fa{transform:translateY(-2px);color:#999}.mam-upload-task-panel .task-list .control .fa:hover{color:#000}.mam-upload-task-panel .no-data{flex:1;display:flex;justify-content:center;align-items:center;padding-bottom:34px;color:#3d3d3d;font-size:20px}.mam-upload-button-group{height:36px}.mam-upload-button-group .btn-group,.mam-upload-button-group .btn-group .btn{height:100%}.mam-upload-button-group .upload-btn-group{position:relative;display:inline-block;height:100%}.mam-upload-button-group .upload-btn-group>button{background:#e98b11;color:#fff;height:100%}.mam-upload-button-group .upload-btn-group>ul{display:none;background-color:#fff;padding:6px 0;position:absolute;top:36px;border:1px solid rgba(0,0,0,.15);border-radius:4px;min-width:160px;z-index:1000;list-style:none;right:0}.mam-upload-button-group .upload-btn-group>ul.left{left:0;right:auto}.mam-upload-button-group .upload-btn-group>ul>li:hover{background:#f5f5f5;color:#262626}.mam-upload-button-group .upload-btn-group>ul>li:hover>a{color:#262626}.mam-upload-button-group .upload-btn-group>ul>li>a{display:flex;align-items:center;padding:3px 20px}.mam-upload-button-group .upload-btn-group>ul>li>a>i{font-size:14px}.mam-upload-button-group .upload-btn-group>ul>li>a:hover{text-decoration:none}.mam-upload-button-group .upload-btn-group:hover>ul{display:block}",""])},function(e,t){function a(e,t){var a=e[1]||"",n=e[3];if(!n)return a;if(t&&"function"==typeof btoa){var r=o(n);return[a].concat(n.sources.map(function(e){return"/*# sourceURL="+n.sourceRoot+e+" */"})).concat([r]).join("\n")}return[a].join("\n")}function o(e){return"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(e))))+" */"}e.exports=function(e){var t=[];return t.toString=function(){return this.map(function(t){var o=a(t,e);return t[2]?"@media "+t[2]+"{"+o+"}":o}).join("")},t.i=function(e,a){"string"==typeof e&&(e=[[null,e,""]]);for(var o={},n=0;n<this.length;n++){var r=this[n][0];"number"==typeof r&&(o[r]=!0)}for(n=0;n<e.length;n++){var i=e[n];"number"==typeof i[0]&&o[i[0]]||(a&&!i[2]?i[2]=a:a&&(i[2]="("+i[2]+") and ("+a+")"),t.push(i))}},t}},function(e,t,a){function o(e,t){for(var a=0;a<e.length;a++){var o=e[a],n=m[o.id];if(n){n.refs++;for(var r=0;r<n.parts.length;r++)n.parts[r](o.parts[r]);for(;r<o.parts.length;r++)n.parts.push(p(o.parts[r],t))}else{for(var i=[],r=0;r<o.parts.length;r++)i.push(p(o.parts[r],t));m[o.id]={id:o.id,refs:1,parts:i}}}}function n(e,t){for(var a=[],o={},n=0;n<e.length;n++){var r=e[n],i=t.base?r[0]+t.base:r[0],s=r[1],l=r[2],u=r[3],p={css:s,media:l,sourceMap:u};o[i]?o[i].parts.push(p):a.push(o[i]={id:i,parts:[p]})}return a}function r(e,t){var a=g(e.insertInto);if(!a)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var o=x[x.length-1];if("top"===e.insertAt)o?o.nextSibling?a.insertBefore(t,o.nextSibling):a.appendChild(t):a.insertBefore(t,a.firstChild),x.push(t);else{if("bottom"!==e.insertAt)throw new Error("Invalid value for parameter 'insertAt'. Must be 'top' or 'bottom'.");a.appendChild(t)}}function i(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e);var t=x.indexOf(e);t>=0&&x.splice(t,1)}function s(e){var t=document.createElement("style");return e.attrs.type="text/css",u(t,e.attrs),r(e,t),t}function l(e){var t=document.createElement("link");return e.attrs.type="text/css",e.attrs.rel="stylesheet",u(t,e.attrs),r(e,t),t}function u(e,t){Object.keys(t).forEach(function(a){e.setAttribute(a,t[a])})}function p(e,t){var a,o,n,r;if(t.transform&&e.css){if(!(r=t.transform(e.css)))return function(){};e.css=r}if(t.singleton){var u=y++;a=b||(b=s(t)),o=f.bind(null,a,u,!1),n=f.bind(null,a,u,!0)}else e.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(a=l(t),o=c.bind(null,a,t),n=function(){i(a),a.href&&URL.revokeObjectURL(a.href)}):(a=s(t),o=d.bind(null,a),n=function(){i(a)});return o(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;o(e=t)}else n()}}function f(e,t,a,o){var n=a?"":o.css;if(e.styleSheet)e.styleSheet.cssText=v(t,n);else{var r=document.createTextNode(n),i=e.childNodes;i[t]&&e.removeChild(i[t]),i.length?e.insertBefore(r,i[t]):e.appendChild(r)}}function d(e,t){var a=t.css,o=t.media;if(o&&e.setAttribute("media",o),e.styleSheet)e.styleSheet.cssText=a;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(a))}}function c(e,t,a){var o=a.css,n=a.sourceMap,r=void 0===t.convertToAbsoluteUrls&&n;(t.convertToAbsoluteUrls||r)&&(o=k(o)),n&&(o+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(n))))+" */");var i=new Blob([o],{type:"text/css"}),s=e.href;e.href=URL.createObjectURL(i),s&&URL.revokeObjectURL(s)}var m={},h=function(e){var t;return function(){return void 0===t&&(t=e.apply(this,arguments)),t}}(function(){return window&&document&&document.all&&!window.atob}),g=function(e){var t={};return function(a){return void 0===t[a]&&(t[a]=e.call(this,a)),t[a]}}(function(e){return document.querySelector(e)}),b=null,y=0,x=[],k=a(17);e.exports=function(e,t){if("undefined"!=typeof DEBUG&&DEBUG&&"object"!=typeof document)throw new Error("The style-loader cannot be used in a non-browser environment");t=t||{},t.attrs="object"==typeof t.attrs?t.attrs:{},t.singleton||(t.singleton=h()),t.insertInto||(t.insertInto="head"),t.insertAt||(t.insertAt="bottom");var a=n(e,t);return o(a,t),function(e){for(var r=[],i=0;i<a.length;i++){var s=a[i],l=m[s.id];l.refs--,r.push(l)}if(e){o(n(e,t),t)}for(var i=0;i<r.length;i++){var l=r[i];if(0===l.refs){for(var u=0;u<l.parts.length;u++)l.parts[u]();delete m[l.id]}}}};var v=function(){var e=[];return function(t,a){return e[t]=a,e.filter(Boolean).join("\n")}}()},function(e,t){e.exports=function(e){var t="undefined"!=typeof window&&window.location;if(!t)throw new Error("fixUrls requires window.location");if(!e||"string"!=typeof e)return e;var a=t.protocol+"//"+t.host,o=a+t.pathname.replace(/\/[^\/]*$/,"/");return e.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,function(e,t){var n=t.trim().replace(/^"(.*)"$/,function(e,t){return t}).replace(/^'(.*)'$/,function(e,t){return t});if(/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/)/i.test(n))return e;var r;return r=0===n.indexOf("//")?n:0===n.indexOf("/")?a+n:o+n.replace(/^\.\//,""),"url("+JSON.stringify(r)+")"})}},function(e,t){angular.module("mam-upload").directive("uploadStatusIcon",function(){return{restrict:"E",template:'<i class="status-icon fa"></i>',scope:{status:"="},link:function(e,t,a){function o(e){switch(e){case"added":return"";case"prepared":return"fa-clock-o";case"success":return"fa-check-circle";case"error":return"fa-times-circle";case"deleteing":return"fa-recycle";default:return"fa-circle-o-notch fa-spin"}}var n=t.find("i");e.$watch("status",function(){n.removeClass("fa-check-circle fa-times-circle fa-recycle fa-circle-o-notch fa-spin"),n.addClass(o(e.status))}),n.addClass(o(e.status))}}})},function(e,t,a){var o=a(1).default;angular.module("mam-upload").directive("uploadButtonGroup",["$http","$uibModal","$q","$timeout",function(e,t,n,r){return{restrict:"E",template:a(20),replace:!0,scope:{targetFolder:"<",module:"=",picturePackageEnable:"<",fileGroupEnable:"<",writeMetadataBefore:"&?",callBack:"&?",relationType:"@",relationContentId:"=",needCopyright:"@",extraField:"&?"},link:function(e,t,a){$(t);e.l=window.l,e.transferType,e.webUploadEnable=nxt.config.webUploadEnable,e.vtubeEnable=nxt.config.vtubeEnable;var n=[{type:1,title:l("upload.fileUpload","文件上传"),icon:"fa fa-file-o"},{type:3,title:l("upload.picturePackageUpload","图片包上传"),icon:"fa fa-picture-o"},{type:2,title:l("upload.groupUpload","视音频分离上传"),icon:"fa fa-file-o"}];e.btns=n,e.transferType=localStorage.getItem("mam-upload-transfer-type"),null==e.transferType?e.transferType=1:e.transferType=parseInt(e.transferType),2==e.transferType?e.vtubeEnable||(e.transferType=1):e.transferType=e.webUploadEnable?1:2,e.changeTransfer=function(t){e.transferType=t,localStorage.setItem("mam-upload-transfer-type",t)},e.upload=function(t){var a=e.targetFolder;null!=a&&""!==a||(a="doha");var n={};n.taskType=t,n.transferType=e.transferType,n.targetFolder=a,n.module=e.module,e.writeMetadataBefore&&(n.writeMetadataBefore=function(t,a){return e.writeMetadataBefore({files:t,params:a})}),e.relationType==o.mamUpload.uploader.relationContentType.dataset&&(n.relationContentType=o.mamUpload.uploader.relationContentType.dataset,n.relationContentId=e.relationContentId,n.needCopyright=e.needCopyright,_.isFunction(e.extraField)&&(n.extraField=e.extraField())),o.mamUpload.uploader.createTask(n,e.callBack)}}}}])},function(e,t){e.exports='<div class=mam-upload-button-group ng-show="webUploadEnable || vtubeEnable"> <div class="btn-group transfer-type" mam-dropdown ng-if=webUploadEnable&&vtubeEnable> <button type=button class="btn btn-default dropdown-toggle"> <i ng-if="transferType==1" class="fa fa-internet-explorer"></i> <i ng-if="transferType==2" class="fa fa-windows"></i> </button> <ul class=dropdown-menu> <li><a ng-click=changeTransfer(1)><i class="fa fa-internet-explorer"></i>{{l(\'upload.webUpload\',\'Web上传\')}}</a></li> <li><a ng-click=changeTransfer(2)><i class="fa fa-windows"></i>{{l(\'upload.clientUpload\',\'客户端上传\')}}</a></li> </ul> </div> <div class="btn-group task-type" mam-dropdown> <button type=button class="btn btn-default dropdown-toggle"> <i class="fa fa-upload"></i> {{l(\'upload.upload\',\'上传\')}} </button> <ul class=dropdown-menu> <li> <a ng-click=upload(btn.type) ng-repeat="btn in btns" ng-if="btn.type == 1 || (btn.type == 2 && fileGroupEnable) || (btn.type == 3 && picturePackageEnable)"> <i class={{btn.icon}}></i>{{btn.title}} </a> </li> </ul> </div> </div>'},function(e,t,a){var o=a(1).default;angular.module("mam-upload").directive("newUploadButtonGroup",["$http","$uibModal","$q","$timeout",function(e,t,n,r){return{restrict:"E",template:a(22),replace:!0,scope:{targetFolder:"<",module:"=",picturePackageEnable:"<",fileGroupEnable:"<",writeMetadataBefore:"&?",callBack:"&?",relationType:"@",relationContentId:"=",needCopyright:"@",extraField:"&?"},link:function(e,t,a){$(t);e.l=window.l,e.transferType,e.webUploadEnable=nxt.config.webUploadEnable,e.vtubeEnable=nxt.config.vtubeEnable;var n=[{type:1,title:l("upload.fileUpload","文件上传"),icon:"fa fa-file-o"},{type:3,title:l("upload.picturePackageUpload","图片包上传"),icon:"fa fa-picture-o"},{type:2,title:l("upload.groupUpload","视音频分离上传"),icon:"fa fa-file-o"}];e.btns=n,e.transferType=localStorage.getItem("mam-upload-transfer-type"),null==e.transferType?e.transferType=1:e.transferType=parseInt(e.transferType),2==e.transferType?e.vtubeEnable||(e.transferType=1):e.transferType=e.webUploadEnable?1:2,e.changeTransfer=function(t){e.transferType=t,localStorage.setItem("mam-upload-transfer-type",t)},e.singleTransfer=function(t){e.picturePackageEnable||e.fileGroupEnable||e.upload(t,1)},e.upload=function(t,a){var n=e.targetFolder;null!=n&&""!==n||(n="doha");var r={};r.taskType=a,r.transferType=t,r.targetFolder=n,r.module=e.module,e.writeMetadataBefore&&(r.writeMetadataBefore=function(t,a){return e.writeMetadataBefore({files:t,params:a})}),e.relationType==o.mamUpload.uploader.relationContentType.dataset&&(r.relationContentType=o.mamUpload.uploader.relationContentType.dataset,r.relationContentId=e.relationContentId,r.needCopyright=e.needCopyright,_.isFunction(e.extraField)&&(r.extraField=e.extraField())),o.mamUpload.uploader.createTask(r,e.callBack)}}}}])},function(e,t){e.exports='<div class=mam-upload-button-group ng-show="webUploadEnable || vtubeEnable"> <div class=upload-btn-group ng-if=webUploadEnable> <button type=button class="btn btn-default" ng-click=singleTransfer(1) ng-mouseover=changeTransfer(1)> {{l(\'upload.webUpload\',\'Web上传\')}} </button> <ul ng-if="fileGroupEnable || picturePackageEnable" ng-class="{\'left\': vtubeEnable && webUploadEnable}"> <li ng-repeat="btn in btns" ng-if="btn.type == 1 || (btn.type == 2 && fileGroupEnable) || (btn.type == 3 && picturePackageEnable)"> <a ng-click=upload(1,btn.type)> <i class={{btn.icon}}></i>{{btn.title}} </a> </li> </ul> </div> <div class=upload-btn-group ng-if=vtubeEnable> <button type=button class="btn btn-default" ng-click=singleTransfer(2) ng-mouseover=changeTransfer(2)> {{l(\'upload.clientUpload\',\'客户端上传\')}} </button> <ul ng-if="fileGroupEnable || picturePackageEnable"> <li ng-repeat="btn in btns" ng-if="btn.type == 1 || (btn.type == 2 && fileGroupEnable) || (btn.type == 3 && picturePackageEnable)"> <a ng-click=upload(2,btn.type)> <i class={{btn.icon}}></i>{{btn.title}} </a> </li> </ul> </div> </div>'},function(e,t,a){var o=a(1).default;angular.module("mam-upload").directive("uploadTaskPanel",["$http","$q","$timeout",function(e,t,n){return{restrict:"E",template:a(24),transclude:!0,scope:{succeed:"&",afterGetUnfinished:"&",contentId:"=",relationContentType:"=",targetType:"="},link:function(e,t,a){e.l=window.l,e.showTaskPanel=!1,e.tasks=[],e.loaded=!1,e.taskTimeTiker=[],e.webUploadEnable=nxt.config.webUploadEnable,o.mamUpload.uploader.web.on("task-init-before",function(t,a){e.tasks.push(a),e.showTaskPanel=!0}),o.mamUpload.uploader.web.on("task-init-success",function(e,t){}),o.mamUpload.uploader.web.on("task-upload-progress",function(t,a){e.$applyAsync()}),o.mamUpload.uploader.web.on("task-delete-success",function(t,a){_.remove(e.tasks,function(e){return e.taskId==a.taskId}),e.$applyAsync()}),o.mamUpload.uploader.web.on("task-upload-success",function(t,a){e.$applyAsync()}),e.continueFile=o.mamUpload.uploader.web.continueUpload,e.canDelete=o.mamUpload.uploader.web.canDeleteTask,e.delete=function(e){o.mamUpload.uploader.web.deleteTask(e)},o.mamUpload.uploader.web.getUnfinishedTask(e.contentId,e.relationContentType,e.targetType).then(function(t){e.tasks=_.concat(e.tasks,t),_.isFunction(e.afterGetUnfinished)&&e.afterGetUnfinished(),e.loaded=!0})}}}])},function(e,t){e.exports="<div class=mam-upload-task-panel ng-style=\"{right: showTaskPanel ? '0' : '-768px'}\"> <div class=panel-control ng-click=\"showTaskPanel=!showTaskPanel\" ng-class=\"showTaskPanel?'open':'close'\" title=\"{{showTaskPanel?l('upload.hideTask','隐藏上传任务'):l('upload.showTask','展开上传任务')}}\"> <i class=\"fa fa-bars\"></i> </div> <div class=box> <div class=header> <div class=box-header-title>{{l('upload.task','上传任务')}}</div> </div> <div ng-if=\"loaded && tasks.length>0\" class=task-list> <div class=item-th> <div class=status>{{l('upload.status','状态')}}</div> <div class=name>{{l('upload.name','名称')}}</div> <div class=size>{{l('upload.size','大小')}}</div> <div class=folder>{{l('upload.location','位置')}}</div> <div class=upload-progress>{{l('upload.progress','进度')}}</div> <div class=control>{{l('upload.control','操作')}}</div> </div> <div class=item-tr ng-repeat=\"task in tasks\" ng-init=\"task.showFiles=task.files.length>1 && task.status=='error'\"> <div class=task> <div class=status title=\"{{task.status | uploadStatus}}\"> <upload-status-icon status=task.status></upload-status-icon> </div> <div class=name ng-if=\"task.files.length<2\" ng-bind=task.metadata.name></div> <div class=\"name name-link\" ng-if=\"task.files.length>1\" ng-bind=task.metadata.name ng-click=\"task.showFiles=!task.showFiles\"></div> <div class=size ng-bind=task.sizeTotalString></div> <div class=folder title={{task.targetFolderName}}> <a ng-if=\"task.targetType==0\" ui-sref=\"main.cc.cloud({isPublic:true,folder: task.targetFolder, page:1, keyword:'',searchType:'',condition:[]})\" ng-bind=task.targetFolderName></a> <a ng-if=\"task.targetType==1\" ui-sref=\"main.cc.cloud({isPublic:false,folder: task.targetFolder, page:1, keyword:'',searchType:'',condition:[]})\" ng-bind=task.targetFolderName></a> <a ng-if=\"task.targetType==5\" mam-href=~/search/#/search/ ng-bind=task.targetFolderName></a> <a ng-if=\"task.targetType==6\" ui-sref=\"main.cc.workspace({folder: task.targetFolder, page:1, keyword:''})\" ng-bind=task.targetFolderName></a> </div> <div class=surplus-time ng-if=\"task.surplusTime!=null\">{{l('upload.surplusTime','大约剩余：')}}<span>{{task.surplusTime | msToSurplusTime}}</span></div> <div class=upload-progress>{{task.progress==100?l('upload.finish','完成'):(task.progress+'%')}}</div> <div class=control> <button class=\"uplaod-options btn-icon\" title=\"{{l('upload.continueUpload','继续上传')}}\" ng-click=continueFile(task,task.files[0]) ng-if=\"task.files.length==1 && (task.status == 'error')\"> <i class=\"icon iconfont icon-right1\"></i> </button> <button class=\"uplaod-options btn-icon\" title=\"{{l('upload.delete','删除')}}\" ng-if=canDelete(task) ng-click=delete(task)><i class=\"icon iconfont icon-icon13\"></i></button> </div> <div class=upload-progress-bar> <span ng-style=\"{width: task.progress + '%'}\"></span> </div> </div> <div class=files ng-if=\"task.files.length>1 && task.showFiles\"> <div class=file ng-repeat=\"file in task.files\"> <div class=status title=\"{{file.status | uploadStatus}}\"> <upload-status-icon status=file.status></upload-status-icon> </div> <div class=name ng-bind=file.fileName></div> <div class=size ng-bind=file.fileSizeString></div> <div class=folder></div> <div class=surplus-time ng-if=\"file.surplusTime!=null\">{{l('upload.surplusTime','大约剩余：')}}<span>{{file.surplusTime | msToSurplusTime}}</span></div> <div class=upload-progress>{{file.progress==100?l('upload.finish','完成'):(file.progress+'%')}}</div> <div class=control> <button class=\"uplaod-options btn-icon\" title=\"{{l('upload.continueUpload','继续上传')}}\" ng-click=continueFile(task,file) ng-if=\"file.status == 'error'\"> <i class=\"icon iconfont icon-right1\"></i> </button> </div> <div class=upload-progress-bar> <span ng-style=\"{width: file.progress + '%'}\"></span> </div> </div> </div> </div> </div> <div ng-if=\"tasks.length==0\" class=no-data>{{l('upload.noTask','暂无任务')}}</div> </div> </div>"},function(e,t){angular.module("mam-upload").filter("uploadStatus",function(){return function(e){return"added"==e?l("upload.statusAdded","等待上传"):"init"==e?l("upload.statusInit","正在初始化"):"prepared"==e?l("upload.statusPrepared","排队中"):"progress"==e?l("upload.statusProgress","上传中"):"error"==e?l("upload.statusError","错误"):"success"==e?l("upload.statusSuccess","完成"):"deleteing"==e?l("upload.statusDeleteing","删除中"):e}})},function(e,t){angular.module("mam-upload").filter("remainTime",function(){return function(e){if(!_.isNumber(e))return"";var t,a,o=0;if((t=Math.round(e/1e3))<60)return t+l("upload.second","秒");a=parseInt(t/60),t%=60,a>=60&&(o=parseInt(a/60),a%=60);var n="";return 0!==o&&(n+=o+l("upload.hour","小时")),0!==a&&(n+=a+l("upload.minute","分")),0!==t&&(n+=t+l("upload.second","秒")),n}})},function(e,t){angular.module("mam-upload").filter("msToSurplusTime",function(){return function(e){if(!_.isNumber(e))return"";var t,a,o=0;if((t=Math.round(e/1e3))<60)return t+"秒";a=parseInt(t/60),t%=60,a>=60&&(o=parseInt(a/60),a%=60);var n="";return 0!==o&&(n+=o+"小时"),0!==a&&(n+=a+"分"),0!==t&&(n+=t+"秒"),n}})}]);
//# sourceMappingURL=mam-upload.min.js.map