.share-container{
    display: flex;
    flex-direction: column;
    height: 100%;
  .content{
    width: 70%;
    flex: 1;
    max-height: calc(100% - 52px);
    margin: 0 auto;
    width: 70%;
    margin: 0 auto;
    .shareinfos{
      margin-top: 39px;
      margin-bottom: 42px;
      text-align: center;
      background: linear-gradient(135deg, #F9FBFF 0%, #F5FBFF 100%);
      border-radius: 8px;
      padding: 30px 0;
      .sharetime{
        margin: 0 70px;
      }
     .name{
      margin-bottom: 10px;
      // height: 25px;
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC;
      line-height: 25px;
      font-weight: 500;
      color: #1E1E1E;
      
     } 
     .text {
      height: 20px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #525252;
      line-height: 20px;
      img{
        margin-right: 3px;
      }
     }
     .username{
      margin-bottom: 10px;
     }
     .time{
      margin-bottom: 10px;
      >.expirationtime{
        margin-left: 15px;
      }
     }
     .opt_btn{
      margin-bottom: 24px;
      .ant-btn{
        margin-right: 20px;
      }
     }
    }
    .opt_btn{
      display: flex;
      justify-content: space-between;
      margin-bottom: 30px;
      .icon{
        font-size: 15px;
        margin-left: 4px;
        color: var(--primary-color);
      }
      .top_left{
        .ant-checkbox-wrapper{
          margin-left: 14px;
        }
        >.ant-btn{
          margin-left: 14px;
        }
      }
      .right_opt{
        display: flex;
        align-items: center;
        flex-direction: row;
        .mode_switch {
          margin-left: 15px;
          cursor: pointer;
          .active{
            color: var(--primary-color);
          }
        }
      }
    }
    .body{
      height: calc(100% - 248px);
      .box_bottom{
        height: calc(100% - 46px);
        .ant-breadcrumb {
          padding: 5px 0 5px 15px;
          a{
            color:var(--primary-color);
          }
          ol > li:last-child a{
            color:rgba(0, 0, 0, 0.85);
            cursor: default;
          }
          span:last-child a{
            color:rgba(0, 0, 0, 0.85);
            cursor: default;
          }

          .ant-breadcrumb-separator{
            margin:0 4px !important;
          }
        }
        .contentitem_box{
          height: calc(100% - 32px);
          .ant-checkbox-group{
            height: 100%;
          }
          .height3_shared_list {
            display: inline-block;
            overflow-y: auto;
          }
          .height3_shared {
            overflow-y: auto;
            display: flex;
            flex-wrap: wrap;
            align-content: flex-start;
            .content_item:nth-child(6n) {
              margin-right: 10px;
            }
            @media screen and (max-width: 3220px) {
            
              .content_item,
              .content_item:nth-child(6n) {
                margin-right: calc((100% - 204px * 6 - 10px) / 5);
              }
            
              .content_item:nth-child(5n) {
                margin-right: 10px;
              }
            }
            
            @media screen and (max-width: 1920px) {
            
              .content_item,
              .content_item:nth-child(6n) {
                margin-right: calc((100% - 204px * 6 - 10px) / 5);
              }
            
              .content_item:nth-child(5n) {
                margin-right: 10px;
              }
            }
            
            @media screen and (max-width: 1680px) {
            
              .content_item,
              .content_item:nth-child(6n),
              .content_item:nth-child(5n) {
                margin-right: calc((100% - 204px * 5 - 10px) / 4);
              }
            
              .content_item:nth-child(4n) {
                margin-right: 10px;
              }
            }
            
            @media screen and (max-width: 1366px) {
            
              .content_item,
              .content_item:nth-child(6n),
              .content_item:nth-child(5n),
              .content_item:nth-child(4n) {
                margin-right: calc((100% - 204px * 4 - 10px) / 3);
              }
            
              .content_item:nth-child(3n) {
                margin-right: 10px;
              }
            }
          }
        }
        .title_Header {
          .ant-checkbox-group {
            height: calc(100% - 46px);
          }
        }
      }
      .pagination{
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #fff;
        padding: 8px 0;
        box-sizing: content-box;
      }
    }
  }
}
.modal_share{
  top: 20%;
  .ant-input{
    width: 80%;
    margin: 0 auto;
    display: block;
  }
}


@media (max-width: 768px) {
  #root {
    height: 100%;
  }
  .share-container{
    .data-use-vue-component-wrap {
      height: 52px;
    }
    .content{
      width: 100%; 
      height: calc(100% - 52px);
      max-height: 100%;
      .shareinfos{
        padding: 20px 0 20px;
        margin: 0;
        margin-bottom: 20px;
        .timeList {
          display: flex;
          justify-content: center;
          .left {
            text-align: right;
            div {
              display: flex;
              align-items: center;
              justify-content: flex-end;
            }
            img {
              margin-right: 6px;
            }
          }
         .right {
            text-align: left;
          }
        }
      }

      .opt_btn {
        padding: 0 10px;
      }

      .body {
        height: calc(100% - 248px);
        padding: 0 10px;
        .box_bottom {
          .ant-breadcrumb {
            padding: 0;
            padding-left: 15px;
            margin-bottom: 10px;
          }
  
          .contentitem_box {
            .pc_show {
              display: block!important;
            }

            .content_item {
              margin-right: 0 !important;
              .content_title {
                padding-right: 15px;
              }
              .content_word {
                span {
                  width: calc(100% - 5px);
                  .title_ {
                    width: 100%;
                    display: inline-block;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  .share-containeraxxt {
    .content {
      height: 100%; 
    }
  }
  .share-containerMobile {
    .content {
      .body .box_bottom {
        display: flex;
        flex-direction: column;
       .contentitem_box {
          flex: 1;
          height: unset;
          overflow: hidden;
       }
      }
    }
  }
}