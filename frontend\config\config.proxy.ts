// const baseUrl = 'https://ecourse.scu.edu.cn';
// const baseUrl = 'http://172.16.151.184/';
// const baseUrl = 'https://172.16.151.202';
const baseUrl = 'https://zhiliao.sobeylingyun.com/';
// const baseUrl = 'http://172.16.151.73/';
// const baseUrl = 'http://172.16.151.229';
// const baseUrl = 'https://axxt.nwpu.edu.cn/';
const proxy = {
  // 代理配置
  '/api': {
    target: `${baseUrl}/rman/v1`,
    // target: baseUrl.indexOf('/learn')===-1?`${baseUrl}/rman/v1`:baseUrl,
    // target: 'http://************:10080/rman/v1',
    // target: 'http://119.3.164.38:30856/rman/v1',
    // 'target': 'http://rap2.taobao.org:38080/app/mock/168113',//数据mock
    changeOrigin: true,
    pathRewrite: { '^/api': '' },
    secure: false,
  },
  '/exam-api': {
    target: baseUrl,
    // target: 'http://************:10080',
    changeOrigin: true,
    secure: false,
  },
  '/sensitiveword': {
    target: baseUrl,
    // target: 'http://************:10080',
    changeOrigin: true,
    secure: false,
  },
  '/exam': {
    target: baseUrl,
    // target: 'http://************:10080',
    changeOrigin: true,
    secure: false,
  },
  '/unifiedplatform': {
    // target: 'http://************:10080',
    target: baseUrl,
    changeOrigin: true,
    secure: false,
  },
  '/learn': {
    // target: 'http://************:10080',
    target: baseUrl,
    changeOrigin: true,
    secure: false,
  },
  '/pdfview': {
    target: baseUrl,
    changeOrigin: true,
    secure: false,
  },
  '/ff': {
    target: baseUrl,
    changeOrigin: true,
    pathRewrite: { '^/ff': '' },
    secure: false,
  },
  '/osapi': {
    target: baseUrl,
    // target: 'http://************:10080',
    changeOrigin: true,
    pathRewrite: { '^/osapi': '' },
    secure: false,
  },
  '/bucket-k': {
    target: baseUrl,
    // target: 'http://************:10080',
    changeOrigin: true,
    secure: false,
  },
  '/bucket-p': {
    target: baseUrl,
    // target: 'http://************:10080',
    changeOrigin: true,
    secure: false,
  },
  '/bucket-z': {
    target: baseUrl,
    // target: 'http://************:10080',
    changeOrigin: true,
    secure: false,
  },
  '/unifiedlogin': {
    target: baseUrl,
    changeOrigin: true,
    secure: false,
  },
  '/textclip': {
    target: baseUrl,
    changeOrigin: true,
    secure: false,
  },
  '/ipingestman': {
    target: baseUrl,
    changeOrigin: true,
    secure: false,
  },
  '/terminator': {
    target: baseUrl,
    changeOrigin: true,
    secure: false,
  },
  '/funasr-online/': {
    target: baseUrl,
    ws:true,
    changeOrigin: true,
    secure: false,
  },
  '/flowbpm': {
    target: baseUrl,
    changeOrigin: true,
    secure: false,
  }
};
export default proxy;
