import {
  Modal,
  Button,
  Menu,
  Input,
  Select,
  DatePicker,
  Checkbox,
  Pagination,
  Spin,
  Space,
} from 'antd';
import React from 'react';
import {
  FolderOpenOutlined,
  FolderOutlined,
  LoadingOutlined,
} from '@ant-design/icons';
import SmartService from '@/service/smartService';

const { SubMenu } = Menu;
const { Option } = Select;
const { RangePicker } = DatePicker;

const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;

const courses = [
  {
    title: '口腔健康与文化：世界，中国，华西',
    img: '/rman/static/images/courseface/1.png',
  },
  {
    title: '批判性思维：洞悉人类思想的真与美',
    img: '/rman/static/images/courseface/2.png',
  },
  {
    title: '生命哲学：爱、美与死亡',
    img: '/rman/static/images/courseface/3.png',
  },
  {
    title: '师法自然：仿生思维与人类生活',
    img: '/rman/static/images/courseface/4.png',
  },
  {
    title: '天工开物：智造工程的技艺与文化',
    img: '/rman/static/images/courseface/5.png',
  },
  {
    title: '万物皆数：探寻数学的理与美',
    img: '/rman/static/images/courseface/6.png',
  },
  {
    title: '弦歌不辍：聆听音乐的多维魅力',
    img: '/rman/static/images/courseface/7.png',
  },
  {
    title: '药物进化史：人类文明的别样视野',
    img: '/rman/static/images/courseface/8.png',
  },
  {
    title: '药物进化史：人类文明的别样视野',
    img: '/rman/static/images/courseface/8.png',
  },
  {
    title: '课程录制_学生画面_2021_5_10',
    img: '/rman/static/images/courseface/1.png',
  },
  {
    title: '课程录制_学生画面_2021_5_10',
    img: '/rman/static/images/courseface/1.png',
  },
  {
    title: '课程录制_学生画面_2021_5_10',
    img: '/rman/static/images/courseface/1.png',
  },
];

class Sider extends React.Component {
  // handleClick = (e: any) => {
  //   console.log('click ', e);
  // };

  render() {
    return (
      //   <Menu
      //     onClick={this.handleClick}
      //     style={{ width: 256 }}
      //     defaultSelectedKeys={['1']}
      //     defaultOpenKeys={['sub1']}
      //     mode="inline"
      //   >
      //     <SubMenu key="sub1" icon={<FolderOpenOutlined />} title="公共资源">
      //       <SubMenu key="sub2" icon={<FolderOpenOutlined />} title="录播资源">
      //         <Menu.Item key="5">片头片尾1</Menu.Item>
      //         <Menu.Item key="6">片头片尾2</Menu.Item>
      //       </SubMenu>
      //       <SubMenu key="sub3" icon={<FolderOpenOutlined />} title="共享资源">
      //         <Menu.Item key="7">片头片尾1</Menu.Item>
      //         <Menu.Item key="8">片头片尾2</Menu.Item>
      //       </SubMenu>
      //     </SubMenu>

      //     <SubMenu key="sub4" icon={<FolderOpenOutlined />} title="个人资源">
      //       <Menu.Item key="9">片头片尾1</Menu.Item>
      //       <Menu.Item key="10">片头片尾2</Menu.Item>
      //       <Menu.Item key="11">片头片尾3</Menu.Item>
      //       <Menu.Item key="12">片头片尾4</Menu.Item>
      //     </SubMenu>
      //   </Menu>
      <div></div>
    );
  }
}

class TitleModal extends React.Component {
  // state = {
  //   visible: false,
  //   disabled: true,
  // };

  // draggleRef = React.createRef();

  // showModal = () => {
  //   this.setState({
  //     visible: true,
  //   });
  // };

  // handleOk = (e: any) => {
  //   console.log(e);
  //   this.props.checkedListClear();
  //   this.setState({
  //     visible: false,
  //   });

  // const checkedList = this.props.checkedList;
  // const sequencemeta = this.props.sequencemeta;
  // const contentId = this.props.contentId;
  // const arr: any = [];

  // sequencemeta.forEach((item: any) => {
  //   checkedList.forEach((element: any) => {
  //     if (item.guid_ === element) {
  //       let tem = item;
  //       tem.fragment_description = '1';
  //       arr.push(tem);
  //     }
  //   });
  // });

  //   const res = SmartService.updatepartSequencemeta(contentId, arr);
  //   console.log(res);
  // };

  // handleCancel = (e: any) => {
  //   console.log(e);
  //   this.setState({
  //     visible: false,
  //   });
  // };

  // onStart = (event: any, uiData: any) => {
  //   const { clientWidth, clientHeight } = window?.document?.documentElement;
  //   const targetRect = this.draggleRef?.current?.getBoundingClientRect();
  //   this.setState({
  //     bounds: {
  //       left: -targetRect?.left + uiData?.x,
  //       right: clientWidth - (targetRect?.right - uiData?.x),
  //       top: -targetRect?.top + uiData?.y,
  //       bottom: clientHeight - (targetRect?.bottom - uiData?.y),
  //     },
  //   });
  // };

  render() {
    // const { disabled, visible } = this.state;
    return (
      //   <div>
      //     <Button type="primary" onClick={this.showModal}>
      //       生成成品
      //     </Button>
      //     <Modal
      //       width="70vw"
      //       visible={visible}
      //       onOk={this.handleOk}
      //       onCancel={this.handleCancel}
      //     >
      //       <div className="title_modal">
      //         <Sider />
      //         <div className="title_modal_right">
      //           <div className="modal_input">
      //             <div>
      //               <Input.Group compact>
      //                 <Select defaultValue="全部">
      //                   <Option Option value="all">
      //                     全部
      //                   </Option>
      //                   <Option value="part">部分</Option>
      //                 </Select>
      //                 <Input.Search style={{ width: '70%' }} defaultValue="" />
      //               </Input.Group>
      //             </div>
      //             <div style={{ marginRight: '10px' }}>
      //               <RangePicker showTime />
      //             </div>
      //             <div>
      //               <Button type="primary">搜索</Button>
      //             </div>
      //           </div>
      //           <div className="title_modal_images">
      //             {courses.map((item, index) => {
      //               return (
      //                 <div
      //                   key={index}
      //                   style={{
      //                     border: '1px solid #999',
      //                     // margin: '4px',
      //                     padding: '5px',
      //                   }}
      //                 >
      //                   <img src={item.img} alt="" />
      //                   <div style={{ display: 'flex', alignItems: 'center' }}>
      //                     <Checkbox></Checkbox>
      //                     <span className="title_modal_title">{item.title}</span>
      //                   </div>
      //                 </div>
      //               );
      //             })}
      //           </div>
      //           <div className="title_modal_pag">
      //             <Pagination defaultCurrent={1} total={50} />
      //           </div>
      //         </div>
      //       </div>
      //     </Modal>
      //   </div>
      <div></div>
    );
  }
}

export default TitleModal;
