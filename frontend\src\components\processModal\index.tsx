import React, {
    FC,
    useState,
    useEffect,
    ChangeEvent,
    useRef,
    useMemo,
} from 'react';
import { Upload } from 'antd';
import { Modal, Button, Checkbox, message, Form, Input, TreeSelect, Table } from 'antd';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { PlusOutlined, CloseOutlined, ForwardFilled, BackwardOutlined, InboxOutlined, PlusCircleOutlined, DeleteOutlined, PlusCircleFilled } from '@ant-design/icons';
import './index.less';
import { useIntl, useSelector, IUpload, useDispatch } from 'umi';
import { copyObject, asyncLoadScript, debounce, getUnique, computeFileInfoMD5 } from '@/utils';
import uploader from '@/components/upload/core/uploader';
import uploadTypes from '@/types/uploadTypes';
import BasicMetadata, {
    IBasicMetadataRef,
} from '@/components/basicMetadata/basicMetadata';
import ResourceModal from '@/components/ResourceModal';
import ResourcePreviewModal from '@/components/ResourcePreviewModal';
import { IFormItem } from '@/types/entityTypes';
import contentListApis from '@/service/contentListApis';
import uploadApis from '@/service/uploadApis';
import { IconFont } from '../iconFont/iconFont';
import ctyun from '@/otherStorage/ctyun';
import aliyun from '@/otherStorage/aliyun';
interface CreateModalProps {
    targetFolder: any;
    modalVisible: boolean;
    processlist: any;
    setProcessModalVisible: (value: boolean) => void,
    refresh:() => void
}
const { Dragger } = Upload;
import rmanApis from '@/service/rman';
import { IPermission } from '@/models/permission';
import globalParams, { ModuleCfg } from '@/permission/globalParams';

const ProcessModal: React.FC<CreateModalProps> = ({ modalVisible, targetFolder, processlist, setProcessModalVisible, refresh }) => {
    const {
        showModal,
        tasks,
        taskPanls,
        fields,
        orginTasks,
        uploadformat,
        uploadformatEnum
    } = useSelector<{ upload: IUpload }, IUpload>(({ upload }) => {
        return upload;
    });
    const { mobileFlag } = useSelector<{ config: any }, any>(
        ({ config }) => config
    );
    const { modules, permissions, rmanGlobalParameter } = useSelector<
        { permission: any },
        IPermission
    >(({ permission }) => permission);
    const intl = useIntl();
    const dispatch = useDispatch();
    const onCancel = () => {
        setProcessModalVisible(false)
        setEditNumber(0);
        // dispatch({
        //     type: 'upload/changeTasks',
        //     payload: {
        //         value: [],
        //     },
        // });
        // dispatch({
        //     type: 'upload/changeModal',
        //     payload: {
        //         value: false,
        //     },
        // });
    };
    const [editNumber, setEditNumber] = useState<number>(0); //正在编辑的素材
    // const [edit,setEdit] = useState(false)
    const metadataRef = useRef<IBasicMetadataRef | null>(null);
    const [webUploader, setWebUpolader] = useState<any>(null);
    const [selectedRows, setSelectedRows] = useState<any>([]);
    const [batchSettingVisible, setBatchSettingVisible] = useState<number>(0); //0 关闭 1 设置水印 2 批量设置
    const [showPanl, setShowPanl] = useState<boolean>(false);
    const [treeData, setTreeData] = useState<any>([]);
    const [selectedKey, setSelectedKey] = useState<any>('');
    const [selectedDetail, setSelectedDetail] = useState<any>(null);
    const currentSelected = useRef<any>([]);
    const [currentType, setCurrentType] = useState<any>('');
    const reftype = useRef<any>('');
    const [resourceModalVisible, setResourceModalVisible] = useState<boolean>(false);
    const initSet = {
        head: {
            keyframe_: '',
            contentId_: '',
        },
        tail: {
            keyframe_: '',
            contentId_: '',
        },
        watermark: {
            keyframe_: '',
            contentId_: '',
            position: 'lt',
        },
    };
    const [batchSet, setBatchSet] = useState<any>(initSet);
    const fileLists = useRef<any>([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
    const [entityPreview, setEntityPreview] = useState<any>(null);
    const [entityModalVisible, setEntityModalVisible] = useState<boolean>(false);
    const [showplaceholder, setShowplaceholder] = useState<string>('');
    const [columns, setColumns] = useState<any>([{
        title: intl.formatMessage({ id: '文件名' }),
        dataIndex: 'name_',
        width: '40%',
    }]);
    const [tableList, setTableList] = useState<any>([])
    useEffect(() => {
        if (processlist.length) {
            const result = JSON.parse(JSON.stringify(processlist))
            result.forEach((item: any)=> {
                item.head = {
                    keyframe_: '',
                    contentId_: '',
                }
                item.tail = {
                    keyframe_: '',
                    contentId_: '',
                }
                item.watermark = {
                    keyframe_: '',
                    contentId_: '',
                    position: 'lt',
                }
            });
            setTableList(result)
        }
    }, [processlist])
    useEffect(() => {
        fileLists.current = tableList;
    },[tableList])
    useEffect(() => {
        fetchTree();
        initcolumns();
    }, []);

    // 资源库上传的默认路径需要改成，在那个目录栏、文件夹上传时就默认哪个目录栏、文件夹
    useEffect(() => {
        if (targetFolder) {
            // setShowplaceholder(getDirTeeStr(targetFolder));
            setSelectedKey(targetFolder);
        }
    }, [targetFolder]);

    // 路径转译
    const getDirTeeStr = (dirTree?: string): string => {
        if (!dirTree) {
            return ''
        }
        if (dirTree.includes('global_sobey_defaultclass/public')) {
            return dirTree.replace('global_sobey_defaultclass/public', '').replace('/', '');
        } else if (dirTree.includes('global_sobey_defaultclass/private')) {
            let newdir = dirTree.replace(
                'global_sobey_defaultclass/private',
                '个人资源',
            );
            if (newdir.includes('/')) {
                let alldir = newdir.split('/');
                if (alldir.length >= 2) {
                    newdir = newdir.replace('/' + alldir[1], '');
                }
            }
            return newdir;
        } else {
            return dirTree;
        }
    };

    // 初始化表格的字段
    const initcolumns = () => {
        // 判断是否有权限
        if (rmanGlobalParameter.includes(globalParams.titles_trailer_display)) {
            columns.push({
                title: intl.formatMessage({ id: '片头' }),
                dataIndex: 'head',
                width: '20%',
                render: (value: any, row: any) => {
                    if (row.type_ === 'biz_sobey_video') {
                        if (value.keyframe_) {
                            return (
                                <div className="keyframe">
                                    <img src={value.keyframe_} />
                                    <Button
                                        type="link"
                                        icon={<DeleteOutlined />}
                                        onClick={() => deleteHeadTail(row, 'head')}
                                    />
                                </div>
                            );
                        } else {
                            return <Button onClick={() => addHeadTail(row, 'head')}>{intl.formatMessage({ id: '添加' })}</Button>;
                        }
                    } else {
                        return '-'
                    }
                },
            })
            columns.push({
                title: intl.formatMessage({ id: '片尾' }),
                dataIndex: 'tail',
                width: '20%',
                render: (value: any, row: any) => {
                    if (row.type_ === 'biz_sobey_video') {
                        if (value.keyframe_) {
                            return (
                                <div className="keyframe">
                                    <img src={value.keyframe_} />
                                    <Button
                                        type="link"
                                        icon={<DeleteOutlined />}
                                        onClick={() => deleteHeadTail(row, 'tail')}
                                    />
                                </div>
                            );
                        } else {
                            return <Button onClick={() => addHeadTail(row, 'tail')}>{intl.formatMessage({ id: '添加' })}</Button>;
                        }
                    } else {
                        return '-'
                    }
                },
            })
        }

        if (rmanGlobalParameter.includes(globalParams.watermark_display)) {
            columns.push({
                title: intl.formatMessage({ id: '水印/LOGO' }),
                dataIndex: 'watermark',
                width: '20%',
                // 当前列不显示
                render: (value: any, row: any) => {
                    if (row.type_ === 'biz_sobey_video') {
                        if (value.keyframe_) {
                            return (
                                <div className="keyframe">
                                    <img src={value.keyframe_} />
                                    <Button
                                        type="link"
                                        icon={<DeleteOutlined />}
                                        onClick={() => deleteHeadTail(row, 'watermark')}
                                    />
                                </div>
                            );
                        } else {
                            return <Button onClick={() => addHeadTail(row, 'watermark')}>{intl.formatMessage({ id: '添加' })}</Button>;
                        }
                    } else {
                        return '-'
                    }
                },
            })
        }
    }

    /**
     * react中异步回调中的state,是创建的那次渲染中看到的,不是最新的state
     * 这个hook使用useRef保存那个state,确保获取最新的state
     * @param state
     * @returns
     */
    const stateRef = useRef<any>()
    useEffect(() => {
        stateRef.current = taskPanls
    }, [taskPanls])
    useEffect(() => {
        if (metadataRef && metadataRef.current) {
            metadataRef.current.resetFields();
        }
    }, [editNumber]);

    const fetchTree = () => {
        rmanApis.gettreebylevel(2).then((res: any) => {
            if (res && res.data && res.success) {
                let data = res.data;
                let newData: any = [];
                data.map((item: any) => {
                    if (item.name === '公共资源') {
                        item.children?.forEach((item: any) => {
                            newData.push({ ...item, layer: 1 });
                        });
                    } else {
                        newData.push(item);
                    }
                });
                newData = newData.filter(Boolean); //过滤空对象
                // if(!permission){
                //   removeTreeListItem(newData, '录播资源');
                // }
                // setPrivatePath(newData.filter((item:any)=>item.name == '个人资源')?.[0].path || newData[0].path);//默认选中个人资源
                // 过滤录播资源 默认不显示
                newData = newData.filter((item: any) => item.name != '录播资源');
                const rootData = newData.map((item: any) => {
                    return {
                        key: item.path,
                        value: item.path,
                        title: item.name,
                        id: item.id,
                        disabled: (item.name === '群组资源') ? true : false,
                    };
                });
                // setSelectedKey(
                //     newData.filter((item: any) => item.name == '个人资源')?.[0].path || newData[0].path
                // ); //默认选中个人资源
                setTreeData(rootData);
            }
        });
    };
    //片头片尾
    const addHeadTail = (row: any, str: string) => {
        currentSelected.current = row.contentId_;
        setCurrentType(str);
        reftype.current = str;
        if (str == 'watermark') {
            setBatchSettingVisible(1);
        } else {
            setResourceModalVisible(true);
        }
    };
    //添加批量片头片尾
    const addHeadTailBatch = (str: string) => {
        setCurrentType(str);
        reftype.current = str;
        setResourceModalVisible(true);
    };
    //删除片头片尾水印
    const deleteHeadTail = (row: any, str: string) => {
        setBatchSet(initSet);
        let newtask = fileLists.current.map((item: any) => {
            if (row.contentId_ == item.contentId_) {
                let temp: any = {
                    keyframe_: '',
                    contentId_: '',
                };
                if (str == 'watermark') {
                    temp.position = 'lt';
                }
                return {
                    ...item,
                    [str]: temp,
                };
            } else {
                return item;
            }
        });
        setTableList(newtask)
    };

    const rowSelections = {
        onChange: (newSelectedRowKeys: Array<any>, newSelectedRows: any) => {
            setSelectedRowKeys(newSelectedRowKeys);
            setSelectedRows(newSelectedRows);
        },
        selectedRowKeys,
    };

    const resourceModalConfirm = async (resource: any[]) => {
        const item = resource[0];
        setResourceModalVisible(false);
        let temp: any = {
            keyframe_: item.keyframe_,
            contentId_: item.contentId_,
        };
        if (currentType == 'watermark') {
            temp.position = 'lt';
        }
        if (batchSettingVisible > 0) {
            //批量
            setBatchSet({
                ...batchSet,
                [currentType]: temp,
            });
        } else {
            let newtask = tableList.map((item: any) => {
                if (currentSelected.current == item.contentId_) {
                    return {
                        ...item,
                        [currentType]: temp,
                    };
                } else {
                    return item;
                }
            })
            console.log(newtask, 'task')
            setTableList(newtask)
        }
    };

    const batchSetPotion = (e: MouseEvent, str: string) => {
        e.stopPropagation();
        setBatchSet({
            ...batchSet,
            watermark: {
                ...batchSet.watermark,
                position: str,
            },
        });
    };

    const onConfirm = () => {
        //单个设置水印
        if (batchSettingVisible == 1) {
            let newtask = tableList.map((item: any) => {
                if (currentSelected.current == item.contentId_) {
                    return {
                        ...item,
                        watermark: batchSet.watermark,
                    };
                } else {
                    return item;
                }
            })
            setTableList(newtask)
        } else {
            const selected = copyObject(tableList);
            const newdata = selected.map((item: any) => {
                if (selectedRowKeys.includes(item.contentId_) && item.type_ == 'biz_sobey_video') {
                    if (batchSet.head.contentId_) {
                        item.head = batchSet.head;
                    }

                    if (batchSet.tail.contentId_) {
                        item.tail = batchSet.tail;
                    }

                    if (batchSet.watermark.contentId_) {
                        item.watermark = batchSet.watermark;
                    }
                    return item;
                } else {
                    return item;
                }
            });
            setTableList(newdata)
        }
        setBatchSet(initSet);  
        setBatchSettingVisible(0);
    };
    const processSaveas = () => {
        const params = tableList.map((item: any) => {
            return {
                saveFolderPath: selectedKey,
                sourceContentId: item.contentId_,
                beginContentId: item.head.contentId_,
                endContentId: item.tail.contentId_,
                waterMark: {
                    contentid: item.watermark.contentId_,
                    start_x: item.watermark.position[0] == 'l' ? 0 : 1,
                    start_y: item.watermark.position[1] == 't' ? 0 : 1,
                    logo_path: item.watermark.keyframe_,
                }
            }
        })
        contentListApis.procesSsaveas(params).then(_res => {
            if(_res?.success) {
                message.success(intl.formatMessage({ id: '批量加工中' }))
                setProcessModalVisible(false)
                refresh()
                onCancel()
            }
        })
    }
    const onSelect = (key: any, node: any) => {
        setSelectedDetail({
            key: node.key,
            title: node.title,
            id: node.id,
        });
        setSelectedKey(key);
        setShowplaceholder(getDirTeeStr(key));
    };
    //动态加载子节点
    const onLoadChild = (node: any, isRoot: boolean = false) => {
        const { key, children, code, title } = node;
        return new Promise(async (resolve) => {
            if (key === 'add') {
                resolve(null);
                return;
            }
            if (children) {
                resolve(null);
                return;
            }
            function updateTreeData(list: any, key: React.Key, children: any): any {
                return list.map((node: any) => {
                    if (node.key === key) {
                        return {
                            ...node,
                            children,
                        };
                    } else if (node.children) {
                        return {
                            ...node,
                            children: updateTreeData(node.children, key, children),
                        };
                    }
                    return node;
                });
            }
            const res: any = await rmanApis.loadChild(key, true);
            if (res && res.data && res.success) {
                let treeList = res.data;
                treeList = treeList.filter((item: any) => {
                    return item.name !== '录播资源';
                });
                setTreeData((origin: any) =>
                    updateTreeData(
                        origin,
                        key,
                        treeList.map((item: any) => {
                            return {
                                key: item.path,
                                title: item.name,
                                id: item.contentId,
                                value: item.path,
                            };
                        }),
                    ),
                );
                resolve(null);
            }
        });
    };
    return (
        <Modal
            destroyOnClose
            maskClosable={false}
            visible={modalVisible}
            footer={null}
            title={intl.formatMessage({ id: '批量加工' })}
            onCancel={() => onCancel()}
            className={`uploadbox${mobileFlag ? ' mobilebox' : ''}`}
            width={926}
        >
            <div className="upload_top">
                <div className="uploaded_file">
                    <div className="top">
                        <div className="left">
                            {
                                (rmanGlobalParameter.includes(globalParams.titles_trailer_display) || rmanGlobalParameter.includes(globalParams.watermark_display)) &&
                                <Button
                                    icon={<IconFont type="iconSettingsbeifen" />}
                                    onClick={() => {
                                        setBatchSettingVisible(2)
                                    }}
                                    disabled={(() => {
                                        if (selectedRows.length == 0 || selectedRows.some((item: any) => item.type_ !== 'biz_sobey_video')) {
                                            return true
                                        } else {
                                            return false
                                        }
                                    })()}
                                >
                                    {intl.formatMessage({ id: '批量加工' })}
                                </Button>
                            }

                        </div>
                        <div className="right">
                            <span>{intl.formatMessage({ id: '保存至' })}</span>
                            <TreeSelect
                                // placeholder={showplaceholder}
                                treeData={treeData}
                                value={showplaceholder}
                                onSelect={onSelect}
                                loadData={onLoadChild}
                            />
                        </div>
                    </div>
                    <div className="center">
                        <Table
                            dataSource={tableList}
                            columns={columns}
                            rowKey="contentId_"
                            size="small"
                            rowSelection={rowSelections}
                            scroll={{ y: 'calc(100vh - 500px' }}
                            pagination={false}
                            onRow={(record, index: any) => {
                                return {
                                    onClick: event => {
                                        setEditNumber(index);
                                    }
                                }
                            }}
                        />
                    </div>
                </div>
            </div>
            <div className="upload_bottom" style={{ marginTop: '10px' }}>
                <Button
                    type="primary"
                    onClick={processSaveas}
                >
                    {intl.formatMessage({ id: '确认' })}
                </Button>
            </div>
            <ResourceModal
                treeData={treeData}
                visible={resourceModalVisible}
                onConfirm={resourceModalConfirm}
                onCancel={() => setResourceModalVisible(false)}
                onShowDetail={(id, detail) => {
                    setEntityPreview({
                        id: id,
                        name: detail.name,
                        type: detail.type,
                    });
                    setEntityModalVisible(true);
                }}
                fileType={(reftype.current == 'watermark' ? ['biz_sobey_picture'] : ['biz_sobey_video'])}
                multi={false}
            />
            {
                entityModalVisible ?
                    <ResourcePreviewModal
                        modalVisible={entityModalVisible}
                        modalClose={() => setEntityModalVisible(false)}
                        resource={entityPreview}
                    /> : null
            }
            <Modal
                title={batchSettingVisible == 1 ? intl.formatMessage({ id: '添加水印' }) : intl.formatMessage({ id: '批量加工' })}
                open={batchSettingVisible == 0 ? false : true}
                onCancel={() => setBatchSettingVisible(0)}
                className="batchSettingModal"
                footer={[<Button onClick={onConfirm}>{intl.formatMessage({ id: '确认' })}</Button>]}
            >
                <div className="body">
                    {rmanGlobalParameter.includes(globalParams.titles_trailer_display) && (
                        <>
                            {batchSettingVisible ===2 &&<div>
                                <label>片头：</label>
                                <div className="add" onClick={() => addHeadTailBatch('head')}>
                                    {batchSet.head.keyframe_ ? (
                                        <div className="done">
                                            <img src={batchSet.head.keyframe_} />
                                            <Button
                                                type="link"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    setBatchSet({
                                                        ...batchSet,
                                                        head: {
                                                            keyframe_: '',
                                                            contentId_: '',
                                                        },
                                                    });
                                                }}
                                                icon={<DeleteOutlined />}
                                            />
                                        </div>
                                    ) : (
                                        <>
                                            <PlusCircleFilled />
                                            <span>{intl.formatMessage({ id: '添加' })}</span>
                                        </>
                                    )}
                                </div>
                            </div>}
                            {batchSettingVisible ===2 &&<div>
                                <label>{intl.formatMessage({ id: '片尾' })}：</label>
                                <div className="add" onClick={() => addHeadTailBatch('tail')}>
                                    {batchSet.tail.keyframe_ ? (
                                        <div className="done">
                                            <img src={batchSet.tail.keyframe_} />
                                            <Button
                                                type="link"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    setBatchSet({
                                                        ...batchSet,
                                                        tail: {
                                                            keyframe_: '',
                                                            contentId_: '',
                                                        },
                                                    });
                                                }}
                                                icon={<DeleteOutlined />}
                                            />
                                        </div>
                                    ) : (
                                        <>
                                            <PlusCircleFilled />
                                            <span>{intl.formatMessage({ id: '添加' })}</span>
                                        </>
                                    )}
                                </div>
                            </div>}
                        </>
                    )}
                    {
                        rmanGlobalParameter.includes(globalParams.watermark_display) &&
                        <div>
                            <label>{intl.formatMessage({ id: '水印/LOGO' })}：</label>
                            <div className="watermark" onClick={() => addHeadTailBatch('watermark')}>
                                {batchSet.watermark.keyframe_ ? (
                                    <div className="done">
                                        <img src={batchSet.watermark.keyframe_} />
                                        <Button
                                            type="link"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                setBatchSet({
                                                    ...batchSet,
                                                    watermark: {
                                                        keyframe_: '',
                                                        contentId_: '',
                                                        position: 'lt',
                                                    },
                                                });
                                            }}
                                            icon={<DeleteOutlined />}
                                        />
                                    </div>
                                ) : (
                                    <div className="add">
                                        <PlusCircleFilled />
                                        <span>{intl.formatMessage({ id: '添加' })}</span>
                                    </div>
                                )}
                                <div
                                    onClick={(e: any) => batchSetPotion(e, 'lt')}
                                    className={`leftTop dot${batchSet.watermark.position == 'lt' ? ' active' : ''}`}
                                ></div>
                                <div
                                    onClick={(e: any) => batchSetPotion(e, 'rt')}
                                    className={`rightTop dot${batchSet.watermark.position == 'rt' ? ' active' : ''}`}
                                ></div>
                                <div
                                    onClick={(e: any) => batchSetPotion(e, 'lb')}
                                    className={`leftBottom dot${batchSet.watermark.position == 'lb' ? ' active' : ''}`}
                                ></div>
                                <div
                                    onClick={(e: any) => batchSetPotion(e, 'rb')}
                                    className={`rightBottom dot${batchSet.watermark.position == 'rb' ? ' active' : ''
                                        }`}
                                ></div>
                                <span className="tips">{intl.formatMessage({ id: '点击小方块选择水印位置' })}</span>
                            </div>
                        </div>
                    }
                </div>
            </Modal>
        </Modal>
    );
};

export default ProcessModal;
