
import { FC, useEffect, useState, useRef } from 'react';
import React from "react";
import "./index.less";
import { Modal, Button, Select, Input, Upload, message, Spin } from "antd";
import ImgCrop from 'antd-img-crop';
import ResourceModal from "@/components/ResourceModal";
import rmanApis from '@/service/rman';
import ResourcePreviewModal from "@/components/ResourcePreviewModal";
import CropperModal from "@/components/CropperModal";
// @ts-ignore
import $ from "jquery";
import { getSensitiveWord } from '@/utils';
import RenderHtml from '../renderHtml';
import { request } from 'umi';
import axios from 'axios'
const { Option } = Select;
const { TextArea } = Input;
import contentListApis from '@/service/contentListApis';
interface CoverModalProps {
  name: string;
  visible: boolean;
  detail: any;
  teacher: Array<string>;
  treeData?: ResourceModal.treeItem[];
  coverSetting?: any;
  coverCancel: () => void;
  refresh: () => void;
}

const fontList = [
  { ch: '宋体', en: 'SimSun' },
  { ch: '黑体', en: 'SimHei' },
  { ch: '微软雅黑', en: 'Microsoft Yahei' },
  { ch: '微软正黑体', en: 'Microsoft JhengHei' },
  { ch: '楷体', en: 'KaiTi' },
  { ch: '新宋体', en: 'NSimSun' },
  { ch: '仿宋', en: 'FangSong' }
];

const textToHtml = (text: string) => text.replace(/\n/g, "<br/>").replace(/\r/, "&nbsp;");
const limitRows = (value: string, limitLength: number, limitRow: number) => {
  const row = value.split("\n");
  if (value.includes("\n") && (row[1].length >= limitLength || row.length > limitRow)) {
    return row[0] + "\n" + row[1].slice(0, limitLength);
  } else {
    return value;
  }
};




const CoverModal: FC<CoverModalProps> = ({  detail, teacher, treeData, visible, refresh, coverCancel }) => {
  const [defaultImages, setDefaultImages] = useState<string[]>([]); // 默认的图片列表，左侧的9张
  // const [imageIndex, setImageIndex] = useState<number>(-1); // 选中图片的索引
  const imageIndex = useRef<number>(-1);
  const [cover, setCover] = useState<string>(''); // 右侧封面的背景图
  const [titleValue, setTitleValue] = useState<string>(""); // 右侧封面的名字
  const [teacherValue, setTeacherValue] = useState<string>(""); // 右侧封面的teacher
  const [color, setColor] = useState<string>("#ffffff"); // 字体颜色
  const [font, setFont] = useState<string>("Microsoft Yahei"); // 字体
  const [blur, setBlur] = useState<boolean>(false);
  const [resourceVisible, setResourceVisible] = useState<boolean>(false); // 资源库弹窗visible
  const [entityVisible, setEntityVisible] = useState<boolean>(false); // 资源详情visible
  const [entityPreview, setEntityPreview] = useState<any>(null); // 资源详情数据
  const [modalTreeData, setModalTreeData] = useState<ResourceModal.treeItem[]>([]); // 资源树数据
  const [resourceImage, setResourceImage] = useState<string>("");
  const [cropperVisible, setCropperVisible] = useState<boolean>(false);
  // const [newCover, setNewCover] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    getCovers();
    rmanApis.getKeyframeSetting({ contentId: detail.contentId_ }).then((res) => {
      if (res.success) {
        setColor(res?.data?.color || "#ffffff");
        setFont(res?.data?.font || "Microsoft Yahei")
        setCover(res?.data?.oldIconKeyframe.replace('/mstorage', '') || detail.keyframe_);
      }
    })
  }, []);

  // useEffect(() => {
  //   if (coverSetting && Reflect.ownKeys(coverSetting).length > 0) {
  //     if (coverSetting.uploadUrl) {
  //       setCover(coverSetting.uploadUrl);
  //       imageIndex.current = 12;
  //     } else {
  //       imageIndex.current = coverSetting.coverType || 0;
  //       setCover(defaultImages?.[imageIndex.current]);
  //     }
  //     setTitleValue(coverSetting.courseName || "");
  //     setTeacherValue(coverSetting.teacherName || "");
  //     setColor(coverSetting.color || "#ffffff");
  //     setFont(fontList[coverSetting.type ?? 2].en);
  //   } else {
  //     setCover(defaultImages[0]);
  //   }
  // }, [coverSetting]);

  // useEffect(() => {
  //   if (defaultImages.length > 0) {
  //     setCover();
  //   }
  // }, [imageIndex]);

  const getCovers = () => {
    rmanApis.getCoverList().then((res) => {
      if (res.status == 200) {
        const images = res.data.map((item: any) => item.url);
        setDefaultImages(images);
        if (imageIndex.current < 12) {
          if (imageIndex.current === -1) {
            imageIndex.current = 0;
          }
          // setCover(images[coverSetting.coverType || 0]);
        }
        // if (res.data.length > 0) {
        //   setImageIndex(Math.floor(Math.random() * res.data.length));
        // }
      }
    });
  };

  const forTree = (tree: any, parentsKeys: string[]) => {
    return tree.map((item: any) => {
      return {
        key: item.id,
        parentsKeys,
        title: item.name,
        path: item.path,
        children: item.children ?
          forTree(item.children, [...parentsKeys, item.code]) :
          []
      };
    });
  };
  const fetchTree = () => {
    rmanApis.gettreebylevel(2).then((res: any) => {
        if (res && res.data && res.success) {
            let data = res.data;
            let newData: any = [];
            data.map((item: any) => {
                if (item.name === '公共资源') {
                    item.children?.forEach((item: any) => {
                        newData.push({ ...item, layer: 1 });
                    });
                } else {
                    newData.push(item);
                }
            });
            newData = newData.filter(Boolean); //过滤空对象
            newData = newData.filter((item: any) => item.name != '录播资源');
            const rootData = newData.map((item: any) => {
                return {
                    key: item.path,
                    value: item.path,
                    title: item.name,
                    id: item.id,
                };
            });
            setModalTreeData(rootData);
            setTimeout(() => {
              setResourceVisible(true);
            }, 100);
        }
    });
  };
  // const getTreeData = () => {
  //   rmanApis.gettreebylevel(2).then((res) => {
  //     if (res && res.success) {
  //       let treeData = forTree(res.data, []);
  //       setModalTreeData(treeData);
  //       setTimeout(() => {
  //         setResourceVisible(true);
  //       }, 100);
  //     } else {
  //       console.error(res);
  //     }
  //   });
  // };
  const confirmHandle = () => {
    if (!cover) {
      message.warning("请选择封面！");
      return;
    }
    handleChangeCover();
  };
  const handleChangeCover = () => {
    setLoading(true);
    const data = {
      contentId: detail.contentId_,
      name: titleValue,
      color,
      fontName: font,
      fontSize: 30,
      createUserCode: (window as any).login_useInfo.userCode,
      createUserName: '',
      // keyframePath: `/mstorage${cover}`,
      keyframePath: cover.startsWith('http') ? cover : `/mstorage${cover}`
    };
    getSensitiveWord(titleValue, "资源名", () => {
      rmanApis[detail.type_ === 'folder'? 'updateFolderKerframe':'updateKerframe'](data).then((res: any) => {
        if (res.success) {
          coverCancel();
          refresh()
        } else {
          message.warning('封面变更失败！');
        }
      }).finally(() => {
        setLoading(false);
      });
    }, () => {
      setLoading(false);
    });

  };
  const selectDefaultCover = (index: number, image: string) => {
    imageIndex.current = index;
    setCover(image);
  };
  const colorChange = (el: any) => {
    setColor(el.target?.value || "#fff");
  };
  // const drawCanvas = () => {
  //   setBlur(true);
  //   setTimeout(() => {
  //     html2canvas((document.querySelector(".cover-content") as HTMLElement), {
  //       scale: 2
  //     }).then(canvas => {
  //       // const width = canvas.width;
  //       // const height = canvas.height;
  //       // console.info(width, height);
  //       // const image = new Image();
  //       // image.src = canvas.toDataURL("image/jpg");
  //       // document.querySelector(".left-wrapper")?.append(image);
  //       setBlur(false);
  //       // coverConfirm(canvas.toDataURL("image/jpg"));
  //       const image = !teacherValue && !titleValue && newCover ? newCover : canvas.toDataURL("image/jpeg");
  //       const bytes = window.atob(image.split(',')[1]);
  //       // 处理异常,将ascii码小于0的转换为大于0
  //       const ab = new ArrayBuffer(bytes.length);
  //       const ia = new Uint8Array(ab);
  //       for (let i = 0; i < bytes.length; i++) {
  //         ia[i] = bytes.charCodeAt(i);
  //       }
  //       const file: any = new Blob([ab], { type: 'image/jpeg' });
  //       file.name = `${new Date().valueOf()}.jpg`;
  //       coverConfirm(file);
  //     });
  //   });
  // };
  const compressImage = (file?: any) => {
    // 图片小于5M不压缩
    const name = file.name; //文件名
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (e: any) => {
      const src = e.target.result;
      const img = new Image();
      img.src = src;
      if (file.size <= Math.sqrt(1024) * 5) {
        setCover(src);
      }
      img.onload = (e: any) => {
        const w = img.width;
        const h = img.height;
        // const quality = (Math.pow(1024, 2) * 5 / file.size).toFixed(1);  // 默认图片质量为0.92
        // 生成canvas
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        // 创建属性节点
        const anw = document.createAttribute("width");
        anw.nodeValue = w.toString();
        const anh = document.createAttribute("height");
        anh.nodeValue = h.toString();
        canvas.setAttributeNode(anw);
        canvas.setAttributeNode(anh);

        // 铺底色 PNG转JPEG时透明区域会变黑色
        (ctx as CanvasRenderingContext2D).fillStyle = "#fff";
        (ctx as CanvasRenderingContext2D).fillRect(0, 0, w, h);

        (ctx as CanvasRenderingContext2D).drawImage(img, 0, 0, w, h);
        // quality值越小，所绘制出的图像越模糊
        const base64 = canvas.toDataURL('image/jpeg'); // 图片格式jpeg或webp可以选0-1质量区间
        base64ToFile(base64, name);

        // setNewCover(base64);
      };
    };
  };
  const base64ToFile = (base64: string, name: string) => {
    // 返回base64转blob的值
    // 去掉url的头，并转换为byte
    const bytes = window.atob(base64.split(',')[1]);
    // 处理异常,将ascii码小于0的转换为大于0
    const ab = new ArrayBuffer(bytes.length);
    const ia = new Uint8Array(ab);
    for (let i = 0; i < bytes.length; i++) {
      ia[i] = bytes.charCodeAt(i);
    }
    const file: any = new Blob([ab], { type: 'image/jpeg' });
    file.name = name;
    if (file.size > Math.sqrt(1024) * 5) {
      setCover(base64);
    }
    handleUpload(base64);
  };
  const beforeUpload = (file: any) => {
    compressImage(file);
    return false;
  };
  const clickResourceHandle = () => {
    if (!treeData?.length) {
      fetchTree();
    }

  };

  const resourceModalConfirm = (resource: Array<any>) => {
    if (resource.length > 0) {
      setResourceImage(resource[0].keyframe_);
      setResourceVisible(false);
      setCropperVisible(true);
    }
  };
  const handleUpload = (file: any) => {
    setLoading(true);
    console.log('file', file);
    var prms = {
      imageBase64: file,
      imageUse: 3,  // 图片
      relatedId: detail.contentId_ // 素材
    }
    contentListApis.createCourseimage(prms).then((res: any) => {
      if (res?.success ) {
        setCover(res.data);
        imageIndex.current = 12;
        message.success("封面上传成功！");
        setLoading(false);
      } else {
        setLoading(false);
        message.error("文件读取失败");
      }
    })
  };
  const updateHandle = () => {
    titleValueChange(detail.name_);
    console.info(teacher);
    const teachers = (teacher instanceof Array ? teacher?.join("、") : teacher) ?? "";
    setTeacherValue(teachers);
  };
  const resourceCropperHandle = (image: string) => {
    base64ToFile(image, `${Date.now().valueOf()}.jpg`);
    setCropperVisible(false);

  };
  const titleValueChange = (value: string) => {
    setTitleValue(limitRows(value, 12, 2));
    // console.info($(".title-input").height())
    setTimeout(() => {
      if ($(".title-input").height() > 70) {
        $(".cover-content").css("padding-top", "61px");
      } else {
        $(".cover-content").css("padding-top", "100px");
      }
    });
  };
  return (
    <Modal title={"批量设置公开画面"} wrapClassName="cover-modal" width={1238} onCancel={coverCancel} onOk={confirmHandle} open={visible} confirmLoading={loading}>
      <Spin spinning={loading}>
        <div className="left-wrapper">
          <div className="left-header">
            <div className="title">封面图模板</div>
            <div className="btn-wrapper">
              <Button type="primary" ghost onClick={clickResourceHandle}>资源库选择</Button>
              <ImgCrop aspect={16 / 9} quality={0.8} rotate>
                <Upload
                  accept='.png,.jpg,.gif,.svg'
                  beforeUpload={beforeUpload}
                  showUploadList={false}>

                  <Button>本地上传</Button>
                </Upload>
              </ImgCrop>
            </div>
          </div>
          {/* <div className="left-content">
            {defaultImages.map((image: string, index: number) =>
              <div className={`image-wrapper ${imageIndex.current === index ? 'active' : ''}`} key={index} onClick={() => selectDefaultCover(index, image)}>
                <img src={image} alt="" />
              </div>)}

          </div> */}

        </div>
        <div className="right-wrapper">
          <div className="right-header">效果预览</div>
            {/* 对象存储不兼容 */}
          {/* <div className="right-opt-wrapper">
            <Select value={font} onChange={(value: string) => setFont(value)}>
              {fontList.map((font) =>
                <Option value={font.en} key={font.en}>{font.ch}</Option>)}

            </Select>
            <div className="color-wrapper">
              <div className="text">颜色</div>
              <input type="color" value={color} className="color" onChange={(el: any) => colorChange(el)} />
            </div>

          </div> */}
          <div className="cover-wrapper-box">
            {/* <img src={cover} alt="" /> */}
            <div className='cover-content' style={{ backgroundImage: `url(${cover})` }}>
              {blur ?
                <>
                  <RenderHtml className={`title-content ${titleValue ? "" : "no-title"}`} style={{ color, fontFamily: font }} dangerouslySetInnerHTML={{ __html: textToHtml(titleValue) }}></RenderHtml>
                </> :

                <>
                {/* 对象存储不兼容 */}
                  {/* <TextArea
                    className="title-input"
                    style={{ color, fontFamily: font }}
                    maxLength={24}
                    onChange={({ target: { value } }) => titleValueChange(value)}
                    value={titleValue}
                    autoSize={{ minRows: 1, maxRows: 2 }} /> */}

                </>}

            </div>
          </div>
          {/* 对象存储不兼容 */}
          {/* <div className="right-btn-wrapper">
            <Button onClick={updateHandle}>添加文字内容</Button>
            <div className="tips">将会把当前资源名自动填入</div>
          </div> */}
        </div>
      </Spin>
      {resourceVisible && <ResourceModal
        treeData={modalTreeData}
        visible={resourceVisible}
        onConfirm={resourceModalConfirm}
        onCancel={() => setResourceVisible(false)}
        onShowDetail={(id, detail) => {
          setEntityPreview({
            id: id,
            name: detail.name,
            type: detail.type
          });
          setEntityVisible(true);
        }}
        fileType={["biz_sobey_picture"]}
        multi={false} />}

      <ResourcePreviewModal
        modalVisible={entityVisible}
        modalClose={() => setEntityVisible(false)}
        resource={entityPreview} />

      <CropperModal visible={cropperVisible} image={resourceImage} cropperOk={resourceCropperHandle} cropperCancel={() => { setCropperVisible(false); }} />
    </Modal>);
};

export default CoverModal;