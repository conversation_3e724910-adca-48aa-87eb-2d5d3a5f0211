# mam-upload 上传插件

## 依赖
> jQuery

## 介绍
> 上传插件

## 安装及使用方式
> 将mam-upload.js引入到项目中。通过window.mamUpload获取插件。  

## 接口及调用方式
要调用上传接口，首先需要调用初始化方法，然后调用上传方法实现上传
> initUploadFramework：初始化插件

```javascript
window.mamUpload.initUploadFramework({
    el : "#angular-framework",//将插件绑定到的元素
    server : "http://localhost:9060",//后端地址
    basePath : "../dist",//插件在本地系统的相对位置
    loginToken : "85eec896656e521fe7f4706c11508867"
}).then(function(){
              console.log("init success");
          });
```
> createTask：如果和本地环境和angularjs冲突，则可以只使用上传基础代码，初始化方式：
```javascript
window.mamUpload.initNoNg({
    server : "http://localhost:9060",//后端地址
    basePath : "../dist",//插件在本地系统的相对位置
    loginToken : "85eec896656e521fe7f4706c11508867"
}).then(function(){
              console.log("init success");
          });

```
> createTask：选择上传文件
```javascript
window.mamUpload.uploader.createTask({
    taskType : window.mamUpload.uploader.taskType.group,//任务类型（普通上次、成组上传、图片包）
    targetFolder : "",//上传路径
    writeMetadataBefore: function (files, params) {//选择文件后执行的函数
        return false;//表示自己处理上传逻辑，为true表示让框架处理元数据等操作
    }
});
```
> createTask：创建上传任务
```javascript
//task为任务对象，包含要上传的文件
var params = {
     taskType: task.taskType,
     transferType: transferType,
     targetFolder: '',
     relationContentType: window.mamUpload.uploader.relationContentType.none
};
window.mamUpload.uploader.transfers['web'].createTask(task, params);
```
taskType：  
window.mamUpload.uploader.taskType.general（普通上传）  
window.mamUpload.uploader.taskType.group（成组上传）
window.mamUpload.uploader.taskType.picturePackage（图片包上传）

事件绑定：  
task-init-success：初始化完成  
task-init-error：初始化错误  
task-upload-error：上传错误  
task-upload-success：上传成功
```html
window.mamUpload.uploader.web.on("task-upload-success", function(e, data){
    console.log("task-upload-success:");
    console.log(data);
});
```

html:
```html
<div id="angular-framework" ng-controller="mamUploadCtrl">
        <button class="btn btn-primary" onclick="onUpload()">上传文件</button>
        <upload-button-group vtbue-transfer-enable="true" web-transfer-enable="true" picture-package-enable="true" file-group-enable="true"></upload-button-group>
        <upload-task-panel content-id="" target-type="0"></upload-task-panel>
    </div>
```