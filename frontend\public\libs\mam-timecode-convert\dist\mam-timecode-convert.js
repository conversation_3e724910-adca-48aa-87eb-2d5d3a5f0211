/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, {
/******/ 				configurable: false,
/******/ 				enumerable: true,
/******/ 				get: getter
/******/ 			});
/******/ 		}
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = 0);
/******/ })
/************************************************************************/
/******/ ([
/* 0 */
/***/ (function(module, exports) {

/*!
 * TimeCode Convert v2.0.1 (http://www.sobey.com)
 * Copyright 2010-2016 Sobey, Inc. CDC
 * Licensed under the MIT license
 * Author: Liujianming (<EMAIL>)
 * Convert From C++(Sobey MPC) ->C#(Sobey MAM) -> Javascript
 */

/**
 * Array Extended contains
 *
 */
Array.prototype.S = String.fromCharCode(2);
Array.prototype.contains = function (e) {
    var r = new RegExp(this.S + e + this.S);
    return (r.test(this.S + this.join(this.S) + this.S));
};
/**
 * String  Extended replaceAll
 *
 */
String.prototype.replaceAll = function (s1, s2) {
    return this.replace(new RegExp(s1, "gm"), s2);
};

var TimeCodeConvert = (function () {
    /** @namespace TimeCodeConvert */
    /**
     * 时码转换帮助类
     *
     * @public
     * @class TimeCodeConvert.TimeCodeConvertHelper
     */
    var timeCodeConvertHelper = function () {
    };

    timeCodeConvertHelper.prototype = {
        /**
        * 桌面视频制式标准枚举定义
        *
        * @class TimeCodeConvert.MpcVideoStandard
        */
        MpcVideoStandard: {
            mpcVideostandardUnknow: 0,
            mpcVideostandardPal: 1,
            mpcVideostandardNtsc2997: 2,
            mpcVideostandardNtsc30: 4,
            mpcVideostandardSecam: 8,
            mpcVideostandard1920108050I: 16,
            mpcVideostandard192010805994I: 32,
            mpcVideostandard1920108060I: 64,
            mpcVideostandard192010802398P: 128,
            mpcVideostandard1920108024P: 256,
            mpcVideostandard1920108025P: 512,
            mpcVideostandard192010802997P: 1024,
            mpcVideostandard1920108030P: 2048,
            mpcVideostandard12807202398P: 4096,
            mpcVideostandard128072024P: 8192,
            mpcVideostandard128072050P: 16384,
            mpcVideostandard12807205994P: 32768,
            mpcVideostandard1440108050I: 65536,
            mpcVideostandard144010805994I: 131072,
            mpcVideostandard1440108060I: 262144
        },

        /**
         * PAL field frequency
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @default 50
         * @type number
         */
        m_mpcStRate25: 50,
        /**
         * PAL frame  frequency
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 25
         * @type number
         */
        MpcStFrameRate25: 25,
        /**
         * PAL scale
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 1
         * @type number
         */
        MpcStScale25: 1,
        /**
         * NTSC field frequency
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @default 60000
         * @type number
         */
        m_mpcStRate2997: 60000,
        /**
         * NTSC frame  frequency
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 30000
         * @type number
         */
        MpcStFrameRate2997: 30000,
        /**
         * NTSC scale
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 1001
         * @type number
         */
        MpcStScale2997: 1001,
        /**
         * 30-F field frequency
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @default 60
         * @type number
         */
        m_mpcStRate30: 60,
        /**
         * 30-F frame frequency
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 30
         * @type number
         */
        MpcStFrameRate30: 30,
        /**
         * 30-F scale
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 1
         * @type number
         */
        MpcStScale30: 1,
        /**
         * 24-F field frequency
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @default 48
         * @type number
         */
        m_mpcStRate24: 48,
        /**
         * 24-F field frequency
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 24
         * @type number
         */
        MpcStFrameRate24: 24,
        /**
         * 24-F scale
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 1
         * @type number
         */
        MpcStScale24: 1,
        /**
         * 2398-F field frequency
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @default 48000
         * @type number
         */
        m_mpcStRate2398: 48000,
        /**
         * 2398-F frame frequency
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 24000
         * @type number
         */
        MpcStFrameRate2398: 24000,
        /**
         * 2398-F scale
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 1001
         * @type number
         */
        MpcStScale2398: 1001,
        /**
         * PAL field frequency
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @default 50
         * @type number
         */
        m_mpcStRate50: 50,
        /**
         * PAL frame  frequency
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 50
         * @type number
         */
        MpcStFrameRate50: 50,
        /**
         * PAL scale
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 1
         * @type number
         */
        MpcStScale50: 1,
        /**
         * NTSC field frequency
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @default 60000
         * @type number
         */
        m_mpcStRate5994: 60000,
        /**
         * NTSC frame  frequency
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 60000
         * @type number
         */
        MpcStFrameRate5994: 60000,
        /**
         * NTSC scale
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 1001
         * @type number
         */
        MpcStScale5994: 1001,
        /**
         * 60-F field frequency
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @default 60
         * @type number
         */
        m_mpcStRate60: 60,
        /**
         * 60-F frame frequency
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 60
         * @type number
         */
        MpcStFrameRate60: 60,
        /**
         * 60-F scale
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 1
         * @type number
         */
        MpcStScale60: 1,
        /**
         * 25 Frame: frames per second
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 25
         * @type number
         */
        MpcFramesSecond25: 25,
        /**
         * 25 Frame: frames per minute
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 1500
         * @type number
         */
        MpcFramesMinute25: 1500,
        /**
         * 25 Frame: frames per hour
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 90000
         * @type number
         */
        MpcFramesHour25: 90000,
        /**
         * 30 DROP Frame: frames per minute
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 1438
         * @type number
         */
        MpcFramesMinute24Drop: 1438,
        /**
         * 30 DROP Frame: frames per 10 minutes
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 14382
         * @type number
         */
        MpcFrames10Minutes24Drop: 14382,
        /**
         * 30 DROP Frame: frames per hour
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 86292
         * @type number
         */
        MpcFramesHour24Drop: 86292,
        /**
         * 24 Frame: frames per second
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 24
         * @type number
         */
        MpcFramesSecond24: 24,
        /**
         * 24 Frame: frames per minute
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 1440
         * @type number
         */
        MpcFramesMinute24: 1440,
        /**
         * 24 Frame: frames per hour
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 86400
         * @type number
         */
        MpcFramesHour24: 86400,
        /**
         * 30 NO_DROP Frame: frames per second
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 30
         * @type number
         */
        MpcFramesSecondNodrop30: 30,
        /**
         * 30 NO_DROP Frame: frames per minute
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 1800
         * @type number
         */
        MpcFramesMinuteNodrop30: 1800,
        /**
         * 30 NO_DROP Frame: frames per hour
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 108000
         * @type number
         */
        MpcFramesHourNodrop30: 108000,
        /**
         * 30 DROP Frame: frames per minute
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 1798
         * @type number
         */
        MpcFramesMinute30Drop: 1798,
        /**
         * 30 DROP Frame: frames per 10 minutes
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 17982
         * @type number
         */
        MpcFrames10Minutes30Drop: 17982,
        /**
         * 30 DROP Frame: frames per hour
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 107892
         * @type number
         */
        MpcFramesHour30Drop: 107892,
        /**
         * 50 Frame: frames per second
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 50
         * @type number
         */
        MpcFramesSecond50: 50,
        /**
         * 50 Frame: frames per minute
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 3000
         * @type number
         */
        MpcFramesMinute50: 3000,
        /**
         * 50 Frame: frames per hour
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 180000
         * @type number
         */
        MpcFramesHour50: 180000,
        /**
         * 60 DROP Frame: frames per minute
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 3596
         * @type number
         */
        MpcFramesMinute60Drop: 3596,
        /**
         * 60 DROP Frame: frames per 10 minutes
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 35964
         * @type number
         */
        MpcFrames10Minutes60Drop: 35964,
        /**
         * 60 DROP Frame: frames per hour
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 215784
         * @type number
         */
        MpcFramesHour60Drop: 215784,
        /**
         * 60 Frame: frames per second
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 60
         * @type number
         */
        MpcFramesSecond60: 60,
        /**
         * 60 Frame: frames per minute
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 3600
         * @type number
         */
        MpcFramesMinute60: 3600,
        /**
         * 60 Frame: frames per hour
         *
         * @static
         * @private
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @constant
         * @default 216000
         * @type number
         */
        MpcFramesHour60: 216000,
        /**
         * 帧转百纳秒
         *
         * @static
         * @public
         * @this TimeCodeConvert.TimeCodeConvertHelper
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @param   {number}    lFrame    帧
         * @param   {number}    dRate     帧率
         * @param   {number}    dScale    
         * @return  {number}              百纳秒
         */
        frame2L100Ns$1: function (lFrame, dRate, dScale) {
            var dFrameRate = { v: this.MpcStFrameRate25 };
            var dFrameScale = { v: this.MpcStScale25 };

            this.rate2ScaleFrameRateAndFrameScale(dRate, dScale, dFrameRate, dFrameScale);

            return parseInt((Math.floor(lFrame * Math.pow(10.0, 7) * dFrameRate.v / dFrameScale.v)));
        },
        /**
         * 帧转百纳秒
         *
         * @static
         * @public
         * @this TimeCodeConvert.TimeCodeConvertHelper
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @param   {number}    dFrame        帧
         * @param   {number}    dFrameRate    帧率
         * @return  {number}                  百纳秒
         */
        frame2L100Ns: function (dFrame, dFrameRate) {
            var dRate = { v: this.MpcStFrameRate25 };
            var dScale = { v: this.MpcStScale25 };
            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);

            return parseInt((Math.floor(dFrame * dScale.v * Math.pow(10.0, 7) / dRate.v)));
        },
        /**
         * 帧转秒
         *
         * @static
         * @public
         * @this TimeCodeConvert.TimeCodeConvertHelper
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @param   {number}    lFrame    帧
         * @param   {number}    dRate     帧率
         * @param   {number}    dScale    
         * @return  {number}              秒
         */
        frame2Second$1: function (lFrame, dRate, dScale) {
            var dFrameRate = { v: this.MpcStFrameRate25 };
            var dFrameScale = { v: this.MpcStScale25 };
            this.rate2ScaleFrameRateAndFrameScale(dRate, dScale, dFrameRate, dFrameScale);

            return (lFrame * dFrameScale.v / dFrameRate.v);
        },
        /**
         * 帧转秒
         *
         * @static
         * @public
         * @this TimeCodeConvert.TimeCodeConvertHelper
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @param   {number}    dFrame        帧
         * @param   {number}    dFrameRate    帧率
         * @return  {number}                  秒
         */
        frame2Second: function (dFrame, dFrameRate) {
            var dRate = { v: this.MpcStFrameRate25 };
            var dScale = { v: this.MpcStScale25 };
            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);

            return (dFrame * dScale.v / dRate.v);
        },
        /**
         * 帧转时码
         *
         * @static
         * @public
         * @this TimeCodeConvert.TimeCodeConvertHelper
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @param   {number}     dFrame       帧
         * @param   {number}     dRate        帧率
         * @param   {number}     dScale       
         * @param   {boolean}    dropFrame    
         * @return  {string}                  时码字符串
         */
        frame2Tc$1: function (dFrame, dRate, dScale, dropFrame) {
            if (!dropFrame) {
                dropFrame = this.getRateDropFrame(dRate);
            }
            var strTc = "";
            if ((dRate === this.MpcStFrameRate25 && dScale === this.MpcStScale25) || (dRate * this.MpcStScale25 === dScale * this.MpcStFrameRate25)) {
                var dHour = parseInt((Math.floor(dFrame / this.MpcFramesHour25)));
                var dResidue = parseInt((Math.floor(dFrame % this.MpcFramesHour25)));
                var dMin = parseInt((Math.floor(dResidue / this.MpcFramesMinute25)));
                dResidue = dResidue % this.MpcFramesMinute25;
                var dSec = parseInt((Math.floor(dResidue / this.MpcFramesSecond25)));
                var dFra = parseInt((Math.floor(dResidue % this.MpcFramesSecond25)));
                strTc = this.formatTimeCodeString(dHour, dMin, dSec, dFra, false);
            } else if ((dRate === this.MpcStFrameRate2997 && dScale === this.MpcStScale2997) || (dRate * this.MpcStScale2997 === dScale * this.MpcStFrameRate2997)) {
                if (dropFrame) {
                    var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHour30Drop)));
                    var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHour30Drop)));
                    var dMin1 = parseInt((Math.floor(10 * Math.floor(this.div(dResidue1, this.MpcFrames10Minutes30Drop)))));
                    dResidue1 = dResidue1 % this.MpcFrames10Minutes30Drop;
                    if (dResidue1 >= this.MpcFramesMinuteNodrop30) {
                        dResidue1 -= this.MpcFramesMinuteNodrop30;
                        dMin1 += 1 + parseInt(Math.floor(this.div(dResidue1, this.MpcFramesMinute30Drop)));
                        dResidue1 %= this.MpcFramesMinute30Drop;
                        dResidue1 += 2;
                    }
                    var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecondNodrop30)));
                    var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecondNodrop30)));
                    strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, true);
                } else {
                    var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHourNodrop30)));
                    var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHourNodrop30)));
                    var dMin11 = parseInt((Math.floor(dResidue11 / this.MpcFramesMinuteNodrop30)));
                    dResidue11 = dResidue11 % this.MpcFramesMinuteNodrop30;
                    var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecondNodrop30)));
                    var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecondNodrop30)));
                    strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, false);
                }
            } else if ((dRate === this.MpcStFrameRate30 && dScale === this.MpcStScale30) || (dRate * this.MpcStScale30 === dScale * this.MpcStFrameRate30)) {
                var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHourNodrop30)));
                var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHourNodrop30)));
                var dMin1 = parseInt((Math.floor(dResidue1 / this.MpcFramesMinuteNodrop30)));
                dResidue1 = dResidue1 % this.MpcFramesMinuteNodrop30;
                var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecondNodrop30)));
                var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecondNodrop30)));
                strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, false);
            } else if ((dRate === this.MpcStFrameRate24 && dScale === this.MpcStScale24) || (dRate * this.MpcStScale24 === dScale * this.MpcStFrameRate24)) {
                var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHour24)));
                var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHour24)));
                var dMin11 = parseInt((Math.floor(dResidue11 / this.MpcFramesMinute24)));
                dResidue11 = dResidue11 % this.MpcFramesMinute24;
                var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecond24)));
                var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecond24)));
                strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, false);
            } else if ((dRate === this.MpcStFrameRate2398 && dScale === this.MpcStScale2398) || (dRate * this.MpcStScale2398 === dScale * this.MpcStFrameRate2398)) {
                if (dropFrame) {
                    var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHour24Drop)));
                    var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHour24Drop)));
                    var dMin1 = parseInt((Math.floor(10 * (this.div(dResidue1, this.MpcFrames10Minutes24Drop)))));
                    dResidue1 = dResidue1 % this.MpcFrames10Minutes24Drop;
                    if (dResidue1 >= this.MpcFramesMinute24) {
                        dResidue1 -= this.MpcFramesMinute24;
                        dMin1 += 1 + parseInt(Math.floor(this.div(dResidue1, this.MpcFramesMinute24Drop)));
                        dResidue1 %= this.MpcFramesMinute24Drop;
                        dResidue1 += 2;
                    }
                    var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecond24)));
                    var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecond24)));
                    strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, true);
                } else {
                    var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHour24)));
                    var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHour24)));
                    var dMin11 = parseInt((Math.floor(dResidue11 / this.MpcFramesMinute24)));
                    dResidue11 = dResidue11 % this.MpcFramesMinute24;
                    var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecond24)));
                    var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecond24)));
                    strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, false);
                }
            } else if ((dRate === this.MpcStFrameRate50 && dScale === this.MpcStScale50) || (dRate * this.MpcStScale50 === dScale * this.MpcStFrameRate50)) {
                var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHour50)));
                var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHour50)));
                var dMin1 = parseInt((Math.floor(dResidue1 / this.MpcFramesMinute50)));
                dResidue1 = dResidue1 % this.MpcFramesMinute50;
                var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecond50)));
                var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecond50)));
                strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, false);
            } else if ((dRate === this.MpcStFrameRate5994 && dScale === this.MpcStScale5994) || (dRate * this.MpcStScale5994 === dScale * this.MpcStFrameRate5994)) {
                if (dropFrame) {
                    var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHour60Drop)));
                    var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHour60Drop)));
                    var dMin11 = parseInt((Math.floor(10 * (this.div(dResidue11, this.MpcFrames10Minutes60Drop)))));
                    dResidue11 = dResidue11 % this.MpcFrames10Minutes60Drop;
                    if (dResidue11 >= this.MpcFramesMinute60) {
                        dResidue11 -= this.MpcFramesMinute60;
                        dMin11 += 1 + parseInt(Math.floor(this.div(dResidue11, this.MpcFramesMinute60Drop)));
                        dResidue11 %= this.MpcFramesMinute60Drop;
                        dResidue11 += 4;
                    }
                    var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecond60)));
                    var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecond60)));
                    strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, true);
                } else {
                    var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHour60)));
                    var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHour60)));
                    var dMin1 = parseInt((Math.floor(dResidue1 / this.MpcFramesMinute60)));
                    dResidue1 = dResidue1 % this.MpcFramesMinute60;
                    var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecond60)));
                    var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecond60)));
                    strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, false);
                }
            } else if ((dRate === this.MpcStFrameRate60 && dScale === this.MpcStScale60) || (dRate * this.MpcStScale60 === dScale * this.MpcStFrameRate60)) {
                var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHour60)));
                var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHour60)));
                var dMin11 = parseInt((Math.floor(dResidue11 / this.MpcFramesMinute60)));
                dResidue11 = dResidue11 % this.MpcFramesMinute60;
                var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecond60)));
                var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecond60)));
                strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, false);
            }
            return strTc;
        },
        /**
         * 帧转时码
         *
         * @static
         * @public
         * @this TimeCodeConvert.TimeCodeConvertHelper
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @param   {number}     dFrame        帧
         * @param   {number}     dFrameRate    帧率
         * @param   {boolean}    dropFrame     是否丢帧
         * @return  {string}                   时码字符串
         */
        frame2Tc: function (dFrame, dFrameRate, dropFrame) {
            if (!dropFrame) {
                dropFrame = this.getRateDropFrame(dFrameRate);
            }
            var dRate = { v: this.MpcStFrameRate25 };
            var dScale = { v: this.MpcStScale25 };
            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);

            var strTc = this.frame2Tc$1(dFrame, dRate.v, dScale.v, dropFrame);
            return strTc;
        },
        /**
         * 时码字符串转帧
         *
         * @static
         * @public
         * @this TimeCodeConvert.TimeCodeConvertHelper
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @param   {string}     sTimeCode    时码
         * @param   {number}     frameRate    帧率
         * @param   {number}     dRate        
         * @param   {number}     dScale       
         * @param   {boolean}    dropFrame    是否是丢帧
         * @return  {number}                  帧
         */
        timeCode2Frame$1: function (sTimeCode, frameRate, dRate, dScale, dropFrame) {
            if (!dropFrame) {
                dropFrame = this.getRateDropFrame(frameRate);
            }
            var ftcFrames = 0;
            if ((dRate === this.MpcStFrameRate25 && dScale === this.MpcStScale25) || (dRate * this.MpcStScale25 === dScale * this.MpcStFrameRate25)) {
                ftcFrames = this.timeCode2NdfFrame(sTimeCode, frameRate);
            } else if ((dRate === this.MpcStFrameRate2997 && dScale === this.MpcStScale2997) || (dRate * this.MpcStScale2997 === dScale * this.MpcStFrameRate2997)) {
                var lHour = { v: 0 };
                var lMinute = { v: 0 };
                var lSecond = { v: 0 };
                var lFrame = { v: 0 };

                this.timeCode2Format(sTimeCode, lHour, lMinute, lSecond, lFrame, frameRate);

                if (dropFrame) {
                    ftcFrames += lHour.v * this.MpcFramesHour30Drop;

                    var lwReste = this.div(lMinute.v, 10);
                    ftcFrames += lwReste * this.MpcFrames10Minutes30Drop;
                    lwReste = lMinute.v % 10;

                    if (lwReste > 0) {
                        ftcFrames += this.MpcFramesMinuteNodrop30;
                        ftcFrames += (lwReste - 1) * this.MpcFramesMinute30Drop;
                        ftcFrames -= 2;
                    }

                    ftcFrames += lSecond.v * this.MpcFramesSecondNodrop30;
                    ftcFrames += lFrame.v;
                } else {
                    ftcFrames = this.timeCode2NdfFrame(sTimeCode, 30);
                }
            } else if ((dRate === this.MpcStFrameRate30 && dScale === this.MpcStScale30) || (dRate * this.MpcStScale30 === dScale * this.MpcStFrameRate30)) {
                ftcFrames = this.timeCode2NdfFrame(sTimeCode, frameRate);
            } else if ((dRate === this.MpcStFrameRate24 && dScale === this.MpcStScale24) || (dRate * this.MpcStScale24 === dScale * this.MpcStFrameRate24)) {
                ftcFrames = this.timeCode2NdfFrame(sTimeCode, frameRate);
            } else if ((dRate === this.MpcStFrameRate2398 && dScale === this.MpcStScale2398) || (dRate * this.MpcStScale2398 === dScale * this.MpcStFrameRate2398)) {
                var lHour1 = { v: 0 };
                var lMinute1 = { v: 0 };
                var lSecond1 = { v: 0 };
                var lFrame1 = { v: 0 };

                this.timeCode2Format(sTimeCode, lHour1, lMinute1, lSecond1, lFrame1, frameRate);

                if (dropFrame) {
                    ftcFrames += lHour.v * this.MpcFramesHour24;

                    var lwReste1 = this.div(lMinute.v, 10);
                    ftcFrames += lwReste1 * this.MpcFrames10Minutes24Drop;
                    lwReste1 = lMinute.v % 10;
                    if (lwReste1 > 0) {
                        ftcFrames += this.MpcFramesMinute60;
                        ftcFrames += (lwReste1 - 1) * this.MpcFramesMinute24;
                        ftcFrames -= 2;
                    }

                    ftcFrames += lSecond.v * this.MpcFramesSecond24;
                    ftcFrames += lFrame.v;
                } else {
                    ftcFrames = this.timeCode2NdfFrame(sTimeCode, 24);
                }
            } else if ((dRate === this.MpcStFrameRate50 && dScale === this.MpcStScale50) || (dRate * this.MpcStScale50 === dScale * this.MpcStFrameRate50)) {
                ftcFrames = this.timeCode2NdfFrame(sTimeCode, frameRate);
            } else if ((dRate === this.MpcStFrameRate5994 && dScale === this.MpcStScale5994) || (dRate * this.MpcStScale5994 === dScale * this.MpcStFrameRate5994)) {
                var lHour11 = { v: 0 };
                var lMinute11 = { v: 0 };
                var lSecond11 = { v: 0 };
                var lFrame11 = { v: 0 };

                this.timeCode2Format(sTimeCode, lHour11, lMinute11, lSecond11, lFrame11, frameRate);
                if (dropFrame) {
                    ftcFrames += lHour.v * MpcFramesHour60Drop;

                    var lwReste11 = this.div(lMinute.v, 10);
                    ftcFrames += lwReste11 * this.MpcFrames10Minutes60Drop;
                    lwReste11 = lMinute.v % 10;
                    if (lwReste11 > 0) {
                        ftcFrames += this.MpcFramesMinute60;
                        ftcFrames += (lwReste11 - 1) * this.MpcFramesMinute60Drop;
                        ftcFrames -= 4;
                    }

                    ftcFrames += lSecond.v * this.MpcFramesSecond60;
                    ftcFrames += lFrame.v;
                } else {
                    ftcFrames = this.timeCode2NdfFrame(sTimeCode, 60);
                }
            } else if ((dRate === this.MpcStFrameRate60 && dScale === this.MpcStScale60) || (dRate * this.MpcStScale60 === dScale * this.MpcStFrameRate60)) {
                ftcFrames = this.timeCode2NdfFrame(sTimeCode, frameRate);
            }
            return ftcFrames;
        },
        /**
         * 时间字符串转帧
         *
         * @static
         * @public
         * @this TimeCodeConvert.TimeCodeConvertHelper
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @param   {string}     sTimeCode     时码
         * @param   {number}     dFrameRate    帧率
         * @param   {boolean}    dropFrame     是否是丢帧
         * @return  {number}                   帧
         */
        timeCode2Frame: function (sTimeCode, dFrameRate, dropFrame) {
            if (!dropFrame) {
                dropFrame = this.getRateDropFrame(dFrameRate);
            }
            //sTimeCode = FormatTimeCodeString(sTimeCode, dFrameRate, GetRateDropFrame(dFrameRate));
            sTimeCode = this.formatTimeCodeString$1(sTimeCode, dFrameRate, dropFrame);


            var dRate = { v: this.MpcStFrameRate25 };
            var dScale = { v: this.MpcStScale25 };
            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);

            var ftcFrames = this.timeCode2Frame$1(sTimeCode, dFrameRate, dRate.v, dScale.v, dropFrame);

            var newTc = this.frame2Tc(ftcFrames, dFrameRate, dropFrame);


            newTc = this.replaceAll(newTc, "\\.", ":");
            sTimeCode = this.replaceAll(sTimeCode, "\\.", ":");


            if (newTc !== sTimeCode) {
                ftcFrames = this.getFrameByTimeCode(sTimeCode, ftcFrames, true, 1, dFrameRate, dropFrame);
            }

            return ftcFrames;
        },
        /**
         * 获取帧率和修正值
         *
         * @static
         * @public
         * @this TimeCodeConvert.TimeCodeConvertHelper
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @param   {number}            dFrameRate    输入帧率
         * @param   {System.Double&}    dRate         修正帧率
         * @param   {System.Double&}    dScale        修正值
         * @return  {void}                            
         */
        frameRate2RateAndScale: function (dFrameRate, dRate, dScale) {
            if (Math.abs(dFrameRate - 25.0) < 0.01) {
                dRate.v = this.MpcStFrameRate25;
                dScale.v = this.MpcStScale25;
            } else if (Math.abs(dFrameRate - 29.970029970029969) < 0.01) {
                dRate.v = this.MpcStFrameRate2997;
                dScale.v = this.MpcStScale2997;
            } else if (Math.abs(dFrameRate - 30.0) < 0.01) {
                dRate.v = this.MpcStFrameRate30;
                dScale.v = this.MpcStScale30;
            } else if (Math.abs(dFrameRate - 24.0) < 0.01) {
                dRate.v = this.MpcStFrameRate24;
                dScale.v = this.MpcStScale24;
            } else if (Math.abs(dFrameRate - 23.976023976023978) < 0.01) {
                dRate.v = this.MpcStFrameRate2398;
                dScale.v = this.MpcStScale2398;
            } else if (Math.abs(dFrameRate - 50.0) < 0.01) {
                dRate.v = this.MpcStFrameRate50;
                dScale.v = this.MpcStScale50;
            } else if (Math.abs(dFrameRate - 59.940059940059939) < 0.01) {
                dRate.v = this.MpcStFrameRate5994;
                dScale.v = this.MpcStScale5994;
            } else if (Math.abs(dFrameRate - 60.0) < 0.01) {
                dRate.v = this.MpcStFrameRate60;
                dScale.v = this.MpcStScale60;
            }
        },
        /**
         * 百纳秒转帧
         *
         * @static
         * @public
         * @this TimeCodeConvert.TimeCodeConvertHelper
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @param   {number}    l100Ns    百纳秒
         * @param   {number}    dRate     帧率
         * @param   {number}    dScale    修正值
         * @return  {number}              帧
         */
        l100Ns2Frame$1: function (l100Ns, dRate, dScale) {
            var dFrameRate = { v: this.MpcStFrameRate25 };
            var dFrameScale = { v: this.MpcStScale25 };
            this.rate2ScaleFrameRateAndFrameScale(dRate, dScale, dFrameRate, dFrameScale);

            return Math.floor(l100Ns / Math.pow(10.0, 7) * dFrameRate.v / dFrameScale.v + 0.5);
        },
        /**
         * 百纳秒转帧
         *
         * @static
         * @public
         * @this TimeCodeConvert.TimeCodeConvertHelper
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @param   {number}    l100Ns        百纳秒
         * @param   {number}    dFrameRate    帧率
         * @return  {number}                  帧
         */
        l100Ns2Frame: function (l100Ns, dFrameRate) {
            var dRate = { v: this.MpcStFrameRate25 };
            var dScale = { v: this.MpcStScale25 };
            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);

            return Math.floor(l100Ns * dRate.v / dScale.v / Math.pow(10.0, 7) + 0.5);
        },
        /**
         * 百纳秒转帧
         *
         * @static
         * @public
         * @this TimeCodeConvert.TimeCodeConvertHelper
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @param   {number}    l100Ns        百纳秒
         * @param   {number}    dFrameRate    帧率
         * @return  {number}                  帧
         */
        l100Ns2FrameNoCorrecte: function (l100Ns, dFrameRate) {
            var dRate = { v: this.MpcStFrameRate25 };
            var dScale = { v: this.MpcStScale25 };
            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);

            return Math.floor(l100Ns * dRate.v / dScale.v / Math.pow(10.0, 7));
        },
        /**
         * 百纳秒转秒
         *
         * @static
         * @public
         * @this TimeCodeConvert.TimeCodeConvertHelper
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @param   {number}    l100Ns        百纳秒
         * @return  {number}                  秒
         */
        l100Ns2Second: function (l100Ns) {
            return l100Ns / 10000000;
        },
        /**
         * 百纳秒转时码
         *
         * @static
         * @public
         * @this TimeCodeConvert.TimeCodeConvertHelper
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @param   {number}     l100Ns       百纳秒
         * @param   {boolean}    dropFrame    是否是丢帧
         * @return  {string}                  时码字符串
         */
        l100Ns2Tc: function (l100Ns, dropFrame) {
            var dHour = (Math.floor(l100Ns / (3600 * Math.pow(10.0, 7))));
            var llResidue = (l100Ns % (3600 * Math.pow(10.0, 7)));
            var dMin = (Math.floor(llResidue / (60 * Math.pow(10.0, 7))));
            llResidue = llResidue % parseInt((Math.floor(60 * Math.pow(10.0, 7))));
            var dSec = (Math.floor(llResidue / (Math.pow(10.0, 7))));
            llResidue = llResidue % parseInt((Math.pow(10.0, 7)));
            var dFraction = (Math.floor(llResidue / (100000)));
            return this.formatTimeCodeString(dHour, dMin, dSec, dFraction, dropFrame);
        },
        /**
         * 百纳秒转时码
         *
         * @static
         * @public
         * @this TimeCodeConvert.TimeCodeConvertHelper
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @param   {number}     l100Ns        百纳秒
         * @param   {number}     dFrameRate    帧率
         * @param   {boolean}    dropFrame     
         * @return  {string}                   时码字符串
         */
        l100Ns2Tc$1: function (l100Ns, dFrameRate, dropFrame) {
            if (!dropFrame) {
                dropFrame = this.getRateDropFrame(dFrameRate);
            }
            return this.frame2Tc(this.l100Ns2Frame(l100Ns, dFrameRate), dFrameRate, dropFrame);
        },
        /**
         * 帧率修正
         *
         * @static
         * @public
         * @this TimeCodeConvert.TimeCodeConvertHelper
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @param   {number}            dRate          帧率
         * @param   {number}            dScale         修正值
         * @param   {System.Double&}    dFrameRate     输出帧率
         * @param   {System.Double&}    dFrameScale    输出修正值
         * @return  {void}                             
         */
        rate2ScaleFrameRateAndFrameScale: function (dRate, dScale, dFrameRate, dFrameScale) {
            if ((dRate === this.MpcStFrameRate25 && dScale === this.MpcStScale25) || (dRate * this.MpcStScale25 === dScale * this.MpcStFrameRate25)) {
                dFrameRate.v = this.MpcStFrameRate25;
                dFrameScale.v = MpcStScale25;
            } else if ((dRate === this.MpcStFrameRate2997 && dScale === this.MpcStScale2997) || (dRate * this.MpcStScale2997 === dScale * this.MpcStFrameRate2997)) {
                dFrameRate.v = this.MpcStFrameRate2997;
                dFrameScale.v = this.MpcStScale2997;
            } else if ((dRate === this.MpcStFrameRate30 && dScale === this.MpcStScale30) || (dRate * this.MpcStScale30 === dScale * this.MpcStFrameRate30)) {
                dFrameRate.v = this.MpcStFrameRate30;
                dFrameScale.v = this.MpcStScale30;
            } else if ((dRate === this.MpcStFrameRate24 && dScale === this.MpcStScale24) || (dRate * this.MpcStScale24 === dScale * this.MpcStFrameRate24)) {
                dFrameRate.v = this.MpcStFrameRate24;
                dFrameScale.v = this.MpcStScale24;
            } else if ((dRate === this.MpcStFrameRate2398 && dScale === this.MpcStScale2398) || (dRate * this.MpcStScale2398 === dScale * this.MpcStFrameRate2398)) {
                dFrameRate.v = this.MpcStFrameRate2398;
                dFrameScale.v = this.MpcStScale2398;
            } else if ((dRate === this.MpcStFrameRate50 && dScale === this.MpcStScale50) || (dRate * this.MpcStScale50 === dScale * this.MpcStFrameRate50)) {
                dFrameRate.v = this.MpcStFrameRate50;
                dFrameScale.v = this.MpcStScale50;
            } else if ((dRate === this.MpcStFrameRate5994 && dScale === this.MpcStScale5994) || (dRate * this.MpcStScale5994 === dScale * this.MpcStFrameRate5994)) {
                dFrameRate.v = this.MpcStFrameRate5994;
                dFrameScale.v = this.MpcStScale5994;
            } else if ((dRate === this.MpcStFrameRate60 && dScale === this.MpcStScale60) || (dRate * this.MpcStScale60 === dScale * this.MpcStFrameRate60)) {
                dFrameRate.v = this.MpcStFrameRate60;
                dFrameScale.v = this.MpcStScale60;
            }
        },
        /**
         * 秒转帧
         *
         * @static
         * @public
         * @this TimeCodeConvert.TimeCodeConvertHelper
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @param   {number}    dbSec     秒数
         * @param   {number}    dRate     帧率
         * @param   {number}    dScale    修正值
         * @return  {number}              帧数
         */
        second2Frame$1: function (dbSec, dRate, dScale) {
            //dbSec = dbSec * Math.pow(10.0, 7);
            dbSec = parseInt(dbSec.toFixed(7).replace('.', ''));
            var dFrameRate = { v: this.MpcStFrameRate25 };
            var dFrameScale = { v: this.MpcStScale25 };
            this.rate2ScaleFrameRateAndFrameScale(dRate, dScale, dFrameRate, dFrameScale);

            //return (long)( dbSec * dRate / dScale );
            return Math.floor(dbSec * dFrameRate.v / dFrameScale.v / Math.pow(10.0, 7) + 0.5);
        },
        /**
         * 秒转帧
         *
         * @static
         * @public
         * @this TimeCodeConvert.TimeCodeConvertHelper
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @param   {number}    dbSec         秒数
         * @param   {number}    dFrameRate    帧率
         * @return  {number}                  帧数
         */
        second2Frame: function (dbSec, dFrameRate) {
            //超过24的归零
            if (dbSec >= 86400) {
                dbSec = dbSec - 86400;

                return this.second2Frame(dbSec, dFrameRate);
            }

            //dbSec = dbSec * Math.pow(10.0, 7);
            dbSec = parseInt(dbSec.toFixed(7).replace('.', ''));
            var dRate = { v: this.MpcStFrameRate25 };
            var dScale = { v: this.MpcStScale25 };
            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);

            //return (long)( dbSec * dRate / dScale );
            //return parseInt((Math.floor(dbSec * dRate.v / dScale.v / Math.pow(10.0, 7))));
            return Math.floor(dbSec * dRate.v / dScale.v / Math.pow(10.0, 7) + 0.5);
        },
        /**
         * 秒转帧不加0.5修正,适用于真实的点
         *
         * @static
         * @public
         * @this TimeCodeConvert.TimeCodeConvertHelper
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @param   {number}    dbSec         秒数
         * @param   {number}    dFrameRate    帧率
         * @return  {number}                  帧数
         */
        second2FrameNoCorrecte: function (dbSec, dFrameRate) {
            //超过24的归零
            if (dbSec >= 86400) {
                dbSec = dbSec - 86400;

                return this.second2FrameNoCorrecte(dbSec, dFrameRate);
            }

            //dbSec = dbSec * Math.pow(10.0, 7);
            dbSec = parseInt(dbSec.toFixed(7).replace('.', ''));
            var dRate = { v: this.MpcStFrameRate25 };
            var dScale = { v: this.MpcStScale25 };
            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);

            //return (long)( dbSec * dRate / dScale );
            //return parseInt((Math.floor(dbSec * dRate.v / dScale.v / Math.pow(10.0, 7))));
            return Math.floor(dbSec * dRate.v / dScale.v / Math.pow(10.0, 7));
        },
        /**
         * 格式化时码字符串
         *
         * @static
         * @public
         * @this TimeCodeConvert.TimeCodeConvertHelper
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @param   {number}     hours        小时数
         * @param   {number}     minutes      分指数
         * @param   {number}     seconds      秒数
         * @param   {number}     frames       帧数
         * @param   {boolean}    dropFrame    是否是丢帧
         * @return  {string}                  格式化后的时码字符串
         */
        formatTimeCodeString: function (hours, minutes, seconds, frames, dropFrame) {
            hours = hours >= 24 ? hours - 24 : hours;
            minutes = minutes >= 60 ? minutes - 60 : minutes;
            seconds = seconds >= 60 ? seconds - 60 : seconds;

            var framesSeparator = ".";
            if (!dropFrame) {
                framesSeparator = ":";
            }

            hours = parseInt((Math.floor(hours % 24.0)));
            function wrap(n) {
                return ((n < 10) ? '0' + n : n);
            }
            //return string.format("{0:D2}:{1:D2}{4}{2:D2}:{3:D2}", hours, minutes, seconds, frames, framesSeparator);
            var smtp = (wrap(hours) + ':' + wrap(minutes) + framesSeparator + wrap(seconds) + ':' + wrap(frames));
            return smtp;
        },
        formatTimeCodeString$1: function (timeCode, dFrameRate, dropFrame) {
            if (timeCode) {
                var hours = 0;
                var minutes = 0;
                var seconds = 0;
                var frames = 0;
                var ftcs = timeCode.split(":");
                hours = parseInt(ftcs[0]);
                if (ftcs.length >= 4) {
                    minutes = parseInt(ftcs[1]);
                    seconds = parseInt(ftcs[2]);

                    if (parseInt(ftcs[3]) >= dFrameRate) {

                        var showframeRate = Math.ceil(dFrameRate) - 1;
                        ftcs[3] = showframeRate.toString();
                    } else {

                        //验证是否是丢帧时码 
                        if (dropFrame) {
                            //如果是丢帧帧率 分钟 除开 00 10 20 30 40 50 外所有的 00 01帧不存在 强制设置为02帧 5994强制设置为04帧
                            var dropM = ["00", "10", "20", "30", "40", "50"];
                            var drop5994F = ["00", "01", "02", "03"];
                            var dropF = ["00", "01"];

                            if (ftcs[2] === "00" && !dropM.contains(ftcs[1]) && drop5994F.contains(ftcs[3])) {
                                if (60.0 - dFrameRate < 0.1) {
                                    ftcs[3] = "04";
                                } else {
                                    if (dropF.contains(ftcs[3])) {
                                        ftcs[3] = "02";
                                    }
                                }
                            }
                        }
                    }
                    frames = parseInt(ftcs[3]);
                } else {
                    var ftcssf = ftcs[2].split(".");
                    minutes = parseInt(ftcs[1]);
                    seconds = parseInt(ftcssf[0]);

                    if (parseInt(ftcssf[1]) >= dFrameRate) {
                        var showframeRate1 = Math.ceil(dFrameRate) - 1;
                        ftcssf[1] = showframeRate1.toString();
                    } else {

                        //验证是否是丢帧时码 
                        if (dropFrame) {
                            //如果是丢帧帧率 分钟 除开 00 10 20 30 40 50 外所有的 00 01帧不存在 强制设置为02帧
                            var dropM1 = ["00", "10", "20", "30", "40", "50"];
                            var drop5994F1 = ["00", "01", "02", "03"];
                            var dropF1 = ["00", "01"];

                            if (ftcssf[0] === "00" && !dropM1.contains(ftcs[1]) && drop5994F1.contains(ftcssf[1])) {
                                if (60.0 - dFrameRate < 0.1) {
                                    ftcssf[1] = "04";
                                } else {
                                    if (dropF1.contains(ftcssf[1])) {
                                        ftcssf[1] = "02";
                                    }
                                }
                            }
                        }

                    }
                    frames = parseInt(ftcssf[1]);
                }

                return this.formatTimeCodeString(hours, minutes, seconds, frames, dropFrame);
            }
            return "--:--:--:--";
        },
        /**
         * 递归解决时码丢帧的问题
         *
         * @static
         * @private
         * @this TimeCodeConvert.TimeCodeConvertHelper
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @param   {string}     sTimeCode     
         * @param   {number}     ftcFrames     
         * @param   {boolean}    isAdded       是否加修正值 为false的时候 为减了修正值
         * @param   {number}     corrValue     修正值
         * @param   {number}     dFrameRate    
         * @param   {boolean}    dropFrame     
         * @return  {number}                   
         */
        getFrameByTimeCode: function (sTimeCode, ftcFrames, isAdded, corrValue, dFrameRate, dropFrame) {
            var ftcNewFrames = 0;
            if (isAdded) {
                ftcNewFrames = ftcFrames + corrValue;
            } else {
                ftcNewFrames = ftcFrames - corrValue;
                corrValue++;
            }
            var newTc = this.frame2Tc(ftcNewFrames, dFrameRate, dropFrame);


            newTc = this.replaceAll(newTc, ".", ":");
            sTimeCode = this.replaceAll(sTimeCode, ".", ":");


            if (newTc !== sTimeCode) {
                return this.getFrameByTimeCode(sTimeCode, ftcFrames, !isAdded, corrValue, dFrameRate, dropFrame);
            }

            return ftcNewFrames;
        },
        /**
         * 获取此帧率是否丢帧
         *
         * @static
         * @public
         * @this TimeCodeConvert.TimeCodeConvertHelper
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @param   {number}     rate    帧率
         * @return  {boolean}            
         */
        getRateDropFrame: function (rate) {
            if (rate === 23.98 || (rate < 24 && rate > 23)) {
                return true;
            } else if (rate === 24.0) {
                return false;
            } else if (rate === 25.0) {
                return false;
            } else if (rate === 29.97 || (rate < 30 && rate > 29)) {
                return true;
            } else if (rate === 30.0) {
                return false;
            } else if (rate === 50.0) {
                return false;
            } else if (rate === 59.94 || (rate < 60 && rate > 59)) {
                return true;
            } else if (rate === 60.0) {
                return false;
            }
            return false;
        },
        /**
         * 时间字符串转秒(未考虑丢帧的情况)
         *
         * @static
         * @private
         * @this TimeCodeConvert.TimeCodeConvertHelper
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @param   {string}    sTimeCode     
         * @param   {number}    dFrameRate    
         * @return  {number}                  帧数
         */
        timeCode2NdfFrame: function (sTimeCode, dFrameRate) {
            var ftcSeconds = 0;
            var ftcFrames = 0;

            var lHour = { v: 0 };
            var lMinute = { v: 0 };
            var lSecond = { v: 0 };
            var lFrame = { v: 0 };

            this.timeCode2Format(sTimeCode, lHour, lMinute, lSecond, lFrame, dFrameRate);

            ftcSeconds += lHour.v * 60 * 60;
            ftcSeconds += lMinute.v * 60;
            ftcSeconds += lSecond.v;
            ftcFrames += lFrame.v;
            //这里以前有bug，因为second2Frame返回的是字符串，不转成int会变成字符串拼接了。。
            ftcFrames += parseInt(this.second2Frame(ftcSeconds, dFrameRate));

            return ftcFrames;
        },
        /**
         * 时间字符串格式化
         *
         * @static
         * @private
         * @this TimeCodeConvert.TimeCodeConvertHelper
         * @memberof TimeCodeConvert.TimeCodeConvertHelper
         * @param   {string}           sTimeCode     
         * @param   {System.Int64&}    lHour         
         * @param   {System.Int64&}    lMinute       
         * @param   {System.Int64&}    lSecond       
         * @param   {System.Int64&}    lFrame        
         * @param   {number}           dFrameRate    
         * @return  {void}                           帧数
         */
        timeCode2Format: function (sTimeCode, lHour, lMinute, lSecond, lFrame, dFrameRate) {
            var ftcCodes = sTimeCode.split(":");

            if (ftcCodes.length >= 4) {
                lHour.v = parseInt(ftcCodes[0]);
                lMinute.v = parseInt(ftcCodes[1]);
                lSecond.v = parseInt(ftcCodes[2]);
                lFrame.v = parseInt(ftcCodes[3]);
            } else {
                var ftcssf = ftcCodes[1].split(".");
                lHour.v = parseInt(ftcCodes[0]);
                lMinute.v = parseInt(ftcssf[0]);
                lSecond.v = parseInt(ftcssf[1]);
                lFrame.v = parseInt(ftcCodes[2]);
            }

            lHour.v = lHour.v >= 24 ? lHour.v - 24 : lHour.v;
            lMinute.v = lMinute.v >= 60 ? lMinute.v - 60 : lMinute.v;
            lSecond.v = lSecond.v >= 60 ? lSecond.v - 60 : lSecond.v;
            lFrame.v = lFrame.v >= Math.ceil(dFrameRate) ? lFrame.v - parseInt(Math.ceil(dFrameRate)) : lFrame.v;
        },

        //音频时间格式转换  2016-04-10 created by wangyu
        SecondToTimeString_audio: function (seconds, opts) {
            var hours = Math.floor(seconds / 3600);
            var minutes = Math.floor((seconds - 3600 * hours) / 60);
            var iseconds = Math.floor(seconds - (60 * minutes) - (3600 * hours));
            var i10Milliseconds = Math.floor((seconds * 100 - Math.floor(seconds) * 100));

            if (i10Milliseconds >= 100) {
                iseconds++;
                i10Milliseconds = i10Milliseconds - 100;
            }

            var hoursStr = hours < 10 ? "0" + hours : hours.toString();
            var minutesStr = minutes < 10 ? "0" + minutes : minutes.toString();
            var isecondsStr = iseconds < 10 ? "0" + iseconds : iseconds.toString();
            var i10MillisecondsStr = i10Milliseconds < 10 ? "0" + i10Milliseconds : i10Milliseconds.toString();
            return hoursStr + ":" + minutesStr + ":" + isecondsStr + (opts === 'exactTo_s' ? "" : (":" + i10MillisecondsStr));
        },
        //音频时码转百纳秒
        timeCode2L100Ns_audio: function (sTimeCode) {
            var ftcCodes = sTimeCode.split(":");
            var lHour = { v: 0 };
            var lMinute = { v: 0 };
            var lSecond = { v: 0 };
            var I10Milliseconds = { v: 0 };

            lHour.v = parseInt(ftcCodes[0]);
            lMinute.v = parseInt(ftcCodes[1]);
            lSecond.v = parseInt(ftcCodes[2]);
            if (ftcCodes[3] != undefined)
                I10Milliseconds.v = parseInt(ftcCodes[3]);

            var l100ns = 0;
            l100ns += lHour.v * 60 * 60;
            l100ns += lMinute.v * 60;
            l100ns += lSecond.v
            l100ns += I10Milliseconds.v / 100;
            l100ns = parseInt((l100ns * Math.pow(10.0, 7)).toFixed(0));

            return l100ns;
        },

        replaceAll: function (str, s1, s2) {
            return str.replaceAll(s1, s2);
        },
        div: function (a, b) {
            return this.hasValue(a) && this.hasValue(b) ? parseInt(a / b) : null;
        },
        hasValue: function (obj) {
            return (obj !== null) && (obj !== undefined);
        },

        second2L100Ns: function (seconds) {
            // 直接 *Math.pow(10,7)有精度问题，例如：314.28*10
            return parseInt((seconds).toFixed(7).replace('.', ''), 10)
        }
    };
    window.timecodeconvert = new timeCodeConvertHelper();
    return new timeCodeConvertHelper();
})();
window.TimeCodeConvert = TimeCodeConvert;

/***/ })
/******/ ]);