import React, { useState, useEffect, useRef } from 'react';
import { Typography } from 'antd';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { useSize } from 'ahooks';

interface TextProps {
  text: string;
  rows?: number;
}
const { Paragraph } = Typography;
const TextMore: React.FC<TextProps> = ({ text, rows = 2 }) => {
  const [visible, setVisible] = useState(false);
  const ref = useRef(null);
  const size = useSize(ref);
  return (
    <div style={{ position: 'relative', width: '100%'}}>
      <Paragraph
        ellipsis={
          visible
            ? false
            : {
                rows: rows,
                expandable: true,
                symbol: (
                  <a style={{ visibility: 'hidden' }}>
                    <DownOutlined />
                  </a>
                ),
              }
        }
        ref={ref}
      >
        {text}
        {visible && (
          <a onClick={() => setVisible(false)} style={{ marginLeft: 2 }}>
            <UpOutlined />
          </a>
        )}
      </Paragraph>
      {/* 默认情况判断下高度 */}
      {!visible && size?.height >= 22 && text.length>56 && (
        <div style={{ position: 'absolute', bottom: 0, right: 10 }}>
          <a
            onClick={() => {
              setVisible(true);
            }}
          >
            <DownOutlined />
          </a>
        </div>
      )}
    </div>
  );
};

export default TextMore;