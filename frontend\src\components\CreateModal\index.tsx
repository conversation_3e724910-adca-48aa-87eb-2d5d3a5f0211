import React from 'react';
import {Mo<PERSON>,Button} from 'antd';

interface CreateModalProps {
  modalVisible: boolean;
  title: string;
  footers:Array<any>
}

const CreateModal: React.FC<CreateModalProps> = (props) => {
  const {modalVisible, title, footers} = props;
  return (
    <Modal
      destroyOnClose
      title={title}
      visible={modalVisible}
      closable={false}
      footer={footers}
    >
      {props.children}
    </Modal>
  );
};

export default CreateModal;
