.metadata_basic_container {
  position: relative;
}
.metadata_basic_container .metadata_basic_header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 20px;
}
#root {
  height: 100%;
  overflow-y: auto;
}
.isAddKeys {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid;
  padding: 4px 20px;
  border-radius: 4px;
}
.Keysbox {
  border-top: 0px;
  border-left: 1px solid;
  border-right: 1px solid;
  border-bottom: 1px solid;
}
