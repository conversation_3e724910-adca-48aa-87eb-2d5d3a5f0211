import React, { useEffect, useState } from "react";
import { message, Spin } from "antd";

const ResourceChat = (
    { frameId, id, fullScreenState, changeFullScreenState }:
        { frameId: string, id: string, fullScreenState: boolean, changeFullScreenState: (state: boolean) => void }) => {

    const [chatLoading, setChatLoading] = useState(true); // 聊天loading

    function handleMessage(event: any) {
        const fullData = JSON.parse(event.data);
        changeFullScreenState(fullData.resourceullScreenState);
    }

    useEffect(() => {
        window.addEventListener("message", handleMessage, false);
        return () => {
            window.removeEventListener("message", handleMessage, false);
        }
    }, [])
    return (
        <div className="resource-chat-tabContent">
            <Spin spinning={chatLoading}>
                <iframe src={`/terminator/resource/chat?id=${id}&frameId=${frameId}`}
                    onLoad={() => { setChatLoading(false) }}
                    id='resource-chat-iframe' height="100%" width="100%" style={{ border: 'none', backgroundColor: `var(--active-bg-color)` }} />
            </Spin>
        </div>
    )
}

export default ResourceChat;