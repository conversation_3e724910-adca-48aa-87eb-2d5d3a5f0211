import React, { useEffect, useState } from 'react';
import { Modal, Tree, Empty, Spin, message, Checkbox } from 'antd';
import './index.less';
import contentListApis from '@/service/contentListApis';
interface PublishModalProps {
    currentPublish: any
    visible: boolean;
    onCancel: () => void;
    title?: string;
    setCheckedList?: (data: any) => void
}

const PublishRescourcemodal: React.FC<PublishModalProps> = ({ visible, onCancel, currentPublish, title, setCheckedList }) => {
    const [checkedKeys, setCheckedKeys] = useState<string[]>([]);
    const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
    const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
    const [treeData, setTreeData] = useState<any[]>([]);
    const [loading, setLoading] = useState<boolean>(false); // 加载状态

    useEffect(() => {
        setLoading(true);
        contentListApis.getAllConfig({ type: 1 }).then((res: any) => {
            if (res?.status === 200) {
                setLoading(false);
                let treeDataTemp = loopData(res?.data, false)
                setTreeData(treeDataTemp)
            }
        })
    }, []);

    // 整理数据
    const loopData = (data: any, checkable?: boolean) => {
        return data?.map((item: any) => {
            return {
                key: item?.code,
                title: item.name,
                resourceList: item.resourceList,
                checkable: checkable, // 第一层禁用复选框
                children: loopData(item?.childThematicConfig, true),
            }
        })
    }

    // 更新树数据
    function getCheckedItems(treeData: any, checkedKeys: any) {
        const result: any = [];
        const traverse = (nodes: any) => {
            nodes.forEach((node: any) => {
                if (checkedKeys.includes(node.key) && !node?.children?.length) {
                    result.push(node);
                }
                if (node.children) {
                    traverse(node.children);
                }
            });
        };
        traverse(treeData);
        return result;
    }

    const handleOk = () => {
        let params: any = []
        let checkedItems: any = getCheckedItems(treeData, checkedKeys)
        // if (title) {
        params = {
            topics: checkedItems?.map((item: any) => {
                return {
                    key: item.key,
                    value: item.title,
                }
            }),
            publishResourceDetailsDtos: currentPublish
        }
        setConfirmLoading(true)
        contentListApis.resourceTopic(params).then((res: any) => {
            if (res?.success) {
                setConfirmLoading(false)
                onCancel()
                message.success('发起成功')
                let src = window.parent.location.pathname + window.parent.location.hash;
                console.log(src, 'src')
                if (src.includes("/basic/rmanList") || src.includes("/basic/rmanCenterList")) {
                    const randomInteger = Math.floor(Math.random() * 1000000) + 1;
                    sessionStorage.setItem('publishResource', randomInteger.toString());
                    setCheckedList && setCheckedList([])
                }
            } else {
                message.error(res?.message || '发起失败')
                setConfirmLoading(false)
            }
        })
        // } else {
        //     currentPublish?.forEach((ele: any) => {
        //         checkedItems?.forEach((item: any) => {
        //             params.push(
        //                 {
        //                     courseClassificationCode: item.key,
        //                     courseId: ele.contentId_,
        //                     order: item?.resourceList?.length ? item?.resourceList?.length : 1
        //                 }
        //             )
        //         })
        //     })
        //     setConfirmLoading(true)
        //     contentListApis.createCourseConfig(params).then((res: any) => {
        //         if (res?.status === 200) {
        //             setConfirmLoading(false)
        //             onCancel()
        //             message.success('发布成功')
        //         } else {
        //             message.error(res?.message || '发布失败')
        //             setConfirmLoading(false)
        //         }
        //     })
        // }
    };

    return (
        <Modal
            title={"资源发布至专题"}
            open={visible}
            onCancel={onCancel}
            onOk={handleOk}
            width={450}
            className="publish-resource-modal"
            destroyOnClose
            confirmLoading={confirmLoading}
            okButtonProps={{ disabled: checkedKeys?.length === 0 }} // 禁用确定按钮

        >
            <div className='tree-container'>
                {
                    treeData?.length > 0 ? <Spin spinning={loading}>
                        <Tree
                            checkable
                            expandedKeys={expandedKeys}
                            onCheck={(checked: any) => setCheckedKeys(checked)}
                            onExpand={(checkedKeys: any) => setExpandedKeys(checkedKeys)}
                            checkedKeys={checkedKeys}
                            treeData={treeData}
                        />
                    </Spin> : <Empty description="暂无资源目录" />
                }
            </div>
        </Modal>
    );
};

export default PublishRescourcemodal;

