import React from 'react';

export interface TreeData {
  name: string;
  key: string;
  selected?: boolean;
  isPerson?: boolean;
  children?: Array<TreeData>;
}

export interface TreeMenuProps {
  selectedKey: string;
  onItemClick: (key: string) => void;
  sourceData: Array<TreeData>;
  operation: React.ReactElement;
}

export interface TreeItemProps {
  title: string;
  isPerson?: boolean;
  isExpand?: boolean;
  isSelected?: boolean;
  afterDom?: React.ReactElement | null;
  onClick: Function;
}

export interface DataNode {
  title: string;
  key: string;
  isLeaf?: boolean;
  children?: DataNode[];
}