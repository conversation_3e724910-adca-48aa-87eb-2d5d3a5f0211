@header-heigth: 70px;
@media screen and (max-width:768px){
  body {
    width: 100%;
    height: 100%;
    min-width: unset !important;
    .content_view{
      .help_btn{
        display: none;
      }
    }
    .basic_height{
      height: 100% !important;
      .uf-header-wrapper{
        padding: 0 15px !important;
      }
      .basic_content{
        height: calc(100% - 52px) !important;
      }
    }
  }
}
body {
  min-width: 1280px;
  //overflow: auto;
}

.basic_height {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  -webkit-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  -ms-user-select: none;

  .basic_top {
    height: @header-heigth;
    width: 100%;
    display: flex;
    align-items: center;
    background-color: var(--second-color);
    padding: 0 32px;
    box-sizing: border-box;
    justify-content: space-between;
    flex-shrink: 0;

    .basic_left {
      display: flex;
      font-size: 16px;
      color: rgba(255, 255, 255, 1);

      .administration {
        padding: 0 17px;
        height: @header-heigth;
        line-height: @header-heigth;
        cursor: pointer;
      }

      .administration1 {
        background-color: #e02d12;
        margin-left: 10px;
      }
    }

    .logo {
      display: flex;
      font-size: 21px;
      color: rgba(255, 255, 255, 1);
      align-items: center;

      img {
        height: 30px;
        margin-right: 6px;
      }
    }

    .link {
      display: flex;
      align-items: center;
      font-size: 16px;
      color: rgba(255, 255, 255, 1);

      .linkbox {
        display: flex;
        align-items: center;
        cursor: pointer;
        color: #fff;
        padding: 0 7px;

        &:hover {
          color: var(--active-bg-color);
        }

        span {
          margin-left: 5px;
        }

        .icon {
          font-size: 12px;
        }
      }

      .divider {
        height: 16px;
        width: 0;
        margin: 0;
        border-left: 1px solid #ffffff;
      }

      img {
        width: 17px;
        // height: ;
      }

      > div {
        margin: 0 12px;
      }
    }
  }

  .basic_content {
    // flex: 1;
    // height: calc(100% - 100vh);
    // height: 80%;
    box-sizing: border-box;
    height: calc(~"100vh - @{header-heigth}");
    flex: 1;
  }
}



.pc_show{
  display: inherit!important;
}
.mobile_show{
  display: none!important;
}

@media screen and (max-width: 768px){
  .mobile_show{
      display: inherit!important;
  }
  .pc_show{
      display: none!important;
  }
}