{"private": true, "scripts": {"start": "node theme && cross-env NODE_OPTIONS=--openssl-legacy-provider umi dev", "build": "cross-env NODE_OPTIONS=--openssl-legacy-provider umi build", "postinstall": "umi generate tmp", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "test": "umi-test", "test:coverage": "umi-test --coverage", "analyze": "node theme && cross-env NODE_OPTIONS=--openssl-legacy-provider ANALYZE=1 umi dev"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "dependencies": {"@antv/g6": "4.8.24", "@antv/layout": "^0.3.23", "@antv/x6": "1.34.1", "@antv/x6-react-components": "1.1.15", "@antv/x6-react-shape": "1.6.1", "@onlyoffice/document-editor-react": "^1.1.0", "@umijs/preset-react": "1.x", "@umijs/test": "^3.5.17", "ahooks": "^3.8.4", "antd-img-crop": "^4.23.0", "antd-theme-generator": "^1.2.8", "array-move": "^3.0.1", "axios": "^1.7.2", "compression-webpack-plugin": "5.0.1", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "dompurify": "^3.2.1", "echarts": "^5.3.3", "echarts-for-react": "^3.0.2", "echarts-wordcloud": "^2.0.0", "file-saver": "^2.0.5", "jquery": "^3.5.1", "js-base64": "^3.4.5", "lint-staged": "^10.0.7", "lodash": "^4.17.21", "mockjs": "^1.0.0", "pdfjs-dist": "^4.4.168", "prettier": "^1.19.1", "qrcode.react": "^3.1.0", "react": "^16.12.0", "react-cropper": "^2.3.3", "react-dom": "^16.12.0", "react-draggable": "^4.4.6", "react-pdf": "^9.1.0", "react-sortable-hoc": "^1.11.0", "react-sortablejs": "2.0.11", "spark-md5": "^3.0.2", "streamsaver": "^2.0.6", "tslib": "^2.6.0", "umi": "^3.5.17", "video.js": "^7.8.4", "weixin-js-sdk": "^1.6.0", "xmind-embed-viewer": "^1.2.0", "yorkie": "^2.0.0"}, "devDependencies": {"@types/jquery": "^3.5.1", "@types/react-pdf": "^4.0.5", "@types/video.js": "^7.3.10", "cross-env": "^7.0.3", "tslint": "^6.1.3", "tslint-config-prettier": "^1.18.0", "tslint-react": "^5.0.0", "typescript": "^3.9.7"}, "resolutions": {"@ant-design/cssinjs": "1.5.6", "lodash": "^4.17.21"}}