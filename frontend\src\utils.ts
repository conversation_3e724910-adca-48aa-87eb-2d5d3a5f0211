import { entityType } from './types/entityTypes';
import {cloneDeep} from 'lodash';
import entityApis from './service/entityApis';
let asyncLoadedScripts: any = {};
let asyncLoadedScriptsCallbackQueue: any = {};
import SparkMD5 from 'spark-md5';
import { Modal } from 'antd';
export const copyObject: <T>(obj: T) => T = oldObj => {
  if (oldObj instanceof Object) {
    // return JSON.parse(JSON.stringify(oldObj)); //这种强转会使文件丢失orignalFile属性
    return cloneDeep(oldObj);
  } else {
    return oldObj;
  }
};

export const getType = (sType: string): entityType => {
  switch (sType) {
    case 'biz_sobey_video':
      return 'video';
    case 'biz_sobey_audio':
      return 'audio';
    case 'biz_sobey_picture':
      return 'picture';
    case 'biz_sobey_document':
      return 'document';
    case 'folder':
      return 'folder';
    case 'biz_sobey_course':
      return 'course';
    case 'biz_sobey_other':
      return 'other';
    default:
      return null;
  }
};

// 防抖
export const debounce = (fn: () => any, delay: number): (() => void) => {
  let timer: NodeJS.Timeout | null = null;
  return () => {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(fn, delay);
  };
};

export const getScriptDomFromUrl = (url: string, rel?: string) => {
  let dom;
  if (/.+\.js$/.test(url)) {
    dom = document.createElement('SCRIPT');
    dom.setAttribute('type', 'text/javascript');
    dom.setAttribute('src', url);
  } //css
  else {
    dom = document.createElement('link');
    dom.href = url;
    dom.type = 'text/css';
    dom.rel = rel || 'stylesheet';
  }
  return dom;
};

/**
 * 异步加载script
 * @param url
 * @param callback
 */
export const asyncLoadScript = (
  url: string,
  callback?: (() => void) | undefined,
): Promise<any> => {
  return new Promise(resolve => {
    if (asyncLoadedScripts[url] !== undefined) {
      // 已加载script标签
      if (callback && typeof callback === 'function') {
        if (asyncLoadedScripts[url] === 0) {
          // 未执行首个script标签的回调
          if (!asyncLoadedScriptsCallbackQueue[url]) {
            asyncLoadedScriptsCallbackQueue[url] = [];
          }
          asyncLoadedScriptsCallbackQueue[url].push(callback);
        } else {
          callback.apply(window, []);
        }
      }
      resolve();
      return;
    }
    asyncLoadedScripts[url] = 0;
    const scriptDom: any = getScriptDomFromUrl(url);
    if (scriptDom.readyState) {
      scriptDom.onreadystatechange = () => {
        if (
          scriptDom.readyState === 'loaded' ||
          scriptDom.readyState === 'complete'
        ) {
          scriptDom.onreadystatechange = null;
          asyncLoadedScripts[url] = 1;
          resolve();
          if (callback && typeof callback === 'function') {
            callback.apply(window, []);
          }
          if (asyncLoadedScriptsCallbackQueue[url]) {
            for (
              let i = 0, j = asyncLoadedScriptsCallbackQueue[url].length;
              i < j;
              i++
            ) {
              asyncLoadedScriptsCallbackQueue[url][i].apply(window, []);
            }
            asyncLoadedScriptsCallbackQueue[url] = undefined;
          }
        }
      };
    } else {
      scriptDom.onload = () => {
        asyncLoadedScripts[url] = 1;
        resolve();
        if (callback && typeof callback === 'function') {
          callback.apply(window, []);
        }
        if (asyncLoadedScriptsCallbackQueue[url]) {
          for (
            let i = 0, j = asyncLoadedScriptsCallbackQueue[url].length;
            i < j;
            i++
          ) {
            asyncLoadedScriptsCallbackQueue[url][i].apply(window, []);
          }
          asyncLoadedScriptsCallbackQueue[url] = undefined;
        }
      };
    }
    document.getElementsByTagName('head')[0].appendChild(scriptDom);
  });
};

export const asyncLoadScriptArr = (scriptArr: string[]) => {
  let promises: any[] = [];
  const loop = (i: number) => {
    promises.push(
      new Promise((resolve, reject) => {
        asyncLoadScript(scriptArr[i], () => {
          resolve();
        });
      }),
    );
  };
  for (let i = 0; i < scriptArr.length; i++) {
    loop(i);
  }
  return Promise.all(promises);
};

export const changesize = (limit: any) => {
  let item = Number(limit);
  let size;
  if (item < 0.1 * 1024) {
    //小于0.1KB，则转化成B
    size = item.toFixed(2) + 'B';
  } else if (item < 0.1 * 1024 * 1024) {
    //小于0.1MB，则转化成KB
    size = (item / 1024).toFixed(2) + 'KB';
  } else if (item < 0.1 * 1024 * 1024 * 1024) {
    //小于0.1GB，则转化成MB
    size = (item / (1024 * 1024)).toFixed(2) + 'MB';
  } else {
    //其他转化成GB
    size = (item / (1024 * 1024 * 1024)).toFixed(2) + 'GB';
  }
  let sizeStr = size + ''; //转成字符串
  let index = sizeStr.indexOf('.'); //获取小数点处的索引
  let dou = sizeStr.substr(index + 1, 2); //获取小数点后两位的值
  if (dou === '00') {
    //判断后两位是否为00，如果是则删除00
    return sizeStr.substring(0, index) + sizeStr.substr(index + 3, 2);
  }
  return size;
};
/**
 * 生成唯一id
 * @param length
 */
export const getId = (length: number = 8) =>
  Number(
    Math.random()
      .toString()
      .substr(3, length) + Date.now(),
  ).toString(36);
/**
 * 百纳秒转时间
 * @param ns
 */
export const l100Ns2Tc$1 = (ns: number,framerate?:number): string => {
  return ns ? (window as any).timecodeconvert.l100Ns2Tc$1(ns,framerate) : '';
};

/**
 * 线程睡眠
 * @param ns
 */
export function sleep(ns: number) {
  return new Promise(resolve =>
    setTimeout(() => {
      resolve(null);
    }, ns),
  );
}

/**
 * 加载icon
 * @param url
 */
export const loadFavicon = (url: string) => {
  const icon = document.createElement('link');
  icon.href = url;
  icon.type = 'image/x-icon';
  icon.rel = 'shortcut icon';
  document.getElementsByTagName('head')[0].appendChild(icon);
};
export const optionType_ = ['单选', '多选', '填空', '主观', '判断'];

/**************************颜色处理***********************************/
//hex颜色转rgb颜色
export function HexToRgb(str: string) {
  const r = /^#?[0-9A-Fa-f]{6}$/;
  //test方法检查在字符串中是否存在一个模式，如果存在则返回true，否则返回false
  if (!r.test(str)) return [];
  //replace替换查找的到的字符串
  str = str.replace('#', '');
  //match得到查询数组
  let hxs: any = str.match(/../g);
  //alert('bf:'+hxs)
  for (let i = 0; i < 3; i++) hxs[i] = parseInt(hxs[i], 16);
  //alert(parseInt(80, 16))
  //console.log(hxs);
  return hxs;
}

//GRB颜色转Hex颜色
function RgbToHex(a: number, b: number, c: number) {
  const r = /^\d{1,3}$/;
  if (!r.test(a + '') || !r.test(b + '') || !r.test(c + '')) return '';
  const hexs = [a.toString(16), b.toString(16), c.toString(16)];
  for (let i = 0; i < 3; i++) if (hexs[i].length == 1) hexs[i] = '0' + hexs[i];
  return '#' + hexs.join('');
}

//得到hex颜色值为color的加深颜色值，level为加深的程度，限0-1之间
export function getDarkColor(color: string, level: number) {
  const r = /^#?[0-9A-F]{6}$/;
  if (!r.test(color)) return '';
  const rgbc = HexToRgb(color);
  //floor 向下取整
  for (let i = 0; i < 3; i++) rgbc[i] = Math.floor(rgbc[i] * (1 - level));
  return RgbToHex(rgbc[0], rgbc[1], rgbc[2]);
}

//得到hex颜色值为color的减淡颜色值，level为加深的程度，限0-1之间
export function getLightColor(color: string, level: number) {
  const r = /^#?[0-9A-F]{6}$/;
  if (!r.test(color)) return '';
  const rgbc = HexToRgb(color);
  for (let i = 0; i < 3; i++)
    rgbc[i] = Math.floor((255 - rgbc[i]) * level + rgbc[i]);
  return RgbToHex(rgbc[0], rgbc[1], rgbc[2]);
}

export function getActiveBgColorFade(color: string, opacity: number) {
  const r = /^#?[0-9A-F]{6}$/;
  if (!r.test(color)) return '';
  // 转换为RGB值
  const rgb = HexToRgb(color);
  // 如果没有传opacity参数,默认使用0.2(20%透明度)
  const alpha = opacity === undefined ? 0.2 : opacity;
  // 返回rgba格式
  return `rgba(${rgb[0]}, ${rgb[1]}, ${rgb[2]}, ${alpha})`;
}   
//对象去重
export function getUnique(orignal: Array<any>, key?: any) {
  let temp: any = [];
  let repeat: any = [];
  if (!key) {
    orignal.forEach((item: any) => {
      if (!temp.includes(item)) {
        temp.push(item);
      }
    });
  } else {
    orignal.forEach((item: any) => {
      let flag = false;
      temp.forEach((item_: any) => {
        if (item_[key] === item[key]) {
          flag = true;
          repeat.push(item);
        }
      });
      if (!flag) {
        temp.push(item);
      }
    });
  }
  return {
    temp,
    repeat,
  };
}
//毫秒转换
export function timeTransfer(data:number){
  const temp = new Date(data);
  const year = temp.getFullYear();
  const month = temp.getMonth()+1;
  const day = temp.getDate();
  const hours = temp.getHours();
  const minutes = temp.getMinutes();
  const sec = temp.getSeconds();
  return `${year}-${month<10?'0'+month:month}-${day<10?'0'+day:day} ${hours<10?'0'+hours:hours}:${minutes<10?'0'+minutes:minutes}:${sec<10?'0'+sec:sec}`
}
//字节单位智能转换
export function byteTransfer(bytes:number){
  if (bytes === 0) return '0 B';
    var k = 1024, // or 1024
        sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'],
        i = Math.floor(Math.log(bytes) / Math.log(k));

   return (bytes / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i];
}

/****************************颜色处理结束**************************************/
//tit:keywords name：检索来源
export function searchKeywords(tit: string,name:string){
  const word_obj:any = JSON.parse(window.localStorage.getItem('searchKeyWord') || '{}');
  const index = tit?.indexOf(word_obj?.word);
  const beforeStr = tit?.substr(0, index);
  const afterStr = tit?.substr(index + word_obj?.word?.length);
  if(index>-1){
    // console.log('searchKeyWord',tit,name)
    window.localStorage.setItem('searchKeyWord',JSON.stringify({
      ...word_obj,
      [name]:true
    }));
  }
  return {
    index,
    tit,
    beforeStr,
    word:word_obj?.word,
    afterStr,
  };
}
//随机生成guid
export function getGuid() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    var r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}
/**
 * 关键词定位视频的出入点
 * @param word 需要的匹配词
 * @param voice  语音信息
 */
export function getVideoTime(word:string,voice:any){
  let temp = {text:word,_in:undefined,_out:undefined}
  if(voice.length===0) return temp;
  for(let i=0;i<voice.length;i++){
    if(voice[i].text.indexOf(word)>-1){
      temp._in = voice[i]._in;
      temp._out = voice[i]._out;
      break;
    }
  }
  return temp;
}
/**
 * 权限值二进制拆分
 */
export function operateCode(code: number){
    // 采用后端给的内容权限标识 枚举值 8：删除 4：执行(复制、上传) 2：编辑 1：可读
    let temp: Array<number> = [];
    switch (code) {
      case 1: temp = [1]; break;
      case 2: temp = [2]; break;
      case 3: temp = [1, 2]; break;
      case 4: temp = [4]; break;
      case 5: temp = [1, 4]; break;
      case 6: temp = [2, 4]; break;
      case 7: temp = [1, 2, 4]; break;
      case 8: temp = [8]; break;
      case 9: temp = [1, 8]; break;
      case 10: temp = [2, 8]; break;
      case 11: temp = [1, 2, 8]; break;
      case 12: temp = [4, 8]; break;
      case 13: temp = [1, 4, 8]; break;
      case 14: temp = [2, 4, 8]; break;
      case 15: temp = [1, 2, 4, 8]; break;
      default: temp = []; break;
    }
    // console.log('operateCode', temp)
    return temp
}
/**
 * 平滑滚动
 * @param dom 滚动节点
 * @param dis 距离
 */
export function updateScrollPosition(obj:any){
  if(!obj.dom) return; //这里是为了防止requestAnimationFrame回调函数带的ID
  if(!window.requestAnimationFrame){
    (window.requestAnimationFrame as any) = function(fn:any){
      setTimeout(fn,17)
    }
  }
  window.requestAnimationFrame(updateScrollPosition);
  //连续同步滚动时不能使用平滑滚动属性 在mac端会有抖动bug;
  obj.dom.scrollTo(0,obj.dis);
}

/**
 * 计算文件基于文件名、文件大小和修改时间等信息的 MD5 值
 * @param {File} file 文件对象
 * @returns {string} MD5 值
 */
export function computeFileInfoMD5(file: File) {
  const fileInfo = `${file.name}${file.size}${file.lastModified}`;
  const spark = new SparkMD5();
  spark.append(fileInfo);
  return spark.end();
}
/**
 * 敏感词检测
 * @param value 要校验的值
 * @param text 检验的中文
 * @param successCallback 校验成功的回调 
 * @param failedCallback  失败回调
 */

export const getSensitiveWord = (value: string, text: string, successCallback: () => void, failedCallback?: () => void) => {
  if (value !== "" && value != undefined) {
    return entityApis.querySensitiveWord(value).then((res: any) => {
      if (res.statusCode === 200 && res.data?.length === 0) {
        return successCallback();
      } else if (res.data?.length > 0) {
        return new Promise((resolve) => {
          Modal.confirm({
            content: `${text}包含敏感词：${Array.from(new Set(res.data.map((item: any) => item.matchKeyword))).join("、")}，确认继续执行吗？`,
            onOk() {
              resolve(true);
            },
            onCancel() {
              resolve(false);
            }
          })
        }).then((confirm?: any) => {
          if (confirm) {
            return successCallback();
          } else {
            return failedCallback?.();
          }
        })
        
      }
    })
  } else {
    return Promise.resolve(successCallback())
  }
}
export const convertVttToJson = (vttString: any) => {
  return new Promise((resolve, reject) => {
  var current = {}
  var sections = []
  var start = false;
  var vttArray = vttString.split('\n');
   vttArray.forEach((line, index) => {
    if (line.replace(/<\/?[^>]+(>|$)/g, "") === " "){
    } else if (line.replace(/<\/?[^>]+(>|$)/g, "") == "") {
    } else if (line.indexOf('-->') !== -1 ) {
      start = true;

      if (current.start) {
        sections.push(clone(current))
      }

      current = {
        start: timeString2ms(line.split("-->")[0].trimRight().split(" ").pop()),
        end: timeString2ms(line.split("-->")[1].trimLeft().split(" ").shift()),
        part: ''
      }
    } else if (line.replace(/<\/?[^>]+(>|$)/g, "") === ""){
    } else if (line.replace(/<\/?[^>]+(>|$)/g, "") === " "){
    } else {
      if (start){
        if (sections.length !== 0) {
          if (sections[sections.length - 1].part.replace(/<\/?[^>]+(>|$)/g, "") === line.replace(/<\/?[^>]+(>|$)/g, "")) {
          } else {
            if (current.part.length === 0) {
              current.part = line
            } else {
              current.part = `${current.part} ${line}`
            }
            // If it's the last line of the subtitles
            if (index === vttArray.length - 1) {
              sections.push(clone(current))
            }
          }
        } else {
          current.part = line
          sections.push(clone(current))
          current.part = ''
        }
      }
    }
  })

  current = []

  var regex = /(<([0-9:.>]+)>)/ig
  sections.forEach(section => {
    let strs = section.part.split()
    var results = strs.map(function(s){
        return s.replace(regex, function(n){
          return n.split('').reduce(function(s,i){ return `==${n.replace("<", "").replace(">", "")}` }, 0)
        })
    });
    let cleanText = results[0].replace(/<\/?[^>]+(>|$)/g, "");
    let cleanArray = cleanText.split(" ")
    let resultsArray = [];
    cleanArray.forEach(function(item){
      if (item.indexOf('==') > -1) {
        var pair = item.split("==")
        var key = pair[0]
        var value = pair[1]
        if(key == "" || key == "##") {
          return;
        }
        resultsArray.push({
          word: cleanWord(item.split("==")[0]),
          time: timeString2ms(item.split("==")[1]),
        })
      } else {
        resultsArray.push({
          word: cleanWord(item),
          time: undefined,
        })
      }
    })
    section.words = resultsArray;
    section.part = section.part.replace(/<\/?[^>]+(>|$)/g, "")
  })
    resolve(sections);
  })
}

function timeString2ms(a,b){// time(HH:MM:SS.mss) // optimized
 return a=a.split('.'), // optimized
  b=a[1]*1||0, // optimized
  a=a[0].split(':'),
  b+(a[2]?a[0]*3600+a[1]*60+a[2]*1:a[1]?a[0]*60+a[1]*1:a[0]*1)*1e3 // optimized
}

// removes everything but characters and apostrophe and dash
function cleanWord(word) {
  return word.replace(/[^0-9a-z'-]/gi, '').toLowerCase()
}

function clone(obj) {
    if (null == obj || "object" != typeof obj) return obj;
    var copy = obj.constructor();
    for (var attr in obj) {
        if (obj.hasOwnProperty(attr)) copy[attr] = obj[attr];
    }
    return copy;
}

// blob下载
export function downloadBlob(res: any, name?: string) {
  return new Promise((resolve, reject) => {
    if (res && res.blob) {
      res.blob().then((blob: Blob) => {
        // 根据响应头获取文件名
        function getFileNameFromResponse(contentDisposition: any) {
          var matchResult = /filename\*?=[^;]*\bUTF-8''([^;]*)/.exec(contentDisposition);
          if (matchResult != null && matchResult[1]) {
            return decodeURIComponent(matchResult[1].replace(/['"]/g, ""));
          }
          return "未命名";
        }
        let fileName = name || getFileNameFromResponse(res.headers.get('content-disposition'));
        // 创建一个临时链接并模拟点击进行下载
        let a = document.createElement('a');
        let url = window.URL.createObjectURL(blob);
        a.href = url
        a.download = fileName;
        a.click();
        window.URL.revokeObjectURL(url);
        resolve(fileName)
      }).catch(() => {
        reject()
      })
    } else {
      console.error("下载失败");
      reject()
    }
  })
}

