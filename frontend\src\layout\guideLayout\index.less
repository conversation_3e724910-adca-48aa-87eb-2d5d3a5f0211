.content_view{
    position: relative;
    width:100%;
    height: 100%;

    .help_btn {
        position: fixed;
        right: 0;
        bottom: 40px;
        width: 70px;
        height: 65px;
        background: #FFFFFF;
        // box-shadow: 0px 2px 8px 2px rgb(129 129 129 / 6%);
        border-radius: 3px;
        border: 1px solid #F1F1F1;
        text-align: center;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        font-weight: 400;
        color: #868686;
        z-index: 9999;
        cursor: pointer;
        -moz-user-select: none; /* 火狐浏览器 */
        -webkit-user-select: none; /* Webkit 内核浏览器 (Chrome/Safari) */
        -ms-user-select: none; /* IE10+ */
        user-select: none; /* 标准语法 */

        .help_icon {
            background-color: var(--primary-color);
            width: 25px;
            height: 18px;
            line-height: 18px;
            color: #fff;
            border-radius: 4px;
            position: relative;
            margin-bottom: 8px;
            font-weight: bold;

            &::before {
                content: '';
                position: absolute;
                bottom: -6px;
                border-top: 6px solid transparent;
                border-left: 6px solid var(--primary-color);
                border-bottom: 6px solid transparent;
                left: 0;
            }
        }
    }


    .helpperview{
        position: fixed;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        z-index: 9999;
        background-color: #fff;


        .content_perview{
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            background-color: rgba(0, 0, 0, 0.45);

            .imgdom{
                height: 100%;
                width:100%;
            }

            .options_view{
                position: absolute;
                width: 20%;
                left: 40%;
                height:50px;
                bottom: 30px;

                display: flex;
                align-items: center;
                justify-content: space-evenly;
            }
        }
    }

}
.npu-app-container {
  background: url(~@/images/myHeader/app-bg.jpg) no-repeat;
  background-size:  100% auto;
  padding-top: 4.375rem;
}

.helpDrawer_view{

    .desc_view{
        width: 100%;
        margin-bottom: 30px;

        .desc_span{
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #525252;
            line-height: 22px;
            margin-bottom: 30px;
        }
    }

    .ant-drawer-body{
        position: relative;
    }

    .zhiyin{
        width: 100%;
        background-color: #f5f5f5;
        margin-bottom: 10px;
        border-radius: 10px;

        .item_view{
            width: 95%;
            margin-left: 5%;
            display: flex;
            align-items: center;

            img{
                width: 20px;
                height: 20px;
            }


            span{
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #549CFF;
                line-height: 35px;
                margin-left: 5px;
                cursor: pointer;
            }

        }
    }

    .content_box{
        position: relative;
        height:90%;
        width: 100%;
        padding-right: 10px;
        overflow-y: auto;
        // 滚动条样式优化
        &::-webkit-scrollbar {
            width: 7px;
            height: 7px;
        }
        &::-webkit-scrollbar-thumb {
            border-radius: 10px;
            background-color: rgba(0, 0, 0, 0.2);
        }
        &::-webkit-scrollbar-track {
            border-radius: 10px;
            background-color: rgba(0, 0, 0, 0.1);
        }
    }

    .mulu{
        width: 100%;
        background-color: #f5f5f5;
        margin-bottom: 10px;
        padding-bottom: 10px;
        padding-top: 10px;
        border-radius: 10px;

        .name_span{
            margin-left: 10px;
            line-height: 35px;
        }

        .item_view{
            width: 95%;
            margin-left: 5%;

            img{
                width: 20px;
                height: 20px;
            }

            span{
                width: 100%;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #549CFF;
                line-height: 25px;
                margin-left: 5px;
                cursor: pointer;

                &:hover{
                    // 下划线
                    text-decoration: underline;
                }
            }

        }
    }

    .noshow{
        position: absolute;
        bottom: 20px;
        width: 85%;
        text-align: center;
    }
}
.hide-header {
    padding-top: 0;
    height: 100%;
}
