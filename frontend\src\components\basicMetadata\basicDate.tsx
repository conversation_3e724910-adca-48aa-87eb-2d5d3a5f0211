import React, { FC } from 'react'
import { Form, DatePicker } from 'antd'
import { IBasicItemProps } from './basicMetadata'

const BasicDate: FC<IBasicItemProps> = (props) => {
    return (
        <Form.Item
            label={props.item.alias}
            name={props.item.fieldName}
            rules={[{ required: props.item.isReadOnly || !props.edit ? false : props.item.isMustInput }]}
        >
            {
                props.item.isReadOnly || !props.edit ? props.item.value : <DatePicker format="YYYY-MM-DD HH:mm:ss" showTime />
            }
        </Form.Item>
    )
}

export default BasicDate