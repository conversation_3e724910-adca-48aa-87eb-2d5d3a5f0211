{"version": 3, "sources": ["webpack:///webpack/bootstrap 6b1eb6df9256481b989b?cf2d", "webpack:///./node_modules/core-js/library/modules/_global.js?ecab", "webpack:///./node_modules/core-js/library/modules/_wks.js?752c", "webpack:///./node_modules/core-js/library/modules/_core.js?15e0", "webpack:///./node_modules/core-js/library/modules/_an-object.js?efb3", "webpack:///./node_modules/core-js/library/modules/_hide.js?849c", "webpack:///./node_modules/core-js/library/modules/_object-dp.js?7af0", "webpack:///./node_modules/core-js/library/modules/_is-object.js?12a8", "webpack:///./node_modules/core-js/library/modules/_descriptors.js?f84d", "webpack:///./node_modules/core-js/library/modules/_has.js?0f62", "webpack:///./node_modules/core-js/library/modules/_to-iobject.js?4dc4", "webpack:///./node_modules/core-js/library/modules/_library.js?3b88", "webpack:///./node_modules/core-js/library/modules/_export.js?90cd", "webpack:///./node_modules/core-js/library/modules/_iterators.js?fdb4", "webpack:///./node_modules/core-js/library/modules/_cof.js?47d3", "webpack:///./node_modules/core-js/library/modules/_ctx.js?f993", "webpack:///./node_modules/core-js/library/modules/_a-function.js?94e9", "webpack:///./node_modules/core-js/library/modules/_fails.js?4bcd", "webpack:///./node_modules/core-js/library/modules/_property-desc.js?5fc0", "webpack:///./node_modules/core-js/library/modules/_uid.js?dc4a", "webpack:///./node_modules/core-js/library/modules/_set-to-string-tag.js?7ba9", "webpack:///./src/common/commonUtil.js?9c0b", "webpack:///./src/common/globalUtil.js?7754", "webpack:///./node_modules/babel-runtime/core-js/json/stringify.js?9af1", "webpack:///./node_modules/core-js/library/modules/_to-integer.js?52e1", "webpack:///./node_modules/core-js/library/modules/_defined.js?e768", "webpack:///./node_modules/core-js/library/modules/_dom-create.js?38dd", "webpack:///./node_modules/core-js/library/modules/_to-primitive.js?3263", "webpack:///./node_modules/core-js/library/modules/_object-keys.js?964b", "webpack:///./node_modules/core-js/library/modules/_shared-key.js?6b1d", "webpack:///./node_modules/core-js/library/modules/_shared.js?7bc0", "webpack:///./node_modules/core-js/library/modules/_enum-bug-keys.js?c677", "webpack:///./node_modules/core-js/library/modules/_new-promise-capability.js?a804", "webpack:///./node_modules/core-js/library/modules/_wks-ext.js?2a1e", "webpack:///./node_modules/core-js/library/modules/_wks-define.js?72b9", "webpack:///./node_modules/core-js/library/modules/_object-pie.js?3692", "webpack:///./src/common/httpUtil.js?fd45", "webpack:///./node_modules/core-js/library/modules/es6.string.iterator.js?cd04", "webpack:///./node_modules/core-js/library/modules/_iter-define.js?bc80", "webpack:///./node_modules/core-js/library/modules/_ie8-dom-define.js?49f0", "webpack:///./node_modules/core-js/library/modules/_redefine.js?f3cd", "webpack:///./node_modules/core-js/library/modules/_object-create.js?6286", "webpack:///./node_modules/core-js/library/modules/_object-keys-internal.js?21b8", "webpack:///./node_modules/core-js/library/modules/_to-length.js?4111", "webpack:///./node_modules/core-js/library/modules/_html.js?44f2", "webpack:///./node_modules/core-js/library/modules/_to-object.js?b01d", "webpack:///./node_modules/core-js/library/modules/web.dom.iterable.js?fad3", "webpack:///./node_modules/core-js/library/modules/_classof.js?458f", "webpack:///./node_modules/core-js/library/modules/_species-constructor.js?b7cc", "webpack:///./node_modules/core-js/library/modules/_task.js?2f8d", "webpack:///./node_modules/core-js/library/modules/_perform.js?74d0", "webpack:///./node_modules/core-js/library/modules/_promise-resolve.js?7c95", "webpack:///./node_modules/core-js/library/modules/_object-gops.js?d644", "webpack:///./node_modules/core-js/library/modules/_object-gopn.js?9f44", "webpack:///./node_modules/babel-runtime/core-js/promise.js?fff1", "webpack:///./node_modules/core-js/library/fn/promise.js?5398", "webpack:///./node_modules/core-js/library/modules/_string-at.js?87ae", "webpack:///./node_modules/core-js/library/modules/_iter-create.js?f785", "webpack:///./node_modules/core-js/library/modules/_object-dps.js?aa2a", "webpack:///./node_modules/core-js/library/modules/_iobject.js?314e", "webpack:///./node_modules/core-js/library/modules/_array-includes.js?bc57", "webpack:///./node_modules/core-js/library/modules/_to-absolute-index.js?7e40", "webpack:///./node_modules/core-js/library/modules/_object-gpo.js?3f3c", "webpack:///./node_modules/core-js/library/modules/es6.array.iterator.js?c469", "webpack:///./node_modules/core-js/library/modules/_add-to-unscopables.js?e267", "webpack:///./node_modules/core-js/library/modules/_iter-step.js?1066", "webpack:///./node_modules/core-js/library/modules/es6.promise.js?097c", "webpack:///./node_modules/core-js/library/modules/_an-instance.js?d8ac", "webpack:///./node_modules/core-js/library/modules/_for-of.js?356b", "webpack:///./node_modules/core-js/library/modules/_iter-call.js?9ac5", "webpack:///./node_modules/core-js/library/modules/_is-array-iter.js?321c", "webpack:///./node_modules/core-js/library/modules/core.get-iterator-method.js?ddfb", "webpack:///./node_modules/core-js/library/modules/_invoke.js?927b", "webpack:///./node_modules/core-js/library/modules/_microtask.js?f363", "webpack:///./node_modules/core-js/library/modules/_user-agent.js?8946", "webpack:///./node_modules/core-js/library/modules/_redefine-all.js?c47f", "webpack:///./node_modules/core-js/library/modules/_set-species.js?6d1a", "webpack:///./node_modules/core-js/library/modules/_iter-detect.js?758d", "webpack:///./node_modules/core-js/library/modules/es7.promise.finally.js?12a0", "webpack:///./node_modules/core-js/library/modules/es7.promise.try.js?8ca5", "webpack:///./node_modules/core-js/library/fn/json/stringify.js?aa42", "webpack:///./src/core/webTransfer.js?7e34", "webpack:///./node_modules/buffer/index.js?12e3", "webpack:///(webpack)/buildin/global.js?3698", "webpack:///./node_modules/base64-js/index.js?10ab", "webpack:///./node_modules/ieee754/index.js?ba37", "webpack:///./node_modules/isarray/index.js?b0e4", "webpack:///./src/libs/md5/spark-md5.js?fa98", "webpack:///./node_modules/babel-runtime/helpers/typeof.js?a456", "webpack:///./node_modules/babel-runtime/core-js/symbol/iterator.js?6738", "webpack:///./node_modules/core-js/library/fn/symbol/iterator.js?fe7e", "webpack:///./node_modules/babel-runtime/core-js/symbol.js?e505", "webpack:///./node_modules/core-js/library/fn/symbol/index.js?0707", "webpack:///./node_modules/core-js/library/modules/es6.symbol.js?7d67", "webpack:///./node_modules/core-js/library/modules/_meta.js?d3a3", "webpack:///./node_modules/core-js/library/modules/_enum-keys.js?5dce", "webpack:///./node_modules/core-js/library/modules/_is-array.js?ed43", "webpack:///./node_modules/core-js/library/modules/_object-gopn-ext.js?46b7", "webpack:///./node_modules/core-js/library/modules/_object-gopd.js?2ca6", "webpack:///./node_modules/core-js/library/modules/es7.symbol.async-iterator.js?3989", "webpack:///./node_modules/core-js/library/modules/es7.symbol.observable.js?4167", "webpack:///./src/core/vtubeTransfer.js?b22d", "webpack:///./src/indexPure.js", "webpack:///./src/core/thirdpartSupport.js"], "names": ["commonUtil", "asyncLoadedScripts", "asyncLoadedScriptsCallbackQueue", "getScriptDomFromUrl", "url", "dom", "test", "document", "createElement", "setAttribute", "href", "type", "rel", "asyncLoadScript", "callback", "$this", "undefined", "push", "apply", "scriptDom", "readyState", "onreadystatechange", "i", "j", "length", "onload", "getElementsByTagName", "append<PERSON><PERSON><PERSON>", "getFileNameFromUrl", "substring", "lastIndexOf", "isIncludeScript", "name", "js", "es", "indexOf", "loadScripts", "scriptArr", "Array", "promises", "resolve", "reject", "all", "getExtensions", "s", "getExtension", "e", "toLowerCase", "getFileName", "getFullFileName", "getTypeByExt", "ext", "config", "types", "_", "get", "window", "extensions", "find", "formatSize", "size", "point<PERSON><PERSON><PERSON>", "units", "unit", "shift", "toFixed", "prompt", "msg", "mam", "msgOk", "message", "ok", "addUrlParam", "param", "<PERSON><PERSON><PERSON><PERSON>", "cname", "ca", "cookie", "split", "c", "trim", "globalUtil", "getQueryParam", "key", "replace", "regex", "RegExp", "results", "exec", "decodeURIComponent", "getUrlQueryParam", "location", "httpUtil", "getGetParamStr", "restful", "str", "$", "isEmptyObject", "defer", "Deferred", "purl", "token", "ajax", "xhrFields", "withCredentials", "then", "res", "success", "console", "error", "l", "data", "code", "title", "post", "opts", "postConfig", "contentType", "processData", "delete", "require", "default", "sparkmd5", "web", "mainOpts", "self", "$control", "controlId", "uploadRetryTimer", "s3", "tasks", "getConfig", "nxt", "configInst", "windowClose", "getFilesByStatus", "init", "on", "getFileTime", "file", "isUndefined", "lastModifiedDate", "lastModified", "getTime", "getDefaultKeyframe", "task", "entityTypes", "entityType", "isOther", "keyframe", "unauthorizedTip", "for<PERSON>ach", "item", "status", "off", "loginUrl", "initControl", "accept", "uniqueId", "isMultiple", "append", "destroyControl", "unbind", "remove", "trigger", "event", "call", "getGroupTask", "targetFolder", "taskType", "transferType", "rettask", "files", "map", "fileName", "metadata", "progress", "getTasksByFiles", "result", "openFileSelector", "single", "removeAttr", "attr", "val", "click", "stopPropagation", "createTask", "dto", "params", "list", "isArray", "fileSize", "fileLastModifiedDate", "Number", "unshift", "relationContentType", "relationContentId", "addTask", "initS3MultipartUpload", "ossClientInfo", "manageType", "initOssMultipartUpload", "Bucket", "bucketName", "Key", "relativePath", "getS3Client", "createUploadPromise", "createMultipartUpload", "promise", "initData", "uploadId", "UploadId", "catch", "err", "info", "credentials", "accessKeyId", "secretAccessKey", "accessKeySecret", "endpoint", "sslEnabled", "useHttp", "s3ForcePathStyle", "signatureVersion", "version", "apiVersion", "region", "AWS", "isHandleHttpPath", "protocol", "host", "S3", "OOS", "initTask", "server", "loginToken", "field", "fieldName", "value", "String", "fileSizeString", "sizeTotal", "chunkTotal", "taskId", "fileTotal", "targetFolderName", "targetType", "inited", "sizeTotalString", "isJsUpload", "prepareUpload", "text", "desc", "chunkFinished", "len", "webUploadThreads", "prepared", "upload", "uploadByAliOss", "uploadByOos", "uploadByKsyun", "uploadByS3", "uploadByBackend", "calcChunkMd5", "webUploadMd5Enable", "fileReader", "FileReader", "spark", "appendBinary", "target", "md5", "end", "destroy", "onerror", "uploadErrorRetry", "readAsBinaryString", "calcSurplusTime", "startTime", "isNumber", "surplusTime", "Date", "chunkIndex", "hasOwnProperty", "errorCount", "fileId", "setTimeout", "start", "chunkSize", "Math", "min", "form", "FormData", "slice", "clearTask", "taskStatus", "callComplete", "calcTaskProgress", "calcFileProgress", "callUpload", "partInfo", "checkPoint", "serverUrl", "uploadByAliOssWithServerCallback", "clientOpt", "bucket", "client", "OSS", "Wrapper", "put", "<PERSON><PERSON><PERSON>", "readAsA<PERSON>y<PERSON><PERSON>er", "callbackUrl", "ossUpCallbackUrl", "partSize", "p", "checkpoint", "done", "doneParts", "callbackBody", "JSON", "parse", "multipartUpload", "log", "doUploadByKsyun", "filedata", "upParams", "PartNumber", "body", "Ks3", "upload_part", "partNumber", "etag", "AK", "SK", "serviceUrl", "ks3Options", "KSSAccessKeyId", "signature", "bucket_name", "acl", "uploadDomain", "autoStart", "pluploadOptions", "drop_element", "tempUpload", "ks3FileUploader", "File", "ACL", "putObject", "rerr", "multitpart_upload_init", "uploadAfter", "Body", "successData", "stack", "ETag", "getUploadIdTask", "uploadPart", "continueUpload", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "calcFileMd5", "fileMd5", "getUnfinishedTask", "relationId", "deferred", "canDeleteTask", "removeTask", "ta", "deleteTask", "timeout", "cancel", "fileAndTag", "hash", "arguments", "x", "y", "z", "calcProgress", "current", "total", "toString", "count", "factory", "exports", "module", "define", "glob", "SparkMD5", "add32", "a", "b", "cmn", "q", "t", "ff", "d", "gg", "hh", "ii", "md5cycle", "k", "md5blk", "md5blks", "charCodeAt", "md5blk_array", "md51", "n", "state", "tail", "tmp", "lo", "hi", "match", "parseInt", "md51_array", "subarray", "Uint8Array", "hex_chr", "rhex", "hex", "join", "reset", "lsw", "msw", "prototype", "unescape", "encodeURIComponent", "contents", "_buff", "_length", "_state", "substr", "raw", "buff", "ret", "_finish", "hashBinary", "content", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arr", "_concatArray<PERSON>uffer", "byteLength", "first", "second", "first<PERSON><PERSON><PERSON>", "set", "vtube", "baseUrl", "getCompleteUrl", "callUrl", "getRequestParams", "TaskName", "TaskGuid", "TransType", "TransFile", "UserInfo", "UserId", "user", "id", "UserName", "nick<PERSON><PERSON>", "loginName", "UserCode", "userCode", "ExtendeAttr", "vtubeInfo", "importType", "CallBackUrl", "serverInfo", "ServerInfo", "HostName", "hostName", "Port", "port", "Scheme", "scheme", "userName", "Password", "password", "PathRoot", "pathRoot", "ItemCode", "ItemName", "alias", "controlType", "Value", "tree", "<PERSON><PERSON><PERSON><PERSON>", "isString", "FileCount", "FileList", "FilePath", "vtubeDownloadPath", "path", "FileSize", "parms", "ExtendData", "userToken", "<PERSON><PERSON><PERSON>", "hostname", "absolutePath", "startChar", "SourceFile", "DestFile", "Result", "Msg", "thirdpartSupport", "mamUpload", "options", "handlePicPkgTaskMetadataName", "index"], "mappings": ";QAAA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;;QAGA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,KAAK;QACL;QACA;;QAEA;QACA;QACA;QACA,2BAA2B,0BAA0B,EAAE;QACvD,iCAAiC,eAAe;QAChD;QACA;QACA;;QAEA;QACA,sDAAsD,+DAA+D;;QAErH;QACA;;QAEA;QACA;;;;;;;AC7DA;AACA;AACA;AACA;AACA;AACA,yCAAyC;;;;;;;ACLzC,YAAY,mBAAO,CAAC,EAAW;AAC/B,UAAU,mBAAO,CAAC,EAAQ;AAC1B,aAAa,mBAAO,CAAC,CAAW;AAChC;;AAEA;AACA;AACA;AACA;;AAEA;;;;;;;ACVA,6BAA6B;AAC7B,uCAAuC;;;;;;;ACDvC,eAAe,mBAAO,CAAC,CAAc;AACrC;AACA;AACA;AACA;;;;;;;ACJA,SAAS,mBAAO,CAAC,CAAc;AAC/B,iBAAiB,mBAAO,CAAC,EAAkB;AAC3C,iBAAiB,mBAAO,CAAC,CAAgB;AACzC;AACA,CAAC;AACD;AACA;AACA;;;;;;;ACPA,eAAe,mBAAO,CAAC,CAAc;AACrC,qBAAqB,mBAAO,CAAC,EAAmB;AAChD,kBAAkB,mBAAO,CAAC,EAAiB;AAC3C;;AAEA,YAAY,mBAAO,CAAC,CAAgB;AACpC;AACA;AACA;AACA;AACA;AACA,GAAG,YAAY;AACf;AACA;AACA;AACA;;;;;;;ACfA;AACA;AACA;;;;;;;ACFA;AACA,kBAAkB,mBAAO,CAAC,EAAU;AACpC,iCAAiC,QAAQ,mBAAmB,UAAU,EAAE,EAAE;AAC1E,CAAC;;;;;;;ACHD,uBAAuB;AACvB;AACA;AACA;;;;;;;ACHA;AACA,cAAc,mBAAO,CAAC,EAAY;AAClC,cAAc,mBAAO,CAAC,EAAY;AAClC;AACA;AACA;;;;;;;ACLA;;;;;;;ACAA,aAAa,mBAAO,CAAC,CAAW;AAChC,WAAW,mBAAO,CAAC,CAAS;AAC5B,UAAU,mBAAO,CAAC,EAAQ;AAC1B,WAAW,mBAAO,CAAC,CAAS;AAC5B,UAAU,mBAAO,CAAC,CAAQ;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAiE;AACjE;AACA,kFAAkF;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,SAAS;AACT;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,+CAA+C;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd,cAAc;AACd,cAAc;AACd,cAAc;AACd,eAAe;AACf,eAAe;AACf,eAAe;AACf,gBAAgB;AAChB;;;;;;;AC7DA;;;;;;;ACAA,iBAAiB;;AAEjB;AACA;AACA;;;;;;;ACJA;AACA,gBAAgB,mBAAO,CAAC,EAAe;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACnBA;AACA;AACA;AACA;;;;;;;ACHA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACPA;AACA;AACA;AACA;AACA;;;;;;;ACJA,UAAU,mBAAO,CAAC,CAAc;AAChC,UAAU,mBAAO,CAAC,CAAQ;AAC1B,UAAU,mBAAO,CAAC,CAAQ;;AAE1B;AACA,oEAAoE,iCAAiC;AACrG;;;;;;;;;;;;;;;;;;;;ACNA,IAAIA,aAAa;AACbC,wBAAqB,EADR;AAEbC,qCAAiC,EAFpB;AAGbC,yBAAsB,6BAASC,GAAT,EAAa;AAC/B,YAAIC,GAAJ;AACA,YAAI,UAAUC,IAAV,CAAeF,GAAf,CAAJ,EACA;AACIC,kBAAME,SAASC,aAAT,CAAuB,QAAvB,CAAN;AACAH,gBAAII,YAAJ,CAAiB,MAAjB,EAAyB,iBAAzB;AACAJ,gBAAII,YAAJ,CAAiB,KAAjB,EAAwBL,GAAxB;AACH,SALD,MAMK,IAAG,WAAWE,IAAX,CAAgBF,GAAhB,CAAH,EACL;AACIC,kBAAME,SAASC,aAAT,CAAuB,MAAvB,CAAN;AACAH,gBAAIK,IAAJ,GAAWN,GAAX;AACAC,gBAAIM,IAAJ,GAAW,UAAX;AACAN,gBAAIO,GAAJ,GAAQ,YAAR;AACH;AACD,eAAOP,GAAP;AACH,KAnBY;AAoBb;;;AAGAQ,qBAAiB,yBAAST,GAAT,EAAcU,QAAd,EAAwB;AACrC,YAAIC,QAAQf,UAAZ;AACA,YAAIe,MAAMd,kBAAN,CAAyBG,GAAzB,KAAiCY,SAArC,EAA+C;AAC/C;AACI,oBAAIF,YAAY,OAAOA,QAAP,IAAoB,UAApC,EAAgD;AAC5C,wBAAIC,MAAMd,kBAAN,CAAyBG,GAAzB,KAAiC,CAArC,EAAuC;AACvC;AACI,gCAAI,CAACW,MAAMb,+BAAN,CAAsCE,GAAtC,CAAL,EAAiD;AAC7CW,sCAAMb,+BAAN,CAAsCE,GAAtC,IAA6C,EAA7C;AACH;AACDW,kCAAMb,+BAAN,CAAsCE,GAAtC,EAA2Ca,IAA3C,CAAgDH,QAAhD;AACH,yBAND,MAOK;AACDA,iCAASI,KAAT,CAAeH,KAAf,EAAsB,EAAtB;AACH;AACJ;AACD;AACH;AACDA,cAAMd,kBAAN,CAAyBG,GAAzB,IAAgC,CAAhC;AACA,YAAIe,YAAYJ,MAAMZ,mBAAN,CAA0BC,GAA1B,CAAhB;AACA,YAAIe,UAAUC,UAAd,EAA0B;AACtBD,sBAAUE,kBAAV,GAA+B,YAAY;AACvC,oBAAIF,UAAUC,UAAV,IAAwB,QAAxB,IAAoCD,UAAUC,UAAV,IAAwB,UAAhE,EAA4E;AACxED,8BAAUE,kBAAV,GAA+B,IAA/B;AACAN,0BAAMd,kBAAN,CAAyBG,GAAzB,IAAgC,CAAhC;AACA,wBAAIU,YAAY,OAAOA,QAAP,IAAoB,UAApC,EAAgD;AAC5CA,iCAASI,KAAT,CAAeH,KAAf,EAAsB,EAAtB;AACH;AACD,wBAAIA,MAAMb,+BAAN,CAAsCE,GAAtC,CAAJ,EAAgD;AAC5C,6BAAK,IAAIkB,IAAI,CAAR,EAAWC,IAAIR,MAAMb,+BAAN,CAAsCE,GAAtC,EAA2CoB,MAA/D,EAAuEF,IAAIC,CAA3E,EAA8ED,GAA9E,EAAmF;AAC/EP,kCAAMb,+BAAN,CAAsCE,GAAtC,EAA2CkB,CAA3C,EAA8CJ,KAA9C,CAAoDH,KAApD,EAA2D,EAA3D;AACH;AACDA,8BAAMb,+BAAN,CAAsCE,GAAtC,IAA6CY,SAA7C;AACH;AACJ;AACJ,aAdD;AAeH,SAhBD,MAiBK;AACDG,sBAAUM,MAAV,GAAmB,YAAY;AAC3BV,sBAAMd,kBAAN,CAAyBG,GAAzB,IAAgC,CAAhC;AACA,oBAAIU,YAAY,OAAOA,QAAP,IAAoB,UAApC,EAAgD;AAC5CA,6BAASI,KAAT,CAAeH,KAAf,EAAsB,EAAtB;AACH;AACD,oBAAIA,MAAMb,+BAAN,CAAsCE,GAAtC,CAAJ,EAAgD;AAC5C,yBAAK,IAAIkB,IAAI,CAAR,EAAWC,IAAIR,MAAMb,+BAAN,CAAsCE,GAAtC,EAA2CoB,MAA/D,EAAuEF,IAAIC,CAA3E,EAA8ED,GAA9E,EAAmF;AAC/EP,8BAAMb,+BAAN,CAAsCE,GAAtC,EAA2CkB,CAA3C,EAA8CJ,KAA9C,CAAoDH,KAApD,EAA2D,EAA3D;AACH;AACDA,0BAAMb,+BAAN,CAAsCE,GAAtC,IAA6CY,SAA7C;AACH;AACJ,aAXD;AAYH;AACDT,iBAASmB,oBAAT,CAA8B,MAA9B,EAAsC,CAAtC,EAAyCC,WAAzC,CAAqDR,SAArD;AACH,KA3EY;AA4EbS,wBAAqB,4BAASxB,GAAT,EAAa;AAC9B,eAAOA,IAAIyB,SAAJ,CAAczB,IAAI0B,WAAJ,CAAgB,GAAhB,IAAuB,CAArC,EAAwC1B,IAAIoB,MAA5C,CAAP;AACH,KA9EY;AA+EbO,qBAAkB,yBAASC,IAAT,EAAc;AAC5B,YAAIC,KAAK,OAAO3B,IAAP,CAAY0B,IAAZ,CAAT;AACA,YAAIE,KAAG3B,SAASmB,oBAAT,CAA8BO,KAAG,QAAH,GAAY,MAA1C,CAAP;AACA,aAAI,IAAIX,IAAE,CAAV,EAAYA,IAAEY,GAAGV,MAAjB,EAAwBF,GAAxB;AACI,gBAAGY,GAAGZ,CAAH,EAAMW,KAAG,KAAH,GAAS,MAAf,EAAuBE,OAAvB,CAA+BH,IAA/B,KAAsC,CAAC,CAA1C,EAA4C,OAAO,IAAP;AADhD,SAEA,OAAO,KAAP;AACH,KArFY;AAsFbI,iBAAc,qBAASC,SAAT,EAAmB;AAC7B,YAAIA,qBAAqBC,KAAzB,EACA;AACI,gBAAIC,WAAW,EAAf;AACA,iBAAK,IAAIjB,IAAI,CAAb,EAAgBA,IAAIe,UAAUb,MAA9B,EAAsCF,GAAtC,EACA;AACIiB,yBAAStB,IAAT,CAAc,sBAAY,UAASuB,OAAT,EAAkBC,MAAlB,EAAyB;AAC/C,wBAAIzC,WAAW+B,eAAX,CAA2B/B,WAAW4B,kBAAX,CAA8BS,UAAUf,CAAV,CAA9B,CAA3B,CAAJ,EACA;AACIkB;AACH,qBAHD,MAKA;AACIxC,mCAAWa,eAAX,CAA2BwB,UAAUf,CAAV,CAA3B,EAAyC,YAAU;AAC/CkB;AACH,yBAFD;AAGH;AACJ,iBAXa,CAAd;AAYH;AACD,mBAAO,kBAAQE,GAAR,CAAYH,QAAZ,CAAP;AACH,SAnBD,MAqBA;AACI,mBAAO,sBAAY,UAASC,OAAT,EAAkBC,MAAlB,EAAyB;AACxCD;AACH,aAFM,CAAP;AAGH;AACJ,KAjHY;AAkHbG,mBAAe,uBAAUC,CAAV,EAAa;AACxB,YAAI,CAACA,CAAL,EAAQ;AACJ,mBAAO,EAAP;AACH;AACD,aAAK,IAAItB,IAAIsB,EAAEpB,MAAf,EAAuBF,KAAK,CAA5B,EAA+BA,GAA/B,EAAoC;AAChC,gBAAIsB,EAAEtB,CAAF,KAAQ,GAAZ,EAAiB;AACb,uBAAOsB,EAAEf,SAAF,CAAYP,CAAZ,EAAesB,EAAEpB,MAAjB,CAAP;AACH;AACJ;AACD,eAAO,EAAP;AACH,KA5HY;AA6HbqB,kBAAc,sBAAUD,CAAV,EAAa;AACvB,YAAIE,IAAI9C,WAAW2C,aAAX,CAAyBC,CAAzB,CAAR;AACA,YAAIE,EAAEtB,MAAF,GAAW,CAAf,EAAkB;AACd,mBAAOsB,EAAEjB,SAAF,CAAY,CAAZ,EAAeiB,EAAEtB,MAAjB,EAAyBuB,WAAzB,EAAP;AACH;AACD,eAAO,EAAP;AACH,KAnIY;AAoIbC,iBAAa,qBAAUJ,CAAV,EAAa;AACtB,YAAIE,IAAIF,EAAEpB,MAAV;AACA,aAAK,IAAIF,IAAIsB,EAAEpB,MAAf,EAAuBF,KAAK,CAA5B,EAA+BA,GAA/B,EAAoC;AAChC,gBAAIsB,EAAEtB,CAAF,KAAQ,GAAR,IAAewB,KAAKF,EAAEpB,MAA1B,EAAkC;AAC9BsB,oBAAIxB,CAAJ;AACA;AACH;AACD,gBAAIsB,EAAEtB,CAAF,KAAQ,IAAZ,EAAkB;AAAE;AAChB,uBAAOsB,EAAEf,SAAF,CAAYP,IAAI,CAAhB,EAAmBwB,CAAnB,CAAP;AACH;AACJ;AACD,eAAOF,EAAEf,SAAF,CAAY,CAAZ,EAAeiB,CAAf,CAAP;AACH,KAhJY;AAiJbG,qBAAiB,yBAAUL,CAAV,EAAa;AAC1B,aAAK,IAAItB,IAAIsB,EAAEpB,MAAf,EAAuBF,KAAK,CAA5B,EAA+BA,GAA/B,EAAoC;AAChC,gBAAIsB,EAAEtB,CAAF,KAAQ,IAAZ,EAAkB;AAAE;AAChB,uBAAOsB,EAAEf,SAAF,CAAYP,IAAI,CAAhB,EAAmBsB,EAAEpB,MAArB,CAAP;AACH;AACJ;AACD,eAAOoB,CAAP;AACH,KAxJY;AAyJbM,kBAAc,sBAAUC,GAAV,EAAeC,MAAf,EAAuB;AACjCD,cAAMA,IAAIJ,WAAJ,EAAN;AACA,YAAII,IAAIhB,OAAJ,CAAY,GAAZ,MAAqB,CAAzB,EACA;AACIgB,kBAAM,MAAMA,GAAZ;AACH;AACD,YAAIE,QAAQC,EAAEC,GAAF,CAAMC,MAAN,EAAc,wBAAd,EAAwC,EAAxC,CAAZ;AACA,YAAIJ,UAAUC,MAAM7B,MAAN,KAAiB,CAA/B,EACA;AACI6B,oBAAQC,EAAEC,GAAF,CAAMH,MAAN,EAAc,aAAd,EAA6B,EAA7B,CAAR;AACH;AACD,aAAK,IAAI9B,IAAI,CAAb,EAAgBA,IAAI+B,MAAM7B,MAA1B,EAAkCF,GAAlC,EAAuC;AACnC,gBAAI+B,MAAM/B,CAAN,EAASmC,UAAT,IAAuBJ,MAAM/B,CAAN,EAASmC,UAAT,CAAoBtB,OAApB,CAA4BgB,GAA5B,KAAoC,CAAC,CAAhE,EAAmE;AAC/D,uBAAOE,MAAM/B,CAAN,CAAP;AACH;AACJ;AACD,eAAOgC,EAAEI,IAAF,CAAOL,KAAP,EAAc,EAAE,QAAQ,OAAV,EAAd,CAAP;AACH,KA1KY;AA2KbM,gBAAY,oBAAUC,IAAV,EAAgBC,WAAhB,EAA6BC,KAA7B,EAAoC;AAC5C,YAAIC,IAAJ;AACAD,gBAAQA,SAAS,CAAC,GAAD,EAAM,IAAN,EAAY,IAAZ,EAAkB,IAAlB,EAAwB,IAAxB,CAAjB;AACA,eAAO,CAACC,OAAOD,MAAME,KAAN,EAAR,KAA0BJ,OAAO,IAAxC,EAA8C;AAC1CA,mBAAOA,OAAO,IAAd;AACH;AACD,eAAO,CAACG,SAAS,GAAT,GAAeH,IAAf,GAAsBA,KAAKK,OAAL,CAAaJ,eAAe,CAA5B,CAAvB,IAAyD,GAAzD,GAA+DE,IAAtE;AACH,KAlLY;AAmLbG,YAAQ,gBAASC,GAAT,EAAa;AACjB,YAAIC,OAAOA,IAAIF,MAAf,EACA;AACIE,gBAAIF,MAAJ,CAAWC,GAAX;AACH;AACJ,KAxLY;AAyLbE,WAAO,eAASF,GAAT,EAAa;AAChB,YAAIC,OAAOA,IAAIE,OAAX,IAAsBF,IAAIE,OAAJ,CAAYC,EAAtC,EACA;AACIH,gBAAIE,OAAJ,CAAYC,EAAZ,CAAeJ,GAAf;AACH;AACJ,KA9LY;AA+LbK,iBAAa,qBAASpE,GAAT,EAAcqE,KAAd,EAAqB;AAC9BrE,eAAO,CAACA,IAAI+B,OAAJ,CAAY,GAAZ,MAAqB,CAAC,CAAtB,GAA0B,GAA1B,GAAgC,GAAjC,IAAwCsC,KAA/C;AACA,eAAOrE,GAAP;AACH,KAlMY;AAmMbsE,eAAW,mBAASC,KAAT,EAAe;AACtB,YAAI3C,OAAO2C,QAAQ,GAAnB;AACA,YAAIC,KAAKrE,SAASsE,MAAT,CAAgBC,KAAhB,CAAsB,GAAtB,CAAT;AACA,aAAI,IAAIxD,IAAE,CAAV,EAAaA,IAAEsD,GAAGpD,MAAlB,EAA0BF,GAA1B,EACA;AACI,gBAAIyD,IAAIH,GAAGtD,CAAH,EAAM0D,IAAN,EAAR;AACA,gBAAID,EAAE5C,OAAF,CAAUH,IAAV,KAAiB,CAArB,EAAwB,OAAO+C,EAAElD,SAAF,CAAYG,KAAKR,MAAjB,EAAwBuD,EAAEvD,MAA1B,CAAP;AAC3B;AACD,eAAO,EAAP;AACH;AA5MY,CAAjB;kBA8MexB,U;;;;;;;;;;;;AC9Mf,IAAIiF,aAAa,EAAjB;kBACeA,U;;;;;;ACDf,kBAAkB,YAAY,mBAAO,CAAC,EAAmC,sB;;;;;;ACAzE;AACA;AACA;AACA;AACA;AACA;;;;;;;ACLA;AACA;AACA;AACA;AACA;;;;;;;ACJA,eAAe,mBAAO,CAAC,CAAc;AACrC,eAAe,mBAAO,CAAC,CAAW;AAClC;AACA;AACA;AACA;AACA;;;;;;;ACNA;AACA,eAAe,mBAAO,CAAC,CAAc;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACXA;AACA,YAAY,mBAAO,CAAC,EAAyB;AAC7C,kBAAkB,mBAAO,CAAC,EAAkB;;AAE5C;AACA;AACA;;;;;;;ACNA,aAAa,mBAAO,CAAC,EAAW;AAChC,UAAU,mBAAO,CAAC,EAAQ;AAC1B;AACA;AACA;;;;;;;ACJA,WAAW,mBAAO,CAAC,CAAS;AAC5B,aAAa,mBAAO,CAAC,CAAW;AAChC;AACA,kDAAkD;;AAElD;AACA,qEAAqE;AACrE,CAAC;AACD;AACA,QAAQ,mBAAO,CAAC,EAAY;AAC5B;AACA,CAAC;;;;;;;ACXD;AACA;AACA;AACA;;;;;;;;ACHa;AACb;AACA,gBAAgB,mBAAO,CAAC,EAAe;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;;;;;;;ACjBA,YAAY,mBAAO,CAAC,CAAQ;;;;;;;ACA5B,aAAa,mBAAO,CAAC,CAAW;AAChC,WAAW,mBAAO,CAAC,CAAS;AAC5B,cAAc,mBAAO,CAAC,EAAY;AAClC,aAAa,mBAAO,CAAC,EAAY;AACjC,qBAAqB,mBAAO,CAAC,CAAc;AAC3C;AACA,0DAA0D,sBAAsB;AAChF,kFAAkF,wBAAwB;AAC1G;;;;;;;ACRA,cAAc;;;;;;;;;;;;;;;;;;ACGd;;;;;;AAEA,IAAIC,gBAAgB,SAAhBA,aAAgB,CAAU9E,GAAV,EAAe+E,GAAf,EAAoB;AACpCA,UAAMA,IAAIpC,WAAJ,GAAkBqC,OAAlB,CAA0B,SAA1B,EAAqC,MAArC,CAAN;AACA,QAAIC,QAAQ,IAAIC,MAAJ,CAAW,SAASH,GAAT,GAAe,mBAA1B,CAAZ;AAAA,QACII,UAAUF,MAAMG,IAAN,CAAWpF,IAAI2C,WAAJ,EAAX,CADd;AAEA,QAAI,CAACwC,OAAL,EAAc,OAAO,IAAP;AACd,QAAI,CAACA,QAAQ,CAAR,CAAL,EAAiB,OAAO,EAAP;AACjB,WAAOE,mBAAmBF,QAAQ,CAAR,EAAWH,OAAX,CAAmB,KAAnB,EAA0B,GAA1B,CAAnB,CAAP;AACH,CAPD,C,CALA;;;;AAaA,IAAIM,mBAAmB,SAAnBA,gBAAmB,CAAUP,GAAV,EAAe;AAClC,WAAOD,cAAc1B,OAAOmC,QAAP,CAAgBjF,IAA9B,EAAoCyE,GAApC,CAAP;AACH,CAFD;AAGA,IAAIX,cAAc,SAAdA,WAAc,CAASpE,GAAT,EAAcqE,KAAd,EAAqB;AACnCrE,WAAO,CAACA,IAAI+B,OAAJ,CAAY,GAAZ,MAAqB,CAAC,CAAtB,GAA0B,GAA1B,GAAgC,GAAjC,IAAwCsC,KAA/C;AACA,WAAOrE,GAAP;AACH,CAHD;;AAKA,IAAIwF,WAAW;AACXC,oBAAiB,wBAASpB,KAAT,EAAgBqB,OAAhB,EAAwB;AACrC,YAAIC,MAAM,EAAV;AACA,YAAItB,SAASzD,SAAT,IAAsB,CAACgF,EAAEC,aAAF,CAAgBxB,KAAhB,CAA3B,EACA;AACI,iBAAK,IAAIU,GAAT,IAAgBV,KAAhB,EACA;AACI,oBAAI,CAACqB,OAAL,EACA;AACIC,2BAAOZ,MAAM,GAAN,GAAYV,MAAMU,GAAN,CAAZ,GAAyB,GAAhC;AACH,iBAHD,MAKA;AACIY,2BAAO,MAAMtB,MAAMU,GAAN,CAAb;AACH;AACJ;AACD,gBAAI,CAACW,OAAL,EACA;AACIC,sBAAM,MAAMA,IAAIlE,SAAJ,CAAc,CAAd,EAAiBkE,IAAIvE,MAAJ,GAAa,CAA9B,CAAZ;AACH;AACJ;AACD,eAAOuE,GAAP;AACH,KAtBU;AAuBXxC,SAAM,aAASnD,GAAT,EAAcqE,KAAd,EAAoB;AACtB,YAAIyB,QAAQF,EAAEG,QAAF,EAAZ;AACA,YAAIC,OAAOhG,GAAX;AACA,YAAI,CAACqE,KAAL,EACA;AACIA,oBAAQ,EAAR;AACH;AACD2B,eAAOhG,MAAMwF,SAASC,cAAT,CAAwBpB,KAAxB,CAAb;AACA,YAAI4B,QAAQX,iBAAiB,OAAjB,CAAZ;AACA,YAAIW,KAAJ,EAAU;AACND,mBAAO5B,YAAY4B,IAAZ,EAAkB,WAAWC,KAA7B,CAAP;AACH;AACDL,UAAEM,IAAF,CAAO;AACH3F,kBAAO,KADJ;AAEH4F,uBAAW;AACPC,iCAAiBpG,IAAI+B,OAAJ,CAAY,QAAZ,IAAwB,CAAC,CAAzB,GAA6B,KAA7B,GAAqC,IAD/C,CACmD;AADnD,aAFR;AAKH/B,iBAAMgG;AALH,SAAP,EAMGK,IANH,CAMQ,UAASC,GAAT,EAAa;AACjB,gBAAI,CAACA,IAAIC,OAAT,EACA;AACIC,wBAAQC,KAAR,CAAc,UAAd,EAA0BH,GAA1B;AACA1G,qCAAWkE,MAAX,CAAkB4C,EAAE,YAAYJ,IAAIK,IAAJ,CAASC,IAAvB,EAA6BN,IAAIK,IAAJ,CAASE,KAAtC,CAAlB;AACAf,sBAAMzD,MAAN,CAAaiE,GAAb;AACH,aALD,MAOA;AACIR,sBAAM1D,OAAN,CAAckE,GAAd;AACH;AACJ,SAjBD,EAiBG,UAASA,GAAT,EAAa;AACZR,kBAAMzD,MAAN,CAAaiE,GAAb;AACH,SAnBD;AAoBA,eAAOR,KAAP;AACH,KAxDU;AAyDXgB,UAAO,cAAS9G,GAAT,EAAcqE,KAAd,EAAqB0C,IAArB,EAA0B;AAC7B,YAAIjB,QAAQF,EAAEG,QAAF,EAAZ;AACA,YAAI,CAAC1B,KAAL,EACA;AACIA,oBAAQ,EAAR;AACH;AACD,YAAI4B,QAAQX,iBAAiB,OAAjB,CAAZ;AACA,YAAIW,KAAJ,EAAU;AACNjG,kBAAMoE,YAAYpE,GAAZ,EAAiB,WAAWiG,KAA5B,CAAN;AACH;AACD,YAAIe,aAAa;AACbzG,kBAAO,MADM;AAEboG,kBAAQtC,KAFK;AAGb4C,yBAAa,kBAHA;AAIbC,yBAAa,KAJA;AAKbf,uBAAW;AACPC,iCAAiBpG,IAAI+B,OAAJ,CAAY,QAAZ,IAAwB,CAAC,CAAzB,GAA6B,KAA7B,GAAqC,IAD/C,CACmD;AADnD,aALE;AAQb/B,iBAAMA;AARO,SAAjB;AAUA,YAAI+G,QAAQA,KAAKE,WAAL,KAAqBrG,SAAjC,EACA;AACIoG,uBAAWC,WAAX,GAAyBF,KAAKE,WAA9B;AACH;AACD,YAAIF,QAAQA,KAAKG,WAAL,KAAqBtG,SAAjC,EACA;AACIoG,uBAAWE,WAAX,GAAyBH,KAAKG,WAA9B;AACH;AACD,YAAIF,WAAWC,WAAX,KAA2B,kBAA/B,EACA;AACID,uBAAWL,IAAX,GAAkB,yBAAetC,KAAf,CAAlB;AACH;AACDuB,UAAEM,IAAF,CAAOc,UAAP,EAAmBX,IAAnB,CAAwB,UAASC,GAAT,EAAa;AACjC,gBAAI,CAACA,IAAIC,OAAT,EACA;AACIC,wBAAQC,KAAR,CAAc,UAAd,EAA0BH,GAA1B;AACA1G,qCAAWkE,MAAX,CAAkB4C,EAAE,YAAYJ,IAAIG,KAAJ,CAAUG,IAAxB,EAA8BN,IAAIG,KAAJ,CAAUI,KAAxC,CAAlB;AACAf,sBAAMzD,MAAN,CAAaiE,GAAb;AACH,aALD,MAOA;AACIR,sBAAM1D,OAAN,CAAckE,GAAd;AACH;AACJ,SAXD,EAWG,UAASA,GAAT,EAAa;AACZR,kBAAMzD,MAAN,CAAaiE,GAAb;AACH,SAbD;AAcA,eAAOR,KAAP;AACH,KAxGU;AAyGXqB,YAAS,iBAASnH,GAAT,EAAcqE,KAAd,EAAqB0C,IAArB,EAA0B;AAC/B,YAAIjB,QAAQF,EAAEG,QAAF,EAAZ;AACA,YAAI,CAAC1B,KAAL,EACA;AACIA,oBAAQ,EAAR;AACH;AACD,YAAI4B,QAAQX,iBAAiB,OAAjB,CAAZ;AACA,YAAIW,KAAJ,EAAU;AACNjG,kBAAMoE,YAAYpE,GAAZ,EAAiB,WAAWiG,KAA5B,CAAN;AACH;AACD,YAAIe,aAAa;AACbzG,kBAAO,QADM;AAEboG,kBAAQtC,KAFK;AAGb4C,yBAAa,kBAHA;AAIbC,yBAAa,KAJA;AAKbf,uBAAW;AACPC,iCAAiBpG,IAAI+B,OAAJ,CAAY,QAAZ,IAAwB,CAAC,CAAzB,GAA6B,KAA7B,GAAqC,IAD/C,CACmD;AADnD,aALE;AAQb/B,iBAAMA;AARO,SAAjB;AAUA,YAAI+G,QAAQA,KAAKE,WAAL,KAAqBrG,SAAjC,EACA;AACIoG,uBAAWC,WAAX,GAAyBF,KAAKE,WAA9B;AACH;AACD,YAAIF,QAAQA,KAAKG,WAAL,KAAqBtG,SAAjC,EACA;AACIoG,uBAAWE,WAAX,GAAyBH,KAAKG,WAA9B;AACH;AACD,YAAIF,WAAWC,WAAX,KAA2B,kBAA/B,EACA;AACID,uBAAWL,IAAX,GAAkB,yBAAetC,KAAf,CAAlB;AACH;AACDuB,UAAEM,IAAF,CAAOc,UAAP,EAAmBX,IAAnB,CAAwB,UAASC,GAAT,EAAa;AACjC,gBAAI,CAACA,IAAIC,OAAT,EACA;AACIC,wBAAQC,KAAR,CAAc,UAAd,EAA0BH,GAA1B;AACA1G,qCAAWkE,MAAX,CAAkB4C,EAAE,YAAYJ,IAAIG,KAAJ,CAAUG,IAAxB,EAA8BN,IAAIG,KAAJ,CAAUI,KAAxC,CAAlB;AACAf,sBAAMzD,MAAN,CAAaiE,GAAb;AACH,aALD,MAOA;AACIR,sBAAM1D,OAAN,CAAckE,GAAd;AACH;AACJ,SAXD,EAWG,UAASA,GAAT,EAAa;AACZR,kBAAMzD,MAAN,CAAaiE,GAAb;AACH,SAbD;AAcA,eAAOR,KAAP;AACH;AAxJU,CAAf;kBA0JeN,Q;;;;;;;;;;;;;AC/KF;AACb,UAAU,mBAAO,CAAC,EAAc;;AAEhC;AACA,mBAAO,CAAC,EAAgB;AACxB,6BAA6B;AAC7B,cAAc;AACd;AACA,CAAC;AACD;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA,UAAU;AACV,CAAC;;;;;;;;AChBY;AACb,cAAc,mBAAO,CAAC,EAAY;AAClC,cAAc,mBAAO,CAAC,EAAW;AACjC,eAAe,mBAAO,CAAC,EAAa;AACpC,WAAW,mBAAO,CAAC,CAAS;AAC5B,gBAAgB,mBAAO,CAAC,EAAc;AACtC,kBAAkB,mBAAO,CAAC,EAAgB;AAC1C,qBAAqB,mBAAO,CAAC,EAAsB;AACnD,qBAAqB,mBAAO,CAAC,EAAe;AAC5C,eAAe,mBAAO,CAAC,CAAQ;AAC/B,8CAA8C;AAC9C;AACA;AACA;;AAEA,8BAA8B,aAAa;;AAE3C;AACA;AACA;AACA;AACA;AACA,yCAAyC,oCAAoC;AAC7E,6CAA6C,oCAAoC;AACjF,KAAK,4BAA4B,oCAAoC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,mBAAmB;AACnC;AACA;AACA,kCAAkC,2BAA2B;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;;;;;;ACpEA,kBAAkB,mBAAO,CAAC,CAAgB,MAAM,mBAAO,CAAC,EAAU;AAClE,+BAA+B,mBAAO,CAAC,EAAe,gBAAgB,mBAAmB,UAAU,EAAE,EAAE;AACvG,CAAC;;;;;;;ACFD,iBAAiB,mBAAO,CAAC,CAAS;;;;;;;ACAlC;AACA,eAAe,mBAAO,CAAC,CAAc;AACrC,UAAU,mBAAO,CAAC,EAAe;AACjC,kBAAkB,mBAAO,CAAC,EAAkB;AAC5C,eAAe,mBAAO,CAAC,EAAe;AACtC,yBAAyB;AACzB;;AAEA;AACA;AACA;AACA,eAAe,mBAAO,CAAC,EAAe;AACtC;AACA;AACA;AACA;AACA;AACA,EAAE,mBAAO,CAAC,EAAS;AACnB,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;;;;;;ACxCA,UAAU,mBAAO,CAAC,CAAQ;AAC1B,gBAAgB,mBAAO,CAAC,CAAe;AACvC,mBAAmB,mBAAO,CAAC,EAAmB;AAC9C,eAAe,mBAAO,CAAC,EAAe;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AChBA;AACA,gBAAgB,mBAAO,CAAC,EAAe;AACvC;AACA;AACA,2DAA2D;AAC3D;;;;;;;ACLA,eAAe,mBAAO,CAAC,CAAW;AAClC;;;;;;;ACDA;AACA,cAAc,mBAAO,CAAC,EAAY;AAClC;AACA;AACA;;;;;;;ACJA,mBAAO,CAAC,EAAsB;AAC9B,aAAa,mBAAO,CAAC,CAAW;AAChC,WAAW,mBAAO,CAAC,CAAS;AAC5B,gBAAgB,mBAAO,CAAC,EAAc;AACtC,oBAAoB,mBAAO,CAAC,CAAQ;;AAEpC;AACA;AACA;AACA;AACA;;AAEA,eAAe,yBAAyB;AACxC;AACA;AACA;AACA;AACA;AACA;;;;;;;AClBA;AACA,UAAU,mBAAO,CAAC,EAAQ;AAC1B,UAAU,mBAAO,CAAC,CAAQ;AAC1B;AACA,2BAA2B,kBAAkB,EAAE;;AAE/C;AACA;AACA;AACA;AACA,GAAG,YAAY;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACtBA;AACA,eAAe,mBAAO,CAAC,CAAc;AACrC,gBAAgB,mBAAO,CAAC,EAAe;AACvC,cAAc,mBAAO,CAAC,CAAQ;AAC9B;AACA;AACA;AACA;AACA;;;;;;;ACRA,UAAU,mBAAO,CAAC,EAAQ;AAC1B,aAAa,mBAAO,CAAC,EAAW;AAChC,WAAW,mBAAO,CAAC,EAAS;AAC5B,UAAU,mBAAO,CAAC,EAAe;AACjC,aAAa,mBAAO,CAAC,CAAW;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,mBAAO,CAAC,EAAQ;AACtB;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACnFA;AACA;AACA,YAAY;AACZ,GAAG;AACH,YAAY;AACZ;AACA;;;;;;;ACNA,eAAe,mBAAO,CAAC,CAAc;AACrC,eAAe,mBAAO,CAAC,CAAc;AACrC,2BAA2B,mBAAO,CAAC,EAA2B;;AAE9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACXA;;;;;;;ACAA;AACA,YAAY,mBAAO,CAAC,EAAyB;AAC7C,iBAAiB,mBAAO,CAAC,EAAkB;;AAE3C;AACA;AACA;;;;;;;ACNA,kBAAkB,YAAY,mBAAO,CAAC,EAA4B,sB;;;;;;ACAlE,mBAAO,CAAC,EAAiC;AACzC,mBAAO,CAAC,EAAgC;AACxC,mBAAO,CAAC,EAA6B;AACrC,mBAAO,CAAC,EAAwB;AAChC,mBAAO,CAAC,EAAgC;AACxC,mBAAO,CAAC,EAA4B;AACpC,iBAAiB,mBAAO,CAAC,CAAkB;;;;;;;ACN3C,gBAAgB,mBAAO,CAAC,EAAe;AACvC,cAAc,mBAAO,CAAC,EAAY;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AChBa;AACb,aAAa,mBAAO,CAAC,EAAkB;AACvC,iBAAiB,mBAAO,CAAC,EAAkB;AAC3C,qBAAqB,mBAAO,CAAC,EAAsB;AACnD;;AAEA;AACA,mBAAO,CAAC,CAAS,qBAAqB,mBAAO,CAAC,CAAQ,4BAA4B,aAAa,EAAE;;AAEjG;AACA,qDAAqD,4BAA4B;AACjF;AACA;;;;;;;ACZA,SAAS,mBAAO,CAAC,CAAc;AAC/B,eAAe,mBAAO,CAAC,CAAc;AACrC,cAAc,mBAAO,CAAC,EAAgB;;AAEtC,iBAAiB,mBAAO,CAAC,CAAgB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACZA;AACA,UAAU,mBAAO,CAAC,EAAQ;AAC1B;AACA;AACA;AACA;;;;;;;ACLA;AACA;AACA,gBAAgB,mBAAO,CAAC,CAAe;AACvC,eAAe,mBAAO,CAAC,EAAc;AACrC,sBAAsB,mBAAO,CAAC,EAAsB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,YAAY,eAAe;AAChC;AACA,KAAK;AACL;AACA;;;;;;;ACtBA,gBAAgB,mBAAO,CAAC,EAAe;AACvC;AACA;AACA;AACA;AACA;AACA;;;;;;;ACNA;AACA,UAAU,mBAAO,CAAC,CAAQ;AAC1B,eAAe,mBAAO,CAAC,EAAc;AACrC,eAAe,mBAAO,CAAC,EAAe;AACtC;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;;;;;;;ACZa;AACb,uBAAuB,mBAAO,CAAC,EAAuB;AACtD,WAAW,mBAAO,CAAC,EAAc;AACjC,gBAAgB,mBAAO,CAAC,EAAc;AACtC,gBAAgB,mBAAO,CAAC,CAAe;;AAEvC;AACA;AACA;AACA;AACA,iBAAiB,mBAAO,CAAC,EAAgB;AACzC,gCAAgC;AAChC,cAAc;AACd,iBAAiB;AACjB;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;;AAEA;AACA;AACA;;;;;;;ACjCA,8BAA8B;;;;;;;ACA9B;AACA,UAAU;AACV;;;;;;;;ACFa;AACb,cAAc,mBAAO,CAAC,EAAY;AAClC,aAAa,mBAAO,CAAC,CAAW;AAChC,UAAU,mBAAO,CAAC,EAAQ;AAC1B,cAAc,mBAAO,CAAC,EAAY;AAClC,cAAc,mBAAO,CAAC,EAAW;AACjC,eAAe,mBAAO,CAAC,CAAc;AACrC,gBAAgB,mBAAO,CAAC,EAAe;AACvC,iBAAiB,mBAAO,CAAC,EAAgB;AACzC,YAAY,mBAAO,CAAC,EAAW;AAC/B,yBAAyB,mBAAO,CAAC,EAAwB;AACzD,WAAW,mBAAO,CAAC,EAAS;AAC5B,gBAAgB,mBAAO,CAAC,EAAc;AACtC,iCAAiC,mBAAO,CAAC,EAA2B;AACpE,cAAc,mBAAO,CAAC,EAAY;AAClC,gBAAgB,mBAAO,CAAC,EAAe;AACvC,qBAAqB,mBAAO,CAAC,EAAoB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;;AAEA;AACA;AACA;AACA;AACA,+CAA+C,EAAE,mBAAO,CAAC,CAAQ;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,YAAY;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,WAAW;AACX,SAAS;AACT,OAAO;AACP;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,mBAAmB,kCAAkC;AACrD,SAAS;AACT;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,eAAe,uCAAuC;AACtD;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA,uBAAuB,0BAA0B;AACjD;AACA;AACA,SAAS;AACT;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA,GAAG;AACH,kBAAkB,yBAAyB,KAAK;AAChD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,wBAAwB;AACxB,gBAAgB;AAChB,oBAAoB;AACpB,wBAAwB;AACxB,gBAAgB;AAChB,oBAAoB;AACpB;AACA,uBAAuB,mBAAO,CAAC,EAAiB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,0DAA0D,oBAAoB;AAC9E,mBAAO,CAAC,EAAsB;AAC9B,mBAAO,CAAC,EAAgB;AACxB,UAAU,mBAAO,CAAC,CAAS;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gDAAgD,mBAAO,CAAC,EAAgB;AACxE;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA,KAAK;AACL;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA,CAAC;;;;;;;AC7RD;AACA;AACA;AACA,GAAG;AACH;;;;;;;ACJA,UAAU,mBAAO,CAAC,EAAQ;AAC1B,WAAW,mBAAO,CAAC,EAAc;AACjC,kBAAkB,mBAAO,CAAC,EAAkB;AAC5C,eAAe,mBAAO,CAAC,CAAc;AACrC,eAAe,mBAAO,CAAC,EAAc;AACrC,gBAAgB,mBAAO,CAAC,EAA4B;AACpD;AACA;AACA;AACA,uCAAuC,iBAAiB,EAAE;AAC1D;AACA;AACA;AACA;AACA;AACA,mEAAmE,gBAAgB;AACnF;AACA;AACA,GAAG,4CAA4C,gCAAgC;AAC/E;AACA;AACA;AACA;AACA;AACA;;;;;;;ACxBA;AACA,eAAe,mBAAO,CAAC,CAAc;AACrC;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;;;;;;ACXA;AACA,gBAAgB,mBAAO,CAAC,EAAc;AACtC,eAAe,mBAAO,CAAC,CAAQ;AAC/B;;AAEA;AACA;AACA;;;;;;;ACPA,cAAc,mBAAO,CAAC,EAAY;AAClC,eAAe,mBAAO,CAAC,CAAQ;AAC/B,gBAAgB,mBAAO,CAAC,EAAc;AACtC,iBAAiB,mBAAO,CAAC,CAAS;AAClC;AACA;AACA;AACA;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;;;;;;ACfA,aAAa,mBAAO,CAAC,CAAW;AAChC,gBAAgB,mBAAO,CAAC,EAAS;AACjC;AACA;AACA;AACA,aAAa,mBAAO,CAAC,EAAQ;;AAE7B;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,uCAAuC,sBAAsB,EAAE;AAC/D;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;;;;;;ACpEA,aAAa,mBAAO,CAAC,CAAW;AAChC;;AAEA;;;;;;;ACHA,WAAW,mBAAO,CAAC,CAAS;AAC5B;AACA;AACA;AACA;AACA,GAAG;AACH;;;;;;;;ACNa;AACb,aAAa,mBAAO,CAAC,CAAW;AAChC,WAAW,mBAAO,CAAC,CAAS;AAC5B,SAAS,mBAAO,CAAC,CAAc;AAC/B,kBAAkB,mBAAO,CAAC,CAAgB;AAC1C,cAAc,mBAAO,CAAC,CAAQ;;AAE9B;AACA;AACA;AACA;AACA,sBAAsB,aAAa;AACnC,GAAG;AACH;;;;;;;ACbA,eAAe,mBAAO,CAAC,CAAQ;AAC/B;;AAEA;AACA;AACA,iCAAiC,qBAAqB;AACtD;AACA,iCAAiC,SAAS,EAAE;AAC5C,CAAC,YAAY;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,SAAS,qBAAqB;AAC3D,iCAAiC,aAAa;AAC9C;AACA,GAAG,YAAY;AACf;AACA;;;;;;;;ACrBA;AACa;AACb,cAAc,mBAAO,CAAC,EAAW;AACjC,WAAW,mBAAO,CAAC,CAAS;AAC5B,aAAa,mBAAO,CAAC,CAAW;AAChC,yBAAyB,mBAAO,CAAC,EAAwB;AACzD,qBAAqB,mBAAO,CAAC,EAAoB;;AAEjD,2CAA2C;AAC3C;AACA;AACA;AACA;AACA,8DAA8D,UAAU,EAAE;AAC1E,KAAK;AACL;AACA,8DAA8D,SAAS,EAAE;AACzE,KAAK;AACL;AACA,CAAC,EAAE;;;;;;;;ACnBU;AACb;AACA,cAAc,mBAAO,CAAC,EAAW;AACjC,2BAA2B,mBAAO,CAAC,EAA2B;AAC9D,cAAc,mBAAO,CAAC,EAAY;;AAElC,+BAA+B;AAC/B;AACA;AACA;AACA;AACA,CAAC,EAAE;;;;;;;ACXH,WAAW,mBAAO,CAAC,CAAqB;AACxC,uCAAuC,4BAA4B;AACnE,yCAAyC;AACzC;AACA;;;;;;;;;;;;;;;;;;ACJA;;;;;;AAEA,IAAIX,aAAauC,mBAAOA,CAAC,EAAR,EAAkCC,OAAnD;AACA,IAAI7B,WAAW4B,mBAAOA,CAAC,EAAR,EAAgCC,OAA/C;AACA,IAAIC,WAAWF,mBAAOA,CAAC,EAAR,CAAf;;AAEA,IAAIG,MAAM,SAANA,GAAM,CAAUC,QAAV,EAAoB;AAC1B,QAAIC,OAAO,IAAX;AAAA,QACIC,QADJ;AAAA,QACcC,SADd;AAAA,QAEIC,mBAAmB,EAFvB;AAAA,QAE2B;AACvBC,MAHJ;AAIA,SAAKC,KAAL,GAAa,EAAb,CAL0B,CAKT;;AAEjB,aAASC,SAAT,GAAqB;AACjB,YAAI3E,OAAO4E,GAAP,IAAc5E,OAAO4E,GAAP,CAAWhF,MAA7B,EAAqC;AACjC,mBAAOI,OAAO4E,GAAP,CAAWhF,MAAlB;AACH,SAFD,MAGK,IAAIwE,SAASS,UAAb,EAAyB;AAC1B,mBAAOT,SAASS,UAAhB;AACH;AACJ;;AAED,aAASC,WAAT,GAAuB;AACnB,YAAIT,KAAKU,gBAAL,CAAsB,UAAtB,EAAkC,WAAlC,EAA+C/G,MAA/C,GAAwD,CAA5D,EAA+D;AAC3D,mBAAOsF,EAAE,qBAAF,EAAyB,qCAAzB,CAAP;AACH;AACJ;;AAED,aAAS0B,IAAT,GAAgB;AACZxC,UAAExC,MAAF,EAAUiF,EAAV,CAAa,cAAb,EAA6BH,WAA7B;AACH;;AAED;AACA,aAASI,WAAT,CAAqBC,IAArB,EAA2B;AACvB;AACA,YAAIrF,EAAEsF,WAAF,CAAcD,KAAKE,gBAAnB,CAAJ,EAA0C;AACtC,mBAAOF,KAAKG,YAAZ;AACH;AACD,eAAOH,KAAKE,gBAAL,CAAsBE,OAAtB,EAAP;AACH;;AAED;AACA,aAASC,kBAAT,CAA4BC,IAA5B,EAAkC;AAC9B,YAAItI,OAAO2C,EAAEI,IAAF,CAAOyE,YAAYe,WAAnB,EAAgC,EAAElC,MAAMiC,KAAKE,UAAb,EAAhC,CAAX;AACA,YAAIxI,QAAQ,IAAZ,EAAkB;AACdA,mBAAO2C,EAAEI,IAAF,CAAOyE,YAAYe,WAAnB,EAAgC,EAAEE,SAAS,IAAX,EAAhC,CAAP;AACH;AACD,YAAIzI,QAAQ,IAAZ,EAAkB;AACd,mBAAO,EAAP;AACH;AACD,eAAOA,KAAK0I,QAAL,CAAcjE,OAAd,CAAsB,GAAtB,EAA2B,EAA3B,CAAP;AACH;;AAED;AACA,aAASkE,eAAT,GAA2B;AACvBhG,UAAEiG,OAAF,CAAU1B,KAAKK,KAAf,EAAsB,UAAUsB,IAAV,EAAgB;AAClC,gBAAIA,KAAKC,MAAL,KAAgB,OAAhB,IAA2BD,KAAKC,MAAL,KAAgB,SAA/C,EACID,KAAKC,MAAL,GAAc,OAAd;AACP,SAHD;AAIAzD,UAAExC,MAAF,EAAUkG,GAAV,CAAc,cAAd,EAA8BpB,WAA9B;AACAtI,6BAAWkE,MAAX,CAAkB4C,EAAE,qBAAF,EAAyB,iBAAzB,CAAlB;AACAnB,iBAASjF,IAAT,GAAgByH,YAAYwB,QAAZ,GAAuB,iBAAvB,GAA2ChE,SAASjF,IAApE;AACH;;AAED,aAASkJ,WAAT,CAAqBC,MAArB,EAA6B;AACzB,YAAIA,SAASA,UAAU,EAAvB;AACA,YAAI/B,YAAY,IAAhB,EAAsB;AAClBC,wBAAYzE,EAAEwG,QAAF,CAAW,mBAAX,CAAZ;AACA,gBAAIlC,SAASmC,UAAT,KAAwB/I,SAAxB,IAAqC4G,SAASmC,UAAlD,EAA8D;AAC1D;AACAjC,2BAAW9B,EAAE,gBAAgB+B,SAAhB,GAA4B,YAA5B,GAA2C8B,MAA3C,GAAoD,+CAAtD,CAAX;AACH,aAHD,MAIK;AACD;AACA/B,2BAAW9B,EAAE,gBAAgB+B,SAAhB,GAA4B,YAA5B,GAA2C8B,MAA3C,GAAoD,sCAAtD,CAAX;AACH;AACD7D,cAAE,MAAF,EAAUgE,MAAV,CAAiBlC,QAAjB;AACH;AACJ;;AAED,aAASmC,cAAT,GAA0B;AACtB,YAAInC,YAAY,IAAhB,EAAsB;AAClBA,qBAASoC,MAAT,CAAgB,QAAhB;AACApC,qBAASqC,MAAT,GAFkB,CAEC;AACnBrC,uBAAW,IAAX;AACH;AACJ;;AAED;AACA,aAASsC,OAAT,CAAiBC,KAAjB,EAAwBtD,IAAxB,EAA8B;AAC1Bf,UAAE6B,IAAF,EAAQuC,OAAR,CAAgBC,KAAhB,EAAuBtD,IAAvB;AACH;;AAED;AACA,SAAK0B,EAAL,GAAU,UAAUzG,IAAV,EAAgBsI,IAAhB,EAAsB;AAC5B;AACAtE,UAAE6B,IAAF,EAAQY,EAAR,CAAWzG,IAAX,EAAiBsI,IAAjB;AACH,KAHD;AAIA,SAAKZ,GAAL,GAAW,UAAU1H,IAAV,EAAgBsI,IAAhB,EAAsB;AAC7BtE,UAAE6B,IAAF,EAAQ6B,GAAR,CAAY1H,IAAZ,EAAkBsI,IAAlB;AACH,KAFD;;AAIA;AACA,SAAKC,YAAL,GAAoB,UAASrC,KAAT,EAAgBlB,IAAhB,EAAsBwD,YAAtB,EAAoCC,QAApC,EAA8CC,YAA9C,EAA4D;AAC5E,YAAI/J,OAAO2C,EAAEI,IAAF,CAAOyE,YAAYe,WAAnB,EAAgC,EAAE,QAAQlC,IAAV,EAAhC,CAAX;;AAEA,YAAI2D,UAAU;AACVxB,wBAAYxI,OAAOA,KAAKqG,IAAZ,GAAmB,EADrB;AAEV4D,mBAAO1C,MAAM2C,GAAN,CAAU,UAAC5B,IAAD,EAAU;AACvB,uBAAO;AACH6B,8BAAU7B,KAAK6B,QADZ;AAEHnC,0BAAMM,KAAKN,IAFR;AAGHoC,8BAAU9B,KAAK8B;AAHZ,iBAAP;AAKH,aANM,CAFG;AASV,wBAAY;AACR/I,sBAAM;AADE,aATF;AAYVwI,0BAAcA,YAZJ;AAaVC,sBAAUA,QAbA;AAcVC,0BAAcA,YAdJ;AAeVjB,oBAAQ,OAfE;AAgBVuB,sBAAU;AAhBA,SAAd;AAkBA,eAAOL,OAAP;AACH,KAtBD;;AAwBA,SAAKM,eAAL,GAAuB,UAASL,KAAT,EAAe;AAClC,YAAIM,SAAS,EAAb;AACA5H,UAAEiG,OAAF,CAAUqB,KAAV,EAAiB,UAAUpB,IAAV,EAAgB;AAC7B,gBAAIrG,MAAM,MAAMnD,qBAAW6C,YAAX,CAAwB2G,KAAKxH,IAA7B,CAAhB;AACAkJ,mBAAOjK,IAAP,CAAY;AACRkI,4BAAYnJ,qBAAWkD,YAAX,CAAwBC,GAAxB,EAA6BgF,WAA7B,EAA0CnB,IAD9C;AAER8D,0BAAUtB,KAAKxH,IAFP;AAGR+I,0BAAU;AACN/I,0BAAMhC,qBAAWgD,WAAX,CAAuBwG,KAAKxH,IAA5B,CADA;AAENmB,yBAAKA;AAFC,iBAHF;AAORsG,wBAAQ,OAPA;AAQRuB,0BAAU,CARF;AASRrC,sBAAMa;AATE,aAAZ;AAWH,SAbD;AAcA,eAAO0B,MAAP;AACH,KAjBD;;AAmBA;AACA,SAAKC,gBAAL,GAAwB,UAAUrK,QAAV,EAAoBsK,MAApB,EAA4BvB,MAA5B,EAAoC;AACxD,YAAIhC,OAAO,IAAX;AACA;AACA;AACAoC;AACAL,oBAAYC,MAAZ;AACA,YAAIuB,WAAW,IAAf,EAAqB;AACjBtD,qBAASuD,UAAT,CAAoB,UAApB;AACH,SAFD,MAEO;AACHvD,qBAASwD,IAAT,CAAc,UAAd,EAA0B,UAA1B;AACH;AACDxD,iBAASW,EAAT,CAAY,QAAZ,EAAsB,YAAY;AAC9B,gBAAIX,SAASyD,GAAT,OAAmB,EAAvB,EAA2B;AACvB;AACH;AACD,gBAAIX,QAAQ9C,SAAS,CAAT,EAAY8C,KAAxB;AACA,gBAAIQ,WAAW,IAAf,EAAqB;AACjBR,wBAAQA,MAAM,CAAN,CAAR;AACH;AACD9J,qBAAS+G,KAAKoD,eAAL,CAAqBL,KAArB,CAAT;AACAhB,wBAAYC,MAAZ;AACH,SAVD;AAWA/B,iBAAS0D,KAAT,CAAe,UAAU1I,CAAV,EAAa;AACxBA,cAAE2I,eAAF;AACH,SAFD;AAGA3D,iBAASsC,OAAT,CAAiB,OAAjB;AACH,KA1BD;;AA4BA;AACA,SAAKsB,UAAL,GAAkB,UAAUC,GAAV,EAAeC,MAAf,EAAuB;AACrC,YAAIC,OAAO,EAAX;AACA,gBAAQD,OAAOnB,QAAf;AACI,iBAAK,CAAL;AAAQ;AACJ,oBAAI,CAACnH,EAAEwI,OAAF,CAAUH,GAAV,CAAL,EAAqB;AACjBA,0BAAM,CAACA,GAAD,CAAN;AACH;AACDrI,kBAAEiG,OAAF,CAAUoC,GAAV,EAAe,UAAU1C,IAAV,EAAgB;AAC3B,wBAAIA,KAAK2B,KAAL,IAAc3B,KAAK2B,KAAL,CAAWpJ,MAAX,GAAoB,CAAtC,EAAyC;AACrCyH,6BAAK2B,KAAL,GAAatH,EAAEuH,GAAF,CAAM5B,KAAK2B,KAAX,EAAkB,UAAUjC,IAAV,EAAgB;AAC3C,mCAAO;AACHA,sCAAMA,KAAKA,IADR;AAEHmC,0CAAUnC,KAAKA,IAAL,CAAU3G,IAFjB;AAGH+J,0CAAUpD,KAAKA,IAAL,CAAU/E,IAHjB;AAIHoI,sDAAsBtD,YAAYC,KAAKA,IAAjB,CAJnB;AAKHhI,sCAAMsL,OAAOtD,KAAKhI,IAAZ,IAAkBsL,OAAOtD,KAAKhI,IAAZ,CAAlB,GAAoC;AALvC,6BAAP;AAOH,yBARY,CAAb;AASA,4BAAIsI,KAAKN,IAAT,EAAc;AACVM,iCAAK2B,KAAL,CAAWsB,OAAX,CAAmB;AACfvD,sCAAMM,KAAKN,IADI;AAEfmC,0CAAU7B,KAAKN,IAAL,CAAU3G,IAFL;AAGf+J,0CAAU9C,KAAKN,IAAL,CAAU/E,IAHL;AAIfoI,sDAAsBtD,YAAYO,KAAKN,IAAjB,CAJP;AAKfhI,sCAAMsI,KAAKtI,IAAL,KAAcK,SAAd,GAA0BiL,OAAOhD,KAAKtI,IAAZ,CAA1B,GAA8C;AALrC,6BAAnB;AAOH;AACJ,qBAnBD,MAoBK;AACDsI,6BAAK2B,KAAL,GAAa,CAAC;AACVjC,kCAAMM,KAAKN,IADD;AAEVmC,sCAAU7B,KAAKN,IAAL,CAAU3G,IAFV;AAGV+J,sCAAU9C,KAAKN,IAAL,CAAU/E,IAHV;AAIVoI,kDAAsBtD,YAAYO,KAAKN,IAAjB,CAJZ;AAKVhI,kCAAMsI,KAAKtI,IAAL,KAAcK,SAAd,GAA0BiL,OAAOhD,KAAKtI,IAAZ,CAA1B,GAA8C;AAL1C,yBAAD,CAAb;AAOH;AACD,2BAAOsI,KAAKN,IAAZ;AACAkD,yBAAK5K,IAAL,CAAUgI,IAAV;AACH,iBAhCD;AAiCA;AACJ,iBAAK,CAAL;AACA,iBAAK,CAAL;AACI3F,kBAAEiG,OAAF,CAAUoC,IAAIf,KAAd,EAAqB,UAAUjC,IAAV,EAAgB;AACjCA,yBAAKmC,QAAL,GAAgBnC,KAAKA,IAAL,CAAU3G,IAA1B;AACA2G,yBAAKoD,QAAL,GAAgBpD,KAAKA,IAAL,CAAU/E,IAA1B;AACA+E,yBAAKqD,oBAAL,GAA4BtD,YAAYC,KAAKA,IAAjB,CAA5B;AACH,iBAJD;AAKAkD,qBAAK5K,IAAL,CAAU0K,GAAV;AACA;AA/CR;AAiDArI,UAAEiG,OAAF,CAAUsC,IAAV,EAAgB,UAAU5C,IAAV,EAAgB;AAC5BA,iBAAKwB,QAAL,GAAgBmB,OAAOnB,QAAvB;AACAxB,iBAAKyB,YAAL,GAAoBkB,OAAOlB,YAA3B;AACAzB,iBAAKuB,YAAL,GAAoBoB,OAAOpB,YAA3B;AACAvB,iBAAKkD,mBAAL,GAA2BP,OAAOO,mBAAlC;AACAlD,iBAAKmD,iBAAL,GAAyBR,OAAOQ,iBAAhC;AACAvE,iBAAKwE,OAAL,CAAapD,IAAb;AACH,SAPD;AAQH,KA3DD;;AA6DA;AACA,aAASqD,qBAAT,CAA+BC,aAA/B,EAA8C5D,IAA9C,EAAoD;AAChD,YAAI,CAAC4D,aAAL,EACI;AACJ,YAAIA,cAAcC,UAAd,KAA6B,QAAjC,EACI,OAAOC,uBAAuBF,aAAvB,EAAsC5D,IAAtC,CAAP;AACJ,YAAI4D,cAAcC,UAAd,KAA6B,SAAjC,EACI;AACJ,YAAIZ,SAAS;AACTc,oBAAQH,cAAcI,UADb;AAETC,iBAAKjE,KAAKkE,YAAL,CAAkBzH,OAAlB,CAA0B,KAA1B,EAAiC,GAAjC;AAFI,SAAb;AAIA0H,oBAAYP,aAAZ;AACA,YAAI5D,KAAKoD,QAAL,IAAkB,IAAI,IAAJ,GAAW,IAAjC,EACI;AACJ,YAAIgB,sBAAsB9E,GAAG+E,qBAAH,CAAyBpB,MAAzB,EAAiCqB,OAAjC,EAA1B;AACAtE,aAAKoE,mBAAL,GAA2BA,mBAA3B;AACA,YAAI7G,QAAQF,EAAEG,QAAF,EAAZ;AACAwC,aAAKoE,mBAAL,CAAyBtG,IAAzB,CAA8B,UAAUyG,QAAV,EAAoB;AAC9CvE,iBAAKwE,QAAL,GAAgBD,SAASE,QAAzB;AACA;;;;;AAKAlH,kBAAM1D,OAAN;AACH,SARD,EAQG6K,KARH,CAQS,UAAUC,GAAV,EAAe;AACpBpH,kBAAMzD,MAAN;AACAmE,oBAAQ2G,IAAR,CAAaD,GAAb,EAFoB,CAEF;AACrB,SAXD;AAYA,eAAOpH,MAAM+G,OAAN,EAAP;AACH;;AAED,aAASH,WAAT,CAAqBP,aAArB,EAAoC;AAChC,YAAIiB,cAAc;AACdC,yBAAalB,cAAckB,WADb;AAEdC,6BAAiBnB,cAAcoB,eAFjB;AAGdC,sBAAUrB,cAAcqB,QAHV;AAIdC,wBAAY,CAACtB,cAAcuB,OAJb;AAKdC,8BAAkB,IALJ;AAMdC,8BAAkB,MAAMzB,cAAc0B,OANxB;AAOdC,wBAAY;AAPE,SAAlB,CADgC,CAS9B;AACF,YAAI3B,cAAc4B,MAAlB,EACIC,IAAIhL,MAAJ,CAAW+K,MAAX,GAAoB5B,cAAc4B,MAAlC,CAX4B,CAWc;AAC9C,YAAIhG,YAAYkG,gBAAZ,IAAgCb,YAAYI,QAAhD,EAA0D;AACtD,gBAAIlN,OAAO8C,OAAOmC,QAAP,CAAgB2I,QAAhB,GAA2B,IAA3B,GAAkC9K,OAAOmC,QAAP,CAAgB4I,IAA7D;AACAf,wBAAYI,QAAZ,GAAuBlN,IAAvB;AACH;AACD,YAAI6L,cAAcC,UAAd,KAA6B,QAAjC,EACIvE,KAAK,IAAImG,IAAII,EAAR,CAAWhB,WAAX,CAAL,CADJ,KAGIvF,KAAK,IAAIwG,IAAID,EAAR,CAAWhB,WAAX,CAAL;AACP;;AAED;AACA,aAASf,sBAAT,CAAgCF,aAAhC,EAA+C5D,IAA/C,EAAqD;AACjD,YAAI,CAAC4D,aAAD,IAAkBA,cAAcC,UAAd,KAA6B,QAAnD,EACI;AACJM,oBAAYP,aAAZ;AACA,YAAI5D,KAAKoD,QAAL,IAAkB,IAAI,IAAJ,GAAW,IAAjC,EACI;AACJ,YAAIH,SAAS;AACTc,oBAAQH,cAAcI,UADb;AAETC,iBAAKjE,KAAKkE,YAAL,CAAkBzH,OAAlB,CAA0B,KAA1B,EAAiC,GAAjC;AAFI,SAAb;AAIA,YAAI2H,sBAAsB9E,GAAG+E,qBAAH,CAAyBpB,MAAzB,EAAiCqB,OAAjC,EAA1B;AACAtE,aAAKoE,mBAAL,GAA2BA,mBAA3B;AACA,YAAI7G,QAAQF,EAAEG,QAAF,EAAZ;AACAwC,aAAKoE,mBAAL,CAAyBtG,IAAzB,CAA8B,UAAUyG,QAAV,EAAoB;AAC9CvE,iBAAKwE,QAAL,GAAgBD,SAASE,QAAzB;AACA;;;;;AAKAlH,kBAAM1D,OAAN;AACH,SARD,EAQG6K,KARH,CAQS,UAAUC,GAAV,EAAe;AACpBpH,kBAAMzD,MAAN;AACAmE,oBAAQ2G,IAAR,CAAaD,GAAb,EAFoB,CAEF;AACrB,SAXD;AAYA,eAAOpH,MAAM+G,OAAN,EAAP;AACH;;AAED;AACA,aAASyB,QAAT,CAAkBzF,IAAlB,EAAwB;AACpBmB,gBAAQ,kBAAR,EAA4BnB,IAA5B;AACA,YAAIiF,aAAalO,qBAAW0E,SAAX,CAAqB,YAArB,CAAjB;AACA,YAAItE,MAAM+H,YAAYwG,MAAZ,GAAqB,wBAA/B;AACA,YAAIT,cAAcA,eAAe,YAAjC,EAA8C;AAC1C9N,kBAAM+H,YAAYwG,MAAZ,GAAqB,iCAA3B;AACH;AACD,YAAI/G,SAASgH,UAAb,EAAwB;AACpBxO,mBAAO,YAAYwH,SAASgH,UAA5B;AACH;AACD,YAAI3F,KAAK8B,QAAL,IAAiB9B,KAAK8B,QAAL,CAAc8D,KAAnC,EAA0C;AACtC;AACA,gBAAI9C,WAAW9C,KAAK8B,QAAL,CAAc8D,KAAd,CAAoBnL,IAApB,CAAyB,UAAUd,CAAV,EAAa;AAAE,uBAAOA,EAAEkM,SAAF,KAAgB,UAAvB;AAAoC,aAA5E,CAAf;AACA,gBAAI/C,QAAJ,EAAc;AACVA,yBAASgD,KAAT,GAAiBC,OAAOjD,SAASgD,KAAhB,CAAjB;AACH;AACJ;AACDnJ,iBAASsB,IAAT,CAAc9G,GAAd,EAAmB6I,IAAnB,EAAyBxC,IAAzB,CAA8B,UAAUC,GAAV,EAAe;AACzCA,kBAAMA,IAAIK,IAAV;AACA,iBAAK,IAAIzF,IAAI,CAAb,EAAgBA,IAAIoF,IAAIkE,KAAJ,CAAUpJ,MAA9B,EAAsCF,GAAtC,EAA2C;AACvC,oBAAIqH,OAAOM,KAAK2B,KAAL,CAAWtJ,CAAX,EAAcqH,IAAzB;AACAM,qBAAK2B,KAAL,CAAWtJ,CAAX,IAAgBoF,IAAIkE,KAAJ,CAAUtJ,CAAV,CAAhB;AACA2H,qBAAK2B,KAAL,CAAWtJ,CAAX,EAAcqH,IAAd,GAAqBA,IAArB;AACAM,qBAAK2B,KAAL,CAAWtJ,CAAX,EAAcmI,MAAd,GAAuB,UAAvB;AACAR,qBAAK2B,KAAL,CAAWtJ,CAAX,EAAc2N,cAAd,GAA+BjP,qBAAW2D,UAAX,CAAsB+C,IAAIkE,KAAJ,CAAUtJ,CAAV,EAAayK,QAAnC,CAA/B;AACA9C,qBAAK2B,KAAL,CAAWtJ,CAAX,EAAc0J,QAAd,GAAyB,CAAzB;AACA/B,qBAAKiG,SAAL,IAAkBxI,IAAIkE,KAAJ,CAAUtJ,CAAV,EAAayK,QAA/B;AACA9C,qBAAKkG,UAAL,IAAmBzI,IAAIkE,KAAJ,CAAUtJ,CAAV,EAAa6N,UAAhC;AACH;AACDlG,iBAAKmG,MAAL,GAAc1I,IAAI0I,MAAlB;AACAnG,iBAAKE,UAAL,GAAkBzC,IAAIyC,UAAtB;AACAF,iBAAKoG,SAAL,GAAiB3I,IAAI2I,SAArB;AACApG,iBAAKuB,YAAL,GAAoB9D,IAAI8D,YAAxB;AACAvB,iBAAKqG,gBAAL,GAAwB5I,IAAI4I,gBAA5B;AACArG,iBAAKsG,UAAL,GAAkB7I,IAAI6I,UAAtB;AACAtG,iBAAKI,QAAL,GAAgB3C,IAAI2C,QAApB;AACAJ,iBAAKQ,MAAL,GAAc,UAAd;AACAR,iBAAKuG,MAAL,GAAc,IAAd;AACAvG,iBAAKwG,eAAL,GAAuBzP,qBAAW2D,UAAX,CAAsBsF,KAAKiG,SAA3B,CAAvB;AACAjG,iBAAKyG,UAAL,GAAkBhJ,IAAIgJ,UAAtB;AACAzG,iBAAKsD,aAAL,GAAqB7F,IAAI6F,aAAzB;AACAnC,oBAAQ,mBAAR,EAA6BnB,IAA7B;AACApB,iBAAK8H,aAAL;AACH,SA1BD,EA0BG,UAAUjJ,GAAV,EAAe;AACd0D,oBAAQ,iBAAR,EAA2B,CAACnB,IAAD,EAAOvC,GAAP,CAA3B;AACA,gBAAIA,IAAI+C,MAAJ,KAAe,GAAnB,EAAwB;AACpBH,gCAAgBzB,KAAKK,KAArB;AACA;AACH,aAHD,MAGO;AACHe,qBAAKQ,MAAL,GAAc,OAAd;AACAnG,kBAAEiG,OAAF,CAAUN,KAAK2B,KAAf,EAAsB,UAAUjC,IAAV,EAAgB;AAAEA,yBAAKc,MAAL,GAAc,OAAd;AAAwB,iBAAhE;AACAzJ,qCAAWkE,MAAX,CAAkB4C,EAAE,SAAF,EAAa,cAAb,EAA6B,EAAE8I,MAAMlJ,IAAIG,KAAJ,CAAUgJ,IAAV,IAAkBnJ,IAAIG,KAAJ,CAAUI,KAApC,EAA7B,CAAlB;AACAY,qBAAK8H,aAAL;AACH;AACJ,SArCD;AAsCH;;AAED;AACA,SAAKtD,OAAL,GAAe,UAAUpD,IAAV,EAAgB;AAC3BA,aAAKQ,MAAL,GAAc,MAAd;AACAR,aAAK+B,QAAL,GAAgB/B,KAAKiG,SAAL,GAAiBjG,KAAK6G,aAAL,GAAqB7G,KAAKkG,UAAL,GAAkB,CAAxE;AACAlG,aAAKI,QAAL,GAAgBL,mBAAmBC,IAAnB,CAAhB;AACApB,aAAKK,KAAL,CAAWjH,IAAX,CAAgBgI,IAAhB;AACAyF,iBAASzF,IAAT;AACH,KAND;;AAQA;AACA,SAAK0G,aAAL,GAAqB,YAAY;AAC7B,YAAI3E,WAAWnD,KAAKU,gBAAL,CAAsB,UAAtB,CAAf;AACA,YAAIwH,MAAM5H,YAAY6H,gBAAZ,GAA+BhF,SAASxJ,MAAlD;AACA,YAAIuO,OAAO,CAAX,EACI;AACJ,YAAIE,WAAWpI,KAAKU,gBAAL,CAAsB,UAAtB,CAAf;AACA,YAAIwH,MAAME,SAASzO,MAAnB,EACIuO,MAAME,SAASzO,MAAf;AACJ,YAAIuO,QAAQ,CAAZ,EAAe;AACX;AACJ,aAAK,IAAIzO,IAAI,CAAb,EAAgBA,IAAIyO,GAApB,EAAyBzO,GAAzB,EAA8B;AAC1B2O,qBAAS3O,CAAT,EAAY2H,IAAZ,CAAiBQ,MAAjB,GAA0BwG,SAAS3O,CAAT,EAAYqH,IAAZ,CAAiBc,MAAjB,GAA0B,UAApD;AACA5B,iBAAKqI,MAAL,CAAYD,SAAS3O,CAAT,CAAZ;AACH;AACJ,KAdD;;AAgBA;AACA,SAAK4O,MAAL,GAAc,UAAU1G,IAAV,EAAgB;AAC1B,YAAIA,KAAKP,IAAL,CAAUsD,aAAd,EAA6B;AACzB,gBAAI/C,KAAKP,IAAL,CAAUsD,aAAV,CAAwBC,UAAxB,KAAuC,SAA3C,EACI,KAAK2D,cAAL,CAAoB3G,IAApB,EADJ,KAEK,IAAIA,KAAKP,IAAL,CAAUsD,aAAV,CAAwBC,UAAxB,KAAuC,QAA3C,EACD,KAAK4D,WAAL,CAAiB5G,IAAjB,EADC,KAEA,IAAIA,KAAKP,IAAL,CAAUsD,aAAV,CAAwBC,UAAxB,KAAuC,OAA3C,EACD,KAAK6D,aAAL,CAAmB7G,IAAnB,EADC,KAGD,KAAK8G,UAAL,CAAgB9G,IAAhB;AACP,SATD,MAUK;AACD,iBAAK+G,eAAL,CAAqB/G,IAArB;AACH;AACJ,KAdD;;AAgBA;AACA,aAASgH,YAAT,CAAsBhH,IAAtB,EAA4BzC,IAA5B,EAAkCjG,QAAlC,EAA4C;AACxC,YAAIqH,YAAYsI,kBAAhB,EAAoC;AAChC,gBAAIjH,KAAKb,IAAL,CAAU+H,UAAV,IAAwB,IAA5B,EACIlH,KAAKb,IAAL,CAAU+H,UAAV,GAAuB,IAAIC,UAAJ,EAAvB;AACJnH,iBAAKb,IAAL,CAAU+H,UAAV,CAAqBjP,MAArB,GAA8B,UAAUqB,CAAV,EAAa;AACvC,oBAAI8N,QAAQ,IAAIlJ,QAAJ,EAAZ;AACAkJ,sBAAMC,YAAN,CAAmB/N,EAAEgO,MAAF,CAAS5F,MAA5B;AACA,oBAAI6F,MAAMH,MAAMI,GAAN,EAAV;AACAJ,sBAAMK,OAAN;AACAnQ,yBAASiQ,GAAT;AACH,aAND;AAOAvH,iBAAKb,IAAL,CAAU+H,UAAV,CAAqBQ,OAArB,GAA+B,UAAUpO,CAAV,EAAa;AAAEqO,iCAAiB3H,IAAjB;AAAyB,aAAvE;AACAA,iBAAKb,IAAL,CAAU+H,UAAV,CAAqBU,kBAArB,CAAwCrK,IAAxC;AACH,SAZD,MAYO;AACHjG,qBAAS,EAAT;AACH;AACJ;;AAED;AACA,aAASuQ,eAAT,CAAyBpI,IAAzB,EAA+BN,IAA/B,EAAqC;AACjC,YAAIA,KAAK2I,SAAL,IAAkB,IAAtB,EAA4B;AACxB,gBAAI,CAAChO,EAAEiO,QAAF,CAAW5I,KAAK6I,WAAhB,CAAL,EACI7I,KAAK6I,WAAL,GAAmB,CAAnB;AACJ7I,iBAAK6I,WAAL,GAAmB,CAAC,IAAIC,IAAJ,KAAa9I,KAAK2I,SAAnB,KAAiC3I,KAAKwG,UAAL,GAAkBxG,KAAK+I,UAAxD,CAAnB;;AAEA,gBAAI,CAACpO,EAAEiO,QAAF,CAAWtI,KAAKuI,WAAhB,CAAL,EACIvI,KAAKuI,WAAL,GAAmB,CAAnB;AACJvI,iBAAKuI,WAAL,GAAmB,CAAC,IAAIC,IAAJ,KAAa9I,KAAK2I,SAAnB,KAAiCrI,KAAKkG,UAAL,GAAkBlG,KAAK6G,aAAxD,CAAnB;AACH;AACDnH,aAAK2I,SAAL,GAAiB,IAAIG,IAAJ,EAAjB;AACH;;AAED;AACA,aAASN,gBAAT,CAA0B3H,IAA1B,EAAgC;AAC5B,YAAI,CAACA,KAAKb,IAAL,CAAUgJ,cAAV,CAAyB,YAAzB,CAAL,EACInI,KAAKb,IAAL,CAAUiJ,UAAV,GAAuB,CAAvB;AACJ,YAAIpI,KAAKb,IAAL,CAAUiJ,UAAV,GAAuB,CAA3B,EAA8B;AAC1BpI,iBAAKb,IAAL,CAAUiJ,UAAV;AACA5J,6BAAiBwB,KAAKb,IAAL,CAAUkJ,MAA3B,IAAqCC,WAAW,YAAY;AAAEjK,qBAAKqI,MAAL,CAAY1G,IAAZ;AAAoB,aAA7C,EAA+C,IAA/C,CAArC;AACH,SAHD,MAGO;AACHA,iBAAKP,IAAL,CAAUQ,MAAV,GAAmBD,KAAKb,IAAL,CAAUc,MAAV,GAAmB,OAAtC;AACAW,oBAAQ,mBAAR,EAA6BZ,KAAKP,IAAlC;AACApB,iBAAK8H,aAAL;AACH;AACJ;;AAED,SAAKY,eAAL,GAAuB,UAAU/G,IAAV,EAAgB;AACnC,YAAIb,OAAOa,KAAKb,IAAhB;AACA,YAAIM,OAAOO,KAAKP,IAAhB;AACA,YAAIN,KAAK+I,UAAL,GAAkB/I,KAAKwG,UAA3B,EAAuC;AACnCkC,4BAAgBpI,IAAhB,EAAsBN,IAAtB;AACA,gBAAIoJ,QAAQpJ,KAAK+I,UAAL,GAAkB/I,KAAKqJ,SAAnC;AAAA,gBACIhB,MAAMiB,KAAKC,GAAL,CAASvJ,KAAKoD,QAAd,EAAwBgG,QAAQpJ,KAAKqJ,SAArC,CADV;AAAA,gBAEIG,OAAO,IAAIC,QAAJ,EAFX;AAAA,gBAGIrL,OAAO4B,KAAKA,IAAL,CAAU0J,KAAV,CAAgBN,KAAhB,EAAuBf,GAAvB,CAHX;AAIA,gBAAIjK,QAAQ,IAAR,IAAgBA,KAAKnD,IAAL,IAAa,CAAjC,EAAoC;AAChC4F,qBAAKP,IAAL,CAAUQ,MAAV,GAAmBD,KAAKb,IAAL,CAAUc,MAAV,GAAmB,OAAtC;AACAW,wBAAQ,mBAAR,EAA6BZ,KAAKP,IAAlC;AACAO,qBAAKb,IAAL,CAAUA,IAAV,GAAiB,IAAjB;AACAd,qBAAK8H,aAAL;AACA;AACH;AACDa,yBAAahH,IAAb,EAAmBzC,IAAnB,EAAyB,UAAUgK,GAAV,EAAe;AACpCoB,qBAAKnI,MAAL,CAAY,UAAZ,EAAwBjD,IAAxB;AACAoL,qBAAKnI,MAAL,CAAY,QAAZ,EAAsBrB,KAAKyG,MAA3B;AACA+C,qBAAKnI,MAAL,CAAY,QAAZ,EAAsBrB,KAAKkJ,MAA3B;AACAM,qBAAKnI,MAAL,CAAY,YAAZ,EAA0BrB,KAAK+I,UAA/B;AACAS,qBAAKnI,MAAL,CAAY,KAAZ,EAAmB+G,GAAnB;AACA;;AAEA;AACA;AACA,oBAAI9H,KAAKQ,MAAL,KAAgB,WAApB,EAAiC;AAC7B5B,yBAAKyK,SAAL,CAAerJ,IAAf;AACA;AACH;AACD,oBAAIiF,aAAalO,qBAAW0E,SAAX,CAAqB,YAArB,CAAjB;AACA,oBAAItE,MAAM,mBAAV;AACA,oBAAI8N,cAAcA,eAAe,YAAjC,EAA8C;AAC1C9N,0BAAM,4BAAN;AACH;AACD,oBAAIwH,SAASgH,UAAb,EAAwB;AACpBxO,2BAAO,YAAYwH,SAASgH,UAA5B;AACH;AACDhJ,yBAASsB,IAAT,CAAciB,YAAYwG,MAAZ,GAAqBvO,GAAnC,EAAwC+R,IAAxC,EAA8C;AAC1C9K,iCAAa,KAD6B;AAE1CC,iCAAa;AAF6B,iBAA9C,EAGGb,IAHH,CAGQ,UAAUC,GAAV,EAAe;AACnB,wBAAIuC,KAAKQ,MAAL,KAAgB,WAApB,EAAiC;AAC7B5B,6BAAKyK,SAAL,CAAerJ,IAAf;AACA;AACH;AACDvC,0BAAMA,IAAIK,IAAV;AACA4B,2BAAOrF,EAAEI,IAAF,CAAOuF,KAAK2B,KAAZ,EAAmB,EAAEiH,QAAQnL,IAAImL,MAAd,EAAnB,CAAP;AACAlJ,yBAAK+I,UAAL,GAAkBhL,IAAIgL,UAAtB;AACA,wBAAIhL,IAAI6L,UAAJ,KAAmB,CAAvB,EAA0B;AAAE;AACxB5J,6BAAKqC,QAAL,GAAgB/B,KAAK+B,QAAL,GAAgB,GAAhC;AACArC,6BAAK6I,WAAL,GAAmBvI,KAAKuI,WAAL,GAAmB,IAAtC;AACA,+BAAO7I,KAAK+H,UAAZ;AACA/H,6BAAKc,MAAL,GAAcR,KAAKQ,MAAL,GAAc,SAA5B;AACA5B,6BAAK8H,aAAL;AACA6C,qCAAavJ,IAAb,EAAmBxC,IAAnB,CAAwB,YAAU;AAC9B2D,oCAAQ,qBAAR,EAA+BnB,IAA/B;AACH,yBAFD;AAGH,qBATD,MASO;AACHA,6BAAK+B,QAAL,GAAgByH,iBAAiBxJ,IAAjB,CAAhB;AACAN,6BAAKqC,QAAL,GAAgB0H,iBAAiB/J,IAAjB,CAAhB;AACAA,6BAAKc,MAAL,GAAcR,KAAKQ,MAAL,GAAc,UAA5B;AACAW,gCAAQ,sBAAR,EAAgCnB,IAAhC;AACAN,6BAAKiJ,UAAL,GAAkB,CAAlB;AACA/J,6BAAKqI,MAAL,CAAY1G,IAAZ;AACH;AACJ,iBA5BD,EA4BG,UAAU9C,GAAV,EAAe;AACd,wBAAIA,IAAI+C,MAAJ,KAAe,GAAnB,EAAwB;AACpBH;AACA;AACH;AACD6H,qCAAiB3H,IAAjB;AACH,iBAlCD;AAmCH,aAzDD;AA0DH,SAvED,MAuEO;AACH,gBAAIb,KAAKgJ,cAAL,CAAoB,YAApB,CAAJ,EAAuC;AACnC,uBAAO3J,iBAAiBW,KAAKkJ,MAAtB,CAAP;AACA,uBAAOlJ,KAAKiJ,UAAZ;AACH;AACD,mBAAOjJ,KAAK+H,UAAZ;AACA/H,iBAAK6I,WAAL,GAAmB,IAAnB;AACA7I,iBAAKqC,QAAL,GAAgB,GAAhB;AACArC,iBAAKc,MAAL,GAAc,SAAd;AACAR,iBAAK+B,QAAL,GAAgByH,iBAAiBxJ,IAAjB,CAAhB;AACAmB,oBAAQ,qBAAR,EAA+BnB,IAA/B;AACApB,iBAAK8H,aAAL;AACH;AACJ,KAvFD;;AAyFA,QAAIgD,aAAa,SAAbA,UAAa,CAAUhK,IAAV,EAAgB;AAC7B,YAAIwJ,OAAO,IAAIC,QAAJ,EAAX;AACAD,aAAKnI,MAAL,CAAY,QAAZ,EAAsBrB,KAAKyG,MAA3B;AACA+C,aAAKnI,MAAL,CAAY,QAAZ,EAAsBrB,KAAKkJ,MAA3B;AACAM,aAAKnI,MAAL,CAAY,YAAZ,EAA0BrB,KAAK+I,UAA/B;AACAS,aAAKnI,MAAL,CAAY,UAAZ,EAAwB,yBAAerB,KAAKiK,QAApB,CAAxB;AACAT,aAAKnI,MAAL,CAAY,UAAZ,EAAwBrB,KAAKwE,QAA7B;AACAgF,aAAKnI,MAAL,CAAY,YAAZ,EAA0BrB,KAAKkK,UAA/B;AACA,YAAI3E,aAAalO,qBAAW0E,SAAX,CAAqB,YAArB,CAAjB;AACA,YAAItE,MAAM,mBAAV;AACA,YAAI8N,cAAcA,eAAe,YAAjC,EAA8C;AAC1C9N,kBAAM,4BAAN;AACH;AACD,YAAIwH,SAASgH,UAAb,EAAwB;AACpBxO,mBAAO,YAAYwH,SAASgH,UAA5B;AACH;AACD,eAAOhJ,SAASsB,IAAT,CAAciB,YAAYwG,MAAZ,GAAqBvO,GAAnC,EAAwC+R,IAAxC,EAA8C;AACjD9K,yBAAa,KADoC;AAEjDC,yBAAa;AAFoC,SAA9C,CAAP;AAIH,KApBD;;AAsBA,QAAIkL,eAAe,SAAfA,YAAe,CAAUvJ,IAAV,EAAgB;AAC/B,YAAI6J,YAAY3K,YAAYwG,MAA5B;AACA,YAAIT,aAAalO,qBAAW0E,SAAX,CAAqB,YAArB,CAAjB;AACA,YAAItE,MAAM,uCAAuC6I,KAAKmG,MAAtD;AACA,YAAIlB,cAAcA,eAAe,YAAjC,EAA8C;AAC1C9N,kBAAM,gDAAgD6I,KAAKmG,MAA3D;AACH;AACD,eAAOxJ,SAASsB,IAAT,CAAc4L,YAAY1S,GAA1B,CAAP;AACH,KARD;;AAUA,SAAK2S,gCAAL,GAAwC,UAAUvJ,IAAV,EAAgB;AACpD,YAAIb,OAAOa,KAAKb,IAAhB;AACA,YAAIM,OAAOO,KAAKP,IAAhB;;AAEA,YAAI+J,YAAY;AACZ7E,oBAAQlF,KAAKsD,aAAL,CAAmB4B,MADf;AAEZV,yBAAaxE,KAAKsD,aAAL,CAAmBkB,WAFpB;AAGZE,6BAAiB1E,KAAKsD,aAAL,CAAmBoB,eAHxB;AAIZsF,oBAAQhK,KAAKsD,aAAL,CAAmBI;AAJf,SAAhB;AAMA,YAAI1D,KAAKsD,aAAL,CAAmBqB,QAAvB,EAAiC;AAC7BoF,sBAAUpF,QAAV,GAAqB3E,KAAKsD,aAAL,CAAmBqB,QAAxC;AACH;AACD,YAAIsF,SAAS,IAAI,KAAKC,GAAL,CAASC,OAAb,CAAqBJ,SAArB,CAAb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAIrK,KAAKoD,QAAL,IAAiB,MAArB,EAA4B;AAC5B;AACIvC,qBAAKkH,UAAL,GAAkB,IAAIC,UAAJ,EAAlB;AACAnH,qBAAKkH,UAAL,CAAgBjP,MAAhB,GAAyB,UAAUqB,CAAV,EAAa;AAClCoQ,2BAAOG,GAAP,CAAW1K,KAAKkE,YAAhB,EAA8B,IAAIyG,MAAJ,CAAW,KAAKpI,MAAhB,CAA9B,EAAuDzE,IAAvD,CAA4D,UAAUC,GAAV,EAAe;AACvEiM,mCAAWhK,IAAX,EAAiBlC,IAAjB,CAAsB,UAAUC,GAAV,EAAe;AACjCA,kCAAMA,IAAIK,IAAV;AACA,gCAAIL,IAAI6L,UAAJ,KAAmB,CAAvB,EAA0B;AAAC;AACvB5J,qCAAKqC,QAAL,GAAgB/B,KAAK+B,QAAL,GAAgB,GAAhC;AACArC,qCAAK6I,WAAL,GAAmBvI,KAAKuI,WAAL,GAAmB,IAAtC;AACA,uCAAO7I,KAAK+H,UAAZ;AACA/H,qCAAKc,MAAL,GAAcR,KAAKQ,MAAL,GAAc,SAA5B;AACA+I,6CAAavJ,IAAb,EAAmBxC,IAAnB,CAAwB,YAAU;AAC9B2D,4CAAQ,qBAAR,EAA+BnB,IAA/B;AACH,iCAFD;AAGH,6BARD,MASK;AACDN,qCAAKc,MAAL,GAAc,SAAd;AACAd,qCAAKqC,QAAL,GAAgB,GAAhB;AACH;AACDnD,iCAAK8H,aAAL;AACH,yBAhBD;AAiBH,qBAlBD,EAkBG,UAAUjJ,GAAV,EAAe;AACdE,gCAAQC,KAAR,CAAcH,GAAd;AACH,qBApBD;AAqBH,iBAtBD;AAuBA8C,qBAAKkH,UAAL,CAAgB6C,iBAAhB,CAAkC5K,KAAKA,IAAvC;AACH,aA3BD,MA4BK;AACD,gBAAI6K,cAAcxT,qBAAWwE,WAAX,CAAuB2D,YAAYsL,gBAAnC,EAAqD,YAAYxK,KAAKmG,MAAtE,CAAlB;AACA,gBAAIxH,SAASgH,UAAb,EAAwB;AACpB4E,8BAAcxT,qBAAWwE,WAAX,CAAuBgP,WAAvB,EAAoC,WAAW5L,SAASgH,UAAxD,CAAd;AACH;AACD,gBAAIzH,OAAO;AACPuM,0BAAU/K,KAAKqJ,SADR;AAEPhH,0BAAU,kBAAU2I,CAAV,EAAaC,UAAb,EAAyBlN,GAAzB,EAA8B;AACpC,2BAAO,UAAUmN,IAAV,EAAgB;AACnB,4BAAI5K,KAAKQ,MAAL,KAAgB,WAApB,EAAiC;AAC7B5B,iCAAKyK,SAAL,CAAerJ,IAAf;AACH,yBAFD,MAEO,IAAIA,KAAKQ,MAAL,KAAgB,SAApB,EAA+B;AAClCoK;AACH,yBAFM,MAGF;AACD,gCAAID,WAAWE,SAAX,CAAqBtS,MAArB,KAAgC,CAApC,EAAsC;AACtC;AACIqS;AACA;AACH;AACDlL,iCAAKiK,QAAL,GAAgBgB,WAAWE,SAAX,CAAqBF,WAAWE,SAAX,CAAqBtS,MAArB,GAA8B,CAAnD,CAAhB;AACAmH,iCAAKwE,QAAL,GAAgByG,WAAWzG,QAA3B;AACAxE,iCAAKkK,UAAL,GAAkB,yBAAee,UAAf,EAA2B,UAAUzO,GAAV,EAAe4J,KAAf,EAAsB;AAC/D,oCAAI5J,QAAQ,MAAZ,EAAoB;AAChB,2CAAOnE,SAAP;AACH;AACD,uCAAO+N,KAAP;AACH,6BALiB,CAAlB;AAMApG,iCAAK+I,UAAL,GAAkBkC,WAAWE,SAAX,CAAqBtS,MAArB,GAA8B,CAAhD,CAdC,CAciD;AAClDmR,uCAAWhK,IAAX,EAAiBlC,IAAjB,CAAsB,UAAUC,GAAV,EAAe;AACjC,oCAAIuC,KAAKQ,MAAL,KAAgB,WAApB,EAAiC;AAC7B5B,yCAAKyK,SAAL,CAAerJ,IAAf;AACA;AACH;AACDvC,sCAAMA,IAAIK,IAAV;AACA4B,qCAAK+I,UAAL,GAAkBhL,IAAIgL,UAAtB;AACA,oCAAIhL,IAAI6L,UAAJ,KAAmB,CAAvB,EAA0B;AAAE;AACxB5J,yCAAKqC,QAAL,GAAgB/B,KAAK+B,QAAL,GAAgB,GAAhC;AACArC,yCAAK6I,WAAL,GAAmBvI,KAAKuI,WAAL,GAAmB,IAAtC;AACA,2CAAO7I,KAAK+H,UAAZ;AACA/H,yCAAKc,MAAL,GAAcR,KAAKQ,MAAL,GAAc,SAA5B;AACAW,4CAAQ,qBAAR,EAA+BnB,IAA/B;AACApB,yCAAK8H,aAAL;AACA;AACH,iCARD,MAQO;AACH,wCAAIhH,KAAK+I,UAAL,GAAkB/I,KAAKwG,UAA3B,EAAuC;AACnClG,6CAAK+B,QAAL,GAAgByH,iBAAiBxJ,IAAjB,CAAhB;AACAN,6CAAKqC,QAAL,GAAgB0H,iBAAiB/J,IAAjB,CAAhB;AACAA,6CAAKc,MAAL,GAAcR,KAAKQ,MAAL,GAAc,UAA5B;AACAW,gDAAQ,sBAAR,EAAgCnB,IAAhC;AACAN,6CAAKiJ,UAAL,GAAkB,CAAlB;AACH,qCAND,MAMO;AACH,4CAAIjJ,KAAKgJ,cAAL,CAAoB,YAApB,CAAJ,EAAuC;AACnC,mDAAO3J,iBAAiBW,KAAKkJ,MAAtB,CAAP;AACA,mDAAOlJ,KAAKiJ,UAAZ;AACH;AACDjJ,6CAAK6I,WAAL,GAAmB,IAAnB;AACA7I,6CAAKqC,QAAL,GAAgB,GAAhB;AACArC,6CAAKc,MAAL,GAAc,SAAd;AACAR,6CAAK+B,QAAL,GAAgByH,iBAAiBxJ,IAAjB,CAAhB;AACAmB,gDAAQ,qBAAR,EAA+BnB,IAA/B;AACApB,6CAAK8H,aAAL;AACH;AACJ;AACDkE;AACH,6BApCD,EAoCG,UAAUnN,GAAV,EAAe;AACd,oCAAIA,IAAI+C,MAAJ,KAAe,GAAnB,EAAwB;AACpBH;AACA;AACH;AACD6H,iDAAiB3H,IAAjB;AACH,6BA1CD;AA2CH;AACJ,qBAjED;AAkEH,iBArEM;AAsEP1I,0BAAU;AACN0S,iCAAaA,WADP;AAENO,kCAAc;AAFR;AAtEH,aAAX;AA2EA,gBAAIpL,KAAKkK,UAAT,EAAoB;AACpB;AACI1L,yBAAKyM,UAAL,GAAkBI,KAAKC,KAAL,CAAWtL,KAAKkK,UAAhB,CAAlB;AACA1L,yBAAKyM,UAAL,CAAgBjL,IAAhB,GAAuBA,KAAKA,IAA5B;AACH;AACDuK,mBAAOgB,eAAP,CAAuBvL,KAAKkE,YAA5B,EAA0ClE,KAAKA,IAA/C,EAAqDxB,IAArD,EAA2DV,IAA3D,CAAgE,UAAUC,GAAV,EAAe;AAC3EE,wBAAQuN,GAAR,CAAY,gBAAZ,EAA8BzN,GAA9B;AACH,aAFD,EAEG,UAAUA,GAAV,EAAe;AACdE,wBAAQuN,GAAR,CAAYzN,GAAZ;AACH,aAJD;AAKH;AACJ,KA5ID;;AA8IA,SAAKyJ,cAAL,GAAsB,UAAU3G,IAAV,EAAgB;AAClC,YAAIb,OAAOa,KAAKb,IAAhB;AACA,YAAIM,OAAOO,KAAKP,IAAhB;;AAEA,YAAI+J,YAAY;AACZ7E,oBAAQlF,KAAKsD,aAAL,CAAmB4B,MADf;AAEZV,yBAAaxE,KAAKsD,aAAL,CAAmBkB,WAFpB;AAGZE,6BAAiB1E,KAAKsD,aAAL,CAAmBoB,eAHxB;AAIZsF,oBAAQhK,KAAKsD,aAAL,CAAmBI;AAJf,SAAhB;AAMA,YAAI1D,KAAKsD,aAAL,CAAmBqB,QAAvB,EAAiC;AAC7BoF,sBAAUpF,QAAV,GAAqB3E,KAAKsD,aAAL,CAAmBqB,QAAxC;AACH;AACD,YAAIsF,SAAS,IAAI,KAAKC,GAAL,CAASC,OAAb,CAAqBJ,SAArB,CAAb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAIrK,KAAKoD,QAAL,IAAiB,MAArB,EAA4B;AAC5B;AACIvC,qBAAKkH,UAAL,GAAkB,IAAIC,UAAJ,EAAlB;AACAnH,qBAAKkH,UAAL,CAAgBjP,MAAhB,GAAyB,UAAUqB,CAAV,EAAa;AAClCoQ,2BAAOG,GAAP,CAAW1K,KAAKkE,YAAhB,EAA8B,IAAIyG,MAAJ,CAAW,KAAKpI,MAAhB,CAA9B,EAAuDzE,IAAvD,CAA4D,UAAUC,GAAV,EAAe;AACvEiM,mCAAWhK,IAAX,EAAiBlC,IAAjB,CAAsB,UAAUC,GAAV,EAAe;AACjCA,kCAAMA,IAAIK,IAAV;AACA,gCAAIL,IAAI6L,UAAJ,KAAmB,CAAvB,EAA0B;AAAC;AACvB5J,qCAAKqC,QAAL,GAAgB/B,KAAK+B,QAAL,GAAgB,GAAhC;AACArC,qCAAK6I,WAAL,GAAmBvI,KAAKuI,WAAL,GAAmB,IAAtC;AACA,uCAAO7I,KAAK+H,UAAZ;AACA/H,qCAAKc,MAAL,GAAcR,KAAKQ,MAAL,GAAc,SAA5B;AACA+I,6CAAavJ,IAAb,EAAmBxC,IAAnB,CAAwB,YAAU;AAC9B2D,4CAAQ,qBAAR,EAA+BnB,IAA/B;AACH,iCAFD;AAGH,6BARD,MASK;AACDN,qCAAKc,MAAL,GAAc,SAAd;AACAd,qCAAKqC,QAAL,GAAgB,GAAhB;AACH;AACDnD,iCAAK8H,aAAL;AACH,yBAhBD;AAiBH,qBAlBD,EAkBG,UAAUjJ,GAAV,EAAe;AACdE,gCAAQC,KAAR,CAAcH,GAAd;AACH,qBApBD;AAqBH,iBAtBD;AAuBA8C,qBAAKkH,UAAL,CAAgB6C,iBAAhB,CAAkC5K,KAAKA,IAAvC;AACH,aA3BD,MA4BK;AACD,gBAAIxB,OAAO;AACPuM,0BAAU/K,KAAKqJ,SADR;AAEPhH,0BAAU,kBAAU2I,CAAV,EAAaC,UAAb,EAAyBlN,GAAzB,EAA8B;AACpC,2BAAO,UAAUmN,IAAV,EAAgB;AACnB,4BAAI5K,KAAKQ,MAAL,KAAgB,WAApB,EAAiC;AAC7B5B,iCAAKyK,SAAL,CAAerJ,IAAf;AACH,yBAFD,MAEO,IAAIA,KAAKQ,MAAL,KAAgB,SAApB,EAA+B;AAClCoK;AACH,yBAFM,MAGF;AACD,gCAAID,WAAWE,SAAX,CAAqBtS,MAArB,KAAgC,CAApC,EAAsC;AACtC;AACIqS;AACA;AACH;AACDlL,iCAAKiK,QAAL,GAAgBgB,WAAWE,SAAX,CAAqBF,WAAWE,SAAX,CAAqBtS,MAArB,GAA8B,CAAnD,CAAhB;AACAmH,iCAAKwE,QAAL,GAAgByG,WAAWzG,QAA3B;AACAxE,iCAAKkK,UAAL,GAAkB,yBAAee,UAAf,EAA2B,UAAUzO,GAAV,EAAe4J,KAAf,EAAsB;AAC/D,oCAAI5J,QAAQ,MAAZ,EAAoB;AAChB,2CAAOnE,SAAP;AACH;AACD,uCAAO+N,KAAP;AACH,6BALiB,CAAlB;AAMApG,iCAAK+I,UAAL,GAAkBkC,WAAWE,SAAX,CAAqBtS,MAArB,GAA8B,CAAhD,CAdC,CAciD;AAClDmR,uCAAWhK,IAAX,EAAiBlC,IAAjB,CAAsB,UAAUC,GAAV,EAAe;AACjC,oCAAIuC,KAAKQ,MAAL,KAAgB,WAApB,EAAiC;AAC7B5B,yCAAKyK,SAAL,CAAerJ,IAAf;AACA;AACH;AACDvC,sCAAMA,IAAIK,IAAV;AACA4B,qCAAK+I,UAAL,GAAkBhL,IAAIgL,UAAtB;AACA,oCAAIhL,IAAI6L,UAAJ,KAAmB,CAAvB,EAA0B;AAAE;AACxB5J,yCAAKqC,QAAL,GAAgB/B,KAAK+B,QAAL,GAAgB,GAAhC;AACArC,yCAAK6I,WAAL,GAAmBvI,KAAKuI,WAAL,GAAmB,IAAtC;AACA,2CAAO7I,KAAK+H,UAAZ;AACA/H,yCAAKc,MAAL,GAAcR,KAAKQ,MAAL,GAAc,SAA5B;AACA5B,yCAAK8H,aAAL;AACA6C,iDAAavJ,IAAb,EAAmBxC,IAAnB,CAAwB,YAAU;AAC9B2D,gDAAQ,qBAAR,EAA+BnB,IAA/B;AACH,qCAFD;AAGH,iCATD,MASO;AACH,wCAAIN,KAAK+I,UAAL,GAAkB/I,KAAKwG,UAA3B,EAAuC;AACnClG,6CAAK+B,QAAL,GAAgByH,iBAAiBxJ,IAAjB,CAAhB;AACAN,6CAAKqC,QAAL,GAAgB0H,iBAAiB/J,IAAjB,CAAhB;AACAA,6CAAKc,MAAL,GAAcR,KAAKQ,MAAL,GAAc,UAA5B;AACAW,gDAAQ,sBAAR,EAAgCnB,IAAhC;AACAN,6CAAKiJ,UAAL,GAAkB,CAAlB;AACH,qCAND,MAMO;AACH,4CAAIjJ,KAAKgJ,cAAL,CAAoB,YAApB,CAAJ,EAAuC;AACnC,mDAAO3J,iBAAiBW,KAAKkJ,MAAtB,CAAP;AACA,mDAAOlJ,KAAKiJ,UAAZ;AACH;AACDjJ,6CAAK6I,WAAL,GAAmB,IAAnB;AACA7I,6CAAKqC,QAAL,GAAgB,GAAhB;AACArC,6CAAKc,MAAL,GAAc,SAAd;AACAR,6CAAK+B,QAAL,GAAgByH,iBAAiBxJ,IAAjB,CAAhB;AACAmB,gDAAQ,qBAAR,EAA+BnB,IAA/B;AACApB,6CAAK8H,aAAL;AACH;AACJ;AACDkE;AACH,6BArCD,EAqCG,UAAUnN,GAAV,EAAe;AACd,oCAAIA,IAAI+C,MAAJ,KAAe,GAAnB,EAAwB;AACpBH;AACA;AACH;AACD6H,iDAAiB3H,IAAjB;AACH,6BA3CD;AA4CH;AACJ,qBAlED;AAmEH;AAtEM,aAAX;AAwEA,gBAAIb,KAAKkK,UAAT,EAAoB;AACpB;AACI1L,yBAAKyM,UAAL,GAAkBI,KAAKC,KAAL,CAAWtL,KAAKkK,UAAhB,CAAlB;AACA1L,yBAAKyM,UAAL,CAAgBjL,IAAhB,GAAuBA,KAAKA,IAA5B;AACH;AACDuK,mBAAOgB,eAAP,CAAuBvL,KAAKkE,YAA5B,EAA0ClE,KAAKA,IAA/C,EAAqDxB,IAArD,EAA2DV,IAA3D,CAAgE,UAAUC,GAAV,EAAe;AAC3EE,wBAAQuN,GAAR,CAAY,gBAAZ,EAA8BzN,GAA9B;AACH,aAFD,EAEG,UAAUA,GAAV,EAAe;AACdE,wBAAQuN,GAAR,CAAYzN,GAAZ;AACH,aAJD;AAKH;AACJ,KArID;;AAuIA,SAAK0N,eAAL,GAAuB,UAAS5K,IAAT,EAAc;AACjC,YAAIb,OAAOa,KAAKb,IAAhB;AACA,YAAIM,OAAOO,KAAKP,IAAhB;;AAEA,YAAI8I,QAAQpJ,KAAK+I,UAAL,GAAkB/I,KAAKqJ,SAAnC;AAAA,YACIhB,MAAMiB,KAAKC,GAAL,CAASvJ,KAAKoD,QAAd,EAAwBgG,QAAQpJ,KAAKqJ,SAArC,CADV;AAAA,YAEIqC,WAAW1L,KAAKA,IAAL,CAAU0J,KAAV,CAAgBN,KAAhB,EAAuBf,GAAvB,CAFf;AAGA,YAAIsD,WAAW;AACX5H,oBAAQzD,KAAKsD,aAAL,CAAmBI,UADhB;AAEXC,iBAAKjE,KAAKkE,YAAL,CAAkBzH,OAAlB,CAA0B,KAA1B,EAAiC,GAAjC,CAFM;AAGXmP,wBAAY5L,KAAK+I,UAAL,GAAkB,CAHnB;AAIXtE,sBAAUzE,KAAKwE,QAJJ;AAKXqH,kBAAMH;AALK,SAAf;AAOAI,YAAIC,WAAJ,CAAgBJ,QAAhB,EAA0B,UAAShH,GAAT,EAAcqH,UAAd,EAA0BC,IAA1B,EAA+B;AACrD,gBAAItH,GAAJ,EAAS;AACL1G,wBAAQC,KAAR,CAAcyG,GAAd;AACA;AACH;AACD,gBAAIrE,KAAKQ,MAAL,KAAgB,WAApB,EAAiC;AAC7B5B,qBAAKyK,SAAL,CAAerJ,IAAf;AACH,aAFD,MAGK;AACD0J,2BAAWhK,IAAX,EAAiBlC,IAAjB,CAAsB,UAAUC,GAAV,EAAe;AACjC,wBAAIuC,KAAKQ,MAAL,KAAgB,WAApB,EAAiC;AAC7B5B,6BAAKyK,SAAL,CAAerJ,IAAf;AACA;AACH;AACDvC,0BAAMA,IAAIK,IAAV;AACA4B,yBAAK+I,UAAL,GAAkBhL,IAAIgL,UAAtB;AACA,wBAAIhL,IAAI6L,UAAJ,KAAmB,CAAvB,EAA0B;AAAE;AACxB5J,6BAAKqC,QAAL,GAAgB/B,KAAK+B,QAAL,GAAgB,GAAhC;AACArC,6BAAK6I,WAAL,GAAmBvI,KAAKuI,WAAL,GAAmB,IAAtC;AACA,+BAAO7I,KAAK+H,UAAZ;AACA/H,6BAAKc,MAAL,GAAcR,KAAKQ,MAAL,GAAc,SAA5B;AACA5B,6BAAK8H,aAAL;AACA6C,qCAAavJ,IAAb,EAAmBxC,IAAnB,CAAwB,YAAU;AAC9B2D,oCAAQ,qBAAR,EAA+BnB,IAA/B;AACH,yBAFD;AAGH,qBATD,MASO;AACH,4BAAIN,KAAK+I,UAAL,GAAkB/I,KAAKwG,UAA3B,EAAuC;AACnClG,iCAAK+B,QAAL,GAAgByH,iBAAiBxJ,IAAjB,CAAhB;AACAN,iCAAKqC,QAAL,GAAgB0H,iBAAiB/J,IAAjB,CAAhB;AACAA,iCAAKc,MAAL,GAAcR,KAAKQ,MAAL,GAAc,UAA5B;AACAW,oCAAQ,sBAAR,EAAgCnB,IAAhC;AACAN,iCAAKiJ,UAAL,GAAkB,CAAlB;AACA/J,iCAAKqI,MAAL,CAAY1G,IAAZ;AACH,yBAPD,MAOO;AACH,gCAAIb,KAAKgJ,cAAL,CAAoB,YAApB,CAAJ,EAAuC;AACnC,uCAAO3J,iBAAiBW,KAAKkJ,MAAtB,CAAP;AACA,uCAAOlJ,KAAKiJ,UAAZ;AACH;AACDjJ,iCAAK6I,WAAL,GAAmB,IAAnB;AACA7I,iCAAKqC,QAAL,GAAgB,GAAhB;AACArC,iCAAKc,MAAL,GAAc,SAAd;AACAR,iCAAK+B,QAAL,GAAgByH,iBAAiBxJ,IAAjB,CAAhB;AACAmB,oCAAQ,qBAAR,EAA+BnB,IAA/B;AACApB,iCAAK8H,aAAL;AACH;AACJ;AACJ,iBArCD,EAqCG,UAAUjJ,GAAV,EAAe;AACd,wBAAIA,IAAI+C,MAAJ,KAAe,GAAnB,EAAwB;AACpBH;AACA;AACH;AACD6H,qCAAiB3H,IAAjB;AACH,iBA3CD;AA4CH;AACJ,SAtDD;AAuDH,KArED;;AAuEA,SAAK6G,aAAL,GAAqB,UAAU7G,IAAV,EAAgB;AACjC,YAAIb,OAAOa,KAAKb,IAAhB;AACA,YAAIM,OAAOO,KAAKP,IAAhB;;AAEA,YAAIA,KAAKsD,aAAL,CAAmBkB,WAAnB,IAAkCxE,KAAKsD,aAAL,CAAmBoB,eAAzD,EAAyE;AACrE8G,gBAAIrR,MAAJ,CAAWyR,EAAX,GAAgB5L,KAAKsD,aAAL,CAAmBkB,WAAnC,CADqE,CACpB;AACjDgH,gBAAIrR,MAAJ,CAAW0R,EAAX,GAAgB7L,KAAKsD,aAAL,CAAmBoB,eAAnC,CAFqE,CAEjB;AACvD;;AAED8G,YAAIrR,MAAJ,CAAW+K,MAAX,GAAoBlF,KAAKsD,aAAL,CAAmB4B,MAAvC;AACAsG,YAAI,UAAJ,OAAmBxL,KAAKsD,aAAL,CAAmB4B,MAAtC,IAAkDlF,KAAKsD,aAAL,CAAmBwI,UAArE;;AAEA;AACA,YAAIC,aAAa;AACbC,4BAAgBhM,KAAKsD,aAAL,CAAmBkB,WADtB;AAEbyH,uBAAWjM,KAAKsD,aAAL,CAAmBoB,eAFjB;AAGbwH,yBAAalM,KAAKsD,aAAL,CAAmBI,UAHnB;AAIbxH,iBAAKwD,KAAKkE,YAAL,CAAkBzH,OAAlB,CAA0B,KAA1B,EAAiC,GAAjC,CAJQ;AAKbgQ,iBAAK,aALQ,EAKO;AACpBC,0BAAcpM,KAAKsD,aAAL,CAAmBwI,UAAnB,GAAgC,GAAhC,GAAsC9L,KAAKsD,aAAL,CAAmBI,UAN1D,EAMsE;AACnF2I,uBAAW;AAPE,SAAjB;AASA,YAAIC,kBAAkB;AAClBC,0BAAcjV,SAASiU,IADL;AAElBpU,iBAAK6I,KAAKsD,aAAL,CAAmBwI;AAFN,SAAtB;AAIA,YAAIU,aAAa,IAAIC,eAAJ,CAAoBV,UAApB,EAAgCO,eAAhC,CAAjB;AACA;;AAEA,YAAI5M,KAAKoD,QAAL,IAAkB,MAAM,IAAN,GAAa,IAAnC,EAAyC;AAAC;AACtC,gBAAIH,SAAS;AACTc,wBAAQzD,KAAKsD,aAAL,CAAmBI,UADlB;AAETC,qBAAKjE,KAAKkE,YAAL,CAAkBzH,OAAlB,CAA0B,KAA1B,EAAiC,GAAjC,CAFI;AAGTuQ,sBAAMhN,KAAKA,IAHF;AAITiN,qBAAK;AAJI,aAAb;AAMAnB,gBAAIoB,SAAJ,CAAcjK,MAAd,EAAsB,UAASkK,IAAT,EAAc;AAChC,oBAAIA,IAAJ,EAAU;AACNlP,4BAAQ2G,IAAR,CAAauI,IAAb,EAAmBA,KAAKjP,KAAxB,EADM,CAC0B;AAChCsK,qCAAiB3H,IAAjB;AACH,iBAHD,MAIK;AACDmJ,+BAAWhK,IAAX,EAAiBlC,IAAjB,CAAsB,UAAUC,GAAV,EAAe;AACjCA,8BAAMA,IAAIK,IAAV;AACA,4BAAIL,IAAI6L,UAAJ,KAAmB,CAAvB,EAA0B;AAAC;AACvB5J,iCAAKqC,QAAL,GAAgB/B,KAAK+B,QAAL,GAAgB,GAAhC;AACArC,iCAAK6I,WAAL,GAAmBvI,KAAKuI,WAAL,GAAmB,IAAtC;AACA,mCAAO7I,KAAK+H,UAAZ;AACA/H,iCAAKc,MAAL,GAAcR,KAAKQ,MAAL,GAAc,SAA5B;AACA+I,yCAAavJ,IAAb,EAAmBxC,IAAnB,CAAwB,YAAU;AAC9B2D,wCAAQ,qBAAR,EAA+BnB,IAA/B;AACH,6BAFD;AAGH,yBARD,MASK;AACDN,iCAAKc,MAAL,GAAc,SAAd;AACAd,iCAAKqC,QAAL,GAAgB,GAAhB;AACH;AACDnD,6BAAK8H,aAAL;AACH,qBAhBD;AAiBH;AACJ,aAxBD;AAyBH,SAhCD,MAiCK;AACD,gBAAI/D,SAAS;AACTc,wBAAQzD,KAAKsD,aAAL,CAAmBI,UADlB;AAETC,qBAAKjE,KAAKkE,YAAL,CAAkBzH,OAAlB,CAA0B,KAA1B,EAAiC,GAAjC,CAFI;AAGTwQ,qBAAK;AAHI,aAAb;AAKA,gBAAI,CAACjN,KAAKwE,QAAV,EAAoB;AAChBsH,oBAAIsB,sBAAJ,CAA2BnK,MAA3B,EAAmC,UAAS0B,GAAT,EAAcH,QAAd,EAAuB;AACtD,wBAAGG,GAAH,EAAQ;AACJ1G,gCAAQC,KAAR,CAAcyG,GAAd;AACH,qBAFD,MAEM;AACF3E,6BAAKwE,QAAL,GAAgBA,QAAhB;AACA,6BAAKiH,eAAL,CAAqB5K,IAArB;AACH;AACJ,iBAPD;AAQH,aATD,MAUK;AACD,qBAAK4K,eAAL,CAAqB5K,IAArB;AACH;AACJ;AACJ,KAlFD;;AAoFA,SAAK8G,UAAL,GAAkB,UAAU9G,IAAV,EAAgB;AAC9B,YAAIb,OAAOa,KAAKb,IAAhB;AACA,YAAIM,OAAOO,KAAKP,IAAhB;AACA,YAAI,CAAChB,EAAL,EACI6E,YAAYtD,KAAKP,IAAL,CAAUsD,aAAtB;AACJ,YAAI5D,KAAK+I,UAAL,GAAkB/I,KAAKwG,UAA3B,EAAuC;AACnC,gBAAI4C,QAAQpJ,KAAK+I,UAAL,GAAkB/I,KAAKqJ,SAAnC;AAAA,gBACIhB,MAAMiB,KAAKC,GAAL,CAASvJ,KAAKoD,QAAd,EAAwBgG,QAAQpJ,KAAKqJ,SAArC,CADV;AAAA,gBAEIG,OAAO,IAAIC,QAAJ,EAFX;AAAA,gBAGIrL,OAAO4B,KAAKA,IAAL,CAAU0J,KAAV,CAAgBN,KAAhB,EAAuBf,GAAvB,CAHX;AAIA,gBAAIjK,QAAQ,IAAR,IAAgBA,KAAKnD,IAAL,IAAa,CAAjC,EAAoC;AAChC4F,qBAAKP,IAAL,CAAUQ,MAAV,GAAmBD,KAAKb,IAAL,CAAUc,MAAV,GAAmB,OAAtC;AACAW,wBAAQ,mBAAR,EAA6BZ,KAAKP,IAAlC;AACAO,qBAAKb,IAAL,CAAUA,IAAV,GAAiB,IAAjB;AACAd,qBAAK8H,aAAL;AACA;AACH;;AAED,gBAAIqG,cAAc,SAAdA,WAAc,CAAU/M,IAAV,EAAgBN,IAAhB,EAAsB;AACpCwJ,qBAAKnI,MAAL,CAAY,QAAZ,EAAsBrB,KAAKyG,MAA3B;AACA+C,qBAAKnI,MAAL,CAAY,QAAZ,EAAsBrB,KAAKkJ,MAA3B;AACAM,qBAAKnI,MAAL,CAAY,YAAZ,EAA0BrB,KAAK+I,UAA/B;AACAS,qBAAKnI,MAAL,CAAY,UAAZ,EAAwBrB,KAAKiK,QAA7B;AACAT,qBAAKnI,MAAL,CAAY,UAAZ,EAAwBrB,KAAKwE,QAA7B;;AAEA;AACA;AACA,oBAAIlE,KAAKQ,MAAL,KAAgB,WAApB,EAAiC;AAC7B5B,yBAAKyK,SAAL,CAAerJ,IAAf;AACA;AACH;;AAED,oBAAIiF,aAAalO,qBAAW0E,SAAX,CAAqB,YAArB,CAAjB;AACA,oBAAItE,MAAM,mBAAV;AACA,oBAAI8N,cAAcA,eAAe,YAAjC,EAA8C;AAC1C9N,0BAAM,4BAAN;AACH;AACD,oBAAIwH,SAASgH,UAAb,EAAwB;AACpBxO,2BAAO,YAAYwH,SAASgH,UAA5B;AACH;AACDhJ,yBAASsB,IAAT,CAAciB,YAAYwG,MAAZ,GAAqBvO,GAAnC,EAAwC+R,IAAxC,EAA8C;AAC1C9K,iCAAa,KAD6B;AAE1CC,iCAAa;AAF6B,iBAA9C,EAGGb,IAHH,CAGQ,UAAUC,GAAV,EAAe;AACnB,wBAAIuC,KAAKQ,MAAL,KAAgB,WAApB,EAAiC;AAC7B5B,6BAAKyK,SAAL,CAAerJ,IAAf;AACA;AACH;AACDvC,0BAAMA,IAAIK,IAAV;AACA4B,2BAAOrF,EAAEI,IAAF,CAAOuF,KAAK2B,KAAZ,EAAmB,EAAEiH,QAAQnL,IAAImL,MAAd,EAAnB,CAAP;AACAlJ,yBAAK+I,UAAL,GAAkBhL,IAAIgL,UAAtB;AACA,wBAAIhL,IAAI6L,UAAJ,KAAmB,CAAvB,EAA0B;AAAE;AACxB5J,6BAAKqC,QAAL,GAAgB/B,KAAK+B,QAAL,GAAgB,GAAhC;AACArC,6BAAK6I,WAAL,GAAmBvI,KAAKuI,WAAL,GAAmB,IAAtC;AACA,+BAAO7I,KAAK+H,UAAZ;AACA/H,6BAAKc,MAAL,GAAcR,KAAKQ,MAAL,GAAc,SAA5B;AACA5B,6BAAK8H,aAAL;AACA6C,qCAAavJ,IAAb,EAAmBxC,IAAnB,CAAwB,YAAU;AAC9B2D,oCAAQ,qBAAR,EAA+BnB,IAA/B;AACH,yBAFD;AAGH,qBATD,MASO;AACHA,6BAAK+B,QAAL,GAAgByH,iBAAiBxJ,IAAjB,CAAhB;AACAN,6BAAKqC,QAAL,GAAgB0H,iBAAiB/J,IAAjB,CAAhB;AACAA,6BAAKc,MAAL,GAAcR,KAAKQ,MAAL,GAAc,UAA5B;AACAW,gCAAQ,sBAAR,EAAgCnB,IAAhC;AACAN,6BAAKiJ,UAAL,GAAkB,CAAlB;AACA/J,6BAAKqI,MAAL,CAAY1G,IAAZ;AACH;AACJ,iBA5BD,EA4BG,UAAU9C,GAAV,EAAe;AACd,wBAAIA,IAAI+C,MAAJ,KAAe,GAAnB,EAAwB;AACpBH;AACA;AACH;AACD6H,qCAAiB3H,IAAjB;AACH,iBAlCD;AAmCH,aAzDD;;AA2DA,gBAAIb,KAAKoD,QAAL,IAAkB,IAAI,IAAJ,GAAW,IAAjC,EAAuC;AACvC;AACI9D,uBAAG4N,SAAH,CAAa;AACTI,8BAAMtN,KAAKA,IADF;AAET+D,gCAAQzD,KAAKsD,aAAL,CAAmBI,UAFlB;AAGTC,6BAAKjE,KAAKkE,YAAL,CAAkBzH,OAAlB,CAA0B,KAA1B,EAAiC,GAAjC;AAHI,qBAAb,EAIG,UAAUkI,GAAV,EAAe4I,WAAf,EAA4B;AAC3B,4BAAI5I,GAAJ,EAAS;AACL1G,oCAAQ2G,IAAR,CAAaD,GAAb,EAAkBA,IAAI6I,KAAtB,EADK,CACyB;AAC9BhF,6CAAiB3H,IAAjB;AACH,yBAHD,MAIK;AACD,gCAAI0M,YAAYE,IAAhB,EACIzN,KAAKiK,QAAL,GAAgBsD,YAAYE,IAAZ,CAAiBhR,OAAjB,CAAyB,IAAzB,EAA+B,EAA/B,CAAhB;AACJ4Q,wCAAY/M,IAAZ,EAAkBN,IAAlB;AACA/B,oCAAQ2G,IAAR,CAAa2I,WAAb,EAJC,CAI0B;AAC9B;AACD;;;;;;AAMH,qBArBD;AAsBH,iBAxBD,MAwBO;AACH,oBAAI,CAACvN,KAAKwE,QAAV,EAAoB;AAChB,wBAAIkJ,kBAAkB/J,sBAAsBrD,KAAKsD,aAA3B,EAA0C5D,IAA1C,CAAtB;AACA,wBAAI0N,eAAJ,EAAqB;AACjBA,wCAAgB5P,IAAhB,CAAqB,YAAY;AAC7BoB,iCAAKqI,MAAL,CAAY1G,IAAZ;AACH,yBAFD;AAGH;AACD;AACH;AACD,oBAAIoC,SAAS;AACTqK,0BAAMlP,IADG;AAET2F,4BAAQzD,KAAKsD,aAAL,CAAmBI,UAFlB;AAGTC,yBAAKjE,KAAKkE,YAAL,CAAkBzH,OAAlB,CAA0B,KAA1B,EAAiC,GAAjC,CAHI;AAITmP,gCAAY5L,KAAK+I,UAAL,GAAkB,CAJrB;AAKTtE,8BAAUzE,KAAKwE;AALN,iBAAb;AAOAlF,mBAAGqO,UAAH,CAAc1K,MAAd,EAAsB,UAAU0B,GAAV,EAAe4I,WAAf,EAA4B;AAC9C,wBAAI5I,GAAJ,EAAS;AACL1G,gCAAQ2G,IAAR,CAAaD,GAAb,EAAkBA,IAAI6I,KAAtB,EADK,CACyB;AAC9BhF,yCAAiB3H,IAAjB;AACH,qBAHD,MAIK;AACD,4BAAI0M,YAAYE,IAAhB,EACIzN,KAAKiK,QAAL,GAAgBsD,YAAYE,IAAZ,CAAiBhR,OAAjB,CAAyB,IAAzB,EAA+B,EAA/B,CAAhB;AACJ4Q,oCAAY/M,IAAZ,EAAkBN,IAAlB;AACA/B,gCAAQ2G,IAAR,CAAa2I,WAAb,EAJC,CAI0B;AAC3B;;;;;AAKH;AACJ,iBAhBD;AAiBH;AACJ,SAnID,MAmIO;AACH,gBAAIvN,KAAKgJ,cAAL,CAAoB,YAApB,CAAJ,EAAuC;AACnC,uBAAO3J,iBAAiBW,KAAKkJ,MAAtB,CAAP;AACA,uBAAOlJ,KAAKiJ,UAAZ;AACH;AACD,mBAAOjJ,KAAK+H,UAAZ;AACA/H,iBAAK6I,WAAL,GAAmB,IAAnB;AACA7I,iBAAKqC,QAAL,GAAgB,GAAhB;AACArC,iBAAKc,MAAL,GAAc,SAAd;AACAR,iBAAK+B,QAAL,GAAgByH,iBAAiBxJ,IAAjB,CAAhB;AACAmB,oBAAQ,qBAAR,EAA+BnB,IAA/B;AACApB,iBAAK8H,aAAL;AACH;AACJ,KArJD;;AAuJA,SAAKS,WAAL,GAAmB,UAAU5G,IAAV,EAAgB;AAC/B,YAAIb,OAAOa,KAAKb,IAAhB;AACA,YAAIM,OAAOO,KAAKP,IAAhB;;AAEA,YAAIN,KAAK+I,UAAL,GAAkB/I,KAAKwG,UAA3B,EAAuC;AACnC,gBAAI4C,QAAQpJ,KAAK+I,UAAL,GAAkB/I,KAAKqJ,SAAnC;AAAA,gBACIhB,MAAMiB,KAAKC,GAAL,CAASvJ,KAAKoD,QAAd,EAAwBgG,QAAQpJ,KAAKqJ,SAArC,CADV;AAAA,gBAEIG,OAAO,IAAIC,QAAJ,EAFX;AAAA,gBAGIrL,OAAO4B,KAAKA,IAAL,CAAU0J,KAAV,CAAgBN,KAAhB,EAAuBf,GAAvB,CAHX;AAIA,gBAAIjK,QAAQ,IAAR,IAAgBA,KAAKnD,IAAL,IAAa,CAAjC,EAAoC;AAChC4F,qBAAKP,IAAL,CAAUQ,MAAV,GAAmBD,KAAKb,IAAL,CAAUc,MAAV,GAAmB,OAAtC;AACAW,wBAAQ,mBAAR,EAA6BZ,KAAKP,IAAlC;AACAO,qBAAKb,IAAL,CAAUA,IAAV,GAAiB,IAAjB;AACAd,qBAAK8H,aAAL;AACA;AACH;AACD,gBAAIqG,cAAc,SAAdA,WAAc,CAAU/M,IAAV,EAAgBN,IAAhB,EAAsB;AACpCwJ,qBAAKnI,MAAL,CAAY,QAAZ,EAAsBrB,KAAKyG,MAA3B;AACA+C,qBAAKnI,MAAL,CAAY,QAAZ,EAAsBrB,KAAKkJ,MAA3B;AACAM,qBAAKnI,MAAL,CAAY,YAAZ,EAA0BrB,KAAK+I,UAA/B;AACAS,qBAAKnI,MAAL,CAAY,UAAZ,EAAwBrB,KAAKiK,QAA7B;AACAT,qBAAKnI,MAAL,CAAY,UAAZ,EAAwBrB,KAAKwE,QAA7B;;AAEA;AACA;AACA,oBAAIlE,KAAKQ,MAAL,KAAgB,WAApB,EAAiC;AAC7B5B,yBAAKyK,SAAL,CAAerJ,IAAf;AACA;AACH;;AAED,oBAAIiF,aAAalO,qBAAW0E,SAAX,CAAqB,YAArB,CAAjB;AACA,oBAAItE,MAAM,mBAAV;AACA,oBAAI8N,cAAcA,eAAe,YAAjC,EAA8C;AAC1C9N,0BAAM,4BAAN;AACH;AACD,oBAAIwH,SAASgH,UAAb,EAAwB;AACpBxO,2BAAO,YAAYwH,SAASgH,UAA5B;AACH;AACDhJ,yBAASsB,IAAT,CAAc9G,GAAd,EAAmB+R,IAAnB,EAAyB;AACrB9K,iCAAa,KADQ;AAErBC,iCAAa;AAFQ,iBAAzB,EAGGb,IAHH,CAGQ,UAAUC,GAAV,EAAe;AACnB,wBAAIuC,KAAKQ,MAAL,KAAgB,WAApB,EAAiC;AAC7B5B,6BAAKyK,SAAL,CAAerJ,IAAf;AACA;AACH;AACDvC,0BAAMA,IAAIK,IAAV;AACA4B,2BAAOrF,EAAEI,IAAF,CAAOuF,KAAK2B,KAAZ,EAAmB,EAAEiH,QAAQnL,IAAImL,MAAd,EAAnB,CAAP;AACAlJ,yBAAK+I,UAAL,GAAkBhL,IAAIgL,UAAtB;AACA,wBAAIhL,IAAI6L,UAAJ,KAAmB,CAAvB,EAA0B;AAAE;AACxB5J,6BAAKqC,QAAL,GAAgB/B,KAAK+B,QAAL,GAAgB,GAAhC;AACArC,6BAAK6I,WAAL,GAAmBvI,KAAKuI,WAAL,GAAmB,IAAtC;AACA,+BAAO7I,KAAK+H,UAAZ;AACA/H,6BAAKc,MAAL,GAAcR,KAAKQ,MAAL,GAAc,SAA5B;AACA5B,6BAAK8H,aAAL;AACA6C,qCAAavJ,IAAb,EAAmBxC,IAAnB,CAAwB,YAAU;AAC9B2D,oCAAQ,qBAAR,EAA+BnB,IAA/B;AACH,yBAFD;AAGH,qBATD,MASO;AACHA,6BAAK+B,QAAL,GAAgByH,iBAAiBxJ,IAAjB,CAAhB;AACAN,6BAAKqC,QAAL,GAAgB0H,iBAAiB/J,IAAjB,CAAhB;AACAA,6BAAKc,MAAL,GAAcR,KAAKQ,MAAL,GAAc,UAA5B;AACAW,gCAAQ,sBAAR,EAAgCnB,IAAhC;AACAN,6BAAKiJ,UAAL,GAAkB,CAAlB;AACA/J,6BAAKqI,MAAL,CAAY1G,IAAZ;AACH;AACJ,iBA5BD,EA4BG,UAAU9C,GAAV,EAAe;AACd,wBAAIA,IAAI+C,MAAJ,KAAe,GAAnB,EAAwB;AACpBH;AACA;AACH;AACD6H,qCAAiB3H,IAAjB;AACH,iBAlCD;AAmCH,aAzDD;AA0DA,gBAAI,CAACvB,EAAL,EAAS;AACL6E,4BAAY7D,KAAKsD,aAAjB;AACH;AACD,gBAAI5D,KAAKoD,QAAL,IAAkB,IAAI,IAAJ,GAAW,IAAjC,EAAuC;AACvC;AACI9D,uBAAG4N,SAAH,CAAa;AACTI,8BAAMtN,KAAKA,IADF;AAET+D,gCAAQzD,KAAKsD,aAAL,CAAmBI,UAFlB;AAGTC,6BAAKjE,KAAKkE,YAAL,CAAkBzH,OAAlB,CAA0B,KAA1B,EAAiC,GAAjC;AAHI,qBAAb,EAIG,UAAUkI,GAAV,EAAe4I,WAAf,EAA4B;AAC3B,4BAAI5I,GAAJ,EAAS;AACL1G,oCAAQ2G,IAAR,CAAaD,GAAb,EAAkBA,IAAI6I,KAAtB,EADK,CACyB;AAC9BhF,6CAAiB3H,IAAjB;AACH,yBAHD,MAIK;AACD,gCAAI0M,YAAYE,IAAhB,EACIzN,KAAKiK,QAAL,GAAgBsD,YAAYE,IAAZ,CAAiBhR,OAAjB,CAAyB,IAAzB,EAA+B,EAA/B,CAAhB;AACJ4Q,wCAAY/M,IAAZ,EAAkBN,IAAlB;AACA/B,oCAAQ2G,IAAR,CAAa2I,WAAb,EAJC,CAI0B;AAC9B;AACD;;;;;;AAMH,qBArBD;AAsBH,iBAxBD,MAwBO;AACH,oBAAI,CAACvN,KAAKwE,QAAV,EAAoB;AAChB,wBAAIkJ,kBAAkB/J,sBAAsBrD,KAAKsD,aAA3B,EAA0C5D,IAA1C,CAAtB;AACA,wBAAI0N,eAAJ,EAAqB;AACjBA,wCAAgB5P,IAAhB,CAAqB,YAAY;AAC7BoB,iCAAKqI,MAAL,CAAY1G,IAAZ;AACH,yBAFD;AAGH;AACD;AACH;AACD,oBAAIoC,SAAS;AACTqK,0BAAMlP,IADG;AAET2F,4BAAQzD,KAAKsD,aAAL,CAAmBI,UAFlB;AAGTC,yBAAKjE,KAAKkE,YAAL,CAAkBzH,OAAlB,CAA0B,KAA1B,EAAiC,GAAjC,CAHI;AAITmP,gCAAY5L,KAAK+I,UAAL,GAAkB,CAJrB;AAKTtE,8BAAUzE,KAAKwE;AALN,iBAAb;AAOAlF,mBAAGqO,UAAH,CAAc1K,MAAd,EAAsB,UAAU0B,GAAV,EAAe4I,WAAf,EAA4B;AAC9C,wBAAI5I,GAAJ,EAAS;AACL1G,gCAAQ2G,IAAR,CAAaD,GAAb,EAAkBA,IAAI6I,KAAtB,EADK,CACyB;AAC9BhF,yCAAiB3H,IAAjB;AACH,qBAHD,MAIK;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAAI0M,YAAYE,IAAhB,EACIzN,KAAKiK,QAAL,GAAgBsD,YAAYE,IAAZ,CAAiBhR,OAAjB,CAAyB,IAAzB,EAA+B,EAA/B,CAAhB;AACJ4Q,oCAAY/M,IAAZ,EAAkBN,IAAlB;AACA/B,gCAAQ2G,IAAR,CAAa2I,WAAb,EAfC,CAe0B;AAC3B;AACH;AACJ,iBAvBD;AAwBH;AACJ,SA3ID,MA2IO;AACH,gBAAIvN,KAAKgJ,cAAL,CAAoB,YAApB,CAAJ,EAAuC;AACnC,uBAAO3J,iBAAiBW,KAAKkJ,MAAtB,CAAP;AACA,uBAAOlJ,KAAKiJ,UAAZ;AACH;AACD,mBAAOjJ,KAAK+H,UAAZ;AACA/H,iBAAK6I,WAAL,GAAmB,IAAnB;AACA7I,iBAAKqC,QAAL,GAAgB,GAAhB;AACArC,iBAAKc,MAAL,GAAc,SAAd;AACAR,iBAAK+B,QAAL,GAAgByH,iBAAiBxJ,IAAjB,CAAhB;AACAmB,oBAAQ,qBAAR,EAA+BnB,IAA/B;AACApB,iBAAK8H,aAAL;AACH;AACJ,KA5JD;;AA8JA;AACA,SAAK4G,cAAL,GAAsB,UAAUtN,IAAV,EAAgBN,IAAhB,EAAsB6N,WAAtB,EAAmC;AACrD,YAAI7N,KAAKA,IAAL,IAAa,IAAjB,EACId,KAAKsD,gBAAL,CAAsB,UAAUD,MAAV,EAAkB;AACpC,gBAAI6F,MAAMlJ,KAAK4O,WAAL,CAAiBvL,OAAO,CAAP,EAAUvC,IAA3B,CAAV;AACA,gBAAIA,KAAK+N,OAAL,KAAiB3F,GAArB,EAA0B;AACtB/Q,qCAAWkE,MAAX,CAAkB4C,EAAE,mBAAF,EAAuB,gBAAvB,CAAlB;AACA,oBAAI0P,WAAJ,EAAiB;AACbA,gCAAYtV,KAAZ,CAAkBsC,MAAlB,EAA0B,CAACsD,EAAE,mBAAF,EAAuB,gBAAvB,CAAD,CAA1B;AACH;AACD;AACH;AACD6B,iBAAKA,IAAL,GAAYuC,OAAO,CAAP,EAAUvC,IAAtB;AACAM,iBAAKQ,MAAL,GAAcd,KAAKc,MAAL,GAAc,UAA5B;AACA5B,iBAAK8H,aAAL;AACH,SAZD,EAYG,KAZH,EADJ,KAcK;AACD,gBAAI1G,KAAKuG,MAAT,EAAiB;AACbvG,qBAAKQ,MAAL,GAAcd,KAAKc,MAAL,GAAc,UAA5B;AACA5B,qBAAK8H,aAAL;AACH,aAHD,MAGO;AACHjB,yBAASzF,IAAT;AACH;AACJ;AACJ,KAvBD;;AAyBA;AACA,SAAK0N,iBAAL,GAAyB,UAAUC,UAAV,EAAsBzK,mBAAtB,EAA2CoD,UAA3C,EAAuD;AAC5E,YAAIsH,WAAW7Q,EAAEG,QAAF,EAAf;AACA,YAAIyQ,cAAc,IAAlB,EACIA,aAAa,EAAb;AACJ,YAAI,CAACtT,EAAEiO,QAAF,CAAWpF,mBAAX,CAAL,EACIA,sBAAsB,CAAtB;AACJ,YAAI,CAAC7I,EAAEiO,QAAF,CAAWpF,mBAAX,CAAL,EACIA,sBAAsB,CAAtB;AACJ,YAAI+B,aAAalO,qBAAW0E,SAAX,CAAqB,YAArB,CAAjB;AACA,YAAItE,MAAM,4CAA4CwW,UAA5C,GAAyD,uBAAzD,GAAmFzK,mBAAnF,GAAyG,cAAzG,GAA0HoD,UAA1H,IAAwI3H,SAASgH,UAAT,GAAsB,YAAYhH,SAASgH,UAA3C,GAAwD,EAAhM,CAAV;AACA,YAAIV,cAAcA,eAAe,YAAjC,EAA8C;AACzC9N,kBAAM,iDAAiDwW,UAAjD,GAA8D,uBAA9D,GAAwFzK,mBAAxF,GAA8G,cAA9G,GAA+HoD,UAA/H,IAA6I3H,SAASgH,UAAT,GAAsB,YAAYhH,SAASgH,UAA3C,GAAwD,EAArM,CAAN;AACJ;AACDhJ,iBAASrC,GAAT,CAAa4E,YAAYwG,MAAZ,GAAqBvO,GAAlC,EAAuCqG,IAAvC,CAA4C,UAAUC,GAAV,EAAe;AACvD,gBAAIwE,SAAS,EAAb;AACA5H,cAAEiG,OAAF,CAAU7C,IAAIK,IAAd,EAAoB,UAAUkC,IAAV,EAAgB;AAChCA,qBAAKQ,MAAL,GAAc,OAAd;AACAR,qBAAKuG,MAAL,GAAc,IAAd;AACAvG,qBAAKiG,SAAL,GAAiBjG,KAAK6G,aAAL,GAAqB7G,KAAKkG,UAAL,GAAkB,CAAxD;AACA7L,kBAAEiG,OAAF,CAAUN,KAAK2B,KAAf,EAAsB,UAAUjC,IAAV,EAAgB;AAClCA,yBAAKsG,cAAL,GAAsBjP,qBAAW2D,UAAX,CAAsBgF,KAAKoD,QAA3B,CAAtB;AACApD,yBAAKqC,QAAL,GAAgB0H,iBAAiB/J,IAAjB,CAAhB;AACAA,yBAAKc,MAAL,GAAcd,KAAKqC,QAAL,KAAkB,GAAlB,GAAwB,SAAxB,GAAoC,OAAlD;AACA/B,yBAAKiG,SAAL,IAAkBvG,KAAKoD,QAAvB;AACA9C,yBAAKkG,UAAL,IAAmBxG,KAAKwG,UAAxB;AACH,iBAND;AAOAlG,qBAAK+B,QAAL,GAAgByH,iBAAiBxJ,IAAjB,CAAhB;AACAA,qBAAKwG,eAAL,GAAuBzP,qBAAW2D,UAAX,CAAsBsF,KAAKiG,SAA3B,CAAvB;AACArH,qBAAKK,KAAL,CAAWjH,IAAX,CAAgBgI,IAAhB;AACAiC,uBAAOjK,IAAP,CAAYgI,IAAZ;AACH,aAfD;AAgBA4N,qBAASrU,OAAT,CAAiB0I,MAAjB;AACH,SAnBD,EAmBG,UAAUxE,GAAV,EAAe;AACdmQ,qBAASpU,MAAT,CAAgBiE,GAAhB;AACH,SArBD;AAsBA,eAAOmQ,QAAP;AACH,KApCD;;AAsCA;AACA,SAAKC,aAAL,GAAqB,UAAU7N,IAAV,EAAgB;AACjC,YAAIA,QAAQ,IAAZ,EACI,OAAO,KAAP;AACJ,YAAIA,KAAKQ,MAAL,IAAe,MAAnB,EACI,OAAO,KAAP;AACJ,YAAIR,KAAKQ,MAAL,IAAe,WAAnB,EACI,OAAO,KAAP;AACJ,YAAIR,KAAKQ,MAAL,IAAe,UAAf,IAA6BR,KAAK6G,aAAL,IAAuB7G,KAAKkG,UAAL,GAAkB,CAA1E,EACI,OAAO,KAAP;AACJ,eAAO,IAAP;AACH,KAVD;;AAYA,SAAK4H,UAAL,GAAkB,UAAU9N,IAAV,EAAgB;AAC9B3F,UAAE6G,MAAF,CAAStC,KAAKK,KAAd,EAAqB,UAAU8O,EAAV,EAAc;AAC/B,mBAAOA,GAAG5H,MAAH,KAAcnG,KAAKmG,MAA1B;AACH,SAFD;AAGH,KAJD;;AAMA;AACA,SAAK6H,UAAL,GAAkB,UAAUhO,IAAV,EAAgB;AAC9B,YAAI,CAAC,KAAK6N,aAAL,CAAmB7N,IAAnB,CAAL,EACI;AACJ,YAAIA,KAAKuG,MAAL,KAAgB,IAApB,EAA0B;AACtBlM,cAAEiG,OAAF,CAAUN,KAAK2B,KAAf,EAAsB,UAAUjC,IAAV,EAAgB;AAClC,oBAAIX,iBAAiBW,KAAKkJ,MAAtB,KAAiC,IAArC,EAA2C;AACvCqF,4BAAQC,MAAR,CAAenP,iBAAiBW,KAAKkJ,MAAtB,CAAf;AACA,2BAAO7J,iBAAiBW,KAAKkJ,MAAtB,CAAP;AACH;AACJ,aALD;AAMA,oBAAQ5I,KAAKQ,MAAb;AACI,qBAAK,UAAL;AACIR,yBAAKQ,MAAL,GAAc,WAAd;AACA;AACJ,qBAAK,UAAL;AACA,qBAAK,OAAL;AACI5B,yBAAKyK,SAAL,CAAerJ,IAAf;AACA;AACJ;AACIpB,yBAAKkP,UAAL,CAAgB9N,IAAhB;AACAmB,4BAAQ,qBAAR,EAA+BnB,IAA/B;AAVR;AAYH,SAnBD,MAmBO;AACHpB,iBAAKkP,UAAL,CAAgB9N,IAAhB;AACAmB,oBAAQ,qBAAR,EAA+BnB,IAA/B;AACH;AACJ,KA1BD;;AA4BA;AACA,SAAKqJ,SAAL,GAAiB,UAAUrJ,IAAV,EAAgB;AAC7B,YAAImO,aAAa,EAAjB;AACA,YAAInO,KAAK2B,KAAL,IAAc3B,KAAK2B,KAAL,CAAWpJ,MAAX,GAAoB,CAAtC,EAAyC;AACrC8B,cAAEiG,OAAF,CAAUN,KAAK2B,KAAf,EAAsB,UAAUjC,IAAV,EAAgB;AAClCyO,2BAAWzO,KAAKkJ,MAAhB,IAA0BlJ,KAAKwE,QAA/B;AACH,aAFD;AAGH;AACD,YAAIe,aAAalO,qBAAW0E,SAAX,CAAqB,YAArB,CAAjB;AACA,YAAItE,MAAM,qBAAV;AACA,YAAI8N,cAAcA,eAAe,YAAjC,EAA8C;AACzC9N,kBAAM,uBAAN;AACJ;AACD,YAAIwH,SAASgH,UAAb,EAAwB;AACpBxO,mBAAO,YAAYwH,SAASgH,UAA5B;AACH;AACD,YAAI,CAACV,UAAD,IAAeA,eAAe,YAAlC,EAA+C;AAC3CtI,qBAASsB,IAAT,CAAciB,YAAYwG,MAAZ,GAAqBvO,GAAnC,EAAwC,EAAEgP,QAAQnG,KAAKmG,MAAf,EAAuBgI,YAAYA,UAAnC,EAAxC,EAAyF3Q,IAAzF,CAA8F,UAAUC,GAAV,EAAe;AACzGmB,qBAAKkP,UAAL,CAAgB9N,IAAhB;AACAmB,wBAAQ,qBAAR,EAA+BnB,IAA/B;AACApB,qBAAK8H,aAAL;AACH,aAJD,EAIG,UAAUjJ,GAAV,EAAe;AACd0D,wBAAQ,mBAAR,EAA6B,CAACnB,IAAD,EAAOvC,GAAP,CAA7B;AACH,aAND;AAOH,SARD,MASI;AACAd,qBAAS2B,MAAT,CAAgBY,YAAYwG,MAAZ,GAAqBvO,GAArC,EAA0C,EAAEgP,QAAQnG,KAAKmG,MAAf,EAAuBgI,YAAYA,UAAnC,EAA1C,EAA2F3Q,IAA3F,CAAgG,UAAUC,GAAV,EAAe;AAC3GmB,qBAAKkP,UAAL,CAAgB9N,IAAhB;AACAmB,wBAAQ,qBAAR,EAA+BnB,IAA/B;AACApB,qBAAK8H,aAAL;AACH,aAJD,EAIG,UAAUjJ,GAAV,EAAe;AACd0D,wBAAQ,mBAAR,EAA6B,CAACnB,IAAD,EAAOvC,GAAP,CAA7B;AACH,aAND;AAOH;AACJ,KAjCD;;AAmCA;AACA,SAAK+P,WAAL,GAAmB,UAAU9N,IAAV,EAAgB;AAC/B;AACA,eAAOjB,SAAS2P,IAAT,CAAc1O,KAAK3G,IAAL,GAAY2G,KAAK/E,IAAjB,GAAwB8E,YAAYC,IAAZ,CAAtC,CAAP;AACH,KAHD;;AAKA;AACA;AACA;AACA;;AAEA;AACA,SAAKJ,gBAAL,GAAwB,YAAY;AAChC,YAAIkB,SAAS,GAAG4I,KAAH,CAAS/H,IAAT,CAAcgN,SAAd,EAAyB,CAAzB,CAAb;AACA,YAAIpM,SAAS,EAAb;AACA,aAAK,IAAIqM,IAAI,CAAb,EAAgBA,IAAI1P,KAAKK,KAAL,CAAW1G,MAA/B,EAAuC+V,GAAvC,EAA4C;AACxC,iBAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAI3P,KAAKK,KAAL,CAAWqP,CAAX,EAAc3M,KAAd,CAAoBpJ,MAAxC,EAAgDgW,GAAhD,EAAqD;AACjD,qBAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIhO,OAAOjI,MAA3B,EAAmCiW,GAAnC,EAAwC;AACpC,wBAAI5P,KAAKK,KAAL,CAAWqP,CAAX,EAAc3M,KAAd,CAAoB4M,CAApB,EAAuB/N,MAAvB,KAAkCA,OAAOgO,CAAP,CAAtC,EAAiD;AAC7CvM,+BAAOjK,IAAP,CAAY,EAAEgI,MAAMpB,KAAKK,KAAL,CAAWqP,CAAX,CAAR,EAAuB5O,MAAMd,KAAKK,KAAL,CAAWqP,CAAX,EAAc3M,KAAd,CAAoB4M,CAApB,CAA7B,EAAZ;AACA;AACH;AACJ;AACJ;AACJ;AACD,eAAOtM,MAAP;AACH,KAdD;;AAgBA;AACA,aAASwM,YAAT,CAAsBC,OAAtB,EAA+BC,KAA/B,EAAsC;AAClC,YAAID,WAAWC,KAAf,EACI,OAAO,GAAP;AACJ,YAAI1M,SAAWyM,UAAUC,KAAX,GAAoB,GAAlC;AACA,YAAI1M,OAAO2M,QAAP,GAAkB1V,OAAlB,CAA0B,GAA1B,KAAkC,CAAC,CAAvC,EACI,OAAO+I,MAAP;AACJ,eAAOA,OAAOjH,OAAP,CAAe,CAAf,CAAP;AACH;;AAED;AACA,aAASyO,gBAAT,CAA0B/J,IAA1B,EAAgC;AAC5B,eAAO+O,aAAa/O,KAAK+I,UAAlB,EAA8B/I,KAAKwG,UAAnC,CAAP;AACH;;AAED;AACA,aAASsD,gBAAT,CAA0BxJ,IAA1B,EAAgC;AAC5B,YAAI6O,QAAQ,CAAZ;AACA,aAAK,IAAIxW,IAAI,CAAb,EAAgBA,IAAI2H,KAAK2B,KAAL,CAAWpJ,MAA/B,EAAuCF,GAAvC;AACIwW,qBAAS7O,KAAK2B,KAAL,CAAWtJ,CAAX,EAAcoQ,UAAvB;AADJ,SAEAzI,KAAK6G,aAAL,GAAqBgI,KAArB;AACA,eAAOJ,aAAazO,KAAK6G,aAAlB,EAAiC7G,KAAKkG,UAAtC,CAAP;AACH;;AAED3G;AACH,CAv/CD;;kBAy/Ceb,G;;;;;;;;AC//Cf;AACA;AACA;AACA;AACA;AACA;AACA;;AAEY;;AAEZ,aAAa,mBAAO,CAAC,EAAW;AAChC,cAAc,mBAAO,CAAC,EAAS;AAC/B,cAAc,mBAAO,CAAC,EAAS;;AAE/B;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,qBAAqB,mDAAmD;AACxE;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,mBAAmB,UAAU;AAC7B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB,YAAY;AAC7B;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA,uCAAuC,SAAS;AAChD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,eAAe,iBAAiB;AAChC;AACA;AACA;;AAEA;AACA;AACA,aAAa,iBAAiB;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,gDAAgD,EAAE;AAClD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA,wBAAwB,eAAe;AACvC;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,wBAAwB,QAAQ;AAChC;AACA,qBAAqB,eAAe;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB,YAAY;AAC7B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,qBAAqB,SAAS;AAC9B;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,qBAAqB,SAAS;AAC9B;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA,qBAAqB,SAAS;AAC9B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB,kBAAkB;AACnC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,mBAAmB,cAAc;AACjC;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,uDAAuD,OAAO;AAC9D;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA,uDAAuD,OAAO;AAC9D;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,kBAAkB;AAClB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,qBAAqB,QAAQ;AAC7B;AACA;AACA,GAAG;AACH;AACA,eAAe,SAAS;AACxB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA,mBAAmB,SAAS;AAC5B;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,eAAe,iBAAiB;AAChC;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,iBAAiB,YAAY;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,iBAAiB,gBAAgB;AACjC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB,gBAAgB;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,iBAAiB,YAAY;AAC7B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;;;;;;AC5vDA;;AAEA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;;AAEA;AACA;AACA,4CAA4C;;AAE5C;;;;;;;;ACpBY;;AAEZ;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,kCAAkC,SAAS;AAC3C;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,qBAAqB,SAAS;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0CAA0C,UAAU;AACpD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;;;;;ACrJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,QAAQ,WAAW;;AAEnB;AACA;AACA;AACA,QAAQ,WAAW;;AAEnB;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA,QAAQ,WAAW;;AAEnB;AACA;AACA,QAAQ,UAAU;;AAElB;AACA;;;;;;;ACpFA,iBAAiB;;AAEjB;AACA;AACA;;;;;;;;;;;;;;;;ACJC,WAAUoQ,OAAV,EAAmB;AAChB,QAAI,8CAAOC,OAAP,OAAmB,QAAvB,EAAiC;AAC7B;AACAC,eAAOD,OAAP,GAAiBD,SAAjB;AACH,KAHD,MAGO,IAAI,IAAJ,EAAgD;AACnD;AACAG,4CAAOH,OAAP;AAAA;AAAA;AAAA;AAAA;AACH,KAHM,MAGA;AACH;AACA,YAAII,IAAJ;;AAEA,YAAI;AACAA,mBAAO3U,MAAP;AACH,SAFD,CAEE,OAAOV,CAAP,EAAU;AACRqV,mBAAOtQ,IAAP;AACH;;AAEDsQ,aAAKC,QAAL,GAAgBL,SAAhB;AACH;AACJ,CAnBA,EAmBC,UAAU/W,SAAV,EAAqB;;AAEnB;;AAEA;;AAEA;;;;;;;;AAQA;;;;;;AAKA,QAAIqX,QAAQ,eAAUC,CAAV,EAAaC,CAAb,EAAgB;AACxB,eAAQD,IAAIC,CAAL,GAAU,UAAjB;AACH,KAFD;AAAA,QAIAC,MAAM,SAANA,GAAM,CAAUC,CAAV,EAAaH,CAAb,EAAgBC,CAAhB,EAAmBhB,CAAnB,EAAsB3U,CAAtB,EAAyB8V,CAAzB,EAA4B;AAC9BJ,YAAID,MAAMA,MAAMC,CAAN,EAASG,CAAT,CAAN,EAAmBJ,MAAMd,CAAN,EAASmB,CAAT,CAAnB,CAAJ;AACA,eAAOL,MAAOC,KAAK1V,CAAN,GAAY0V,MAAO,KAAK1V,CAA9B,EAAmC2V,CAAnC,CAAP;AACH,KAPD;AAAA,QASAI,KAAK,SAALA,EAAK,CAAUL,CAAV,EAAaC,CAAb,EAAgBxT,CAAhB,EAAmB6T,CAAnB,EAAsBrB,CAAtB,EAAyB3U,CAAzB,EAA4B8V,CAA5B,EAA+B;AAChC,eAAOF,IAAKD,IAAIxT,CAAL,GAAY,CAACwT,CAAF,GAAOK,CAAtB,EAA0BN,CAA1B,EAA6BC,CAA7B,EAAgChB,CAAhC,EAAmC3U,CAAnC,EAAsC8V,CAAtC,CAAP;AACH,KAXD;AAAA,QAaAG,KAAK,SAALA,EAAK,CAAUP,CAAV,EAAaC,CAAb,EAAgBxT,CAAhB,EAAmB6T,CAAnB,EAAsBrB,CAAtB,EAAyB3U,CAAzB,EAA4B8V,CAA5B,EAA+B;AAChC,eAAOF,IAAKD,IAAIK,CAAL,GAAW7T,IAAK,CAAC6T,CAArB,EAA0BN,CAA1B,EAA6BC,CAA7B,EAAgChB,CAAhC,EAAmC3U,CAAnC,EAAsC8V,CAAtC,CAAP;AACH,KAfD;AAAA,QAiBAI,KAAK,SAALA,EAAK,CAAUR,CAAV,EAAaC,CAAb,EAAgBxT,CAAhB,EAAmB6T,CAAnB,EAAsBrB,CAAtB,EAAyB3U,CAAzB,EAA4B8V,CAA5B,EAA+B;AAChC,eAAOF,IAAID,IAAIxT,CAAJ,GAAQ6T,CAAZ,EAAeN,CAAf,EAAkBC,CAAlB,EAAqBhB,CAArB,EAAwB3U,CAAxB,EAA2B8V,CAA3B,CAAP;AACH,KAnBD;AAAA,QAqBAK,KAAK,SAALA,EAAK,CAAUT,CAAV,EAAaC,CAAb,EAAgBxT,CAAhB,EAAmB6T,CAAnB,EAAsBrB,CAAtB,EAAyB3U,CAAzB,EAA4B8V,CAA5B,EAA+B;AAChC,eAAOF,IAAIzT,KAAKwT,IAAK,CAACK,CAAX,CAAJ,EAAoBN,CAApB,EAAuBC,CAAvB,EAA0BhB,CAA1B,EAA6B3U,CAA7B,EAAgC8V,CAAhC,CAAP;AACH,KAvBD;AAAA,QAyBAM,WAAW,SAAXA,QAAW,CAAUzB,CAAV,EAAa0B,CAAb,EAAgB;AACvB,YAAIX,IAAIf,EAAE,CAAF,CAAR;AAAA,YACIgB,IAAIhB,EAAE,CAAF,CADR;AAAA,YAEIxS,IAAIwS,EAAE,CAAF,CAFR;AAAA,YAGIqB,IAAIrB,EAAE,CAAF,CAHR;;AAKAe,YAAIK,GAAGL,CAAH,EAAMC,CAAN,EAASxT,CAAT,EAAY6T,CAAZ,EAAeK,EAAE,CAAF,CAAf,EAAqB,CAArB,EAAwB,CAAC,SAAzB,CAAJ;AACAL,YAAID,GAAGC,CAAH,EAAMN,CAAN,EAASC,CAAT,EAAYxT,CAAZ,EAAekU,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,CAAC,SAA1B,CAAJ;AACAlU,YAAI4T,GAAG5T,CAAH,EAAM6T,CAAN,EAASN,CAAT,EAAYC,CAAZ,EAAeU,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,SAAzB,CAAJ;AACAV,YAAII,GAAGJ,CAAH,EAAMxT,CAAN,EAAS6T,CAAT,EAAYN,CAAZ,EAAeW,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,CAAC,UAA1B,CAAJ;AACAX,YAAIK,GAAGL,CAAH,EAAMC,CAAN,EAASxT,CAAT,EAAY6T,CAAZ,EAAeK,EAAE,CAAF,CAAf,EAAqB,CAArB,EAAwB,CAAC,SAAzB,CAAJ;AACAL,YAAID,GAAGC,CAAH,EAAMN,CAAN,EAASC,CAAT,EAAYxT,CAAZ,EAAekU,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,UAAzB,CAAJ;AACAlU,YAAI4T,GAAG5T,CAAH,EAAM6T,CAAN,EAASN,CAAT,EAAYC,CAAZ,EAAeU,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,CAAC,UAA1B,CAAJ;AACAV,YAAII,GAAGJ,CAAH,EAAMxT,CAAN,EAAS6T,CAAT,EAAYN,CAAZ,EAAeW,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,CAAC,QAA1B,CAAJ;AACAX,YAAIK,GAAGL,CAAH,EAAMC,CAAN,EAASxT,CAAT,EAAY6T,CAAZ,EAAeK,EAAE,CAAF,CAAf,EAAqB,CAArB,EAAwB,UAAxB,CAAJ;AACAL,YAAID,GAAGC,CAAH,EAAMN,CAAN,EAASC,CAAT,EAAYxT,CAAZ,EAAekU,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,CAAC,UAA1B,CAAJ;AACAlU,YAAI4T,GAAG5T,CAAH,EAAM6T,CAAN,EAASN,CAAT,EAAYC,CAAZ,EAAeU,EAAE,EAAF,CAAf,EAAsB,EAAtB,EAA0B,CAAC,KAA3B,CAAJ;AACAV,YAAII,GAAGJ,CAAH,EAAMxT,CAAN,EAAS6T,CAAT,EAAYN,CAAZ,EAAeW,EAAE,EAAF,CAAf,EAAsB,EAAtB,EAA0B,CAAC,UAA3B,CAAJ;AACAX,YAAIK,GAAGL,CAAH,EAAMC,CAAN,EAASxT,CAAT,EAAY6T,CAAZ,EAAeK,EAAE,EAAF,CAAf,EAAsB,CAAtB,EAAyB,UAAzB,CAAJ;AACAL,YAAID,GAAGC,CAAH,EAAMN,CAAN,EAASC,CAAT,EAAYxT,CAAZ,EAAekU,EAAE,EAAF,CAAf,EAAsB,EAAtB,EAA0B,CAAC,QAA3B,CAAJ;AACAlU,YAAI4T,GAAG5T,CAAH,EAAM6T,CAAN,EAASN,CAAT,EAAYC,CAAZ,EAAeU,EAAE,EAAF,CAAf,EAAsB,EAAtB,EAA0B,CAAC,UAA3B,CAAJ;AACAV,YAAII,GAAGJ,CAAH,EAAMxT,CAAN,EAAS6T,CAAT,EAAYN,CAAZ,EAAeW,EAAE,EAAF,CAAf,EAAsB,EAAtB,EAA0B,UAA1B,CAAJ;;AAEAX,YAAIO,GAAGP,CAAH,EAAMC,CAAN,EAASxT,CAAT,EAAY6T,CAAZ,EAAeK,EAAE,CAAF,CAAf,EAAqB,CAArB,EAAwB,CAAC,SAAzB,CAAJ;AACAL,YAAIC,GAAGD,CAAH,EAAMN,CAAN,EAASC,CAAT,EAAYxT,CAAZ,EAAekU,EAAE,CAAF,CAAf,EAAqB,CAArB,EAAwB,CAAC,UAAzB,CAAJ;AACAlU,YAAI8T,GAAG9T,CAAH,EAAM6T,CAAN,EAASN,CAAT,EAAYC,CAAZ,EAAeU,EAAE,EAAF,CAAf,EAAsB,EAAtB,EAA0B,SAA1B,CAAJ;AACAV,YAAIM,GAAGN,CAAH,EAAMxT,CAAN,EAAS6T,CAAT,EAAYN,CAAZ,EAAeW,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,CAAC,SAA1B,CAAJ;AACAX,YAAIO,GAAGP,CAAH,EAAMC,CAAN,EAASxT,CAAT,EAAY6T,CAAZ,EAAeK,EAAE,CAAF,CAAf,EAAqB,CAArB,EAAwB,CAAC,SAAzB,CAAJ;AACAL,YAAIC,GAAGD,CAAH,EAAMN,CAAN,EAASC,CAAT,EAAYxT,CAAZ,EAAekU,EAAE,EAAF,CAAf,EAAsB,CAAtB,EAAyB,QAAzB,CAAJ;AACAlU,YAAI8T,GAAG9T,CAAH,EAAM6T,CAAN,EAASN,CAAT,EAAYC,CAAZ,EAAeU,EAAE,EAAF,CAAf,EAAsB,EAAtB,EAA0B,CAAC,SAA3B,CAAJ;AACAV,YAAIM,GAAGN,CAAH,EAAMxT,CAAN,EAAS6T,CAAT,EAAYN,CAAZ,EAAeW,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,CAAC,SAA1B,CAAJ;AACAX,YAAIO,GAAGP,CAAH,EAAMC,CAAN,EAASxT,CAAT,EAAY6T,CAAZ,EAAeK,EAAE,CAAF,CAAf,EAAqB,CAArB,EAAwB,SAAxB,CAAJ;AACAL,YAAIC,GAAGD,CAAH,EAAMN,CAAN,EAASC,CAAT,EAAYxT,CAAZ,EAAekU,EAAE,EAAF,CAAf,EAAsB,CAAtB,EAAyB,CAAC,UAA1B,CAAJ;AACAlU,YAAI8T,GAAG9T,CAAH,EAAM6T,CAAN,EAASN,CAAT,EAAYC,CAAZ,EAAeU,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,CAAC,SAA1B,CAAJ;AACAV,YAAIM,GAAGN,CAAH,EAAMxT,CAAN,EAAS6T,CAAT,EAAYN,CAAZ,EAAeW,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,UAAzB,CAAJ;AACAX,YAAIO,GAAGP,CAAH,EAAMC,CAAN,EAASxT,CAAT,EAAY6T,CAAZ,EAAeK,EAAE,EAAF,CAAf,EAAsB,CAAtB,EAAyB,CAAC,UAA1B,CAAJ;AACAL,YAAIC,GAAGD,CAAH,EAAMN,CAAN,EAASC,CAAT,EAAYxT,CAAZ,EAAekU,EAAE,CAAF,CAAf,EAAqB,CAArB,EAAwB,CAAC,QAAzB,CAAJ;AACAlU,YAAI8T,GAAG9T,CAAH,EAAM6T,CAAN,EAASN,CAAT,EAAYC,CAAZ,EAAeU,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,UAAzB,CAAJ;AACAV,YAAIM,GAAGN,CAAH,EAAMxT,CAAN,EAAS6T,CAAT,EAAYN,CAAZ,EAAeW,EAAE,EAAF,CAAf,EAAsB,EAAtB,EAA0B,CAAC,UAA3B,CAAJ;;AAEAX,YAAIQ,GAAGR,CAAH,EAAMC,CAAN,EAASxT,CAAT,EAAY6T,CAAZ,EAAeK,EAAE,CAAF,CAAf,EAAqB,CAArB,EAAwB,CAAC,MAAzB,CAAJ;AACAL,YAAIE,GAAGF,CAAH,EAAMN,CAAN,EAASC,CAAT,EAAYxT,CAAZ,EAAekU,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,CAAC,UAA1B,CAAJ;AACAlU,YAAI+T,GAAG/T,CAAH,EAAM6T,CAAN,EAASN,CAAT,EAAYC,CAAZ,EAAeU,EAAE,EAAF,CAAf,EAAsB,EAAtB,EAA0B,UAA1B,CAAJ;AACAV,YAAIO,GAAGP,CAAH,EAAMxT,CAAN,EAAS6T,CAAT,EAAYN,CAAZ,EAAeW,EAAE,EAAF,CAAf,EAAsB,EAAtB,EAA0B,CAAC,QAA3B,CAAJ;AACAX,YAAIQ,GAAGR,CAAH,EAAMC,CAAN,EAASxT,CAAT,EAAY6T,CAAZ,EAAeK,EAAE,CAAF,CAAf,EAAqB,CAArB,EAAwB,CAAC,UAAzB,CAAJ;AACAL,YAAIE,GAAGF,CAAH,EAAMN,CAAN,EAASC,CAAT,EAAYxT,CAAZ,EAAekU,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,UAAzB,CAAJ;AACAlU,YAAI+T,GAAG/T,CAAH,EAAM6T,CAAN,EAASN,CAAT,EAAYC,CAAZ,EAAeU,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,CAAC,SAA1B,CAAJ;AACAV,YAAIO,GAAGP,CAAH,EAAMxT,CAAN,EAAS6T,CAAT,EAAYN,CAAZ,EAAeW,EAAE,EAAF,CAAf,EAAsB,EAAtB,EAA0B,CAAC,UAA3B,CAAJ;AACAX,YAAIQ,GAAGR,CAAH,EAAMC,CAAN,EAASxT,CAAT,EAAY6T,CAAZ,EAAeK,EAAE,EAAF,CAAf,EAAsB,CAAtB,EAAyB,SAAzB,CAAJ;AACAL,YAAIE,GAAGF,CAAH,EAAMN,CAAN,EAASC,CAAT,EAAYxT,CAAZ,EAAekU,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,CAAC,SAA1B,CAAJ;AACAlU,YAAI+T,GAAG/T,CAAH,EAAM6T,CAAN,EAASN,CAAT,EAAYC,CAAZ,EAAeU,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,CAAC,SAA1B,CAAJ;AACAV,YAAIO,GAAGP,CAAH,EAAMxT,CAAN,EAAS6T,CAAT,EAAYN,CAAZ,EAAeW,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,QAAzB,CAAJ;AACAX,YAAIQ,GAAGR,CAAH,EAAMC,CAAN,EAASxT,CAAT,EAAY6T,CAAZ,EAAeK,EAAE,CAAF,CAAf,EAAqB,CAArB,EAAwB,CAAC,SAAzB,CAAJ;AACAL,YAAIE,GAAGF,CAAH,EAAMN,CAAN,EAASC,CAAT,EAAYxT,CAAZ,EAAekU,EAAE,EAAF,CAAf,EAAsB,EAAtB,EAA0B,CAAC,SAA3B,CAAJ;AACAlU,YAAI+T,GAAG/T,CAAH,EAAM6T,CAAN,EAASN,CAAT,EAAYC,CAAZ,EAAeU,EAAE,EAAF,CAAf,EAAsB,EAAtB,EAA0B,SAA1B,CAAJ;AACAV,YAAIO,GAAGP,CAAH,EAAMxT,CAAN,EAAS6T,CAAT,EAAYN,CAAZ,EAAeW,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,CAAC,SAA1B,CAAJ;;AAEAX,YAAIS,GAAGT,CAAH,EAAMC,CAAN,EAASxT,CAAT,EAAY6T,CAAZ,EAAeK,EAAE,CAAF,CAAf,EAAqB,CAArB,EAAwB,CAAC,SAAzB,CAAJ;AACAL,YAAIG,GAAGH,CAAH,EAAMN,CAAN,EAASC,CAAT,EAAYxT,CAAZ,EAAekU,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,UAAzB,CAAJ;AACAlU,YAAIgU,GAAGhU,CAAH,EAAM6T,CAAN,EAASN,CAAT,EAAYC,CAAZ,EAAeU,EAAE,EAAF,CAAf,EAAsB,EAAtB,EAA0B,CAAC,UAA3B,CAAJ;AACAV,YAAIQ,GAAGR,CAAH,EAAMxT,CAAN,EAAS6T,CAAT,EAAYN,CAAZ,EAAeW,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,CAAC,QAA1B,CAAJ;AACAX,YAAIS,GAAGT,CAAH,EAAMC,CAAN,EAASxT,CAAT,EAAY6T,CAAZ,EAAeK,EAAE,EAAF,CAAf,EAAsB,CAAtB,EAAyB,UAAzB,CAAJ;AACAL,YAAIG,GAAGH,CAAH,EAAMN,CAAN,EAASC,CAAT,EAAYxT,CAAZ,EAAekU,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,CAAC,UAA1B,CAAJ;AACAlU,YAAIgU,GAAGhU,CAAH,EAAM6T,CAAN,EAASN,CAAT,EAAYC,CAAZ,EAAeU,EAAE,EAAF,CAAf,EAAsB,EAAtB,EAA0B,CAAC,OAA3B,CAAJ;AACAV,YAAIQ,GAAGR,CAAH,EAAMxT,CAAN,EAAS6T,CAAT,EAAYN,CAAZ,EAAeW,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,CAAC,UAA1B,CAAJ;AACAX,YAAIS,GAAGT,CAAH,EAAMC,CAAN,EAASxT,CAAT,EAAY6T,CAAZ,EAAeK,EAAE,CAAF,CAAf,EAAqB,CAArB,EAAwB,UAAxB,CAAJ;AACAL,YAAIG,GAAGH,CAAH,EAAMN,CAAN,EAASC,CAAT,EAAYxT,CAAZ,EAAekU,EAAE,EAAF,CAAf,EAAsB,EAAtB,EAA0B,CAAC,QAA3B,CAAJ;AACAlU,YAAIgU,GAAGhU,CAAH,EAAM6T,CAAN,EAASN,CAAT,EAAYC,CAAZ,EAAeU,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,CAAC,UAA1B,CAAJ;AACAV,YAAIQ,GAAGR,CAAH,EAAMxT,CAAN,EAAS6T,CAAT,EAAYN,CAAZ,EAAeW,EAAE,EAAF,CAAf,EAAsB,EAAtB,EAA0B,UAA1B,CAAJ;AACAX,YAAIS,GAAGT,CAAH,EAAMC,CAAN,EAASxT,CAAT,EAAY6T,CAAZ,EAAeK,EAAE,CAAF,CAAf,EAAqB,CAArB,EAAwB,CAAC,SAAzB,CAAJ;AACAL,YAAIG,GAAGH,CAAH,EAAMN,CAAN,EAASC,CAAT,EAAYxT,CAAZ,EAAekU,EAAE,EAAF,CAAf,EAAsB,EAAtB,EAA0B,CAAC,UAA3B,CAAJ;AACAlU,YAAIgU,GAAGhU,CAAH,EAAM6T,CAAN,EAASN,CAAT,EAAYC,CAAZ,EAAeU,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,SAAzB,CAAJ;AACAV,YAAIQ,GAAGR,CAAH,EAAMxT,CAAN,EAAS6T,CAAT,EAAYN,CAAZ,EAAeW,EAAE,CAAF,CAAf,EAAqB,EAArB,EAAyB,CAAC,SAA1B,CAAJ;;AAEA1B,UAAE,CAAF,IAAOc,MAAMC,CAAN,EAASf,EAAE,CAAF,CAAT,CAAP;AACAA,UAAE,CAAF,IAAOc,MAAME,CAAN,EAAShB,EAAE,CAAF,CAAT,CAAP;AACAA,UAAE,CAAF,IAAOc,MAAMtT,CAAN,EAASwS,EAAE,CAAF,CAAT,CAAP;AACAA,UAAE,CAAF,IAAOc,MAAMO,CAAN,EAASrB,EAAE,CAAF,CAAT,CAAP;AACH,KAvGD;;;AAyGA;;;;;;;;;;;;;;;AAeA2B,aAAS,SAATA,MAAS,CAAUtW,CAAV,EAAa;AAClB,YAAIuW,UAAU,EAAd;AAAA,YACI7X,CADJ,CADkB,CAEX;;AAEP,aAAKA,IAAI,CAAT,EAAYA,IAAI,EAAhB,EAAoBA,KAAK,CAAzB,EAA4B;AACxB6X,oBAAQ7X,KAAK,CAAb,IAAkBsB,EAAEwW,UAAF,CAAa9X,CAAb,KAAmBsB,EAAEwW,UAAF,CAAa9X,IAAI,CAAjB,KAAuB,CAA1C,KAAgDsB,EAAEwW,UAAF,CAAa9X,IAAI,CAAjB,KAAuB,EAAvE,KAA8EsB,EAAEwW,UAAF,CAAa9X,IAAI,CAAjB,KAAuB,EAArG,CAAlB;AACH;AACD,eAAO6X,OAAP;AACH,KAhID;AAAA,QAkIAE,eAAe,SAAfA,YAAe,CAAUf,CAAV,EAAa;AACxB,YAAIa,UAAU,EAAd;AAAA,YACI7X,CADJ,CADwB,CAEjB;;AAEP,aAAKA,IAAI,CAAT,EAAYA,IAAI,EAAhB,EAAoBA,KAAK,CAAzB,EAA4B;AACxB6X,oBAAQ7X,KAAK,CAAb,IAAkBgX,EAAEhX,CAAF,KAAQgX,EAAEhX,IAAI,CAAN,KAAY,CAApB,KAA0BgX,EAAEhX,IAAI,CAAN,KAAY,EAAtC,KAA6CgX,EAAEhX,IAAI,CAAN,KAAY,EAAzD,CAAlB;AACH;AACD,eAAO6X,OAAP;AACH,KA1ID;AAAA,QA4IAG,OAAO,SAAPA,IAAO,CAAU1W,CAAV,EAAa;AAChB,YAAI2W,IAAI3W,EAAEpB,MAAV;AAAA,YACIgY,QAAQ,CAAC,UAAD,EAAa,CAAC,SAAd,EAAyB,CAAC,UAA1B,EAAsC,SAAtC,CADZ;AAAA,YAEIlY,CAFJ;AAAA,YAGIE,MAHJ;AAAA,YAIIiY,IAJJ;AAAA,YAKIC,GALJ;AAAA,YAMIC,EANJ;AAAA,YAOIC,EAPJ;;AASA,aAAKtY,IAAI,EAAT,EAAaA,KAAKiY,CAAlB,EAAqBjY,KAAK,EAA1B,EAA8B;AAC1B0X,qBAASQ,KAAT,EAAgBN,OAAOtW,EAAEf,SAAF,CAAYP,IAAI,EAAhB,EAAoBA,CAApB,CAAP,CAAhB;AACH;AACDsB,YAAIA,EAAEf,SAAF,CAAYP,IAAI,EAAhB,CAAJ;AACAE,iBAASoB,EAAEpB,MAAX;AACAiY,eAAO,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B,EAAkC,CAAlC,EAAqC,CAArC,EAAwC,CAAxC,EAA2C,CAA3C,EAA8C,CAA9C,CAAP;AACA,aAAKnY,IAAI,CAAT,EAAYA,IAAIE,MAAhB,EAAwBF,KAAK,CAA7B,EAAgC;AAC5BmY,iBAAKnY,KAAK,CAAV,KAAgBsB,EAAEwW,UAAF,CAAa9X,CAAb,MAAqBA,IAAI,CAAL,IAAW,CAA/B,CAAhB;AACH;AACDmY,aAAKnY,KAAK,CAAV,KAAgB,SAAUA,IAAI,CAAL,IAAW,CAApB,CAAhB;AACA,YAAIA,IAAI,EAAR,EAAY;AACR0X,qBAASQ,KAAT,EAAgBC,IAAhB;AACA,iBAAKnY,IAAI,CAAT,EAAYA,IAAI,EAAhB,EAAoBA,KAAK,CAAzB,EAA4B;AACxBmY,qBAAKnY,CAAL,IAAU,CAAV;AACH;AACJ;;AAED;AACAoY,cAAMH,IAAI,CAAV;AACAG,cAAMA,IAAI7B,QAAJ,CAAa,EAAb,EAAiBgC,KAAjB,CAAuB,gBAAvB,CAAN;AACAF,aAAKG,SAASJ,IAAI,CAAJ,CAAT,EAAiB,EAAjB,CAAL;AACAE,aAAKE,SAASJ,IAAI,CAAJ,CAAT,EAAiB,EAAjB,KAAwB,CAA7B;;AAEAD,aAAK,EAAL,IAAWE,EAAX;AACAF,aAAK,EAAL,IAAWG,EAAX;;AAEAZ,iBAASQ,KAAT,EAAgBC,IAAhB;AACA,eAAOD,KAAP;AACH,KAlLD;AAAA,QAoLAO,aAAa,SAAbA,UAAa,CAAUzB,CAAV,EAAa;AACtB,YAAIiB,IAAIjB,EAAE9W,MAAV;AAAA,YACIgY,QAAQ,CAAC,UAAD,EAAa,CAAC,SAAd,EAAyB,CAAC,UAA1B,EAAsC,SAAtC,CADZ;AAAA,YAEIlY,CAFJ;AAAA,YAGIE,MAHJ;AAAA,YAIIiY,IAJJ;AAAA,YAKIC,GALJ;AAAA,YAMIC,EANJ;AAAA,YAOIC,EAPJ;;AASA,aAAKtY,IAAI,EAAT,EAAaA,KAAKiY,CAAlB,EAAqBjY,KAAK,EAA1B,EAA8B;AAC1B0X,qBAASQ,KAAT,EAAgBH,aAAaf,EAAE0B,QAAF,CAAW1Y,IAAI,EAAf,EAAmBA,CAAnB,CAAb,CAAhB;AACH;;AAED;AACA;AACA;AACA;AACAgX,YAAKhX,IAAI,EAAL,GAAWiY,CAAX,GAAejB,EAAE0B,QAAF,CAAW1Y,IAAI,EAAf,CAAf,GAAoC,IAAI2Y,UAAJ,CAAe,CAAf,CAAxC;;AAEAzY,iBAAS8W,EAAE9W,MAAX;AACAiY,eAAO,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B,EAAkC,CAAlC,EAAqC,CAArC,EAAwC,CAAxC,EAA2C,CAA3C,EAA8C,CAA9C,CAAP;AACA,aAAKnY,IAAI,CAAT,EAAYA,IAAIE,MAAhB,EAAwBF,KAAK,CAA7B,EAAgC;AAC5BmY,iBAAKnY,KAAK,CAAV,KAAgBgX,EAAEhX,CAAF,MAAUA,IAAI,CAAL,IAAW,CAApB,CAAhB;AACH;;AAEDmY,aAAKnY,KAAK,CAAV,KAAgB,SAAUA,IAAI,CAAL,IAAW,CAApB,CAAhB;AACA,YAAIA,IAAI,EAAR,EAAY;AACR0X,qBAASQ,KAAT,EAAgBC,IAAhB;AACA,iBAAKnY,IAAI,CAAT,EAAYA,IAAI,EAAhB,EAAoBA,KAAK,CAAzB,EAA4B;AACxBmY,qBAAKnY,CAAL,IAAU,CAAV;AACH;AACJ;;AAED;AACAoY,cAAMH,IAAI,CAAV;AACAG,cAAMA,IAAI7B,QAAJ,CAAa,EAAb,EAAiBgC,KAAjB,CAAuB,gBAAvB,CAAN;AACAF,aAAKG,SAASJ,IAAI,CAAJ,CAAT,EAAiB,EAAjB,CAAL;AACAE,aAAKE,SAASJ,IAAI,CAAJ,CAAT,EAAiB,EAAjB,KAAwB,CAA7B;;AAEAD,aAAK,EAAL,IAAWE,EAAX;AACAF,aAAK,EAAL,IAAWG,EAAX;;AAEAZ,iBAASQ,KAAT,EAAgBC,IAAhB;;AAEA,eAAOD,KAAP;AACH,KAlOD;AAAA,QAoOAU,UAAU,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,EAAqB,GAArB,EAA0B,GAA1B,EAA+B,GAA/B,EAAoC,GAApC,EAAyC,GAAzC,EAA8C,GAA9C,EAAmD,GAAnD,EAAwD,GAAxD,EAA6D,GAA7D,EAAkE,GAAlE,EAAuE,GAAvE,EAA4E,GAA5E,CApOV;AAAA,QAsOAC,OAAO,SAAPA,IAAO,CAAUZ,CAAV,EAAa;AAChB,YAAI3W,IAAI,EAAR;AAAA,YACIrB,CADJ;AAEA,aAAKA,IAAI,CAAT,EAAYA,IAAI,CAAhB,EAAmBA,KAAK,CAAxB,EAA2B;AACvBqB,iBAAKsX,QAASX,KAAMhY,IAAI,CAAJ,GAAQ,CAAf,GAAqB,IAA7B,IAAqC2Y,QAASX,KAAMhY,IAAI,CAAX,GAAiB,IAAzB,CAA1C;AACH;AACD,eAAOqB,CAAP;AACH,KA7OD;AAAA,QA+OAwX,MAAM,SAANA,GAAM,CAAU7C,CAAV,EAAa;AACf,YAAIjW,CAAJ;AACA,aAAKA,IAAI,CAAT,EAAYA,IAAIiW,EAAE/V,MAAlB,EAA0BF,KAAK,CAA/B,EAAkC;AAC9BiW,cAAEjW,CAAF,IAAO6Y,KAAK5C,EAAEjW,CAAF,CAAL,CAAP;AACH;AACD,eAAOiW,EAAE8C,IAAF,CAAO,EAAP,CAAP;AACH,KArPD;AAAA,QAuPAtJ,MAAM,SAANA,GAAM,CAAUnO,CAAV,EAAa;AACf,eAAOwX,IAAId,KAAK1W,CAAL,CAAJ,CAAP;AACH,KAzPD;;;AA6PA;;AAEA;;;;;;AAMAwV,eAAW,SAAXA,QAAW,GAAY;AACnB;AACA,aAAKkC,KAAL;AACH,KAxQD;;AA2QA;AACA,QAAIvJ,IAAI,OAAJ,MAAiB,kCAArB,EAAyD;AACrDsH,gBAAQ,eAAUd,CAAV,EAAaC,CAAb,EAAgB;AACpB,gBAAI+C,MAAM,CAAChD,IAAI,MAAL,KAAgBC,IAAI,MAApB,CAAV;AAAA,gBACIgD,MAAM,CAACjD,KAAK,EAAN,KAAaC,KAAK,EAAlB,KAAyB+C,OAAO,EAAhC,CADV;AAEA,mBAAQC,OAAO,EAAR,GAAeD,MAAM,MAA5B;AACH,SAJD;AAKH;;AAGD;;;;;;;;AAQAnC,aAASqC,SAAT,CAAmBzQ,MAAnB,GAA4B,UAAUjE,GAAV,EAAe;AACvC;AACA,YAAI,kBAAkBzF,IAAlB,CAAuByF,GAAvB,CAAJ,EAAiC;AAC7BA,kBAAM2U,SAASC,mBAAmB5U,GAAnB,CAAT,CAAN;AACH;;AAED;AACA,aAAK8K,YAAL,CAAkB9K,GAAlB;;AAEA,eAAO,IAAP;AACH,KAVD;;AAYA;;;;;;;AAOAqS,aAASqC,SAAT,CAAmB5J,YAAnB,GAAkC,UAAU+J,QAAV,EAAoB;AAClD,aAAKC,KAAL,IAAcD,QAAd;AACA,aAAKE,OAAL,IAAgBF,SAASpZ,MAAzB;;AAEA,YAAIA,SAAS,KAAKqZ,KAAL,CAAWrZ,MAAxB;AAAA,YACIF,CADJ;;AAGA,aAAKA,IAAI,EAAT,EAAaA,KAAKE,MAAlB,EAA0BF,KAAK,EAA/B,EAAmC;AAC/B0X,qBAAS,KAAK+B,MAAd,EAAsB7B,OAAO,KAAK2B,KAAL,CAAWhZ,SAAX,CAAqBP,IAAI,EAAzB,EAA6BA,CAA7B,CAAP,CAAtB;AACH;;AAED,aAAKuZ,KAAL,GAAa,KAAKA,KAAL,CAAWG,MAAX,CAAkB1Z,IAAI,EAAtB,CAAb;;AAEA,eAAO,IAAP;AACH,KAdD;;AAgBA;;;;;;;;;AASA8W,aAASqC,SAAT,CAAmBzJ,GAAnB,GAAyB,UAAUiK,GAAV,EAAe;AACpC,YAAIC,OAAO,KAAKL,KAAhB;AAAA,YACIrZ,SAAS0Z,KAAK1Z,MADlB;AAAA,YAEIF,CAFJ;AAAA,YAGImY,OAAO,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B,EAAkC,CAAlC,EAAqC,CAArC,EAAwC,CAAxC,EAA2C,CAA3C,EAA8C,CAA9C,CAHX;AAAA,YAII0B,GAJJ;;AAMA,aAAK7Z,IAAI,CAAT,EAAYA,IAAIE,MAAhB,EAAwBF,KAAK,CAA7B,EAAgC;AAC5BmY,iBAAKnY,KAAK,CAAV,KAAgB4Z,KAAK9B,UAAL,CAAgB9X,CAAhB,MAAwBA,IAAI,CAAL,IAAW,CAAlC,CAAhB;AACH;;AAED,aAAK8Z,OAAL,CAAa3B,IAAb,EAAmBjY,MAAnB;AACA2Z,cAAM,CAAC,CAACF,GAAF,GAAQ,KAAKF,MAAb,GAAsBX,IAAI,KAAKW,MAAT,CAA5B;;AAEA,aAAKT,KAAL;;AAEA,eAAOa,GAAP;AACH,KAjBD;;AAmBA;;;;;;AAMA/C,aAASqC,SAAT,CAAmBW,OAAnB,GAA6B,UAAU3B,IAAV,EAAgBjY,MAAhB,EAAwB;AACjD,YAAIF,IAAIE,MAAR;AAAA,YACIkY,GADJ;AAAA,YAEIC,EAFJ;AAAA,YAGIC,EAHJ;;AAKAH,aAAKnY,KAAK,CAAV,KAAgB,SAAUA,IAAI,CAAL,IAAW,CAApB,CAAhB;AACA,YAAIA,IAAI,EAAR,EAAY;AACR0X,qBAAS,KAAK+B,MAAd,EAAsBtB,IAAtB;AACA,iBAAKnY,IAAI,CAAT,EAAYA,IAAI,EAAhB,EAAoBA,KAAK,CAAzB,EAA4B;AACxBmY,qBAAKnY,CAAL,IAAU,CAAV;AACH;AACJ;;AAED;AACA;AACAoY,cAAM,KAAKoB,OAAL,GAAe,CAArB;AACApB,cAAMA,IAAI7B,QAAJ,CAAa,EAAb,EAAiBgC,KAAjB,CAAuB,gBAAvB,CAAN;AACAF,aAAKG,SAASJ,IAAI,CAAJ,CAAT,EAAiB,EAAjB,CAAL;AACAE,aAAKE,SAASJ,IAAI,CAAJ,CAAT,EAAiB,EAAjB,KAAwB,CAA7B;;AAEAD,aAAK,EAAL,IAAWE,EAAX;AACAF,aAAK,EAAL,IAAWG,EAAX;AACAZ,iBAAS,KAAK+B,MAAd,EAAsBtB,IAAtB;AACH,KAxBD;;AA0BA;;;;;AAKArB,aAASqC,SAAT,CAAmBH,KAAnB,GAA2B,YAAY;AACnC,aAAKO,KAAL,GAAa,EAAb;AACA,aAAKC,OAAL,GAAe,CAAf;AACA,aAAKC,MAAL,GAAc,CAAC,UAAD,EAAa,CAAC,SAAd,EAAyB,CAAC,UAA1B,EAAsC,SAAtC,CAAd;;AAEA,eAAO,IAAP;AACH,KAND;;AAQA;;;;AAIA3C,aAASqC,SAAT,CAAmBxJ,OAAnB,GAA6B,YAAY;AACrC,eAAO,KAAK8J,MAAZ;AACA,eAAO,KAAKF,KAAZ;AACA,eAAO,KAAKC,OAAZ;AACH,KAJD;;AAOA;;;;;;;;;AASA1C,aAASf,IAAT,GAAgB,UAAUtR,GAAV,EAAekV,GAAf,EAAoB;AAChC;AACA,YAAI,kBAAkB3a,IAAlB,CAAuByF,GAAvB,CAAJ,EAAiC;AAC7BA,kBAAM2U,SAASC,mBAAmB5U,GAAnB,CAAT,CAAN;AACH;;AAED,YAAIsR,OAAOiC,KAAKvT,GAAL,CAAX;;AAEA,eAAO,CAAC,CAACkV,GAAF,GAAQ5D,IAAR,GAAe+C,IAAI/C,IAAJ,CAAtB;AACH,KATD;;AAWA;;;;;;;;AAQAe,aAASiD,UAAT,GAAsB,UAAUC,OAAV,EAAmBL,GAAnB,EAAwB;AAC1C,YAAI5D,OAAOiC,KAAKgC,OAAL,CAAX;;AAEA,eAAO,CAAC,CAACL,GAAF,GAAQ5D,IAAR,GAAe+C,IAAI/C,IAAJ,CAAtB;AACH,KAJD;;AAMA;;;;;AAKAe,aAASmD,WAAT,GAAuB,YAAY;AAC/B;AACA,aAAKjB,KAAL;AACH,KAHD;;AAKA;;AAEA;;;;;;;AAOAlC,aAASmD,WAAT,CAAqBd,SAArB,CAA+BzQ,MAA/B,GAAwC,UAAUwR,GAAV,EAAe;AACnD;AACA;AACA,YAAIN,OAAO,KAAKO,kBAAL,CAAwB,KAAKZ,KAA7B,EAAoCW,GAApC,CAAX;AAAA,YACIha,SAAS0Z,KAAK1Z,MADlB;AAAA,YAEIF,CAFJ;;AAIA,aAAKwZ,OAAL,IAAgBU,IAAIE,UAApB;;AAEA,aAAKpa,IAAI,EAAT,EAAaA,KAAKE,MAAlB,EAA0BF,KAAK,EAA/B,EAAmC;AAC/B0X,qBAAS,KAAK+B,MAAd,EAAsB1B,aAAa6B,KAAKlB,QAAL,CAAc1Y,IAAI,EAAlB,EAAsBA,CAAtB,CAAb,CAAtB;AACH;;AAED;AACA,aAAKuZ,KAAL,GAAcvZ,IAAI,EAAL,GAAWE,MAAX,GAAoB0Z,KAAKlB,QAAL,CAAc1Y,IAAI,EAAlB,CAApB,GAA4C,IAAI2Y,UAAJ,CAAe,CAAf,CAAzD;;AAEA,eAAO,IAAP;AACH,KAjBD;;AAmBA;;;;;;;;;AASA7B,aAASmD,WAAT,CAAqBd,SAArB,CAA+BzJ,GAA/B,GAAqC,UAAUiK,GAAV,EAAe;AAChD,YAAIC,OAAO,KAAKL,KAAhB;AAAA,YACIrZ,SAAS0Z,KAAK1Z,MADlB;AAAA,YAEIiY,OAAO,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B,EAAkC,CAAlC,EAAqC,CAArC,EAAwC,CAAxC,EAA2C,CAA3C,EAA8C,CAA9C,CAFX;AAAA,YAGInY,CAHJ;AAAA,YAII6Z,GAJJ;;AAMA,aAAK7Z,IAAI,CAAT,EAAYA,IAAIE,MAAhB,EAAwBF,KAAK,CAA7B,EAAgC;AAC5BmY,iBAAKnY,KAAK,CAAV,KAAgB4Z,KAAK5Z,CAAL,MAAaA,IAAI,CAAL,IAAW,CAAvB,CAAhB;AACH;;AAED,aAAK8Z,OAAL,CAAa3B,IAAb,EAAmBjY,MAAnB;AACA2Z,cAAM,CAAC,CAACF,GAAF,GAAQ,KAAKF,MAAb,GAAsBX,IAAI,KAAKW,MAAT,CAA5B;;AAEA,aAAKT,KAAL;;AAEA,eAAOa,GAAP;AACH,KAjBD;;AAmBA/C,aAASmD,WAAT,CAAqBd,SAArB,CAA+BW,OAA/B,GAAyChD,SAASqC,SAAT,CAAmBW,OAA5D;;AAEA;;;;;AAKAhD,aAASmD,WAAT,CAAqBd,SAArB,CAA+BH,KAA/B,GAAuC,YAAY;AAC/C,aAAKO,KAAL,GAAa,IAAIZ,UAAJ,CAAe,CAAf,CAAb;AACA,aAAKa,OAAL,GAAe,CAAf;AACA,aAAKC,MAAL,GAAc,CAAC,UAAD,EAAa,CAAC,SAAd,EAAyB,CAAC,UAA1B,EAAsC,SAAtC,CAAd;;AAEA,eAAO,IAAP;AACH,KAND;;AAQA;;;;AAIA3C,aAASmD,WAAT,CAAqBd,SAArB,CAA+BxJ,OAA/B,GAAyCmH,SAASqC,SAAT,CAAmBxJ,OAA5D;;AAEA;;;;;;;;AAQAmH,aAASmD,WAAT,CAAqBd,SAArB,CAA+BgB,kBAA/B,GAAoD,UAAUE,KAAV,EAAiBC,MAAjB,EAAyB;AACzE,YAAIC,cAAcF,MAAMna,MAAxB;AAAA,YACI0J,SAAS,IAAI+O,UAAJ,CAAe4B,cAAcD,OAAOF,UAApC,CADb;;AAGAxQ,eAAO4Q,GAAP,CAAWH,KAAX;AACAzQ,eAAO4Q,GAAP,CAAW,IAAI7B,UAAJ,CAAe2B,MAAf,CAAX,EAAmCC,WAAnC;;AAEA,eAAO3Q,MAAP;AACH,KARD;;AAUA;;;;;;;;AAQAkN,aAASmD,WAAT,CAAqBlE,IAArB,GAA4B,UAAUmE,GAAV,EAAeP,GAAf,EAAoB;AAC5C,YAAI5D,OAAO0C,WAAW,IAAIE,UAAJ,CAAeuB,GAAf,CAAX,CAAX;;AAEA,eAAO,CAAC,CAACP,GAAF,GAAQ5D,IAAR,GAAe+C,IAAI/C,IAAJ,CAAtB;AACH,KAJD;;AAMA,WAAOe,QAAP;AACH,CAplBA,CAAD,C;;;;;;;ACAa;;AAEb;;AAEA,gBAAgB,mBAAO,CAAC,EAA4B;;AAEpD;;AAEA,cAAc,mBAAO,CAAC,EAAmB;;AAEzC;;AAEA,iHAAiH,mBAAmB,EAAE,mBAAmB,4JAA4J;;AAErT,sCAAsC,uCAAuC,gBAAgB;;AAE7F;AACA;AACA,CAAC;AACD;AACA,E;;;;;;ACpBA,kBAAkB,YAAY,mBAAO,CAAC,EAAoC,sB;;;;;;ACA1E,mBAAO,CAAC,EAAmC;AAC3C,mBAAO,CAAC,EAAgC;AACxC,iBAAiB,mBAAO,CAAC,EAAwB;;;;;;;ACFjD,kBAAkB,YAAY,mBAAO,CAAC,EAA2B,sB;;;;;;ACAjE,mBAAO,CAAC,EAA0B;AAClC,mBAAO,CAAC,EAAoC;AAC5C,mBAAO,CAAC,EAAyC;AACjD,mBAAO,CAAC,GAAqC;AAC7C,iBAAiB,mBAAO,CAAC,CAAqB;;;;;;;;ACJjC;AACb;AACA,aAAa,mBAAO,CAAC,CAAW;AAChC,UAAU,mBAAO,CAAC,CAAQ;AAC1B,kBAAkB,mBAAO,CAAC,CAAgB;AAC1C,cAAc,mBAAO,CAAC,EAAW;AACjC,eAAe,mBAAO,CAAC,EAAa;AACpC,WAAW,mBAAO,CAAC,EAAS;AAC5B,aAAa,mBAAO,CAAC,EAAU;AAC/B,aAAa,mBAAO,CAAC,EAAW;AAChC,qBAAqB,mBAAO,CAAC,EAAsB;AACnD,UAAU,mBAAO,CAAC,EAAQ;AAC1B,UAAU,mBAAO,CAAC,CAAQ;AAC1B,aAAa,mBAAO,CAAC,EAAY;AACjC,gBAAgB,mBAAO,CAAC,EAAe;AACvC,eAAe,mBAAO,CAAC,EAAc;AACrC,cAAc,mBAAO,CAAC,EAAa;AACnC,eAAe,mBAAO,CAAC,CAAc;AACrC,eAAe,mBAAO,CAAC,CAAc;AACrC,eAAe,mBAAO,CAAC,EAAc;AACrC,gBAAgB,mBAAO,CAAC,CAAe;AACvC,kBAAkB,mBAAO,CAAC,EAAiB;AAC3C,iBAAiB,mBAAO,CAAC,EAAkB;AAC3C,cAAc,mBAAO,CAAC,EAAkB;AACxC,cAAc,mBAAO,CAAC,EAAoB;AAC1C,YAAY,mBAAO,CAAC,EAAgB;AACpC,YAAY,mBAAO,CAAC,EAAgB;AACpC,UAAU,mBAAO,CAAC,CAAc;AAChC,YAAY,mBAAO,CAAC,EAAgB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,sBAAsB;AACtB,sBAAsB,uBAAuB,WAAW,IAAI;AAC5D,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,CAAC;AACD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D;AAC3D;AACA,KAAK;AACL;AACA,sBAAsB,mCAAmC;AACzD,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gEAAgE,gCAAgC;AAChG;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,EAAE,mBAAO,CAAC,EAAgB;AAC1B,EAAE,mBAAO,CAAC,EAAe;AACzB;;AAEA,sBAAsB,mBAAO,CAAC,EAAY;AAC1C;AACA;;AAEA;AACA;AACA;AACA;;AAEA,0DAA0D,kBAAkB;;AAE5E;AACA;AACA;AACA,oBAAoB,uBAAuB;;AAE3C,oDAAoD,6BAA6B;;AAEjF;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH,0BAA0B,eAAe,EAAE;AAC3C,0BAA0B,gBAAgB;AAC1C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA,8CAA8C,YAAY,EAAE;;AAE5D;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,OAAO,QAAQ,iCAAiC;AACpG,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,wEAAwE;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,oCAAoC,mBAAO,CAAC,CAAS;AACrD;AACA;AACA;AACA;AACA;AACA;;;;;;;ACrPA,WAAW,mBAAO,CAAC,EAAQ;AAC3B,eAAe,mBAAO,CAAC,CAAc;AACrC,UAAU,mBAAO,CAAC,CAAQ;AAC1B,cAAc,mBAAO,CAAC,CAAc;AACpC;AACA;AACA;AACA;AACA,cAAc,mBAAO,CAAC,EAAU;AAChC,iDAAiD;AACjD,CAAC;AACD;AACA,qBAAqB;AACrB;AACA,SAAS;AACT,GAAG,EAAE;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACpDA;AACA,cAAc,mBAAO,CAAC,EAAgB;AACtC,WAAW,mBAAO,CAAC,EAAgB;AACnC,UAAU,mBAAO,CAAC,EAAe;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;;;;;;ACdA;AACA,UAAU,mBAAO,CAAC,EAAQ;AAC1B;AACA;AACA;;;;;;;ACJA;AACA,gBAAgB,mBAAO,CAAC,CAAe;AACvC,WAAW,mBAAO,CAAC,EAAgB;AACnC,iBAAiB;;AAEjB;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;;;;;;;AClBA,UAAU,mBAAO,CAAC,EAAe;AACjC,iBAAiB,mBAAO,CAAC,EAAkB;AAC3C,gBAAgB,mBAAO,CAAC,CAAe;AACvC,kBAAkB,mBAAO,CAAC,EAAiB;AAC3C,UAAU,mBAAO,CAAC,CAAQ;AAC1B,qBAAqB,mBAAO,CAAC,EAAmB;AAChD;;AAEA,YAAY,mBAAO,CAAC,CAAgB;AACpC;AACA;AACA;AACA;AACA,GAAG,YAAY;AACf;AACA;;;;;;;ACfA,mBAAO,CAAC,EAAe;;;;;;;ACAvB,mBAAO,CAAC,EAAe;;;;;;;;;;;;;;;;;;ACAvB;;;;;;AAEA,IAAInT,aAAauC,mBAAOA,CAAC,EAAR,EAAkCC,OAAnD;AACA,IAAI7B,WAAW4B,mBAAOA,CAAC,EAAR,EAAgCC,OAA/C;;AAEA,IAAIsU,QAAQ,SAARA,KAAQ,CAAUnU,QAAV,EAAoB;AAC5B,QAAIC,OAAO,IAAX;AACA,QAAImU,UAAU,wBAAd;;AAEA,aAAS7T,SAAT,GAAqB;AACjB,YAAI3E,OAAO4E,GAAP,IAAc5E,OAAO4E,GAAP,CAAWhF,MAA7B,EAAqC;AACjC,mBAAOI,OAAO4E,GAAP,CAAWhF,MAAlB;AACH,SAFD,MAGK,IAAIwE,SAASS,UAAb,EAAyB;AAC1B,mBAAOT,SAASS,UAAhB;AACH;AACJ;;AAED,aAAS4T,cAAT,GAA0B;AACtB,YAAIC,UAAU,YAAYvW,SAAS4I,IAAnC;AACA;AACA;AACA;AACA,YAAIL,aAAalO,qBAAW0E,SAAX,CAAqB,YAArB,CAAjB;AACA,YAAItE,MAAM,kBAAV;AACA,YAAI8N,cAAcA,eAAe,YAAjC,EAA8C;AAC1C9N,kBAAM,2BAAN;AACH;AACD,eAAO8b,UAAU9b,GAAjB;AACH;;AAED,aAAS+b,gBAAT,CAA0BlT,IAA1B,EAAgC;AAC5B,YAAIvC,MAAM;AACN0V,sBAAUnT,KAAK8B,QAAL,CAAc/I,IADlB;AAENqa,sBAAUpT,KAAKmG,MAFT;AAGNkN,uBAAW,QAHL;AAINC,uBAAW,EAJL;AAKNC,sBAAU;AACNC,wBAAQrU,IAAIsU,IAAJ,CAAS/E,OAAT,CAAiBgF,EAAjB,CAAoB9E,QAApB,EADF;AAEN+E,0BAAUxU,IAAIsU,IAAJ,CAAS/E,OAAT,CAAiBkF,QAAjB,IAA6BzU,IAAIsU,IAAJ,CAAS/E,OAAT,CAAiBmF,SAFlD;AAGNC,0BAAU3U,IAAIsU,IAAJ,CAAS/E,OAAT,CAAiBqF;AAHrB,aALJ;AAUNC,yBAAa;AAVP,SAAV;AAYA,YAAI9U,YAAY+U,SAAZ,CAAsBC,UAAtB,IAAoC,CAAxC,EAA2C;AACvCzW,gBAAI0W,WAAJ,GAAkBnB,gBAAlB;AACH;AACD,YAAIhT,KAAKoU,UAAT,EAAqB;AACjB3W,gBAAI4W,UAAJ,GAAiB;AACbC,0BAAUtU,KAAKoU,UAAL,CAAgBG,QADb;AAEbC,sBAAMxU,KAAKoU,UAAL,CAAgBK,IAFT;AAGbC,wBAAQ1U,KAAKoU,UAAL,CAAgBO,MAHX;AAIbhB,0BAAU3T,KAAKoU,UAAL,CAAgBQ,QAJb;AAKbC,0BAAU7U,KAAKoU,UAAL,CAAgBU,QALb;AAMbC,0BAAU/U,KAAKoU,UAAL,CAAgBY;AANb,aAAjB;AAQH;AACDvX,YAAIuW,WAAJ,GAAkB3Z,EAAEuH,GAAF,CAAM5B,KAAK8B,QAAL,CAAc8D,KAApB,EAA2B,UAAUrF,IAAV,EAAgB;AACzD,gBAAI+P,IAAI,EAAE2E,UAAU1U,KAAKsF,SAAjB,EAA4BqP,UAAU3U,KAAK4U,KAA3C,EAAR;AACA,gBAAI5U,KAAK6U,WAAL,IAAoB,CAAxB,EAA2B;AACvB,oBAAI7U,KAAKuF,KAAL,IAAc,IAAd,IAAsBvF,KAAKuF,KAAL,IAAc,EAApC,IAA0CvF,KAAKuF,KAAL,IAAc,IAA5D,EAAkE;AAC9D,wBAAI;AACAwK,0BAAE+E,KAAF,GAAUtK,KAAKC,KAAL,CAAWzK,KAAKuF,KAAhB,EAAuB,CAAvB,CAAV;AACH,qBAFD,CAEE,OAAOjM,CAAP,EAAU;AACRyW,0BAAE+E,KAAF,GAAU,EAAV;AACH;AACJ,iBAND,MAMO;AACH/E,sBAAE+E,KAAF,GAAU,EAAV;AACH;AACJ,aAVD,MAUO;AACH/E,kBAAE+E,KAAF,GAAU9U,KAAKuF,KAAL,IAAc,EAAxB;AACH;AACD,mBAAOwK,CAAP;AACH,SAhBiB,CAAlB;AAiBA7S,YAAIuW,WAAJ,CAAgBhc,IAAhB,CAAqB;AACjBid,sBAAU,MADO;AAEjBC,sBAAU,KAFO;AAGjBG,mBAAOrV,KAAKsV;AAHK,SAArB;AAKAjb,UAAE6G,MAAF,CAASzD,IAAIuW,WAAb,EAA0B,EAAEiB,UAAU,UAAZ,EAA1B;AACAxX,YAAIuW,WAAJ,CAAgBhc,IAAhB,CAAqB;AACjBid,sBAAU,UADO;AAEjBC,sBAAU,MAFO;AAGjBG,mBAAOrV,KAAKE;AAHK,SAArB;AAKA,eAAOzC,GAAP;AACH;;AAED,SAAKyE,gBAAL,GAAwB,UAAUrK,QAAV,EAAoB0d,aAApB,EAAmC;AACvDxY,UAAEM,IAAF,CAAO;AACHlG,iBAAK4b,UAAU,iDADZ;AAEHrV,qBAAS,iBAAUD,GAAV,EAAe;AACpB,oBAAIK,OAAOzD,EAAEmb,QAAF,CAAW/X,GAAX,IAAkBsN,KAAKC,KAAL,CAAWvN,GAAX,CAAlB,GAAoCA,GAA/C;AACA,oBAAIK,KAAK2X,SAAL,KAAmB,CAAvB,EACI;AACJ,oBAAIxT,SAAS,EAAb;AACA5H,kBAAEiG,OAAF,CAAUxC,KAAK4X,QAAf,EAAyB,UAAUnV,IAAV,EAAgB;AACrC,wBAAIrG,MAAM,MAAMnD,qBAAW6C,YAAX,CAAwB2G,KAAKoV,QAA7B,CAAhB;AACA1T,2BAAOjK,IAAP,CAAY;AACRkI,oCAAYnJ,qBAAWkD,YAAX,CAAwBC,GAAxB,EAA6BgF,WAA7B,EAA0CnB,IAD9C;AAER8D,kCAAU9K,qBAAWiD,eAAX,CAA2BuG,KAAKoV,QAAhC,CAFF;AAGR7T,kCAAU;AACN/I,kCAAMhC,qBAAWgD,WAAX,CAAuBwG,KAAKoV,QAA5B,CADA;AAENzb,iCAAKA;AAFC,yBAHF;AAORsG,gCAAQ,OAPA;AAQRuB,kCAAU,CARF;AASRrC,8BAAMa;AATE,qBAAZ;AAWH,iBAbD;AAcA1I,yBAASoK,MAAT;AACH,aAtBE;AAuBHrE,mBAAO2X,iBAAiB,UAAU9X,GAAV,EAAe;AACnCE,wBAAQC,KAAR,CAAcH,GAAd;AACA,oBAAImY,oBAAoB1W,YAAY0W,iBAAZ,IAAiC1W,YAAYwG,MAAZ,GAAqB,sCAA9E;AACA3O,qCAAWkE,MAAX,CAAkB4C,EAAE,yBAAF,EAA6B,gLAA7B,EAA+M,EAAEgY,MAAMD,iBAAR,EAA/M,CAAlB;AACH;AA3BE,SAAP;AA6BH,KA9BD;;AAgCA,SAAKnT,UAAL,GAAkB,UAAUC,GAAV,EAAeC,MAAf,EAAuB;AACrC,YAAIC,OAAO,EAAX;AACA,gBAAQD,OAAOnB,QAAf;AACI,iBAAK,CAAL;AAAQ;AACJ,oBAAI,CAACnH,EAAEwI,OAAF,CAAUH,GAAV,CAAL,EAAqB;AACjBA,0BAAM,CAACA,GAAD,CAAN;AACH;AACDrI,kBAAEiG,OAAF,CAAUoC,GAAV,EAAe,UAAU1C,IAAV,EAAgB;AAC3B,wBAAIA,KAAKN,IAAT,EAAe;AACXM,6BAAK2B,KAAL,GAAa,CAAC;AACVjC,kCAAMM,KAAKN,IADD;AAEVmC,sCAAU7B,KAAK6B,QAFL;AAGViB,sCAAU9C,KAAKN,IAAL,CAAUoW;AAHV,yBAAD,CAAb;AAKA,+BAAO9V,KAAKN,IAAZ;AACAkD,6BAAK5K,IAAL,CAAUgI,IAAV;AACH,qBARD,MASK,IAAIA,KAAK2B,KAAL,IAAc3B,KAAK2B,KAAL,CAAWpJ,MAAX,GAAoB,CAAtC,EAAyC;AAC1CyH,6BAAK2B,KAAL,GAAatH,EAAEuH,GAAF,CAAM5B,KAAK2B,KAAX,EAAkB,UAAUjC,IAAV,EAAgB;AAC3C,mCAAO;AACHA,sCAAMA,KAAKA,IADR;AAEHmC,0CAAUnC,KAAKmC,QAFZ;AAGHiB,0CAAUpD,KAAKoD;AAHZ,6BAAP;AAKH,yBANY,CAAb;AAOAF,6BAAK5K,IAAL,CAAUgI,IAAV;AACH;AACJ,iBApBD;AAqBA;AACJ,iBAAK,CAAL;AACA,iBAAK,CAAL;AAAQ;AACJ4C,qBAAK5K,IAAL,CAAU0K,GAAV;AACA;AA9BR;AAgCArI,UAAEiG,OAAF,CAAUsC,IAAV,EAAgB,UAAU5C,IAAV,EAAgB;AAC5BA,iBAAKwB,QAAL,GAAgBmB,OAAOnB,QAAvB;AACAxB,iBAAKyB,YAAL,GAAoBkB,OAAOlB,YAA3B;AACAzB,iBAAKuB,YAAL,GAAoBoB,OAAOpB,YAA3B;AACAvB,iBAAKkD,mBAAL,GAA2BP,OAAOO,mBAAlC;AACAlD,iBAAKmD,iBAAL,GAAyBR,OAAOQ,iBAAhC;AACAvE,iBAAKwE,OAAL,CAAapD,IAAb;AACH,SAPD;AAQH,KA1CD;;AA4CA,SAAKoD,OAAL,GAAe,UAAUpD,IAAV,EAAgB;AAC3B,YAAIiF,aAAalO,qBAAW0E,SAAX,CAAqB,YAArB,CAAjB;AACA,YAAItE,MAAM,kCAAkCwH,SAASgH,UAArD;AACA,YAAIV,cAAcA,eAAe,YAAjC,EAA8C;AAC1C9N,kBAAM,2CAA2CwH,SAASgH,UAA1D;AACH;AACDhJ,iBAASsB,IAAT,CAAciB,YAAYwG,MAAZ,GAAqBvO,GAAnC,EAAwC6I,IAAxC,EAA8CxC,IAA9C,CAAmD,UAAUC,GAAV,EAAe;AAC9DA,kBAAMA,IAAIK,IAAV;AACA,gBAAIiY,QAAQ7C,iBAAiBzV,GAAjB,CAAZ;AACAsY,kBAAMC,UAAN,GAAmBvY,IAAIwY,SAAvB;AACA,qBAASC,OAAT,CAAiBxW,IAAjB,EAAuB;AACnB,oBAAIR,YAAYkG,gBAAZ,IAAgC3H,GAAhC,IAAuCA,IAAI6F,aAA/C,EAA8D;AAC1D,wBAAI6S,WAAW5b,OAAOmC,QAAP,CAAgByZ,QAA/B;AACA,wBAAIC,eAAe1W,KAAK0W,YAAL,CAAkBva,KAAlB,CAAwB,GAAxB,CAAnB;AACA,wBAAIua,aAAa7d,MAAb,GAAsB,CAA1B,EAA6B;AACzB,4BAAI8d,YAAYD,aAAa,CAAb,EAAgBld,OAAhB,CAAwB,GAAxB,IAA+B,CAAC,CAAhC,GAAoC,GAApC,GAA0C,GAA1D;AACAwG,6BAAK0W,YAAL,GAAoBA,aAAa,CAAb,IAAkB,GAAlB,GAAwBD,QAAxB,GAAmCC,aAAa,CAAb,EAAgBrE,MAAhB,CAAuBqE,aAAa,CAAb,EAAgBld,OAAhB,CAAwBmd,SAAxB,CAAvB,CAAvD;AACH;AACJ;AACD,uBAAQ5Y,OAAOA,IAAI6F,aAAZ,GAA6B5D,KAAK0W,YAAlC,GAAiD1W,KAAKkE,YAA7D;AACH;;AAED,oBAAQ5D,KAAKwB,QAAb;AACI,qBAAK,CAAL;AACIuU,0BAAMzC,SAAN,CAAgBtb,IAAhB,CAAqB;AACjBse,oCAAYtW,KAAK2B,KAAL,CAAW,CAAX,EAAcjC,IAAd,CAAmBiW,QADd;AAEjBY,kCAAUL,QAAQzY,IAAIkE,KAAJ,CAAU,CAAV,CAAR;AAFO,qBAArB;AAIA;AACJ,qBAAK,CAAL;AACA,qBAAK,CAAL;AACItH,sBAAEiG,OAAF,CAAU7C,IAAIkE,KAAd,EAAqB,UAAUjC,IAAV,EAAgB;AACjCqW,8BAAMzC,SAAN,CAAgBtb,IAAhB,CAAqB;AACjBse,wCAAY5W,KAAKmC,QADA;AAEjB0U,sCAAUL,QAAQxW,IAAR;AAFO,yBAArB;AAIH,qBALD;AAMA;AACJ;AAhBJ;AAkBA3C,cAAEM,IAAF,CAAO;AACHlG,qBAAK4b,UAAU,6BADZ;AAEHrb,sBAAM,MAFH;AAGH0G,6BAAa,kBAHV;AAIHN,sBAAM,yBAAeiY,KAAf,CAJH;AAKHrY,yBAAS,iBAAUuE,MAAV,EAAkB;AACvB,wBAAI,OAAQA,MAAR,KAAoB,QAAxB,EAAkC;AAC9BA,iCAAS8I,KAAKC,KAAL,CAAW/I,MAAX,CAAT;AACH;AACD,wBAAIA,OAAOuU,MAAP,IAAiB,CAArB,EAAwB;AACpBzf,6CAAWqE,KAAX,CAAiByC,EAAE,kBAAF,EAAsB,QAAtB,CAAjB;AACH,qBAFD,MAEO;AACH9G,6CAAWkE,MAAX,CAAkB4C,EAAE,qBAAF,EAAyB,SAAzB,IAAsCoE,OAAOwU,GAA/D;AACH;AACD9Y,4BAAQ2G,IAAR,CAAarC,MAAb;AACH,iBAfE;AAgBHrE,uBAAO,eAAUqE,MAAV,EAAkB;AACrBtE,4BAAQ2G,IAAR,CAAarC,MAAb;AACAlL,yCAAWkE,MAAX,CAAkB4C,EAAE,qBAAF,EAAyB,QAAzB,CAAlB;AACH;AAnBE,aAAP;AAqBH,SAvDD,EAuDG,UAAUJ,GAAV,EAAe;AACd1G,iCAAWkE,MAAX,CAAkB4C,EAAE,oBAAF,EAAwB,OAAxB,IAAmCJ,IAAIK,IAAJ,CAAS8I,IAA9D;AACH,SAzDD;AA0DH,KAhED;AAiEH,CAhOD;;kBAkOekM,K;;;;;;;;;;;;;;;;;;;;;;;;;;ACvOf,IAAIpU,MAAMH,mBAAOA,CAAC,EAAR,CAAV;AACA,IAAIuU,QAAQvU,mBAAOA,CAAC,GAAR,CAAZ;AACA,IAAImY,mBAAmBnY,mBAAOA,CAAC,GAAR,CAAvB;;AAEA,IAAIoY,YAAY;AACZpX,UAAM,cAASqX,OAAT,EAAiB;AACnBD,kBAAUjY,GAAV,GAAgB,IAAIA,IAAIF,OAAR,CAAgBoY,OAAhB,CAAhB;AACAD,kBAAU7D,KAAV,GAAkB,IAAIA,MAAMtU,OAAV,CAAkBoY,OAAlB,CAAlB;AACAD,kBAAUD,gBAAV,GAA6B,IAAIA,iBAAiBlY,OAArB,CAA6BoY,OAA7B,CAA7B;;AAEA,YAAIrc,OAAO2P,GAAX,EACA;AACIyM,sBAAUjY,GAAV,CAAcwL,GAAd,GAAoB3P,OAAO2P,GAA3B;AACH;AACJ;AAVW,CAAhB;;AAaA3P,OAAOoc,SAAP,GAAmBA,SAAnB,C;;;;;;;;;;;;ACjBA,IAAID,mBAAmB,SAAnBA,gBAAmB,CAAU/X,QAAV,EAAoB;AACvC,SAAKkY,4BAAL,GAAoC,UAAS5X,KAAT,EAAgB;AAChD5E,UAAEiG,OAAF,CAAUrB,MAAM0C,KAAhB,EAAuB,UAACjC,IAAD,EAAOoX,KAAP,EAAiB;AACpC,gBAAIA,UAAU,CAAV,KAAgB7X,MAAM6C,QAAN,CAAe/I,IAAf,IAAuB,IAAvB,IAA+BkG,MAAM6C,QAAN,CAAe/I,IAAf,KAAwB,EAAvE,CAAJ,EAAgF;AAC5EkG,sBAAM6C,QAAN,CAAe/I,IAAf,GAAsB2G,KAAKoC,QAAL,CAAc/I,IAApC;AACH;AACJ,SAJD;AAKH,KAND;AAOH,CARD;kBASe2d,gB", "file": "mam-upload-pure.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, {\n \t\t\t\tconfigurable: false,\n \t\t\t\tenumerable: true,\n \t\t\t\tget: getter\n \t\t\t});\n \t\t}\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/dist/\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 119);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap 6b1eb6df9256481b989b", "// https://github.com/zloirock/core-js/issues/86#issuecomment-115759028\nvar global = module.exports = typeof window != 'undefined' && window.Math == Math\n  ? window : typeof self != 'undefined' && self.Math == Math ? self\n  // eslint-disable-next-line no-new-func\n  : Function('return this')();\nif (typeof __g == 'number') __g = global; // eslint-disable-line no-undef\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_global.js\n// module id = 0\n// module chunks = 0 1", "var store = require('./_shared')('wks');\nvar uid = require('./_uid');\nvar Symbol = require('./_global').Symbol;\nvar USE_SYMBOL = typeof Symbol == 'function';\n\nvar $exports = module.exports = function (name) {\n  return store[name] || (store[name] =\n    USE_SYMBOL && Symbol[name] || (USE_SYMBOL ? Symbol : uid)('Symbol.' + name));\n};\n\n$exports.store = store;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_wks.js\n// module id = 1\n// module chunks = 0 1", "var core = module.exports = { version: '2.6.12' };\nif (typeof __e == 'number') __e = core; // eslint-disable-line no-undef\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_core.js\n// module id = 2\n// module chunks = 0 1", "var isObject = require('./_is-object');\nmodule.exports = function (it) {\n  if (!isObject(it)) throw TypeError(it + ' is not an object!');\n  return it;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_an-object.js\n// module id = 3\n// module chunks = 0 1", "var dP = require('./_object-dp');\nvar createDesc = require('./_property-desc');\nmodule.exports = require('./_descriptors') ? function (object, key, value) {\n  return dP.f(object, key, createDesc(1, value));\n} : function (object, key, value) {\n  object[key] = value;\n  return object;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_hide.js\n// module id = 4\n// module chunks = 0 1", "var anObject = require('./_an-object');\nvar IE8_DOM_DEFINE = require('./_ie8-dom-define');\nvar toPrimitive = require('./_to-primitive');\nvar dP = Object.defineProperty;\n\nexports.f = require('./_descriptors') ? Object.defineProperty : function defineProperty(O, P, Attributes) {\n  anObject(O);\n  P = toPrimitive(P, true);\n  anObject(Attributes);\n  if (IE8_DOM_DEFINE) try {\n    return dP(O, P, Attributes);\n  } catch (e) { /* empty */ }\n  if ('get' in Attributes || 'set' in Attributes) throw TypeError('Accessors not supported!');\n  if ('value' in Attributes) O[P] = Attributes.value;\n  return O;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-dp.js\n// module id = 5\n// module chunks = 0 1", "module.exports = function (it) {\n  return typeof it === 'object' ? it !== null : typeof it === 'function';\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_is-object.js\n// module id = 6\n// module chunks = 0 1", "// Thank's IE8 for his funny defineProperty\nmodule.exports = !require('./_fails')(function () {\n  return Object.defineProperty({}, 'a', { get: function () { return 7; } }).a != 7;\n});\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_descriptors.js\n// module id = 7\n// module chunks = 0 1", "var hasOwnProperty = {}.hasOwnProperty;\nmodule.exports = function (it, key) {\n  return hasOwnProperty.call(it, key);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_has.js\n// module id = 8\n// module chunks = 0 1", "// to indexed object, toObject with fallback for non-array-like ES3 strings\nvar IObject = require('./_iobject');\nvar defined = require('./_defined');\nmodule.exports = function (it) {\n  return IObject(defined(it));\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_to-iobject.js\n// module id = 9\n// module chunks = 0 1", "module.exports = true;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_library.js\n// module id = 10\n// module chunks = 0 1", "var global = require('./_global');\nvar core = require('./_core');\nvar ctx = require('./_ctx');\nvar hide = require('./_hide');\nvar has = require('./_has');\nvar PROTOTYPE = 'prototype';\n\nvar $export = function (type, name, source) {\n  var IS_FORCED = type & $export.F;\n  var IS_GLOBAL = type & $export.G;\n  var IS_STATIC = type & $export.S;\n  var IS_PROTO = type & $export.P;\n  var IS_BIND = type & $export.B;\n  var IS_WRAP = type & $export.W;\n  var exports = IS_GLOBAL ? core : core[name] || (core[name] = {});\n  var expProto = exports[PROTOTYPE];\n  var target = IS_GLOBAL ? global : IS_STATIC ? global[name] : (global[name] || {})[PROTOTYPE];\n  var key, own, out;\n  if (IS_GLOBAL) source = name;\n  for (key in source) {\n    // contains in native\n    own = !IS_FORCED && target && target[key] !== undefined;\n    if (own && has(exports, key)) continue;\n    // export native or passed\n    out = own ? target[key] : source[key];\n    // prevent global pollution for namespaces\n    exports[key] = IS_GLOBAL && typeof target[key] != 'function' ? source[key]\n    // bind timers to global for call from export context\n    : IS_BIND && own ? ctx(out, global)\n    // wrap global constructors for prevent change them in library\n    : IS_WRAP && target[key] == out ? (function (C) {\n      var F = function (a, b, c) {\n        if (this instanceof C) {\n          switch (arguments.length) {\n            case 0: return new C();\n            case 1: return new C(a);\n            case 2: return new C(a, b);\n          } return new C(a, b, c);\n        } return C.apply(this, arguments);\n      };\n      F[PROTOTYPE] = C[PROTOTYPE];\n      return F;\n    // make static versions for prototype methods\n    })(out) : IS_PROTO && typeof out == 'function' ? ctx(Function.call, out) : out;\n    // export proto methods to core.%CONSTRUCTOR%.methods.%NAME%\n    if (IS_PROTO) {\n      (exports.virtual || (exports.virtual = {}))[key] = out;\n      // export proto methods to core.%CONSTRUCTOR%.prototype.%NAME%\n      if (type & $export.R && expProto && !expProto[key]) hide(expProto, key, out);\n    }\n  }\n};\n// type bitmap\n$export.F = 1;   // forced\n$export.G = 2;   // global\n$export.S = 4;   // static\n$export.P = 8;   // proto\n$export.B = 16;  // bind\n$export.W = 32;  // wrap\n$export.U = 64;  // safe\n$export.R = 128; // real proto method for `library`\nmodule.exports = $export;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_export.js\n// module id = 11\n// module chunks = 0 1", "module.exports = {};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_iterators.js\n// module id = 12\n// module chunks = 0 1", "var toString = {}.toString;\n\nmodule.exports = function (it) {\n  return toString.call(it).slice(8, -1);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_cof.js\n// module id = 13\n// module chunks = 0 1", "// optional / simple context binding\nvar aFunction = require('./_a-function');\nmodule.exports = function (fn, that, length) {\n  aFunction(fn);\n  if (that === undefined) return fn;\n  switch (length) {\n    case 1: return function (a) {\n      return fn.call(that, a);\n    };\n    case 2: return function (a, b) {\n      return fn.call(that, a, b);\n    };\n    case 3: return function (a, b, c) {\n      return fn.call(that, a, b, c);\n    };\n  }\n  return function (/* ...args */) {\n    return fn.apply(that, arguments);\n  };\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_ctx.js\n// module id = 14\n// module chunks = 0 1", "module.exports = function (it) {\n  if (typeof it != 'function') throw TypeError(it + ' is not a function!');\n  return it;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_a-function.js\n// module id = 15\n// module chunks = 0 1", "module.exports = function (exec) {\n  try {\n    return !!exec();\n  } catch (e) {\n    return true;\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_fails.js\n// module id = 16\n// module chunks = 0 1", "module.exports = function (bitmap, value) {\n  return {\n    enumerable: !(bitmap & 1),\n    configurable: !(bitmap & 2),\n    writable: !(bitmap & 4),\n    value: value\n  };\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_property-desc.js\n// module id = 17\n// module chunks = 0 1", "var id = 0;\nvar px = Math.random();\nmodule.exports = function (key) {\n  return 'Symbol('.concat(key === undefined ? '' : key, ')_', (++id + px).toString(36));\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_uid.js\n// module id = 18\n// module chunks = 0 1", "var def = require('./_object-dp').f;\nvar has = require('./_has');\nvar TAG = require('./_wks')('toStringTag');\n\nmodule.exports = function (it, tag, stat) {\n  if (it && !has(it = stat ? it : it.prototype, TAG)) def(it, TAG, { configurable: true, value: tag });\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_set-to-string-tag.js\n// module id = 19\n// module chunks = 0 1", "var commonUtil = {\r\n    asyncLoadedScripts : {},\r\n    asyncLoadedScriptsCallbackQueue: {},\r\n    getScriptDomFromUrl : function(url){\r\n        var dom;\r\n        if (/.+\\.js$/.test(url))\r\n        {\r\n            dom = document.createElement(\"SCRIPT\");\r\n            dom.setAttribute(\"type\", \"text/javascript\");\r\n            dom.setAttribute(\"src\", url);\r\n        }\r\n        else if(/.+\\.css$/.test(url))\r\n        {\r\n            dom = document.createElement('link');\r\n            dom.href = url;\r\n            dom.type = \"text/css\";\r\n            dom.rel=\"stylesheet\";\r\n        }\r\n        return dom;\r\n    },\r\n    /**\r\n     * 异步加载script或css\r\n     */\r\n    asyncLoadScript: function(url, callback) {\r\n        var $this = commonUtil;\r\n        if ($this.asyncLoadedScripts[url] != undefined)//已加载script标签\r\n        {\r\n            if (callback && typeof(callback) == \"function\") {\r\n                if ($this.asyncLoadedScripts[url] == 0)//未执行首个script标签的回调\r\n                {\r\n                    if (!$this.asyncLoadedScriptsCallbackQueue[url]) {\r\n                        $this.asyncLoadedScriptsCallbackQueue[url] = [];\r\n                    }\r\n                    $this.asyncLoadedScriptsCallbackQueue[url].push(callback);\r\n                }\r\n                else {\r\n                    callback.apply($this, []);\r\n                }\r\n            }\r\n            return;\r\n        }\r\n        $this.asyncLoadedScripts[url] = 0;\r\n        var scriptDom = $this.getScriptDomFromUrl(url);\r\n        if (scriptDom.readyState) {\r\n            scriptDom.onreadystatechange = function () {\r\n                if (scriptDom.readyState == \"loaded\" || scriptDom.readyState == \"complete\") {\r\n                    scriptDom.onreadystatechange = null;\r\n                    $this.asyncLoadedScripts[url] = 1;\r\n                    if (callback && typeof(callback) == \"function\") {\r\n                        callback.apply($this, []);\r\n                    }\r\n                    if ($this.asyncLoadedScriptsCallbackQueue[url]) {\r\n                        for (var i = 0, j = $this.asyncLoadedScriptsCallbackQueue[url].length; i < j; i++) {\r\n                            $this.asyncLoadedScriptsCallbackQueue[url][i].apply($this, []);\r\n                        }\r\n                        $this.asyncLoadedScriptsCallbackQueue[url] = undefined;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            scriptDom.onload = function () {\r\n                $this.asyncLoadedScripts[url] = 1;\r\n                if (callback && typeof(callback) == \"function\") {\r\n                    callback.apply($this, []);\r\n                }\r\n                if ($this.asyncLoadedScriptsCallbackQueue[url]) {\r\n                    for (var i = 0, j = $this.asyncLoadedScriptsCallbackQueue[url].length; i < j; i++) {\r\n                        $this.asyncLoadedScriptsCallbackQueue[url][i].apply($this, []);\r\n                    }\r\n                    $this.asyncLoadedScriptsCallbackQueue[url] = undefined;\r\n                }\r\n            }\r\n        }\r\n        document.getElementsByTagName('head')[0].appendChild(scriptDom);\r\n    },\r\n    getFileNameFromUrl : function(url){\r\n        return url.substring(url.lastIndexOf(\"/\") + 1, url.length);\r\n    },\r\n    isIncludeScript : function(name){\r\n        var js = /js$/i.test(name);\r\n        var es=document.getElementsByTagName(js?'script':'link');\r\n        for(var i=0;i<es.length;i++)\r\n            if(es[i][js?'src':'href'].indexOf(name)!=-1)return true;\r\n        return false;\r\n    },\r\n    loadScripts : function(scriptArr){\r\n        if (scriptArr instanceof Array)\r\n        {\r\n            var promises = [];\r\n            for (var i = 0; i < scriptArr.length; i++)\r\n            {\r\n                promises.push(new Promise(function(resolve, reject){\r\n                    if (commonUtil.isIncludeScript(commonUtil.getFileNameFromUrl(scriptArr[i])))\r\n                    {\r\n                        resolve();\r\n                    }\r\n                    else\r\n                    {\r\n                        commonUtil.asyncLoadScript(scriptArr[i], function(){\r\n                            resolve();\r\n                        });\r\n                    }\r\n                }));\r\n            }\r\n            return Promise.all(promises);\r\n        }\r\n        else\r\n        {\r\n            return new Promise(function(resolve, reject){\r\n                resolve();\r\n            });\r\n        }\r\n    },\r\n    getExtensions: function (s) {\r\n        if (!s) {\r\n            return '';\r\n        }\r\n        for (var i = s.length; i != 0; i--) {\r\n            if (s[i] == '.') {\r\n                return s.substring(i, s.length);\r\n            }\r\n        }\r\n        return '';\r\n    },\r\n    getExtension: function (s) {\r\n        var e = commonUtil.getExtensions(s);\r\n        if (e.length > 0) {\r\n            return e.substring(1, e.length).toLowerCase();\r\n        }\r\n        return '';\r\n    },\r\n    getFileName: function (s) {\r\n        var e = s.length;\r\n        for (var i = s.length; i != 0; i--) {\r\n            if (s[i] == '.' && e == s.length) {\r\n                e = i;\r\n                continue;\r\n            }\r\n            if (s[i] == '\\\\') { // 此处需要考虑路径的多种分隔符，暂时就写一种\r\n                return s.substring(i + 1, e);\r\n            }\r\n        }\r\n        return s.substring(0, e);\r\n    },\r\n    getFullFileName: function (s) {\r\n        for (var i = s.length; i != 0; i--) {\r\n            if (s[i] == '\\\\') { // 此处需要考虑路径的多种分隔符，暂时就写一种\r\n                return s.substring(i + 1, s.length);\r\n            }\r\n        }\r\n        return s;\r\n    },\r\n    getTypeByExt: function (ext, config) {\r\n        ext = ext.toLowerCase();\r\n        if (ext.indexOf(\".\") !== 0)\r\n        {\r\n            ext = \".\" + ext;\r\n        }\r\n        var types = _.get(window, 'nxt.config.entityTypes', []);\r\n        if (config && types.length === 0)\r\n        {\r\n            types = _.get(config, 'entityTypes', []);\r\n        }\r\n        for (var i = 0; i < types.length; i++) {\r\n            if (types[i].extensions && types[i].extensions.indexOf(ext) != -1) {\r\n                return types[i];\r\n            }\r\n        }\r\n        return _.find(types, { 'code': 'other' });\r\n    },\r\n    formatSize: function (size, pointLength, units) {\r\n        var unit;\r\n        units = units || ['B', 'KB', 'MB', 'GB', 'TB'];\r\n        while ((unit = units.shift()) && size > 1024) {\r\n            size = size / 1024;\r\n        }\r\n        return (unit === 'B' ? size : size.toFixed(pointLength || 2)) + ' ' + unit;\r\n    },\r\n    prompt: function(msg){\r\n        if (mam && mam.prompt)\r\n        {\r\n            mam.prompt(msg);\r\n        }\r\n    },\r\n    msgOk: function(msg){\r\n        if (mam && mam.message && mam.message.ok)\r\n        {\r\n            mam.message.ok(msg);\r\n        }\r\n    },\r\n    addUrlParam: function(url, param) {\r\n        url += (url.indexOf('?') !== -1 ? '&' : '?') + param;\r\n        return url;\r\n    },\r\n    getCookie: function(cname){\r\n        var name = cname + \"=\";\r\n        var ca = document.cookie.split(';');\r\n        for(var i=0; i<ca.length; i++) \r\n        {\r\n            var c = ca[i].trim();\r\n            if (c.indexOf(name)==0) return c.substring(name.length,c.length);\r\n        }\r\n        return \"\";\r\n    }\r\n};\r\nexport default commonUtil;\r\n\n\n\n// WEBPACK FOOTER //\n// ./src/common/commonUtil.js", "var globalUtil = {};\r\nexport default globalUtil;\n\n\n// WEBPACK FOOTER //\n// ./src/common/globalUtil.js", "module.exports = { \"default\": require(\"core-js/library/fn/json/stringify\"), __esModule: true };\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/core-js/json/stringify.js\n// module id = 22\n// module chunks = 0 1", "// 7.1.4 ToInteger\nvar ceil = Math.ceil;\nvar floor = Math.floor;\nmodule.exports = function (it) {\n  return isNaN(it = +it) ? 0 : (it > 0 ? floor : ceil)(it);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_to-integer.js\n// module id = 23\n// module chunks = 0 1", "// 7.2.1 RequireObjectCoercible(argument)\nmodule.exports = function (it) {\n  if (it == undefined) throw TypeError(\"Can't call method on  \" + it);\n  return it;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_defined.js\n// module id = 24\n// module chunks = 0 1", "var isObject = require('./_is-object');\nvar document = require('./_global').document;\n// typeof document.createElement is 'object' in old IE\nvar is = isObject(document) && isObject(document.createElement);\nmodule.exports = function (it) {\n  return is ? document.createElement(it) : {};\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_dom-create.js\n// module id = 25\n// module chunks = 0 1", "// 7.1.1 ToPrimitive(input [, PreferredType])\nvar isObject = require('./_is-object');\n// instead of the ES6 spec version, we didn't implement @@toPrimitive case\n// and the second argument - flag - preferred type is a string\nmodule.exports = function (it, S) {\n  if (!isObject(it)) return it;\n  var fn, val;\n  if (S && typeof (fn = it.toString) == 'function' && !isObject(val = fn.call(it))) return val;\n  if (typeof (fn = it.valueOf) == 'function' && !isObject(val = fn.call(it))) return val;\n  if (!S && typeof (fn = it.toString) == 'function' && !isObject(val = fn.call(it))) return val;\n  throw TypeError(\"Can't convert object to primitive value\");\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_to-primitive.js\n// module id = 26\n// module chunks = 0 1", "// 19.1.2.14 / 15.2.3.14 Object.keys(O)\nvar $keys = require('./_object-keys-internal');\nvar enumBugKeys = require('./_enum-bug-keys');\n\nmodule.exports = Object.keys || function keys(O) {\n  return $keys(O, enumBugKeys);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-keys.js\n// module id = 27\n// module chunks = 0 1", "var shared = require('./_shared')('keys');\nvar uid = require('./_uid');\nmodule.exports = function (key) {\n  return shared[key] || (shared[key] = uid(key));\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_shared-key.js\n// module id = 28\n// module chunks = 0 1", "var core = require('./_core');\nvar global = require('./_global');\nvar SHARED = '__core-js_shared__';\nvar store = global[SHARED] || (global[SHARED] = {});\n\n(module.exports = function (key, value) {\n  return store[key] || (store[key] = value !== undefined ? value : {});\n})('versions', []).push({\n  version: core.version,\n  mode: require('./_library') ? 'pure' : 'global',\n  copyright: '© 2020 <PERSON> (zloirock.ru)'\n});\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_shared.js\n// module id = 29\n// module chunks = 0 1", "// IE 8- don't enum bug keys\nmodule.exports = (\n  'constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf'\n).split(',');\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_enum-bug-keys.js\n// module id = 30\n// module chunks = 0 1", "'use strict';\n// 25.4.1.5 NewPromiseCapability(C)\nvar aFunction = require('./_a-function');\n\nfunction PromiseCapability(C) {\n  var resolve, reject;\n  this.promise = new C(function ($$resolve, $$reject) {\n    if (resolve !== undefined || reject !== undefined) throw TypeError('Bad Promise constructor');\n    resolve = $$resolve;\n    reject = $$reject;\n  });\n  this.resolve = aFunction(resolve);\n  this.reject = aFunction(reject);\n}\n\nmodule.exports.f = function (C) {\n  return new PromiseCapability(C);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_new-promise-capability.js\n// module id = 31\n// module chunks = 0 1", "exports.f = require('./_wks');\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_wks-ext.js\n// module id = 32\n// module chunks = 0 1", "var global = require('./_global');\nvar core = require('./_core');\nvar LIBRARY = require('./_library');\nvar wksExt = require('./_wks-ext');\nvar defineProperty = require('./_object-dp').f;\nmodule.exports = function (name) {\n  var $Symbol = core.Symbol || (core.Symbol = LIBRARY ? {} : global.Symbol || {});\n  if (name.charAt(0) != '_' && !(name in $Symbol)) defineProperty($Symbol, name, { value: wksExt.f(name) });\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_wks-define.js\n// module id = 33\n// module chunks = 0 1", "exports.f = {}.propertyIsEnumerable;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-pie.js\n// module id = 34\n// module chunks = 0 1", "/**\r\n * Created by heju on 2017/5/31.\r\n */\r\nimport commonUtil from \"@/common/commonUtil\";\r\n\r\nvar getQueryParam = function (url, key) {\r\n    key = key.toLowerCase().replace(/[\\[\\]]/g, '\\\\$&');\r\n    var regex = new RegExp('[?&]' + key + '(=([^&#]*)|&|#|$)'),\r\n        results = regex.exec(url.toLowerCase());\r\n    if (!results) return null;\r\n    if (!results[2]) return '';\r\n    return decodeURIComponent(results[2].replace(/\\+/g, ' '));\r\n}\r\nvar getUrlQueryParam = function (key) {\r\n    return getQueryParam(window.location.href, key);\r\n}\r\nvar addUrlParam = function(url, param) {\r\n    url += (url.indexOf('?') !== -1 ? '&' : '?') + param;\r\n    return url;\r\n}\r\n\r\nvar httpUtil = {\r\n    getGetParamStr : function(param, restful){\r\n        var str = \"\";\r\n        if (param != undefined && !$.isEmptyObject(param))\r\n        {\r\n            for (var key in param)\r\n            {\r\n                if (!restful)\r\n                {\r\n                    str += key + \"=\" + param[key] + \"&\";\r\n                }\r\n                else\r\n                {\r\n                    str += \"/\" + param[key];\r\n                }\r\n            }\r\n            if (!restful)\r\n            {\r\n                str = \"?\" + str.substring(0, str.length - 1);\r\n            }\r\n        }\r\n        return str;\r\n    },\r\n    get : function(url, param){\r\n        var defer = $.Deferred();\r\n        var purl = url;\r\n        if (!param)\r\n        {\r\n            param = {};\r\n        }\r\n        purl = url + httpUtil.getGetParamStr(param);\r\n        var token = getUrlQueryParam('token');\r\n        if (token){\r\n            purl = addUrlParam(purl, 'token=' + token);\r\n        }\r\n        $.ajax({\r\n            type : \"get\",\r\n            xhrFields: {\r\n                withCredentials: url.indexOf('token=') > -1 ? false : true//如果传了token，就不用传cookie了\r\n            },\r\n            url : purl\r\n        }).then(function(res){\r\n            if (!res.success)\r\n            {\r\n                console.error('response', res);\r\n                commonUtil.prompt(l('system.' + res.data.code, res.data.title));\r\n                defer.reject(res);\r\n            }\r\n            else\r\n            {\r\n                defer.resolve(res);\r\n            }\r\n        }, function(res){\r\n            defer.reject(res);\r\n        });\r\n        return defer;\r\n    },\r\n    post : function(url, param, opts){\r\n        var defer = $.Deferred();\r\n        if (!param)\r\n        {\r\n            param = {};\r\n        }\r\n        var token = getUrlQueryParam('token');\r\n        if (token){\r\n            url = addUrlParam(url, 'token=' + token);\r\n        }\r\n        var postConfig = {\r\n            type : \"post\",\r\n            data :  param,\r\n            contentType: 'application/json',\r\n            processData: false,\r\n            xhrFields: {\r\n                withCredentials: url.indexOf('token=') > -1 ? false : true//如果传了token，就不用传cookie了\r\n            },\r\n            url : url\r\n        };\r\n        if (opts && opts.contentType !== undefined)\r\n        {\r\n            postConfig.contentType = opts.contentType;\r\n        }\r\n        if (opts && opts.processData !== undefined)\r\n        {\r\n            postConfig.processData = opts.processData;\r\n        }\r\n        if (postConfig.contentType === 'application/json')\r\n        {\r\n            postConfig.data = JSON.stringify(param);\r\n        }\r\n        $.ajax(postConfig).then(function(res){\r\n            if (!res.success)\r\n            {\r\n                console.error('response', res);\r\n                commonUtil.prompt(l('system.' + res.error.code, res.error.title));\r\n                defer.reject(res);\r\n            }\r\n            else\r\n            {\r\n                defer.resolve(res);\r\n            }\r\n        }, function(res){\r\n            defer.reject(res);\r\n        });\r\n        return defer;\r\n    },\r\n    delete : function(url, param, opts){\r\n        var defer = $.Deferred();\r\n        if (!param)\r\n        {\r\n            param = {};\r\n        }\r\n        var token = getUrlQueryParam('token');\r\n        if (token){\r\n            url = addUrlParam(url, 'token=' + token);\r\n        }\r\n        var postConfig = {\r\n            type : \"delete\",\r\n            data :  param,\r\n            contentType: 'application/json',\r\n            processData: false,\r\n            xhrFields: {\r\n                withCredentials: url.indexOf('token=') > -1 ? false : true//如果传了token，就不用传cookie了\r\n            },\r\n            url : url\r\n        };\r\n        if (opts && opts.contentType !== undefined)\r\n        {\r\n            postConfig.contentType = opts.contentType;\r\n        }\r\n        if (opts && opts.processData !== undefined)\r\n        {\r\n            postConfig.processData = opts.processData;\r\n        }\r\n        if (postConfig.contentType === 'application/json')\r\n        {\r\n            postConfig.data = JSON.stringify(param);\r\n        }\r\n        $.ajax(postConfig).then(function(res){\r\n            if (!res.success)\r\n            {\r\n                console.error('response', res);\r\n                commonUtil.prompt(l('system.' + res.error.code, res.error.title));\r\n                defer.reject(res);\r\n            }\r\n            else\r\n            {\r\n                defer.resolve(res);\r\n            }\r\n        }, function(res){\r\n            defer.reject(res);\r\n        });\r\n        return defer;\r\n    }\r\n};\r\nexport default httpUtil;\n\n\n// WEBPACK FOOTER //\n// ./src/common/httpUtil.js", "'use strict';\nvar $at = require('./_string-at')(true);\n\n// 21.1.3.27 String.prototype[@@iterator]()\nrequire('./_iter-define')(String, 'String', function (iterated) {\n  this._t = String(iterated); // target\n  this._i = 0;                // next index\n// 21.1.5.2.1 %StringIteratorPrototype%.next()\n}, function () {\n  var O = this._t;\n  var index = this._i;\n  var point;\n  if (index >= O.length) return { value: undefined, done: true };\n  point = $at(O, index);\n  this._i += point.length;\n  return { value: point, done: false };\n});\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/es6.string.iterator.js\n// module id = 37\n// module chunks = 0 1", "'use strict';\nvar LIBRARY = require('./_library');\nvar $export = require('./_export');\nvar redefine = require('./_redefine');\nvar hide = require('./_hide');\nvar Iterators = require('./_iterators');\nvar $iterCreate = require('./_iter-create');\nvar setToStringTag = require('./_set-to-string-tag');\nvar getPrototypeOf = require('./_object-gpo');\nvar ITERATOR = require('./_wks')('iterator');\nvar BUGGY = !([].keys && 'next' in [].keys()); // <PERSON>fari has buggy iterators w/o `next`\nvar FF_ITERATOR = '@@iterator';\nvar KEYS = 'keys';\nvar VALUES = 'values';\n\nvar returnThis = function () { return this; };\n\nmodule.exports = function (Base, NAME, Constructor, next, DEFAULT, IS_SET, FORCED) {\n  $iterCreate(Constructor, NAME, next);\n  var getMethod = function (kind) {\n    if (!BUGGY && kind in proto) return proto[kind];\n    switch (kind) {\n      case KEYS: return function keys() { return new Constructor(this, kind); };\n      case VALUES: return function values() { return new Constructor(this, kind); };\n    } return function entries() { return new Constructor(this, kind); };\n  };\n  var TAG = NAME + ' Iterator';\n  var DEF_VALUES = DEFAULT == VALUES;\n  var VALUES_BUG = false;\n  var proto = Base.prototype;\n  var $native = proto[ITERATOR] || proto[FF_ITERATOR] || DEFAULT && proto[DEFAULT];\n  var $default = $native || getMethod(DEFAULT);\n  var $entries = DEFAULT ? !DEF_VALUES ? $default : getMethod('entries') : undefined;\n  var $anyNative = NAME == 'Array' ? proto.entries || $native : $native;\n  var methods, key, IteratorPrototype;\n  // Fix native\n  if ($anyNative) {\n    IteratorPrototype = getPrototypeOf($anyNative.call(new Base()));\n    if (IteratorPrototype !== Object.prototype && IteratorPrototype.next) {\n      // Set @@toStringTag to native iterators\n      setToStringTag(IteratorPrototype, TAG, true);\n      // fix for some old engines\n      if (!LIBRARY && typeof IteratorPrototype[ITERATOR] != 'function') hide(IteratorPrototype, ITERATOR, returnThis);\n    }\n  }\n  // fix Array#{values, @@iterator}.name in V8 / FF\n  if (DEF_VALUES && $native && $native.name !== VALUES) {\n    VALUES_BUG = true;\n    $default = function values() { return $native.call(this); };\n  }\n  // Define iterator\n  if ((!LIBRARY || FORCED) && (BUGGY || VALUES_BUG || !proto[ITERATOR])) {\n    hide(proto, ITERATOR, $default);\n  }\n  // Plug for library\n  Iterators[NAME] = $default;\n  Iterators[TAG] = returnThis;\n  if (DEFAULT) {\n    methods = {\n      values: DEF_VALUES ? $default : getMethod(VALUES),\n      keys: IS_SET ? $default : getMethod(KEYS),\n      entries: $entries\n    };\n    if (FORCED) for (key in methods) {\n      if (!(key in proto)) redefine(proto, key, methods[key]);\n    } else $export($export.P + $export.F * (BUGGY || VALUES_BUG), NAME, methods);\n  }\n  return methods;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_iter-define.js\n// module id = 38\n// module chunks = 0 1", "module.exports = !require('./_descriptors') && !require('./_fails')(function () {\n  return Object.defineProperty(require('./_dom-create')('div'), 'a', { get: function () { return 7; } }).a != 7;\n});\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_ie8-dom-define.js\n// module id = 39\n// module chunks = 0 1", "module.exports = require('./_hide');\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_redefine.js\n// module id = 40\n// module chunks = 0 1", "// ******** / ******** Object.create(O [, Properties])\nvar anObject = require('./_an-object');\nvar dPs = require('./_object-dps');\nvar enumBugKeys = require('./_enum-bug-keys');\nvar IE_PROTO = require('./_shared-key')('IE_PROTO');\nvar Empty = function () { /* empty */ };\nvar PROTOTYPE = 'prototype';\n\n// Create object with fake `null` prototype: use iframe Object with cleared prototype\nvar createDict = function () {\n  // Thrash, waste and sodomy: IE GC bug\n  var iframe = require('./_dom-create')('iframe');\n  var i = enumBugKeys.length;\n  var lt = '<';\n  var gt = '>';\n  var iframeDocument;\n  iframe.style.display = 'none';\n  require('./_html').appendChild(iframe);\n  iframe.src = 'javascript:'; // eslint-disable-line no-script-url\n  // createDict = iframe.contentWindow.Object;\n  // html.removeChild(iframe);\n  iframeDocument = iframe.contentWindow.document;\n  iframeDocument.open();\n  iframeDocument.write(lt + 'script' + gt + 'document.F=Object' + lt + '/script' + gt);\n  iframeDocument.close();\n  createDict = iframeDocument.F;\n  while (i--) delete createDict[PROTOTYPE][enumBugKeys[i]];\n  return createDict();\n};\n\nmodule.exports = Object.create || function create(O, Properties) {\n  var result;\n  if (O !== null) {\n    Empty[PROTOTYPE] = anObject(O);\n    result = new Empty();\n    Empty[PROTOTYPE] = null;\n    // add \"__proto__\" for Object.getPrototypeOf polyfill\n    result[IE_PROTO] = O;\n  } else result = createDict();\n  return Properties === undefined ? result : dPs(result, Properties);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-create.js\n// module id = 41\n// module chunks = 0 1", "var has = require('./_has');\nvar toIObject = require('./_to-iobject');\nvar arrayIndexOf = require('./_array-includes')(false);\nvar IE_PROTO = require('./_shared-key')('IE_PROTO');\n\nmodule.exports = function (object, names) {\n  var O = toIObject(object);\n  var i = 0;\n  var result = [];\n  var key;\n  for (key in O) if (key != IE_PROTO) has(O, key) && result.push(key);\n  // Don't enum bug & hidden keys\n  while (names.length > i) if (has(O, key = names[i++])) {\n    ~arrayIndexOf(result, key) || result.push(key);\n  }\n  return result;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-keys-internal.js\n// module id = 42\n// module chunks = 0 1", "// 7.1.15 ToLength\nvar toInteger = require('./_to-integer');\nvar min = Math.min;\nmodule.exports = function (it) {\n  return it > 0 ? min(toInteger(it), 0x1fffffffffffff) : 0; // pow(2, 53) - 1 == 9007199254740991\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_to-length.js\n// module id = 43\n// module chunks = 0 1", "var document = require('./_global').document;\nmodule.exports = document && document.documentElement;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_html.js\n// module id = 44\n// module chunks = 0 1", "// 7.1.13 ToObject(argument)\nvar defined = require('./_defined');\nmodule.exports = function (it) {\n  return Object(defined(it));\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_to-object.js\n// module id = 45\n// module chunks = 0 1", "require('./es6.array.iterator');\nvar global = require('./_global');\nvar hide = require('./_hide');\nvar Iterators = require('./_iterators');\nvar TO_STRING_TAG = require('./_wks')('toStringTag');\n\nvar DOMIterables = ('CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,' +\n  'DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,' +\n  'MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,' +\n  'SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,' +\n  'TextTrackList,TouchList').split(',');\n\nfor (var i = 0; i < DOMIterables.length; i++) {\n  var NAME = DOMIterables[i];\n  var Collection = global[NAME];\n  var proto = Collection && Collection.prototype;\n  if (proto && !proto[TO_STRING_TAG]) hide(proto, TO_STRING_TAG, NAME);\n  Iterators[NAME] = Iterators.Array;\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/web.dom.iterable.js\n// module id = 46\n// module chunks = 0 1", "// getting tag from ******** Object.prototype.toString()\nvar cof = require('./_cof');\nvar TAG = require('./_wks')('toStringTag');\n// ES3 wrong here\nvar ARG = cof(function () { return arguments; }()) == 'Arguments';\n\n// fallback for IE11 Script Access Denied error\nvar tryGet = function (it, key) {\n  try {\n    return it[key];\n  } catch (e) { /* empty */ }\n};\n\nmodule.exports = function (it) {\n  var O, T, B;\n  return it === undefined ? 'Undefined' : it === null ? 'Null'\n    // @@toStringTag case\n    : typeof (T = tryGet(O = Object(it), TAG)) == 'string' ? T\n    // builtinTag case\n    : ARG ? cof(O)\n    // ES3 arguments fallback\n    : (B = cof(O)) == 'Object' && typeof O.callee == 'function' ? 'Arguments' : B;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_classof.js\n// module id = 47\n// module chunks = 0 1", "// 7.3.20 SpeciesConstructor(O, defaultConstructor)\nvar anObject = require('./_an-object');\nvar aFunction = require('./_a-function');\nvar SPECIES = require('./_wks')('species');\nmodule.exports = function (O, D) {\n  var C = anObject(O).constructor;\n  var S;\n  return C === undefined || (S = anObject(C)[SPECIES]) == undefined ? D : aFunction(S);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_species-constructor.js\n// module id = 48\n// module chunks = 0 1", "var ctx = require('./_ctx');\nvar invoke = require('./_invoke');\nvar html = require('./_html');\nvar cel = require('./_dom-create');\nvar global = require('./_global');\nvar process = global.process;\nvar setTask = global.setImmediate;\nvar clearTask = global.clearImmediate;\nvar MessageChannel = global.MessageChannel;\nvar Dispatch = global.Dispatch;\nvar counter = 0;\nvar queue = {};\nvar ONREADYSTATECHANGE = 'onreadystatechange';\nvar defer, channel, port;\nvar run = function () {\n  var id = +this;\n  // eslint-disable-next-line no-prototype-builtins\n  if (queue.hasOwnProperty(id)) {\n    var fn = queue[id];\n    delete queue[id];\n    fn();\n  }\n};\nvar listener = function (event) {\n  run.call(event.data);\n};\n// Node.js 0.9+ & IE10+ has setImmediate, otherwise:\nif (!setTask || !clearTask) {\n  setTask = function setImmediate(fn) {\n    var args = [];\n    var i = 1;\n    while (arguments.length > i) args.push(arguments[i++]);\n    queue[++counter] = function () {\n      // eslint-disable-next-line no-new-func\n      invoke(typeof fn == 'function' ? fn : Function(fn), args);\n    };\n    defer(counter);\n    return counter;\n  };\n  clearTask = function clearImmediate(id) {\n    delete queue[id];\n  };\n  // Node.js 0.8-\n  if (require('./_cof')(process) == 'process') {\n    defer = function (id) {\n      process.nextTick(ctx(run, id, 1));\n    };\n  // Sphere (JS game engine) Dispatch API\n  } else if (Dispatch && Dispatch.now) {\n    defer = function (id) {\n      Dispatch.now(ctx(run, id, 1));\n    };\n  // Browsers with MessageChannel, includes WebWorkers\n  } else if (MessageChannel) {\n    channel = new MessageChannel();\n    port = channel.port2;\n    channel.port1.onmessage = listener;\n    defer = ctx(port.postMessage, port, 1);\n  // Browsers with postMessage, skip WebWorkers\n  // IE8 has postMessage, but it's sync & typeof its postMessage is 'object'\n  } else if (global.addEventListener && typeof postMessage == 'function' && !global.importScripts) {\n    defer = function (id) {\n      global.postMessage(id + '', '*');\n    };\n    global.addEventListener('message', listener, false);\n  // IE8-\n  } else if (ONREADYSTATECHANGE in cel('script')) {\n    defer = function (id) {\n      html.appendChild(cel('script'))[ONREADYSTATECHANGE] = function () {\n        html.removeChild(this);\n        run.call(id);\n      };\n    };\n  // Rest old browsers\n  } else {\n    defer = function (id) {\n      setTimeout(ctx(run, id, 1), 0);\n    };\n  }\n}\nmodule.exports = {\n  set: setTask,\n  clear: clearTask\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_task.js\n// module id = 49\n// module chunks = 0 1", "module.exports = function (exec) {\n  try {\n    return { e: false, v: exec() };\n  } catch (e) {\n    return { e: true, v: e };\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_perform.js\n// module id = 50\n// module chunks = 0 1", "var anObject = require('./_an-object');\nvar isObject = require('./_is-object');\nvar newPromiseCapability = require('./_new-promise-capability');\n\nmodule.exports = function (C, x) {\n  anObject(C);\n  if (isObject(x) && x.constructor === C) return x;\n  var promiseCapability = newPromiseCapability.f(C);\n  var resolve = promiseCapability.resolve;\n  resolve(x);\n  return promiseCapability.promise;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_promise-resolve.js\n// module id = 51\n// module chunks = 0 1", "exports.f = Object.getOwnPropertySymbols;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-gops.js\n// module id = 52\n// module chunks = 0 1", "// ******** / 15.2.3.4 Object.getOwnPropertyNames(O)\nvar $keys = require('./_object-keys-internal');\nvar hiddenKeys = require('./_enum-bug-keys').concat('length', 'prototype');\n\nexports.f = Object.getOwnPropertyNames || function getOwnPropertyNames(O) {\n  return $keys(O, hiddenKeys);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-gopn.js\n// module id = 53\n// module chunks = 0 1", "module.exports = { \"default\": require(\"core-js/library/fn/promise\"), __esModule: true };\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/core-js/promise.js\n// module id = 54\n// module chunks = 0 1", "require('../modules/es6.object.to-string');\nrequire('../modules/es6.string.iterator');\nrequire('../modules/web.dom.iterable');\nrequire('../modules/es6.promise');\nrequire('../modules/es7.promise.finally');\nrequire('../modules/es7.promise.try');\nmodule.exports = require('../modules/_core').Promise;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/fn/promise.js\n// module id = 55\n// module chunks = 0 1", "var toInteger = require('./_to-integer');\nvar defined = require('./_defined');\n// true  -> String#at\n// false -> String#codePointAt\nmodule.exports = function (TO_STRING) {\n  return function (that, pos) {\n    var s = String(defined(that));\n    var i = toInteger(pos);\n    var l = s.length;\n    var a, b;\n    if (i < 0 || i >= l) return TO_STRING ? '' : undefined;\n    a = s.charCodeAt(i);\n    return a < 0xd800 || a > 0xdbff || i + 1 === l || (b = s.charCodeAt(i + 1)) < 0xdc00 || b > 0xdfff\n      ? TO_STRING ? s.charAt(i) : a\n      : TO_STRING ? s.slice(i, i + 2) : (a - 0xd800 << 10) + (b - 0xdc00) + 0x10000;\n  };\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_string-at.js\n// module id = 56\n// module chunks = 0 1", "'use strict';\nvar create = require('./_object-create');\nvar descriptor = require('./_property-desc');\nvar setToStringTag = require('./_set-to-string-tag');\nvar IteratorPrototype = {};\n\n// ********.1 %IteratorPrototype%[@@iterator]()\nrequire('./_hide')(IteratorPrototype, require('./_wks')('iterator'), function () { return this; });\n\nmodule.exports = function (Constructor, NAME, next) {\n  Constructor.prototype = create(IteratorPrototype, { next: descriptor(1, next) });\n  setToStringTag(Constructor, NAME + ' Iterator');\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_iter-create.js\n// module id = 57\n// module chunks = 0 1", "var dP = require('./_object-dp');\nvar anObject = require('./_an-object');\nvar getKeys = require('./_object-keys');\n\nmodule.exports = require('./_descriptors') ? Object.defineProperties : function defineProperties(O, Properties) {\n  anObject(O);\n  var keys = getKeys(Properties);\n  var length = keys.length;\n  var i = 0;\n  var P;\n  while (length > i) dP.f(O, P = keys[i++], Properties[P]);\n  return O;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-dps.js\n// module id = 58\n// module chunks = 0 1", "// fallback for non-array-like ES3 and non-enumerable old V8 strings\nvar cof = require('./_cof');\n// eslint-disable-next-line no-prototype-builtins\nmodule.exports = Object('z').propertyIsEnumerable(0) ? Object : function (it) {\n  return cof(it) == 'String' ? it.split('') : Object(it);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_iobject.js\n// module id = 59\n// module chunks = 0 1", "// false -> Array#indexOf\n// true  -> Array#includes\nvar toIObject = require('./_to-iobject');\nvar toLength = require('./_to-length');\nvar toAbsoluteIndex = require('./_to-absolute-index');\nmodule.exports = function (IS_INCLUDES) {\n  return function ($this, el, fromIndex) {\n    var O = toIObject($this);\n    var length = toLength(O.length);\n    var index = toAbsoluteIndex(fromIndex, length);\n    var value;\n    // Array#includes uses SameValueZero equality algorithm\n    // eslint-disable-next-line no-self-compare\n    if (IS_INCLUDES && el != el) while (length > index) {\n      value = O[index++];\n      // eslint-disable-next-line no-self-compare\n      if (value != value) return true;\n    // Array#indexOf ignores holes, Array#includes - not\n    } else for (;length > index; index++) if (IS_INCLUDES || index in O) {\n      if (O[index] === el) return IS_INCLUDES || index || 0;\n    } return !IS_INCLUDES && -1;\n  };\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_array-includes.js\n// module id = 60\n// module chunks = 0 1", "var toInteger = require('./_to-integer');\nvar max = Math.max;\nvar min = Math.min;\nmodule.exports = function (index, length) {\n  index = toInteger(index);\n  return index < 0 ? max(index + length, 0) : min(index, length);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_to-absolute-index.js\n// module id = 61\n// module chunks = 0 1", "// 19.1.2.9 / 15.2.3.2 Object.getPrototypeOf(O)\nvar has = require('./_has');\nvar toObject = require('./_to-object');\nvar IE_PROTO = require('./_shared-key')('IE_PROTO');\nvar ObjectProto = Object.prototype;\n\nmodule.exports = Object.getPrototypeOf || function (O) {\n  O = toObject(O);\n  if (has(O, IE_PROTO)) return O[IE_PROTO];\n  if (typeof O.constructor == 'function' && O instanceof O.constructor) {\n    return O.constructor.prototype;\n  } return O instanceof Object ? ObjectProto : null;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-gpo.js\n// module id = 62\n// module chunks = 0 1", "'use strict';\nvar addToUnscopables = require('./_add-to-unscopables');\nvar step = require('./_iter-step');\nvar Iterators = require('./_iterators');\nvar toIObject = require('./_to-iobject');\n\n// 22.1.3.4 Array.prototype.entries()\n// 22.1.3.13 Array.prototype.keys()\n// 22.1.3.29 Array.prototype.values()\n// 22.1.3.30 Array.prototype[@@iterator]()\nmodule.exports = require('./_iter-define')(Array, 'Array', function (iterated, kind) {\n  this._t = toIObject(iterated); // target\n  this._i = 0;                   // next index\n  this._k = kind;                // kind\n// 22.1.5.2.1 %ArrayIteratorPrototype%.next()\n}, function () {\n  var O = this._t;\n  var kind = this._k;\n  var index = this._i++;\n  if (!O || index >= O.length) {\n    this._t = undefined;\n    return step(1);\n  }\n  if (kind == 'keys') return step(0, index);\n  if (kind == 'values') return step(0, O[index]);\n  return step(0, [index, O[index]]);\n}, 'values');\n\n// argumentsList[@@iterator] is %ArrayProto_values% (9.4.4.6, 9.4.4.7)\nIterators.Arguments = Iterators.Array;\n\naddToUnscopables('keys');\naddToUnscopables('values');\naddToUnscopables('entries');\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/es6.array.iterator.js\n// module id = 63\n// module chunks = 0 1", "module.exports = function () { /* empty */ };\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_add-to-unscopables.js\n// module id = 64\n// module chunks = 0 1", "module.exports = function (done, value) {\n  return { value: value, done: !!done };\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_iter-step.js\n// module id = 65\n// module chunks = 0 1", "'use strict';\nvar LIBRARY = require('./_library');\nvar global = require('./_global');\nvar ctx = require('./_ctx');\nvar classof = require('./_classof');\nvar $export = require('./_export');\nvar isObject = require('./_is-object');\nvar aFunction = require('./_a-function');\nvar anInstance = require('./_an-instance');\nvar forOf = require('./_for-of');\nvar speciesConstructor = require('./_species-constructor');\nvar task = require('./_task').set;\nvar microtask = require('./_microtask')();\nvar newPromiseCapabilityModule = require('./_new-promise-capability');\nvar perform = require('./_perform');\nvar userAgent = require('./_user-agent');\nvar promiseResolve = require('./_promise-resolve');\nvar PROMISE = 'Promise';\nvar TypeError = global.TypeError;\nvar process = global.process;\nvar versions = process && process.versions;\nvar v8 = versions && versions.v8 || '';\nvar $Promise = global[PROMISE];\nvar isNode = classof(process) == 'process';\nvar empty = function () { /* empty */ };\nvar Internal, newGenericPromiseCapability, OwnPromiseCapability, Wrapper;\nvar newPromiseCapability = newGenericPromiseCapability = newPromiseCapabilityModule.f;\n\nvar USE_NATIVE = !!function () {\n  try {\n    // correct subclassing with @@species support\n    var promise = $Promise.resolve(1);\n    var FakePromise = (promise.constructor = {})[require('./_wks')('species')] = function (exec) {\n      exec(empty, empty);\n    };\n    // unhandled rejections tracking support, NodeJS Promise without it fails @@species test\n    return (isNode || typeof PromiseRejectionEvent == 'function')\n      && promise.then(empty) instanceof FakePromise\n      // v8 6.6 (Node 10 and Chrome 66) have a bug with resolving custom thenables\n      // https://bugs.chromium.org/p/chromium/issues/detail?id=830565\n      // we can't detect it synchronously, so just check versions\n      && v8.indexOf('6.6') !== 0\n      && userAgent.indexOf('Chrome/66') === -1;\n  } catch (e) { /* empty */ }\n}();\n\n// helpers\nvar isThenable = function (it) {\n  var then;\n  return isObject(it) && typeof (then = it.then) == 'function' ? then : false;\n};\nvar notify = function (promise, isReject) {\n  if (promise._n) return;\n  promise._n = true;\n  var chain = promise._c;\n  microtask(function () {\n    var value = promise._v;\n    var ok = promise._s == 1;\n    var i = 0;\n    var run = function (reaction) {\n      var handler = ok ? reaction.ok : reaction.fail;\n      var resolve = reaction.resolve;\n      var reject = reaction.reject;\n      var domain = reaction.domain;\n      var result, then, exited;\n      try {\n        if (handler) {\n          if (!ok) {\n            if (promise._h == 2) onHandleUnhandled(promise);\n            promise._h = 1;\n          }\n          if (handler === true) result = value;\n          else {\n            if (domain) domain.enter();\n            result = handler(value); // may throw\n            if (domain) {\n              domain.exit();\n              exited = true;\n            }\n          }\n          if (result === reaction.promise) {\n            reject(TypeError('Promise-chain cycle'));\n          } else if (then = isThenable(result)) {\n            then.call(result, resolve, reject);\n          } else resolve(result);\n        } else reject(value);\n      } catch (e) {\n        if (domain && !exited) domain.exit();\n        reject(e);\n      }\n    };\n    while (chain.length > i) run(chain[i++]); // variable length - can't use forEach\n    promise._c = [];\n    promise._n = false;\n    if (isReject && !promise._h) onUnhandled(promise);\n  });\n};\nvar onUnhandled = function (promise) {\n  task.call(global, function () {\n    var value = promise._v;\n    var unhandled = isUnhandled(promise);\n    var result, handler, console;\n    if (unhandled) {\n      result = perform(function () {\n        if (isNode) {\n          process.emit('unhandledRejection', value, promise);\n        } else if (handler = global.onunhandledrejection) {\n          handler({ promise: promise, reason: value });\n        } else if ((console = global.console) && console.error) {\n          console.error('Unhandled promise rejection', value);\n        }\n      });\n      // Browsers should not trigger `rejectionHandled` event if it was handled here, NodeJS - should\n      promise._h = isNode || isUnhandled(promise) ? 2 : 1;\n    } promise._a = undefined;\n    if (unhandled && result.e) throw result.v;\n  });\n};\nvar isUnhandled = function (promise) {\n  return promise._h !== 1 && (promise._a || promise._c).length === 0;\n};\nvar onHandleUnhandled = function (promise) {\n  task.call(global, function () {\n    var handler;\n    if (isNode) {\n      process.emit('rejectionHandled', promise);\n    } else if (handler = global.onrejectionhandled) {\n      handler({ promise: promise, reason: promise._v });\n    }\n  });\n};\nvar $reject = function (value) {\n  var promise = this;\n  if (promise._d) return;\n  promise._d = true;\n  promise = promise._w || promise; // unwrap\n  promise._v = value;\n  promise._s = 2;\n  if (!promise._a) promise._a = promise._c.slice();\n  notify(promise, true);\n};\nvar $resolve = function (value) {\n  var promise = this;\n  var then;\n  if (promise._d) return;\n  promise._d = true;\n  promise = promise._w || promise; // unwrap\n  try {\n    if (promise === value) throw TypeError(\"Promise can't be resolved itself\");\n    if (then = isThenable(value)) {\n      microtask(function () {\n        var wrapper = { _w: promise, _d: false }; // wrap\n        try {\n          then.call(value, ctx($resolve, wrapper, 1), ctx($reject, wrapper, 1));\n        } catch (e) {\n          $reject.call(wrapper, e);\n        }\n      });\n    } else {\n      promise._v = value;\n      promise._s = 1;\n      notify(promise, false);\n    }\n  } catch (e) {\n    $reject.call({ _w: promise, _d: false }, e); // wrap\n  }\n};\n\n// constructor polyfill\nif (!USE_NATIVE) {\n  // 25.4.3.1 Promise(executor)\n  $Promise = function Promise(executor) {\n    anInstance(this, $Promise, PROMISE, '_h');\n    aFunction(executor);\n    Internal.call(this);\n    try {\n      executor(ctx($resolve, this, 1), ctx($reject, this, 1));\n    } catch (err) {\n      $reject.call(this, err);\n    }\n  };\n  // eslint-disable-next-line no-unused-vars\n  Internal = function Promise(executor) {\n    this._c = [];             // <- awaiting reactions\n    this._a = undefined;      // <- checked in isUnhandled reactions\n    this._s = 0;              // <- state\n    this._d = false;          // <- done\n    this._v = undefined;      // <- value\n    this._h = 0;              // <- rejection state, 0 - default, 1 - handled, 2 - unhandled\n    this._n = false;          // <- notify\n  };\n  Internal.prototype = require('./_redefine-all')($Promise.prototype, {\n    // 25.4.5.3 Promise.prototype.then(onFulfilled, onRejected)\n    then: function then(onFulfilled, onRejected) {\n      var reaction = newPromiseCapability(speciesConstructor(this, $Promise));\n      reaction.ok = typeof onFulfilled == 'function' ? onFulfilled : true;\n      reaction.fail = typeof onRejected == 'function' && onRejected;\n      reaction.domain = isNode ? process.domain : undefined;\n      this._c.push(reaction);\n      if (this._a) this._a.push(reaction);\n      if (this._s) notify(this, false);\n      return reaction.promise;\n    },\n    // 25.4.5.1 Promise.prototype.catch(onRejected)\n    'catch': function (onRejected) {\n      return this.then(undefined, onRejected);\n    }\n  });\n  OwnPromiseCapability = function () {\n    var promise = new Internal();\n    this.promise = promise;\n    this.resolve = ctx($resolve, promise, 1);\n    this.reject = ctx($reject, promise, 1);\n  };\n  newPromiseCapabilityModule.f = newPromiseCapability = function (C) {\n    return C === $Promise || C === Wrapper\n      ? new OwnPromiseCapability(C)\n      : newGenericPromiseCapability(C);\n  };\n}\n\n$export($export.G + $export.W + $export.F * !USE_NATIVE, { Promise: $Promise });\nrequire('./_set-to-string-tag')($Promise, PROMISE);\nrequire('./_set-species')(PROMISE);\nWrapper = require('./_core')[PROMISE];\n\n// statics\n$export($export.S + $export.F * !USE_NATIVE, PROMISE, {\n  // 25.4.4.5 Promise.reject(r)\n  reject: function reject(r) {\n    var capability = newPromiseCapability(this);\n    var $$reject = capability.reject;\n    $$reject(r);\n    return capability.promise;\n  }\n});\n$export($export.S + $export.F * (LIBRARY || !USE_NATIVE), PROMISE, {\n  // 25.4.4.6 Promise.resolve(x)\n  resolve: function resolve(x) {\n    return promiseResolve(LIBRARY && this === Wrapper ? $Promise : this, x);\n  }\n});\n$export($export.S + $export.F * !(USE_NATIVE && require('./_iter-detect')(function (iter) {\n  $Promise.all(iter)['catch'](empty);\n})), PROMISE, {\n  // 25.4.4.1 Promise.all(iterable)\n  all: function all(iterable) {\n    var C = this;\n    var capability = newPromiseCapability(C);\n    var resolve = capability.resolve;\n    var reject = capability.reject;\n    var result = perform(function () {\n      var values = [];\n      var index = 0;\n      var remaining = 1;\n      forOf(iterable, false, function (promise) {\n        var $index = index++;\n        var alreadyCalled = false;\n        values.push(undefined);\n        remaining++;\n        C.resolve(promise).then(function (value) {\n          if (alreadyCalled) return;\n          alreadyCalled = true;\n          values[$index] = value;\n          --remaining || resolve(values);\n        }, reject);\n      });\n      --remaining || resolve(values);\n    });\n    if (result.e) reject(result.v);\n    return capability.promise;\n  },\n  // 25.4.4.4 Promise.race(iterable)\n  race: function race(iterable) {\n    var C = this;\n    var capability = newPromiseCapability(C);\n    var reject = capability.reject;\n    var result = perform(function () {\n      forOf(iterable, false, function (promise) {\n        C.resolve(promise).then(capability.resolve, reject);\n      });\n    });\n    if (result.e) reject(result.v);\n    return capability.promise;\n  }\n});\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/es6.promise.js\n// module id = 66\n// module chunks = 0 1", "module.exports = function (it, Constructor, name, forbiddenField) {\n  if (!(it instanceof Constructor) || (forbiddenField !== undefined && forbiddenField in it)) {\n    throw TypeError(name + ': incorrect invocation!');\n  } return it;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_an-instance.js\n// module id = 67\n// module chunks = 0 1", "var ctx = require('./_ctx');\nvar call = require('./_iter-call');\nvar isArrayIter = require('./_is-array-iter');\nvar anObject = require('./_an-object');\nvar toLength = require('./_to-length');\nvar getIterFn = require('./core.get-iterator-method');\nvar BREAK = {};\nvar RETURN = {};\nvar exports = module.exports = function (iterable, entries, fn, that, ITERATOR) {\n  var iterFn = ITERATOR ? function () { return iterable; } : getIterFn(iterable);\n  var f = ctx(fn, that, entries ? 2 : 1);\n  var index = 0;\n  var length, step, iterator, result;\n  if (typeof iterFn != 'function') throw TypeError(iterable + ' is not iterable!');\n  // fast case for arrays with default iterator\n  if (isArrayIter(iterFn)) for (length = toLength(iterable.length); length > index; index++) {\n    result = entries ? f(anObject(step = iterable[index])[0], step[1]) : f(iterable[index]);\n    if (result === BREAK || result === RETURN) return result;\n  } else for (iterator = iterFn.call(iterable); !(step = iterator.next()).done;) {\n    result = call(iterator, f, step.value, entries);\n    if (result === BREAK || result === RETURN) return result;\n  }\n};\nexports.BREAK = BREAK;\nexports.RETURN = RETURN;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_for-of.js\n// module id = 68\n// module chunks = 0 1", "// call something on iterator step with safe closing on error\nvar anObject = require('./_an-object');\nmodule.exports = function (iterator, fn, value, entries) {\n  try {\n    return entries ? fn(anObject(value)[0], value[1]) : fn(value);\n  // 7.4.6 IteratorClose(iterator, completion)\n  } catch (e) {\n    var ret = iterator['return'];\n    if (ret !== undefined) anObject(ret.call(iterator));\n    throw e;\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_iter-call.js\n// module id = 69\n// module chunks = 0 1", "// check on default Array iterator\nvar Iterators = require('./_iterators');\nvar ITERATOR = require('./_wks')('iterator');\nvar ArrayProto = Array.prototype;\n\nmodule.exports = function (it) {\n  return it !== undefined && (Iterators.Array === it || ArrayProto[ITERATOR] === it);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_is-array-iter.js\n// module id = 70\n// module chunks = 0 1", "var classof = require('./_classof');\nvar ITERATOR = require('./_wks')('iterator');\nvar Iterators = require('./_iterators');\nmodule.exports = require('./_core').getIteratorMethod = function (it) {\n  if (it != undefined) return it[ITERATOR]\n    || it['@@iterator']\n    || Iterators[classof(it)];\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/core.get-iterator-method.js\n// module id = 71\n// module chunks = 0 1", "// fast apply, http://jsperf.lnkit.com/fast-apply/5\nmodule.exports = function (fn, args, that) {\n  var un = that === undefined;\n  switch (args.length) {\n    case 0: return un ? fn()\n                      : fn.call(that);\n    case 1: return un ? fn(args[0])\n                      : fn.call(that, args[0]);\n    case 2: return un ? fn(args[0], args[1])\n                      : fn.call(that, args[0], args[1]);\n    case 3: return un ? fn(args[0], args[1], args[2])\n                      : fn.call(that, args[0], args[1], args[2]);\n    case 4: return un ? fn(args[0], args[1], args[2], args[3])\n                      : fn.call(that, args[0], args[1], args[2], args[3]);\n  } return fn.apply(that, args);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_invoke.js\n// module id = 72\n// module chunks = 0 1", "var global = require('./_global');\nvar macrotask = require('./_task').set;\nvar Observer = global.MutationObserver || global.WebKitMutationObserver;\nvar process = global.process;\nvar Promise = global.Promise;\nvar isNode = require('./_cof')(process) == 'process';\n\nmodule.exports = function () {\n  var head, last, notify;\n\n  var flush = function () {\n    var parent, fn;\n    if (isNode && (parent = process.domain)) parent.exit();\n    while (head) {\n      fn = head.fn;\n      head = head.next;\n      try {\n        fn();\n      } catch (e) {\n        if (head) notify();\n        else last = undefined;\n        throw e;\n      }\n    } last = undefined;\n    if (parent) parent.enter();\n  };\n\n  // Node.js\n  if (isNode) {\n    notify = function () {\n      process.nextTick(flush);\n    };\n  // browsers with MutationObserver, except iOS Safari - https://github.com/zloirock/core-js/issues/339\n  } else if (Observer && !(global.navigator && global.navigator.standalone)) {\n    var toggle = true;\n    var node = document.createTextNode('');\n    new Observer(flush).observe(node, { characterData: true }); // eslint-disable-line no-new\n    notify = function () {\n      node.data = toggle = !toggle;\n    };\n  // environments with maybe non-completely correct, but existent Promise\n  } else if (Promise && Promise.resolve) {\n    // Promise.resolve without an argument throws an error in LG WebOS 2\n    var promise = Promise.resolve(undefined);\n    notify = function () {\n      promise.then(flush);\n    };\n  // for other environments - macrotask based on:\n  // - setImmediate\n  // - MessageChannel\n  // - window.postMessag\n  // - onreadystatechange\n  // - setTimeout\n  } else {\n    notify = function () {\n      // strange IE + webpack dev server bug - use .call(global)\n      macrotask.call(global, flush);\n    };\n  }\n\n  return function (fn) {\n    var task = { fn: fn, next: undefined };\n    if (last) last.next = task;\n    if (!head) {\n      head = task;\n      notify();\n    } last = task;\n  };\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_microtask.js\n// module id = 73\n// module chunks = 0 1", "var global = require('./_global');\nvar navigator = global.navigator;\n\nmodule.exports = navigator && navigator.userAgent || '';\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_user-agent.js\n// module id = 74\n// module chunks = 0 1", "var hide = require('./_hide');\nmodule.exports = function (target, src, safe) {\n  for (var key in src) {\n    if (safe && target[key]) target[key] = src[key];\n    else hide(target, key, src[key]);\n  } return target;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_redefine-all.js\n// module id = 75\n// module chunks = 0 1", "'use strict';\nvar global = require('./_global');\nvar core = require('./_core');\nvar dP = require('./_object-dp');\nvar DESCRIPTORS = require('./_descriptors');\nvar SPECIES = require('./_wks')('species');\n\nmodule.exports = function (KEY) {\n  var C = typeof core[KEY] == 'function' ? core[KEY] : global[KEY];\n  if (DESCRIPTORS && C && !C[SPECIES]) dP.f(C, SPECIES, {\n    configurable: true,\n    get: function () { return this; }\n  });\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_set-species.js\n// module id = 76\n// module chunks = 0 1", "var ITERATOR = require('./_wks')('iterator');\nvar SAFE_CLOSING = false;\n\ntry {\n  var riter = [7][ITERATOR]();\n  riter['return'] = function () { SAFE_CLOSING = true; };\n  // eslint-disable-next-line no-throw-literal\n  Array.from(riter, function () { throw 2; });\n} catch (e) { /* empty */ }\n\nmodule.exports = function (exec, skipClosing) {\n  if (!skipClosing && !SAFE_CLOSING) return false;\n  var safe = false;\n  try {\n    var arr = [7];\n    var iter = arr[ITERATOR]();\n    iter.next = function () { return { done: safe = true }; };\n    arr[ITERATOR] = function () { return iter; };\n    exec(arr);\n  } catch (e) { /* empty */ }\n  return safe;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_iter-detect.js\n// module id = 77\n// module chunks = 0 1", "// https://github.com/tc39/proposal-promise-finally\n'use strict';\nvar $export = require('./_export');\nvar core = require('./_core');\nvar global = require('./_global');\nvar speciesConstructor = require('./_species-constructor');\nvar promiseResolve = require('./_promise-resolve');\n\n$export($export.P + $export.R, 'Promise', { 'finally': function (onFinally) {\n  var C = speciesConstructor(this, core.Promise || global.Promise);\n  var isFunction = typeof onFinally == 'function';\n  return this.then(\n    isFunction ? function (x) {\n      return promiseResolve(C, onFinally()).then(function () { return x; });\n    } : onFinally,\n    isFunction ? function (e) {\n      return promiseResolve(C, onFinally()).then(function () { throw e; });\n    } : onFinally\n  );\n} });\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/es7.promise.finally.js\n// module id = 78\n// module chunks = 0 1", "'use strict';\n// https://github.com/tc39/proposal-promise-try\nvar $export = require('./_export');\nvar newPromiseCapability = require('./_new-promise-capability');\nvar perform = require('./_perform');\n\n$export($export.S, 'Promise', { 'try': function (callbackfn) {\n  var promiseCapability = newPromiseCapability.f(this);\n  var result = perform(callbackfn);\n  (result.e ? promiseCapability.reject : promiseCapability.resolve)(result.v);\n  return promiseCapability.promise;\n} });\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/es7.promise.try.js\n// module id = 79\n// module chunks = 0 1", "var core = require('../../modules/_core');\nvar $JSON = core.JSON || (core.JSON = { stringify: JSON.stringify });\nmodule.exports = function stringify(it) { // eslint-disable-line no-unused-vars\n  return $JSON.stringify.apply($JSON, arguments);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/fn/json/stringify.js\n// module id = 80\n// module chunks = 0 1", "import commonUtil from \"@/common/commonUtil\";\r\n\r\nvar globalUtil = require('@/common/globalUtil.js').default;\r\nvar httpUtil = require('@/common/httpUtil.js').default;\r\nvar sparkmd5 = require('@/libs/md5/spark-md5.js');\r\n\r\nvar web = function (mainOpts) {\r\n    var self = this,\r\n        $control, controlId,\r\n        uploadRetryTimer = {}, //文件上传重试计时器\r\n        s3;\r\n    this.tasks = []; //任务列表\r\n\r\n    function getConfig() {\r\n        if (window.nxt && window.nxt.config) {\r\n            return window.nxt.config;\r\n        }\r\n        else if (mainOpts.configInst) {\r\n            return mainOpts.configInst;\r\n        }\r\n    }\r\n\r\n    function windowClose() {\r\n        if (self.getFilesByStatus('progress', 'deleteing').length > 0) {\r\n            return l('upload.closePageTip', '当前页面存在未完成的上传任务，关闭页面会导致上传失败，你确定要关闭吗？');\r\n        }\r\n    }\r\n\r\n    function init() {\r\n        $(window).on('beforeunload', windowClose);\r\n    }\r\n\r\n    //获取文件最后修改时间\r\n    function getFileTime(file) {\r\n        //兼容不同浏览器\r\n        if (_.isUndefined(file.lastModifiedDate)) {\r\n            return file.lastModified;\r\n        }\r\n        return file.lastModifiedDate.getTime();\r\n    }\r\n\r\n    //获取默认关键帧\r\n    function getDefaultKeyframe(task) {\r\n        var type = _.find(getConfig().entityTypes, { code: task.entityType });\r\n        if (type == null) {\r\n            type = _.find(getConfig().entityTypes, { isOther: true });\r\n        }\r\n        if (type == null) {\r\n            return '';\r\n        }\r\n        return type.keyframe.replace('~', '');\r\n    }\r\n\r\n    //未授权提示\r\n    function unauthorizedTip() {\r\n        _.forEach(self.tasks, function (item) {\r\n            if (item.status !== 'added' && item.status !== 'success')\r\n                item.status = 'error';\r\n        });\r\n        $(window).off('beforeunload', windowClose);\r\n        commonUtil.prompt(l('upload.unauthorized', '你未登录或已超时，请重新登录。'));\r\n        location.href = getConfig().loginUrl + '?login_backUrl=' + location.href;\r\n    }\r\n\r\n    function initControl(accept) {\r\n        var accept = accept || '';\r\n        if ($control == null) {\r\n            controlId = _.uniqueId('mam-web-transfer-');\r\n            if (mainOpts.isMultiple === undefined || mainOpts.isMultiple) {\r\n                //$control = $('<input id=\"' + controlId + '\" type=\"file\" multiple style=\"display:none\"/>');\r\n                $control = $('<input id=\"' + controlId + '\" accept=\"' + accept + '\" type=\"file\" multiple style=\"display:none\"/>');\r\n            }\r\n            else {\r\n                //$control = $('<input id=\"' + controlId + '\" type=\"file\" style=\"display:none\"/>');\r\n                $control = $('<input id=\"' + controlId + '\" accept=\"' + accept + '\" type=\"file\" style=\"display:none\"/>');\r\n            }\r\n            $('body').append($control);\r\n        }\r\n    }\r\n\r\n    function destroyControl() {\r\n        if ($control != null) {\r\n            $control.unbind('change');\r\n            $control.remove(); //因为IE10 重置文件框的值比较麻烦，如果不重置，无法重复选择。所以每次初始化来兼容IE10\r\n            $control = null;\r\n        }\r\n    }\r\n\r\n    //触发事件\r\n    function trigger(event, data) {\r\n        $(self).trigger(event, data);\r\n    }\r\n\r\n    //订阅事件\r\n    this.on = function (name, call) {\r\n        //利用jquery的事件，因为HTML5的自定义事件不兼容IE10。\r\n        $(self).on(name, call);\r\n    }\r\n    this.off = function (name, call) {\r\n        $(self).off(name, call);\r\n    }\r\n\r\n    /** 为图片包和成组上传获取任务，单个任务里包含多个文件这种方式 */\r\n    this.getGroupTask = function(tasks, code, targetFolder, taskType, transferType) {\r\n        var type = _.find(getConfig().entityTypes, { 'code': code });\r\n\r\n        var rettask = {\r\n            entityType: type ? type.code : '',\r\n            files: tasks.map((task) => {\r\n                return {\r\n                    fileName: task.fileName,\r\n                    file: task.file,\r\n                    metadata: task.metadata\r\n                }\r\n            }),\r\n            'metadata': {\r\n                name: ''\r\n            },\r\n            targetFolder: targetFolder,\r\n            taskType: taskType,\r\n            transferType: transferType,\r\n            status: 'added',\r\n            progress: 0\r\n        };\r\n        return rettask;\r\n    }\r\n\r\n    this.getTasksByFiles = function(files){\r\n        var result = [];\r\n        _.forEach(files, function (item) {\r\n            var ext = '.' + commonUtil.getExtension(item.name);\r\n            result.push({\r\n                entityType: commonUtil.getTypeByExt(ext, getConfig()).code,\r\n                fileName: item.name,\r\n                metadata: {\r\n                    name: commonUtil.getFileName(item.name),\r\n                    ext: ext\r\n                },\r\n                status: 'added',\r\n                progress: 0,\r\n                file: item\r\n            });\r\n        });\r\n        return result;\r\n    }\r\n\r\n    //打开文件选取框\r\n    this.openFileSelector = function (callback, single, accept) {\r\n        var self = this;\r\n        // console.log(AWS)\r\n        // console.log(this.OSS)\r\n        destroyControl();\r\n        initControl(accept);\r\n        if (single === true) {\r\n            $control.removeAttr('multiple');\r\n        } else {\r\n            $control.attr('multiple', 'multiple');\r\n        }\r\n        $control.on('change', function () {\r\n            if ($control.val() === '') {\r\n                return;\r\n            }\r\n            var files = $control[0].files;\r\n            if (single === true) {\r\n                files = files[0];\r\n            }\r\n            callback(self.getTasksByFiles(files));\r\n            initControl(accept);\r\n        });\r\n        $control.click(function (e) {\r\n            e.stopPropagation();\r\n        });\r\n        $control.trigger('click');\r\n    }\r\n\r\n    //创建任务\r\n    this.createTask = function (dto, params) {\r\n        var list = [];\r\n        switch (params.taskType) {\r\n            case 1: //普通类型 //另外，素材包类型进行多个素材上传并分别创建任务，这些个任务在上一步已重置为了普通类型上传\r\n                if (!_.isArray(dto)) {\r\n                    dto = [dto];\r\n                }\r\n                _.forEach(dto, function (task) {\r\n                    if (task.files && task.files.length > 0) {\r\n                        task.files = _.map(task.files, function (file) {\r\n                            return {\r\n                                file: file.file,\r\n                                fileName: file.file.name,\r\n                                fileSize: file.file.size,\r\n                                fileLastModifiedDate: getFileTime(file.file),\r\n                                type: Number(file.type)?Number(file.type):0\r\n                            };\r\n                        });\r\n                        if (task.file){\r\n                            task.files.unshift({\r\n                                file: task.file,\r\n                                fileName: task.file.name,\r\n                                fileSize: task.file.size,\r\n                                fileLastModifiedDate: getFileTime(task.file),\r\n                                type: task.type !== undefined ? Number(task.type) : 0\r\n                            });\r\n                        }\r\n                    }\r\n                    else {\r\n                        task.files = [{\r\n                            file: task.file,\r\n                            fileName: task.file.name,\r\n                            fileSize: task.file.size,\r\n                            fileLastModifiedDate: getFileTime(task.file),\r\n                            type: task.type !== undefined ? Number(task.type) : 0\r\n                        }];\r\n                    }\r\n                    delete task.file;\r\n                    list.push(task);\r\n                });\r\n                break;\r\n            case 2:\r\n            case 3:\r\n                _.forEach(dto.files, function (file) {\r\n                    file.fileName = file.file.name;\r\n                    file.fileSize = file.file.size;\r\n                    file.fileLastModifiedDate = getFileTime(file.file);\r\n                });\r\n                list.push(dto);\r\n                break;\r\n        }\r\n        _.forEach(list, function (task) {\r\n            task.taskType = params.taskType;\r\n            task.transferType = params.transferType;\r\n            task.targetFolder = params.targetFolder;\r\n            task.relationContentType = params.relationContentType;\r\n            task.relationContentId = params.relationContentId;\r\n            self.addTask(task);\r\n        });\r\n    }\r\n\r\n    //亚马逊OSS初始化 + Vida Grid（类型是5）\r\n    function initS3MultipartUpload(ossClientInfo, file) {\r\n        if (!ossClientInfo)\r\n            return;\r\n        if (ossClientInfo.manageType === 'TYY_S3')\r\n            return initOssMultipartUpload(ossClientInfo, file);\r\n        if (ossClientInfo.manageType === 'ALY_OSS')\r\n            return;\r\n        var params = {\r\n            Bucket: ossClientInfo.bucketName,\r\n            Key: file.relativePath.replace(/\\\\/g, '/')\r\n        };\r\n        getS3Client(ossClientInfo);\r\n        if (file.fileSize <= (5 * 1024 * 1024))\r\n            return;\r\n        var createUploadPromise = s3.createMultipartUpload(params).promise();\r\n        file.createUploadPromise = createUploadPromise;\r\n        var defer = $.Deferred();\r\n        file.createUploadPromise.then(function (initData) {\r\n            file.uploadId = initData.UploadId;\r\n            /* initData = {\r\n                Bucket: \"examplebucket\", \r\n                Key: \"largeobject\", \r\n                UploadId: \"ibZBv_75gd9r8lH_gqXatLdxMVpAlj6ZQjEs.OwyF3953YdwbcQnMA2BLGn8Lx12fQNICtMw5KyteFeHw.Sjng--\"\r\n            } */\r\n            defer.resolve();\r\n        }).catch(function (err) {\r\n            defer.reject();\r\n            console.info(err);// an error occurred\r\n        });\r\n        return defer.promise();\r\n    }\r\n\r\n    function getS3Client(ossClientInfo) {\r\n        var credentials = {\r\n            accessKeyId: ossClientInfo.accessKeyId,\r\n            secretAccessKey: ossClientInfo.accessKeySecret,\r\n            endpoint: ossClientInfo.endpoint,\r\n            sslEnabled: !ossClientInfo.useHttp,\r\n            s3ForcePathStyle: true,\r\n            signatureVersion: 'v' + ossClientInfo.version,\r\n            apiVersion: '2006-03-01'\r\n        };// 秘钥形式的登录上传\r\n        if (ossClientInfo.region)\r\n            AWS.config.region = ossClientInfo.region; // 设置0000000区域\r\n        if (getConfig().isHandleHttpPath && credentials.endpoint) {\r\n            var href = window.location.protocol + '//' + window.location.host;\r\n            credentials.endpoint = href;\r\n        }\r\n        if (ossClientInfo.manageType !== 'TYY_S3')\r\n            s3 = new AWS.S3(credentials);\r\n        else\r\n            s3 = new OOS.S3(credentials);\r\n    }\r\n\r\n    //天翼云OSS初始化\r\n    function initOssMultipartUpload(ossClientInfo, file) {\r\n        if (!ossClientInfo || ossClientInfo.manageType !== 'TYY_S3')\r\n            return;\r\n        getS3Client(ossClientInfo);\r\n        if (file.fileSize <= (5 * 1024 * 1024))\r\n            return;\r\n        var params = {\r\n            Bucket: ossClientInfo.bucketName,\r\n            Key: file.relativePath.replace(/\\\\/g, '/')\r\n        };\r\n        var createUploadPromise = s3.createMultipartUpload(params).promise();\r\n        file.createUploadPromise = createUploadPromise;\r\n        var defer = $.Deferred();\r\n        file.createUploadPromise.then(function (initData) {\r\n            file.uploadId = initData.UploadId;\r\n            /* initData = {\r\n                Bucket: \"examplebucket\", \r\n                Key: \"largeobject\", \r\n                UploadId: \"ibZBv_75gd9r8lH_gqXatLdxMVpAlj6ZQjEs.OwyF3953YdwbcQnMA2BLGn8Lx12fQNICtMw5KyteFeHw.Sjng--\"\r\n            } */\r\n            defer.resolve();\r\n        }).catch(function (err) {\r\n            defer.reject();\r\n            console.info(err);// an error occurred\r\n        });\r\n        return defer.promise();\r\n    }\r\n\r\n    //初始化任务\r\n    function initTask(task) {\r\n        trigger('task-init-before', task);\r\n        let apiVersion = commonUtil.getCookie('apiVersion');\r\n        var url = getConfig().server + '/upload/multipart/init';\r\n        if (apiVersion && apiVersion === 'mamcore2.3'){\r\n            url = getConfig().server + '/sflud/v1/upload/multipart/init';\r\n        }\r\n        if (mainOpts.loginToken){\r\n            url += '?token=' + mainOpts.loginToken\r\n        }\r\n        if (task.metadata && task.metadata.field) {\r\n            // 因后台升级为.net core3.0 需要数据类型一致\r\n            var fileSize = task.metadata.field.find(function (s) { return s.fieldName === 'filesize'; });\r\n            if (fileSize) {\r\n                fileSize.value = String(fileSize.value);\r\n            }\r\n        }\r\n        httpUtil.post(url, task).then(function (res) {\r\n            res = res.data;\r\n            for (var i = 0; i < res.files.length; i++) {\r\n                var file = task.files[i].file;\r\n                task.files[i] = res.files[i];\r\n                task.files[i].file = file;\r\n                task.files[i].status = 'prepared';\r\n                task.files[i].fileSizeString = commonUtil.formatSize(res.files[i].fileSize);\r\n                task.files[i].progress = 0;\r\n                task.sizeTotal += res.files[i].fileSize;\r\n                task.chunkTotal += res.files[i].chunkTotal;\r\n            }\r\n            task.taskId = res.taskId;\r\n            task.entityType = res.entityType;\r\n            task.fileTotal = res.fileTotal;\r\n            task.targetFolder = res.targetFolder;\r\n            task.targetFolderName = res.targetFolderName;\r\n            task.targetType = res.targetType;\r\n            task.keyframe = res.keyframe;\r\n            task.status = 'prepared';\r\n            task.inited = true;\r\n            task.sizeTotalString = commonUtil.formatSize(task.sizeTotal);\r\n            task.isJsUpload = res.isJsUpload;\r\n            task.ossClientInfo = res.ossClientInfo;\r\n            trigger('task-init-success', task);\r\n            self.prepareUpload();\r\n        }, function (res) {\r\n            trigger('task-init-error', [task, res]);\r\n            if (res.status === 401) {\r\n                unauthorizedTip(self.tasks);\r\n                return;\r\n            } else {\r\n                task.status = 'error';\r\n                _.forEach(task.files, function (file) { file.status = 'error'; });\r\n                commonUtil.prompt(l('upload.', '上传失败：${text}', { text: res.error.desc || res.error.title }));\r\n                self.prepareUpload();\r\n            }\r\n        });\r\n    }\r\n\r\n    //添加任务\r\n    this.addTask = function (task) {\r\n        task.status = 'init';\r\n        task.progress = task.sizeTotal = task.chunkFinished = task.chunkTotal = 0;\r\n        task.keyframe = getDefaultKeyframe(task);\r\n        self.tasks.push(task);\r\n        initTask(task);\r\n    }\r\n\r\n    //准备上传\r\n    this.prepareUpload = function () {\r\n        var progress = self.getFilesByStatus('progress');\r\n        var len = getConfig().webUploadThreads - progress.length;\r\n        if (len <= 0)\r\n            return;\r\n        var prepared = self.getFilesByStatus('prepared');\r\n        if (len > prepared.length)\r\n            len = prepared.length;\r\n        if (len === 0) //判断是否存在需要上传的任务\r\n            return;\r\n        for (var i = 0; i < len; i++) {\r\n            prepared[i].task.status = prepared[i].file.status = 'progress';\r\n            self.upload(prepared[i]);\r\n        }\r\n    }\r\n\r\n    //上传\r\n    this.upload = function (item) {\r\n        if (item.task.ossClientInfo) {\r\n            if (item.task.ossClientInfo.manageType === 'ALY_OSS')\r\n                this.uploadByAliOss(item);\r\n            else if (item.task.ossClientInfo.manageType === 'TYY_S3')\r\n                this.uploadByOos(item);\r\n            else if (item.task.ossClientInfo.manageType === 'KSYUN')    \r\n                this.uploadByKsyun(item);\r\n            else\r\n                this.uploadByS3(item);\r\n        }\r\n        else {\r\n            this.uploadByBackend(item);\r\n        }\r\n    }\r\n\r\n    //计算分片数据的MD5\r\n    function calcChunkMd5(item, data, callback) {\r\n        if (getConfig().webUploadMd5Enable) {\r\n            if (item.file.fileReader == null)\r\n                item.file.fileReader = new FileReader();\r\n            item.file.fileReader.onload = function (e) {\r\n                var spark = new sparkmd5();\r\n                spark.appendBinary(e.target.result);\r\n                var md5 = spark.end();\r\n                spark.destroy();\r\n                callback(md5);\r\n            };\r\n            item.file.fileReader.onerror = function (e) { uploadErrorRetry(item); };\r\n            item.file.fileReader.readAsBinaryString(data);\r\n        } else {\r\n            callback('');\r\n        }\r\n    }\r\n\r\n    //计算剩余时间\r\n    function calcSurplusTime(task, file) {\r\n        if (file.startTime != null) {\r\n            if (!_.isNumber(file.surplusTime))\r\n                file.surplusTime = 0;\r\n            file.surplusTime = (new Date() - file.startTime) * (file.chunkTotal - file.chunkIndex);\r\n\r\n            if (!_.isNumber(task.surplusTime))\r\n                task.surplusTime = 0;\r\n            task.surplusTime = (new Date() - file.startTime) * (task.chunkTotal - task.chunkFinished);\r\n        }\r\n        file.startTime = new Date();\r\n    }\r\n\r\n    //上传失败重试\r\n    function uploadErrorRetry(item) {\r\n        if (!item.file.hasOwnProperty('errorCount'))\r\n            item.file.errorCount = 0;\r\n        if (item.file.errorCount < 4) {\r\n            item.file.errorCount++;\r\n            uploadRetryTimer[item.file.fileId] = setTimeout(function () { self.upload(item); }, 3000);\r\n        } else {\r\n            item.task.status = item.file.status = 'error';\r\n            trigger('task-upload-error', item.task);\r\n            self.prepareUpload();\r\n        }\r\n    }\r\n\r\n    this.uploadByBackend = function (item) {\r\n        var file = item.file;\r\n        var task = item.task;\r\n        if (file.chunkIndex < file.chunkTotal) {\r\n            calcSurplusTime(task, file);\r\n            var start = file.chunkIndex * file.chunkSize,\r\n                end = Math.min(file.fileSize, start + file.chunkSize),\r\n                form = new FormData(),\r\n                data = file.file.slice(start, end);\r\n            if (data == null || data.size == 0) {\r\n                item.task.status = item.file.status = 'error';\r\n                trigger('task-upload-error', item.task);\r\n                item.file.file = null;\r\n                self.prepareUpload();\r\n                return;\r\n            }\r\n            calcChunkMd5(item, data, function (md5) {\r\n                form.append('fileData', data);\r\n                form.append('taskId', file.taskId);\r\n                form.append('fileId', file.fileId);\r\n                form.append('chunkIndex', file.chunkIndex);\r\n                form.append('md5', md5);\r\n                //form.append('uploadId', file.tag);\r\n\r\n                //下面还写一次是因为有2种情况，必须在这里也要删除才合理，1,初始化成功准备上传，2，上传一片后上传下一片之前\r\n                //之所有放在构造formData之后，是因为构造formData是需要一点点时间的。避免时间差带来的偶现bug\r\n                if (task.status === 'deleteing') {\r\n                    self.clearTask(task);\r\n                    return;\r\n                }\r\n                let apiVersion = commonUtil.getCookie('apiVersion');\r\n                var url = '/upload/multipart'\r\n                if (apiVersion && apiVersion === 'mamcore2.3'){\r\n                    url = '/sflud/v1/upload/multipart'\r\n                }\r\n                if (mainOpts.loginToken){\r\n                    url += '?token=' + mainOpts.loginToken\r\n                }\r\n                httpUtil.post(getConfig().server + url, form, {\r\n                    contentType: false,\r\n                    processData: false\r\n                }).then(function (res) {\r\n                    if (task.status === 'deleteing') {\r\n                        self.clearTask(task);\r\n                        return;\r\n                    }\r\n                    res = res.data;\r\n                    file = _.find(task.files, { fileId: res.fileId });\r\n                    file.chunkIndex = res.chunkIndex;\r\n                    if (res.taskStatus === 3) { // 3：任务完成\r\n                        file.progress = task.progress = 100;\r\n                        file.surplusTime = task.surplusTime = null;\r\n                        delete file.fileReader;\r\n                        file.status = task.status = 'success';\r\n                        self.prepareUpload();\r\n                        callComplete(task).then(function(){\r\n                            trigger('task-upload-success', task);\r\n                        });\r\n                    } else {\r\n                        task.progress = calcTaskProgress(task);\r\n                        file.progress = calcFileProgress(file);\r\n                        file.status = task.status = 'progress';\r\n                        trigger('task-upload-progress', task);\r\n                        file.errorCount = 0;\r\n                        self.upload(item);\r\n                    }\r\n                }, function (res) {\r\n                    if (res.status === 401) {\r\n                        unauthorizedTip();\r\n                        return;\r\n                    }\r\n                    uploadErrorRetry(item);\r\n                });\r\n            });\r\n        } else {\r\n            if (file.hasOwnProperty('errorCount')) {\r\n                delete uploadRetryTimer[file.fileId];\r\n                delete file.errorCount;\r\n            }\r\n            delete file.fileReader;\r\n            file.surplusTime = null;\r\n            file.progress = 100;\r\n            file.status = 'success';\r\n            task.progress = calcTaskProgress(task);\r\n            trigger('task-upload-success', task);\r\n            self.prepareUpload();\r\n        }\r\n    }\r\n\r\n    var callUpload = function (file) {\r\n        var form = new FormData();\r\n        form.append('taskId', file.taskId);\r\n        form.append('fileId', file.fileId);\r\n        form.append('chunkIndex', file.chunkIndex);\r\n        form.append('partInfo', JSON.stringify(file.partInfo));\r\n        form.append('uploadId', file.uploadId);\r\n        form.append('checkPoint', file.checkPoint);\r\n        let apiVersion = commonUtil.getCookie('apiVersion');\r\n        var url = '/upload/multipart'\r\n        if (apiVersion && apiVersion === 'mamcore2.3'){\r\n            url = '/sflud/v1/upload/multipart'\r\n        }\r\n        if (mainOpts.loginToken){\r\n            url += '?token=' + mainOpts.loginToken\r\n        }\r\n        return httpUtil.post(getConfig().server + url, form, {\r\n            contentType: false,\r\n            processData: false\r\n        })\r\n    }\r\n\r\n    var callComplete = function (task) {\r\n        var serverUrl = getConfig().server\r\n        let apiVersion = commonUtil.getCookie('apiVersion');\r\n        var url = '/upload/multipart/complete?taskId=' + task.taskId\r\n        if (apiVersion && apiVersion === 'mamcore2.3'){\r\n            url = '/sflud/v1/upload/multipart/complete?taskId=' + task.taskId\r\n        }\r\n        return httpUtil.post(serverUrl + url);\r\n    }\r\n\r\n    this.uploadByAliOssWithServerCallback = function (item) {\r\n        var file = item.file;\r\n        var task = item.task;\r\n\r\n        var clientOpt = {\r\n            region: task.ossClientInfo.region,\r\n            accessKeyId: task.ossClientInfo.accessKeyId,\r\n            accessKeySecret: task.ossClientInfo.accessKeySecret,\r\n            bucket: task.ossClientInfo.bucketName\r\n        };\r\n        if (task.ossClientInfo.endpoint) {\r\n            clientOpt.endpoint = task.ossClientInfo.endpoint;\r\n        }\r\n        var client = new this.OSS.Wrapper(clientOpt);\r\n        // var client = new OSS.Wrapper({\r\n        //     region: 'oss-cn-beijing',\r\n        //     accessKeyId: 'LTAIJNTTrKH8Aoat',\r\n        //     accessKeySecret: 'HjYCH0K4bYXZQ3I0JIoJ8Rbp2zYK0J',\r\n        //     bucket: 'bucket-pan-test'\r\n        // });\r\n\r\n        if (file.fileSize <= 102400)// 小文件不能分片上传\r\n        {\r\n            item.fileReader = new FileReader();\r\n            item.fileReader.onload = function (e) {\r\n                client.put(file.relativePath, new Buffer(this.result)).then(function (res) {\r\n                    callUpload(file).then(function (res) {\r\n                        res = res.data;\r\n                        if (res.taskStatus === 3) {// 3：任务完成\r\n                            file.progress = task.progress = 100;\r\n                            file.surplusTime = task.surplusTime = null;\r\n                            delete file.fileReader;\r\n                            file.status = task.status = 'success';\r\n                            callComplete(task).then(function(){\r\n                                trigger('task-upload-success', task);\r\n                            });\r\n                        }\r\n                        else {\r\n                            file.status = 'success';\r\n                            file.progress = 100;\r\n                        }\r\n                        self.prepareUpload();\r\n                    });\r\n                }, function (res) {\r\n                    console.error(res);\r\n                });\r\n            };\r\n            item.fileReader.readAsArrayBuffer(file.file);\r\n        }\r\n        else {\r\n            var callbackUrl = commonUtil.addUrlParam(getConfig().ossUpCallbackUrl, 'taskId=' + task.taskId);\r\n            if (mainOpts.loginToken){\r\n                callbackUrl = commonUtil.addUrlParam(callbackUrl, 'token=' + mainOpts.loginToken);\r\n            }\r\n            var opts = {\r\n                partSize: file.chunkSize,\r\n                progress: function (p, checkpoint, res) {\r\n                    return function (done) {\r\n                        if (task.status === 'deleteing') {\r\n                            self.clearTask(task);\r\n                        } else if (task.status === 'success') {\r\n                            done();\r\n                        }\r\n                        else {\r\n                            if (checkpoint.doneParts.length === 0)//第一次回调表示初始化，oss还未传文件上去，可忽略\r\n                            {\r\n                                done();\r\n                                return;\r\n                            }\r\n                            file.partInfo = checkpoint.doneParts[checkpoint.doneParts.length - 1];\r\n                            file.uploadId = checkpoint.uploadId;\r\n                            file.checkPoint = JSON.stringify(checkpoint, function (key, value) {\r\n                                if (key === 'file') {\r\n                                    return undefined;\r\n                                }\r\n                                return value;\r\n                            });\r\n                            file.chunkIndex = checkpoint.doneParts.length - 1;//以oss的完成片数为准，避免调用done以前连续收到两次回调\r\n                            callUpload(file).then(function (res) {\r\n                                if (task.status === 'deleteing') {\r\n                                    self.clearTask(task);\r\n                                    return;\r\n                                }\r\n                                res = res.data;\r\n                                file.chunkIndex = res.chunkIndex;\r\n                                if (res.taskStatus === 3) { // 3：任务完成\r\n                                    file.progress = task.progress = 100;\r\n                                    file.surplusTime = task.surplusTime = null;\r\n                                    delete file.fileReader;\r\n                                    file.status = task.status = 'success';\r\n                                    trigger('task-upload-success', task);\r\n                                    self.prepareUpload();\r\n                                    //callComplete(task);\r\n                                } else {\r\n                                    if (file.chunkIndex < file.chunkTotal) {\r\n                                        task.progress = calcTaskProgress(task);\r\n                                        file.progress = calcFileProgress(file);\r\n                                        file.status = task.status = 'progress';\r\n                                        trigger('task-upload-progress', task);\r\n                                        file.errorCount = 0;\r\n                                    } else {\r\n                                        if (file.hasOwnProperty('errorCount')) {\r\n                                            delete uploadRetryTimer[file.fileId];\r\n                                            delete file.errorCount;\r\n                                        }\r\n                                        file.surplusTime = null;\r\n                                        file.progress = 100;\r\n                                        file.status = 'success';\r\n                                        task.progress = calcTaskProgress(task);\r\n                                        trigger('task-upload-success', task);\r\n                                        self.prepareUpload();\r\n                                    }\r\n                                }\r\n                                done();\r\n                            }, function (res) {\r\n                                if (res.status === 401) {\r\n                                    unauthorizedTip();\r\n                                    return;\r\n                                }\r\n                                uploadErrorRetry(item);\r\n                            });\r\n                        }\r\n                    };\r\n                },\r\n                callback: {\r\n                    callbackUrl: callbackUrl,\r\n                    callbackBody: 'bucket=${bucket}&object=${object}&etag=${etag}&size=${size}&mimeType=${mimeType}&imageInfo.height=${imageInfo.height}&imageInfo.width=${imageInfo.width}&imageInfo.format=${imageInfo.format}'\r\n                }\r\n            };\r\n            if (file.checkPoint)//断点续传\r\n            {\r\n                opts.checkpoint = JSON.parse(file.checkPoint);\r\n                opts.checkpoint.file = file.file;\r\n            }\r\n            client.multipartUpload(file.relativePath, file.file, opts).then(function (res) {\r\n                console.log('upload success', res);\r\n            }, function (res) {\r\n                console.log(res);\r\n            });\r\n        }\r\n    };\r\n\r\n    this.uploadByAliOss = function (item) {\r\n        var file = item.file;\r\n        var task = item.task;\r\n\r\n        var clientOpt = {\r\n            region: task.ossClientInfo.region,\r\n            accessKeyId: task.ossClientInfo.accessKeyId,\r\n            accessKeySecret: task.ossClientInfo.accessKeySecret,\r\n            bucket: task.ossClientInfo.bucketName\r\n        };\r\n        if (task.ossClientInfo.endpoint) {\r\n            clientOpt.endpoint = task.ossClientInfo.endpoint;\r\n        }\r\n        var client = new this.OSS.Wrapper(clientOpt);\r\n        // var client = new OSS.Wrapper({\r\n        //     region: 'oss-cn-beijing',\r\n        //     accessKeyId: 'LTAIJNTTrKH8Aoat',\r\n        //     accessKeySecret: 'HjYCH0K4bYXZQ3I0JIoJ8Rbp2zYK0J',\r\n        //     bucket: 'bucket-pan-test'\r\n        // });\r\n\r\n        if (file.fileSize <= 102400)// 小文件不能分片上传\r\n        {\r\n            item.fileReader = new FileReader();\r\n            item.fileReader.onload = function (e) {\r\n                client.put(file.relativePath, new Buffer(this.result)).then(function (res) {\r\n                    callUpload(file).then(function (res) {\r\n                        res = res.data;\r\n                        if (res.taskStatus === 3) {// 3：任务完成\r\n                            file.progress = task.progress = 100;\r\n                            file.surplusTime = task.surplusTime = null;\r\n                            delete file.fileReader;\r\n                            file.status = task.status = 'success';\r\n                            callComplete(task).then(function(){\r\n                                trigger('task-upload-success', task);\r\n                            });\r\n                        }\r\n                        else {\r\n                            file.status = 'success';\r\n                            file.progress = 100;\r\n                        }\r\n                        self.prepareUpload();\r\n                    });\r\n                }, function (res) {\r\n                    console.error(res);\r\n                });\r\n            };\r\n            item.fileReader.readAsArrayBuffer(file.file);\r\n        }\r\n        else {\r\n            var opts = {\r\n                partSize: file.chunkSize,\r\n                progress: function (p, checkpoint, res) {\r\n                    return function (done) {\r\n                        if (task.status === 'deleteing') {\r\n                            self.clearTask(task);\r\n                        } else if (task.status === 'success') {\r\n                            done();\r\n                        }\r\n                        else {\r\n                            if (checkpoint.doneParts.length === 0)//第一次回调表示初始化，oss还未传文件上去，可忽略\r\n                            {\r\n                                done();\r\n                                return;\r\n                            }\r\n                            file.partInfo = checkpoint.doneParts[checkpoint.doneParts.length - 1];\r\n                            file.uploadId = checkpoint.uploadId;\r\n                            file.checkPoint = JSON.stringify(checkpoint, function (key, value) {\r\n                                if (key === 'file') {\r\n                                    return undefined;\r\n                                }\r\n                                return value;\r\n                            });\r\n                            file.chunkIndex = checkpoint.doneParts.length - 1;//以oss的完成片数为准，避免调用done以前连续收到两次回调\r\n                            callUpload(file).then(function (res) {\r\n                                if (task.status === 'deleteing') {\r\n                                    self.clearTask(task);\r\n                                    return;\r\n                                }\r\n                                res = res.data;\r\n                                file.chunkIndex = res.chunkIndex;\r\n                                if (res.taskStatus === 3) { // 3：任务完成\r\n                                    file.progress = task.progress = 100;\r\n                                    file.surplusTime = task.surplusTime = null;\r\n                                    delete file.fileReader;\r\n                                    file.status = task.status = 'success';\r\n                                    self.prepareUpload();\r\n                                    callComplete(task).then(function(){\r\n                                        trigger('task-upload-success', task);\r\n                                    });\r\n                                } else {\r\n                                    if (file.chunkIndex < file.chunkTotal) {\r\n                                        task.progress = calcTaskProgress(task);\r\n                                        file.progress = calcFileProgress(file);\r\n                                        file.status = task.status = 'progress';\r\n                                        trigger('task-upload-progress', task);\r\n                                        file.errorCount = 0;\r\n                                    } else {\r\n                                        if (file.hasOwnProperty('errorCount')) {\r\n                                            delete uploadRetryTimer[file.fileId];\r\n                                            delete file.errorCount;\r\n                                        }\r\n                                        file.surplusTime = null;\r\n                                        file.progress = 100;\r\n                                        file.status = 'success';\r\n                                        task.progress = calcTaskProgress(task);\r\n                                        trigger('task-upload-success', task);\r\n                                        self.prepareUpload();\r\n                                    }\r\n                                }\r\n                                done();\r\n                            }, function (res) {\r\n                                if (res.status === 401) {\r\n                                    unauthorizedTip();\r\n                                    return;\r\n                                }\r\n                                uploadErrorRetry(item);\r\n                            });\r\n                        }\r\n                    };\r\n                }\r\n            };\r\n            if (file.checkPoint)//断点续传\r\n            {\r\n                opts.checkpoint = JSON.parse(file.checkPoint);\r\n                opts.checkpoint.file = file.file;\r\n            }\r\n            client.multipartUpload(file.relativePath, file.file, opts).then(function (res) {\r\n                console.log('upload success', res);\r\n            }, function (res) {\r\n                console.log(res);\r\n            });\r\n        }\r\n    };\r\n\r\n    this.doUploadByKsyun = function(item){\r\n        var file = item.file;\r\n        var task = item.task;\r\n\r\n        var start = file.chunkIndex * file.chunkSize,\r\n            end = Math.min(file.fileSize, start + file.chunkSize),\r\n            filedata = file.file.slice(start, end);\r\n        var upParams = {\r\n            Bucket: task.ossClientInfo.bucketName,\r\n            Key: file.relativePath.replace(/\\\\/g, '/'),\r\n            PartNumber: file.chunkIndex + 1,\r\n            UploadId: file.uploadId,\r\n            body: filedata\r\n        }\r\n        Ks3.upload_part(upParams, function(err, partNumber, etag){\r\n            if (err) {\r\n                console.error(err);\r\n                return;\r\n            }\r\n            if (task.status === 'deleteing') {\r\n                self.clearTask(task);\r\n            }\r\n            else {\r\n                callUpload(file).then(function (res) {\r\n                    if (task.status === 'deleteing') {\r\n                        self.clearTask(task);\r\n                        return;\r\n                    }\r\n                    res = res.data;\r\n                    file.chunkIndex = res.chunkIndex;\r\n                    if (res.taskStatus === 3) { // 3：任务完成\r\n                        file.progress = task.progress = 100;\r\n                        file.surplusTime = task.surplusTime = null;\r\n                        delete file.fileReader;\r\n                        file.status = task.status = 'success';\r\n                        self.prepareUpload();\r\n                        callComplete(task).then(function(){\r\n                            trigger('task-upload-success', task);\r\n                        });\r\n                    } else {\r\n                        if (file.chunkIndex < file.chunkTotal) {\r\n                            task.progress = calcTaskProgress(task);\r\n                            file.progress = calcFileProgress(file);\r\n                            file.status = task.status = 'progress';\r\n                            trigger('task-upload-progress', task);\r\n                            file.errorCount = 0;\r\n                            self.upload(item);\r\n                        } else {\r\n                            if (file.hasOwnProperty('errorCount')) {\r\n                                delete uploadRetryTimer[file.fileId];\r\n                                delete file.errorCount;\r\n                            }\r\n                            file.surplusTime = null;\r\n                            file.progress = 100;\r\n                            file.status = 'success';\r\n                            task.progress = calcTaskProgress(task);\r\n                            trigger('task-upload-success', task);\r\n                            self.prepareUpload();\r\n                        }\r\n                    }\r\n                }, function (res) {\r\n                    if (res.status === 401) {\r\n                        unauthorizedTip();\r\n                        return;\r\n                    }\r\n                    uploadErrorRetry(item);\r\n                });\r\n            }\r\n        })\r\n    }\r\n\r\n    this.uploadByKsyun = function (item) {\r\n        var file = item.file;\r\n        var task = item.task;\r\n\r\n        if (task.ossClientInfo.accessKeyId && task.ossClientInfo.accessKeySecret){\r\n            Ks3.config.AK = task.ossClientInfo.accessKeyId;  //TODO： 请替换为您的AK\r\n            Ks3.config.SK = task.ossClientInfo.accessKeySecret; //TODO: 测试时请填写您的secret key  注意：前端计算signature不安全\r\n        }\r\n\r\n        Ks3.config.region = task.ossClientInfo.region;\r\n        Ks3['ENDPOINT'][`${task.ossClientInfo.region}`] = task.ossClientInfo.serviceUrl\r\n\r\n        //必须调用，不然没法用。。。\r\n        var ks3Options = {\r\n            KSSAccessKeyId: task.ossClientInfo.accessKeyId,\r\n            signature: task.ossClientInfo.accessKeySecret,\r\n            bucket_name: task.ossClientInfo.bucketName,\r\n            key: file.relativePath.replace(/\\\\/g, '/'),\r\n            acl: \"public-read\", //此处需要与policy中的acl值保持一致，默认值：private。\r\n            uploadDomain: task.ossClientInfo.serviceUrl + '/' + task.ossClientInfo.bucketName, //杭州region\r\n            autoStart: false\r\n        };\r\n        var pluploadOptions = {\r\n            drop_element: document.body,\r\n            url: task.ossClientInfo.serviceUrl\r\n        };\r\n        var tempUpload = new ks3FileUploader(ks3Options, pluploadOptions);\r\n        //必须调用，不然没法用。。。\r\n\r\n        if (file.fileSize <= (100 * 1024 * 1024)){// 小于100M文件不用分片上传\r\n            var params = {\r\n                Bucket: task.ossClientInfo.bucketName,\r\n                Key: file.relativePath.replace(/\\\\/g, '/'),\r\n                File: file.file,\r\n                ACL: 'public-read'\r\n            }\r\n            Ks3.putObject(params, function(rerr){\r\n                if (rerr) {\r\n                    console.info(rerr, rerr.error); // an error occurred\r\n                    uploadErrorRetry(item);\r\n                }\r\n                else {\r\n                    callUpload(file).then(function (res) {\r\n                        res = res.data;\r\n                        if (res.taskStatus === 3) {// 3：任务完成\r\n                            file.progress = task.progress = 100;\r\n                            file.surplusTime = task.surplusTime = null;\r\n                            delete file.fileReader;\r\n                            file.status = task.status = 'success';\r\n                            callComplete(task).then(function(){\r\n                                trigger('task-upload-success', task);\r\n                            });\r\n                        }\r\n                        else {\r\n                            file.status = 'success';\r\n                            file.progress = 100;\r\n                        }\r\n                        self.prepareUpload();\r\n                    });\r\n                }\r\n            })\r\n        }\r\n        else {\r\n            var params = {\r\n                Bucket: task.ossClientInfo.bucketName,\r\n                Key: file.relativePath.replace(/\\\\/g, '/'),\r\n                ACL: 'public-read'\r\n            }\r\n            if (!file.uploadId) {\r\n                Ks3.multitpart_upload_init(params, function(err, uploadId){\r\n                    if(err) {\r\n                        console.error(err);\r\n                    }else {\r\n                        file.uploadId = uploadId;\r\n                        this.doUploadByKsyun(item);\r\n                    }\r\n                })\r\n            }\r\n            else {\r\n                this.doUploadByKsyun(item);\r\n            }\r\n        }\r\n    }\r\n\r\n    this.uploadByS3 = function (item) {\r\n        var file = item.file;\r\n        var task = item.task;\r\n        if (!s3)\r\n            getS3Client(item.task.ossClientInfo);\r\n        if (file.chunkIndex < file.chunkTotal) {\r\n            var start = file.chunkIndex * file.chunkSize,\r\n                end = Math.min(file.fileSize, start + file.chunkSize),\r\n                form = new FormData(),\r\n                data = file.file.slice(start, end);\r\n            if (data == null || data.size == 0) {\r\n                item.task.status = item.file.status = 'error';\r\n                trigger('task-upload-error', item.task);\r\n                item.file.file = null;\r\n                self.prepareUpload();\r\n                return;\r\n            }\r\n\r\n            var uploadAfter = function (task, file) {\r\n                form.append('taskId', file.taskId);\r\n                form.append('fileId', file.fileId);\r\n                form.append('chunkIndex', file.chunkIndex);\r\n                form.append('partInfo', file.partInfo);\r\n                form.append('uploadId', file.uploadId);\r\n\r\n                // 下面还写一次是因为有2种情况，必须在这里也要删除才合理，1,初始化成功准备上传，2，上传一片后上传下一片之前\r\n                // 之所有放在构造formData之后，是因为构造formData是需要一点点时间的。避免时间差带来的偶现bug\r\n                if (task.status === 'deleteing') {\r\n                    self.clearTask(task);\r\n                    return;\r\n                }\r\n\r\n                let apiVersion = commonUtil.getCookie('apiVersion');\r\n                var url = '/upload/multipart'\r\n                if (apiVersion && apiVersion === 'mamcore2.3'){\r\n                    url = '/sflud/v1/upload/multipart'\r\n                }\r\n                if (mainOpts.loginToken){\r\n                    url += '?token=' + mainOpts.loginToken\r\n                }\r\n                httpUtil.post(getConfig().server + url, form, {\r\n                    contentType: false,\r\n                    processData: false\r\n                }).then(function (res) {\r\n                    if (task.status === 'deleteing') {\r\n                        self.clearTask(task);\r\n                        return;\r\n                    }\r\n                    res = res.data;\r\n                    file = _.find(task.files, { fileId: res.fileId });\r\n                    file.chunkIndex = res.chunkIndex;\r\n                    if (res.taskStatus === 3) { // 3：任务完成\r\n                        file.progress = task.progress = 100;\r\n                        file.surplusTime = task.surplusTime = null;\r\n                        delete file.fileReader;\r\n                        file.status = task.status = 'success';\r\n                        self.prepareUpload();\r\n                        callComplete(task).then(function(){\r\n                            trigger('task-upload-success', task);\r\n                        });\r\n                    } else {\r\n                        task.progress = calcTaskProgress(task);\r\n                        file.progress = calcFileProgress(file);\r\n                        file.status = task.status = 'progress';\r\n                        trigger('task-upload-progress', task);\r\n                        file.errorCount = 0;\r\n                        self.upload(item);\r\n                    }\r\n                }, function (res) {\r\n                    if (res.status === 401) {\r\n                        unauthorizedTip();\r\n                        return;\r\n                    }\r\n                    uploadErrorRetry(item);\r\n                });\r\n            };\r\n\r\n            if (file.fileSize <= (5 * 1024 * 1024))// 小于5M文件不用分片上传\r\n            {\r\n                s3.putObject({\r\n                    Body: file.file,\r\n                    Bucket: task.ossClientInfo.bucketName,\r\n                    Key: file.relativePath.replace(/\\\\/g, '/')\r\n                }, function (err, successData) {\r\n                    if (err) {\r\n                        console.info(err, err.stack); // an error occurred\r\n                        uploadErrorRetry(item);\r\n                    }\r\n                    else {\r\n                        if (successData.ETag)\r\n                            file.partInfo = successData.ETag.replace(/\"/g, '');\r\n                        uploadAfter(task, file);\r\n                        console.info(successData); // successful response\r\n                    }\r\n                    /*\r\n                    data = {\r\n                        ETag: \"\\\"6805f2cfc46c0f04559748bb039d69ae\\\"\",\r\n                        VersionId: \"Kirh.unyZwjQ69YxcQLA8z4F5j3kJJKr\"\r\n                    }\r\n                   */\r\n                });\r\n            } else {\r\n                if (!file.uploadId) {\r\n                    var getUploadIdTask = initS3MultipartUpload(task.ossClientInfo, file);\r\n                    if (getUploadIdTask) {\r\n                        getUploadIdTask.then(function () {\r\n                            self.upload(item);\r\n                        });\r\n                    }\r\n                    return;\r\n                }\r\n                var params = {\r\n                    Body: data,\r\n                    Bucket: task.ossClientInfo.bucketName,\r\n                    Key: file.relativePath.replace(/\\\\/g, '/'),\r\n                    PartNumber: file.chunkIndex + 1,\r\n                    UploadId: file.uploadId\r\n                };\r\n                s3.uploadPart(params, function (err, successData) {\r\n                    if (err) {\r\n                        console.info(err, err.stack); // an error occurred\r\n                        uploadErrorRetry(item);\r\n                    }\r\n                    else {\r\n                        if (successData.ETag)\r\n                            file.partInfo = successData.ETag.replace(/\"/g, '');\r\n                        uploadAfter(task, file);\r\n                        console.info(successData); // successful response\r\n                        /*\r\n                        successData = {\r\n                            ETag: \"\\\"d8c2eafd90c266e19ab9dcacc479f8af\\\"\"\r\n                       }\r\n                       */\r\n                    }\r\n                });\r\n            }\r\n        } else {\r\n            if (file.hasOwnProperty('errorCount')) {\r\n                delete uploadRetryTimer[file.fileId];\r\n                delete file.errorCount;\r\n            }\r\n            delete file.fileReader;\r\n            file.surplusTime = null;\r\n            file.progress = 100;\r\n            file.status = 'success';\r\n            task.progress = calcTaskProgress(task);\r\n            trigger('task-upload-success', task);\r\n            self.prepareUpload();\r\n        }\r\n    }\r\n\r\n    this.uploadByOos = function (item) {\r\n        var file = item.file;\r\n        var task = item.task;\r\n\r\n        if (file.chunkIndex < file.chunkTotal) {\r\n            var start = file.chunkIndex * file.chunkSize,\r\n                end = Math.min(file.fileSize, start + file.chunkSize),\r\n                form = new FormData(),\r\n                data = file.file.slice(start, end);\r\n            if (data == null || data.size == 0) {\r\n                item.task.status = item.file.status = 'error';\r\n                trigger('task-upload-error', item.task);\r\n                item.file.file = null;\r\n                self.prepareUpload();\r\n                return;\r\n            }\r\n            var uploadAfter = function (task, file) {\r\n                form.append('taskId', file.taskId);\r\n                form.append('fileId', file.fileId);\r\n                form.append('chunkIndex', file.chunkIndex);\r\n                form.append('partInfo', file.partInfo);\r\n                form.append('uploadId', file.uploadId);\r\n\r\n                // 下面还写一次是因为有2种情况，必须在这里也要删除才合理，1,初始化成功准备上传，2，上传一片后上传下一片之前\r\n                // 之所有放在构造formData之后，是因为构造formData是需要一点点时间的。避免时间差带来的偶现bug\r\n                if (task.status === 'deleteing') {\r\n                    self.clearTask(task);\r\n                    return;\r\n                }\r\n\r\n                let apiVersion = commonUtil.getCookie('apiVersion');\r\n                var url = '/upload/multipart'\r\n                if (apiVersion && apiVersion === 'mamcore2.3'){\r\n                    url = '/sflud/v1/upload/multipart'\r\n                }\r\n                if (mainOpts.loginToken){\r\n                    url += '?token=' + mainOpts.loginToken\r\n                }\r\n                httpUtil.post(url, form, {\r\n                    contentType: false,\r\n                    processData: false\r\n                }).then(function (res) {\r\n                    if (task.status === 'deleteing') {\r\n                        self.clearTask(task);\r\n                        return;\r\n                    }\r\n                    res = res.data;\r\n                    file = _.find(task.files, { fileId: res.fileId });\r\n                    file.chunkIndex = res.chunkIndex;\r\n                    if (res.taskStatus === 3) { // 3：任务完成\r\n                        file.progress = task.progress = 100;\r\n                        file.surplusTime = task.surplusTime = null;\r\n                        delete file.fileReader;\r\n                        file.status = task.status = 'success';\r\n                        self.prepareUpload();\r\n                        callComplete(task).then(function(){\r\n                            trigger('task-upload-success', task);\r\n                        });\r\n                    } else {\r\n                        task.progress = calcTaskProgress(task);\r\n                        file.progress = calcFileProgress(file);\r\n                        file.status = task.status = 'progress';\r\n                        trigger('task-upload-progress', task);\r\n                        file.errorCount = 0;\r\n                        self.upload(item);\r\n                    }\r\n                }, function (res) {\r\n                    if (res.status === 401) {\r\n                        unauthorizedTip();\r\n                        return;\r\n                    }\r\n                    uploadErrorRetry(item);\r\n                });\r\n            };\r\n            if (!s3) {\r\n                getS3Client(task.ossClientInfo);\r\n            }\r\n            if (file.fileSize <= (5 * 1024 * 1024))// 小于5M文件不用分片上传\r\n            {\r\n                s3.putObject({\r\n                    Body: file.file,\r\n                    Bucket: task.ossClientInfo.bucketName,\r\n                    Key: file.relativePath.replace(/\\\\/g, '/')\r\n                }, function (err, successData) {\r\n                    if (err) {\r\n                        console.info(err, err.stack); // an error occurred\r\n                        uploadErrorRetry(item);\r\n                    }\r\n                    else {\r\n                        if (successData.ETag)\r\n                            file.partInfo = successData.ETag.replace(/\"/g, '');\r\n                        uploadAfter(task, file);\r\n                        console.info(successData); // successful response\r\n                    }\r\n                    /*\r\n                    data = {\r\n                        ETag: \"\\\"6805f2cfc46c0f04559748bb039d69ae\\\"\",\r\n                        VersionId: \"Kirh.unyZwjQ69YxcQLA8z4F5j3kJJKr\"\r\n                    }\r\n                   */\r\n                });\r\n            } else {\r\n                if (!file.uploadId) {\r\n                    var getUploadIdTask = initS3MultipartUpload(task.ossClientInfo, file);\r\n                    if (getUploadIdTask) {\r\n                        getUploadIdTask.then(function () {\r\n                            self.upload(item);\r\n                        });\r\n                    }\r\n                    return;\r\n                }\r\n                var params = {\r\n                    Body: data,\r\n                    Bucket: task.ossClientInfo.bucketName,\r\n                    Key: file.relativePath.replace(/\\\\/g, '/'),\r\n                    PartNumber: file.chunkIndex + 1,\r\n                    UploadId: file.uploadId\r\n                };\r\n                s3.uploadPart(params, function (err, successData) {\r\n                    if (err) {\r\n                        console.info(err, err.stack); // an error occurred\r\n                        uploadErrorRetry(item);\r\n                    }\r\n                    else {\r\n                        // if (params.PartNumber == file.chunkTotal) {\r\n                        //     s3.completeMultipartUpload({ Bucket: params.Bucket, Key: params.Key, UploadId: params.UploadId }, function (err, data) {\r\n                        //         if (err) {\r\n                        //             console.info(err, err.stack); // an error occurred\r\n                        //             uploadErrorRetry(item);\r\n                        //         } else {\r\n                        //             console.log('upload complete', data);\r\n                        //             uploadAfter(task, file);\r\n                        //         }\r\n                        //     });\r\n                        // } else {\r\n                        if (successData.ETag)\r\n                            file.partInfo = successData.ETag.replace(/\"/g, '');\r\n                        uploadAfter(task, file);\r\n                        console.info(successData); // successful response\r\n                        // }\r\n                    }\r\n                });\r\n            }\r\n        } else {\r\n            if (file.hasOwnProperty('errorCount')) {\r\n                delete uploadRetryTimer[file.fileId];\r\n                delete file.errorCount;\r\n            }\r\n            delete file.fileReader;\r\n            file.surplusTime = null;\r\n            file.progress = 100;\r\n            file.status = 'success';\r\n            task.progress = calcTaskProgress(task);\r\n            trigger('task-upload-success', task);\r\n            self.prepareUpload();\r\n        }\r\n    }\r\n\r\n    //继续上传\r\n    this.continueUpload = function (task, file, errCallback) {\r\n        if (file.file == null)\r\n            self.openFileSelector(function (result) {\r\n                var md5 = self.calcFileMd5(result[0].file);\r\n                if (file.fileMd5 !== md5) {\r\n                    commonUtil.prompt(l('upload.fileDiffer', '选择的文件不一致，请重新上传'));\r\n                    if (errCallback) {\r\n                        errCallback.apply(window, [l('upload.fileDiffer', '选择的文件不一致，请重新上传')]);\r\n                    }\r\n                    return;\r\n                }\r\n                file.file = result[0].file;\r\n                task.status = file.status = 'prepared';\r\n                self.prepareUpload();\r\n            }, false);\r\n        else {\r\n            if (task.inited) {\r\n                task.status = file.status = 'prepared';\r\n                self.prepareUpload();\r\n            } else {\r\n                initTask(task);\r\n            }\r\n        }\r\n    }\r\n\r\n    //获取未完成需要断点续传的任务\r\n    this.getUnfinishedTask = function (relationId, relationContentType, targetType) {\r\n        var deferred = $.Deferred();\r\n        if (relationId == null)\r\n            relationId = '';\r\n        if (!_.isNumber(relationContentType))\r\n            relationContentType = 0;\r\n        if (!_.isNumber(relationContentType))\r\n            relationContentType = 1;\r\n        let apiVersion = commonUtil.getCookie('apiVersion');\r\n        var url = '/upload/get-unfinished-task?relationId=' + relationId + '&relationContentType=' + relationContentType + '&targetType=' + targetType + (mainOpts.loginToken ? '&token=' + mainOpts.loginToken : '')\r\n        if (apiVersion && apiVersion === 'mamcore2.3'){\r\n             url = '/sflud/v1/upload/unfinished-task?relationId=' + relationId + '&relationContentType=' + relationContentType + '&targetType=' + targetType + (mainOpts.loginToken ? '&token=' + mainOpts.loginToken : '')\r\n        }\r\n        httpUtil.get(getConfig().server + url).then(function (res) {\r\n            var result = [];\r\n            _.forEach(res.data, function (task) {\r\n                task.status = 'error';\r\n                task.inited = true;\r\n                task.sizeTotal = task.chunkFinished = task.chunkTotal = 0;\r\n                _.forEach(task.files, function (file) {\r\n                    file.fileSizeString = commonUtil.formatSize(file.fileSize);\r\n                    file.progress = calcFileProgress(file);\r\n                    file.status = file.progress === 100 ? 'success' : 'error';\r\n                    task.sizeTotal += file.fileSize;\r\n                    task.chunkTotal += file.chunkTotal;\r\n                });\r\n                task.progress = calcTaskProgress(task);\r\n                task.sizeTotalString = commonUtil.formatSize(task.sizeTotal);\r\n                self.tasks.push(task);\r\n                result.push(task);\r\n            });\r\n            deferred.resolve(result);\r\n        }, function (res) {\r\n            deferred.reject(res);\r\n        });\r\n        return deferred;\r\n    }\r\n\r\n    //是否能删除任务\r\n    this.canDeleteTask = function (task) {\r\n        if (task == null)\r\n            return false;\r\n        if (task.status == 'init')\r\n            return false;\r\n        if (task.status == 'deleteing')\r\n            return false;\r\n        if (task.status == 'progress' && task.chunkFinished == (task.chunkTotal - 1))\r\n            return false;\r\n        return true;\r\n    }\r\n\r\n    this.removeTask = function (task) {\r\n        _.remove(self.tasks, function (ta) {\r\n            return ta.taskId === task.taskId;\r\n        });\r\n    }\r\n\r\n    //删除任务\r\n    this.deleteTask = function (task) {\r\n        if (!this.canDeleteTask(task))\r\n            return;\r\n        if (task.inited === true) {\r\n            _.forEach(task.files, function (file) {\r\n                if (uploadRetryTimer[file.fileId] != null) {\r\n                    timeout.cancel(uploadRetryTimer[file.fileId]);\r\n                    delete uploadRetryTimer[file.fileId];\r\n                }\r\n            });\r\n            switch (task.status) {\r\n                case 'progress':\r\n                    task.status = 'deleteing';\r\n                    return;\r\n                case 'prepared':\r\n                case 'error':\r\n                    self.clearTask(task);\r\n                    return;\r\n                default:\r\n                    self.removeTask(task);\r\n                    trigger('task-delete-success', task);\r\n            }\r\n        } else {\r\n            self.removeTask(task);\r\n            trigger('task-delete-success', task);\r\n        }\r\n    }\r\n\r\n    //清理任务\r\n    this.clearTask = function (task) {\r\n        var fileAndTag = {};\r\n        if (task.files && task.files.length > 0) {\r\n            _.forEach(task.files, function (file) {\r\n                fileAndTag[file.fileId] = file.uploadId;\r\n            });\r\n        }\r\n        let apiVersion = commonUtil.getCookie('apiVersion');\r\n        var url = '/upload/delete-task'\r\n        if (apiVersion && apiVersion === 'mamcore2.3'){\r\n             url = '/sflud/v1/upload/task'\r\n        }\r\n        if (mainOpts.loginToken){\r\n            url += '?token=' + mainOpts.loginToken\r\n        }\r\n        if (!apiVersion || apiVersion === 'mamcore2.1'){\r\n            httpUtil.post(getConfig().server + url, { taskId: task.taskId, fileAndTag: fileAndTag }).then(function (res) {\r\n                self.removeTask(task);\r\n                trigger('task-delete-success', task);\r\n                self.prepareUpload();\r\n            }, function (res) {\r\n                trigger('task-delete-error', [task, res]);\r\n            });\r\n        }\r\n        else{\r\n            httpUtil.delete(getConfig().server + url, { taskId: task.taskId, fileAndTag: fileAndTag }).then(function (res) {\r\n                self.removeTask(task);\r\n                trigger('task-delete-success', task);\r\n                self.prepareUpload();\r\n            }, function (res) {\r\n                trigger('task-delete-error', [task, res]);\r\n            });\r\n        }\r\n    }\r\n\r\n    //计算文件MD5\r\n    this.calcFileMd5 = function (file) {\r\n        //只是简单计算一下，没办法整个文件进行MD5计算。现在这种方式肯定是会有bug的。只能应对大部分情况。\r\n        return sparkmd5.hash(file.name + file.size + getFileTime(file));\r\n    }\r\n\r\n    //获取一个任务\r\n    //this.getTask = function (taskId) {\r\n    //    return _.find(tasks, function (n) { return n.taskId === taskId });\r\n    //}\r\n\r\n    //根据状态获取文件，参数可传入多个状态code\r\n    this.getFilesByStatus = function () {\r\n        var status = [].slice.call(arguments, 0);\r\n        var result = [];\r\n        for (var x = 0; x < self.tasks.length; x++) {\r\n            for (var y = 0; y < self.tasks[x].files.length; y++) {\r\n                for (var z = 0; z < status.length; z++) {\r\n                    if (self.tasks[x].files[y].status === status[z]) {\r\n                        result.push({ task: self.tasks[x], file: self.tasks[x].files[y] });\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        return result;\r\n    }\r\n\r\n    //计算进度\r\n    function calcProgress(current, total) {\r\n        if (current == total)\r\n            return 100;\r\n        var result = ((current / total) * 100);\r\n        if (result.toString().indexOf('.') == -1)\r\n            return result;\r\n        return result.toFixed(2);\r\n    }\r\n\r\n    //计算文件进度\r\n    function calcFileProgress(file) {\r\n        return calcProgress(file.chunkIndex, file.chunkTotal);\r\n    }\r\n\r\n    //计算任务进度\r\n    function calcTaskProgress(task) {\r\n        var count = 0;\r\n        for (var i = 0; i < task.files.length; i++)\r\n            count += task.files[i].chunkIndex;\r\n        task.chunkFinished = count;\r\n        return calcProgress(task.chunkFinished, task.chunkTotal);\r\n    }\r\n\r\n    init();\r\n}\r\n\r\nexport default web;\r\n\n\n\n// WEBPACK FOOTER //\n// ./src/core/webTransfer.js", "/*!\n * The buffer module from node.js, for the browser.\n *\n * <AUTHOR> <http://feross.org>\n * @license  MIT\n */\n/* eslint-disable no-proto */\n\n'use strict'\n\nvar base64 = require('base64-js')\nvar ieee754 = require('ieee754')\nvar isArray = require('isarray')\n\nexports.Buffer = Buffer\nexports.SlowBuffer = SlowBuffer\nexports.INSPECT_MAX_BYTES = 50\n\n/**\n * If `Buffer.TYPED_ARRAY_SUPPORT`:\n *   === true    Use Uint8Array implementation (fastest)\n *   === false   Use Object implementation (most compatible, even IE6)\n *\n * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,\n * Opera 11.6+, iOS 4.2+.\n *\n * Due to various browser bugs, sometimes the Object implementation will be used even\n * when the browser supports typed arrays.\n *\n * Note:\n *\n *   - Firefox 4-29 lacks support for adding new properties to `Uint8Array` instances,\n *     See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438.\n *\n *   - Chrome 9-10 is missing the `TypedArray.prototype.subarray` function.\n *\n *   - <PERSON><PERSON><PERSON> has a broken `TypedArray.prototype.subarray` function which returns arrays of\n *     incorrect length in some situations.\n\n * We detect these buggy browsers and set `Buffer.TYPED_ARRAY_SUPPORT` to `false` so they\n * get the Object implementation, which is slower but behaves correctly.\n */\nBuffer.TYPED_ARRAY_SUPPORT = global.TYPED_ARRAY_SUPPORT !== undefined\n  ? global.TYPED_ARRAY_SUPPORT\n  : typedArraySupport()\n\n/*\n * Export kMaxLength after typed array support is determined.\n */\nexports.kMaxLength = kMaxLength()\n\nfunction typedArraySupport () {\n  try {\n    var arr = new Uint8Array(1)\n    arr.__proto__ = {__proto__: Uint8Array.prototype, foo: function () { return 42 }}\n    return arr.foo() === 42 && // typed array instances can be augmented\n        typeof arr.subarray === 'function' && // chrome 9-10 lack `subarray`\n        arr.subarray(1, 1).byteLength === 0 // ie10 has broken `subarray`\n  } catch (e) {\n    return false\n  }\n}\n\nfunction kMaxLength () {\n  return Buffer.TYPED_ARRAY_SUPPORT\n    ? 0x7fffffff\n    : 0x3fffffff\n}\n\nfunction createBuffer (that, length) {\n  if (kMaxLength() < length) {\n    throw new RangeError('Invalid typed array length')\n  }\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = new Uint8Array(length)\n    that.__proto__ = Buffer.prototype\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    if (that === null) {\n      that = new Buffer(length)\n    }\n    that.length = length\n  }\n\n  return that\n}\n\n/**\n * The Buffer constructor returns instances of `Uint8Array` that have their\n * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of\n * `Uint8Array`, so the returned instances will have all the node `Buffer` methods\n * and the `Uint8Array` methods. Square bracket notation works as expected -- it\n * returns a single octet.\n *\n * The `Uint8Array` prototype remains unmodified.\n */\n\nfunction Buffer (arg, encodingOrOffset, length) {\n  if (!Buffer.TYPED_ARRAY_SUPPORT && !(this instanceof Buffer)) {\n    return new Buffer(arg, encodingOrOffset, length)\n  }\n\n  // Common case.\n  if (typeof arg === 'number') {\n    if (typeof encodingOrOffset === 'string') {\n      throw new Error(\n        'If encoding is specified then the first argument must be a string'\n      )\n    }\n    return allocUnsafe(this, arg)\n  }\n  return from(this, arg, encodingOrOffset, length)\n}\n\nBuffer.poolSize = 8192 // not used by this implementation\n\n// TODO: Legacy, not needed anymore. Remove in next major version.\nBuffer._augment = function (arr) {\n  arr.__proto__ = Buffer.prototype\n  return arr\n}\n\nfunction from (that, value, encodingOrOffset, length) {\n  if (typeof value === 'number') {\n    throw new TypeError('\"value\" argument must not be a number')\n  }\n\n  if (typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer) {\n    return fromArrayBuffer(that, value, encodingOrOffset, length)\n  }\n\n  if (typeof value === 'string') {\n    return fromString(that, value, encodingOrOffset)\n  }\n\n  return fromObject(that, value)\n}\n\n/**\n * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError\n * if value is a number.\n * Buffer.from(str[, encoding])\n * Buffer.from(array)\n * Buffer.from(buffer)\n * Buffer.from(arrayBuffer[, byteOffset[, length]])\n **/\nBuffer.from = function (value, encodingOrOffset, length) {\n  return from(null, value, encodingOrOffset, length)\n}\n\nif (Buffer.TYPED_ARRAY_SUPPORT) {\n  Buffer.prototype.__proto__ = Uint8Array.prototype\n  Buffer.__proto__ = Uint8Array\n  if (typeof Symbol !== 'undefined' && Symbol.species &&\n      Buffer[Symbol.species] === Buffer) {\n    // Fix subarray() in ES2016. See: https://github.com/feross/buffer/pull/97\n    Object.defineProperty(Buffer, Symbol.species, {\n      value: null,\n      configurable: true\n    })\n  }\n}\n\nfunction assertSize (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('\"size\" argument must be a number')\n  } else if (size < 0) {\n    throw new RangeError('\"size\" argument must not be negative')\n  }\n}\n\nfunction alloc (that, size, fill, encoding) {\n  assertSize(size)\n  if (size <= 0) {\n    return createBuffer(that, size)\n  }\n  if (fill !== undefined) {\n    // Only pay attention to encoding if it's a string. This\n    // prevents accidentally sending in a number that would\n    // be interpretted as a start offset.\n    return typeof encoding === 'string'\n      ? createBuffer(that, size).fill(fill, encoding)\n      : createBuffer(that, size).fill(fill)\n  }\n  return createBuffer(that, size)\n}\n\n/**\n * Creates a new filled Buffer instance.\n * alloc(size[, fill[, encoding]])\n **/\nBuffer.alloc = function (size, fill, encoding) {\n  return alloc(null, size, fill, encoding)\n}\n\nfunction allocUnsafe (that, size) {\n  assertSize(size)\n  that = createBuffer(that, size < 0 ? 0 : checked(size) | 0)\n  if (!Buffer.TYPED_ARRAY_SUPPORT) {\n    for (var i = 0; i < size; ++i) {\n      that[i] = 0\n    }\n  }\n  return that\n}\n\n/**\n * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.\n * */\nBuffer.allocUnsafe = function (size) {\n  return allocUnsafe(null, size)\n}\n/**\n * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.\n */\nBuffer.allocUnsafeSlow = function (size) {\n  return allocUnsafe(null, size)\n}\n\nfunction fromString (that, string, encoding) {\n  if (typeof encoding !== 'string' || encoding === '') {\n    encoding = 'utf8'\n  }\n\n  if (!Buffer.isEncoding(encoding)) {\n    throw new TypeError('\"encoding\" must be a valid string encoding')\n  }\n\n  var length = byteLength(string, encoding) | 0\n  that = createBuffer(that, length)\n\n  var actual = that.write(string, encoding)\n\n  if (actual !== length) {\n    // Writing a hex string, for example, that contains invalid characters will\n    // cause everything after the first invalid character to be ignored. (e.g.\n    // 'abxxcd' will be treated as 'ab')\n    that = that.slice(0, actual)\n  }\n\n  return that\n}\n\nfunction fromArrayLike (that, array) {\n  var length = array.length < 0 ? 0 : checked(array.length) | 0\n  that = createBuffer(that, length)\n  for (var i = 0; i < length; i += 1) {\n    that[i] = array[i] & 255\n  }\n  return that\n}\n\nfunction fromArrayBuffer (that, array, byteOffset, length) {\n  array.byteLength // this throws if `array` is not a valid ArrayBuffer\n\n  if (byteOffset < 0 || array.byteLength < byteOffset) {\n    throw new RangeError('\\'offset\\' is out of bounds')\n  }\n\n  if (array.byteLength < byteOffset + (length || 0)) {\n    throw new RangeError('\\'length\\' is out of bounds')\n  }\n\n  if (byteOffset === undefined && length === undefined) {\n    array = new Uint8Array(array)\n  } else if (length === undefined) {\n    array = new Uint8Array(array, byteOffset)\n  } else {\n    array = new Uint8Array(array, byteOffset, length)\n  }\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = array\n    that.__proto__ = Buffer.prototype\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    that = fromArrayLike(that, array)\n  }\n  return that\n}\n\nfunction fromObject (that, obj) {\n  if (Buffer.isBuffer(obj)) {\n    var len = checked(obj.length) | 0\n    that = createBuffer(that, len)\n\n    if (that.length === 0) {\n      return that\n    }\n\n    obj.copy(that, 0, 0, len)\n    return that\n  }\n\n  if (obj) {\n    if ((typeof ArrayBuffer !== 'undefined' &&\n        obj.buffer instanceof ArrayBuffer) || 'length' in obj) {\n      if (typeof obj.length !== 'number' || isnan(obj.length)) {\n        return createBuffer(that, 0)\n      }\n      return fromArrayLike(that, obj)\n    }\n\n    if (obj.type === 'Buffer' && isArray(obj.data)) {\n      return fromArrayLike(that, obj.data)\n    }\n  }\n\n  throw new TypeError('First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.')\n}\n\nfunction checked (length) {\n  // Note: cannot use `length < kMaxLength()` here because that fails when\n  // length is NaN (which is otherwise coerced to zero.)\n  if (length >= kMaxLength()) {\n    throw new RangeError('Attempt to allocate Buffer larger than maximum ' +\n                         'size: 0x' + kMaxLength().toString(16) + ' bytes')\n  }\n  return length | 0\n}\n\nfunction SlowBuffer (length) {\n  if (+length != length) { // eslint-disable-line eqeqeq\n    length = 0\n  }\n  return Buffer.alloc(+length)\n}\n\nBuffer.isBuffer = function isBuffer (b) {\n  return !!(b != null && b._isBuffer)\n}\n\nBuffer.compare = function compare (a, b) {\n  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {\n    throw new TypeError('Arguments must be Buffers')\n  }\n\n  if (a === b) return 0\n\n  var x = a.length\n  var y = b.length\n\n  for (var i = 0, len = Math.min(x, y); i < len; ++i) {\n    if (a[i] !== b[i]) {\n      x = a[i]\n      y = b[i]\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n}\n\nBuffer.isEncoding = function isEncoding (encoding) {\n  switch (String(encoding).toLowerCase()) {\n    case 'hex':\n    case 'utf8':\n    case 'utf-8':\n    case 'ascii':\n    case 'latin1':\n    case 'binary':\n    case 'base64':\n    case 'ucs2':\n    case 'ucs-2':\n    case 'utf16le':\n    case 'utf-16le':\n      return true\n    default:\n      return false\n  }\n}\n\nBuffer.concat = function concat (list, length) {\n  if (!isArray(list)) {\n    throw new TypeError('\"list\" argument must be an Array of Buffers')\n  }\n\n  if (list.length === 0) {\n    return Buffer.alloc(0)\n  }\n\n  var i\n  if (length === undefined) {\n    length = 0\n    for (i = 0; i < list.length; ++i) {\n      length += list[i].length\n    }\n  }\n\n  var buffer = Buffer.allocUnsafe(length)\n  var pos = 0\n  for (i = 0; i < list.length; ++i) {\n    var buf = list[i]\n    if (!Buffer.isBuffer(buf)) {\n      throw new TypeError('\"list\" argument must be an Array of Buffers')\n    }\n    buf.copy(buffer, pos)\n    pos += buf.length\n  }\n  return buffer\n}\n\nfunction byteLength (string, encoding) {\n  if (Buffer.isBuffer(string)) {\n    return string.length\n  }\n  if (typeof ArrayBuffer !== 'undefined' && typeof ArrayBuffer.isView === 'function' &&\n      (ArrayBuffer.isView(string) || string instanceof ArrayBuffer)) {\n    return string.byteLength\n  }\n  if (typeof string !== 'string') {\n    string = '' + string\n  }\n\n  var len = string.length\n  if (len === 0) return 0\n\n  // Use a for loop to avoid recursion\n  var loweredCase = false\n  for (;;) {\n    switch (encoding) {\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return len\n      case 'utf8':\n      case 'utf-8':\n      case undefined:\n        return utf8ToBytes(string).length\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return len * 2\n      case 'hex':\n        return len >>> 1\n      case 'base64':\n        return base64ToBytes(string).length\n      default:\n        if (loweredCase) return utf8ToBytes(string).length // assume utf8\n        encoding = ('' + encoding).toLowerCase()\n        loweredCase = true\n    }\n  }\n}\nBuffer.byteLength = byteLength\n\nfunction slowToString (encoding, start, end) {\n  var loweredCase = false\n\n  // No need to verify that \"this.length <= MAX_UINT32\" since it's a read-only\n  // property of a typed array.\n\n  // This behaves neither like String nor Uint8Array in that we set start/end\n  // to their upper/lower bounds if the value passed is out of range.\n  // undefined is handled specially as per ECMA-262 6th Edition,\n  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.\n  if (start === undefined || start < 0) {\n    start = 0\n  }\n  // Return early if start > this.length. Done here to prevent potential uint32\n  // coercion fail below.\n  if (start > this.length) {\n    return ''\n  }\n\n  if (end === undefined || end > this.length) {\n    end = this.length\n  }\n\n  if (end <= 0) {\n    return ''\n  }\n\n  // Force coersion to uint32. This will also coerce falsey/NaN values to 0.\n  end >>>= 0\n  start >>>= 0\n\n  if (end <= start) {\n    return ''\n  }\n\n  if (!encoding) encoding = 'utf8'\n\n  while (true) {\n    switch (encoding) {\n      case 'hex':\n        return hexSlice(this, start, end)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Slice(this, start, end)\n\n      case 'ascii':\n        return asciiSlice(this, start, end)\n\n      case 'latin1':\n      case 'binary':\n        return latin1Slice(this, start, end)\n\n      case 'base64':\n        return base64Slice(this, start, end)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return utf16leSlice(this, start, end)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = (encoding + '').toLowerCase()\n        loweredCase = true\n    }\n  }\n}\n\n// The property is used by `Buffer.isBuffer` and `is-buffer` (in Safari 5-7) to detect\n// Buffer instances.\nBuffer.prototype._isBuffer = true\n\nfunction swap (b, n, m) {\n  var i = b[n]\n  b[n] = b[m]\n  b[m] = i\n}\n\nBuffer.prototype.swap16 = function swap16 () {\n  var len = this.length\n  if (len % 2 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 16-bits')\n  }\n  for (var i = 0; i < len; i += 2) {\n    swap(this, i, i + 1)\n  }\n  return this\n}\n\nBuffer.prototype.swap32 = function swap32 () {\n  var len = this.length\n  if (len % 4 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 32-bits')\n  }\n  for (var i = 0; i < len; i += 4) {\n    swap(this, i, i + 3)\n    swap(this, i + 1, i + 2)\n  }\n  return this\n}\n\nBuffer.prototype.swap64 = function swap64 () {\n  var len = this.length\n  if (len % 8 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 64-bits')\n  }\n  for (var i = 0; i < len; i += 8) {\n    swap(this, i, i + 7)\n    swap(this, i + 1, i + 6)\n    swap(this, i + 2, i + 5)\n    swap(this, i + 3, i + 4)\n  }\n  return this\n}\n\nBuffer.prototype.toString = function toString () {\n  var length = this.length | 0\n  if (length === 0) return ''\n  if (arguments.length === 0) return utf8Slice(this, 0, length)\n  return slowToString.apply(this, arguments)\n}\n\nBuffer.prototype.equals = function equals (b) {\n  if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer')\n  if (this === b) return true\n  return Buffer.compare(this, b) === 0\n}\n\nBuffer.prototype.inspect = function inspect () {\n  var str = ''\n  var max = exports.INSPECT_MAX_BYTES\n  if (this.length > 0) {\n    str = this.toString('hex', 0, max).match(/.{2}/g).join(' ')\n    if (this.length > max) str += ' ... '\n  }\n  return '<Buffer ' + str + '>'\n}\n\nBuffer.prototype.compare = function compare (target, start, end, thisStart, thisEnd) {\n  if (!Buffer.isBuffer(target)) {\n    throw new TypeError('Argument must be a Buffer')\n  }\n\n  if (start === undefined) {\n    start = 0\n  }\n  if (end === undefined) {\n    end = target ? target.length : 0\n  }\n  if (thisStart === undefined) {\n    thisStart = 0\n  }\n  if (thisEnd === undefined) {\n    thisEnd = this.length\n  }\n\n  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {\n    throw new RangeError('out of range index')\n  }\n\n  if (thisStart >= thisEnd && start >= end) {\n    return 0\n  }\n  if (thisStart >= thisEnd) {\n    return -1\n  }\n  if (start >= end) {\n    return 1\n  }\n\n  start >>>= 0\n  end >>>= 0\n  thisStart >>>= 0\n  thisEnd >>>= 0\n\n  if (this === target) return 0\n\n  var x = thisEnd - thisStart\n  var y = end - start\n  var len = Math.min(x, y)\n\n  var thisCopy = this.slice(thisStart, thisEnd)\n  var targetCopy = target.slice(start, end)\n\n  for (var i = 0; i < len; ++i) {\n    if (thisCopy[i] !== targetCopy[i]) {\n      x = thisCopy[i]\n      y = targetCopy[i]\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n}\n\n// Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,\n// OR the last index of `val` in `buffer` at offset <= `byteOffset`.\n//\n// Arguments:\n// - buffer - a Buffer to search\n// - val - a string, Buffer, or number\n// - byteOffset - an index into `buffer`; will be clamped to an int32\n// - encoding - an optional encoding, relevant is val is a string\n// - dir - true for indexOf, false for lastIndexOf\nfunction bidirectionalIndexOf (buffer, val, byteOffset, encoding, dir) {\n  // Empty buffer means no match\n  if (buffer.length === 0) return -1\n\n  // Normalize byteOffset\n  if (typeof byteOffset === 'string') {\n    encoding = byteOffset\n    byteOffset = 0\n  } else if (byteOffset > 0x7fffffff) {\n    byteOffset = 0x7fffffff\n  } else if (byteOffset < -0x80000000) {\n    byteOffset = -0x80000000\n  }\n  byteOffset = +byteOffset  // Coerce to Number.\n  if (isNaN(byteOffset)) {\n    // byteOffset: it it's undefined, null, NaN, \"foo\", etc, search whole buffer\n    byteOffset = dir ? 0 : (buffer.length - 1)\n  }\n\n  // Normalize byteOffset: negative offsets start from the end of the buffer\n  if (byteOffset < 0) byteOffset = buffer.length + byteOffset\n  if (byteOffset >= buffer.length) {\n    if (dir) return -1\n    else byteOffset = buffer.length - 1\n  } else if (byteOffset < 0) {\n    if (dir) byteOffset = 0\n    else return -1\n  }\n\n  // Normalize val\n  if (typeof val === 'string') {\n    val = Buffer.from(val, encoding)\n  }\n\n  // Finally, search either indexOf (if dir is true) or lastIndexOf\n  if (Buffer.isBuffer(val)) {\n    // Special case: looking for empty string/buffer always fails\n    if (val.length === 0) {\n      return -1\n    }\n    return arrayIndexOf(buffer, val, byteOffset, encoding, dir)\n  } else if (typeof val === 'number') {\n    val = val & 0xFF // Search for a byte value [0-255]\n    if (Buffer.TYPED_ARRAY_SUPPORT &&\n        typeof Uint8Array.prototype.indexOf === 'function') {\n      if (dir) {\n        return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset)\n      } else {\n        return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset)\n      }\n    }\n    return arrayIndexOf(buffer, [ val ], byteOffset, encoding, dir)\n  }\n\n  throw new TypeError('val must be string, number or Buffer')\n}\n\nfunction arrayIndexOf (arr, val, byteOffset, encoding, dir) {\n  var indexSize = 1\n  var arrLength = arr.length\n  var valLength = val.length\n\n  if (encoding !== undefined) {\n    encoding = String(encoding).toLowerCase()\n    if (encoding === 'ucs2' || encoding === 'ucs-2' ||\n        encoding === 'utf16le' || encoding === 'utf-16le') {\n      if (arr.length < 2 || val.length < 2) {\n        return -1\n      }\n      indexSize = 2\n      arrLength /= 2\n      valLength /= 2\n      byteOffset /= 2\n    }\n  }\n\n  function read (buf, i) {\n    if (indexSize === 1) {\n      return buf[i]\n    } else {\n      return buf.readUInt16BE(i * indexSize)\n    }\n  }\n\n  var i\n  if (dir) {\n    var foundIndex = -1\n    for (i = byteOffset; i < arrLength; i++) {\n      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {\n        if (foundIndex === -1) foundIndex = i\n        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize\n      } else {\n        if (foundIndex !== -1) i -= i - foundIndex\n        foundIndex = -1\n      }\n    }\n  } else {\n    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength\n    for (i = byteOffset; i >= 0; i--) {\n      var found = true\n      for (var j = 0; j < valLength; j++) {\n        if (read(arr, i + j) !== read(val, j)) {\n          found = false\n          break\n        }\n      }\n      if (found) return i\n    }\n  }\n\n  return -1\n}\n\nBuffer.prototype.includes = function includes (val, byteOffset, encoding) {\n  return this.indexOf(val, byteOffset, encoding) !== -1\n}\n\nBuffer.prototype.indexOf = function indexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, true)\n}\n\nBuffer.prototype.lastIndexOf = function lastIndexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, false)\n}\n\nfunction hexWrite (buf, string, offset, length) {\n  offset = Number(offset) || 0\n  var remaining = buf.length - offset\n  if (!length) {\n    length = remaining\n  } else {\n    length = Number(length)\n    if (length > remaining) {\n      length = remaining\n    }\n  }\n\n  // must be an even number of digits\n  var strLen = string.length\n  if (strLen % 2 !== 0) throw new TypeError('Invalid hex string')\n\n  if (length > strLen / 2) {\n    length = strLen / 2\n  }\n  for (var i = 0; i < length; ++i) {\n    var parsed = parseInt(string.substr(i * 2, 2), 16)\n    if (isNaN(parsed)) return i\n    buf[offset + i] = parsed\n  }\n  return i\n}\n\nfunction utf8Write (buf, string, offset, length) {\n  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nfunction asciiWrite (buf, string, offset, length) {\n  return blitBuffer(asciiToBytes(string), buf, offset, length)\n}\n\nfunction latin1Write (buf, string, offset, length) {\n  return asciiWrite(buf, string, offset, length)\n}\n\nfunction base64Write (buf, string, offset, length) {\n  return blitBuffer(base64ToBytes(string), buf, offset, length)\n}\n\nfunction ucs2Write (buf, string, offset, length) {\n  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nBuffer.prototype.write = function write (string, offset, length, encoding) {\n  // Buffer#write(string)\n  if (offset === undefined) {\n    encoding = 'utf8'\n    length = this.length\n    offset = 0\n  // Buffer#write(string, encoding)\n  } else if (length === undefined && typeof offset === 'string') {\n    encoding = offset\n    length = this.length\n    offset = 0\n  // Buffer#write(string, offset[, length][, encoding])\n  } else if (isFinite(offset)) {\n    offset = offset | 0\n    if (isFinite(length)) {\n      length = length | 0\n      if (encoding === undefined) encoding = 'utf8'\n    } else {\n      encoding = length\n      length = undefined\n    }\n  // legacy write(string, encoding, offset, length) - remove in v0.13\n  } else {\n    throw new Error(\n      'Buffer.write(string, encoding, offset[, length]) is no longer supported'\n    )\n  }\n\n  var remaining = this.length - offset\n  if (length === undefined || length > remaining) length = remaining\n\n  if ((string.length > 0 && (length < 0 || offset < 0)) || offset > this.length) {\n    throw new RangeError('Attempt to write outside buffer bounds')\n  }\n\n  if (!encoding) encoding = 'utf8'\n\n  var loweredCase = false\n  for (;;) {\n    switch (encoding) {\n      case 'hex':\n        return hexWrite(this, string, offset, length)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Write(this, string, offset, length)\n\n      case 'ascii':\n        return asciiWrite(this, string, offset, length)\n\n      case 'latin1':\n      case 'binary':\n        return latin1Write(this, string, offset, length)\n\n      case 'base64':\n        // Warning: maxLength not taken into account in base64Write\n        return base64Write(this, string, offset, length)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return ucs2Write(this, string, offset, length)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = ('' + encoding).toLowerCase()\n        loweredCase = true\n    }\n  }\n}\n\nBuffer.prototype.toJSON = function toJSON () {\n  return {\n    type: 'Buffer',\n    data: Array.prototype.slice.call(this._arr || this, 0)\n  }\n}\n\nfunction base64Slice (buf, start, end) {\n  if (start === 0 && end === buf.length) {\n    return base64.fromByteArray(buf)\n  } else {\n    return base64.fromByteArray(buf.slice(start, end))\n  }\n}\n\nfunction utf8Slice (buf, start, end) {\n  end = Math.min(buf.length, end)\n  var res = []\n\n  var i = start\n  while (i < end) {\n    var firstByte = buf[i]\n    var codePoint = null\n    var bytesPerSequence = (firstByte > 0xEF) ? 4\n      : (firstByte > 0xDF) ? 3\n      : (firstByte > 0xBF) ? 2\n      : 1\n\n    if (i + bytesPerSequence <= end) {\n      var secondByte, thirdByte, fourthByte, tempCodePoint\n\n      switch (bytesPerSequence) {\n        case 1:\n          if (firstByte < 0x80) {\n            codePoint = firstByte\n          }\n          break\n        case 2:\n          secondByte = buf[i + 1]\n          if ((secondByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0x1F) << 0x6 | (secondByte & 0x3F)\n            if (tempCodePoint > 0x7F) {\n              codePoint = tempCodePoint\n            }\n          }\n          break\n        case 3:\n          secondByte = buf[i + 1]\n          thirdByte = buf[i + 2]\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | (thirdByte & 0x3F)\n            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {\n              codePoint = tempCodePoint\n            }\n          }\n          break\n        case 4:\n          secondByte = buf[i + 1]\n          thirdByte = buf[i + 2]\n          fourthByte = buf[i + 3]\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | (fourthByte & 0x3F)\n            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {\n              codePoint = tempCodePoint\n            }\n          }\n      }\n    }\n\n    if (codePoint === null) {\n      // we did not generate a valid codePoint so insert a\n      // replacement char (U+FFFD) and advance only 1 byte\n      codePoint = 0xFFFD\n      bytesPerSequence = 1\n    } else if (codePoint > 0xFFFF) {\n      // encode to utf16 (surrogate pair dance)\n      codePoint -= 0x10000\n      res.push(codePoint >>> 10 & 0x3FF | 0xD800)\n      codePoint = 0xDC00 | codePoint & 0x3FF\n    }\n\n    res.push(codePoint)\n    i += bytesPerSequence\n  }\n\n  return decodeCodePointsArray(res)\n}\n\n// Based on http://stackoverflow.com/a/22747272/680742, the browser with\n// the lowest limit is Chrome, with 0x10000 args.\n// We go 1 magnitude less, for safety\nvar MAX_ARGUMENTS_LENGTH = 0x1000\n\nfunction decodeCodePointsArray (codePoints) {\n  var len = codePoints.length\n  if (len <= MAX_ARGUMENTS_LENGTH) {\n    return String.fromCharCode.apply(String, codePoints) // avoid extra slice()\n  }\n\n  // Decode in chunks to avoid \"call stack size exceeded\".\n  var res = ''\n  var i = 0\n  while (i < len) {\n    res += String.fromCharCode.apply(\n      String,\n      codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH)\n    )\n  }\n  return res\n}\n\nfunction asciiSlice (buf, start, end) {\n  var ret = ''\n  end = Math.min(buf.length, end)\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i] & 0x7F)\n  }\n  return ret\n}\n\nfunction latin1Slice (buf, start, end) {\n  var ret = ''\n  end = Math.min(buf.length, end)\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i])\n  }\n  return ret\n}\n\nfunction hexSlice (buf, start, end) {\n  var len = buf.length\n\n  if (!start || start < 0) start = 0\n  if (!end || end < 0 || end > len) end = len\n\n  var out = ''\n  for (var i = start; i < end; ++i) {\n    out += toHex(buf[i])\n  }\n  return out\n}\n\nfunction utf16leSlice (buf, start, end) {\n  var bytes = buf.slice(start, end)\n  var res = ''\n  for (var i = 0; i < bytes.length; i += 2) {\n    res += String.fromCharCode(bytes[i] + bytes[i + 1] * 256)\n  }\n  return res\n}\n\nBuffer.prototype.slice = function slice (start, end) {\n  var len = this.length\n  start = ~~start\n  end = end === undefined ? len : ~~end\n\n  if (start < 0) {\n    start += len\n    if (start < 0) start = 0\n  } else if (start > len) {\n    start = len\n  }\n\n  if (end < 0) {\n    end += len\n    if (end < 0) end = 0\n  } else if (end > len) {\n    end = len\n  }\n\n  if (end < start) end = start\n\n  var newBuf\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    newBuf = this.subarray(start, end)\n    newBuf.__proto__ = Buffer.prototype\n  } else {\n    var sliceLen = end - start\n    newBuf = new Buffer(sliceLen, undefined)\n    for (var i = 0; i < sliceLen; ++i) {\n      newBuf[i] = this[i + start]\n    }\n  }\n\n  return newBuf\n}\n\n/*\n * Need to make sure that buffer isn't trying to write out of bounds.\n */\nfunction checkOffset (offset, ext, length) {\n  if ((offset % 1) !== 0 || offset < 0) throw new RangeError('offset is not uint')\n  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length')\n}\n\nBuffer.prototype.readUIntLE = function readUIntLE (offset, byteLength, noAssert) {\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  var val = this[offset]\n  var mul = 1\n  var i = 0\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul\n  }\n\n  return val\n}\n\nBuffer.prototype.readUIntBE = function readUIntBE (offset, byteLength, noAssert) {\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) {\n    checkOffset(offset, byteLength, this.length)\n  }\n\n  var val = this[offset + --byteLength]\n  var mul = 1\n  while (byteLength > 0 && (mul *= 0x100)) {\n    val += this[offset + --byteLength] * mul\n  }\n\n  return val\n}\n\nBuffer.prototype.readUInt8 = function readUInt8 (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length)\n  return this[offset]\n}\n\nBuffer.prototype.readUInt16LE = function readUInt16LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  return this[offset] | (this[offset + 1] << 8)\n}\n\nBuffer.prototype.readUInt16BE = function readUInt16BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  return (this[offset] << 8) | this[offset + 1]\n}\n\nBuffer.prototype.readUInt32LE = function readUInt32LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return ((this[offset]) |\n      (this[offset + 1] << 8) |\n      (this[offset + 2] << 16)) +\n      (this[offset + 3] * 0x1000000)\n}\n\nBuffer.prototype.readUInt32BE = function readUInt32BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset] * 0x1000000) +\n    ((this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    this[offset + 3])\n}\n\nBuffer.prototype.readIntLE = function readIntLE (offset, byteLength, noAssert) {\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  var val = this[offset]\n  var mul = 1\n  var i = 0\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul\n  }\n  mul *= 0x80\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength)\n\n  return val\n}\n\nBuffer.prototype.readIntBE = function readIntBE (offset, byteLength, noAssert) {\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  var i = byteLength\n  var mul = 1\n  var val = this[offset + --i]\n  while (i > 0 && (mul *= 0x100)) {\n    val += this[offset + --i] * mul\n  }\n  mul *= 0x80\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength)\n\n  return val\n}\n\nBuffer.prototype.readInt8 = function readInt8 (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length)\n  if (!(this[offset] & 0x80)) return (this[offset])\n  return ((0xff - this[offset] + 1) * -1)\n}\n\nBuffer.prototype.readInt16LE = function readInt16LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  var val = this[offset] | (this[offset + 1] << 8)\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n}\n\nBuffer.prototype.readInt16BE = function readInt16BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  var val = this[offset + 1] | (this[offset] << 8)\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n}\n\nBuffer.prototype.readInt32LE = function readInt32LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset]) |\n    (this[offset + 1] << 8) |\n    (this[offset + 2] << 16) |\n    (this[offset + 3] << 24)\n}\n\nBuffer.prototype.readInt32BE = function readInt32BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset] << 24) |\n    (this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    (this[offset + 3])\n}\n\nBuffer.prototype.readFloatLE = function readFloatLE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n  return ieee754.read(this, offset, true, 23, 4)\n}\n\nBuffer.prototype.readFloatBE = function readFloatBE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n  return ieee754.read(this, offset, false, 23, 4)\n}\n\nBuffer.prototype.readDoubleLE = function readDoubleLE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length)\n  return ieee754.read(this, offset, true, 52, 8)\n}\n\nBuffer.prototype.readDoubleBE = function readDoubleBE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length)\n  return ieee754.read(this, offset, false, 52, 8)\n}\n\nfunction checkInt (buf, value, offset, ext, max, min) {\n  if (!Buffer.isBuffer(buf)) throw new TypeError('\"buffer\" argument must be a Buffer instance')\n  if (value > max || value < min) throw new RangeError('\"value\" argument is out of bounds')\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n}\n\nBuffer.prototype.writeUIntLE = function writeUIntLE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1\n    checkInt(this, value, offset, byteLength, maxBytes, 0)\n  }\n\n  var mul = 1\n  var i = 0\n  this[offset] = value & 0xFF\n  while (++i < byteLength && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeUIntBE = function writeUIntBE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1\n    checkInt(this, value, offset, byteLength, maxBytes, 0)\n  }\n\n  var i = byteLength - 1\n  var mul = 1\n  this[offset + i] = value & 0xFF\n  while (--i >= 0 && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeUInt8 = function writeUInt8 (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0)\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value)\n  this[offset] = (value & 0xff)\n  return offset + 1\n}\n\nfunction objectWriteUInt16 (buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffff + value + 1\n  for (var i = 0, j = Math.min(buf.length - offset, 2); i < j; ++i) {\n    buf[offset + i] = (value & (0xff << (8 * (littleEndian ? i : 1 - i)))) >>>\n      (littleEndian ? i : 1 - i) * 8\n  }\n}\n\nBuffer.prototype.writeUInt16LE = function writeUInt16LE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value & 0xff)\n    this[offset + 1] = (value >>> 8)\n  } else {\n    objectWriteUInt16(this, value, offset, true)\n  }\n  return offset + 2\n}\n\nBuffer.prototype.writeUInt16BE = function writeUInt16BE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 8)\n    this[offset + 1] = (value & 0xff)\n  } else {\n    objectWriteUInt16(this, value, offset, false)\n  }\n  return offset + 2\n}\n\nfunction objectWriteUInt32 (buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffffffff + value + 1\n  for (var i = 0, j = Math.min(buf.length - offset, 4); i < j; ++i) {\n    buf[offset + i] = (value >>> (littleEndian ? i : 3 - i) * 8) & 0xff\n  }\n}\n\nBuffer.prototype.writeUInt32LE = function writeUInt32LE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset + 3] = (value >>> 24)\n    this[offset + 2] = (value >>> 16)\n    this[offset + 1] = (value >>> 8)\n    this[offset] = (value & 0xff)\n  } else {\n    objectWriteUInt32(this, value, offset, true)\n  }\n  return offset + 4\n}\n\nBuffer.prototype.writeUInt32BE = function writeUInt32BE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 24)\n    this[offset + 1] = (value >>> 16)\n    this[offset + 2] = (value >>> 8)\n    this[offset + 3] = (value & 0xff)\n  } else {\n    objectWriteUInt32(this, value, offset, false)\n  }\n  return offset + 4\n}\n\nBuffer.prototype.writeIntLE = function writeIntLE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1)\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit)\n  }\n\n  var i = 0\n  var mul = 1\n  var sub = 0\n  this[offset] = value & 0xFF\n  while (++i < byteLength && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {\n      sub = 1\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeIntBE = function writeIntBE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1)\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit)\n  }\n\n  var i = byteLength - 1\n  var mul = 1\n  var sub = 0\n  this[offset + i] = value & 0xFF\n  while (--i >= 0 && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {\n      sub = 1\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeInt8 = function writeInt8 (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80)\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value)\n  if (value < 0) value = 0xff + value + 1\n  this[offset] = (value & 0xff)\n  return offset + 1\n}\n\nBuffer.prototype.writeInt16LE = function writeInt16LE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value & 0xff)\n    this[offset + 1] = (value >>> 8)\n  } else {\n    objectWriteUInt16(this, value, offset, true)\n  }\n  return offset + 2\n}\n\nBuffer.prototype.writeInt16BE = function writeInt16BE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 8)\n    this[offset + 1] = (value & 0xff)\n  } else {\n    objectWriteUInt16(this, value, offset, false)\n  }\n  return offset + 2\n}\n\nBuffer.prototype.writeInt32LE = function writeInt32LE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value & 0xff)\n    this[offset + 1] = (value >>> 8)\n    this[offset + 2] = (value >>> 16)\n    this[offset + 3] = (value >>> 24)\n  } else {\n    objectWriteUInt32(this, value, offset, true)\n  }\n  return offset + 4\n}\n\nBuffer.prototype.writeInt32BE = function writeInt32BE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)\n  if (value < 0) value = 0xffffffff + value + 1\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 24)\n    this[offset + 1] = (value >>> 16)\n    this[offset + 2] = (value >>> 8)\n    this[offset + 3] = (value & 0xff)\n  } else {\n    objectWriteUInt32(this, value, offset, false)\n  }\n  return offset + 4\n}\n\nfunction checkIEEE754 (buf, value, offset, ext, max, min) {\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n  if (offset < 0) throw new RangeError('Index out of range')\n}\n\nfunction writeFloat (buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38)\n  }\n  ieee754.write(buf, value, offset, littleEndian, 23, 4)\n  return offset + 4\n}\n\nBuffer.prototype.writeFloatLE = function writeFloatLE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, true, noAssert)\n}\n\nBuffer.prototype.writeFloatBE = function writeFloatBE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, false, noAssert)\n}\n\nfunction writeDouble (buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308)\n  }\n  ieee754.write(buf, value, offset, littleEndian, 52, 8)\n  return offset + 8\n}\n\nBuffer.prototype.writeDoubleLE = function writeDoubleLE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, true, noAssert)\n}\n\nBuffer.prototype.writeDoubleBE = function writeDoubleBE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, false, noAssert)\n}\n\n// copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)\nBuffer.prototype.copy = function copy (target, targetStart, start, end) {\n  if (!start) start = 0\n  if (!end && end !== 0) end = this.length\n  if (targetStart >= target.length) targetStart = target.length\n  if (!targetStart) targetStart = 0\n  if (end > 0 && end < start) end = start\n\n  // Copy 0 bytes; we're done\n  if (end === start) return 0\n  if (target.length === 0 || this.length === 0) return 0\n\n  //  conditions\n  if (targetStart < 0) {\n    throw new RangeError('targetStart out of bounds')\n  }\n  if (start < 0 || start >= this.length) throw new RangeError('sourceStart out of bounds')\n  if (end < 0) throw new RangeError('sourceEnd out of bounds')\n\n  // Are we oob?\n  if (end > this.length) end = this.length\n  if (target.length - targetStart < end - start) {\n    end = target.length - targetStart + start\n  }\n\n  var len = end - start\n  var i\n\n  if (this === target && start < targetStart && targetStart < end) {\n    // descending copy from end\n    for (i = len - 1; i >= 0; --i) {\n      target[i + targetStart] = this[i + start]\n    }\n  } else if (len < 1000 || !Buffer.TYPED_ARRAY_SUPPORT) {\n    // ascending copy from start\n    for (i = 0; i < len; ++i) {\n      target[i + targetStart] = this[i + start]\n    }\n  } else {\n    Uint8Array.prototype.set.call(\n      target,\n      this.subarray(start, start + len),\n      targetStart\n    )\n  }\n\n  return len\n}\n\n// Usage:\n//    buffer.fill(number[, offset[, end]])\n//    buffer.fill(buffer[, offset[, end]])\n//    buffer.fill(string[, offset[, end]][, encoding])\nBuffer.prototype.fill = function fill (val, start, end, encoding) {\n  // Handle string cases:\n  if (typeof val === 'string') {\n    if (typeof start === 'string') {\n      encoding = start\n      start = 0\n      end = this.length\n    } else if (typeof end === 'string') {\n      encoding = end\n      end = this.length\n    }\n    if (val.length === 1) {\n      var code = val.charCodeAt(0)\n      if (code < 256) {\n        val = code\n      }\n    }\n    if (encoding !== undefined && typeof encoding !== 'string') {\n      throw new TypeError('encoding must be a string')\n    }\n    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {\n      throw new TypeError('Unknown encoding: ' + encoding)\n    }\n  } else if (typeof val === 'number') {\n    val = val & 255\n  }\n\n  // Invalid ranges are not set to a default, so can range check early.\n  if (start < 0 || this.length < start || this.length < end) {\n    throw new RangeError('Out of range index')\n  }\n\n  if (end <= start) {\n    return this\n  }\n\n  start = start >>> 0\n  end = end === undefined ? this.length : end >>> 0\n\n  if (!val) val = 0\n\n  var i\n  if (typeof val === 'number') {\n    for (i = start; i < end; ++i) {\n      this[i] = val\n    }\n  } else {\n    var bytes = Buffer.isBuffer(val)\n      ? val\n      : utf8ToBytes(new Buffer(val, encoding).toString())\n    var len = bytes.length\n    for (i = 0; i < end - start; ++i) {\n      this[i + start] = bytes[i % len]\n    }\n  }\n\n  return this\n}\n\n// HELPER FUNCTIONS\n// ================\n\nvar INVALID_BASE64_RE = /[^+\\/0-9A-Za-z-_]/g\n\nfunction base64clean (str) {\n  // Node strips out invalid characters like \\n and \\t from the string, base64-js does not\n  str = stringtrim(str).replace(INVALID_BASE64_RE, '')\n  // Node converts strings with length < 2 to ''\n  if (str.length < 2) return ''\n  // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not\n  while (str.length % 4 !== 0) {\n    str = str + '='\n  }\n  return str\n}\n\nfunction stringtrim (str) {\n  if (str.trim) return str.trim()\n  return str.replace(/^\\s+|\\s+$/g, '')\n}\n\nfunction toHex (n) {\n  if (n < 16) return '0' + n.toString(16)\n  return n.toString(16)\n}\n\nfunction utf8ToBytes (string, units) {\n  units = units || Infinity\n  var codePoint\n  var length = string.length\n  var leadSurrogate = null\n  var bytes = []\n\n  for (var i = 0; i < length; ++i) {\n    codePoint = string.charCodeAt(i)\n\n    // is surrogate component\n    if (codePoint > 0xD7FF && codePoint < 0xE000) {\n      // last char was a lead\n      if (!leadSurrogate) {\n        // no lead yet\n        if (codePoint > 0xDBFF) {\n          // unexpected trail\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n          continue\n        } else if (i + 1 === length) {\n          // unpaired lead\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n          continue\n        }\n\n        // valid lead\n        leadSurrogate = codePoint\n\n        continue\n      }\n\n      // 2 leads in a row\n      if (codePoint < 0xDC00) {\n        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n        leadSurrogate = codePoint\n        continue\n      }\n\n      // valid surrogate pair\n      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000\n    } else if (leadSurrogate) {\n      // valid bmp char, but last char was a lead\n      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n    }\n\n    leadSurrogate = null\n\n    // encode utf8\n    if (codePoint < 0x80) {\n      if ((units -= 1) < 0) break\n      bytes.push(codePoint)\n    } else if (codePoint < 0x800) {\n      if ((units -= 2) < 0) break\n      bytes.push(\n        codePoint >> 0x6 | 0xC0,\n        codePoint & 0x3F | 0x80\n      )\n    } else if (codePoint < 0x10000) {\n      if ((units -= 3) < 0) break\n      bytes.push(\n        codePoint >> 0xC | 0xE0,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      )\n    } else if (codePoint < 0x110000) {\n      if ((units -= 4) < 0) break\n      bytes.push(\n        codePoint >> 0x12 | 0xF0,\n        codePoint >> 0xC & 0x3F | 0x80,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      )\n    } else {\n      throw new Error('Invalid code point')\n    }\n  }\n\n  return bytes\n}\n\nfunction asciiToBytes (str) {\n  var byteArray = []\n  for (var i = 0; i < str.length; ++i) {\n    // Node's code seems to be doing this and not & 0x7F..\n    byteArray.push(str.charCodeAt(i) & 0xFF)\n  }\n  return byteArray\n}\n\nfunction utf16leToBytes (str, units) {\n  var c, hi, lo\n  var byteArray = []\n  for (var i = 0; i < str.length; ++i) {\n    if ((units -= 2) < 0) break\n\n    c = str.charCodeAt(i)\n    hi = c >> 8\n    lo = c % 256\n    byteArray.push(lo)\n    byteArray.push(hi)\n  }\n\n  return byteArray\n}\n\nfunction base64ToBytes (str) {\n  return base64.toByteArray(base64clean(str))\n}\n\nfunction blitBuffer (src, dst, offset, length) {\n  for (var i = 0; i < length; ++i) {\n    if ((i + offset >= dst.length) || (i >= src.length)) break\n    dst[i + offset] = src[i]\n  }\n  return i\n}\n\nfunction isnan (val) {\n  return val !== val // eslint-disable-line no-self-compare\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/buffer/index.js\n// module id = 82\n// module chunks = 0 1", "var g;\r\n\r\n// This works in non-strict mode\r\ng = (function() {\r\n\treturn this;\r\n})();\r\n\r\ntry {\r\n\t// This works if eval is allowed (see CSP)\r\n\tg = g || Function(\"return this\")() || (1,eval)(\"this\");\r\n} catch(e) {\r\n\t// This works if the window reference is available\r\n\tif(typeof window === \"object\")\r\n\t\tg = window;\r\n}\r\n\r\n// g can still be undefined, but nothing to do about it...\r\n// We return undefined, instead of nothing here, so it's\r\n// easier to handle this case. if(!global) { ...}\r\n\r\nmodule.exports = g;\r\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// (webpack)/buildin/global.js\n// module id = 83\n// module chunks = 0 1", "'use strict'\n\nexports.byteLength = byteLength\nexports.toByteArray = toByteArray\nexports.fromByteArray = fromByteArray\n\nvar lookup = []\nvar revLookup = []\nvar Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array\n\nvar code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'\nfor (var i = 0, len = code.length; i < len; ++i) {\n  lookup[i] = code[i]\n  revLookup[code.charCodeAt(i)] = i\n}\n\n// Support decoding URL-safe base64 strings, as Node.js does.\n// See: https://en.wikipedia.org/wiki/Base64#URL_applications\nrevLookup['-'.charCodeAt(0)] = 62\nrevLookup['_'.charCodeAt(0)] = 63\n\nfunction getLens (b64) {\n  var len = b64.length\n\n  if (len % 4 > 0) {\n    throw new Error('Invalid string. Length must be a multiple of 4')\n  }\n\n  // Trim off extra bytes after placeholder bytes are found\n  // See: https://github.com/beatgammit/base64-js/issues/42\n  var validLen = b64.indexOf('=')\n  if (validLen === -1) validLen = len\n\n  var placeHoldersLen = validLen === len\n    ? 0\n    : 4 - (validLen % 4)\n\n  return [validLen, placeHoldersLen]\n}\n\n// base64 is 4/3 + up to two characters of the original data\nfunction byteLength (b64) {\n  var lens = getLens(b64)\n  var validLen = lens[0]\n  var placeHoldersLen = lens[1]\n  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen\n}\n\nfunction _byteLength (b64, validLen, placeHoldersLen) {\n  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen\n}\n\nfunction toByteArray (b64) {\n  var tmp\n  var lens = getLens(b64)\n  var validLen = lens[0]\n  var placeHoldersLen = lens[1]\n\n  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen))\n\n  var curByte = 0\n\n  // if there are placeholders, only get up to the last complete 4 chars\n  var len = placeHoldersLen > 0\n    ? validLen - 4\n    : validLen\n\n  var i\n  for (i = 0; i < len; i += 4) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 18) |\n      (revLookup[b64.charCodeAt(i + 1)] << 12) |\n      (revLookup[b64.charCodeAt(i + 2)] << 6) |\n      revLookup[b64.charCodeAt(i + 3)]\n    arr[curByte++] = (tmp >> 16) & 0xFF\n    arr[curByte++] = (tmp >> 8) & 0xFF\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  if (placeHoldersLen === 2) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 2) |\n      (revLookup[b64.charCodeAt(i + 1)] >> 4)\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  if (placeHoldersLen === 1) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 10) |\n      (revLookup[b64.charCodeAt(i + 1)] << 4) |\n      (revLookup[b64.charCodeAt(i + 2)] >> 2)\n    arr[curByte++] = (tmp >> 8) & 0xFF\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  return arr\n}\n\nfunction tripletToBase64 (num) {\n  return lookup[num >> 18 & 0x3F] +\n    lookup[num >> 12 & 0x3F] +\n    lookup[num >> 6 & 0x3F] +\n    lookup[num & 0x3F]\n}\n\nfunction encodeChunk (uint8, start, end) {\n  var tmp\n  var output = []\n  for (var i = start; i < end; i += 3) {\n    tmp =\n      ((uint8[i] << 16) & 0xFF0000) +\n      ((uint8[i + 1] << 8) & 0xFF00) +\n      (uint8[i + 2] & 0xFF)\n    output.push(tripletToBase64(tmp))\n  }\n  return output.join('')\n}\n\nfunction fromByteArray (uint8) {\n  var tmp\n  var len = uint8.length\n  var extraBytes = len % 3 // if we have 1 byte left, pad 2 bytes\n  var parts = []\n  var maxChunkLength = 16383 // must be multiple of 3\n\n  // go through the array every three bytes, we'll deal with trailing stuff later\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(encodeChunk(uint8, i, (i + maxChunkLength) > len2 ? len2 : (i + maxChunkLength)))\n  }\n\n  // pad the end with zeros, but make sure to not forget the extra bytes\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1]\n    parts.push(\n      lookup[tmp >> 2] +\n      lookup[(tmp << 4) & 0x3F] +\n      '=='\n    )\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + uint8[len - 1]\n    parts.push(\n      lookup[tmp >> 10] +\n      lookup[(tmp >> 4) & 0x3F] +\n      lookup[(tmp << 2) & 0x3F] +\n      '='\n    )\n  }\n\n  return parts.join('')\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/base64-js/index.js\n// module id = 84\n// module chunks = 0 1", "/*! ieee754. BSD-3-Clause License. Feross A<PERSON> <https://feross.org/opensource> */\nexports.read = function (buffer, offset, isLE, mLen, nBytes) {\n  var e, m\n  var eLen = (nBytes * 8) - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var nBits = -7\n  var i = isLE ? (nBytes - 1) : 0\n  var d = isLE ? -1 : 1\n  var s = buffer[offset + i]\n\n  i += d\n\n  e = s & ((1 << (-nBits)) - 1)\n  s >>= (-nBits)\n  nBits += eLen\n  for (; nBits > 0; e = (e * 256) + buffer[offset + i], i += d, nBits -= 8) {}\n\n  m = e & ((1 << (-nBits)) - 1)\n  e >>= (-nBits)\n  nBits += mLen\n  for (; nBits > 0; m = (m * 256) + buffer[offset + i], i += d, nBits -= 8) {}\n\n  if (e === 0) {\n    e = 1 - eBias\n  } else if (e === eMax) {\n    return m ? NaN : ((s ? -1 : 1) * Infinity)\n  } else {\n    m = m + Math.pow(2, mLen)\n    e = e - eBias\n  }\n  return (s ? -1 : 1) * m * Math.pow(2, e - mLen)\n}\n\nexports.write = function (buffer, value, offset, isLE, mLen, nBytes) {\n  var e, m, c\n  var eLen = (nBytes * 8) - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var rt = (mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0)\n  var i = isLE ? 0 : (nBytes - 1)\n  var d = isLE ? 1 : -1\n  var s = value < 0 || (value === 0 && 1 / value < 0) ? 1 : 0\n\n  value = Math.abs(value)\n\n  if (isNaN(value) || value === Infinity) {\n    m = isNaN(value) ? 1 : 0\n    e = eMax\n  } else {\n    e = Math.floor(Math.log(value) / Math.LN2)\n    if (value * (c = Math.pow(2, -e)) < 1) {\n      e--\n      c *= 2\n    }\n    if (e + eBias >= 1) {\n      value += rt / c\n    } else {\n      value += rt * Math.pow(2, 1 - eBias)\n    }\n    if (value * c >= 2) {\n      e++\n      c /= 2\n    }\n\n    if (e + eBias >= eMax) {\n      m = 0\n      e = eMax\n    } else if (e + eBias >= 1) {\n      m = ((value * c) - 1) * Math.pow(2, mLen)\n      e = e + eBias\n    } else {\n      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen)\n      e = 0\n    }\n  }\n\n  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}\n\n  e = (e << mLen) | m\n  eLen += mLen\n  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}\n\n  buffer[offset + i - d] |= s * 128\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/ieee754/index.js\n// module id = 85\n// module chunks = 0 1", "var toString = {}.toString;\n\nmodule.exports = Array.isArray || function (arr) {\n  return toString.call(arr) == '[object Array]';\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/isarray/index.js\n// module id = 86\n// module chunks = 0 1", "(function (factory) {\r\n    if (typeof exports === 'object') {\r\n        // Node/CommonJS\r\n        module.exports = factory();\r\n    } else if (typeof define === 'function' && define.amd) {\r\n        // AMD\r\n        define(factory);\r\n    } else {\r\n        // Browser globals (with support for web workers)\r\n        var glob;\r\n\r\n        try {\r\n            glob = window;\r\n        } catch (e) {\r\n            glob = self;\r\n        }\r\n\r\n        glob.SparkMD5 = factory();\r\n    }\r\n}(function (undefined) {\r\n\r\n    'use strict';\r\n\r\n    ////////////////////////////////////////////////////////////////////////////\r\n\r\n    /*\r\n     * Fastest md5 implementation around (JKM md5)\r\n     * Credits: <PERSON>\r\n     *\r\n     * @see http://www.myersdaily.org/joseph/javascript/md5-text.html\r\n     * @see http://jsperf.com/md5-shootout/7\r\n     */\r\n\r\n    /* this function is much faster,\r\n      so if possible we use it. Some IEs\r\n      are the only ones I know of that\r\n      need the idiotic second function,\r\n      generated by an if clause.  */\r\n    var add32 = function (a, b) {\r\n        return (a + b) & 0xFFFFFFFF;\r\n    },\r\n\r\n    cmn = function (q, a, b, x, s, t) {\r\n        a = add32(add32(a, q), add32(x, t));\r\n        return add32((a << s) | (a >>> (32 - s)), b);\r\n    },\r\n\r\n    ff = function (a, b, c, d, x, s, t) {\r\n        return cmn((b & c) | ((~b) & d), a, b, x, s, t);\r\n    },\r\n\r\n    gg = function (a, b, c, d, x, s, t) {\r\n        return cmn((b & d) | (c & (~d)), a, b, x, s, t);\r\n    },\r\n\r\n    hh = function (a, b, c, d, x, s, t) {\r\n        return cmn(b ^ c ^ d, a, b, x, s, t);\r\n    },\r\n\r\n    ii = function (a, b, c, d, x, s, t) {\r\n        return cmn(c ^ (b | (~d)), a, b, x, s, t);\r\n    },\r\n\r\n    md5cycle = function (x, k) {\r\n        var a = x[0],\r\n            b = x[1],\r\n            c = x[2],\r\n            d = x[3];\r\n\r\n        a = ff(a, b, c, d, k[0], 7, -680876936);\r\n        d = ff(d, a, b, c, k[1], 12, -389564586);\r\n        c = ff(c, d, a, b, k[2], 17, 606105819);\r\n        b = ff(b, c, d, a, k[3], 22, -1044525330);\r\n        a = ff(a, b, c, d, k[4], 7, -176418897);\r\n        d = ff(d, a, b, c, k[5], 12, 1200080426);\r\n        c = ff(c, d, a, b, k[6], 17, -1473231341);\r\n        b = ff(b, c, d, a, k[7], 22, -45705983);\r\n        a = ff(a, b, c, d, k[8], 7, 1770035416);\r\n        d = ff(d, a, b, c, k[9], 12, -1958414417);\r\n        c = ff(c, d, a, b, k[10], 17, -42063);\r\n        b = ff(b, c, d, a, k[11], 22, -1990404162);\r\n        a = ff(a, b, c, d, k[12], 7, 1804603682);\r\n        d = ff(d, a, b, c, k[13], 12, -40341101);\r\n        c = ff(c, d, a, b, k[14], 17, -1502002290);\r\n        b = ff(b, c, d, a, k[15], 22, 1236535329);\r\n\r\n        a = gg(a, b, c, d, k[1], 5, -165796510);\r\n        d = gg(d, a, b, c, k[6], 9, -1069501632);\r\n        c = gg(c, d, a, b, k[11], 14, 643717713);\r\n        b = gg(b, c, d, a, k[0], 20, -373897302);\r\n        a = gg(a, b, c, d, k[5], 5, -701558691);\r\n        d = gg(d, a, b, c, k[10], 9, 38016083);\r\n        c = gg(c, d, a, b, k[15], 14, -660478335);\r\n        b = gg(b, c, d, a, k[4], 20, -405537848);\r\n        a = gg(a, b, c, d, k[9], 5, 568446438);\r\n        d = gg(d, a, b, c, k[14], 9, -1019803690);\r\n        c = gg(c, d, a, b, k[3], 14, -187363961);\r\n        b = gg(b, c, d, a, k[8], 20, 1163531501);\r\n        a = gg(a, b, c, d, k[13], 5, -1444681467);\r\n        d = gg(d, a, b, c, k[2], 9, -51403784);\r\n        c = gg(c, d, a, b, k[7], 14, 1735328473);\r\n        b = gg(b, c, d, a, k[12], 20, -1926607734);\r\n\r\n        a = hh(a, b, c, d, k[5], 4, -378558);\r\n        d = hh(d, a, b, c, k[8], 11, -2022574463);\r\n        c = hh(c, d, a, b, k[11], 16, 1839030562);\r\n        b = hh(b, c, d, a, k[14], 23, -35309556);\r\n        a = hh(a, b, c, d, k[1], 4, -1530992060);\r\n        d = hh(d, a, b, c, k[4], 11, 1272893353);\r\n        c = hh(c, d, a, b, k[7], 16, -155497632);\r\n        b = hh(b, c, d, a, k[10], 23, -1094730640);\r\n        a = hh(a, b, c, d, k[13], 4, 681279174);\r\n        d = hh(d, a, b, c, k[0], 11, -358537222);\r\n        c = hh(c, d, a, b, k[3], 16, -722521979);\r\n        b = hh(b, c, d, a, k[6], 23, 76029189);\r\n        a = hh(a, b, c, d, k[9], 4, -640364487);\r\n        d = hh(d, a, b, c, k[12], 11, -421815835);\r\n        c = hh(c, d, a, b, k[15], 16, 530742520);\r\n        b = hh(b, c, d, a, k[2], 23, -995338651);\r\n\r\n        a = ii(a, b, c, d, k[0], 6, -198630844);\r\n        d = ii(d, a, b, c, k[7], 10, 1126891415);\r\n        c = ii(c, d, a, b, k[14], 15, -1416354905);\r\n        b = ii(b, c, d, a, k[5], 21, -57434055);\r\n        a = ii(a, b, c, d, k[12], 6, 1700485571);\r\n        d = ii(d, a, b, c, k[3], 10, -1894986606);\r\n        c = ii(c, d, a, b, k[10], 15, -1051523);\r\n        b = ii(b, c, d, a, k[1], 21, -2054922799);\r\n        a = ii(a, b, c, d, k[8], 6, 1873313359);\r\n        d = ii(d, a, b, c, k[15], 10, -30611744);\r\n        c = ii(c, d, a, b, k[6], 15, -1560198380);\r\n        b = ii(b, c, d, a, k[13], 21, 1309151649);\r\n        a = ii(a, b, c, d, k[4], 6, -145523070);\r\n        d = ii(d, a, b, c, k[11], 10, -1120210379);\r\n        c = ii(c, d, a, b, k[2], 15, 718787259);\r\n        b = ii(b, c, d, a, k[9], 21, -343485551);\r\n\r\n        x[0] = add32(a, x[0]);\r\n        x[1] = add32(b, x[1]);\r\n        x[2] = add32(c, x[2]);\r\n        x[3] = add32(d, x[3]);\r\n    },\r\n\r\n    /* there needs to be support for Unicode here,\r\n       * unless we pretend that we can redefine the MD-5\r\n       * algorithm for multi-byte characters (perhaps\r\n       * by adding every four 16-bit characters and\r\n       * shortening the sum to 32 bits). Otherwise\r\n       * I suggest performing MD-5 as if every character\r\n       * was two bytes--e.g., 0040 0025 = @%--but then\r\n       * how will an ordinary MD-5 sum be matched?\r\n       * There is no way to standardize text to something\r\n       * like UTF-8 before transformation; speed cost is\r\n       * utterly prohibitive. The JavaScript standard\r\n       * itself needs to look at this: it should start\r\n       * providing access to strings as preformed UTF-8\r\n       * 8-bit unsigned value arrays.\r\n       */\r\n    md5blk = function (s) {\r\n        var md5blks = [],\r\n            i; /* Andy King said do it this way. */\r\n\r\n        for (i = 0; i < 64; i += 4) {\r\n            md5blks[i >> 2] = s.charCodeAt(i) + (s.charCodeAt(i + 1) << 8) + (s.charCodeAt(i + 2) << 16) + (s.charCodeAt(i + 3) << 24);\r\n        }\r\n        return md5blks;\r\n    },\r\n\r\n    md5blk_array = function (a) {\r\n        var md5blks = [],\r\n            i; /* Andy King said do it this way. */\r\n\r\n        for (i = 0; i < 64; i += 4) {\r\n            md5blks[i >> 2] = a[i] + (a[i + 1] << 8) + (a[i + 2] << 16) + (a[i + 3] << 24);\r\n        }\r\n        return md5blks;\r\n    },\r\n\r\n    md51 = function (s) {\r\n        var n = s.length,\r\n            state = [1732584193, -271733879, -1732584194, 271733878],\r\n            i,\r\n            length,\r\n            tail,\r\n            tmp,\r\n            lo,\r\n            hi;\r\n\r\n        for (i = 64; i <= n; i += 64) {\r\n            md5cycle(state, md5blk(s.substring(i - 64, i)));\r\n        }\r\n        s = s.substring(i - 64);\r\n        length = s.length;\r\n        tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\r\n        for (i = 0; i < length; i += 1) {\r\n            tail[i >> 2] |= s.charCodeAt(i) << ((i % 4) << 3);\r\n        }\r\n        tail[i >> 2] |= 0x80 << ((i % 4) << 3);\r\n        if (i > 55) {\r\n            md5cycle(state, tail);\r\n            for (i = 0; i < 16; i += 1) {\r\n                tail[i] = 0;\r\n            }\r\n        }\r\n\r\n        // Beware that the final length might not fit in 32 bits so we take care of that\r\n        tmp = n * 8;\r\n        tmp = tmp.toString(16).match(/(.*?)(.{0,8})$/);\r\n        lo = parseInt(tmp[2], 16);\r\n        hi = parseInt(tmp[1], 16) || 0;\r\n\r\n        tail[14] = lo;\r\n        tail[15] = hi;\r\n\r\n        md5cycle(state, tail);\r\n        return state;\r\n    },\r\n\r\n    md51_array = function (a) {\r\n        var n = a.length,\r\n            state = [1732584193, -271733879, -1732584194, 271733878],\r\n            i,\r\n            length,\r\n            tail,\r\n            tmp,\r\n            lo,\r\n            hi;\r\n\r\n        for (i = 64; i <= n; i += 64) {\r\n            md5cycle(state, md5blk_array(a.subarray(i - 64, i)));\r\n        }\r\n\r\n        // Not sure if it is a bug, however IE10 will always produce a sub array of length 1\r\n        // containing the last element of the parent array if the sub array specified starts\r\n        // beyond the length of the parent array - weird.\r\n        // https://connect.microsoft.com/IE/feedback/details/771452/typed-array-subarray-issue\r\n        a = (i - 64) < n ? a.subarray(i - 64) : new Uint8Array(0);\r\n\r\n        length = a.length;\r\n        tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\r\n        for (i = 0; i < length; i += 1) {\r\n            tail[i >> 2] |= a[i] << ((i % 4) << 3);\r\n        }\r\n\r\n        tail[i >> 2] |= 0x80 << ((i % 4) << 3);\r\n        if (i > 55) {\r\n            md5cycle(state, tail);\r\n            for (i = 0; i < 16; i += 1) {\r\n                tail[i] = 0;\r\n            }\r\n        }\r\n\r\n        // Beware that the final length might not fit in 32 bits so we take care of that\r\n        tmp = n * 8;\r\n        tmp = tmp.toString(16).match(/(.*?)(.{0,8})$/);\r\n        lo = parseInt(tmp[2], 16);\r\n        hi = parseInt(tmp[1], 16) || 0;\r\n\r\n        tail[14] = lo;\r\n        tail[15] = hi;\r\n\r\n        md5cycle(state, tail);\r\n\r\n        return state;\r\n    },\r\n\r\n    hex_chr = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'],\r\n\r\n    rhex = function (n) {\r\n        var s = '',\r\n            j;\r\n        for (j = 0; j < 4; j += 1) {\r\n            s += hex_chr[(n >> (j * 8 + 4)) & 0x0F] + hex_chr[(n >> (j * 8)) & 0x0F];\r\n        }\r\n        return s;\r\n    },\r\n\r\n    hex = function (x) {\r\n        var i;\r\n        for (i = 0; i < x.length; i += 1) {\r\n            x[i] = rhex(x[i]);\r\n        }\r\n        return x.join('');\r\n    },\r\n\r\n    md5 = function (s) {\r\n        return hex(md51(s));\r\n    },\r\n\r\n\r\n\r\n    ////////////////////////////////////////////////////////////////////////////\r\n\r\n    /**\r\n     * SparkMD5 OOP implementation.\r\n     *\r\n     * Use this class to perform an incremental md5, otherwise use the\r\n     * static methods instead.\r\n     */\r\n    SparkMD5 = function () {\r\n        // call reset to init the instance\r\n        this.reset();\r\n    };\r\n\r\n\r\n    // In some cases the fast add32 function cannot be used..\r\n    if (md5('hello') !== '5d41402abc4b2a76b9719d911017c592') {\r\n        add32 = function (x, y) {\r\n            var lsw = (x & 0xFFFF) + (y & 0xFFFF),\r\n                msw = (x >> 16) + (y >> 16) + (lsw >> 16);\r\n            return (msw << 16) | (lsw & 0xFFFF);\r\n        };\r\n    }\r\n\r\n\r\n    /**\r\n     * Appends a string.\r\n     * A conversion will be applied if an utf8 string is detected.\r\n     *\r\n     * @param {String} str The string to be appended\r\n     *\r\n     * @return {SparkMD5} The instance itself\r\n     */\r\n    SparkMD5.prototype.append = function (str) {\r\n        // converts the string to utf8 bytes if necessary\r\n        if (/[\\u0080-\\uFFFF]/.test(str)) {\r\n            str = unescape(encodeURIComponent(str));\r\n        }\r\n\r\n        // then append as binary\r\n        this.appendBinary(str);\r\n\r\n        return this;\r\n    };\r\n\r\n    /**\r\n     * Appends a binary string.\r\n     *\r\n     * @param {String} contents The binary string to be appended\r\n     *\r\n     * @return {SparkMD5} The instance itself\r\n     */\r\n    SparkMD5.prototype.appendBinary = function (contents) {\r\n        this._buff += contents;\r\n        this._length += contents.length;\r\n\r\n        var length = this._buff.length,\r\n            i;\r\n\r\n        for (i = 64; i <= length; i += 64) {\r\n            md5cycle(this._state, md5blk(this._buff.substring(i - 64, i)));\r\n        }\r\n\r\n        this._buff = this._buff.substr(i - 64);\r\n\r\n        return this;\r\n    };\r\n\r\n    /**\r\n     * Finishes the incremental computation, reseting the internal state and\r\n     * returning the result.\r\n     * Use the raw parameter to obtain the raw result instead of the hex one.\r\n     *\r\n     * @param {Boolean} raw True to get the raw result, false to get the hex result\r\n     *\r\n     * @return {String|Array} The result\r\n     */\r\n    SparkMD5.prototype.end = function (raw) {\r\n        var buff = this._buff,\r\n            length = buff.length,\r\n            i,\r\n            tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],\r\n            ret;\r\n\r\n        for (i = 0; i < length; i += 1) {\r\n            tail[i >> 2] |= buff.charCodeAt(i) << ((i % 4) << 3);\r\n        }\r\n\r\n        this._finish(tail, length);\r\n        ret = !!raw ? this._state : hex(this._state);\r\n\r\n        this.reset();\r\n\r\n        return ret;\r\n    };\r\n\r\n    /**\r\n     * Finish the final calculation based on the tail.\r\n     *\r\n     * @param {Array}  tail   The tail (will be modified)\r\n     * @param {Number} length The length of the remaining buffer\r\n     */\r\n    SparkMD5.prototype._finish = function (tail, length) {\r\n        var i = length,\r\n            tmp,\r\n            lo,\r\n            hi;\r\n\r\n        tail[i >> 2] |= 0x80 << ((i % 4) << 3);\r\n        if (i > 55) {\r\n            md5cycle(this._state, tail);\r\n            for (i = 0; i < 16; i += 1) {\r\n                tail[i] = 0;\r\n            }\r\n        }\r\n\r\n        // Do the final computation based on the tail and length\r\n        // Beware that the final length may not fit in 32 bits so we take care of that\r\n        tmp = this._length * 8;\r\n        tmp = tmp.toString(16).match(/(.*?)(.{0,8})$/);\r\n        lo = parseInt(tmp[2], 16);\r\n        hi = parseInt(tmp[1], 16) || 0;\r\n\r\n        tail[14] = lo;\r\n        tail[15] = hi;\r\n        md5cycle(this._state, tail);\r\n    };\r\n\r\n    /**\r\n     * Resets the internal state of the computation.\r\n     *\r\n     * @return {SparkMD5} The instance itself\r\n     */\r\n    SparkMD5.prototype.reset = function () {\r\n        this._buff = '';\r\n        this._length = 0;\r\n        this._state = [1732584193, -271733879, -1732584194, 271733878];\r\n\r\n        return this;\r\n    };\r\n\r\n    /**\r\n     * Releases memory used by the incremental buffer and other aditional\r\n     * resources. If you plan to use the instance again, use reset instead.\r\n     */\r\n    SparkMD5.prototype.destroy = function () {\r\n        delete this._state;\r\n        delete this._buff;\r\n        delete this._length;\r\n    };\r\n\r\n\r\n    /**\r\n     * Performs the md5 hash on a string.\r\n     * A conversion will be applied if utf8 string is detected.\r\n     *\r\n     * @param {String}  str The string\r\n     * @param {Boolean} raw True to get the raw result, false to get the hex result\r\n     *\r\n     * @return {String|Array} The result\r\n     */\r\n    SparkMD5.hash = function (str, raw) {\r\n        // converts the string to utf8 bytes if necessary\r\n        if (/[\\u0080-\\uFFFF]/.test(str)) {\r\n            str = unescape(encodeURIComponent(str));\r\n        }\r\n\r\n        var hash = md51(str);\r\n\r\n        return !!raw ? hash : hex(hash);\r\n    };\r\n\r\n    /**\r\n     * Performs the md5 hash on a binary string.\r\n     *\r\n     * @param {String}  content The binary string\r\n     * @param {Boolean} raw     True to get the raw result, false to get the hex result\r\n     *\r\n     * @return {String|Array} The result\r\n     */\r\n    SparkMD5.hashBinary = function (content, raw) {\r\n        var hash = md51(content);\r\n\r\n        return !!raw ? hash : hex(hash);\r\n    };\r\n\r\n    /**\r\n     * SparkMD5 OOP implementation for array buffers.\r\n     *\r\n     * Use this class to perform an incremental md5 ONLY for array buffers.\r\n     */\r\n    SparkMD5.ArrayBuffer = function () {\r\n        // call reset to init the instance\r\n        this.reset();\r\n    };\r\n\r\n    ////////////////////////////////////////////////////////////////////////////\r\n\r\n    /**\r\n     * Appends an array buffer.\r\n     *\r\n     * @param {ArrayBuffer} arr The array to be appended\r\n     *\r\n     * @return {SparkMD5.ArrayBuffer} The instance itself\r\n     */\r\n    SparkMD5.ArrayBuffer.prototype.append = function (arr) {\r\n        // TODO: we could avoid the concatenation here but the algorithm would be more complex\r\n        //       if you find yourself needing extra performance, please make a PR.\r\n        var buff = this._concatArrayBuffer(this._buff, arr),\r\n            length = buff.length,\r\n            i;\r\n\r\n        this._length += arr.byteLength;\r\n\r\n        for (i = 64; i <= length; i += 64) {\r\n            md5cycle(this._state, md5blk_array(buff.subarray(i - 64, i)));\r\n        }\r\n\r\n        // Avoids IE10 weirdness (documented above)\r\n        this._buff = (i - 64) < length ? buff.subarray(i - 64) : new Uint8Array(0);\r\n\r\n        return this;\r\n    };\r\n\r\n    /**\r\n     * Finishes the incremental computation, reseting the internal state and\r\n     * returning the result.\r\n     * Use the raw parameter to obtain the raw result instead of the hex one.\r\n     *\r\n     * @param {Boolean} raw True to get the raw result, false to get the hex result\r\n     *\r\n     * @return {String|Array} The result\r\n     */\r\n    SparkMD5.ArrayBuffer.prototype.end = function (raw) {\r\n        var buff = this._buff,\r\n            length = buff.length,\r\n            tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],\r\n            i,\r\n            ret;\r\n\r\n        for (i = 0; i < length; i += 1) {\r\n            tail[i >> 2] |= buff[i] << ((i % 4) << 3);\r\n        }\r\n\r\n        this._finish(tail, length);\r\n        ret = !!raw ? this._state : hex(this._state);\r\n\r\n        this.reset();\r\n\r\n        return ret;\r\n    };\r\n\r\n    SparkMD5.ArrayBuffer.prototype._finish = SparkMD5.prototype._finish;\r\n\r\n    /**\r\n     * Resets the internal state of the computation.\r\n     *\r\n     * @return {SparkMD5.ArrayBuffer} The instance itself\r\n     */\r\n    SparkMD5.ArrayBuffer.prototype.reset = function () {\r\n        this._buff = new Uint8Array(0);\r\n        this._length = 0;\r\n        this._state = [1732584193, -271733879, -1732584194, 271733878];\r\n\r\n        return this;\r\n    };\r\n\r\n    /**\r\n     * Releases memory used by the incremental buffer and other aditional\r\n     * resources. If you plan to use the instance again, use reset instead.\r\n     */\r\n    SparkMD5.ArrayBuffer.prototype.destroy = SparkMD5.prototype.destroy;\r\n\r\n    /**\r\n     * Concats two array buffers, returning a new one.\r\n     *\r\n     * @param  {ArrayBuffer} first  The first array buffer\r\n     * @param  {ArrayBuffer} second The second array buffer\r\n     *\r\n     * @return {ArrayBuffer} The new array buffer\r\n     */\r\n    SparkMD5.ArrayBuffer.prototype._concatArrayBuffer = function (first, second) {\r\n        var firstLength = first.length,\r\n            result = new Uint8Array(firstLength + second.byteLength);\r\n\r\n        result.set(first);\r\n        result.set(new Uint8Array(second), firstLength);\r\n\r\n        return result;\r\n    };\r\n\r\n    /**\r\n     * Performs the md5 hash on an array buffer.\r\n     *\r\n     * @param {ArrayBuffer} arr The array buffer\r\n     * @param {Boolean}     raw True to get the raw result, false to get the hex result\r\n     *\r\n     * @return {String|Array} The result\r\n     */\r\n    SparkMD5.ArrayBuffer.hash = function (arr, raw) {\r\n        var hash = md51_array(new Uint8Array(arr));\r\n\r\n        return !!raw ? hash : hex(hash);\r\n    };\r\n\r\n    return SparkMD5;\r\n}));\n\n\n// WEBPACK FOOTER //\n// ./src/libs/md5/spark-md5.js", "\"use strict\";\n\nexports.__esModule = true;\n\nvar _iterator = require(\"../core-js/symbol/iterator\");\n\nvar _iterator2 = _interopRequireDefault(_iterator);\n\nvar _symbol = require(\"../core-js/symbol\");\n\nvar _symbol2 = _interopRequireDefault(_symbol);\n\nvar _typeof = typeof _symbol2.default === \"function\" && typeof _iterator2.default === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof _symbol2.default === \"function\" && obj.constructor === _symbol2.default && obj !== _symbol2.default.prototype ? \"symbol\" : typeof obj; };\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.default = typeof _symbol2.default === \"function\" && _typeof(_iterator2.default) === \"symbol\" ? function (obj) {\n  return typeof obj === \"undefined\" ? \"undefined\" : _typeof(obj);\n} : function (obj) {\n  return obj && typeof _symbol2.default === \"function\" && obj.constructor === _symbol2.default && obj !== _symbol2.default.prototype ? \"symbol\" : typeof obj === \"undefined\" ? \"undefined\" : _typeof(obj);\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/helpers/typeof.js\n// module id = 88\n// module chunks = 0 1", "module.exports = { \"default\": require(\"core-js/library/fn/symbol/iterator\"), __esModule: true };\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/core-js/symbol/iterator.js\n// module id = 89\n// module chunks = 0 1", "require('../../modules/es6.string.iterator');\nrequire('../../modules/web.dom.iterable');\nmodule.exports = require('../../modules/_wks-ext').f('iterator');\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/fn/symbol/iterator.js\n// module id = 90\n// module chunks = 0 1", "module.exports = { \"default\": require(\"core-js/library/fn/symbol\"), __esModule: true };\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/core-js/symbol.js\n// module id = 91\n// module chunks = 0 1", "require('../../modules/es6.symbol');\nrequire('../../modules/es6.object.to-string');\nrequire('../../modules/es7.symbol.async-iterator');\nrequire('../../modules/es7.symbol.observable');\nmodule.exports = require('../../modules/_core').Symbol;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/fn/symbol/index.js\n// module id = 92\n// module chunks = 0 1", "'use strict';\n// ECMAScript 6 symbols shim\nvar global = require('./_global');\nvar has = require('./_has');\nvar DESCRIPTORS = require('./_descriptors');\nvar $export = require('./_export');\nvar redefine = require('./_redefine');\nvar META = require('./_meta').KEY;\nvar $fails = require('./_fails');\nvar shared = require('./_shared');\nvar setToStringTag = require('./_set-to-string-tag');\nvar uid = require('./_uid');\nvar wks = require('./_wks');\nvar wksExt = require('./_wks-ext');\nvar wksDefine = require('./_wks-define');\nvar enumKeys = require('./_enum-keys');\nvar isArray = require('./_is-array');\nvar anObject = require('./_an-object');\nvar isObject = require('./_is-object');\nvar toObject = require('./_to-object');\nvar toIObject = require('./_to-iobject');\nvar toPrimitive = require('./_to-primitive');\nvar createDesc = require('./_property-desc');\nvar _create = require('./_object-create');\nvar gOPNExt = require('./_object-gopn-ext');\nvar $GOPD = require('./_object-gopd');\nvar $GOPS = require('./_object-gops');\nvar $DP = require('./_object-dp');\nvar $keys = require('./_object-keys');\nvar gOPD = $GOPD.f;\nvar dP = $DP.f;\nvar gOPN = gOPNExt.f;\nvar $Symbol = global.Symbol;\nvar $JSON = global.JSON;\nvar _stringify = $JSON && $JSON.stringify;\nvar PROTOTYPE = 'prototype';\nvar HIDDEN = wks('_hidden');\nvar TO_PRIMITIVE = wks('toPrimitive');\nvar isEnum = {}.propertyIsEnumerable;\nvar SymbolRegistry = shared('symbol-registry');\nvar AllSymbols = shared('symbols');\nvar OPSymbols = shared('op-symbols');\nvar ObjectProto = Object[PROTOTYPE];\nvar USE_NATIVE = typeof $Symbol == 'function' && !!$GOPS.f;\nvar QObject = global.QObject;\n// Don't use setters in Qt Script, https://github.com/zloirock/core-js/issues/173\nvar setter = !QObject || !QObject[PROTOTYPE] || !QObject[PROTOTYPE].findChild;\n\n// fallback for old Android, https://code.google.com/p/v8/issues/detail?id=687\nvar setSymbolDesc = DESCRIPTORS && $fails(function () {\n  return _create(dP({}, 'a', {\n    get: function () { return dP(this, 'a', { value: 7 }).a; }\n  })).a != 7;\n}) ? function (it, key, D) {\n  var protoDesc = gOPD(ObjectProto, key);\n  if (protoDesc) delete ObjectProto[key];\n  dP(it, key, D);\n  if (protoDesc && it !== ObjectProto) dP(ObjectProto, key, protoDesc);\n} : dP;\n\nvar wrap = function (tag) {\n  var sym = AllSymbols[tag] = _create($Symbol[PROTOTYPE]);\n  sym._k = tag;\n  return sym;\n};\n\nvar isSymbol = USE_NATIVE && typeof $Symbol.iterator == 'symbol' ? function (it) {\n  return typeof it == 'symbol';\n} : function (it) {\n  return it instanceof $Symbol;\n};\n\nvar $defineProperty = function defineProperty(it, key, D) {\n  if (it === ObjectProto) $defineProperty(OPSymbols, key, D);\n  anObject(it);\n  key = toPrimitive(key, true);\n  anObject(D);\n  if (has(AllSymbols, key)) {\n    if (!D.enumerable) {\n      if (!has(it, HIDDEN)) dP(it, HIDDEN, createDesc(1, {}));\n      it[HIDDEN][key] = true;\n    } else {\n      if (has(it, HIDDEN) && it[HIDDEN][key]) it[HIDDEN][key] = false;\n      D = _create(D, { enumerable: createDesc(0, false) });\n    } return setSymbolDesc(it, key, D);\n  } return dP(it, key, D);\n};\nvar $defineProperties = function defineProperties(it, P) {\n  anObject(it);\n  var keys = enumKeys(P = toIObject(P));\n  var i = 0;\n  var l = keys.length;\n  var key;\n  while (l > i) $defineProperty(it, key = keys[i++], P[key]);\n  return it;\n};\nvar $create = function create(it, P) {\n  return P === undefined ? _create(it) : $defineProperties(_create(it), P);\n};\nvar $propertyIsEnumerable = function propertyIsEnumerable(key) {\n  var E = isEnum.call(this, key = toPrimitive(key, true));\n  if (this === ObjectProto && has(AllSymbols, key) && !has(OPSymbols, key)) return false;\n  return E || !has(this, key) || !has(AllSymbols, key) || has(this, HIDDEN) && this[HIDDEN][key] ? E : true;\n};\nvar $getOwnPropertyDescriptor = function getOwnPropertyDescriptor(it, key) {\n  it = toIObject(it);\n  key = toPrimitive(key, true);\n  if (it === ObjectProto && has(AllSymbols, key) && !has(OPSymbols, key)) return;\n  var D = gOPD(it, key);\n  if (D && has(AllSymbols, key) && !(has(it, HIDDEN) && it[HIDDEN][key])) D.enumerable = true;\n  return D;\n};\nvar $getOwnPropertyNames = function getOwnPropertyNames(it) {\n  var names = gOPN(toIObject(it));\n  var result = [];\n  var i = 0;\n  var key;\n  while (names.length > i) {\n    if (!has(AllSymbols, key = names[i++]) && key != HIDDEN && key != META) result.push(key);\n  } return result;\n};\nvar $getOwnPropertySymbols = function getOwnPropertySymbols(it) {\n  var IS_OP = it === ObjectProto;\n  var names = gOPN(IS_OP ? OPSymbols : toIObject(it));\n  var result = [];\n  var i = 0;\n  var key;\n  while (names.length > i) {\n    if (has(AllSymbols, key = names[i++]) && (IS_OP ? has(ObjectProto, key) : true)) result.push(AllSymbols[key]);\n  } return result;\n};\n\n// ******** Symbol([description])\nif (!USE_NATIVE) {\n  $Symbol = function Symbol() {\n    if (this instanceof $Symbol) throw TypeError('Symbol is not a constructor!');\n    var tag = uid(arguments.length > 0 ? arguments[0] : undefined);\n    var $set = function (value) {\n      if (this === ObjectProto) $set.call(OPSymbols, value);\n      if (has(this, HIDDEN) && has(this[HIDDEN], tag)) this[HIDDEN][tag] = false;\n      setSymbolDesc(this, tag, createDesc(1, value));\n    };\n    if (DESCRIPTORS && setter) setSymbolDesc(ObjectProto, tag, { configurable: true, set: $set });\n    return wrap(tag);\n  };\n  redefine($Symbol[PROTOTYPE], 'toString', function toString() {\n    return this._k;\n  });\n\n  $GOPD.f = $getOwnPropertyDescriptor;\n  $DP.f = $defineProperty;\n  require('./_object-gopn').f = gOPNExt.f = $getOwnPropertyNames;\n  require('./_object-pie').f = $propertyIsEnumerable;\n  $GOPS.f = $getOwnPropertySymbols;\n\n  if (DESCRIPTORS && !require('./_library')) {\n    redefine(ObjectProto, 'propertyIsEnumerable', $propertyIsEnumerable, true);\n  }\n\n  wksExt.f = function (name) {\n    return wrap(wks(name));\n  };\n}\n\n$export($export.G + $export.W + $export.F * !USE_NATIVE, { Symbol: $Symbol });\n\nfor (var es6Symbols = (\n  // ********, ********, ********, ********, ********, ********, *********, *********, *********, *********, *********\n  'hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables'\n).split(','), j = 0; es6Symbols.length > j;)wks(es6Symbols[j++]);\n\nfor (var wellKnownSymbols = $keys(wks.store), k = 0; wellKnownSymbols.length > k;) wksDefine(wellKnownSymbols[k++]);\n\n$export($export.S + $export.F * !USE_NATIVE, 'Symbol', {\n  // ******** Symbol.for(key)\n  'for': function (key) {\n    return has(SymbolRegistry, key += '')\n      ? SymbolRegistry[key]\n      : SymbolRegistry[key] = $Symbol(key);\n  },\n  // ******** Symbol.keyFor(sym)\n  keyFor: function keyFor(sym) {\n    if (!isSymbol(sym)) throw TypeError(sym + ' is not a symbol!');\n    for (var key in SymbolRegistry) if (SymbolRegistry[key] === sym) return key;\n  },\n  useSetter: function () { setter = true; },\n  useSimple: function () { setter = false; }\n});\n\n$export($export.S + $export.F * !USE_NATIVE, 'Object', {\n  // ******** Object.create(O [, Properties])\n  create: $create,\n  // ******** Object.defineProperty(O, P, Attributes)\n  defineProperty: $defineProperty,\n  // ******** Object.defineProperties(O, Properties)\n  defineProperties: $defineProperties,\n  // ******** Object.getOwnPropertyDescriptor(O, P)\n  getOwnPropertyDescriptor: $getOwnPropertyDescriptor,\n  // ******** Object.getOwnPropertyNames(O)\n  getOwnPropertyNames: $getOwnPropertyNames,\n  // ******** Object.getOwnPropertySymbols(O)\n  getOwnPropertySymbols: $getOwnPropertySymbols\n});\n\n// Chrome 38 and 39 `Object.getOwnPropertySymbols` fails on primitives\n// https://bugs.chromium.org/p/v8/issues/detail?id=3443\nvar FAILS_ON_PRIMITIVES = $fails(function () { $GOPS.f(1); });\n\n$export($export.S + $export.F * FAILS_ON_PRIMITIVES, 'Object', {\n  getOwnPropertySymbols: function getOwnPropertySymbols(it) {\n    return $GOPS.f(toObject(it));\n  }\n});\n\n// 24.3.2 JSON.stringify(value [, replacer [, space]])\n$JSON && $export($export.S + $export.F * (!USE_NATIVE || $fails(function () {\n  var S = $Symbol();\n  // MS Edge converts symbol values to JSON as {}\n  // WebKit converts symbol values to JSON as null\n  // V8 throws on boxed symbols\n  return _stringify([S]) != '[null]' || _stringify({ a: S }) != '{}' || _stringify(Object(S)) != '{}';\n})), 'JSON', {\n  stringify: function stringify(it) {\n    var args = [it];\n    var i = 1;\n    var replacer, $replacer;\n    while (arguments.length > i) args.push(arguments[i++]);\n    $replacer = replacer = args[1];\n    if (!isObject(replacer) && it === undefined || isSymbol(it)) return; // IE8 returns string on undefined\n    if (!isArray(replacer)) replacer = function (key, value) {\n      if (typeof $replacer == 'function') value = $replacer.call(this, key, value);\n      if (!isSymbol(value)) return value;\n    };\n    args[1] = replacer;\n    return _stringify.apply($JSON, args);\n  }\n});\n\n// 19.4.3.4 Symbol.prototype[@@toPrimitive](hint)\n$Symbol[PROTOTYPE][TO_PRIMITIVE] || require('./_hide')($Symbol[PROTOTYPE], TO_PRIMITIVE, $Symbol[PROTOTYPE].valueOf);\n// 19.4.3.5 Symbol.prototype[@@toStringTag]\nsetToStringTag($Symbol, 'Symbol');\n// 20.2.1.9 Math[@@toStringTag]\nsetToStringTag(Math, 'Math', true);\n// 24.3.3 JSON[@@toStringTag]\nsetToStringTag(global.JSON, 'JSON', true);\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/es6.symbol.js\n// module id = 93\n// module chunks = 0 1", "var META = require('./_uid')('meta');\nvar isObject = require('./_is-object');\nvar has = require('./_has');\nvar setDesc = require('./_object-dp').f;\nvar id = 0;\nvar isExtensible = Object.isExtensible || function () {\n  return true;\n};\nvar FREEZE = !require('./_fails')(function () {\n  return isExtensible(Object.preventExtensions({}));\n});\nvar setMeta = function (it) {\n  setDesc(it, META, { value: {\n    i: 'O' + ++id, // object ID\n    w: {}          // weak collections IDs\n  } });\n};\nvar fastKey = function (it, create) {\n  // return primitive with prefix\n  if (!isObject(it)) return typeof it == 'symbol' ? it : (typeof it == 'string' ? 'S' : 'P') + it;\n  if (!has(it, META)) {\n    // can't set metadata to uncaught frozen object\n    if (!isExtensible(it)) return 'F';\n    // not necessary to add metadata\n    if (!create) return 'E';\n    // add missing metadata\n    setMeta(it);\n  // return object ID\n  } return it[META].i;\n};\nvar getWeak = function (it, create) {\n  if (!has(it, META)) {\n    // can't set metadata to uncaught frozen object\n    if (!isExtensible(it)) return true;\n    // not necessary to add metadata\n    if (!create) return false;\n    // add missing metadata\n    setMeta(it);\n  // return hash weak collections IDs\n  } return it[META].w;\n};\n// add metadata on freeze-family methods calling\nvar onFreeze = function (it) {\n  if (FREEZE && meta.NEED && isExtensible(it) && !has(it, META)) setMeta(it);\n  return it;\n};\nvar meta = module.exports = {\n  KEY: META,\n  NEED: false,\n  fastKey: fastKey,\n  getWeak: getWeak,\n  onFreeze: onFreeze\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_meta.js\n// module id = 94\n// module chunks = 0 1", "// all enumerable object keys, includes symbols\nvar getKeys = require('./_object-keys');\nvar gOPS = require('./_object-gops');\nvar pIE = require('./_object-pie');\nmodule.exports = function (it) {\n  var result = getKeys(it);\n  var getSymbols = gOPS.f;\n  if (getSymbols) {\n    var symbols = getSymbols(it);\n    var isEnum = pIE.f;\n    var i = 0;\n    var key;\n    while (symbols.length > i) if (isEnum.call(it, key = symbols[i++])) result.push(key);\n  } return result;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_enum-keys.js\n// module id = 95\n// module chunks = 0 1", "// 7.2.2 IsArray(argument)\nvar cof = require('./_cof');\nmodule.exports = Array.isArray || function isArray(arg) {\n  return cof(arg) == 'Array';\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_is-array.js\n// module id = 96\n// module chunks = 0 1", "// fallback for IE11 buggy Object.getOwnPropertyNames with iframe and window\nvar toIObject = require('./_to-iobject');\nvar gOPN = require('./_object-gopn').f;\nvar toString = {}.toString;\n\nvar windowNames = typeof window == 'object' && window && Object.getOwnPropertyNames\n  ? Object.getOwnPropertyNames(window) : [];\n\nvar getWindowNames = function (it) {\n  try {\n    return gOPN(it);\n  } catch (e) {\n    return windowNames.slice();\n  }\n};\n\nmodule.exports.f = function getOwnPropertyNames(it) {\n  return windowNames && toString.call(it) == '[object Window]' ? getWindowNames(it) : gOPN(toIObject(it));\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-gopn-ext.js\n// module id = 97\n// module chunks = 0 1", "var pIE = require('./_object-pie');\nvar createDesc = require('./_property-desc');\nvar toIObject = require('./_to-iobject');\nvar toPrimitive = require('./_to-primitive');\nvar has = require('./_has');\nvar IE8_DOM_DEFINE = require('./_ie8-dom-define');\nvar gOPD = Object.getOwnPropertyDescriptor;\n\nexports.f = require('./_descriptors') ? gOPD : function getOwnPropertyDescriptor(O, P) {\n  O = toIObject(O);\n  P = toPrimitive(P, true);\n  if (IE8_DOM_DEFINE) try {\n    return gOPD(O, P);\n  } catch (e) { /* empty */ }\n  if (has(O, P)) return createDesc(!pIE.f.call(O, P), O[P]);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-gopd.js\n// module id = 98\n// module chunks = 0 1", "require('./_wks-define')('asyncIterator');\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/es7.symbol.async-iterator.js\n// module id = 99\n// module chunks = 0 1", "require('./_wks-define')('observable');\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/es7.symbol.observable.js\n// module id = 100\n// module chunks = 0 1", "import commonUtil from \"@/common/commonUtil\";\r\n\r\nvar globalUtil = require('@/common/globalUtil.js').default;\r\nvar httpUtil = require('@/common/httpUtil.js').default;\r\n\r\nvar vtube = function (mainOpts) {\r\n    var self = this;\r\n    var baseUrl = 'http://127.0.0.1:8084/';\r\n\r\n    function getConfig() {\r\n        if (window.nxt && window.nxt.config) {\r\n            return window.nxt.config;\r\n        }\r\n        else if (mainOpts.configInst) {\r\n            return mainOpts.configInst;\r\n        }\r\n    }\r\n\r\n    function getCompleteUrl() {\r\n        var callUrl = 'http://' + location.host;\r\n        // if (nxt.config.server != '') {\r\n        //     callUrl = callUrl + nxt.config.server + '/';\r\n        // }\r\n        let apiVersion = commonUtil.getCookie('apiVersion');\r\n        var url = '/upload/complete'\r\n        if (apiVersion && apiVersion === 'mamcore2.3'){\r\n            url = '/sflud/v1/upload/complete'\r\n        }\r\n        return callUrl + url;\r\n    }\r\n\r\n    function getRequestParams(task) {\r\n        var res = {\r\n            TaskName: task.metadata.name,\r\n            TaskGuid: task.taskId,\r\n            TransType: 'Upload',\r\n            TransFile: [],\r\n            UserInfo: {\r\n                UserId: nxt.user.current.id.toString(),\r\n                UserName: nxt.user.current.nickName || nxt.user.current.loginName,\r\n                UserCode: nxt.user.current.userCode,\r\n            },\r\n            ExtendeAttr: []\r\n        };\r\n        if (getConfig().vtubeInfo.importType == 1) {\r\n            res.CallBackUrl = getCompleteUrl();\r\n        }\r\n        if (task.serverInfo) {\r\n            res.ServerInfo = {\r\n                HostName: task.serverInfo.hostName,\r\n                Port: task.serverInfo.port,\r\n                Scheme: task.serverInfo.scheme,\r\n                UserName: task.serverInfo.userName,\r\n                Password: task.serverInfo.password,\r\n                PathRoot: task.serverInfo.pathRoot\r\n            };\r\n        }\r\n        res.ExtendeAttr = _.map(task.metadata.field, function (item) {\r\n            var n = { ItemCode: item.fieldName, ItemName: item.alias };\r\n            if (item.controlType == 8) {\r\n                if (item.value != null && item.value != '' && item.value != '[]') {\r\n                    try {\r\n                        n.Value = JSON.parse(item.value)[0];\r\n                    } catch (e) {\r\n                        n.Value = '';\r\n                    }\r\n                } else {\r\n                    n.Value = '';\r\n                }\r\n            } else {\r\n                n.Value = item.value || '';\r\n            }\r\n            return n;\r\n        });\r\n        res.ExtendeAttr.push({\r\n            ItemCode: 'tree',\r\n            ItemName: '目录树',\r\n            Value: task.tree\r\n        });\r\n        _.remove(res.ExtendeAttr, { ItemCode: 'cliptype' });\r\n        res.ExtendeAttr.push({\r\n            ItemCode: 'cliptype',\r\n            ItemName: '素材类型',\r\n            Value: task.entityType\r\n        });\r\n        return res;\r\n    }\r\n\r\n    this.openFileSelector = function (callback, errorCallback) {\r\n        $.ajax({\r\n            url: baseUrl + 'request/getuploadfiles?user_token=&filetype=all',\r\n            success: function (res) {\r\n                var data = _.isString(res) ? JSON.parse(res) : res;\r\n                if (data.FileCount === 0)\r\n                    return;\r\n                var result = [];\r\n                _.forEach(data.FileList, function (item) {\r\n                    var ext = '.' + commonUtil.getExtension(item.FilePath);\r\n                    result.push({\r\n                        entityType: commonUtil.getTypeByExt(ext, getConfig()).code,\r\n                        fileName: commonUtil.getFullFileName(item.FilePath),\r\n                        metadata: {\r\n                            name: commonUtil.getFileName(item.FilePath),\r\n                            ext: ext\r\n                        },\r\n                        status: 'added',\r\n                        progress: 0,\r\n                        file: item\r\n                    });\r\n                });\r\n                callback(result);\r\n            },\r\n            error: errorCallback || function (res) {\r\n                console.error(res);\r\n                var vtubeDownloadPath = getConfig().vtubeDownloadPath || getConfig().server + '/assets/Sobey_vRocket_v2.0_Setup.exe'\r\n                commonUtil.prompt(l('upload.clientInstallTip', '<p>打开客户端失败</p><p>请确定已经正常开启客户端或重启客户端再重试。</p><p>如没有安装，请点击下面地址。</p><p><a href=\"${path}\"><i class=\"icon iconfont icon-iconfontxiazai\" style=\"margin-right:5px\"></i>点击客户端下载</a></p>', { path: vtubeDownloadPath }));\r\n            }\r\n        });\r\n    }\r\n\r\n    this.createTask = function (dto, params) {\r\n        var list = [];\r\n        switch (params.taskType) {\r\n            case 1: //普通类型\r\n                if (!_.isArray(dto)) {\r\n                    dto = [dto];\r\n                }\r\n                _.forEach(dto, function (task) {\r\n                    if (task.file) {\r\n                        task.files = [{\r\n                            file: task.file,\r\n                            fileName: task.fileName,\r\n                            fileSize: task.file.FileSize\r\n                        }];\r\n                        delete task.file;\r\n                        list.push(task);\r\n                    }\r\n                    else if (task.files && task.files.length > 0) {\r\n                        task.files = _.map(task.files, function (file) {\r\n                            return {\r\n                                file: file.file,\r\n                                fileName: file.fileName,\r\n                                fileSize: file.fileSize\r\n                            };\r\n                        });\r\n                        list.push(task);\r\n                    }\r\n                });\r\n                break;\r\n            case 2:\r\n            case 3: //普通类型\r\n                list.push(dto);\r\n                break;\r\n        }\r\n        _.forEach(list, function (task) {\r\n            task.taskType = params.taskType;\r\n            task.transferType = params.transferType;\r\n            task.targetFolder = params.targetFolder;\r\n            task.relationContentType = params.relationContentType;\r\n            task.relationContentId = params.relationContentId;\r\n            self.addTask(task);\r\n        });\r\n    }\r\n\r\n    this.addTask = function (task) {\r\n        let apiVersion = commonUtil.getCookie('apiVersion');\r\n        var url = '/upload/multipart/init?token=' + mainOpts.loginToken\r\n        if (apiVersion && apiVersion === 'mamcore2.3'){\r\n            url = '/sflud/v1/upload/multipart/init?token=' + mainOpts.loginToken\r\n        }\r\n        httpUtil.post(getConfig().server + url, task).then(function (res) {\r\n            res = res.data;\r\n            var parms = getRequestParams(res);\r\n            parms.ExtendData = res.userToken;\r\n            function getPath(file) {\r\n                if (getConfig().isHandleHttpPath && res && res.ossClientInfo) {\r\n                    var hostname = window.location.hostname;\r\n                    var absolutePath = file.absolutePath.split('@');\r\n                    if (absolutePath.length > 1) {\r\n                        var startChar = absolutePath[1].indexOf(':') > -1 ? ':' : '/';\r\n                        file.absolutePath = absolutePath[0] + '@' + hostname + absolutePath[1].substr(absolutePath[1].indexOf(startChar));\r\n                    }\r\n                }\r\n                return (res && res.ossClientInfo) ? file.absolutePath : file.relativePath;\r\n            }\r\n\r\n            switch (task.taskType) {\r\n                case 1:\r\n                    parms.TransFile.push({\r\n                        SourceFile: task.files[0].file.FilePath,\r\n                        DestFile: getPath(res.files[0])\r\n                    });\r\n                    break;\r\n                case 2:\r\n                case 3:\r\n                    _.forEach(res.files, function (file) {\r\n                        parms.TransFile.push({\r\n                            SourceFile: file.fileName,\r\n                            DestFile: getPath(file)\r\n                        });\r\n                    });\r\n                    break;\r\n                default:\r\n            }\r\n            $.ajax({\r\n                url: baseUrl + 'request/addtask?user_token=',\r\n                type: 'POST',\r\n                contentType: 'application/json',\r\n                data: JSON.stringify(parms),\r\n                success: function (result) {\r\n                    if (typeof (result) === 'string') {\r\n                        result = JSON.parse(result);\r\n                    }\r\n                    if (result.Result == 1) {\r\n                        commonUtil.msgOk(l('upload.addTaskOk', '添加任务成功'));\r\n                    } else {\r\n                        commonUtil.prompt(l('upload.addTaskError', '添加任务失败：') + result.Msg);\r\n                    }\r\n                    console.info(result);\r\n                },\r\n                error: function (result) {\r\n                    console.info(result);\r\n                    commonUtil.prompt(l('upload.addTaskError', '添加任务失败'));\r\n                }\r\n            });\r\n        }, function (res) {\r\n            commonUtil.prompt(l('upload.uploadError', '上传失败：') + res.data.desc);\r\n        });\r\n    }\r\n}\r\n\r\nexport default vtube;\r\n\n\n\n// WEBPACK FOOTER //\n// ./src/core/vtubeTransfer.js", "var web = require('./core/webTransfer.js');\r\nvar vtube = require('./core/vtubeTransfer.js');\r\nvar thirdpartSupport = require('./core/thirdpartSupport.js');\r\n\r\nvar mamUpload = {\r\n    init: function(options){\r\n        mamUpload.web = new web.default(options);\r\n        mamUpload.vtube = new vtube.default(options);\r\n        mamUpload.thirdpartSupport = new thirdpartSupport.default(options);\r\n\r\n        if (window.OSS)\r\n        {\r\n            mamUpload.web.OSS = window.OSS;\r\n        }\r\n    }\r\n}\r\n\r\nwindow.mamUpload = mamUpload;\n\n\n// WEBPACK FOOTER //\n// ./src/indexPure.js", "var thirdpartSupport = function (mainOpts) {\r\n    this.handlePicPkgTaskMetadataName = function(tasks) {\r\n        _.forEach(tasks.files, (file, index) => {\r\n            if (index === 0 && (tasks.metadata.name == null || tasks.metadata.name === '')) {\r\n                tasks.metadata.name = file.metadata.name;\r\n            }\r\n        });\r\n    }\r\n}\r\nexport default thirdpartSupport;\n\n\n// WEBPACK FOOTER //\n// ./src/core/thirdpartSupport.js"], "sourceRoot": ""}