import  searchTypes from '@/types/searchTypes'
import { IReducers } from '@/types/modelsTypes'

export interface Idownlist {
    showLoading: searchTypes.ISearchData[]
}

export default {
    namespace: 'download',
    state: {
        downlist: []
    },
    reducers: {
        changedownload: (state: Idownlist, { payload: data }: IReducers<searchTypes.ISearchData[]>) => {
            return {
                // ...state,
                downlist: data.value
            }
        }
    }
}