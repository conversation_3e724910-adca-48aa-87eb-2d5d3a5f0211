import http from '../http/http';
import searchTypes from '@/types/searchTypes';
import copyandmoveTypes from '@/types/copyandmoveTypes';

namespace copyandmoveApis {
  export const movetree = (num?: number) => {
    return http<searchTypes.IFolder>(
      `/folder/all/tree?level=${num ? num : 1}`,
      {
        method: 'GET',
      },
    );
  };
  export const movetreeChildern = (path: string, isOwner?: any) => {
    return http<searchTypes.IFolder>(
      `/rman/v1/folder/children?folderPath=${encodeURIComponent(
        path,
      )}%2F&isChildCount=true${isOwner ? '&isOwner=true' : ''}`,
      {
        method: 'GET',
      },
    );
  };
  //移动
  export const entityMove = (data: copyandmoveTypes.ImoveData) => {
    return http('/entity/move', {
      method: 'POST',
      data: data,
    });
  };
  //复制
  export const entityCopy = (
    data: copyandmoveTypes.IcopyData,
    isShare?: any,
  ) => {
    return http(`/entity/copy${isShare === 1 ? '?isShare=true' : ''}`, {
      method: 'POST',
      data: data,
    });
  };
  //共享
  export const entityShare = (data: any) => {
    return http(`/rman/v1/share/entity`, {
      method: 'POST',
      data: data,
    });
  };
  //取消共享
  export const entityShareCancel = (contentId: any) => {
    return http(`/rman/v1/share/cancel/entity`, {
      method: 'POST',
      params: {
        contentId,
      },
    });
  };
}

export default copyandmoveApis;
