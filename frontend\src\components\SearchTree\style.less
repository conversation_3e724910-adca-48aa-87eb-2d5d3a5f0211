@import '../../themes/variables.less';
@search-item-height: 40px;

.search-tree {
  .ant-tree.ant-tree-directory {
    .ant-tree-treenode .ant-tree-node-content-wrapper {
      line-height: @search-item-height;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-left: -6px;
      // margin: 0 50px 0 27px;
      .ant-tree-iconEle {
        display: inline-flex;
        height: @search-item-height;
        align-items: center;
        justify-content: center;
        font-size: 15px;
        // margin-left: -28px;
        &:empty{
            display: none;
        }
  
  
  
        .point {
          width: 5px;
          height: 5px;
          background-color: var(--primary-color);
          display: inline-block;
          border-radius: 50%;
        }
      }
  
      &.ant-tree-node-selected {
          .ant-tree-iconEle .point {
            background-color: #fff;
          }
        }
      .ant-tree-title{
        margin-left: 4px;
        .root_node_title{
          font-size: 16px;
        }
      }
    }
    .ant-tree-list{
      .unreadNums{
        width: 20px;
        height: 20px;
        background: #FF5252;
        box-shadow: 0px 2px 4px 0px rgba(253, 118, 118, 0.1);
        border-radius: 50%;
        color: #FFFFFF;
        line-height: 20px;
        display: inline-block;
        text-align: center;
      }
      .ant-tree-treenode{
        margin-bottom: 4px;
      }
      .split_node{
        height: 0;
        width: 90%;
        border-bottom: 1px solid #dedede;
        margin: 10px 5% 18px 5%;
      }
      .ant-tree-list-holder{
        ::-webkit-scrollbar{
          width: 2px;
        }
        ::-webkit-scrollbar-track {/*滚动条里面轨道*/
          --webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
          border-radius: 10px;
          background: #EDEDED;
        }
        ::-webkit-scrollbar-thumb {/*滚动条里面小方块*/
          border-radius: 10px;
          //  --webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
          // background: #535353;
          background: var(--primary-color);
        }
        >div{
          height: 100% !important;
          .ant-tree-list-holder-inner{
            overflow-y: auto;
            height: calc(100vh - 165px);
          }
        }
      }
    }
    .ant-tree-treenode-selected{
      // background-color: color(~`colorPalette('@{second-color}', 2) `); 
      // background-color: var(--active-bg-color);
      // background-color: @menu-item-active-bg;
      span.ant-tree-node-selected{
        color: var(--primary-color);
        .ant-tree-title{
          .ant-badge{
              color: var(--primary-color);
          }
        }
      }
    }
    .ant-tree-treenode-selected .ant-tree-switcher{
      color: var(--primary-color);
    }
    .ant-tree-treenode-selected::before, .ant-tree-treenode-selected:hover::before{
        // background-color: color(~`colorPalette('@{second-color}', 2) `); 
        background-color: var(--primary-bg-color);
        // background-color: @menu-item-active-bg;
      }
    
  } 
      
  
  
  
  

  .ant-tree .ant-tree-switcher {
    line-height: 35px;
    // display: inline-flex;
    height: 40px;
    align-items: center;
    justify-content: center;
    // position: absolute;
    // right:0;
    margin-left: 15px;
    .ant-tree-switcher-icon {
      font-size: 14px;
      vertical-align: middle;
    }
  }

  .ant-tree-indent-unit {
    width: 12px;
  }

}
.mobile_tree{
  .ant-tree{
    .ant-tree-list{
      .ant-tree-list-holder-inner{
        .ant-tree-treenode{
          .ant-tree-switcher{
            .ant-tree-switcher-icon{
              font-size: 24px;
            }
          }
          .ant-tree-node-content-wrapper{
            margin-left: 0;
          }
        }
      }
    }
  }
}
