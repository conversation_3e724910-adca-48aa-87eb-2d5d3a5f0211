// @ts-nocheck
import React from 'react';
import { ApplyPluginsType, dynamic } from 'D:/公司项目/云上川大/rman/frontend/node_modules/@umijs/runtime';
import * as umiExports from './umiExports';
import { plugin } from './plugin';
import LoadingComponent from '@/components/loading/loading';

export function getRoutes() {
  const routes = [
  {
    "path": "/",
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'layout__guideLayout__index' */'@/layout/guideLayout/index'), loading: LoadingComponent}),
    "routes": [
      {
        "path": "/",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__index' */'@/pages/index'), loading: LoadingComponent}),
        "title": "home.title",
        "redirect": "/basic",
        "exact": true
      },
      {
        "path": "/basic",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'layout__basicLayout' */'@/layout/basicLayout'), loading: LoadingComponent}),
        "routes": [
          {
            "exact": true,
            "path": "/basic",
            "redirect": "/basic/rmanList"
          },
          {
            "exact": true,
            "path": "/basic/contentlist",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__contentlibrary__contentList' */'@/pages/contentlibrary/contentList'), loading: LoadingComponent}),
            "title": "search.title"
          },
          {
            "exact": true,
            "path": "/basic/similarityGraph",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__contentlibrary__similarityGraph' */'@/pages/contentlibrary/similarityGraph'), loading: LoadingComponent}),
            "title": "search.title"
          },
          {
            "exact": true,
            "path": "/basic/rmanCenterList",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__contentlibrary__contentList' */'@/pages/contentlibrary/contentList'), loading: LoadingComponent}),
            "title": "search.title"
          },
          {
            "exact": true,
            "path": "/basic/rmanCenterList/?hidebanner=true",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__contentlibrary__contentList' */'@/pages/contentlibrary/contentList'), loading: LoadingComponent}),
            "title": "search.title"
          },
          {
            "exact": true,
            "path": "/basic/rmanList",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__contentlibrary__contentList' */'@/pages/contentlibrary/contentList'), loading: LoadingComponent}),
            "title": "search.title"
          },
          {
            "exact": true,
            "path": "/basic/rmanList/mobileResourceGroup",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__mobile__resourceGroup' */'@/pages/mobile/resourceGroup.tsx'), loading: LoadingComponent}),
            "title": "search.title"
          },
          {
            "exact": true,
            "path": "/basic/out",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__contentlibrary__contentList' */'@/pages/contentlibrary/contentList'), loading: LoadingComponent}),
            "title": "search.title"
          },
          {
            "exact": true,
            "path": "/basic/resourceSelect",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'components__ResourceSelect' */'@/components/ResourceSelect'), loading: LoadingComponent})
          },
          {
            "exact": true,
            "path": "/basic/rmanDetail/:contentId",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__contentlibrary__contentDetail__detail' */'@/pages/contentlibrary/contentDetail/detail'), loading: LoadingComponent}),
            "title": "素材详情"
          },
          {
            "exact": true,
            "path": "/basic/rmanDetailV2/:contentId",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__contentlibrary__contentDetail__detailV2' */'@/pages/contentlibrary/contentDetail/detailV2'), loading: LoadingComponent}),
            "title": "素材详情"
          },
          {
            "exact": true,
            "path": "/basic/rmanDetailV3/:contentId",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__contentlibrary__contentDetail__detailV3' */'@/pages/contentlibrary/contentDetail/detailV3'), loading: LoadingComponent}),
            "title": "素材详情"
          },
          {
            "exact": true,
            "path": "/basic/contentDetail/:contentId",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__contentlibrary__contentDetail__detail' */'@/pages/contentlibrary/contentDetail/detail'), loading: LoadingComponent}),
            "title": "素材详情"
          },
          {
            "exact": true,
            "path": "/basic/shareDetail/:link",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__shareDetail' */'@/pages/shareDetail'), loading: LoadingComponent}),
            "title": "分享详情"
          }
        ]
      },
      {
        "path": "/uploadMobile",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__uploadMobile' */'@/pages/uploadMobile'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/mapv3",
        "title": "资源图谱",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Mapv3' */'@/pages/Mapv3'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/task",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'layout__basicLayout' */'@/layout/basicLayout'), loading: LoadingComponent}),
        "routes": [
          {
            "exact": true,
            "path": "/task/taskprogress",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__taskprogress' */'@/pages/taskprogress'), loading: LoadingComponent}),
            "title": "任务进度"
          }
        ]
      },
      {
        "path": "/login",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__login' */'@/pages/login'), loading: LoadingComponent}),
        "title": "login.title",
        "exact": true
      },
      {
        "path": "/upload",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__upload__upload' */'@/pages/upload/upload'), loading: LoadingComponent}),
        "title": "上传测试",
        "exact": true
      },
      {
        "path": "/entitylist",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__management__metadata__basic' */'@/pages/management/metadata/basic'), loading: LoadingComponent}),
        "title": "元数据列表",
        "exact": true
      },
      {
        "path": "/entity/:moduleType/:code",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__management__metadata__detail' */'@/pages/management/metadata/detail'), loading: LoadingComponent}),
        "title": "元数据配置",
        "exact": true
      },
      {
        "path": "/basicentity/:code/:id",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__management__metadata__basic__detail' */'@/pages/management/metadata/basic/detail'), loading: LoadingComponent}),
        "title": "基础元数据配置",
        "exact": true
      },
      {
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__404' */'@/pages/404'), loading: LoadingComponent}),
        "title": "404",
        "exact": true
      }
    ]
  }
];

  // allow user to extend routes
  plugin.applyPlugins({
    key: 'patchRoutes',
    type: ApplyPluginsType.event,
    args: { routes },
  });

  return routes;
}
