import React, {
    FC,
    useEffect,
    useRef,
    useState,
} from 'react';
import contentListApis from '@/service/contentListApis';
import { useSelector, useIntl } from 'umi';
import { Checkbox, Button, Empty, Pagination, message, Popconfirm } from 'antd';
import { optionType_ } from "@/utils";
import './questionList.less'
import timecodeconvert from '@/components/time-code/timeCode';
import ResourcePreviewModal from '@/components/ResourcePreviewModal';
const CheckboxGroup = Checkbox.Group;
interface props {
    questionList: any,
    frameRate: number,
    canEdit: boolean,
    handleAddTopic: () => void,
    questionVersion: string,
    getQuestionList: () => void,
    getQuestionPoint: () => void,
    setQuestionItem: (item: any) => void,
    setSelectType: (item: any) => void,
    setOperation: (item: any) => void,
    setviewTopicModalVisible: (item: boolean) => void,
    setQvModalVisible: (item: boolean) => void
}
const QuestionList: FC<props> = ({ questionList, questionVersion, frameRate, canEdit, setQvModalVisible, setOperation, getQuestionList, setSelectType, setviewTopicModalVisible, getQuestionPoint, handleAddTopic, setQuestionItem }) => {
    const { mobileFlag } = useSelector<{ config: any }, any>(
        ({ config }) => config
    )
    //全选start
    const [checkedList, setCheckedList] = useState<any[]>([]);
    const [indeterminate, setIndeterminate] = useState(false);
    const innerHTMLRef = useRef<any>(null);
    const [checkAll, setCheckAll] = useState(false);
    const intl = useIntl();
    const [entityModalVisible, setEntityModalVisible] = useState<boolean>(false);
    const [entityPreview, setEntityPreview] = useState<any>(null);
    // 全选
    const onCheckAllChange = (e: any) => {
        setCheckedList(e.target.checked ? questionList.map((item: { uniqueId: any; }) => item.uniqueId) : []);
        setIndeterminate(false);
        setCheckAll(e.target.checked);
    };
    // 单选
    const onChange = (check: Array<any>) => {
        setCheckedList(check);
        console.log(check, 'check')
        setIndeterminate(!!check.length && check.length < questionList.length);
        setCheckAll(check.length === questionList.length);
    };
    const deleteQuestion = () => {
        deleteQuestionBySingle(checkedList)
    }
    const view = (item: any) => {
        setQuestionItem(item)
        setviewTopicModalVisible(true)
    }
    const replace = (item: any) => {
        setQuestionItem(item)
        setSelectType("radio");
        handleAddTopic()
    }
    const add = () => {
        if (mobileFlag) {
            message.info(intl.formatMessage({ id: '暂不支持手机端，请前往电脑端操作' }));
            return
        }
        setSelectType("checkbox");
        setQuestionItem({})
        handleAddTopic()
    }
    const deleteQuestionBySingle = (list: any[]) => {
        contentListApis.deleteQuestionBySingle(list).then(_res => {
            getQuestionList()
            getQuestionPoint()
            setCheckAll(false);
            setIndeterminate(false);
            setCheckedList([]);
        })
    }
    const deleteItem = (item: any) => {
        deleteQuestionBySingle([item.uniqueId])
    }
    useEffect(() => {
        if (innerHTMLRef.current) {
          const images = innerHTMLRef.current.getElementsByTagName('img');
          Array.from(images).forEach((img: any) => {
            img.addEventListener('click', () => {
                setEntityModalVisible(true)
                setEntityPreview({ name: '预览', src: img.src, type: 'picture' });
            });
          });
        }
      }, [questionList]);
    return (
        <div  ref={innerHTMLRef} className='questionList'>
            <div style={{ margin: '10px 0' }}>
                <Checkbox
                    indeterminate={indeterminate}
                    onChange={onCheckAllChange}
                    checked={checkAll}
                    disabled={questionList.length === 0}
                >
                    {intl.formatMessage({ id: '全选' })}
                </Checkbox>
                {mobileFlag && <Button
                    disabled={!canEdit}
                    className={!canEdit ?
                        'vd_ghost_btn_disabled'
                        : 'vd_ghost_btn'}
                    onClick={() => {
                        setQvModalVisible(true)
                        setOperation('ADD')
                    }}
                    size="middle">
                    {intl.formatMessage({ id: '新建互动' })}
                </Button>}
                <Button
                    style={{margin: mobileFlag? '0 3px' : '0 10px'  }}
                    onClick={add}
                    disabled={questionVersion.length === 0 || !canEdit}
                    size="middle">
                    {intl.formatMessage({ id: '添加题目' })}
                </Button>
                <Popconfirm placement="bottom" title={intl.formatMessage({ id: '确定删除题目吗' })}
                    onConfirm={deleteQuestion}
                    okText={intl.formatMessage({ id: '确定' })}
                    cancelText={intl.formatMessage({ id: '取消' })}>
                    <Button
                        disabled={questionList.length === 0 || checkedList.length === 0 || !canEdit}
                    >
                    {intl.formatMessage({ id: '删除题目' })}
                    </Button>
                </Popconfirm>

            </div>
            <div style={{ height: '90%', overflow: 'auto' }}>
                <CheckboxGroup
                    value={checkedList}
                    onChange={onChange}
                    style={{ width: '100%' }}
                >
                    {
                        questionList.length ? questionList.map((item: any) => {
                            return (
                                <div key={item.uniqueId} style={{ marginBottom: '15px' }}>
                                    <div className="header">
                                        <div>
                                            <Checkbox value={item.uniqueId} />
                                            <span style={{marginLeft: '10px'}}>{timecodeconvert.l100Ns2Tc$1(item.pointIn, frameRate)}</span>
                                        </div>
                                        <div className="btn">
                                            <a onClick={() => { view(item) }}>{intl.formatMessage({ id: '查看' })}</a>
                                            <a style={{ margin: '0 10px' }} onClick={() => {
                                                if (canEdit) {
                                                    replace(item)
                                                }
                                                else {
                                                    message.error(intl.formatMessage({ id: '无权限' }));
                                                }
                                            }}>{intl.formatMessage({ id: '替换' })}</a>
                                            <Popconfirm placement="bottom" title={intl.formatMessage({ id: '确定删除题目吗' })}
                                                onConfirm={() => {
                                                    if (canEdit) {
                                                        deleteItem(item)
                                                    }
                                                    else {
                                                        message.error(intl.formatMessage({ id: '无权限' }));
                                                    }
                                                }}
                                                okText={intl.formatMessage({ id: '确定' })}
                                                cancelText={intl.formatMessage({ id: '取消' })}>
                                                <a>{intl.formatMessage({ id: '删除' })}</a>
                                            </Popconfirm>

                                        </div>
                                    </div>
                                    <div className="item-content">
                                        <span className='type'>{optionType_[item.questions_type]}</span>
                                        <div dangerouslySetInnerHTML={{ __html: item.questions_content }}></div>
                                    </div>
                                </div>
                            )
                        })
                            : <Empty description={intl.formatMessage({ id: '暂无题目' })} />}
                </CheckboxGroup>
            </div>
            {entityModalVisible && <ResourcePreviewModal
                modalVisible={entityModalVisible}
                modalClose={() => setEntityModalVisible(false)}
                resource={entityPreview}
                />}
        </div>
    )
}
export default QuestionList