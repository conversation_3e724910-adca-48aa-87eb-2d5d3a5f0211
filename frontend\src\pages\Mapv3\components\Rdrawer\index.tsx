import React, { FC, useState, useEffect, useRef } from 'react';
import './index.less';
import { useSelector, useDispatch, useLocation } from 'umi';
import { IconFont } from '@/components/iconFont';
const background_img = require('@/assets/imgs/icon/knowledge_bg.png');
import Icon,{
  CloseOutlined,
} from '@ant-design/icons';
import {
  Input,
  Button,
  Select,
  Spin,
  Modal,
  Tooltip,
  message,
  List,
  Tag,
  Tabs,
  Empty
} from 'antd';
const { TabPane } = Tabs;
const { confirm } = Modal;
import { IGlobalModelState,IGlobal } from '@/models/global';
import TextArea from 'antd/lib/input/TextArea';
import ResourceModal from '../../../../../components/ResourceModal';
import ResourcePreviewModal from '../../../../../components/ResourcePreviewModal';
import Entity from '@/components/entity/entity';
import chapterApis from '@/api/chapter';
import CourseQA from '@/pages/CourseQA/index';
import CourseQAdetail from '@/pages/CourseQA/detail';
import Askquestions from '../../../askquestions';
// 对比辨析组件
import TabComparativeanalysis from '../../../TabComparativeanalysis';
// 引用选择作业的弹窗
import TopicSelectModal from '@/pages/HomeworkManagement/components/TopicSelectModal';
// 作业组件
import HomeworkSubItem from '@/pages/HomeworkManagement/components/HomeworkSubItem';

// 引用svg
import { ReactComponent as chatgpticon} from '@/assets/imgs/icon/chatgpt.svg';

import { getTreebylevel,addlog} from '@/api/addCourse';
import { getSmartExplain } from '@/api/chatgpt';
import { createguid } from '../../../Editmap/util';
import { bindQuestionToNode,getknowledgelist,deleteQuestionToNode,questionsanswers,finishKnowledge,getStudyResourceList} from '@/api/coursemap';
import statisticsApi from "@/api/statistics";
import {defaultNodeData} from '../../../Editmap/util'

const Rdrawer: FC<any> = ({
  setVisible,
  x6node,
  isedit,
  updatanode,
  graph,
  centerednode,
  visible,
  onback,
  mapid,
  perviewtype,
  updatanodecompare,
  courseid,
  coursename
}) => {
  const location: any = useLocation();
  const parameterConfig = useSelector<any, any>(
    ({ global }) => global.parameterConfig,
  );
  const { parameterConfigObj,buttonPermission } = useSelector<
    { global: IGlobalModelState },
    IGlobalModelState
  >(state => state.global);
  const mapinfo: any = useSelector<any, any>(
    (state: { coursemap: { mapinfo: any } }) => state.coursemap.mapinfo,
  );
  const { userInfo } = useSelector<any, IGlobal>(state => state.global);

  const [selectNode, setSelectNode] = useState<any>(null); //选中的节点
  const [nodeinfo, setNodeinfo] = useState<any>(null); //选中的节点
  // const [courseknowledge, setCourseknowledge] = useState<any>([]);
  // const [knowledgeindex, setKnowledgeindex] = useState<number>(0);

  const [perviewvisit, setPerviewvisit] = useState<boolean>(false);
  const [perviewsrc, setPerviewsrc] = useState<string>(''); //预览路径
  // 引用的知识点
  const [referencedknowledge, setReferencedknowledge] = useState<any>([]);

  const [previewEntity, setPreviewEntity] = useState<any>(null);
  const [entityLoading, setEntityLoading] = useState<boolean>(false);

  // 选择资源弹窗的数据
  const [modalTreeData, setModalTreeData] = useState<ResourceModal.treeItem[]>(
    [],
  );
  //   选择资源弹窗的开关
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  // 资源预览相关state
  const [entityModalVisible, setEntityModalVisible] = useState(false);
  const [entityPreview, setEntityPreview] = useState<any>(null);

  const [modalLoading, setModalLoading] = useState<boolean>(false); //选择资源弹窗控制
  // 父级节点
  const [parentnode, setParentnode] = useState<any>([]);
  const [preorder, setPreorder] = useState<any>([]); //前序知识点
  const [nextorder, setNextorder] = useState<any>([]); //后序知识点

  const [seecontentid, setSeecontentid] = useState<string>(''); //查看内容id
  const [questionvisible, setQuestionvisible] = useState<number>(1); //1 是展示列表  2是添加评论  3是展示详情
  const [todetail, setTodetail] = useState<any>(null); //跳转到评论详情的 参数
  // 参考资料
  const [reference, setReference] = useState<any>([]);
  // 作业
  const [homework, setHomework] = useState<any>([]);
  // 添加题目的开关
  const [addtopicvisible, setAddtopicvisible] = useState<boolean>(false);
  // 当前是添加绑定资源还是添加参考资料
  const [addtype, setAddtype] = useState<string>('bindresource');
  //当前用户选择的所有答案
  const [useranswer, setUseranswer] = useState<any>([]);
  // 是否查看答案
  const [isseeanswer, setIsseeanswer] = useState<boolean>(false);

  // 当前查看视频的窗口是在哪里
  const [currentvideotype, setCurrentvideotype] = useState<number>(1);  //顶部

  const [explanation, setExplanation] = useState<string>(''); //解析

  // 智能解析loading
  const [intelligentloading, setIntelligentloading] = useState<boolean>(false);

  // 当前新绑定的资源
  const newBindResource = useRef<any>([]);

  // 当前已经学习完毕的资源
  const [finishresource, setFinishresource] = useState<any>([]);

  // 掌握率  完成率
  const [rate,setRate] = useState<any>({
    finishRate:0,
    masterRate:0
  })

  useEffect(() => {
    if (x6node) {
      const data = x6node.getData();
      setSelectNode((pre:any)=>{
        return {
          id: x6node.id,
          ...defaultNodeData,
          ...data,
          temp: new Date().getTime(),
        }
      });
      // 获取所有绑定的资源 并且 如果第一个是视频  默认播放
      if (data.bindresource.length) {
        let item = data.bindresource[0];
        let type = item.type.split('_');
        if(item.type == 'biz_sobey_point' || item.recousetype == 'point'){
          onEntityPreview(item.parentcontentId, {
            name: item.name,
            type: 'video',
            knowledge:{
              inpoint:item.inpoint,
              outpoint:item.outpoint
            }
          });
          setSeecontentid(item.contentId);
        }else if (type[type.length - 1] == 'video') {
          onEntityPreview(item.contentId, {
            name: item.name,
            type: type[type.length - 1],
          });
          setSeecontentid(item.contentId);
        }
      }
      let referencedarr = []
      // 获取所有的引用知识点
      if (data.quoteKnowledge?.length) {
        let nodearr = graph
          .getNodes()
          .filter((item: any) => item.store.data.data.type == '2');
        if (nodearr.length) {
          let arr = nodearr.filter((item: any) =>
            data.quoteKnowledge.includes(item.id),
          );
          setReferencedknowledge(arr);
          referencedarr = arr;
        }
      } else {
        setReferencedknowledge([]);
      }

      if(referencedarr.length){
        let str = data.explanation;
        referencedarr.forEach((element:any) => {
          // 替换匹配到的字符串
          str = str.replace(element.store.data.data.label,`<span style="color:#549CFF;cursor: pointer;">${element.store.data.data.label}</span>`);
        });
        setExplanation(str);
      }else{
        setExplanation(data.explanation);
      }


      // // 获取所有的参考资料
      // if(data.referenceMaterial?.length){
      //   setReference(data.referenceMaterial);
      // }

      // // 获取所有的
      // if (data.homework?.length) {
      //   setUseranswer(
      //     data.homework.map((item: any) => item.questions_answers || []),
      //   );
      // }

      // 获取所有的出点
      let pre = graph.getOutgoingEdges(x6node);
      // 入点
      let next = graph.getIncomingEdges(x6node);
      // 1连接到2   1就是2的前序  2就是1的后序
      if (pre && pre.length) {
        let node: any = [];
        pre.forEach((element: any) => {
          if (element.store.data.data.type == 3) {
            let cell = graph.getCellById(element.store.data.target.cell);
            node.push(cell);
          }
        });
        setNextorder(node);
      } else {
        setNextorder([]);
      }

      if (next && next.length) {
        let parent: any = [];
        let node: any = [];
        // type 1包含 2等价 3后续
        next.forEach((element: any) => {
          if (element.store.data.data.type == 3) {
            let cell = graph.getCellById(element.store.data.source.cell);
            node.push(cell);
          }

          if (element.store.data.data.type == 1) {
            let cell = graph.getCellById(element.store.data.source.cell);
            parent.push(cell);
          }
        });
        setPreorder(node);
        setParentnode(parent);
      } else {
        setPreorder([]);
        setParentnode([]);
      }
      setPreviewEntity(null);
      getlist();
      getresourcelearning();
      getRate();
      setUseranswer([]);
      setIsseeanswer(false);
    }
  }, [x6node]);

  /**
   * resource资源对象中取资源url
   *
   * @param {*} resource
   * @return {*}
   */
  const getPreviewPath = (resource: any) => {
    if (resource && resource.fileGroups) {
      const { fileGroups } = resource;
      const previewFile = fileGroups.filter(
        (file: any) => file.typeCode === 'previewfile',
      );
      const videogroupFile = fileGroups.filter(
        (file: any) => file.typeCode === 'sourcefile',
      );
      return previewFile.length > 0
        ? previewFile[0].fileItems[0].filePath
        : videogroupFile[0].fileItems[0].filePath;
    }
    return '';
  };
  /**
   * 查询资源预览网址，显示资源详情modal
   *
   * @param {string} id
   * @param {*} { name, type }
   */
  const onEntityPreview = (id: string, { name, type,knowledge }: any) => {
    setEntityLoading(true);
    chapterApis
      .resourceDetailNew([id])
      .then(res => {
        if (res.status == 200) {
          if(res.data.length){
            if (type == 'document') {
              setPerviewvisit(true);
              setPerviewsrc(res.data[0].filePath);
            } else {
              console.log('res',res);
              setPreviewEntity({
                src: res.data[0].filePath,
                name,
                type,
                knowledge:knowledge ? knowledge :{
                  inpoint:0,
                }
              });
            }
          }
        }
      })
      .finally(() => setEntityLoading(false));
  };

  // 取消知识点关联资源
  const removevideobyid = (e: any, item: any, index: number) => {
    e.stopPropagation();
    confirm({
      title: `确认取消"${item.name}" 与知识点的关联吗？`,
      onOk() {
        // 根据下标删除数组中的元素
        let data = selectNode.bindresource.filter(
          (item: any, index2: number) => index2 != index,
        );
        setSelectNode({
          ...selectNode,
          bindresource: data,
          temp: new Date().getTime(),
        });
        updatanode(x6node.id, {
          ...selectNode,
          bindresource: data,
        });
      },
      onCancel() {},
    });
  };

  // 取消知识点关联参考资料
  const removeReference = (e: any, item: any, index: number) => {
    e.stopPropagation();
    confirm({
      title: `确认取消"${item.name}" 与知识点的关联吗？`,
      onOk() {
        // 根据下标删除数组中的元素
        let data = selectNode.referenceMaterials.filter(
          (item: any, index2: number) => index2 != index,
        );
        setSelectNode({
          ...selectNode,
          referenceMaterials: data,
          temp: new Date().getTime(),
        });
        updatanode(x6node.id, {
          ...selectNode,
          referenceMaterials: data,
        });
      },
      onCancel() {},
    });
  };

  // 取消知识点关联题目
  const removeQuestion = (e: any, item: any, index: number) => {
    e.stopPropagation();
    confirm({
      title: `确认删除题目吗？`,
      onOk() {
        let deletearr:any = [];
        // 根据下标删除数组中的元素
        let data = selectNode.homework.filter((item: any, index2: number) => {
          if(index2 != index){
            return true;
          }else{
            deletearr.push(item);
            return false;
          }
        });
        setSelectNode({
          ...selectNode,
          homework: data,
          temp:new Date().getTime()
        });
        updatanode(x6node.id, {
          ...selectNode,
          homework: data,
        });
        deletequestion(deletearr);
      },
      onCancel() {},
    });
  };

  //显示Modal
  const showModal = (e: string) => {
    setModalLoading(true);
    setAddtype(e);
    const getTreeData = () => {
      getTreebylevel().then(res => {
        if (res && res.success) {
          let treeData = forTree(res.data, []);
          setModalTreeData(treeData);
          setTimeout(() => {
            setModalLoading(false);
            setModalVisible(true);
          }, 100);
        } else {
          console.error(res);
        }
      });
    };
    // 遍历目录树
    const forTree = (tree: any, parentsKeys: string[]) => {
      return tree.map((item: any) => {
        return {
          key: item.id,
          parentsKeys,
          title: item.name,
          path: item.path,
          children: item.children
            ? forTree(item.children, [...parentsKeys, item.code])
            : [],
        };
      });
    };
    getTreeData();
  };

  // 动态渲染文件类型
  const getTag = (item: any) => {
    if(item.type == 'biz_sobey_point' || item.recousetype == 'point'){
      return <Tag color="green">视频知识点</Tag>;
    } else if (item.type == 'biz_sobey_video') {
      return <Tag color="green">视频</Tag>;
    } else if (item.type == 'biz_sobey_audio') {
      return <Tag color="blue">音频</Tag>;
    } else if (item.type == 'biz_sobey_picture') {
      return <Tag color="orange">图片</Tag>;
    } else if (item.type == 'biz_sobey_document') {
      return <Tag color="purple">文档</Tag>;
    } else{
      return <Tag color="purple">其他</Tag>;
    }
  };

  // 知识点关联资源
  const addrecouse = (selectrecouse: any) => {
    let obj = selectNode;
    let newdata = selectrecouse.map((item: any) => {
      return {
        name: item.name,
        contentId: item.contentId,
        contentId_: item.contentId_,
        type: item.type_,
        keyframe_: item.keyframe_,
        tempid: createguid(),
      };
    });
    if (obj.bindresource && obj.bindresource.length) {
      let newarr = newdata.filter((item: any) => {
        return obj.bindresource.every(
          (item2: any) => item.contentId_ != item2.contentId_,
        );
      });
      if (newarr.length) {
        obj.bindresource = [...obj.bindresource, ...newarr];
        createlogdata(obj,newarr,1)
      } else {
        message.warning('已存在该资源');
        setModalVisible(false);
        return;
      }
    } else {
      obj.bindresource = newdata || [];
      createlogdata(obj,newdata,1)
    }
    setSelectNode({
      ...selectNode,
      ...obj,
      temp: new Date().getTime(),
    });
    updatanode(x6node.id, obj);
    setModalVisible(false);
    addlogs();
  };


   // 知识点绑定资源知识点
   const addpoint = (selectrecouse: any) =>{
    let obj = selectNode;
    let newdata = selectrecouse.map((item: any) => {
        return {
            recousetype:'point',
            name: item.title,
            contentId: item.guid_,
            contentId_: item.guid_,
            type: 'biz_sobey_point',
            keyframe_: item.keyframepath,
            tempid: createguid(),
            keyframeno:item.keyframeno,
            parentcontentId:item.contentId,
            fragment_description:item.fragment_description,
            inpoint:item.inpoint,
            outpoint:item.outpoint,
            parentname:item.name,
            createDate: item.createDate,
            suffix:item.suffix
        }
    })
    if (obj[addtype] && obj[addtype].length) {
        let newarr = newdata.filter((item: any) => {
            return obj[addtype].every((item2: any) => item.contentId_ != item2.contentId_);
        });
        if (newarr.length) {
            obj[addtype] = [...obj[addtype], ...newarr];
            createlogdata(obj,newarr,2)
        } else {
            message.warning('已存在该知识点');
            setModalVisible(false);
            return;
        }
    } else {
        obj[addtype] = newdata || [];
        createlogdata(obj,newdata,2)
    }
    setSelectNode({
      ...selectNode,
      ...obj,
      temp: new Date().getTime(),
    });
    updatanode(x6node.id, obj);
    setModalVisible(false);
    addlogs();
}


const addlogs = () =>{
  if(newBindResource.current.length){
      addlog(mapinfo.mapName,newBindResource.current).then((res)=>{
          if(res.success){
              newBindResource.current = [];
              console.log('添加资源日志记录成功！')
          }else{
              console.log('添加资源日志记录失败！')
          }
      })
  }
}

const createlogdata =(node:any,arr:any,type:number)=>{
  arr.forEach((item:any)=>{
    newBindResource.current.push({
      "contentId": item.contentId, //资源id
      "name": item.name,  //资源名称
      "courseKnowledgeName": node.label,  //知识节点名称
      "knowledgeNames": type==2 ? [item.name]:[]
    });
  })
}

  // 知识点添加参考资料
  const addreferenceMaterials = (selectrecouse: any) => {
    let obj = selectNode;
    let newdata = selectrecouse.map((item: any) => {
      return {
        name: item.name,
        contentId: item.contentId,
        contentId_: item.contentId_,
        type: item.type_,
        keyframe_: item.keyframe_,
        tempid: createguid(),
      };
    });
    if (obj.referenceMaterials && obj.referenceMaterials?.length) {
      let newarr = newdata.filter((item: any) => {
        return obj.referenceMaterials.every(
          (item2: any) => item.contentId_ != item2.contentId_,
        );
      });
      if (newarr.length) {
        obj.referenceMaterials = [...obj.referenceMaterials, ...newarr];
        createlogdata(obj,newarr,1)
      } else {
        message.warning('已存在该资源');
        setModalVisible(false);
        return;
      }
    } else {
      obj.referenceMaterials = newdata || [];
      createlogdata(obj,newdata,1)
    }
    setSelectNode({
      ...selectNode,
      ...obj,
      temp: new Date().getTime(),
    });
    updatanode(x6node.id, obj);
    setModalVisible(false);
    addlogs();
  };

  // 智能详解
  const getGptdetail = () => {
    setIntelligentloading(true);
    let label =  selectNode?.label;
    if(label && label!=''){
      getSmartExplain(label,((responseText:any)=>{
        if(responseText && responseText!=''){
          setSelectNode({
            ...selectNode,
            explanation: responseText,
            temp: new Date().getTime(),
          });
        }
      }),((responseText:any)=>{
        if(responseText){
          updatanode(x6node.id, {
            ...selectNode,
            explanation: responseText,
          });
        }
        setIntelligentloading(false);
      }))
    }else{
      message.info("请输入当前知识节点名称");
    }
  }

  // 一键引用
  const onOneKeyQuote = () => {
    let nodearr = graph
      .getNodes()
      .filter((item: any) => item.store.data.data.type == '2');
    if (selectNode?.explanation) {
      // 判断详解是否包含知识点的名称
      let iscontain = nodearr.filter(
        (item: any) =>
          selectNode.explanation.indexOf(item.store.data.data.label) >= 0 &&
          selectNode.label != item.store.data.data.label,
      );
      if (iscontain.length) {
        message.success('一键引用成功');
        setReferencedknowledge(iscontain);
        setSelectNode({
          ...selectNode,
          quoteKnowledge: iscontain.length
            ? iscontain.map((item: any) => item.id)
            : [],
          temp: new Date().getTime(),
        });
        updatanode(x6node.id, {
          ...selectNode,
          quoteKnowledge: iscontain.length
            ? iscontain.map((item: any) => item.id)
            : [],
        });
      } else {
        message.warning(
          '引用失败，图中无可用内容。类型为知识点的节点才可引用！',
        );
        return;
      }
    } else {
      message.warning('请先填写知识点详解');
      return;
    }
  };

  // 根据下标删除元素
  const deleteQuote = (e: any, index: number) => {
    e.stopPropagation();
    let arr: [] = referencedknowledge.filter(
      (item: any, index2: number) => index2 != index,
    );
    setReferencedknowledge(arr);
    updatanode(x6node.id, {
      ...selectNode,
      quoteKnowledge: arr.length ? arr.map((item: any) => item.id) : [],
    });
  };

  // 获取当前节点的前序节点
  const getlevelable = (id: string) => {
    //获取所有前序节点
    const nodes = graph.getCellById(id);
    const Predenode = graph.getPredecessors(nodes);
    let  str = nodes.store.data.data.label;

    if(Predenode.length >= 1){
      str = Predenode[0].store.data.data.label + '/' + str;
    }
    if(Predenode.length >= 2){
      str = Predenode[1].store.data.data.label + '/' + str;
    }
    return str;
  };

  // 获取当前节点的所有试题
  const getlist = () =>{
    getknowledgelist({
      "mapId": mapid,
      "courseId":courseid,
      // "mapName": mapinfo.mapName,
      "nodeId": x6node.id,
      "stuCode": userInfo.userCode
    }).then((res:any)=>{
      if(res.status == 200 && res.data){
        setNodeinfo(res.data);
        let newuseranswer:any = [];
        let newhomework = res.data.questions || [];
        newhomework.forEach((item:any,index:number)=>{
          newuseranswer.push(item.student_answers || []);
        })
        setUseranswer(newuseranswer);
        let newdata = null;
        setSelectNode((pre:any)=>{
          newdata = {
            ...pre,
            homework: newhomework,
            temp: new Date().getTime(),
          }
          return newdata
        });
        // updatanode(x6node.id, newdata);
      }
    })
  }

  // 调用试题的新增接口
  const addquestion = (arr: any) => {
    let data:any = {
      entity: selectNode.label,  //知识点名称
      // entity_id: mapinfo.id,  //知识点id
      courseId: courseid, //课程id
      courseName: coursename, //课程名称
      mapId: mapid, //地图id
      mapName:mapinfo.name, //地图名称
      nodeId: selectNode.id, //知识点id
      question_ids:arr.map((item:any)=>item.id),
      questions: arr, //试题数组
      stuCode: userInfo.userCode, //学生code
      stuName: userInfo.nickName, //学生名称
    }
    if(selectNode?.homework?.length){
      data.id = selectNode.homework[0].id;
    }

    if(selectNode?.homework?.length){
      data.id = selectNode.homework[0].id;
    }

    bindQuestionToNode(data).then((res: any) => {
      if(res.status == 200){
        message.success('添加试题成功');
      }else{
        message.error('添加试题失败');
      }
    });
  };

  //删除试题
  const deletequestion = (item:any) =>{
    deleteQuestionToNode({
      // id:nodehomewordid.current,
      knowledgeTestQuestions:item,
      courseId:courseid,
      mapId: mapid,
      nodeId: x6node.id, //知识点id
    }).then((res:any)=>{
      if(res.status == 200 && res.data){
        message.success('删除试题成功');
      }else{
        message.error('删除试题失败');
      }
      getlist();
    })
  }

  // 提交答案
  const submitanswer = () => {
    confirm({
      title: `查看答案`,
      content: `若此刻提交，未做的题目将被记为0分，是否查看？`,
      onOk() {
        setIsseeanswer(!isseeanswer);
        let newhomework = selectNode.homework.map((item:any,index:number)=>{
          return {
            ...item,
            student_answers:useranswer[index] || []
          }
        })
        questionsanswers(nodeinfo.id,newhomework).then((res: any) => {
          if (res.status == 200 && res.data) {
            message.success('提交成功');
            // getlist();
          } else {
            message.error('提交失败');
          }
        });
      },
      onCancel() {},
    });
  }

  // 获取资源学习记录
  const getresourcelearning = () =>{
    if(perviewtype != 0 && perviewtype != 3 && location.pathname!='/perviewemap'){
      getStudyResourceList({
        courseId: courseid,
        mapId: mapid,
        nodeId: x6node.id,
      }).then((res:any)=>{
        if(res.status == 200){
          setFinishresource(res.data);
        }else{
          message.error('获取资源学习记录失败');
        }
      })
    }
  }

  // 添加资源学习
  const addResourcelearning = ()=>{
    let flage:any = [];
    setFinishresource((pre:any)=>{
      flage = pre;
      return pre
    })
    // 判断是否已经学习过了
    if(flage.includes(seecontentid)){
      return
    }

    let type = 0 ; //0 视频 1文档 2 图片 3音频 4 作业 5 问答  课程地图目前没有 作业 和 问答
    if(previewEntity.type == 'biz_sobey_video'){
      type = 0;
    }else if(previewEntity.type == 'biz_sobey_audio'){
      type = 3;
    }else if(previewEntity.type == 'biz_sobey_picture'){
      type = 2;
    }else if(previewEntity.type == 'biz_sobey_document'){
      type = 1;
      console.log('添加稳定学习记录')
    }

    let data = [{
      "courseId": courseid,
      "courseName": coursename,
      "mapId": mapid,
      "nodeId": x6node.id,
      "resourceId": seecontentid,
      "resourceType": type,
      "userCode": userInfo.userCode,
    }]
    finishKnowledge(data).then((res:any)=>{
      if(res.status == 200){
        // message.success('添加成功');
      }else{
        message.error('添加学习记录失败！');
      }
      getresourcelearning();
      getRate();
    })
  }

  // 查询当前知识点 完成率 掌握率
  const getRate = () =>{
    if(perviewtype != 0 && perviewtype != 3 && location.pathname!='/perviewemap'){
      statisticsApi.getTeacherOrStudentRate({
        "courseId": courseid,
        "mapId": mapid,
        "nodeId": x6node.id,
        "type": perviewtype == 1 ? 0 :1   //教师端 1  学生端 0
      }).then((res:any)=>{
        if(res.data.status == 200){
          setRate({
            finishRate:res.data.data.finishRate,
            masterRate:res.data.data.masterRate
          })
        }else{
          message.error('获取完成率失败！');
        }

      })
    }
  }

  return (
    <Tabs defaultActiveKey="1" onChange={(e)=>{
      if(perviewtype !=0 && perviewtype != 3 && e=='1'){
        getRate();
      }
    }}>
      <TabPane tab="知识点详情" key="1">
        <div className="rdrawer">
          <div className="drawer_view">
            {/* <div className="options_view">
                    <Button type="primary" icon={<IconFont type="iconjiaohuan" />} onClick={() => getbianxiAll(selectNode.id)}>对比辨析</Button>
                    <Button type="primary" icon={<IconFont type="icondaochu1" />} style={{ marginLeft: '20px' }} onClick={()=>{}}>关联节点</Button>
                </div> */}
            {
              (perviewtype != 0 && perviewtype != 3 && location.pathname!='/perviewemap') &&
              <div className='rate_box'>
                  <span className="rate_title">总完成率：</span>
                  <span className="rate_value">{Number(rate.finishRate)}%</span>
                  <span className="rate_title" style={{marginLeft:'20px'}}>总掌握率：</span>
                  <span className="rate_value">{Number(rate.masterRate)}%</span>
              </div>
            }
            {
             (perviewtype != 0 && perviewtype != 3 && location.pathname!='/perviewemap') && <div className="divider_dashed" style={{ marginTop: '5px' }}></div>
            }
            <div className="title">
              <span
                className="span1"
                onClick={() => {
                  centerednode(selectNode.id);
                }}
                title={selectNode ? selectNode.label : '暂无名称'}
              >
                {selectNode ? selectNode.label : '暂无名称'}
              </span>
              {isedit ? (
                <div>
                  {
                    // 判断是否有权限
                    buttonPermission.includes('Intelligentdetail') ?
                    <Button
                      size="small"
                      type="primary"
                      style={{marginRight:'10px'}}
                      icon={<Icon component={chatgpticon} ></Icon>}
                      onClick={() => getGptdetail()}
                      loading={intelligentloading}
                    >
                      智能详解
                    </Button>:null
                  }
                  <Button
                    size="small"
                    type="primary"
                    onClick={() => onOneKeyQuote()}
                  >
                    一键引用
                  </Button>
                </div>
              ) : null}
            </div>
            <div className="detail">
              {isedit ? (
                <TextArea
                  value={selectNode?.explanation}
                  rows={4}
                  placeholder="请输入详解"
                  onBlurCapture={e => {
                    updatanode(x6node.id, selectNode);
                  }}
                  onPressEnter={(e: any) => {
                    updatanode(x6node.id, selectNode);
                  }}
                  onChange={(e: any) => {
                    setSelectNode({
                      ...selectNode,
                      explanation: e.target.value,
                      temp: new Date().getTime(),
                    });
                  }}
                />
              ) : (
                // <p>{selectNode?.explanation}</p>
                // 这里是我自己截取拼接的字段 来源是安全的 所以不用xss过滤
                <div>
                  <div dangerouslySetInnerHTML={{__html:explanation}}></div>
                </div>
              )}
            </div>

            <div className="other_view">
              <div className="redio_view"></div>
              <span className="other_title">引用知识点</span>
              {referencedknowledge?.map((item: any, index: number) => {
                return (
                  <div
                    className="other_item"
                    key={index}
                    onClick={() => {
                      centerednode(item.id);
                    }}
                  >
                    <Tooltip title={getlevelable(item.id)}>
                      <span className="span_name">
                        {item.store.data.data.label}
                      </span>
                    </Tooltip>

                    {isedit ? (
                      <CloseOutlined
                        className="icon_delete"
                        onClick={e => deleteQuote(e, index)}
                      />
                    ) : null}
                  </div>
                );
              })}
              {referencedknowledge.length && isedit ? (
                <Button
                  onClick={() => {
                    setReferencedknowledge([]);
                    setSelectNode({
                      ...selectNode,
                      quoteKnowledge: [],
                      temp: new Date().getTime(),
                    });
                    updatanode(x6node.id, {
                      ...selectNode,
                      quoteKnowledge: [],
                    });
                  }}
                  icon={
                    <IconFont
                      title="清空引用"
                      style={{ fontSize: '14px' }}
                      type="iconhuishouzhan-huise"
                    />
                  }
                  style={{
                    marginLeft: '10px',
                    height: '28px',
                    marginTop: '5px',
                  }}
                  size="small"
                  type="primary"
                  shape="round"
                  danger
                >
                  清空引用
                </Button>
              ) : null}
            </div>

            <div className="divider_dashed" style={{ marginTop: '5px' }}></div>
            <div className="other_view">
              <div className="redio_view"></div>
              <span className="other_title">前序知识点</span>
              {preorder?.map((item: any, index: number) => {
                return (
                  <div
                    className="other_item"
                    key={index}
                    onClick={() => {
                      centerednode(item.id);
                    }}
                  >
                    <span className="span_name">
                      {item.store.data.data.label}
                    </span>
                  </div>
                );
              })}
            </div>

            <div className="divider_dashed" style={{ marginTop: '5px' }}></div>

            <div className="other_view">
              <div className="redio_view"></div>
              <span className="other_title">后续知识点</span>
              {nextorder?.map((item: any, index: number) => {
                return (
                  <div
                    className="other_item"
                    key={index}
                    onClick={() => {
                      centerednode(item.id);
                    }}
                  >
                    <span className="span_name">
                      {item.store.data.data.label}
                    </span>
                  </div>
                );
              })}
            </div>
            <div className="divider_dashed" style={{ marginTop: '5px' }}></div>
            <div className="video_view">
              <div className="redio_view"></div>
              <span className="title_span">绑定资源</span>
              <div style={{ flex: 1, textAlign: 'right' }}>
                {isedit ? (
                  <Button
                    loading={modalLoading}
                    size="small"
                    type="primary"
                    onClick={e => showModal('bindresource')}
                  >
                    添加
                  </Button>
                ) : null}
              </div>
            </div>
            {/* 预览资源 */}
            <Spin spinning={entityLoading}>
              {(previewEntity && visible == 1 && selectNode.bindresource.length && currentvideotype == 1) ? (
                <div className="entity-preview" style={{width:'100%',position:'relative'}}>
                  <div className="video-wrap">
                    <Entity
                      type={previewEntity.type}
                      src={previewEntity.src}
                      id={'previewVideo'}
                      isAutoplay={false}
                      pip={false}
                      knowledge={previewEntity.knowledge}
                      onListener={(e:string)=>{
                        // 只有在学生端才会有学习记录
                        if(e == 'ended' && perviewtype == 2 && location.query.isJoin == 'true'){
                          addResourcelearning();
                        }
                      }}
                    />
                  </div>
                </div>
              ) : null}
            </Spin>
            <List
              bordered
              locale={{ emptyText: '暂无绑定资源' }}
              dataSource={selectNode?.bindresource || []}
              renderItem={(item: any, index: number) => (
                <List.Item
                  style={{ cursor: 'pointer' }}
                  key={index}
                  onClick={() => {
                    if(item.type == 'biz_sobey_point' || item.recousetype == 'point'){
                      onEntityPreview(item.parentcontentId, {
                        name: item.name,
                        type: 'video',
                        knowledge:{
                          inpoint:item.inpoint,
                          outpoint:item.outpoint
                        }
                      });
                    }else{
                      let type = item.type.split('_');
                      onEntityPreview(item.contentId, {
                        name: item.name,
                        type: type[type.length - 1],
                      });
                    }
                    setSeecontentid(item.contentId);
                    setCurrentvideotype(1);
                  }}
                >
                  <div className='content_item'>
                    {getTag(item)}
                    <span
                      style={{
                        color:
                          seecontentid == item.contentId
                            ? 'var(--primary-color)'
                            : '',
                      }}
                      className='content_name'
                    >
                      {item.name}
                    </span>
                  </div>
                  <div style={{display:'flex',alignItems:'center',height:'100%'}}>
                    {
                      perviewtype == 2 ? <span style={{ color: '#999', fontSize: '12px' }}>{finishresource.includes(item.contentId) ? '已完成' :'去完成' }</span> : null
                    }
                    {isedit ? (
                      <IconFont
                        title="取消关联"
                        onClick={e => removevideobyid(e, item, index)}
                        style={{
                          color: 'var(--primary-color)',
                          fontSize: '14px',
                          marginLeft: '20px',
                        }}
                        type="iconhuishouzhan-huise"
                      />
                    ) : null}
                  </div>
                </List.Item>
              )}
            />

            <div className="video_view">
              <div className="redio_view"></div>
              <span className="title_span">外部链接</span>
            </div>

            {isedit ? (
              <Select
                mode="tags"
                style={{ width: '100%' }}
                placeholder="请输入链接地址，如果有多个请用回车隔开"
                onChange={e => {
                  setSelectNode({
                    ...selectNode,
                    bindlink: e,
                    temp: new Date().getTime(),
                  });
                  updatanode(x6node.id, {
                    ...selectNode,
                    bindlink: e,
                  });
                }}
                value={selectNode?.bindlink || []}
                options={[]}
              />
            ) : (
              <List
                bordered
                locale={{ emptyText: '暂无链接地址' }}
                dataSource={selectNode?.bindlink || []}
                renderItem={(item: any, index: number) => (
                  <List.Item style={{ cursor: 'pointer' }} key={index}>
                    <a href={item} target="_blank">
                      {item}
                    </a>
                  </List.Item>
                )}
              />
            )}
            <div className="video_view">
              <div className="redio_view"></div>
              <span className="title_span">参考资料</span>
              <div style={{ flex: 1, textAlign: 'right' }}>
                {isedit ? (
                  <Button
                    loading={modalLoading}
                    size="small"
                    type="primary"
                    onClick={e => showModal('referenceMaterials')}
                  >
                    添加
                  </Button>
                ) : null}
              </div>
            </div>

            {/* 预览资源 */}
            <Spin spinning={entityLoading}>
              {(previewEntity && visible == 1 && selectNode.referenceMaterials?.length && currentvideotype == 2) ? (
                <div className="entity-preview" style={{width:'100%'}}>
                  <div className="video-wrap">
                    <Entity
                      type={previewEntity.type}
                      src={previewEntity.src}
                      id={'previewVideo'}
                      isAutoplay={false}
                      pip={false}
                      knowledge={previewEntity.knowledge}
                      onListener={(e:string)=>{
                        // 只有在学生端才会有学习记录
                        if(e == 'ended' && perviewtype == 2 && !finishresource.includes(seecontentid) && location.query.isJoin == 'true'){
                          addResourcelearning();
                        }
                      }}
                    />
                  </div>
                </div>
              ) : null}
            </Spin>

            <List
              bordered
              locale={{ emptyText: '暂无绑定参考资料' }}
              dataSource={selectNode?.referenceMaterials || []}
              renderItem={(item: any, index: number) => (
                <List.Item
                  style={{ cursor: 'pointer' }}
                  key={index}
                  onClick={() => {
                    if(item.type == 'biz_sobey_point' || item.recousetype == 'point'){
                      onEntityPreview(item.parentcontentId, {
                        name: item.name,
                        type: 'video',
                        knowledge:{
                          inpoint:item.inpoint,
                          outpoint:item.outpoint
                        }
                      });
                    }else{
                      let type = item.type.split('_');
                      onEntityPreview(item.contentId, {
                        name: item.name,
                        type: type[type.length - 1],
                      });
                    }
                    setSeecontentid(item.contentId);
                    setCurrentvideotype(2);
                  }}
                >
                  <div className='content_item'>
                    {getTag(item)}
                    <span
                      style={{
                        color:
                          seecontentid == item.contentId
                            ? 'var(--primary-color)'
                            : '',
                      }}
                      className='content_name'
                    >
                      {item.name}
                    </span>
                  </div>
                  <div style={{display:'flex',alignItems:'center',height:'100%'}}>
                    {
                      perviewtype == 2 ? <span style={{ color: '#999', fontSize: '12px' }}>{finishresource.includes(item.contentId) ? '已完成' :'去完成' }</span> : null
                    }
                    {isedit ? (
                      <IconFont
                        title="取消关联"
                        onClick={e => removeReference(e, item, index)}
                        style={{
                          color: 'var(--primary-color)',
                          fontSize: '14px',
                          marginLeft: '20px',
                        }}
                        type="iconhuishouzhan-huise"
                      />
                    ) : null}
                  </div>
                </List.Item>
              )}
            />
          </div>
          {modalVisible ? (
            <ResourceModal
              currentname={selectNode ? selectNode.label : ''}
              treeData={modalTreeData}
              visible={modalVisible}
              onConfirm={(e: any) => {
                if (addtype == 'bindresource') {
                  addrecouse(e);
                } else if (addtype == 'referenceMaterials') {
                  addreferenceMaterials(e);
                }
              }}
              PointConfirm={(e:any)=>{
                addpoint(e);
              }}
              showPoint={true}
              onCancel={() => setModalVisible(false)}
              onShowDetail={(id, detail) => {
                setEntityPreview({
                  id: id,
                  name: detail.name,
                  type: detail.type,
                });
                setEntityModalVisible(true);
              }}
              // fileType={['biz_sobey_video','biz_sobey_audio','biz_sobey_picture','biz_sobey_document']}
              multi={true}
            />
          ) : null}

          {/* 资源预览modal */}
          <ResourcePreviewModal
            modalVisible={entityModalVisible}
            modalClose={() => setEntityModalVisible(false)}
            resource={entityPreview}
          />

          <Modal
            width={'70%'}
            title="文档预览1"
            visible={perviewvisit}
            className="document_modal_wrap"
            onCancel={() => setPerviewvisit(false)}
            footer={null}
          >
            <div style={{height:'65vh'}}>
              {
               (perviewsrc!="" && perviewsrc) ? <Entity src={perviewsrc} type="document"
               onListener={(e:string)=>{
                // 只有在学生端才会有学习记录
                if(e == 'ended' && perviewtype == 2 && location.query.isJoin == 'true'){
                  addResourcelearning();
                }
              }}></Entity>:null
              }
              {/* <Entity src={'http://172.16.151.202/bucket-z/u-cwcc268v239qk92m/document/2022/12/19/f9981dc0b2324739a94d43d1a8130f02/0/知了高校智慧教育平台解决方案 V2·1 1208.pptx'} type="document"></Entity> */}
            </div>

          </Modal>

          {/* 选择题目 excludeQuestionTypes-排除不需要的题目类型，0:单选,1:多选,2:填空,3:主观问答 4：判断，多个类型使用英文逗号分割 */}
          {
            addtopicvisible?
            <TopicSelectModal
              currentname={selectNode ? selectNode.label : ''}
              selectKeys={homework}
              visible={addtopicvisible}
              type={'checkbox'}
              disabled={[3]}
              onConfirm={e => {
                let arr = selectNode.homework || [];
                if(arr && arr.length){
                  // 去重
                  let newarr = e.filter((item:any)=>{
                    let flage = true;
                    arr.forEach((item2:any)=>{
                      if(item.id == item2.id){
                        flage = false;
                      }
                    })
                    return flage;
                  })
                  arr = [...arr,...newarr];
                  if(newarr.length == 0){
                    message.warning('试题重复添加！');
                    return
                  }
                }else{
                  arr = e;
                }
                setSelectNode({
                  ...selectNode,
                  homework: arr,
                  temp: new Date().getTime(),
                });
                updatanode(x6node.id, {
                  ...selectNode,
                  homework: arr,
                });
                setAddtopicvisible(false);
                addquestion(arr);
              }}
              iscoursemap={true}
              onclose={() => setAddtopicvisible(false)}
              onAdd={() => {
                window.open(`${window.location.origin}/exam/#/exam/topicManage`);
              }}
            />:null
          }
        </div>
      </TabPane>
      <TabPane tab="对比辨析" key="2">
        <TabComparativeanalysis
          graph={graph}
          selectnode={x6node}
          onback={onback}
          mapid={mapid}
          perviewtype={perviewtype}
          updatanodecompare={updatanodecompare}
        />
      </TabPane>
      <TabPane tab="试题" key="3">
        <div className='rdrawer'>
          <div className='drawer_view'>
          <div className="video_view" style={{marginTop:'-10px'}}>
            {/* <div className="redio_view"></div> */}
            {/* <span className="title_span">试题</span> */}
            <div style={{ flex: 1, textAlign: 'right' }}>
              {selectNode?.homework?.length ? (
                <Button
                  size="small"
                  type="primary"
                  onClick={e => {
                    if(!isseeanswer){
                      // 学生端 并且 不是管理员角色
                      if(perviewtype == 2 && location.query.isJoin == 'true'){
                        submitanswer()
                      }else{
                        setIsseeanswer(!isseeanswer);
                      }
                    }else{
                      setIsseeanswer(!isseeanswer);
                    }
                  }}
                  style={{ marginRight: '10px' }}
                >
                  {isseeanswer ? '重新答题' : '查看答案'}
                </Button>
              ) : null}
              {isedit ? (
                <Button
                  size="small"
                  type="primary"
                  onClick={e => setAddtopicvisible(true)}
                >
                  添加
                </Button>
              ) : null}
            </div>
          </div>
          {
            selectNode?.homework?.length > 0 && <div className="divider_dashed" style={{ marginTop: '0px' }}></div>
          }

          {selectNode?.homework?.map((item: any, index: number) => {
            return (
              <div className="topic_box" key={index}>
                {
                  isedit?
                  <IconFont
                    title="删除题目"
                    className="remove_topic"
                    onClick={e => removeQuestion(e, item, index)}
                    style={{
                      color: 'var(--primary-color)',
                      fontSize: '16px',
                    }}
                    type="iconhuishouzhan-huise"
                  />:null
                }
                <div style={{width:'95%'}}>
                  <HomeworkSubItem
                    score={0}
                    showscore={false}
                    key={index}
                    isEnd={isseeanswer}
                    files={null}
                    canEdit={true}
                    openParse={true}
                    index={index + 1}
                    data={item}
                    answer={useranswer[index]}
                    onChange={e => {
                      let arr = useranswer;
                      arr[index] = e;
                      setUseranswer(() => {
                        return [...arr];
                      });
                    }}
                  />
                </div>
              </div>
            );
          })}
          {
            selectNode?.homework?.length == 0 && <Empty style={{marginTop:50}} description="暂无试题"></Empty>
          }
        </div>
        </div>
      </TabPane>
      {(parameterConfig.forum_display == 'true' && perviewtype != 0 && perviewtype != 3 && location.pathname!='/perviewemap') ? (
        <TabPane tab="问答" key="4">
          <div className="rdrawer">
            {questionvisible == 1 ? (
              <div className="drawer_view">
                <div className="options_view">
                  <Button
                    type="primary"
                    icon={<IconFont type="iconbiji" />}
                    onClick={() => setQuestionvisible(2)}
                  >
                    发起提问
                  </Button>
                </div>
                <div className="detail_view">
                  <CourseQA
                    ismap={true}
                    extend_link_id={selectNode?.id}
                    extend_type={((location.query.type == '1' || location.query.type == 'mooc') ? '03' :'01')}
                    showdetail={(e: any) => {
                      setTodetail(e);
                      setQuestionvisible(3);
                    }}
                  ></CourseQA>
                </div>
              </div>
            ) : (
              ''
            )}
            {questionvisible == 2 ? (
              <div className="drawer_view">
                <Askquestions
                  mapid={mapid}
                  courseid={courseid}
                  perviewtype={perviewtype}
                  selectNode={selectNode}
                  coursename={coursename}
                  onback={() => {
                    setQuestionvisible(1);
                  }}
                ></Askquestions>
              </div>
            ) : (
              ''
            )}
            {questionvisible == 3 ? (
              <div className="drawer_view">
                <CourseQAdetail
                  ismap={true}
                  topicid={todetail.topicid}
                  onback={() => {
                    setQuestionvisible(1);
                  }}
                ></CourseQAdetail>
              </div>
            ) : (
              ''
            )}
          </div>
        </TabPane>
      ) : (
        ''
      )}
    </Tabs>
  );
};

export default Rdrawer;
