!function(e){function t(r){if(n[r])return n[r].exports;var o=n[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,t),o.l=!0,o.exports}var n={};t.m=e,t.c=n,t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:r})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="/dist/",t(t.s=119)}([function(e,t){var n=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},function(e,t,n){var r=n(29)("wks"),o=n(18),i=n(0).Symbol,s="function"==typeof i;(e.exports=function(e){return r[e]||(r[e]=s&&i[e]||(s?i:o)("Symbol."+e))}).store=r},function(e,t){var n=e.exports={version:"2.6.12"};"number"==typeof __e&&(__e=n)},function(e,t,n){var r=n(6);e.exports=function(e){if(!r(e))throw TypeError(e+" is not an object!");return e}},function(e,t,n){var r=n(5),o=n(17);e.exports=n(7)?function(e,t,n){return r.f(e,t,o(1,n))}:function(e,t,n){return e[t]=n,e}},function(e,t,n){var r=n(3),o=n(39),i=n(26),s=Object.defineProperty;t.f=n(7)?Object.defineProperty:function(e,t,n){if(r(e),t=i(t,!0),r(n),o)try{return s(e,t,n)}catch(e){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(e[t]=n.value),e}},function(e,t){e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},function(e,t,n){e.exports=!n(16)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},function(e,t){var n={}.hasOwnProperty;e.exports=function(e,t){return n.call(e,t)}},function(e,t,n){var r=n(59),o=n(24);e.exports=function(e){return r(o(e))}},function(e,t){e.exports=!0},function(e,t,n){var r=n(0),o=n(2),i=n(14),s=n(4),a=n(8),u=function(e,t,n){var f,c,l,p=e&u.F,d=e&u.G,h=e&u.S,g=e&u.P,y=e&u.B,v=e&u.W,m=d?o:o[t]||(o[t]={}),k=m.prototype,w=d?r:h?r[t]:(r[t]||{}).prototype;d&&(n=t);for(f in n)(c=!p&&w&&void 0!==w[f])&&a(m,f)||(l=c?w[f]:n[f],m[f]=d&&"function"!=typeof w[f]?n[f]:y&&c?i(l,r):v&&w[f]==l?function(e){var t=function(t,n,r){if(this instanceof e){switch(arguments.length){case 0:return new e;case 1:return new e(t);case 2:return new e(t,n)}return new e(t,n,r)}return e.apply(this,arguments)};return t.prototype=e.prototype,t}(l):g&&"function"==typeof l?i(Function.call,l):l,g&&((m.virtual||(m.virtual={}))[f]=l,e&u.R&&k&&!k[f]&&s(k,f,l)))};u.F=1,u.G=2,u.S=4,u.P=8,u.B=16,u.W=32,u.U=64,u.R=128,e.exports=u},function(e,t){e.exports={}},function(e,t){var n={}.toString;e.exports=function(e){return n.call(e).slice(8,-1)}},function(e,t,n){var r=n(15);e.exports=function(e,t,n){if(r(e),void 0===t)return e;switch(n){case 1:return function(n){return e.call(t,n)};case 2:return function(n,r){return e.call(t,n,r)};case 3:return function(n,r,o){return e.call(t,n,r,o)}}return function(){return e.apply(t,arguments)}}},function(e,t){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},function(e,t){e.exports=function(e){try{return!!e()}catch(e){return!0}}},function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},function(e,t){var n=0,r=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++n+r).toString(36))}},function(e,t,n){var r=n(5).f,o=n(8),i=n(1)("toStringTag");e.exports=function(e,t,n){e&&!o(e=n?e:e.prototype,i)&&r(e,i,{configurable:!0,value:t})}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(54),o=function(e){return e&&e.__esModule?e:{default:e}}(r),i={asyncLoadedScripts:{},asyncLoadedScriptsCallbackQueue:{},getScriptDomFromUrl:function(e){var t;return/.+\.js$/.test(e)?(t=document.createElement("SCRIPT"),t.setAttribute("type","text/javascript"),t.setAttribute("src",e)):/.+\.css$/.test(e)&&(t=document.createElement("link"),t.href=e,t.type="text/css",t.rel="stylesheet"),t},asyncLoadScript:function(e,t){var n=i;if(void 0!=n.asyncLoadedScripts[e])return void(t&&"function"==typeof t&&(0==n.asyncLoadedScripts[e]?(n.asyncLoadedScriptsCallbackQueue[e]||(n.asyncLoadedScriptsCallbackQueue[e]=[]),n.asyncLoadedScriptsCallbackQueue[e].push(t)):t.apply(n,[])));n.asyncLoadedScripts[e]=0;var r=n.getScriptDomFromUrl(e);r.readyState?r.onreadystatechange=function(){if(("loaded"==r.readyState||"complete"==r.readyState)&&(r.onreadystatechange=null,n.asyncLoadedScripts[e]=1,t&&"function"==typeof t&&t.apply(n,[]),n.asyncLoadedScriptsCallbackQueue[e])){for(var o=0,i=n.asyncLoadedScriptsCallbackQueue[e].length;o<i;o++)n.asyncLoadedScriptsCallbackQueue[e][o].apply(n,[]);n.asyncLoadedScriptsCallbackQueue[e]=void 0}}:r.onload=function(){if(n.asyncLoadedScripts[e]=1,t&&"function"==typeof t&&t.apply(n,[]),n.asyncLoadedScriptsCallbackQueue[e]){for(var r=0,o=n.asyncLoadedScriptsCallbackQueue[e].length;r<o;r++)n.asyncLoadedScriptsCallbackQueue[e][r].apply(n,[]);n.asyncLoadedScriptsCallbackQueue[e]=void 0}},document.getElementsByTagName("head")[0].appendChild(r)},getFileNameFromUrl:function(e){return e.substring(e.lastIndexOf("/")+1,e.length)},isIncludeScript:function(e){for(var t=/js$/i.test(e),n=document.getElementsByTagName(t?"script":"link"),r=0;r<n.length;r++)if(-1!=n[r][t?"src":"href"].indexOf(e))return!0;return!1},loadScripts:function(e){if(e instanceof Array){for(var t=[],n=0;n<e.length;n++)t.push(new o.default(function(t,r){i.isIncludeScript(i.getFileNameFromUrl(e[n]))?t():i.asyncLoadScript(e[n],function(){t()})}));return o.default.all(t)}return new o.default(function(e,t){e()})},getExtensions:function(e){if(!e)return"";for(var t=e.length;0!=t;t--)if("."==e[t])return e.substring(t,e.length);return""},getExtension:function(e){var t=i.getExtensions(e);return t.length>0?t.substring(1,t.length).toLowerCase():""},getFileName:function(e){for(var t=e.length,n=e.length;0!=n;n--)if("."!=e[n]||t!=e.length){if("\\"==e[n])return e.substring(n+1,t)}else t=n;return e.substring(0,t)},getFullFileName:function(e){for(var t=e.length;0!=t;t--)if("\\"==e[t])return e.substring(t+1,e.length);return e},getTypeByExt:function(e,t){e=e.toLowerCase(),0!==e.indexOf(".")&&(e="."+e);var n=_.get(window,"nxt.config.entityTypes",[]);t&&0===n.length&&(n=_.get(t,"entityTypes",[]));for(var r=0;r<n.length;r++)if(n[r].extensions&&-1!=n[r].extensions.indexOf(e))return n[r];return _.find(n,{code:"other"})},formatSize:function(e,t,n){var r;for(n=n||["B","KB","MB","GB","TB"];(r=n.shift())&&e>1024;)e/=1024;return("B"===r?e:e.toFixed(t||2))+" "+r},prompt:function(e){mam&&mam.prompt&&mam.prompt(e)},msgOk:function(e){mam&&mam.message&&mam.message.ok&&mam.message.ok(e)},addUrlParam:function(e,t){return e+=(-1!==e.indexOf("?")?"&":"?")+t},getCookie:function(e){for(var t=e+"=",n=document.cookie.split(";"),r=0;r<n.length;r++){var o=n[r].trim();if(0==o.indexOf(t))return o.substring(t.length,o.length)}return""}};t.default=i},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r={};t.default=r},function(e,t,n){e.exports={default:n(80),__esModule:!0}},function(e,t){var n=Math.ceil,r=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?r:n)(e)}},function(e,t){e.exports=function(e){if(void 0==e)throw TypeError("Can't call method on  "+e);return e}},function(e,t,n){var r=n(6),o=n(0).document,i=r(o)&&r(o.createElement);e.exports=function(e){return i?o.createElement(e):{}}},function(e,t,n){var r=n(6);e.exports=function(e,t){if(!r(e))return e;var n,o;if(t&&"function"==typeof(n=e.toString)&&!r(o=n.call(e)))return o;if("function"==typeof(n=e.valueOf)&&!r(o=n.call(e)))return o;if(!t&&"function"==typeof(n=e.toString)&&!r(o=n.call(e)))return o;throw TypeError("Can't convert object to primitive value")}},function(e,t,n){var r=n(42),o=n(30);e.exports=Object.keys||function(e){return r(e,o)}},function(e,t,n){var r=n(29)("keys"),o=n(18);e.exports=function(e){return r[e]||(r[e]=o(e))}},function(e,t,n){var r=n(2),o=n(0),i=o["__core-js_shared__"]||(o["__core-js_shared__"]={});(e.exports=function(e,t){return i[e]||(i[e]=void 0!==t?t:{})})("versions",[]).push({version:r.version,mode:n(10)?"pure":"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})},function(e,t){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(e,t,n){"use strict";function r(e){var t,n;this.promise=new e(function(e,r){if(void 0!==t||void 0!==n)throw TypeError("Bad Promise constructor");t=e,n=r}),this.resolve=o(t),this.reject=o(n)}var o=n(15);e.exports.f=function(e){return new r(e)}},function(e,t,n){t.f=n(1)},function(e,t,n){var r=n(0),o=n(2),i=n(10),s=n(32),a=n(5).f;e.exports=function(e){var t=o.Symbol||(o.Symbol=i?{}:r.Symbol||{});"_"==e.charAt(0)||e in t||a(t,e,{value:s.f(e)})}},function(e,t){t.f={}.propertyIsEnumerable},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0});var o=n(22),i=r(o),s=n(20),a=r(s),u=function(e,t){t=t.toLowerCase().replace(/[\[\]]/g,"\\$&");var n=new RegExp("[?&]"+t+"(=([^&#]*)|&|#|$)"),r=n.exec(e.toLowerCase());return r?r[2]?decodeURIComponent(r[2].replace(/\+/g," ")):"":null},f=function(e){return u(window.location.href,e)},c=function(e,t){return e+=(-1!==e.indexOf("?")?"&":"?")+t},p={getGetParamStr:function(e,t){var n="";if(void 0!=e&&!$.isEmptyObject(e)){for(var r in e)n+=t?"/"+e[r]:r+"="+e[r]+"&";t||(n="?"+n.substring(0,n.length-1))}return n},get:function(e,t){var n=$.Deferred(),r=e;t||(t={}),r=e+p.getGetParamStr(t);var o=f("token");return o&&(r=c(r,"token="+o)),$.ajax({type:"get",xhrFields:{withCredentials:!(e.indexOf("token=")>-1)},url:r}).then(function(e){e.success?n.resolve(e):(console.error("response",e),a.default.prompt(l("system."+e.data.code,e.data.title)),n.reject(e))},function(e){n.reject(e)}),n},post:function(e,t,n){var r=$.Deferred();t||(t={});var o=f("token");o&&(e=c(e,"token="+o));var s={type:"post",data:t,contentType:"application/json",processData:!1,xhrFields:{withCredentials:!(e.indexOf("token=")>-1)},url:e};return n&&void 0!==n.contentType&&(s.contentType=n.contentType),n&&void 0!==n.processData&&(s.processData=n.processData),"application/json"===s.contentType&&(s.data=(0,i.default)(t)),$.ajax(s).then(function(e){e.success?r.resolve(e):(console.error("response",e),a.default.prompt(l("system."+e.error.code,e.error.title)),r.reject(e))},function(e){r.reject(e)}),r},delete:function(e,t,n){var r=$.Deferred();t||(t={});var o=f("token");o&&(e=c(e,"token="+o));var s={type:"delete",data:t,contentType:"application/json",processData:!1,xhrFields:{withCredentials:!(e.indexOf("token=")>-1)},url:e};return n&&void 0!==n.contentType&&(s.contentType=n.contentType),n&&void 0!==n.processData&&(s.processData=n.processData),"application/json"===s.contentType&&(s.data=(0,i.default)(t)),$.ajax(s).then(function(e){e.success?r.resolve(e):(console.error("response",e),a.default.prompt(l("system."+e.error.code,e.error.title)),r.reject(e))},function(e){r.reject(e)}),r}};t.default=p},function(e,t){},function(e,t,n){"use strict";var r=n(56)(!0);n(38)(String,"String",function(e){this._t=String(e),this._i=0},function(){var e,t=this._t,n=this._i;return n>=t.length?{value:void 0,done:!0}:(e=r(t,n),this._i+=e.length,{value:e,done:!1})})},function(e,t,n){"use strict";var r=n(10),o=n(11),i=n(40),s=n(4),a=n(12),u=n(57),f=n(19),c=n(62),l=n(1)("iterator"),p=!([].keys&&"next"in[].keys()),d=function(){return this};e.exports=function(e,t,n,h,g,y,v){u(n,t,h);var m,k,w,T=function(e){if(!p&&e in S)return S[e];switch(e){case"keys":case"values":return function(){return new n(this,e)}}return function(){return new n(this,e)}},b=t+" Iterator",_="values"==g,I=!1,S=e.prototype,x=S[l]||S["@@iterator"]||g&&S[g],P=x||T(g),E=g?_?T("entries"):P:void 0,A="Array"==t?S.entries||x:x;if(A&&(w=c(A.call(new e)))!==Object.prototype&&w.next&&(f(w,b,!0),r||"function"==typeof w[l]||s(w,l,d)),_&&x&&"values"!==x.name&&(I=!0,P=function(){return x.call(this)}),r&&!v||!p&&!I&&S[l]||s(S,l,P),a[t]=P,a[b]=d,g)if(m={values:_?P:T("values"),keys:y?P:T("keys"),entries:E},v)for(k in m)k in S||i(S,k,m[k]);else o(o.P+o.F*(p||I),t,m);return m}},function(e,t,n){e.exports=!n(7)&&!n(16)(function(){return 7!=Object.defineProperty(n(25)("div"),"a",{get:function(){return 7}}).a})},function(e,t,n){e.exports=n(4)},function(e,t,n){var r=n(3),o=n(58),i=n(30),s=n(28)("IE_PROTO"),a=function(){},u=function(){var e,t=n(25)("iframe"),r=i.length;for(t.style.display="none",n(44).appendChild(t),t.src="javascript:",e=t.contentWindow.document,e.open(),e.write("<script>document.F=Object<\/script>"),e.close(),u=e.F;r--;)delete u.prototype[i[r]];return u()};e.exports=Object.create||function(e,t){var n;return null!==e?(a.prototype=r(e),n=new a,a.prototype=null,n[s]=e):n=u(),void 0===t?n:o(n,t)}},function(e,t,n){var r=n(8),o=n(9),i=n(60)(!1),s=n(28)("IE_PROTO");e.exports=function(e,t){var n,a=o(e),u=0,f=[];for(n in a)n!=s&&r(a,n)&&f.push(n);for(;t.length>u;)r(a,n=t[u++])&&(~i(f,n)||f.push(n));return f}},function(e,t,n){var r=n(23),o=Math.min;e.exports=function(e){return e>0?o(r(e),9007199254740991):0}},function(e,t,n){var r=n(0).document;e.exports=r&&r.documentElement},function(e,t,n){var r=n(24);e.exports=function(e){return Object(r(e))}},function(e,t,n){n(63);for(var r=n(0),o=n(4),i=n(12),s=n(1)("toStringTag"),a="CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,TextTrackList,TouchList".split(","),u=0;u<a.length;u++){var f=a[u],c=r[f],l=c&&c.prototype;l&&!l[s]&&o(l,s,f),i[f]=i.Array}},function(e,t,n){var r=n(13),o=n(1)("toStringTag"),i="Arguments"==r(function(){return arguments}()),s=function(e,t){try{return e[t]}catch(e){}};e.exports=function(e){var t,n,a;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=s(t=Object(e),o))?n:i?r(t):"Object"==(a=r(t))&&"function"==typeof t.callee?"Arguments":a}},function(e,t,n){var r=n(3),o=n(15),i=n(1)("species");e.exports=function(e,t){var n,s=r(e).constructor;return void 0===s||void 0==(n=r(s)[i])?t:o(n)}},function(e,t,n){var r,o,i,s=n(14),a=n(72),u=n(44),f=n(25),c=n(0),l=c.process,p=c.setImmediate,d=c.clearImmediate,h=c.MessageChannel,g=c.Dispatch,y=0,v={},m=function(){var e=+this;if(v.hasOwnProperty(e)){var t=v[e];delete v[e],t()}},k=function(e){m.call(e.data)};p&&d||(p=function(e){for(var t=[],n=1;arguments.length>n;)t.push(arguments[n++]);return v[++y]=function(){a("function"==typeof e?e:Function(e),t)},r(y),y},d=function(e){delete v[e]},"process"==n(13)(l)?r=function(e){l.nextTick(s(m,e,1))}:g&&g.now?r=function(e){g.now(s(m,e,1))}:h?(o=new h,i=o.port2,o.port1.onmessage=k,r=s(i.postMessage,i,1)):c.addEventListener&&"function"==typeof postMessage&&!c.importScripts?(r=function(e){c.postMessage(e+"","*")},c.addEventListener("message",k,!1)):r="onreadystatechange"in f("script")?function(e){u.appendChild(f("script")).onreadystatechange=function(){u.removeChild(this),m.call(e)}}:function(e){setTimeout(s(m,e,1),0)}),e.exports={set:p,clear:d}},function(e,t){e.exports=function(e){try{return{e:!1,v:e()}}catch(e){return{e:!0,v:e}}}},function(e,t,n){var r=n(3),o=n(6),i=n(31);e.exports=function(e,t){if(r(e),o(t)&&t.constructor===e)return t;var n=i.f(e);return(0,n.resolve)(t),n.promise}},function(e,t){t.f=Object.getOwnPropertySymbols},function(e,t,n){var r=n(42),o=n(30).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return r(e,o)}},function(e,t,n){e.exports={default:n(55),__esModule:!0}},function(e,t,n){n(36),n(37),n(46),n(66),n(78),n(79),e.exports=n(2).Promise},function(e,t,n){var r=n(23),o=n(24);e.exports=function(e){return function(t,n){var i,s,a=String(o(t)),u=r(n),f=a.length;return u<0||u>=f?e?"":void 0:(i=a.charCodeAt(u),i<55296||i>56319||u+1===f||(s=a.charCodeAt(u+1))<56320||s>57343?e?a.charAt(u):i:e?a.slice(u,u+2):s-56320+(i-55296<<10)+65536)}}},function(e,t,n){"use strict";var r=n(41),o=n(17),i=n(19),s={};n(4)(s,n(1)("iterator"),function(){return this}),e.exports=function(e,t,n){e.prototype=r(s,{next:o(1,n)}),i(e,t+" Iterator")}},function(e,t,n){var r=n(5),o=n(3),i=n(27);e.exports=n(7)?Object.defineProperties:function(e,t){o(e);for(var n,s=i(t),a=s.length,u=0;a>u;)r.f(e,n=s[u++],t[n]);return e}},function(e,t,n){var r=n(13);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==r(e)?e.split(""):Object(e)}},function(e,t,n){var r=n(9),o=n(43),i=n(61);e.exports=function(e){return function(t,n,s){var a,u=r(t),f=o(u.length),c=i(s,f);if(e&&n!=n){for(;f>c;)if((a=u[c++])!=a)return!0}else for(;f>c;c++)if((e||c in u)&&u[c]===n)return e||c||0;return!e&&-1}}},function(e,t,n){var r=n(23),o=Math.max,i=Math.min;e.exports=function(e,t){return e=r(e),e<0?o(e+t,0):i(e,t)}},function(e,t,n){var r=n(8),o=n(45),i=n(28)("IE_PROTO"),s=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=o(e),r(e,i)?e[i]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?s:null}},function(e,t,n){"use strict";var r=n(64),o=n(65),i=n(12),s=n(9);e.exports=n(38)(Array,"Array",function(e,t){this._t=s(e),this._i=0,this._k=t},function(){var e=this._t,t=this._k,n=this._i++;return!e||n>=e.length?(this._t=void 0,o(1)):"keys"==t?o(0,n):"values"==t?o(0,e[n]):o(0,[n,e[n]])},"values"),i.Arguments=i.Array,r("keys"),r("values"),r("entries")},function(e,t){e.exports=function(){}},function(e,t){e.exports=function(e,t){return{value:t,done:!!e}}},function(e,t,n){"use strict";var r,o,i,s,a=n(10),u=n(0),f=n(14),c=n(47),l=n(11),p=n(6),d=n(15),h=n(67),g=n(68),y=n(48),v=n(49).set,m=n(73)(),k=n(31),w=n(50),T=n(74),b=n(51),_=u.TypeError,I=u.process,S=I&&I.versions,x=S&&S.v8||"",P=u.Promise,E="process"==c(I),A=function(){},C=o=k.f,O=!!function(){try{var e=P.resolve(1),t=(e.constructor={})[n(1)("species")]=function(e){e(A,A)};return(E||"function"==typeof PromiseRejectionEvent)&&e.then(A)instanceof t&&0!==x.indexOf("6.6")&&-1===T.indexOf("Chrome/66")}catch(e){}}(),U=function(e){var t;return!(!p(e)||"function"!=typeof(t=e.then))&&t},R=function(e,t){if(!e._n){e._n=!0;var n=e._c;m(function(){for(var r=e._v,o=1==e._s,i=0;n.length>i;)!function(t){var n,i,s,a=o?t.ok:t.fail,u=t.resolve,f=t.reject,c=t.domain;try{a?(o||(2==e._h&&F(e),e._h=1),!0===a?n=r:(c&&c.enter(),n=a(r),c&&(c.exit(),s=!0)),n===t.promise?f(_("Promise-chain cycle")):(i=U(n))?i.call(n,u,f):u(n)):f(r)}catch(e){c&&!s&&c.exit(),f(e)}}(n[i++]);e._c=[],e._n=!1,t&&!e._h&&B(e)})}},B=function(e){v.call(u,function(){var t,n,r,o=e._v,i=N(e);if(i&&(t=w(function(){E?I.emit("unhandledRejection",o,e):(n=u.onunhandledrejection)?n({promise:e,reason:o}):(r=u.console)&&r.error&&r.error("Unhandled promise rejection",o)}),e._h=E||N(e)?2:1),e._a=void 0,i&&t.e)throw t.v})},N=function(e){return 1!==e._h&&0===(e._a||e._c).length},F=function(e){v.call(u,function(){var t;E?I.emit("rejectionHandled",e):(t=u.onrejectionhandled)&&t({promise:e,reason:e._v})})},M=function(e){var t=this;t._d||(t._d=!0,t=t._w||t,t._v=e,t._s=2,t._a||(t._a=t._c.slice()),R(t,!0))},j=function(e){var t,n=this;if(!n._d){n._d=!0,n=n._w||n;try{if(n===e)throw _("Promise can't be resolved itself");(t=U(e))?m(function(){var r={_w:n,_d:!1};try{t.call(e,f(j,r,1),f(M,r,1))}catch(e){M.call(r,e)}}):(n._v=e,n._s=1,R(n,!1))}catch(e){M.call({_w:n,_d:!1},e)}}};O||(P=function(e){h(this,P,"Promise","_h"),d(e),r.call(this);try{e(f(j,this,1),f(M,this,1))}catch(e){M.call(this,e)}},r=function(e){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1},r.prototype=n(75)(P.prototype,{then:function(e,t){var n=C(y(this,P));return n.ok="function"!=typeof e||e,n.fail="function"==typeof t&&t,n.domain=E?I.domain:void 0,this._c.push(n),this._a&&this._a.push(n),this._s&&R(this,!1),n.promise},catch:function(e){return this.then(void 0,e)}}),i=function(){var e=new r;this.promise=e,this.resolve=f(j,e,1),this.reject=f(M,e,1)},k.f=C=function(e){return e===P||e===s?new i(e):o(e)}),l(l.G+l.W+l.F*!O,{Promise:P}),n(19)(P,"Promise"),n(76)("Promise"),s=n(2).Promise,l(l.S+l.F*!O,"Promise",{reject:function(e){var t=C(this);return(0,t.reject)(e),t.promise}}),l(l.S+l.F*(a||!O),"Promise",{resolve:function(e){return b(a&&this===s?P:this,e)}}),l(l.S+l.F*!(O&&n(77)(function(e){P.all(e).catch(A)})),"Promise",{all:function(e){var t=this,n=C(t),r=n.resolve,o=n.reject,i=w(function(){var n=[],i=0,s=1;g(e,!1,function(e){var a=i++,u=!1;n.push(void 0),s++,t.resolve(e).then(function(e){u||(u=!0,n[a]=e,--s||r(n))},o)}),--s||r(n)});return i.e&&o(i.v),n.promise},race:function(e){var t=this,n=C(t),r=n.reject,o=w(function(){g(e,!1,function(e){t.resolve(e).then(n.resolve,r)})});return o.e&&r(o.v),n.promise}})},function(e,t){e.exports=function(e,t,n,r){if(!(e instanceof t)||void 0!==r&&r in e)throw TypeError(n+": incorrect invocation!");return e}},function(e,t,n){var r=n(14),o=n(69),i=n(70),s=n(3),a=n(43),u=n(71),f={},c={},t=e.exports=function(e,t,n,l,p){var d,h,g,y,v=p?function(){return e}:u(e),m=r(n,l,t?2:1),k=0;if("function"!=typeof v)throw TypeError(e+" is not iterable!");if(i(v)){for(d=a(e.length);d>k;k++)if((y=t?m(s(h=e[k])[0],h[1]):m(e[k]))===f||y===c)return y}else for(g=v.call(e);!(h=g.next()).done;)if((y=o(g,m,h.value,t))===f||y===c)return y};t.BREAK=f,t.RETURN=c},function(e,t,n){var r=n(3);e.exports=function(e,t,n,o){try{return o?t(r(n)[0],n[1]):t(n)}catch(t){var i=e.return;throw void 0!==i&&r(i.call(e)),t}}},function(e,t,n){var r=n(12),o=n(1)("iterator"),i=Array.prototype;e.exports=function(e){return void 0!==e&&(r.Array===e||i[o]===e)}},function(e,t,n){var r=n(47),o=n(1)("iterator"),i=n(12);e.exports=n(2).getIteratorMethod=function(e){if(void 0!=e)return e[o]||e["@@iterator"]||i[r(e)]}},function(e,t){e.exports=function(e,t,n){var r=void 0===n;switch(t.length){case 0:return r?e():e.call(n);case 1:return r?e(t[0]):e.call(n,t[0]);case 2:return r?e(t[0],t[1]):e.call(n,t[0],t[1]);case 3:return r?e(t[0],t[1],t[2]):e.call(n,t[0],t[1],t[2]);case 4:return r?e(t[0],t[1],t[2],t[3]):e.call(n,t[0],t[1],t[2],t[3])}return e.apply(n,t)}},function(e,t,n){var r=n(0),o=n(49).set,i=r.MutationObserver||r.WebKitMutationObserver,s=r.process,a=r.Promise,u="process"==n(13)(s);e.exports=function(){var e,t,n,f=function(){var r,o;for(u&&(r=s.domain)&&r.exit();e;){o=e.fn,e=e.next;try{o()}catch(r){throw e?n():t=void 0,r}}t=void 0,r&&r.enter()};if(u)n=function(){s.nextTick(f)};else if(!i||r.navigator&&r.navigator.standalone)if(a&&a.resolve){var c=a.resolve(void 0);n=function(){c.then(f)}}else n=function(){o.call(r,f)};else{var l=!0,p=document.createTextNode("");new i(f).observe(p,{characterData:!0}),n=function(){p.data=l=!l}}return function(r){var o={fn:r,next:void 0};t&&(t.next=o),e||(e=o,n()),t=o}}},function(e,t,n){var r=n(0),o=r.navigator;e.exports=o&&o.userAgent||""},function(e,t,n){var r=n(4);e.exports=function(e,t,n){for(var o in t)n&&e[o]?e[o]=t[o]:r(e,o,t[o]);return e}},function(e,t,n){"use strict";var r=n(0),o=n(2),i=n(5),s=n(7),a=n(1)("species");e.exports=function(e){var t="function"==typeof o[e]?o[e]:r[e];s&&t&&!t[a]&&i.f(t,a,{configurable:!0,get:function(){return this}})}},function(e,t,n){var r=n(1)("iterator"),o=!1;try{var i=[7][r]();i.return=function(){o=!0},Array.from(i,function(){throw 2})}catch(e){}e.exports=function(e,t){if(!t&&!o)return!1;var n=!1;try{var i=[7],s=i[r]();s.next=function(){return{done:n=!0}},i[r]=function(){return s},e(i)}catch(e){}return n}},function(e,t,n){"use strict";var r=n(11),o=n(2),i=n(0),s=n(48),a=n(51);r(r.P+r.R,"Promise",{finally:function(e){var t=s(this,o.Promise||i.Promise),n="function"==typeof e;return this.then(n?function(n){return a(t,e()).then(function(){return n})}:e,n?function(n){return a(t,e()).then(function(){throw n})}:e)}})},function(e,t,n){"use strict";var r=n(11),o=n(31),i=n(50);r(r.S,"Promise",{try:function(e){var t=o.f(this),n=i(e);return(n.e?t.reject:t.resolve)(n.v),t.promise}})},function(e,t,n){var r=n(2),o=r.JSON||(r.JSON={stringify:JSON.stringify});e.exports=function(e){return o.stringify.apply(o,arguments)}},function(e,t,n){"use strict";(function(e){function r(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0});var o=n(22),i=r(o),s=n(20),a=r(s),u=(n(21).default,n(35).default),f=n(87),c=function(t){function n(){return window.nxt&&window.nxt.config?window.nxt.config:t.configInst?t.configInst:void 0}function r(){if(A.getFilesByStatus("progress","deleteing").length>0)return l("upload.closePageTip","当前页面存在未完成的上传任务，关闭页面会导致上传失败，你确定要关闭吗？")}function o(e){return _.isUndefined(e.lastModifiedDate)?e.lastModified:e.lastModifiedDate.getTime()}function s(e){var t=_.find(n().entityTypes,{code:e.entityType});return null==t&&(t=_.find(n().entityTypes,{isOther:!0})),null==t?"":t.keyframe.replace("~","")}function c(){_.forEach(A.tasks,function(e){"added"!==e.status&&"success"!==e.status&&(e.status="error")}),$(window).off("beforeunload",r),a.default.prompt(l("upload.unauthorized","你未登录或已超时，请重新登录。")),location.href=n().loginUrl+"?login_backUrl="+location.href}function p(e){var e=e||"";null==x&&(P=_.uniqueId("mam-web-transfer-"),x=void 0===t.isMultiple||t.isMultiple?$('<input id="'+P+'" accept="'+e+'" type="file" multiple style="display:none"/>'):$('<input id="'+P+'" accept="'+e+'" type="file" style="display:none"/>'),$("body").append(x))}function d(){null!=x&&(x.unbind("change"),x.remove(),x=null)}function h(e,t){$(A).trigger(e,t)}function g(e,t){if(e){if("TYY_S3"===e.manageType)return v(e,t);if("ALY_OSS"!==e.manageType){var n={Bucket:e.bucketName,Key:t.relativePath.replace(/\\/g,"/")};if(y(e),!(t.fileSize<=5242880)){var r=E.createMultipartUpload(n).promise();t.createUploadPromise=r;var o=$.Deferred();return t.createUploadPromise.then(function(e){t.uploadId=e.UploadId,o.resolve()}).catch(function(e){o.reject(),console.info(e)}),o.promise()}}}}function y(e){var t={accessKeyId:e.accessKeyId,secretAccessKey:e.accessKeySecret,endpoint:e.endpoint,sslEnabled:!e.useHttp,s3ForcePathStyle:!0,signatureVersion:"v"+e.version,apiVersion:"2006-03-01"};if(e.region&&(AWS.config.region=e.region),n().isHandleHttpPath&&t.endpoint){var r=window.location.protocol+"//"+window.location.host;t.endpoint=r}E="TYY_S3"!==e.manageType?new AWS.S3(t):new OOS.S3(t)}function v(e,t){if(e&&"TYY_S3"===e.manageType&&(y(e),!(t.fileSize<=5242880))){var n={Bucket:e.bucketName,Key:t.relativePath.replace(/\\/g,"/")},r=E.createMultipartUpload(n).promise();t.createUploadPromise=r;var o=$.Deferred();return t.createUploadPromise.then(function(e){t.uploadId=e.UploadId,o.resolve()}).catch(function(e){o.reject(),console.info(e)}),o.promise()}}function m(e){h("task-init-before",e);var r=a.default.getCookie("apiVersion"),o=n().server+"/upload/multipart/init";if(r&&"mamcore2.3"===r&&(o=n().server+"/sflud/v1/upload/multipart/init"),t.loginToken&&(o+="?token="+t.loginToken),e.metadata&&e.metadata.field){var i=e.metadata.field.find(function(e){return"filesize"===e.fieldName});i&&(i.value=String(i.value))}u.post(o,e).then(function(t){t=t.data;for(var n=0;n<t.files.length;n++){var r=e.files[n].file;e.files[n]=t.files[n],e.files[n].file=r,e.files[n].status="prepared",e.files[n].fileSizeString=a.default.formatSize(t.files[n].fileSize),e.files[n].progress=0,e.sizeTotal+=t.files[n].fileSize,e.chunkTotal+=t.files[n].chunkTotal}e.taskId=t.taskId,e.entityType=t.entityType,e.fileTotal=t.fileTotal,e.targetFolder=t.targetFolder,e.targetFolderName=t.targetFolderName,e.targetType=t.targetType,e.keyframe=t.keyframe,e.status="prepared",e.inited=!0,e.sizeTotalString=a.default.formatSize(e.sizeTotal),e.isJsUpload=t.isJsUpload,e.ossClientInfo=t.ossClientInfo,h("task-init-success",e),A.prepareUpload()},function(t){if(h("task-init-error",[e,t]),401===t.status)return void c(A.tasks);e.status="error",_.forEach(e.files,function(e){e.status="error"}),a.default.prompt(l("upload.","上传失败：${text}",{text:t.error.desc||t.error.title})),A.prepareUpload()})}function k(e,t,r){n().webUploadMd5Enable?(null==e.file.fileReader&&(e.file.fileReader=new FileReader),e.file.fileReader.onload=function(e){var t=new f;t.appendBinary(e.target.result);var n=t.end();t.destroy(),r(n)},e.file.fileReader.onerror=function(t){T(e)},e.file.fileReader.readAsBinaryString(t)):r("")}function w(e,t){null!=t.startTime&&(_.isNumber(t.surplusTime)||(t.surplusTime=0),t.surplusTime=(new Date-t.startTime)*(t.chunkTotal-t.chunkIndex),_.isNumber(e.surplusTime)||(e.surplusTime=0),e.surplusTime=(new Date-t.startTime)*(e.chunkTotal-e.chunkFinished)),t.startTime=new Date}function T(e){e.file.hasOwnProperty("errorCount")||(e.file.errorCount=0),e.file.errorCount<4?(e.file.errorCount++,C[e.file.fileId]=setTimeout(function(){A.upload(e)},3e3)):(e.task.status=e.file.status="error",h("task-upload-error",e.task),A.prepareUpload())}function b(e,t){if(e==t)return 100;var n=e/t*100;return-1==n.toString().indexOf(".")?n:n.toFixed(2)}function I(e){return b(e.chunkIndex,e.chunkTotal)}function S(e){for(var t=0,n=0;n<e.files.length;n++)t+=e.files[n].chunkIndex;return e.chunkFinished=t,b(e.chunkFinished,e.chunkTotal)}var x,P,E,A=this,C={};this.tasks=[],this.on=function(e,t){$(A).on(e,t)},this.off=function(e,t){$(A).off(e,t)},this.getGroupTask=function(e,t,r,o,i){var s=_.find(n().entityTypes,{code:t});return{entityType:s?s.code:"",files:e.map(function(e){return{fileName:e.fileName,file:e.file,metadata:e.metadata}}),metadata:{name:""},targetFolder:r,taskType:o,transferType:i,status:"added",progress:0}},this.getTasksByFiles=function(e){var t=[];return _.forEach(e,function(e){var r="."+a.default.getExtension(e.name);t.push({entityType:a.default.getTypeByExt(r,n()).code,fileName:e.name,metadata:{name:a.default.getFileName(e.name),ext:r},status:"added",progress:0,file:e})}),t},this.openFileSelector=function(e,t,n){var r=this;d(),p(n),!0===t?x.removeAttr("multiple"):x.attr("multiple","multiple"),x.on("change",function(){if(""!==x.val()){var o=x[0].files;!0===t&&(o=o[0]),e(r.getTasksByFiles(o)),p(n)}}),x.click(function(e){e.stopPropagation()}),x.trigger("click")},this.createTask=function(e,t){var n=[];switch(t.taskType){case 1:_.isArray(e)||(e=[e]),_.forEach(e,function(e){e.files&&e.files.length>0?(e.files=_.map(e.files,function(e){return{file:e.file,fileName:e.file.name,fileSize:e.file.size,fileLastModifiedDate:o(e.file),type:Number(e.type)?Number(e.type):0}}),e.file&&e.files.unshift({file:e.file,fileName:e.file.name,fileSize:e.file.size,fileLastModifiedDate:o(e.file),type:void 0!==e.type?Number(e.type):0})):e.files=[{file:e.file,fileName:e.file.name,fileSize:e.file.size,fileLastModifiedDate:o(e.file),type:void 0!==e.type?Number(e.type):0}],delete e.file,n.push(e)});break;case 2:case 3:_.forEach(e.files,function(e){e.fileName=e.file.name,e.fileSize=e.file.size,e.fileLastModifiedDate=o(e.file)}),n.push(e)}_.forEach(n,function(e){e.taskType=t.taskType,e.transferType=t.transferType,e.targetFolder=t.targetFolder,e.relationContentType=t.relationContentType,e.relationContentId=t.relationContentId,A.addTask(e)})},this.addTask=function(e){e.status="init",e.progress=e.sizeTotal=e.chunkFinished=e.chunkTotal=0,e.keyframe=s(e),A.tasks.push(e),m(e)},this.prepareUpload=function(){var e=A.getFilesByStatus("progress"),t=n().webUploadThreads-e.length;if(!(t<=0)){var r=A.getFilesByStatus("prepared");if(t>r.length&&(t=r.length),0!==t)for(var o=0;o<t;o++)r[o].task.status=r[o].file.status="progress",A.upload(r[o])}},this.upload=function(e){e.task.ossClientInfo?"ALY_OSS"===e.task.ossClientInfo.manageType?this.uploadByAliOss(e):"TYY_S3"===e.task.ossClientInfo.manageType?this.uploadByOos(e):"KSYUN"===e.task.ossClientInfo.manageType?this.uploadByKsyun(e):this.uploadByS3(e):this.uploadByBackend(e)},this.uploadByBackend=function(e){var r=e.file,o=e.task;if(r.chunkIndex<r.chunkTotal){w(o,r);var i=r.chunkIndex*r.chunkSize,s=Math.min(r.fileSize,i+r.chunkSize),f=new FormData,l=r.file.slice(i,s);if(null==l||0==l.size)return e.task.status=e.file.status="error",h("task-upload-error",e.task),e.file.file=null,void A.prepareUpload();k(e,l,function(i){if(f.append("fileData",l),f.append("taskId",r.taskId),f.append("fileId",r.fileId),f.append("chunkIndex",r.chunkIndex),f.append("md5",i),"deleteing"===o.status)return void A.clearTask(o);var s=a.default.getCookie("apiVersion"),p="/upload/multipart";s&&"mamcore2.3"===s&&(p="/sflud/v1/upload/multipart"),t.loginToken&&(p+="?token="+t.loginToken),u.post(n().server+p,f,{contentType:!1,processData:!1}).then(function(t){if("deleteing"===o.status)return void A.clearTask(o);t=t.data,r=_.find(o.files,{fileId:t.fileId}),r.chunkIndex=t.chunkIndex,3===t.taskStatus?(r.progress=o.progress=100,r.surplusTime=o.surplusTime=null,delete r.fileReader,r.status=o.status="success",A.prepareUpload(),U(o).then(function(){h("task-upload-success",o)})):(o.progress=S(o),r.progress=I(r),r.status=o.status="progress",h("task-upload-progress",o),r.errorCount=0,A.upload(e))},function(t){if(401===t.status)return void c();T(e)})})}else r.hasOwnProperty("errorCount")&&(delete C[r.fileId],delete r.errorCount),delete r.fileReader,r.surplusTime=null,r.progress=100,r.status="success",o.progress=S(o),h("task-upload-success",o),A.prepareUpload()};var O=function(e){var r=new FormData;r.append("taskId",e.taskId),r.append("fileId",e.fileId),r.append("chunkIndex",e.chunkIndex),r.append("partInfo",(0,i.default)(e.partInfo)),r.append("uploadId",e.uploadId),r.append("checkPoint",e.checkPoint);var o=a.default.getCookie("apiVersion"),s="/upload/multipart";return o&&"mamcore2.3"===o&&(s="/sflud/v1/upload/multipart"),t.loginToken&&(s+="?token="+t.loginToken),u.post(n().server+s,r,{contentType:!1,processData:!1})},U=function(e){var t=n().server,r=a.default.getCookie("apiVersion"),o="/upload/multipart/complete?taskId="+e.taskId;return r&&"mamcore2.3"===r&&(o="/sflud/v1/upload/multipart/complete?taskId="+e.taskId),u.post(t+o)};this.uploadByAliOssWithServerCallback=function(r){var o=r.file,s=r.task,u={region:s.ossClientInfo.region,accessKeyId:s.ossClientInfo.accessKeyId,accessKeySecret:s.ossClientInfo.accessKeySecret,bucket:s.ossClientInfo.bucketName};s.ossClientInfo.endpoint&&(u.endpoint=s.ossClientInfo.endpoint);var f=new this.OSS.Wrapper(u);if(o.fileSize<=102400)r.fileReader=new FileReader,r.fileReader.onload=function(t){f.put(o.relativePath,new e(this.result)).then(function(e){O(o).then(function(e){e=e.data,3===e.taskStatus?(o.progress=s.progress=100,o.surplusTime=s.surplusTime=null,delete o.fileReader,o.status=s.status="success",U(s).then(function(){h("task-upload-success",s)})):(o.status="success",o.progress=100),A.prepareUpload()})},function(e){console.error(e)})},r.fileReader.readAsArrayBuffer(o.file);else{var l=a.default.addUrlParam(n().ossUpCallbackUrl,"taskId="+s.taskId);t.loginToken&&(l=a.default.addUrlParam(l,"token="+t.loginToken));var p={partSize:o.chunkSize,progress:function(e,t,n){return function(e){if("deleteing"===s.status)A.clearTask(s);else if("success"===s.status)e();else{if(0===t.doneParts.length)return void e();o.partInfo=t.doneParts[t.doneParts.length-1],o.uploadId=t.uploadId,o.checkPoint=(0,i.default)(t,function(e,t){if("file"!==e)return t}),o.chunkIndex=t.doneParts.length-1,O(o).then(function(t){if("deleteing"===s.status)return void A.clearTask(s);t=t.data,o.chunkIndex=t.chunkIndex,3===t.taskStatus?(o.progress=s.progress=100,o.surplusTime=s.surplusTime=null,delete o.fileReader,o.status=s.status="success",h("task-upload-success",s),A.prepareUpload()):o.chunkIndex<o.chunkTotal?(s.progress=S(s),o.progress=I(o),o.status=s.status="progress",h("task-upload-progress",s),o.errorCount=0):(o.hasOwnProperty("errorCount")&&(delete C[o.fileId],delete o.errorCount),o.surplusTime=null,o.progress=100,o.status="success",s.progress=S(s),h("task-upload-success",s),A.prepareUpload()),e()},function(e){if(401===e.status)return void c();T(r)})}}},callback:{callbackUrl:l,callbackBody:"bucket=${bucket}&object=${object}&etag=${etag}&size=${size}&mimeType=${mimeType}&imageInfo.height=${imageInfo.height}&imageInfo.width=${imageInfo.width}&imageInfo.format=${imageInfo.format}"}};o.checkPoint&&(p.checkpoint=JSON.parse(o.checkPoint),p.checkpoint.file=o.file),f.multipartUpload(o.relativePath,o.file,p).then(function(e){console.log("upload success",e)},function(e){console.log(e)})}},this.uploadByAliOss=function(t){var n=t.file,r=t.task,o={region:r.ossClientInfo.region,accessKeyId:r.ossClientInfo.accessKeyId,accessKeySecret:r.ossClientInfo.accessKeySecret,bucket:r.ossClientInfo.bucketName};r.ossClientInfo.endpoint&&(o.endpoint=r.ossClientInfo.endpoint);var s=new this.OSS.Wrapper(o);if(n.fileSize<=102400)t.fileReader=new FileReader,t.fileReader.onload=function(t){s.put(n.relativePath,new e(this.result)).then(function(e){O(n).then(function(e){e=e.data,3===e.taskStatus?(n.progress=r.progress=100,n.surplusTime=r.surplusTime=null,delete n.fileReader,n.status=r.status="success",U(r).then(function(){h("task-upload-success",r)})):(n.status="success",n.progress=100),A.prepareUpload()})},function(e){console.error(e)})},t.fileReader.readAsArrayBuffer(n.file);else{var a={partSize:n.chunkSize,progress:function(e,o,s){return function(e){if("deleteing"===r.status)A.clearTask(r);else if("success"===r.status)e();else{if(0===o.doneParts.length)return void e();n.partInfo=o.doneParts[o.doneParts.length-1],n.uploadId=o.uploadId,n.checkPoint=(0,i.default)(o,function(e,t){if("file"!==e)return t}),n.chunkIndex=o.doneParts.length-1,O(n).then(function(t){if("deleteing"===r.status)return void A.clearTask(r);t=t.data,n.chunkIndex=t.chunkIndex,3===t.taskStatus?(n.progress=r.progress=100,n.surplusTime=r.surplusTime=null,delete n.fileReader,n.status=r.status="success",A.prepareUpload(),U(r).then(function(){h("task-upload-success",r)})):n.chunkIndex<n.chunkTotal?(r.progress=S(r),n.progress=I(n),n.status=r.status="progress",h("task-upload-progress",r),n.errorCount=0):(n.hasOwnProperty("errorCount")&&(delete C[n.fileId],delete n.errorCount),n.surplusTime=null,n.progress=100,n.status="success",r.progress=S(r),h("task-upload-success",r),A.prepareUpload()),e()},function(e){if(401===e.status)return void c();T(t)})}}}};n.checkPoint&&(a.checkpoint=JSON.parse(n.checkPoint),a.checkpoint.file=n.file),s.multipartUpload(n.relativePath,n.file,a).then(function(e){console.log("upload success",e)},function(e){console.log(e)})}},this.doUploadByKsyun=function(e){var t=e.file,n=e.task,r=t.chunkIndex*t.chunkSize,o=Math.min(t.fileSize,r+t.chunkSize),i=t.file.slice(r,o),s={Bucket:n.ossClientInfo.bucketName,Key:t.relativePath.replace(/\\/g,"/"),PartNumber:t.chunkIndex+1,UploadId:t.uploadId,body:i};Ks3.upload_part(s,function(r,o,i){if(r)return void console.error(r);"deleteing"===n.status?A.clearTask(n):O(t).then(function(r){if("deleteing"===n.status)return void A.clearTask(n);r=r.data,t.chunkIndex=r.chunkIndex,3===r.taskStatus?(t.progress=n.progress=100,t.surplusTime=n.surplusTime=null,delete t.fileReader,t.status=n.status="success",A.prepareUpload(),U(n).then(function(){h("task-upload-success",n)})):t.chunkIndex<t.chunkTotal?(n.progress=S(n),t.progress=I(t),t.status=n.status="progress",h("task-upload-progress",n),t.errorCount=0,A.upload(e)):(t.hasOwnProperty("errorCount")&&(delete C[t.fileId],delete t.errorCount),t.surplusTime=null,t.progress=100,t.status="success",n.progress=S(n),h("task-upload-success",n),A.prepareUpload())},function(t){if(401===t.status)return void c();T(e)})})},this.uploadByKsyun=function(e){var t=e.file,n=e.task;n.ossClientInfo.accessKeyId&&n.ossClientInfo.accessKeySecret&&(Ks3.config.AK=n.ossClientInfo.accessKeyId,Ks3.config.SK=n.ossClientInfo.accessKeySecret),Ks3.config.region=n.ossClientInfo.region,Ks3.ENDPOINT[""+n.ossClientInfo.region]=n.ossClientInfo.serviceUrl;var r={KSSAccessKeyId:n.ossClientInfo.accessKeyId,signature:n.ossClientInfo.accessKeySecret,bucket_name:n.ossClientInfo.bucketName,key:t.relativePath.replace(/\\/g,"/"),acl:"public-read",uploadDomain:n.ossClientInfo.serviceUrl+"/"+n.ossClientInfo.bucketName,autoStart:!1},o={drop_element:document.body,url:n.ossClientInfo.serviceUrl};new ks3FileUploader(r,o);if(t.fileSize<=104857600){var i={Bucket:n.ossClientInfo.bucketName,Key:t.relativePath.replace(/\\/g,"/"),File:t.file,ACL:"public-read"};Ks3.putObject(i,function(r){r?(console.info(r,r.error),T(e)):O(t).then(function(e){e=e.data,3===e.taskStatus?(t.progress=n.progress=100,t.surplusTime=n.surplusTime=null,delete t.fileReader,t.status=n.status="success",U(n).then(function(){h("task-upload-success",n)})):(t.status="success",t.progress=100),A.prepareUpload()})})}else{var i={Bucket:n.ossClientInfo.bucketName,Key:t.relativePath.replace(/\\/g,"/"),ACL:"public-read"};t.uploadId?this.doUploadByKsyun(e):Ks3.multitpart_upload_init(i,function(n,r){n?console.error(n):(t.uploadId=r,this.doUploadByKsyun(e))})}},this.uploadByS3=function(e){var r=e.file,o=e.task;if(E||y(e.task.ossClientInfo),r.chunkIndex<r.chunkTotal){var i=r.chunkIndex*r.chunkSize,s=Math.min(r.fileSize,i+r.chunkSize),f=new FormData,l=r.file.slice(i,s);if(null==l||0==l.size)return e.task.status=e.file.status="error",h("task-upload-error",e.task),e.file.file=null,void A.prepareUpload();var p=function(r,o){if(f.append("taskId",o.taskId),f.append("fileId",o.fileId),f.append("chunkIndex",o.chunkIndex),f.append("partInfo",o.partInfo),f.append("uploadId",o.uploadId),"deleteing"===r.status)return void A.clearTask(r);var i=a.default.getCookie("apiVersion"),s="/upload/multipart";i&&"mamcore2.3"===i&&(s="/sflud/v1/upload/multipart"),t.loginToken&&(s+="?token="+t.loginToken),u.post(n().server+s,f,{contentType:!1,processData:!1}).then(function(t){if("deleteing"===r.status)return void A.clearTask(r);t=t.data,o=_.find(r.files,{fileId:t.fileId}),o.chunkIndex=t.chunkIndex,3===t.taskStatus?(o.progress=r.progress=100,o.surplusTime=r.surplusTime=null,delete o.fileReader,o.status=r.status="success",A.prepareUpload(),U(r).then(function(){h("task-upload-success",r)})):(r.progress=S(r),o.progress=I(o),o.status=r.status="progress",h("task-upload-progress",r),o.errorCount=0,A.upload(e))},function(t){if(401===t.status)return void c();T(e)})};if(r.fileSize<=5242880)E.putObject({Body:r.file,Bucket:o.ossClientInfo.bucketName,Key:r.relativePath.replace(/\\/g,"/")},function(t,n){t?(console.info(t,t.stack),T(e)):(n.ETag&&(r.partInfo=n.ETag.replace(/"/g,"")),p(o,r),console.info(n))});else{if(!r.uploadId){var d=g(o.ossClientInfo,r);return void(d&&d.then(function(){A.upload(e)}))}var v={Body:l,Bucket:o.ossClientInfo.bucketName,Key:r.relativePath.replace(/\\/g,"/"),PartNumber:r.chunkIndex+1,UploadId:r.uploadId};E.uploadPart(v,function(t,n){t?(console.info(t,t.stack),T(e)):(n.ETag&&(r.partInfo=n.ETag.replace(/"/g,"")),p(o,r),console.info(n))})}}else r.hasOwnProperty("errorCount")&&(delete C[r.fileId],delete r.errorCount),delete r.fileReader,r.surplusTime=null,r.progress=100,r.status="success",o.progress=S(o),h("task-upload-success",o),A.prepareUpload()},this.uploadByOos=function(e){var n=e.file,r=e.task;if(n.chunkIndex<n.chunkTotal){var o=n.chunkIndex*n.chunkSize,i=Math.min(n.fileSize,o+n.chunkSize),s=new FormData,f=n.file.slice(o,i);if(null==f||0==f.size)return e.task.status=e.file.status="error",h("task-upload-error",e.task),e.file.file=null,void A.prepareUpload();var l=function(n,r){if(s.append("taskId",r.taskId),s.append("fileId",r.fileId),s.append("chunkIndex",r.chunkIndex),s.append("partInfo",r.partInfo),s.append("uploadId",r.uploadId),"deleteing"===n.status)return void A.clearTask(n);var o=a.default.getCookie("apiVersion"),i="/upload/multipart";o&&"mamcore2.3"===o&&(i="/sflud/v1/upload/multipart"),t.loginToken&&(i+="?token="+t.loginToken),u.post(i,s,{contentType:!1,processData:!1}).then(function(t){if("deleteing"===n.status)return void A.clearTask(n);t=t.data,r=_.find(n.files,{fileId:t.fileId}),r.chunkIndex=t.chunkIndex,3===t.taskStatus?(r.progress=n.progress=100,r.surplusTime=n.surplusTime=null,delete r.fileReader,r.status=n.status="success",A.prepareUpload(),U(n).then(function(){h("task-upload-success",n)})):(n.progress=S(n),r.progress=I(r),r.status=n.status="progress",h("task-upload-progress",n),r.errorCount=0,A.upload(e))},function(t){if(401===t.status)return void c();T(e)})};if(E||y(r.ossClientInfo),n.fileSize<=5242880)E.putObject({Body:n.file,Bucket:r.ossClientInfo.bucketName,Key:n.relativePath.replace(/\\/g,"/")},function(t,o){t?(console.info(t,t.stack),T(e)):(o.ETag&&(n.partInfo=o.ETag.replace(/"/g,"")),l(r,n),console.info(o))});else{if(!n.uploadId){var p=g(r.ossClientInfo,n);return void(p&&p.then(function(){A.upload(e)}))}var d={Body:f,Bucket:r.ossClientInfo.bucketName,Key:n.relativePath.replace(/\\/g,"/"),PartNumber:n.chunkIndex+1,UploadId:n.uploadId};E.uploadPart(d,function(t,o){t?(console.info(t,t.stack),T(e)):(o.ETag&&(n.partInfo=o.ETag.replace(/"/g,"")),l(r,n),console.info(o))})}}else n.hasOwnProperty("errorCount")&&(delete C[n.fileId],delete n.errorCount),delete n.fileReader,n.surplusTime=null,n.progress=100,n.status="success",r.progress=S(r),h("task-upload-success",r),A.prepareUpload()},this.continueUpload=function(e,t,n){null==t.file?A.openFileSelector(function(r){var o=A.calcFileMd5(r[0].file);if(t.fileMd5!==o)return a.default.prompt(l("upload.fileDiffer","选择的文件不一致，请重新上传")),void(n&&n.apply(window,[l("upload.fileDiffer","选择的文件不一致，请重新上传")]));t.file=r[0].file,e.status=t.status="prepared",A.prepareUpload()},!1):e.inited?(e.status=t.status="prepared",A.prepareUpload()):m(e)},this.getUnfinishedTask=function(e,r,o){var i=$.Deferred();null==e&&(e=""),_.isNumber(r)||(r=0),_.isNumber(r)||(r=1);var s=a.default.getCookie("apiVersion"),f="/upload/get-unfinished-task?relationId="+e+"&relationContentType="+r+"&targetType="+o+(t.loginToken?"&token="+t.loginToken:"");return s&&"mamcore2.3"===s&&(f="/sflud/v1/upload/unfinished-task?relationId="+e+"&relationContentType="+r+"&targetType="+o+(t.loginToken?"&token="+t.loginToken:"")),u.get(n().server+f).then(function(e){var t=[];_.forEach(e.data,function(e){e.status="error",e.inited=!0,e.sizeTotal=e.chunkFinished=e.chunkTotal=0,_.forEach(e.files,function(t){t.fileSizeString=a.default.formatSize(t.fileSize),t.progress=I(t),t.status=100===t.progress?"success":"error",e.sizeTotal+=t.fileSize,e.chunkTotal+=t.chunkTotal}),e.progress=S(e),e.sizeTotalString=a.default.formatSize(e.sizeTotal),A.tasks.push(e),t.push(e)}),i.resolve(t)},function(e){i.reject(e)}),i},this.canDeleteTask=function(e){return null!=e&&("init"!=e.status&&("deleteing"!=e.status&&("progress"!=e.status||e.chunkFinished!=e.chunkTotal-1)))},this.removeTask=function(e){_.remove(A.tasks,function(t){return t.taskId===e.taskId})},this.deleteTask=function(e){if(this.canDeleteTask(e))if(!0===e.inited)switch(_.forEach(e.files,function(e){null!=C[e.fileId]&&(timeout.cancel(C[e.fileId]),delete C[e.fileId])}),e.status){case"progress":return void(e.status="deleteing");case"prepared":case"error":return void A.clearTask(e);default:A.removeTask(e),h("task-delete-success",e)}else A.removeTask(e),h("task-delete-success",e)},this.clearTask=function(e){var r={};e.files&&e.files.length>0&&_.forEach(e.files,function(e){r[e.fileId]=e.uploadId});var o=a.default.getCookie("apiVersion"),i="/upload/delete-task";o&&"mamcore2.3"===o&&(i="/sflud/v1/upload/task"),t.loginToken&&(i+="?token="+t.loginToken),o&&"mamcore2.1"!==o?u.delete(n().server+i,{taskId:e.taskId,fileAndTag:r}).then(function(t){A.removeTask(e),h("task-delete-success",e),A.prepareUpload()},function(t){h("task-delete-error",[e,t])}):u.post(n().server+i,{taskId:e.taskId,fileAndTag:r}).then(function(t){A.removeTask(e),h("task-delete-success",e),A.prepareUpload()},function(t){h("task-delete-error",[e,t])})},this.calcFileMd5=function(e){return f.hash(e.name+e.size+o(e))},this.getFilesByStatus=function(){for(var e=[].slice.call(arguments,0),t=[],n=0;n<A.tasks.length;n++)for(var r=0;r<A.tasks[n].files.length;r++)for(var o=0;o<e.length;o++)if(A.tasks[n].files[r].status===e[o]){t.push({task:A.tasks[n],file:A.tasks[n].files[r]});break}return t},function(){$(window).on("beforeunload",r)}()};t.default=c}).call(t,n(82).Buffer)},function(e,t,n){"use strict";(function(e){function r(){return i.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function o(e,t){if(r()<t)throw new RangeError("Invalid typed array length");return i.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t),e.__proto__=i.prototype):(null===e&&(e=new i(t)),e.length=t),e}function i(e,t,n){if(!(i.TYPED_ARRAY_SUPPORT||this instanceof i))return new i(e,t,n);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return f(this,e)}return s(this,e,t,n)}function s(e,t,n,r){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?p(e,t,n,r):"string"==typeof t?c(e,t,n):d(e,t)}function a(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function u(e,t,n,r){return a(t),t<=0?o(e,t):void 0!==n?"string"==typeof r?o(e,t).fill(n,r):o(e,t).fill(n):o(e,t)}function f(e,t){if(a(t),e=o(e,t<0?0:0|h(t)),!i.TYPED_ARRAY_SUPPORT)for(var n=0;n<t;++n)e[n]=0;return e}function c(e,t,n){if("string"==typeof n&&""!==n||(n="utf8"),!i.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var r=0|y(t,n);e=o(e,r);var s=e.write(t,n);return s!==r&&(e=e.slice(0,s)),e}function l(e,t){var n=t.length<0?0:0|h(t.length);e=o(e,n);for(var r=0;r<n;r+=1)e[r]=255&t[r];return e}function p(e,t,n,r){if(t.byteLength,n<0||t.byteLength<n)throw new RangeError("'offset' is out of bounds");if(t.byteLength<n+(r||0))throw new RangeError("'length' is out of bounds");return t=void 0===n&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,n):new Uint8Array(t,n,r),i.TYPED_ARRAY_SUPPORT?(e=t,e.__proto__=i.prototype):e=l(e,t),e}function d(e,t){if(i.isBuffer(t)){var n=0|h(t.length);return e=o(e,n),0===e.length?e:(t.copy(e,0,0,n),e)}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||W(t.length)?o(e,0):l(e,t);if("Buffer"===t.type&&X(t.data))return l(e,t.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function h(e){if(e>=r())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+r().toString(16)+" bytes");return 0|e}function g(e){return+e!=e&&(e=0),i.alloc(+e)}function y(e,t){if(i.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var n=e.length;if(0===n)return 0;for(var r=!1;;)switch(t){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return $(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return J(e).length;default:if(r)return $(e).length;t=(""+t).toLowerCase(),r=!0}}function v(e,t,n){var r=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if(n>>>=0,t>>>=0,n<=t)return"";for(e||(e="utf8");;)switch(e){case"hex":return U(this,t,n);case"utf8":case"utf-8":return E(this,t,n);case"ascii":return C(this,t,n);case"latin1":case"binary":return O(this,t,n);case"base64":return P(this,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return R(this,t,n);default:if(r)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),r=!0}}function m(e,t,n){var r=e[t];e[t]=e[n],e[n]=r}function k(e,t,n,r,o){if(0===e.length)return-1;if("string"==typeof n?(r=n,n=0):n>2147483647?n=2147483647:n<-2147483648&&(n=-2147483648),n=+n,isNaN(n)&&(n=o?0:e.length-1),n<0&&(n=e.length+n),n>=e.length){if(o)return-1;n=e.length-1}else if(n<0){if(!o)return-1;n=0}if("string"==typeof t&&(t=i.from(t,r)),i.isBuffer(t))return 0===t.length?-1:w(e,t,n,r,o);if("number"==typeof t)return t&=255,i.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?o?Uint8Array.prototype.indexOf.call(e,t,n):Uint8Array.prototype.lastIndexOf.call(e,t,n):w(e,[t],n,r,o);throw new TypeError("val must be string, number or Buffer")}function w(e,t,n,r,o){function i(e,t){return 1===s?e[t]:e.readUInt16BE(t*s)}var s=1,a=e.length,u=t.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(e.length<2||t.length<2)return-1;s=2,a/=2,u/=2,n/=2}var f;if(o){var c=-1;for(f=n;f<a;f++)if(i(e,f)===i(t,-1===c?0:f-c)){if(-1===c&&(c=f),f-c+1===u)return c*s}else-1!==c&&(f-=f-c),c=-1}else for(n+u>a&&(n=a-u),f=n;f>=0;f--){for(var l=!0,p=0;p<u;p++)if(i(e,f+p)!==i(t,p)){l=!1;break}if(l)return f}return-1}function T(e,t,n,r){n=Number(n)||0;var o=e.length-n;r?(r=Number(r))>o&&(r=o):r=o;var i=t.length;if(i%2!=0)throw new TypeError("Invalid hex string");r>i/2&&(r=i/2);for(var s=0;s<r;++s){var a=parseInt(t.substr(2*s,2),16);if(isNaN(a))return s;e[n+s]=a}return s}function b(e,t,n,r){return Q($(t,e.length-n),e,n,r)}function _(e,t,n,r){return Q(V(t),e,n,r)}function I(e,t,n,r){return _(e,t,n,r)}function S(e,t,n,r){return Q(J(t),e,n,r)}function x(e,t,n,r){return Q(G(t,e.length-n),e,n,r)}function P(e,t,n){return 0===t&&n===e.length?H.fromByteArray(e):H.fromByteArray(e.slice(t,n))}function E(e,t,n){n=Math.min(e.length,n);for(var r=[],o=t;o<n;){var i=e[o],s=null,a=i>239?4:i>223?3:i>191?2:1;if(o+a<=n){var u,f,c,l;switch(a){case 1:i<128&&(s=i);break;case 2:u=e[o+1],128==(192&u)&&(l=(31&i)<<6|63&u)>127&&(s=l);break;case 3:u=e[o+1],f=e[o+2],128==(192&u)&&128==(192&f)&&(l=(15&i)<<12|(63&u)<<6|63&f)>2047&&(l<55296||l>57343)&&(s=l);break;case 4:u=e[o+1],f=e[o+2],c=e[o+3],128==(192&u)&&128==(192&f)&&128==(192&c)&&(l=(15&i)<<18|(63&u)<<12|(63&f)<<6|63&c)>65535&&l<1114112&&(s=l)}}null===s?(s=65533,a=1):s>65535&&(s-=65536,r.push(s>>>10&1023|55296),s=56320|1023&s),r.push(s),o+=a}return A(r)}function A(e){var t=e.length;if(t<=Z)return String.fromCharCode.apply(String,e);for(var n="",r=0;r<t;)n+=String.fromCharCode.apply(String,e.slice(r,r+=Z));return n}function C(e,t,n){var r="";n=Math.min(e.length,n);for(var o=t;o<n;++o)r+=String.fromCharCode(127&e[o]);return r}function O(e,t,n){var r="";n=Math.min(e.length,n);for(var o=t;o<n;++o)r+=String.fromCharCode(e[o]);return r}function U(e,t,n){var r=e.length;(!t||t<0)&&(t=0),(!n||n<0||n>r)&&(n=r);for(var o="",i=t;i<n;++i)o+=K(e[i]);return o}function R(e,t,n){for(var r=e.slice(t,n),o="",i=0;i<r.length;i+=2)o+=String.fromCharCode(r[i]+256*r[i+1]);return o}function B(e,t,n){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>n)throw new RangeError("Trying to access beyond buffer length")}function N(e,t,n,r,o,s){if(!i.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>o||t<s)throw new RangeError('"value" argument is out of bounds');if(n+r>e.length)throw new RangeError("Index out of range")}function F(e,t,n,r){t<0&&(t=65535+t+1);for(var o=0,i=Math.min(e.length-n,2);o<i;++o)e[n+o]=(t&255<<8*(r?o:1-o))>>>8*(r?o:1-o)}function M(e,t,n,r){t<0&&(t=4294967295+t+1);for(var o=0,i=Math.min(e.length-n,4);o<i;++o)e[n+o]=t>>>8*(r?o:3-o)&255}function j(e,t,n,r,o,i){if(n+r>e.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function L(e,t,n,r,o){return o||j(e,t,n,4,3.4028234663852886e38,-3.4028234663852886e38),q.write(e,t,n,r,23,4),n+4}function D(e,t,n,r,o){return o||j(e,t,n,8,1.7976931348623157e308,-1.7976931348623157e308),q.write(e,t,n,r,52,8),n+8}function z(e){if(e=Y(e).replace(ee,""),e.length<2)return"";for(;e.length%4!=0;)e+="=";return e}function Y(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}function K(e){return e<16?"0"+e.toString(16):e.toString(16)}function $(e,t){t=t||1/0;for(var n,r=e.length,o=null,i=[],s=0;s<r;++s){if((n=e.charCodeAt(s))>55295&&n<57344){if(!o){if(n>56319){(t-=3)>-1&&i.push(239,191,189);continue}if(s+1===r){(t-=3)>-1&&i.push(239,191,189);continue}o=n;continue}if(n<56320){(t-=3)>-1&&i.push(239,191,189),o=n;continue}n=65536+(o-55296<<10|n-56320)}else o&&(t-=3)>-1&&i.push(239,191,189);if(o=null,n<128){if((t-=1)<0)break;i.push(n)}else if(n<2048){if((t-=2)<0)break;i.push(n>>6|192,63&n|128)}else if(n<65536){if((t-=3)<0)break;i.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;i.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return i}function V(e){for(var t=[],n=0;n<e.length;++n)t.push(255&e.charCodeAt(n));return t}function G(e,t){for(var n,r,o,i=[],s=0;s<e.length&&!((t-=2)<0);++s)n=e.charCodeAt(s),r=n>>8,o=n%256,i.push(o),i.push(r);return i}function J(e){return H.toByteArray(z(e))}function Q(e,t,n,r){for(var o=0;o<r&&!(o+n>=t.length||o>=e.length);++o)t[o+n]=e[o];return o}function W(e){return e!==e}/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <http://feross.org>
 * @license  MIT
 */
var H=n(84),q=n(85),X=n(86);t.Buffer=i,t.SlowBuffer=g,t.INSPECT_MAX_BYTES=50,i.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}(),t.kMaxLength=r(),i.poolSize=8192,i._augment=function(e){return e.__proto__=i.prototype,e},i.from=function(e,t,n){return s(null,e,t,n)},i.TYPED_ARRAY_SUPPORT&&(i.prototype.__proto__=Uint8Array.prototype,i.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&i[Symbol.species]===i&&Object.defineProperty(i,Symbol.species,{value:null,configurable:!0})),i.alloc=function(e,t,n){return u(null,e,t,n)},i.allocUnsafe=function(e){return f(null,e)},i.allocUnsafeSlow=function(e){return f(null,e)},i.isBuffer=function(e){return!(null==e||!e._isBuffer)},i.compare=function(e,t){if(!i.isBuffer(e)||!i.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var n=e.length,r=t.length,o=0,s=Math.min(n,r);o<s;++o)if(e[o]!==t[o]){n=e[o],r=t[o];break}return n<r?-1:r<n?1:0},i.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},i.concat=function(e,t){if(!X(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return i.alloc(0);var n;if(void 0===t)for(t=0,n=0;n<e.length;++n)t+=e[n].length;var r=i.allocUnsafe(t),o=0;for(n=0;n<e.length;++n){var s=e[n];if(!i.isBuffer(s))throw new TypeError('"list" argument must be an Array of Buffers');s.copy(r,o),o+=s.length}return r},i.byteLength=y,i.prototype._isBuffer=!0,i.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)m(this,t,t+1);return this},i.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)m(this,t,t+3),m(this,t+1,t+2);return this},i.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)m(this,t,t+7),m(this,t+1,t+6),m(this,t+2,t+5),m(this,t+3,t+4);return this},i.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?E(this,0,e):v.apply(this,arguments)},i.prototype.equals=function(e){if(!i.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===i.compare(this,e)},i.prototype.inspect=function(){var e="",n=t.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,n).match(/.{2}/g).join(" "),this.length>n&&(e+=" ... ")),"<Buffer "+e+">"},i.prototype.compare=function(e,t,n,r,o){if(!i.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===n&&(n=e?e.length:0),void 0===r&&(r=0),void 0===o&&(o=this.length),t<0||n>e.length||r<0||o>this.length)throw new RangeError("out of range index");if(r>=o&&t>=n)return 0;if(r>=o)return-1;if(t>=n)return 1;if(t>>>=0,n>>>=0,r>>>=0,o>>>=0,this===e)return 0;for(var s=o-r,a=n-t,u=Math.min(s,a),f=this.slice(r,o),c=e.slice(t,n),l=0;l<u;++l)if(f[l]!==c[l]){s=f[l],a=c[l];break}return s<a?-1:a<s?1:0},i.prototype.includes=function(e,t,n){return-1!==this.indexOf(e,t,n)},i.prototype.indexOf=function(e,t,n){return k(this,e,t,n,!0)},i.prototype.lastIndexOf=function(e,t,n){return k(this,e,t,n,!1)},i.prototype.write=function(e,t,n,r){if(void 0===t)r="utf8",n=this.length,t=0;else if(void 0===n&&"string"==typeof t)r=t,n=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(n)?(n|=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}var o=this.length-t;if((void 0===n||n>o)&&(n=o),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var i=!1;;)switch(r){case"hex":return T(this,e,t,n);case"utf8":case"utf-8":return b(this,e,t,n);case"ascii":return _(this,e,t,n);case"latin1":case"binary":return I(this,e,t,n);case"base64":return S(this,e,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return x(this,e,t,n);default:if(i)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),i=!0}},i.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var Z=4096;i.prototype.slice=function(e,t){var n=this.length;e=~~e,t=void 0===t?n:~~t,e<0?(e+=n)<0&&(e=0):e>n&&(e=n),t<0?(t+=n)<0&&(t=0):t>n&&(t=n),t<e&&(t=e);var r;if(i.TYPED_ARRAY_SUPPORT)r=this.subarray(e,t),r.__proto__=i.prototype;else{var o=t-e;r=new i(o,void 0);for(var s=0;s<o;++s)r[s]=this[s+e]}return r},i.prototype.readUIntLE=function(e,t,n){e|=0,t|=0,n||B(e,t,this.length);for(var r=this[e],o=1,i=0;++i<t&&(o*=256);)r+=this[e+i]*o;return r},i.prototype.readUIntBE=function(e,t,n){e|=0,t|=0,n||B(e,t,this.length);for(var r=this[e+--t],o=1;t>0&&(o*=256);)r+=this[e+--t]*o;return r},i.prototype.readUInt8=function(e,t){return t||B(e,1,this.length),this[e]},i.prototype.readUInt16LE=function(e,t){return t||B(e,2,this.length),this[e]|this[e+1]<<8},i.prototype.readUInt16BE=function(e,t){return t||B(e,2,this.length),this[e]<<8|this[e+1]},i.prototype.readUInt32LE=function(e,t){return t||B(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},i.prototype.readUInt32BE=function(e,t){return t||B(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},i.prototype.readIntLE=function(e,t,n){e|=0,t|=0,n||B(e,t,this.length);for(var r=this[e],o=1,i=0;++i<t&&(o*=256);)r+=this[e+i]*o;return o*=128,r>=o&&(r-=Math.pow(2,8*t)),r},i.prototype.readIntBE=function(e,t,n){e|=0,t|=0,n||B(e,t,this.length);for(var r=t,o=1,i=this[e+--r];r>0&&(o*=256);)i+=this[e+--r]*o;return o*=128,i>=o&&(i-=Math.pow(2,8*t)),i},i.prototype.readInt8=function(e,t){return t||B(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},i.prototype.readInt16LE=function(e,t){t||B(e,2,this.length);var n=this[e]|this[e+1]<<8;return 32768&n?4294901760|n:n},i.prototype.readInt16BE=function(e,t){t||B(e,2,this.length);var n=this[e+1]|this[e]<<8;return 32768&n?4294901760|n:n},i.prototype.readInt32LE=function(e,t){return t||B(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},i.prototype.readInt32BE=function(e,t){return t||B(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},i.prototype.readFloatLE=function(e,t){return t||B(e,4,this.length),q.read(this,e,!0,23,4)},i.prototype.readFloatBE=function(e,t){return t||B(e,4,this.length),q.read(this,e,!1,23,4)},i.prototype.readDoubleLE=function(e,t){return t||B(e,8,this.length),q.read(this,e,!0,52,8)},i.prototype.readDoubleBE=function(e,t){return t||B(e,8,this.length),q.read(this,e,!1,52,8)},i.prototype.writeUIntLE=function(e,t,n,r){if(e=+e,t|=0,n|=0,!r){N(this,e,t,n,Math.pow(2,8*n)-1,0)}var o=1,i=0;for(this[t]=255&e;++i<n&&(o*=256);)this[t+i]=e/o&255;return t+n},i.prototype.writeUIntBE=function(e,t,n,r){if(e=+e,t|=0,n|=0,!r){N(this,e,t,n,Math.pow(2,8*n)-1,0)}var o=n-1,i=1;for(this[t+o]=255&e;--o>=0&&(i*=256);)this[t+o]=e/i&255;return t+n},i.prototype.writeUInt8=function(e,t,n){return e=+e,t|=0,n||N(this,e,t,1,255,0),i.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},i.prototype.writeUInt16LE=function(e,t,n){return e=+e,t|=0,n||N(this,e,t,2,65535,0),i.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):F(this,e,t,!0),t+2},i.prototype.writeUInt16BE=function(e,t,n){return e=+e,t|=0,n||N(this,e,t,2,65535,0),i.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):F(this,e,t,!1),t+2},i.prototype.writeUInt32LE=function(e,t,n){return e=+e,t|=0,n||N(this,e,t,4,4294967295,0),i.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):M(this,e,t,!0),t+4},i.prototype.writeUInt32BE=function(e,t,n){return e=+e,t|=0,n||N(this,e,t,4,4294967295,0),i.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):M(this,e,t,!1),t+4},i.prototype.writeIntLE=function(e,t,n,r){if(e=+e,t|=0,!r){var o=Math.pow(2,8*n-1);N(this,e,t,n,o-1,-o)}var i=0,s=1,a=0;for(this[t]=255&e;++i<n&&(s*=256);)e<0&&0===a&&0!==this[t+i-1]&&(a=1),this[t+i]=(e/s>>0)-a&255;return t+n},i.prototype.writeIntBE=function(e,t,n,r){if(e=+e,t|=0,!r){var o=Math.pow(2,8*n-1);N(this,e,t,n,o-1,-o)}var i=n-1,s=1,a=0;for(this[t+i]=255&e;--i>=0&&(s*=256);)e<0&&0===a&&0!==this[t+i+1]&&(a=1),this[t+i]=(e/s>>0)-a&255;return t+n},i.prototype.writeInt8=function(e,t,n){return e=+e,t|=0,n||N(this,e,t,1,127,-128),i.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},i.prototype.writeInt16LE=function(e,t,n){return e=+e,t|=0,n||N(this,e,t,2,32767,-32768),i.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):F(this,e,t,!0),t+2},i.prototype.writeInt16BE=function(e,t,n){return e=+e,t|=0,n||N(this,e,t,2,32767,-32768),i.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):F(this,e,t,!1),t+2},i.prototype.writeInt32LE=function(e,t,n){return e=+e,t|=0,n||N(this,e,t,4,2147483647,-2147483648),i.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):M(this,e,t,!0),t+4},i.prototype.writeInt32BE=function(e,t,n){return e=+e,t|=0,n||N(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),i.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):M(this,e,t,!1),t+4},i.prototype.writeFloatLE=function(e,t,n){return L(this,e,t,!0,n)},i.prototype.writeFloatBE=function(e,t,n){return L(this,e,t,!1,n)},i.prototype.writeDoubleLE=function(e,t,n){return D(this,e,t,!0,n)},i.prototype.writeDoubleBE=function(e,t,n){return D(this,e,t,!1,n)},i.prototype.copy=function(e,t,n,r){if(n||(n=0),r||0===r||(r=this.length),t>=e.length&&(t=e.length),t||(t=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),e.length-t<r-n&&(r=e.length-t+n);var o,s=r-n;if(this===e&&n<t&&t<r)for(o=s-1;o>=0;--o)e[o+t]=this[o+n];else if(s<1e3||!i.TYPED_ARRAY_SUPPORT)for(o=0;o<s;++o)e[o+t]=this[o+n];else Uint8Array.prototype.set.call(e,this.subarray(n,n+s),t);return s},i.prototype.fill=function(e,t,n,r){if("string"==typeof e){if("string"==typeof t?(r=t,t=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),1===e.length){var o=e.charCodeAt(0);o<256&&(e=o)}if(void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!i.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(n<=t)return this;t>>>=0,n=void 0===n?this.length:n>>>0,e||(e=0);var s;if("number"==typeof e)for(s=t;s<n;++s)this[s]=e;else{var a=i.isBuffer(e)?e:$(new i(e,r).toString()),u=a.length;for(s=0;s<n-t;++s)this[s+t]=a[s%u]}return this};var ee=/[^+\/0-9A-Za-z-_]/g}).call(t,n(83))},function(e,t){var n;n=function(){return this}();try{n=n||Function("return this")()||(0,eval)("this")}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t,n){"use strict";function r(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var n=e.indexOf("=");return-1===n&&(n=t),[n,n===t?0:4-n%4]}function o(e){var t=r(e),n=t[0],o=t[1];return 3*(n+o)/4-o}function i(e,t,n){return 3*(t+n)/4-n}function s(e){var t,n,o=r(e),s=o[0],a=o[1],u=new p(i(e,s,a)),f=0,c=a>0?s-4:s;for(n=0;n<c;n+=4)t=l[e.charCodeAt(n)]<<18|l[e.charCodeAt(n+1)]<<12|l[e.charCodeAt(n+2)]<<6|l[e.charCodeAt(n+3)],u[f++]=t>>16&255,u[f++]=t>>8&255,u[f++]=255&t;return 2===a&&(t=l[e.charCodeAt(n)]<<2|l[e.charCodeAt(n+1)]>>4,u[f++]=255&t),1===a&&(t=l[e.charCodeAt(n)]<<10|l[e.charCodeAt(n+1)]<<4|l[e.charCodeAt(n+2)]>>2,u[f++]=t>>8&255,u[f++]=255&t),u}function a(e){return c[e>>18&63]+c[e>>12&63]+c[e>>6&63]+c[63&e]}function u(e,t,n){for(var r,o=[],i=t;i<n;i+=3)r=(e[i]<<16&16711680)+(e[i+1]<<8&65280)+(255&e[i+2]),o.push(a(r));return o.join("")}function f(e){for(var t,n=e.length,r=n%3,o=[],i=0,s=n-r;i<s;i+=16383)o.push(u(e,i,i+16383>s?s:i+16383));return 1===r?(t=e[n-1],o.push(c[t>>2]+c[t<<4&63]+"==")):2===r&&(t=(e[n-2]<<8)+e[n-1],o.push(c[t>>10]+c[t>>4&63]+c[t<<2&63]+"=")),o.join("")}t.byteLength=o,t.toByteArray=s,t.fromByteArray=f;for(var c=[],l=[],p="undefined"!=typeof Uint8Array?Uint8Array:Array,d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",h=0,g=d.length;h<g;++h)c[h]=d[h],l[d.charCodeAt(h)]=h;l["-".charCodeAt(0)]=62,l["_".charCodeAt(0)]=63},function(e,t){/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
t.read=function(e,t,n,r,o){var i,s,a=8*o-r-1,u=(1<<a)-1,f=u>>1,c=-7,l=n?o-1:0,p=n?-1:1,d=e[t+l];for(l+=p,i=d&(1<<-c)-1,d>>=-c,c+=a;c>0;i=256*i+e[t+l],l+=p,c-=8);for(s=i&(1<<-c)-1,i>>=-c,c+=r;c>0;s=256*s+e[t+l],l+=p,c-=8);if(0===i)i=1-f;else{if(i===u)return s?NaN:1/0*(d?-1:1);s+=Math.pow(2,r),i-=f}return(d?-1:1)*s*Math.pow(2,i-r)},t.write=function(e,t,n,r,o,i){var s,a,u,f=8*i-o-1,c=(1<<f)-1,l=c>>1,p=23===o?Math.pow(2,-24)-Math.pow(2,-77):0,d=r?0:i-1,h=r?1:-1,g=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,s=c):(s=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-s))<1&&(s--,u*=2),t+=s+l>=1?p/u:p*Math.pow(2,1-l),t*u>=2&&(s++,u/=2),s+l>=c?(a=0,s=c):s+l>=1?(a=(t*u-1)*Math.pow(2,o),s+=l):(a=t*Math.pow(2,l-1)*Math.pow(2,o),s=0));o>=8;e[n+d]=255&a,d+=h,a/=256,o-=8);for(s=s<<o|a,f+=o;f>0;e[n+d]=255&s,d+=h,s/=256,f-=8);e[n+d-h]|=128*g}},function(e,t){var n={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==n.call(e)}},function(e,t,n){"use strict";var r,o,i=n(88),s=function(e){return e&&e.__esModule?e:{default:e}}(i);!function(i){if("object"===(0,s.default)(t))e.exports=i();else{r=i,void 0!==(o="function"==typeof r?r.call(t,n,t,e):r)&&(e.exports=o)}}(function(e){var t=function(e,t){return e+t&4294967295},n=function(e,n,r,o,i,s){return n=t(t(n,e),t(o,s)),t(n<<i|n>>>32-i,r)},r=function(e,t,r,o,i,s,a){return n(t&r|~t&o,e,t,i,s,a)},o=function(e,t,r,o,i,s,a){return n(t&o|r&~o,e,t,i,s,a)},i=function(e,t,r,o,i,s,a){return n(t^r^o,e,t,i,s,a)},s=function(e,t,r,o,i,s,a){return n(r^(t|~o),e,t,i,s,a)},a=function(e,n){var a=e[0],u=e[1],f=e[2],c=e[3];a=r(a,u,f,c,n[0],7,-680876936),c=r(c,a,u,f,n[1],12,-389564586),f=r(f,c,a,u,n[2],17,606105819),u=r(u,f,c,a,n[3],22,-1044525330),a=r(a,u,f,c,n[4],7,-176418897),c=r(c,a,u,f,n[5],12,1200080426),f=r(f,c,a,u,n[6],17,-1473231341),u=r(u,f,c,a,n[7],22,-45705983),a=r(a,u,f,c,n[8],7,1770035416),c=r(c,a,u,f,n[9],12,-1958414417),f=r(f,c,a,u,n[10],17,-42063),u=r(u,f,c,a,n[11],22,-1990404162),a=r(a,u,f,c,n[12],7,1804603682),c=r(c,a,u,f,n[13],12,-40341101),f=r(f,c,a,u,n[14],17,-1502002290),u=r(u,f,c,a,n[15],22,1236535329),a=o(a,u,f,c,n[1],5,-165796510),c=o(c,a,u,f,n[6],9,-1069501632),f=o(f,c,a,u,n[11],14,643717713),u=o(u,f,c,a,n[0],20,-373897302),a=o(a,u,f,c,n[5],5,-701558691),c=o(c,a,u,f,n[10],9,38016083),f=o(f,c,a,u,n[15],14,-660478335),u=o(u,f,c,a,n[4],20,-405537848),a=o(a,u,f,c,n[9],5,568446438),c=o(c,a,u,f,n[14],9,-1019803690),f=o(f,c,a,u,n[3],14,-187363961),u=o(u,f,c,a,n[8],20,1163531501),a=o(a,u,f,c,n[13],5,-1444681467),c=o(c,a,u,f,n[2],9,-51403784),f=o(f,c,a,u,n[7],14,1735328473),u=o(u,f,c,a,n[12],20,-1926607734),a=i(a,u,f,c,n[5],4,-378558),c=i(c,a,u,f,n[8],11,-2022574463),f=i(f,c,a,u,n[11],16,1839030562),u=i(u,f,c,a,n[14],23,-35309556),a=i(a,u,f,c,n[1],4,-1530992060),c=i(c,a,u,f,n[4],11,1272893353),f=i(f,c,a,u,n[7],16,-155497632),u=i(u,f,c,a,n[10],23,-1094730640),a=i(a,u,f,c,n[13],4,681279174),c=i(c,a,u,f,n[0],11,-358537222),f=i(f,c,a,u,n[3],16,-722521979),u=i(u,f,c,a,n[6],23,76029189),a=i(a,u,f,c,n[9],4,-640364487),c=i(c,a,u,f,n[12],11,-421815835),f=i(f,c,a,u,n[15],16,530742520),u=i(u,f,c,a,n[2],23,-995338651),a=s(a,u,f,c,n[0],6,-198630844),c=s(c,a,u,f,n[7],10,1126891415),f=s(f,c,a,u,n[14],15,-1416354905),u=s(u,f,c,a,n[5],21,-57434055),a=s(a,u,f,c,n[12],6,1700485571),c=s(c,a,u,f,n[3],10,-1894986606),f=s(f,c,a,u,n[10],15,-1051523),u=s(u,f,c,a,n[1],21,-2054922799),a=s(a,u,f,c,n[8],6,1873313359),c=s(c,a,u,f,n[15],10,-30611744),f=s(f,c,a,u,n[6],15,-1560198380),u=s(u,f,c,a,n[13],21,1309151649),a=s(a,u,f,c,n[4],6,-145523070),c=s(c,a,u,f,n[11],10,-1120210379),f=s(f,c,a,u,n[2],15,718787259),u=s(u,f,c,a,n[9],21,-343485551),e[0]=t(a,e[0]),e[1]=t(u,e[1]),e[2]=t(f,e[2]),e[3]=t(c,e[3])},u=function(e){var t,n=[];for(t=0;t<64;t+=4)n[t>>2]=e.charCodeAt(t)+(e.charCodeAt(t+1)<<8)+(e.charCodeAt(t+2)<<16)+(e.charCodeAt(t+3)<<24);return n},f=function(e){var t,n=[];for(t=0;t<64;t+=4)n[t>>2]=e[t]+(e[t+1]<<8)+(e[t+2]<<16)+(e[t+3]<<24);return n},c=function(e){var t,n,r,o,i,s,f=e.length,c=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=f;t+=64)a(c,u(e.substring(t-64,t)));for(e=e.substring(t-64),n=e.length,r=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],t=0;t<n;t+=1)r[t>>2]|=e.charCodeAt(t)<<(t%4<<3);if(r[t>>2]|=128<<(t%4<<3),t>55)for(a(c,r),t=0;t<16;t+=1)r[t]=0;return o=8*f,o=o.toString(16).match(/(.*?)(.{0,8})$/),i=parseInt(o[2],16),s=parseInt(o[1],16)||0,r[14]=i,r[15]=s,a(c,r),c},l=function(e){var t,n,r,o,i,s,u=e.length,c=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=u;t+=64)a(c,f(e.subarray(t-64,t)));for(e=t-64<u?e.subarray(t-64):new Uint8Array(0),n=e.length,r=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],t=0;t<n;t+=1)r[t>>2]|=e[t]<<(t%4<<3);if(r[t>>2]|=128<<(t%4<<3),t>55)for(a(c,r),t=0;t<16;t+=1)r[t]=0;return o=8*u,o=o.toString(16).match(/(.*?)(.{0,8})$/),i=parseInt(o[2],16),s=parseInt(o[1],16)||0,r[14]=i,r[15]=s,a(c,r),c},p=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"],d=function(e){var t,n="";for(t=0;t<4;t+=1)n+=p[e>>8*t+4&15]+p[e>>8*t&15];return n},h=function(e){var t;for(t=0;t<e.length;t+=1)e[t]=d(e[t]);return e.join("")},g=function(){this.reset()};return"5d41402abc4b2a76b9719d911017c592"!==function(e){return h(c(e))}("hello")&&(t=function(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}),g.prototype.append=function(e){return/[\u0080-\uFFFF]/.test(e)&&(e=unescape(encodeURIComponent(e))),this.appendBinary(e),this},g.prototype.appendBinary=function(e){this._buff+=e,this._length+=e.length;var t,n=this._buff.length;for(t=64;t<=n;t+=64)a(this._state,u(this._buff.substring(t-64,t)));return this._buff=this._buff.substr(t-64),this},g.prototype.end=function(e){var t,n,r=this._buff,o=r.length,i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<o;t+=1)i[t>>2]|=r.charCodeAt(t)<<(t%4<<3);return this._finish(i,o),n=e?this._state:h(this._state),this.reset(),n},g.prototype._finish=function(e,t){var n,r,o,i=t;if(e[i>>2]|=128<<(i%4<<3),i>55)for(a(this._state,e),i=0;i<16;i+=1)e[i]=0;n=8*this._length,n=n.toString(16).match(/(.*?)(.{0,8})$/),r=parseInt(n[2],16),o=parseInt(n[1],16)||0,e[14]=r,e[15]=o,a(this._state,e)},g.prototype.reset=function(){return this._buff="",this._length=0,this._state=[1732584193,-271733879,-1732584194,271733878],this},g.prototype.destroy=function(){delete this._state,delete this._buff,delete this._length},g.hash=function(e,t){/[\u0080-\uFFFF]/.test(e)&&(e=unescape(encodeURIComponent(e)));var n=c(e);return t?n:h(n)},g.hashBinary=function(e,t){var n=c(e);return t?n:h(n)},g.ArrayBuffer=function(){this.reset()},g.ArrayBuffer.prototype.append=function(e){var t,n=this._concatArrayBuffer(this._buff,e),r=n.length;for(this._length+=e.byteLength,t=64;t<=r;t+=64)a(this._state,f(n.subarray(t-64,t)));return this._buff=t-64<r?n.subarray(t-64):new Uint8Array(0),this},g.ArrayBuffer.prototype.end=function(e){var t,n,r=this._buff,o=r.length,i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<o;t+=1)i[t>>2]|=r[t]<<(t%4<<3);return this._finish(i,o),n=e?this._state:h(this._state),this.reset(),n},g.ArrayBuffer.prototype._finish=g.prototype._finish,g.ArrayBuffer.prototype.reset=function(){return this._buff=new Uint8Array(0),this._length=0,this._state=[1732584193,-271733879,-1732584194,271733878],this},g.ArrayBuffer.prototype.destroy=g.prototype.destroy,g.ArrayBuffer.prototype._concatArrayBuffer=function(e,t){var n=e.length,r=new Uint8Array(n+t.byteLength);return r.set(e),r.set(new Uint8Array(t),n),r},g.ArrayBuffer.hash=function(e,t){var n=l(new Uint8Array(e));return t?n:h(n)},g})},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}t.__esModule=!0;var o=n(89),i=r(o),s=n(91),a=r(s),u="function"==typeof a.default&&"symbol"==typeof i.default?function(e){return typeof e}:function(e){return e&&"function"==typeof a.default&&e.constructor===a.default&&e!==a.default.prototype?"symbol":typeof e};t.default="function"==typeof a.default&&"symbol"===u(i.default)?function(e){return void 0===e?"undefined":u(e)}:function(e){return e&&"function"==typeof a.default&&e.constructor===a.default&&e!==a.default.prototype?"symbol":void 0===e?"undefined":u(e)}},function(e,t,n){e.exports={default:n(90),__esModule:!0}},function(e,t,n){n(37),n(46),e.exports=n(32).f("iterator")},function(e,t,n){e.exports={default:n(92),__esModule:!0}},function(e,t,n){n(93),n(36),n(99),n(100),e.exports=n(2).Symbol},function(e,t,n){"use strict";var r=n(0),o=n(8),i=n(7),s=n(11),a=n(40),u=n(94).KEY,f=n(16),c=n(29),l=n(19),p=n(18),d=n(1),h=n(32),g=n(33),y=n(95),v=n(96),m=n(3),k=n(6),w=n(45),T=n(9),b=n(26),_=n(17),I=n(41),S=n(97),x=n(98),P=n(52),E=n(5),A=n(27),C=x.f,O=E.f,U=S.f,R=r.Symbol,B=r.JSON,N=B&&B.stringify,F=d("_hidden"),M=d("toPrimitive"),j={}.propertyIsEnumerable,L=c("symbol-registry"),D=c("symbols"),z=c("op-symbols"),Y=Object.prototype,K="function"==typeof R&&!!P.f,$=r.QObject,V=!$||!$.prototype||!$.prototype.findChild,G=i&&f(function(){return 7!=I(O({},"a",{get:function(){return O(this,"a",{value:7}).a}})).a})?function(e,t,n){var r=C(Y,t);r&&delete Y[t],O(e,t,n),r&&e!==Y&&O(Y,t,r)}:O,J=function(e){var t=D[e]=I(R.prototype);return t._k=e,t},Q=K&&"symbol"==typeof R.iterator?function(e){return"symbol"==typeof e}:function(e){return e instanceof R},W=function(e,t,n){return e===Y&&W(z,t,n),m(e),t=b(t,!0),m(n),o(D,t)?(n.enumerable?(o(e,F)&&e[F][t]&&(e[F][t]=!1),n=I(n,{enumerable:_(0,!1)})):(o(e,F)||O(e,F,_(1,{})),e[F][t]=!0),G(e,t,n)):O(e,t,n)},H=function(e,t){m(e);for(var n,r=y(t=T(t)),o=0,i=r.length;i>o;)W(e,n=r[o++],t[n]);return e},q=function(e,t){return void 0===t?I(e):H(I(e),t)},X=function(e){var t=j.call(this,e=b(e,!0));return!(this===Y&&o(D,e)&&!o(z,e))&&(!(t||!o(this,e)||!o(D,e)||o(this,F)&&this[F][e])||t)},Z=function(e,t){if(e=T(e),t=b(t,!0),e!==Y||!o(D,t)||o(z,t)){var n=C(e,t);return!n||!o(D,t)||o(e,F)&&e[F][t]||(n.enumerable=!0),n}},ee=function(e){for(var t,n=U(T(e)),r=[],i=0;n.length>i;)o(D,t=n[i++])||t==F||t==u||r.push(t);return r},te=function(e){for(var t,n=e===Y,r=U(n?z:T(e)),i=[],s=0;r.length>s;)!o(D,t=r[s++])||n&&!o(Y,t)||i.push(D[t]);return i};K||(R=function(){if(this instanceof R)throw TypeError("Symbol is not a constructor!");var e=p(arguments.length>0?arguments[0]:void 0),t=function(n){this===Y&&t.call(z,n),o(this,F)&&o(this[F],e)&&(this[F][e]=!1),G(this,e,_(1,n))};return i&&V&&G(Y,e,{configurable:!0,set:t}),J(e)},a(R.prototype,"toString",function(){return this._k}),x.f=Z,E.f=W,n(53).f=S.f=ee,n(34).f=X,P.f=te,i&&!n(10)&&a(Y,"propertyIsEnumerable",X,!0),h.f=function(e){return J(d(e))}),s(s.G+s.W+s.F*!K,{Symbol:R});for(var ne="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),re=0;ne.length>re;)d(ne[re++]);for(var oe=A(d.store),ie=0;oe.length>ie;)g(oe[ie++]);s(s.S+s.F*!K,"Symbol",{for:function(e){return o(L,e+="")?L[e]:L[e]=R(e)},keyFor:function(e){if(!Q(e))throw TypeError(e+" is not a symbol!");for(var t in L)if(L[t]===e)return t},useSetter:function(){V=!0},useSimple:function(){V=!1}}),s(s.S+s.F*!K,"Object",{create:q,defineProperty:W,defineProperties:H,getOwnPropertyDescriptor:Z,getOwnPropertyNames:ee,getOwnPropertySymbols:te});var se=f(function(){P.f(1)});s(s.S+s.F*se,"Object",{getOwnPropertySymbols:function(e){return P.f(w(e))}}),B&&s(s.S+s.F*(!K||f(function(){var e=R();return"[null]"!=N([e])||"{}"!=N({a:e})||"{}"!=N(Object(e))})),"JSON",{stringify:function(e){for(var t,n,r=[e],o=1;arguments.length>o;)r.push(arguments[o++]);if(n=t=r[1],(k(t)||void 0!==e)&&!Q(e))return v(t)||(t=function(e,t){if("function"==typeof n&&(t=n.call(this,e,t)),!Q(t))return t}),r[1]=t,N.apply(B,r)}}),R.prototype[M]||n(4)(R.prototype,M,R.prototype.valueOf),l(R,"Symbol"),l(Math,"Math",!0),l(r.JSON,"JSON",!0)},function(e,t,n){var r=n(18)("meta"),o=n(6),i=n(8),s=n(5).f,a=0,u=Object.isExtensible||function(){return!0},f=!n(16)(function(){return u(Object.preventExtensions({}))}),c=function(e){s(e,r,{value:{i:"O"+ ++a,w:{}}})},l=function(e,t){if(!o(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!i(e,r)){if(!u(e))return"F";if(!t)return"E";c(e)}return e[r].i},p=function(e,t){if(!i(e,r)){if(!u(e))return!0;if(!t)return!1;c(e)}return e[r].w},d=function(e){return f&&h.NEED&&u(e)&&!i(e,r)&&c(e),e},h=e.exports={KEY:r,NEED:!1,fastKey:l,getWeak:p,onFreeze:d}},function(e,t,n){var r=n(27),o=n(52),i=n(34);e.exports=function(e){var t=r(e),n=o.f;if(n)for(var s,a=n(e),u=i.f,f=0;a.length>f;)u.call(e,s=a[f++])&&t.push(s);return t}},function(e,t,n){var r=n(13);e.exports=Array.isArray||function(e){return"Array"==r(e)}},function(e,t,n){var r=n(9),o=n(53).f,i={}.toString,s="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],a=function(e){try{return o(e)}catch(e){return s.slice()}};e.exports.f=function(e){return s&&"[object Window]"==i.call(e)?a(e):o(r(e))}},function(e,t,n){var r=n(34),o=n(17),i=n(9),s=n(26),a=n(8),u=n(39),f=Object.getOwnPropertyDescriptor;t.f=n(7)?f:function(e,t){if(e=i(e),t=s(t,!0),u)try{return f(e,t)}catch(e){}if(a(e,t))return o(!r.f.call(e,t),e[t])}},function(e,t,n){n(33)("asyncIterator")},function(e,t,n){n(33)("observable")},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0});var o=n(22),i=r(o),s=n(20),a=r(s),u=(n(21).default,n(35).default),f=function(e){function t(){return window.nxt&&window.nxt.config?window.nxt.config:e.configInst?e.configInst:void 0}function n(){var e="http://"+location.host,t=a.default.getCookie("apiVersion"),n="/upload/complete";return t&&"mamcore2.3"===t&&(n="/sflud/v1/upload/complete"),e+n}function r(e){var r={TaskName:e.metadata.name,TaskGuid:e.taskId,TransType:"Upload",TransFile:[],UserInfo:{UserId:nxt.user.current.id.toString(),UserName:nxt.user.current.nickName||nxt.user.current.loginName,UserCode:nxt.user.current.userCode},ExtendeAttr:[]};return 1==t().vtubeInfo.importType&&(r.CallBackUrl=n()),e.serverInfo&&(r.ServerInfo={HostName:e.serverInfo.hostName,Port:e.serverInfo.port,Scheme:e.serverInfo.scheme,UserName:e.serverInfo.userName,Password:e.serverInfo.password,PathRoot:e.serverInfo.pathRoot}),r.ExtendeAttr=_.map(e.metadata.field,function(e){var t={ItemCode:e.fieldName,ItemName:e.alias};if(8==e.controlType)if(null!=e.value&&""!=e.value&&"[]"!=e.value)try{t.Value=JSON.parse(e.value)[0]}catch(e){t.Value=""}else t.Value="";else t.Value=e.value||"";return t}),r.ExtendeAttr.push({ItemCode:"tree",ItemName:"目录树",Value:e.tree}),_.remove(r.ExtendeAttr,{ItemCode:"cliptype"}),r.ExtendeAttr.push({ItemCode:"cliptype",ItemName:"素材类型",Value:e.entityType}),r}var o=this,s="http://127.0.0.1:8084/";this.openFileSelector=function(e,n){$.ajax({url:s+"request/getuploadfiles?user_token=&filetype=all",success:function(n){var r=_.isString(n)?JSON.parse(n):n;if(0!==r.FileCount){var o=[];_.forEach(r.FileList,function(e){var n="."+a.default.getExtension(e.FilePath);o.push({entityType:a.default.getTypeByExt(n,t()).code,fileName:a.default.getFullFileName(e.FilePath),metadata:{name:a.default.getFileName(e.FilePath),ext:n},status:"added",progress:0,file:e})}),e(o)}},error:n||function(e){console.error(e);var n=t().vtubeDownloadPath||t().server+"/assets/Sobey_vRocket_v2.0_Setup.exe";a.default.prompt(l("upload.clientInstallTip",'<p>打开客户端失败</p><p>请确定已经正常开启客户端或重启客户端再重试。</p><p>如没有安装，请点击下面地址。</p><p><a href="${path}"><i class="icon iconfont icon-iconfontxiazai" style="margin-right:5px"></i>点击客户端下载</a></p>',{path:n}))}})},this.createTask=function(e,t){var n=[];switch(t.taskType){case 1:_.isArray(e)||(e=[e]),_.forEach(e,function(e){e.file?(e.files=[{file:e.file,fileName:e.fileName,fileSize:e.file.FileSize}],delete e.file,n.push(e)):e.files&&e.files.length>0&&(e.files=_.map(e.files,function(e){return{file:e.file,fileName:e.fileName,fileSize:e.fileSize}}),n.push(e))});break;case 2:case 3:n.push(e)}_.forEach(n,function(e){e.taskType=t.taskType,e.transferType=t.transferType,e.targetFolder=t.targetFolder,e.relationContentType=t.relationContentType,e.relationContentId=t.relationContentId,o.addTask(e)})},this.addTask=function(n){var o=a.default.getCookie("apiVersion"),f="/upload/multipart/init?token="+e.loginToken;o&&"mamcore2.3"===o&&(f="/sflud/v1/upload/multipart/init?token="+e.loginToken),u.post(t().server+f,n).then(function(e){function o(n){if(t().isHandleHttpPath&&e&&e.ossClientInfo){var r=window.location.hostname,o=n.absolutePath.split("@");if(o.length>1){var i=o[1].indexOf(":")>-1?":":"/";n.absolutePath=o[0]+"@"+r+o[1].substr(o[1].indexOf(i))}}return e&&e.ossClientInfo?n.absolutePath:n.relativePath}e=e.data;var u=r(e);switch(u.ExtendData=e.userToken,n.taskType){case 1:u.TransFile.push({SourceFile:n.files[0].file.FilePath,DestFile:o(e.files[0])});break;case 2:case 3:_.forEach(e.files,function(e){u.TransFile.push({SourceFile:e.fileName,DestFile:o(e)})})}$.ajax({url:s+"request/addtask?user_token=",type:"POST",contentType:"application/json",data:(0,i.default)(u),success:function(e){"string"==typeof e&&(e=JSON.parse(e)),1==e.Result?a.default.msgOk(l("upload.addTaskOk","添加任务成功")):a.default.prompt(l("upload.addTaskError","添加任务失败：")+e.Msg),console.info(e)},error:function(e){console.info(e),a.default.prompt(l("upload.addTaskError","添加任务失败"))}})},function(e){a.default.prompt(l("upload.uploadError","上传失败：")+e.data.desc)})}};t.default=f},,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";var r=n(81),o=n(101),i=n(120),s={init:function(e){s.web=new r.default(e),s.vtube=new o.default(e),s.thirdpartSupport=new i.default(e),window.OSS&&(s.web.OSS=window.OSS)}};window.mamUpload=s},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(e){this.handlePicPkgTaskMetadataName=function(e){_.forEach(e.files,function(t,n){0!==n||null!=e.metadata.name&&""!==e.metadata.name||(e.metadata.name=t.metadata.name)})}};t.default=r}]);
//# sourceMappingURL=mam-upload-pure.min.js.map