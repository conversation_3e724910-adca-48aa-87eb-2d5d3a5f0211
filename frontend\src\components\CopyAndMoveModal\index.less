.copy-move-modal{
  .newfolderbtn{
    margin-bottom: 20px;
  }
  .rename{
    display: flex;
    align-items: center;
    input{
      margin-left: 5px;
    }
    .ant-row{
      width: 100%;
      .ant-col{
        &.ant-form-item-label{
          max-width: 50px;
        }
        &.ant-form-item-control{
          max-width: calc(100% - 50px);
        }
        white-space: nowrap;
      }
    }
  }
  .ant-tree.ant-tree-directory,.ant-tree-node-content-wrapper{
    display: flex;
  }
  .movetreebox{
    max-height: 450px;
    width: 350px;
    overflow: auto;
    text-align: center;
  }
}
.moveProcessModal{
  .ant-modal-body{
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    .ant-progress{
     width: 340px;
    }
    .result{
      width: 300px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      margin-top: 10px;
    }
  }
}
.copyOrMoveModal{
  top:400px !important;
}
