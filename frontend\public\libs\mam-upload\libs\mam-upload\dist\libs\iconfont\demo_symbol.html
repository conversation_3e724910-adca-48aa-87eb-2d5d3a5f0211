
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>IconFont</title>
    <link rel="stylesheet" href="demo.css">
    <script src="iconfont.js"></script>

    <style type="text/css">
        .icon {
          /* 通过设置 font-size 来改变图标大小 */
          width: 1em; height: 1em;
          /* 图标和文字相邻时，垂直对齐 */
          vertical-align: -0.15em;
          /* 通过设置 color 来改变 SVG 的颜色/fill */
          fill: currentColor;
          /* path 和 stroke 溢出 viewBox 部分在 IE 下会显示
             normalize.css 中也包含这行 */
          overflow: hidden;
        }

    </style>
</head>
<body>
    <div class="main markdown">
        <h1>IconFont 图标</h1>
        <ul class="icon_lists clear">
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-youjian"></use>
                    </svg>
                    <div class="name">邮件</div>
                    <div class="fontclass">#icon-youjian</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-shuaxin"></use>
                    </svg>
                    <div class="name">刷新</div>
                    <div class="fontclass">#icon-shuaxin</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-sousuo"></use>
                    </svg>
                    <div class="name">搜索</div>
                    <div class="fontclass">#icon-sousuo</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-iconxiaotu"></use>
                    </svg>
                    <div class="name">格子</div>
                    <div class="fontclass">#icon-iconxiaotu</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-left"></use>
                    </svg>
                    <div class="name">left</div>
                    <div class="fontclass">#icon-left</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-iconfontxiazai"></use>
                    </svg>
                    <div class="name">下载</div>
                    <div class="fontclass">#icon-iconfontxiazai</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-shangchuan"></use>
                    </svg>
                    <div class="name">上传</div>
                    <div class="fontclass">#icon-shangchuan</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-icon"></use>
                    </svg>
                    <div class="name">方块</div>
                    <div class="fontclass">#icon-icon</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-mingpian"></use>
                    </svg>
                    <div class="name">名片</div>
                    <div class="fontclass">#icon-mingpian</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-star-outline"></use>
                    </svg>
                    <div class="name">星星</div>
                    <div class="fontclass">#icon-star-outline</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-xingxing"></use>
                    </svg>
                    <div class="name">星星</div>
                    <div class="fontclass">#icon-xingxing</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-gou"></use>
                    </svg>
                    <div class="name">勾</div>
                    <div class="fontclass">#icon-gou</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-right1"></use>
                    </svg>
                    <div class="name">right</div>
                    <div class="fontclass">#icon-right1</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-yonghu1"></use>
                    </svg>
                    <div class="name">用户</div>
                    <div class="fontclass">#icon-yonghu1</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-iconfont89"></use>
                    </svg>
                    <div class="name">文件夹</div>
                    <div class="fontclass">#icon-iconfont89</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-iconfontcolor100"></use>
                    </svg>
                    <div class="name">警告</div>
                    <div class="fontclass">#icon-iconfontcolor100</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-share"></use>
                    </svg>
                    <div class="name">分享</div>
                    <div class="fontclass">#icon-share</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-dianhua"></use>
                    </svg>
                    <div class="name">电话</div>
                    <div class="fontclass">#icon-dianhua</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-icon13"></use>
                    </svg>
                    <div class="name">删除</div>
                    <div class="fontclass">#icon-icon13</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-bofang1"></use>
                    </svg>
                    <div class="name">播放</div>
                    <div class="fontclass">#icon-bofang1</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-fenxiang"></use>
                    </svg>
                    <div class="name">分享</div>
                    <div class="fontclass">#icon-fenxiang</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-xinxi2"></use>
                    </svg>
                    <div class="name">信息</div>
                    <div class="fontclass">#icon-xinxi2</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-list"></use>
                    </svg>
                    <div class="name">列表</div>
                    <div class="fontclass">#icon-list</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-gou1"></use>
                    </svg>
                    <div class="name">勾</div>
                    <div class="fontclass">#icon-gou1</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-tianjia"></use>
                    </svg>
                    <div class="name">添加</div>
                    <div class="fontclass">#icon-tianjia</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-list1"></use>
                    </svg>
                    <div class="name">列表</div>
                    <div class="fontclass">#icon-list1</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-intelligent"></use>
                    </svg>
                    <div class="name">智能馆</div>
                    <div class="fontclass">#icon-intelligent</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-icon4"></use>
                    </svg>
                    <div class="name">文件</div>
                    <div class="fontclass">#icon-icon4</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-xingming"></use>
                    </svg>
                    <div class="name">姓名</div>
                    <div class="fontclass">#icon-xingming</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-ie"></use>
                    </svg>
                    <div class="name">IE</div>
                    <div class="fontclass">#icon-ie</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-bianji"></use>
                    </svg>
                    <div class="name">编辑</div>
                    <div class="fontclass">#icon-bianji</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-tianjiazhandian"></use>
                    </svg>
                    <div class="name">添加站点</div>
                    <div class="fontclass">#icon-tianjiazhandian</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-dian"></use>
                    </svg>
                    <div class="name">原点</div>
                    <div class="fontclass">#icon-dian</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-wenjianjia"></use>
                    </svg>
                    <div class="name">文件夹</div>
                    <div class="fontclass">#icon-wenjianjia</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-jiantou-copy"></use>
                    </svg>
                    <div class="name">箭头</div>
                    <div class="fontclass">#icon-jiantou-copy</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-mima"></use>
                    </svg>
                    <div class="name">密码</div>
                    <div class="fontclass">#icon-mima</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-youjiankuai"></use>
                    </svg>
                    <div class="name">邮件_块</div>
                    <div class="fontclass">#icon-youjiankuai</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-folder"></use>
                    </svg>
                    <div class="name">文件夹</div>
                    <div class="fontclass">#icon-folder</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-dakaixinkuai"></use>
                    </svg>
                    <div class="name">打开信_块</div>
                    <div class="fontclass">#icon-dakaixinkuai</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-wenjian"></use>
                    </svg>
                    <div class="name">文件</div>
                    <div class="fontclass">#icon-wenjian</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-bumen"></use>
                    </svg>
                    <div class="name">部门</div>
                    <div class="fontclass">#icon-bumen</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-windows"></use>
                    </svg>
                    <div class="name">windows</div>
                    <div class="fontclass">#icon-windows</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-yonghu2"></use>
                    </svg>
                    <div class="name">用户</div>
                    <div class="fontclass">#icon-yonghu2</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-jinhuodan"></use>
                    </svg>
                    <div class="name">列表</div>
                    <div class="fontclass">#icon-jinhuodan</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-yidong"></use>
                    </svg>
                    <div class="name">移动</div>
                    <div class="fontclass">#icon-yidong</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-zhandian"></use>
                    </svg>
                    <div class="name">站点</div>
                    <div class="fontclass">#icon-zhandian</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-icon-down-copy-copy"></use>
                    </svg>
                    <div class="name">三角形</div>
                    <div class="fontclass">#icon-icon-down-copy-copy</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-nopic"></use>
                    </svg>
                    <div class="name">三横</div>
                    <div class="fontclass">#icon-nopic</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-huishouzhan"></use>
                    </svg>
                    <div class="name">回收站</div>
                    <div class="fontclass">#icon-huishouzhan</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-zutu"></use>
                    </svg>
                    <div class="name">组图</div>
                    <div class="fontclass">#icon-zutu</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-sql"></use>
                    </svg>
                    <div class="name">数据库</div>
                    <div class="fontclass">#icon-sql</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-riqi"></use>
                    </svg>
                    <div class="name">日期</div>
                    <div class="fontclass">#icon-riqi</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-yhzg"></use>
                    </svg>
                    <div class="name">用户组管理</div>
                    <div class="fontclass">#icon-yhzg</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-jiantou"></use>
                    </svg>
                    <div class="name">箭头</div>
                    <div class="fontclass">#icon-jiantou</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-qiapian"></use>
                    </svg>
                    <div class="name">卡片</div>
                    <div class="fontclass">#icon-qiapian</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-yuyuefuwu-zhandian"></use>
                    </svg>
                    <div class="name">预约服务-站点</div>
                    <div class="fontclass">#icon-yuyuefuwu-zhandian</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-fenxiang1"></use>
                    </svg>
                    <div class="name">分享</div>
                    <div class="fontclass">#icon-fenxiang1</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-xiaoxi"></use>
                    </svg>
                    <div class="name"> 消息</div>
                    <div class="fontclass">#icon-xiaoxi</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-xihuan"></use>
                    </svg>
                    <div class="name">喜欢2</div>
                    <div class="fontclass">#icon-xihuan</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-xihuan1"></use>
                    </svg>
                    <div class="name">喜欢</div>
                    <div class="fontclass">#icon-xihuan1</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-zan"></use>
                    </svg>
                    <div class="name">赞2</div>
                    <div class="fontclass">#icon-zan</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-zan1"></use>
                    </svg>
                    <div class="name">赞</div>
                    <div class="fontclass">#icon-zan1</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-bofang"></use>
                    </svg>
                    <div class="name">播放2</div>
                    <div class="fontclass">#icon-bofang</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-zhekou"></use>
                    </svg>
                    <div class="name"> 折扣</div>
                    <div class="fontclass">#icon-zhekou</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-shaixuan"></use>
                    </svg>
                    <div class="name"> 筛选</div>
                    <div class="fontclass">#icon-shaixuan</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-pinglun"></use>
                    </svg>
                    <div class="name">评论</div>
                    <div class="fontclass">#icon-pinglun</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-saoyisao"></use>
                    </svg>
                    <div class="name">扫一扫</div>
                    <div class="fontclass">#icon-saoyisao</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-shezhi"></use>
                    </svg>
                    <div class="name">设置</div>
                    <div class="fontclass">#icon-shezhi</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-tuichu"></use>
                    </svg>
                    <div class="name">退出</div>
                    <div class="fontclass">#icon-tuichu</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-gengduo"></use>
                    </svg>
                    <div class="name">更多</div>
                    <div class="fontclass">#icon-gengduo</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-ic_view_module_px"></use>
                    </svg>
                    <div class="name">查看模块</div>
                    <div class="fontclass">#icon-ic_view_module_px</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-yonghu"></use>
                    </svg>
                    <div class="name">用户</div>
                    <div class="fontclass">#icon-yonghu</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-cha"></use>
                    </svg>
                    <div class="name">叉</div>
                    <div class="fontclass">#icon-cha</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-xiugai"></use>
                    </svg>
                    <div class="name">修改</div>
                    <div class="fontclass">#icon-xiugai</div>
                </li>
            
        </ul>


        <h2 id="symbol-">symbol引用</h2>
        <hr>

        <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
        这种用法其实是做了一个svg的集合，与另外两种相比具有如下特点：</p>
        <ul>
          <li>支持多色图标了，不再受单色限制。</li>
          <li>通过一些技巧，支持像字体那样，通过<code>font-size</code>,<code>color</code>来调整样式。</li>
          <li>兼容性较差，支持 ie9+,及现代浏览器。</li>
          <li>浏览器渲染svg的性能一般，还不如png。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-symbol-">第一步：引入项目下面生成的symbol代码：</h3>
        <pre><code class="lang-js hljs javascript"><span class="hljs-comment">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;</span></code></pre>
        <h3 id="-css-">第二步：加入通用css代码（引入一次就行）：</h3>
        <pre><code class="lang-js hljs javascript">&lt;style type=<span class="hljs-string">"text/css"</span>&gt;
.icon {
   width: <span class="hljs-number">1</span>em; height: <span class="hljs-number">1</span>em;
   vertical-align: <span class="hljs-number">-0.15</span>em;
   fill: currentColor;
   overflow: hidden;
}
&lt;<span class="hljs-regexp">/style&gt;</span></code></pre>
        <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
        <pre><code class="lang-js hljs javascript">&lt;svg <span class="hljs-class"><span class="hljs-keyword">class</span></span>=<span class="hljs-string">"icon"</span> aria-hidden=<span class="hljs-string">"true"</span>&gt;<span class="xml"><span class="hljs-tag">
  &lt;<span class="hljs-name">use</span> <span class="hljs-attr">xlink:href</span>=<span class="hljs-string">"#icon-xxx"</span>&gt;</span><span class="hljs-tag">&lt;/<span class="hljs-name">use</span>&gt;</span>
</span>&lt;<span class="hljs-regexp">/svg&gt;
        </span></code></pre>
    </div>
</body>
</html>
