.entity-preview {
  .video-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 565px;
    // min-height: 300px;
    padding: 5px;
    border: 1px solid #afafaf;
    > #videoElement {
      // width: calc(100% - 350px);
      // height: 600px;
      width: 100% !important;
      height: 600px !important;
    }
    > #previewVideo {
      width: 100% !important;
      padding-top: 56.25% !important;
    }
  }
  .ant-btn {
    margin-bottom: 15px;
  }

  .entity-img > img {
    max-width: calc(100% - 350px);
    // height: auto;
    // width: 100%;
    max-height: 600px;
  }
}
