
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>IconFont</title>
    <link rel="stylesheet" href="demo.css">

    <style type="text/css">

        @font-face {font-family: "iconfont";
          src: url('iconfont.eot'); /* IE9*/
          src: url('iconfont.eot#iefix') format('embedded-opentype'), /* IE6-IE8 */
          url('iconfont.woff') format('woff'), /* chrome, firefox */
          url('iconfont.ttf') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
          url('iconfont.svg#iconfont') format('svg'); /* iOS 4.1- */
        }

        .iconfont {
          font-family:"iconfont" !important;
          font-size:16px;
          font-style:normal;
          -webkit-font-smoothing: antialiased;
          -webkit-text-stroke-width: 0.2px;
          -moz-osx-font-smoothing: grayscale;
        }

    </style>
</head>
<body>
    <div class="main markdown">
        <h1>IconFont 图标</h1>
        <ul class="icon_lists clear">
            
                <li>
                <i class="icon iconfont">&#xe607;</i>
                    <div class="name">邮件</div>
                    <div class="code">&amp;#xe607;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe600;</i>
                    <div class="name">刷新</div>
                    <div class="code">&amp;#xe600;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe603;</i>
                    <div class="name">搜索</div>
                    <div class="code">&amp;#xe603;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe618;</i>
                    <div class="name">格子</div>
                    <div class="code">&amp;#xe618;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe62d;</i>
                    <div class="name">left</div>
                    <div class="code">&amp;#xe62d;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe60d;</i>
                    <div class="name">下载</div>
                    <div class="code">&amp;#xe60d;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe636;</i>
                    <div class="name">上传</div>
                    <div class="code">&amp;#xe636;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe601;</i>
                    <div class="name">方块</div>
                    <div class="code">&amp;#xe601;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe62f;</i>
                    <div class="name">名片</div>
                    <div class="code">&amp;#xe62f;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe634;</i>
                    <div class="name">星星</div>
                    <div class="code">&amp;#xe634;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe659;</i>
                    <div class="name">星星</div>
                    <div class="code">&amp;#xe659;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe707;</i>
                    <div class="name">勾</div>
                    <div class="code">&amp;#xe707;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe614;</i>
                    <div class="name">right</div>
                    <div class="code">&amp;#xe614;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe628;</i>
                    <div class="name">用户</div>
                    <div class="code">&amp;#xe628;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe752;</i>
                    <div class="name">文件夹</div>
                    <div class="code">&amp;#xe752;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe789;</i>
                    <div class="name">警告</div>
                    <div class="code">&amp;#xe789;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe616;</i>
                    <div class="name">分享</div>
                    <div class="code">&amp;#xe616;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe609;</i>
                    <div class="name">电话</div>
                    <div class="code">&amp;#xe609;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe627;</i>
                    <div class="name">删除</div>
                    <div class="code">&amp;#xe627;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe606;</i>
                    <div class="name">播放</div>
                    <div class="code">&amp;#xe606;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe617;</i>
                    <div class="name">分享</div>
                    <div class="code">&amp;#xe617;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe633;</i>
                    <div class="name">信息</div>
                    <div class="code">&amp;#xe633;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe65d;</i>
                    <div class="name">列表</div>
                    <div class="code">&amp;#xe65d;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe641;</i>
                    <div class="name">勾</div>
                    <div class="code">&amp;#xe641;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe613;</i>
                    <div class="name">添加</div>
                    <div class="code">&amp;#xe613;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe60f;</i>
                    <div class="name">列表</div>
                    <div class="code">&amp;#xe60f;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe639;</i>
                    <div class="name">智能馆</div>
                    <div class="code">&amp;#xe639;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe605;</i>
                    <div class="name">文件</div>
                    <div class="code">&amp;#xe605;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe638;</i>
                    <div class="name">姓名</div>
                    <div class="code">&amp;#xe638;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe751;</i>
                    <div class="name">IE</div>
                    <div class="code">&amp;#xe751;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe651;</i>
                    <div class="name">编辑</div>
                    <div class="code">&amp;#xe651;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe60e;</i>
                    <div class="name">添加站点</div>
                    <div class="code">&amp;#xe60e;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe669;</i>
                    <div class="name">原点</div>
                    <div class="code">&amp;#xe669;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe632;</i>
                    <div class="name">文件夹</div>
                    <div class="code">&amp;#xe632;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe655;</i>
                    <div class="name">箭头</div>
                    <div class="code">&amp;#xe655;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe654;</i>
                    <div class="name">密码</div>
                    <div class="code">&amp;#xe654;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe650;</i>
                    <div class="name">邮件_块</div>
                    <div class="code">&amp;#xe650;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe670;</i>
                    <div class="name">文件夹</div>
                    <div class="code">&amp;#xe670;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe6d2;</i>
                    <div class="name">打开信_块</div>
                    <div class="code">&amp;#xe6d2;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe686;</i>
                    <div class="name">文件</div>
                    <div class="code">&amp;#xe686;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe6ce;</i>
                    <div class="name">部门</div>
                    <div class="code">&amp;#xe6ce;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe6e1;</i>
                    <div class="name">windows</div>
                    <div class="code">&amp;#xe6e1;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe61d;</i>
                    <div class="name">用户</div>
                    <div class="code">&amp;#xe61d;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe608;</i>
                    <div class="name">列表</div>
                    <div class="code">&amp;#xe608;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe615;</i>
                    <div class="name">移动</div>
                    <div class="code">&amp;#xe615;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe60b;</i>
                    <div class="name">站点</div>
                    <div class="code">&amp;#xe60b;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe604;</i>
                    <div class="name">三角形</div>
                    <div class="code">&amp;#xe604;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe602;</i>
                    <div class="name">三横</div>
                    <div class="code">&amp;#xe602;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe649;</i>
                    <div class="name">回收站</div>
                    <div class="code">&amp;#xe649;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe662;</i>
                    <div class="name">组图</div>
                    <div class="code">&amp;#xe662;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe687;</i>
                    <div class="name">数据库</div>
                    <div class="code">&amp;#xe687;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe60a;</i>
                    <div class="name">日期</div>
                    <div class="code">&amp;#xe60a;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe64e;</i>
                    <div class="name">用户组管理</div>
                    <div class="code">&amp;#xe64e;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe610;</i>
                    <div class="name">箭头</div>
                    <div class="code">&amp;#xe610;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe60c;</i>
                    <div class="name">卡片</div>
                    <div class="code">&amp;#xe60c;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe67c;</i>
                    <div class="name">预约服务-站点</div>
                    <div class="code">&amp;#xe67c;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe86e;</i>
                    <div class="name">分享</div>
                    <div class="code">&amp;#xe86e;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe86f;</i>
                    <div class="name"> 消息</div>
                    <div class="code">&amp;#xe86f;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe870;</i>
                    <div class="name">喜欢2</div>
                    <div class="code">&amp;#xe870;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe871;</i>
                    <div class="name">喜欢</div>
                    <div class="code">&amp;#xe871;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe872;</i>
                    <div class="name">赞2</div>
                    <div class="code">&amp;#xe872;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe873;</i>
                    <div class="name">赞</div>
                    <div class="code">&amp;#xe873;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe87c;</i>
                    <div class="name">播放2</div>
                    <div class="code">&amp;#xe87c;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe881;</i>
                    <div class="name"> 折扣</div>
                    <div class="code">&amp;#xe881;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe888;</i>
                    <div class="name"> 筛选</div>
                    <div class="code">&amp;#xe888;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe891;</i>
                    <div class="name">评论</div>
                    <div class="code">&amp;#xe891;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe892;</i>
                    <div class="name">扫一扫</div>
                    <div class="code">&amp;#xe892;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe893;</i>
                    <div class="name">设置</div>
                    <div class="code">&amp;#xe893;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe894;</i>
                    <div class="name">退出</div>
                    <div class="code">&amp;#xe894;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe8a6;</i>
                    <div class="name">更多</div>
                    <div class="code">&amp;#xe8a6;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe906;</i>
                    <div class="name">查看模块</div>
                    <div class="code">&amp;#xe906;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe684;</i>
                    <div class="name">用户</div>
                    <div class="code">&amp;#xe684;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe653;</i>
                    <div class="name">叉</div>
                    <div class="code">&amp;#xe653;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe8cf;</i>
                    <div class="name">修改</div>
                    <div class="code">&amp;#xe8cf;</div>
                </li>
            
        </ul>
        <h2 id="unicode-">unicode引用</h2>
        <hr>

        <p>unicode是字体在网页端最原始的应用方式，特点是：</p>
        <ul>
        <li>兼容性最好，支持ie6+，及所有现代浏览器。</li>
        <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
        <li>但是因为是字体，所以不支持多色。只能使用平台里单色的图标，就算项目里有多色图标也会自动去色。</li>
        </ul>
        <blockquote>
        <p>注意：新版iconfont支持多色图标，这些多色图标在unicode模式下将不能使用，如果有需求建议使用symbol的引用方式</p>
        </blockquote>
        <p>unicode使用步骤如下：</p>
        <h3 id="-font-face">第一步：拷贝项目下面生成的font-face</h3>
        <pre><code class="lang-js hljs javascript">@font-face {
  font-family: <span class="hljs-string">'iconfont'</span>;
  src: url(<span class="hljs-string">'iconfont.eot'</span>);
  src: url(<span class="hljs-string">'iconfont.eot?#iefix'</span>) format(<span class="hljs-string">'embedded-opentype'</span>),
  url(<span class="hljs-string">'iconfont.woff'</span>) format(<span class="hljs-string">'woff'</span>),
  url(<span class="hljs-string">'iconfont.ttf'</span>) format(<span class="hljs-string">'truetype'</span>),
  url(<span class="hljs-string">'iconfont.svg#iconfont'</span>) format(<span class="hljs-string">'svg'</span>);
}
</code></pre>
        <h3 id="-iconfont-">第二步：定义使用iconfont的样式</h3>
        <pre><code class="lang-js hljs javascript">.iconfont{
  font-family:<span class="hljs-string">"iconfont"</span> !important;
  font-size:<span class="hljs-number">16</span>px;font-style:normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: <span class="hljs-number">0.2</span>px;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
        <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
        <pre><code class="lang-js hljs javascript">&lt;i <span class="hljs-class"><span class="hljs-keyword">class</span></span>=<span class="hljs-string">"iconfont"</span>&gt;&amp;#x33;<span class="xml"><span class="hljs-tag">&lt;/<span class="hljs-name">i</span>&gt;</span></span></code></pre>

        <blockquote>
        <p>"iconfont"是你项目下的font-family。可以通过编辑项目查看，默认是"iconfont"。</p>
        </blockquote>
    </div>


</body>
</html>
