import React from 'react';
import { Input, Button } from 'antd';
import './index.less';
interface BasicProps {
    title: string;
    placeholder?: string;
    mustinput?: boolean;
    value?:string;
    // footers:Array<any>
}

const InputItem: React.FC<BasicProps> = (props) => {
    const { title, placeholder, mustinput, value} = props;
    return (
        <div className='basicinformation_item'>
            <div className='input_title'>
                {
                    mustinput?(<span>*</span>):('')
                }
                {title}：
            </div>
            <div>
                <Input placeholder={placeholder} className='input_box' value={value} autoComplete={'off'}/>
            </div>
        </div>
    );
};

export default InputItem;
