.publish_management {
  padding: 0 0 0 15px;
  height: 100%;
  .body {
    .ant-checkbox-group {
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
    }
    height: calc(100% - 118px);
    display: flex;
    flex-direction: column;
    .searchBox {
      .type-width{
        width: 30%;
      }
      .keyword-width{
        width: 70%;
      }
      .ant-form {
        .ant-col {
          margin-right: 15px;
        }
      }
    }
    .operatBox {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 10px 20px 10px 0;
      .left_ {
        display: flex;
        justify-content: center;
        align-items: center;
        > div {
          height: 25px;
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 0 10px;
          position: relative;
          color: #525252;
          cursor: pointer;
          &:hover {
            color: var(--primary-color);
          }
          &[disabled] {
            color: rgba(0, 0, 0, 0.25);
          }
          > span:last-child {
            margin-left: 8px;
          }
          &.disabled {
            color: rgba(0, 0, 0, 0.25);
          }
          &:not(:last-child)::after {
            content: '';
            display: inline-block;
            width: 1px;
            height: 16px;
            background: #d8d8d8;
            right: 0;
            top: 4px;
            position: absolute;
          }
        }
      }
      .right_{
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        .mode_switch {
          margin-left: 15px;
          cursor: pointer;
          transition: all .2s ease 0s;
          color: #919596;

          .active {
            color: var(--primary-color);
          }
        }
      }
    }
    .list {
      height: calc(100% - 120px);
      overflow-y: auto;
    }
    .pagination {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
.pc_show {
  display: flex !important;
}

