import React, { FC, useEffect, useState } from 'react';
import { IBasicItemProps } from './basicMetadata';
import { Form, Select, Tag } from 'antd';
import {DoubleRightOutlined, CloseCircleOutlined} from '@ant-design/icons';
import { getValue } from './basicMetadata';
import { getVideoTime, searchKeywords } from '@/utils';

const BasicTag: FC<IBasicItemProps> = props => {
  const [showMore,setShowMore]=useState<any>({
    hasMore:false,
    row:3, //默认展示前三行
    rowHeight:32,
    ready:true,
    expand:false
  });
  useEffect(()=>{
    // window.addEventListener('resize',function (){
    //   //重新计算tab栏
    //   setShowMore({
    //     ...showMore,
    //     hasMore:true,
    //     ready:true
    //   })
    // });
    if(props.item.value){
      const tagContainer:any = document.getElementsByClassName(`tagContainer_${props.item.fieldName}`)[0];
      if(!showMore.ready || !tagContainer?.lastChild) return;
      if(tagContainer.lastChild.offsetTop > showMore.row*showMore.rowHeight){
        setShowMore({
          ...showMore,
          hasMore:true,
          ready:false
        })
      }else{
        setShowMore({
          ...showMore,
          hasMore:false,
          ready:false
        })
      }
    }
  },[props.item.value,showMore.ready])
  return (
    <Form.Item
      label={props.item.alias}
      name={props.item.fieldName}
      rules={[
        {
          required:
            props.item.isReadOnly || !props.edit
              ? false
              : props.item.isMustInput,
        },
      ]}
    >
      {props.item.isReadOnly || !props.edit ? (
        <div>
          <div 
            className={`tagContainer_${props.item.fieldName}`}
            style={{
            lineHeight: '32px',
            wordBreak: 'break-all',
            whiteSpace: 'break-spaces',
            height:(showMore.hasMore && !showMore.expand)?showMore.row*showMore.rowHeight:'unset',
            overflow:(showMore.hasMore && !showMore.expand)?'hidden':'unset',
          }}>
            {props.item.value
              ? getValue(
                  props.item.value as string,
                ).map((item: string, index: number) => {
                  const temp = searchKeywords(item,'basic');
                  const obj = getVideoTime(item,props.voiceList);
                  return <Tag key={index} onClick={()=>props.jumpVideoTime?.(obj._in)} className={typeof(obj._in)==='undefined'?'':'jumpTag'}>
                          {temp.index>-1?
                          <span>
                            {temp.beforeStr}
                            <span className="key-search-value tag_highlight">{temp.word}</span>
                            {temp.afterStr}
                          </span>
                          :<span>{item}</span>}</Tag>
                  } 
                )
              : ''}
          </div>
          {
            showMore.hasMore && 
            <div
              style={{
                display :'flex',
                alignItems:'center',
                justifyContent:'center',
                // color:'var(--primary-color)',
                cursor:'pointer'
              }}
              onClick={()=>setShowMore((pre:any)=>({
                ...pre,
                expand:!pre.expand,
              }))}
            >
               {/* {showMore.expand?'收起':'更多'}  */}
               {showMore.hasMore}
               <DoubleRightOutlined style={{transform:showMore.expand?'rotate(270deg)':'rotate(90deg)'}}/>
            </div>
          }
        </div>
      ) : (
        <Select mode="tags" removeIcon={<CloseCircleOutlined />}></Select>
      )}
    </Form.Item>
  );
};

export default BasicTag;
