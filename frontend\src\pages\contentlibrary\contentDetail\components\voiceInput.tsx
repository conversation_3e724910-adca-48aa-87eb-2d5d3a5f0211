import React, {
    FC,
    useEffect,
    useRef,
    useState,
    KeyboardEvent,
    FocusEvent,
} from 'react';
import { Input, Button, message, Timeline, Anchor, Select, Dropdown } from 'antd';
import { useParams, useDispatch, useSelector, useHistory, useIntl } from 'umi';
import { UpOutlined, CheckCircleOutlined, DownOutlined } from '@ant-design/icons';
import globalParams from '@/permission/globalParams';
import SmartService from '@/service/smartService';
import { IconFont } from '@/components';
const { Option } = Select;

interface IData {
    detail: any;
    setCurrentTime: any;
    currentFrame: any;
    voiceCheck: any;
    currentLanguage: any;
    getQueryVoice: any;
    searchVoiceWord: string;
    Timelineindex: number;
    contentDetail: any;
    player: any;
    contentId: any;
    voice: any;
    shareFlag_: any;
    fontSize: any
    translateFlag: any;
    searchIndex : any,
    searchCurrent: any;
    setFontSize: (value) => void;
    setVoiceCheck: (value) => void;
    setTranslateFlag: (value) => void;
    setSearchVoiceWord: (value) => void;
    setCurrentLanguage: (value) => void;
    setSearchCurrent: (value) => void;
    setSearchIndex: (value) => void;
}

const voiceInput: FC<IData> = props => {
    const {
        detail,
        setCurrentTime,
        currentFrame,
        voice,
        voiceCheck,
        getQueryVoice,
        contentId,
        Timelineindex,
        player,
        setVoiceCheck,
        setFontSize,
        fontSize,
        contentDetail,
        searchIndex,
        shareFlag_,
        translateFlag,
        searchCurrent,
        setTranslateFlag,
        setSearchVoiceWord,
        setCurrentLanguage,
        setSearchCurrent,
        setSearchIndex
    } = props;
    const intl = useIntl()
    const { modules, permissions, rmanGlobalParameter, rmanGlobalText } = useSelector<
        { permission: any },
        IPermission
    >(({ permission }) => permission);
    const searchVoive = (
        e: KeyboardEvent<HTMLInputElement> | FocusEvent<HTMLInputElement> | any,
    ) => {
        e.preventDefault();
        let name = e.target.value;
        let Index: number[] = [];
        voice.forEach((item, index) => {
            if (item.text?.indexOf(name) > -1) {
                Index.push(index);
            }
        });
        setSearchCurrent(0);
        setSearchIndex(Index);
        setSearchVoiceWord(name);
    };
    const languageChange = (e: any) => {
        setCurrentLanguage(e);
    }
    const items: MenuProps['items'] = [
        {
            key: '1',
            label: (
                <a rel="noopener noreferrer" onClick={() => voiceFileDownload('txt')}>
                    {intl.formatMessage({ id: '下载txt格式' })}
                </a>
            ),
        },
        {
            key: '2',
            label: (
                <a rel="noopener noreferrer" onClick={() => voiceFileDownload('srt')}>
                    {intl.formatMessage({ id: '下载srt格式' })}
                </a>
            ),
        },
    ]
    //语音文本下载
    const voiceFileDownload = async (type: string) => {
        const res: any = await SmartService.resourceCaptionDownload(contentId, contentDetail.entityName, false);
        if (res.success) {
            const a = document.createElement('a');
            a.href = res.data[type];
            a.download = contentDetail.entityName + '.' + type;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            message.success(`${intl.formatMessage({ id: '下载成功' })}`);
        } else {
            message.error(`${intl.formatMessage({ id: '下载出错了' })}`);
        }
    }
    //语音文本检查
    const voiceTextCheck = async () => {
        const res: any = await SmartService.voiceTextCheck(contentId, true);
        if (res.success) {
            message.success(`${intl.formatMessage({ id: '检查成功' })}`);
            setVoiceCheck(true);
        } else {
            message.success(`${intl.formatMessage({ id: '检查失败' })}`);
        }
    }
    const changeCurrent = (direction: string) => {
        let current = searchCurrent;
        if (direction === 'up') {
            current = current - 1;
            if (current < 0) {
                current = searchIndex.length - 1;
            }
            setSearchCurrent(current);
        } else {
            current = current + 1;
            if (current === searchIndex.length) {
                current = 0;
            }
            setSearchCurrent(current);
        }
    };

    return (
        <div className="voice-input">
            <div className='voice-input-inner'>
                <Input
                    placeholder={intl.formatMessage({ id: '检索语音文本' })}
                    onPressEnter={searchVoive}
                    addonBefore={
                        <Select defaultValue={'cn'} onChange={languageChange}>
                            <Option value={'cn'}>{intl.formatMessage({ id: '中文' })}</Option>
                            {
                                translateFlag &&
                                <>
                                    <Option value={'en'}>{intl.formatMessage({ id: '英文' })}</Option>
                                    {/* {
                                        isVision && <Option value={'cnen'}>{intl.formatMessage({ id: '中/英文' })}</Option>
                                    } */}
                                </>
                            }
                        </Select>
                    }
                    addonAfter={
                        <>
                            <UpOutlined
                                title={intl.formatMessage({ id: '上一条' })}
                                onClick={() => {
                                    changeCurrent('up');
                                }}
                            />
                            <DownOutlined
                                title={intl.formatMessage({ id: '下一条' })}
                                onClick={() => {
                                    changeCurrent('down');
                                }}
                            />
                        </>
                    }
                />
            </div>
            {/* <Button
          onClick={() => {
            const isVis = !isVision;
            setIsVision(isVis);
          }}
          title={intl.formatMessage({ id: '切换显示模式' })}
          className="changeVision"
          icon={<RetweetOutlined />}
        /> */}
            <Dropdown menu={{ items }}>
                <Button
                    title={intl.formatMessage({ id: '下载' })}
                    className="voiceDownload"
                    icon={<IconFont type='icondownload' />}
                />
            </Dropdown>
            {
                rmanGlobalParameter.includes(globalParams.voicetext_check_display) &&
                (voiceCheck ?
                    <div className='checked'><CheckCircleOutlined />文本已检查</div> :
                    shareFlag_ !== 'share' && <Button
                        onClick={voiceTextCheck}
                        className="text_check"
                        type="primary"
                        icon={<CheckCircleOutlined />}
                    >
                        {intl.formatMessage({ id: '文本检查完成' })}
                    </Button>)
            }
            {!rmanGlobalParameter.includes(globalParams.voicetext_check_display) && <div className='right_'>
                <Button
                    type='link'
                    icon={<IconFont type='iconzitifangda' />}
                    title={intl.formatMessage({ id: '字号变大' })}
                    disabled={fontSize >= 32}
                    onClick={(e: any) => {
                        e.stopPropagation();
                        e.preventDefault();
                        setFontSize((pre: number) => pre += 1)
                    }}
                />
                <Button
                    type='link'
                    icon={<IconFont type='iconzitisuoxiao' />}
                    title={intl.formatMessage({ id: '字号变小' })}
                    disabled={fontSize <= 12}
                    onClick={(e: any) => {
                        e.stopPropagation();
                        e.preventDefault();
                        setFontSize((pre: any) => pre -= 1)
                    }}
                />
            </div>}
        </div>
    )
}

export default voiceInput