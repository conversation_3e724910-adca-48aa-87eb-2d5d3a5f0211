.bindKnowledgeModal{
  .modal_content{
    .ant-tabs-nav{
      margin: 0;
      .ant-tabs-nav-list{
        margin-left: 2%;
      }
    }
    
  }
  .main{
    display: flex;
    width: 948px;
    height: 523px;
    .searchMapContainer{
     
      // background: #F7F9FA;
      width: 323px;
      .search_top{
        display: flex;
        margin: 30px 30px 12px 20px;
        .search{
          .ant-input{
            // background: #F7F9FA;
          }
        }
        .ant-btn{
          // background: #F7F9FA;
        }
      }
      .mapListContainer{
        overflow: auto;
        height: 440px;
        .activeItem{
          // background: #efeded;
          background: var(--active-bg-color);
        }
        overflow-y: auto;
        .ant-list-item{
          cursor: pointer;
          padding: 12px 12px 12px 20px;
        }
        .ant-list-item:hover{
          // background: #efeded;
          background: var(--active-bg-color);
        }
      }
    }
    .searchNodeContainer{
      width: 525px;
      .searchNode_top{
        display: flex;
        margin: 30px 0px 0px 50px;
        .search{
          width: 237px;
          // margin: 0 10px 0 50px;
        }
      }
      .knowledgeListContainer{
        margin-left: 50px;
        margin-top: 20px;
        // overflow: auto;
        // height: 440px;
        .ant-checkbox-group{
          width: 100%;
        }
      }
    }
    .search{
      margin-right: 9px;
      position: relative;
      .searchIcon{
        position: absolute;
        right: 10px;
        top: 10px;
      }
    }
  }
  .knowledgeMapList{
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 523px;
    padding: 2%;
    overflow-y: auto;
    .ant-table-wrapper{
      margin-bottom: 20px;
      .ant-table{
        // background-color: #F7F9FA;
        .ant-table-thead > tr > th{
          // background-color: #F7F9FA;
        }
      }
    }
    .empty{
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
    }
  }

  .ant-modal-body{
    padding: 0;
  }
  .ant-modal-footer{
    display: flex;
    border:0;
    padding: 0;
    .footerLeft{
      // background: #F7F9FA;
      width: 323px;
    }
    .footerLeft_{
      width: 323px;
    }
    .footerRight{
      width: 625px;
      >div{
        margin-right: 30px;
        margin-bottom: 20px;
      }
    }
  }
}
