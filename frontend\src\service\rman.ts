import { request } from 'umi';
import searchTypes from '@/types/searchTypes';
namespace rmanApis {
  export const gettreebylevel = (gettreebylevel: number = 2) => {
    return request<searchTypes.IFolder[]>(`/folder/all/tree?level=${gettreebylevel}`, {
      method: 'GET',
    });
  };
  export const loadChild = (path: string, isOwner?: any) => {
    return request<searchTypes.IFolder>(
      `/rman/v1/folder/children?folderPath=${encodeURIComponent(path)}%2F&isChildCount=true${
        isOwner ? '&isOwner=true' : ''
      }`,
      {
        method: 'GET',
      },
    );
  };
  //查询资源
  export const resourceDetail = (id: string) => {
    return request(`/rman/v1/entity/base/${id}?isSysAuth=true`, {
      method: 'GET',
    });
  };
  // 获取水印
  export const getLinkWatermark = (link:string) => {
    return request(`/rman/v1/share/link/watermark?link=${link}`, {
      method: 'GET'
    });
  };
  export function searchResList(
    id: any,
    path: any,
    current: number,
    type: string[] | undefined,
    pageSize?: number,
  ) {
    let condition: any = [
      // {
      //   field: 'createDate_',
      //   searchRelation: 6,
      //   value: [''],
      // },
      // {
      //   field: 'createDate_',
      //   searchRelation: 4,
      //   value: [''],
      // },
      // {
      //   field: 'type_',
      //   searchRelation: 0,
      //   value: [''],
      // },
    ];
    if (type) {
      condition = [
        ...condition,
        {
          field: 'type_',
          searchRelation: 0,
          value: type,
        },
      ];
    }
    return request('/rman/v1/search/folder', {
      method: 'POST',
      data: {
        folderId: id,
        folderPath: path + '/',
        // path: encodeURIComponent(id),
        keyword: [''],
        conditions: condition,
        sortFields: [
          {
            field: 'createDate_',
            isDesc: true,
          },
        ],
        pageIndex: current,
        pageSize: pageSize || 9,
      },
    });
  }
  export function searchResAll(
    id: any,
    type: string,
    path: string,
    keyword: string,
    starttime: string,
    endtime: string,
    current: number,
    asr_status?: boolean,
  ) {
    let conditions = [] as any
    if(type){
      conditions = [
        {
          field: 'type_',
          searchRelation: 0,
          value: [type],
        }
      ];
    }
    if (asr_status) {
      conditions.push({
        field: 'asr_status',
        searchRelation: 0,
        value: ['2'],
      });
    }
    return request('/rman/v1/search/all', {
      method: 'POST',
      data: {
        folderId: id,
        keyword: [keyword],
        folderPath: path,
        conditions: conditions,
        sortFields: [
          {
            field: 'createDate_',
            isDesc: true,
          },
        ],
        pageIndex: current,
        pageSize: 9,
      },
    });
  }
  //查询我的收藏
  export const getmycollectionlist = (data: any) => {
    return request(`/rman/v1/metadata/resource/search/collection`, {
      method: 'POST',
      data: data,
    });
  };
  //我的录播
  export const getmyvideolist = (data: any) => {
    return request(`/rman/v1/search/my/video`, {
      method: 'POST',
      data: data,
    });
  };
  export const getMyVideo = (params: any) => {
    return request(`/rman/v1/search/my/video`, {
      method: 'get',
      params
    });
  }
  //院系录播
  export const getdepartmentvideolist = (data: any) => {
    return request(`/rman/v1/search/department/video`, {
        method: 'POST',
        data: data
    })
  }
  //查询分享给自己的
  export const shareMyself = (data: any) => {
    return request(`/rman/v1/share/search`, {
      method: 'POST',
      data,
    });
  };
  export const getuploadFormat = (type?: string) => {
    return request(`/rman/v1/upload/format`, {
      method: 'GET',
      params: {
        type,
      },
    });
  };
  /**
   * OOS配置
   * @param data
   */
  export const storageConfig = async (data: any) => {
    return request(`/rman/v1/upload/client/path`, {
      method: 'POST',
      data,
    });
  };
  /**
   * 分片上传初始化
   * @param data
   */
  export const uploadInit = async (data: any) => {
    return request(`/rman/v1/upload/client/init`, {
      method: 'POST',
      data,
    });
  };
  /**
   * 取消上传
   * @param data
   */
  export const uploadDelete = async (uid: any) => {
    return request(`/rman/v1/upload/client/task/${uid}/delete`, {
      method: 'POST',
    });
  };
  /**
   * 文件合并
   * @param data
   */
  export const filemerge = (guid: string, fileName: string, fileGuid: string) => {
    return request<string>('/rman/v1/upload/client/filemerge', {
      method: 'POST',
      data: {
        guid,
        fileName,
        fileGuid,
      },
    });
  };
  export const uploadImport = (data: any) => {
    return request<boolean>('/rman/v1/upload/client/import', {
      method: 'POST',
      data,
    });
  };
  /**
   * 查询合并进度
   * @param fileGuid
   */
  export const fetchMergeStatus = (fileGuid: string) =>
    request<{
      state: number;
      errorMsg: string;
      finalFilePath: string;
    }>(`/rman/v1/upload/client/task/${fileGuid}`);
  /**
   * 查询已完成的上传任务
   * @param
   */
  export const fetchUploadTasks = (data: any) =>
    request<{
      state: number;
      errorMsg: string;
      finalFilePath: string;
    }>(`/rman/v1/upload/client/task/list`, {
      method: 'POST',
      data,
    });
  /**
   * 删除任务记录
   * @param id
   */
  export const deleteUploadTask = (id: any) =>
    request<{
      state: number;
      errorMsg: string;
      finalFilePath: string;
    }>(`/rman/v1/upload/client/task/${id}/delete`, {
      method: 'POST',
    });
  /**
   * 清空任务记录
   * @param id
   */
  export const cleanUploadTask = () =>
    request<any>(`/rman/v1/upload/client/tasklist/clear`, {
      method: 'GET',
    });
  export const changeCover = (data: any) =>
    request(`/learn/v1/course/cover/images`, {
      method: 'POST',
      data
    })
  export const updateKerframe = (data: any) =>
    request(`/rman/v1/entity/icon/keyframe/update`, {
      method: 'POST',
      data
  })
  export const addMajorCategory = (data: any) =>
    request(`/rman/v1/folder/major/category`, {
      method: 'POST',
      data
  })
  export const updateFolderKerframe = (data: any) =>
    request(`/rman/v1/folder/custom/keyframe`, {
      method: 'POST',
      data
  })

  export const getKeyframeSetting = (params: any) =>
    request(`/rman/v1/entity/icon/keyframe`, {
      method: 'GET',
      params
  })
  export const getCoverList = () =>
  request('/learn/v1/course/cover', {method: 'get'})  

  export const getGroupStroage = (params: any) => request('/rman/v1/group/storage', {
    method: 'get',
    params
  })

  export const updateStroage = (data: any) => request('/rman/v1/group/add/or/update/storage', {
      method: 'post',
      data
    })

  

  export const folderDownloadDelete = (data: any) => {
    return request(`/rman/v1/folder/download/delete`, {
      method: 'DELETE',
      data,
    });
  };

 //判断是不是管理员
  export const administrator = (folderId: string) => 
    request(`/rman/v1/group/administrator?folderId=${folderId}`,{  method: 'get' });
  };


export default rmanApis;
