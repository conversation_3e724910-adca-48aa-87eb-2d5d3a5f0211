.publish-resource-modal {
    .ant-modal-title {
        font-size: 14px;
    }

    .ant-modal-body {
        padding: 16px 15px 16px 30px;
    }

    .tree-container {
        height: 250px;
        overflow-y: auto;

        &::-webkit-scrollbar {
            width: 5px;
        }

        &::-webkit-scrollbar-thumb {
            border-radius: 5px;
            background-color: rgb(215, 215, 215);
        }

        &::-webkit-scrollbar-button {
            display: none;
        }

        .tree-item {
            display: flex;
            flex-direction: column;

            .tree-item-title {
                font-size: 14px;
                font-weight: 500;
                color: #333;
                margin-left: 10px;
                max-width: 350px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .first-rank {
                display: flex;
                align-items: center;
                font-size: 12px;
                color: #666;
                margin-bottom: 10px;
            }

            .second-rank {
                display: flex;
                align-items: center;
                font-size: 12px;
                color: #666;
                margin-left: 24px;
                margin-bottom: 10px;
            }
        }

    }
}