﻿# 内容库插件文档

## 引入

需要 dom loaded 以后

1. 原生引入

```html
<div id="container"></div>
<script src="./contentLibrary.min.js"></script>
<script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function () {
    const contentLibrary = new ContentLibrary("要挂载的id", {
      ipPrefix: "http://www.baidu.com",
      isAutoPlay: false,
      pageSize: 24,
      primaryColor: "#ff0000",
      isMultiple: true,
      showOther: ["myCollection", "myVideo"],
    });
    contentLibrary.open(); // 开启挂载
    contentLibrary.setToken("600cf7559b735d75042132b0e48be5ff"); // 设置token
  });
</script>
<link rel="stylesheet/less" href="theme.less" />
<script src="./less.min.js"></script>
```

2. Vue 引入

```js
import contentLibrary from "./contentLibrary.min.js";
export default {
  mounted() {
    const contentLibrary = new ContentLibrary("要挂载的id", {
      ipPrefix: "http://www.baidu.com",
      accessKey: "",
      isAutoPlay: false,
      pageSize: 24,
      userCode: "admin",
      useClickCb: function (data) {
        console.info(data);
      },
    });
    contentLibrary.open(); // 开启挂载
    contentLibrary.setToken("600cf7559b735d75042132b0e48be5ff"); // 设置token
  },
};
```

3. React 引入

- hooks 方式

```js
import contentLibrary from "./contentLibrary.min.js";
function Example() {
  useEffect(() => {
    const contentLibrary = new ContentLibrary("要挂载的id", {
      ipPrefix: "http://www.baidu.com",
      accessKey: "",
      isAutoPlay: false,
      pageSize: 24,
      userCode: "admin",
      useClickCb: function (data) {
        console.info(data);
      },
    });
    contentLibrary.open(); // 开启挂载
    contentLibrary.setToken("600cf7559b735d75042132b0e48be5ff"); // 设置token
  }, []);
}
```

- 老版本

```js
import contentLibrary from "./contentLibrary.min.js";
class Example extends React.Component {
  componentDidMount() {
    const contentLibrary = new ContentLibrary("要挂载的id", {
      ipPrefix: "http://www.baidu.com",
      accessKey: "",
      isAutoPlay: false,
      pageSize: 24,
      userCode: "admin",
      useClickCb: function (data) {
        console.info(data);
      },
    });
    contentLibrary.open(); // 开启挂载
    contentLibrary.setToken("600cf7559b735d75042132b0e48be5ff"); // 设置token
  }
}
```

## 参数以及方法相关

1. 实例化

- 第一个参数是要挂载的 document 的 Id
- 第二个参数是传入的参数，是一个对象，详细内容见下

```js
const cl = new ContentLibrary("id", options);
```

2. 参数相关

```TypeScript
// 参数
interface Options {
  ipPrefix?: string; // ip, 不传就是当前ip前缀，如http://www.baidu.com
  isAutoPlay?: boolean; // 详情中播放，是否自动播放， 默认false
  pageSize?: number; // 列表的pageSize，默认9
  onlyType?: "video" | "audio" | "picture" | "document"; // 用于单独只查询某种类型的文件，不传时可以查询所以类型
  primaryColor?: string; // 设置主题色，使用十六进制
  showOther?: Array<"myVideo" | "myCollection"> // 我的录播和我的收藏显示，传入数组，默认["myVideo", "myCollection"]
  isMultiple?: boolean; // 多选或者单选，默认false
  onSelect: (ids: string) => void; // 调用会返回选中的id
}
```

3. 方法调用

```js
cl.open(); // 将插件中的html挂载到当前dom中
cl.setToken("token"); // 设置token，由于token会过期，请定期刷新token
```

4. 需要在初始化插件后，再引入 theme.less 和 less.min.js（尽量是 2 版本的），如果是框架使用需要动态引入
