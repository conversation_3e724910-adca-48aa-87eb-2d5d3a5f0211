.uploadbox{
  width: 200;
  // .ant-modal-close-x{
  //   color: #fff;
  // }
  // .ant-modal-header{
    // background: var(--second-color);
    // .ant-modal-title{
    //   color: #fff;
    // }
  // }
}
.ant-modal-body{
  padding: 20px;
}
.qrcode{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.upload_top{
  display: flex;
  align-items: center;
  .uploaded_file{
    // height: 250px;
    // height: 300px;
    width: 880px;
    margin-right: 10px;
    .file_top{
      display: flex;
      justify-content: space-between;
      padding: 0 0 20px 9px;
      .icon_box{
        display: flex;
        align-items: flex-end;
        .ant-btn{
          // width:33px;
          padding: 9px;
          margin-left: 12px;
          height:30px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 14px;
        }
        div:nth-of-type(1){
          background-color: var(--primary-color);
          color: #fff;
          position: relative;
          .hide_open_file{
            position: absolute;
            width:33px;
            height:24px;
            opacity: 0;
          }
        }
        div:nth-of-type(2){
          background-color: #E4E4E4;
        }
      }
    }
    .file_bottom{
      // height: 535px;
      height: 200px;
      width: 100%;
      // border: 1px solid #999999;
      .select_all{
        display: flex;
        align-items: center;
        padding-left: 15px;
        padding-right: 8px;
        height: 40px;
        background-color: #F6F6F6;
      }
      .select_box{
        display: flex;
        flex-direction: column;
        overflow: auto;
        // height: calc(100% - 342px);
        height: calc(100% - 42px);

        .select_item{
          border-bottom: 1px solid #DFDFDF;
          border-left: 1px solid #E8E8E8;
          border-right: 1px solid #E8E8E8;
          display: flex;
          align-items: center;
          padding-left: 15px;
          height: 42px;
          justify-content: space-between;
          cursor: pointer;
          position: relative;
          // position: relative;
          .change_edit{
            // position: absolute;
            height: 40px;
            line-height: 40px;
            width: 93%;
            // right: 0;
            position: relative;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            }
            span{
              position: relative;
              z-index: 2;
            }
          }
          .no_edit{
            font-weight: bolder;
          }

          .progress{
            position: absolute;
            height: 42px;
            left: 0;
            top: 0;
            background-color: var(--primary-color);
            opacity: .2;
            transition: background .2s linear 0s;
            &.progress-success{
              background-color:green;
            }
        }
      }
    }


    .top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .left {
        > .ant-btn {
          margin-right: 10px;
          padding: 4px 8px;
          border-radius: 5px;
        }
        > span {
          .ant-btn {
            margin-right: 10px;
            padding: 4px 8px;
            border-radius: 5px;
          }
        }
      }
      .right {
        display: flex;
        align-items: center;
        .ant-select {
          width: 300px;
          height: 32px;
          margin-left: 10px;
        }
      }
    }
    .center {
      margin-top: 20px;
      .ant-table-body {
        .ant-table-tbody {
          .ant-table-cell {
            .keyframe {
              display: flex;
              align-items: center;
              > .ant-btn {
                display: none;
                height: 30px;
              }
              &:hover {
                .ant-btn {
                  display: inline-block !important;
                  transition: all 0.3s;
                }
              }
              img {
                width: 56px;
                height: 32px;
              }
            }
          }
        }
      }
    }
  }
  .switch {
    cursor: pointer;
    font-size: 20px;
    // color: #ffffff;
    height: 40px;
    // background-color: var(--primary-color);
    display: flex;
    justify-content: center;
    align-items: center;
    .expand{
      position: absolute;
      right: -12px;
    }
    .shrink{
      position: absolute;
      right: 0;
    }
  }
  .switch_expand{
    width: 31px;
    position: relative;
  }
  .splitLine{
    width: 1px;
    height: 237px;
    border-left: 1px solid #E9E9E9;
    margin-right: 30px;
  }
  .basic_data_hidden{
    display: none;
  }
  .basic_data{
    height: 250px;
    width: 372px;
    .file_bottom{
      height: 220px;
      width: 100%;
      // border:1px solid rgba(232,232,232,1);
      padding-top: 20px;
      overflow: auto;
    }
    // .box-header-title{
    //   background:rgba(217,217,217,1);
    //   color: #333333;
    // }
  }
  .box-header-title{
    font-size: 14px;
    padding: 0;
    // height: 35px;
    // line-height: 36px;
    display: inline-block;
    // color: #fff;
    border-radius: 3px 3px 0 0;
    // background: var(--primary-color);
  }
}
.upload_bottom{
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.dragUpload{
  width: 100%;
  height: 80px;
  margin-top: 20px;
  padding-bottom:20px;
  .ant-upload-drag{
    .ant-upload{
      padding: 0;
    }
    .ant-upload-text{
      opacity: 0.5;
    }
  }
}

//H5移动端适配
.mobilebox{
  .ant-modal-content{
    .ant-modal-body{
      .upload_top{
       .uploaded_file{
        width: 100%;
        .top{
          display: block;
          .right{
            margin-top: 20px;
            & > span{
              white-space: nowrap;
            }
          }
        }
       }
       .switch{
        display: none;
       }
      }
      .ant-table-thead{
        white-space: nowrap;
      }
    }
  }
}

.batchSettingModal {
  width: 460px !important;
  .ant-modal-body {
    padding: 20px 70px;
    .body {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      > div {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 20px;
        > label {
          width: 100px;
          text-align: right;
        }
        .add {
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 160px;
          height: 90px;
          color: var(--ant-primary-color);
          background: #f7f9fa;
          border-radius: 2px;
          cursor: pointer;
          > span:last-child {
            margin-top: 8px;
            line-height: 20px;
          }
          .anticon-plus-circle {
            font-size: 24px;
          }
          .ant-btn {
            font-size: 16px;
          }
        }
        .done {
          display: flex;
          align-items: center;
          width: 100%;
          height: 100%;
          > img {
            width: 100%;
            height: 100%;
          }
          .ant-btn {
            position: absolute;
            right: -36px;
            margin-left: 5px;
          }
        }
        .watermark {
          position: relative;
          width: 160px;
          height: 90px;
          .dot {
            position: absolute;
            width: 14px;
            height: 14px;
            background: #d8d8d8;
            cursor: pointer;
            &.active {
              // background: var(--ant-primary-color) !important;
              background: #1890ff !important;
            }
            &.leftTop {
              top: 0;
              left: 0;
            }
            &.rightTop {
              top: 0;
              right: 0;
            }
            &.leftBottom {
              bottom: 0;
              left: 0;
            }
            &.rightBottom {
              right: 0;
              bottom: 0;
            }
          }
          .tips {
            position: absolute;
            width: 160px;
            margin-top: 8px;
            color: #a4a4a4;
            font-size: 12px;
            text-align: center;
          }
        }
      }
    }
  }
}
